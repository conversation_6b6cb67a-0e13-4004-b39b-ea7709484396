# School ERP System - Production Environment Configuration
# Copy this file to .env and update the values for your production environment

# =============================================================================
# CORE SETTINGS
# =============================================================================

# Environment
ENVIRONMENT=production
DEBUG=False

# Security
SECRET_KEY=your-super-secret-key-here-make-it-at-least-50-characters-long
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com,api.yourdomain.com

# Admin URL (change for security)
ADMIN_URL=secure-admin/

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Database URL
DATABASE_URL=postgresql://school_user:your_password@localhost:5432/school_erp_prod

# Database connection pooling
USE_PGBOUNCER=True

# =============================================================================
# CACHE AND SESSION CONFIGURATION
# =============================================================================

# Redis URL for caching and sessions
REDIS_URL=redis://localhost:6379/1

# =============================================================================
# CELERY CONFIGURATION
# =============================================================================

# Celery broker and result backend
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================

# SMTP Settings
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=School ERP System <<EMAIL>>

# =============================================================================
# FILE STORAGE CONFIGURATION
# =============================================================================

# Use AWS S3 for file storage (recommended for production)
USE_S3=True
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=your-school-erp-bucket
AWS_S3_REGION_NAME=us-east-1

# =============================================================================
# MONITORING AND LOGGING
# =============================================================================

# Sentry for error tracking
SENTRY_DSN=https://<EMAIL>/project-id

# Application version for tracking
APP_VERSION=1.0.0

# Performance monitoring
PERFORMANCE_MONITORING_ENABLED=True
SLOW_QUERY_THRESHOLD=1.0
SLOW_REQUEST_THRESHOLD=2.0
MEMORY_THRESHOLD=80

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================

# Backup settings
BACKUP_ENABLED=True
BACKUP_STORAGE=s3
BACKUP_RETENTION_DAYS=30

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Communication features
ENABLE_SMS_NOTIFICATIONS=True
ENABLE_EMAIL_NOTIFICATIONS=True
ENABLE_PUSH_NOTIFICATIONS=True

# System features
ENABLE_ANALYTICS=True
ENABLE_AUDIT_LOGGING=True
ENABLE_API_VERSIONING=True
ENABLE_RATE_LIMITING=True

# Maintenance mode (set to True during maintenance)
ENABLE_MAINTENANCE_MODE=False

# =============================================================================
# THIRD-PARTY INTEGRATIONS
# =============================================================================

# Google Workspace Integration
GOOGLE_WORKSPACE_ENABLED=False
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_DOMAIN=yourdomain.com

# Microsoft 365 Integration
MICROSOFT_365_ENABLED=False
MICROSOFT_CLIENT_ID=your-microsoft-client-id
MICROSOFT_CLIENT_SECRET=your-microsoft-client-secret
MICROSOFT_TENANT_ID=your-tenant-id

# Payment Gateway - Stripe
STRIPE_ENABLED=True
STRIPE_PUBLIC_KEY=pk_live_your_stripe_public_key
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Payment Gateway - PayPal
PAYPAL_ENABLED=False
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
PAYPAL_MODE=live

# SMS Provider - Twilio
TWILIO_ENABLED=True
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_FROM_NUMBER=+**********

# SMS Provider - AWS SNS
AWS_SNS_ENABLED=False
AWS_SNS_REGION=us-east-1

# =============================================================================
# API AND CORS CONFIGURATION
# =============================================================================

# CORS settings (if API is accessed from different domains)
ENABLE_CORS=False
CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# =============================================================================
# SSL AND SECURITY CONFIGURATION
# =============================================================================

# SSL settings (handled by reverse proxy in production)
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=********
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True

# =============================================================================
# ADDITIONAL PRODUCTION SETTINGS
# =============================================================================

# Time zone
TIME_ZONE=UTC

# Language settings
LANGUAGE_CODE=en-us
USE_I18N=True
USE_L10N=True
USE_TZ=True

# File upload limits
FILE_UPLOAD_MAX_MEMORY_SIZE=5242880
DATA_UPLOAD_MAX_MEMORY_SIZE=5242880
DATA_UPLOAD_MAX_NUMBER_FIELDS=1000

# =============================================================================
# DEVELOPMENT OVERRIDES (for staging environment)
# =============================================================================

# Uncomment these for staging environment
# ENVIRONMENT=staging
# DEBUG=True
# ALLOWED_HOSTS=staging.yourdomain.com
# DATABASE_URL=postgresql://school_user:password@localhost:5432/school_erp_staging
# SENTRY_DSN=https://<EMAIL>/project-id

# =============================================================================
# NOTES
# =============================================================================

# 1. Never commit this file with real credentials to version control
# 2. Use strong, unique passwords for all services
# 3. Regularly rotate API keys and secrets
# 4. Monitor your application logs for security issues
# 5. Keep all dependencies updated
# 6. Use HTTPS for all external communications
# 7. Implement proper backup and disaster recovery procedures
# 8. Test your configuration in a staging environment first

# =============================================================================
# SECURITY CHECKLIST
# =============================================================================

# □ SECRET_KEY is at least 50 characters and randomly generated
# □ DEBUG is set to False
# □ ALLOWED_HOSTS is properly configured
# □ Database credentials are secure and unique
# □ All API keys and secrets are properly secured
# □ SSL/TLS is properly configured
# □ Backup procedures are tested and working
# □ Monitoring and alerting are configured
# □ Security headers are properly set
# □ File permissions are correctly configured
# □ Firewall rules are properly configured
# □ Regular security updates are scheduled