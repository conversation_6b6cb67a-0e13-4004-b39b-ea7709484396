# Design Document

## Overview

This design document outlines the implementation of a comprehensive school selection system and library borrowing fixes for the Django-based School ERP system. The solution uses Django middleware for automatic school context, session-based persistence, AJAX for seamless interactions, and Bootstrap 5 for a modern, responsive interface.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[User Request] --> B[School Selection Middleware]
    B --> C{School Selected?}
    C -->|No| D[Redirect to School Selection]
    C -->|Yes| E[Add School Context to Request]
    E --> F[View Processing]
    F --> G[Template Rendering with School Context]
    G --> H[Response with School-Filtered Data]
    
    I[School Switcher Component] --> J[AJAX School Switch]
    J --> K[Update Session]
    K --> L[Refresh Page Data]
    
    M[Library Operations] --> N[AJAX API Calls]
    N --> O[School-Scoped Database Operations]
    O --> P[JSON Response]
    P --> Q[UI Update with Feedback]
```

### Technology Stack

- **Backend**: Django 4.x with custom middleware
- **Frontend**: Bootstrap 5, vanilla JavaScript with AJAX
- **Session Management**: Django sessions with database backend
- **API**: Django REST framework for library operations
- **UI Components**: Reusable template components
- **Caching**: Django cache framework for performance

## Components and Interfaces

### 1. School Selection Middleware

#### SchoolSelectionMiddleware
```python
class SchoolSelectionMiddleware:
    """
    Middleware to handle automatic school selection and context injection
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.exempt_urls = ['/admin/', '/accounts/login/', '/school/select/', '/static/', '/media/']
    
    def __call__(self, request):
        # Skip middleware for exempt URLs and unauthenticated users
        if self.should_skip(request):
            return self.get_response(request)
        
        # Get or set school context
        school = self.get_school_context(request)
        if not school:
            return redirect('core:school_select')
        
        # Add school to request
        request.school = school
        return self.get_response(request)
```

#### SchoolContextMiddleware
```python
class SchoolContextMiddleware:
    """
    Middleware to add school context data to all requests
    """
    
    def __call__(self, request):
        if hasattr(request, 'school'):
            request.school_context = {
                'current_school': request.school,
                'school_name': request.school.name,
                'school_code': request.school.code,
            }
        return self.get_response(request)
```

### 2. School Selection Views

#### School Selection View
```python
@login_required
def school_select(request):
    """
    Professional school selection interface
    """
    if request.method == 'POST':
        school_id = request.POST.get('school_id')
        if school_id:
            school = get_object_or_404(School, id=school_id)
            request.session['selected_school_id'] = str(school.id)
            messages.success(request, f'School changed to {school.name}')
            return redirect(request.GET.get('next', '/dashboard/'))
    
    # Get available schools based on user permissions
    schools = get_user_schools(request.user)
    selected_school = get_current_school(request)
    
    return render(request, 'core/school_select.html', {
        'schools': schools,
        'selected_school': selected_school,
        'next': request.GET.get('next', '/dashboard/')
    })
```

#### AJAX School Switching
```python
@login_required
@require_http_methods(["POST"])
def switch_school(request):
    """
    AJAX endpoint for seamless school switching
    """
    school_id = request.POST.get('school_id')
    try:
        school = School.objects.get(id=school_id)
        # Verify user has access to this school
        if not user_has_school_access(request.user, school):
            return JsonResponse({'success': False, 'message': 'Access denied'})
        
        request.session['selected_school_id'] = str(school.id)
        return JsonResponse({
            'success': True,
            'message': f'Switched to {school.name}',
            'school_name': school.name,
            'school_code': school.code
        })
    except School.DoesNotExist:
        return JsonResponse({'success': False, 'message': 'Invalid school'})
```

### 3. School Selection Interface

#### Professional School Selection Template
```html
<!-- Modern card-based school selection -->
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow-lg">
                <div class="card-header bg-primary text-white text-center">
                    <h4><i class="fas fa-school me-2"></i>Select School</h4>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        {% for school in schools %}
                        <div class="col-md-4 mb-3">
                            <div class="card school-card h-100 {% if selected_school and school.id == selected_school.id %}border-primary{% endif %}">
                                <div class="card-body text-center">
                                    <i class="fas fa-university text-primary mb-3" style="font-size: 3rem;"></i>
                                    <h5 class="card-title">{{ school.name }}</h5>
                                    <p class="card-text text-muted">
                                        <small>{{ school.code }} • {{ school.address|truncatechars:30 }}</small>
                                    </p>
                                    <button type="submit" name="school_id" value="{{ school.id }}" 
                                            class="btn btn-primary w-100">
                                        Select School
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
```

### 4. Navbar School Switcher Component

#### School Switcher Dropdown
```html
<!-- Reusable school switcher component -->
<div class="dropdown">
    <button class="btn btn-outline-light dropdown-toggle" type="button" 
            id="schoolSwitcher" data-bs-toggle="dropdown">
        <i class="fas fa-school me-2"></i>
        <span id="currentSchoolName">{{ request.school.name|truncatechars:20 }}</span>
    </button>
    <ul class="dropdown-menu dropdown-menu-end" style="min-width: 300px;">
        <li><h6 class="dropdown-header">Available Schools</h6></li>
        <li><hr class="dropdown-divider"></li>
        {% for school in schools %}
        <li>
            <a class="dropdown-item school-option {% if request.school.id == school.id %}active{% endif %}" 
               href="#" data-school-id="{{ school.id }}">
                <div class="d-flex align-items-center">
                    <i class="fas fa-university text-primary me-3"></i>
                    <div class="flex-grow-1">
                        <div class="fw-bold">{{ school.name }}</div>
                        <small class="text-muted">{{ school.code }}</small>
                    </div>
                    {% if request.school.id == school.id %}
                    <i class="fas fa-check text-success"></i>
                    {% endif %}
                </div>
            </a>
        </li>
        {% endfor %}
    </ul>
</div>
```

### 5. Library AJAX Operations

#### Library API Endpoints
```python
@login_required
@require_http_methods(["POST"])
def borrow_book(request):
    """
    AJAX endpoint for book borrowing
    """
    book_id = request.POST.get('book_id')
    try:
        book = Book.objects.get(id=book_id, school=request.school)
        
        # Check if book is available
        if book.available_copies <= 0:
            return JsonResponse({
                'success': False, 
                'message': 'Book is not available for borrowing'
            })
        
        # Create borrowing record
        borrowing = Borrowing.objects.create(
            book=book,
            borrower=request.user,
            school=request.school,
            borrowed_date=timezone.now(),
            due_date=timezone.now() + timedelta(days=14)
        )
        
        # Update book availability
        book.available_copies -= 1
        book.save()
        
        return JsonResponse({
            'success': True,
            'message': f'Successfully borrowed "{book.title}"',
            'borrowing_id': borrowing.id,
            'due_date': borrowing.due_date.strftime('%Y-%m-%d')
        })
        
    except Book.DoesNotExist:
        return JsonResponse({'success': False, 'message': 'Book not found'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': 'An error occurred'})

@login_required
@require_http_methods(["POST"])
def return_book(request):
    """
    AJAX endpoint for book returning
    """
    borrowing_id = request.POST.get('borrowing_id')
    try:
        borrowing = Borrowing.objects.get(
            id=borrowing_id, 
            borrower=request.user,
            school=request.school,
            returned_date__isnull=True
        )
        
        # Mark as returned
        borrowing.returned_date = timezone.now()
        borrowing.save()
        
        # Update book availability
        borrowing.book.available_copies += 1
        borrowing.book.save()
        
        return JsonResponse({
            'success': True,
            'message': f'Successfully returned "{borrowing.book.title}"'
        })
        
    except Borrowing.DoesNotExist:
        return JsonResponse({'success': False, 'message': 'Borrowing record not found'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': 'An error occurred'})
```

#### Frontend JavaScript for Library Operations
```javascript
// AJAX library operations with error handling
function borrowBook(bookId) {
    if (confirm('Do you want to borrow this book?')) {
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        
        // Show loading state
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Borrowing...';
        button.disabled = true;
        
        // Make AJAX request
        fetch('/library/api/borrow/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `book_id=${bookId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('success', data.message);
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast('error', data.message);
                button.innerHTML = originalText;
                button.disabled = false;
            }
        })
        .catch(error => {
            showToast('error', 'Error borrowing book. Please try again.');
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
}

function returnBook(borrowingId) {
    if (confirm('Do you want to return this book?')) {
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        
        // Show loading state
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Returning...';
        button.disabled = true;
        
        // Make AJAX request
        fetch('/library/api/return/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `borrowing_id=${borrowingId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('success', data.message);
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast('error', data.message);
                button.innerHTML = originalText;
                button.disabled = false;
            }
        })
        .catch(error => {
            showToast('error', 'Error returning book. Please try again.');
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
}
```

## Data Models

### School Context Integration

#### Enhanced Models with School Context
```python
class BaseSchoolModel(models.Model):
    """
    Abstract base model that automatically includes school context
    """
    school = models.ForeignKey(School, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        abstract = True

class ClassSubject(BaseSchoolModel):
    """
    Enhanced ClassSubject model with automatic school context
    """
    class_assigned = models.ForeignKey(Class, on_delete=models.CASCADE)
    subject = models.ForeignKey(Subject, on_delete=models.CASCADE)
    teacher = models.ForeignKey(Teacher, on_delete=models.SET_NULL, null=True, blank=True)
    weekly_hours = models.PositiveIntegerField(default=1)
    
    class Meta:
        unique_together = ['class_assigned', 'subject', 'school']

class Borrowing(BaseSchoolModel):
    """
    Library borrowing model with school context
    """
    book = models.ForeignKey(Book, on_delete=models.CASCADE)
    borrower = models.ForeignKey(User, on_delete=models.CASCADE)
    borrowed_date = models.DateTimeField(auto_now_add=True)
    due_date = models.DateTimeField()
    returned_date = models.DateTimeField(null=True, blank=True)
    fine_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    @property
    def is_overdue(self):
        return self.due_date < timezone.now() and not self.returned_date
```

### Context Processors

#### School Context Processor
```python
def school_context(request):
    """
    Add school-related context to all templates
    """
    context = {
        'current_school': None,
        'schools': School.objects.none(),
        'has_school_selection': False,
    }
    
    if hasattr(request, 'school'):
        context['current_school'] = request.school
    
    if request.user.is_authenticated:
        schools = get_user_schools(request.user)
        context['schools'] = schools
        context['has_school_selection'] = schools.count() > 1
    
    return context
```

## Error Handling

### Comprehensive Error Management

#### Middleware Error Handling
```python
class SchoolSelectionMiddleware:
    def __call__(self, request):
        try:
            # School selection logic
            return self.process_request(request)
        except School.DoesNotExist:
            # Clear invalid school from session
            if 'selected_school_id' in request.session:
                del request.session['selected_school_id']
            return redirect('core:school_select')
        except Exception as e:
            # Log error and provide fallback
            logger.error(f"School selection error: {e}")
            messages.error(request, "An error occurred. Please select a school.")
            return redirect('core:school_select')
```

#### AJAX Error Handling
```javascript
// Comprehensive error handling for AJAX operations
function handleAjaxError(error, button, originalText) {
    console.error('AJAX Error:', error);
    
    // Restore button state
    if (button) {
        button.innerHTML = originalText;
        button.disabled = false;
    }
    
    // Show user-friendly error message
    showToast('error', 'An error occurred. Please try again.');
}

// Enhanced fetch with retry logic
async function makeAjaxRequest(url, data, retries = 3) {
    for (let i = 0; i < retries; i++) {
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: data
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            if (i === retries - 1) throw error;
            await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
        }
    }
}
```

## Testing Strategy

### Unit Tests
- Test middleware functionality with various user scenarios
- Test school selection views with different permission levels
- Test library API endpoints with valid and invalid data
- Test context processors with authenticated and anonymous users

### Integration Tests
- Test complete school selection workflow
- Test library borrowing and returning process
- Test school switching with data filtering
- Test error scenarios and recovery

### Frontend Tests
- Test AJAX operations with network failures
- Test UI responsiveness and loading states
- Test school switcher dropdown functionality
- Test toast notifications and user feedback

### Performance Tests
- Test middleware performance with high concurrent users
- Test database query optimization with school filtering
- Test session management under load
- Test AJAX response times

## Security Considerations

### Access Control
- Verify user permissions for school access
- Validate school context in all operations
- Prevent unauthorized school switching
- Secure API endpoints with proper authentication

### Data Protection
- Encrypt sensitive school data in sessions
- Implement CSRF protection for all AJAX calls
- Validate and sanitize all user inputs
- Log security-related events for auditing

### Session Security
- Use secure session cookies
- Implement session timeout for inactive users
- Clear sensitive data on logout
- Prevent session fixation attacks

This design provides a comprehensive, secure, and user-friendly solution that will resolve both the IntegrityError issue and the library borrowing functionality while providing an excellent user experience for multi-school management.