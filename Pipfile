[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
django = "*"
djangorestframework = "*"
django-cors-headers = "*"
django-extensions = "*"
pillow = "*"
celery = "*"
redis = "*"
django-celery-beat = "*"
django-modeltranslation = "*"
django-crispy-forms = "*"
crispy-bootstrap4 = "*"
django-import-export = "*"
django-filter = "*"
django-debug-toolbar = "*"
gunicorn = "*"
psycopg2-binary = "*"
django-redis = "*"
django-widget-tweaks = "*"
djangorestframework-simplejwt = "*"
cryptography = "*"
python-decouple = "*"
whitenoise = "*"
polib = "*"
openpyxl = "*"
reportlab = "*"
numpy = "*"
pandas = "*"
scikit-learn = "*"
scipy = "*"
channels = "*"
daphne = "*"
channels-redis = "*"
pytest = "*"
pytest-django = "*"
pytest-cov = "*"

[dev-packages]
pytest = "*"
pytest-django = "*"
pytest-cov = "*"
factory-boy = "*"
black = "*"
flake8 = "*"

[requires]
python_version = "3.12"
