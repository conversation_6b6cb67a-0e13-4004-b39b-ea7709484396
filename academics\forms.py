from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Fieldset, Row, Column, Submit, HTML
from crispy_forms.bootstrap import FormActions
from core.models import AcademicYear
from .models import (
    Subject, Teacher, ClassSubject, Schedule, Exam, StudentGrade,
    StudentAttendance, AttendanceSession, Curriculum, CurriculumSubject,
    GradeCapacityManagement, CurriculumPlan, AttendanceRule, BiometricDevice
)
from students.models import Student, Grade, Class


class AcademicYearForm(forms.ModelForm):
    """
    Academic year form
    """
    class Meta:
        model = AcademicYear
        fields = ['name', 'start_date', 'end_date', 'is_current']
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date:
            if start_date >= end_date:
                raise ValidationError(_('Start date must be before end date.'))

        return cleaned_data


class SubjectForm(forms.ModelForm):
    """
    Subject form
    """
    class Meta:
        model = Subject
        fields = ['name', 'name_ar', 'code', 'description', 'credit_hours']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        self.school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        if self.school and not instance.school_id:
            instance.school = self.school
        if commit:
            instance.save()
            self.save_m2m()
        return instance


class TeacherForm(forms.ModelForm):
    """
    Teacher form
    """
    class Meta:
        model = Teacher
        fields = [
            'user', 'employee_id', 'qualification', 'hire_date',
            'experience_years', 'salary', 'department', 'subjects'
        ]
        widgets = {
            'hire_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'qualification': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'subjects': forms.CheckboxSelectMultiple(),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class ClassSubjectForm(forms.ModelForm):
    """
    Class subject form
    """
    class Meta:
        model = ClassSubject
        fields = ['class_obj', 'subject', 'teacher', 'academic_year', 'semester', 'weekly_hours']

    def __init__(self, *args, **kwargs):
        self.school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        
        # Filter querysets based on school if provided
        if self.school:
            self.fields['class_obj'].queryset = Class.objects.filter(school=self.school, is_active=True)
            self.fields['subject'].queryset = Subject.objects.filter(school=self.school, is_active=True)
            self.fields['teacher'].queryset = Teacher.objects.filter(school=self.school, is_active=True)
            self.fields['academic_year'].queryset = AcademicYear.objects.filter(school=self.school, is_active=True)
            self.fields['semester'].queryset = Semester.objects.filter(school=self.school, is_active=True)
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        if self.school and not instance.school_id:
            instance.school = self.school
        if commit:
            instance.save()
            self.save_m2m()
        return instance


class ScheduleForm(forms.ModelForm):
    """
    Schedule form
    """
    class Meta:
        model = Schedule
        fields = ['class_subject', 'day_of_week', 'start_time', 'end_time', 'room_number']
        widgets = {
            'start_time': forms.TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
            'end_time': forms.TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        self.school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        
        # Filter class_subject to only show active records for the current school
        queryset = ClassSubject.objects.filter(is_active=True)
        if self.school:
            queryset = queryset.filter(school=self.school)
        
        self.fields['class_subject'].queryset = queryset.select_related(
            'class_obj', 'subject', 'teacher'
        ).order_by('class_obj__grade__level', 'class_obj__name', 'subject__name')
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        if self.school and not instance.school_id:
            instance.school = self.school
        if commit:
            instance.save()
            self.save_m2m()
        return instance

    def clean(self):
        cleaned_data = super().clean()
        start_time = cleaned_data.get('start_time')
        end_time = cleaned_data.get('end_time')

        if start_time and end_time:
            if start_time >= end_time:
                raise ValidationError(_('Start time must be before end time.'))

        return cleaned_data


class ExamForm(forms.ModelForm):
    """
    Exam form
    """
    class Meta:
        model = Exam
        fields = [
            'name', 'name_ar', 'class_subject', 'exam_type', 'exam_date',
            'start_time', 'end_time', 'duration_minutes', 'total_marks',
            'passing_marks', 'room', 'exam_session', 'instructions'
        ]
        widgets = {
            'exam_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'start_time': forms.TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
            'end_time': forms.TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
            'instructions': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            Fieldset(
                _('Basic Information'),
                Row(
                    Column('name', css_class='form-group col-md-6 mb-3'),
                    Column('name_ar', css_class='form-group col-md-6 mb-3'),
                ),
                Row(
                    Column('class_subject', css_class='form-group col-md-6 mb-3'),
                    Column('exam_type', css_class='form-group col-md-6 mb-3'),
                ),
            ),
            Fieldset(
                _('Schedule'),
                Row(
                    Column('exam_date', css_class='form-group col-md-4 mb-3'),
                    Column('start_time', css_class='form-group col-md-4 mb-3'),
                    Column('end_time', css_class='form-group col-md-4 mb-3'),
                ),
                Row(
                    Column('duration_minutes', css_class='form-group col-md-6 mb-3'),
                    Column('room_number', css_class='form-group col-md-6 mb-3'),
                ),
            ),
            Fieldset(
                _('Grading'),
                Row(
                    Column('total_marks', css_class='form-group col-md-6 mb-3'),
                    Column('passing_marks', css_class='form-group col-md-6 mb-3'),
                ),
                'instructions',
            ),
            FormActions(
                Submit('submit', _('Save Exam'), css_class='btn btn-primary'),
            )
        )


class StudentGradeForm(forms.ModelForm):
    """
    Student grade form
    """
    class Meta:
        model = StudentGrade
        fields = ['student', 'exam', 'marks_obtained', 'remarks']
        widgets = {
            'marks_obtained': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'remarks': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'

    def clean_marks_obtained(self):
        marks_obtained = self.cleaned_data.get('marks_obtained')
        exam = self.cleaned_data.get('exam')
        
        if marks_obtained and exam and marks_obtained > exam.total_marks:
            raise ValidationError(_('Marks obtained cannot exceed total marks.'))
        
        return marks_obtained


class StudentAttendanceForm(forms.ModelForm):
    """
    Student attendance form
    """
    class Meta:
        model = StudentAttendance
        fields = ['session', 'student', 'class_subject', 'status', 'notes']
        widgets = {
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        
        # Filter class_subject to only show active records
        self.fields['class_subject'].queryset = ClassSubject.objects.filter(
            is_active=True
        ).select_related('class_obj', 'subject', 'teacher').order_by(
            'class_obj__grade__level', 'class_obj__name', 'subject__name'
        )
        
        # Filter students to only show active records
        self.fields['student'].queryset = Student.objects.filter(
            is_active=True
        ).order_by('first_name', 'last_name')
        
        # Filter sessions to only show active sessions
        self.fields['session'].queryset = AttendanceSession.objects.filter(
            status__in=['scheduled', 'active']
        ).select_related('class_subject').order_by('-session_date', '-start_time')


class CurriculumForm(forms.ModelForm):
    """
    Curriculum form
    """
    class Meta:
        model = Curriculum
        fields = ['name', 'name_ar', 'grade', 'academic_year', 'description', 'objectives']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'objectives': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


# Removed duplicate CurriculumSubjectForm - using the one at the end of file


# Bulk attendance form
class BulkAttendanceForm(forms.Form):
    """
    Bulk attendance form for marking attendance for multiple students
    """
    class_subject = forms.ModelChoiceField(
        queryset=ClassSubject.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Class Subject')
    )
    
    date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        label=_('Date')
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


# Grade entry form for multiple students
class BulkGradeEntryForm(forms.Form):
    """
    Bulk grade entry form for entering grades for multiple students
    """
    exam = forms.ModelChoiceField(
        queryset=Exam.objects.filter(is_published=True),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Exam')
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class GradeCapacityManagementForm(forms.ModelForm):
    """
    Grade Capacity Management form
    """
    class Meta:
        model = GradeCapacityManagement
        fields = [
            'grade', 'academic_year', 'total_capacity', 'current_enrollment',
            'waiting_list_capacity', 'enrollment_start_date', 'enrollment_end_date',
            'is_enrollment_open', 'minimum_class_size', 'maximum_class_size',
            'auto_create_sections'
        ]
        widgets = {
            'enrollment_start_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'enrollment_end_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'total_capacity': forms.NumberInput(attrs={'class': 'form-control', 'min': '1'}),
            'current_enrollment': forms.NumberInput(attrs={'class': 'form-control', 'min': '0'}),
            'waiting_list_capacity': forms.NumberInput(attrs={'class': 'form-control', 'min': '0'}),
            'minimum_class_size': forms.NumberInput(attrs={'class': 'form-control', 'min': '1'}),
            'maximum_class_size': forms.NumberInput(attrs={'class': 'form-control', 'min': '1'}),
        }

    def __init__(self, *args, **kwargs):
        self.school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        
        # Filter querysets based on school if provided
        if self.school:
            self.fields['grade'].queryset = Grade.objects.filter(school=self.school, is_active=True)
            self.fields['academic_year'].queryset = AcademicYear.objects.filter(school=self.school, is_active=True)
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        if self.school and not instance.school_id:
            instance.school = self.school
        if commit:
            instance.save()
            self.save_m2m()
        return instance


class CurriculumPlanForm(forms.ModelForm):
    """
    Curriculum Plan form
    """
    class Meta:
        model = CurriculumPlan
        fields = [
            'name', 'name_ar', 'code', 'curriculum_type', 'grades', 'academic_year',
            'description', 'objectives', 'total_credit_hours', 'minimum_credit_hours',
            'core_subjects_required', 'elective_subjects_required', 'minimum_gpa_required',
            'duration_years', 'is_active', 'effective_date', 'expiry_date',
            'prerequisites', 'assessment_policy', 'progression_rules'
        ]
        widgets = {
            'effective_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'expiry_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'objectives': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'prerequisites': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'assessment_policy': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'progression_rules': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'grades': forms.CheckboxSelectMultiple(),
            'minimum_gpa_required': forms.NumberInput(attrs={'step': '0.01', 'min': '0', 'max': '4.00'}),
        }

    def __init__(self, *args, **kwargs):
        self.school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        
        # Filter querysets based on school if provided
        if self.school:
            self.fields['grades'].queryset = Grade.objects.filter(school=self.school, is_active=True)
            self.fields['academic_year'].queryset = AcademicYear.objects.filter(school=self.school, is_active=True)
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        if self.school and not instance.school_id:
            instance.school = self.school
        if commit:
            instance.save()
            self.save_m2m()
        return instance


class AttendanceRuleForm(forms.ModelForm):
    """
    Attendance Rule form
    """
    class Meta:
        model = AttendanceRule
        fields = [
            'name', 'description', 'rule_type', 'applicable_to', 'rule_value',
            'target_grades', 'target_classes', 'target_subjects', 'is_active',
            'effective_from', 'effective_to'
        ]
        widgets = {
            'effective_from': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'effective_to': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'rule_value': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'target_grades': forms.CheckboxSelectMultiple(),
            'target_classes': forms.CheckboxSelectMultiple(),
            'target_subjects': forms.CheckboxSelectMultiple(),
        }

    def __init__(self, *args, **kwargs):
        self.school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        
        # Filter querysets based on school if provided
        if self.school:
            self.fields['target_grades'].queryset = Grade.objects.filter(school=self.school, is_active=True)
            self.fields['target_classes'].queryset = Class.objects.filter(school=self.school, is_active=True)
            self.fields['target_subjects'].queryset = Subject.objects.filter(school=self.school, is_active=True)
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        if self.school and not instance.school_id:
            instance.school = self.school
        if commit:
            instance.save()
            self.save_m2m()
        return instance


class BiometricDeviceForm(forms.ModelForm):
    """
    Biometric Device form
    """
    class Meta:
        model = BiometricDevice
        fields = [
            'name', 'device_type', 'device_id', 'location', 'ip_address',
            'port', 'status', 'configuration'
        ]
        widgets = {
            'port': forms.NumberInput(attrs={'min': '1', 'max': '65535', 'class': 'form-control'}),
            'configuration': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        self.school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        if self.school and not instance.school_id:
            instance.school = self.school
        if commit:
            instance.save()
            self.save_m2m()
        return instance

class AttendanceSessionForm(forms.ModelForm):
    """
    Attendance Session form
    """
    class Meta:
        model = AttendanceSession
        fields = [
            'class_subject', 'session_date', 'start_time', 'end_time',
            'session_topic', 'attendance_method', 'status'
        ]
        widgets = {
            'session_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'start_time': forms.TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
            'end_time': forms.TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
            'session_topic': forms.TextInput(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        self.school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if self.school:
            self.fields['class_subject'].queryset = ClassSubject.objects.filter(
                school=self.school
            ).select_related('class_obj', 'subject', 'teacher')

    def save(self, commit=True):
        instance = super().save(commit=False)
        if self.school and not instance.school_id:
            instance.school = self.school
        if commit:
            instance.save()
            self.save_m2m()
        return instance


class CurriculumSubjectForm(forms.ModelForm):
    """
    Curriculum Subject form
    """
    class Meta:
        model = CurriculumSubject
        fields = [
            'curriculum', 'subject', 'credit_hours', 'weekly_hours', 'semester',
            'is_mandatory', 'sequence_order', 'applicable_grades'
        ]

    def __init__(self, *args, **kwargs):
        self.school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if self.school:
            self.fields['curriculum'].queryset = CurriculumPlan.objects.filter(school=self.school)
            self.fields['subject'].queryset = Subject.objects.filter(school=self.school)

    def save(self, commit=True):
        instance = super().save(commit=False)
        if self.school and not instance.school_id:
            instance.school = self.school
        if commit:
            instance.save()
            self.save_m2m()
        return instance