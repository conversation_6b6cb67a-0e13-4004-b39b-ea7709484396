# Generated by Django 5.2.5 on 2025-08-14 14:34

from django.db import migrations


def create_hr_departments_from_teachers(apps, schema_editor):
    """
    Create HR Department records based on existing teacher department values
    """
    Teacher = apps.get_model('academics', 'Teacher')
    Department = apps.get_model('hr', 'Department')
    
    # Get unique department names from teachers
    dept_names = Teacher.objects.exclude(
        department__isnull=True
    ).exclude(
        department=''
    ).values_list('department', 'school').distinct()
    
    # Create HR departments for each unique department name per school
    for dept_name, school_id in dept_names:
        if dept_name and school_id:
            # Check if department already exists
            if not Department.objects.filter(name=dept_name, school_id=school_id).exists():
                # Create new department
                Department.objects.create(
                    name=dept_name,
                    code=dept_name.upper().replace(' ', '_')[:20],
                    school_id=school_id,
                    description=f'Migrated from teacher department: {dept_name}'
                )


def reverse_create_hr_departments(apps, schema_editor):
    """
    Reverse migration - remove created departments
    """
    Department = apps.get_model('hr', 'Department')
    # Remove departments that were created by migration
    Department.objects.filter(
        description__startswith='Migrated from teacher department:'
    ).delete()


class Migration(migrations.Migration):

    dependencies = [
        ("academics", "0010_assignment_assignmentanalytics_assignmentgroup_and_more"),
        ("hr", "0001_initial"),  # Ensure HR app is migrated
    ]

    operations = [
        migrations.RunPython(
            create_hr_departments_from_teachers,
            reverse_create_hr_departments,
        ),
    ]
