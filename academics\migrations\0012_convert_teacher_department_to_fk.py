# Generated by Django 5.2.5 on 2025-08-14 14:36

from django.db import migrations, models
import django.db.models.deletion


def convert_teacher_departments_to_fk(apps, schema_editor):
    """
    Convert teacher department CharField values to ForeignKey references
    """
    Teacher = apps.get_model('academics', 'Teacher')
    Department = apps.get_model('hr', 'Department')
    
    # Create a temporary field to store the FK references
    # This will be handled by the AlterField operation
    pass


def reverse_convert_teacher_departments(apps, schema_editor):
    """
    Reverse the conversion
    """
    pass


class Migration(migrations.Migration):

    dependencies = [
        ("academics", "0011_migrate_departments_to_hr"),
        ("hr", "0005_performancegoal_performanceimprovementplan_and_more"),
    ]

    operations = [
        # Add a temporary field to store FK references
        migrations.AddField(
            model_name='teacher',
            name='department_fk',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name='teachers_temp',
                to='hr.department',
                verbose_name='Department FK',
            ),
        ),
        
        # Populate the FK field based on CharField values
        migrations.RunPython(
            lambda apps, schema_editor: populate_department_fk(apps, schema_editor),
            lambda apps, schema_editor: None,
        ),
        
        # Remove the old CharField
        migrations.RemoveField(
            model_name='teacher',
            name='department',
        ),
        
        # Rename the FK field to department
        migrations.RenameField(
            model_name='teacher',
            old_name='department_fk',
            new_name='department',
        ),
        
        # Update the related_name
        migrations.AlterField(
            model_name='teacher',
            name='department',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name='teachers',
                to='hr.department',
                verbose_name='Department',
            ),
        ),
    ]


def populate_department_fk(apps, schema_editor):
    """
    Populate the FK field based on CharField values
    """
    Teacher = apps.get_model('academics', 'Teacher')
    Department = apps.get_model('hr', 'Department')
    
    for teacher in Teacher.objects.exclude(department__isnull=True).exclude(department=''):
        try:
            # Find the corresponding HR department
            dept = Department.objects.get(name=teacher.department, school=teacher.school)
            teacher.department_fk = dept
            teacher.save(update_fields=['department_fk'])
        except Department.DoesNotExist:
            # If department doesn't exist, leave FK as null
            pass
