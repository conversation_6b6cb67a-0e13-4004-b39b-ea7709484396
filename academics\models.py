from django.db import models
from django.db.models import Count, Av<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, F, Q
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.conf import settings
from django.core.validators import MinValueValidator, MaxValueValidator
from django.core.exceptions import ValidationError
from datetime import datetime, date, timedelta
import uuid
from core.models import BaseModel, AcademicYear, Semester
from students.models import Grade, Class, Student
import hashlib
import difflib


class Subject(BaseModel):
    """
    Enhanced Subject model with credit system and prerequisite management
    """
    SUBJECT_TYPES = (
        ('core', _('Core Subject')),
        ('elective', _('Elective')),
        ('language', _('Language')),
        ('science', _('Science')),
        ('mathematics', _('Mathematics')),
        ('arts', _('Arts')),
        ('physical_education', _('Physical Education')),
        ('religious', _('Religious Studies')),
    )

    name = models.CharField(
        max_length=100,
        verbose_name=_('Subject Name')
    )

    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Subject Name (Arabic)')
    )

    code = models.CharField(
        max_length=20,
        verbose_name=_('Subject Code')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    subject_type = models.CharField(
        max_length=20,
        choices=SUBJECT_TYPES,
        default='core',
        verbose_name=_('Subject Type')
    )

    grades = models.ManyToManyField(
        Grade,
        related_name='subjects',
        verbose_name=_('Grades')
    )

    is_mandatory = models.BooleanField(
        default=True,
        verbose_name=_('Is Mandatory')
    )

    credit_hours = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(10)],
        verbose_name=_('Credit Hours'),
        help_text=_('Number of credit hours for this subject (1-10)')
    )

    weekly_hours = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(20)],
        verbose_name=_('Weekly Hours'),
        help_text=_('Number of hours per week for this subject')
    )

    prerequisites = models.ManyToManyField(
        'self',
        blank=True,
        symmetrical=False,
        related_name='dependent_subjects',
        verbose_name=_('Prerequisites'),
        help_text=_('Subjects that must be completed before taking this subject')
    )

    corequisites = models.ManyToManyField(
        'self',
        blank=True,
        symmetrical=False,
        related_name='corequisite_subjects',
        verbose_name=_('Corequisites'),
        help_text=_('Subjects that must be taken simultaneously with this subject')
    )

    minimum_grade_required = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=60.00,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name=_('Minimum Grade Required'),
        help_text=_('Minimum grade percentage required to pass this subject')
    )

    max_students_per_class = models.PositiveIntegerField(
        default=30,
        validators=[MinValueValidator(1), MaxValueValidator(100)],
        verbose_name=_('Maximum Students per Class'),
        help_text=_('Maximum number of students allowed in a class for this subject')
    )

    requires_lab = models.BooleanField(
        default=False,
        verbose_name=_('Requires Laboratory'),
        help_text=_('Whether this subject requires laboratory facilities')
    )

    requires_special_equipment = models.BooleanField(
        default=False,
        verbose_name=_('Requires Special Equipment'),
        help_text=_('Whether this subject requires special equipment or facilities')
    )

    equipment_requirements = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Equipment Requirements'),
        help_text=_('List of special equipment or facilities required')
    )

    learning_objectives = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Learning Objectives'),
        help_text=_('Key learning objectives for this subject')
    )

    assessment_methods = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Assessment Methods'),
        help_text=_('Methods used to assess student performance in this subject')
    )

    textbook_required = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Required Textbook'),
        help_text=_('Required textbook for this subject')
    )

    reference_materials = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Reference Materials'),
        help_text=_('Additional reference materials for this subject')
    )

    class Meta:
        verbose_name = _('Subject')
        verbose_name_plural = _('Subjects')
        ordering = ['name']
        unique_together = ['school', 'code']
        indexes = [
            models.Index(fields=['school', 'subject_type']),
            models.Index(fields=['school', 'is_mandatory']),
            models.Index(fields=['code']),
        ]

    def __str__(self):
        return f"{self.code} - {self.name}"

    def clean(self):
        super().clean()
        
        # Validate that a subject cannot be its own prerequisite
        if self.pk:
            if self.prerequisites.filter(pk=self.pk).exists():
                raise ValidationError(_('A subject cannot be its own prerequisite.'))
            
            if self.corequisites.filter(pk=self.pk).exists():
                raise ValidationError(_('A subject cannot be its own corequisite.'))
        
        # Validate credit hours vs weekly hours ratio
        if self.credit_hours and self.weekly_hours:
            if self.credit_hours > self.weekly_hours:
                raise ValidationError(
                    _('Credit hours cannot exceed weekly hours.')
                )

    def check_prerequisites_met(self, student, academic_year=None):
        """
        Check if a student has met all prerequisites for this subject
        """
        if not self.prerequisites.exists():
            return True, []
        
        # Get all prerequisite subjects
        prerequisite_subjects = self.prerequisites.all()
        unmet_prerequisites = []
        
        # Check if student has passed all prerequisites
        for prereq in prerequisite_subjects:
            # Check if student has a passing grade in the prerequisite subject
            passing_grade = StudentGrade.objects.filter(
                student=student,
                exam__class_subject__subject=prereq,
                percentage__gte=prereq.minimum_grade_required
            ).exists()
            
            if not passing_grade:
                unmet_prerequisites.append(prereq)
        
        return len(unmet_prerequisites) == 0, unmet_prerequisites

    def check_corequisites_available(self, student, academic_year=None):
        """
        Check if all corequisites are available for enrollment
        """
        if not self.corequisites.exists():
            return True, []
        
        unavailable_corequisites = []
        for coreq in self.corequisites.all():
            # Check if corequisite is offered in the same academic year
            if academic_year:
                if not ClassSubject.objects.filter(
                    subject=coreq,
                    academic_year=academic_year
                ).exists():
                    unavailable_corequisites.append(coreq)
        
        return len(unavailable_corequisites) == 0, unavailable_corequisites

    def get_prerequisite_chain(self):
        """
        Get the complete chain of prerequisites for this subject
        """
        chain = []
        visited = set()
        
        def _get_chain(subject):
            if subject.id in visited:
                return  # Avoid circular dependencies
            
            visited.add(subject.id)
            prerequisites = subject.prerequisites.all()
            
            for prereq in prerequisites:
                chain.append(prereq)
                _get_chain(prereq)
        
        _get_chain(self)
        return list(set(chain))  # Remove duplicates

    def detect_circular_prerequisites(self):
        """
        Detect circular dependencies in prerequisites
        """
        visited = set()
        rec_stack = set()
        
        def _has_cycle(subject):
            visited.add(subject.id)
            rec_stack.add(subject.id)
            
            for prereq in subject.prerequisites.all():
                if prereq.id not in visited:
                    if _has_cycle(prereq):
                        return True
                elif prereq.id in rec_stack:
                    return True
            
            rec_stack.remove(subject.id)
            return False
        
        return _has_cycle(self)

    def get_available_capacity(self, academic_year=None):
        """
        Get available capacity for this subject across all classes
        """
        if not academic_year:
            from core.models import AcademicYear
            academic_year = AcademicYear.objects.filter(
                school=self.school,
                is_current=True
            ).first()
        
        if not academic_year:
            return 0
        
        total_capacity = 0
        class_subjects = ClassSubject.objects.filter(
            subject=self,
            academic_year=academic_year
        )
        
        for class_subject in class_subjects:
            capacity = min(
                self.max_students_per_class,
                class_subject.class_obj.max_students
            )
            current_enrollment = class_subject.class_obj.students.filter(
                is_active=True
            ).count()
            total_capacity += max(0, capacity - current_enrollment)
        
        return total_capacity

    def get_difficulty_level(self):
        """
        Calculate difficulty level based on prerequisites and credit hours
        """
        prerequisite_count = self.prerequisites.count()
        if prerequisite_count == 0:
            return 'Beginner'
        elif prerequisite_count <= 2 and self.credit_hours <= 3:
            return 'Intermediate'
        elif prerequisite_count <= 4 and self.credit_hours <= 6:
            return 'Advanced'
        else:
            return 'Expert'

    def get_workload_estimate(self):
        """
        Estimate weekly workload in hours (including study time)
        """
        # Rule of thumb: 2-3 hours of study per credit hour
        study_multiplier = 2.5
        return self.credit_hours * study_multiplier + self.weekly_hours


class GradeCapacityManagement(BaseModel):
    """
    Grade capacity management model for tracking enrollment limits and availability
    """
    grade = models.OneToOneField(
        Grade,
        on_delete=models.CASCADE,
        related_name='capacity_management',
        verbose_name=_('Grade')
    )

    academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        related_name='grade_capacities',
        verbose_name=_('Academic Year')
    )

    total_capacity = models.PositiveIntegerField(
        verbose_name=_('Total Capacity'),
        help_text=_('Maximum number of students that can be enrolled in this grade')
    )

    current_enrollment = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Current Enrollment'),
        help_text=_('Current number of enrolled students')
    )

    waiting_list_capacity = models.PositiveIntegerField(
        default=10,
        verbose_name=_('Waiting List Capacity'),
        help_text=_('Maximum number of students allowed on waiting list')
    )

    enrollment_start_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Enrollment Start Date')
    )

    enrollment_end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Enrollment End Date')
    )

    is_enrollment_open = models.BooleanField(
        default=True,
        verbose_name=_('Is Enrollment Open')
    )

    minimum_class_size = models.PositiveIntegerField(
        default=15,
        verbose_name=_('Minimum Class Size'),
        help_text=_('Minimum number of students required to form a class')
    )

    maximum_class_size = models.PositiveIntegerField(
        default=30,
        verbose_name=_('Maximum Class Size'),
        help_text=_('Maximum number of students allowed per class section')
    )

    auto_create_sections = models.BooleanField(
        default=True,
        verbose_name=_('Auto Create Sections'),
        help_text=_('Automatically create new sections when capacity is reached')
    )

    class Meta:
        verbose_name = _('Grade Capacity Management')
        verbose_name_plural = _('Grade Capacity Management')
        unique_together = ['grade', 'academic_year']
        indexes = [
            models.Index(fields=['school', 'academic_year']),
            models.Index(fields=['grade', 'is_enrollment_open']),
        ]

    def __str__(self):
        return f"{self.grade.name} - {self.academic_year.name} Capacity"

    def clean(self):
        super().clean()
        
        if self.minimum_class_size > self.maximum_class_size:
            raise ValidationError(
                _('Minimum class size cannot be greater than maximum class size.')
            )
        
        if self.enrollment_start_date and self.enrollment_end_date:
            if self.enrollment_start_date >= self.enrollment_end_date:
                raise ValidationError(
                    _('Enrollment start date must be before end date.')
                )

    @property
    def available_capacity(self):
        """Get available capacity for enrollment"""
        total = self.total_capacity if self.total_capacity is not None else 0
        current = self.current_enrollment if self.current_enrollment is not None else 0
        return max(0, total - current)

    @property
    def enrollment_percentage(self):
        """Get enrollment percentage"""
        total = self.total_capacity if self.total_capacity is not None else 0
        current = self.current_enrollment if self.current_enrollment is not None else 0
        if total == 0:
            return 0
        return (current / total) * 100

    @property
    def is_full(self):
        """Check if grade is at full capacity"""
        total = self.total_capacity if self.total_capacity is not None else 0
        current = self.current_enrollment if self.current_enrollment is not None else 0
        return current >= total and total > 0

    @property
    def needs_new_section(self):
        """Check if a new section should be created"""
        if not self.auto_create_sections:
            return False
        
        # Check if any existing class is at maximum capacity
        classes = Class.objects.filter(
            grade=self.grade,
            academic_year=self.academic_year
        )
        
        for class_obj in classes:
            if class_obj.current_students_count < self.maximum_class_size:
                return False
        
        return self.available_capacity > 0

    def update_enrollment_count(self):
        """Update current enrollment count based on active students"""
        from students.models import Student
        
        self.current_enrollment = Student.objects.filter(
            current_class__grade=self.grade,
            current_class__academic_year=self.academic_year,
            is_active=True
        ).count()
        
        self.save(update_fields=['current_enrollment'])

    def can_enroll_student(self):
        """Check if a student can be enrolled"""
        if not self.is_enrollment_open:
            return False, _('Enrollment is closed for this grade.')
        
        if self.is_full:
            return False, _('Grade is at full capacity.')
        
        from datetime import date
        today = date.today()
        
        if self.enrollment_start_date and today < self.enrollment_start_date:
            return False, _('Enrollment has not started yet.')
        
        if self.enrollment_end_date and today > self.enrollment_end_date:
            return False, _('Enrollment period has ended.')
        
        return True, _('Student can be enrolled.')

    def get_suggested_sections_count(self):
        """Get suggested number of sections based on enrollment"""
        if self.current_enrollment == 0:
            return 1
        
        return max(1, (self.current_enrollment + self.maximum_class_size - 1) // self.maximum_class_size)

    def create_new_section(self, section_name=None):
        """Create a new class section for this grade"""
        if not section_name:
            existing_sections = Class.objects.filter(
                grade=self.grade,
                academic_year=self.academic_year
            ).count()
            section_name = f"Section {existing_sections + 1}"
        
        new_class = Class.objects.create(
            name=section_name,
            grade=self.grade,
            academic_year=self.academic_year,
            max_students=self.maximum_class_size,
            school=self.school
        )
        
        return new_class


class Teacher(BaseModel):
    """
    Teacher model
    """
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='teacher_profile',
        limit_choices_to={'user_type': 'teacher'},
        verbose_name=_('User Account')
    )

    employee_id = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Employee ID')
    )

    subjects = models.ManyToManyField(
        Subject,
        related_name='teachers',
        verbose_name=_('Subjects')
    )

    hire_date = models.DateField(
        verbose_name=_('Hire Date')
    )

    qualification = models.CharField(
        max_length=200,
        verbose_name=_('Qualification')
    )

    experience_years = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Experience Years')
    )

    salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name=_('Salary')
    )

    department = models.ForeignKey(
        'hr.Department',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='teachers',
        verbose_name=_('Department')
    )

    class Meta:
        verbose_name = _('Teacher')
        verbose_name_plural = _('Teachers')
        ordering = ['user__first_name', 'user__last_name']

    def __str__(self):
        return f"{self.user.first_name} {self.user.last_name} ({self.employee_id})"


class ClassSubject(BaseModel):
    """
    Enhanced Class-Subject assignment model with capacity and resource management
    """
    class_obj = models.ForeignKey(
        Class,
        on_delete=models.CASCADE,
        related_name='class_subjects',
        verbose_name=_('Class')
    )

    subject = models.ForeignKey(
        Subject,
        on_delete=models.CASCADE,
        related_name='class_subjects',
        verbose_name=_('Subject')
    )

    teacher = models.ForeignKey(
        Teacher,
        on_delete=models.CASCADE,
        related_name='class_subjects',
        verbose_name=_('Primary Teacher')
    )

    assistant_teachers = models.ManyToManyField(
        Teacher,
        blank=True,
        related_name='assisted_class_subjects',
        verbose_name=_('Assistant Teachers'),
        help_text=_('Additional teachers assisting with this subject')
    )

    academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        related_name='class_subjects',
        verbose_name=_('Academic Year')
    )

    semester = models.ForeignKey(
        Semester,
        on_delete=models.CASCADE,
        related_name='class_subjects',
        verbose_name=_('Semester')
    )

    weekly_hours = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(20)],
        verbose_name=_('Weekly Hours')
    )

    max_students = models.PositiveIntegerField(
        null=True,
        blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(100)],
        verbose_name=_('Maximum Students'),
        help_text=_('Override default subject capacity for this class')
    )

    room_preference = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('Room Preference'),
        help_text=_('Preferred room for this subject')
    )

    requires_special_setup = models.BooleanField(
        default=False,
        verbose_name=_('Requires Special Setup'),
        help_text=_('Whether this class requires special room setup')
    )

    setup_requirements = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Setup Requirements'),
        help_text=_('Special setup requirements for this class')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )

    start_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Start Date')
    )

    end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('End Date')
    )

    grading_scale = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Grading Scale'),
        help_text=_('Custom grading scale for this class-subject combination')
    )

    class Meta:
        verbose_name = _('Class Subject')
        verbose_name_plural = _('Class Subjects')
        unique_together = ['class_obj', 'subject', 'academic_year', 'semester']
        ordering = ['class_obj', 'subject']
        indexes = [
            models.Index(fields=['school', 'academic_year', 'semester']),
            models.Index(fields=['teacher', 'academic_year']),
            models.Index(fields=['subject', 'is_active']),
        ]

    def __str__(self):
        return f"{self.class_obj} - {self.subject} ({self.teacher})"

    def clean(self):
        super().clean()
        
        # Validate teacher is qualified for the subject
        if self.teacher and not self.teacher.subjects.filter(pk=self.subject.pk).exists():
            raise ValidationError(
                _('Teacher {} is not qualified to teach {}.').format(
                    self.teacher, self.subject
                )
            )
        
        # Validate semester belongs to academic year
        if self.semester and self.academic_year:
            if self.semester.academic_year != self.academic_year:
                raise ValidationError(
                    _('Semester must belong to the selected academic year.')
                )
        
        # Validate date range
        if self.start_date and self.end_date:
            if self.start_date >= self.end_date:
                raise ValidationError(
                    _('Start date must be before end date.')
                )
        
        # Validate max students doesn't exceed subject or class limits
        if self.max_students:
            if self.max_students > self.subject.max_students_per_class:
                raise ValidationError(
                    _('Maximum students cannot exceed subject limit of {}.').format(
                        self.subject.max_students_per_class
                    )
                )
            
            if self.max_students > self.class_obj.max_students:
                raise ValidationError(
                    _('Maximum students cannot exceed class limit of {}.').format(
                        self.class_obj.max_students
                    )
                )

    @property
    def effective_max_students(self):
        """Get effective maximum students for this class-subject"""
        limits = [self.class_obj.max_students, self.subject.max_students_per_class]
        
        if self.max_students:
            limits.append(self.max_students)
        
        return min(limits)

    @property
    def current_enrollment(self):
        """Get current number of enrolled students"""
        return self.class_obj.students.filter(is_active=True).count()

    @property
    def available_capacity(self):
        """Get available capacity for enrollment"""
        return max(0, self.effective_max_students - self.current_enrollment)

    @property
    def is_full(self):
        """Check if class-subject is at capacity"""
        return self.current_enrollment >= self.effective_max_students

    def get_teacher_workload(self):
        """Calculate teacher's total weekly hours for this assignment"""
        base_hours = self.weekly_hours
        
        # Add preparation time (typically 50% of teaching time)
        preparation_hours = base_hours * 0.5
        
        # Add grading time (varies by class size)
        grading_hours = (self.current_enrollment / 10) * 0.5
        
        return base_hours + preparation_hours + grading_hours

    def check_schedule_conflicts(self):
        """Check for scheduling conflicts with teacher's other assignments"""
        conflicts = []
        
        # Get all schedules for this class-subject
        schedules = self.schedules.all()
        
        # Check conflicts with teacher's other schedules
        for schedule in schedules:
            conflicting_schedules = Schedule.objects.filter(
                class_subject__teacher=self.teacher,
                day_of_week=schedule.day_of_week,
                start_time__lt=schedule.end_time,
                end_time__gt=schedule.start_time
            ).exclude(class_subject=self)
            
            conflicts.extend(conflicting_schedules)
        
        return conflicts

    def get_resource_requirements(self):
        """Get all resource requirements for this class-subject"""
        requirements = {
            'room_type': 'regular',
            'special_equipment': [],
            'setup_time': 0,
            'capacity': self.effective_max_students
        }
        
        if self.subject.requires_lab:
            requirements['room_type'] = 'laboratory'
        
        if self.subject.requires_special_equipment:
            requirements['special_equipment'] = [
                self.subject.equipment_requirements
            ]
        
        if self.requires_special_setup:
            requirements['setup_time'] = 15  # minutes
            requirements['special_equipment'].append(self.setup_requirements)
        
        return requirements

    def calculate_grade_distribution(self):
        """Calculate grade distribution for this class-subject"""
        from django.db.models import Count, Avg
        
        grades = StudentGrade.objects.filter(
            exam__class_subject=self
        ).values('grade_letter').annotate(
            count=Count('id')
        ).order_by('grade_letter')
        
        total_students = sum(grade['count'] for grade in grades)
        
        distribution = {}
        for grade in grades:
            percentage = (grade['count'] / total_students * 100) if total_students > 0 else 0
            distribution[grade['grade_letter']] = {
                'count': grade['count'],
                'percentage': round(percentage, 2)
            }
        
        return distribution

    def get_attendance_summary(self):
        """Get attendance summary for this class-subject"""
        from django.db.models import Count, Q
        
        attendance_data = StudentAttendance.objects.filter(
            class_subject=self
        ).aggregate(
            total_sessions=Count('id'),
            present_count=Count('id', filter=Q(status='present')),
            absent_count=Count('id', filter=Q(status='absent')),
            late_count=Count('id', filter=Q(status='late')),
            excused_count=Count('id', filter=Q(status='excused'))
        )
        
        total = attendance_data['total_sessions'] or 1
        
        return {
            'total_sessions': attendance_data['total_sessions'],
            'attendance_rate': round((attendance_data['present_count'] / total) * 100, 2),
            'absence_rate': round((attendance_data['absent_count'] / total) * 100, 2),
            'late_rate': round((attendance_data['late_count'] / total) * 100, 2),
            'excused_rate': round((attendance_data['excused_count'] / total) * 100, 2)
        }


# Room and Resource Management Models

class Room(BaseModel):
    """
    Room model for managing classroom resources and allocation
    """
    ROOM_TYPES = (
        ('classroom', _('Regular Classroom')),
        ('laboratory', _('Laboratory')),
        ('computer_lab', _('Computer Laboratory')),
        ('library', _('Library')),
        ('auditorium', _('Auditorium')),
        ('gymnasium', _('Gymnasium')),
        ('art_room', _('Art Room')),
        ('music_room', _('Music Room')),
        ('conference', _('Conference Room')),
    )

    name = models.CharField(
        max_length=50,
        verbose_name=_('Room Name')
    )

    room_number = models.CharField(
        max_length=20,
        verbose_name=_('Room Number')
    )

    room_type = models.CharField(
        max_length=20,
        choices=ROOM_TYPES,
        default='classroom',
        verbose_name=_('Room Type')
    )

    capacity = models.PositiveIntegerField(
        verbose_name=_('Capacity'),
        help_text=_('Maximum number of students this room can accommodate')
    )

    floor = models.CharField(
        max_length=10,
        blank=True,
        null=True,
        verbose_name=_('Floor')
    )

    building = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('Building')
    )

    equipment = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Available Equipment'),
        help_text=_('List of equipment available in this room')
    )

    has_projector = models.BooleanField(
        default=False,
        verbose_name=_('Has Projector')
    )

    has_computer = models.BooleanField(
        default=False,
        verbose_name=_('Has Computer')
    )

    has_internet = models.BooleanField(
        default=True,
        verbose_name=_('Has Internet')
    )

    has_whiteboard = models.BooleanField(
        default=True,
        verbose_name=_('Has Whiteboard')
    )

    is_accessible = models.BooleanField(
        default=True,
        verbose_name=_('Wheelchair Accessible')
    )

    is_available = models.BooleanField(
        default=True,
        verbose_name=_('Is Available'),
        help_text=_('Whether this room is available for scheduling')
    )

    maintenance_notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Maintenance Notes')
    )

    class Meta:
        verbose_name = _('Room')
        verbose_name_plural = _('Rooms')
        ordering = ['building', 'floor', 'room_number']
        unique_together = ['school', 'room_number']
        indexes = [
            models.Index(fields=['school', 'room_type']),
            models.Index(fields=['school', 'is_available']),
        ]

    def __str__(self):
        return f"{self.room_number} - {self.name}"

    def is_suitable_for_subject(self, subject):
        """Check if room is suitable for a specific subject"""
        if subject.requires_lab and self.room_type not in ['laboratory', 'computer_lab']:
            return False
        
        if subject.requires_special_equipment:
            # Check if room has required equipment
            if 'projector' in subject.equipment_requirements.lower() and not self.has_projector:
                return False
            if 'computer' in subject.equipment_requirements.lower() and not self.has_computer:
                return False
        
        return True

    def get_availability_for_day(self, day_of_week, academic_year=None):
        """Get room availability for a specific day"""
        if not academic_year:
            from core.models import AcademicYear
            academic_year = AcademicYear.objects.filter(
                school=self.school,
                is_current=True
            ).first()
        
        if not academic_year:
            return []
        
        # Get all scheduled times for this room on this day
        scheduled_times = Schedule.objects.filter(
            room=self,
            day_of_week=day_of_week,
            class_subject__academic_year=academic_year
        ).values_list('start_time', 'end_time')
        
        return scheduled_times


class TeacherAvailability(BaseModel):
    """
    Teacher availability model for managing when teachers are available to teach
    """
    DAYS_OF_WEEK = (
        ('monday', _('Monday')),
        ('tuesday', _('Tuesday')),
        ('wednesday', _('Wednesday')),
        ('thursday', _('Thursday')),
        ('friday', _('Friday')),
        ('saturday', _('Saturday')),
        ('sunday', _('Sunday')),
    )

    teacher = models.ForeignKey(
        Teacher,
        on_delete=models.CASCADE,
        related_name='availability_slots',
        verbose_name=_('Teacher')
    )

    day_of_week = models.CharField(
        max_length=10,
        choices=DAYS_OF_WEEK,
        verbose_name=_('Day of Week')
    )

    start_time = models.TimeField(
        verbose_name=_('Available From')
    )

    end_time = models.TimeField(
        verbose_name=_('Available Until')
    )

    is_preferred = models.BooleanField(
        default=False,
        verbose_name=_('Preferred Time'),
        help_text=_('Whether this is a preferred time slot for the teacher')
    )

    max_consecutive_hours = models.PositiveIntegerField(
        default=4,
        verbose_name=_('Max Consecutive Hours'),
        help_text=_('Maximum consecutive hours teacher can teach')
    )

    break_duration = models.PositiveIntegerField(
        default=15,
        verbose_name=_('Required Break (minutes)'),
        help_text=_('Minimum break time required between classes')
    )

    academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        related_name='teacher_availabilities',
        verbose_name=_('Academic Year')
    )

    class Meta:
        verbose_name = _('Teacher Availability')
        verbose_name_plural = _('Teacher Availabilities')
        unique_together = ['teacher', 'day_of_week', 'start_time', 'academic_year']
        ordering = ['teacher', 'day_of_week', 'start_time']
        indexes = [
            models.Index(fields=['school', 'academic_year']),
            models.Index(fields=['teacher', 'day_of_week']),
        ]

    def __str__(self):
        return f"{self.teacher} - {self.get_day_of_week_display()} {self.start_time}-{self.end_time}"

    def clean(self):
        super().clean()
        
        if self.start_time >= self.end_time:
            raise ValidationError(
                _('Start time must be before end time.')
            )

    def conflicts_with_schedule(self, start_time, end_time):
        """Check if a proposed schedule conflicts with this availability"""
        return not (end_time <= self.start_time or start_time >= self.end_time)

    def get_available_duration_minutes(self):
        """Get available duration in minutes"""
        from datetime import datetime, timedelta
        
        start_dt = datetime.combine(datetime.today(), self.start_time)
        end_dt = datetime.combine(datetime.today(), self.end_time)
        
        return int((end_dt - start_dt).total_seconds() / 60)


class TimetableTemplate(BaseModel):
    """
    Timetable template for defining school's time structure
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('Template Name')
    )

    academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        related_name='timetable_templates',
        verbose_name=_('Academic Year')
    )

    periods_per_day = models.PositiveIntegerField(
        default=8,
        verbose_name=_('Periods per Day')
    )

    period_duration = models.PositiveIntegerField(
        default=45,
        verbose_name=_('Period Duration (minutes)')
    )

    break_duration = models.PositiveIntegerField(
        default=15,
        verbose_name=_('Break Duration (minutes)')
    )

    lunch_break_duration = models.PositiveIntegerField(
        default=45,
        verbose_name=_('Lunch Break Duration (minutes)')
    )

    school_start_time = models.TimeField(
        verbose_name=_('School Start Time')
    )

    school_end_time = models.TimeField(
        verbose_name=_('School End Time')
    )

    working_days = models.JSONField(
        default=list,
        verbose_name=_('Working Days'),
        help_text=_('List of working days (e.g., ["monday", "tuesday", ...])')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )

    class Meta:
        verbose_name = _('Timetable Template')
        verbose_name_plural = _('Timetable Templates')
        unique_together = ['school', 'name', 'academic_year']
        ordering = ['name']

    def __str__(self):
        return f"{self.name} - {self.academic_year}"

    def generate_time_slots(self):
        """Generate time slots based on template configuration"""
        from datetime import datetime, timedelta
        
        slots = []
        current_time = datetime.combine(datetime.today(), self.school_start_time)
        period_delta = timedelta(minutes=self.period_duration)
        break_delta = timedelta(minutes=self.break_duration)
        lunch_delta = timedelta(minutes=self.lunch_break_duration)
        
        for period in range(1, self.periods_per_day + 1):
            start_time = current_time.time()
            end_time = (current_time + period_delta).time()
            
            slots.append({
                'period': period,
                'start_time': start_time,
                'end_time': end_time,
                'is_break': False
            })
            
            current_time += period_delta
            
            # Add break after certain periods
            if period == 2:  # Short break after 2nd period
                current_time += break_delta
            elif period == 4:  # Lunch break after 4th period
                current_time += lunch_delta
            elif period == 6:  # Short break after 6th period
                current_time += break_delta
        
        return slots


class Schedule(BaseModel):
    """
    Enhanced Class schedule model with resource management and conflict detection
    """
    DAYS_OF_WEEK = (
        ('monday', _('Monday')),
        ('tuesday', _('Tuesday')),
        ('wednesday', _('Wednesday')),
        ('thursday', _('Thursday')),
        ('friday', _('Friday')),
        ('saturday', _('Saturday')),
        ('sunday', _('Sunday')),
    )

    SCHEDULE_STATUS = (
        ('active', _('Active')),
        ('cancelled', _('Cancelled')),
        ('rescheduled', _('Rescheduled')),
        ('completed', _('Completed')),
    )

    class_subject = models.ForeignKey(
        ClassSubject,
        on_delete=models.CASCADE,
        related_name='schedules',
        verbose_name=_('Class Subject')
    )

    day_of_week = models.CharField(
        max_length=10,
        choices=DAYS_OF_WEEK,
        verbose_name=_('Day of Week')
    )

    start_time = models.TimeField(
        verbose_name=_('Start Time')
    )

    end_time = models.TimeField(
        verbose_name=_('End Time')
    )

    room = models.ForeignKey(
        Room,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='schedules',
        verbose_name=_('Room')
    )

    room_number = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('Room Number'),
        help_text=_('Alternative room identifier if Room model not used')
    )

    period_number = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name=_('Period Number'),
        help_text=_('Period number in the daily schedule')
    )

    status = models.CharField(
        max_length=15,
        choices=SCHEDULE_STATUS,
        default='active',
        verbose_name=_('Status')
    )

    is_recurring = models.BooleanField(
        default=True,
        verbose_name=_('Is Recurring'),
        help_text=_('Whether this schedule repeats weekly')
    )

    effective_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Effective Date'),
        help_text=_('Date from which this schedule is effective')
    )

    end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('End Date'),
        help_text=_('Date until which this schedule is effective')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    class Meta:
        verbose_name = _('Schedule')
        verbose_name_plural = _('Schedules')
        ordering = ['day_of_week', 'start_time']
        unique_together = ['class_subject', 'day_of_week', 'start_time']
        indexes = [
            models.Index(fields=['school', 'day_of_week', 'start_time']),
            models.Index(fields=['class_subject', 'status']),
            models.Index(fields=['room', 'day_of_week']),
        ]

    def __str__(self):
        return f"{self.class_subject} - {self.get_day_of_week_display()} {self.start_time}"

    def clean(self):
        super().clean()
        
        if self.start_time >= self.end_time:
            raise ValidationError(
                _('Start time must be before end time.')
            )
        
        if self.effective_date and self.end_date:
            if self.effective_date >= self.end_date:
                raise ValidationError(
                    _('Effective date must be before end date.')
                )

    def get_duration_minutes(self):
        """Get schedule duration in minutes"""
        from datetime import datetime, timedelta
        
        start_dt = datetime.combine(datetime.today(), self.start_time)
        end_dt = datetime.combine(datetime.today(), self.end_time)
        
        return int((end_dt - start_dt).total_seconds() / 60)

    def check_teacher_conflicts(self):
        """Check for teacher scheduling conflicts"""
        teacher = self.class_subject.teacher
        
        conflicts = Schedule.objects.filter(
            class_subject__teacher=teacher,
            day_of_week=self.day_of_week,
            start_time__lt=self.end_time,
            end_time__gt=self.start_time,
            status='active'
        ).exclude(pk=self.pk)
        
        return list(conflicts)

    def check_room_conflicts(self):
        """Check for room scheduling conflicts"""
        if not self.room:
            return []
        
        conflicts = Schedule.objects.filter(
            room=self.room,
            day_of_week=self.day_of_week,
            start_time__lt=self.end_time,
            end_time__gt=self.start_time,
            status='active'
        ).exclude(pk=self.pk)
        
        return list(conflicts)

    def check_class_conflicts(self):
        """Check for class scheduling conflicts"""
        class_obj = self.class_subject.class_obj
        
        conflicts = Schedule.objects.filter(
            class_subject__class_obj=class_obj,
            day_of_week=self.day_of_week,
            start_time__lt=self.end_time,
            end_time__gt=self.start_time,
            status='active'
        ).exclude(pk=self.pk)
        
        return list(conflicts)

    def get_all_conflicts(self):
        """Get all types of conflicts for this schedule"""
        conflicts = {
            'teacher': self.check_teacher_conflicts(),
            'room': self.check_room_conflicts(),
            'class': self.check_class_conflicts()
        }
        
        return conflicts

    def is_teacher_available(self):
        """Check if teacher is available at this time"""
        teacher = self.class_subject.teacher
        
        availability = TeacherAvailability.objects.filter(
            teacher=teacher,
            day_of_week=self.day_of_week,
            start_time__lte=self.start_time,
            end_time__gte=self.end_time,
            academic_year=self.class_subject.academic_year
        ).exists()
        
        return availability

    def is_room_suitable(self):
        """Check if assigned room is suitable for the subject"""
        if not self.room:
            return True  # No room assigned, assume suitable
        
        return self.room.is_suitable_for_subject(self.class_subject.subject)

    def validate_schedule(self):
        """Comprehensive schedule validation"""
        errors = []
        
        # Check conflicts
        conflicts = self.get_all_conflicts()
        if conflicts['teacher']:
            errors.append(_('Teacher has conflicting schedules'))
        if conflicts['room']:
            errors.append(_('Room has conflicting schedules'))
        if conflicts['class']:
            errors.append(_('Class has conflicting schedules'))
        
        # Check teacher availability
        if not self.is_teacher_available():
            errors.append(_('Teacher is not available at this time'))
        
        # Check room suitability
        if not self.is_room_suitable():
            errors.append(_('Room is not suitable for this subject'))
        
        # Check room capacity
        if self.room and self.room.capacity < self.class_subject.effective_max_students:
            errors.append(_('Room capacity is insufficient for class size'))
        
        return errors


class Exam(BaseModel):
    """
    Exam model
    """
    EXAM_TYPES = (
        ('quiz', _('Quiz')),
        ('midterm', _('Midterm')),
        ('final', _('Final')),
        ('assignment', _('Assignment')),
        ('project', _('Project')),
        ('oral', _('Oral Exam')),
    )

    name = models.CharField(
        max_length=200,
        verbose_name=_('Exam Name')
    )

    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Exam Name (Arabic)')
    )

    class_subject = models.ForeignKey(
        ClassSubject,
        on_delete=models.CASCADE,
        related_name='exams',
        verbose_name=_('Class Subject')
    )

    exam_type = models.CharField(
        max_length=20,
        choices=EXAM_TYPES,
        verbose_name=_('Exam Type')
    )

    exam_date = models.DateField(
        verbose_name=_('Exam Date')
    )

    start_time = models.TimeField(
        verbose_name=_('Start Time')
    )

    end_time = models.TimeField(
        verbose_name=_('End Time')
    )

    duration_minutes = models.PositiveIntegerField(
        verbose_name=_('Duration (Minutes)')
    )

    total_marks = models.PositiveIntegerField(
        verbose_name=_('Total Marks')
    )

    passing_marks = models.PositiveIntegerField(
        verbose_name=_('Passing Marks')
    )

    room = models.ForeignKey(
        Room,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='exams',
        verbose_name=_('Room')
    )

    exam_session = models.ForeignKey(
        'ExamSession',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='exams',
        verbose_name=_('Exam Session')
    )

    instructions = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Instructions')
    )

    is_published = models.BooleanField(
        default=False,
        verbose_name=_('Is Published')
    )

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_exams',
        verbose_name=_('Created By')
    )

    class Meta:
        verbose_name = _('Exam')
        verbose_name_plural = _('Exams')
        ordering = ['-exam_date', '-start_time']

    def __str__(self):
        return f"{self.name} - {self.class_subject}"

    @property
    def is_completed(self):
        from datetime import datetime, date
        if self.exam_date is None or self.end_time is None:
            return False
        exam_datetime = datetime.combine(self.exam_date, self.end_time)
        return exam_datetime < datetime.now()


class StudentGrade(BaseModel):
    """
    Student grade/assessment model
    """
    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='academic_grades',
        verbose_name=_('Student')
    )

    exam = models.ForeignKey(
        Exam,
        on_delete=models.CASCADE,
        related_name='student_grades',
        verbose_name=_('Exam')
    )

    marks_obtained = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('Marks Obtained')
    )

    grade_letter = models.CharField(
        max_length=2,
        blank=True,
        null=True,
        verbose_name=_('Grade Letter')
    )

    percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name=_('Percentage')
    )

    remarks = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Remarks')
    )

    graded_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='graded_assessments',
        verbose_name=_('Graded By')
    )

    graded_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Graded At')
    )

    class Meta:
        verbose_name = _('Student Grade')
        verbose_name_plural = _('Student Grades')
        unique_together = ['student', 'exam']
        ordering = ['-graded_at']

    def __str__(self):
        return f"{self.student.full_name} - {self.exam.name} ({self.marks_obtained}/{self.exam.total_marks})"

    def save(self, *args, **kwargs):
        # Auto-calculate percentage
        if self.marks_obtained is not None and self.exam.total_marks > 0:
            self.percentage = (self.marks_obtained / self.exam.total_marks) * 100

            # Get grading scale from class subject or use default
            grading_scale = self.get_grading_scale()
            self.grade_letter = self.calculate_grade_letter(self.percentage, grading_scale)

        super().save(*args, **kwargs)

    def get_grading_scale(self):
        """Get grading scale for this assessment"""
        # Try to get custom grading scale from class subject
        if hasattr(self.exam, 'class_subject') and self.exam.class_subject.grading_scale:
            return self.exam.class_subject.grading_scale
        
        # Use school's default grading scale
        school_scale = GradingScale.objects.filter(
            school=self.school,
            is_default=True
        ).first()
        
        if school_scale:
            return school_scale.scale_definition
        
        # Fallback to standard scale
        return {
            'A+': {'min': 90, 'max': 100, 'points': 4.0},
            'A': {'min': 85, 'max': 89, 'points': 3.7},
            'B+': {'min': 80, 'max': 84, 'points': 3.3},
            'B': {'min': 75, 'max': 79, 'points': 3.0},
            'C+': {'min': 70, 'max': 74, 'points': 2.7},
            'C': {'min': 65, 'max': 69, 'points': 2.3},
            'D': {'min': 60, 'max': 64, 'points': 2.0},
            'F': {'min': 0, 'max': 59, 'points': 0.0}
        }

    def calculate_grade_letter(self, percentage, grading_scale):
        """Calculate grade letter based on percentage and grading scale"""
        for grade, criteria in grading_scale.items():
            if criteria['min'] <= percentage <= criteria['max']:
                return grade
        return 'F'  # Default to F if no match

    def get_grade_points(self):
        """Get grade points for GPA calculation"""
        grading_scale = self.get_grading_scale()
        if self.grade_letter and self.grade_letter in grading_scale:
            return grading_scale[self.grade_letter]['points']
        return 0.0

    def is_passing_grade(self):
        """Check if this is a passing grade"""
        return self.get_grade_points() >= 2.0  # D or above

    def get_improvement_suggestions(self):
        """Get suggestions for grade improvement"""
        suggestions = []
        
        if self.percentage < 60:
            suggestions.append("Focus on fundamental concepts")
            suggestions.append("Seek additional help from teacher")
            suggestions.append("Form study groups with classmates")
        elif self.percentage < 75:
            suggestions.append("Review missed topics")
            suggestions.append("Practice more exercises")
            suggestions.append("Attend extra help sessions")
        elif self.percentage < 85:
            suggestions.append("Focus on advanced problem solving")
            suggestions.append("Participate more in class discussions")
        
        return suggestions


class GradingScale(BaseModel):
    """
    Grading scale configuration for schools
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('Scale Name')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    scale_definition = models.JSONField(
        verbose_name=_('Scale Definition'),
        help_text=_('JSON definition of grading scale with grade letters, ranges, and points')
    )

    is_default = models.BooleanField(
        default=False,
        verbose_name=_('Is Default Scale')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )

    class Meta:
        verbose_name = _('Grading Scale')
        verbose_name_plural = _('Grading Scales')
        ordering = ['name']
        indexes = [
            models.Index(fields=['school', 'is_default']),
            models.Index(fields=['school', 'is_active']),
        ]

    def __str__(self):
        return f"{self.name} ({'Default' if self.is_default else 'Custom'})"

    def clean(self):
        super().clean()
        
        # Validate scale definition structure
        if not isinstance(self.scale_definition, dict):
            raise ValidationError(_('Scale definition must be a dictionary'))
        
        for grade, criteria in self.scale_definition.items():
            if not isinstance(criteria, dict):
                raise ValidationError(f'Criteria for grade {grade} must be a dictionary')
            
            required_keys = ['min', 'max', 'points']
            for key in required_keys:
                if key not in criteria:
                    raise ValidationError(f'Grade {grade} missing required key: {key}')

    def save(self, *args, **kwargs):
        # Ensure only one default scale per school
        if self.is_default:
            GradingScale.objects.filter(
                school=self.school,
                is_default=True
            ).exclude(pk=self.pk).update(is_default=False)
        
        super().save(*args, **kwargs)


class StudentGPA(BaseModel):
    """
    Student GPA tracking and calculation
    """
    GPA_TYPES = (
        ('semester', _('Semester GPA')),
        ('cumulative', _('Cumulative GPA')),
        ('yearly', _('Yearly GPA')),
    )

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='gpa_records',
        verbose_name=_('Student')
    )

    academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        related_name='student_gpas',
        verbose_name=_('Academic Year')
    )

    semester = models.ForeignKey(
        Semester,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='student_gpas',
        verbose_name=_('Semester')
    )

    gpa_type = models.CharField(
        max_length=15,
        choices=GPA_TYPES,
        verbose_name=_('GPA Type')
    )

    gpa_value = models.DecimalField(
        max_digits=4,
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(4.00)],
        verbose_name=_('GPA Value')
    )

    total_credit_hours = models.PositiveIntegerField(
        verbose_name=_('Total Credit Hours')
    )

    earned_credit_hours = models.PositiveIntegerField(
        verbose_name=_('Earned Credit Hours')
    )

    quality_points = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        verbose_name=_('Quality Points')
    )

    class_rank = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name=_('Class Rank')
    )

    total_students = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name=_('Total Students in Class')
    )

    calculation_date = models.DateTimeField(
        auto_now=True,
        verbose_name=_('Calculation Date')
    )

    class Meta:
        verbose_name = _('Student GPA')
        verbose_name_plural = _('Student GPAs')
        unique_together = ['student', 'academic_year', 'semester', 'gpa_type']
        ordering = ['-calculation_date']
        indexes = [
            models.Index(fields=['school', 'academic_year', 'gpa_type']),
            models.Index(fields=['student', 'gpa_type']),
        ]

    def __str__(self):
        return f"{self.student.full_name} - {self.gpa_type} GPA: {self.gpa_value}"

    @property
    def gpa_letter_grade(self):
        """Convert GPA to letter grade"""
        if self.gpa_value >= 3.7:
            return 'A'
        elif self.gpa_value >= 3.3:
            return 'B+'
        elif self.gpa_value >= 3.0:
            return 'B'
        elif self.gpa_value >= 2.7:
            return 'C+'
        elif self.gpa_value >= 2.3:
            return 'C'
        elif self.gpa_value >= 2.0:
            return 'D'
        else:
            return 'F'

    @property
    def academic_standing(self):
        """Determine academic standing based on GPA"""
        if self.gpa_value >= 3.5:
            return 'Dean\'s List'
        elif self.gpa_value >= 3.0:
            return 'Good Standing'
        elif self.gpa_value >= 2.0:
            return 'Satisfactory'
        else:
            return 'Academic Probation'

    def calculate_percentile_rank(self):
        """Calculate percentile rank within class"""
        if not self.class_rank or not self.total_students:
            return None
        
        percentile = ((self.total_students - self.class_rank + 1) / self.total_students) * 100
        return round(percentile, 1)


class Transcript(BaseModel):
    """
    Student transcript generation and management
    """
    TRANSCRIPT_TYPES = (
        ('official', _('Official Transcript')),
        ('unofficial', _('Unofficial Transcript')),
        ('partial', _('Partial Transcript')),
        ('transfer', _('Transfer Transcript')),
    )

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='transcripts',
        verbose_name=_('Student')
    )

    transcript_type = models.CharField(
        max_length=15,
        choices=TRANSCRIPT_TYPES,
        default='unofficial',
        verbose_name=_('Transcript Type')
    )

    academic_year_start = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        related_name='transcript_starts',
        verbose_name=_('Start Academic Year')
    )

    academic_year_end = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        related_name='transcript_ends',
        verbose_name=_('End Academic Year')
    )

    generated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='generated_transcripts',
        verbose_name=_('Generated By')
    )

    generated_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Generated At')
    )

    is_sealed = models.BooleanField(
        default=False,
        verbose_name=_('Is Sealed'),
        help_text=_('Sealed transcripts cannot be modified')
    )

    seal_date = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Seal Date')
    )

    transcript_data = models.JSONField(
        verbose_name=_('Transcript Data'),
        help_text=_('Complete transcript data in JSON format')
    )

    cumulative_gpa = models.DecimalField(
        max_digits=4,
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(4.00)],
        verbose_name=_('Cumulative GPA')
    )

    total_credit_hours = models.PositiveIntegerField(
        verbose_name=_('Total Credit Hours')
    )

    class_rank = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name=_('Class Rank')
    )

    graduation_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Graduation Date')
    )

    class Meta:
        verbose_name = _('Transcript')
        verbose_name_plural = _('Transcripts')
        ordering = ['-generated_at']
        indexes = [
            models.Index(fields=['school', 'student']),
            models.Index(fields=['transcript_type', 'is_sealed']),
        ]

    def __str__(self):
        return f"{self.student.full_name} - {self.get_transcript_type_display()} Transcript"

    def seal_transcript(self, user):
        """Seal the transcript to prevent modifications"""
        if not self.is_sealed:
            self.is_sealed = True
            self.seal_date = timezone.now()
            self.save()

    def can_modify(self):
        """Check if transcript can be modified"""
        return not self.is_sealed

    def generate_transcript_data(self):
        """Generate complete transcript data"""
        # This will be implemented in the transcript generation utility
        pass


class GradeReport(BaseModel):
    """
    Grade report generation and management
    """
    REPORT_TYPES = (
        ('progress', _('Progress Report')),
        ('midterm', _('Midterm Report')),
        ('final', _('Final Report')),
        ('semester', _('Semester Report')),
        ('annual', _('Annual Report')),
    )

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='grade_reports',
        verbose_name=_('Student')
    )

    report_type = models.CharField(
        max_length=15,
        choices=REPORT_TYPES,
        verbose_name=_('Report Type')
    )

    academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        related_name='grade_reports',
        verbose_name=_('Academic Year')
    )

    semester = models.ForeignKey(
        Semester,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='grade_reports',
        verbose_name=_('Semester')
    )

    report_period_start = models.DateField(
        verbose_name=_('Report Period Start')
    )

    report_period_end = models.DateField(
        verbose_name=_('Report Period End')
    )

    generated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='generated_reports',
        verbose_name=_('Generated By')
    )

    generated_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Generated At')
    )

    report_data = models.JSONField(
        verbose_name=_('Report Data'),
        help_text=_('Complete report data in JSON format')
    )

    overall_gpa = models.DecimalField(
        max_digits=4,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(0), MaxValueValidator(4.00)],
        verbose_name=_('Overall GPA')
    )

    total_subjects = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Total Subjects')
    )

    subjects_passed = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Subjects Passed')
    )

    attendance_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name=_('Attendance Percentage')
    )

    teacher_comments = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Teacher Comments')
    )

    parent_viewed = models.BooleanField(
        default=False,
        verbose_name=_('Viewed by Parent')
    )

    parent_viewed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Parent Viewed At')
    )

    class Meta:
        verbose_name = _('Grade Report')
        verbose_name_plural = _('Grade Reports')
        ordering = ['-generated_at']
        indexes = [
            models.Index(fields=['school', 'academic_year', 'report_type']),
            models.Index(fields=['student', 'report_type']),
        ]

    def __str__(self):
        return f"{self.student.full_name} - {self.get_report_type_display()}"

    def mark_viewed_by_parent(self):
        """Mark report as viewed by parent"""
        if not self.parent_viewed:
            self.parent_viewed = True
            self.parent_viewed_at = timezone.now()
            self.save()

    @property
    def pass_percentage(self):
        """Calculate pass percentage"""
        if self.total_subjects == 0:
            return 0
        return (self.subjects_passed / self.total_subjects) * 100


# Enhanced Attendance Management System for Task 4.5

class AttendanceSession(BaseModel):
    """
    Attendance session model for tracking individual class sessions
    """
    ATTENDANCE_METHODS = (
        ('manual', _('Manual Entry')),
        ('biometric', _('Biometric Scanner')),
        ('qr_code', _('QR Code Scan')),
        ('rfid', _('RFID Card')),
        ('mobile_app', _('Mobile App')),
        ('facial_recognition', _('Facial Recognition')),
    )

    SESSION_STATUS = (
        ('scheduled', _('Scheduled')),
        ('active', _('Active')),
        ('completed', _('Completed')),
        ('cancelled', _('Cancelled')),
    )

    class_subject = models.ForeignKey(
        ClassSubject,
        on_delete=models.CASCADE,
        related_name='attendance_sessions',
        verbose_name=_('Class Subject')
    )

    session_date = models.DateField(
        verbose_name=_('Session Date')
    )

    start_time = models.TimeField(
        verbose_name=_('Start Time')
    )

    end_time = models.TimeField(
        verbose_name=_('End Time')
    )

    session_topic = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Session Topic')
    )

    attendance_method = models.CharField(
        max_length=20,
        choices=ATTENDANCE_METHODS,
        default='manual',
        verbose_name=_('Attendance Method')
    )

    status = models.CharField(
        max_length=20,
        choices=SESSION_STATUS,
        default='scheduled',
        verbose_name=_('Session Status')
    )

    qr_code_token = models.UUIDField(
        default=uuid.uuid4,
        unique=True,
        verbose_name=_('QR Code Token')
    )

    qr_code_expires_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('QR Code Expiry')
    )

    location = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Session Location')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Session Notes')
    )

    total_students = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Total Students')
    )

    present_count = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Present Count')
    )

    absent_count = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Absent Count')
    )

    late_count = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Late Count')
    )

    excused_count = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Excused Count')
    )

    class Meta:
        verbose_name = _('Attendance Session')
        verbose_name_plural = _('Attendance Sessions')
        unique_together = ['class_subject', 'session_date', 'start_time']
        ordering = ['-session_date', '-start_time']
        indexes = [
            models.Index(fields=['school', 'session_date']),
            models.Index(fields=['class_subject', 'status']),
            models.Index(fields=['qr_code_token']),
        ]

    def __str__(self):
        return f"{self.class_subject} - {self.session_date} {self.start_time}"

    def clean(self):
        super().clean()
        
        if self.start_time and self.end_time:
            if self.start_time >= self.end_time:
                raise ValidationError(
                    _('Start time must be before end time.')
                )
        
        if self.session_date and self.session_date > date.today():
            if self.status == 'completed':
                raise ValidationError(
                    _('Cannot mark future sessions as completed.')
                )

    def get_attendance_rate(self) -> float:
        """Calculate attendance rate for this session"""
        if self.total_students == 0:
            return 0.0
        return (self.present_count / self.total_students) * 100

    def generate_qr_code(self) -> str:
        """Generate QR code for attendance session"""
        from .attendance_system import generate_qr_code_for_session
        return generate_qr_code_for_session(self)

    def start_session(self):
        """Start the attendance session"""
        if self.status != 'scheduled':
            raise ValidationError(_('Only scheduled sessions can be started.'))
        
        self.status = 'active'
        self.total_students = self.class_subject.class_obj.students.filter(
            is_active=True
        ).count()
        
        # Create attendance records for all students
        students = self.class_subject.class_obj.students.filter(is_active=True)
        attendance_records = []
        
        for student in students:
            attendance_records.append(
                StudentAttendance(
                    session=self,
                    student=student,
                    class_subject=self.class_subject,
                    status='absent',  # Default to absent
                    school=self.school
                )
            )
        
        StudentAttendance.objects.bulk_create(attendance_records)
        self.save()

    def complete_session(self):
        """Complete the attendance session and update counts"""
        if self.status != 'active':
            raise ValidationError(_('Only active sessions can be completed.'))
        
        # Update attendance counts
        attendance_counts = self.attendance_records.aggregate(
            present=Count('id', filter=Q(status='present')),
            absent=Count('id', filter=Q(status='absent')),
            late=Count('id', filter=Q(status='late')),
            excused=Count('id', filter=Q(status='excused'))
        )
        
        self.present_count = attendance_counts['present']
        self.absent_count = attendance_counts['absent']
        self.late_count = attendance_counts['late']
        self.excused_count = attendance_counts['excused']
        self.status = 'completed'
        
        self.save()
        
        # Trigger parent notifications for absent students
        self._send_absence_notifications()

    def _send_absence_notifications(self):
        """Send notifications to parents of absent students"""
        from .attendance_system import AttendanceNotificationService
        
        absent_students = self.attendance_records.filter(
            status__in=['absent', 'late']
        ).select_related('student')
        
        for attendance in absent_students:
            AttendanceNotificationService.send_absence_notification(
                student=attendance.student,
                session=self
            )

    def is_qr_code_valid(self) -> bool:
        """Check if QR code is still valid"""
        if not self.qr_code_expires_at:
            return False
        return timezone.now() < self.qr_code_expires_at

    def get_session_summary(self) -> dict:
        """Get comprehensive session summary"""
        return {
            'total_students': self.total_students,
            'present_count': self.present_count,
            'absent_count': self.absent_count,
            'late_count': self.late_count,
            'excused_count': self.excused_count,
            'attendance_rate': self.get_attendance_rate(),
            'session_duration': self._calculate_duration(),
            'method_used': self.get_attendance_method_display(),
            'status': self.get_status_display()
        }

    def _calculate_duration(self) -> int:
        """Calculate session duration in minutes"""
        if not self.start_time or not self.end_time:
            return 0
        
        start_datetime = datetime.combine(date.today(), self.start_time)
        end_datetime = datetime.combine(date.today(), self.end_time)
        
        return int((end_datetime - start_datetime).total_seconds() / 60)


class StudentAttendance(BaseModel):
    """
    Enhanced student attendance record for each session with multiple tracking methods
    """
    ATTENDANCE_STATUS = (
        ('present', _('Present')),
        ('absent', _('Absent')),
        ('late', _('Late')),
        ('excused', _('Excused')),
        ('sick', _('Sick')),
        ('authorized_absence', _('Authorized Absence')),
    )

    session = models.ForeignKey(
        AttendanceSession,
        on_delete=models.CASCADE,
        related_name='attendance_records',
        verbose_name=_('Attendance Session')
    )

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='attendance_records',
        verbose_name=_('Student')
    )

    class_subject = models.ForeignKey(
        ClassSubject,
        on_delete=models.CASCADE,
        related_name='student_attendance',
        verbose_name=_('Class Subject')
    )

    status = models.CharField(
        max_length=20,
        choices=ATTENDANCE_STATUS,
        default='absent',
        verbose_name=_('Attendance Status')
    )

    marked_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Marked At')
    )

    marked_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='marked_attendance',
        verbose_name=_('Marked By')
    )

    marking_method = models.CharField(
        max_length=20,
        choices=AttendanceSession.ATTENDANCE_METHODS,
        default='manual',
        verbose_name=_('Marking Method')
    )

    arrival_time = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_('Arrival Time')
    )

    departure_time = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_('Departure Time')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    biometric_data = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Biometric Data'),
        help_text=_('Biometric verification data (fingerprint hash, etc.)')
    )

    location_data = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Location Data'),
        help_text=_('GPS coordinates or location information')
    )

    device_info = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Device Information'),
        help_text=_('Information about the device used for marking attendance')
    )

    parent_notified = models.BooleanField(
        default=False,
        verbose_name=_('Parent Notified')
    )

    notification_sent_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Notification Sent At')
    )

    class Meta:
        verbose_name = _('Student Attendance')
        verbose_name_plural = _('Student Attendance')
        unique_together = ['session', 'student']
        ordering = ['-session__session_date', 'student__first_name']
        indexes = [
            models.Index(fields=['school', 'student']),
            models.Index(fields=['class_subject', 'status']),
            models.Index(fields=['marked_at']),
        ]

    def __str__(self):
        return f"{self.student} - {self.session} ({self.status})"

    def clean(self):
        super().clean()
        
        # Validate that student belongs to the class
        if self.student and self.class_subject:
            if not self.class_subject.class_obj.students.filter(
                id=self.student.id, is_active=True
            ).exists():
                raise ValidationError(
                    _('Student does not belong to this class.')
                )

    def mark_attendance(self, status: str, marked_by=None, method='manual', **kwargs):
        """Mark attendance for the student"""
        self.status = status
        self.marked_at = timezone.now()
        self.marked_by = marked_by
        self.marking_method = method
        
        # Set arrival time for present/late status
        if status in ['present', 'late'] and not self.arrival_time:
            self.arrival_time = timezone.now().time()
        
        # Store additional data
        if 'biometric_data' in kwargs:
            self.biometric_data = kwargs['biometric_data']
        
        if 'location_data' in kwargs:
            self.location_data = kwargs['location_data']
        
        if 'device_info' in kwargs:
            self.device_info = kwargs['device_info']
        
        if 'notes' in kwargs:
            self.notes = kwargs['notes']
        
        self.save()

    def is_late(self) -> bool:
        """Check if student arrived late"""
        if not self.arrival_time or not self.session.start_time:
            return False
        
        # Consider late if arrived more than 10 minutes after start time
        start_datetime = datetime.combine(date.today(), self.session.start_time)
        arrival_datetime = datetime.combine(date.today(), self.arrival_time)
        
        return arrival_datetime > start_datetime + timedelta(minutes=10)

    def calculate_session_duration(self) -> int:
        """Calculate how long student was present in minutes"""
        if not self.arrival_time:
            return 0
        
        departure = self.departure_time or self.session.end_time
        if not departure:
            return 0
        
        arrival_datetime = datetime.combine(date.today(), self.arrival_time)
        departure_datetime = datetime.combine(date.today(), departure)
        
        return max(0, int((departure_datetime - arrival_datetime).total_seconds() / 60))


class BiometricDevice(BaseModel):
    """
    Biometric device configuration for attendance tracking
    """
    DEVICE_TYPES = (
        ('fingerprint', _('Fingerprint Scanner')),
        ('facial_recognition', _('Facial Recognition')),
        ('iris_scanner', _('Iris Scanner')),
        ('palm_scanner', _('Palm Scanner')),
    )

    DEVICE_STATUS = (
        ('active', _('Active')),
        ('inactive', _('Inactive')),
        ('maintenance', _('Under Maintenance')),
        ('error', _('Error')),
    )

    name = models.CharField(
        max_length=100,
        verbose_name=_('Device Name')
    )

    device_type = models.CharField(
        max_length=20,
        choices=DEVICE_TYPES,
        verbose_name=_('Device Type')
    )

    device_id = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('Device ID')
    )

    location = models.CharField(
        max_length=100,
        verbose_name=_('Device Location')
    )

    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name=_('IP Address')
    )

    port = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name=_('Port')
    )

    status = models.CharField(
        max_length=20,
        choices=DEVICE_STATUS,
        default='active',
        verbose_name=_('Device Status')
    )

    last_sync = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Last Sync')
    )

    configuration = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Device Configuration')
    )

    class Meta:
        verbose_name = _('Biometric Device')
        verbose_name_plural = _('Biometric Devices')
        ordering = ['name']
        indexes = [
            models.Index(fields=['school', 'status']),
            models.Index(fields=['device_type']),
        ]

    def __str__(self):
        return f"{self.name} ({self.device_type})"


class AttendanceRule(BaseModel):
    """
    Attendance rules and policies for different classes or grades
    """
    RULE_TYPES = (
        ('minimum_attendance', _('Minimum Attendance Percentage')),
        ('late_threshold', _('Late Arrival Threshold (minutes)')),
        ('absence_notification', _('Absence Notification Rules')),
        ('makeup_policy', _('Makeup Session Policy')),
    )

    name = models.CharField(
        max_length=100,
        verbose_name=_('Rule Name')
    )

    rule_type = models.CharField(
        max_length=30,
        choices=RULE_TYPES,
        verbose_name=_('Rule Type')
    )

    applicable_to = models.CharField(
        max_length=20,
        choices=[
            ('all', _('All Classes')),
            ('grade', _('Specific Grade')),
            ('class', _('Specific Class')),
            ('subject', _('Specific Subject')),
        ],
        default='all',
        verbose_name=_('Applicable To')
    )

    target_grades = models.ManyToManyField(
        'students.Grade',
        blank=True,
        related_name='attendance_rules',
        verbose_name=_('Target Grades')
    )

    target_classes = models.ManyToManyField(
        'students.Class',
        blank=True,
        related_name='attendance_rules',
        verbose_name=_('Target Classes')
    )

    target_subjects = models.ManyToManyField(
        'Subject',
        blank=True,
        related_name='attendance_rules',
        verbose_name=_('Target Subjects')
    )

    rule_value = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        verbose_name=_('Rule Value')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )

    effective_from = models.DateField(
        verbose_name=_('Effective From')
    )

    effective_to = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Effective To')
    )

    class Meta:
        verbose_name = _('Attendance Rule')
        verbose_name_plural = _('Attendance Rules')
        ordering = ['name']
        indexes = [
            models.Index(fields=['school', 'rule_type', 'is_active']),
            models.Index(fields=['effective_from', 'effective_to']),
        ]

    def __str__(self):
        return f"{self.name} ({self.get_rule_type_display()})"


class CurriculumPlan(BaseModel):
    """
    Enhanced curriculum planning model for academic structure management
    """
    CURRICULUM_TYPES = (
        ('national', _('National Curriculum')),
        ('international', _('International Curriculum')),
        ('bilingual', _('Bilingual Curriculum')),
        ('specialized', _('Specialized Program')),
        ('remedial', _('Remedial Program')),
        ('advanced', _('Advanced Program')),
    )

    name = models.CharField(
        max_length=200,
        verbose_name=_('Curriculum Plan Name')
    )

    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Curriculum Plan Name (Arabic)')
    )

    code = models.CharField(
        max_length=20,
        verbose_name=_('Curriculum Code')
    )

    curriculum_type = models.CharField(
        max_length=20,
        choices=CURRICULUM_TYPES,
        default='national',
        verbose_name=_('Curriculum Type')
    )

    grades = models.ManyToManyField(
        Grade,
        related_name='curriculum_plans',
        verbose_name=_('Applicable Grades')
    )

    academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        related_name='curriculum_plans',
        verbose_name=_('Academic Year')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    objectives = models.TextField(
        verbose_name=_('Learning Objectives'),
        help_text=_('Overall learning objectives for this curriculum')
    )

    total_credit_hours = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Total Credit Hours'),
        help_text=_('Total credit hours required for completion')
    )

    minimum_credit_hours = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Minimum Credit Hours'),
        help_text=_('Minimum credit hours required for progression')
    )

    core_subjects_required = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Core Subjects Required'),
        help_text=_('Number of core subjects that must be completed')
    )

    elective_subjects_required = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Elective Subjects Required'),
        help_text=_('Number of elective subjects that must be completed')
    )

    minimum_gpa_required = models.DecimalField(
        max_digits=4,
        decimal_places=2,
        default=2.00,
        validators=[MinValueValidator(0), MaxValueValidator(4.00)],
        verbose_name=_('Minimum GPA Required'),
        help_text=_('Minimum GPA required for curriculum completion')
    )

    duration_years = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(12)],
        verbose_name=_('Duration (Years)'),
        help_text=_('Expected duration to complete this curriculum')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )

    effective_date = models.DateField(
        verbose_name=_('Effective Date'),
        help_text=_('Date when this curriculum becomes effective')
    )

    expiry_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Expiry Date'),
        help_text=_('Date when this curriculum expires (optional)')
    )

    prerequisites = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('General Prerequisites'),
        help_text=_('General prerequisites for enrollment in this curriculum')
    )

    assessment_policy = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Assessment Policy'),
        help_text=_('Assessment and grading policy for this curriculum')
    )

    progression_rules = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Progression Rules'),
        help_text=_('Rules for student progression through the curriculum')
    )

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_curriculum_plans',
        verbose_name=_('Created By')
    )

    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_curriculum_plans',
        verbose_name=_('Approved By')
    )

    approved_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Approved At')
    )

    class Meta:
        verbose_name = _('Curriculum Plan')
        verbose_name_plural = _('Curriculum Plans')
        ordering = ['name']
        unique_together = ['school', 'code', 'academic_year']
        indexes = [
            models.Index(fields=['school', 'curriculum_type']),
            models.Index(fields=['school', 'is_active']),
            models.Index(fields=['academic_year', 'is_active']),
        ]

    def __str__(self):
        return f"{self.code} - {self.name}"

    def clean(self):
        super().clean()
        
        if self.expiry_date and self.effective_date:
            if self.expiry_date <= self.effective_date:
                raise ValidationError(
                    _('Expiry date must be after effective date.')
                )
        
        if (self.minimum_credit_hours is not None and self.total_credit_hours is not None and 
            self.minimum_credit_hours > self.total_credit_hours):
            raise ValidationError(
                _('Minimum credit hours cannot exceed total credit hours.')
            )

    @property
    def is_current(self):
        """Check if curriculum is currently active"""
        from datetime import date
        today = date.today()
        
        if not self.is_active:
            return False
        
        if self.effective_date and today < self.effective_date:
            return False
        
        if self.expiry_date and today > self.expiry_date:
            return False
        
        return True

    def get_subject_distribution(self):
        """Get distribution of subjects by type"""
        subjects = self.curriculum_subjects.all()
        
        distribution = {}
        for subject_mapping in subjects:
            subject_type = subject_mapping.subject.subject_type
            if subject_type not in distribution:
                distribution[subject_type] = {
                    'count': 0,
                    'credit_hours': 0,
                    'weekly_hours': 0
                }
            
            distribution[subject_type]['count'] += 1
            distribution[subject_type]['credit_hours'] += subject_mapping.credit_hours
            distribution[subject_type]['weekly_hours'] += subject_mapping.weekly_hours
        
        return distribution

    def calculate_completion_requirements(self, student):
        """Calculate what a student needs to complete the curriculum"""
        completed_subjects = StudentGrade.objects.filter(
            student=student,
            exam__class_subject__subject__curriculum_mappings__curriculum=self,
            percentage__gte=60  # Assuming 60% is passing
        ).values_list('exam__class_subject__subject', flat=True).distinct()
        
        total_subjects = self.curriculum_subjects.count()
        completed_count = len(completed_subjects)
        
        # Calculate credit hours completed
        completed_credit_hours = self.curriculum_subjects.filter(
            subject__in=completed_subjects
        ).aggregate(
            total=models.Sum('credit_hours')
        )['total'] or 0
        
        return {
            'subjects_completed': completed_count,
            'subjects_remaining': total_subjects - completed_count,
            'credit_hours_completed': completed_credit_hours,
            'credit_hours_remaining': self.total_credit_hours - completed_credit_hours,
            'completion_percentage': (completed_count / total_subjects * 100) if total_subjects > 0 else 0
        }

    def get_prerequisite_map(self):
        """Get a map of all prerequisite relationships in the curriculum"""
        prerequisite_map = {}
        
        for subject_mapping in self.curriculum_subjects.all():
            subject = subject_mapping.subject
            prerequisites = list(subject.prerequisites.all())
            prerequisite_map[subject.id] = {
                'subject': subject,
                'prerequisites': prerequisites,
                'prerequisite_ids': [p.id for p in prerequisites]
            }
        
        return prerequisite_map

    def validate_curriculum_structure(self):
        """Validate the curriculum structure for consistency"""
        errors = []
        
        # Check for circular dependencies
        for subject_mapping in self.curriculum_subjects.all():
            if subject_mapping.subject.detect_circular_prerequisites():
                errors.append(
                    f"Circular prerequisite dependency detected for {subject_mapping.subject}"
                )
        
        # Check if total credit hours match sum of subject credit hours
        calculated_total = self.curriculum_subjects.aggregate(
            total=models.Sum('credit_hours')
        )['total'] or 0
        
        if calculated_total != self.total_credit_hours:
            errors.append(
                f"Total credit hours mismatch: declared {self.total_credit_hours}, "
                f"calculated {calculated_total}"
            )
        
        # Check if minimum requirements are achievable
        core_subjects_count = self.curriculum_subjects.filter(
            subject__subject_type='core'
        ).count()
        
        if self.core_subjects_required > core_subjects_count:
            errors.append(
                f"Required core subjects ({self.core_subjects_required}) exceeds "
                f"available core subjects ({core_subjects_count})"
            )
        
        return errors

    def generate_study_plan(self, student_grade):
        """Generate a suggested study plan for a specific grade"""
        subjects = self.curriculum_subjects.filter(
            applicable_grades=student_grade
        ).order_by('semester', 'subject__prerequisites__count')
        
        study_plan = {
            'first_semester': [],
            'second_semester': [],
            'full_year': []
        }
        
        for subject_mapping in subjects:
            semester_key = f"{subject_mapping.semester}_semester" if subject_mapping.semester != 'full_year' else 'full_year'
            study_plan[semester_key].append({
                'subject': subject_mapping.subject,
                'credit_hours': subject_mapping.credit_hours,
                'weekly_hours': subject_mapping.weekly_hours,
                'is_mandatory': subject_mapping.is_mandatory,
                'prerequisites': list(subject_mapping.subject.prerequisites.all())
            })
        
        return study_plan


class Curriculum(BaseModel):
    """
    Curriculum model for academic planning
    """
    name = models.CharField(
        max_length=200,
        verbose_name=_('Curriculum Name')
    )

    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Curriculum Name (Arabic)')
    )

    grade = models.ForeignKey(
        Grade,
        on_delete=models.CASCADE,
        related_name='curriculums',
        verbose_name=_('Grade')
    )

    academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        related_name='curriculums',
        verbose_name=_('Academic Year')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    objectives = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Learning Objectives')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_curriculums',
        verbose_name=_('Created By')
    )

    class Meta:
        verbose_name = _('Curriculum')
        verbose_name_plural = _('Curriculums')
        ordering = ['grade', 'name']

    def __str__(self):
        return f"{self.name} - {self.grade}"


class CurriculumSubject(BaseModel):
    """
    Enhanced curriculum subject mapping model with detailed planning capabilities
    """
    curriculum = models.ForeignKey(
        CurriculumPlan,
        on_delete=models.CASCADE,
        related_name='curriculum_subjects',
        verbose_name=_('Curriculum Plan')
    )

    subject = models.ForeignKey(
        Subject,
        on_delete=models.CASCADE,
        related_name='curriculum_mappings',
        verbose_name=_('Subject')
    )

    applicable_grades = models.ManyToManyField(
        Grade,
        related_name='curriculum_subject_mappings',
        verbose_name=_('Applicable Grades'),
        help_text=_('Grades where this subject is taught in this curriculum')
    )

    credit_hours = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(10)],
        verbose_name=_('Credit Hours'),
        help_text=_('Credit hours for this subject in this curriculum')
    )

    weekly_hours = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(20)],
        verbose_name=_('Weekly Hours')
    )

    semester = models.CharField(
        max_length=20,
        choices=[
            ('first', _('First Semester')),
            ('second', _('Second Semester')),
            ('full_year', _('Full Year')),
        ],
        default='full_year',
        verbose_name=_('Semester')
    )

    is_mandatory = models.BooleanField(
        default=True,
        verbose_name=_('Is Mandatory')
    )

    sequence_order = models.PositiveIntegerField(
        default=1,
        verbose_name=_('Sequence Order'),
        help_text=_('Order in which this subject should be taught')
    )

    minimum_passing_grade = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=60.00,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name=_('Minimum Passing Grade'),
        help_text=_('Minimum grade required to pass this subject in this curriculum')
    )

    weight_in_gpa = models.DecimalField(
        max_digits=4,
        decimal_places=2,
        default=1.00,
        validators=[MinValueValidator(0.1), MaxValueValidator(5.0)],
        verbose_name=_('Weight in GPA'),
        help_text=_('Weight of this subject in overall GPA calculation')
    )

    learning_outcomes = models.TextField(
        verbose_name=_('Learning Outcomes'),
        help_text=_('Specific learning outcomes for this subject in this curriculum')
    )

    assessment_criteria = models.TextField(
        verbose_name=_('Assessment Criteria'),
        help_text=_('Assessment criteria and methods for this subject')
    )

    assessment_breakdown = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Assessment Breakdown'),
        help_text=_('Breakdown of assessment components (e.g., {"midterm": 30, "final": 40, "assignments": 30})')
    )

    required_resources = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Required Resources'),
        help_text=_('Resources required to teach this subject effectively')
    )

    textbooks = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Textbooks'),
        help_text=_('Required and recommended textbooks')
    )

    digital_resources = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Digital Resources'),
        help_text=_('Digital resources and online materials')
    )

    teacher_qualifications = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Teacher Qualifications'),
        help_text=_('Required qualifications for teachers of this subject')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )

    effective_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Effective Date')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes'),
        help_text=_('Additional notes or special instructions')
    )

    class Meta:
        verbose_name = _('Curriculum Subject')
        verbose_name_plural = _('Curriculum Subjects')
        unique_together = ['curriculum', 'subject', 'semester']
        ordering = ['curriculum', 'sequence_order', 'subject']
        indexes = [
            models.Index(fields=['curriculum', 'is_mandatory']),
            models.Index(fields=['curriculum', 'semester']),
            models.Index(fields=['subject', 'is_active']),
        ]

    def __str__(self):
        return f"{self.curriculum.name} - {self.subject.name} ({self.semester})"

    def clean(self):
        super().clean()
        
        # Validate credit hours don't exceed subject's credit hours
        if self.credit_hours > self.subject.credit_hours:
            raise ValidationError(
                _('Curriculum credit hours cannot exceed subject credit hours.')
            )
        
        # Validate weekly hours don't exceed subject's weekly hours
        if self.weekly_hours > self.subject.weekly_hours:
            raise ValidationError(
                _('Curriculum weekly hours cannot exceed subject weekly hours.')
            )
        
        # Validate assessment breakdown totals 100%
        if self.assessment_breakdown:
            total_percentage = sum(self.assessment_breakdown.values())
            if total_percentage != 100:
                raise ValidationError(
                    _('Assessment breakdown must total 100%. Current total: {}%').format(
                        total_percentage
                    )
                )

    def get_prerequisite_subjects_in_curriculum(self):
        """Get prerequisite subjects that are also in this curriculum"""
        prerequisite_subjects = self.subject.prerequisites.all()
        
        curriculum_prerequisites = CurriculumSubject.objects.filter(
            curriculum=self.curriculum,
            subject__in=prerequisite_subjects
        )
        
        return curriculum_prerequisites

    def check_prerequisite_sequence(self):
        """Check if prerequisite subjects are sequenced before this subject"""
        errors = []
        
        prerequisite_mappings = self.get_prerequisite_subjects_in_curriculum()
        
        for prereq_mapping in prerequisite_mappings:
            # Check if prerequisite is scheduled in an earlier semester or same semester with lower sequence
            if self.semester == 'first' and prereq_mapping.semester == 'second':
                errors.append(
                    f"Prerequisite {prereq_mapping.subject.name} is scheduled after this subject"
                )
            elif (self.semester == prereq_mapping.semester and 
                  prereq_mapping.sequence_order >= self.sequence_order):
                errors.append(
                    f"Prerequisite {prereq_mapping.subject.name} has same or later sequence order"
                )
        
        return errors

    def calculate_workload(self):
        """Calculate total workload for this subject in the curriculum"""
        # Base teaching hours
        teaching_hours = self.weekly_hours
        
        # Estimated study hours (typically 2-3 times teaching hours)
        study_hours = teaching_hours * 2.5
        
        # Assessment hours (varies by assessment type)
        assessment_hours = 0
        if self.assessment_breakdown:
            # Rough estimate based on assessment types
            for assessment_type, percentage in self.assessment_breakdown.items():
                if 'exam' in assessment_type.lower():
                    assessment_hours += (percentage / 100) * 3  # 3 hours per exam
                elif 'assignment' in assessment_type.lower():
                    assessment_hours += (percentage / 100) * 5  # 5 hours per assignment
                elif 'project' in assessment_type.lower():
                    assessment_hours += (percentage / 100) * 10  # 10 hours per project
        
        return {
            'weekly_teaching_hours': teaching_hours,
            'weekly_study_hours': study_hours,
            'total_weekly_hours': teaching_hours + study_hours,
            'assessment_hours_per_semester': assessment_hours,
            'credit_hours': self.credit_hours
        }

    def get_qualified_teachers(self):
        """Get teachers qualified to teach this subject"""
        qualified_teachers = Teacher.objects.filter(
            school=self.school,
            subjects=self.subject,
            is_active=True
        )
        
        # Additional filtering based on teacher qualifications if specified
        if self.teacher_qualifications:
            # This would require more complex filtering based on teacher profiles
            # For now, return all teachers qualified for the subject
            pass
        
        return qualified_teachers

    def estimate_resource_cost(self):
        """Estimate resource cost for this subject in the curriculum"""
        # This is a simplified cost estimation
        base_cost = self.credit_hours * 100  # Base cost per credit hour
        
        # Additional costs for special requirements
        if self.subject.requires_lab:
            base_cost += 500
        
        if self.subject.requires_special_equipment:
            base_cost += 300
        
        # Textbook costs (estimated)
        if self.textbooks:
            base_cost += 50 * len(self.textbooks.split('\n'))
        
        return base_cost

    def generate_assessment_schedule(self, academic_year):
        """Generate a suggested assessment schedule for this subject"""
        if not self.assessment_breakdown:
            return []
        
        schedule = []
        semester_weeks = 16  # Typical semester length
        
        for assessment_type, percentage in self.assessment_breakdown.items():
            if 'midterm' in assessment_type.lower():
                week = semester_weeks // 2
            elif 'final' in assessment_type.lower():
                week = semester_weeks - 1
            elif 'quiz' in assessment_type.lower():
                # Distribute quizzes throughout semester
                week = semester_weeks // 4
            else:
                # Assignments and projects distributed throughout
                week = semester_weeks // 3
            
            schedule.append({
                'assessment_type': assessment_type,
                'percentage': percentage,
                'suggested_week': week,
                'estimated_duration': self._get_assessment_duration(assessment_type)
            })
        
        return sorted(schedule, key=lambda x: x['suggested_week'])

    def _get_assessment_duration(self, assessment_type):
        """Get estimated duration for different assessment types"""
        duration_map = {
            'quiz': 30,
            'midterm': 90,
            'final': 120,
            'assignment': 0,  # Take-home
            'project': 0,     # Take-home
            'presentation': 15,
            'oral': 20
        }
        
        for key, duration in duration_map.items():
            if key in assessment_type.lower():
                return duration
        
        return 60  # Default duration


# Enhanced Examination System Models for Task 4.4

class ExamSession(BaseModel):
    """
    Exam session model for grouping related exams
    """
    SESSION_TYPES = (
        ('midterm', _('Midterm Exams')),
        ('final', _('Final Exams')),
        ('monthly', _('Monthly Tests')),
        ('quarterly', _('Quarterly Exams')),
        ('annual', _('Annual Exams')),
    )

    name = models.CharField(
        max_length=200,
        verbose_name=_('Session Name')
    )

    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Session Name (Arabic)')
    )

    session_type = models.CharField(
        max_length=20,
        choices=SESSION_TYPES,
        verbose_name=_('Session Type')
    )

    academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        related_name='exam_sessions',
        verbose_name=_('Academic Year')
    )

    semester = models.ForeignKey(
        Semester,
        on_delete=models.CASCADE,
        related_name='exam_sessions',
        verbose_name=_('Semester')
    )

    start_date = models.DateField(
        verbose_name=_('Start Date')
    )

    end_date = models.DateField(
        verbose_name=_('End Date')
    )

    registration_start = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Registration Start Date')
    )

    registration_end = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Registration End Date')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )

    instructions = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('General Instructions')
    )

    class Meta:
        verbose_name = _('Exam Session')
        verbose_name_plural = _('Exam Sessions')
        ordering = ['-start_date']
        unique_together = ['school', 'name', 'academic_year']

    def __str__(self):
        return f"{self.name} - {self.academic_year}"

    def clean(self):
        super().clean()
        
        if self.start_date and self.end_date:
            if self.start_date >= self.end_date:
                raise ValidationError(_('Start date must be before end date.'))
        
        if self.registration_start and self.registration_end:
            if self.registration_start >= self.registration_end:
                raise ValidationError(_('Registration start date must be before end date.'))
        
        if self.registration_end and self.start_date:
            if self.registration_end > self.start_date:
                raise ValidationError(_('Registration must end before exam session starts.'))

    @property
    def is_registration_open(self):
        """Check if registration is currently open"""
        if not self.registration_start or not self.registration_end:
            return False
        
        today = date.today()
        return self.registration_start <= today <= self.registration_end

    @property
    def is_ongoing(self):
        """Check if exam session is currently ongoing"""
        today = date.today()
        return self.start_date <= today <= self.end_date

    def get_exam_count(self):
        """Get total number of exams in this session"""
        return self.exams.count()

    def get_completion_percentage(self):
        """Get completion percentage of exams in this session"""
        total_exams = self.get_exam_count()
        if total_exams == 0:
            return 0
        
        completed_exams = self.exams.filter(is_completed=True).count()
        return (completed_exams / total_exams) * 100


class ExamInvigilator(BaseModel):
    """
    Exam invigilator assignment model
    """
    INVIGILATOR_ROLES = (
        ('chief', _('Chief Invigilator')),
        ('assistant', _('Assistant Invigilator')),
        ('observer', _('Observer')),
        ('special_needs', _('Special Needs Support')),
    )

    exam = models.ForeignKey(
        Exam,
        on_delete=models.CASCADE,
        related_name='invigilators',
        verbose_name=_('Exam')
    )

    teacher = models.ForeignKey(
        Teacher,
        on_delete=models.CASCADE,
        related_name='invigilation_duties',
        verbose_name=_('Teacher')
    )

    role = models.CharField(
        max_length=20,
        choices=INVIGILATOR_ROLES,
        default='assistant',
        verbose_name=_('Role')
    )

    assigned_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Assigned At')
    )

    assigned_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='assigned_invigilations',
        verbose_name=_('Assigned By')
    )

    is_confirmed = models.BooleanField(
        default=False,
        verbose_name=_('Is Confirmed')
    )

    confirmed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Confirmed At')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    class Meta:
        verbose_name = _('Exam Invigilator')
        verbose_name_plural = _('Exam Invigilators')
        unique_together = ['exam', 'teacher']
        ordering = ['exam__exam_date', 'exam__start_time', 'role']

    def __str__(self):
        return f"{self.teacher} - {self.exam} ({self.role})"

    def clean(self):
        super().clean()
        
        # Check for conflicts with other invigilation duties
        conflicting_duties = ExamInvigilator.objects.filter(
            teacher=self.teacher,
            exam__exam_date=self.exam.exam_date,
            exam__start_time__lt=self.exam.end_time,
            exam__end_time__gt=self.exam.start_time
        ).exclude(pk=self.pk if self.pk else None)
        
        if conflicting_duties.exists():
            raise ValidationError(
                _('Teacher has conflicting invigilation duties at this time.')
            )

    def confirm_assignment(self, confirmed_by=None):
        """Confirm the invigilation assignment"""
        self.is_confirmed = True
        self.confirmed_at = timezone.now()
        if confirmed_by:
            self.notes = f"Confirmed by {confirmed_by}"
        self.save()

    def get_duration_minutes(self):
        """Get duration of invigilation duty in minutes"""
        return self.exam.duration_minutes


class ExamSeating(BaseModel):
    """
    Exam seating arrangement model
    """
    exam = models.ForeignKey(
        Exam,
        on_delete=models.CASCADE,
        related_name='seating_arrangements',
        verbose_name=_('Exam')
    )

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='exam_seats',
        verbose_name=_('Student')
    )

    room = models.ForeignKey(
        Room,
        on_delete=models.CASCADE,
        related_name='exam_seatings',
        verbose_name=_('Room')
    )

    seat_number = models.CharField(
        max_length=20,
        verbose_name=_('Seat Number')
    )

    row_number = models.PositiveIntegerField(
        verbose_name=_('Row Number')
    )

    column_number = models.PositiveIntegerField(
        verbose_name=_('Column Number')
    )

    is_special_needs = models.BooleanField(
        default=False,
        verbose_name=_('Special Needs Accommodation')
    )

    special_requirements = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Special Requirements')
    )

    assigned_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Assigned At')
    )

    class Meta:
        verbose_name = _('Exam Seating')
        verbose_name_plural = _('Exam Seatings')
        unique_together = ['exam', 'student']
        ordering = ['exam', 'row_number', 'column_number']

    def __str__(self):
        return f"{self.student} - {self.exam} - Seat {self.seat_number}"

    def clean(self):
        super().clean()
        
        # Check for seat conflicts
        conflicting_seat = ExamSeating.objects.filter(
            exam=self.exam,
            room=self.room,
            seat_number=self.seat_number
        ).exclude(pk=self.pk if self.pk else None)
        
        if conflicting_seat.exists():
            raise ValidationError(
                _('Seat {} is already assigned to another student.').format(self.seat_number)
            )


class ExamResult(BaseModel):
    """
    Comprehensive exam result model with analytics
    """
    exam = models.OneToOneField(
        Exam,
        on_delete=models.CASCADE,
        related_name='result_summary',
        verbose_name=_('Exam')
    )

    total_students = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Total Students')
    )

    students_appeared = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Students Appeared')
    )

    students_passed = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Students Passed')
    )

    pass_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        verbose_name=_('Pass Percentage')
    )

    average_marks = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        default=0,
        verbose_name=_('Average Marks')
    )

    highest_marks = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        default=0,
        verbose_name=_('Highest Marks')
    )

    lowest_marks = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        default=0,
        verbose_name=_('Lowest Marks')
    )

    standard_deviation = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        default=0,
        verbose_name=_('Standard Deviation')
    )

    grade_distribution = models.JSONField(
        default=dict,
        verbose_name=_('Grade Distribution')
    )

    performance_analysis = models.JSONField(
        default=dict,
        verbose_name=_('Performance Analysis')
    )

    generated_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Generated At')
    )

    generated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='generated_exam_results',
        verbose_name=_('Generated By')
    )

    class Meta:
        verbose_name = _('Exam Result')
        verbose_name_plural = _('Exam Results')
        ordering = ['-generated_at']

    def __str__(self):
        return f"Results for {self.exam}"

    def calculate_statistics(self):
        """Calculate and update exam statistics"""
        grades = self.exam.student_grades.all()
        
        self.total_students = self.exam.class_subject.class_obj.students.filter(is_active=True).count()
        self.students_appeared = grades.count()
        
        if grades.exists():
            marks_list = list(grades.values_list('marks_obtained', flat=True))
            
            self.average_marks = sum(marks_list) / len(marks_list)
            self.highest_marks = max(marks_list)
            self.lowest_marks = min(marks_list)
            
            # Calculate standard deviation
            mean = float(self.average_marks)
            variance = sum((float(x) - mean) ** 2 for x in marks_list) / len(marks_list)
            self.standard_deviation = variance ** 0.5
            
            # Calculate pass percentage
            self.students_passed = grades.filter(marks_obtained__gte=self.exam.passing_marks).count()
            self.pass_percentage = (self.students_passed / self.students_appeared) * 100
            
            # Grade distribution
            grade_dist = grades.values('grade_letter').annotate(count=Count('id'))
            self.grade_distribution = {item['grade_letter']: item['count'] for item in grade_dist}
            
            # Performance analysis
            total_marks = float(self.exam.total_marks)
            self.performance_analysis = {
                'excellent': grades.filter(marks_obtained__gte=total_marks * 0.9).count(),
                'good': grades.filter(
                    marks_obtained__gte=total_marks * 0.75,
                    marks_obtained__lt=total_marks * 0.9
                ).count(),
                'average': grades.filter(
                    marks_obtained__gte=total_marks * 0.6,
                    marks_obtained__lt=total_marks * 0.75
                ).count(),
                'below_average': grades.filter(
                    marks_obtained__gte=self.exam.passing_marks,
                    marks_obtained__lt=total_marks * 0.6
                ).count(),
                'failed': grades.filter(marks_obtained__lt=self.exam.passing_marks).count()
            }
        
        self.save()

    def get_performance_insights(self):
        """Generate performance insights"""
        insights = []
        
        if self.pass_percentage >= 90:
            insights.append("Excellent overall performance")
        elif self.pass_percentage >= 75:
            insights.append("Good overall performance")
        elif self.pass_percentage >= 60:
            insights.append("Average performance, room for improvement")
        else:
            insights.append("Poor performance, requires attention")
        
        if self.standard_deviation > 15:
            insights.append("High variation in student performance")
        elif self.standard_deviation < 5:
            insights.append("Consistent performance across students")
        
        return insights


class ExamAnalytics(BaseModel):
    """
    Comprehensive exam analytics model
    """
    ANALYTICS_TYPES = (
        ('subject', _('Subject Analytics')),
        ('teacher', _('Teacher Analytics')),
        ('class', _('Class Analytics')),
        ('school', _('School Analytics')),
        ('comparative', _('Comparative Analytics')),
    )

    analytics_type = models.CharField(
        max_length=20,
        choices=ANALYTICS_TYPES,
        verbose_name=_('Analytics Type')
    )

    academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        related_name='exam_analytics',
        verbose_name=_('Academic Year')
    )

    semester = models.ForeignKey(
        Semester,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='exam_analytics',
        verbose_name=_('Semester')
    )

    subject = models.ForeignKey(
        Subject,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='analytics',
        verbose_name=_('Subject')
    )

    teacher = models.ForeignKey(
        Teacher,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='analytics',
        verbose_name=_('Teacher')
    )

    class_obj = models.ForeignKey(
        Class,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='analytics',
        verbose_name=_('Class')
    )

    analytics_data = models.JSONField(
        default=dict,
        verbose_name=_('Analytics Data')
    )

    insights = models.JSONField(
        default=list,
        verbose_name=_('Generated Insights')
    )

    recommendations = models.JSONField(
        default=list,
        verbose_name=_('Recommendations')
    )

    generated_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Generated At')
    )

    generated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='generated_analytics',
        verbose_name=_('Generated By')
    )

    class Meta:
        verbose_name = _('Exam Analytics')
        verbose_name_plural = _('Exam Analytics')
        ordering = ['-generated_at']

    def __str__(self):
        return f"{self.analytics_type} Analytics - {self.academic_year}"

    def generate_insights(self):
        """Generate insights based on analytics data"""
        insights = []
        data = self.analytics_data
        
        if self.analytics_type == 'subject' and 'average_performance' in data:
            avg_perf = data['average_performance']
            if avg_perf >= 85:
                insights.append("Subject shows excellent performance")
            elif avg_perf >= 70:
                insights.append("Subject performance is satisfactory")
            else:
                insights.append("Subject needs improvement")
        
        if 'performance_trends' in data:
            trends = data['performance_trends']
            if len(trends) >= 2:
                if trends[-1] > trends[-2]:
                    insights.append("Performance is improving")
                elif trends[-1] < trends[-2]:
                    insights.append("Performance is declining")
        
        self.insights = insights
        self.save()

    def generate_recommendations(self):
        """Generate recommendations based on analytics"""
        recommendations = []
        data = self.analytics_data
        
        if self.analytics_type == 'subject':
            if data.get('pass_rate', 0) < 70:
                recommendations.append("Consider additional tutoring sessions")
                recommendations.append("Review teaching methodology")
            
            if data.get('standard_deviation', 0) > 20:
                recommendations.append("Implement differentiated instruction")
        
        if self.analytics_type == 'teacher':
            if data.get('average_class_performance', 0) < 70:
                recommendations.append("Professional development recommended")
                recommendations.append("Peer observation and mentoring")
        
        self.recommendations = recommendations
        self.save()


# Update the Exam model to include session relationship
# This would be added to the existing Exam model
# exam_session = models.ForeignKey(
#     ExamSession,
#     on_delete=models.SET_NULL,
#     null=True,
#     blank=True,
#     related_name='exams',
#     verbose_name=_('Exam Session')
# )


# Assignment Management Models

class Assignment(BaseModel):
    """
    Assignment model for managing homework, projects, and coursework
    """
    ASSIGNMENT_TYPES = (
        ('homework', _('Homework')),
        ('project', _('Project')),
        ('essay', _('Essay')),
        ('research', _('Research Paper')),
        ('presentation', _('Presentation')),
        ('lab_report', _('Lab Report')),
        ('case_study', _('Case Study')),
        ('group_work', _('Group Work')),
        ('quiz', _('Quiz')),
        ('other', _('Other')),
    )

    DIFFICULTY_LEVELS = (
        ('easy', _('Easy')),
        ('medium', _('Medium')),
        ('hard', _('Hard')),
        ('expert', _('Expert')),
    )

    STATUS_CHOICES = (
        ('draft', _('Draft')),
        ('published', _('Published')),
        ('closed', _('Closed')),
        ('graded', _('Graded')),
        ('archived', _('Archived')),
    )

    title = models.CharField(
        max_length=200,
        verbose_name=_('Assignment Title')
    )

    description = models.TextField(
        verbose_name=_('Description'),
        help_text=_('Detailed description of the assignment')
    )

    class_subject = models.ForeignKey(
        ClassSubject,
        on_delete=models.CASCADE,
        related_name='assignments',
        verbose_name=_('Class Subject')
    )

    assignment_type = models.CharField(
        max_length=20,
        choices=ASSIGNMENT_TYPES,
        default='homework',
        verbose_name=_('Assignment Type')
    )

    difficulty_level = models.CharField(
        max_length=10,
        choices=DIFFICULTY_LEVELS,
        default='medium',
        verbose_name=_('Difficulty Level')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        verbose_name=_('Status')
    )

    instructions = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Instructions'),
        help_text=_('Specific instructions for completing the assignment')
    )

    max_score = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        default=100.00,
        validators=[MinValueValidator(0), MaxValueValidator(1000)],
        verbose_name=_('Maximum Score')
    )

    weight_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=10.00,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name=_('Weight Percentage'),
        help_text=_('Percentage weight in final grade calculation')
    )

    assigned_date = models.DateTimeField(
        default=timezone.now,
        verbose_name=_('Assigned Date')
    )

    due_date = models.DateTimeField(
        verbose_name=_('Due Date')
    )

    late_submission_allowed = models.BooleanField(
        default=True,
        verbose_name=_('Allow Late Submission')
    )

    late_penalty_per_day = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=5.00,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name=_('Late Penalty Per Day (%)'),
        help_text=_('Percentage penalty per day for late submission')
    )

    max_late_days = models.PositiveIntegerField(
        default=3,
        validators=[MinValueValidator(0), MaxValueValidator(30)],
        verbose_name=_('Maximum Late Days'),
        help_text=_('Maximum number of days late submission is accepted')
    )

    allow_resubmission = models.BooleanField(
        default=False,
        verbose_name=_('Allow Resubmission')
    )

    max_resubmissions = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(10)],
        verbose_name=_('Maximum Resubmissions')
    )

    group_assignment = models.BooleanField(
        default=False,
        verbose_name=_('Group Assignment')
    )

    max_group_size = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(20)],
        verbose_name=_('Maximum Group Size')
    )

    submission_format = models.CharField(
        max_length=100,
        default='pdf',
        verbose_name=_('Submission Format'),
        help_text=_('Required file format(s) for submission (e.g., pdf, docx, txt)')
    )

    max_file_size_mb = models.PositiveIntegerField(
        default=10,
        validators=[MinValueValidator(1), MaxValueValidator(100)],
        verbose_name=_('Maximum File Size (MB)')
    )

    plagiarism_check_enabled = models.BooleanField(
        default=True,
        verbose_name=_('Enable Plagiarism Check')
    )

    plagiarism_threshold = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=20.00,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name=_('Plagiarism Threshold (%)'),
        help_text=_('Similarity percentage threshold for plagiarism detection')
    )

    auto_grade_enabled = models.BooleanField(
        default=False,
        verbose_name=_('Enable Auto Grading'),
        help_text=_('Enable automatic grading for objective assignments')
    )

    rubric = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Grading Rubric'),
        help_text=_('JSON structure defining grading criteria and points')
    )

    resources = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Resources'),
        help_text=_('Additional resources, links, or references for the assignment')
    )

    estimated_duration_hours = models.DecimalField(
        max_digits=4,
        decimal_places=1,
        default=2.0,
        validators=[MinValueValidator(0.1), MaxValueValidator(100.0)],
        verbose_name=_('Estimated Duration (Hours)')
    )

    learning_objectives = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Learning Objectives'),
        help_text=_('What students should learn from this assignment')
    )

    class Meta:
        verbose_name = _('Assignment')
        verbose_name_plural = _('Assignments')
        ordering = ['-assigned_date']
        indexes = [
            models.Index(fields=['school', 'class_subject', 'status']),
            models.Index(fields=['due_date', 'status']),
            models.Index(fields=['assigned_date']),
        ]

    def __str__(self):
        return f"{self.title} - {self.class_subject}"

    def clean(self):
        super().clean()
        
        if self.due_date <= self.assigned_date:
            raise ValidationError(_('Due date must be after assigned date.'))
        
        if self.group_assignment and self.max_group_size < 2:
            raise ValidationError(_('Group assignments must allow at least 2 members.'))
        
        if self.weight_percentage < 0 or self.weight_percentage > 100:
            raise ValidationError(_('Weight percentage must be between 0 and 100.'))

    @property
    def is_overdue(self):
        """Check if assignment is overdue"""
        return timezone.now() > self.due_date and self.status in ['published']

    @property
    def days_until_due(self):
        """Get number of days until due date"""
        if self.due_date:
            delta = self.due_date - timezone.now()
            return delta.days
        return None

    @property
    def submission_count(self):
        """Get total number of submissions"""
        return self.submissions.count()

    @property
    def graded_submission_count(self):
        """Get number of graded submissions"""
        return self.submissions.filter(is_graded=True).count()

    @property
    def average_score(self):
        """Get average score for graded submissions"""
        graded_submissions = self.submissions.filter(is_graded=True)
        if graded_submissions.exists():
            return graded_submissions.aggregate(avg_score=Avg('score'))['avg_score']
        return None

    def get_submission_statistics(self):
        """Get comprehensive submission statistics"""
        total_students = self.class_subject.class_obj.students.filter(is_active=True).count()
        submitted = self.submissions.count()
        graded = self.submissions.filter(is_graded=True).count()
        late_submissions = self.submissions.filter(
            submitted_at__gt=self.due_date
        ).count()
        
        return {
            'total_students': total_students,
            'submitted': submitted,
            'not_submitted': total_students - submitted,
            'graded': graded,
            'pending_grading': submitted - graded,
            'late_submissions': late_submissions,
            'submission_rate': (submitted / total_students * 100) if total_students > 0 else 0,
            'grading_progress': (graded / submitted * 100) if submitted > 0 else 0
        }

    def calculate_late_penalty(self, submission_date):
        """Calculate late penalty for a submission"""
        if not self.late_submission_allowed or submission_date <= self.due_date:
            return 0
        
        days_late = (submission_date - self.due_date).days
        if days_late > self.max_late_days:
            return 100  # Full penalty
        
        return min(days_late * self.late_penalty_per_day, 100)

    def get_plagiarism_summary(self):
        """Get plagiarism detection summary"""
        if not self.plagiarism_check_enabled:
            return None
        
        submissions = self.submissions.filter(plagiarism_score__isnull=False)
        if not submissions.exists():
            return None
        
        high_similarity = submissions.filter(
            plagiarism_score__gte=self.plagiarism_threshold
        ).count()
        
        return {
            'total_checked': submissions.count(),
            'high_similarity_count': high_similarity,
            'average_similarity': submissions.aggregate(
                avg=Avg('plagiarism_score')
            )['avg'],
            'max_similarity': submissions.aggregate(
                max=Max('plagiarism_score')
            )['max']
        }

    def publish(self):
        """Publish the assignment"""
        if self.status == 'draft':
            self.status = 'published'
            self.save()
            
            # Create notification for students
            self._notify_students_assignment_published()

    def close(self):
        """Close the assignment for submissions"""
        if self.status == 'published':
            self.status = 'closed'
            self.save()

    def _notify_students_assignment_published(self):
        """Send notifications to students when assignment is published"""
        # This would integrate with the notification system
        # For now, we'll just log the action
        pass


class AssignmentSubmission(BaseModel):
    """
    Student submission for assignments
    """
    SUBMISSION_STATUS = (
        ('draft', _('Draft')),
        ('submitted', _('Submitted')),
        ('late', _('Late Submission')),
        ('resubmitted', _('Resubmitted')),
        ('graded', _('Graded')),
        ('returned', _('Returned for Revision')),
    )

    assignment = models.ForeignKey(
        Assignment,
        on_delete=models.CASCADE,
        related_name='submissions',
        verbose_name=_('Assignment')
    )

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='assignment_submissions',
        verbose_name=_('Student')
    )

    group_members = models.ManyToManyField(
        Student,
        blank=True,
        related_name='group_submissions',
        verbose_name=_('Group Members'),
        help_text=_('Other students in the group (for group assignments)')
    )

    submission_text = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Submission Text'),
        help_text=_('Text content of the submission')
    )

    submission_file = models.FileField(
        upload_to='assignment_submissions/',
        blank=True,
        null=True,
        verbose_name=_('Submission File')
    )

    additional_files = models.JSONField(
        default=list,
        blank=True,
        verbose_name=_('Additional Files'),
        help_text=_('List of additional file paths for multi-file submissions')
    )

    submitted_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Submitted At')
    )

    status = models.CharField(
        max_length=20,
        choices=SUBMISSION_STATUS,
        default='draft',
        verbose_name=_('Status')
    )

    is_late = models.BooleanField(
        default=False,
        verbose_name=_('Is Late Submission')
    )

    late_penalty_applied = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0.00,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name=_('Late Penalty Applied (%)')
    )

    score = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(0)],
        verbose_name=_('Score')
    )

    adjusted_score = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(0)],
        verbose_name=_('Adjusted Score'),
        help_text=_('Score after applying late penalties and adjustments')
    )

    percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name=_('Percentage')
    )

    grade_letter = models.CharField(
        max_length=5,
        blank=True,
        null=True,
        verbose_name=_('Grade Letter')
    )

    is_graded = models.BooleanField(
        default=False,
        verbose_name=_('Is Graded')
    )

    graded_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Graded At')
    )

    graded_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='graded_submissions',
        verbose_name=_('Graded By')
    )

    feedback = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Feedback'),
        help_text=_('Teacher feedback on the submission')
    )

    rubric_scores = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Rubric Scores'),
        help_text=_('Detailed scores for each rubric criterion')
    )

    plagiarism_score = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name=_('Plagiarism Score (%)')
    )

    plagiarism_report = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Plagiarism Report'),
        help_text=_('Detailed plagiarism detection results')
    )

    similarity_sources = models.JSONField(
        default=list,
        blank=True,
        verbose_name=_('Similarity Sources'),
        help_text=_('Sources with high similarity detected')
    )

    submission_count = models.PositiveIntegerField(
        default=1,
        verbose_name=_('Submission Count'),
        help_text=_('Number of times this assignment has been submitted')
    )

    word_count = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name=_('Word Count')
    )

    file_size_bytes = models.PositiveBigIntegerField(
        null=True,
        blank=True,
        verbose_name=_('File Size (Bytes)')
    )

    time_spent_minutes = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name=_('Time Spent (Minutes)'),
        help_text=_('Estimated time spent on the assignment')
    )

    class Meta:
        verbose_name = _('Assignment Submission')
        verbose_name_plural = _('Assignment Submissions')
        unique_together = ['assignment', 'student']
        ordering = ['-submitted_at']
        indexes = [
            models.Index(fields=['school', 'assignment', 'status']),
            models.Index(fields=['student', 'is_graded']),
            models.Index(fields=['submitted_at']),
            models.Index(fields=['plagiarism_score']),
        ]

    def __str__(self):
        return f"{self.assignment.title} - {self.student}"

    def clean(self):
        super().clean()
        
        if self.score and self.score > self.assignment.max_score:
            raise ValidationError(
                _('Score cannot exceed maximum score of {}.').format(
                    self.assignment.max_score
                )
            )
        
        if self.assignment.group_assignment and not self.group_members.exists():
            # Allow saving without group members initially, but warn
            pass

    def save(self, *args, **kwargs):
        # Calculate adjusted score and percentage
        if self.score is not None:
            self.adjusted_score = self.score * (1 - self.late_penalty_applied / 100)
            self.percentage = (self.adjusted_score / self.assignment.max_score) * 100
            
            # Calculate grade letter based on percentage
            self.grade_letter = self._calculate_grade_letter()
        
        # Set submission status
        if self.submitted_at and not self.is_graded:
            if self.submitted_at > self.assignment.due_date:
                self.status = 'late'
                self.is_late = True
                self.late_penalty_applied = self.assignment.calculate_late_penalty(
                    self.submitted_at
                )
            else:
                self.status = 'submitted'
        
        super().save(*args, **kwargs)

    def _calculate_grade_letter(self):
        """Calculate grade letter based on percentage"""
        if self.percentage is None:
            return None
        
        # Standard grading scale - can be customized per school
        if self.percentage >= 90:
            return 'A'
        elif self.percentage >= 80:
            return 'B'
        elif self.percentage >= 70:
            return 'C'
        elif self.percentage >= 60:
            return 'D'
        else:
            return 'F'

    def submit(self):
        """Submit the assignment"""
        if self.status == 'draft':
            self.submitted_at = timezone.now()
            self.status = 'submitted'
            
            # Check if late
            if self.submitted_at > self.assignment.due_date:
                self.is_late = True
                self.status = 'late'
                self.late_penalty_applied = self.assignment.calculate_late_penalty(
                    self.submitted_at
                )
            
            self.save()
            
            # Run plagiarism check if enabled
            if self.assignment.plagiarism_check_enabled:
                self._run_plagiarism_check()

    def resubmit(self):
        """Resubmit the assignment"""
        if (self.assignment.allow_resubmission and 
            self.submission_count < self.assignment.max_resubmissions):
            
            self.submission_count += 1
            self.submitted_at = timezone.now()
            self.status = 'resubmitted'
            self.is_graded = False
            self.graded_at = None
            self.graded_by = None
            
            self.save()
            
            # Run plagiarism check again
            if self.assignment.plagiarism_check_enabled:
                self._run_plagiarism_check()

    def grade(self, score, feedback='', graded_by=None, rubric_scores=None):
        """Grade the submission"""
        self.score = score
        self.feedback = feedback
        self.graded_by = graded_by
        self.graded_at = timezone.now()
        self.is_graded = True
        self.status = 'graded'
        
        if rubric_scores:
            self.rubric_scores = rubric_scores
        
        self.save()

    def _run_plagiarism_check(self):
        """Run plagiarism detection on the submission"""
        if not self.submission_text and not self.submission_file:
            return
        
        # Simple plagiarism detection using text similarity
        # In a real implementation, this would use a proper plagiarism detection service
        content = self.submission_text or ''
        
        if self.submission_file:
            # Extract text from file (simplified)
            try:
                with open(self.submission_file.path, 'r', encoding='utf-8') as f:
                    content += f.read()
            except:
                pass
        
        # Compare with other submissions
        similarity_score = self._calculate_similarity_score(content)
        self.plagiarism_score = similarity_score
        
        if similarity_score >= self.assignment.plagiarism_threshold:
            self.plagiarism_report = {
                'status': 'high_similarity',
                'score': similarity_score,
                'threshold': self.assignment.plagiarism_threshold,
                'detected_at': timezone.now().isoformat()
            }
        
        self.save()

    def _calculate_similarity_score(self, content):
        """Calculate similarity score with other submissions"""
        if not content.strip():
            return 0
        
        # Get other submissions for the same assignment
        other_submissions = AssignmentSubmission.objects.filter(
            assignment=self.assignment,
            submission_text__isnull=False
        ).exclude(id=self.id)
        
        max_similarity = 0
        similar_sources = []
        
        for other in other_submissions:
            if other.submission_text:
                similarity = self._text_similarity(content, other.submission_text)
                if similarity > max_similarity:
                    max_similarity = similarity
                
                if similarity > 50:  # Threshold for recording as similar source
                    similar_sources.append({
                        'student': str(other.student),
                        'similarity': similarity,
                        'submission_id': other.id
                    })
        
        self.similarity_sources = similar_sources
        return max_similarity

    def _text_similarity(self, text1, text2):
        """Calculate text similarity percentage"""
        # Simple similarity calculation using difflib
        # In production, use more sophisticated algorithms
        
        # Normalize texts
        text1 = text1.lower().strip()
        text2 = text2.lower().strip()
        
        if not text1 or not text2:
            return 0
        
        # Calculate similarity ratio
        similarity = difflib.SequenceMatcher(None, text1, text2).ratio()
        return similarity * 100

    def get_file_hash(self):
        """Generate hash of submitted file for integrity checking"""
        if not self.submission_file:
            return None
        
        try:
            with open(self.submission_file.path, 'rb') as f:
                file_hash = hashlib.md5(f.read()).hexdigest()
                return file_hash
        except:
            return None

    def calculate_word_count(self):
        """Calculate word count of submission"""
        content = self.submission_text or ''
        
        if self.submission_file:
            try:
                with open(self.submission_file.path, 'r', encoding='utf-8') as f:
                    content += f.read()
            except:
                pass
        
        # Simple word count
        words = content.split()
        self.word_count = len(words)
        self.save(update_fields=['word_count'])
        
        return self.word_count


class AssignmentGroup(BaseModel):
    """
    Group management for group assignments
    """
    assignment = models.ForeignKey(
        Assignment,
        on_delete=models.CASCADE,
        related_name='groups',
        verbose_name=_('Assignment')
    )

    name = models.CharField(
        max_length=100,
        verbose_name=_('Group Name')
    )

    leader = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='led_groups',
        verbose_name=_('Group Leader')
    )

    members = models.ManyToManyField(
        Student,
        related_name='assignment_groups',
        verbose_name=_('Group Members')
    )

    is_finalized = models.BooleanField(
        default=False,
        verbose_name=_('Is Finalized'),
        help_text=_('Whether group membership is finalized')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )

    class Meta:
        verbose_name = _('Assignment Group')
        verbose_name_plural = _('Assignment Groups')
        unique_together = ['assignment', 'name']
        ordering = ['assignment', 'name']

    def __str__(self):
        return f"{self.assignment.title} - {self.name}"

    def clean(self):
        super().clean()
        
        if not self.assignment.group_assignment:
            raise ValidationError(_('This assignment does not allow groups.'))

    @property
    def member_count(self):
        """Get number of group members"""
        return self.members.count()

    def add_member(self, student):
        """Add a member to the group"""
        if self.is_finalized:
            raise ValidationError(_('Cannot modify finalized group.'))
        
        if self.member_count >= self.assignment.max_group_size:
            raise ValidationError(
                _('Group is at maximum capacity of {} members.').format(
                    self.assignment.max_group_size
                )
            )
        
        self.members.add(student)

    def remove_member(self, student):
        """Remove a member from the group"""
        if self.is_finalized:
            raise ValidationError(_('Cannot modify finalized group.'))
        
        self.members.remove(student)

    def finalize(self):
        """Finalize the group membership"""
        if self.member_count < 2:
            raise ValidationError(_('Group must have at least 2 members.'))
        
        self.is_finalized = True
        self.save()


class AssignmentAnalytics(BaseModel):
    """
    Analytics and reporting for assignments
    """
    ANALYTICS_TYPES = (
        ('assignment', _('Assignment Analytics')),
        ('student', _('Student Performance')),
        ('class', _('Class Performance')),
        ('subject', _('Subject Analytics')),
    )

    assignment = models.ForeignKey(
        Assignment,
        on_delete=models.CASCADE,
        related_name='analytics',
        verbose_name=_('Assignment')
    )

    analytics_type = models.CharField(
        max_length=20,
        choices=ANALYTICS_TYPES,
        verbose_name=_('Analytics Type')
    )

    data = models.JSONField(
        default=dict,
        verbose_name=_('Analytics Data')
    )

    generated_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Generated At')
    )

    insights = models.JSONField(
        default=list,
        verbose_name=_('Generated Insights')
    )

    recommendations = models.JSONField(
        default=list,
        verbose_name=_('Recommendations')
    )

    class Meta:
        verbose_name = _('Assignment Analytics')
        verbose_name_plural = _('Assignment Analytics')
        ordering = ['-generated_at']
        indexes = [
            models.Index(fields=['assignment', 'analytics_type']),
            models.Index(fields=['generated_at']),
        ]

    def __str__(self):
        return f"{self.assignment.title} - {self.get_analytics_type_display()}"

    def generate_assignment_analytics(self):
        """Generate comprehensive assignment analytics"""
        submissions = self.assignment.submissions.all()
        graded_submissions = submissions.filter(is_graded=True)
        
        if not submissions.exists():
            return
        
        # Basic statistics
        stats = self.assignment.get_submission_statistics()
        
        # Score statistics
        if graded_submissions.exists():
            scores = graded_submissions.values_list('adjusted_score', flat=True)
            stats.update({
                'average_score': sum(scores) / len(scores),
                'highest_score': max(scores),
                'lowest_score': min(scores),
                'median_score': sorted(scores)[len(scores) // 2],
                'score_distribution': self._calculate_score_distribution(scores)
            })
        
        # Time analysis
        submission_times = submissions.filter(
            submitted_at__isnull=False
        ).values_list('submitted_at', flat=True)
        
        if submission_times:
            stats.update({
                'early_submissions': len([
                    t for t in submission_times 
                    if t < self.assignment.due_date - timedelta(days=1)
                ]),
                'on_time_submissions': len([
                    t for t in submission_times 
                    if t <= self.assignment.due_date
                ]),
                'late_submissions': len([
                    t for t in submission_times 
                    if t > self.assignment.due_date
                ])
            })
        
        # Plagiarism analysis
        if self.assignment.plagiarism_check_enabled:
            plagiarism_data = self.assignment.get_plagiarism_summary()
            if plagiarism_data:
                stats.update(plagiarism_data)
        
        self.data = stats
        self._generate_insights()
        self.save()

    def _calculate_score_distribution(self, scores):
        """Calculate score distribution by grade ranges"""
        distribution = {
            'A (90-100)': 0,
            'B (80-89)': 0,
            'C (70-79)': 0,
            'D (60-69)': 0,
            'F (0-59)': 0
        }
        
        max_score = self.assignment.max_score
        
        for score in scores:
            percentage = (score / max_score) * 100
            
            if percentage >= 90:
                distribution['A (90-100)'] += 1
            elif percentage >= 80:
                distribution['B (80-89)'] += 1
            elif percentage >= 70:
                distribution['C (70-79)'] += 1
            elif percentage >= 60:
                distribution['D (60-69)'] += 1
            else:
                distribution['F (0-59)'] += 1
        
        return distribution

    def _generate_insights(self):
        """Generate insights based on analytics data"""
        insights = []
        data = self.data
        
        # Submission rate insights
        if data.get('submission_rate', 0) < 80:
            insights.append({
                'type': 'warning',
                'message': f"Low submission rate: {data.get('submission_rate', 0):.1f}%",
                'recommendation': 'Consider sending reminders or extending deadline'
            })
        
        # Performance insights
        if data.get('average_score'):
            avg_percentage = (data['average_score'] / self.assignment.max_score) * 100
            
            if avg_percentage < 70:
                insights.append({
                    'type': 'concern',
                    'message': f"Low average performance: {avg_percentage:.1f}%",
                    'recommendation': 'Review assignment difficulty and provide additional support'
                })
            elif avg_percentage > 95:
                insights.append({
                    'type': 'info',
                    'message': f"Very high average performance: {avg_percentage:.1f}%",
                    'recommendation': 'Consider increasing assignment difficulty for better differentiation'
                })
        
        # Late submission insights
        late_rate = data.get('late_submissions', 0) / data.get('total_students', 1) * 100
        if late_rate > 30:
            insights.append({
                'type': 'warning',
                'message': f"High late submission rate: {late_rate:.1f}%",
                'recommendation': 'Review assignment timeline and student workload'
            })
        
        # Plagiarism insights
        if data.get('high_similarity_count', 0) > 0:
            insights.append({
                'type': 'alert',
                'message': f"{data['high_similarity_count']} submissions flagged for high similarity",
                'recommendation': 'Review flagged submissions for potential plagiarism'
            })
        
        self.insights = insights

    def generate_student_performance_analytics(self, student):
        """Generate analytics for a specific student's performance"""
        student_submissions = AssignmentSubmission.objects.filter(
            student=student,
            assignment__class_subject__class_obj=self.assignment.class_subject.class_obj
        )
        
        if not student_submissions.exists():
            return
        
        # Performance trends
        graded_submissions = student_submissions.filter(is_graded=True).order_by(
            'assignment__assigned_date'
        )
        
        performance_data = {
            'total_assignments': student_submissions.count(),
            'completed_assignments': student_submissions.filter(
                status__in=['submitted', 'late', 'graded']
            ).count(),
            'average_score': graded_submissions.aggregate(
                avg=Avg('adjusted_score')
            )['avg'],
            'improvement_trend': self._calculate_improvement_trend(graded_submissions),
            'submission_patterns': self._analyze_submission_patterns(student_submissions)
        }
        
        self.data = performance_data
        self.save()

    def _calculate_improvement_trend(self, submissions):
        """Calculate if student performance is improving over time"""
        if submissions.count() < 3:
            return 'insufficient_data'
        
        scores = list(submissions.values_list('percentage', flat=True))
        
        # Simple trend calculation
        first_half = scores[:len(scores)//2]
        second_half = scores[len(scores)//2:]
        
        first_avg = sum(first_half) / len(first_half)
        second_avg = sum(second_half) / len(second_half)
        
        if second_avg > first_avg + 5:
            return 'improving'
        elif second_avg < first_avg - 5:
            return 'declining'
        else:
            return 'stable'

    def _analyze_submission_patterns(self, submissions):
        """Analyze student's submission timing patterns"""
        patterns = {
            'early_submissions': 0,
            'on_time_submissions': 0,
            'late_submissions': 0,
            'average_days_before_due': 0
        }
        
        submitted = submissions.filter(submitted_at__isnull=False)
        
        if not submitted.exists():
            return patterns
        
        days_before_due = []
        
        for submission in submitted:
            days_diff = (submission.assignment.due_date - submission.submitted_at).days
            days_before_due.append(days_diff)
            
            if days_diff > 1:
                patterns['early_submissions'] += 1
            elif days_diff >= 0:
                patterns['on_time_submissions'] += 1
            else:
                patterns['late_submissions'] += 1
        
        if days_before_due:
            patterns['average_days_before_due'] = sum(days_before_due) / len(days_before_due)
        
        return patterns