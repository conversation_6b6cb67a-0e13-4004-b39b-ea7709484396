from django.urls import path
from . import views

app_name = 'academics'

urlpatterns = [
    # Academic Dashboard
    path('', views.AcademicDashboardView.as_view(), name='dashboard'),

    # Study Year Management
    path('study-year/', views.StudyYearView.as_view(), name='study_year'),
    path('academic-years/', views.AcademicYearListView.as_view(), name='academic_years'),
    path('semesters/', views.SemesterListView.as_view(), name='semesters'),

    # Subject Management
    path('subjects/', views.SubjectListView.as_view(), name='subjects'),
    path('subjects/add/', views.SubjectCreateView.as_view(), name='subject_add'),
    path('subjects/<uuid:pk>/', views.SubjectDetailView.as_view(), name='subject_detail'),
    path('subjects/<uuid:pk>/edit/', views.SubjectUpdateView.as_view(), name='subject_edit'),
    path('subjects/<uuid:pk>/delete/', views.SubjectDeleteView.as_view(), name='subject_delete'),

    # Teacher Management
    path('teachers/', views.TeacherListView.as_view(), name='teachers'),
    path('teachers/add/', views.TeacherCreateView.as_view(), name='teacher_add'),
    path('teachers/<uuid:pk>/', views.TeacherDetailView.as_view(), name='teacher_detail'),
    path('teachers/<uuid:pk>/edit/', views.TeacherUpdateView.as_view(), name='teacher_edit'),

    # Class Management
    path('classes/', views.ClassListView.as_view(), name='classes'),
    path('classes/add/', views.ClassCreateView.as_view(), name='class_add'),
    path('classes/<uuid:pk>/', views.ClassDetailView.as_view(), name='class_detail'),
    path('classes/<uuid:pk>/edit/', views.ClassUpdateView.as_view(), name='class_edit'),

    # Schedule Management
    path('schedules/', views.ScheduleListView.as_view(), name='schedules'),
    path('schedules/add/', views.ScheduleCreateView.as_view(), name='schedule_add'),
    path('schedules/create/', views.ScheduleCreateView.as_view(), name='schedule_create'),  # Alternative name
    path('schedules/<uuid:pk>/', views.ScheduleDetailView.as_view(), name='schedule_detail'),
    path('schedules/<uuid:pk>/edit/', views.ScheduleUpdateView.as_view(), name='schedule_edit'),
    path('schedules/<uuid:pk>/update/', views.ScheduleUpdateView.as_view(), name='schedule_update'),  # Alternative name
    path('schedules/timetable/', views.TimetableView.as_view(), name='timetable'),

    # Grades and Assessment
    path('grades/', views.GradeManagementView.as_view(), name='grades'),
    path('grades/create/', views.GradeCreateView.as_view(), name='grade_create'),
    path('grades/<uuid:pk>/', views.GradeDetailView.as_view(), name='grade_detail'),
    path('grades/<uuid:pk>/update/', views.GradeUpdateView.as_view(), name='grade_update'),
    path('grades/entry/', views.GradeEntryView.as_view(), name='grade_entry'),
    path('grades/reports/', views.GradeReportsView.as_view(), name='grade_reports'),
    path('grades/report/', views.GradeReportView.as_view(), name='grade_report'),
    path('grades/export/', views.GradeExportView.as_view(), name='grade_export'),
    path('grades/transcripts/', views.TranscriptsView.as_view(), name='transcripts'),

    # Exams
    path('exams/', views.ExamListView.as_view(), name='exams'),
    path('exams/add/', views.ExamCreateView.as_view(), name='exam_add'),
    path('exams/<uuid:pk>/', views.ExamDetailView.as_view(), name='exam_detail'),
    path('exams/<uuid:pk>/edit/', views.ExamUpdateView.as_view(), name='exam_edit'),
    path('exams/schedule/', views.ExamScheduleView.as_view(), name='exam_schedule'),
    path('exams/results/', views.ExamResultsView.as_view(), name='exam_results'),

    # Attendance
    path('attendance/', views.AttendanceView.as_view(), name='attendance'),
    path('attendance/create/', views.AttendanceCreateView.as_view(), name='attendance_create'),
    path('attendance/<uuid:pk>/', views.AttendanceDetailView.as_view(), name='attendance_detail'),
    path('attendance/<uuid:pk>/update/', views.AttendanceUpdateView.as_view(), name='attendance_update'),
    path('attendance/take/', views.TakeAttendanceView.as_view(), name='take_attendance'),
    path('attendance/report/', views.AttendanceReportView.as_view(), name='attendance_report'),
    path('attendance/export/', views.AttendanceExportView.as_view(), name='attendance_export'),
    path('attendance/reports/', views.AttendanceReportsView.as_view(), name='attendance_reports'),
    path('attendance/summary/', views.AttendanceSummaryView.as_view(), name='attendance_summary'),

    # Curriculum and Syllabus
    path('curriculum/', views.CurriculumView.as_view(), name='curriculum'),
    path('syllabus/', views.SyllabusView.as_view(), name='syllabus'),
    path('lesson-plans/', views.LessonPlansView.as_view(), name='lesson_plans'),

    # Academic Reports
    path('reports/', views.AcademicReportsView.as_view(), name='reports'),
    path('reports/progress/', views.ProgressReportsView.as_view(), name='progress_reports'),
    path('reports/performance/', views.PerformanceReportsView.as_view(), name='performance_reports'),
    path('reports/class-summary/', views.ClassSummaryReportsView.as_view(), name='class_summary_reports'),

    # Additional Academic Features
    path('attendance-rules/', views.AttendanceRuleListView.as_view(), name='attendance_rules'),
    path('attendance-rules/add/', views.AttendanceRuleCreateView.as_view(), name='attendance_rule_add'),
    path('attendance-rules/<uuid:pk>/', views.AttendanceRuleDetailView.as_view(), name='attendance_rule_detail'),
    path('attendance-rules/<uuid:pk>/edit/', views.AttendanceRuleUpdateView.as_view(), name='attendance_rule_edit'),

    path('attendance-sessions/', views.AttendanceSessionListView.as_view(), name='attendance_sessions'),
    path('attendance-sessions/add/', views.AttendanceSessionCreateView.as_view(), name='attendance_session_add'),
    path('attendance-sessions/<uuid:pk>/', views.AttendanceSessionDetailView.as_view(), name='attendance_session_detail'),
    path('attendance-sessions/<uuid:pk>/edit/', views.AttendanceSessionUpdateView.as_view(), name='attendance_session_edit'),

    path('biometric-devices/', views.BiometricDeviceListView.as_view(), name='biometric_devices'),
    path('biometric-devices/add/', views.BiometricDeviceCreateView.as_view(), name='biometric_device_add'),
    path('biometric-devices/<uuid:pk>/', views.BiometricDeviceDetailView.as_view(), name='biometric_device_detail'),
    path('biometric-devices/<uuid:pk>/edit/', views.BiometricDeviceUpdateView.as_view(), name='biometric_device_edit'),

    path('class-subjects/', views.ClassSubjectListView.as_view(), name='class_subjects'),
    path('class-subjects/add/', views.ClassSubjectCreateView.as_view(), name='class_subject_add'),
    path('class-subjects/<uuid:pk>/', views.ClassSubjectDetailView.as_view(), name='class_subject_detail'),
    path('class-subjects/<uuid:pk>/edit/', views.ClassSubjectUpdateView.as_view(), name='class_subject_edit'),

    path('curriculum-plans/', views.CurriculumPlanListView.as_view(), name='curriculum_plans'),
    path('curriculum-plans/add/', views.CurriculumPlanCreateView.as_view(), name='curriculum_plan_add'),
    path('curriculum-plans/<uuid:pk>/', views.CurriculumPlanDetailView.as_view(), name='curriculum_plan_detail'),
    path('curriculum-plans/<uuid:pk>/edit/', views.CurriculumPlanUpdateView.as_view(), name='curriculum_plan_edit'),

    path('curriculum-subjects/', views.CurriculumSubjectListView.as_view(), name='curriculum_subjects'),
    path('curriculum-subjects/add/', views.CurriculumSubjectCreateView.as_view(), name='curriculum_subject_add'),
    path('curriculum-subjects/<uuid:pk>/', views.CurriculumSubjectDetailView.as_view(), name='curriculum_subject_detail'),
    path('curriculum-subjects/<uuid:pk>/edit/', views.CurriculumSubjectUpdateView.as_view(), name='curriculum_subject_edit'),

    path('curriculums/', views.CurriculumListView.as_view(), name='curriculums'),
    path('curriculums/add/', views.CurriculumCreateView.as_view(), name='curriculum_add'),
    path('curriculums/<uuid:pk>/', views.CurriculumDetailView.as_view(), name='curriculum_detail'),
    path('curriculums/<uuid:pk>/edit/', views.CurriculumUpdateView.as_view(), name='curriculum_edit'),

    path('grade-capacity/', views.GradeCapacityManagementListView.as_view(), name='grade_capacity'),
    path('grade-capacity/add/', views.GradeCapacityManagementCreateView.as_view(), name='grade_capacity_add'),
    path('grade-capacity/<uuid:pk>/', views.GradeCapacityManagementDetailView.as_view(), name='grade_capacity_detail'),
    path('grade-capacity/<uuid:pk>/edit/', views.GradeCapacityManagementUpdateView.as_view(), name='grade_capacity_edit'),
]
