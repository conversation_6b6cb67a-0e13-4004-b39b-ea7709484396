from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.views.generic import TemplateView, ListView, DetailView, CreateView, UpdateView, DeleteView, View
from django.contrib import messages
from django.urls import reverse_lazy
from django.db.models import Q, Count, Avg, Sum
from django.db import models
from django.http import HttpResponse, JsonResponse
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from datetime import datetime, timedelta
from core.models import AcademicYear
from core.school_utils import get_current_school
from .models import (
    Subject, Teacher, ClassSubject, Schedule, Exam, StudentGrade,
    StudentAttendance, Curriculum, CurriculumSubject, GradeCapacityManagement,
    CurriculumPlan, AttendanceRule, BiometricDevice, AttendanceSession
)
from .forms import (
    AcademicYearForm, SubjectForm, TeacherForm, ClassSubjectForm, ScheduleForm,
    ExamForm, StudentGradeForm, StudentAttendanceForm, CurriculumForm,
    CurriculumSubjectForm, BulkAttendanceForm, BulkGradeEntryForm,
    GradeCapacityManagementForm, CurriculumPlanForm, AttendanceRuleForm, 
    BiometricDeviceForm, AttendanceSessionForm
)
from students.models import Student, Grade, Class

def placeholder_view(request):
    return HttpResponse("Academics module - Coming soon!")

# Academic Dashboard
class AcademicDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get current school
        school = get_current_school(self.request)
        if not school:
            return context
        
        # Get current academic year
        current_year = AcademicYear.objects.filter(
            school=school,
            is_current=True
        ).first()
        
        # Basic counts
        context['total_subjects'] = Subject.objects.filter(school=school).count()
        context['total_teachers'] = Teacher.objects.filter(school=school).count()
        context['total_classes'] = Class.objects.filter(school=school).count()
        
        # Upcoming exams
        from datetime import date, timedelta
        upcoming_exams = Exam.objects.filter(
            school=school,
            exam_date__gte=date.today(),
            exam_date__lte=date.today() + timedelta(days=30)
        ).select_related('class_subject__subject', 'class_subject__class_obj')[:5]
        context['upcoming_exams'] = upcoming_exams
        
        # Today's schedule
        if current_year:
            todays_schedule = Schedule.objects.filter(
                school=school,
                class_subject__academic_year=current_year,
                day_of_week=date.today().weekday() + 1  # Django uses 1-7, Python uses 0-6
            ).select_related('class_subject__subject', 'class_subject__class_obj', 'class_subject__teacher__user')[:10]
            context['todays_schedule'] = todays_schedule
        else:
            context['todays_schedule'] = []
        
        # Subject overview with statistics
        subject_overview = []
        subjects = Subject.objects.filter(school=school)
        for subject in subjects:
            class_subjects = ClassSubject.objects.filter(subject=subject, school=school)
            
            # Calculate statistics
            teacher_count = class_subjects.values('teacher').distinct().count()
            class_count = class_subjects.count()
            
            # Get average grade (if StudentGrade model exists)
            try:
                from django.db.models import Avg
                avg_grade = StudentGrade.objects.filter(
                    class_subject__subject=subject,
                    school=school
                ).aggregate(avg=Avg('grade'))['avg']
            except:
                avg_grade = None
            
            subject_overview.append({
                'name': subject.name,
                'teacher_count': teacher_count,
                'class_count': class_count,
                'student_count': 0,  # This would need to be calculated based on enrollments
                'avg_grade': avg_grade
            })
        
        context['subject_overview'] = subject_overview
        context['current_academic_year'] = current_year
        
        # Academic calendar info
        if current_year:
            from datetime import date
            today = date.today()
            start_date = current_year.start_date
            end_date = current_year.end_date
            
            if start_date and end_date:
                total_days = (end_date - start_date).days
                elapsed_days = (today - start_date).days
                remaining_days = (end_date - today).days
                
                context['days_remaining'] = max(0, remaining_days)
                context['academic_progress'] = min(100, max(0, (elapsed_days / total_days) * 100)) if total_days > 0 else 0
            else:
                context['days_remaining'] = 0
                context['academic_progress'] = 0
        else:
            context['days_remaining'] = 0
            context['academic_progress'] = 0
        
        return context

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Basic statistics
        context['total_subjects'] = Subject.objects.filter(is_active=True).count()
        context['total_teachers'] = Teacher.objects.filter(is_active=True).count()
        context['total_classes'] = Class.objects.filter(is_active=True).count()
        context['total_students'] = Student.objects.filter(is_active=True).count()

        # Current academic year
        current_year = AcademicYear.objects.filter(is_current=True).first()
        context['current_academic_year'] = current_year

        # Upcoming exams
        context['upcoming_exams'] = Exam.objects.filter(
            exam_date__gte=timezone.now().date(),
            is_published=True
        ).order_by('exam_date', 'start_time')[:5]

        # Recent grades
        context['recent_grades'] = StudentGrade.objects.select_related(
            'student', 'exam'
        ).order_by('-graded_at')[:10]

        # Today's schedule
        today = timezone.now().date()
        weekday = today.strftime('%A').lower()
        context['todays_schedule'] = Schedule.objects.filter(
            day_of_week=weekday,
            is_active=True
        ).select_related('class_subject', 'class_subject__subject', 'class_subject__teacher').order_by('start_time')[:10]

        # Attendance statistics for current month
        current_month = timezone.now().replace(day=1)
        next_month = (current_month + timedelta(days=32)).replace(day=1)

        total_attendance = StudentAttendance.objects.filter(
            session__session_date__gte=current_month,
            session__session_date__lt=next_month
        ).count()

        # Subject overview with real data
        context['subject_overview'] = Subject.objects.filter(is_active=True).annotate(
            teacher_count=Count('class_subjects__teacher', distinct=True),
            class_count=Count('class_subjects__class_obj', distinct=True),
            student_count=Count('class_subjects__class_obj__students', distinct=True),
            avg_grade=Avg('class_subjects__exams__student_grades__percentage')
        ).select_related().order_by('name')[:5]

        # Exam statistics
        today = timezone.now().date()
        current_month_start = today.replace(day=1)
        context['completed_exams_this_month'] = Exam.objects.filter(
            exam_date__gte=current_month_start,
            exam_date__lt=today,
            is_published=True
        ).count()

        # Average score this month
        monthly_grades = StudentGrade.objects.filter(
            exam__exam_date__gte=current_month_start,
            exam__exam_date__lt=today
        ).aggregate(avg_score=Avg('percentage'))
        context['monthly_avg_score'] = monthly_grades['avg_score'] or 0

        # Academic calendar info
        if current_year:
            total_days = (current_year.end_date - current_year.start_date).days
            elapsed_days = (today - current_year.start_date).days
            remaining_days = (current_year.end_date - today).days
            context['academic_progress'] = min(100, max(0, (elapsed_days / total_days) * 100)) if total_days > 0 else 0
            context['days_remaining'] = max(0, remaining_days)
        else:
            context['academic_progress'] = 0
            context['days_remaining'] = 0

        present_attendance = StudentAttendance.objects.filter(
            session__session_date__gte=current_month,
            session__session_date__lt=next_month,
            status__in=['present', 'late']
        ).count()

        context['attendance_rate'] = (present_attendance / total_attendance * 100) if total_attendance > 0 else 0

        # Subject performance (average grades)
        subject_performance = StudentGrade.objects.filter(
            exam__exam_date__gte=current_month
        ).values(
            'exam__class_subject__subject__name'
        ).annotate(
            avg_percentage=Avg('percentage')
        ).order_by('-avg_percentage')[:5]

        context['subject_performance'] = subject_performance

        # Chart data for performance
        subjects = [item['exam__class_subject__subject__name'] for item in subject_performance]
        averages = [float(item['avg_percentage']) if item['avg_percentage'] else 0 for item in subject_performance]

        context['performance_labels'] = subjects
        context['performance_data'] = averages

        return context

# Study Year Management
class StudyYearView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/study_year.html'

class AcademicYearListView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/academic_years.html'

class SemesterListView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/semesters.html'

# Subject Management
class SubjectListView(LoginRequiredMixin, ListView):
    model = Subject
    template_name = 'academics/subject_list.html'
    context_object_name = 'subjects'
    paginate_by = 20

    def get_queryset(self):
        return Subject.objects.annotate(
            teacher_count=Count('class_subjects__teacher', distinct=True),
            class_count=Count('class_subjects__class_obj', distinct=True)
        ).order_by('name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Statistics
        context['total_subjects'] = Subject.objects.count()
        context['active_subjects'] = Subject.objects.filter(is_active=True).count()
        context['total_teachers'] = Teacher.objects.filter(is_active=True).count()
        context['total_classes'] = ClassSubject.objects.filter(is_active=True).count()

        return context

    def get_queryset(self):
        queryset = Subject.objects.all()
        search = self.request.GET.get('search')

        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(name_ar__icontains=search) |
                Q(code__icontains=search)
            )

        return queryset.order_by('name')


class SubjectCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Subject
    form_class = SubjectForm
    template_name = 'academics/subject_form.html'
    permission_required = 'academics.add_subject'
    success_url = reverse_lazy('academics:subjects')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        from core.school_utils import get_current_school
        current_school = get_current_school(self.request)
        if current_school:
            kwargs['school'] = current_school
        return kwargs

    def form_valid(self, form):
        messages.success(self.request, _('Subject created successfully!'))
        return super().form_valid(form)


class SubjectDetailView(LoginRequiredMixin, DetailView):
    model = Subject
    template_name = 'academics/subject_detail.html'
    context_object_name = 'subject'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        subject = self.get_object()

        # Get classes teaching this subject
        context['class_subjects'] = ClassSubject.objects.filter(
            subject=subject,
            is_active=True
        ).select_related('class_obj', 'teacher')

        # Get recent exams
        context['recent_exams'] = Exam.objects.filter(
            class_subject__subject=subject
        ).order_by('-exam_date')[:5]

        return context


class SubjectUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = Subject
    form_class = SubjectForm
    template_name = 'academics/subject_form.html'
    permission_required = 'academics.change_subject'
    success_url = reverse_lazy('academics:subjects')

    def form_valid(self, form):
        messages.success(self.request, _('Subject updated successfully!'))
        return super().form_valid(form)

class SubjectDeleteView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/subject_confirm_delete.html'

# Teacher Management
class TeacherListView(LoginRequiredMixin, ListView):
    model = Teacher
    template_name = 'academics/teachers.html'
    context_object_name = 'teachers'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = Teacher.objects.select_related('user').prefetch_related('subjects', 'class_subjects')
        search = self.request.GET.get('search')
        department = self.request.GET.get('department')
        status = self.request.GET.get('status')
        experience = self.request.GET.get('experience')
        
        if search:
            queryset = queryset.filter(
                Q(user__first_name__icontains=search) |
                Q(user__last_name__icontains=search) |
                Q(employee_id__icontains=search)
            )
        
        if department:
            queryset = queryset.filter(department=department)
            
        if status:
            if status == 'active':
                queryset = queryset.filter(is_active=True)
            elif status == 'inactive':
                queryset = queryset.filter(is_active=False)
        
        if experience:
            if experience == '0-2':
                queryset = queryset.filter(experience_years__lte=2)
            elif experience == '3-5':
                queryset = queryset.filter(experience_years__gte=3, experience_years__lte=5)
            elif experience == '6-10':
                queryset = queryset.filter(experience_years__gte=6, experience_years__lte=10)
            elif experience == '10+':
                queryset = queryset.filter(experience_years__gt=10)
        
        return queryset.annotate(
            student_count=Count('class_subjects__class_obj__students', distinct=True)
        )
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Statistics
        context['total_teachers'] = Teacher.objects.count()
        context['active_teachers'] = Teacher.objects.filter(is_active=True).count()
        context['subjects_taught'] = Subject.objects.filter(teachers__isnull=False).distinct().count()
        
        # Count classes scheduled for today
        today = timezone.now().date()
        weekday = today.strftime('%A').lower()
        context['classes_today'] = Schedule.objects.filter(
            day_of_week=weekday,
            is_active=True
        ).count()
        
        # Get unique departments for filter
        context['departments'] = Teacher.objects.exclude(
            department__isnull=True
        ).exclude(
            department=''
        ).values_list('department', flat=True).distinct()
        
        return context

class TeacherCreateView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/teacher_form.html'

class TeacherDetailView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/teacher_detail.html'

class TeacherUpdateView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/teacher_form.html'

# Class Management - Redirect to Students App
from django.shortcuts import redirect
from django.urls import reverse

class ClassListView(LoginRequiredMixin, View):
    def get(self, request, *args, **kwargs):
        return redirect('students:class_list')

class ClassCreateView(LoginRequiredMixin, View):
    def get(self, request, *args, **kwargs):
        return redirect('students:class_add')

class ClassDetailView(LoginRequiredMixin, View):
    def get(self, request, *args, **kwargs):
        pk = kwargs.get('pk')
        if pk:
            return redirect('students:class_detail', pk=pk)
        return redirect('students:class_list')

class ClassUpdateView(LoginRequiredMixin, View):
    def get(self, request, *args, **kwargs):
        pk = kwargs.get('pk')
        if pk:
            return redirect('students:class_edit', pk=pk)
        return redirect('students:class_list')

# Schedule Management
class ScheduleListView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/schedules.html'

class ScheduleCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Schedule
    form_class = ScheduleForm
    template_name = 'academics/schedule_form.html'
    permission_required = 'academics.add_schedule'
    success_url = reverse_lazy('academics:schedules')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        from core.school_utils import get_current_school
        current_school = get_current_school(self.request)
        if current_school:
            kwargs['school'] = current_school
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get active class subjects for dropdown
        context['class_subjects'] = ClassSubject.objects.filter(
            is_active=True
        ).select_related('class_obj', 'subject', 'teacher').order_by(
            'class_obj__grade__level', 'class_obj__name', 'subject__name'
        )

        # Get current academic year
        context['current_academic_year'] = AcademicYear.objects.filter(is_current=True).first()

        # Days of week for the form
        context['days_of_week'] = Schedule.DAYS_OF_WEEK

        return context

    def form_valid(self, form):
        messages.success(self.request, _('Schedule created successfully!'))
        return super().form_valid(form)
        
    def post(self, request, *args, **kwargs):
        # Check if this is a submission from the timetable view
        if 'class_id' in request.POST and 'subject_id' in request.POST and 'teacher_id' in request.POST:
            try:
                from core.school_utils import get_current_school
                current_school = get_current_school(request)
                
                if not current_school:
                    messages.error(request, _('No school selected. Please select a school first.'))
                    return redirect('core:school_select')
                
                # Get the class
                class_id = request.POST.get('class_id')
                class_obj = Class.objects.get(id=class_id, school=current_school)
                
                # Get the subject and teacher
                subject_id = request.POST.get('subject_id')
                teacher_id = request.POST.get('teacher_id')
                subject = Subject.objects.get(id=subject_id, school=current_school)
                teacher = Teacher.objects.get(id=teacher_id, school=current_school)
                
                # Get current academic year for this school
                current_academic_year = AcademicYear.objects.filter(
                    school=current_school, 
                    is_current=True
                ).first()
                
                if not current_academic_year:
                    messages.error(request, _('No current academic year found for this school.'))
                    return redirect('academics:timetable')
                
                # Get current semester for this academic year
                current_semester = current_academic_year.semesters.filter(is_current=True).first()
                
                if not current_semester:
                    messages.error(request, _('No current semester found for this academic year.'))
                    return redirect('academics:timetable')
                
                # Get or create the class subject
                class_subject, created = ClassSubject.objects.get_or_create(
                    class_obj=class_obj,
                    subject=subject,
                    teacher=teacher,
                    academic_year=current_academic_year,
                    semester=current_semester,
                    school=current_school,
                    defaults={'weekly_hours': 1}
                )
                
                # Get the day and time
                day_of_week = request.POST.get('day_of_week')
                start_time = request.POST.get('start_time')
                end_time = request.POST.get('end_time')
                room_number = request.POST.get('room_number', '')
                
                # Create or update the schedule
                schedule, created = Schedule.objects.update_or_create(
                    class_subject=class_subject,
                    day_of_week=day_of_week,
                    start_time=start_time,
                    school=current_school,
                    defaults={
                        'end_time': end_time,
                        'room_number': room_number
                    }
                )
                
                messages.success(request, _('Schedule updated successfully!'))
                return redirect(f'/academics/schedules/timetable/?class={class_id}')
                
            except (Class.DoesNotExist, Subject.DoesNotExist, Teacher.DoesNotExist) as e:
                messages.error(request, _('Error creating schedule: {0}').format(str(e)))
                return redirect('academics:timetable')
        
        # Otherwise, process as a normal form submission
        return super().post(request, *args, **kwargs)

class ScheduleDetailView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/schedule_detail.html'

class ScheduleUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = Schedule
    form_class = ScheduleForm
    template_name = 'academics/schedule_form.html'
    permission_required = 'academics.change_schedule'
    success_url = reverse_lazy('academics:schedules')

    def form_valid(self, form):
        messages.success(self.request, _('Schedule updated successfully!'))
        return super().form_valid(form)

class TimetableView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/timetable.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get current academic year
        current_year = AcademicYear.objects.filter(is_current=True).first()
        context['current_academic_year'] = current_year

        # Get all classes for dropdown
        context['grades'] = Grade.objects.filter(is_active=True).order_by('level')
        context['classes'] = Class.objects.filter(is_active=True).select_related('grade').order_by('grade__level', 'name')

        # Get selected class or default to first class
        class_id = self.request.GET.get('class')
        if class_id:
            selected_class = get_object_or_404(Class, id=class_id)
        else:
            selected_class = Class.objects.filter(is_active=True).first()

        context['selected_class'] = selected_class

        if selected_class:
            # Get class subjects
            class_subjects = ClassSubject.objects.filter(
                class_obj=selected_class,
                is_active=True
            ).select_related('subject', 'teacher')
            context['class_subjects'] = class_subjects

            # Get schedule for each day
            days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday']
            schedule_by_day = {}

            for day in days:
                day_schedule = Schedule.objects.filter(
                    class_subject__class_obj=selected_class,
                    day_of_week=day,
                    is_active=True
                ).select_related(
                    'class_subject',
                    'class_subject__subject',
                    'class_subject__teacher'
                ).order_by('start_time')

                schedule_by_day[day] = day_schedule

            context['schedule_by_day'] = schedule_by_day
            context['days'] = days

            # Get class teacher and student count
            context['class_teacher'] = getattr(selected_class, 'class_teacher', None)
            context['student_count'] = Student.objects.filter(
                current_class=selected_class,
                is_active=True
            ).count()
            
            # Get all subjects and teachers for the schedule form
            context['subjects'] = Subject.objects.filter(is_active=True).order_by('name')
            context['teachers'] = Teacher.objects.filter(is_active=True).order_by('user__first_name', 'user__last_name')

            # Get total weekly hours
            total_hours = 0
            for day, schedules in schedule_by_day.items():
                for schedule in schedules:
                    if schedule.start_time and schedule.end_time:
                        start = datetime.strptime(str(schedule.start_time), '%H:%M:%S')
                        end = datetime.strptime(str(schedule.end_time), '%H:%M:%S')
                        duration = (end - start).seconds / 3600
                        total_hours += duration

            context['total_weekly_hours'] = round(total_hours, 1)

        return context

# Grades and Assessment
class GradeManagementView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/grades.html'

class GradeCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = StudentGrade
    form_class = StudentGradeForm
    template_name = 'academics/grade_form.html'
    permission_required = 'academics.add_studentgrade'
    success_url = reverse_lazy('academics:grades')

    def form_valid(self, form):
        messages.success(self.request, _('Grade created successfully!'))
        return super().form_valid(form)

class GradeDetailView(LoginRequiredMixin, DetailView):
    model = StudentGrade
    template_name = 'academics/grade_detail.html'
    context_object_name = 'grade'

class GradeUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = StudentGrade
    form_class = StudentGradeForm
    template_name = 'academics/grade_form.html'
    permission_required = 'academics.change_studentgrade'
    success_url = reverse_lazy('academics:grades')

    def form_valid(self, form):
        messages.success(self.request, _('Grade updated successfully!'))
        return super().form_valid(form)

class GradeReportView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/grade_report.html'

class GradeExportView(LoginRequiredMixin, View):
    """Export grade report to PDF/Excel"""

    def get(self, request, *args, **kwargs):
        from django.http import HttpResponse
        import json
        from datetime import datetime

        # Get export parameters
        export_format = request.GET.get('format', 'pdf')
        class_id = request.GET.get('class')
        subject_id = request.GET.get('subject')

        # Generate filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'grades_report_{timestamp}'

        if export_format == 'pdf':
            response = HttpResponse(content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="{filename}.pdf"'

            # For now, return a placeholder - implement actual PDF generation later
            response.write(b'Grade Report PDF - Implementation in progress')
            return response

        elif export_format == 'excel':
            response = HttpResponse(content_type='application/vnd.ms-excel')
            response['Content-Disposition'] = f'attachment; filename="{filename}.xlsx"'

            # For now, return a placeholder - implement actual Excel generation later
            response.write(b'Grade Report Excel - Implementation in progress')
            return response

        else:
            return HttpResponse('Invalid export format', status=400)

class GradeEntryView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/grade_entry.html'

class GradeReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/grade_reports.html'

class TranscriptsView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/transcripts.html'

# Exams
class ExamListView(LoginRequiredMixin, ListView):
    model = Exam
    template_name = 'academics/exams.html'
    context_object_name = 'exams'
    paginate_by = 20

    def get_queryset(self):
        queryset = Exam.objects.select_related('class_subject', 'class_subject__subject', 'created_by').all()
        search = self.request.GET.get('search')
        exam_type = self.request.GET.get('exam_type')
        class_subject = self.request.GET.get('class_subject')

        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(name_ar__icontains=search)
            )
        if exam_type:
            queryset = queryset.filter(exam_type=exam_type)
        if class_subject:
            queryset = queryset.filter(class_subject_id=class_subject)

        return queryset.order_by('-exam_date', '-start_time')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Add filter options
        context['exam_types'] = Exam.EXAM_TYPES
        context['class_subjects'] = ClassSubject.objects.select_related('class_obj', 'subject').filter(is_active=True)

        # Add statistics
        context['total_exams'] = Exam.objects.count()
        context['upcoming_exams_count'] = Exam.objects.filter(exam_date__gte=timezone.now().date()).count()
        context['completed_exams_count'] = Exam.objects.filter(exam_date__lt=timezone.now().date()).count()

        return context


class ExamCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Exam
    form_class = ExamForm
    template_name = 'academics/exam_form.html'
    permission_required = 'academics.add_exam'
    success_url = reverse_lazy('academics:exams')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        messages.success(self.request, _('Exam created successfully!'))
        return super().form_valid(form)


class ExamDetailView(LoginRequiredMixin, DetailView):
    model = Exam
    template_name = 'academics/exam_detail.html'
    context_object_name = 'exam'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        exam = self.get_object()

        # Get student grades for this exam
        context['student_grades'] = StudentGrade.objects.filter(
            exam=exam
        ).select_related('student').order_by('student__first_name', 'student__last_name')

        # Calculate statistics
        grades = context['student_grades']
        if grades:
            context['total_students'] = grades.count()
            context['average_marks'] = grades.aggregate(avg=Avg('marks_obtained'))['avg']
            context['highest_marks'] = grades.aggregate(max=Sum('marks_obtained'))['max']
            context['lowest_marks'] = grades.aggregate(min=Sum('marks_obtained'))['min']
            context['pass_count'] = grades.filter(marks_obtained__gte=exam.passing_marks).count()
            context['fail_count'] = grades.filter(marks_obtained__lt=exam.passing_marks).count()

        return context


class ExamUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = Exam
    form_class = ExamForm
    template_name = 'academics/exam_form.html'
    permission_required = 'academics.change_exam'
    success_url = reverse_lazy('academics:exams')

    def form_valid(self, form):
        messages.success(self.request, _('Exam updated successfully!'))
        return super().form_valid(form)


class ExamScheduleView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/exam_schedule.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get upcoming exams
        context['upcoming_exams'] = Exam.objects.filter(
            exam_date__gte=timezone.now().date(),
            is_published=True
        ).select_related('class_subject', 'class_subject__subject').order_by('exam_date', 'start_time')

        return context


class ExamResultsView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/exam_results.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get completed exams with results
        context['completed_exams'] = Exam.objects.filter(
            exam_date__lt=timezone.now().date(),
            is_published=True,
            student_grades__isnull=False
        ).distinct().select_related('class_subject', 'class_subject__subject').order_by('-exam_date')

        return context

# Attendance
class AttendanceView(LoginRequiredMixin, ListView):
    model = StudentAttendance
    template_name = 'academics/attendance.html'
    context_object_name = 'attendance_records'
    paginate_by = 20

    def get_queryset(self):
        queryset = StudentAttendance.objects.select_related(
            'student', 'class_subject', 'class_subject__subject', 
            'class_subject__class_obj', 'marked_by'
        ).all()
        
        # Apply filters
        class_id = self.request.GET.get('class')
        subject_id = self.request.GET.get('subject')
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        status = self.request.GET.get('status')
        
        if class_id:
            queryset = queryset.filter(class_subject__class_obj_id=class_id)
        
        if subject_id:
            queryset = queryset.filter(class_subject__subject_id=subject_id)
        
        if date_from:
            queryset = queryset.filter(marked_at__date__gte=date_from)
        
        if date_to:
            queryset = queryset.filter(marked_at__date__lte=date_to)
        
        if status:
            queryset = queryset.filter(status=status)
        
        return queryset.order_by('-marked_at', 'student__first_name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Add classes and subjects for filters
        context['classes'] = Class.objects.filter(is_active=True).select_related('grade').order_by('grade__level', 'name')
        context['subjects'] = Subject.objects.filter(is_active=True).order_by('name')
        
        # Add statistics
        queryset = self.get_queryset()
        context['total_records'] = queryset.count()
        context['present_count'] = queryset.filter(status='present').count()
        context['absent_count'] = queryset.filter(status='absent').count()
        context['late_count'] = queryset.filter(status='late').count()
        
        return context

class AttendanceCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = StudentAttendance
    form_class = StudentAttendanceForm
    template_name = 'academics/attendance_form.html'
    permission_required = 'academics.add_studentattendance'
    success_url = reverse_lazy('academics:attendance')

    def form_valid(self, form):
        form.instance.marked_by = self.request.user
        messages.success(self.request, _('Attendance recorded successfully!'))
        return super().form_valid(form)

class AttendanceDetailView(LoginRequiredMixin, DetailView):
    model = StudentAttendance
    template_name = 'academics/attendance_detail.html'
    context_object_name = 'attendance'

class AttendanceUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = StudentAttendance
    form_class = StudentAttendanceForm
    template_name = 'academics/attendance_form.html'
    permission_required = 'academics.change_studentattendance'
    success_url = reverse_lazy('academics:attendance')

    def form_valid(self, form):
        messages.success(self.request, _('Attendance updated successfully!'))
        return super().form_valid(form)

class TakeAttendanceView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/take_attendance.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get all active class subjects for dropdown
        context['class_subjects'] = ClassSubject.objects.filter(
            is_active=True
        ).select_related('class_obj', 'subject', 'teacher', 'academic_year').order_by(
            'class_obj__grade__level', 'class_obj__name', 'subject__name'
        )
        
        # Get all active classes for debugging
        context['classes'] = Class.objects.filter(is_active=True).select_related('grade').order_by('grade__level', 'name')
        
        # Get current academic year
        context['current_academic_year'] = AcademicYear.objects.filter(is_current=True).first()
        
        # Handle class subject selection
        class_subject_id = self.request.GET.get('class_subject')
        selected_date = self.request.GET.get('date')
        
        if class_subject_id:
            try:
                selected_class_subject = ClassSubject.objects.select_related(
                    'class_obj', 'subject', 'teacher'
                ).get(id=class_subject_id, is_active=True)
                context['selected_class_subject'] = selected_class_subject
                
                # Get students in the selected class
                context['students'] = Student.objects.filter(
                    current_class=selected_class_subject.class_obj,
                    is_active=True
                ).order_by('first_name', 'last_name')
                
            except ClassSubject.DoesNotExist:
                context['selected_class_subject'] = None
                context['students'] = []
        else:
            context['selected_class_subject'] = None
            context['students'] = []
        
        # Set selected date (default to today)
        from datetime import date
        if selected_date:
            try:
                context['selected_date'] = date.fromisoformat(selected_date)
            except ValueError:
                context['selected_date'] = date.today()
        else:
            context['selected_date'] = date.today()
        
        return context

    def post(self, request, *args, **kwargs):
        """Handle bulk attendance submission"""
        class_subject_id = request.POST.get('class_subject')
        attendance_date = request.POST.get('date')
        
        if not class_subject_id or not attendance_date:
            messages.error(request, _('Class subject and date are required.'))
            return self.get(request, *args, **kwargs)
        
        try:
            class_subject = ClassSubject.objects.get(id=class_subject_id, is_active=True)
            from datetime import datetime
            attendance_date = datetime.strptime(attendance_date, '%Y-%m-%d').date()
            
            # Process attendance for each student
            attendance_records = []
            for key, value in request.POST.items():
                if key.startswith('student_') and value:
                    student_id = key.replace('student_', '')
                    try:
                        student = Student.objects.get(id=student_id, is_active=True)
                        
                        # Create or update attendance record
                        attendance, created = StudentAttendance.objects.update_or_create(
                            student=student,
                            class_subject=class_subject,
                            date=attendance_date,
                            defaults={
                                'status': value,
                                'marked_by': request.user
                            }
                        )
                        attendance_records.append(attendance)
                        
                    except Student.DoesNotExist:
                        continue
            
            if attendance_records:
                messages.success(
                    request,
                    _('Attendance recorded for {} students.').format(len(attendance_records))
                )
            else:
                messages.warning(request, _('No attendance records were created.'))
                
        except (ClassSubject.DoesNotExist, ValueError) as e:
            messages.error(request, _('Invalid class subject or date.'))
        
        return redirect('academics:take_attendance')

class AttendanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/attendance_report.html'

class AttendanceExportView(LoginRequiredMixin, View):
    """Export attendance report to PDF/Excel"""

    def get(self, request, *args, **kwargs):
        from django.http import HttpResponse
        from datetime import datetime

        # Get export parameters
        export_format = request.GET.get('format', 'pdf')
        class_id = request.GET.get('class')
        date_from = request.GET.get('date_from')
        date_to = request.GET.get('date_to')

        # Generate filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'attendance_report_{timestamp}'

        if export_format == 'pdf':
            response = HttpResponse(content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="{filename}.pdf"'

            # For now, return a placeholder - implement actual PDF generation later
            response.write(b'Attendance Report PDF - Implementation in progress')
            return response

        elif export_format == 'excel':
            response = HttpResponse(content_type='application/vnd.ms-excel')
            response['Content-Disposition'] = f'attachment; filename="{filename}.xlsx"'

            # For now, return a placeholder - implement actual Excel generation later
            response.write(b'Attendance Report Excel - Implementation in progress')
            return response

        else:
            return HttpResponse('Invalid export format', status=400)

class AttendanceReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/attendance_reports.html'

class AttendanceSummaryView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/attendance_summary.html'

# Curriculum and Syllabus
class CurriculumView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/curriculum.html'

class SyllabusView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/syllabus.html'

class LessonPlansView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/lesson_plans.html'

# Academic Reports
# Additional Academic Feature Views

class AttendanceRuleListView(LoginRequiredMixin, ListView):
    model = AttendanceRule
    template_name = 'academics/attendance_rule_list.html'
    context_object_name = 'object_list'
    
    def get_queryset(self):
        school = get_current_school(self.request)
        if not school:
            return AttendanceRule.objects.none()
        return AttendanceRule.objects.filter(school=school)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        rules = self.get_queryset()
        context['active_rules_count'] = rules.filter(is_active=True).count()
        context['inactive_rules_count'] = rules.filter(is_active=False).count()
        context['minimum_attendance_rules'] = rules.filter(rule_type='minimum_attendance').count()
        context['late_threshold_rules'] = rules.filter(rule_type='late_threshold').count()
        return context

class ProgressReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/progress_reports.html'

class PerformanceReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/performance_reports.html'

class ClassSummaryReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/class_summary_reports.html'

# Enhanced Scheduling Views for Task 4.2

from .scheduling import TimetableGenerator, ResourceAllocator, ScheduleAnalyzer
from .models import Room, TeacherAvailability, TimetableTemplate


class AdvancedTimetableGeneratorView(LoginRequiredMixin, TemplateView):
    """
    Advanced timetable generation with optimization algorithms
    """
    template_name = 'academics/advanced_timetable_generator.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get current academic year
        school = get_current_school(self.request)
        if not school:
            return context
            
        current_year = AcademicYear.objects.filter(
            school=school,
            is_current=True
        ).first()
        
        context['current_academic_year'] = current_year
        context['timetable_templates'] = TimetableTemplate.objects.filter(
            school=school,
            academic_year=current_year
        )
        
        # Get generation statistics
        if current_year:
            generator = TimetableGenerator(self.request.user.school, current_year)
            class_subjects = generator.get_class_subjects_to_schedule()
            
            context['total_class_subjects'] = len(class_subjects)
            context['scheduled_count'] = Schedule.objects.filter(
                school=school,
                class_subject__academic_year=current_year,
                status='active'
            ).count()
        
        return context
    
    def post(self, request, *args, **kwargs):
        """Handle timetable generation request"""
        current_year = AcademicYear.objects.filter(
            school=request.user.school,
            is_current=True
        ).first()
        
        if not current_year:
            messages.error(request, _('No current academic year found'))
            return redirect('academics:advanced_timetable_generator')
        
        template_id = request.POST.get('template_id')
        action = request.POST.get('action')
        
        try:
            generator = TimetableGenerator(request.user.school, current_year)
            
            if action == 'generate':
                # Generate new timetable
                template = None
                if template_id:
                    template = get_object_or_404(TimetableTemplate, id=template_id)
                
                result = generator.generate_complete_timetable(template)
                
                # Save generated schedules
                for schedule in result['schedules']:
                    schedule.save()
                
                messages.success(
                    request,
                    _('Timetable generated successfully! {} class subjects scheduled, {} failed.').format(
                        result['report']['successfully_scheduled'],
                        result['report']['failed_to_schedule']
                    )
                )
                
                if result['report']['warnings']:
                    for warning in result['report']['warnings'][:5]:  # Show first 5 warnings
                        messages.warning(request, warning)
            
            elif action == 'optimize':
                # Optimize existing timetable
                result = generator.optimize_existing_timetable()
                
                messages.success(
                    request,
                    _('Timetable optimized! {} improvements made, {} conflicts resolved.').format(
                        result['improvements_made'],
                        result['conflicts_resolved']
                    )
                )
            
        except Exception as e:
            messages.error(request, _('Error generating timetable: {}').format(str(e)))
        
        return redirect('academics:advanced_timetable_generator')


class ResourceAllocationView(LoginRequiredMixin, TemplateView):
    """
    Room and resource allocation management
    """
    template_name = 'academics/resource_allocation.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        current_year = AcademicYear.objects.filter(
            school=school,
            is_current=True
        ).first()
        
        if current_year:
            allocator = ResourceAllocator(self.request.user.school, current_year)
            
            context['room_utilization'] = allocator.get_room_utilization()
            context['resource_conflicts'] = allocator.detect_resource_conflicts()
            context['rooms'] = Room.objects.filter(
                school=school,
                is_available=True
            )
        
        return context


class TeacherAvailabilityView(LoginRequiredMixin, TemplateView):
    """
    Teacher availability management
    """
    template_name = 'academics/teacher_availability.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        current_year = AcademicYear.objects.filter(
            school=school,
            is_current=True
        ).first()
        
        context['current_academic_year'] = current_year
        context['teachers'] = Teacher.objects.filter(
            school=school
        ).select_related('user')
        
        # Get teacher availability data
        teacher_id = self.request.GET.get('teacher')
        if teacher_id:
            teacher = get_object_or_404(Teacher, id=teacher_id)
            context['selected_teacher'] = teacher
            context['availability_slots'] = TeacherAvailability.objects.filter(
                teacher=teacher,
                academic_year=current_year
            ).order_by('day_of_week', 'start_time')
        
        return context
    
    def post(self, request, *args, **kwargs):
        """Handle availability updates"""
        teacher_id = request.POST.get('teacher_id')
        day_of_week = request.POST.get('day_of_week')
        start_time = request.POST.get('start_time')
        end_time = request.POST.get('end_time')
        is_preferred = request.POST.get('is_preferred') == 'on'
        
        current_year = AcademicYear.objects.filter(
            school=request.user.school,
            is_current=True
        ).first()
        
        try:
            teacher = get_object_or_404(Teacher, id=teacher_id)
            
            availability, created = TeacherAvailability.objects.get_or_create(
                teacher=teacher,
                day_of_week=day_of_week,
                start_time=start_time,
                academic_year=current_year,
                defaults={
                    'school': request.user.school,
                    'end_time': end_time,
                    'is_preferred': is_preferred
                }
            )
            
            if not created:
                availability.end_time = end_time
                availability.is_preferred = is_preferred
                availability.save()
            
            messages.success(request, _('Teacher availability updated successfully'))
            
        except Exception as e:
            messages.error(request, _('Error updating availability: {}').format(str(e)))
        
        return redirect('academics:teacher_availability')


class ScheduleConflictView(LoginRequiredMixin, TemplateView):
    """
    Schedule conflict detection and resolution
    """
    template_name = 'academics/schedule_conflicts.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        current_year = AcademicYear.objects.filter(
            school=school,
            is_current=True
        ).first()
        
        if current_year:
            # Get all active schedules
            schedules = Schedule.objects.filter(
                school=school,
                class_subject__academic_year=current_year,
                status='active'
            ).select_related('class_subject', 'room')
            
            # Detect conflicts
            conflicts = []
            for schedule in schedules:
                schedule_conflicts = schedule.get_all_conflicts()
                if any(schedule_conflicts.values()):
                    conflicts.append({
                        'schedule': schedule,
                        'conflicts': schedule_conflicts
                    })
            
            context['conflicts'] = conflicts
            context['total_schedules'] = schedules.count()
            context['conflict_count'] = len(conflicts)
        
        return context


class ScheduleAnalyticsView(LoginRequiredMixin, TemplateView):
    """
    Schedule analytics and reporting
    """
    template_name = 'academics/schedule_analytics.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        current_year = AcademicYear.objects.filter(
            school=school,
            is_current=True
        ).first()
        
        if current_year:
            analyzer = ScheduleAnalyzer(self.request.user.school, current_year)
            context['schedule_report'] = analyzer.generate_schedule_report()
        
        return context


class TimetableTemplateView(LoginRequiredMixin, CreateView):
    """
    Timetable template management
    """
    model = TimetableTemplate
    template_name = 'academics/timetable_template.html'
    fields = [
        'name', 'periods_per_day', 'period_duration', 'break_duration',
        'lunch_break_duration', 'school_start_time', 'school_end_time',
        'working_days'
    ]
    
    def form_valid(self, form):
        school = get_current_school(self.request)
        form.instance.school = school
        form.instance.academic_year = AcademicYear.objects.filter(
            school=school,
            is_current=True
        ).first()
        
        messages.success(self.request, _('Timetable template created successfully'))
        return super().form_valid(form)
    
    def get_success_url(self):
        return reverse_lazy('academics:timetable_template')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        context['templates'] = TimetableTemplate.objects.filter(
            school=school
        ).order_by('-created_at')
        
        return context


class RoomManagementView(LoginRequiredMixin, CreateView):
    """
    Room management for scheduling
    """
    model = Room
    template_name = 'academics/room_management.html'
    fields = [
        'name', 'room_number', 'room_type', 'capacity', 'floor', 'building',
        'equipment', 'has_projector', 'has_computer', 'has_internet',
        'has_whiteboard', 'is_accessible', 'is_available'
    ]
    
    def form_valid(self, form):
        form.instance.school = self.request.user.school
        messages.success(self.request, _('Room created successfully'))
        return super().form_valid(form)
    
    def get_success_url(self):
        return reverse_lazy('academics:room_management')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        context['rooms'] = Room.objects.filter(
            school=school
        ).order_by('building', 'floor', 'room_number')
        
        return context

# Academic Reports Views
class AcademicReportsView(LoginRequiredMixin, TemplateView):
    """Academic reports dashboard"""
    template_name = 'academics/reports.html'

class ProgressReportsView(LoginRequiredMixin, TemplateView):
    """Progress reports view"""
    template_name = 'academics/progress_reports.html'

class PerformanceReportsView(LoginRequiredMixin, TemplateView):
    """Performance reports view"""
    template_name = 'academics/performance_reports.html'

class ClassSummaryReportsView(LoginRequiredMixin, TemplateView):
    """Class summary reports view"""
    template_name = 'academics/class_summary_reports.html'

# Additional Academic Feature Views
class AttendanceRuleCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    """Create attendance rule"""
    model = AttendanceRule
    form_class = AttendanceRuleForm
    template_name = 'academics/attendance_rule_form.html'
    permission_required = 'academics.add_attendancerule'

    def form_valid(self, form):
        form.instance.school = self.request.user.school
        messages.success(self.request, _('Attendance rule created successfully'))
        return super().form_valid(form)

    def get_success_url(self):
        return reverse_lazy('academics:attendance_rules')

class AttendanceRuleDetailView(LoginRequiredMixin, DetailView):
    """Attendance rule detail view"""
    model = AttendanceRule
    template_name = 'academics/attendance_rule_detail.html'
    context_object_name = 'rule'

class AttendanceRuleUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    """Update attendance rule"""
    model = AttendanceRule
    form_class = AttendanceRuleForm
    template_name = 'academics/attendance_rule_form.html'
    permission_required = 'academics.change_attendancerule'

    def form_valid(self, form):
        messages.success(self.request, _('Attendance rule updated successfully'))
        return super().form_valid(form)

    def get_success_url(self):
        return reverse_lazy('academics:attendance_rules')

class AttendanceSessionListView(LoginRequiredMixin, ListView):
    """List attendance sessions"""
    model = AttendanceSession
    template_name = 'academics/attendance_session_list.html'
    context_object_name = 'sessions'
    
    def get_queryset(self):
        school = get_current_school(self.request)
        if not school:
            return AttendanceSession.objects.none()
        return AttendanceSession.objects.filter(school=school).select_related('class_subject__subject', 'class_subject__class_obj')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        sessions = self.get_queryset()
        context['scheduled_count'] = sessions.filter(status='scheduled').count()
        context['active_count'] = sessions.filter(status='active').count()
        context['completed_count'] = sessions.filter(status='completed').count()
        context['cancelled_count'] = sessions.filter(status='cancelled').count()
        return context
    
# Duplicate method removed
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        sessions = self.get_queryset()
        context['scheduled_count'] = sessions.filter(status='scheduled').count()
        context['active_count'] = sessions.filter(status='active').count()
        context['completed_count'] = sessions.filter(status='completed').count()
        context['cancelled_count'] = sessions.filter(status='cancelled').count()
        return context

class AttendanceSessionCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    """Create attendance session"""
    model = AttendanceSession
    form_class = AttendanceSessionForm
    template_name = 'academics/attendance_session_form.html'
    permission_required = 'academics.add_attendancesession'

    def form_valid(self, form):
        form.instance.school = self.request.user.school
        messages.success(self.request, _('Attendance session created successfully'))
        return super().form_valid(form)

    def get_success_url(self):
        return reverse_lazy('academics:attendance_sessions')

class AttendanceSessionDetailView(LoginRequiredMixin, DetailView):
    """Attendance session detail view"""
    model = AttendanceSession
    template_name = 'academics/attendance_session_detail.html'
    context_object_name = 'session'

class AttendanceSessionUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    """Update attendance session"""
    model = AttendanceSession
    form_class = AttendanceSessionForm
    template_name = 'academics/attendance_session_form.html'
    permission_required = 'academics.change_attendancesession'

    def form_valid(self, form):
        messages.success(self.request, _('Attendance session updated successfully'))
        return super().form_valid(form)

    def get_success_url(self):
        return reverse_lazy('academics:attendance_sessions')

class BiometricDeviceListView(LoginRequiredMixin, ListView):
    """List biometric devices"""
    model = BiometricDevice
    template_name = 'academics/biometric_device_list.html'
    context_object_name = 'devices'
    
    def get_queryset(self):
        school = get_current_school(self.request)
        if not school:
            return BiometricDevice.objects.none()
        return BiometricDevice.objects.filter(school=school)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        devices = self.get_queryset()
        context['active_count'] = devices.filter(status='active').count()
        context['inactive_count'] = devices.filter(status='inactive').count()
        context['maintenance_count'] = devices.filter(status='maintenance').count()
        context['error_count'] = devices.filter(status='error').count()
        return context
    
# Duplicate method removed
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        devices = self.get_queryset()
        context['active_count'] = devices.filter(status='active').count()
        context['inactive_count'] = devices.filter(status='inactive').count()
        context['maintenance_count'] = devices.filter(status='maintenance').count()
        context['error_count'] = devices.filter(status='error').count()
        return context

class BiometricDeviceCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    """Create biometric device"""
    model = BiometricDevice
    form_class = BiometricDeviceForm
    template_name = 'academics/biometric_device_form.html'
    permission_required = 'academics.add_biometricdevice'

    def form_valid(self, form):
        form.instance.school = self.request.user.school
        messages.success(self.request, _('Biometric device created successfully'))
        return super().form_valid(form)

    def get_success_url(self):
        return reverse_lazy('academics:biometric_devices')

class BiometricDeviceDetailView(LoginRequiredMixin, DetailView):
    """Biometric device detail view"""
    model = BiometricDevice
    template_name = 'academics/biometric_device_detail.html'
    context_object_name = 'device'

class BiometricDeviceUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    """Update biometric device"""
    model = BiometricDevice
    form_class = BiometricDeviceForm
    template_name = 'academics/biometric_device_form.html'
    permission_required = 'academics.change_biometricdevice'

    def form_valid(self, form):
        messages.success(self.request, _('Biometric device updated successfully'))
        return super().form_valid(form)

    def get_success_url(self):
        return reverse_lazy('academics:biometric_devices')

class ClassSubjectListView(LoginRequiredMixin, ListView):
    """List class subjects"""
    model = ClassSubject
    template_name = 'academics/class_subject_list.html'
    context_object_name = 'class_subjects'
    
    def get_queryset(self):
        school = get_current_school(self.request)
        if not school:
            return ClassSubject.objects.none()
        return ClassSubject.objects.filter(school=school).select_related('subject', 'class_obj__grade', 'teacher__user', 'academic_year')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        class_subjects = self.get_queryset()
        context['total_subjects'] = class_subjects.values('subject').distinct().count()
        context['total_classes'] = class_subjects.values('class_obj').distinct().count()
        context['total_teachers'] = class_subjects.values('teacher').distinct().count()
        context['total_hours'] = class_subjects.aggregate(total=models.Sum('weekly_hours'))['total'] or 0
        return context
    
# Duplicate method removed
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        class_subjects = self.get_queryset()
        context['total_subjects'] = class_subjects.values('subject').distinct().count()
        context['total_classes'] = class_subjects.values('class_obj').distinct().count()
        context['total_teachers'] = class_subjects.values('teacher').distinct().count()
        context['total_hours'] = class_subjects.aggregate(total=models.Sum('weekly_hours'))['total'] or 0
        return context

class ClassSubjectCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    """Create class subject"""
    model = ClassSubject
    form_class = ClassSubjectForm
    template_name = 'academics/class_subject_form.html'
    permission_required = 'academics.add_classsubject'

    def form_valid(self, form):
        form.instance.school = self.request.user.school
        messages.success(self.request, _('Class subject created successfully'))
        return super().form_valid(form)

    def get_success_url(self):
        return reverse_lazy('academics:class_subjects')

class ClassSubjectDetailView(LoginRequiredMixin, DetailView):
    """Class subject detail view"""
    model = ClassSubject
    template_name = 'academics/class_subject_detail.html'
    context_object_name = 'class_subject'

class ClassSubjectUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    """Update class subject"""
    model = ClassSubject
    form_class = ClassSubjectForm
    template_name = 'academics/class_subject_form.html'
    permission_required = 'academics.change_classsubject'

    def form_valid(self, form):
        messages.success(self.request, _('Class subject updated successfully'))
        return super().form_valid(form)

    def get_success_url(self):
        return reverse_lazy('academics:class_subjects')

class CurriculumPlanListView(LoginRequiredMixin, ListView):
    """List curriculum plans"""
    model = CurriculumPlan
    template_name = 'academics/curriculum_plan_list.html'
    context_object_name = 'plans'
    
    def get_queryset(self):
        school = get_current_school(self.request)
        if not school:
            return CurriculumPlan.objects.none()
        return CurriculumPlan.objects.filter(school=school).select_related('academic_year')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        plans = self.get_queryset()
        context['national_count'] = plans.filter(curriculum_type='national').count()
        context['international_count'] = plans.filter(curriculum_type='international').count()
        context['bilingual_count'] = plans.filter(curriculum_type='bilingual').count()
        context['specialized_count'] = plans.filter(curriculum_type='specialized').count()
        return context

class CurriculumPlanCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    """Create curriculum plan"""
    model = CurriculumPlan
    form_class = CurriculumPlanForm
    template_name = 'academics/curriculum_plan_form.html'
    permission_required = 'academics.add_curriculumplan'

    def form_valid(self, form):
        form.instance.school = self.request.user.school
        form.instance.created_by = self.request.user
        messages.success(self.request, _('Curriculum plan created successfully'))
        return super().form_valid(form)

    def get_success_url(self):
        return reverse_lazy('academics:curriculum_plans')

class CurriculumPlanDetailView(LoginRequiredMixin, DetailView):
    """Curriculum plan detail view"""
    model = CurriculumPlan
    template_name = 'academics/curriculum_plan_detail.html'
    context_object_name = 'plan'

class CurriculumPlanUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    """Update curriculum plan"""
    model = CurriculumPlan
    form_class = CurriculumPlanForm
    template_name = 'academics/curriculum_plan_form.html'
    permission_required = 'academics.change_curriculumplan'

    def form_valid(self, form):
        messages.success(self.request, _('Curriculum plan updated successfully'))
        return super().form_valid(form)

    def get_success_url(self):
        return reverse_lazy('academics:curriculum_plans')

class CurriculumSubjectListView(LoginRequiredMixin, ListView):
    """List curriculum subjects"""
    model = CurriculumSubject
    template_name = 'academics/curriculum_subject_list.html'
    context_object_name = 'curriculum_subjects'
    
    def get_queryset(self):
        school = get_current_school(self.request)
        if not school:
            return CurriculumSubject.objects.none()
        return CurriculumSubject.objects.filter(school=school).select_related('curriculum', 'subject')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        curriculum_subjects = self.get_queryset()
        context['mandatory_count'] = curriculum_subjects.filter(is_mandatory=True).count()
        context['elective_count'] = curriculum_subjects.filter(is_mandatory=False).count()
        context['total_credits'] = curriculum_subjects.aggregate(total=models.Sum('credit_hours'))['total'] or 0
        context['total_hours'] = curriculum_subjects.aggregate(total=models.Sum('weekly_hours'))['total'] or 0
        return context

class CurriculumSubjectCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    """Create curriculum subject"""
    model = CurriculumSubject
    form_class = CurriculumSubjectForm
    template_name = 'academics/curriculum_subject_form.html'
    permission_required = 'academics.add_curriculumsubject'

    def form_valid(self, form):
        form.instance.school = self.request.user.school
        messages.success(self.request, _('Curriculum subject created successfully'))
        return super().form_valid(form)

    def get_success_url(self):
        return reverse_lazy('academics:curriculum_subjects')

class CurriculumSubjectDetailView(LoginRequiredMixin, DetailView):
    """Curriculum subject detail view"""
    model = CurriculumSubject
    template_name = 'academics/curriculum_subject_detail.html'
    context_object_name = 'curriculum_subject'

class CurriculumSubjectUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    """Update curriculum subject"""
    model = CurriculumSubject
    form_class = CurriculumSubjectForm
    template_name = 'academics/curriculum_subject_form.html'
    permission_required = 'academics.change_curriculumsubject'

    def form_valid(self, form):
        messages.success(self.request, _('Curriculum subject updated successfully'))
        return super().form_valid(form)

    def get_success_url(self):
        return reverse_lazy('academics:curriculum_subjects')

class CurriculumListView(LoginRequiredMixin, ListView):
    """List curriculums"""
    model = Curriculum
    template_name = 'academics/curriculum_list.html'
    context_object_name = 'curriculums'
    
    def get_queryset(self):
        school = get_current_school(self.request)
        if not school:
            return Curriculum.objects.none()
        return Curriculum.objects.filter(school=school).select_related('academic_year', 'grade')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        curriculums = self.get_queryset()
        context['total_curriculums'] = curriculums.count()
        context['active_curriculums'] = curriculums.filter(is_active=True).count()
        context['total_subjects'] = curriculums.aggregate(total=models.Count('subjects', distinct=True))['total'] or 0
        context['total_grades'] = curriculums.aggregate(total=models.Count('grades', distinct=True))['total'] or 0
        return context

class CurriculumCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    """Create curriculum"""
    model = Curriculum
    form_class = CurriculumForm
    template_name = 'academics/curriculum_form.html'
    permission_required = 'academics.add_curriculum'

    def form_valid(self, form):
        form.instance.school = self.request.user.school
        messages.success(self.request, _('Curriculum created successfully'))
        return super().form_valid(form)

    def get_success_url(self):
        return reverse_lazy('academics:curriculums')

class CurriculumDetailView(LoginRequiredMixin, DetailView):
    """Curriculum detail view"""
    model = Curriculum
    template_name = 'academics/curriculum_detail.html'
    context_object_name = 'curriculum'

class CurriculumUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    """Update curriculum"""
    model = Curriculum
    form_class = CurriculumForm
    template_name = 'academics/curriculum_form.html'
    permission_required = 'academics.change_curriculum'

    def form_valid(self, form):
        messages.success(self.request, _('Curriculum updated successfully'))
        return super().form_valid(form)

    def get_success_url(self):
        return reverse_lazy('academics:curriculums')

class GradeCapacityManagementListView(LoginRequiredMixin, ListView):
    """List grade capacity management"""
    model = GradeCapacityManagement
    template_name = 'academics/grade_capacity_list.html'
    context_object_name = 'capacities'
    
    def get_queryset(self):
        school = get_current_school(self.request)
        if not school:
            return GradeCapacityManagement.objects.none()
        return GradeCapacityManagement.objects.filter(school=school).select_related('grade', 'academic_year')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        capacities = self.get_queryset()
        context['total_capacity'] = capacities.aggregate(total=models.Sum('total_capacity'))['total'] or 0
        context['total_enrolled'] = capacities.aggregate(total=models.Sum('current_enrollment'))['total'] or 0
        context['total_available'] = context['total_capacity'] - context['total_enrolled']
        context['total_waiting'] = capacities.aggregate(total=models.Sum('waiting_list_capacity'))['total'] or 0
        return context

class GradeCapacityManagementCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    """Create grade capacity management"""
    model = GradeCapacityManagement
    form_class = GradeCapacityManagementForm
    template_name = 'academics/grade_capacity_form.html'
    permission_required = 'academics.add_gradecapacitymanagement'

    def form_valid(self, form):
        form.instance.school = self.request.user.school
        messages.success(self.request, _('Grade capacity management created successfully'))
        return super().form_valid(form)

    def get_success_url(self):
        return reverse_lazy('academics:grade_capacity')

class GradeCapacityManagementDetailView(LoginRequiredMixin, DetailView):
    """Grade capacity management detail view"""
    model = GradeCapacityManagement
    template_name = 'academics/grade_capacity_detail.html'
    context_object_name = 'capacity'

class GradeCapacityManagementUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    """Update grade capacity management"""
    model = GradeCapacityManagement
    form_class = GradeCapacityManagementForm
    template_name = 'academics/grade_capacity_form.html'
    permission_required = 'academics.change_gradecapacitymanagement'

    def form_valid(self, form):
        messages.success(self.request, _('Grade capacity management updated successfully'))
        return super().form_valid(form)

    def get_success_url(self):
        return reverse_lazy('academics:grade_capacity')
