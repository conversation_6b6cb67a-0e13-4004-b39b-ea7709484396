"""
Announcement services for managing school announcements
"""
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from django.utils import timezone
from django.db import transaction
from django.core.exceptions import ValidationError
from django.db.models import Q, Count, Prefetch

from .models import (
    Announcement, AnnouncementCategory, AnnouncementTarget,
    AnnouncementView, AnnouncementComment, AnnouncementReaction
)
from .services import NotificationService, GroupService
from .models import NotificationGroup

logger = logging.getLogger(__name__)


class AnnouncementService:
    """
    Service for managing announcements
    """
    
    @staticmethod
    def create_announcement(
        title: str,
        content: str,
        category_id: int,
        author,
        school,
        priority: str = 'normal',
        audience: str = 'all',
        scheduled_at: datetime = None,
        expires_at: datetime = None,
        send_notification: bool = True,
        notification_channels: List[str] = None,
        targets: List[Dict[str, Any]] = None,
        title_ar: str = None,
        content_ar: str = None
    ) -> Announcement:
        """
        Create a new announcement
        """
        try:
            with transaction.atomic():
                # Get category
                category = AnnouncementCategory.objects.get(
                    id=category_id,
                    school=school,
                    is_active=True
                )
                
                # Determine status
                status = 'draft'
                if scheduled_at:
                    if scheduled_at <= timezone.now():
                        status = 'published'
                    else:
                        status = 'scheduled'
                
                # Create announcement
                announcement = Announcement.objects.create(
                    school=school,
                    title=title,
                    title_ar=title_ar,
                    content=content,
                    content_ar=content_ar,
                    category=category,
                    priority=priority,
                    status=status,
                    audience=audience,
                    author=author,
                    scheduled_at=scheduled_at,
                    expires_at=expires_at,
                    send_notification=send_notification,
                    notification_channels=notification_channels or ['email'],
                    created_by=author
                )
                
                # Set published_at if publishing immediately
                if status == 'published':
                    announcement.published_at = timezone.now()
                    announcement.save()
                
                # Add custom targets if specified
                if audience == 'custom' and targets:
                    for target in targets:
                        AnnouncementTarget.objects.create(
                            school=school,
                            announcement=announcement,
                            target_type=target['type'],
                            target_id=target['id'],
                            created_by=author
                        )
                
                # Send notifications if published and enabled
                if status == 'published' and send_notification:
                    AnnouncementService._send_announcement_notifications(announcement)
                
                return announcement
                
        except Exception as e:
            logger.error(f"Error creating announcement: {str(e)}")
            raise ValidationError(f"Failed to create announcement: {str(e)}")
    
    @staticmethod
    def publish_announcement(announcement: Announcement, user=None) -> bool:
        """
        Publish a draft or scheduled announcement
        """
        try:
            with transaction.atomic():
                if announcement.status not in ['draft', 'scheduled']:
                    raise ValidationError("Only draft or scheduled announcements can be published")
                
                announcement.status = 'published'
                announcement.published_at = timezone.now()
                announcement.save()
                
                # Send notifications if enabled
                if announcement.send_notification:
                    AnnouncementService._send_announcement_notifications(announcement)
                
                return True
                
        except Exception as e:
            logger.error(f"Error publishing announcement {announcement.id}: {str(e)}")
            return False
    
    @staticmethod
    def schedule_announcement(announcement: Announcement, scheduled_at: datetime) -> bool:
        """
        Schedule an announcement for future publication
        """
        try:
            if announcement.status != 'draft':
                raise ValidationError("Only draft announcements can be scheduled")
            
            if scheduled_at <= timezone.now():
                raise ValidationError("Scheduled time must be in the future")
            
            announcement.status = 'scheduled'
            announcement.scheduled_at = scheduled_at
            announcement.save()
            
            return True
            
        except Exception as e:
            logger.error(f"Error scheduling announcement {announcement.id}: {str(e)}")
            return False
    
    @staticmethod
    def archive_announcement(announcement: Announcement) -> bool:
        """
        Archive an announcement
        """
        try:
            announcement.status = 'archived'
            announcement.save()
            return True
            
        except Exception as e:
            logger.error(f"Error archiving announcement {announcement.id}: {str(e)}")
            return False
    
    @staticmethod
    def get_announcements_for_user(user, school, limit: int = 10, include_expired: bool = False):
        """
        Get announcements visible to a specific user
        """
        try:
            # Base query for published announcements
            announcements = Announcement.objects.filter(
                school=school,
                status='published'
            ).select_related('category', 'author').prefetch_related(
                'targets',
                'views',
                'reactions'
            )
            
            # Filter by expiration
            if not include_expired:
                now = timezone.now()
                announcements = announcements.filter(
                    Q(expires_at__isnull=True) | Q(expires_at__gt=now)
                )
            
            # Filter by audience
            user_announcements = []
            
            for announcement in announcements:
                if AnnouncementService._is_announcement_visible_to_user(announcement, user):
                    user_announcements.append(announcement)
            
            # Sort by pinned status and date
            user_announcements.sort(
                key=lambda x: (x.is_pinned, x.published_at),
                reverse=True
            )
            
            return user_announcements[:limit] if limit else user_announcements
            
        except Exception as e:
            logger.error(f"Error getting announcements for user {user.id}: {str(e)}")
            return []
    
    @staticmethod
    def mark_announcement_as_viewed(announcement: Announcement, user) -> bool:
        """
        Mark an announcement as viewed by a user
        """
        try:
            view, created = AnnouncementView.objects.get_or_create(
                announcement=announcement,
                user=user,
                defaults={
                    'school': announcement.school,
                    'created_by': user
                }
            )
            
            if created:
                announcement.increment_view_count()
            
            return True
            
        except Exception as e:
            logger.error(f"Error marking announcement {announcement.id} as viewed: {str(e)}")
            return False
    
    @staticmethod
    def add_reaction(announcement: Announcement, user, reaction: str) -> bool:
        """
        Add or update a user's reaction to an announcement
        """
        try:
            reaction_obj, created = AnnouncementReaction.objects.update_or_create(
                announcement=announcement,
                user=user,
                defaults={
                    'reaction': reaction,
                    'school': announcement.school,
                    'created_by': user
                }
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error adding reaction to announcement {announcement.id}: {str(e)}")
            return False
    
    @staticmethod
    def remove_reaction(announcement: Announcement, user) -> bool:
        """
        Remove a user's reaction from an announcement
        """
        try:
            AnnouncementReaction.objects.filter(
                announcement=announcement,
                user=user
            ).delete()
            
            return True
            
        except Exception as e:
            logger.error(f"Error removing reaction from announcement {announcement.id}: {str(e)}")
            return False
    
    @staticmethod
    def add_comment(announcement: Announcement, user, content: str, parent_id: int = None) -> Optional[AnnouncementComment]:
        """
        Add a comment to an announcement
        """
        try:
            parent = None
            if parent_id:
                parent = AnnouncementComment.objects.get(
                    id=parent_id,
                    announcement=announcement
                )
            
            comment = AnnouncementComment.objects.create(
                school=announcement.school,
                announcement=announcement,
                user=user,
                content=content,
                parent=parent,
                created_by=user
            )
            
            return comment
            
        except Exception as e:
            logger.error(f"Error adding comment to announcement {announcement.id}: {str(e)}")
            return None
    
    @staticmethod
    def process_scheduled_announcements():
        """
        Process announcements scheduled for publication
        """
        now = timezone.now()
        scheduled_announcements = Announcement.objects.filter(
            status='scheduled',
            scheduled_at__lte=now
        )
        
        for announcement in scheduled_announcements:
            AnnouncementService.publish_announcement(announcement)
    
    @staticmethod
    def get_announcement_analytics(announcement: Announcement) -> Dict[str, Any]:
        """
        Get analytics for an announcement
        """
        try:
            total_views = announcement.view_count
            unique_viewers = AnnouncementView.objects.filter(
                announcement=announcement
            ).count()
            
            # Reaction counts
            reactions = AnnouncementReaction.objects.filter(
                announcement=announcement
            ).values('reaction').annotate(count=Count('reaction'))
            
            reaction_counts = {r['reaction']: r['count'] for r in reactions}
            
            # Comment count
            comment_count = AnnouncementComment.objects.filter(
                announcement=announcement
            ).count()
            
            return {
                'total_views': total_views,
                'unique_viewers': unique_viewers,
                'reactions': reaction_counts,
                'comments': comment_count,
                'engagement_rate': (unique_viewers / max(total_views, 1)) * 100
            }
            
        except Exception as e:
            logger.error(f"Error getting analytics for announcement {announcement.id}: {str(e)}")
            return {}
    
    @staticmethod
    def _is_announcement_visible_to_user(announcement: Announcement, user) -> bool:
        """
        Check if an announcement is visible to a specific user
        """
        try:
            if announcement.audience == 'all':
                return True
            
            # Check user role/type based on audience
            if announcement.audience == 'students':
                return hasattr(user, 'student_profile')
            elif announcement.audience == 'parents':
                return hasattr(user, 'parent_profile')
            elif announcement.audience == 'teachers':
                return hasattr(user, 'employee_profile') and user.employee_profile.position.title.lower() in ['teacher', 'instructor']
            elif announcement.audience == 'staff':
                return hasattr(user, 'employee_profile')
            elif announcement.audience == 'custom':
                # Check custom targets
                targets = AnnouncementTarget.objects.filter(announcement=announcement)
                for target in targets:
                    if target.target_type == 'user' and target.target_id == user.id:
                        return True
                    # Add more target type checks as needed
                return False
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking visibility for announcement {announcement.id}: {str(e)}")
            return False
    
    @staticmethod
    def _send_announcement_notifications(announcement: Announcement):
        """
        Send notifications for a published announcement
        """
        try:
            # Create notification template variables
            variables = {
                'announcement_title': announcement.title,
                'announcement_content': announcement.content[:200] + '...' if len(announcement.content) > 200 else announcement.content,
                'author_name': f"{announcement.author.first_name} {announcement.author.last_name}",
                'category': announcement.category.name,
                'school_name': announcement.school.name
            }
            
            # Determine recipients based on audience
            recipients = AnnouncementService._get_announcement_recipients(announcement)
            
            # Send notifications through specified channels
            for channel in announcement.notification_channels:
                try:
                    # Use a generic announcement notification template
                    NotificationService.send_bulk_notification(
                        template_code='ANNOUNCEMENT_NOTIFICATION',
                        recipients=recipients,
                        variables=variables,
                        priority=announcement.priority,
                        school=announcement.school
                    )
                except Exception as e:
                    logger.error(f"Error sending {channel} notifications for announcement {announcement.id}: {str(e)}")
                    continue
            
        except Exception as e:
            logger.error(f"Error sending notifications for announcement {announcement.id}: {str(e)}")
    
    @staticmethod
    def _get_announcement_recipients(announcement: Announcement) -> List[Dict[str, Any]]:
        """
        Get list of recipients for announcement notifications
        """
        recipients = []
        
        try:
            if announcement.audience == 'all':
                # Get all users in the school
                from accounts.models import User
                users = User.objects.filter(
                    Q(employee_profile__school=announcement.school) |
                    Q(student_profile__school=announcement.school) |
                    Q(parent_profile__school=announcement.school)
                ).distinct()
                
                for user in users:
                    if user.email:
                        recipients.append({
                            'contact': user.email,
                            'type': 'user',
                            'id': user.id,
                            'variables': {
                                'recipient_name': f"{user.first_name} {user.last_name}"
                            }
                        })
            
            elif announcement.audience == 'students':
                # Get all students
                from students.models import Student
                students = Student.objects.filter(
                    school=announcement.school,
                    status='active'
                ).select_related('user')
                
                for student in students:
                    if student.user and student.user.email:
                        recipients.append({
                            'contact': student.user.email,
                            'type': 'student',
                            'id': student.id,
                            'variables': {
                                'recipient_name': f"{student.user.first_name} {student.user.last_name}"
                            }
                        })
            
            elif announcement.audience == 'staff':
                # Get all employees
                from hr.models import Employee
                employees = Employee.objects.filter(
                    school=announcement.school,
                    employment_status='active'
                ).select_related('user')
                
                for employee in employees:
                    if employee.user and employee.user.email:
                        recipients.append({
                            'contact': employee.user.email,
                            'type': 'employee',
                            'id': employee.id,
                            'variables': {
                                'recipient_name': f"{employee.user.first_name} {employee.user.last_name}"
                            }
                        })
            
            # Add more audience types as needed
            
        except Exception as e:
            logger.error(f"Error getting recipients for announcement {announcement.id}: {str(e)}")
        
        return recipients


class AnnouncementCategoryService:
    """
    Service for managing announcement categories
    """
    
    @staticmethod
    def create_category(
        name: str,
        code: str,
        school,
        description: str = None,
        color: str = '#007bff',
        icon: str = None,
        name_ar: str = None
    ) -> AnnouncementCategory:
        """
        Create a new announcement category
        """
        try:
            category = AnnouncementCategory.objects.create(
                school=school,
                name=name,
                name_ar=name_ar,
                code=code,
                description=description,
                color=color,
                icon=icon
            )
            
            return category
            
        except Exception as e:
            logger.error(f"Error creating announcement category: {str(e)}")
            raise ValidationError(f"Failed to create category: {str(e)}")
    
    @staticmethod
    def get_active_categories(school):
        """
        Get all active categories for a school
        """
        return AnnouncementCategory.objects.filter(
            school=school,
            is_active=True
        ).order_by('name')
    
    @staticmethod
    def get_category_statistics(category: AnnouncementCategory) -> Dict[str, Any]:
        """
        Get statistics for a category
        """
        try:
            total_announcements = Announcement.objects.filter(category=category).count()
            published_announcements = Announcement.objects.filter(
                category=category,
                status='published'
            ).count()
            
            return {
                'total_announcements': total_announcements,
                'published_announcements': published_announcements,
                'draft_announcements': total_announcements - published_announcements
            }
            
        except Exception as e:
            logger.error(f"Error getting statistics for category {category.id}: {str(e)}")
            return {}