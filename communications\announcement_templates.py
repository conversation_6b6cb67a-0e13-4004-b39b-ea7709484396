"""
Default announcement notification templates
"""
from .models import NotificationTemplate
from .services import TemplateService


def create_default_announcement_templates(school):
    """
    Create default notification templates for announcements
    """
    templates = [
        {
            'name': 'Announcement Notification',
            'code': 'ANNOUNCEMENT_NOTIFICATION',
            'category': 'general',
            'channel': 'email',
            'subject': 'New Announcement: {{announcement_title}}',
            'body': '''
Dear {{recipient_name}},

A new announcement has been posted by {{author_name}}:

Title: {{announcement_title}}
Category: {{category}}

{{announcement_content}}

Please log in to the school portal to view the full announcement.

Best regards,
{{school_name}}
            '''.strip(),
            'variables': {
                'recipient_name': 'Recipient name',
                'announcement_title': 'Announcement title',
                'announcement_content': 'Announcement content preview',
                'author_name': 'Author name',
                'category': 'Announcement category',
                'school_name': 'School name'
            }
        },
        {
            'name': 'Urgent Announcement Notification',
            'code': 'URGENT_ANNOUNCEMENT_NOTIFICATION',
            'category': 'emergency',
            'channel': 'email',
            'subject': 'URGENT: {{announcement_title}}',
            'body': '''
URGENT ANNOUNCEMENT

Dear {{recipient_name}},

An urgent announcement has been posted by {{author_name}}:

{{announcement_title}}

{{announcement_content}}

Please take immediate action as required.

{{school_name}}
            '''.strip(),
            'variables': {
                'recipient_name': 'Recipient name',
                'announcement_title': 'Announcement title',
                'announcement_content': 'Announcement content',
                'author_name': 'Author name',
                'school_name': 'School name'
            }
        },
        {
            'name': 'Event Announcement',
            'code': 'EVENT_ANNOUNCEMENT',
            'category': 'academic',
            'channel': 'email',
            'subject': 'Event Announcement: {{announcement_title}}',
            'body': '''
Dear {{recipient_name}},

We are pleased to announce an upcoming event:

{{announcement_title}}

{{announcement_content}}

We look forward to your participation.

Best regards,
{{school_name}}
            '''.strip(),
            'variables': {
                'recipient_name': 'Recipient name',
                'announcement_title': 'Event title',
                'announcement_content': 'Event details',
                'school_name': 'School name'
            }
        }
    ]
    
    created_templates = []
    for template_data in templates:
        try:
            # Check if template already exists
            existing = NotificationTemplate.objects.filter(
                school=school,
                code=template_data['code']
            ).first()
            
            if not existing:
                template = TemplateService.create_template(
                    school=school,
                    **template_data
                )
                created_templates.append(template)
        except Exception as e:
            print(f"Error creating template {template_data['code']}: {str(e)}")
            continue
    
    return created_templates


def create_default_announcement_categories(school):
    """
    Create default announcement categories
    """
    from .announcement_services import AnnouncementCategoryService
    
    categories = [
        {
            'name': 'General',
            'code': 'GENERAL',
            'description': 'General announcements',
            'color': '#007bff',
            'icon': 'fas fa-bullhorn'
        },
        {
            'name': 'Academic',
            'code': 'ACADEMIC',
            'description': 'Academic-related announcements',
            'color': '#28a745',
            'icon': 'fas fa-graduation-cap'
        },
        {
            'name': 'Events',
            'code': 'EVENTS',
            'description': 'School events and activities',
            'color': '#ffc107',
            'icon': 'fas fa-calendar-alt'
        },
        {
            'name': 'Emergency',
            'code': 'EMERGENCY',
            'description': 'Emergency announcements',
            'color': '#dc3545',
            'icon': 'fas fa-exclamation-triangle'
        },
        {
            'name': 'Administrative',
            'code': 'ADMINISTRATIVE',
            'description': 'Administrative announcements',
            'color': '#6c757d',
            'icon': 'fas fa-cog'
        }
    ]
    
    created_categories = []
    for category_data in categories:
        try:
            category = AnnouncementCategoryService.create_category(
                school=school,
                **category_data
            )
            created_categories.append(category)
        except Exception as e:
            print(f"Error creating category {category_data['code']}: {str(e)}")
            continue
    
    return created_categories