# Generated by Django 5.2.4 on 2025-08-03 11:31

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="EmailConfiguration",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "name",
                    models.CharField(max_length=100, verbose_name="Configuration Name"),
                ),
                (
                    "smtp_host",
                    models.CharField(max_length=255, verbose_name="SMTP Host"),
                ),
                (
                    "smtp_port",
                    models.IntegerField(default=587, verbose_name="SMTP Port"),
                ),
                (
                    "smtp_username",
                    models.CharField(max_length=255, verbose_name="SMTP Username"),
                ),
                (
                    "smtp_password",
                    models.CharField(max_length=255, verbose_name="SMTP Password"),
                ),
                ("use_tls", models.BooleanField(default=True, verbose_name="Use TLS")),
                ("use_ssl", models.BooleanField(default=False, verbose_name="Use SSL")),
                (
                    "from_email",
                    models.EmailField(max_length=254, verbose_name="From Email"),
                ),
                (
                    "from_name",
                    models.CharField(max_length=100, verbose_name="From Name"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "is_default",
                    models.BooleanField(default=False, verbose_name="Is Default"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Email Configuration",
                "verbose_name_plural": "Email Configurations",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="NotificationChannel",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                ("name", models.CharField(max_length=50, verbose_name="Channel Name")),
                (
                    "channel_type",
                    models.CharField(
                        choices=[
                            ("email", "Email"),
                            ("sms", "SMS"),
                            ("whatsapp", "WhatsApp"),
                            ("push", "Push Notification"),
                        ],
                        max_length=20,
                        verbose_name="Channel Type",
                    ),
                ),
                (
                    "configuration",
                    models.JSONField(
                        default=dict,
                        help_text="API keys, endpoints, and other settings",
                        verbose_name="Channel Configuration",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "is_default",
                    models.BooleanField(
                        default=False,
                        help_text="Default channel for this type",
                        verbose_name="Is Default Channel",
                    ),
                ),
                (
                    "rate_limit",
                    models.IntegerField(
                        default=100,
                        help_text="Maximum messages per hour",
                        verbose_name="Rate Limit per Hour",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Notification Channel",
                "verbose_name_plural": "Notification Channels",
                "ordering": ["channel_type", "name"],
            },
        ),
        migrations.CreateModel(
            name="Notification",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "recipient_type",
                    models.CharField(
                        choices=[
                            ("user", "User"),
                            ("employee", "Employee"),
                            ("student", "Student"),
                            ("parent", "Parent"),
                            ("group", "Group"),
                            ("all", "All Users"),
                        ],
                        max_length=20,
                        verbose_name="Recipient Type",
                    ),
                ),
                (
                    "recipient_id",
                    models.IntegerField(
                        blank=True,
                        help_text="ID of the recipient (if applicable)",
                        null=True,
                        verbose_name="Recipient ID",
                    ),
                ),
                (
                    "recipient_contact",
                    models.CharField(
                        help_text="Email, phone number, or other contact info",
                        max_length=255,
                        verbose_name="Recipient Contact",
                    ),
                ),
                (
                    "subject",
                    models.CharField(max_length=200, verbose_name="Subject/Title"),
                ),
                ("message", models.TextField(verbose_name="Message Content")),
                (
                    "variables",
                    models.JSONField(
                        default=dict,
                        help_text="Variables used to render the message",
                        verbose_name="Template Variables",
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("normal", "Normal"),
                            ("high", "High"),
                            ("urgent", "Urgent"),
                        ],
                        default="normal",
                        max_length=10,
                        verbose_name="Priority",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("sent", "Sent"),
                            ("delivered", "Delivered"),
                            ("failed", "Failed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "scheduled_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When to send the notification (leave blank for immediate)",
                        null=True,
                        verbose_name="Scheduled At",
                    ),
                ),
                (
                    "sent_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="Sent At"),
                ),
                (
                    "delivered_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Delivered At"
                    ),
                ),
                (
                    "error_message",
                    models.TextField(
                        blank=True, null=True, verbose_name="Error Message"
                    ),
                ),
                (
                    "external_id",
                    models.CharField(
                        blank=True,
                        help_text="ID from external service (SMS gateway, etc.)",
                        max_length=100,
                        null=True,
                        verbose_name="External ID",
                    ),
                ),
                (
                    "retry_count",
                    models.IntegerField(default=0, verbose_name="Retry Count"),
                ),
                (
                    "max_retries",
                    models.IntegerField(default=3, verbose_name="Max Retries"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "channel",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notifications",
                        to="communications.notificationchannel",
                        verbose_name="Channel",
                    ),
                ),
            ],
            options={
                "verbose_name": "Notification",
                "verbose_name_plural": "Notifications",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="NotificationGroup",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                ("name", models.CharField(max_length=100, verbose_name="Group Name")),
                (
                    "name_ar",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="Group Name (Arabic)",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="Description"),
                ),
                (
                    "group_type",
                    models.CharField(
                        choices=[
                            ("static", "Static Group"),
                            ("dynamic", "Dynamic Group"),
                        ],
                        default="static",
                        max_length=20,
                        verbose_name="Group Type",
                    ),
                ),
                (
                    "criteria",
                    models.JSONField(
                        default=dict,
                        help_text="Criteria for dynamic groups",
                        verbose_name="Group Criteria",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Notification Group",
                "verbose_name_plural": "Notification Groups",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="NotificationLog",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "action",
                    models.CharField(
                        choices=[
                            ("created", "Created"),
                            ("sent", "Sent"),
                            ("delivered", "Delivered"),
                            ("failed", "Failed"),
                            ("retried", "Retried"),
                            ("cancelled", "Cancelled"),
                        ],
                        max_length=20,
                        verbose_name="Action",
                    ),
                ),
                (
                    "details",
                    models.TextField(blank=True, null=True, verbose_name="Details"),
                ),
                (
                    "response_data",
                    models.JSONField(
                        default=dict,
                        help_text="Response from external service",
                        verbose_name="Response Data",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "notification",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="logs",
                        to="communications.notification",
                        verbose_name="Notification",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Notification Log",
                "verbose_name_plural": "Notification Logs",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="NotificationTemplate",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "name",
                    models.CharField(max_length=100, verbose_name="Template Name"),
                ),
                (
                    "name_ar",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="Template Name (Arabic)",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        help_text="Unique identifier for the template",
                        max_length=50,
                        unique=True,
                        verbose_name="Template Code",
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("academic", "Academic"),
                            ("administrative", "Administrative"),
                            ("financial", "Financial"),
                            ("emergency", "Emergency"),
                            ("general", "General"),
                            ("hr", "Human Resources"),
                            ("student", "Student Affairs"),
                            ("parent", "Parent Communication"),
                        ],
                        default="general",
                        max_length=20,
                        verbose_name="Category",
                    ),
                ),
                (
                    "channel",
                    models.CharField(
                        choices=[
                            ("email", "Email"),
                            ("sms", "SMS"),
                            ("whatsapp", "WhatsApp"),
                            ("push", "Push Notification"),
                            ("in_app", "In-App Notification"),
                        ],
                        max_length=20,
                        verbose_name="Notification Channel",
                    ),
                ),
                (
                    "subject",
                    models.CharField(max_length=200, verbose_name="Subject/Title"),
                ),
                (
                    "subject_ar",
                    models.CharField(
                        blank=True,
                        max_length=200,
                        null=True,
                        verbose_name="Subject/Title (Arabic)",
                    ),
                ),
                (
                    "body",
                    models.TextField(
                        help_text="Use {{variable}} for dynamic content",
                        verbose_name="Message Body",
                    ),
                ),
                (
                    "body_ar",
                    models.TextField(
                        blank=True,
                        help_text="Use {{variable}} for dynamic content",
                        null=True,
                        verbose_name="Message Body (Arabic)",
                    ),
                ),
                (
                    "variables",
                    models.JSONField(
                        default=dict,
                        help_text="Available variables for this template",
                        verbose_name="Template Variables",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "is_system",
                    models.BooleanField(
                        default=False,
                        help_text="System templates cannot be deleted",
                        verbose_name="Is System Template",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Notification Template",
                "verbose_name_plural": "Notification Templates",
                "ordering": ["category", "name"],
            },
        ),
        migrations.AddField(
            model_name="notification",
            name="template",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="notifications",
                to="communications.notificationtemplate",
                verbose_name="Template",
            ),
        ),
        migrations.CreateModel(
            name="SMSConfiguration",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "name",
                    models.CharField(max_length=100, verbose_name="Configuration Name"),
                ),
                (
                    "gateway_type",
                    models.CharField(
                        choices=[
                            ("twilio", "Twilio"),
                            ("nexmo", "Nexmo/Vonage"),
                            ("aws_sns", "AWS SNS"),
                            ("custom", "Custom Gateway"),
                        ],
                        max_length=20,
                        verbose_name="Gateway Type",
                    ),
                ),
                ("api_key", models.CharField(max_length=255, verbose_name="API Key")),
                (
                    "api_secret",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="API Secret"
                    ),
                ),
                (
                    "sender_id",
                    models.CharField(max_length=20, verbose_name="Sender ID"),
                ),
                (
                    "webhook_url",
                    models.URLField(blank=True, null=True, verbose_name="Webhook URL"),
                ),
                (
                    "configuration",
                    models.JSONField(
                        default=dict, verbose_name="Additional Configuration"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "is_default",
                    models.BooleanField(default=False, verbose_name="Is Default"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "SMS Configuration",
                "verbose_name_plural": "SMS Configurations",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="WhatsAppConfiguration",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "name",
                    models.CharField(max_length=100, verbose_name="Configuration Name"),
                ),
                (
                    "business_account_id",
                    models.CharField(
                        max_length=100, verbose_name="Business Account ID"
                    ),
                ),
                (
                    "phone_number_id",
                    models.CharField(max_length=100, verbose_name="Phone Number ID"),
                ),
                (
                    "access_token",
                    models.CharField(max_length=500, verbose_name="Access Token"),
                ),
                (
                    "webhook_verify_token",
                    models.CharField(
                        max_length=255, verbose_name="Webhook Verify Token"
                    ),
                ),
                (
                    "webhook_url",
                    models.URLField(blank=True, null=True, verbose_name="Webhook URL"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "is_default",
                    models.BooleanField(default=False, verbose_name="Is Default"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "WhatsApp Configuration",
                "verbose_name_plural": "WhatsApp Configurations",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="NotificationGroupMember",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "member_type",
                    models.CharField(
                        choices=[
                            ("user", "User"),
                            ("employee", "Employee"),
                            ("student", "Student"),
                            ("parent", "Parent"),
                        ],
                        max_length=20,
                        verbose_name="Member Type",
                    ),
                ),
                ("member_id", models.IntegerField(verbose_name="Member ID")),
                (
                    "contact_info",
                    models.JSONField(
                        default=dict,
                        help_text="Email, phone, etc.",
                        verbose_name="Contact Information",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "group",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="members",
                        to="communications.notificationgroup",
                        verbose_name="Group",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Group Member",
                "verbose_name_plural": "Group Members",
                "ordering": ["group", "member_type"],
                "unique_together": {("group", "member_type", "member_id")},
            },
        ),
        migrations.AddIndex(
            model_name="notification",
            index=models.Index(
                fields=["status", "scheduled_at"], name="communicati_status_9490cd_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="notification",
            index=models.Index(
                fields=["recipient_type", "recipient_id"],
                name="communicati_recipie_96be41_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="notification",
            index=models.Index(
                fields=["channel", "status"], name="communicati_channel_ee391e_idx"
            ),
        ),
    ]
