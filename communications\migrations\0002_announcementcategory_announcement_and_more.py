# Generated by Django 5.2.4 on 2025-08-03 11:54

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("communications", "0001_initial"),
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AnnouncementCategory",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "name",
                    models.CharField(max_length=100, verbose_name="Category Name"),
                ),
                (
                    "name_ar",
                    models.Char<PERSON>ield(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="Category Name (Arabic)",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="Category Code"
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="Description"),
                ),
                (
                    "color",
                    models.CharField(
                        default="#007bff",
                        help_text="Hex color code for the category",
                        max_length=7,
                        verbose_name="Color",
                    ),
                ),
                (
                    "icon",
                    models.CharField(
                        blank=True,
                        help_text="Font Awesome icon class",
                        max_length=50,
                        null=True,
                        verbose_name="Icon",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Announcement Category",
                "verbose_name_plural": "Announcement Categories",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Announcement",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("title", models.CharField(max_length=200, verbose_name="Title")),
                (
                    "title_ar",
                    models.CharField(
                        blank=True,
                        max_length=200,
                        null=True,
                        verbose_name="Title (Arabic)",
                    ),
                ),
                ("content", models.TextField(verbose_name="Content")),
                (
                    "content_ar",
                    models.TextField(
                        blank=True, null=True, verbose_name="Content (Arabic)"
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("normal", "Normal"),
                            ("high", "High"),
                            ("urgent", "Urgent"),
                        ],
                        default="normal",
                        max_length=10,
                        verbose_name="Priority",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("scheduled", "Scheduled"),
                            ("published", "Published"),
                            ("archived", "Archived"),
                        ],
                        default="draft",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "audience",
                    models.CharField(
                        choices=[
                            ("all", "All Users"),
                            ("students", "Students"),
                            ("parents", "Parents"),
                            ("teachers", "Teachers"),
                            ("staff", "Staff"),
                            ("custom", "Custom Groups"),
                        ],
                        default="all",
                        max_length=20,
                        verbose_name="Target Audience",
                    ),
                ),
                (
                    "published_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Published At"
                    ),
                ),
                (
                    "scheduled_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When to publish the announcement",
                        null=True,
                        verbose_name="Scheduled At",
                    ),
                ),
                (
                    "expires_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the announcement expires",
                        null=True,
                        verbose_name="Expires At",
                    ),
                ),
                (
                    "is_pinned",
                    models.BooleanField(
                        default=False,
                        help_text="Pinned announcements appear at the top",
                        verbose_name="Is Pinned",
                    ),
                ),
                (
                    "send_notification",
                    models.BooleanField(
                        default=True,
                        help_text="Send notification when published",
                        verbose_name="Send Notification",
                    ),
                ),
                (
                    "notification_channels",
                    models.JSONField(
                        default=list,
                        help_text="Channels to send notifications through",
                        verbose_name="Notification Channels",
                    ),
                ),
                (
                    "attachment",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to="announcements/attachments/",
                        verbose_name="Attachment",
                    ),
                ),
                (
                    "view_count",
                    models.PositiveIntegerField(default=0, verbose_name="View Count"),
                ),
                (
                    "author",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="authored_announcements",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Author",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="announcements",
                        to="communications.announcementcategory",
                        verbose_name="Category",
                    ),
                ),
            ],
            options={
                "verbose_name": "Announcement",
                "verbose_name_plural": "Announcements",
                "ordering": ["-is_pinned", "-published_at", "-created_at"],
            },
        ),
        migrations.CreateModel(
            name="AnnouncementComment",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("content", models.TextField(verbose_name="Comment")),
                (
                    "is_approved",
                    models.BooleanField(default=True, verbose_name="Is Approved"),
                ),
                (
                    "announcement",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="comments",
                        to="communications.announcement",
                        verbose_name="Announcement",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="replies",
                        to="communications.announcementcomment",
                        verbose_name="Parent Comment",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="announcement_comments",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "Announcement Comment",
                "verbose_name_plural": "Announcement Comments",
                "ordering": ["created_at"],
            },
        ),
        migrations.CreateModel(
            name="AnnouncementReaction",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "reaction",
                    models.CharField(
                        choices=[
                            ("like", "Like"),
                            ("love", "Love"),
                            ("laugh", "Laugh"),
                            ("wow", "Wow"),
                            ("sad", "Sad"),
                            ("angry", "Angry"),
                        ],
                        max_length=10,
                        verbose_name="Reaction",
                    ),
                ),
                (
                    "announcement",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reactions",
                        to="communications.announcement",
                        verbose_name="Announcement",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="announcement_reactions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "Announcement Reaction",
                "verbose_name_plural": "Announcement Reactions",
            },
        ),
        migrations.CreateModel(
            name="AnnouncementTarget",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "target_type",
                    models.CharField(
                        choices=[
                            ("user", "User"),
                            ("group", "Group"),
                            ("department", "Department"),
                            ("grade", "Grade"),
                            ("class", "Class"),
                        ],
                        max_length=20,
                        verbose_name="Target Type",
                    ),
                ),
                ("target_id", models.IntegerField(verbose_name="Target ID")),
                (
                    "announcement",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="targets",
                        to="communications.announcement",
                        verbose_name="Announcement",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Announcement Target",
                "verbose_name_plural": "Announcement Targets",
            },
        ),
        migrations.CreateModel(
            name="AnnouncementView",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "viewed_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Viewed At"),
                ),
                (
                    "announcement",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="views",
                        to="communications.announcement",
                        verbose_name="Announcement",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="announcement_views",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "Announcement View",
                "verbose_name_plural": "Announcement Views",
                "ordering": ["-viewed_at"],
            },
        ),
        migrations.AddIndex(
            model_name="announcement",
            index=models.Index(
                fields=["status", "published_at"], name="communicati_status_4a291c_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="announcement",
            index=models.Index(
                fields=["audience", "status"], name="communicati_audienc_5735de_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="announcement",
            index=models.Index(
                fields=["category", "status"], name="communicati_categor_1ae632_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="announcementreaction",
            unique_together={("announcement", "user")},
        ),
        migrations.AlterUniqueTogether(
            name="announcementtarget",
            unique_together={("announcement", "target_type", "target_id")},
        ),
        migrations.AlterUniqueTogether(
            name="announcementview",
            unique_together={("announcement", "user")},
        ),
    ]
