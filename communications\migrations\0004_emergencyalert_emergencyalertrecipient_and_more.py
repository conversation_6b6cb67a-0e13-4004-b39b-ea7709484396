# Generated by Django 5.2.4 on 2025-08-03 12:21

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("communications", "0003_auto_20250803_1510"),
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="EmergencyAlert",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                ("title", models.CharField(max_length=200, verbose_name="Alert Title")),
                (
                    "title_ar",
                    models.CharField(
                        blank=True,
                        max_length=200,
                        null=True,
                        verbose_name="Alert Title (Arabic)",
                    ),
                ),
                ("message", models.TextField(verbose_name="Alert Message")),
                (
                    "message_ar",
                    models.TextField(
                        blank=True, null=True, verbose_name="Alert Message (Arabic)"
                    ),
                ),
                (
                    "alert_type",
                    models.CharField(
                        choices=[
                            ("weather", "Weather Emergency"),
                            ("security", "Security Alert"),
                            ("medical", "Medical Emergency"),
                            ("fire", "Fire Emergency"),
                            ("lockdown", "Lockdown"),
                            ("evacuation", "Evacuation"),
                            ("general", "General Emergency"),
                        ],
                        max_length=20,
                        verbose_name="Alert Type",
                    ),
                ),
                (
                    "severity",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("critical", "Critical"),
                        ],
                        default="medium",
                        max_length=10,
                        verbose_name="Severity Level",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("resolved", "Resolved"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="active",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "issued_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Issued At"),
                ),
                (
                    "resolved_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Resolved At"
                    ),
                ),
                (
                    "affected_areas",
                    models.JSONField(
                        default=list,
                        help_text="List of affected buildings, floors, or areas",
                        verbose_name="Affected Areas",
                    ),
                ),
                (
                    "instructions",
                    models.TextField(
                        blank=True, null=True, verbose_name="Emergency Instructions"
                    ),
                ),
                (
                    "instructions_ar",
                    models.TextField(
                        blank=True,
                        null=True,
                        verbose_name="Emergency Instructions (Arabic)",
                    ),
                ),
                (
                    "contact_info",
                    models.JSONField(
                        default=dict, verbose_name="Emergency Contact Information"
                    ),
                ),
                (
                    "auto_resolve_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="Automatically resolve alert at this time",
                        null=True,
                        verbose_name="Auto Resolve At",
                    ),
                ),
                (
                    "notification_sent",
                    models.BooleanField(
                        default=False, verbose_name="Notification Sent"
                    ),
                ),
                (
                    "acknowledgment_required",
                    models.BooleanField(
                        default=False,
                        help_text="Require recipients to acknowledge receipt",
                        verbose_name="Acknowledgment Required",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "issued_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="issued_emergency_alerts",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Issued By",
                    ),
                ),
                (
                    "resolved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="resolved_emergency_alerts",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Resolved By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Emergency Alert",
                "verbose_name_plural": "Emergency Alerts",
                "ordering": ["-issued_at"],
            },
        ),
        migrations.CreateModel(
            name="EmergencyAlertRecipient",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "recipient_type",
                    models.CharField(
                        choices=[
                            ("user", "User"),
                            ("student", "Student"),
                            ("parent", "Parent"),
                            ("employee", "Employee"),
                            ("contact", "Emergency Contact"),
                        ],
                        max_length=20,
                        verbose_name="Recipient Type",
                    ),
                ),
                ("recipient_id", models.IntegerField(verbose_name="Recipient ID")),
                (
                    "contact_method",
                    models.CharField(
                        choices=[
                            ("email", "Email"),
                            ("sms", "SMS"),
                            ("whatsapp", "WhatsApp"),
                            ("push", "Push Notification"),
                            ("call", "Phone Call"),
                        ],
                        max_length=20,
                        verbose_name="Contact Method",
                    ),
                ),
                (
                    "contact_address",
                    models.CharField(
                        help_text="Email, phone number, etc.",
                        max_length=255,
                        verbose_name="Contact Address",
                    ),
                ),
                (
                    "sent_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="Sent At"),
                ),
                (
                    "delivered_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Delivered At"
                    ),
                ),
                (
                    "acknowledged_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Acknowledged At"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("sent", "Sent"),
                            ("delivered", "Delivered"),
                            ("failed", "Failed"),
                            ("acknowledged", "Acknowledged"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "error_message",
                    models.TextField(
                        blank=True, null=True, verbose_name="Error Message"
                    ),
                ),
                (
                    "alert",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="recipients",
                        to="communications.emergencyalert",
                        verbose_name="Emergency Alert",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Emergency Alert Recipient",
                "verbose_name_plural": "Emergency Alert Recipients",
                "ordering": ["-sent_at"],
            },
        ),
        migrations.CreateModel(
            name="EmergencyContact",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                ("name", models.CharField(max_length=100, verbose_name="Contact Name")),
                (
                    "contact_type",
                    models.CharField(
                        choices=[
                            ("police", "Police"),
                            ("fire", "Fire Department"),
                            ("medical", "Medical/Ambulance"),
                            ("security", "Security"),
                            ("maintenance", "Maintenance"),
                            ("administration", "Administration"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                        verbose_name="Contact Type",
                    ),
                ),
                (
                    "phone_primary",
                    models.CharField(max_length=20, verbose_name="Primary Phone"),
                ),
                (
                    "phone_secondary",
                    models.CharField(
                        blank=True,
                        max_length=20,
                        null=True,
                        verbose_name="Secondary Phone",
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        blank=True, max_length=254, null=True, verbose_name="Email"
                    ),
                ),
                (
                    "address",
                    models.TextField(blank=True, null=True, verbose_name="Address"),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="Notes"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "priority_order",
                    models.IntegerField(
                        default=0,
                        help_text="Lower numbers have higher priority",
                        verbose_name="Priority Order",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Emergency Contact",
                "verbose_name_plural": "Emergency Contacts",
                "ordering": ["contact_type", "priority_order", "name"],
            },
        ),
        migrations.CreateModel(
            name="EmergencyProcedure",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "title",
                    models.CharField(max_length=200, verbose_name="Procedure Title"),
                ),
                (
                    "title_ar",
                    models.CharField(
                        blank=True,
                        max_length=200,
                        null=True,
                        verbose_name="Procedure Title (Arabic)",
                    ),
                ),
                (
                    "procedure_type",
                    models.CharField(
                        choices=[
                            ("evacuation", "Evacuation"),
                            ("lockdown", "Lockdown"),
                            ("shelter", "Shelter in Place"),
                            ("medical", "Medical Emergency"),
                            ("fire", "Fire Emergency"),
                            ("weather", "Severe Weather"),
                            ("security", "Security Incident"),
                            ("general", "General Emergency"),
                        ],
                        max_length=20,
                        verbose_name="Procedure Type",
                    ),
                ),
                ("description", models.TextField(verbose_name="Description")),
                (
                    "description_ar",
                    models.TextField(
                        blank=True, null=True, verbose_name="Description (Arabic)"
                    ),
                ),
                (
                    "steps",
                    models.JSONField(
                        default=list,
                        help_text="List of steps to follow",
                        verbose_name="Procedure Steps",
                    ),
                ),
                (
                    "steps_ar",
                    models.JSONField(
                        default=list, verbose_name="Procedure Steps (Arabic)"
                    ),
                ),
                (
                    "responsible_roles",
                    models.JSONField(
                        default=list,
                        help_text="Roles responsible for executing this procedure",
                        verbose_name="Responsible Roles",
                    ),
                ),
                (
                    "required_resources",
                    models.JSONField(
                        default=list,
                        help_text="Resources needed for this procedure",
                        verbose_name="Required Resources",
                    ),
                ),
                (
                    "estimated_duration",
                    models.IntegerField(
                        blank=True,
                        null=True,
                        verbose_name="Estimated Duration (minutes)",
                    ),
                ),
                (
                    "last_reviewed",
                    models.DateField(
                        blank=True, null=True, verbose_name="Last Reviewed"
                    ),
                ),
                (
                    "review_frequency",
                    models.IntegerField(
                        default=365, verbose_name="Review Frequency (days)"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "attachment",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to="emergency/procedures/",
                        verbose_name="Procedure Document",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Emergency Procedure",
                "verbose_name_plural": "Emergency Procedures",
                "ordering": ["procedure_type", "title"],
            },
        ),
        migrations.CreateModel(
            name="EmergencyDrill",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("title", models.CharField(max_length=200, verbose_name="Drill Title")),
                (
                    "drill_type",
                    models.CharField(
                        choices=[
                            ("fire", "Fire Drill"),
                            ("evacuation", "Evacuation Drill"),
                            ("lockdown", "Lockdown Drill"),
                            ("shelter", "Shelter in Place Drill"),
                            ("medical", "Medical Emergency Drill"),
                            ("security", "Security Drill"),
                        ],
                        max_length=20,
                        verbose_name="Drill Type",
                    ),
                ),
                ("scheduled_date", models.DateTimeField(verbose_name="Scheduled Date")),
                (
                    "actual_start_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Actual Start Time"
                    ),
                ),
                (
                    "actual_end_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Actual End Time"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("scheduled", "Scheduled"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="scheduled",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "participants_expected",
                    models.IntegerField(verbose_name="Expected Participants"),
                ),
                (
                    "participants_actual",
                    models.IntegerField(
                        blank=True, null=True, verbose_name="Actual Participants"
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="Notes"),
                ),
                (
                    "evaluation_score",
                    models.IntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(10),
                        ],
                        verbose_name="Evaluation Score (1-10)",
                    ),
                ),
                (
                    "areas_for_improvement",
                    models.TextField(
                        blank=True, null=True, verbose_name="Areas for Improvement"
                    ),
                ),
                (
                    "coordinator",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="coordinated_drills",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Drill Coordinator",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "observers",
                    models.ManyToManyField(
                        blank=True,
                        related_name="observed_drills",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Observers",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "procedure",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="drills",
                        to="communications.emergencyprocedure",
                        verbose_name="Emergency Procedure",
                    ),
                ),
            ],
            options={
                "verbose_name": "Emergency Drill",
                "verbose_name_plural": "Emergency Drills",
                "ordering": ["-scheduled_date"],
            },
        ),
        migrations.AddIndex(
            model_name="emergencyalert",
            index=models.Index(
                fields=["status", "severity"], name="communicati_status_302574_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="emergencyalert",
            index=models.Index(
                fields=["alert_type", "status"], name="communicati_alert_t_8d511a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="emergencyalert",
            index=models.Index(
                fields=["issued_at"], name="communicati_issued__4bcde3_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="emergencyalertrecipient",
            unique_together={
                ("alert", "recipient_type", "recipient_id", "contact_method")
            },
        ),
    ]
