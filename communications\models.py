"""
Communications models for notification and messaging system
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.conf import settings
from django.core.validators import RegexValidator, MinValueValidator, MaxValueValidator
from datetime import datetime, timed<PERSON>ta
from core.models import BaseModel


class NotificationTemplate(BaseModel):
    """
    Template for notifications with support for multiple channels
    """
    CHANNEL_CHOICES = [
        ('email', _('Email')),
        ('sms', _('SMS')),
        ('whatsapp', _('WhatsApp')),
        ('push', _('Push Notification')),
        ('in_app', _('In-App Notification')),
    ]
    
    CATEGORY_CHOICES = [
        ('academic', _('Academic')),
        ('administrative', _('Administrative')),
        ('financial', _('Financial')),
        ('emergency', _('Emergency')),
        ('general', _('General')),
        ('hr', _('Human Resources')),
        ('student', _('Student Affairs')),
        ('parent', _('Parent Communication')),
    ]
    
    name = models.CharField(
        max_length=100,
        verbose_name=_('Template Name')
    )
    
    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Template Name (Arabic)')
    )
    
    code = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('Template Code'),
        help_text=_('Unique identifier for the template')
    )
    
    category = models.CharField(
        max_length=20,
        choices=CATEGORY_CHOICES,
        default='general',
        verbose_name=_('Category')
    )
    
    channel = models.CharField(
        max_length=20,
        choices=CHANNEL_CHOICES,
        verbose_name=_('Notification Channel')
    )
    
    subject = models.CharField(
        max_length=200,
        verbose_name=_('Subject/Title')
    )
    
    subject_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Subject/Title (Arabic)')
    )
    
    body = models.TextField(
        verbose_name=_('Message Body'),
        help_text=_('Use {{variable}} for dynamic content')
    )
    
    body_ar = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Message Body (Arabic)'),
        help_text=_('Use {{variable}} for dynamic content')
    )
    
    variables = models.JSONField(
        default=dict,
        verbose_name=_('Template Variables'),
        help_text=_('Available variables for this template')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    
    is_system = models.BooleanField(
        default=False,
        verbose_name=_('Is System Template'),
        help_text=_('System templates cannot be deleted')
    )
    
    class Meta:
        verbose_name = _('Notification Template')
        verbose_name_plural = _('Notification Templates')
        ordering = ['category', 'name']
    
    def __str__(self):
        return f"{self.name} ({self.get_channel_display()})"


class NotificationChannel(BaseModel):
    """
    Configuration for notification channels
    """
    CHANNEL_TYPES = [
        ('email', _('Email')),
        ('sms', _('SMS')),
        ('whatsapp', _('WhatsApp')),
        ('push', _('Push Notification')),
    ]
    
    name = models.CharField(
        max_length=50,
        verbose_name=_('Channel Name')
    )
    
    channel_type = models.CharField(
        max_length=20,
        choices=CHANNEL_TYPES,
        verbose_name=_('Channel Type')
    )
    
    configuration = models.JSONField(
        default=dict,
        verbose_name=_('Channel Configuration'),
        help_text=_('API keys, endpoints, and other settings')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    
    is_default = models.BooleanField(
        default=False,
        verbose_name=_('Is Default Channel'),
        help_text=_('Default channel for this type')
    )
    
    rate_limit = models.IntegerField(
        default=100,
        verbose_name=_('Rate Limit per Hour'),
        help_text=_('Maximum messages per hour')
    )
    
    class Meta:
        verbose_name = _('Notification Channel')
        verbose_name_plural = _('Notification Channels')
        ordering = ['channel_type', 'name']
    
    def __str__(self):
        return f"{self.name} ({self.get_channel_type_display()})"


class Notification(BaseModel):
    """
    Individual notification record
    """
    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('sent', _('Sent')),
        ('delivered', _('Delivered')),
        ('failed', _('Failed')),
        ('cancelled', _('Cancelled')),
    ]
    
    PRIORITY_CHOICES = [
        ('low', _('Low')),
        ('normal', _('Normal')),
        ('high', _('High')),
        ('urgent', _('Urgent')),
    ]
    
    template = models.ForeignKey(
        NotificationTemplate,
        on_delete=models.CASCADE,
        related_name='notifications',
        verbose_name=_('Template')
    )
    
    channel = models.ForeignKey(
        NotificationChannel,
        on_delete=models.CASCADE,
        related_name='notifications',
        verbose_name=_('Channel')
    )
    
    recipient_type = models.CharField(
        max_length=20,
        choices=[
            ('user', _('User')),
            ('employee', _('Employee')),
            ('student', _('Student')),
            ('parent', _('Parent')),
            ('group', _('Group')),
            ('all', _('All Users')),
        ],
        verbose_name=_('Recipient Type')
    )
    
    recipient_id = models.IntegerField(
        null=True,
        blank=True,
        verbose_name=_('Recipient ID'),
        help_text=_('ID of the recipient (if applicable)')
    )
    
    recipient_contact = models.CharField(
        max_length=255,
        verbose_name=_('Recipient Contact'),
        help_text=_('Email, phone number, or other contact info')
    )
    
    subject = models.CharField(
        max_length=200,
        verbose_name=_('Subject/Title')
    )
    
    message = models.TextField(
        verbose_name=_('Message Content')
    )
    
    variables = models.JSONField(
        default=dict,
        verbose_name=_('Template Variables'),
        help_text=_('Variables used to render the message')
    )
    
    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_CHOICES,
        default='normal',
        verbose_name=_('Priority')
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('Status')
    )
    
    scheduled_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Scheduled At'),
        help_text=_('When to send the notification (leave blank for immediate)')
    )
    
    sent_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Sent At')
    )
    
    delivered_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Delivered At')
    )
    
    error_message = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Error Message')
    )
    
    external_id = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('External ID'),
        help_text=_('ID from external service (SMS gateway, etc.)')
    )
    
    retry_count = models.IntegerField(
        default=0,
        verbose_name=_('Retry Count')
    )
    
    max_retries = models.IntegerField(
        default=3,
        verbose_name=_('Max Retries')
    )
    
    class Meta:
        verbose_name = _('Notification')
        verbose_name_plural = _('Notifications')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'scheduled_at']),
            models.Index(fields=['recipient_type', 'recipient_id']),
            models.Index(fields=['channel', 'status']),
        ]
    
    def __str__(self):
        return f"{self.subject} - {self.recipient_contact} ({self.status})"
    
    @property
    def can_retry(self):
        """Check if notification can be retried"""
        return (
            self.status == 'failed' and 
            self.retry_count < self.max_retries
        )
    
    @property
    def is_overdue(self):
        """Check if scheduled notification is overdue"""
        if self.scheduled_at and self.status == 'pending':
            from django.utils import timezone
            return self.scheduled_at < timezone.now()
        return False


class NotificationGroup(BaseModel):
    """
    Groups for bulk notifications
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('Group Name')
    )
    
    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Group Name (Arabic)')
    )
    
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )
    
    group_type = models.CharField(
        max_length=20,
        choices=[
            ('static', _('Static Group')),
            ('dynamic', _('Dynamic Group')),
        ],
        default='static',
        verbose_name=_('Group Type')
    )
    
    criteria = models.JSONField(
        default=dict,
        verbose_name=_('Group Criteria'),
        help_text=_('Criteria for dynamic groups')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    
    class Meta:
        verbose_name = _('Notification Group')
        verbose_name_plural = _('Notification Groups')
        ordering = ['name']
    
    def __str__(self):
        return self.name


class NotificationGroupMember(BaseModel):
    """
    Members of notification groups
    """
    group = models.ForeignKey(
        NotificationGroup,
        on_delete=models.CASCADE,
        related_name='members',
        verbose_name=_('Group')
    )
    
    member_type = models.CharField(
        max_length=20,
        choices=[
            ('user', _('User')),
            ('employee', _('Employee')),
            ('student', _('Student')),
            ('parent', _('Parent')),
        ],
        verbose_name=_('Member Type')
    )
    
    member_id = models.IntegerField(
        verbose_name=_('Member ID')
    )
    
    contact_info = models.JSONField(
        default=dict,
        verbose_name=_('Contact Information'),
        help_text=_('Email, phone, etc.')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    
    class Meta:
        verbose_name = _('Group Member')
        verbose_name_plural = _('Group Members')
        unique_together = ['group', 'member_type', 'member_id']
        ordering = ['group', 'member_type']
    
    def __str__(self):
        return f"{self.group.name} - {self.member_type} #{self.member_id}"


class NotificationLog(BaseModel):
    """
    Log of notification activities
    """
    ACTION_CHOICES = [
        ('created', _('Created')),
        ('sent', _('Sent')),
        ('delivered', _('Delivered')),
        ('failed', _('Failed')),
        ('retried', _('Retried')),
        ('cancelled', _('Cancelled')),
    ]
    
    notification = models.ForeignKey(
        Notification,
        on_delete=models.CASCADE,
        related_name='logs',
        verbose_name=_('Notification')
    )
    
    action = models.CharField(
        max_length=20,
        choices=ACTION_CHOICES,
        verbose_name=_('Action')
    )
    
    details = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Details')
    )
    
    response_data = models.JSONField(
        default=dict,
        verbose_name=_('Response Data'),
        help_text=_('Response from external service')
    )
    
    class Meta:
        verbose_name = _('Notification Log')
        verbose_name_plural = _('Notification Logs')
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.notification.subject} - {self.action} at {self.created_at}"


class EmailConfiguration(BaseModel):
    """
    Email server configuration
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('Configuration Name')
    )
    
    smtp_host = models.CharField(
        max_length=255,
        verbose_name=_('SMTP Host')
    )
    
    smtp_port = models.IntegerField(
        default=587,
        verbose_name=_('SMTP Port')
    )
    
    smtp_username = models.CharField(
        max_length=255,
        verbose_name=_('SMTP Username')
    )
    
    smtp_password = models.CharField(
        max_length=255,
        verbose_name=_('SMTP Password')
    )
    
    use_tls = models.BooleanField(
        default=True,
        verbose_name=_('Use TLS')
    )
    
    use_ssl = models.BooleanField(
        default=False,
        verbose_name=_('Use SSL')
    )
    
    from_email = models.EmailField(
        verbose_name=_('From Email')
    )
    
    from_name = models.CharField(
        max_length=100,
        verbose_name=_('From Name')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    
    is_default = models.BooleanField(
        default=False,
        verbose_name=_('Is Default')
    )
    
    class Meta:
        verbose_name = _('Email Configuration')
        verbose_name_plural = _('Email Configurations')
        ordering = ['name']
    
    def __str__(self):
        return self.name


class SMSConfiguration(BaseModel):
    """
    SMS gateway configuration
    """
    GATEWAY_CHOICES = [
        ('twilio', _('Twilio')),
        ('nexmo', _('Nexmo/Vonage')),
        ('aws_sns', _('AWS SNS')),
        ('custom', _('Custom Gateway')),
    ]
    
    name = models.CharField(
        max_length=100,
        verbose_name=_('Configuration Name')
    )
    
    gateway_type = models.CharField(
        max_length=20,
        choices=GATEWAY_CHOICES,
        verbose_name=_('Gateway Type')
    )
    
    api_key = models.CharField(
        max_length=255,
        verbose_name=_('API Key')
    )
    
    api_secret = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_('API Secret')
    )
    
    sender_id = models.CharField(
        max_length=20,
        verbose_name=_('Sender ID')
    )
    
    webhook_url = models.URLField(
        blank=True,
        null=True,
        verbose_name=_('Webhook URL')
    )
    
    configuration = models.JSONField(
        default=dict,
        verbose_name=_('Additional Configuration')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    
    is_default = models.BooleanField(
        default=False,
        verbose_name=_('Is Default')
    )
    
    class Meta:
        verbose_name = _('SMS Configuration')
        verbose_name_plural = _('SMS Configurations')
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.get_gateway_type_display()})"


class WhatsAppConfiguration(BaseModel):
    """
    WhatsApp Business API configuration
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('Configuration Name')
    )
    
    business_account_id = models.CharField(
        max_length=100,
        verbose_name=_('Business Account ID')
    )
    
    phone_number_id = models.CharField(
        max_length=100,
        verbose_name=_('Phone Number ID')
    )
    
    access_token = models.CharField(
        max_length=500,
        verbose_name=_('Access Token')
    )
    
    webhook_verify_token = models.CharField(
        max_length=255,
        verbose_name=_('Webhook Verify Token')
    )
    
    webhook_url = models.URLField(
        blank=True,
        null=True,
        verbose_name=_('Webhook URL')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    
    is_default = models.BooleanField(
        default=False,
        verbose_name=_('Is Default')
    )
    
    class Meta:
        verbose_name = _('WhatsApp Configuration')
        verbose_name_plural = _('WhatsApp Configurations')
        ordering = ['name']
    
    def __str__(self):
        return self.name


class AnnouncementCategory(BaseModel):
    """
    Categories for announcements
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('Category Name')
    )
    
    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Category Name (Arabic)')
    )
    
    code = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('Category Code')
    )
    
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )
    
    color = models.CharField(
        max_length=7,
        default='#007bff',
        verbose_name=_('Color'),
        help_text=_('Hex color code for the category')
    )
    
    icon = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('Icon'),
        help_text=_('Font Awesome icon class')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    
    class Meta:
        verbose_name = _('Announcement Category')
        verbose_name_plural = _('Announcement Categories')
        ordering = ['name']
    
    def __str__(self):
        return self.name


class Announcement(BaseModel):
    """
    Main announcement model
    """
    PRIORITY_CHOICES = [
        ('low', _('Low')),
        ('normal', _('Normal')),
        ('high', _('High')),
        ('urgent', _('Urgent')),
    ]
    
    STATUS_CHOICES = [
        ('draft', _('Draft')),
        ('scheduled', _('Scheduled')),
        ('published', _('Published')),
        ('archived', _('Archived')),
    ]
    
    AUDIENCE_CHOICES = [
        ('all', _('All Users')),
        ('students', _('Students')),
        ('parents', _('Parents')),
        ('teachers', _('Teachers')),
        ('staff', _('Staff')),
        ('custom', _('Custom Groups')),
    ]
    
    title = models.CharField(
        max_length=200,
        verbose_name=_('Title')
    )
    
    title_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Title (Arabic)')
    )
    
    content = models.TextField(
        verbose_name=_('Content')
    )
    
    content_ar = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Content (Arabic)')
    )
    
    category = models.ForeignKey(
        AnnouncementCategory,
        on_delete=models.CASCADE,
        related_name='announcements',
        verbose_name=_('Category')
    )
    
    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_CHOICES,
        default='normal',
        verbose_name=_('Priority')
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        verbose_name=_('Status')
    )
    
    audience = models.CharField(
        max_length=20,
        choices=AUDIENCE_CHOICES,
        default='all',
        verbose_name=_('Target Audience')
    )
    
    author = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='authored_announcements',
        verbose_name=_('Author')
    )
    
    published_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Published At')
    )
    
    scheduled_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Scheduled At'),
        help_text=_('When to publish the announcement')
    )
    
    expires_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Expires At'),
        help_text=_('When the announcement expires')
    )
    
    is_pinned = models.BooleanField(
        default=False,
        verbose_name=_('Is Pinned'),
        help_text=_('Pinned announcements appear at the top')
    )
    
    send_notification = models.BooleanField(
        default=True,
        verbose_name=_('Send Notification'),
        help_text=_('Send notification when published')
    )
    
    notification_channels = models.JSONField(
        default=list,
        verbose_name=_('Notification Channels'),
        help_text=_('Channels to send notifications through')
    )
    
    attachment = models.FileField(
        upload_to='announcements/attachments/',
        blank=True,
        null=True,
        verbose_name=_('Attachment')
    )
    
    view_count = models.PositiveIntegerField(
        default=0,
        verbose_name=_('View Count')
    )
    
    class Meta:
        verbose_name = _('Announcement')
        verbose_name_plural = _('Announcements')
        ordering = ['-is_pinned', '-published_at', '-created_at']
        indexes = [
            models.Index(fields=['status', 'published_at']),
            models.Index(fields=['audience', 'status']),
            models.Index(fields=['category', 'status']),
        ]
    
    def __str__(self):
        return self.title
    
    @property
    def is_published(self):
        """Check if announcement is published"""
        return self.status == 'published'
    
    @property
    def is_expired(self):
        """Check if announcement is expired"""
        if self.expires_at:
            from django.utils import timezone
            return self.expires_at < timezone.now()
        return False
    
    @property
    def is_scheduled(self):
        """Check if announcement is scheduled for future"""
        if self.scheduled_at:
            from django.utils import timezone
            return self.scheduled_at > timezone.now()
        return False
    
    def increment_view_count(self):
        """Increment view count"""
        self.view_count += 1
        self.save(update_fields=['view_count'])


class AnnouncementTarget(BaseModel):
    """
    Specific targets for announcements (when audience is 'custom')
    """
    announcement = models.ForeignKey(
        Announcement,
        on_delete=models.CASCADE,
        related_name='targets',
        verbose_name=_('Announcement')
    )
    
    target_type = models.CharField(
        max_length=20,
        choices=[
            ('user', _('User')),
            ('group', _('Group')),
            ('department', _('Department')),
            ('grade', _('Grade')),
            ('class', _('Class')),
        ],
        verbose_name=_('Target Type')
    )
    
    target_id = models.IntegerField(
        verbose_name=_('Target ID')
    )
    
    class Meta:
        verbose_name = _('Announcement Target')
        verbose_name_plural = _('Announcement Targets')
        unique_together = ['announcement', 'target_type', 'target_id']
    
    def __str__(self):
        return f"{self.announcement.title} - {self.target_type} #{self.target_id}"


class AnnouncementView(BaseModel):
    """
    Track who has viewed announcements
    """
    announcement = models.ForeignKey(
        Announcement,
        on_delete=models.CASCADE,
        related_name='views',
        verbose_name=_('Announcement')
    )
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='announcement_views',
        verbose_name=_('User')
    )
    
    viewed_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Viewed At')
    )
    
    class Meta:
        verbose_name = _('Announcement View')
        verbose_name_plural = _('Announcement Views')
        unique_together = ['announcement', 'user']
        ordering = ['-viewed_at']
    
    def __str__(self):
        return f"{self.user.username} viewed {self.announcement.title}"


class AnnouncementComment(BaseModel):
    """
    Comments on announcements
    """
    announcement = models.ForeignKey(
        Announcement,
        on_delete=models.CASCADE,
        related_name='comments',
        verbose_name=_('Announcement')
    )
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='announcement_comments',
        verbose_name=_('User')
    )
    
    content = models.TextField(
        verbose_name=_('Comment')
    )
    
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='replies',
        verbose_name=_('Parent Comment')
    )
    
    is_approved = models.BooleanField(
        default=True,
        verbose_name=_('Is Approved')
    )
    
    class Meta:
        verbose_name = _('Announcement Comment')
        verbose_name_plural = _('Announcement Comments')
        ordering = ['created_at']
    
    def __str__(self):
        return f"Comment by {self.user.username} on {self.announcement.title}"


class AnnouncementReaction(BaseModel):
    """
    Reactions to announcements (like, dislike, etc.)
    """
    REACTION_CHOICES = [
        ('like', _('Like')),
        ('love', _('Love')),
        ('laugh', _('Laugh')),
        ('wow', _('Wow')),
        ('sad', _('Sad')),
        ('angry', _('Angry')),
    ]
    
    announcement = models.ForeignKey(
        Announcement,
        on_delete=models.CASCADE,
        related_name='reactions',
        verbose_name=_('Announcement')
    )
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='announcement_reactions',
        verbose_name=_('User')
    )
    
    reaction = models.CharField(
        max_length=10,
        choices=REACTION_CHOICES,
        verbose_name=_('Reaction')
    )
    
    class Meta:
        verbose_name = _('Announcement Reaction')
        verbose_name_plural = _('Announcement Reactions')
        unique_together = ['announcement', 'user']
    
    def __str__(self):
        return f"{self.user.username} {self.reaction} {self.announcement.title}"


class EmergencyAlert(BaseModel):
    """
    Emergency alert system for critical notifications
    """
    SEVERITY_CHOICES = [
        ('low', _('Low')),
        ('medium', _('Medium')),
        ('high', _('High')),
        ('critical', _('Critical')),
    ]
    
    ALERT_TYPE_CHOICES = [
        ('weather', _('Weather Emergency')),
        ('security', _('Security Alert')),
        ('medical', _('Medical Emergency')),
        ('fire', _('Fire Emergency')),
        ('lockdown', _('Lockdown')),
        ('evacuation', _('Evacuation')),
        ('general', _('General Emergency')),
    ]
    
    STATUS_CHOICES = [
        ('active', _('Active')),
        ('resolved', _('Resolved')),
        ('cancelled', _('Cancelled')),
    ]
    
    title = models.CharField(
        max_length=200,
        verbose_name=_('Alert Title')
    )
    
    title_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Alert Title (Arabic)')
    )
    
    message = models.TextField(
        verbose_name=_('Alert Message')
    )
    
    message_ar = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Alert Message (Arabic)')
    )
    
    alert_type = models.CharField(
        max_length=20,
        choices=ALERT_TYPE_CHOICES,
        verbose_name=_('Alert Type')
    )
    
    severity = models.CharField(
        max_length=10,
        choices=SEVERITY_CHOICES,
        default='medium',
        verbose_name=_('Severity Level')
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='active',
        verbose_name=_('Status')
    )
    
    issued_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='issued_emergency_alerts',
        verbose_name=_('Issued By')
    )
    
    issued_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Issued At')
    )
    
    resolved_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Resolved At')
    )
    
    resolved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='resolved_emergency_alerts',
        verbose_name=_('Resolved By')
    )
    
    affected_areas = models.JSONField(
        default=list,
        verbose_name=_('Affected Areas'),
        help_text=_('List of affected buildings, floors, or areas')
    )
    
    instructions = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Emergency Instructions')
    )
    
    instructions_ar = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Emergency Instructions (Arabic)')
    )
    
    contact_info = models.JSONField(
        default=dict,
        verbose_name=_('Emergency Contact Information')
    )
    
    auto_resolve_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Auto Resolve At'),
        help_text=_('Automatically resolve alert at this time')
    )
    
    notification_sent = models.BooleanField(
        default=False,
        verbose_name=_('Notification Sent')
    )
    
    acknowledgment_required = models.BooleanField(
        default=False,
        verbose_name=_('Acknowledgment Required'),
        help_text=_('Require recipients to acknowledge receipt')
    )
    
    class Meta:
        verbose_name = _('Emergency Alert')
        verbose_name_plural = _('Emergency Alerts')
        ordering = ['-issued_at']
        indexes = [
            models.Index(fields=['status', 'severity']),
            models.Index(fields=['alert_type', 'status']),
            models.Index(fields=['issued_at']),
        ]
    
    def __str__(self):
        return f"{self.get_alert_type_display()}: {self.title}"
    
    @property
    def is_active(self):
        """Check if alert is currently active"""
        return self.status == 'active'
    
    @property
    def is_critical(self):
        """Check if alert is critical severity"""
        return self.severity == 'critical'
    
    @property
    def duration_minutes(self):
        """Get alert duration in minutes"""
        if self.resolved_at:
            return int((self.resolved_at - self.issued_at).total_seconds() / 60)
        return int((timezone.now() - self.issued_at).total_seconds() / 60)
    
    def resolve(self, user=None):
        """Resolve the emergency alert"""
        self.status = 'resolved'
        self.resolved_at = timezone.now()
        if user:
            self.resolved_by = user
        self.save()
    
    def cancel(self, user=None):
        """Cancel the emergency alert"""
        self.status = 'cancelled'
        self.resolved_at = timezone.now()
        if user:
            self.resolved_by = user
        self.save()


class EmergencyContact(BaseModel):
    """
    Emergency contact information
    """
    CONTACT_TYPE_CHOICES = [
        ('police', _('Police')),
        ('fire', _('Fire Department')),
        ('medical', _('Medical/Ambulance')),
        ('security', _('Security')),
        ('maintenance', _('Maintenance')),
        ('administration', _('Administration')),
        ('other', _('Other')),
    ]
    
    name = models.CharField(
        max_length=100,
        verbose_name=_('Contact Name')
    )
    
    contact_type = models.CharField(
        max_length=20,
        choices=CONTACT_TYPE_CHOICES,
        verbose_name=_('Contact Type')
    )
    
    phone_primary = models.CharField(
        max_length=20,
        verbose_name=_('Primary Phone')
    )
    
    phone_secondary = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('Secondary Phone')
    )
    
    email = models.EmailField(
        blank=True,
        null=True,
        verbose_name=_('Email')
    )
    
    address = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Address')
    )
    
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    
    priority_order = models.IntegerField(
        default=0,
        verbose_name=_('Priority Order'),
        help_text=_('Lower numbers have higher priority')
    )
    
    class Meta:
        verbose_name = _('Emergency Contact')
        verbose_name_plural = _('Emergency Contacts')
        ordering = ['contact_type', 'priority_order', 'name']
    
    def __str__(self):
        return f"{self.name} ({self.get_contact_type_display()})"


class EmergencyAlertRecipient(BaseModel):
    """
    Track who received emergency alerts
    """
    alert = models.ForeignKey(
        EmergencyAlert,
        on_delete=models.CASCADE,
        related_name='recipients',
        verbose_name=_('Emergency Alert')
    )
    
    recipient_type = models.CharField(
        max_length=20,
        choices=[
            ('user', _('User')),
            ('student', _('Student')),
            ('parent', _('Parent')),
            ('employee', _('Employee')),
            ('contact', _('Emergency Contact')),
        ],
        verbose_name=_('Recipient Type')
    )
    
    recipient_id = models.IntegerField(
        verbose_name=_('Recipient ID')
    )
    
    contact_method = models.CharField(
        max_length=20,
        choices=[
            ('email', _('Email')),
            ('sms', _('SMS')),
            ('whatsapp', _('WhatsApp')),
            ('push', _('Push Notification')),
            ('call', _('Phone Call')),
        ],
        verbose_name=_('Contact Method')
    )
    
    contact_address = models.CharField(
        max_length=255,
        verbose_name=_('Contact Address'),
        help_text=_('Email, phone number, etc.')
    )
    
    sent_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Sent At')
    )
    
    delivered_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Delivered At')
    )
    
    acknowledged_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Acknowledged At')
    )
    
    status = models.CharField(
        max_length=20,
        choices=[
            ('pending', _('Pending')),
            ('sent', _('Sent')),
            ('delivered', _('Delivered')),
            ('failed', _('Failed')),
            ('acknowledged', _('Acknowledged')),
        ],
        default='pending',
        verbose_name=_('Status')
    )
    
    error_message = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Error Message')
    )
    
    class Meta:
        verbose_name = _('Emergency Alert Recipient')
        verbose_name_plural = _('Emergency Alert Recipients')
        unique_together = ['alert', 'recipient_type', 'recipient_id', 'contact_method']
        ordering = ['-sent_at']
    
    def __str__(self):
        return f"{self.alert.title} - {self.recipient_type} #{self.recipient_id}"


class EmergencyProcedure(BaseModel):
    """
    Emergency procedures and protocols
    """
    PROCEDURE_TYPE_CHOICES = [
        ('evacuation', _('Evacuation')),
        ('lockdown', _('Lockdown')),
        ('shelter', _('Shelter in Place')),
        ('medical', _('Medical Emergency')),
        ('fire', _('Fire Emergency')),
        ('weather', _('Severe Weather')),
        ('security', _('Security Incident')),
        ('general', _('General Emergency')),
    ]
    
    title = models.CharField(
        max_length=200,
        verbose_name=_('Procedure Title')
    )
    
    title_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Procedure Title (Arabic)')
    )
    
    procedure_type = models.CharField(
        max_length=20,
        choices=PROCEDURE_TYPE_CHOICES,
        verbose_name=_('Procedure Type')
    )
    
    description = models.TextField(
        verbose_name=_('Description')
    )
    
    description_ar = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description (Arabic)')
    )
    
    steps = models.JSONField(
        default=list,
        verbose_name=_('Procedure Steps'),
        help_text=_('List of steps to follow')
    )
    
    steps_ar = models.JSONField(
        default=list,
        verbose_name=_('Procedure Steps (Arabic)')
    )
    
    responsible_roles = models.JSONField(
        default=list,
        verbose_name=_('Responsible Roles'),
        help_text=_('Roles responsible for executing this procedure')
    )
    
    required_resources = models.JSONField(
        default=list,
        verbose_name=_('Required Resources'),
        help_text=_('Resources needed for this procedure')
    )
    
    estimated_duration = models.IntegerField(
        null=True,
        blank=True,
        verbose_name=_('Estimated Duration (minutes)')
    )
    
    last_reviewed = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Last Reviewed')
    )
    
    review_frequency = models.IntegerField(
        default=365,
        verbose_name=_('Review Frequency (days)')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    
    attachment = models.FileField(
        upload_to='emergency/procedures/',
        blank=True,
        null=True,
        verbose_name=_('Procedure Document')
    )
    
    class Meta:
        verbose_name = _('Emergency Procedure')
        verbose_name_plural = _('Emergency Procedures')
        ordering = ['procedure_type', 'title']
    
    def __str__(self):
        return f"{self.get_procedure_type_display()}: {self.title}"
    
    @property
    def is_due_for_review(self):
        """Check if procedure is due for review"""
        if not self.last_reviewed:
            return True
        
        from django.utils import timezone
        next_review = self.last_reviewed + timedelta(days=self.review_frequency)
        return next_review <= timezone.now().date()


class EmergencyDrill(BaseModel):
    """
    Emergency drill records and scheduling
    """
    DRILL_TYPE_CHOICES = [
        ('fire', _('Fire Drill')),
        ('evacuation', _('Evacuation Drill')),
        ('lockdown', _('Lockdown Drill')),
        ('shelter', _('Shelter in Place Drill')),
        ('medical', _('Medical Emergency Drill')),
        ('security', _('Security Drill')),
    ]
    
    STATUS_CHOICES = [
        ('scheduled', _('Scheduled')),
        ('in_progress', _('In Progress')),
        ('completed', _('Completed')),
        ('cancelled', _('Cancelled')),
    ]
    
    title = models.CharField(
        max_length=200,
        verbose_name=_('Drill Title')
    )
    
    drill_type = models.CharField(
        max_length=20,
        choices=DRILL_TYPE_CHOICES,
        verbose_name=_('Drill Type')
    )
    
    procedure = models.ForeignKey(
        EmergencyProcedure,
        on_delete=models.CASCADE,
        related_name='drills',
        verbose_name=_('Emergency Procedure')
    )
    
    scheduled_date = models.DateTimeField(
        verbose_name=_('Scheduled Date')
    )
    
    actual_start_time = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Actual Start Time')
    )
    
    actual_end_time = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Actual End Time')
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='scheduled',
        verbose_name=_('Status')
    )
    
    participants_expected = models.IntegerField(
        verbose_name=_('Expected Participants')
    )
    
    participants_actual = models.IntegerField(
        null=True,
        blank=True,
        verbose_name=_('Actual Participants')
    )
    
    coordinator = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='coordinated_drills',
        verbose_name=_('Drill Coordinator')
    )
    
    observers = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        blank=True,
        related_name='observed_drills',
        verbose_name=_('Observers')
    )
    
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )
    
    evaluation_score = models.IntegerField(
        null=True,
        blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(10)],
        verbose_name=_('Evaluation Score (1-10)')
    )
    
    areas_for_improvement = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Areas for Improvement')
    )
    
    class Meta:
        verbose_name = _('Emergency Drill')
        verbose_name_plural = _('Emergency Drills')
        ordering = ['-scheduled_date']
    
    def __str__(self):
        return f"{self.get_drill_type_display()} - {self.scheduled_date.strftime('%Y-%m-%d')}"
    
    @property
    def duration_minutes(self):
        """Get drill duration in minutes"""
        if self.actual_start_time and self.actual_end_time:
            return int((self.actual_end_time - self.actual_start_time).total_seconds() / 60)
        return None
    
    @property
    def participation_rate(self):
        """Get participation rate percentage"""
        if self.participants_expected and self.participants_actual:
            return round((self.participants_actual / self.participants_expected) * 100, 2)
        return None