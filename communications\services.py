"""
Communication services for handling notifications and messaging
"""
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from django.conf import settings
from django.core.mail import send_mail, EmailMultiAlternatives
from django.template import Template, Context
from django.utils import timezone
from django.db import transaction
from django.core.exceptions import ValidationError

from .models import (
    NotificationTemplate, NotificationChannel, Notification,
    NotificationGroup, NotificationGroupMember, NotificationLog,
    EmailConfiguration, SMSConfiguration, WhatsAppConfiguration
)

logger = logging.getLogger(__name__)


class NotificationService:
    """
    Core service for managing notifications
    """
    
    @staticmethod
    def create_notification(
        template_code: str,
        recipient_contact: str,
        variables: Dict[str, Any] = None,
        recipient_type: str = 'user',
        recipient_id: int = None,
        priority: str = 'normal',
        scheduled_at: datetime = None,
        channel_type: str = None,
        school=None
    ) -> Notification:
        """
        Create a new notification
        """
        try:
            with transaction.atomic():
                # Get template
                template_filter = {'code': template_code, 'is_active': True}
                if school:
                    template_filter['school'] = school
                template = NotificationTemplate.objects.get(**template_filter)
                
                # Get channel
                channel_filter = {'is_active': True, 'is_default': True}
                if school:
                    channel_filter['school'] = school
                
                if channel_type:
                    channel_filter['channel_type'] = channel_type
                else:
                    channel_filter['channel_type'] = template.channel
                
                channel = NotificationChannel.objects.get(**channel_filter)
                
                # Render message content
                variables = variables or {}
                subject = NotificationService._render_template(template.subject, variables)
                message = NotificationService._render_template(template.body, variables)
                
                # Create notification
                notification_data = {
                    'template': template,
                    'channel': channel,
                    'recipient_type': recipient_type,
                    'recipient_id': recipient_id,
                    'recipient_contact': recipient_contact,
                    'subject': subject,
                    'message': message,
                    'variables': variables,
                    'priority': priority,
                    'scheduled_at': scheduled_at
                }
                
                if school:
                    notification_data['school'] = school
                
                notification = Notification.objects.create(**notification_data)
                
                # Log creation
                log_data = {
                    'notification': notification,
                    'action': 'created',
                    'details': f'Notification created with template {template_code}'
                }
                if school:
                    log_data['school'] = school
                
                NotificationLog.objects.create(**log_data)
                
                return notification
                
        except Exception as e:
            logger.error(f"Error creating notification: {str(e)}")
            raise ValidationError(f"Failed to create notification: {str(e)}")
    
    @staticmethod
    def send_notification(notification: Notification) -> bool:
        """
        Send a notification through the appropriate channel
        """
        try:
            channel_type = notification.channel.channel_type
            
            if channel_type == 'email':
                return EmailService.send_email_notification(notification)
            elif channel_type == 'sms':
                return SMSService.send_sms_notification(notification)
            elif channel_type == 'whatsapp':
                return WhatsAppService.send_whatsapp_notification(notification)
            elif channel_type == 'push':
                return PushNotificationService.send_push_notification(notification)
            else:
                logger.error(f"Unsupported channel type: {channel_type}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending notification {notification.id}: {str(e)}")
            notification.status = 'failed'
            notification.error_message = str(e)
            notification.save()
            
            # Log failure
            log_data = {
                'notification': notification,
                'action': 'failed',
                'details': str(e)
            }
            if hasattr(notification, 'school') and notification.school:
                log_data['school'] = notification.school
            
            NotificationLog.objects.create(**log_data)
            
            return False
    
    @staticmethod
    def send_bulk_notification(
        template_code: str,
        recipients: List[Dict[str, Any]],
        variables: Dict[str, Any] = None,
        priority: str = 'normal',
        scheduled_at: datetime = None,
        school=None
    ) -> List[Notification]:
        """
        Send notifications to multiple recipients
        """
        notifications = []
        
        for recipient in recipients:
            try:
                notification = NotificationService.create_notification(
                    template_code=template_code,
                    recipient_contact=recipient['contact'],
                    variables={**(variables or {}), **recipient.get('variables', {})},
                    recipient_type=recipient.get('type', 'user'),
                    recipient_id=recipient.get('id'),
                    priority=priority,
                    scheduled_at=scheduled_at,
                    school=school
                )
                notifications.append(notification)
                
                # Send immediately if not scheduled
                if not scheduled_at:
                    NotificationService.send_notification(notification)
                    
            except Exception as e:
                logger.error(f"Error creating bulk notification for {recipient}: {str(e)}")
                continue
        
        return notifications
    
    @staticmethod
    def send_group_notification(
        group_id: int,
        template_code: str,
        variables: Dict[str, Any] = None,
        priority: str = 'normal',
        scheduled_at: datetime = None
    ) -> List[Notification]:
        """
        Send notification to all members of a group
        """
        try:
            group = NotificationGroup.objects.get(id=group_id, is_active=True)
            members = NotificationGroupMember.objects.filter(
                group=group,
                is_active=True
            )
            
            recipients = []
            for member in members:
                contact_info = member.contact_info
                primary_contact = (
                    contact_info.get('email') or 
                    contact_info.get('phone') or 
                    contact_info.get('whatsapp')
                )
                
                if primary_contact:
                    recipients.append({
                        'contact': primary_contact,
                        'type': member.member_type,
                        'id': member.member_id,
                        'variables': contact_info
                    })
            
            return NotificationService.send_bulk_notification(
                template_code=template_code,
                recipients=recipients,
                variables=variables,
                priority=priority,
                scheduled_at=scheduled_at,
                school=group.school
            )
            
        except Exception as e:
            logger.error(f"Error sending group notification: {str(e)}")
            return []
    
    @staticmethod
    def retry_failed_notification(notification: Notification) -> bool:
        """
        Retry a failed notification
        """
        if not notification.can_retry:
            return False
        
        try:
            notification.retry_count += 1
            notification.status = 'pending'
            notification.error_message = None
            notification.save()
            
            # Log retry
            log_data = {
                'notification': notification,
                'action': 'retried',
                'details': f'Retry attempt {notification.retry_count}'
            }
            if hasattr(notification, 'school') and notification.school:
                log_data['school'] = notification.school
            
            NotificationLog.objects.create(**log_data)
            
            return NotificationService.send_notification(notification)
            
        except Exception as e:
            logger.error(f"Error retrying notification {notification.id}: {str(e)}")
            return False
    
    @staticmethod
    def process_scheduled_notifications():
        """
        Process notifications scheduled for sending
        """
        now = timezone.now()
        scheduled_notifications = Notification.objects.filter(
            status='pending',
            scheduled_at__lte=now
        )
        
        for notification in scheduled_notifications:
            NotificationService.send_notification(notification)
    
    @staticmethod
    def _render_template(template_string: str, variables: Dict[str, Any]) -> str:
        """
        Render template with variables
        """
        try:
            template = Template(template_string)
            context = Context(variables)
            return template.render(context)
        except Exception as e:
            logger.error(f"Error rendering template: {str(e)}")
            return template_string


class EmailService:
    """
    Service for handling email notifications
    """
    
    @staticmethod
    def send_email_notification(notification: Notification) -> bool:
        """
        Send email notification
        """
        try:
            # Get email configuration
            config = EmailConfiguration.objects.filter(
                is_active=True,
                is_default=True
            ).first()
            
            if not config:
                raise ValidationError("No active email configuration found")
            
            # Create email
            email = EmailMultiAlternatives(
                subject=notification.subject,
                body=notification.message,
                from_email=f"{config.from_name} <{config.from_email}>",
                to=[notification.recipient_contact]
            )
            
            # Send email
            email.send()
            
            # Update notification status
            notification.status = 'sent'
            notification.sent_at = timezone.now()
            notification.save()
            
            # Log success
            log_data = {
                'notification': notification,
                'action': 'sent',
                'details': 'Email sent successfully'
            }
            if hasattr(notification, 'school') and notification.school:
                log_data['school'] = notification.school
            
            NotificationLog.objects.create(**log_data)
            
            return True
            
        except Exception as e:
            logger.error(f"Error sending email notification: {str(e)}")
            notification.status = 'failed'
            notification.error_message = str(e)
            notification.save()
            return False


class SMSService:
    """
    Service for handling SMS notifications
    """
    
    @staticmethod
    def send_sms_notification(notification: Notification) -> bool:
        """
        Send SMS notification
        """
        try:
            # Get SMS configuration
            config = SMSConfiguration.objects.filter(
                is_active=True,
                is_default=True
            ).first()
            
            if not config:
                raise ValidationError("No active SMS configuration found")
            
            # Send based on gateway type
            if config.gateway_type == 'twilio':
                return SMSService._send_twilio_sms(notification, config)
            elif config.gateway_type == 'nexmo':
                return SMSService._send_nexmo_sms(notification, config)
            elif config.gateway_type == 'aws_sns':
                return SMSService._send_aws_sns_sms(notification, config)
            else:
                return SMSService._send_custom_sms(notification, config)
                
        except Exception as e:
            logger.error(f"Error sending SMS notification: {str(e)}")
            notification.status = 'failed'
            notification.error_message = str(e)
            notification.save()
            return False
    
    @staticmethod
    def _send_twilio_sms(notification: Notification, config: SMSConfiguration) -> bool:
        """
        Send SMS via Twilio
        """
        try:
            from twilio.rest import Client
            
            client = Client(config.api_key, config.api_secret)
            
            message = client.messages.create(
                body=notification.message,
                from_=config.sender_id,
                to=notification.recipient_contact
            )
            
            notification.status = 'sent'
            notification.sent_at = timezone.now()
            notification.external_id = message.sid
            notification.save()
            
            # Log success
            log_data = {
                'notification': notification,
                'action': 'sent',
                'details': 'SMS sent via Twilio',
                'response_data': {'sid': message.sid}
            }
            if hasattr(notification, 'school') and notification.school:
                log_data['school'] = notification.school
            
            NotificationLog.objects.create(**log_data)
            
            return True
            
        except Exception as e:
            logger.error(f"Error sending Twilio SMS: {str(e)}")
            return False
    
    @staticmethod
    def _send_nexmo_sms(notification: Notification, config: SMSConfiguration) -> bool:
        """
        Send SMS via Nexmo/Vonage
        """
        # Implementation for Nexmo/Vonage
        # This would require the nexmo library
        logger.info("Nexmo SMS sending not implemented yet")
        return False
    
    @staticmethod
    def _send_aws_sns_sms(notification: Notification, config: SMSConfiguration) -> bool:
        """
        Send SMS via AWS SNS
        """
        # Implementation for AWS SNS
        # This would require boto3
        logger.info("AWS SNS SMS sending not implemented yet")
        return False
    
    @staticmethod
    def _send_custom_sms(notification: Notification, config: SMSConfiguration) -> bool:
        """
        Send SMS via custom gateway
        """
        # Implementation for custom SMS gateway
        logger.info("Custom SMS gateway not implemented yet")
        return False


class WhatsAppService:
    """
    Service for handling WhatsApp notifications
    """
    
    @staticmethod
    def send_whatsapp_notification(notification: Notification) -> bool:
        """
        Send WhatsApp notification
        """
        try:
            # Get WhatsApp configuration
            config = WhatsAppConfiguration.objects.filter(
                is_active=True,
                is_default=True
            ).first()
            
            if not config:
                raise ValidationError("No active WhatsApp configuration found")
            
            # Send WhatsApp message via Business API
            return WhatsAppService._send_business_api_message(notification, config)
            
        except Exception as e:
            logger.error(f"Error sending WhatsApp notification: {str(e)}")
            notification.status = 'failed'
            notification.error_message = str(e)
            notification.save()
            return False
    
    @staticmethod
    def _send_business_api_message(notification: Notification, config: WhatsAppConfiguration) -> bool:
        """
        Send message via WhatsApp Business API
        """
        try:
            import requests
            
            url = f"https://graph.facebook.com/v17.0/{config.phone_number_id}/messages"
            
            headers = {
                'Authorization': f'Bearer {config.access_token}',
                'Content-Type': 'application/json'
            }
            
            data = {
                'messaging_product': 'whatsapp',
                'to': notification.recipient_contact,
                'type': 'text',
                'text': {
                    'body': notification.message
                }
            }
            
            response = requests.post(url, headers=headers, json=data)
            response.raise_for_status()
            
            result = response.json()
            
            notification.status = 'sent'
            notification.sent_at = timezone.now()
            notification.external_id = result.get('messages', [{}])[0].get('id')
            notification.save()
            
            # Log success
            log_data = {
                'notification': notification,
                'action': 'sent',
                'details': 'WhatsApp message sent via Business API',
                'response_data': result
            }
            if hasattr(notification, 'school') and notification.school:
                log_data['school'] = notification.school
            
            NotificationLog.objects.create(**log_data)
            
            return True
            
        except Exception as e:
            logger.error(f"Error sending WhatsApp Business API message: {str(e)}")
            return False


class PushNotificationService:
    """
    Service for handling push notifications
    """
    
    @staticmethod
    def send_push_notification(notification: Notification) -> bool:
        """
        Send push notification
        """
        try:
            # Implementation for push notifications
            # This would typically use Firebase Cloud Messaging or similar
            logger.info("Push notification sending not implemented yet")
            
            # For now, mark as sent
            notification.status = 'sent'
            notification.sent_at = timezone.now()
            notification.save()
            
            return True
            
        except Exception as e:
            logger.error(f"Error sending push notification: {str(e)}")
            return False


class TemplateService:
    """
    Service for managing notification templates
    """
    
    @staticmethod
    def create_template(
        name: str,
        code: str,
        category: str,
        channel: str,
        subject: str,
        body: str,
        variables: Dict[str, Any] = None,
        school=None
    ) -> NotificationTemplate:
        """
        Create a new notification template
        """
        try:
            template = NotificationTemplate.objects.create(
                school=school,
                name=name,
                code=code,
                category=category,
                channel=channel,
                subject=subject,
                body=body,
                variables=variables or {}
            )
            
            return template
            
        except Exception as e:
            logger.error(f"Error creating template: {str(e)}")
            raise ValidationError(f"Failed to create template: {str(e)}")
    
    @staticmethod
    def get_available_variables(template_code: str) -> Dict[str, Any]:
        """
        Get available variables for a template
        """
        try:
            template = NotificationTemplate.objects.get(code=template_code)
            return template.variables
        except NotificationTemplate.DoesNotExist:
            return {}
    
    @staticmethod
    def validate_template(subject: str, body: str, variables: Dict[str, Any]) -> bool:
        """
        Validate template syntax
        """
        try:
            # Test rendering with sample variables
            NotificationService._render_template(subject, variables)
            NotificationService._render_template(body, variables)
            return True
        except Exception as e:
            logger.error(f"Template validation error: {str(e)}")
            return False


class GroupService:
    """
    Service for managing notification groups
    """
    
    @staticmethod
    def create_group(name: str, description: str = None, school=None) -> NotificationGroup:
        """
        Create a new notification group
        """
        try:
            group = NotificationGroup.objects.create(
                school=school,
                name=name,
                description=description
            )
            
            return group
            
        except Exception as e:
            logger.error(f"Error creating group: {str(e)}")
            raise ValidationError(f"Failed to create group: {str(e)}")
    
    @staticmethod
    def add_member_to_group(
        group_id: int,
        member_type: str,
        member_id: int,
        contact_info: Dict[str, str]
    ) -> NotificationGroupMember:
        """
        Add a member to a notification group
        """
        try:
            group = NotificationGroup.objects.get(id=group_id)
            
            member, created = NotificationGroupMember.objects.get_or_create(
                group=group,
                member_type=member_type,
                member_id=member_id,
                defaults={
                    'contact_info': contact_info,
                    'school': group.school
                }
            )
            
            if not created:
                member.contact_info = contact_info
                member.is_active = True
                member.save()
            
            return member
            
        except Exception as e:
            logger.error(f"Error adding member to group: {str(e)}")
            raise ValidationError(f"Failed to add member to group: {str(e)}")
    
    @staticmethod
    def remove_member_from_group(group_id: int, member_type: str, member_id: int):
        """
        Remove a member from a notification group
        """
        try:
            NotificationGroupMember.objects.filter(
                group_id=group_id,
                member_type=member_type,
                member_id=member_id
            ).update(is_active=False)
            
        except Exception as e:
            logger.error(f"Error removing member from group: {str(e)}")
            raise ValidationError(f"Failed to remove member from group: {str(e)}")