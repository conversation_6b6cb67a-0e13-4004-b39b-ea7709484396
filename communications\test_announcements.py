"""
Tests for announcement system
"""
import pytest
from datetime import date, datetime, timedelta
from django.test import TestCase
from django.utils import timezone
from django.core.exceptions import ValidationError

from core.models import School
from accounts.models import User
from .models import (
    Announcement, AnnouncementCategory, AnnouncementTarget,
    AnnouncementView, AnnouncementComment, AnnouncementReaction
)
from .announcement_services import AnnouncementService, AnnouncementCategoryService


@pytest.mark.django_db
class TestAnnouncementModels:
    """Test announcement models"""
    
    @pytest.fixture
    def setup_data(self):
        """Setup test data"""
        # Create school
        school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today() - timedelta(days=365)
        )
        
        # Create user
        user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User"
        )
        
        # Create category
        category = AnnouncementCategory.objects.create(
            school=school,
            name="General",
            code="GENERAL",
            description="General announcements",
            color="#007bff",
            created_by=user
        )
        
        return {
            'school': school,
            'user': user,
            'category': category
        }
    
    def test_announcement_category_creation(self, setup_data):
        """Test announcement category creation"""
        data = setup_data
        
        category = AnnouncementCategory.objects.create(
            school=data['school'],
            name="Academic",
            code="ACADEMIC",
            description="Academic announcements",
            color="#28a745",
            icon="fas fa-graduation-cap",
            created_by=data['user']
        )
        
        assert category.name == "Academic"
        assert category.code == "ACADEMIC"
        assert category.color == "#28a745"
        assert category.is_active
        assert str(category) == "Academic"
    
    def test_announcement_creation(self, setup_data):
        """Test announcement creation"""
        data = setup_data
        
        announcement = Announcement.objects.create(
            school=data['school'],
            title="Test Announcement",
            content="This is a test announcement content.",
            category=data['category'],
            priority="normal",
            status="draft",
            audience="all",
            author=data['user'],
            created_by=data['user']
        )
        
        assert announcement.title == "Test Announcement"
        assert announcement.category == data['category']
        assert announcement.priority == "normal"
        assert announcement.status == "draft"
        assert announcement.audience == "all"
        assert announcement.author == data['user']
        assert not announcement.is_published
        assert not announcement.is_expired
        assert str(announcement) == "Test Announcement"
    
    def test_announcement_properties(self, setup_data):
        """Test announcement properties"""
        data = setup_data
        
        # Test published announcement
        published_announcement = Announcement.objects.create(
            school=data['school'],
            title="Published Announcement",
            content="Published content",
            category=data['category'],
            status="published",
            author=data['user'],
            published_at=timezone.now(),
            created_by=data['user']
        )
        
        assert published_announcement.is_published
        assert not published_announcement.is_expired
        
        # Test expired announcement
        expired_announcement = Announcement.objects.create(
            school=data['school'],
            title="Expired Announcement",
            content="Expired content",
            category=data['category'],
            status="published",
            author=data['user'],
            published_at=timezone.now(),
            expires_at=timezone.now() - timedelta(hours=1),
            created_by=data['user']
        )
        
        assert expired_announcement.is_published
        assert expired_announcement.is_expired
        
        # Test scheduled announcement
        scheduled_announcement = Announcement.objects.create(
            school=data['school'],
            title="Scheduled Announcement",
            content="Scheduled content",
            category=data['category'],
            status="scheduled",
            author=data['user'],
            scheduled_at=timezone.now() + timedelta(hours=1),
            created_by=data['user']
        )
        
        assert not scheduled_announcement.is_published
        assert scheduled_announcement.is_scheduled
    
    def test_announcement_view_tracking(self, setup_data):
        """Test announcement view tracking"""
        data = setup_data
        
        announcement = Announcement.objects.create(
            school=data['school'],
            title="View Test Announcement",
            content="Content for view testing",
            category=data['category'],
            author=data['user'],
            created_by=data['user']
        )
        
        # Create view
        view = AnnouncementView.objects.create(
            school=data['school'],
            announcement=announcement,
            user=data['user'],
            created_by=data['user']
        )
        
        assert view.announcement == announcement
        assert view.user == data['user']
        assert str(view) == f"{data['user'].username} viewed {announcement.title}"
        
        # Test view count increment
        initial_count = announcement.view_count
        announcement.increment_view_count()
        assert announcement.view_count == initial_count + 1
    
    def test_announcement_comment(self, setup_data):
        """Test announcement comments"""
        data = setup_data
        
        announcement = Announcement.objects.create(
            school=data['school'],
            title="Comment Test Announcement",
            content="Content for comment testing",
            category=data['category'],
            author=data['user'],
            created_by=data['user']
        )
        
        # Create comment
        comment = AnnouncementComment.objects.create(
            school=data['school'],
            announcement=announcement,
            user=data['user'],
            content="This is a test comment",
            created_by=data['user']
        )
        
        assert comment.announcement == announcement
        assert comment.user == data['user']
        assert comment.content == "This is a test comment"
        assert comment.is_approved
        assert str(comment) == f"Comment by {data['user'].username} on {announcement.title}"
        
        # Create reply
        reply = AnnouncementComment.objects.create(
            school=data['school'],
            announcement=announcement,
            user=data['user'],
            content="This is a reply",
            parent=comment,
            created_by=data['user']
        )
        
        assert reply.parent == comment
        assert reply in comment.replies.all()
    
    def test_announcement_reaction(self, setup_data):
        """Test announcement reactions"""
        data = setup_data
        
        announcement = Announcement.objects.create(
            school=data['school'],
            title="Reaction Test Announcement",
            content="Content for reaction testing",
            category=data['category'],
            author=data['user'],
            created_by=data['user']
        )
        
        # Create reaction
        reaction = AnnouncementReaction.objects.create(
            school=data['school'],
            announcement=announcement,
            user=data['user'],
            reaction="like",
            created_by=data['user']
        )
        
        assert reaction.announcement == announcement
        assert reaction.user == data['user']
        assert reaction.reaction == "like"
        assert str(reaction) == f"{data['user'].username} like {announcement.title}"


@pytest.mark.django_db
class TestAnnouncementService:
    """Test announcement service"""
    
    @pytest.fixture
    def setup_service_data(self):
        """Setup test data for service tests"""
        # Create school
        school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today() - timedelta(days=365)
        )
        
        # Create user
        user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User"
        )
        
        # Create category
        category = AnnouncementCategoryService.create_category(
            name="Test Category",
            code="TEST_CAT",
            school=school,
            description="Test category for announcements"
        )
        
        return {
            'school': school,
            'user': user,
            'category': category
        }
    
    def test_create_announcement_success(self, setup_service_data):
        """Test successful announcement creation"""
        data = setup_service_data
        
        announcement = AnnouncementService.create_announcement(
            title="Test Service Announcement",
            content="This is a test announcement created via service",
            category_id=data['category'].id,
            author=data['user'],
            school=data['school'],
            priority="high",
            audience="all",
            send_notification=False
        )
        
        assert announcement is not None
        assert announcement.title == "Test Service Announcement"
        assert announcement.category == data['category']
        assert announcement.priority == "high"
        assert announcement.audience == "all"
        assert announcement.author == data['user']
        assert announcement.status == "draft"
    
    def test_create_scheduled_announcement(self, setup_service_data):
        """Test creating scheduled announcement"""
        data = setup_service_data
        
        future_time = timezone.now() + timedelta(hours=2)
        
        announcement = AnnouncementService.create_announcement(
            title="Scheduled Announcement",
            content="This will be published later",
            category_id=data['category'].id,
            author=data['user'],
            school=data['school'],
            scheduled_at=future_time,
            send_notification=False
        )
        
        assert announcement.status == "scheduled"
        assert announcement.scheduled_at == future_time
        assert announcement.is_scheduled
    
    def test_publish_announcement(self, setup_service_data):
        """Test publishing announcement"""
        data = setup_service_data
        
        # Create draft announcement
        announcement = AnnouncementService.create_announcement(
            title="Draft Announcement",
            content="This is a draft",
            category_id=data['category'].id,
            author=data['user'],
            school=data['school'],
            send_notification=False
        )
        
        assert announcement.status == "draft"
        
        # Publish announcement
        success = AnnouncementService.publish_announcement(announcement, data['user'])
        
        assert success
        announcement.refresh_from_db()
        assert announcement.status == "published"
        assert announcement.published_at is not None
        assert announcement.is_published
    
    def test_schedule_announcement(self, setup_service_data):
        """Test scheduling announcement"""
        data = setup_service_data
        
        # Create draft announcement
        announcement = AnnouncementService.create_announcement(
            title="To Be Scheduled",
            content="This will be scheduled",
            category_id=data['category'].id,
            author=data['user'],
            school=data['school'],
            send_notification=False
        )
        
        future_time = timezone.now() + timedelta(hours=3)
        success = AnnouncementService.schedule_announcement(announcement, future_time)
        
        assert success
        announcement.refresh_from_db()
        assert announcement.status == "scheduled"
        assert announcement.scheduled_at == future_time
    
    def test_archive_announcement(self, setup_service_data):
        """Test archiving announcement"""
        data = setup_service_data
        
        # Create published announcement
        announcement = AnnouncementService.create_announcement(
            title="To Be Archived",
            content="This will be archived",
            category_id=data['category'].id,
            author=data['user'],
            school=data['school'],
            send_notification=False
        )
        
        AnnouncementService.publish_announcement(announcement, data['user'])
        
        # Archive announcement
        success = AnnouncementService.archive_announcement(announcement)
        
        assert success
        announcement.refresh_from_db()
        assert announcement.status == "archived"
    
    def test_mark_as_viewed(self, setup_service_data):
        """Test marking announcement as viewed"""
        data = setup_service_data
        
        announcement = AnnouncementService.create_announcement(
            title="View Test",
            content="Test viewing",
            category_id=data['category'].id,
            author=data['user'],
            school=data['school'],
            send_notification=False
        )
        
        initial_count = announcement.view_count
        success = AnnouncementService.mark_announcement_as_viewed(announcement, data['user'])
        
        assert success
        announcement.refresh_from_db()
        assert announcement.view_count == initial_count + 1
        
        # Check view record was created
        view_exists = AnnouncementView.objects.filter(
            announcement=announcement,
            user=data['user']
        ).exists()
        assert view_exists
    
    def test_add_reaction(self, setup_service_data):
        """Test adding reaction to announcement"""
        data = setup_service_data
        
        announcement = AnnouncementService.create_announcement(
            title="Reaction Test",
            content="Test reactions",
            category_id=data['category'].id,
            author=data['user'],
            school=data['school'],
            send_notification=False
        )
        
        success = AnnouncementService.add_reaction(announcement, data['user'], "like")
        
        assert success
        
        # Check reaction was created
        reaction = AnnouncementReaction.objects.filter(
            announcement=announcement,
            user=data['user']
        ).first()
        
        assert reaction is not None
        assert reaction.reaction == "like"
    
    def test_add_comment(self, setup_service_data):
        """Test adding comment to announcement"""
        data = setup_service_data
        
        announcement = AnnouncementService.create_announcement(
            title="Comment Test",
            content="Test comments",
            category_id=data['category'].id,
            author=data['user'],
            school=data['school'],
            send_notification=False
        )
        
        comment = AnnouncementService.add_comment(
            announcement, 
            data['user'], 
            "This is a test comment"
        )
        
        assert comment is not None
        assert comment.content == "This is a test comment"
        assert comment.user == data['user']
        assert comment.announcement == announcement
    
    def test_get_announcement_analytics(self, setup_service_data):
        """Test getting announcement analytics"""
        data = setup_service_data
        
        announcement = AnnouncementService.create_announcement(
            title="Analytics Test",
            content="Test analytics",
            category_id=data['category'].id,
            author=data['user'],
            school=data['school'],
            send_notification=False
        )
        
        # Add some interactions
        AnnouncementService.mark_announcement_as_viewed(announcement, data['user'])
        AnnouncementService.add_reaction(announcement, data['user'], "like")
        AnnouncementService.add_comment(announcement, data['user'], "Test comment")
        
        analytics = AnnouncementService.get_announcement_analytics(announcement)
        
        assert 'total_views' in analytics
        assert 'unique_viewers' in analytics
        assert 'reactions' in analytics
        assert 'comments' in analytics
        assert 'engagement_rate' in analytics
        
        assert analytics['total_views'] >= 1
        assert analytics['unique_viewers'] >= 1
        assert analytics['comments'] >= 1


@pytest.mark.django_db
class TestAnnouncementCategoryService:
    """Test announcement category service"""
    
    @pytest.fixture
    def setup_category_data(self):
        """Setup test data for category tests"""
        # Create school
        school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today() - timedelta(days=365)
        )
        
        return {'school': school}
    
    def test_create_category_success(self, setup_category_data):
        """Test successful category creation"""
        data = setup_category_data
        
        category = AnnouncementCategoryService.create_category(
            name="Academic",
            code="ACADEMIC",
            school=data['school'],
            description="Academic announcements",
            color="#28a745",
            icon="fas fa-graduation-cap"
        )
        
        assert category.name == "Academic"
        assert category.code == "ACADEMIC"
        assert category.description == "Academic announcements"
        assert category.color == "#28a745"
        assert category.icon == "fas fa-graduation-cap"
        assert category.is_active
    
    def test_get_active_categories(self, setup_category_data):
        """Test getting active categories"""
        data = setup_category_data
        
        # Create categories
        cat1 = AnnouncementCategoryService.create_category(
            name="Academic",
            code="ACADEMIC",
            school=data['school']
        )
        
        cat2 = AnnouncementCategoryService.create_category(
            name="Sports",
            code="SPORTS",
            school=data['school']
        )
        
        # Deactivate one category
        cat2.is_active = False
        cat2.save()
        
        active_categories = AnnouncementCategoryService.get_active_categories(data['school'])
        
        assert len(active_categories) == 1
        assert cat1 in active_categories
        assert cat2 not in active_categories


class TestAnnouncementIntegration(TestCase):
    """Integration tests for announcement system"""
    
    def setUp(self):
        """Setup test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today() - timedelta(days=365)
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User"
        )
        
        self.category = AnnouncementCategoryService.create_category(
            name="General",
            code="GENERAL",
            school=self.school,
            description="General announcements"
        )
    
    def test_complete_announcement_workflow(self):
        """Test complete announcement workflow"""
        # Create announcement
        announcement = AnnouncementService.create_announcement(
            title="Complete Workflow Test",
            content="Testing the complete announcement workflow",
            category_id=self.category.id,
            author=self.user,
            school=self.school,
            priority="high",
            audience="all",
            send_notification=False
        )
        
        # Verify initial state
        self.assertEqual(announcement.status, "draft")
        self.assertFalse(announcement.is_published)
        
        # Publish announcement
        success = AnnouncementService.publish_announcement(announcement, self.user)
        self.assertTrue(success)
        
        announcement.refresh_from_db()
        self.assertEqual(announcement.status, "published")
        self.assertTrue(announcement.is_published)
        self.assertIsNotNone(announcement.published_at)
        
        # Add interactions
        AnnouncementService.mark_announcement_as_viewed(announcement, self.user)
        AnnouncementService.add_reaction(announcement, self.user, "like")
        comment = AnnouncementService.add_comment(announcement, self.user, "Great announcement!")
        
        # Verify interactions
        self.assertTrue(announcement.view_count > 0)
        self.assertTrue(announcement.reactions.filter(user=self.user, reaction="like").exists())
        self.assertIsNotNone(comment)
        self.assertEqual(comment.content, "Great announcement!")
        
        # Get analytics
        analytics = AnnouncementService.get_announcement_analytics(announcement)
        self.assertGreater(analytics['total_views'], 0)
        self.assertGreater(analytics['unique_viewers'], 0)
        self.assertGreater(analytics['comments'], 0)
        self.assertIn('like', analytics['reactions'])
        
        # Archive announcement
        success = AnnouncementService.archive_announcement(announcement)
        self.assertTrue(success)
        
        announcement.refresh_from_db()
        self.assertEqual(announcement.status, "archived")
    
    def test_scheduled_announcement_processing(self):
        """Test processing of scheduled announcements"""
        # Create scheduled announcement (in the past to trigger processing)
        past_time = timezone.now() - timedelta(minutes=5)
        
        announcement = AnnouncementService.create_announcement(
            title="Scheduled Test",
            content="This should be auto-published",
            category_id=self.category.id,
            author=self.user,
            school=self.school,
            scheduled_at=past_time,
            send_notification=False
        )
        
        # Initially should be published since scheduled time is in the past
        self.assertEqual(announcement.status, "published")
        self.assertIsNotNone(announcement.published_at)
        
        # Test future scheduled announcement
        future_time = timezone.now() + timedelta(hours=1)
        
        future_announcement = AnnouncementService.create_announcement(
            title="Future Scheduled",
            content="This should remain scheduled",
            category_id=self.category.id,
            author=self.user,
            school=self.school,
            scheduled_at=future_time,
            send_notification=False
        )
        
        self.assertEqual(future_announcement.status, "scheduled")
        self.assertTrue(future_announcement.is_scheduled)
        
        # Process scheduled announcements
        AnnouncementService.process_scheduled_announcements()
        
        # Future announcement should still be scheduled
        future_announcement.refresh_from_db()
        self.assertEqual(future_announcement.status, "scheduled")