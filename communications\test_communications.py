"""
Tests for communications module
"""
import pytest
from decimal import Decimal
from datetime import date, datetime, timedelta
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.utils import timezone
from unittest.mock import patch, Mock

from core.models import School
from accounts.models import User
from .models import (
    NotificationTemplate, NotificationChannel, Notification,
    NotificationGroup, NotificationGroupMember, NotificationLog,
    EmailConfiguration, SMSConfiguration, WhatsAppConfiguration
)
from .services import (
    NotificationService, TemplateService, GroupService,
    EmailService, SMSService, WhatsAppService
)


@pytest.mark.django_db
class TestNotificationModels:
    """Test notification models"""
    
    @pytest.fixture
    def setup_data(self):
        """Setup test data"""
        # Create school
        school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today() - timedelta(days=365)
        )
        
        # Create user
        user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User"
        )
        
        return {
            'school': school,
            'user': user
        }
    
    def test_notification_template_creation(self, setup_data):
        """Test notification template creation"""
        data = setup_data
        
        template = NotificationTemplate.objects.create(
            school=data['school'],
            name="Welcome Email",
            code="WELCOME_EMAIL",
            category="general",
            channel="email",
            subject="Welcome to {{school_name}}",
            body="Hello {{student_name}}, welcome to our school!",
            variables={
                "school_name": "School name",
                "student_name": "Student name"
            },
            created_by=data['user']
        )
        
        assert template.name == "Welcome Email"
        assert template.code == "WELCOME_EMAIL"
        assert template.is_active
        assert not template.is_system
        assert str(template) == "Welcome Email (Email)"
    
    def test_notification_channel_creation(self, setup_data):
        """Test notification channel creation"""
        data = setup_data
        
        channel = NotificationChannel.objects.create(
            school=data['school'],
            name="Default Email",
            channel_type="email",
            configuration={
                "smtp_host": "smtp.gmail.com",
                "smtp_port": 587
            },
            is_default=True,
            created_by=data['user']
        )
        
        assert channel.name == "Default Email"
        assert channel.channel_type == "email"
        assert channel.is_active
        assert channel.is_default
        assert str(channel) == "Default Email (Email)"
    
    def test_notification_creation(self, setup_data):
        """Test notification creation"""
        data = setup_data
        
        # Create template and channel first
        template = NotificationTemplate.objects.create(
            school=data['school'],
            name="Test Template",
            code="TEST_TEMPLATE",
            category="general",
            channel="email",
            subject="Test Subject",
            body="Test message",
            created_by=data['user']
        )
        
        channel = NotificationChannel.objects.create(
            school=data['school'],
            name="Test Channel",
            channel_type="email",
            is_default=True,
            created_by=data['user']
        )
        
        notification = Notification.objects.create(
            school=data['school'],
            template=template,
            channel=channel,
            recipient_type="user",
            recipient_id=data['user'].id,
            recipient_contact="<EMAIL>",
            subject="Test Subject",
            message="Test message",
            priority="normal",
            created_by=data['user']
        )
        
        assert notification.template == template
        assert notification.channel == channel
        assert notification.status == "pending"
        assert notification.priority == "normal"
        assert notification.can_retry
        assert not notification.is_overdue
        assert str(notification) == "Test Subject - <EMAIL> (pending)"
    
    def test_notification_group_creation(self, setup_data):
        """Test notification group creation"""
        data = setup_data
        
        group = NotificationGroup.objects.create(
            school=data['school'],
            name="All Students",
            description="Group for all students",
            group_type="static",
            created_by=data['user']
        )
        
        assert group.name == "All Students"
        assert group.group_type == "static"
        assert group.is_active
        assert str(group) == "All Students"
    
    def test_notification_group_member_creation(self, setup_data):
        """Test notification group member creation"""
        data = setup_data
        
        group = NotificationGroup.objects.create(
            school=data['school'],
            name="Test Group",
            created_by=data['user']
        )
        
        member = NotificationGroupMember.objects.create(
            school=data['school'],
            group=group,
            member_type="user",
            member_id=data['user'].id,
            contact_info={
                "email": "<EMAIL>",
                "phone": "+*********0"
            },
            created_by=data['user']
        )
        
        assert member.group == group
        assert member.member_type == "user"
        assert member.member_id == data['user'].id
        assert member.is_active
        assert str(member) == f"Test Group - user #{data['user'].id}"
    
    def test_email_configuration_creation(self, setup_data):
        """Test email configuration creation"""
        data = setup_data
        
        config = EmailConfiguration.objects.create(
            school=data['school'],
            name="Gmail SMTP",
            smtp_host="smtp.gmail.com",
            smtp_port=587,
            smtp_username="<EMAIL>",
            smtp_password="password",
            use_tls=True,
            from_email="<EMAIL>",
            from_name="Test School",
            is_default=True,
            created_by=data['user']
        )
        
        assert config.name == "Gmail SMTP"
        assert config.smtp_host == "smtp.gmail.com"
        assert config.use_tls
        assert config.is_default
        assert str(config) == "Gmail SMTP"
    
    def test_sms_configuration_creation(self, setup_data):
        """Test SMS configuration creation"""
        data = setup_data
        
        config = SMSConfiguration.objects.create(
            school=data['school'],
            name="Twilio SMS",
            gateway_type="twilio",
            api_key="test_api_key",
            api_secret="test_api_secret",
            sender_id="SCHOOL",
            is_default=True,
            created_by=data['user']
        )
        
        assert config.name == "Twilio SMS"
        assert config.gateway_type == "twilio"
        assert config.sender_id == "SCHOOL"
        assert config.is_default
        assert str(config) == "Twilio SMS (Twilio)"
    
    def test_whatsapp_configuration_creation(self, setup_data):
        """Test WhatsApp configuration creation"""
        data = setup_data
        
        config = WhatsAppConfiguration.objects.create(
            school=data['school'],
            name="WhatsApp Business",
            business_account_id="*********",
            phone_number_id="*********",
            access_token="test_access_token",
            webhook_verify_token="verify_token",
            is_default=True,
            created_by=data['user']
        )
        
        assert config.name == "WhatsApp Business"
        assert config.business_account_id == "*********"
        assert config.is_default
        assert str(config) == "WhatsApp Business"


@pytest.mark.django_db
class TestNotificationService:
    """Test notification service"""
    
    @pytest.fixture
    def setup_service_data(self):
        """Setup test data for service tests"""
        # Create school
        school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today() - timedelta(days=365)
        )
        
        # Create user
        user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User"
        )
        
        # Create template
        template = NotificationTemplate.objects.create(
            school=school,
            name="Test Template",
            code="TEST_TEMPLATE",
            category="general",
            channel="email",
            subject="Hello {{name}}",
            body="Welcome {{name}} to {{school_name}}!",
            variables={
                "name": "Recipient name",
                "school_name": "School name"
            },
            created_by=user
        )
        
        # Create channel
        channel = NotificationChannel.objects.create(
            school=school,
            name="Test Email Channel",
            channel_type="email",
            is_default=True,
            created_by=user
        )
        
        return {
            'school': school,
            'user': user,
            'template': template,
            'channel': channel
        }
    
    def test_create_notification_success(self, setup_service_data):
        """Test successful notification creation"""
        data = setup_service_data
        
        notification = NotificationService.create_notification(
            template_code="TEST_TEMPLATE",
            recipient_contact="<EMAIL>",
            variables={
                "name": "John Doe",
                "school_name": "Test School"
            },
            recipient_type="user",
            priority="normal"
        )
        
        assert notification is not None
        assert notification.template.code == "TEST_TEMPLATE"
        assert notification.recipient_contact == "<EMAIL>"
        assert notification.subject == "Hello John Doe"
        assert notification.message == "Welcome John Doe to Test School!"
        assert notification.status == "pending"
    
    def test_create_notification_invalid_template(self, setup_service_data):
        """Test notification creation with invalid template"""
        with pytest.raises(ValidationError):
            NotificationService.create_notification(
                template_code="INVALID_TEMPLATE",
                recipient_contact="<EMAIL>"
            )
    
    def test_send_bulk_notification(self, setup_service_data):
        """Test sending bulk notifications"""
        data = setup_service_data
        
        recipients = [
            {
                "contact": "<EMAIL>",
                "type": "user",
                "id": 1,
                "variables": {"name": "User One"}
            },
            {
                "contact": "<EMAIL>",
                "type": "user",
                "id": 2,
                "variables": {"name": "User Two"}
            }
        ]
        
        notifications = NotificationService.send_bulk_notification(
            template_code="TEST_TEMPLATE",
            recipients=recipients,
            variables={"school_name": "Test School"}
        )
        
        assert len(notifications) == 2
        assert notifications[0].recipient_contact == "<EMAIL>"
        assert notifications[1].recipient_contact == "<EMAIL>"
        assert "User One" in notifications[0].message
        assert "User Two" in notifications[1].message
    
    def test_template_rendering(self, setup_service_data):
        """Test template rendering with variables"""
        variables = {
            "name": "John Doe",
            "school_name": "Test School"
        }
        
        subject = NotificationService._render_template("Hello {{name}}", variables)
        message = NotificationService._render_template("Welcome {{name}} to {{school_name}}!", variables)
        
        assert subject == "Hello John Doe"
        assert message == "Welcome John Doe to Test School!"
    
    def test_retry_failed_notification(self, setup_service_data):
        """Test retrying failed notification"""
        data = setup_service_data
        
        # Create a failed notification
        notification = Notification.objects.create(
            school=data['school'],
            template=data['template'],
            channel=data['channel'],
            recipient_type="user",
            recipient_contact="<EMAIL>",
            subject="Test Subject",
            message="Test message",
            status="failed",
            error_message="Test error",
            created_by=data['user']
        )
        
        # Mock the send method to succeed on retry
        with patch.object(NotificationService, 'send_notification', return_value=True):
            success = NotificationService.retry_failed_notification(notification)
            
            assert success
            assert notification.retry_count == 1
            assert notification.status == "pending"
            assert notification.error_message is None


@pytest.mark.django_db
class TestTemplateService:
    """Test template service"""
    
    @pytest.fixture
    def setup_template_data(self):
        """Setup test data for template tests"""
        # Create school
        school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today() - timedelta(days=365)
        )
        
        # Create user
        user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User"
        )
        
        return {
            'school': school,
            'user': user
        }
    
    def test_create_template_success(self, setup_template_data):
        """Test successful template creation"""
        data = setup_template_data
        
        template = TemplateService.create_template(
            name="Test Template",
            code="TEST_TEMPLATE",
            category="general",
            channel="email",
            subject="Test Subject {{name}}",
            body="Hello {{name}}, welcome to {{school_name}}!",
            variables={
                "name": "Recipient name",
                "school_name": "School name"
            },
            school=data['school']
        )
        
        assert template.name == "Test Template"
        assert template.code == "TEST_TEMPLATE"
        assert template.category == "general"
        assert template.channel == "email"
        assert template.is_active
    
    def test_validate_template_success(self, setup_template_data):
        """Test successful template validation"""
        variables = {
            "name": "John Doe",
            "school_name": "Test School"
        }
        
        is_valid = TemplateService.validate_template(
            subject="Hello {{name}}",
            body="Welcome {{name}} to {{school_name}}!",
            variables=variables
        )
        
        assert is_valid
    
    def test_validate_template_invalid_syntax(self, setup_template_data):
        """Test template validation with invalid syntax"""
        variables = {
            "name": "John Doe"
        }
        
        # Missing closing brace
        is_valid = TemplateService.validate_template(
            subject="Hello {{name}",
            body="Welcome {{name}} to {{school_name}}!",
            variables=variables
        )
        
        assert not is_valid
    
    def test_get_available_variables(self, setup_template_data):
        """Test getting available variables for template"""
        data = setup_template_data
        
        # Create template
        template = NotificationTemplate.objects.create(
            school=data['school'],
            name="Test Template",
            code="TEST_TEMPLATE",
            category="general",
            channel="email",
            subject="Test Subject",
            body="Test body",
            variables={
                "name": "Recipient name",
                "school_name": "School name"
            },
            created_by=data['user']
        )
        
        variables = TemplateService.get_available_variables("TEST_TEMPLATE")
        
        assert "name" in variables
        assert "school_name" in variables
        assert variables["name"] == "Recipient name"


@pytest.mark.django_db
class TestGroupService:
    """Test group service"""
    
    @pytest.fixture
    def setup_group_data(self):
        """Setup test data for group tests"""
        # Create school
        school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today() - timedelta(days=365)
        )
        
        # Create user
        user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User"
        )
        
        return {
            'school': school,
            'user': user
        }
    
    def test_create_group_success(self, setup_group_data):
        """Test successful group creation"""
        data = setup_group_data
        
        group = GroupService.create_group(
            name="Test Group",
            description="A test group",
            school=data['school']
        )
        
        assert group.name == "Test Group"
        assert group.description == "A test group"
        assert group.is_active
    
    def test_add_member_to_group(self, setup_group_data):
        """Test adding member to group"""
        data = setup_group_data
        
        # Create group
        group = GroupService.create_group(
            name="Test Group",
            school=data['school']
        )
        
        # Add member
        member = GroupService.add_member_to_group(
            group_id=group.id,
            member_type="user",
            member_id=data['user'].id,
            contact_info={
                "email": "<EMAIL>",
                "phone": "+*********0"
            }
        )
        
        assert member.group == group
        assert member.member_type == "user"
        assert member.member_id == data['user'].id
        assert member.contact_info["email"] == "<EMAIL>"
        assert member.is_active
    
    def test_remove_member_from_group(self, setup_group_data):
        """Test removing member from group"""
        data = setup_group_data
        
        # Create group and add member
        group = GroupService.create_group(
            name="Test Group",
            school=data['school']
        )
        
        member = GroupService.add_member_to_group(
            group_id=group.id,
            member_type="user",
            member_id=data['user'].id,
            contact_info={"email": "<EMAIL>"}
        )
        
        # Remove member
        GroupService.remove_member_from_group(
            group_id=group.id,
            member_type="user",
            member_id=data['user'].id
        )
        
        # Refresh member from database
        member.refresh_from_db()
        assert not member.is_active


@pytest.mark.django_db
class TestEmailService:
    """Test email service"""
    
    @pytest.fixture
    def setup_email_data(self):
        """Setup test data for email tests"""
        # Create school
        school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today() - timedelta(days=365)
        )
        
        # Create user
        user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User"
        )
        
        # Create email configuration
        email_config = EmailConfiguration.objects.create(
            school=school,
            name="Test Email Config",
            smtp_host="smtp.gmail.com",
            smtp_port=587,
            smtp_username="<EMAIL>",
            smtp_password="password",
            use_tls=True,
            from_email="<EMAIL>",
            from_name="Test School",
            is_default=True,
            created_by=user
        )
        
        # Create template and channel
        template = NotificationTemplate.objects.create(
            school=school,
            name="Email Template",
            code="EMAIL_TEMPLATE",
            category="general",
            channel="email",
            subject="Test Email",
            body="This is a test email.",
            created_by=user
        )
        
        channel = NotificationChannel.objects.create(
            school=school,
            name="Email Channel",
            channel_type="email",
            is_default=True,
            created_by=user
        )
        
        return {
            'school': school,
            'user': user,
            'email_config': email_config,
            'template': template,
            'channel': channel
        }
    
    @patch('communications.services.EmailMultiAlternatives')
    def test_send_email_notification_success(self, mock_email, setup_email_data):
        """Test successful email sending"""
        data = setup_email_data
        
        # Create notification
        notification = Notification.objects.create(
            school=data['school'],
            template=data['template'],
            channel=data['channel'],
            recipient_type="user",
            recipient_contact="<EMAIL>",
            subject="Test Email",
            message="This is a test email.",
            created_by=data['user']
        )
        
        # Mock email sending
        mock_email_instance = Mock()
        mock_email.return_value = mock_email_instance
        mock_email_instance.send.return_value = True
        
        # Send email
        success = EmailService.send_email_notification(notification)
        
        assert success
        assert notification.status == "sent"
        assert notification.sent_at is not None
        mock_email.assert_called_once()
        mock_email_instance.send.assert_called_once()
    
    def test_send_email_notification_no_config(self, setup_email_data):
        """Test email sending without configuration"""
        data = setup_email_data
        
        # Delete email configuration
        EmailConfiguration.objects.all().delete()
        
        # Create notification
        notification = Notification.objects.create(
            school=data['school'],
            template=data['template'],
            channel=data['channel'],
            recipient_type="user",
            recipient_contact="<EMAIL>",
            subject="Test Email",
            message="This is a test email.",
            created_by=data['user']
        )
        
        # Try to send email
        success = EmailService.send_email_notification(notification)
        
        assert not success
        assert notification.status == "failed"
        assert "No active email configuration found" in notification.error_message


class TestCommunicationsIntegration(TestCase):
    """Integration tests for communications system"""
    
    def setUp(self):
        """Setup test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today() - timedelta(days=365)
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User"
        )
    
    def test_complete_notification_workflow(self):
        """Test complete notification workflow from template to delivery"""
        # Create email configuration
        email_config = EmailConfiguration.objects.create(
            school=self.school,
            name="Test Config",
            smtp_host="smtp.gmail.com",
            smtp_port=587,
            smtp_username="<EMAIL>",
            smtp_password="password",
            from_email="<EMAIL>",
            from_name="Test School",
            is_default=True,
            created_by=self.user
        )
        
        # Create template
        template = TemplateService.create_template(
            name="Welcome Email",
            code="WELCOME_EMAIL",
            category="general",
            channel="email",
            subject="Welcome {{name}} to {{school_name}}",
            body="Hello {{name}}, welcome to {{school_name}}! We're excited to have you.",
            variables={
                "name": "Student name",
                "school_name": "School name"
            },
            school=self.school
        )
        
        # Create channel
        channel = NotificationChannel.objects.create(
            school=self.school,
            name="Default Email",
            channel_type="email",
            is_default=True,
            created_by=self.user
        )
        
        # Create notification
        notification = NotificationService.create_notification(
            template_code="WELCOME_EMAIL",
            recipient_contact="<EMAIL>",
            variables={
                "name": "John Doe",
                "school_name": "Test School"
            },
            recipient_type="student",
            priority="normal",
            school=self.school
        )
        
        # Verify notification was created correctly
        self.assertEqual(notification.template, template)
        self.assertEqual(notification.channel, channel)
        self.assertEqual(notification.subject, "Welcome John Doe to Test School")
        self.assertIn("Hello John Doe", notification.message)
        self.assertEqual(notification.status, "pending")
        
        # Verify logs were created
        logs = NotificationLog.objects.filter(notification=notification)
        self.assertTrue(logs.exists())
        self.assertEqual(logs.first().action, "created")
    
    def test_group_notification_workflow(self):
        """Test group notification workflow"""
        # Create template and channel
        template = TemplateService.create_template(
            name="Group Announcement",
            code="GROUP_ANNOUNCEMENT",
            category="general",
            channel="email",
            subject="Important Announcement",
            body="Dear {{name}}, we have an important announcement for you.",
            variables={"name": "Recipient name"},
            school=self.school
        )
        
        channel = NotificationChannel.objects.create(
            school=self.school,
            name="Email Channel",
            channel_type="email",
            is_default=True,
            created_by=self.user
        )
        
        # Create group
        group = GroupService.create_group(
            name="All Students",
            description="All students group",
            school=self.school
        )
        
        # Add members to group
        GroupService.add_member_to_group(
            group_id=group.id,
            member_type="student",
            member_id=1,
            contact_info={
                "email": "<EMAIL>",
                "name": "Student One"
            }
        )
        
        GroupService.add_member_to_group(
            group_id=group.id,
            member_type="student",
            member_id=2,
            contact_info={
                "email": "<EMAIL>",
                "name": "Student Two"
            }
        )
        
        # Send group notification
        notifications = NotificationService.send_group_notification(
            group_id=group.id,
            template_code="GROUP_ANNOUNCEMENT",
            variables={"announcement": "School will be closed tomorrow"}
        )
        
        # Verify notifications were created for all group members
        self.assertEqual(len(notifications), 2)
        self.assertEqual(notifications[0].recipient_contact, "<EMAIL>")
        self.assertEqual(notifications[1].recipient_contact, "<EMAIL>")
        
        # Verify all notifications have correct content
        for notification in notifications:
            self.assertEqual(notification.template, template)
            self.assertEqual(notification.subject, "Important Announcement")
            self.assertIn("Dear", notification.message)
    
    def test_scheduled_notification_processing(self):
        """Test processing of scheduled notifications"""
        # Create template and channel
        template = TemplateService.create_template(
            name="Scheduled Message",
            code="SCHEDULED_MESSAGE",
            category="general",
            channel="email",
            subject="Scheduled Message",
            body="This is a scheduled message.",
            school=self.school
        )
        
        channel = NotificationChannel.objects.create(
            school=self.school,
            name="Email Channel",
            channel_type="email",
            is_default=True,
            created_by=self.user
        )
        
        # Create scheduled notification (in the past to trigger processing)
        past_time = timezone.now() - timedelta(minutes=5)
        notification = NotificationService.create_notification(
            template_code="SCHEDULED_MESSAGE",
            recipient_contact="<EMAIL>",
            scheduled_at=past_time,
            school=self.school
        )
        
        # Verify notification is pending and scheduled
        self.assertEqual(notification.status, "pending")
        self.assertTrue(notification.is_overdue)
        
        # Mock the send method and process scheduled notifications
        with patch.object(NotificationService, 'send_notification', return_value=True) as mock_send:
            NotificationService.process_scheduled_notifications()
            
            # Verify send was called
            mock_send.assert_called_once_with(notification)