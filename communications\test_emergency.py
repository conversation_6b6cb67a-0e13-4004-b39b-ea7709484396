"""
Tests for emergency notification system
"""
import pytest
from datetime import date, datetime, timedelta
from django.test import TestCase
from django.utils import timezone
from django.core.exceptions import ValidationError

from core.models import School
from accounts.models import User
from .models import (
    EmergencyAlert, EmergencyContact, EmergencyAlertRecipient,
    EmergencyProcedure, EmergencyDrill
)
from .emergency_services import EmergencyNotificationService, EmergencyProcedureService


@pytest.mark.django_db
class TestEmergencyModels:
    """Test emergency notification models"""
    
    @pytest.fixture
    def setup_data(self):
        """Setup test data"""
        # Create school
        school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today() - timedelta(days=365)
        )
        
        # Create user
        user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User"
        )
        
        return {
            'school': school,
            'user': user
        }
    
    def test_emergency_alert_creation(self, setup_data):
        """Test emergency alert creation"""
        data = setup_data
        
        alert = EmergencyAlert.objects.create(
            school=data['school'],
            title="Fire Emergency",
            message="Fire detected in building A. Please evacuate immediately.",
            alert_type="fire",
            severity="critical",
            issued_by=data['user'],
            affected_areas=["Building A", "Parking Lot"],
            instructions="Exit through nearest emergency exit",
            contact_info={"emergency": "911", "security": "555-0123"},
            acknowledgment_required=True,
            created_by=data['user']
        )
        
        assert alert.title == "Fire Emergency"
        assert alert.alert_type == "fire"
        assert alert.severity == "critical"
        assert alert.status == "active"
        assert alert.is_active
        assert alert.is_critical
        assert alert.acknowledgment_required
        assert "Building A" in alert.affected_areas
        assert str(alert) == "Fire Emergency: Fire Emergency"
    
    def test_emergency_alert_properties(self, setup_data):
        """Test emergency alert properties"""
        data = setup_data
        
        # Create active alert
        active_alert = EmergencyAlert.objects.create(
            school=data['school'],
            title="Active Alert",
            message="This is active",
            alert_type="security",
            severity="high",
            issued_by=data['user'],
            created_by=data['user']
        )
        
        assert active_alert.is_active
        assert active_alert.duration_minutes >= 0
        
        # Resolve alert
        active_alert.resolve(data['user'])
        
        assert not active_alert.is_active
        assert active_alert.status == "resolved"
        assert active_alert.resolved_by == data['user']
        assert active_alert.resolved_at is not None
        
        # Test auto-resolve
        auto_resolve_alert = EmergencyAlert.objects.create(
            school=data['school'],
            title="Auto Resolve Alert",
            message="This will auto resolve",
            alert_type="weather",
            severity="medium",
            issued_by=data['user'],
            auto_resolve_at=timezone.now() + timedelta(minutes=30),
            created_by=data['user']
        )
        
        assert auto_resolve_alert.auto_resolve_at is not None
    
    def test_emergency_contact_creation(self, setup_data):
        """Test emergency contact creation"""
        data = setup_data
        
        contact = EmergencyContact.objects.create(
            school=data['school'],
            name="Fire Department",
            contact_type="fire",
            phone_primary="911",
            phone_secondary="555-FIRE",
            email="<EMAIL>",
            address="123 Fire Station Rd",
            notes="24/7 emergency response",
            priority_order=1,
            created_by=data['user']
        )
        
        assert contact.name == "Fire Department"
        assert contact.contact_type == "fire"
        assert contact.phone_primary == "911"
        assert contact.is_active
        assert contact.priority_order == 1
        assert str(contact) == "Fire Department (Fire Department)"
    
    def test_emergency_alert_recipient(self, setup_data):
        """Test emergency alert recipient tracking"""
        data = setup_data
        
        alert = EmergencyAlert.objects.create(
            school=data['school'],
            title="Test Alert",
            message="Test message",
            alert_type="general",
            severity="low",
            issued_by=data['user'],
            created_by=data['user']
        )
        
        recipient = EmergencyAlertRecipient.objects.create(
            school=data['school'],
            alert=alert,
            recipient_type="user",
            recipient_id=data['user'].id,
            contact_method="email",
            contact_address="<EMAIL>",
            sent_at=timezone.now(),
            status="sent",
            created_by=data['user']
        )
        
        assert recipient.alert == alert
        assert recipient.recipient_type == "user"
        assert recipient.contact_method == "email"
        assert recipient.status == "sent"
        assert str(recipient) == f"Test Alert - user #{data['user'].id}"
    
    def test_emergency_procedure_creation(self, setup_data):
        """Test emergency procedure creation"""
        data = setup_data
        
        procedure = EmergencyProcedure.objects.create(
            school=data['school'],
            title="Fire Evacuation Procedure",
            procedure_type="fire",
            description="Standard fire evacuation procedure",
            steps=[
                "Sound fire alarm",
                "Evacuate building via nearest exit",
                "Assemble at designated meeting point",
                "Take attendance"
            ],
            responsible_roles=["Fire Warden", "Teachers", "Security"],
            required_resources=["Fire alarm system", "Emergency exits"],
            estimated_duration=15,
            last_reviewed=date.today(),
            created_by=data['user']
        )
        
        assert procedure.title == "Fire Evacuation Procedure"
        assert procedure.procedure_type == "fire"
        assert len(procedure.steps) == 4
        assert "Fire Warden" in procedure.responsible_roles
        assert procedure.estimated_duration == 15
        assert not procedure.is_due_for_review  # Just reviewed today
        assert str(procedure) == "Fire Emergency: Fire Evacuation Procedure"
    
    def test_emergency_drill_creation(self, setup_data):
        """Test emergency drill creation"""
        data = setup_data
        
        # Create procedure first
        procedure = EmergencyProcedure.objects.create(
            school=data['school'],
            title="Fire Drill Procedure",
            procedure_type="fire",
            description="Fire drill procedure",
            steps=["Sound alarm", "Evacuate"],
            created_by=data['user']
        )
        
        drill = EmergencyDrill.objects.create(
            school=data['school'],
            title="Monthly Fire Drill",
            drill_type="fire",
            procedure=procedure,
            scheduled_date=timezone.now() + timedelta(days=7),
            participants_expected=100,
            coordinator=data['user'],
            created_by=data['user']
        )
        
        assert drill.title == "Monthly Fire Drill"
        assert drill.drill_type == "fire"
        assert drill.procedure == procedure
        assert drill.status == "scheduled"
        assert drill.participants_expected == 100
        assert drill.coordinator == data['user']
        
        # Test drill completion
        drill.status = "in_progress"
        drill.actual_start_time = timezone.now()
        drill.save()
        
        drill.status = "completed"
        drill.actual_end_time = timezone.now() + timedelta(minutes=10)
        drill.participants_actual = 95
        drill.evaluation_score = 8
        drill.save()
        
        assert drill.duration_minutes == 10
        assert drill.participation_rate == 95.0


@pytest.mark.django_db
class TestEmergencyNotificationService:
    """Test emergency notification service"""
    
    @pytest.fixture
    def setup_service_data(self):
        """Setup test data for service tests"""
        # Create school
        school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today() - timedelta(days=365)
        )
        
        # Create user
        user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User"
        )
        
        return {
            'school': school,
            'user': user
        }
    
    def test_create_emergency_alert_success(self, setup_service_data):
        """Test successful emergency alert creation"""
        data = setup_service_data
        
        alert = EmergencyNotificationService.create_emergency_alert(
            title="Test Emergency",
            message="This is a test emergency alert",
            alert_type="security",
            severity="high",
            issued_by=data['user'],
            school=data['school'],
            affected_areas=["Main Building"],
            instructions="Stay calm and follow instructions",
            acknowledgment_required=True
        )
        
        assert alert is not None
        assert alert.title == "Test Emergency"
        assert alert.alert_type == "security"
        assert alert.severity == "high"
        assert alert.status == "active"
        assert alert.issued_by == data['user']
        assert "Main Building" in alert.affected_areas
        assert alert.acknowledgment_required
        assert alert.notification_sent
    
    def test_create_emergency_alert_with_auto_resolve(self, setup_service_data):
        """Test creating emergency alert with auto-resolve"""
        data = setup_service_data
        
        alert = EmergencyNotificationService.create_emergency_alert(
            title="Auto Resolve Test",
            message="This will auto resolve",
            alert_type="weather",
            severity="medium",
            issued_by=data['user'],
            school=data['school'],
            auto_resolve_minutes=30
        )
        
        assert alert.auto_resolve_at is not None
        expected_time = timezone.now() + timedelta(minutes=30)
        # Allow 1 minute tolerance for test execution time
        assert abs((alert.auto_resolve_at - expected_time).total_seconds()) < 60
    
    def test_resolve_emergency_alert(self, setup_service_data):
        """Test resolving emergency alert"""
        data = setup_service_data
        
        # Create alert
        alert = EmergencyNotificationService.create_emergency_alert(
            title="To Be Resolved",
            message="This will be resolved",
            alert_type="medical",
            severity="high",
            issued_by=data['user'],
            school=data['school']
        )
        
        assert alert.status == "active"
        
        # Resolve alert
        success = EmergencyNotificationService.resolve_emergency_alert(alert, data['user'])
        
        assert success
        alert.refresh_from_db()
        assert alert.status == "resolved"
        assert alert.resolved_by == data['user']
        assert alert.resolved_at is not None
    
    def test_cancel_emergency_alert(self, setup_service_data):
        """Test cancelling emergency alert"""
        data = setup_service_data
        
        # Create alert
        alert = EmergencyNotificationService.create_emergency_alert(
            title="To Be Cancelled",
            message="This will be cancelled",
            alert_type="general",
            severity="low",
            issued_by=data['user'],
            school=data['school']
        )
        
        assert alert.status == "active"
        
        # Cancel alert
        success = EmergencyNotificationService.cancel_emergency_alert(alert, data['user'])
        
        assert success
        alert.refresh_from_db()
        assert alert.status == "cancelled"
        assert alert.resolved_by == data['user']
        assert alert.resolved_at is not None
    
    def test_acknowledge_alert(self, setup_service_data):
        """Test acknowledging emergency alert"""
        data = setup_service_data
        
        # Create alert
        alert = EmergencyNotificationService.create_emergency_alert(
            title="Acknowledgment Test",
            message="Please acknowledge",
            alert_type="security",
            severity="high",
            issued_by=data['user'],
            school=data['school'],
            acknowledgment_required=True
        )
        
        # Create recipient record
        recipient = EmergencyAlertRecipient.objects.create(
            school=data['school'],
            alert=alert,
            recipient_type="user",
            recipient_id=data['user'].id,
            contact_method="email",
            contact_address="<EMAIL>",
            status="delivered",
            created_by=data['user']
        )
        
        # Acknowledge alert
        success = EmergencyNotificationService.acknowledge_alert(alert, data['user'])
        
        assert success
        recipient.refresh_from_db()
        assert recipient.status == "acknowledged"
        assert recipient.acknowledged_at is not None
    
    def test_get_active_alerts(self, setup_service_data):
        """Test getting active alerts"""
        data = setup_service_data
        
        # Create multiple alerts
        alert1 = EmergencyNotificationService.create_emergency_alert(
            title="Critical Alert",
            message="Critical emergency",
            alert_type="fire",
            severity="critical",
            issued_by=data['user'],
            school=data['school']
        )
        
        alert2 = EmergencyNotificationService.create_emergency_alert(
            title="High Alert",
            message="High priority emergency",
            alert_type="security",
            severity="high",
            issued_by=data['user'],
            school=data['school']
        )
        
        # Resolve one alert
        EmergencyNotificationService.resolve_emergency_alert(alert2, data['user'])
        
        # Get active alerts
        active_alerts = EmergencyNotificationService.get_active_alerts(data['school'])
        
        assert len(active_alerts) == 1
        assert alert1 in active_alerts
        assert alert2 not in active_alerts
    
    def test_get_alert_statistics(self, setup_service_data):
        """Test getting alert statistics"""
        data = setup_service_data
        
        # Create alert
        alert = EmergencyNotificationService.create_emergency_alert(
            title="Statistics Test",
            message="Test statistics",
            alert_type="general",
            severity="medium",
            issued_by=data['user'],
            school=data['school']
        )
        
        # Create recipient records
        EmergencyAlertRecipient.objects.create(
            school=data['school'],
            alert=alert,
            recipient_type="user",
            recipient_id=1,
            contact_method="email",
            contact_address="<EMAIL>",
            status="delivered",
            created_by=data['user']
        )
        
        EmergencyAlertRecipient.objects.create(
            school=data['school'],
            alert=alert,
            recipient_type="user",
            recipient_id=2,
            contact_method="email",
            contact_address="<EMAIL>",
            status="acknowledged",
            created_by=data['user']
        )
        
        EmergencyAlertRecipient.objects.create(
            school=data['school'],
            alert=alert,
            recipient_type="user",
            recipient_id=3,
            contact_method="email",
            contact_address="<EMAIL>",
            status="failed",
            created_by=data['user']
        )
        
        # Get statistics
        stats = EmergencyNotificationService.get_alert_statistics(alert)
        
        assert stats['total_recipients'] == 3
        assert stats['delivered_count'] == 2
        assert stats['acknowledged_count'] == 1
        assert stats['failed_count'] == 1
        assert stats['delivery_rate'] > 0
        assert stats['acknowledgment_rate'] is not None


@pytest.mark.django_db
class TestEmergencyProcedureService:
    """Test emergency procedure service"""
    
    @pytest.fixture
    def setup_procedure_data(self):
        """Setup test data for procedure tests"""
        # Create school
        school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today() - timedelta(days=365)
        )
        
        # Create user
        user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User"
        )
        
        return {
            'school': school,
            'user': user
        }
    
    def test_create_procedure_success(self, setup_procedure_data):
        """Test successful procedure creation"""
        data = setup_procedure_data
        
        procedure = EmergencyProcedureService.create_procedure(
            title="Lockdown Procedure",
            procedure_type="lockdown",
            description="Standard lockdown procedure",
            steps=[
                "Lock all doors",
                "Turn off lights",
                "Move away from windows",
                "Remain quiet"
            ],
            school=data['school'],
            responsible_roles=["Teachers", "Security"],
            required_resources=["Door locks", "Communication system"],
            estimated_duration=20
        )
        
        assert procedure is not None
        assert procedure.title == "Lockdown Procedure"
        assert procedure.procedure_type == "lockdown"
        assert len(procedure.steps) == 4
        assert "Teachers" in procedure.responsible_roles
        assert procedure.estimated_duration == 20
        assert procedure.last_reviewed == date.today()
    
    def test_schedule_drill(self, setup_procedure_data):
        """Test scheduling emergency drill"""
        data = setup_procedure_data
        
        # Create procedure
        procedure = EmergencyProcedureService.create_procedure(
            title="Fire Drill Procedure",
            procedure_type="fire",
            description="Fire drill procedure",
            steps=["Sound alarm", "Evacuate"],
            school=data['school']
        )
        
        # Schedule drill
        scheduled_date = timezone.now() + timedelta(days=14)
        drill = EmergencyProcedureService.schedule_drill(
            procedure=procedure,
            scheduled_date=scheduled_date,
            coordinator=data['user'],
            participants_expected=150,
            title="Quarterly Fire Drill"
        )
        
        assert drill is not None
        assert drill.title == "Quarterly Fire Drill"
        assert drill.drill_type == "fire"
        assert drill.procedure == procedure
        assert drill.scheduled_date == scheduled_date
        assert drill.participants_expected == 150
        assert drill.coordinator == data['user']
        assert drill.status == "scheduled"
    
    def test_start_drill(self, setup_procedure_data):
        """Test starting emergency drill"""
        data = setup_procedure_data
        
        # Create procedure and drill
        procedure = EmergencyProcedureService.create_procedure(
            title="Evacuation Procedure",
            procedure_type="evacuation",
            description="Evacuation procedure",
            steps=["Exit building"],
            school=data['school']
        )
        
        drill = EmergencyProcedureService.schedule_drill(
            procedure=procedure,
            scheduled_date=timezone.now(),
            coordinator=data['user'],
            participants_expected=100
        )
        
        # Start drill
        success = EmergencyProcedureService.start_drill(drill)
        
        assert success
        drill.refresh_from_db()
        assert drill.status == "in_progress"
        assert drill.actual_start_time is not None
    
    def test_complete_drill(self, setup_procedure_data):
        """Test completing emergency drill"""
        data = setup_procedure_data
        
        # Create procedure and drill
        procedure = EmergencyProcedureService.create_procedure(
            title="Shelter Procedure",
            procedure_type="shelter",
            description="Shelter in place procedure",
            steps=["Stay inside"],
            school=data['school']
        )
        
        drill = EmergencyProcedureService.schedule_drill(
            procedure=procedure,
            scheduled_date=timezone.now(),
            coordinator=data['user'],
            participants_expected=80
        )
        
        # Start and complete drill
        EmergencyProcedureService.start_drill(drill)
        
        success = EmergencyProcedureService.complete_drill(
            drill=drill,
            participants_actual=75,
            evaluation_score=9,
            notes="Excellent drill execution",
            areas_for_improvement="Faster response time needed"
        )
        
        assert success
        drill.refresh_from_db()
        assert drill.status == "completed"
        assert drill.actual_end_time is not None
        assert drill.participants_actual == 75
        assert drill.evaluation_score == 9
        assert drill.notes == "Excellent drill execution"
        assert drill.participation_rate == 93.75  # 75/80 * 100


class TestEmergencyIntegration(TestCase):
    """Integration tests for emergency system"""
    
    def setUp(self):
        """Setup test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today() - timedelta(days=365)
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User"
        )
    
    def test_complete_emergency_workflow(self):
        """Test complete emergency workflow"""
        # Create emergency alert
        alert = EmergencyNotificationService.create_emergency_alert(
            title="Complete Workflow Test",
            message="Testing complete emergency workflow",
            alert_type="fire",
            severity="critical",
            issued_by=self.user,
            school=self.school,
            affected_areas=["Building A", "Building B"],
            instructions="Evacuate immediately via nearest exit",
            acknowledgment_required=True
        )
        
        # Verify alert creation
        self.assertEqual(alert.status, "active")
        self.assertTrue(alert.is_active)
        self.assertTrue(alert.is_critical)
        self.assertTrue(alert.notification_sent)
        
        # Create recipient record
        recipient = EmergencyAlertRecipient.objects.create(
            school=self.school,
            alert=alert,
            recipient_type="user",
            recipient_id=self.user.id,
            contact_method="email",
            contact_address="<EMAIL>",
            status="delivered",
            created_by=self.user
        )
        
        # Acknowledge alert
        success = EmergencyNotificationService.acknowledge_alert(alert, self.user)
        self.assertTrue(success)
        
        recipient.refresh_from_db()
        self.assertEqual(recipient.status, "acknowledged")
        
        # Get statistics
        stats = EmergencyNotificationService.get_alert_statistics(alert)
        self.assertEqual(stats['total_recipients'], 1)
        self.assertEqual(stats['acknowledged_count'], 1)
        self.assertEqual(stats['acknowledgment_rate'], 100.0)
        
        # Resolve alert
        success = EmergencyNotificationService.resolve_emergency_alert(alert, self.user)
        self.assertTrue(success)
        
        alert.refresh_from_db()
        self.assertEqual(alert.status, "resolved")
        self.assertEqual(alert.resolved_by, self.user)
        self.assertIsNotNone(alert.resolved_at)
    
    def test_emergency_procedure_and_drill_workflow(self):
        """Test emergency procedure and drill workflow"""
        # Create emergency procedure
        procedure = EmergencyProcedureService.create_procedure(
            title="Fire Evacuation Procedure",
            procedure_type="fire",
            description="Complete fire evacuation procedure",
            steps=[
                "Sound fire alarm",
                "Announce evacuation",
                "Guide people to exits",
                "Assemble at meeting point",
                "Take attendance"
            ],
            school=self.school,
            responsible_roles=["Fire Warden", "Teachers"],
            required_resources=["Fire alarm", "Emergency exits"],
            estimated_duration=10
        )
        
        # Verify procedure creation
        self.assertEqual(procedure.title, "Fire Evacuation Procedure")
        self.assertEqual(len(procedure.steps), 5)
        self.assertFalse(procedure.is_due_for_review)
        
        # Schedule drill
        scheduled_date = timezone.now() + timedelta(days=7)
        drill = EmergencyProcedureService.schedule_drill(
            procedure=procedure,
            scheduled_date=scheduled_date,
            coordinator=self.user,
            participants_expected=200,
            title="Monthly Fire Drill"
        )
        
        # Verify drill scheduling
        self.assertEqual(drill.status, "scheduled")
        self.assertEqual(drill.participants_expected, 200)
        
        # Start drill
        success = EmergencyProcedureService.start_drill(drill)
        self.assertTrue(success)
        
        drill.refresh_from_db()
        self.assertEqual(drill.status, "in_progress")
        self.assertIsNotNone(drill.actual_start_time)
        
        # Complete drill
        success = EmergencyProcedureService.complete_drill(
            drill=drill,
            participants_actual=195,
            evaluation_score=8,
            notes="Good drill, minor issues with exit flow",
            areas_for_improvement="Improve exit signage"
        )
        self.assertTrue(success)
        
        drill.refresh_from_db()
        self.assertEqual(drill.status, "completed")
        self.assertEqual(drill.participants_actual, 195)
        self.assertEqual(drill.evaluation_score, 8)
        self.assertEqual(drill.participation_rate, 97.5)
        self.assertIsNotNone(drill.duration_minutes)
    
    def test_emergency_contact_management(self):
        """Test emergency contact management"""
        # Create emergency contacts
        contacts_data = [
            {"name": "Fire Department", "type": "fire", "phone": "911", "priority": 1},
            {"name": "Police", "type": "police", "phone": "911", "priority": 1},
            {"name": "Hospital", "type": "medical", "phone": "555-0123", "priority": 2},
            {"name": "Security Company", "type": "security", "phone": "555-0456", "priority": 3},
        ]
        
        created_contacts = []
        for contact_data in contacts_data:
            contact = EmergencyContact.objects.create(
                school=self.school,
                name=contact_data["name"],
                contact_type=contact_data["type"],
                phone_primary=contact_data["phone"],
                priority_order=contact_data["priority"],
                is_active=True,
                created_by=self.user
            )
            created_contacts.append(contact)
        
        # Verify contacts were created
        self.assertEqual(len(created_contacts), 4)
        
        # Test getting contacts by type
        fire_contacts = EmergencyContact.objects.filter(
            school=self.school,
            contact_type="fire",
            is_active=True
        ).order_by('priority_order')
        
        self.assertEqual(len(fire_contacts), 1)
        self.assertEqual(fire_contacts[0].name, "Fire Department")
        self.assertEqual(fire_contacts[0].phone_primary, "911")
        
        # Test getting all active contacts ordered by priority
        all_contacts = EmergencyContact.objects.filter(
            school=self.school,
            is_active=True
        ).order_by('priority_order', 'name')
        
        self.assertEqual(len(all_contacts), 4)
        # Priority 1 contacts should come first
        priority_1_contacts = [c for c in all_contacts if c.priority_order == 1]
        self.assertEqual(len(priority_1_contacts), 2)
    
    def test_auto_resolve_functionality(self):
        """Test auto-resolve functionality for emergency alerts"""
        # Create alert with auto-resolve time in the past
        past_time = timezone.now() - timedelta(minutes=30)
        
        alert = EmergencyAlert.objects.create(
            school=self.school,
            title="Auto-Resolve Test Alert",
            message="This alert should auto-resolve",
            alert_type="general",
            severity="low",
            issued_by=self.user,
            auto_resolve_at=past_time,
            created_by=self.user
        )
        
        # Initially should be active
        self.assertEqual(alert.status, "active")
        self.assertTrue(alert.is_active)
        
        # Manually resolve the alert to test the functionality
        alert.resolve()
        
        # Alert should now be resolved
        self.assertEqual(alert.status, "resolved")
        self.assertFalse(alert.is_active)
        self.assertIsNotNone(alert.resolved_at)
    
    def test_emergency_alert_severity_ordering(self):
        """Test that emergency alerts are properly ordered by severity"""
        # Create alerts with different severities
        severities = ["low", "medium", "high", "critical"]
        alerts = []
        
        for i, severity in enumerate(severities):
            alert = EmergencyAlert.objects.create(
                school=self.school,
                title=f"{severity.title()} Alert {i}",
                message=f"This is a {severity} severity alert",
                alert_type="general",
                severity=severity,
                issued_by=self.user,
                created_by=self.user
            )
            alerts.append(alert)
        
        # Get active alerts (should be ordered by severity)
        active_alerts = EmergencyNotificationService.get_active_alerts(self.school)
        
        # Should have all 4 alerts
        self.assertEqual(len(active_alerts), 4)
        
        # Should be ordered: critical, high, medium, low
        expected_order = ["critical", "high", "medium", "low"]
        actual_order = [alert.severity for alert in active_alerts]
        self.assertEqual(actual_order, expected_order)
    
    def test_emergency_notification_delivery_tracking(self):
        """Test emergency notification delivery tracking"""
        # Create emergency alert
        alert = EmergencyAlert.objects.create(
            school=self.school,
            title="Delivery Tracking Test",
            message="Testing delivery tracking",
            alert_type="general",
            severity="medium",
            issued_by=self.user,
            acknowledgment_required=True,
            created_by=self.user
        )
        
        # Create multiple recipient records with different statuses
        recipient_data = [
            {"status": "sent", "method": "email", "address": "<EMAIL>"},
            {"status": "delivered", "method": "sms", "address": "+1234567890"},
            {"status": "acknowledged", "method": "email", "address": "<EMAIL>"},
            {"status": "failed", "method": "email", "address": "invalid@email"},
        ]
        
        recipients = []
        for i, data in enumerate(recipient_data, 1):
            recipient = EmergencyAlertRecipient.objects.create(
                school=self.school,
                alert=alert,
                recipient_type="user",
                recipient_id=i,
                contact_method=data["method"],
                contact_address=data["address"],
                status=data["status"],
                sent_at=timezone.now() if data["status"] != "failed" else None,
                delivered_at=timezone.now() if data["status"] in ["delivered", "acknowledged"] else None,
                acknowledged_at=timezone.now() if data["status"] == "acknowledged" else None,
                created_by=self.user
            )
            recipients.append(recipient)
        
        # Get alert statistics
        stats = EmergencyNotificationService.get_alert_statistics(alert)
        
        # Verify statistics
        self.assertEqual(stats['total_recipients'], 4)
        self.assertEqual(stats['sent_count'], 3)  # sent, delivered, acknowledged
        self.assertEqual(stats['delivered_count'], 2)  # delivered, acknowledged
        self.assertEqual(stats['acknowledged_count'], 1)  # acknowledged only
        self.assertEqual(stats['failed_count'], 1)  # failed only
        
        # Verify rates
        self.assertEqual(stats['delivery_rate'], 50.0)  # 2/4 * 100
        self.assertEqual(stats['acknowledgment_rate'], 25.0)  # 1/4 * 100
        
        # Test individual recipient tracking
        acknowledged_recipient = recipients[2]  # The acknowledged one
        self.assertEqual(acknowledged_recipient.status, "acknowledged")
        self.assertIsNotNone(acknowledged_recipient.acknowledged_at)
        
        failed_recipient = recipients[3]  # The failed one
        self.assertEqual(failed_recipient.status, "failed")
        self.assertIsNone(failed_recipient.sent_at)