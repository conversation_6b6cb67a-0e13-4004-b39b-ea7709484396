"""
Tests for parent communication system
"""
import pytest
from datetime import date, datetime, timedelta
from django.test import TestCase
from django.utils import timezone
from django.core.exceptions import ValidationError

from core.models import School
from accounts.models import User
from .models import (
    NotificationTemplate, NotificationChannel, Notification,
    NotificationGroup, NotificationGroupMember
)
from .services import NotificationService, GroupService, TemplateService


@pytest.mark.django_db
class TestParentCommunication:
    """Test parent communication features"""
    
    @pytest.fixture
    def setup_parent_data(self):
        """Setup test data for parent communication"""
        # Create school
        school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today() - timedelta(days=365)
        )
        
        # Create users (teacher and parent)
        teacher = User.objects.create_user(
            username="teacher",
            email="<EMAIL>",
            password="testpass123",
            first_name="<PERSON>",
            last_name="Teacher"
        )
        
        parent = User.objects.create_user(
            username="parent",
            email="<EMAIL>",
            password="testpass123",
            first_name="<PERSON>",
            last_name="Parent"
        )
        
        # Create notification channel
        channel = NotificationChannel.objects.create(
            school=school,
            name="Parent Email Channel",
            channel_type="email",
            is_default=True,
            is_active=True,
            created_by=teacher
        )
        
        return {
            'school': school,
            'teacher': teacher,
            'parent': parent,
            'channel': channel
        }
    
    def test_parent_progress_report_template(self, setup_parent_data):
        """Test parent progress report notification template"""
        data = setup_parent_data
        
        # Create progress report template
        template = TemplateService.create_template(
            name="Student Progress Report",
            code="PARENT_PROGRESS_REPORT",
            category="parent",
            channel="email",
            subject="{{student_name}} - Progress Report for {{period}}",
            body="""Dear {{parent_name}},

Here is the progress report for {{student_name}} for the period {{period}}:

Academic Performance:
- Overall Grade: {{overall_grade}}
- Attendance Rate: {{attendance_rate}}%
- Subjects:
{{#subjects}}
  - {{subject_name}}: {{grade}} ({{teacher_comment}})
{{/subjects}}

Behavioral Notes:
{{behavioral_notes}}

Next Parent-Teacher Conference: {{next_conference_date}}

Best regards,
{{school_name}} Administration""",
            variables={
                "student_name": "Student's full name",
                "parent_name": "Parent's name",
                "period": "Reporting period",
                "overall_grade": "Overall grade/GPA",
                "attendance_rate": "Attendance percentage",
                "subjects": "List of subjects with grades",
                "behavioral_notes": "Behavioral observations",
                "next_conference_date": "Next conference date",
                "school_name": "School name"
            },
            school=data['school']
        )
        
        assert template.name == "Student Progress Report"
        assert template.code == "PARENT_PROGRESS_REPORT"
        assert template.category == "parent"
        assert "{{student_name}}" in template.subject
        assert "{{parent_name}}" in template.body
    
    def test_parent_event_notification_template(self, setup_parent_data):
        """Test parent event notification template"""
        data = setup_parent_data
        
        # Create event notification template
        template = TemplateService.create_template(
            name="School Event Notification",
            code="PARENT_EVENT_NOTIFICATION",
            category="parent",
            channel="email",
            subject="{{event_name}} - {{event_date}}",
            body="""Dear {{parent_name}},

We would like to inform you about an upcoming school event:

Event: {{event_name}}
Date: {{event_date}}
Time: {{event_time}}
Location: {{event_location}}

Description:
{{event_description}}

{{#requires_permission}}
Permission Required: Please sign and return the attached permission slip.
{{/requires_permission}}

{{#requires_payment}}
Fee: {{event_fee}}
Payment Due: {{payment_due_date}}
{{/requires_payment}}

For questions, please contact us at {{contact_info}}.

Best regards,
{{school_name}}""",
            variables={
                "parent_name": "Parent's name",
                "event_name": "Event name",
                "event_date": "Event date",
                "event_time": "Event time",
                "event_location": "Event location",
                "event_description": "Event description",
                "requires_permission": "Boolean for permission requirement",
                "requires_payment": "Boolean for payment requirement",
                "event_fee": "Event fee amount",
                "payment_due_date": "Payment due date",
                "contact_info": "School contact information",
                "school_name": "School name"
            },
            school=data['school']
        )
        
        assert template.name == "School Event Notification"
        assert template.code == "PARENT_EVENT_NOTIFICATION"
        assert "{{event_name}}" in template.subject
        assert "{{parent_name}}" in template.body
    
    def test_parent_absence_notification_template(self, setup_parent_data):
        """Test parent absence notification template"""
        data = setup_parent_data
        
        # Create absence notification template
        template = TemplateService.create_template(
            name="Student Absence Notification",
            code="PARENT_ABSENCE_NOTIFICATION",
            category="parent",
            channel="sms",
            subject="{{student_name}} Absence Alert",
            body="{{student_name}} was marked absent from {{subject}} at {{time}} on {{date}}. If this is an error, please contact the school at {{phone}}.",
            variables={
                "student_name": "Student's name",
                "subject": "Subject/class name",
                "time": "Class time",
                "date": "Date of absence",
                "phone": "School phone number"
            },
            school=data['school']
        )
        
        assert template.name == "Student Absence Notification"
        assert template.code == "PARENT_ABSENCE_NOTIFICATION"
        assert template.channel == "sms"
        assert "{{student_name}}" in template.body
    
    def test_parent_group_creation(self, setup_parent_data):
        """Test creating parent notification groups"""
        data = setup_parent_data
        
        # Create parent group
        parent_group = GroupService.create_group(
            name="All Parents",
            description="Group for all parents in the school",
            school=data['school']
        )
        
        assert parent_group.name == "All Parents"
        assert parent_group.school == data['school']
        
        # Add parent to group
        member = GroupService.add_member_to_group(
            group_id=parent_group.id,
            member_type="parent",
            member_id=1,  # Assuming parent ID
            contact_info={
                "email": "<EMAIL>",
                "phone": "+1234567890",
                "name": "Jane Parent"
            }
        )
        
        assert member.group == parent_group
        assert member.member_type == "parent"
        assert member.contact_info["email"] == "<EMAIL>"
    
    def test_grade_specific_parent_group(self, setup_parent_data):
        """Test creating grade-specific parent groups"""
        data = setup_parent_data
        
        # Create grade-specific parent group
        grade_1_parents = GroupService.create_group(
            name="Grade 1 Parents",
            description="Parents of Grade 1 students",
            school=data['school']
        )
        
        # Add multiple parents
        parents_data = [
            {"id": 1, "email": "<EMAIL>", "name": "Parent One"},
            {"id": 2, "email": "<EMAIL>", "name": "Parent Two"},
            {"id": 3, "email": "<EMAIL>", "name": "Parent Three"}
        ]
        
        for parent_data in parents_data:
            GroupService.add_member_to_group(
                group_id=grade_1_parents.id,
                member_type="parent",
                member_id=parent_data["id"],
                contact_info={
                    "email": parent_data["email"],
                    "name": parent_data["name"],
                    "phone": f"+123456789{parent_data['id']}"
                }
            )
        
        # Verify group membership
        members = NotificationGroupMember.objects.filter(
            group=grade_1_parents,
            is_active=True
        )
        
        assert members.count() == 3
        assert all(member.member_type == "parent" for member in members)
    
    def test_send_progress_report_to_parents(self, setup_parent_data):
        """Test sending progress reports to parents"""
        data = setup_parent_data
        
        # Create progress report template
        template = TemplateService.create_template(
            name="Progress Report",
            code="PROGRESS_REPORT",
            category="parent",
            channel="email",
            subject="{{student_name}} Progress Report",
            body="Dear {{parent_name}}, here is {{student_name}}'s progress report.",
            school=data['school']
        )
        
        # Send progress report notification
        notification = NotificationService.create_notification(
            template_code="PROGRESS_REPORT",
            recipient_contact="<EMAIL>",
            variables={
                "student_name": "John Student",
                "parent_name": "Jane Parent",
                "overall_grade": "A-",
                "attendance_rate": "95"
            },
            recipient_type="parent",
            recipient_id=1,
            priority="normal",
            school=data['school']
        )
        
        assert notification is not None
        assert notification.template == template
        assert notification.recipient_type == "parent"
        assert "John Student Progress Report" in notification.subject
        assert "Jane Parent" in notification.message
    
    def test_send_event_notification_to_parent_group(self, setup_parent_data):
        """Test sending event notifications to parent groups"""
        data = setup_parent_data
        
        # Create event notification template
        template = TemplateService.create_template(
            name="Event Notification",
            code="EVENT_NOTIFICATION",
            category="parent",
            channel="email",
            subject="School Event: {{event_name}}",
            body="Dear {{parent_name}}, we invite you to {{event_name}} on {{event_date}}.",
            school=data['school']
        )
        
        # Create parent group
        parent_group = GroupService.create_group(
            name="Event Parents",
            description="Parents for event notifications",
            school=data['school']
        )
        
        # Add parents to group
        GroupService.add_member_to_group(
            group_id=parent_group.id,
            member_type="parent",
            member_id=1,
            contact_info={
                "email": "<EMAIL>",
                "name": "Parent One"
            }
        )
        
        GroupService.add_member_to_group(
            group_id=parent_group.id,
            member_type="parent",
            member_id=2,
            contact_info={
                "email": "<EMAIL>",
                "name": "Parent Two"
            }
        )
        
        # Send group notification
        notifications = NotificationService.send_group_notification(
            group_id=parent_group.id,
            template_code="EVENT_NOTIFICATION",
            variables={
                "event_name": "Science Fair",
                "event_date": "March 15, 2025",
                "event_time": "2:00 PM",
                "event_location": "School Gymnasium"
            },
            priority="normal",
            school=data['school']
        )
        
        assert len(notifications) == 2
        assert all(notif.template == template for notif in notifications)
        assert all(notif.recipient_type == "parent" for notif in notifications)
    
    def test_automated_absence_notification(self, setup_parent_data):
        """Test automated absence notifications to parents"""
        data = setup_parent_data
        
        # Create absence notification template
        template = TemplateService.create_template(
            name="Absence Alert",
            code="ABSENCE_ALERT",
            category="parent",
            channel="sms",
            subject="{{student_name}} Absence",
            body="{{student_name}} was absent from {{class_name}} today. Please contact school if this is an error.",
            school=data['school']
        )
        
        # Send absence notification
        notification = NotificationService.create_notification(
            template_code="ABSENCE_ALERT",
            recipient_contact="+1234567890",
            variables={
                "student_name": "John Student",
                "class_name": "Mathematics",
                "date": "March 10, 2025",
                "time": "10:00 AM"
            },
            recipient_type="parent",
            recipient_id=1,
            priority="high",
            school=data['school']
        )
        
        assert notification is not None
        assert notification.template == template
        assert notification.priority == "high"
        assert "John Student" in notification.message
        assert "Mathematics" in notification.message
    
    def test_parent_conference_reminder(self, setup_parent_data):
        """Test parent-teacher conference reminders"""
        data = setup_parent_data
        
        # Create conference reminder template
        template = TemplateService.create_template(
            name="Conference Reminder",
            code="CONFERENCE_REMINDER",
            category="parent",
            channel="email",
            subject="Parent-Teacher Conference Reminder - {{student_name}}",
            body="""Dear {{parent_name}},

This is a reminder about your upcoming parent-teacher conference:

Student: {{student_name}}
Date: {{conference_date}}
Time: {{conference_time}}
Teacher: {{teacher_name}}
Location: {{location}}

Please arrive 5 minutes early. If you need to reschedule, please contact us at {{contact_phone}}.

Best regards,
{{school_name}}""",
            variables={
                "parent_name": "Parent's name",
                "student_name": "Student's name",
                "conference_date": "Conference date",
                "conference_time": "Conference time",
                "teacher_name": "Teacher's name",
                "location": "Meeting location",
                "contact_phone": "School contact phone",
                "school_name": "School name"
            },
            school=data['school']
        )
        
        # Send conference reminder
        notification = NotificationService.create_notification(
            template_code="CONFERENCE_REMINDER",
            recipient_contact="<EMAIL>",
            variables={
                "parent_name": "Jane Parent",
                "student_name": "John Student",
                "conference_date": "March 20, 2025",
                "conference_time": "3:00 PM",
                "teacher_name": "Ms. Smith",
                "location": "Room 101",
                "contact_phone": "555-0123",
                "school_name": "Test School"
            },
            recipient_type="parent",
            priority="normal",
            scheduled_at=timezone.now() + timedelta(days=1),  # Send tomorrow
            school=data['school']
        )
        
        assert notification is not None
        assert notification.scheduled_at is not None
        assert "Jane Parent" in notification.message
        assert "March 20, 2025" in notification.message


class TestParentCommunicationIntegration(TestCase):
    """Integration tests for parent communication"""
    
    def setUp(self):
        """Setup test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today() - timedelta(days=365)
        )
        
        self.teacher = User.objects.create_user(
            username="teacher",
            email="<EMAIL>",
            password="testpass123",
            first_name="John",
            last_name="Teacher"
        )
        
        # Create notification channel
        self.channel = NotificationChannel.objects.create(
            school=self.school,
            name="Parent Communication Channel",
            channel_type="email",
            is_default=True,
            is_active=True,
            created_by=self.teacher
        )
    
    def test_complete_parent_communication_workflow(self):
        """Test complete parent communication workflow"""
        # Create parent notification templates
        progress_template = TemplateService.create_template(
            name="Monthly Progress Report",
            code="MONTHLY_PROGRESS",
            category="parent",
            channel="email",
            subject="{{student_name}} - Monthly Progress Report",
            body="Dear {{parent_name}}, {{student_name}} has achieved {{grade}} this month.",
            variables={
                "parent_name": "Parent's name",
                "student_name": "Student's name",
                "grade": "Monthly grade"
            },
            school=self.school
        )
        
        event_template = TemplateService.create_template(
            name="School Event",
            code="SCHOOL_EVENT",
            category="parent",
            channel="email",
            subject="Upcoming Event: {{event_name}}",
            body="Dear {{parent_name}}, we invite you to {{event_name}} on {{date}}.",
            variables={
                "parent_name": "Parent's name",
                "event_name": "Event name",
                "date": "Event date"
            },
            school=self.school
        )
        
        # Create parent groups
        all_parents = GroupService.create_group(
            name="All Parents",
            description="All school parents",
            school=self.school
        )
        
        grade_1_parents = GroupService.create_group(
            name="Grade 1 Parents",
            description="Grade 1 parents only",
            school=self.school
        )
        
        # Add parents to groups
        parents_data = [
            {"id": 1, "name": "Parent One", "email": "<EMAIL>"},
            {"id": 2, "name": "Parent Two", "email": "<EMAIL>"},
            {"id": 3, "name": "Parent Three", "email": "<EMAIL>"}
        ]
        
        for parent in parents_data:
            # Add to all parents group
            GroupService.add_member_to_group(
                group_id=all_parents.id,
                member_type="parent",
                member_id=parent["id"],
                contact_info={
                    "email": parent["email"],
                    "name": parent["name"]
                }
            )
            
            # Add first two to grade 1 parents
            if parent["id"] <= 2:
                GroupService.add_member_to_group(
                    group_id=grade_1_parents.id,
                    member_type="parent",
                    member_id=parent["id"],
                    contact_info={
                        "email": parent["email"],
                        "name": parent["name"]
                    }
                )
        
        # Send individual progress report
        progress_notification = NotificationService.create_notification(
            template_code="MONTHLY_PROGRESS",
            recipient_contact="<EMAIL>",
            variables={
                "parent_name": "Parent One",
                "student_name": "Student One",
                "grade": "A+"
            },
            recipient_type="parent",
            recipient_id=1,
            school=self.school
        )
        
        self.assertIsNotNone(progress_notification)
        self.assertEqual(progress_notification.template, progress_template)
        self.assertIn("Student One", progress_notification.message)
        self.assertIn("A+", progress_notification.message)
        
        # Send event notification to all parents
        event_notifications = NotificationService.send_group_notification(
            group_id=all_parents.id,
            template_code="SCHOOL_EVENT",
            variables={
                "event_name": "Spring Concert",
                "date": "April 15, 2025"
            },
            priority="normal"
        )
        
        self.assertEqual(len(event_notifications), 3)  # All 3 parents
        for notification in event_notifications:
            self.assertEqual(notification.template, event_template)
            self.assertIn("Spring Concert", notification.message)
        
        # Send grade-specific notification
        grade_notifications = NotificationService.send_group_notification(
            group_id=grade_1_parents.id,
            template_code="SCHOOL_EVENT",
            variables={
                "event_name": "Grade 1 Field Trip",
                "date": "May 1, 2025"
            },
            priority="normal"
        )
        
        self.assertEqual(len(grade_notifications), 2)  # Only 2 grade 1 parents
        for notification in grade_notifications:
            self.assertIn("Grade 1 Field Trip", notification.message)
        
        # Verify group memberships
        all_parents_count = NotificationGroupMember.objects.filter(
            group=all_parents,
            is_active=True
        ).count()
        self.assertEqual(all_parents_count, 3)
        
        grade_1_count = NotificationGroupMember.objects.filter(
            group=grade_1_parents,
            is_active=True
        ).count()
        self.assertEqual(grade_1_count, 2)
    
    def test_multilingual_parent_communication(self):
        """Test multilingual parent communication"""
        # Create bilingual template
        template = TemplateService.create_template(
            name="Bilingual Event Notification",
            code="BILINGUAL_EVENT",
            category="parent",
            channel="email",
            subject="School Event / فعالية مدرسية",
            body="""Dear {{parent_name}} / عزيزي {{parent_name_ar}},

English:
We invite you to {{event_name}} on {{date}}.

Arabic:
ندعوكم لحضور {{event_name_ar}} في {{date_ar}}.

Best regards / مع أطيب التحيات,
{{school_name}}""",
            variables={
                "parent_name": "Parent name in English",
                "parent_name_ar": "Parent name in Arabic",
                "event_name": "Event name in English",
                "event_name_ar": "Event name in Arabic",
                "date": "Date in English",
                "date_ar": "Date in Arabic",
                "school_name": "School name"
            },
            school=self.school
        )
        
        # Send bilingual notification
        notification = NotificationService.create_notification(
            template_code="BILINGUAL_EVENT",
            recipient_contact="<EMAIL>",
            variables={
                "parent_name": "John Parent",
                "parent_name_ar": "جون الوالد",
                "event_name": "Science Fair",
                "event_name_ar": "معرض العلوم",
                "date": "March 15, 2025",
                "date_ar": "١٥ مارس ٢٠٢٥",
                "school_name": "Test School"
            },
            recipient_type="parent",
            school=self.school
        )
        
        self.assertIsNotNone(notification)
        self.assertIn("John Parent", notification.message)
        self.assertIn("جون الوالد", notification.message)
        self.assertIn("Science Fair", notification.message)
        self.assertIn("معرض العلوم", notification.message)