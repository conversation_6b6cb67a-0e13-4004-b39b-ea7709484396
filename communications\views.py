"""
Communication views for notification management
"""
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required, permission_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
import json
from datetime import datetime, timedelta

from .models import (
    NotificationTemplate, NotificationChannel, Notification,
    NotificationGroup, NotificationGroupMember, NotificationLog,
    EmailConfiguration, SMSConfiguration, WhatsAppConfiguration
)


@login_required
def communications_dashboard(request):
    """Communications dashboard view"""
    context = {
        'total_notifications': Notification.objects.count(),
        'recent_notifications': Notification.objects.order_by('-created_at')[:5],
        'notification_channels': NotificationChannel.objects.all(),
    }
    return render(request, 'communications/dashboard.html', context)


@login_required
def announcement_list(request):
    """List all announcements"""
    announcements = Notification.objects.filter(
        notification_type='announcement'
    ).order_by('-created_at')
    
    context = {
        'announcements': announcements,
    }
    return render(request, 'communications/announcement_list.html', context)


@login_required
def announcement_create(request):
    """Create new announcement"""
    if request.method == 'POST':
        # Handle announcement creation
        messages.success(request, 'Announcement created successfully!')
        return redirect('communications:announcement_list')
    
    return render(request, 'communications/announcement_create.html')


@login_required
def emergency_dashboard(request):
    """Emergency communications dashboard"""
    context = {
        'emergency_notifications': Notification.objects.filter(
            priority='high'
        ).order_by('-created_at')[:10],
    }
    return render(request, 'communications/emergency_dashboard.html', context)


@login_required
def parent_communication_list(request):
    """List parent communications"""
    communications = Notification.objects.filter(
        recipient_type='parent'
    ).order_by('-created_at')
    
    context = {
        'communications': communications,
    }
    return render(request, 'communications/parent_communication_list.html', context)
# Removed problematic imports for now


@login_required
@permission_required('communications.view_notification', raise_exception=True)
def communication_dashboard(request):
    """Communication dashboard with overview"""
    try:
        employee = request.user.employee_profile
        school = employee.school
    except:
        messages.error(request, 'Employee profile not found.')
        return redirect('core:dashboard')
    
    # Get statistics
    today = timezone.now().date()
    week_ago = today - timedelta(days=7)
    
    stats = {
        'total_notifications': Notification.objects.filter(school=school).count(),
        'sent_today': Notification.objects.filter(
            school=school,
            sent_at__date=today
        ).count(),
        'sent_this_week': Notification.objects.filter(
            school=school,
            sent_at__date__gte=week_ago
        ).count(),
        'failed_notifications': Notification.objects.filter(
            school=school,
            status='failed'
        ).count(),
        'pending_notifications': Notification.objects.filter(
            school=school,
            status='pending'
        ).count(),
    }
    
    # Get recent notifications
    recent_notifications = Notification.objects.filter(
        school=school
    ).order_by('-created_at')[:10]
    
    # Get channel statistics
    channel_stats = Notification.objects.filter(
        school=school
    ).values('channel__channel_type').annotate(
        count=Count('id')
    ).order_by('-count')
    
    # Get template usage
    template_stats = Notification.objects.filter(
        school=school
    ).values('template__name').annotate(
        count=Count('id')
    ).order_by('-count')[:5]
    
    context = {
        'stats': stats,
        'recent_notifications': recent_notifications,
        'channel_stats': channel_stats,
        'template_stats': template_stats,
    }
    
    return render(request, 'communications/dashboard.html', context)


@login_required
@permission_required('communications.view_notification', raise_exception=True)
def notification_list(request):
    """List all notifications"""
    try:
        employee = request.user.employee_profile
        school = employee.school
    except:
        messages.error(request, 'Employee profile not found.')
        return redirect('core:dashboard')
    
    # Get filter parameters
    status = request.GET.get('status', '')
    channel = request.GET.get('channel', '')
    template = request.GET.get('template', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    search_query = request.GET.get('search', '')
    
    # Build queryset
    notifications = Notification.objects.filter(
        school=school
    ).select_related('template', 'channel')
    
    # Apply filters
    if status:
        notifications = notifications.filter(status=status)
    
    if channel:
        notifications = notifications.filter(channel__channel_type=channel)
    
    if template:
        notifications = notifications.filter(template_id=template)
    
    if date_from:
        notifications = notifications.filter(created_at__date__gte=date_from)
    
    if date_to:
        notifications = notifications.filter(created_at__date__lte=date_to)
    
    if search_query:
        notifications = notifications.filter(
            Q(subject__icontains=search_query) |
            Q(recipient_contact__icontains=search_query) |
            Q(message__icontains=search_query)
        )
    
    notifications = notifications.order_by('-created_at')
    
    # Pagination
    paginator = Paginator(notifications, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get filter options
    templates = NotificationTemplate.objects.filter(school=school, is_active=True)
    channels = NotificationChannel.objects.filter(school=school, is_active=True)
    
    context = {
        'page_obj': page_obj,
        'templates': templates,
        'channels': channels,
        'selected_status': status,
        'selected_channel': channel,
        'selected_template': template,
        'date_from': date_from,
        'date_to': date_to,
        'search_query': search_query,
    }
    
    return render(request, 'communications/notification_list.html', context)


@login_required
@permission_required('communications.add_notification', raise_exception=True)
def send_notification(request):
    """Send a new notification"""
    try:
        employee = request.user.employee_profile
        school = employee.school
    except:
        messages.error(request, 'Employee profile not found.')
        return redirect('core:dashboard')
    
    if request.method == 'POST':
        form = NotificationForm(request.POST)
        if form.is_valid():
            try:
                notification = NotificationService.create_notification(
                    template_code=form.cleaned_data['template'].code,
                    recipient_contact=form.cleaned_data['recipient_contact'],
                    variables=form.cleaned_data.get('variables', {}),
                    recipient_type=form.cleaned_data['recipient_type'],
                    recipient_id=form.cleaned_data.get('recipient_id'),
                    priority=form.cleaned_data['priority'],
                    scheduled_at=form.cleaned_data.get('scheduled_at')
                )
                
                # Send immediately if not scheduled
                if not form.cleaned_data.get('scheduled_at'):
                    NotificationService.send_notification(notification)
                
                messages.success(request, 'Notification sent successfully.')
                return redirect('communications:notification_detail', notification_id=notification.id)
                
            except Exception as e:
                messages.error(request, str(e))
    else:
        form = NotificationForm()
        # Filter templates by school
        form.fields['template'].queryset = NotificationTemplate.objects.filter(
            school=school,
            is_active=True
        )
    
    context = {
        'form': form,
    }
    
    return render(request, 'communications/send_notification.html', context)


@login_required
@permission_required('communications.view_notification', raise_exception=True)
def notification_detail(request, notification_id):
    """View notification details"""
    notification = get_object_or_404(
        Notification.objects.select_related('template', 'channel'),
        id=notification_id
    )
    
    # Get logs
    logs = NotificationLog.objects.filter(
        notification=notification
    ).order_by('-created_at')
    
    context = {
        'notification': notification,
        'logs': logs,
    }
    
    return render(request, 'communications/notification_detail.html', context)


@login_required
@permission_required('communications.view_notificationtemplate', raise_exception=True)
def template_list(request):
    """List notification templates"""
    try:
        employee = request.user.employee_profile
        school = employee.school
    except:
        messages.error(request, 'Employee profile not found.')
        return redirect('core:dashboard')
    
    # Get filter parameters
    category = request.GET.get('category', '')
    channel = request.GET.get('channel', '')
    search_query = request.GET.get('search', '')
    
    # Build queryset
    templates = NotificationTemplate.objects.filter(school=school)
    
    # Apply filters
    if category:
        templates = templates.filter(category=category)
    
    if channel:
        templates = templates.filter(channel=channel)
    
    if search_query:
        templates = templates.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(subject__icontains=search_query)
        )
    
    templates = templates.order_by('category', 'name')
    
    # Pagination
    paginator = Paginator(templates, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'selected_category': category,
        'selected_channel': channel,
        'search_query': search_query,
        'categories': NotificationTemplate.CATEGORY_CHOICES,
        'channels': NotificationTemplate.CHANNEL_CHOICES,
    }
    
    return render(request, 'communications/template_list.html', context)


@login_required
@permission_required('communications.add_notificationtemplate', raise_exception=True)
def create_template(request):
    """Create a new notification template"""
    try:
        employee = request.user.employee_profile
        school = employee.school
    except:
        messages.error(request, 'Employee profile not found.')
        return redirect('core:dashboard')
    
    if request.method == 'POST':
        form = NotificationTemplateForm(request.POST)
        if form.is_valid():
            try:
                template = TemplateService.create_template(
                    name=form.cleaned_data['name'],
                    code=form.cleaned_data['code'],
                    category=form.cleaned_data['category'],
                    channel=form.cleaned_data['channel'],
                    subject=form.cleaned_data['subject'],
                    body=form.cleaned_data['body'],
                    variables=form.cleaned_data.get('variables', {}),
                    school=school
                )
                
                messages.success(request, 'Template created successfully.')
                return redirect('communications:template_detail', template_id=template.id)
                
            except Exception as e:
                messages.error(request, str(e))
    else:
        form = NotificationTemplateForm()
    
    context = {
        'form': form,
    }
    
    return render(request, 'communications/create_template.html', context)


@login_required
@permission_required('communications.view_notificationtemplate', raise_exception=True)
def template_detail(request, template_id):
    """View template details"""
    template = get_object_or_404(NotificationTemplate, id=template_id)
    
    # Get usage statistics
    usage_stats = {
        'total_sent': Notification.objects.filter(template=template).count(),
        'sent_this_month': Notification.objects.filter(
            template=template,
            sent_at__month=timezone.now().month
        ).count(),
        'success_rate': 0,
    }
    
    total_notifications = Notification.objects.filter(template=template).count()
    if total_notifications > 0:
        successful = Notification.objects.filter(
            template=template,
            status__in=['sent', 'delivered']
        ).count()
        usage_stats['success_rate'] = round((successful / total_notifications) * 100, 2)
    
    context = {
        'template': template,
        'usage_stats': usage_stats,
    }
    
    return render(request, 'communications/template_detail.html', context)


@login_required
@permission_required('communications.view_notificationgroup', raise_exception=True)
def group_list(request):
    """List notification groups"""
    try:
        employee = request.user.employee_profile
        school = employee.school
    except:
        messages.error(request, 'Employee profile not found.')
        return redirect('core:dashboard')
    
    # Get filter parameters
    search_query = request.GET.get('search', '')
    
    # Build queryset
    groups = NotificationGroup.objects.filter(school=school)
    
    if search_query:
        groups = groups.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query)
        )
    
    groups = groups.annotate(
        member_count=Count('members', filter=Q(members__is_active=True))
    ).order_by('name')
    
    # Pagination
    paginator = Paginator(groups, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'search_query': search_query,
    }
    
    return render(request, 'communications/group_list.html', context)


@login_required
@permission_required('communications.add_notificationgroup', raise_exception=True)
def create_group(request):
    """Create a new notification group"""
    try:
        employee = request.user.employee_profile
        school = employee.school
    except:
        messages.error(request, 'Employee profile not found.')
        return redirect('core:dashboard')
    
    if request.method == 'POST':
        form = NotificationGroupForm(request.POST)
        if form.is_valid():
            try:
                group = GroupService.create_group(
                    name=form.cleaned_data['name'],
                    description=form.cleaned_data.get('description'),
                    school=school
                )
                
                messages.success(request, 'Group created successfully.')
                return redirect('communications:group_detail', group_id=group.id)
                
            except Exception as e:
                messages.error(request, str(e))
    else:
        form = NotificationGroupForm()
    
    context = {
        'form': form,
    }
    
    return render(request, 'communications/create_group.html', context)


@login_required
@permission_required('communications.view_notificationgroup', raise_exception=True)
def group_detail(request, group_id):
    """View group details and members"""
    group = get_object_or_404(NotificationGroup, id=group_id)
    
    # Get members
    members = NotificationGroupMember.objects.filter(
        group=group,
        is_active=True
    ).order_by('member_type', 'member_id')
    
    # Pagination for members
    paginator = Paginator(members, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'group': group,
        'page_obj': page_obj,
    }
    
    return render(request, 'communications/group_detail.html', context)


# AJAX Views
@login_required
@require_http_methods(["POST"])
def retry_notification(request, notification_id):
    """Retry a failed notification (AJAX)"""
    try:
        notification = get_object_or_404(Notification, id=notification_id)
        
        if not notification.can_retry:
            return JsonResponse({
                'success': False,
                'message': 'Notification cannot be retried'
            })
        
        success = NotificationService.retry_failed_notification(notification)
        
        return JsonResponse({
            'success': success,
            'message': 'Notification retried successfully' if success else 'Failed to retry notification'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@login_required
@require_http_methods(["POST"])
def cancel_notification(request, notification_id):
    """Cancel a pending notification (AJAX)"""
    try:
        notification = get_object_or_404(Notification, id=notification_id)
        
        if notification.status != 'pending':
            return JsonResponse({
                'success': False,
                'message': 'Only pending notifications can be cancelled'
            })
        
        notification.status = 'cancelled'
        notification.save()
        
        # Log cancellation
        log_data = {
            'notification': notification,
            'action': 'cancelled',
            'details': 'Notification cancelled by user'
        }
        if hasattr(notification, 'school') and notification.school:
            log_data['school'] = notification.school
        
        NotificationLog.objects.create(**log_data)
        
        return JsonResponse({
            'success': True,
            'message': 'Notification cancelled successfully'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@login_required
@require_http_methods(["GET"])
def template_preview(request, template_id):
    """Preview template with sample variables (AJAX)"""
    try:
        template = get_object_or_404(NotificationTemplate, id=template_id)
        
        # Get sample variables from request
        variables = {}
        for key, value in request.GET.items():
            if key.startswith('var_'):
                var_name = key[4:]  # Remove 'var_' prefix
                variables[var_name] = value
        
        # Render template
        subject = NotificationService._render_template(template.subject, variables)
        message = NotificationService._render_template(template.body, variables)
        
        return JsonResponse({
            'success': True,
            'subject': subject,
            'message': message,
            'variables': template.variables
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@login_required
@require_http_methods(["POST"])
def send_test_notification(request):
    """Send a test notification (AJAX)"""
    try:
        data = json.loads(request.body)
        
        notification = NotificationService.create_notification(
            template_code=data['template_code'],
            recipient_contact=data['recipient_contact'],
            variables=data.get('variables', {}),
            recipient_type='user',
            priority='normal'
        )
        
        success = NotificationService.send_notification(notification)
        
        return JsonResponse({
            'success': success,
            'message': 'Test notification sent successfully' if success else 'Failed to send test notification',
            'notification_id': notification.id
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


# Webhook endpoints
@csrf_exempt
@require_http_methods(["POST"])
def sms_webhook(request):
    """Handle SMS delivery webhooks"""
    try:
        # Parse webhook data based on SMS provider
        # This is a generic implementation
        data = json.loads(request.body)
        
        external_id = data.get('id') or data.get('message_id')
        status = data.get('status', '').lower()
        
        if external_id:
            try:
                notification = Notification.objects.get(external_id=external_id)
                
                if status in ['delivered', 'read']:
                    notification.status = 'delivered'
                    notification.delivered_at = timezone.now()
                elif status in ['failed', 'undelivered']:
                    notification.status = 'failed'
                    notification.error_message = data.get('error', 'Delivery failed')
                
                notification.save()
                
                # Log webhook
                log_data = {
                    'notification': notification,
                    'action': status,
                    'details': f'Webhook received: {status}',
                    'response_data': data
                }
                if hasattr(notification, 'school') and notification.school:
                    log_data['school'] = notification.school
                
                NotificationLog.objects.create(**log_data)
                
            except Notification.DoesNotExist:
                pass
        
        return JsonResponse({'status': 'ok'})
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=400)


@csrf_exempt
@require_http_methods(["POST"])
def whatsapp_webhook(request):
    """Handle WhatsApp delivery webhooks"""
    try:
        data = json.loads(request.body)
        
        # Parse WhatsApp webhook data
        entry = data.get('entry', [{}])[0]
        changes = entry.get('changes', [{}])[0]
        value = changes.get('value', {})
        
        # Handle status updates
        statuses = value.get('statuses', [])
        for status_data in statuses:
            message_id = status_data.get('id')
            status = status_data.get('status', '').lower()
            
            if message_id:
                try:
                    notification = Notification.objects.get(external_id=message_id)
                    
                    if status == 'delivered':
                        notification.status = 'delivered'
                        notification.delivered_at = timezone.now()
                    elif status == 'failed':
                        notification.status = 'failed'
                        notification.error_message = 'WhatsApp delivery failed'
                    
                    notification.save()
                    
                    # Log webhook
                    log_data = {
                        'notification': notification,
                        'action': status,
                        'details': f'WhatsApp webhook: {status}',
                        'response_data': status_data
                    }
                    if hasattr(notification, 'school') and notification.school:
                        log_data['school'] = notification.school
                    
                    NotificationLog.objects.create(**log_data)
                    
                except Notification.DoesNotExist:
                    pass
        
        return JsonResponse({'status': 'ok'})
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=400)