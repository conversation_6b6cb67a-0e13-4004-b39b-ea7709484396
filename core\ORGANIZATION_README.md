# Core App Organization

This document describes the reorganized structure of the core Django app for better maintainability and separation of concerns.

## Directory Structure

### Authentication (`core/authentication/`)
- **backends/**: Authentication backends (`backends.py`)
- **forms/**: Authentication forms (`auth_forms.py`)
- **middleware/**: Authentication middleware (`auth_middleware.py`)
- **models/**: Authentication models (`auth_models.py`)
- **utils/**: Authentication utilities (`auth_utils.py`)
- **views/**: Authentication views (`auth_views.py`)

### Audit System (`core/audit/`)
- `audit_middleware.py`: Audit middleware for tracking changes
- `audit_models.py`: Models for audit logging
- `audit_system.py`: Core audit system functionality

### Cache Management (`core/cache/`)
- `cache.py`: Cache utilities and helpers
- `cache_invalidation.py`: Cache invalidation strategies

### Calendar System (`core/calendar/`)
- `calendar_models.py`: Calendar-related models
- `calendar_utils.py`: Calendar utilities and helpers

### Database (`core/database/`)
- `database_optimization.py`: Database optimization utilities

### Encryption Management (`core/encryption_management/`)
- `encryption.py`: Core encryption functionality
- `encrypted_fields.py`: Django field encryption
- `key_management.py`: Encryption key management

### Notifications (`core/notifications/`)
- `push_notifications.py`: Push notification system

### Mobile (`core/mobile/`)
- `mobile_api.py`: Mobile API endpoints
- `mobile_auth.py`: Mobile authentication
- `mobile_auth_urls.py`: Mobile authentication URLs
- `mobile_urls.py`: Mobile URL patterns

### Localization (`core/localization/`)
- `localization.py`: Localization utilities
- `localization_views.py`: Localization views

### Monitoring (`core/monitoring/`)
- `alerting.py`: System alerting
- `dashboards.py`: Monitoring dashboards
- `performance_monitoring.py`: Performance monitoring

### Performance (`core/performance/`)
- `cache.py`: Performance-related caching
- `database_optimization.py`: Database performance optimization
- `monitoring.py`: Performance monitoring utilities

### Scalability (`core/scalability/`)
- `load_balancing.py`: Load balancing utilities

### Security (`core/security/`)
- `encryption.py`: Security encryption utilities

### API (`core/api/`)
- Complete REST API implementation with versioning, throttling, and documentation

### Integrations (`core/integrations/`)
- Third-party service integrations (email, SMS, payment gateways, cloud storage)

### WebHooks (`core/webhooks/`)
- Webhook system for external integrations

### WebSockets (`core/websockets/`)
- Real-time communication via WebSockets

### Tests (`core/tests/`)
- All test files organized in one location

## Core Files (Root Level)
The following files remain in the core root directory as they are central to the Django app:

- `__init__.py`: Package initialization
- `admin.py`: Django admin configuration
- `apps.py`: Django app configuration
- `models.py`: Core models
- `views.py`: Core views
- `urls.py`: URL patterns
- `utils.py`: General utilities
- `middleware.py`: Core middleware
- `permissions.py`: Permission classes
- `access_control.py`: Access control utilities
- `context_processors.py`: Template context processors
- `mixins.py`: View mixins
- `tasks.py`: Celery tasks
- `celery.py`: Celery configuration
- `consumers.py`: WebSocket consumers
- `routing.py`: WebSocket routing
- `logging_config.py`: Logging configuration
- `tests.py`: Core tests

## Benefits of This Organization

1. **Better Separation of Concerns**: Related functionality is grouped together
2. **Easier Navigation**: Developers can quickly find relevant code
3. **Improved Maintainability**: Changes to specific features are isolated
4. **Cleaner Imports**: More descriptive import paths
5. **Scalability**: Easy to add new modules without cluttering the root directory

## Import Path Changes

After this reorganization, import paths have changed. For example:
- `from core.auth_utils import ...` → `from core.authentication.utils.auth_utils import ...`
- `from core.audit_system import ...` → `from core.audit.audit_system import ...`
- `from core.cache import ...` → `from core.cache.cache import ...`
- `from core.calendar_models import ...` → `from core.calendar.calendar_models import ...`
- `from core.localization import ...` → `from core.localization.localization import ...`

## Status

✅ **Reorganization Complete**: All files have been moved to appropriate directories
✅ **Django Check Passes**: `python manage.py check` runs without errors
✅ **Import Paths Fixed**: Critical import statements have been updated
✅ **Calendar Models Active**: Calendar functionality is being used in migrations, tests, and template tags

## Calendar Models Usage

The calendar models are actively used throughout the system:
- **Database Schema**: Part of core migrations (migration 0003)
- **Template Tags**: Hijri calendar functionality in localization tags
- **Tests**: Comprehensive test coverage in `core/tests/test_calendar.py`
- **Localization**: Calendar preferences in localization views
- **Reports**: Referenced in reports app migrations

The calendar system provides:
- Hijri calendar support
- Calendar event management
- User calendar preferences
- Event attendees and reminders
- Multi-calendar system support (Gregorian/Hijri)

Make sure to update any imports in your codebase accordingly.