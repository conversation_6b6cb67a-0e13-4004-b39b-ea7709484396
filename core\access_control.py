"""
Role-based access control system for School ERP
"""
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth import get_user_model
from django.core.exceptions import PermissionDenied
from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from functools import wraps
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


class RoleManager:
    """
    Manage roles and permissions for the school system
    """
    
    # Predefined roles with their permissions
    ROLE_DEFINITIONS = {
        'SUPER_ADMIN': {
            'name': _('Super Administrator'),
            'description': _('Full system access across all schools'),
            'permissions': ['*'],  # All permissions
            'is_school_specific': False
        },
        'SCHOOL_ADMIN': {
            'name': _('School Administrator'),
            'description': _('Full access within school'),
            'permissions': [
                'add_*', 'change_*', 'delete_*', 'view_*',
                'manage_users', 'manage_roles', 'view_reports',
                'export_data', 'backup_data'
            ],
            'is_school_specific': True
        },
        'PRINCIPAL': {
            'name': _('Principal'),
            'description': _('School leadership with broad access'),
            'permissions': [
                'view_*', 'change_student', 'change_teacher', 'change_class',
                'view_reports', 'manage_academics', 'manage_discipline'
            ],
            'is_school_specific': True
        },
        'VICE_PRINCIPAL': {
            'name': _('Vice Principal'),
            'description': _('Assistant leadership role'),
            'permissions': [
                'view_*', 'change_student', 'change_teacher',
                'view_reports', 'manage_academics'
            ],
            'is_school_specific': True
        },
        'HEAD_TEACHER': {
            'name': _('Head Teacher'),
            'description': _('Department head with teaching responsibilities'),
            'permissions': [
                'view_student', 'change_student', 'view_teacher', 'change_teacher',
                'view_class', 'change_class', 'manage_grades', 'view_reports'
            ],
            'is_school_specific': True
        },
        'TEACHER': {
            'name': _('Teacher'),
            'description': _('Teaching staff with student management'),
            'permissions': [
                'view_student', 'change_student', 'view_class',
                'manage_grades', 'manage_attendance', 'view_own_reports'
            ],
            'is_school_specific': True
        },
        'SUBSTITUTE_TEACHER': {
            'name': _('Substitute Teacher'),
            'description': _('Temporary teaching staff'),
            'permissions': [
                'view_student', 'view_class', 'manage_attendance'
            ],
            'is_school_specific': True
        },
        'REGISTRAR': {
            'name': _('Registrar'),
            'description': _('Student registration and records'),
            'permissions': [
                'add_student', 'change_student', 'view_student',
                'manage_enrollment', 'view_academic_records'
            ],
            'is_school_specific': True
        },
        'ACADEMIC_COORDINATOR': {
            'name': _('Academic Coordinator'),
            'description': _('Academic program management'),
            'permissions': [
                'view_student', 'view_teacher', 'view_class',
                'manage_curriculum', 'manage_schedule', 'view_reports'
            ],
            'is_school_specific': True
        },
        'COUNSELOR': {
            'name': _('Counselor'),
            'description': _('Student guidance and counseling'),
            'permissions': [
                'view_student', 'change_student', 'view_discipline',
                'manage_counseling', 'view_student_reports'
            ],
            'is_school_specific': True
        },
        'LIBRARIAN': {
            'name': _('Librarian'),
            'description': _('Library management'),
            'permissions': [
                'view_student', 'manage_library', 'manage_books',
                'manage_borrowing', 'view_library_reports'
            ],
            'is_school_specific': True
        },
        'FINANCE_MANAGER': {
            'name': _('Finance Manager'),
            'description': _('Financial operations management'),
            'permissions': [
                'view_student', 'manage_finance', 'manage_fees',
                'manage_payments', 'view_financial_reports', 'export_financial_data'
            ],
            'is_school_specific': True
        },
        'ACCOUNTANT': {
            'name': _('Accountant'),
            'description': _('Accounting and bookkeeping'),
            'permissions': [
                'view_student', 'view_finance', 'manage_payments',
                'view_financial_reports'
            ],
            'is_school_specific': True
        },
        'CASHIER': {
            'name': _('Cashier'),
            'description': _('Payment processing'),
            'permissions': [
                'view_student', 'process_payments', 'view_payment_reports'
            ],
            'is_school_specific': True
        },
        'HR_MANAGER': {
            'name': _('HR Manager'),
            'description': _('Human resources management'),
            'permissions': [
                'manage_employees', 'manage_payroll', 'manage_leave',
                'view_hr_reports', 'manage_recruitment'
            ],
            'is_school_specific': True
        },
        'HR_ASSISTANT': {
            'name': _('HR Assistant'),
            'description': _('HR support functions'),
            'permissions': [
                'view_employees', 'manage_leave', 'view_hr_reports'
            ],
            'is_school_specific': True
        },
        'IT_ADMIN': {
            'name': _('IT Administrator'),
            'description': _('Technical system management'),
            'permissions': [
                'manage_system', 'manage_users', 'view_system_logs',
                'backup_data', 'manage_security'
            ],
            'is_school_specific': True
        },
        'NURSE': {
            'name': _('Nurse'),
            'description': _('Health and medical services'),
            'permissions': [
                'view_student', 'manage_health', 'view_medical_records',
                'manage_medications', 'view_health_reports'
            ],
            'is_school_specific': True
        },
        'SECURITY_GUARD': {
            'name': _('Security Guard'),
            'description': _('Campus security'),
            'permissions': [
                'view_student', 'manage_visitors', 'view_security_logs'
            ],
            'is_school_specific': True
        },
        'MAINTENANCE': {
            'name': _('Maintenance Staff'),
            'description': _('Facility maintenance'),
            'permissions': [
                'manage_inventory', 'view_maintenance_requests',
                'manage_assets'
            ],
            'is_school_specific': True
        },
        'TRANSPORT_MANAGER': {
            'name': _('Transport Manager'),
            'description': _('Transportation services'),
            'permissions': [
                'view_student', 'manage_transportation', 'manage_routes',
                'manage_vehicles', 'view_transport_reports'
            ],
            'is_school_specific': True
        },
        'DRIVER': {
            'name': _('Driver'),
            'description': _('Vehicle operation'),
            'permissions': [
                'view_student', 'view_routes', 'manage_attendance'
            ],
            'is_school_specific': True
        },
        'PARENT': {
            'name': _('Parent/Guardian'),
            'description': _('Student parent or guardian'),
            'permissions': [
                'view_own_children', 'view_child_grades', 'view_child_attendance',
                'communicate_teachers', 'view_child_reports'
            ],
            'is_school_specific': True
        },
        'STUDENT': {
            'name': _('Student'),
            'description': _('Enrolled student'),
            'permissions': [
                'view_own_profile', 'view_own_grades', 'view_own_attendance',
                'view_assignments', 'submit_assignments'
            ],
            'is_school_specific': True
        },
        'GUEST': {
            'name': _('Guest'),
            'description': _('Limited access visitor'),
            'permissions': [
                'view_public_info'
            ],
            'is_school_specific': True
        }
    }
    
    def __init__(self, school=None):
        self.school = school
    
    def create_role(self, role_code, school=None):
        """
        Create a role with predefined permissions
        
        Args:
            role_code: Role code from ROLE_DEFINITIONS
            school: School instance (for school-specific roles)
            
        Returns:
            Group instance representing the role
        """
        if role_code not in self.ROLE_DEFINITIONS:
            raise ValueError(f"Unknown role code: {role_code}")
        
        role_def = self.ROLE_DEFINITIONS[role_code]
        
        # Create group name
        if role_def['is_school_specific'] and school:
            group_name = f"{school.code}_{role_code}"
        else:
            group_name = role_code
        
        # Create or get group
        group, created = Group.objects.get_or_create(
            name=group_name,
            defaults={'name': group_name}
        )
        
        if created:
            logger.info(f"Created role: {group_name}")
        
        # Assign permissions
        self._assign_permissions_to_role(group, role_def['permissions'], school)
        
        return group
    
    def _assign_permissions_to_role(self, group, permission_patterns, school=None):
        """
        Assign permissions to role based on patterns
        
        Args:
            group: Group instance
            permission_patterns: List of permission patterns
            school: School instance for context
        """
        permissions_to_add = []
        
        for pattern in permission_patterns:
            if pattern == '*':
                # All permissions
                permissions_to_add.extend(Permission.objects.all())
            elif pattern.endswith('_*'):
                # Action-based permissions (e.g., 'add_*', 'view_*')
                action = pattern[:-2]
                permissions_to_add.extend(
                    Permission.objects.filter(codename__startswith=f"{action}_")
                )
            elif pattern.startswith('manage_'):
                # Custom management permissions
                permissions_to_add.extend(
                    self._get_management_permissions(pattern, school)
                )
            else:
                # Specific permission
                try:
                    perm = Permission.objects.get(codename=pattern)
                    permissions_to_add.append(perm)
                except Permission.DoesNotExist:
                    logger.warning(f"Permission not found: {pattern}")
        
        # Remove duplicates and assign
        unique_permissions = list(set(permissions_to_add))
        group.permissions.set(unique_permissions)
        
        logger.info(f"Assigned {len(unique_permissions)} permissions to {group.name}")
    
    def _get_management_permissions(self, management_type, school=None):
        """
        Get permissions for management types
        
        Args:
            management_type: Type of management (e.g., 'manage_students')
            school: School context
            
        Returns:
            List of Permission objects
        """
        permission_map = {
            'manage_users': ['add_user', 'change_user', 'delete_user', 'view_user'],
            'manage_roles': ['add_group', 'change_group', 'delete_group', 'view_group'],
            'manage_students': ['add_student', 'change_student', 'delete_student', 'view_student'],
            'manage_teachers': ['add_teacher', 'change_teacher', 'delete_teacher', 'view_teacher'],
            'manage_academics': ['add_class', 'change_class', 'add_subject', 'change_subject'],
            'manage_grades': ['add_grade', 'change_grade', 'view_grade'],
            'manage_attendance': ['add_attendance', 'change_attendance', 'view_attendance'],
            'manage_finance': ['add_payment', 'change_payment', 'view_payment', 'add_fee', 'change_fee'],
            'manage_library': ['add_book', 'change_book', 'add_borrowing', 'change_borrowing'],
            'manage_health': ['add_healthrecord', 'change_healthrecord', 'view_healthrecord'],
            'manage_transportation': ['add_route', 'change_route', 'add_vehicle', 'change_vehicle'],
            'manage_inventory': ['add_asset', 'change_asset', 'add_inventory', 'change_inventory'],
            'manage_system': ['add_auditlog', 'view_auditlog', 'change_settings'],
        }
        
        permissions = []
        codenames = permission_map.get(management_type, [])
        
        for codename in codenames:
            try:
                perm = Permission.objects.get(codename=codename)
                permissions.append(perm)
            except Permission.DoesNotExist:
                logger.warning(f"Permission not found: {codename}")
        
        return permissions
    
    def assign_role_to_user(self, user, role_code, school=None):
        """
        Assign role to user
        
        Args:
            user: User instance
            role_code: Role code
            school: School instance
        """
        role_group = self.create_role(role_code, school)
        user.groups.add(role_group)
        
        logger.info(f"Assigned role {role_code} to user {user.username}")
    
    def remove_role_from_user(self, user, role_code, school=None):
        """
        Remove role from user
        
        Args:
            user: User instance
            role_code: Role code
            school: School instance
        """
        role_def = self.ROLE_DEFINITIONS.get(role_code)
        if not role_def:
            return
        
        if role_def['is_school_specific'] and school:
            group_name = f"{school.code}_{role_code}"
        else:
            group_name = role_code
        
        try:
            role_group = Group.objects.get(name=group_name)
            user.groups.remove(role_group)
            logger.info(f"Removed role {role_code} from user {user.username}")
        except Group.DoesNotExist:
            logger.warning(f"Role group not found: {group_name}")
    
    def get_user_roles(self, user, school=None):
        """
        Get all roles for a user
        
        Args:
            user: User instance
            school: School instance (optional filter)
            
        Returns:
            List of role codes
        """
        roles = []
        
        for group in user.groups.all():
            # Check if it's a school-specific role
            if school and group.name.startswith(f"{school.code}_"):
                role_code = group.name.replace(f"{school.code}_", "")
                if role_code in self.ROLE_DEFINITIONS:
                    roles.append(role_code)
            elif not school and '_' not in group.name:
                # Global roles
                if group.name in self.ROLE_DEFINITIONS:
                    roles.append(group.name)
        
        return roles
    
    def user_has_role(self, user, role_code, school=None):
        """
        Check if user has specific role
        
        Args:
            user: User instance
            role_code: Role code to check
            school: School instance
            
        Returns:
            Boolean
        """
        return role_code in self.get_user_roles(user, school)
    
    def get_users_with_role(self, role_code, school=None):
        """
        Get all users with specific role
        
        Args:
            role_code: Role code
            school: School instance
            
        Returns:
            QuerySet of User objects
        """
        role_def = self.ROLE_DEFINITIONS.get(role_code)
        if not role_def:
            return User.objects.none()
        
        if role_def['is_school_specific'] and school:
            group_name = f"{school.code}_{role_code}"
        else:
            group_name = role_code
        
        try:
            role_group = Group.objects.get(name=group_name)
            return User.objects.filter(groups=role_group)
        except Group.DoesNotExist:
            return User.objects.none()


class PermissionChecker:
    """
    Check permissions for users and resources
    """
    
    def __init__(self, user, school=None):
        self.user = user
        self.school = school
    
    def has_permission(self, permission_code, obj=None):
        """
        Check if user has specific permission
        
        Args:
            permission_code: Permission codename
            obj: Object to check permission against (optional)
            
        Returns:
            Boolean
        """
        # Superuser has all permissions
        if self.user.is_superuser:
            return True
        
        # Check direct permission
        if self.user.has_perm(permission_code, obj):
            return True
        
        # Check school-specific permissions
        if self.school:
            return self._check_school_permission(permission_code, obj)
        
        return False
    
    def _check_school_permission(self, permission_code, obj=None):
        """
        Check school-specific permission
        
        Args:
            permission_code: Permission codename
            obj: Object to check permission against
            
        Returns:
            Boolean
        """
        # Check if object belongs to user's school
        if obj and hasattr(obj, 'school') and obj.school != self.school:
            return False
        
        # Check user's roles in this school
        role_manager = RoleManager(self.school)
        user_roles = role_manager.get_user_roles(self.user, self.school)
        
        # Check if any role grants this permission
        for role_code in user_roles:
            role_def = role_manager.ROLE_DEFINITIONS.get(role_code)
            if role_def and self._role_has_permission(role_def, permission_code):
                return True
        
        return False
    
    def _role_has_permission(self, role_def, permission_code):
        """
        Check if role definition includes permission
        
        Args:
            role_def: Role definition dictionary
            permission_code: Permission codename
            
        Returns:
            Boolean
        """
        permissions = role_def.get('permissions', [])
        
        for pattern in permissions:
            if pattern == '*':
                return True
            elif pattern == permission_code:
                return True
            elif pattern.endswith('_*') and permission_code.startswith(pattern[:-1]):
                return True
        
        return False
    
    def can_access_resource(self, resource_type, action='view', obj=None):
        """
        Check if user can access resource
        
        Args:
            resource_type: Type of resource (e.g., 'student', 'teacher')
            action: Action to perform ('view', 'add', 'change', 'delete')
            obj: Specific object instance
            
        Returns:
            Boolean
        """
        permission_code = f"{action}_{resource_type}"
        return self.has_permission(permission_code, obj)
    
    def filter_queryset(self, queryset, action='view'):
        """
        Filter queryset based on user permissions
        
        Args:
            queryset: Django QuerySet
            action: Action being performed
            
        Returns:
            Filtered QuerySet
        """
        # Superuser sees everything
        if self.user.is_superuser:
            return queryset
        
        # Filter by school if applicable
        if self.school and hasattr(queryset.model, 'school'):
            queryset = queryset.filter(school=self.school)
        
        # Apply role-based filtering
        return self._apply_role_filters(queryset, action)
    
    def _apply_role_filters(self, queryset, action):
        """
        Apply role-based filters to queryset
        
        Args:
            queryset: Django QuerySet
            action: Action being performed
            
        Returns:
            Filtered QuerySet
        """
        model_name = queryset.model._meta.model_name
        
        # Get user roles
        role_manager = RoleManager(self.school)
        user_roles = role_manager.get_user_roles(self.user, self.school)
        
        # Apply filters based on roles
        if 'PARENT' in user_roles:
            # Parents can only see their children's data
            if model_name == 'student':
                # Assuming parent-child relationship exists
                if hasattr(self.user, 'parent_profile'):
                    return queryset.filter(parent=self.user.parent_profile)
            return queryset.none()
        
        elif 'STUDENT' in user_roles:
            # Students can only see their own data
            if model_name == 'student':
                if hasattr(self.user, 'student_profile'):
                    return queryset.filter(id=self.user.student_profile.id)
            return queryset.none()
        
        elif 'TEACHER' in user_roles:
            # Teachers can see students in their classes
            if model_name == 'student':
                if hasattr(self.user, 'teacher_profile'):
                    # Assuming teacher-class relationship exists
                    teacher_classes = self.user.teacher_profile.classes.all()
                    return queryset.filter(current_class__in=teacher_classes)
        
        # Default: return full queryset for admin roles
        return queryset


class ResourceAccessControl:
    """
    Control access to specific resources
    """
    
    def __init__(self, user, school=None):
        self.user = user
        self.school = school
        self.permission_checker = PermissionChecker(user, school)
    
    def check_student_access(self, student, action='view'):
        """
        Check access to student record
        
        Args:
            student: Student instance
            action: Action to perform
            
        Returns:
            Boolean
        """
        # Check basic permission
        if not self.permission_checker.can_access_resource('student', action, student):
            return False
        
        # Additional checks based on user role
        role_manager = RoleManager(self.school)
        user_roles = role_manager.get_user_roles(self.user, self.school)
        
        if 'PARENT' in user_roles:
            # Parents can only access their children
            if hasattr(self.user, 'parent_profile'):
                return student in self.user.parent_profile.children.all()
            return False
        
        elif 'STUDENT' in user_roles:
            # Students can only access their own record
            if hasattr(self.user, 'student_profile'):
                return student == self.user.student_profile
            return False
        
        elif 'TEACHER' in user_roles:
            # Teachers can access students in their classes
            if hasattr(self.user, 'teacher_profile'):
                teacher_classes = self.user.teacher_profile.classes.all()
                return student.current_class in teacher_classes
            return False
        
        # Admin roles have full access
        return True
    
    def check_financial_access(self, financial_record, action='view'):
        """
        Check access to financial records
        
        Args:
            financial_record: Financial record instance
            action: Action to perform
            
        Returns:
            Boolean
        """
        # Financial data requires special permissions
        if not self.permission_checker.can_access_resource('payment', action, financial_record):
            return False
        
        role_manager = RoleManager(self.school)
        user_roles = role_manager.get_user_roles(self.user, self.school)
        
        # Only finance roles can access financial data
        finance_roles = ['FINANCE_MANAGER', 'ACCOUNTANT', 'CASHIER', 'SCHOOL_ADMIN']
        
        return any(role in user_roles for role in finance_roles)
    
    def check_grade_access(self, grade, action='view'):
        """
        Check access to grade records
        
        Args:
            grade: Grade instance
            action: Action to perform
            
        Returns:
            Boolean
        """
        if not self.permission_checker.can_access_resource('grade', action, grade):
            return False
        
        role_manager = RoleManager(self.school)
        user_roles = role_manager.get_user_roles(self.user, self.school)
        
        if 'PARENT' in user_roles:
            # Parents can view their children's grades
            if hasattr(self.user, 'parent_profile') and hasattr(grade, 'student'):
                return grade.student in self.user.parent_profile.children.all()
            return False
        
        elif 'STUDENT' in user_roles:
            # Students can view their own grades
            if hasattr(self.user, 'student_profile') and hasattr(grade, 'student'):
                return grade.student == self.user.student_profile
            return False
        
        elif 'TEACHER' in user_roles:
            # Teachers can manage grades for their subjects/classes
            if hasattr(self.user, 'teacher_profile') and hasattr(grade, 'subject'):
                teacher_subjects = self.user.teacher_profile.subjects.all()
                return grade.subject in teacher_subjects
            return False
        
        return True


# Decorators for access control
def require_role(role_code, school_param='school'):
    """
    Decorator to require specific role
    
    Args:
        role_code: Required role code
        school_param: Parameter name for school in view
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                raise PermissionDenied("Authentication required")
            
            # Get school from view parameters
            school = kwargs.get(school_param) or getattr(request, 'school', None)
            
            # Check role
            role_manager = RoleManager(school)
            if not role_manager.user_has_role(request.user, role_code, school):
                raise PermissionDenied(f"Role {role_code} required")
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def require_permission(permission_code, obj_param=None):
    """
    Decorator to require specific permission
    
    Args:
        permission_code: Required permission codename
        obj_param: Parameter name for object in view
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                raise PermissionDenied("Authentication required")
            
            # Get object if specified
            obj = kwargs.get(obj_param) if obj_param else None
            school = getattr(request, 'school', None)
            
            # Check permission
            permission_checker = PermissionChecker(request.user, school)
            if not permission_checker.has_permission(permission_code, obj):
                raise PermissionDenied(f"Permission {permission_code} required")
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def school_admin_required(view_func):
    """
    Decorator to require school admin role
    """
    return require_role('SCHOOL_ADMIN')(view_func)


def teacher_required(view_func):
    """
    Decorator to require teacher role or higher
    """
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            raise PermissionDenied("Authentication required")
        
        school = getattr(request, 'school', None)
        role_manager = RoleManager(school)
        user_roles = role_manager.get_user_roles(request.user, school)
        
        teacher_roles = ['TEACHER', 'HEAD_TEACHER', 'VICE_PRINCIPAL', 'PRINCIPAL', 'SCHOOL_ADMIN']
        
        if not any(role in user_roles for role in teacher_roles):
            raise PermissionDenied("Teacher role or higher required")
        
        return view_func(request, *args, **kwargs)
    
    return wrapper


# Utility functions
def get_role_manager(school=None):
    """
    Get role manager instance
    
    Args:
        school: School instance
        
    Returns:
        RoleManager instance
    """
    return RoleManager(school)


def get_permission_checker(user, school=None):
    """
    Get permission checker instance
    
    Args:
        user: User instance
        school: School instance
        
    Returns:
        PermissionChecker instance
    """
    return PermissionChecker(user, school)


def get_resource_access_control(user, school=None):
    """
    Get resource access control instance
    
    Args:
        user: User instance
        school: School instance
        
    Returns:
        ResourceAccessControl instance
    """
    return ResourceAccessControl(user, school)


def setup_default_roles(school):
    """
    Setup default roles for a school
    
    Args:
        school: School instance
    """
    role_manager = RoleManager(school)
    
    # Create essential roles
    essential_roles = [
        'SCHOOL_ADMIN', 'PRINCIPAL', 'TEACHER', 'STUDENT', 'PARENT',
        'FINANCE_MANAGER', 'LIBRARIAN', 'REGISTRAR'
    ]
    
    for role_code in essential_roles:
        try:
            role_manager.create_role(role_code, school)
            logger.info(f"Created role {role_code} for school {school.name}")
        except Exception as e:
            logger.error(f"Failed to create role {role_code}: {e}")


def assign_user_role(user, role_code, school):
    """
    Assign role to user
    
    Args:
        user: User instance
        role_code: Role code
        school: School instance
    """
    role_manager = RoleManager(school)
    role_manager.assign_role_to_user(user, role_code, school)