"""
API analytics and monitoring system for School ERP
"""

from django.core.cache import cache
from django.utils import timezone
from django.db import models
from django.contrib.auth import get_user_model
from datetime import datetime, timedelta
import json
import logging
from collections import defaultdict, Counter

logger = logging.getLogger(__name__)
User = get_user_model()


class APIAnalyticsCollector:
    """
    Collects and stores API usage analytics
    """
    
    def __init__(self):
        self.cache_prefix = 'api_analytics'
        self.daily_key = f'{self.cache_prefix}_daily'
        self.hourly_key = f'{self.cache_prefix}_hourly'
        self.realtime_key = f'{self.cache_prefix}_realtime'
    
    def record_request(self, request, response, view, duration_ms):
        """
        Record API request analytics
        """
        now = timezone.now()
        
        # Basic request data
        request_data = {
            'timestamp': now.isoformat(),
            'method': request.method,
            'path': request.path,
            'status_code': response.status_code,
            'duration_ms': duration_ms,
            'user_id': request.user.id if request.user.is_authenticated else None,
            'user_role': getattr(request.user, 'role', None) if request.user.is_authenticated else None,
            'ip_address': self._get_client_ip(request),
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'api_version': getattr(request, 'version', 'v1'),
            'view_name': view.__class__.__name__ if view else None,
            'action': getattr(view, 'action', None) if view else None
        }
        
        # Record in different time buckets
        self._record_daily(request_data)
        self._record_hourly(request_data)
        self._record_realtime(request_data)
        
        # Update counters
        self._update_counters(request_data)
    
    def _get_client_ip(self, request):
        """
        Get client IP address
        """
        xff = request.META.get('HTTP_X_FORWARDED_FOR')
        if xff:
            return xff.split(',')[0].strip()
        return request.META.get('REMOTE_ADDR', '')
    
    def _record_daily(self, request_data):
        """
        Record daily analytics
        """
        date_key = datetime.now().strftime('%Y-%m-%d')
        cache_key = f'{self.daily_key}_{date_key}'
        
        daily_data = cache.get(cache_key, {
            'date': date_key,
            'total_requests': 0,
            'unique_users': set(),
            'unique_ips': set(),
            'endpoints': defaultdict(int),
            'methods': defaultdict(int),
            'status_codes': defaultdict(int),
            'user_roles': defaultdict(int),
            'api_versions': defaultdict(int),
            'response_times': [],
            'error_requests': []
        })
        
        # Update counters
        daily_data['total_requests'] += 1
        daily_data['endpoints'][request_data['path']] += 1
        daily_data['methods'][request_data['method']] += 1
        daily_data['status_codes'][str(request_data['status_code'])] += 1
        daily_data['api_versions'][request_data['api_version']] += 1
        
        if request_data['user_id']:
            daily_data['unique_users'].add(request_data['user_id'])
        if request_data['user_role']:
            daily_data['user_roles'][request_data['user_role']] += 1
        if request_data['ip_address']:
            daily_data['unique_ips'].add(request_data['ip_address'])
        
        daily_data['response_times'].append(request_data['duration_ms'])
        
        # Record errors
        if request_data['status_code'] >= 400:
            daily_data['error_requests'].append({
                'timestamp': request_data['timestamp'],
                'path': request_data['path'],
                'method': request_data['method'],
                'status_code': request_data['status_code'],
                'user_id': request_data['user_id'],
                'ip_address': request_data['ip_address']
            })
        
        # Convert sets to lists for JSON serialization
        daily_data['unique_users'] = list(daily_data['unique_users'])
        daily_data['unique_ips'] = list(daily_data['unique_ips'])
        
        # Cache for 25 hours to ensure we don't lose data
        cache.set(cache_key, daily_data, 90000)
    
    def _record_hourly(self, request_data):
        """
        Record hourly analytics
        """
        hour_key = datetime.now().strftime('%Y-%m-%d_%H')
        cache_key = f'{self.hourly_key}_{hour_key}'
        
        hourly_data = cache.get(cache_key, {
            'hour': hour_key,
            'total_requests': 0,
            'unique_users': set(),
            'endpoints': defaultdict(int),
            'response_times': [],
            'peak_minute': {'minute': 0, 'requests': 0}
        })
        
        hourly_data['total_requests'] += 1
        if request_data['user_id']:
            hourly_data['unique_users'].add(request_data['user_id'])
        hourly_data['endpoints'][request_data['path']] += 1
        hourly_data['response_times'].append(request_data['duration_ms'])
        
        # Track peak minute
        current_minute = datetime.now().minute
        minute_key = f'{cache_key}_minute_{current_minute}'
        minute_requests = cache.get(minute_key, 0) + 1
        cache.set(minute_key, minute_requests, 3600)
        
        if minute_requests > hourly_data['peak_minute']['requests']:
            hourly_data['peak_minute'] = {
                'minute': current_minute,
                'requests': minute_requests
            }
        
        # Convert sets to lists
        hourly_data['unique_users'] = list(hourly_data['unique_users'])
        
        # Cache for 2 hours
        cache.set(cache_key, hourly_data, 7200)
    
    def _record_realtime(self, request_data):
        """
        Record real-time analytics (last 5 minutes)
        """
        now = datetime.now()
        minute_key = now.strftime('%Y-%m-%d_%H:%M')
        cache_key = f'{self.realtime_key}_{minute_key}'
        
        realtime_data = cache.get(cache_key, {
            'minute': minute_key,
            'requests': [],
            'total_requests': 0,
            'avg_response_time': 0
        })
        
        realtime_data['requests'].append({
            'timestamp': request_data['timestamp'],
            'method': request_data['method'],
            'path': request_data['path'],
            'status_code': request_data['status_code'],
            'duration_ms': request_data['duration_ms']
        })
        
        realtime_data['total_requests'] += 1
        
        # Calculate average response time
        response_times = [req['duration_ms'] for req in realtime_data['requests']]
        realtime_data['avg_response_time'] = sum(response_times) / len(response_times)
        
        # Keep only last 100 requests per minute
        if len(realtime_data['requests']) > 100:
            realtime_data['requests'] = realtime_data['requests'][-100:]
        
        # Cache for 10 minutes
        cache.set(cache_key, realtime_data, 600)
    
    def _update_counters(self, request_data):
        """
        Update global counters
        """
        # Total requests counter
        total_key = f'{self.cache_prefix}_total_requests'
        total_requests = cache.get(total_key, 0) + 1
        cache.set(total_key, total_requests, None)  # Never expire
        
        # Popular endpoints
        endpoint_key = f'{self.cache_prefix}_popular_endpoints'
        popular_endpoints = cache.get(endpoint_key, defaultdict(int))
        popular_endpoints[request_data['path']] += 1
        cache.set(endpoint_key, dict(popular_endpoints), 86400)  # 24 hours
        
        # Active users (last hour)
        if request_data['user_id']:
            active_users_key = f'{self.cache_prefix}_active_users'
            active_users = cache.get(active_users_key, set())
            active_users.add(request_data['user_id'])
            cache.set(active_users_key, active_users, 3600)  # 1 hour
    
    def get_daily_analytics(self, date=None):
        """
        Get daily analytics data
        """
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')
        
        cache_key = f'{self.daily_key}_{date}'
        return cache.get(cache_key, {})
    
    def get_hourly_analytics(self, hour=None):
        """
        Get hourly analytics data
        """
        if hour is None:
            hour = datetime.now().strftime('%Y-%m-%d_%H')
        
        cache_key = f'{self.hourly_key}_{hour}'
        return cache.get(cache_key, {})
    
    def get_realtime_analytics(self):
        """
        Get real-time analytics (last 5 minutes)
        """
        now = datetime.now()
        realtime_data = {
            'current_minute': 0,
            'last_5_minutes': [],
            'total_requests_5min': 0,
            'avg_response_time_5min': 0,
            'active_users_now': 0
        }
        
        # Get data for last 5 minutes
        for i in range(5):
            minute = now - timedelta(minutes=i)
            minute_key = minute.strftime('%Y-%m-%d_%H:%M')
            cache_key = f'{self.realtime_key}_{minute_key}'
            minute_data = cache.get(cache_key, {'total_requests': 0, 'avg_response_time': 0})
            
            realtime_data['last_5_minutes'].append({
                'minute': minute_key,
                'requests': minute_data.get('total_requests', 0),
                'avg_response_time': minute_data.get('avg_response_time', 0)
            })
            
            realtime_data['total_requests_5min'] += minute_data.get('total_requests', 0)
        
        # Calculate average response time for 5 minutes
        response_times = [m['avg_response_time'] for m in realtime_data['last_5_minutes'] if m['avg_response_time'] > 0]
        if response_times:
            realtime_data['avg_response_time_5min'] = sum(response_times) / len(response_times)
        
        # Get active users
        active_users_key = f'{self.cache_prefix}_active_users'
        active_users = cache.get(active_users_key, set())
        realtime_data['active_users_now'] = len(active_users)
        
        return realtime_data
    
    def get_summary_stats(self):
        """
        Get summary statistics
        """
        # Total requests
        total_key = f'{self.cache_prefix}_total_requests'
        total_requests = cache.get(total_key, 0)
        
        # Popular endpoints
        endpoint_key = f'{self.cache_prefix}_popular_endpoints'
        popular_endpoints = cache.get(endpoint_key, {})
        
        # Today's data
        today_data = self.get_daily_analytics()
        
        # Active users
        active_users_key = f'{self.cache_prefix}_active_users'
        active_users = cache.get(active_users_key, set())
        
        return {
            'total_requests_all_time': total_requests,
            'requests_today': today_data.get('total_requests', 0),
            'unique_users_today': len(today_data.get('unique_users', [])),
            'active_users_now': len(active_users),
            'popular_endpoints': dict(sorted(popular_endpoints.items(), key=lambda x: x[1], reverse=True)[:10]),
            'avg_response_time_today': sum(today_data.get('response_times', [0])) / max(len(today_data.get('response_times', [1])), 1),
            'error_rate_today': len(today_data.get('error_requests', [])) / max(today_data.get('total_requests', 1), 1) * 100
        }


class APIPerformanceMonitor:
    """
    Monitor API performance and detect issues
    """
    
    def __init__(self):
        self.cache_prefix = 'api_performance'
        self.alert_thresholds = {
            'response_time_ms': 5000,  # 5 seconds
            'error_rate_percent': 10,   # 10%
            'requests_per_minute': 1000 # 1000 requests per minute
        }
    
    def check_performance_alerts(self):
        """
        Check for performance issues and generate alerts
        """
        alerts = []
        
        # Check response time
        avg_response_time = self._get_avg_response_time()
        if avg_response_time > self.alert_thresholds['response_time_ms']:
            alerts.append({
                'type': 'high_response_time',
                'message': f'Average response time is {avg_response_time}ms (threshold: {self.alert_thresholds["response_time_ms"]}ms)',
                'severity': 'warning',
                'timestamp': timezone.now().isoformat()
            })
        
        # Check error rate
        error_rate = self._get_error_rate()
        if error_rate > self.alert_thresholds['error_rate_percent']:
            alerts.append({
                'type': 'high_error_rate',
                'message': f'Error rate is {error_rate:.2f}% (threshold: {self.alert_thresholds["error_rate_percent"]}%)',
                'severity': 'critical',
                'timestamp': timezone.now().isoformat()
            })
        
        # Check request rate
        request_rate = self._get_request_rate()
        if request_rate > self.alert_thresholds['requests_per_minute']:
            alerts.append({
                'type': 'high_request_rate',
                'message': f'Request rate is {request_rate} requests/minute (threshold: {self.alert_thresholds["requests_per_minute"]})',
                'severity': 'info',
                'timestamp': timezone.now().isoformat()
            })
        
        # Store alerts
        if alerts:
            alerts_key = f'{self.cache_prefix}_alerts'
            existing_alerts = cache.get(alerts_key, [])
            existing_alerts.extend(alerts)
            # Keep only last 100 alerts
            if len(existing_alerts) > 100:
                existing_alerts = existing_alerts[-100:]
            cache.set(alerts_key, existing_alerts, 86400)  # 24 hours
        
        return alerts
    
    def _get_avg_response_time(self):
        """
        Get average response time for last hour
        """
        collector = APIAnalyticsCollector()
        hourly_data = collector.get_hourly_analytics()
        response_times = hourly_data.get('response_times', [])
        
        if response_times:
            return sum(response_times) / len(response_times)
        return 0
    
    def _get_error_rate(self):
        """
        Get error rate for last hour
        """
        collector = APIAnalyticsCollector()
        hourly_data = collector.get_hourly_analytics()
        total_requests = hourly_data.get('total_requests', 0)
        
        if total_requests == 0:
            return 0
        
        # Count 4xx and 5xx status codes from daily data (more comprehensive)
        daily_data = collector.get_daily_analytics()
        status_codes = daily_data.get('status_codes', {})
        
        error_requests = sum(
            count for status, count in status_codes.items()
            if status.startswith('4') or status.startswith('5')
        )
        
        return (error_requests / max(daily_data.get('total_requests', 1), 1)) * 100
    
    def _get_request_rate(self):
        """
        Get current request rate (requests per minute)
        """
        collector = APIAnalyticsCollector()
        realtime_data = collector.get_realtime_analytics()
        
        # Sum requests from last 5 minutes and average
        total_requests_5min = realtime_data.get('total_requests_5min', 0)
        return total_requests_5min / 5  # Average per minute
    
    def get_performance_metrics(self):
        """
        Get comprehensive performance metrics
        """
        return {
            'avg_response_time_ms': self._get_avg_response_time(),
            'error_rate_percent': self._get_error_rate(),
            'requests_per_minute': self._get_request_rate(),
            'alerts': self.get_recent_alerts(),
            'thresholds': self.alert_thresholds
        }
    
    def get_recent_alerts(self):
        """
        Get recent performance alerts
        """
        alerts_key = f'{self.cache_prefix}_alerts'
        alerts = cache.get(alerts_key, [])
        
        # Return only alerts from last 24 hours
        now = timezone.now()
        recent_alerts = []
        
        for alert in alerts:
            alert_time = datetime.fromisoformat(alert['timestamp'].replace('Z', '+00:00'))
            if (now - alert_time).total_seconds() < 86400:  # 24 hours
                recent_alerts.append(alert)
        
        return recent_alerts


class APIUsageReporter:
    """
    Generate API usage reports
    """
    
    def __init__(self):
        self.collector = APIAnalyticsCollector()
    
    def generate_daily_report(self, date=None):
        """
        Generate daily usage report
        """
        daily_data = self.collector.get_daily_analytics(date)
        
        if not daily_data:
            return {'error': 'No data available for the specified date'}
        
        # Calculate metrics
        response_times = daily_data.get('response_times', [])
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        error_requests = daily_data.get('error_requests', [])
        error_rate = (len(error_requests) / max(daily_data.get('total_requests', 1), 1)) * 100
        
        return {
            'date': daily_data.get('date'),
            'summary': {
                'total_requests': daily_data.get('total_requests', 0),
                'unique_users': len(daily_data.get('unique_users', [])),
                'unique_ips': len(daily_data.get('unique_ips', [])),
                'avg_response_time_ms': round(avg_response_time, 2),
                'error_rate_percent': round(error_rate, 2)
            },
            'top_endpoints': dict(sorted(
                daily_data.get('endpoints', {}).items(),
                key=lambda x: x[1], reverse=True
            )[:10]),
            'methods_distribution': dict(daily_data.get('methods', {})),
            'status_codes_distribution': dict(daily_data.get('status_codes', {})),
            'user_roles_distribution': dict(daily_data.get('user_roles', {})),
            'api_versions_distribution': dict(daily_data.get('api_versions', {})),
            'errors': error_requests[:20]  # Top 20 errors
        }
    
    def generate_weekly_report(self, start_date=None):
        """
        Generate weekly usage report
        """
        if start_date is None:
            start_date = datetime.now() - timedelta(days=7)
        
        weekly_data = {
            'period': f"{start_date.strftime('%Y-%m-%d')} to {(start_date + timedelta(days=6)).strftime('%Y-%m-%d')}",
            'daily_breakdown': [],
            'summary': {
                'total_requests': 0,
                'unique_users': set(),
                'avg_response_time_ms': 0,
                'error_rate_percent': 0
            },
            'trends': {}
        }
        
        all_response_times = []
        total_errors = 0
        
        for i in range(7):
            date = start_date + timedelta(days=i)
            date_str = date.strftime('%Y-%m-%d')
            daily_data = self.collector.get_daily_analytics(date_str)
            
            if daily_data:
                response_times = daily_data.get('response_times', [])
                error_count = len(daily_data.get('error_requests', []))
                
                daily_summary = {
                    'date': date_str,
                    'requests': daily_data.get('total_requests', 0),
                    'unique_users': len(daily_data.get('unique_users', [])),
                    'avg_response_time': sum(response_times) / len(response_times) if response_times else 0,
                    'errors': error_count
                }
                
                weekly_data['daily_breakdown'].append(daily_summary)
                weekly_data['summary']['total_requests'] += daily_summary['requests']
                weekly_data['summary']['unique_users'].update(daily_data.get('unique_users', []))
                all_response_times.extend(response_times)
                total_errors += error_count
        
        # Calculate weekly averages
        if all_response_times:
            weekly_data['summary']['avg_response_time_ms'] = sum(all_response_times) / len(all_response_times)
        
        if weekly_data['summary']['total_requests'] > 0:
            weekly_data['summary']['error_rate_percent'] = (total_errors / weekly_data['summary']['total_requests']) * 100
        
        weekly_data['summary']['unique_users'] = len(weekly_data['summary']['unique_users'])
        
        return weekly_data
    
    def export_analytics_data(self, format='json', date_range=None):
        """
        Export analytics data in various formats
        """
        if date_range is None:
            # Default to last 30 days
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
        else:
            start_date, end_date = date_range
        
        export_data = {
            'export_info': {
                'generated_at': timezone.now().isoformat(),
                'date_range': {
                    'start': start_date.strftime('%Y-%m-%d'),
                    'end': end_date.strftime('%Y-%m-%d')
                },
                'format': format
            },
            'data': []
        }
        
        # Collect data for each day in range
        current_date = start_date
        while current_date <= end_date:
            date_str = current_date.strftime('%Y-%m-%d')
            daily_data = self.collector.get_daily_analytics(date_str)
            
            if daily_data:
                export_data['data'].append(daily_data)
            
            current_date += timedelta(days=1)
        
        if format == 'json':
            return json.dumps(export_data, indent=2, default=str)
        elif format == 'csv':
            # Convert to CSV format (simplified)
            csv_lines = ['Date,Total Requests,Unique Users,Avg Response Time,Error Rate']
            
            for daily_data in export_data['data']:
                response_times = daily_data.get('response_times', [])
                avg_response_time = sum(response_times) / len(response_times) if response_times else 0
                error_rate = (len(daily_data.get('error_requests', [])) / max(daily_data.get('total_requests', 1), 1)) * 100
                
                csv_lines.append(f"{daily_data.get('date')},{daily_data.get('total_requests', 0)},{len(daily_data.get('unique_users', []))},{avg_response_time:.2f},{error_rate:.2f}")
            
            return '\n'.join(csv_lines)
        
        return export_data


# Global analytics collector instance
analytics_collector = APIAnalyticsCollector()
performance_monitor = APIPerformanceMonitor()
usage_reporter = APIUsageReporter()