"""
API documentation system for School ERP
"""

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.conf import settings
from django.urls import reverse
from django.utils import timezone
from .versioning import APIVersionInfo, get_api_version_context
from .analytics import analytics_collector
import json


class APIDocumentationGenerator:
    """
    Generate comprehensive API documentation
    """
    
    def __init__(self):
        self.base_info = {
            'title': 'School ERP API',
            'description': 'Comprehensive API for School Enterprise Resource Planning System',
            'version': '2.0.0',
            'contact': {
                'name': 'School ERP Support',
                'email': '<EMAIL>'
            },
            'license': {
                'name': 'MIT License',
                'url': 'https://opensource.org/licenses/MIT'
            }
        }
    
    def generate_openapi_spec(self, request=None):
        """
        Generate OpenAPI 3.0 specification
        """
        base_url = request.build_absolute_uri('/') if request else 'http://localhost:8000/'
        
        spec = {
            'openapi': '3.0.0',
            'info': self.base_info,
            'servers': [
                {
                    'url': f'{base_url}api/v1/',
                    'description': 'API Version 1 (Deprecated)'
                },
                {
                    'url': f'{base_url}api/v2/',
                    'description': 'API Version 2 (Current)'
                }
            ],
            'paths': self._generate_paths(),
            'components': {
                'schemas': self._generate_schemas(),
                'securitySchemes': self._generate_security_schemes(),
                'responses': self._generate_common_responses(),
                'parameters': self._generate_common_parameters()
            },
            'security': [
                {'BearerAuth': []},
                {'ApiKeyAuth': []}
            ],
            'tags': self._generate_tags()
        }
        
        return spec
    
    def _generate_paths(self):
        """
        Generate API paths documentation
        """
        paths = {}
        
        # Authentication endpoints
        paths['/auth/login/'] = {
            'post': {
                'tags': ['Authentication'],
                'summary': 'User login',
                'description': 'Authenticate user and return JWT tokens',
                'requestBody': {
                    'required': True,
                    'content': {
                        'application/json': {
                            'schema': {
                                'type': 'object',
                                'properties': {
                                    'username': {'type': 'string'},
                                    'password': {'type': 'string'}
                                },
                                'required': ['username', 'password']
                            }
                        }
                    }
                },
                'responses': {
                    '200': {
                        'description': 'Login successful',
                        'content': {
                            'application/json': {
                                'schema': {
                                    'type': 'object',
                                    'properties': {
                                        'access': {'type': 'string'},
                                        'refresh': {'type': 'string'},
                                        'user': {'$ref': '#/components/schemas/User'}
                                    }
                                }
                            }
                        }
                    },
                    '401': {'$ref': '#/components/responses/Unauthorized'}
                }
            }
        }
        
        # Students endpoints
        paths['/students/'] = {
            'get': {
                'tags': ['Students'],
                'summary': 'List students',
                'description': 'Get paginated list of students',
                'parameters': [
                    {'$ref': '#/components/parameters/PageParam'},
                    {'$ref': '#/components/parameters/PageSizeParam'},
                    {
                        'name': 'search',
                        'in': 'query',
                        'description': 'Search students by name or ID',
                        'schema': {'type': 'string'}
                    },
                    {
                        'name': 'grade',
                        'in': 'query',
                        'description': 'Filter by grade',
                        'schema': {'type': 'string'}
                    }
                ],
                'responses': {
                    '200': {
                        'description': 'Students list',
                        'content': {
                            'application/json': {
                                'schema': {
                                    'type': 'object',
                                    'properties': {
                                        'count': {'type': 'integer'},
                                        'next': {'type': 'string', 'nullable': True},
                                        'previous': {'type': 'string', 'nullable': True},
                                        'results': {
                                            'type': 'array',
                                            'items': {'$ref': '#/components/schemas/Student'}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            'post': {
                'tags': ['Students'],
                'summary': 'Create student',
                'description': 'Create a new student record',
                'requestBody': {
                    'required': True,
                    'content': {
                        'application/json': {
                            'schema': {'$ref': '#/components/schemas/StudentCreate'}
                        }
                    }
                },
                'responses': {
                    '201': {
                        'description': 'Student created',
                        'content': {
                            'application/json': {
                                'schema': {'$ref': '#/components/schemas/Student'}
                            }
                        }
                    },
                    '400': {'$ref': '#/components/responses/BadRequest'}
                }
            }
        }
        
        # Add more endpoints for other modules
        self._add_academics_endpoints(paths)
        self._add_finance_endpoints(paths)
        self._add_hr_endpoints(paths)
        self._add_inventory_endpoints(paths)
        self._add_transportation_endpoints(paths)
        
        return paths
    
    def _add_academics_endpoints(self, paths):
        """Add academics module endpoints"""
        paths['/academics/courses/'] = {
            'get': {
                'tags': ['Academics'],
                'summary': 'List courses',
                'responses': {
                    '200': {
                        'description': 'Courses list',
                        'content': {
                            'application/json': {
                                'schema': {
                                    'type': 'array',
                                    'items': {'$ref': '#/components/schemas/Course'}
                                }
                            }
                        }
                    }
                }
            }
        }
        
        paths['/academics/grades/'] = {
            'get': {
                'tags': ['Academics'],
                'summary': 'List grades',
                'responses': {
                    '200': {
                        'description': 'Grades list'
                    }
                }
            }
        }
    
    def _add_finance_endpoints(self, paths):
        """Add finance module endpoints"""
        paths['/finance/fees/'] = {
            'get': {
                'tags': ['Finance'],
                'summary': 'List fees',
                'responses': {
                    '200': {
                        'description': 'Fees list'
                    }
                }
            }
        }
        
        paths['/finance/payments/'] = {
            'get': {
                'tags': ['Finance'],
                'summary': 'List payments',
                'responses': {
                    '200': {
                        'description': 'Payments list'
                    }
                }
            }
        }
    
    def _add_hr_endpoints(self, paths):
        """Add HR module endpoints"""
        paths['/hr/employees/'] = {
            'get': {
                'tags': ['Human Resources'],
                'summary': 'List employees',
                'responses': {
                    '200': {
                        'description': 'Employees list'
                    }
                }
            }
        }
    
    def _add_inventory_endpoints(self, paths):
        """Add inventory module endpoints"""
        paths['/inventory/assets/'] = {
            'get': {
                'tags': ['Inventory'],
                'summary': 'List assets',
                'responses': {
                    '200': {
                        'description': 'Assets list'
                    }
                }
            }
        }
    
    def _add_transportation_endpoints(self, paths):
        """Add transportation module endpoints"""
        paths['/transportation/routes/'] = {
            'get': {
                'tags': ['Transportation'],
                'summary': 'List routes',
                'responses': {
                    '200': {
                        'description': 'Routes list'
                    }
                }
            }
        }
    
    def _generate_schemas(self):
        """
        Generate data schemas
        """
        return {
            'User': {
                'type': 'object',
                'properties': {
                    'id': {'type': 'integer'},
                    'username': {'type': 'string'},
                    'email': {'type': 'string', 'format': 'email'},
                    'first_name': {'type': 'string'},
                    'last_name': {'type': 'string'},
                    'role': {'type': 'string', 'enum': ['admin', 'teacher', 'student', 'parent', 'staff']},
                    'is_active': {'type': 'boolean'},
                    'date_joined': {'type': 'string', 'format': 'date-time'}
                }
            },
            'Student': {
                'type': 'object',
                'properties': {
                    'id': {'type': 'integer'},
                    'student_id': {'type': 'string'},
                    'user': {'$ref': '#/components/schemas/User'},
                    'grade': {'type': 'string'},
                    'section': {'type': 'string'},
                    'admission_date': {'type': 'string', 'format': 'date'},
                    'date_of_birth': {'type': 'string', 'format': 'date'},
                    'gender': {'type': 'string', 'enum': ['M', 'F']},
                    'address': {'type': 'string'},
                    'phone': {'type': 'string'},
                    'emergency_contact': {'type': 'string'},
                    'medical_info': {'type': 'string'},
                    'is_active': {'type': 'boolean'}
                }
            },
            'StudentCreate': {
                'type': 'object',
                'required': ['student_id', 'grade', 'admission_date'],
                'properties': {
                    'student_id': {'type': 'string'},
                    'first_name': {'type': 'string'},
                    'last_name': {'type': 'string'},
                    'email': {'type': 'string', 'format': 'email'},
                    'grade': {'type': 'string'},
                    'section': {'type': 'string'},
                    'admission_date': {'type': 'string', 'format': 'date'},
                    'date_of_birth': {'type': 'string', 'format': 'date'},
                    'gender': {'type': 'string', 'enum': ['M', 'F']},
                    'address': {'type': 'string'},
                    'phone': {'type': 'string'},
                    'emergency_contact': {'type': 'string'},
                    'medical_info': {'type': 'string'}
                }
            },
            'Course': {
                'type': 'object',
                'properties': {
                    'id': {'type': 'integer'},
                    'name': {'type': 'string'},
                    'code': {'type': 'string'},
                    'description': {'type': 'string'},
                    'credits': {'type': 'integer'},
                    'grade': {'type': 'string'},
                    'is_active': {'type': 'boolean'}
                }
            },
            'Error': {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string'},
                    'message': {'type': 'string'},
                    'details': {'type': 'object'},
                    'timestamp': {'type': 'string', 'format': 'date-time'}
                }
            },
            'ValidationError': {
                'type': 'object',
                'properties': {
                    'field_errors': {
                        'type': 'object',
                        'additionalProperties': {
                            'type': 'array',
                            'items': {'type': 'string'}
                        }
                    },
                    'non_field_errors': {
                        'type': 'array',
                        'items': {'type': 'string'}
                    }
                }
            }
        }
    
    def _generate_security_schemes(self):
        """
        Generate security schemes
        """
        return {
            'BearerAuth': {
                'type': 'http',
                'scheme': 'bearer',
                'bearerFormat': 'JWT',
                'description': 'JWT token obtained from login endpoint'
            },
            'ApiKeyAuth': {
                'type': 'apiKey',
                'in': 'header',
                'name': 'X-API-Key',
                'description': 'API key for service-to-service authentication'
            }
        }
    
    def _generate_common_responses(self):
        """
        Generate common response schemas
        """
        return {
            'BadRequest': {
                'description': 'Bad request',
                'content': {
                    'application/json': {
                        'schema': {'$ref': '#/components/schemas/ValidationError'}
                    }
                }
            },
            'Unauthorized': {
                'description': 'Authentication required',
                'content': {
                    'application/json': {
                        'schema': {'$ref': '#/components/schemas/Error'}
                    }
                }
            },
            'Forbidden': {
                'description': 'Permission denied',
                'content': {
                    'application/json': {
                        'schema': {'$ref': '#/components/schemas/Error'}
                    }
                }
            },
            'NotFound': {
                'description': 'Resource not found',
                'content': {
                    'application/json': {
                        'schema': {'$ref': '#/components/schemas/Error'}
                    }
                }
            },
            'TooManyRequests': {
                'description': 'Rate limit exceeded',
                'content': {
                    'application/json': {
                        'schema': {'$ref': '#/components/schemas/Error'}
                    }
                }
            },
            'InternalServerError': {
                'description': 'Internal server error',
                'content': {
                    'application/json': {
                        'schema': {'$ref': '#/components/schemas/Error'}
                    }
                }
            }
        }
    
    def _generate_common_parameters(self):
        """
        Generate common parameters
        """
        return {
            'PageParam': {
                'name': 'page',
                'in': 'query',
                'description': 'Page number for pagination',
                'schema': {
                    'type': 'integer',
                    'minimum': 1,
                    'default': 1
                }
            },
            'PageSizeParam': {
                'name': 'page_size',
                'in': 'query',
                'description': 'Number of items per page',
                'schema': {
                    'type': 'integer',
                    'minimum': 1,
                    'maximum': 100,
                    'default': 20
                }
            },
            'OrderingParam': {
                'name': 'ordering',
                'in': 'query',
                'description': 'Field to order results by. Prefix with - for descending order.',
                'schema': {'type': 'string'}
            },
            'SearchParam': {
                'name': 'search',
                'in': 'query',
                'description': 'Search term to filter results',
                'schema': {'type': 'string'}
            }
        }
    
    def _generate_tags(self):
        """
        Generate API tags for grouping endpoints
        """
        return [
            {
                'name': 'Authentication',
                'description': 'User authentication and authorization'
            },
            {
                'name': 'Students',
                'description': 'Student management operations'
            },
            {
                'name': 'Academics',
                'description': 'Academic records and course management'
            },
            {
                'name': 'Finance',
                'description': 'Financial operations and fee management'
            },
            {
                'name': 'Human Resources',
                'description': 'Employee and staff management'
            },
            {
                'name': 'Inventory',
                'description': 'Asset and inventory management'
            },
            {
                'name': 'Transportation',
                'description': 'Transportation and route management'
            },
            {
                'name': 'Reports',
                'description': 'Reporting and analytics'
            },
            {
                'name': 'System',
                'description': 'System administration and monitoring'
            }
        ]
    
    def generate_postman_collection(self, request=None):
        """
        Generate Postman collection for API testing
        """
        base_url = request.build_absolute_uri('/api/v2/') if request else 'http://localhost:8000/api/v2/'
        
        collection = {
            'info': {
                'name': 'School ERP API',
                'description': 'Postman collection for School ERP API testing',
                'schema': 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json'
            },
            'auth': {
                'type': 'bearer',
                'bearer': [
                    {
                        'key': 'token',
                        'value': '{{access_token}}',
                        'type': 'string'
                    }
                ]
            },
            'variable': [
                {
                    'key': 'base_url',
                    'value': base_url,
                    'type': 'string'
                },
                {
                    'key': 'access_token',
                    'value': '',
                    'type': 'string'
                }
            ],
            'item': [
                {
                    'name': 'Authentication',
                    'item': [
                        {
                            'name': 'Login',
                            'request': {
                                'method': 'POST',
                                'header': [
                                    {
                                        'key': 'Content-Type',
                                        'value': 'application/json'
                                    }
                                ],
                                'body': {
                                    'mode': 'raw',
                                    'raw': json.dumps({
                                        'username': 'admin',
                                        'password': 'password'
                                    }, indent=2)
                                },
                                'url': {
                                    'raw': '{{base_url}}auth/login/',
                                    'host': ['{{base_url}}'],
                                    'path': ['auth', 'login', '']
                                }
                            },
                            'event': [
                                {
                                    'listen': 'test',
                                    'script': {
                                        'exec': [
                                            'if (pm.response.code === 200) {',
                                            '    const response = pm.response.json();',
                                            '    pm.collectionVariables.set("access_token", response.access);',
                                            '}'
                                        ]
                                    }
                                }
                            ]
                        }
                    ]
                },
                {
                    'name': 'Students',
                    'item': [
                        {
                            'name': 'List Students',
                            'request': {
                                'method': 'GET',
                                'url': {
                                    'raw': '{{base_url}}students/',
                                    'host': ['{{base_url}}'],
                                    'path': ['students', '']
                                }
                            }
                        },
                        {
                            'name': 'Create Student',
                            'request': {
                                'method': 'POST',
                                'header': [
                                    {
                                        'key': 'Content-Type',
                                        'value': 'application/json'
                                    }
                                ],
                                'body': {
                                    'mode': 'raw',
                                    'raw': json.dumps({
                                        'student_id': 'STU001',
                                        'first_name': 'John',
                                        'last_name': 'Doe',
                                        'email': '<EMAIL>',
                                        'grade': '10',
                                        'section': 'A',
                                        'admission_date': '2025-01-01'
                                    }, indent=2)
                                },
                                'url': {
                                    'raw': '{{base_url}}students/',
                                    'host': ['{{base_url}}'],
                                    'path': ['students', '']
                                }
                            }
                        }
                    ]
                }
            ]
        }
        
        return collection


@api_view(['GET'])
@permission_classes([AllowAny])
def api_documentation_view(request):
    """
    API documentation endpoint
    """
    doc_generator = APIDocumentationGenerator()
    
    format_param = request.GET.get('format', 'json')
    
    if format_param == 'openapi':
        spec = doc_generator.generate_openapi_spec(request)
        return Response(spec)
    elif format_param == 'postman':
        collection = doc_generator.generate_postman_collection(request)
        return Response(collection)
    else:
        # Return general API information
        version_context = get_api_version_context()
        version_info = APIVersionInfo.get_all_versions()
        
        # Get basic analytics
        summary_stats = analytics_collector.get_summary_stats()
        
        documentation = {
            'api_info': doc_generator.base_info,
            'version_info': version_context,
            'versions': version_info,
            'endpoints': {
                'openapi_spec': request.build_absolute_uri('?format=openapi'),
                'postman_collection': request.build_absolute_uri('?format=postman'),
                'interactive_docs': request.build_absolute_uri('/api/docs/'),
                'redoc': request.build_absolute_uri('/api/redoc/')
            },
            'authentication': {
                'methods': ['JWT Bearer Token', 'API Key'],
                'login_endpoint': '/api/v2/auth/login/',
                'token_refresh_endpoint': '/api/v2/auth/refresh/'
            },
            'rate_limits': {
                'authenticated_users': '1000 requests/hour (varies by role)',
                'anonymous_users': '100 requests/hour',
                'burst_limit': '20 requests/minute'
            },
            'usage_stats': {
                'total_requests_today': summary_stats.get('requests_today', 0),
                'active_users': summary_stats.get('active_users_now', 0),
                'avg_response_time_ms': round(summary_stats.get('avg_response_time_today', 0), 2)
            },
            'support': {
                'documentation': 'https://docs.schoolerp.com',
                'support_email': '<EMAIL>',
                'github': 'https://github.com/schoolerp/api'
            }
        }
        
        return Response(documentation)


@api_view(['GET'])
@permission_classes([AllowAny])
def api_health_check(request):
    """
    API health check endpoint
    """
    from .analytics import performance_monitor
    
    # Basic health check
    health_status = {
        'status': 'healthy',
        'timestamp': timezone.now().isoformat(),
        'version': get_api_version_context()['current_version'],
        'uptime': 'N/A',  # Would need to track application start time
        'checks': {
            'database': 'healthy',
            'cache': 'healthy',
            'external_services': 'healthy'
        }
    }
    
    # Add performance metrics
    try:
        performance_metrics = performance_monitor.get_performance_metrics()
        health_status['performance'] = {
            'avg_response_time_ms': performance_metrics['avg_response_time_ms'],
            'error_rate_percent': performance_metrics['error_rate_percent'],
            'requests_per_minute': performance_metrics['requests_per_minute']
        }
        
        # Check if any critical alerts
        alerts = performance_metrics.get('alerts', [])
        critical_alerts = [alert for alert in alerts if alert.get('severity') == 'critical']
        
        if critical_alerts:
            health_status['status'] = 'degraded'
            health_status['alerts'] = critical_alerts
    
    except Exception as e:
        health_status['status'] = 'degraded'
        health_status['error'] = str(e)
    
    # Return appropriate status code
    status_code = status.HTTP_200_OK if health_status['status'] == 'healthy' else status.HTTP_503_SERVICE_UNAVAILABLE
    
    return Response(health_status, status=status_code)