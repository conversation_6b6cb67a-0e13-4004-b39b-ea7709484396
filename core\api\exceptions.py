"""
Custom exception handling for School ERP API
"""

from rest_framework.views import exception_handler
from rest_framework.response import Response
from rest_framework import status
from django.utils import timezone
from django.core.exceptions import ValidationError as DjangoValidationError
from django.http import Http404
from django.db import IntegrityError
from django.conf import settings
import logging
import uuid

logger = logging.getLogger(__name__)


def custom_exception_handler(exc, context):
    """
    Custom exception handler that provides consistent error responses
    """
    # Get the standard error response
    response = exception_handler(exc, context)
    
    # Generate unique request ID for tracking
    request_id = str(uuid.uuid4())[:8]
    
    # Get request information
    request = context.get('request')
    view = context.get('view')
    
    # Log the exception
    logger.error(
        f"API Exception [{request_id}]: {exc.__class__.__name__}: {str(exc)}",
        extra={
            'request_id': request_id,
            'path': request.path if request else None,
            'method': request.method if request else None,
            'user': str(request.user) if request and hasattr(request, 'user') else None,
            'view': view.__class__.__name__ if view else None,
            'exception_type': exc.__class__.__name__,
            'exception_message': str(exc)
        },
        exc_info=True
    )
    
    if response is not None:
        # Customize the error response format
        custom_response_data = {
            'error': True,
            'message': 'An error occurred while processing your request',
            'details': response.data,
            'error_code': get_error_code(exc),
            'timestamp': timezone.now().isoformat(),
            'request_id': request_id
        }
        
        # Add specific error messages based on exception type
        if hasattr(exc, 'detail'):
            if isinstance(exc.detail, dict):
                custom_response_data['message'] = 'Validation error'
                custom_response_data['field_errors'] = exc.detail
            elif isinstance(exc.detail, list):
                custom_response_data['message'] = exc.detail[0] if exc.detail else 'An error occurred'
            else:
                custom_response_data['message'] = str(exc.detail)
        
        # Add helpful information for different status codes
        if response.status_code == status.HTTP_400_BAD_REQUEST:
            custom_response_data['help'] = 'Please check your request data and try again'
        elif response.status_code == status.HTTP_401_UNAUTHORIZED:
            custom_response_data['help'] = 'Please provide valid authentication credentials'
        elif response.status_code == status.HTTP_403_FORBIDDEN:
            custom_response_data['help'] = 'You do not have permission to perform this action'
        elif response.status_code == status.HTTP_404_NOT_FOUND:
            custom_response_data['help'] = 'The requested resource was not found'
        elif response.status_code == status.HTTP_429_TOO_MANY_REQUESTS:
            custom_response_data['help'] = 'You have exceeded the rate limit. Please try again later'
            # Add retry information if available
            if hasattr(exc, 'wait'):
                custom_response_data['retry_after_seconds'] = exc.wait()
        elif response.status_code >= 500:
            custom_response_data['help'] = 'An internal server error occurred. Please try again later'
            custom_response_data['support_contact'] = '<EMAIL>'
        
        response.data = custom_response_data
    
    else:
        # Handle exceptions that don't have a response (like Django ValidationError)
        if isinstance(exc, DjangoValidationError):
            response = Response({
                'error': True,
                'message': 'Validation error',
                'details': exc.message_dict if hasattr(exc, 'message_dict') else [str(exc)],
                'error_code': 'VALIDATION_ERROR',
                'timestamp': timezone.now().isoformat(),
                'request_id': request_id
            }, status=status.HTTP_400_BAD_REQUEST)
        
        elif isinstance(exc, Http404):
            response = Response({
                'error': True,
                'message': 'Resource not found',
                'details': str(exc),
                'error_code': 'NOT_FOUND',
                'timestamp': timezone.now().isoformat(),
                'request_id': request_id,
                'help': 'The requested resource was not found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        elif isinstance(exc, IntegrityError):
            response = Response({
                'error': True,
                'message': 'Data integrity error',
                'details': 'The operation violates data integrity constraints',
                'error_code': 'INTEGRITY_ERROR',
                'timestamp': timezone.now().isoformat(),
                'request_id': request_id,
                'help': 'Please check for duplicate values or missing required relationships'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        else:
            # Generic server error
            response = Response({
                'error': True,
                'message': 'Internal server error',
                'details': str(exc) if settings.DEBUG else 'An unexpected error occurred',
                'error_code': 'INTERNAL_ERROR',
                'timestamp': timezone.now().isoformat(),
                'request_id': request_id,
                'help': 'An internal server error occurred. Please try again later',
                'support_contact': '<EMAIL>'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    # Add CORS headers if needed
    if request and request.method == 'OPTIONS':
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, PATCH, DELETE, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
    
    return response


def get_error_code(exc):
    """
    Generate error code based on exception type
    """
    error_codes = {
        'ValidationError': 'VALIDATION_ERROR',
        'PermissionDenied': 'PERMISSION_DENIED',
        'NotAuthenticated': 'NOT_AUTHENTICATED',
        'AuthenticationFailed': 'AUTHENTICATION_FAILED',
        'NotFound': 'NOT_FOUND',
        'MethodNotAllowed': 'METHOD_NOT_ALLOWED',
        'NotAcceptable': 'NOT_ACCEPTABLE',
        'UnsupportedMediaType': 'UNSUPPORTED_MEDIA_TYPE',
        'Throttled': 'RATE_LIMIT_EXCEEDED',
        'ParseError': 'PARSE_ERROR',
        'IntegrityError': 'INTEGRITY_ERROR',
        'Http404': 'NOT_FOUND',
        'DjangoValidationError': 'VALIDATION_ERROR'
    }
    
    return error_codes.get(exc.__class__.__name__, 'UNKNOWN_ERROR')


class APIException(Exception):
    """
    Base API exception class
    """
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_code = 'API_ERROR'
    message = 'An API error occurred'
    
    def __init__(self, message=None, error_code=None, status_code=None, details=None):
        if message:
            self.message = message
        if error_code:
            self.error_code = error_code
        if status_code:
            self.status_code = status_code
        self.details = details
        super().__init__(self.message)


class ValidationException(APIException):
    """
    Validation error exception
    """
    status_code = status.HTTP_400_BAD_REQUEST
    error_code = 'VALIDATION_ERROR'
    message = 'Validation failed'


class AuthenticationException(APIException):
    """
    Authentication error exception
    """
    status_code = status.HTTP_401_UNAUTHORIZED
    error_code = 'AUTHENTICATION_ERROR'
    message = 'Authentication failed'


class PermissionException(APIException):
    """
    Permission error exception
    """
    status_code = status.HTTP_403_FORBIDDEN
    error_code = 'PERMISSION_ERROR'
    message = 'Permission denied'


class ResourceNotFoundException(APIException):
    """
    Resource not found exception
    """
    status_code = status.HTTP_404_NOT_FOUND
    error_code = 'RESOURCE_NOT_FOUND'
    message = 'Resource not found'


class RateLimitException(APIException):
    """
    Rate limit exceeded exception
    """
    status_code = status.HTTP_429_TOO_MANY_REQUESTS
    error_code = 'RATE_LIMIT_EXCEEDED'
    message = 'Rate limit exceeded'
    
    def __init__(self, message=None, retry_after=None, **kwargs):
        super().__init__(message, **kwargs)
        self.retry_after = retry_after


class BusinessLogicException(APIException):
    """
    Business logic error exception
    """
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    error_code = 'BUSINESS_LOGIC_ERROR'
    message = 'Business logic validation failed'


class ExternalServiceException(APIException):
    """
    External service error exception
    """
    status_code = status.HTTP_502_BAD_GATEWAY
    error_code = 'EXTERNAL_SERVICE_ERROR'
    message = 'External service error'


class MaintenanceModeException(APIException):
    """
    Maintenance mode exception
    """
    status_code = status.HTTP_503_SERVICE_UNAVAILABLE
    error_code = 'MAINTENANCE_MODE'
    message = 'System is under maintenance'


# Error response helpers
def error_response(message, error_code=None, status_code=status.HTTP_400_BAD_REQUEST, details=None):
    """
    Helper function to create error responses
    """
    return Response({
        'error': True,
        'message': message,
        'error_code': error_code or 'ERROR',
        'details': details,
        'timestamp': timezone.now().isoformat()
    }, status=status_code)


def validation_error_response(field_errors, non_field_errors=None):
    """
    Helper function to create validation error responses
    """
    return Response({
        'error': True,
        'message': 'Validation failed',
        'error_code': 'VALIDATION_ERROR',
        'field_errors': field_errors,
        'non_field_errors': non_field_errors or [],
        'timestamp': timezone.now().isoformat()
    }, status=status.HTTP_400_BAD_REQUEST)


def success_response(data=None, message='Success', status_code=status.HTTP_200_OK):
    """
    Helper function to create success responses
    """
    return Response({
        'success': True,
        'message': message,
        'data': data,
        'timestamp': timezone.now().isoformat()
    }, status=status_code)