"""
Core API serializers for School ERP
"""

from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.utils import timezone
from accounts.models import UserProfile

User = get_user_model()


class UserSerializer(serializers.ModelSerializer):
    """
    User serializer for API responses
    """
    full_name = serializers.SerializerMethodField()
    role_display = serializers.CharField(source='get_user_type_display', read_only=True)
    last_login_formatted = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name', 'full_name',
            'user_type', 'role_display', 'is_active', 'is_staff', 'date_joined',
            'last_login', 'last_login_formatted'
        ]
        read_only_fields = ['id', 'date_joined', 'last_login']
    
    def get_full_name(self, obj):
        return f"{obj.first_name} {obj.last_name}".strip()
    
    def get_last_login_formatted(self, obj):
        if obj.last_login:
            return obj.last_login.strftime('%Y-%m-%d %H:%M:%S')
        return None


class UserCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating new users
    """
    password = serializers.CharField(write_only=True, min_length=8)
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = [
            'username', 'email', 'first_name', 'last_name', 'user_type',
            'password', 'password_confirm'
        ]
    
    def validate(self, data):
        if data['password'] != data['password_confirm']:
            raise serializers.ValidationError("Passwords don't match")
        return data
    
    def create(self, validated_data):
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        
        return user


class UserUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating user information
    """
    
    class Meta:
        model = User
        fields = [
            'email', 'first_name', 'last_name', 'is_active'
        ]



class UserProfileSerializer(serializers.ModelSerializer):
    """
    User profile serializer
    """
    user = UserSerializer(read_only=True)
    
    class Meta:
        model = UserProfile
        fields = [
            'id', 'user', 'national_id', 'emergency_contact', 'emergency_phone',
            'blood_type', 'medical_conditions', 'notes', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class APIResponseSerializer(serializers.Serializer):
    """
    Standard API response serializer
    """
    success = serializers.BooleanField(default=True)
    message = serializers.CharField(required=False)
    data = serializers.JSONField(required=False)
    errors = serializers.JSONField(required=False)
    timestamp = serializers.DateTimeField(default=timezone.now)


class PaginatedResponseSerializer(serializers.Serializer):
    """
    Paginated response serializer
    """
    count = serializers.IntegerField()
    next = serializers.URLField(allow_null=True)
    previous = serializers.URLField(allow_null=True)
    results = serializers.ListField()


class BulkOperationSerializer(serializers.Serializer):
    """
    Bulk operation request serializer
    """
    operation = serializers.ChoiceField(choices=['create', 'update', 'delete'])
    model = serializers.CharField()
    data = serializers.ListField()
    options = serializers.JSONField(required=False, default=dict)


class BulkOperationResponseSerializer(serializers.Serializer):
    """
    Bulk operation response serializer
    """
    operation = serializers.CharField()
    model = serializers.CharField()
    total_items = serializers.IntegerField()
    successful_items = serializers.IntegerField()
    failed_items = serializers.IntegerField()
    errors = serializers.ListField()
    processing_time_ms = serializers.FloatField()


class ExportRequestSerializer(serializers.Serializer):
    """
    Export request serializer
    """
    model = serializers.CharField()
    format = serializers.ChoiceField(choices=['json', 'csv', 'xlsx'])
    filters = serializers.JSONField(required=False, default=dict)
    fields = serializers.ListField(required=False)
    options = serializers.JSONField(required=False, default=dict)


class ExportResponseSerializer(serializers.Serializer):
    """
    Export response serializer
    """
    export_id = serializers.CharField()
    model = serializers.CharField()
    format = serializers.CharField()
    status = serializers.ChoiceField(choices=['queued', 'processing', 'completed', 'failed'])
    progress_percent = serializers.IntegerField(default=0)
    total_records = serializers.IntegerField(required=False)
    processed_records = serializers.IntegerField(default=0)
    download_url = serializers.URLField(required=False)
    expires_at = serializers.DateTimeField(required=False)
    error_message = serializers.CharField(required=False)
    created_at = serializers.DateTimeField()
    completed_at = serializers.DateTimeField(required=False)


class ImportRequestSerializer(serializers.Serializer):
    """
    Import request serializer
    """
    model = serializers.CharField()
    file = serializers.FileField()
    options = serializers.JSONField(required=False, default=dict)
    mapping = serializers.JSONField(required=False)
    validation_rules = serializers.JSONField(required=False)


class ImportResponseSerializer(serializers.Serializer):
    """
    Import response serializer
    """
    import_id = serializers.CharField()
    model = serializers.CharField()
    filename = serializers.CharField()
    file_size = serializers.IntegerField()
    status = serializers.ChoiceField(choices=['queued', 'processing', 'completed', 'failed'])
    progress_percent = serializers.IntegerField(default=0)
    total_records = serializers.IntegerField(required=False)
    processed_records = serializers.IntegerField(default=0)
    created_records = serializers.IntegerField(default=0)
    updated_records = serializers.IntegerField(default=0)
    failed_records = serializers.IntegerField(default=0)
    errors = serializers.ListField(default=list)
    warnings = serializers.ListField(default=list)
    created_at = serializers.DateTimeField()
    completed_at = serializers.DateTimeField(required=False)


class SearchRequestSerializer(serializers.Serializer):
    """
    Search request serializer
    """
    query = serializers.CharField(min_length=2)
    models = serializers.ListField(required=False)
    limit = serializers.IntegerField(default=10, min_value=1, max_value=100)
    offset = serializers.IntegerField(default=0, min_value=0)
    filters = serializers.JSONField(required=False, default=dict)


class SearchResultSerializer(serializers.Serializer):
    """
    Search result item serializer
    """
    id = serializers.IntegerField()
    model = serializers.CharField()
    title = serializers.CharField()
    description = serializers.CharField(required=False)
    url = serializers.URLField(required=False)
    score = serializers.FloatField(required=False)
    highlight = serializers.JSONField(required=False)


class SearchResponseSerializer(serializers.Serializer):
    """
    Search response serializer
    """
    query = serializers.CharField()
    total_results = serializers.IntegerField()
    results_by_model = serializers.JSONField()
    results = SearchResultSerializer(many=True)
    suggestions = serializers.ListField(required=False)
    facets = serializers.JSONField(required=False)
    search_time_ms = serializers.FloatField()


class AutocompleteRequestSerializer(serializers.Serializer):
    """
    Autocomplete request serializer
    """
    model = serializers.CharField()
    field = serializers.CharField()
    query = serializers.CharField(min_length=1)
    limit = serializers.IntegerField(default=10, min_value=1, max_value=50)
    filters = serializers.JSONField(required=False, default=dict)


class AutocompleteSuggestionSerializer(serializers.Serializer):
    """
    Autocomplete suggestion serializer
    """
    value = serializers.CharField()
    label = serializers.CharField()
    description = serializers.CharField(required=False)
    category = serializers.CharField(required=False)


class AutocompleteResponseSerializer(serializers.Serializer):
    """
    Autocomplete response serializer
    """
    query = serializers.CharField()
    model = serializers.CharField()
    field = serializers.CharField()
    suggestions = AutocompleteSuggestionSerializer(many=True)


class APIAnalyticsSerializer(serializers.Serializer):
    """
    API analytics data serializer
    """
    total_requests = serializers.IntegerField()
    unique_users = serializers.IntegerField()
    avg_response_time_ms = serializers.FloatField()
    error_rate_percent = serializers.FloatField()
    requests_per_minute = serializers.FloatField()
    popular_endpoints = serializers.JSONField()
    status_code_distribution = serializers.JSONField()
    user_role_distribution = serializers.JSONField()
    api_version_distribution = serializers.JSONField()
    peak_hours = serializers.JSONField()
    geographic_distribution = serializers.JSONField(required=False)


class APIPerformanceSerializer(serializers.Serializer):
    """
    API performance metrics serializer
    """
    avg_response_time_ms = serializers.FloatField()
    p95_response_time_ms = serializers.FloatField()
    p99_response_time_ms = serializers.FloatField()
    error_rate_percent = serializers.FloatField()
    requests_per_minute = serializers.FloatField()
    active_connections = serializers.IntegerField()
    memory_usage_percent = serializers.FloatField()
    cpu_usage_percent = serializers.FloatField()
    alerts = serializers.ListField()


class APIStatusSerializer(serializers.Serializer):
    """
    API status serializer
    """
    status = serializers.ChoiceField(choices=['operational', 'degraded', 'down'])
    timestamp = serializers.DateTimeField()
    version = serializers.CharField()
    environment = serializers.CharField()
    uptime_seconds = serializers.IntegerField(required=False)
    performance = APIPerformanceSerializer(required=False)
    alerts = serializers.ListField(required=False)
    maintenance_mode = serializers.BooleanField(default=False)


class ErrorResponseSerializer(serializers.Serializer):
    """
    Error response serializer
    """
    error = serializers.CharField()
    message = serializers.CharField()
    details = serializers.JSONField(required=False)
    error_code = serializers.CharField(required=False)
    timestamp = serializers.DateTimeField(default=timezone.now)
    request_id = serializers.CharField(required=False)


class ValidationErrorSerializer(serializers.Serializer):
    """
    Validation error response serializer
    """
    field_errors = serializers.JSONField()
    non_field_errors = serializers.ListField(required=False)
    error_count = serializers.IntegerField()


# Version-specific serializers for API versioning
class UserV1Serializer(UserSerializer):
    """
    User serializer for API v1 (simplified)
    """
    class Meta(UserSerializer.Meta):
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'role', 'is_active', 'date_joined'
        ]


class UserV2Serializer(UserSerializer):
    """
    User serializer for API v2 (enhanced)
    """
    permissions = serializers.SerializerMethodField()
    preferences = serializers.JSONField(required=False)
    
    class Meta(UserSerializer.Meta):
        fields = UserSerializer.Meta.fields + ['permissions', 'preferences']
    
    def get_permissions(self, obj):
        # Return user permissions
        return list(obj.user_permissions.values_list('codename', flat=True))


# Custom field serializers
class TimestampField(serializers.DateTimeField):
    """
    Custom timestamp field with consistent formatting
    """
    def to_representation(self, value):
        if value:
            return value.isoformat()
        return None


class FileUploadSerializer(serializers.Serializer):
    """
    File upload serializer
    """
    file = serializers.FileField()
    description = serializers.CharField(required=False)
    tags = serializers.ListField(required=False)
    
    def validate_file(self, value):
        # Add file validation logic
        max_size = 10 * 1024 * 1024  # 10MB
        if value.size > max_size:
            raise serializers.ValidationError("File size cannot exceed 10MB")
        
        return value