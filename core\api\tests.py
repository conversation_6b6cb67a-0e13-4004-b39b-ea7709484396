"""
Unit tests for School ERP API system
"""

from django.test import TestCase, override_settings
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.utils import timezone
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from unittest.mock import patch, MagicMock
import json
import time

from .versioning import SchoolERPVersioning, APIVersionInfo
from .throttling import (
    SchoolERPUserRateThrottle, SchoolERPAnonRateThrottle,
    APIEndpointThrottle, BurstRateThrottle
)
from .analytics import APIAnalyticsCollector, APIPerformanceMonitor
from .documentation import APIDocumentationGenerator
from accounts.models import UserProfile

User = get_user_model()


class APIVersioningTestCase(TestCase):
    """
    Test API versioning functionality
    """
    
    def setUp(self):
        self.versioning = SchoolERPVersioning()
    
    def test_default_version(self):
        """Test default version is returned when none specified"""
        self.assertEqual(self.versioning.default_version, 'v1')
    
    def test_allowed_versions(self):
        """Test allowed versions configuration"""
        self.assertIn('v1', self.versioning.allowed_versions)
        self.assertIn('v2', self.versioning.allowed_versions)
    
    def test_version_info(self):
        """Test version information retrieval"""
        v1_info = APIVersionInfo.get_version_info('v1')
        self.assertIn('features', v1_info)
        self.assertIn('status', v1_info)
        
        v2_info = APIVersionInfo.get_version_info('v2')
        self.assertIn('features', v2_info)
        self.assertIn('breaking_changes', v2_info)
    
    def test_version_deprecation(self):
        """Test version deprecation detection"""
        self.assertTrue(APIVersionInfo.is_version_deprecated('v1'))
        self.assertFalse(APIVersionInfo.is_version_deprecated('v2'))
    
    def test_latest_version(self):
        """Test latest version detection"""
        latest = APIVersionInfo.get_latest_version()
        self.assertEqual(latest, 'v2')


class APIThrottlingTestCase(TestCase):
    """
    Test API throttling functionality
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='student'
        )
        cache.clear()
    
    def test_user_rate_throttle(self):
        """Test user-based rate throttling"""
        throttle = SchoolERPUserRateThrottle()
        throttle.scope = 'user'
        
        # Mock request
        request = MagicMock()
        request.user = self.user
        
        # Mock view
        view = MagicMock()
        
        # Test rate limit based on user role
        rate = throttle.get_rate()
        self.assertEqual(rate, '1000/hour')  # Default for student
    
    def test_anonymous_rate_throttle(self):
        """Test anonymous user rate throttling"""
        throttle = SchoolERPAnonRateThrottle()
        
        # Mock request
        request = MagicMock()
        request.META = {'REMOTE_ADDR': '127.0.0.1', 'HTTP_USER_AGENT': 'TestAgent'}
        
        # Mock view
        view = MagicMock()
        
        # Test cache key generation
        cache_key = throttle.get_cache_key(request, view)
        self.assertIsNotNone(cache_key)
        self.assertIn('anon', cache_key)
    
    def test_endpoint_specific_throttle(self):
        """Test endpoint-specific throttling"""
        throttle = APIEndpointThrottle()
        
        # Mock view with login endpoint
        view = MagicMock()
        view.throttle_endpoint = 'login'
        
        rate = throttle.get_rate('login')
        self.assertEqual(rate, '10/minute')
        
        # Test default rate
        default_rate = throttle.get_rate('unknown_endpoint')
        self.assertEqual(default_rate, '1000/hour')
    
    def test_burst_rate_throttle(self):
        """Test burst rate throttling"""
        throttle = BurstRateThrottle()
        
        # Mock request
        request = MagicMock()
        request.user = self.user
        request.META = {'REMOTE_ADDR': '127.0.0.1'}
        
        # Mock view
        view = MagicMock()
        
        # Test initial request is allowed
        self.assertTrue(throttle.allow_request(request, view))


class APIAnalyticsTestCase(TestCase):
    """
    Test API analytics functionality
    """
    
    def setUp(self):
        self.collector = APIAnalyticsCollector()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='teacher'
        )
        cache.clear()
    
    def test_request_recording(self):
        """Test API request recording"""
        # Mock request and response
        request = MagicMock()
        request.method = 'GET'
        request.path = '/api/v2/students/'
        request.user = self.user
        request.META = {
            'REMOTE_ADDR': '127.0.0.1',
            'HTTP_USER_AGENT': 'TestAgent'
        }
        
        response = MagicMock()
        response.status_code = 200
        
        view = MagicMock()
        view.__class__.__name__ = 'StudentViewSet'
        view.action = 'list'
        
        # Record request
        self.collector.record_request(request, response, view, 150.5)
        
        # Check if data was recorded
        daily_data = self.collector.get_daily_analytics()
        self.assertGreater(daily_data.get('total_requests', 0), 0)
    
    def test_summary_stats(self):
        """Test summary statistics generation"""
        # Record some test data first
        request = MagicMock()
        request.method = 'GET'
        request.path = '/api/v2/test/'
        request.user = self.user
        request.META = {'REMOTE_ADDR': '127.0.0.1', 'HTTP_USER_AGENT': 'Test'}
        
        response = MagicMock()
        response.status_code = 200
        
        self.collector.record_request(request, response, None, 100)
        
        # Get summary stats
        stats = self.collector.get_summary_stats()
        
        self.assertIn('requests_today', stats)
        self.assertIn('active_users_now', stats)
        self.assertIn('popular_endpoints', stats)
    
    def test_realtime_analytics(self):
        """Test real-time analytics"""
        # Record a request
        request = MagicMock()
        request.method = 'POST'
        request.path = '/api/v2/students/'
        request.user = self.user
        request.META = {'REMOTE_ADDR': '127.0.0.1', 'HTTP_USER_AGENT': 'Test'}
        
        response = MagicMock()
        response.status_code = 201
        
        self.collector.record_request(request, response, None, 200)
        
        # Get real-time data
        realtime_data = self.collector.get_realtime_analytics()
        
        self.assertIn('last_5_minutes', realtime_data)
        self.assertIn('total_requests_5min', realtime_data)
        self.assertIn('active_users_now', realtime_data)


class APIPerformanceMonitorTestCase(TestCase):
    """
    Test API performance monitoring
    """
    
    def setUp(self):
        self.monitor = APIPerformanceMonitor()
        cache.clear()
    
    def test_performance_metrics(self):
        """Test performance metrics collection"""
        metrics = self.monitor.get_performance_metrics()
        
        self.assertIn('avg_response_time_ms', metrics)
        self.assertIn('error_rate_percent', metrics)
        self.assertIn('requests_per_minute', metrics)
        self.assertIn('alerts', metrics)
        self.assertIn('thresholds', metrics)
    
    @patch('core.api.analytics.APIAnalyticsCollector')
    def test_performance_alerts(self, mock_collector):
        """Test performance alert generation"""
        # Mock high response time
        mock_collector.return_value.get_hourly_analytics.return_value = {
            'response_times': [6000, 7000, 8000]  # High response times
        }
        
        alerts = self.monitor.check_performance_alerts()
        
        # Should generate high response time alert
        self.assertTrue(any(alert['type'] == 'high_response_time' for alert in alerts))


class APIDocumentationTestCase(TestCase):
    """
    Test API documentation generation
    """
    
    def setUp(self):
        self.doc_generator = APIDocumentationGenerator()
    
    def test_openapi_spec_generation(self):
        """Test OpenAPI specification generation"""
        spec = self.doc_generator.generate_openapi_spec()
        
        self.assertEqual(spec['openapi'], '3.0.0')
        self.assertIn('info', spec)
        self.assertIn('paths', spec)
        self.assertIn('components', spec)
        self.assertIn('security', spec)
    
    def test_postman_collection_generation(self):
        """Test Postman collection generation"""
        collection = self.doc_generator.generate_postman_collection()
        
        self.assertIn('info', collection)
        self.assertIn('item', collection)
        self.assertIn('variable', collection)
        
        # Check for authentication folder
        auth_folder = next((item for item in collection['item'] if item['name'] == 'Authentication'), None)
        self.assertIsNotNone(auth_folder)
    
    def test_schema_generation(self):
        """Test schema generation"""
        schemas = self.doc_generator._generate_schemas()
        
        self.assertIn('User', schemas)
        self.assertIn('Student', schemas)
        self.assertIn('Error', schemas)
        self.assertIn('ValidationError', schemas)
    
    def test_security_schemes(self):
        """Test security schemes generation"""
        security_schemes = self.doc_generator._generate_security_schemes()
        
        self.assertIn('BearerAuth', security_schemes)
        self.assertIn('ApiKeyAuth', security_schemes)


class APIIntegrationTestCase(APITestCase):
    """
    Integration tests for API endpoints
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
        
        self.client = APIClient()
        
        # Get JWT token
        refresh = RefreshToken.for_user(self.user)
        self.access_token = str(refresh.access_token)
        
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
    
    def test_api_documentation_endpoint(self):
        """Test API documentation endpoint"""
        url = reverse('api_documentation')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('api_info', response.data)
        self.assertIn('version_info', response.data)
        self.assertIn('endpoints', response.data)
    
    def test_api_health_check(self):
        """Test API health check endpoint"""
        url = reverse('api_health')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('status', response.data)
        self.assertIn('timestamp', response.data)
        self.assertIn('version', response.data)
    
    def test_api_status_endpoint(self):
        """Test API status endpoint"""
        url = reverse('v2:api_status')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('status', response.data)
        self.assertIn('timestamp', response.data)
    
    def test_api_analytics_endpoint(self):
        """Test API analytics endpoint (admin only)"""
        url = reverse('v2:api_analytics')
        
        # Test with admin user
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Test with non-admin user
        regular_user = User.objects.create_user(
            username='regular',
            email='<EMAIL>',
            password='testpass123',
            user_type='student'
        )
        
        refresh = RefreshToken.for_user(regular_user)
        regular_token = str(refresh.access_token)
        
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {regular_token}')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_search_endpoint(self):
        """Test global search endpoint"""
        url = reverse('v2:global_search')
        
        # Test with valid query
        response = self.client.get(url, {'q': 'test'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('query', response.data)
        self.assertIn('total_results', response.data)
        
        # Test with short query
        response = self.client.get(url, {'q': 'a'})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # Test without query
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_autocomplete_endpoint(self):
        """Test autocomplete endpoint"""
        url = reverse('v2:autocomplete')
        
        # Test with valid parameters
        response = self.client.get(url, {
            'model': 'student',
            'field': 'name',
            'q': 'john'
        })
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('suggestions', response.data)
        
        # Test with missing parameters
        response = self.client.get(url, {'q': 'john'})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_bulk_operations_endpoint(self):
        """Test bulk operations endpoint"""
        url = reverse('v2:bulk_operations')
        
        data = {
            'operation': 'create',
            'model': 'student',
            'data': [
                {'name': 'John Doe', 'email': '<EMAIL>'},
                {'name': 'Jane Smith', 'email': '<EMAIL>'}
            ]
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('operation', response.data)
        self.assertIn('model', response.data)
        self.assertIn('status', response.data)
    
    def test_export_endpoint(self):
        """Test data export endpoint"""
        url = reverse('v2:export_data')
        
        data = {
            'model': 'student',
            'format': 'json',
            'filters': {'grade': '10'}
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('export_id', response.data)
        self.assertIn('status', response.data)
    
    def test_import_endpoint(self):
        """Test data import endpoint"""
        url = reverse('v2:import_data')
        
        # Create a test file
        import io
        test_file = io.StringIO('name,email\nJohn Doe,<EMAIL>')
        test_file.name = 'test.csv'
        
        data = {
            'model': 'student',
            'file': test_file
        }
        
        response = self.client.post(url, data, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('import_id', response.data)
        self.assertIn('status', response.data)
    
    def test_authentication_required(self):
        """Test that authentication is required for protected endpoints"""
        # Remove authentication
        self.client.credentials()
        
        url = reverse('v2:api_analytics')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_rate_limiting(self):
        """Test rate limiting functionality"""
        url = reverse('v2:global_search')
        
        # Make multiple requests quickly
        responses = []
        for i in range(25):  # Exceed burst limit
            response = self.client.get(url, {'q': f'test{i}'})
            responses.append(response.status_code)
        
        # Should eventually get rate limited
        self.assertIn(status.HTTP_429_TOO_MANY_REQUESTS, responses)
    
    def test_api_versioning(self):
        """Test API versioning"""
        # Test v1 endpoint
        url = '/api/v1/status/'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Test v2 endpoint
        url = '/api/v2/status/'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Test latest endpoint
        url = '/api/latest/status/'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_error_handling(self):
        """Test custom error handling"""
        # Test 404 error
        url = '/api/v2/nonexistent/'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        
        if response.status_code == status.HTTP_404_NOT_FOUND:
            self.assertIn('error', response.data)
            self.assertIn('message', response.data)
            self.assertIn('timestamp', response.data)
    
    def test_cache_management_endpoint(self):
        """Test cache management endpoint (superuser only)"""
        url = reverse('v2:cache_management')
        
        # Test with regular admin (should be forbidden)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        
        # Test with superuser
        self.user.is_superuser = True
        self.user.save()
        
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('backend', response.data)


class APIMiddlewareTestCase(TestCase):
    """
    Test API middleware functionality
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        cache.clear()
    
    @patch('core.api.analytics.analytics_collector')
    def test_analytics_middleware(self, mock_collector):
        """Test analytics middleware records requests"""
        client = APIClient()
        
        # Make API request
        response = client.get('/api/v2/status/')
        
        # Verify analytics were recorded
        self.assertTrue(mock_collector.record_request.called)
    
    def test_version_middleware(self):
        """Test version middleware adds headers"""
        client = APIClient()
        
        response = client.get('/api/v2/status/')
        
        # Check for version headers
        self.assertIn('API-Version', response)
        self.assertIn('API-Supported-Versions', response)


if __name__ == '__main__':
    import django
    from django.conf import settings
    from django.test.utils import get_runner
    
    django.setup()
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    failures = test_runner.run_tests(['core.api.tests'])