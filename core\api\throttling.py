"""
API rate limiting and throttling system for School ERP
"""

from rest_framework.throttling import UserRate<PERSON>hrottle, AnonRateThrottle, BaseThrottle
from django.core.cache import cache
from django.conf import settings
from django.utils import timezone
from datetime import timedelta
import hashlib
import json
import logging

logger = logging.getLogger(__name__)


class SchoolERPUserRateThrottle(UserRateThrottle):
    """
    Custom user rate throttle with role-based limits
    """
    scope = 'user'
    
    def get_cache_key(self, request, view):
        """
        Generate cache key including user role for different limits
        """
        if request.user.is_authenticated:
            user_role = getattr(request.user, 'role', 'student')
            ident = f"{request.user.pk}_{user_role}"
        else:
            ident = self.get_ident(request)
        
        return self.cache_format % {
            'scope': self.scope,
            'ident': ident
        }
    
    def get_rate(self):
        """
        Get rate limit based on user role
        """
        if hasattr(self, 'request') and self.request.user.is_authenticated:
            user_role = getattr(self.request.user, 'role', 'student')
            
            # Role-based rate limits
            role_rates = {
                'super_admin': '10000/hour',
                'admin': '5000/hour',
                'teacher': '2000/hour',
                'student': '1000/hour',
                'parent': '500/hour',
                'staff': '1500/hour'
            }
            
            return role_rates.get(user_role, '1000/hour')
        
        return super().get_rate()


class SchoolERPAnonRateThrottle(AnonRateThrottle):
    """
    Custom anonymous rate throttle with IP-based tracking
    """
    scope = 'anon'
    rate = '100/hour'
    
    def get_cache_key(self, request, view):
        """
        Generate cache key with additional request info
        """
        ident = self.get_ident(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        # Create a hash of IP + User Agent for better tracking
        combined = f"{ident}_{user_agent}"
        hashed_ident = hashlib.md5(combined.encode()).hexdigest()
        
        return self.cache_format % {
            'scope': self.scope,
            'ident': hashed_ident
        }


class APIEndpointThrottle(BaseThrottle):
    """
    Throttle specific API endpoints with custom limits
    """
    
    # Endpoint-specific rate limits
    ENDPOINT_RATES = {
        'login': '10/minute',
        'password_reset': '5/hour',
        'bulk_operations': '50/hour',
        'reports': '100/hour',
        'file_upload': '20/hour',
        'export': '10/hour',
        'import': '5/hour'
    }
    
    def __init__(self):
        self.cache_format = 'throttle_endpoint_%(endpoint)s_%(ident)s'
    
    def get_endpoint_key(self, view):
        """
        Determine endpoint key from view
        """
        if hasattr(view, 'throttle_endpoint'):
            return view.throttle_endpoint
        
        # Try to determine from view name or action
        view_name = getattr(view, 'basename', '') or view.__class__.__name__.lower()
        action = getattr(view, 'action', '')
        
        # Map common patterns
        if 'login' in view_name or 'auth' in view_name:
            return 'login'
        elif 'password' in view_name and 'reset' in view_name:
            return 'password_reset'
        elif 'bulk' in action or 'batch' in action:
            return 'bulk_operations'
        elif 'report' in view_name:
            return 'reports'
        elif 'upload' in action:
            return 'file_upload'
        elif 'export' in action:
            return 'export'
        elif 'import' in action:
            return 'import'
        
        return 'default'
    
    def get_rate(self, endpoint_key):
        """
        Get rate limit for specific endpoint
        """
        return self.ENDPOINT_RATES.get(endpoint_key, '1000/hour')
    
    def parse_rate(self, rate):
        """
        Parse rate string into number and duration
        """
        if rate is None:
            return (None, None)
        
        num, period = rate.split('/')
        num_requests = int(num)
        
        duration_map = {
            'second': 1,
            'minute': 60,
            'hour': 3600,
            'day': 86400
        }
        
        duration = duration_map.get(period, 3600)
        return (num_requests, duration)
    
    def get_cache_key(self, request, view):
        """
        Generate cache key for endpoint throttling
        """
        endpoint_key = self.get_endpoint_key(view)
        
        if request.user.is_authenticated:
            ident = request.user.pk
        else:
            ident = self.get_ident(request)
        
        return self.cache_format % {
            'endpoint': endpoint_key,
            'ident': ident
        }
    
    def get_ident(self, request):
        """
        Get client identifier
        """
        xff = request.META.get('HTTP_X_FORWARDED_FOR')
        remote_addr = request.META.get('REMOTE_ADDR')
        
        if xff:
            return xff.split(',')[0].strip()
        return remote_addr
    
    def allow_request(self, request, view):
        """
        Check if request should be allowed
        """
        endpoint_key = self.get_endpoint_key(view)
        rate = self.get_rate(endpoint_key)
        
        if rate is None:
            return True
        
        num_requests, duration = self.parse_rate(rate)
        if num_requests is None:
            return True
        
        cache_key = self.get_cache_key(request, view)
        now = timezone.now()
        
        # Get current request history
        history = cache.get(cache_key, [])
        
        # Remove old requests outside the time window
        cutoff = now - timedelta(seconds=duration)
        history = [timestamp for timestamp in history if timestamp > cutoff]
        
        # Check if limit exceeded
        if len(history) >= num_requests:
            return False
        
        # Add current request
        history.append(now)
        cache.set(cache_key, history, duration)
        
        return True
    
    def wait(self):
        """
        Return time to wait before next request
        """
        return 60  # Default wait time


class BurstRateThrottle(BaseThrottle):
    """
    Throttle for handling burst requests
    """
    
    def __init__(self):
        self.cache_format = 'throttle_burst_%(ident)s'
        self.burst_rate = '20/minute'  # Allow 20 requests per minute
        self.sustained_rate = '100/hour'  # But only 100 per hour sustained
    
    def get_cache_key(self, request, view):
        """
        Generate cache key for burst throttling
        """
        if request.user.is_authenticated:
            ident = request.user.pk
        else:
            ident = self.get_ident(request)
        
        return self.cache_format % {'ident': ident}
    
    def get_ident(self, request):
        """
        Get client identifier
        """
        xff = request.META.get('HTTP_X_FORWARDED_FOR')
        remote_addr = request.META.get('REMOTE_ADDR')
        
        if xff:
            return xff.split(',')[0].strip()
        return remote_addr
    
    def allow_request(self, request, view):
        """
        Check both burst and sustained rates
        """
        cache_key = self.get_cache_key(request, view)
        now = timezone.now()
        
        # Get request history
        history = cache.get(cache_key, {'requests': [], 'burst_count': 0})
        
        # Clean old requests (older than 1 hour)
        cutoff_hour = now - timedelta(hours=1)
        history['requests'] = [
            timestamp for timestamp in history['requests'] 
            if timestamp > cutoff_hour
        ]
        
        # Check sustained rate (100/hour)
        if len(history['requests']) >= 100:
            return False
        
        # Check burst rate (20/minute)
        cutoff_minute = now - timedelta(minutes=1)
        recent_requests = [
            timestamp for timestamp in history['requests']
            if timestamp > cutoff_minute
        ]
        
        if len(recent_requests) >= 20:
            return False
        
        # Add current request
        history['requests'].append(now)
        cache.set(cache_key, history, 3600)  # Cache for 1 hour
        
        return True
    
    def wait(self):
        """
        Return time to wait before next request
        """
        return 60


class APIAnalyticsThrottle(BaseThrottle):
    """
    Throttle that also collects analytics data
    """
    
    def __init__(self):
        self.cache_format = 'throttle_analytics_%(ident)s'
        self.analytics_key = 'api_analytics_%(date)s'
    
    def get_cache_key(self, request, view):
        """
        Generate cache key
        """
        if request.user.is_authenticated:
            ident = f"user_{request.user.pk}"
        else:
            ident = f"anon_{self.get_ident(request)}"
        
        return self.cache_format % {'ident': ident}
    
    def get_ident(self, request):
        """
        Get client identifier
        """
        xff = request.META.get('HTTP_X_FORWARDED_FOR')
        remote_addr = request.META.get('REMOTE_ADDR')
        
        if xff:
            return xff.split(',')[0].strip()
        return remote_addr
    
    def collect_analytics(self, request, view, allowed):
        """
        Collect API usage analytics
        """
        now = timezone.now()
        date_key = now.strftime('%Y-%m-%d')
        analytics_key = self.analytics_key % {'date': date_key}
        
        # Get current analytics data
        analytics = cache.get(analytics_key, {
            'total_requests': 0,
            'allowed_requests': 0,
            'throttled_requests': 0,
            'endpoints': {},
            'users': {},
            'ips': {}
        })
        
        # Update counters
        analytics['total_requests'] += 1
        if allowed:
            analytics['allowed_requests'] += 1
        else:
            analytics['throttled_requests'] += 1
        
        # Track endpoint usage
        endpoint = f"{request.method} {request.path}"
        analytics['endpoints'][endpoint] = analytics['endpoints'].get(endpoint, 0) + 1
        
        # Track user usage
        if request.user.is_authenticated:
            user_key = f"user_{request.user.pk}"
            analytics['users'][user_key] = analytics['users'].get(user_key, 0) + 1
        
        # Track IP usage
        ip = self.get_ident(request)
        analytics['ips'][ip] = analytics['ips'].get(ip, 0) + 1
        
        # Store analytics (cache for 25 hours to ensure we don't lose data)
        cache.set(analytics_key, analytics, 90000)
    
    def allow_request(self, request, view):
        """
        Always allow but collect analytics
        """
        self.collect_analytics(request, view, True)
        return True


class DynamicRateThrottle(BaseThrottle):
    """
    Dynamic throttle that adjusts rates based on system load
    """
    
    def __init__(self):
        self.cache_format = 'throttle_dynamic_%(ident)s'
        self.load_key = 'system_load_metrics'
    
    def get_system_load(self):
        """
        Get current system load metrics
        """
        load_metrics = cache.get(self.load_key, {
            'cpu_usage': 50,
            'memory_usage': 50,
            'active_connections': 100
        })
        
        return load_metrics
    
    def calculate_dynamic_rate(self, base_rate='1000/hour'):
        """
        Calculate rate based on system load
        """
        load_metrics = self.get_system_load()
        
        # Base rate parsing
        num, period = base_rate.split('/')
        base_num = int(num)
        
        # Adjust based on system load
        cpu_factor = max(0.1, 1 - (load_metrics['cpu_usage'] - 50) / 100)
        memory_factor = max(0.1, 1 - (load_metrics['memory_usage'] - 50) / 100)
        
        # Calculate adjusted rate
        adjusted_num = int(base_num * cpu_factor * memory_factor)
        adjusted_rate = f"{adjusted_num}/{period}"
        
        logger.info(f"Dynamic rate adjusted from {base_rate} to {adjusted_rate}")
        return adjusted_rate
    
    def allow_request(self, request, view):
        """
        Check request against dynamic rate
        """
        # Use standard user throttle logic with dynamic rate
        dynamic_rate = self.calculate_dynamic_rate()
        
        # Implement basic throttling logic
        cache_key = self.get_cache_key(request, view)
        now = timezone.now()
        
        # Parse dynamic rate
        num_requests, duration = self.parse_rate(dynamic_rate)
        if num_requests is None:
            return True
        
        # Get request history
        history = cache.get(cache_key, [])
        
        # Remove old requests
        cutoff = now - timedelta(seconds=duration)
        history = [timestamp for timestamp in history if timestamp > cutoff]
        
        # Check limit
        if len(history) >= num_requests:
            return False
        
        # Add current request
        history.append(now)
        cache.set(cache_key, history, duration)
        
        return True
    
    def parse_rate(self, rate):
        """
        Parse rate string
        """
        if rate is None:
            return (None, None)
        
        num, period = rate.split('/')
        num_requests = int(num)
        
        duration_map = {
            'second': 1,
            'minute': 60,
            'hour': 3600,
            'day': 86400
        }
        
        duration = duration_map.get(period, 3600)
        return (num_requests, duration)
    
    def get_cache_key(self, request, view):
        """
        Generate cache key
        """
        if request.user.is_authenticated:
            ident = request.user.pk
        else:
            ident = self.get_ident(request)
        
        return self.cache_format % {'ident': ident}
    
    def get_ident(self, request):
        """
        Get client identifier
        """
        xff = request.META.get('HTTP_X_FORWARDED_FOR')
        remote_addr = request.META.get('REMOTE_ADDR')
        
        if xff:
            return xff.split(',')[0].strip()
        return remote_addr


def get_throttle_classes_for_view(view_name, action=None):
    """
    Get appropriate throttle classes for a specific view
    """
    # Default throttles
    throttles = [SchoolERPUserRateThrottle, SchoolERPAnonRateThrottle]
    
    # Add endpoint-specific throttles
    sensitive_endpoints = ['login', 'password_reset', 'bulk_operations']
    if any(endpoint in view_name.lower() for endpoint in sensitive_endpoints):
        throttles.append(APIEndpointThrottle)
    
    # Add burst throttle for high-frequency endpoints
    high_frequency_endpoints = ['search', 'autocomplete', 'suggestions']
    if any(endpoint in view_name.lower() for endpoint in high_frequency_endpoints):
        throttles.append(BurstRateThrottle)
    
    # Add analytics throttle for monitoring
    throttles.append(APIAnalyticsThrottle)
    
    return throttles


class ThrottleStatusMixin:
    """
    Mixin to add throttle status information to API responses
    """
    
    def finalize_response(self, request, response, *args, **kwargs):
        """
        Add throttle status headers to response
        """
        response = super().finalize_response(request, response, *args, **kwargs)
        
        # Add rate limit headers
        if hasattr(self, 'throttle_classes'):
            for throttle_class in self.throttle_classes:
                throttle = throttle_class()
                if hasattr(throttle, 'get_rate'):
                    rate = throttle.get_rate()
                    if rate:
                        response['X-RateLimit-Limit'] = rate
                        
                        # Try to get remaining requests
                        cache_key = throttle.get_cache_key(request, self)
                        if cache_key:
                            history = cache.get(cache_key, [])
                            num_requests, duration = throttle.parse_rate(rate) if hasattr(throttle, 'parse_rate') else (None, None)
                            if num_requests:
                                remaining = max(0, num_requests - len(history))
                                response['X-RateLimit-Remaining'] = str(remaining)
        
        return response