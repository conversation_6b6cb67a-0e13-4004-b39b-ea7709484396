"""
API URL configuration for School ERP
"""

from django.urls import path, include
from rest_framework.routers import DefaultRout<PERSON>
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
    TokenVerifyView
)
from .views import (
    APIAnalyticsView, APIStatusView, CacheManagementView,
    BulkOperationsView, ExportView, ImportView, SearchView, AutocompleteView
)
from .documentation import api_documentation_view, api_health_check

# Create router for viewsets
router = DefaultRouter()

# API v2 URL patterns
v2_patterns = [
    # Authentication endpoints
    path('auth/login/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('auth/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('auth/verify/', TokenVerifyView.as_view(), name='token_verify'),
    
    # Core API endpoints
    path('', include(router.urls)),
    
    # System endpoints
    path('analytics/', APIAnalyticsView.as_view(), name='api_analytics'),
    path('status/', APIStatusView.as_view(), name='api_status'),
    path('health/', api_health_check, name='api_health'),
    path('cache/', CacheManagementView.as_view(), name='cache_management'),
    
    # Utility endpoints
    path('bulk/', BulkOperationsView.as_view(), name='bulk_operations'),
    path('export/', ExportView.as_view(), name='export_data'),
    path('import/', ImportView.as_view(), name='import_data'),
    path('search/', SearchView.as_view(), name='global_search'),
    path('autocomplete/', AutocompleteView.as_view(), name='autocomplete'),
    
    # Module-specific API endpoints (to be implemented)
    # path('students/', include('students.api.urls')),
    # path('academics/', include('academics.api.urls')),
    # path('hr/', include('hr.api.urls')),
    # path('finance/', include('finance.api.urls')),
    # path('inventory/', include('inventory.api.urls')),
    # path('transportation/', include('transportation.api.urls')),
    # path('health/', include('health.api.urls')),
    # path('library/', include('library.api.urls')),
    # path('communications/', include('communications.api.urls')),
    # path('reports/', include('reports.api.urls')),
]

# API v1 URL patterns (deprecated but maintained for compatibility)
v1_patterns = [
    # Basic authentication
    path('auth/login/', TokenObtainPairView.as_view(), name='v1_token_obtain_pair'),
    path('auth/refresh/', TokenRefreshView.as_view(), name='v1_token_refresh'),
    
    # Limited endpoints for v1
    # path('students/', include('students.api.urls')),
    path('status/', APIStatusView.as_view(), name='v1_api_status'),
]

# Main API URL patterns
urlpatterns = [
    # API Documentation
    path('', api_documentation_view, name='api_documentation'),
    path('docs/', api_documentation_view, name='api_docs'),
    
    # Versioned API endpoints
    path('v1/', include((v1_patterns, 'v1'), namespace='v1')),
    path('v2/', include((v2_patterns, 'v2'), namespace='v2')),
    
    # Default to latest version
    path('latest/', include((v2_patterns, 'latest'), namespace='latest')),
]