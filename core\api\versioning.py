"""
API versioning system for School ERP
"""

from rest_framework.versioning import URLPathVersioning, AcceptHeaderVersioning
from rest_framework.response import Response
from rest_framework import status
from django.conf import settings


class SchoolERPVersioning(URLPathVersioning):
    """
    Custom versioning class for School ERP API
    Supports URL path versioning with fallback to accept header
    """
    default_version = 'v1'
    allowed_versions = ['v1', 'v2']
    version_param = 'version'
    
    def determine_version(self, request, *args, **kwargs):
        """
        Determine the API version from URL path or accept header
        """
        # Try URL path versioning first
        version = super().determine_version(request, *args, **kwargs)
        
        # Fallback to accept header if no version in URL
        if not version:
            accept_header_versioning = AcceptHeaderVersioning()
            version = accept_header_versioning.determine_version(request, *args, **kwargs)
        
        # Use default version if none specified
        if not version:
            version = self.default_version
            
        return version
    
    def reverse(self, viewname, args=None, kwargs=None, request=None, format=None, **extra):
        """
        Include version in URL reversal
        """
        if request and hasattr(request, 'version'):
            kwargs = kwargs or {}
            kwargs['version'] = request.version
        
        return super().reverse(viewname, args, kwargs, request, format, **extra)


class APIVersionMiddleware:
    """
    Middleware to handle API version compatibility and deprecation warnings
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        response = self.get_response(request)
        
        # Add version headers to API responses
        if request.path.startswith('/api/'):
            version = getattr(request, 'version', 'v1')
            response['API-Version'] = version
            response['API-Supported-Versions'] = ','.join(SchoolERPVersioning.allowed_versions)
            
            # Add deprecation warnings for older versions
            if version == 'v1' and 'v2' in SchoolERPVersioning.allowed_versions:
                response['API-Deprecation-Warning'] = 'API v1 is deprecated. Please migrate to v2.'
        
        return response


def get_api_version_context():
    """
    Get API version context for documentation and responses
    """
    return {
        'current_version': SchoolERPVersioning.default_version,
        'supported_versions': SchoolERPVersioning.allowed_versions,
        'version_format': 'URL path: /api/v{version}/ or Accept header: application/vnd.schoolerp.v{version}+json'
    }


class VersionCompatibilityMixin:
    """
    Mixin to handle version-specific serializer and behavior changes
    """
    
    def get_serializer_class(self):
        """
        Return version-specific serializer class
        """
        serializer_class = super().get_serializer_class()
        version = getattr(self.request, 'version', 'v1')
        
        # Check for version-specific serializer
        version_serializer_name = f"{serializer_class.__name__.replace('Serializer', '')}V{version.upper()}Serializer"
        
        # Try to get version-specific serializer from the same module
        try:
            module = __import__(serializer_class.__module__, fromlist=[version_serializer_name])
            version_serializer = getattr(module, version_serializer_name, None)
            if version_serializer:
                return version_serializer
        except (ImportError, AttributeError):
            pass
        
        return serializer_class
    
    def get_queryset(self):
        """
        Return version-specific queryset if needed
        """
        queryset = super().get_queryset()
        version = getattr(self.request, 'version', 'v1')
        
        # Apply version-specific filtering or optimization
        if hasattr(self, f'get_v{version}_queryset'):
            version_method = getattr(self, f'get_v{version}_queryset')
            return version_method(queryset)
        
        return queryset
    
    def finalize_response(self, request, response, *args, **kwargs):
        """
        Add version-specific response modifications
        """
        response = super().finalize_response(request, response, *args, **kwargs)
        
        # Add version-specific response headers or modifications
        version = getattr(request, 'version', 'v1')
        if hasattr(self, f'modify_v{version}_response'):
            version_method = getattr(self, f'modify_v{version}_response')
            response = version_method(response)
        
        return response


def api_version_required(supported_versions=None):
    """
    Decorator to specify which API versions are supported by a view
    """
    if supported_versions is None:
        supported_versions = SchoolERPVersioning.allowed_versions
    
    def decorator(view_func):
        def wrapper(request, *args, **kwargs):
            version = getattr(request, 'version', 'v1')
            
            if version not in supported_versions:
                return Response(
                    {
                        'error': 'API version not supported',
                        'version': version,
                        'supported_versions': supported_versions
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            return view_func(request, *args, **kwargs)
        
        wrapper.__name__ = view_func.__name__
        wrapper.__doc__ = view_func.__doc__
        return wrapper
    
    return decorator


class APIVersionInfo:
    """
    Class to manage API version information and changelog
    """
    
    VERSION_CHANGELOG = {
        'v1': {
            'release_date': '2025-01-01',
            'status': 'deprecated',
            'features': [
                'Basic CRUD operations for all modules',
                'JWT authentication',
                'Basic filtering and pagination',
                'Multi-language support'
            ],
            'breaking_changes': [],
            'deprecation_date': '2025-06-01'
        },
        'v2': {
            'release_date': '2025-03-01',
            'status': 'stable',
            'features': [
                'Enhanced filtering and search capabilities',
                'Bulk operations support',
                'Real-time notifications via WebSocket',
                'Advanced analytics endpoints',
                'Improved error handling and validation',
                'Rate limiting and throttling',
                'API documentation with OpenAPI 3.0'
            ],
            'breaking_changes': [
                'Changed date format from YYYY-MM-DD to ISO 8601',
                'Renamed some field names for consistency',
                'Modified error response format'
            ]
        }
    }
    
    @classmethod
    def get_version_info(cls, version):
        """Get information about a specific API version"""
        return cls.VERSION_CHANGELOG.get(version, {})
    
    @classmethod
    def get_all_versions(cls):
        """Get information about all API versions"""
        return cls.VERSION_CHANGELOG
    
    @classmethod
    def is_version_deprecated(cls, version):
        """Check if a version is deprecated"""
        version_info = cls.get_version_info(version)
        return version_info.get('status') == 'deprecated'
    
    @classmethod
    def get_latest_version(cls):
        """Get the latest stable API version"""
        for version, info in reversed(cls.VERSION_CHANGELOG.items()):
            if info.get('status') == 'stable':
                return version
        return SchoolERPVersioning.default_version