"""
Core API views and viewsets for School ERP
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.generics import ListAPIView, CreateAPIView, RetrieveUpdateDestroyAPIView
from django_filters.rest_framework import DjangoFilter<PERSON>ackend
from rest_framework.filters import <PERSON><PERSON><PERSON><PERSON>, OrderingFilter
from django.utils import timezone
from django.db.models import Q
from django.core.cache import cache
from .versioning import VersionCompatibilityMixin
from .throttling import ThrottleStatusMixin
from .analytics import analytics_collector, performance_monitor
import logging

logger = logging.getLogger(__name__)


class BaseAPIViewSet(VersionCompatibilityMixin, ThrottleStatusMixin, viewsets.ModelViewSet):
    """
    Base viewset with common functionality for all API endpoints
    """
    
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """
        Get queryset with school filtering and optimization
        """
        queryset = super().get_queryset()
        
        # Apply school filtering if user has school context
        if hasattr(self.request.user, 'school') and self.request.user.school:
            if hasattr(queryset.model, 'school'):
                queryset = queryset.filter(school=self.request.user.school)
        
        # Apply soft delete filtering
        if hasattr(queryset.model, 'is_deleted'):
            queryset = queryset.filter(is_deleted=False)
        
        return queryset
    
    def perform_create(self, serializer):
        """
        Add school context and audit info when creating objects
        """
        # Add school if model has school field
        if hasattr(serializer.Meta.model, 'school') and hasattr(self.request.user, 'school'):
            serializer.save(
                school=self.request.user.school,
                created_by=self.request.user
            )
        else:
            serializer.save(created_by=self.request.user)
    
    def perform_update(self, serializer):
        """
        Add audit info when updating objects
        """
        serializer.save(updated_by=self.request.user)
    
    def perform_destroy(self, instance):
        """
        Soft delete if supported, otherwise hard delete
        """
        if hasattr(instance, 'is_deleted'):
            instance.is_deleted = True
            instance.deleted_by = self.request.user
            instance.deleted_at = timezone.now()
            instance.save()
        else:
            instance.delete()
    
    @action(detail=False, methods=['get'])
    def summary(self, request):
        """
        Get summary statistics for the resource
        """
        queryset = self.get_queryset()
        
        summary_data = {
            'total_count': queryset.count(),
            'active_count': queryset.filter(is_active=True).count() if hasattr(queryset.model, 'is_active') else None,
            'recent_count': queryset.filter(
                created_at__gte=timezone.now() - timezone.timedelta(days=30)
            ).count() if hasattr(queryset.model, 'created_at') else None
        }
        
        # Remove None values
        summary_data = {k: v for k, v in summary_data.items() if v is not None}
        
        return Response(summary_data)
    
    @action(detail=False, methods=['post'])
    def bulk_create(self, request):
        """
        Create multiple objects in bulk
        """
        if not isinstance(request.data, list):
            return Response(
                {'error': 'Expected a list of objects'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        serializer = self.get_serializer(data=request.data, many=True)
        if serializer.is_valid():
            self.perform_bulk_create(serializer)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def perform_bulk_create(self, serializer):
        """
        Perform bulk creation with audit info
        """
        instances = []
        for validated_data in serializer.validated_data:
            if hasattr(serializer.Meta.model, 'school') and hasattr(self.request.user, 'school'):
                validated_data['school'] = self.request.user.school
            validated_data['created_by'] = self.request.user
            instances.append(serializer.Meta.model(**validated_data))
        
        serializer.Meta.model.objects.bulk_create(instances)
    
    @action(detail=False, methods=['patch'])
    def bulk_update(self, request):
        """
        Update multiple objects in bulk
        """
        if not isinstance(request.data, list):
            return Response(
                {'error': 'Expected a list of objects with id field'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        updated_objects = []
        errors = []
        
        for item_data in request.data:
            if 'id' not in item_data:
                errors.append({'error': 'Missing id field', 'data': item_data})
                continue
            
            try:
                instance = self.get_queryset().get(id=item_data['id'])
                serializer = self.get_serializer(instance, data=item_data, partial=True)
                
                if serializer.is_valid():
                    self.perform_update(serializer)
                    updated_objects.append(serializer.data)
                else:
                    errors.append({'id': item_data['id'], 'errors': serializer.errors})
            
            except self.queryset.model.DoesNotExist:
                errors.append({'error': f'Object with id {item_data["id"]} not found'})
        
        response_data = {
            'updated': updated_objects,
            'errors': errors,
            'updated_count': len(updated_objects),
            'error_count': len(errors)
        }
        
        return Response(response_data, status=status.HTTP_200_OK)
    
    @action(detail=False, methods=['delete'])
    def bulk_delete(self, request):
        """
        Delete multiple objects in bulk
        """
        ids = request.data.get('ids', [])
        
        if not isinstance(ids, list) or not ids:
            return Response(
                {'error': 'Expected a list of ids'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        queryset = self.get_queryset().filter(id__in=ids)
        deleted_count = queryset.count()
        
        # Perform bulk soft delete if supported
        if hasattr(queryset.model, 'is_deleted'):
            queryset.update(
                is_deleted=True,
                deleted_by=self.request.user,
                deleted_at=timezone.now()
            )
        else:
            queryset.delete()
        
        return Response({
            'deleted_count': deleted_count,
            'message': f'Successfully deleted {deleted_count} objects'
        })


class APIAnalyticsView(APIView):
    """
    API analytics and monitoring endpoint
    """
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """
        Get API analytics data
        """
        # Check if user has permission to view analytics
        if not request.user.is_staff:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        analytics_type = request.GET.get('type', 'summary')
        date = request.GET.get('date')
        
        if analytics_type == 'summary':
            data = analytics_collector.get_summary_stats()
        elif analytics_type == 'daily':
            data = analytics_collector.get_daily_analytics(date)
        elif analytics_type == 'realtime':
            data = analytics_collector.get_realtime_analytics()
        elif analytics_type == 'performance':
            data = performance_monitor.get_performance_metrics()
        else:
            return Response(
                {'error': 'Invalid analytics type'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        return Response(data)


class APIStatusView(APIView):
    """
    API status and health monitoring
    """
    
    permission_classes = [permissions.AllowAny]
    
    def get(self, request):
        """
        Get API status information
        """
        # Basic status info
        status_info = {
            'status': 'operational',
            'timestamp': timezone.now().isoformat(),
            'version': getattr(request, 'version', 'v1'),
            'environment': 'production' if not settings.DEBUG else 'development'
        }
        
        # Add performance metrics if user is authenticated
        if request.user.is_authenticated:
            try:
                performance_metrics = performance_monitor.get_performance_metrics()
                status_info['performance'] = {
                    'avg_response_time_ms': performance_metrics['avg_response_time_ms'],
                    'error_rate_percent': performance_metrics['error_rate_percent'],
                    'requests_per_minute': performance_metrics['requests_per_minute']
                }
                
                # Check for alerts
                alerts = performance_metrics.get('alerts', [])
                if alerts:
                    status_info['alerts'] = alerts
                    if any(alert.get('severity') == 'critical' for alert in alerts):
                        status_info['status'] = 'degraded'
            
            except Exception as e:
                logger.error(f"Error getting performance metrics: {e}")
                status_info['status'] = 'degraded'
                status_info['error'] = 'Unable to retrieve performance metrics'
        
        return Response(status_info)


class CacheManagementView(APIView):
    """
    Cache management endpoint for administrators
    """
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """
        Get cache statistics
        """
        if not request.user.is_superuser:
            return Response(
                {'error': 'Superuser access required'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Get cache statistics (implementation depends on cache backend)
        cache_stats = {
            'backend': cache.__class__.__name__,
            'operations': {
                'get': 'Available',
                'set': 'Available',
                'delete': 'Available',
                'clear': 'Available'
            }
        }
        
        return Response(cache_stats)
    
    def delete(self, request):
        """
        Clear cache
        """
        if not request.user.is_superuser:
            return Response(
                {'error': 'Superuser access required'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        cache_key = request.data.get('key')
        
        if cache_key:
            # Clear specific cache key
            cache.delete(cache_key)
            message = f'Cache key "{cache_key}" cleared'
        else:
            # Clear all cache
            cache.clear()
            message = 'All cache cleared'
        
        return Response({'message': message})


class BulkOperationsView(APIView):
    """
    Generic bulk operations endpoint
    """
    
    permission_classes = [permissions.IsAuthenticated]
    throttle_endpoint = 'bulk_operations'
    
    def post(self, request):
        """
        Perform bulk operations
        """
        operation = request.data.get('operation')
        model_name = request.data.get('model')
        data = request.data.get('data', [])
        
        if not operation or not model_name:
            return Response(
                {'error': 'operation and model fields are required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Validate operation
        allowed_operations = ['create', 'update', 'delete']
        if operation not in allowed_operations:
            return Response(
                {'error': f'Operation must be one of: {allowed_operations}'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # This would need to be implemented based on specific requirements
        # For now, return a placeholder response
        return Response({
            'message': f'Bulk {operation} operation for {model_name}',
            'operation': operation,
            'model': model_name,
            'data_count': len(data) if isinstance(data, list) else 0,
            'status': 'queued'  # Could be processed asynchronously
        })


class ExportView(APIView):
    """
    Data export endpoint
    """
    
    permission_classes = [permissions.IsAuthenticated]
    throttle_endpoint = 'export'
    
    def post(self, request):
        """
        Export data in various formats
        """
        model_name = request.data.get('model')
        format_type = request.data.get('format', 'json')
        filters = request.data.get('filters', {})
        
        if not model_name:
            return Response(
                {'error': 'model field is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Validate format
        allowed_formats = ['json', 'csv', 'xlsx']
        if format_type not in allowed_formats:
            return Response(
                {'error': f'Format must be one of: {allowed_formats}'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # This would need to be implemented based on specific requirements
        # For now, return a placeholder response
        export_id = f"export_{timezone.now().strftime('%Y%m%d_%H%M%S')}"
        
        return Response({
            'export_id': export_id,
            'model': model_name,
            'format': format_type,
            'filters': filters,
            'status': 'processing',
            'estimated_completion': timezone.now() + timezone.timedelta(minutes=5)
        })
    
    def get(self, request):
        """
        Get export status or download export file
        """
        export_id = request.GET.get('export_id')
        
        if not export_id:
            return Response(
                {'error': 'export_id parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # This would check the actual export status
        # For now, return a placeholder response
        return Response({
            'export_id': export_id,
            'status': 'completed',
            'download_url': f'/api/v2/exports/{export_id}/download/',
            'expires_at': timezone.now() + timezone.timedelta(hours=24)
        })


class ImportView(APIView):
    """
    Data import endpoint
    """
    
    permission_classes = [permissions.IsAuthenticated]
    throttle_endpoint = 'import'
    
    def post(self, request):
        """
        Import data from uploaded file
        """
        model_name = request.data.get('model')
        file_obj = request.FILES.get('file')
        options = request.data.get('options', {})
        
        if not model_name or not file_obj:
            return Response(
                {'error': 'model and file fields are required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Validate file type
        allowed_extensions = ['.csv', '.xlsx', '.json']
        file_extension = file_obj.name.lower().split('.')[-1]
        
        if f'.{file_extension}' not in allowed_extensions:
            return Response(
                {'error': f'File type must be one of: {allowed_extensions}'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # This would process the actual import
        # For now, return a placeholder response
        import_id = f"import_{timezone.now().strftime('%Y%m%d_%H%M%S')}"
        
        return Response({
            'import_id': import_id,
            'model': model_name,
            'filename': file_obj.name,
            'file_size': file_obj.size,
            'options': options,
            'status': 'processing',
            'estimated_completion': timezone.now() + timezone.timedelta(minutes=10)
        })
    
    def get(self, request):
        """
        Get import status
        """
        import_id = request.GET.get('import_id')
        
        if not import_id:
            return Response(
                {'error': 'import_id parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # This would check the actual import status
        # For now, return a placeholder response
        return Response({
            'import_id': import_id,
            'status': 'completed',
            'records_processed': 150,
            'records_created': 140,
            'records_updated': 10,
            'records_failed': 0,
            'errors': [],
            'completed_at': timezone.now()
        })


class SearchView(APIView):
    """
    Global search endpoint
    """
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """
        Perform global search across multiple models
        """
        query = request.GET.get('q', '').strip()
        models = request.GET.getlist('models', [])
        limit = int(request.GET.get('limit', 10))
        
        if not query:
            return Response(
                {'error': 'Query parameter "q" is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if len(query) < 2:
            return Response(
                {'error': 'Query must be at least 2 characters long'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # This would implement actual search across models
        # For now, return a placeholder response
        search_results = {
            'query': query,
            'total_results': 0,
            'results_by_model': {},
            'suggestions': []
        }
        
        # Placeholder search results
        if 'students' in models or not models:
            search_results['results_by_model']['students'] = {
                'count': 5,
                'results': [
                    {'id': 1, 'name': 'John Doe', 'student_id': 'STU001'},
                    {'id': 2, 'name': 'Jane Smith', 'student_id': 'STU002'}
                ]
            }
        
        if 'teachers' in models or not models:
            search_results['results_by_model']['teachers'] = {
                'count': 2,
                'results': [
                    {'id': 1, 'name': 'Prof. Johnson', 'employee_id': 'EMP001'}
                ]
            }
        
        search_results['total_results'] = sum(
            model_results['count'] 
            for model_results in search_results['results_by_model'].values()
        )
        
        return Response(search_results)


class AutocompleteView(APIView):
    """
    Autocomplete endpoint for form fields
    """
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """
        Get autocomplete suggestions
        """
        model_name = request.GET.get('model')
        field = request.GET.get('field')
        query = request.GET.get('q', '').strip()
        limit = int(request.GET.get('limit', 10))
        
        if not model_name or not field:
            return Response(
                {'error': 'model and field parameters are required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if len(query) < 1:
            return Response({'suggestions': []})
        
        # This would implement actual autocomplete
        # For now, return placeholder suggestions
        suggestions = [
            {'value': f'{query}1', 'label': f'{query.title()} Option 1'},
            {'value': f'{query}2', 'label': f'{query.title()} Option 2'},
            {'value': f'{query}3', 'label': f'{query.title()} Option 3'}
        ]
        
        return Response({
            'query': query,
            'model': model_name,
            'field': field,
            'suggestions': suggestions[:limit]
        })


# Middleware for API analytics collection
class APIAnalyticsMiddleware:
    """
    Middleware to collect API analytics
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Record request start time
        start_time = timezone.now()
        
        # Process request
        response = self.get_response(request)
        
        # Calculate duration
        end_time = timezone.now()
        duration_ms = (end_time - start_time).total_seconds() * 1000
        
        # Record analytics for API requests only
        if request.path.startswith('/api/'):
            try:
                view = getattr(request, 'resolver_match', None)
                view_func = view.func if view else None
                
                analytics_collector.record_request(
                    request=request,
                    response=response,
                    view=view_func,
                    duration_ms=duration_ms
                )
            except Exception as e:
                logger.error(f"Error recording API analytics: {e}")
        
        return response