"""
Audit middleware for automatic event logging
"""
import time
import json
from django.utils.deprecation import MiddlewareMixin
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone
from django.conf import settings
from django.db import transaction
from django.core.exceptions import ObjectDoesNotExist
import logging

from core.audit.audit_system import get_audit_logger
from core.models import School

logger = logging.getLogger(__name__)


class AuditMiddleware(MiddlewareMixin):
    """
    Middleware to automatically log audit events
    """
    
    def __init__(self, get_response):
        super().__init__(get_response)
        self.get_response = get_response
        
        # Paths to exclude from auditing
        self.excluded_paths = getattr(settings, 'AUDIT_EXCLUDED_PATHS', [
            '/admin/jsi18n/',
            '/static/',
            '/media/',
            '/favicon.ico',
            '/health/',
            '/metrics/',
        ])
        
        # Methods to audit
        self.audited_methods = getattr(settings, 'AUDIT_METHODS', [
            'POST', 'PUT', 'PATCH', 'DELETE'
        ])
    
    def process_request(self, request):
        """
        Process incoming request
        """
        # Skip if path is excluded
        if self._should_exclude_path(request.path):
            return None
        
        # Store request start time for duration calculation
        request._audit_start_time = time.time()
        
        # Store original request body for change tracking
        if hasattr(request, 'body') and request.method in self.audited_methods:
            try:
                request._audit_original_body = request.body.decode('utf-8')
            except (UnicodeDecodeError, AttributeError):
                request._audit_original_body = None
        
        return None
    
    def process_response(self, request, response):
        """
        Process response and log audit event
        """
        # Skip if path is excluded or method not audited
        if (self._should_exclude_path(request.path) or 
            request.method not in self.audited_methods):
            return response
        
        # Skip if no authenticated user (unless configured otherwise)
        if not hasattr(request, 'user') or not request.user.is_authenticated:
            if not getattr(settings, 'AUDIT_ANONYMOUS_REQUESTS', False):
                return response
        
        # Get school context
        school = self._get_school_from_request(request)
        if not school:
            return response
        
        # Calculate request duration
        duration_ms = None
        if hasattr(request, '_audit_start_time'):
            duration_ms = int((time.time() - request._audit_start_time) * 1000)
        
        # Log the audit event
        try:
            self._log_request_event(request, response, school, duration_ms)
        except Exception as e:
            logger.error(f"Failed to log audit event: {e}")
        
        return response
    
    def process_exception(self, request, exception):
        """
        Process exceptions and log error events
        """
        # Skip if path is excluded
        if self._should_exclude_path(request.path):
            return None
        
        # Get school context
        school = self._get_school_from_request(request)
        if not school:
            return None
        
        # Calculate request duration
        duration_ms = None
        if hasattr(request, '_audit_start_time'):
            duration_ms = int((time.time() - request._audit_start_time) * 1000)
        
        # Log the exception event
        try:
            audit_logger = get_audit_logger(school)
            audit_logger.log_event(
                request=request,
                event_type='ERROR',
                event_category='System Error',
                description=f"Exception occurred: {str(exception)}",
                severity='HIGH',
                risk_level='MEDIUM',
                duration_ms=duration_ms,
                is_successful=False,
                error_message=str(exception),
                metadata={
                    'exception_type': exception.__class__.__name__,
                    'exception_module': exception.__class__.__module__,
                }
            )
        except Exception as e:
            logger.error(f"Failed to log exception audit event: {e}")
        
        return None
    
    def _should_exclude_path(self, path):
        """
        Check if path should be excluded from auditing
        """
        return any(path.startswith(excluded) for excluded in self.excluded_paths)
    
    def _get_school_from_request(self, request):
        """
        Get school context from request
        """
        # Try to get school from user
        if hasattr(request, 'user') and request.user.is_authenticated:
            # Check employee relationship
            if hasattr(request.user, 'employee') and request.user.employee:
                return request.user.employee.school
            
            # Check student relationship
            if hasattr(request.user, 'student') and request.user.student:
                return request.user.student.school
            
            # Check parent relationship
            if hasattr(request.user, 'parent') and request.user.parent:
                return request.user.parent.school
        
        # Try to get school from session or request
        if hasattr(request, 'school'):
            return request.school
        
        # Try to get from session
        school_id = request.session.get('school_id')
        if school_id:
            try:
                return School.objects.get(id=school_id)
            except School.DoesNotExist:
                pass
        
        # Default to first school if only one exists (for single-tenant setups)
        schools = School.objects.filter(is_active=True)
        if schools.count() == 1:
            return schools.first()
        
        return None
    
    def _log_request_event(self, request, response, school, duration_ms):
        """
        Log audit event for the request
        """
        audit_logger = get_audit_logger(school)
        
        # Determine event type based on method and response
        event_type = self._determine_event_type(request, response)
        
        # Determine event category based on URL
        event_category = self._determine_event_category(request.path)
        
        # Create event description
        description = self._create_event_description(request, response)
        
        # Determine severity
        severity = self._determine_severity(request, response)
        
        # Extract metadata
        metadata = self._extract_metadata(request, response)
        
        # Check if successful
        is_successful = 200 <= response.status_code < 400
        
        # Error message for failed requests
        error_message = ''
        if not is_successful:
            error_message = f"HTTP {response.status_code}"
            if hasattr(response, 'content'):
                try:
                    content = response.content.decode('utf-8')[:500]  # Limit length
                    error_message += f": {content}"
                except:
                    pass
        
        # Log the event
        audit_logger.log_event(
            request=request,
            event_type=event_type,
            event_category=event_category,
            description=description,
            severity=severity,
            duration_ms=duration_ms,
            is_successful=is_successful,
            error_message=error_message,
            metadata=metadata,
            compliance_relevant=self._is_compliance_relevant(request, response)
        )
    
    def _determine_event_type(self, request, response):
        """
        Determine event type based on request method and response
        """
        method_map = {
            'POST': 'CREATE',
            'PUT': 'UPDATE',
            'PATCH': 'UPDATE',
            'DELETE': 'DELETE',
            'GET': 'VIEW'
        }
        
        return method_map.get(request.method, 'CUSTOM')
    
    def _determine_event_category(self, path):
        """
        Determine event category based on URL path
        """
        # Map URL patterns to categories
        category_patterns = {
            'Student Management': ['/students/', '/student/', '/enrollment/'],
            'Academic Management': ['/academics/', '/grades/', '/courses/', '/curriculum/'],
            'Financial Management': ['/finance/', '/payments/', '/fees/', '/accounting/'],
            'HR Management': ['/hr/', '/employees/', '/staff/', '/payroll/'],
            'User Management': ['/users/', '/accounts/', '/auth/', '/permissions/'],
            'System Administration': ['/admin/', '/settings/', '/config/'],
            'Reports': ['/reports/', '/analytics/', '/dashboard/'],
            'Communication': ['/messages/', '/notifications/', '/announcements/'],
            'Library Management': ['/library/', '/books/', '/resources/'],
            'Transportation': ['/transportation/', '/buses/', '/routes/'],
            'Health Management': ['/health/', '/medical/', '/wellness/'],
            'Inventory Management': ['/inventory/', '/assets/', '/equipment/'],
        }
        
        for category, patterns in category_patterns.items():
            if any(pattern in path.lower() for pattern in patterns):
                return category
        
        return 'General'
    
    def _create_event_description(self, request, response):
        """
        Create human-readable event description
        """
        method = request.method
        path = request.path
        status = response.status_code
        
        # Extract resource name from path
        resource = self._extract_resource_name(path)
        
        action_map = {
            'POST': f'Created {resource}',
            'PUT': f'Updated {resource}',
            'PATCH': f'Modified {resource}',
            'DELETE': f'Deleted {resource}',
            'GET': f'Viewed {resource}'
        }
        
        description = action_map.get(method, f'{method} request to {path}')
        
        if not (200 <= status < 400):
            description += f' (Failed with status {status})'
        
        return description
    
    def _extract_resource_name(self, path):
        """
        Extract resource name from URL path
        """
        # Remove leading/trailing slashes and split
        parts = path.strip('/').split('/')
        
        # Common resource patterns
        if len(parts) >= 2:
            # Look for resource names in common positions
            for part in parts:
                if part and not part.isdigit() and part not in ['api', 'v1', 'v2']:
                    return part.replace('-', ' ').replace('_', ' ').title()
        
        return 'Resource'
    
    def _determine_severity(self, request, response):
        """
        Determine event severity
        """
        # High severity for errors
        if response.status_code >= 500:
            return 'HIGH'
        elif response.status_code >= 400:
            return 'MEDIUM'
        
        # High severity for sensitive operations
        if request.method in ['DELETE']:
            return 'HIGH'
        elif request.method in ['POST', 'PUT', 'PATCH']:
            return 'MEDIUM'
        
        return 'LOW'
    
    def _extract_metadata(self, request, response):
        """
        Extract additional metadata from request/response
        """
        metadata = {
            'status_code': response.status_code,
            'content_type': response.get('Content-Type', ''),
            'content_length': response.get('Content-Length', ''),
        }
        
        # Add request parameters (excluding sensitive data)
        if request.GET:
            metadata['query_params'] = dict(request.GET)
        
        # Add form data for POST requests (excluding sensitive fields)
        if request.method == 'POST' and hasattr(request, 'POST'):
            post_data = dict(request.POST)
            # Remove sensitive fields
            sensitive_fields = ['password', 'token', 'secret', 'key', 'csrf']
            for field in sensitive_fields:
                post_data.pop(field, None)
            metadata['form_data'] = post_data
        
        return metadata
    
    def _is_compliance_relevant(self, request, response):
        """
        Determine if request is compliance relevant
        """
        # Compliance relevant paths
        compliance_paths = [
            '/students/', '/grades/', '/payments/', '/users/', '/admin/',
            '/reports/', '/export/', '/backup/'
        ]
        
        return any(path in request.path.lower() for path in compliance_paths)


class ModelAuditMiddleware(MiddlewareMixin):
    """
    Middleware to audit model changes using Django signals
    """
    
    def __init__(self, get_response):
        super().__init__(get_response)
        self.get_response = get_response
        self._setup_model_signals()
    
    def _setup_model_signals(self):
        """
        Setup Django model signals for auditing
        """
        from django.db.models.signals import post_save, post_delete, pre_save
        from django.dispatch import receiver
        from django.apps import apps
        
        # Get models to audit
        audited_models = getattr(settings, 'AUDIT_MODELS', [])
        
        if not audited_models:
            # Default to all models if none specified
            audited_models = ['__all__']
        
        @receiver(pre_save)
        def audit_pre_save(sender, instance, **kwargs):
            """
            Capture old values before save
            """
            if not self._should_audit_model(sender):
                return
            
            if instance.pk:  # Existing instance
                try:
                    old_instance = sender.objects.get(pk=instance.pk)
                    instance._audit_old_values = self._get_model_values(old_instance)
                except sender.DoesNotExist:
                    instance._audit_old_values = {}
            else:
                instance._audit_old_values = {}
        
        @receiver(post_save)
        def audit_post_save(sender, instance, created, **kwargs):
            """
            Log create/update events
            """
            if not self._should_audit_model(sender):
                return
            
            # Get current request from thread local storage
            request = getattr(self._local, 'request', None)
            if not request:
                return
            
            # Get school context
            school = self._get_school_from_instance(instance)
            if not school:
                return
            
            # Get audit logger
            audit_logger = get_audit_logger(school)
            
            # Determine action
            action = 'create' if created else 'update'
            
            # Get old and new values
            old_values = getattr(instance, '_audit_old_values', {})
            new_values = self._get_model_values(instance)
            
            # Log the change
            audit_logger.log_model_change(
                request=request,
                instance=instance,
                action=action,
                old_values=old_values,
                new_values=new_values
            )
        
        @receiver(post_delete)
        def audit_post_delete(sender, instance, **kwargs):
            """
            Log delete events
            """
            if not self._should_audit_model(sender):
                return
            
            # Get current request from thread local storage
            request = getattr(self._local, 'request', None)
            if not request:
                return
            
            # Get school context
            school = self._get_school_from_instance(instance)
            if not school:
                return
            
            # Get audit logger
            audit_logger = get_audit_logger(school)
            
            # Get model values
            old_values = self._get_model_values(instance)
            
            # Log the deletion
            audit_logger.log_model_change(
                request=request,
                instance=instance,
                action='delete',
                old_values=old_values,
                new_values={}
            )
    
    def process_request(self, request):
        """
        Store request in thread local storage
        """
        import threading
        if not hasattr(self, '_local'):
            self._local = threading.local()
        
        self._local.request = request
        return None
    
    def _should_audit_model(self, model_class):
        """
        Check if model should be audited
        """
        audited_models = getattr(settings, 'AUDIT_MODELS', ['__all__'])
        
        if '__all__' in audited_models:
            # Exclude certain models by default
            excluded_models = getattr(settings, 'AUDIT_EXCLUDED_MODELS', [
                'Session', 'LogEntry', 'ContentType', 'Permission',
                'AuditEvent', 'AuditTrail', 'ComplianceViolation'
            ])
            return model_class.__name__ not in excluded_models
        
        return model_class.__name__ in audited_models
    
    def _get_school_from_instance(self, instance):
        """
        Get school from model instance
        """
        # Check if instance has school field
        if hasattr(instance, 'school'):
            return instance.school
        
        # Check if instance is a school
        if instance.__class__.__name__ == 'School':
            return instance
        
        # Try to get school from related fields
        for field in instance._meta.fields:
            if field.name.endswith('_school') or 'school' in field.name.lower():
                school = getattr(instance, field.name, None)
                if school:
                    return school
        
        return None
    
    def _get_model_values(self, instance):
        """
        Get model field values as dictionary
        """
        values = {}
        
        for field in instance._meta.fields:
            field_name = field.name
            try:
                field_value = getattr(instance, field_name)
                
                # Convert to serializable format
                if hasattr(field_value, 'pk'):
                    values[field_name] = str(field_value.pk)
                elif isinstance(field_value, (list, dict)):
                    values[field_name] = field_value
                else:
                    values[field_name] = str(field_value) if field_value is not None else None
            except Exception:
                # Skip fields that can't be accessed
                continue
        
        return values