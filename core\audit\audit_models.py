"""
Audit system models for School ERP
"""
from django.db import models
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.core.serializers.json import DjangoJSONEncoder
import uuid
import json

User = get_user_model()


class AuditEvent(models.Model):
    """
    Comprehensive audit event model
    """
    EVENT_TYPES = (
        ('CREATE', _('Create')),
        ('UPDATE', _('Update')),
        ('DELETE', _('Delete')),
        ('VIEW', _('View')),
        ('LOGIN', _('Login')),
        ('LOGOUT', _('Logout')),
        ('EXPORT', _('Export')),
        ('IMPORT', _('Import')),
        ('PRINT', _('Print')),
        ('EMAIL', _('Email')),
        ('BACKUP', _('Backup')),
        ('RESTORE', _('Restore')),
        ('PERMISSION_CHANGE', _('Permission Change')),
        ('SYSTEM_CONFIG', _('System Configuration')),
        ('DATA_MIGRATION', _('Data Migration')),
        ('SECURITY_EVENT', _('Security Event')),
        ('COMPLIANCE_CHECK', _('Compliance Check')),
        ('CUSTOM', _('Custom Event')),
    )
    
    SEVERITY_LEVELS = (
        ('LOW', _('Low')),
        ('MEDIUM', _('Medium')),
        ('HIGH', _('High')),
        ('CRITICAL', _('Critical')),
    )
    
    RISK_LEVELS = (
        ('NONE', _('None')),
        ('LOW', _('Low')),
        ('MEDIUM', _('Medium')),
        ('HIGH', _('High')),
        ('CRITICAL', _('Critical')),
    )
    
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )
    
    school = models.ForeignKey(
        'core.School',
        on_delete=models.CASCADE,
        verbose_name=_('School'),
        db_index=True
    )
    
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('User'),
        db_index=True
    )
    
    event_type = models.CharField(
        max_length=50,
        choices=EVENT_TYPES,
        verbose_name=_('Event Type'),
        db_index=True
    )
    
    event_category = models.CharField(
        max_length=100,
        verbose_name=_('Event Category'),
        help_text=_('Specific category like "Student Management", "Financial"'),
        db_index=True
    )
    
    event_description = models.TextField(
        verbose_name=_('Event Description')
    )
    
    # Generic foreign key to any model
    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Content Type')
    )
    object_id = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_('Object ID')
    )
    content_object = GenericForeignKey('content_type', 'object_id')
    
    object_repr = models.CharField(
        max_length=500,
        blank=True,
        verbose_name=_('Object Representation')
    )
    
    # Change tracking
    old_values = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Old Values'),
        encoder=DjangoJSONEncoder
    )
    
    new_values = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('New Values'),
        encoder=DjangoJSONEncoder
    )
    
    changed_fields = models.JSONField(
        default=list,
        blank=True,
        verbose_name=_('Changed Fields')
    )
    
    # Request information
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name=_('IP Address'),
        db_index=True
    )
    
    user_agent = models.TextField(
        blank=True,
        verbose_name=_('User Agent')
    )
    
    session_key = models.CharField(
        max_length=40,
        blank=True,
        verbose_name=_('Session Key')
    )
    
    request_path = models.CharField(
        max_length=500,
        blank=True,
        verbose_name=_('Request Path')
    )
    
    request_method = models.CharField(
        max_length=10,
        blank=True,
        verbose_name=_('Request Method')
    )
    
    # Additional metadata
    metadata = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Additional Metadata'),
        encoder=DjangoJSONEncoder
    )
    
    # Risk and compliance
    severity = models.CharField(
        max_length=20,
        choices=SEVERITY_LEVELS,
        default='LOW',
        verbose_name=_('Severity Level'),
        db_index=True
    )
    
    risk_level = models.CharField(
        max_length=20,
        choices=RISK_LEVELS,
        default='NONE',
        verbose_name=_('Risk Level'),
        db_index=True
    )
    
    compliance_relevant = models.BooleanField(
        default=False,
        verbose_name=_('Compliance Relevant'),
        db_index=True
    )
    
    # Timing
    timestamp = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Timestamp'),
        db_index=True
    )
    
    duration_ms = models.IntegerField(
        null=True,
        blank=True,
        verbose_name=_('Duration (milliseconds)')
    )
    
    # Status
    is_successful = models.BooleanField(
        default=True,
        verbose_name=_('Is Successful'),
        db_index=True
    )
    
    error_message = models.TextField(
        blank=True,
        verbose_name=_('Error Message')
    )
    
    # Flags
    is_sensitive = models.BooleanField(
        default=False,
        verbose_name=_('Contains Sensitive Data'),
        db_index=True
    )
    
    requires_review = models.BooleanField(
        default=False,
        verbose_name=_('Requires Review'),
        db_index=True
    )
    
    is_reviewed = models.BooleanField(
        default=False,
        verbose_name=_('Is Reviewed'),
        db_index=True
    )
    
    reviewed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='reviewed_audit_events',
        verbose_name=_('Reviewed By')
    )
    
    reviewed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Reviewed At')
    )
    
    review_notes = models.TextField(
        blank=True,
        verbose_name=_('Review Notes')
    )

    class Meta:
        verbose_name = _('Audit Event')
        verbose_name_plural = _('Audit Events')
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['school', 'timestamp']),
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['event_type', 'timestamp']),
            models.Index(fields=['event_category', 'timestamp']),
            models.Index(fields=['ip_address', 'timestamp']),
            models.Index(fields=['severity', 'timestamp']),
            models.Index(fields=['risk_level', 'timestamp']),
            models.Index(fields=['compliance_relevant', 'timestamp']),
            models.Index(fields=['requires_review', 'is_reviewed']),
            models.Index(fields=['content_type', 'object_id']),
        ]

    def __str__(self):
        return f"{self.event_type} - {self.event_category} - {self.timestamp}"
    
    def get_changes_summary(self):
        """
        Get a summary of changes made
        """
        if not self.changed_fields:
            return "No changes recorded"
        
        changes = []
        for field in self.changed_fields:
            old_val = self.old_values.get(field, 'N/A')
            new_val = self.new_values.get(field, 'N/A')
            changes.append(f"{field}: {old_val} → {new_val}")
        
        return "; ".join(changes)
    
    def mark_as_reviewed(self, reviewer, notes=""):
        """
        Mark audit event as reviewed
        """
        self.is_reviewed = True
        self.reviewed_by = reviewer
        self.reviewed_at = timezone.now()
        self.review_notes = notes
        self.save(update_fields=['is_reviewed', 'reviewed_by', 'reviewed_at', 'review_notes'])


class AuditTrail(models.Model):
    """
    Audit trail for tracking related events
    """
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )
    
    school = models.ForeignKey(
        'core.School',
        on_delete=models.CASCADE,
        verbose_name=_('School')
    )
    
    trail_name = models.CharField(
        max_length=200,
        verbose_name=_('Trail Name')
    )
    
    description = models.TextField(
        blank=True,
        verbose_name=_('Description')
    )
    
    events = models.ManyToManyField(
        AuditEvent,
        through='AuditTrailEvent',
        verbose_name=_('Events')
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )
    
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Created By')
    )

    class Meta:
        verbose_name = _('Audit Trail')
        verbose_name_plural = _('Audit Trails')
        ordering = ['-created_at']

    def __str__(self):
        return self.trail_name


class AuditTrailEvent(models.Model):
    """
    Through model for audit trail events
    """
    trail = models.ForeignKey(
        AuditTrail,
        on_delete=models.CASCADE,
        verbose_name=_('Trail')
    )
    
    event = models.ForeignKey(
        AuditEvent,
        on_delete=models.CASCADE,
        verbose_name=_('Event')
    )
    
    sequence_number = models.IntegerField(
        verbose_name=_('Sequence Number')
    )
    
    added_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Added At')
    )

    class Meta:
        verbose_name = _('Audit Trail Event')
        verbose_name_plural = _('Audit Trail Events')
        ordering = ['sequence_number']
        unique_together = ['trail', 'sequence_number']


class ComplianceRule(models.Model):
    """
    Compliance rules for audit monitoring
    """
    RULE_TYPES = (
        ('DATA_ACCESS', _('Data Access')),
        ('DATA_MODIFICATION', _('Data Modification')),
        ('USER_ACTIVITY', _('User Activity')),
        ('SYSTEM_CHANGE', _('System Change')),
        ('SECURITY_EVENT', _('Security Event')),
        ('FINANCIAL_TRANSACTION', _('Financial Transaction')),
        ('STUDENT_RECORD', _('Student Record')),
        ('CUSTOM', _('Custom Rule')),
    )
    
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )
    
    school = models.ForeignKey(
        'core.School',
        on_delete=models.CASCADE,
        verbose_name=_('School')
    )
    
    name = models.CharField(
        max_length=200,
        verbose_name=_('Rule Name')
    )
    
    description = models.TextField(
        verbose_name=_('Description')
    )
    
    rule_type = models.CharField(
        max_length=50,
        choices=RULE_TYPES,
        verbose_name=_('Rule Type')
    )
    
    conditions = models.JSONField(
        default=dict,
        verbose_name=_('Rule Conditions'),
        help_text=_('JSON conditions for rule matching')
    )
    
    actions = models.JSONField(
        default=list,
        verbose_name=_('Actions'),
        help_text=_('Actions to take when rule matches')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    
    severity = models.CharField(
        max_length=20,
        choices=AuditEvent.SEVERITY_LEVELS,
        default='MEDIUM',
        verbose_name=_('Severity Level')
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )
    
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Created By')
    )
    
    last_triggered = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Last Triggered')
    )
    
    trigger_count = models.IntegerField(
        default=0,
        verbose_name=_('Trigger Count')
    )

    class Meta:
        verbose_name = _('Compliance Rule')
        verbose_name_plural = _('Compliance Rules')
        ordering = ['name']

    def __str__(self):
        return self.name
    
    def evaluate(self, audit_event):
        """
        Evaluate if this rule matches the audit event
        """
        # This would contain the logic to evaluate conditions
        # For now, return False as placeholder
        return False


class ComplianceViolation(models.Model):
    """
    Compliance violations detected by rules
    """
    VIOLATION_STATUS = (
        ('OPEN', _('Open')),
        ('INVESTIGATING', _('Investigating')),
        ('RESOLVED', _('Resolved')),
        ('FALSE_POSITIVE', _('False Positive')),
        ('ACKNOWLEDGED', _('Acknowledged')),
    )
    
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )
    
    school = models.ForeignKey(
        'core.School',
        on_delete=models.CASCADE,
        verbose_name=_('School')
    )
    
    rule = models.ForeignKey(
        ComplianceRule,
        on_delete=models.CASCADE,
        verbose_name=_('Compliance Rule')
    )
    
    audit_event = models.ForeignKey(
        AuditEvent,
        on_delete=models.CASCADE,
        verbose_name=_('Audit Event')
    )
    
    violation_description = models.TextField(
        verbose_name=_('Violation Description')
    )
    
    severity = models.CharField(
        max_length=20,
        choices=AuditEvent.SEVERITY_LEVELS,
        verbose_name=_('Severity Level')
    )
    
    status = models.CharField(
        max_length=20,
        choices=VIOLATION_STATUS,
        default='OPEN',
        verbose_name=_('Status')
    )
    
    detected_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Detected At')
    )
    
    assigned_to = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_violations',
        verbose_name=_('Assigned To')
    )
    
    resolved_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Resolved At')
    )
    
    resolved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='resolved_violations',
        verbose_name=_('Resolved By')
    )
    
    resolution_notes = models.TextField(
        blank=True,
        verbose_name=_('Resolution Notes')
    )

    class Meta:
        verbose_name = _('Compliance Violation')
        verbose_name_plural = _('Compliance Violations')
        ordering = ['-detected_at']
        indexes = [
            models.Index(fields=['school', 'status']),
            models.Index(fields=['severity', 'status']),
            models.Index(fields=['detected_at']),
        ]

    def __str__(self):
        return f"{self.rule.name} - {self.detected_at}"
    
    def resolve(self, resolver, notes=""):
        """
        Mark violation as resolved
        """
        self.status = 'RESOLVED'
        self.resolved_by = resolver
        self.resolved_at = timezone.now()
        self.resolution_notes = notes
        self.save(update_fields=['status', 'resolved_by', 'resolved_at', 'resolution_notes'])


class AuditReport(models.Model):
    """
    Generated audit reports
    """
    REPORT_TYPES = (
        ('ACTIVITY_SUMMARY', _('Activity Summary')),
        ('USER_ACTIVITY', _('User Activity')),
        ('DATA_ACCESS', _('Data Access')),
        ('SECURITY_EVENTS', _('Security Events')),
        ('COMPLIANCE_REPORT', _('Compliance Report')),
        ('VIOLATION_REPORT', _('Violation Report')),
        ('CUSTOM', _('Custom Report')),
    )
    
    REPORT_FORMATS = (
        ('PDF', _('PDF')),
        ('EXCEL', _('Excel')),
        ('CSV', _('CSV')),
        ('JSON', _('JSON')),
    )
    
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )
    
    school = models.ForeignKey(
        'core.School',
        on_delete=models.CASCADE,
        verbose_name=_('School')
    )
    
    name = models.CharField(
        max_length=200,
        verbose_name=_('Report Name')
    )
    
    report_type = models.CharField(
        max_length=50,
        choices=REPORT_TYPES,
        verbose_name=_('Report Type')
    )
    
    description = models.TextField(
        blank=True,
        verbose_name=_('Description')
    )
    
    filters = models.JSONField(
        default=dict,
        verbose_name=_('Report Filters')
    )
    
    date_from = models.DateTimeField(
        verbose_name=_('Date From')
    )
    
    date_to = models.DateTimeField(
        verbose_name=_('Date To')
    )
    
    format = models.CharField(
        max_length=10,
        choices=REPORT_FORMATS,
        default='PDF',
        verbose_name=_('Format')
    )
    
    file_path = models.CharField(
        max_length=500,
        blank=True,
        verbose_name=_('File Path')
    )
    
    file_size = models.IntegerField(
        null=True,
        blank=True,
        verbose_name=_('File Size (bytes)')
    )
    
    generated_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Generated At')
    )
    
    generated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Generated By')
    )
    
    is_scheduled = models.BooleanField(
        default=False,
        verbose_name=_('Is Scheduled Report')
    )
    
    schedule_config = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Schedule Configuration')
    )

    class Meta:
        verbose_name = _('Audit Report')
        verbose_name_plural = _('Audit Reports')
        ordering = ['-generated_at']

    def __str__(self):
        return f"{self.name} - {self.generated_at}"


class AuditConfiguration(models.Model):
    """
    Audit system configuration per school
    """
    school = models.OneToOneField(
        'core.School',
        on_delete=models.CASCADE,
        related_name='audit_config',
        verbose_name=_('School')
    )
    
    # Retention settings
    retention_days = models.IntegerField(
        default=2555,  # 7 years
        verbose_name=_('Retention Days'),
        help_text=_('Number of days to retain audit logs')
    )
    
    # Event types to audit
    audit_create_events = models.BooleanField(
        default=True,
        verbose_name=_('Audit Create Events')
    )
    
    audit_update_events = models.BooleanField(
        default=True,
        verbose_name=_('Audit Update Events')
    )
    
    audit_delete_events = models.BooleanField(
        default=True,
        verbose_name=_('Audit Delete Events')
    )
    
    audit_view_events = models.BooleanField(
        default=False,
        verbose_name=_('Audit View Events')
    )
    
    audit_login_events = models.BooleanField(
        default=True,
        verbose_name=_('Audit Login Events')
    )
    
    audit_export_events = models.BooleanField(
        default=True,
        verbose_name=_('Audit Export Events')
    )
    
    # Sensitive data handling
    mask_sensitive_data = models.BooleanField(
        default=True,
        verbose_name=_('Mask Sensitive Data')
    )
    
    sensitive_fields = models.JSONField(
        default=list,
        verbose_name=_('Sensitive Fields'),
        help_text=_('List of field names considered sensitive')
    )
    
    # Compliance settings
    enable_compliance_monitoring = models.BooleanField(
        default=True,
        verbose_name=_('Enable Compliance Monitoring')
    )
    
    compliance_frameworks = models.JSONField(
        default=list,
        verbose_name=_('Compliance Frameworks'),
        help_text=_('List of compliance frameworks to monitor')
    )
    
    # Notification settings
    notify_on_violations = models.BooleanField(
        default=True,
        verbose_name=_('Notify on Violations')
    )
    
    violation_notification_emails = models.JSONField(
        default=list,
        verbose_name=_('Violation Notification Emails')
    )
    
    # Performance settings
    async_logging = models.BooleanField(
        default=True,
        verbose_name=_('Asynchronous Logging')
    )
    
    batch_size = models.IntegerField(
        default=100,
        verbose_name=_('Batch Size'),
        help_text=_('Number of events to process in batch')
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('Updated At')
    )

    class Meta:
        verbose_name = _('Audit Configuration')
        verbose_name_plural = _('Audit Configurations')

    def __str__(self):
        return f"Audit Config - {self.school.name}"