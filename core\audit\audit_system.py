"""
Comprehensive audit system for School ERP
"""
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db.models import Count, Q
from django.core.serializers.json import DjangoJSONEncoder
from django.conf import settings
from celery import shared_task

from core.audit.audit_models import (
    AuditEvent, AuditTrail, ComplianceRule, ComplianceViolation,
    AuditReport, AuditConfiguration
)
from core.encryption_management.encryption import DataMasking

logger = logging.getLogger(__name__)
User = get_user_model()


class AuditLogger:
    """
    Central audit logging system
    """
    
    def __init__(self, school):
        self.school = school
        self.config = self._get_audit_config()
    
    def _get_audit_config(self):
        """
        Get audit configuration for the school
        """
        try:
            return AuditConfiguration.objects.get(school=self.school)
        except AuditConfiguration.DoesNotExist:
            # Create default configuration
            try:
                return AuditConfiguration.objects.create(school=self.school)
            except Exception:
                # Return a mock config if table doesn't exist (for testing)
                class MockConfig:
                    def __init__(self):
                        self.retention_days = 365
                        self.audit_create_events = True
                        self.audit_update_events = True
                        self.audit_delete_events = True
                        self.audit_view_events = False
                        self.audit_login_events = True
                        self.audit_export_events = True
                        self.mask_sensitive_data = True
                        self.sensitive_fields = []
                        self.enable_compliance_monitoring = True
                        self.notify_on_violations = True
                        self.violation_notification_emails = []
                        self.async_logging = True
                        self.batch_size = 100
                
                return MockConfig()
        except Exception:
            # Return mock config for any other errors
            class MockConfig:
                def __init__(self):
                    self.retention_days = 365
                    self.audit_create_events = True
                    self.audit_update_events = True
                    self.audit_delete_events = True
                    self.audit_view_events = False
                    self.audit_login_events = True
                    self.audit_export_events = True
                    self.mask_sensitive_data = True
                    self.sensitive_fields = []
                    self.enable_compliance_monitoring = True
                    self.notify_on_violations = True
                    self.violation_notification_emails = []
                    self.async_logging = True
                    self.batch_size = 100
            
            return MockConfig()
    
    def log_event(self, request=None, event_type='CUSTOM', event_category='General',
                  description='', object_instance=None, old_values=None, new_values=None,
                  changed_fields=None, severity='LOW', risk_level='NONE',
                  compliance_relevant=False, metadata=None, duration_ms=None,
                  is_successful=True, error_message='', requires_review=False):
        """
        Log an audit event
        
        Args:
            request: Django request object
            event_type: Type of event (CREATE, UPDATE, DELETE, etc.)
            event_category: Category of the event
            description: Human-readable description
            object_instance: The object being audited
            old_values: Previous values (for updates)
            new_values: New values (for updates)
            changed_fields: List of changed field names
            severity: Severity level
            risk_level: Risk level
            compliance_relevant: Whether this event is compliance relevant
            metadata: Additional metadata
            duration_ms: Duration in milliseconds
            is_successful: Whether the operation was successful
            error_message: Error message if unsuccessful
            requires_review: Whether this event requires manual review
            
        Returns:
            AuditEvent instance
        """
        # Check if this event type should be audited
        if not self._should_audit_event(event_type):
            return None
        
        # Extract request information
        user = None
        ip_address = None
        user_agent = ''
        session_key = ''
        request_path = ''
        request_method = ''
        
        if request:
            user = getattr(request, 'user', None) if hasattr(request, 'user') else None
            if user and not user.is_authenticated:
                user = None
            
            ip_address = self._get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '')
            session_key = getattr(request.session, 'session_key', '') if hasattr(request, 'session') else ''
            request_path = getattr(request, 'path', '')
            request_method = getattr(request, 'method', '')
        
        # Handle object information
        content_type = None
        object_id = None
        object_repr = ''
        
        if object_instance:
            content_type = ContentType.objects.get_for_model(object_instance)
            object_id = str(object_instance.pk)
            object_repr = str(object_instance)[:500]  # Limit length
        
        # Process values for sensitive data
        processed_old_values = self._process_sensitive_data(old_values or {})
        processed_new_values = self._process_sensitive_data(new_values or {})
        
        # Determine if event contains sensitive data
        is_sensitive = self._contains_sensitive_data(
            processed_old_values, processed_new_values, changed_fields or []
        )
        
        # Create audit event
        event = AuditEvent.objects.create(
            school=self.school,
            user=user,
            event_type=event_type,
            event_category=event_category,
            event_description=description,
            content_type=content_type,
            object_id=object_id,
            object_repr=object_repr,
            old_values=processed_old_values,
            new_values=processed_new_values,
            changed_fields=changed_fields or [],
            ip_address=ip_address,
            user_agent=user_agent,
            session_key=session_key,
            request_path=request_path,
            request_method=request_method,
            metadata=metadata or {},
            severity=severity,
            risk_level=risk_level,
            compliance_relevant=compliance_relevant,
            duration_ms=duration_ms,
            is_successful=is_successful,
            error_message=error_message,
            is_sensitive=is_sensitive,
            requires_review=requires_review
        )
        
        # Trigger compliance monitoring if enabled
        if self.config.enable_compliance_monitoring:
            self._trigger_compliance_check(event)
        
        logger.info(f"Audit event logged: {event.id} - {event_type} - {event_category}")
        return event
    
    def log_model_change(self, request, instance, action, old_values=None, new_values=None):
        """
        Log model change event
        
        Args:
            request: Django request object
            instance: Model instance
            action: Action performed (create, update, delete)
            old_values: Previous field values
            new_values: New field values
        """
        model_name = instance._meta.verbose_name
        
        # Determine changed fields
        changed_fields = []
        if old_values and new_values:
            changed_fields = [
                field for field in new_values.keys()
                if old_values.get(field) != new_values.get(field)
            ]
        
        # Map action to event type
        event_type_map = {
            'create': 'CREATE',
            'update': 'UPDATE',
            'delete': 'DELETE'
        }
        
        event_type = event_type_map.get(action.lower(), 'CUSTOM')
        
        # Determine severity based on model and action
        severity = self._determine_severity(instance, action)
        
        return self.log_event(
            request=request,
            event_type=event_type,
            event_category=f"{model_name} Management",
            description=f"{action.title()} {model_name}: {str(instance)}",
            object_instance=instance,
            old_values=old_values,
            new_values=new_values,
            changed_fields=changed_fields,
            severity=severity,
            compliance_relevant=self._is_compliance_relevant(instance, action)
        )
    
    def log_user_activity(self, request, activity_type, description, metadata=None):
        """
        Log user activity event
        
        Args:
            request: Django request object
            activity_type: Type of activity (LOGIN, LOGOUT, VIEW, etc.)
            description: Activity description
            metadata: Additional metadata
        """
        return self.log_event(
            request=request,
            event_type=activity_type,
            event_category='User Activity',
            description=description,
            metadata=metadata,
            severity='LOW'
        )
    
    def log_security_event(self, request, event_description, severity='HIGH', metadata=None):
        """
        Log security-related event
        
        Args:
            request: Django request object
            event_description: Description of security event
            severity: Severity level
            metadata: Additional metadata
        """
        return self.log_event(
            request=request,
            event_type='SECURITY_EVENT',
            event_category='Security',
            description=event_description,
            severity=severity,
            risk_level='HIGH',
            compliance_relevant=True,
            requires_review=True,
            metadata=metadata
        )
    
    def _should_audit_event(self, event_type):
        """
        Check if event type should be audited based on configuration
        """
        event_config_map = {
            'CREATE': self.config.audit_create_events,
            'UPDATE': self.config.audit_update_events,
            'DELETE': self.config.audit_delete_events,
            'VIEW': self.config.audit_view_events,
            'LOGIN': self.config.audit_login_events,
            'LOGOUT': self.config.audit_login_events,
            'EXPORT': self.config.audit_export_events,
        }
        
        return event_config_map.get(event_type, True)  # Default to True for unknown types
    
    def _get_client_ip(self, request):
        """
        Get client IP address from request
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def _process_sensitive_data(self, values):
        """
        Process values to mask sensitive data if configured
        """
        if not self.config.mask_sensitive_data or not values:
            return values
        
        processed_values = values.copy()
        sensitive_fields = self.config.sensitive_fields
        
        for field_name, field_value in processed_values.items():
            if field_name.lower() in [f.lower() for f in sensitive_fields]:
                # Mask sensitive data
                if isinstance(field_value, str):
                    if '@' in field_value:  # Email
                        processed_values[field_name] = DataMasking.mask_email(field_value)
                    elif field_value.isdigit():  # Phone or ID
                        processed_values[field_name] = DataMasking.mask_phone(field_value)
                    else:  # Generic masking
                        processed_values[field_name] = self._mask_generic_data(field_value)
        
        return processed_values
    
    def _mask_generic_data(self, value):
        """
        Generic data masking
        """
        if len(value) <= 4:
            return '*' * len(value)
        return value[:2] + '*' * (len(value) - 4) + value[-2:]
    
    def _contains_sensitive_data(self, old_values, new_values, changed_fields):
        """
        Check if the event contains sensitive data
        """
        sensitive_fields = [f.lower() for f in self.config.sensitive_fields]
        
        # Check if any changed field is sensitive
        for field in changed_fields:
            if field.lower() in sensitive_fields:
                return True
        
        # Check if any value contains sensitive patterns
        all_values = list(old_values.values()) + list(new_values.values())
        for value in all_values:
            if isinstance(value, str):
                # Check for common sensitive patterns
                if any(pattern in value.lower() for pattern in ['ssn', 'password', 'secret', 'key']):
                    return True
        
        return False
    
    def _determine_severity(self, instance, action):
        """
        Determine severity based on model and action
        """
        # High-risk models
        high_risk_models = ['User', 'Student', 'Employee', 'Payment', 'Grade']
        model_name = instance._meta.model_name
        
        if model_name in [m.lower() for m in high_risk_models]:
            if action.lower() == 'delete':
                return 'HIGH'
            elif action.lower() == 'update':
                return 'MEDIUM'
        
        return 'LOW'
    
    def _is_compliance_relevant(self, instance, action):
        """
        Determine if the event is compliance relevant
        """
        # Models that are always compliance relevant
        compliance_models = ['Student', 'Grade', 'Payment', 'User']
        model_name = instance._meta.model_name
        
        return model_name in [m.lower() for m in compliance_models]
    
    def _trigger_compliance_check(self, event):
        """
        Trigger compliance monitoring for the event
        """
        if self.config.async_logging:
            # Use Celery for async processing
            check_event_compliance.delay(event.id)
        else:
            # Synchronous processing
            monitor = ComplianceMonitor(self.school)
            monitor.check_event_compliance(event)


class ComplianceMonitor:
    """
    Compliance monitoring system
    """
    
    def __init__(self, school):
        self.school = school
        self.rules = ComplianceRule.objects.filter(school=school, is_active=True)
    
    def check_event_compliance(self, event):
        """
        Check event against all compliance rules
        
        Args:
            event: AuditEvent instance
            
        Returns:
            List of ComplianceViolation instances
        """
        violations = []
        
        for rule in self.rules:
            if self._evaluate_rule(rule, event):
                violation = self._create_violation(rule, event)
                violations.append(violation)
                
                # Execute rule actions
                self._execute_rule_actions(rule, violation)
        
        return violations
    
    def _evaluate_rule(self, rule, event):
        """
        Evaluate if rule conditions match the event
        
        Args:
            rule: ComplianceRule instance
            event: AuditEvent instance
            
        Returns:
            Boolean indicating if rule matches
        """
        conditions = rule.conditions
        
        # Simple condition matching (can be extended for complex logic)
        for field, expected_value in conditions.items():
            event_value = getattr(event, field, None)
            
            if isinstance(expected_value, list):
                if event_value not in expected_value:
                    return False
            elif isinstance(expected_value, dict):
                # Handle complex conditions
                operator = expected_value.get('operator', 'equals')
                value = expected_value.get('value')
                
                if operator == 'equals' and event_value != value:
                    return False
                elif operator == 'contains' and value not in str(event_value):
                    return False
                elif operator == 'greater_than' and event_value <= value:
                    return False
                # Add more operators as needed
            else:
                if event_value != expected_value:
                    return False
        
        return True
    
    def _create_violation(self, rule, event):
        """
        Create compliance violation
        
        Args:
            rule: ComplianceRule instance
            event: AuditEvent instance
            
        Returns:
            ComplianceViolation instance
        """
        violation_description = f"Rule '{rule.name}' violated by event: {event.event_description}"
        
        violation = ComplianceViolation.objects.create(
            school=self.school,
            rule=rule,
            audit_event=event,
            violation_description=violation_description,
            severity=rule.severity
        )
        
        # Update rule statistics
        rule.last_triggered = timezone.now()
        rule.trigger_count += 1
        rule.save(update_fields=['last_triggered', 'trigger_count'])
        
        logger.warning(f"Compliance violation detected: {violation.id}")
        return violation
    
    def _execute_rule_actions(self, rule, violation):
        """
        Execute actions defined in the rule
        
        Args:
            rule: ComplianceRule instance
            violation: ComplianceViolation instance
        """
        actions = rule.actions
        
        for action in actions:
            if action == 'log':
                logger.warning(f"Compliance violation: {violation.violation_description}")
            elif action == 'notify':
                self._send_violation_notification(violation)
            elif action == 'block':
                # Placeholder for blocking action
                logger.critical(f"Blocking action triggered for violation: {violation.id}")
            elif action == 'escalate':
                self._escalate_violation(violation)
    
    def _send_violation_notification(self, violation):
        """
        Send notification about compliance violation
        """
        # Get notification emails from audit config
        audit_config = AuditConfiguration.objects.get(school=self.school)
        
        if audit_config.notify_on_violations and audit_config.violation_notification_emails:
            # Use Celery for async email sending
            send_violation_notification.delay(violation.id)
    
    def _escalate_violation(self, violation):
        """
        Escalate violation to higher severity
        """
        # Mark as requiring review
        violation.audit_event.requires_review = True
        violation.audit_event.save(update_fields=['requires_review'])
        
        # Could also assign to specific users, create tickets, etc.
        logger.critical(f"Violation escalated: {violation.id}")


class AuditReportGenerator:
    """
    Generate audit reports
    """
    
    def __init__(self, school):
        self.school = school
    
    def generate_activity_summary(self, date_from, date_to, filters=None):
        """
        Generate activity summary report
        
        Args:
            date_from: Start date
            date_to: End date
            filters: Additional filters
            
        Returns:
            Dictionary with report data
        """
        queryset = AuditEvent.objects.filter(
            school=self.school,
            timestamp__gte=date_from,
            timestamp__lte=date_to
        )
        
        # Apply additional filters
        if filters:
            if 'event_types' in filters:
                queryset = queryset.filter(event_type__in=filters['event_types'])
            if 'users' in filters:
                queryset = queryset.filter(user__in=filters['users'])
            if 'categories' in filters:
                queryset = queryset.filter(event_category__in=filters['categories'])
        
        # Generate statistics
        total_events = queryset.count()
        
        events_by_type = queryset.values('event_type').annotate(
            count=Count('id')
        ).order_by('-count')
        
        events_by_category = queryset.values('event_category').annotate(
            count=Count('id')
        ).order_by('-count')
        
        events_by_user = queryset.filter(user__isnull=False).values(
            'user__username', 'user__first_name', 'user__last_name'
        ).annotate(count=Count('id')).order_by('-count')[:10]
        
        events_by_severity = queryset.values('severity').annotate(
            count=Count('id')
        ).order_by('-count')
        
        # Security events
        security_events = queryset.filter(
            Q(event_type='SECURITY_EVENT') | Q(risk_level__in=['HIGH', 'CRITICAL'])
        ).count()
        
        # Compliance violations
        violations = ComplianceViolation.objects.filter(
            school=self.school,
            detected_at__gte=date_from,
            detected_at__lte=date_to
        ).count()
        
        return {
            'total_events': total_events,
            'events_by_type': list(events_by_type),
            'events_by_category': list(events_by_category),
            'events_by_user': list(events_by_user),
            'events_by_severity': list(events_by_severity),
            'security_events': security_events,
            'compliance_violations': violations,
            'date_from': date_from,
            'date_to': date_to
        }
    
    def generate_user_activity_report(self, user, date_from, date_to):
        """
        Generate user activity report
        
        Args:
            user: User instance
            date_from: Start date
            date_to: End date
            
        Returns:
            Dictionary with user activity data
        """
        queryset = AuditEvent.objects.filter(
            school=self.school,
            user=user,
            timestamp__gte=date_from,
            timestamp__lte=date_to
        )
        
        total_activities = queryset.count()
        
        activities_by_type = queryset.values('event_type').annotate(
            count=Count('id')
        ).order_by('-count')
        
        activities_by_category = queryset.values('event_category').annotate(
            count=Count('id')
        ).order_by('-count')
        
        # Recent activities
        recent_activities = queryset.order_by('-timestamp')[:20].values(
            'event_type', 'event_category', 'event_description', 'timestamp'
        )
        
        # Login sessions
        login_events = queryset.filter(event_type='LOGIN').count()
        
        # Failed operations
        failed_operations = queryset.filter(is_successful=False).count()
        
        return {
            'user': {
                'username': user.username,
                'full_name': user.get_full_name(),
                'email': user.email
            },
            'total_activities': total_activities,
            'activities_by_type': list(activities_by_type),
            'activities_by_category': list(activities_by_category),
            'recent_activities': list(recent_activities),
            'login_sessions': login_events,
            'failed_operations': failed_operations,
            'date_from': date_from,
            'date_to': date_to
        }
    
    def generate_compliance_report(self, date_from, date_to):
        """
        Generate compliance report
        
        Args:
            date_from: Start date
            date_to: End date
            
        Returns:
            Dictionary with compliance data
        """
        # Compliance violations
        violations = ComplianceViolation.objects.filter(
            school=self.school,
            detected_at__gte=date_from,
            detected_at__lte=date_to
        )
        
        total_violations = violations.count()
        
        violations_by_rule = violations.values(
            'rule__name', 'rule__rule_type'
        ).annotate(count=Count('id')).order_by('-count')
        
        violations_by_severity = violations.values('severity').annotate(
            count=Count('id')
        ).order_by('-count')
        
        violations_by_status = violations.values('status').annotate(
            count=Count('id')
        ).order_by('-count')
        
        # Open violations
        open_violations = violations.filter(status='OPEN').count()
        
        # Resolved violations
        resolved_violations = violations.filter(status='RESOLVED').count()
        
        # High-risk events
        high_risk_events = AuditEvent.objects.filter(
            school=self.school,
            timestamp__gte=date_from,
            timestamp__lte=date_to,
            risk_level__in=['HIGH', 'CRITICAL']
        ).count()
        
        return {
            'total_violations': total_violations,
            'violations_by_rule': list(violations_by_rule),
            'violations_by_severity': list(violations_by_severity),
            'violations_by_status': list(violations_by_status),
            'open_violations': open_violations,
            'resolved_violations': resolved_violations,
            'high_risk_events': high_risk_events,
            'date_from': date_from,
            'date_to': date_to
        }


class AuditTrailManager:
    """
    Manage audit trails for complex workflows
    """
    
    def __init__(self, school):
        self.school = school
    
    def create_trail(self, name, description, created_by):
        """
        Create new audit trail
        
        Args:
            name: Trail name
            description: Trail description
            created_by: User creating the trail
            
        Returns:
            AuditTrail instance
        """
        return AuditTrail.objects.create(
            school=self.school,
            trail_name=name,
            description=description,
            created_by=created_by
        )
    
    def add_event_to_trail(self, trail, event, sequence_number=None):
        """
        Add event to audit trail
        
        Args:
            trail: AuditTrail instance
            event: AuditEvent instance
            sequence_number: Optional sequence number
        """
        from core.audit.audit_models import AuditTrailEvent
        
        if sequence_number is None:
            # Get next sequence number
            last_event = AuditTrailEvent.objects.filter(trail=trail).order_by('-sequence_number').first()
            sequence_number = (last_event.sequence_number + 1) if last_event else 1
        
        AuditTrailEvent.objects.create(
            trail=trail,
            event=event,
            sequence_number=sequence_number
        )
    
    def get_trail_events(self, trail):
        """
        Get all events in a trail ordered by sequence
        
        Args:
            trail: AuditTrail instance
            
        Returns:
            QuerySet of AuditEvent instances
        """
        return AuditEvent.objects.filter(
            audittrailevent__trail=trail
        ).order_by('audittrailevent__sequence_number')


# Celery tasks for async processing
@shared_task
def check_event_compliance(event_id):
    """
    Async task to check event compliance
    """
    try:
        event = AuditEvent.objects.get(id=event_id)
        monitor = ComplianceMonitor(event.school)
        violations = monitor.check_event_compliance(event)
        
        logger.info(f"Compliance check completed for event {event_id}: {len(violations)} violations found")
        return len(violations)
    except Exception as e:
        logger.error(f"Error checking compliance for event {event_id}: {e}")
        return 0


@shared_task
def send_violation_notification(violation_id):
    """
    Async task to send violation notification
    """
    try:
        violation = ComplianceViolation.objects.get(id=violation_id)
        audit_config = AuditConfiguration.objects.get(school=violation.school)
        
        # Send email notification (placeholder)
        # In production, integrate with email service
        logger.info(f"Violation notification sent for {violation_id}")
        
        return True
    except Exception as e:
        logger.error(f"Error sending violation notification {violation_id}: {e}")
        return False


@shared_task
def cleanup_old_audit_logs():
    """
    Cleanup old audit logs based on retention policy
    """
    schools = AuditConfiguration.objects.all()
    total_deleted = 0
    
    for config in schools:
        cutoff_date = timezone.now() - timedelta(days=config.retention_days)
        
        deleted_count = AuditEvent.objects.filter(
            school=config.school,
            timestamp__lt=cutoff_date
        ).delete()[0]
        
        total_deleted += deleted_count
        logger.info(f"Deleted {deleted_count} old audit logs for {config.school.name}")
    
    logger.info(f"Total audit logs deleted: {total_deleted}")
    return total_deleted


# Utility functions
def get_audit_logger(school):
    """
    Get audit logger instance for school
    """
    return AuditLogger(school)


def get_compliance_monitor(school):
    """
    Get compliance monitor instance for school
    """
    return ComplianceMonitor(school)


def get_report_generator(school):
    """
    Get report generator instance for school
    """
    return AuditReportGenerator(school)