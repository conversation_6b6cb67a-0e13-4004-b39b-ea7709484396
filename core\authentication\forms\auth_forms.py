"""
Authentication forms for School ERP system
"""
from django import forms
from django.contrib.auth import get_user_model
from django.contrib.auth.forms import AuthenticationForm, PasswordChangeForm
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from .auth_models import MFADevice, PasswordPolicy
from .auth_utils import PasswordUtils, MFAUtils

User = get_user_model()


class EnhancedLoginForm(AuthenticationForm):
    """
    Enhanced login form with MFA support
    """
    username = forms.CharField(
        max_length=254,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Username or Email'),
            'autofocus': True
        })
    )
    
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': _('Password')
        })
    )
    
    mfa_token = forms.CharField(
        max_length=6,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Verification Code'),
            'maxlength': '6'
        })
    )
    
    backup_code = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Backup Code')
        })
    )
    
    remember_me = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        })
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['username'].label = _('Username or Email')
        self.fields['password'].label = _('Password')
        self.fields['mfa_token'].label = _('Verification Code')
        self.fields['backup_code'].label = _('Backup Code')
        self.fields['remember_me'].label = _('Remember me')


class MFASetupForm(forms.Form):
    """
    MFA setup form
    """
    device_name = forms.CharField(
        max_length=100,
        initial='Authenticator App',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Device Name')
        })
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['device_name'].label = _('Device Name')


class MFAVerificationForm(forms.Form):
    """
    MFA verification form
    """
    token = forms.CharField(
        max_length=6,
        min_length=6,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('6-digit code'),
            'maxlength': '6',
            'pattern': '[0-9]{6}'
        })
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['token'].label = _('Verification Code')
    
    def clean_token(self):
        token = self.cleaned_data['token']
        if not token.isdigit():
            raise ValidationError(_('Verification code must contain only digits'))
        return token


class EnhancedPasswordChangeForm(PasswordChangeForm):
    """
    Enhanced password change form with policy validation
    """
    
    def __init__(self, user, *args, **kwargs):
        super().__init__(user, *args, **kwargs)
        
        # Add CSS classes
        for field_name, field in self.fields.items():
            field.widget.attrs['class'] = 'form-control'
        
        # Get password policy
        self.password_policy = self._get_password_policy(user)
    
    def _get_password_policy(self, user):
        """
        Get password policy for user
        """
        try:
            if hasattr(user, 'employee') and user.employee and user.employee.school:
                return user.employee.school.password_policy
        except:
            pass
        return None
    
    def clean_new_password1(self):
        password = self.cleaned_data.get('new_password1')
        
        if password:
            # Validate against password policy
            if self.password_policy:
                errors = self.password_policy.validate_password(password)
                if errors:
                    raise ValidationError(errors)
            else:
                # Default validation
                errors = PasswordUtils.validate_password_strength(password)
                if errors:
                    raise ValidationError(errors)
            
            # Check password history
            if not PasswordUtils.check_password_history(self.user, password):
                raise ValidationError(
                    _('This password was used recently. Please choose a different password.')
                )
        
        return password


class PasswordPolicyForm(forms.ModelForm):
    """
    Password policy configuration form
    """
    
    class Meta:
        model = PasswordPolicy
        fields = [
            'min_length', 'require_uppercase', 'require_lowercase',
            'require_numbers', 'require_special_chars', 'max_age_days',
            'history_count', 'lockout_attempts', 'lockout_duration_minutes'
        ]
        widgets = {
            'min_length': forms.NumberInput(attrs={'class': 'form-control', 'min': '4', 'max': '50'}),
            'max_age_days': forms.NumberInput(attrs={'class': 'form-control', 'min': '1', 'max': '365'}),
            'history_count': forms.NumberInput(attrs={'class': 'form-control', 'min': '1', 'max': '20'}),
            'lockout_attempts': forms.NumberInput(attrs={'class': 'form-control', 'min': '3', 'max': '20'}),
            'lockout_duration_minutes': forms.NumberInput(attrs={'class': 'form-control', 'min': '5', 'max': '1440'}),
            'require_uppercase': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'require_lowercase': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'require_numbers': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'require_special_chars': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Set field labels
        self.fields['min_length'].label = _('Minimum Length')
        self.fields['require_uppercase'].label = _('Require Uppercase Letters')
        self.fields['require_lowercase'].label = _('Require Lowercase Letters')
        self.fields['require_numbers'].label = _('Require Numbers')
        self.fields['require_special_chars'].label = _('Require Special Characters')
        self.fields['max_age_days'].label = _('Password Expires After (Days)')
        self.fields['history_count'].label = _('Remember Previous Passwords')
        self.fields['lockout_attempts'].label = _('Failed Attempts Before Lockout')
        self.fields['lockout_duration_minutes'].label = _('Lockout Duration (Minutes)')
        
        # Set help texts
        self.fields['min_length'].help_text = _('Minimum number of characters required')
        self.fields['max_age_days'].help_text = _('Password will expire after this many days')
        self.fields['history_count'].help_text = _('Number of previous passwords to remember')
        self.fields['lockout_attempts'].help_text = _('Account will be locked after this many failed attempts')
        self.fields['lockout_duration_minutes'].help_text = _('How long the account will be locked')


class APITokenForm(forms.Form):
    """
    API token creation form
    """
    name = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Token Name')
        })
    )
    
    expires_in = forms.ChoiceField(
        choices=[
            (3600, _('1 Hour')),
            (86400, _('1 Day')),
            (604800, _('1 Week')),
            (2592000, _('1 Month')),
            (********, _('1 Year')),
            (0, _('Never'))
        ],
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    scopes = forms.MultipleChoiceField(
        choices=[
            ('read', _('Read Access')),
            ('write', _('Write Access')),
            ('admin', _('Admin Access'))
        ],
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-check-input'}),
        required=False
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['name'].label = _('Token Name')
        self.fields['expires_in'].label = _('Expires In')
        self.fields['scopes'].label = _('Permissions')


class OAuth2ClientForm(forms.Form):
    """
    OAuth2 client registration form
    """
    name = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Application Name')
        })
    )
    
    description = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': _('Application Description')
        }),
        required=False
    )
    
    redirect_uris = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': _('One redirect URI per line')
        }),
        help_text=_('Enter one redirect URI per line')
    )
    
    client_type = forms.ChoiceField(
        choices=[
            ('confidential', _('Confidential')),
            ('public', _('Public'))
        ],
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['name'].label = _('Application Name')
        self.fields['description'].label = _('Description')
        self.fields['redirect_uris'].label = _('Redirect URIs')
        self.fields['client_type'].label = _('Client Type')
    
    def clean_redirect_uris(self):
        uris = self.cleaned_data['redirect_uris']
        uri_list = [uri.strip() for uri in uris.split('\n') if uri.strip()]
        
        # Validate each URI
        from django.core.validators import URLValidator
        validator = URLValidator()
        
        for uri in uri_list:
            try:
                validator(uri)
            except ValidationError:
                raise ValidationError(f'Invalid URI: {uri}')
        
        return uri_list


class SecuritySettingsForm(forms.Form):
    """
    Security settings form
    """
    session_timeout = forms.IntegerField(
        min_value=5,
        max_value=1440,
        initial=30,
        widget=forms.NumberInput(attrs={
            'class': 'form-control'
        }),
        help_text=_('Session timeout in minutes')
    )
    
    max_login_attempts = forms.IntegerField(
        min_value=3,
        max_value=20,
        initial=5,
        widget=forms.NumberInput(attrs={
            'class': 'form-control'
        }),
        help_text=_('Maximum failed login attempts before lockout')
    )
    
    lockout_duration = forms.IntegerField(
        min_value=5,
        max_value=1440,
        initial=30,
        widget=forms.NumberInput(attrs={
            'class': 'form-control'
        }),
        help_text=_('Account lockout duration in minutes')
    )
    
    require_mfa_for_admin = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        })
    )
    
    require_mfa_for_finance = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        })
    )
    
    allow_concurrent_sessions = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        })
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['session_timeout'].label = _('Session Timeout')
        self.fields['max_login_attempts'].label = _('Max Login Attempts')
        self.fields['lockout_duration'].label = _('Lockout Duration')
        self.fields['require_mfa_for_admin'].label = _('Require MFA for Administrators')
        self.fields['require_mfa_for_finance'].label = _('Require MFA for Finance Users')
        self.fields['allow_concurrent_sessions'].label = _('Allow Concurrent Sessions')


class BackupCodeForm(forms.Form):
    """
    Backup code verification form
    """
    backup_code = forms.CharField(
        max_length=20,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Enter backup code'),
            'autocomplete': 'off'
        })
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['backup_code'].label = _('Backup Code')
    
    def clean_backup_code(self):
        code = self.cleaned_data['backup_code'].upper().strip()
        return code


class TwoFactorDisableForm(forms.Form):
    """
    Form to disable two-factor authentication
    """
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': _('Current Password')
        })
    )
    
    confirmation = forms.BooleanField(
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        })
    )
    
    def __init__(self, user, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)
        self.fields['password'].label = _('Current Password')
        self.fields['confirmation'].label = _('I understand that disabling 2FA reduces my account security')
    
    def clean_password(self):
        password = self.cleaned_data['password']
        if not self.user.check_password(password):
            raise ValidationError(_('Incorrect password'))
        return password
    
    def clean_confirmation(self):
        confirmation = self.cleaned_data['confirmation']
        if not confirmation:
            raise ValidationError(_('You must confirm that you understand the security implications'))
        return confirmation