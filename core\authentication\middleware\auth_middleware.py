"""
Authentication middleware for School ERP system
"""
from django.utils.deprecation import MiddlewareMixin
from django.contrib.auth import logout
from django.shortcuts import redirect
from django.urls import reverse
from django.utils import timezone
from django.conf import settings
from django.http import JsonResponse
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from datetime import timedelta
import logging

from .auth_models import LoginAttempt, SessionSecurity, MFADevice
from .auth_utils import SecurityUtils, SessionUtils

logger = logging.getLogger(__name__)


class EnhancedSecurityMiddleware(MiddlewareMixin):
    """
    Enhanced security middleware for authentication
    """
    
    def process_request(self, request):
        """
        Process incoming request for security checks
        """
        # Skip security checks for certain paths
        skip_paths = [
            '/admin/',
            '/static/',
            '/media/',
            '/api/auth/login/',
            '/api/auth/logout/',
        ]
        
        if any(request.path.startswith(path) for path in skip_paths):
            return None
        
        # Check session security for authenticated users
        if request.user.is_authenticated:
            if not self._validate_session_security(request):
                logout(request)
                messages.error(request, _('Session expired for security reasons'))
                return redirect('auth:login')
            
            # Check for suspicious activity
            if self._detect_suspicious_activity(request):
                self._handle_suspicious_activity(request)
        
        return None
    
    def _validate_session_security(self, request):
        """
        Validate session security
        """
        try:
            # Check if session exists in our security tracking
            session_security = SessionSecurity.objects.get(
                session_key=request.session.session_key,
                user=request.user,
                is_active=True
            )
            
            # Check IP address consistency
            current_ip = SecurityUtils.get_client_ip(request)
            if session_security.ip_address != current_ip:
                logger.warning(
                    f"IP address mismatch for user {request.user.username}: "
                    f"expected {session_security.ip_address}, got {current_ip}"
                )
                return False
            
            # Check session timeout
            if session_security.is_expired():
                session_security.is_active = False
                session_security.save()
                return False
            
            # Update last activity
            session_security.last_activity = timezone.now()
            session_security.save()
            
            return True
            
        except SessionSecurity.DoesNotExist:
            logger.warning(f"Session security record not found for user {request.user.username}")
            return False
        except Exception as e:
            logger.error(f"Session validation error: {e}")
            return False
    
    def _detect_suspicious_activity(self, request):
        """
        Detect suspicious activity patterns
        """
        user = request.user
        ip_address = SecurityUtils.get_client_ip(request)
        
        # Check for rapid requests (potential bot activity)
        cache_key = f"request_count_{user.id}_{ip_address}"
        from django.core.cache import cache
        
        request_count = cache.get(cache_key, 0)
        if request_count > 100:  # More than 100 requests per minute
            return True
        
        cache.set(cache_key, request_count + 1, 60)  # 1 minute window
        
        # Check for unusual access patterns
        if self._check_unusual_access_pattern(request):
            return True
        
        return False
    
    def _check_unusual_access_pattern(self, request):
        """
        Check for unusual access patterns
        """
        # Check if user is accessing resources they don't normally access
        # This is a simplified check - in production, you'd use ML models
        
        user = request.user
        current_hour = timezone.now().hour
        
        # Check if user is accessing system outside normal hours
        if hasattr(user, 'employee') and user.employee:
            # For employees, check if accessing outside work hours
            if current_hour < 6 or current_hour > 22:  # Outside 6 AM - 10 PM
                recent_logins = LoginAttempt.objects.filter(
                    user=user,
                    success=True,
                    timestamp__gte=timezone.now() - timedelta(days=30)
                ).values_list('timestamp', flat=True)
                
                # Check if user normally accesses system at this hour
                normal_hours = [login.hour for login in recent_logins]
                if current_hour not in normal_hours:
                    return True
        
        return False
    
    def _handle_suspicious_activity(self, request):
        """
        Handle suspicious activity
        """
        user = request.user
        ip_address = SecurityUtils.get_client_ip(request)
        
        logger.warning(
            f"Suspicious activity detected for user {user.username} "
            f"from IP {ip_address}"
        )
        
        # Log the suspicious activity
        LoginAttempt.objects.create(
            user=user,
            username=user.username,
            ip_address=ip_address,
            user_agent=SecurityUtils.get_user_agent(request),
            success=False,
            failure_reason='suspicious_activity'
        )
        
        # For high-risk activities, force re-authentication
        if self._is_high_risk_activity(request):
            request.session['require_reauth'] = True


class MFAEnforcementMiddleware(MiddlewareMixin):
    """
    Middleware to enforce MFA for certain user types
    """
    
    def process_request(self, request):
        """
        Check if MFA is required for the current user
        """
        if not request.user.is_authenticated:
            return None
        
        # Skip MFA checks for certain paths
        skip_paths = [
            '/auth/mfa/',
            '/auth/logout/',
            '/static/',
            '/media/',
        ]
        
        if any(request.path.startswith(path) for path in skip_paths):
            return None
        
        # Check if MFA is required for this user
        if self._is_mfa_required(request.user):
            # Check if user has completed MFA
            if not self._has_completed_mfa(request):
                # Redirect to MFA setup/verification
                if self._has_mfa_devices(request.user):
                    return redirect('auth:mfa_verify')
                else:
                    return redirect('auth:mfa_setup')
        
        return None
    
    def _is_mfa_required(self, user):
        """
        Check if MFA is required for this user
        """
        # MFA required for superusers
        if user.is_superuser:
            return True
        
        # MFA required for certain roles
        if hasattr(user, 'employee') and user.employee:
            high_privilege_roles = ['ADMIN', 'FINANCE_MANAGER', 'HR_MANAGER']
            return user.employee.role in high_privilege_roles
        
        return False
    
    def _has_completed_mfa(self, request):
        """
        Check if user has completed MFA for this session
        """
        return request.session.get('mfa_verified', False)
    
    def _has_mfa_devices(self, user):
        """
        Check if user has MFA devices configured
        """
        return MFADevice.objects.filter(user=user, is_active=True).exists()


class PasswordExpiryMiddleware(MiddlewareMixin):
    """
    Middleware to check password expiry
    """
    
    def process_request(self, request):
        """
        Check if user's password has expired
        """
        if not request.user.is_authenticated:
            return None
        
        # Skip password checks for certain paths
        skip_paths = [
            '/auth/password/change/',
            '/auth/logout/',
            '/static/',
            '/media/',
        ]
        
        if any(request.path.startswith(path) for path in skip_paths):
            return None
        
        # Check password expiry
        if self._is_password_expired(request.user):
            messages.warning(request, _('Your password has expired. Please change it.'))
            return redirect('auth:password_change')
        
        # Check if password will expire soon
        days_until_expiry = self._days_until_password_expiry(request.user)
        if days_until_expiry is not None and days_until_expiry <= 7:
            messages.info(
                request,
                _('Your password will expire in {} days. Please consider changing it.').format(
                    days_until_expiry
                )
            )
        
        return None
    
    def _is_password_expired(self, user):
        """
        Check if user's password has expired
        """
        try:
            # Get password policy
            if hasattr(user, 'employee') and user.employee and user.employee.school:
                policy = user.employee.school.password_policy
                max_age_days = policy.max_age_days
            else:
                max_age_days = 90  # Default
            
            # Check last password change
            from .auth_models import PasswordHistory
            last_change = PasswordHistory.objects.filter(user=user).first()
            
            if last_change:
                days_since_change = (timezone.now().date() - last_change.created_at.date()).days
                return days_since_change >= max_age_days
            else:
                # If no password history, check user's date_joined
                days_since_joined = (timezone.now().date() - user.date_joined.date()).days
                return days_since_joined >= max_age_days
                
        except Exception as e:
            logger.error(f"Password expiry check error: {e}")
            return False
    
    def _days_until_password_expiry(self, user):
        """
        Get days until password expires
        """
        try:
            # Get password policy
            if hasattr(user, 'employee') and user.employee and user.employee.school:
                policy = user.employee.school.password_policy
                max_age_days = policy.max_age_days
            else:
                max_age_days = 90  # Default
            
            # Check last password change
            from .auth_models import PasswordHistory
            last_change = PasswordHistory.objects.filter(user=user).first()
            
            if last_change:
                days_since_change = (timezone.now().date() - last_change.created_at.date()).days
                return max_age_days - days_since_change
            else:
                # If no password history, check user's date_joined
                days_since_joined = (timezone.now().date() - user.date_joined.date()).days
                return max_age_days - days_since_joined
                
        except Exception as e:
            logger.error(f"Password expiry calculation error: {e}")
            return None


class RateLimitMiddleware(MiddlewareMixin):
    """
    Rate limiting middleware for authentication endpoints
    """
    
    def process_request(self, request):
        """
        Apply rate limiting to sensitive endpoints
        """
        # Define rate limits for different endpoints
        rate_limits = {
            '/auth/login/': {'limit': 5, 'window': 300},  # 5 attempts per 5 minutes
            '/auth/password/reset/': {'limit': 3, 'window': 3600},  # 3 attempts per hour
            '/api/auth/token/': {'limit': 10, 'window': 300},  # 10 attempts per 5 minutes
        }
        
        # Check if current path needs rate limiting
        for path, config in rate_limits.items():
            if request.path.startswith(path):
                if not self._check_rate_limit(request, path, config):
                    return self._rate_limit_exceeded_response(request)
        
        return None
    
    def _check_rate_limit(self, request, endpoint, config):
        """
        Check if request is within rate limit
        """
        from django.core.cache import cache
        
        ip_address = SecurityUtils.get_client_ip(request)
        cache_key = f"rate_limit_{endpoint}_{ip_address}"
        
        # Get current count
        current_count = cache.get(cache_key, 0)
        
        if current_count >= config['limit']:
            return False
        
        # Increment count
        cache.set(cache_key, current_count + 1, config['window'])
        return True
    
    def _rate_limit_exceeded_response(self, request):
        """
        Return response when rate limit is exceeded
        """
        if request.content_type == 'application/json' or request.path.startswith('/api/'):
            return JsonResponse({
                'error': 'rate_limit_exceeded',
                'message': _('Too many requests. Please try again later.')
            }, status=429)
        else:
            messages.error(request, _('Too many requests. Please try again later.'))
            return redirect('auth:login')


class AuditMiddleware(MiddlewareMixin):
    """
    Middleware for auditing authentication events
    """
    
    def process_request(self, request):
        """
        Log authentication-related requests
        """
        # Store request info for later use
        request._audit_info = {
            'ip_address': SecurityUtils.get_client_ip(request),
            'user_agent': SecurityUtils.get_user_agent(request),
            'timestamp': timezone.now(),
            'path': request.path,
            'method': request.method
        }
        
        return None
    
    def process_response(self, request, response):
        """
        Log authentication events based on response
        """
        if not hasattr(request, '_audit_info'):
            return response
        
        # Log authentication events
        if request.path.startswith('/auth/'):
            self._log_auth_event(request, response)
        
        return response
    
    def _log_auth_event(self, request, response):
        """
        Log authentication event
        """
        from core.models import AuditLog
        
        try:
            # Determine event type based on path and response
            event_type = self._determine_event_type(request.path, response.status_code)
            
            if event_type:
                # Get school context
                school = None
                if request.user.is_authenticated:
                    if hasattr(request.user, 'employee') and request.user.employee:
                        school = request.user.employee.school
                    elif hasattr(request.user, 'student') and request.user.student:
                        school = request.user.student.school
                    elif hasattr(request.user, 'parent') and request.user.parent:
                        school = request.user.parent.school
                
                if school:  # Only log if we have school context
                    AuditLog.objects.create(
                        school=school,
                        user=request.user if request.user.is_authenticated else None,
                        action=event_type,
                        model_name='Authentication',
                        object_id=str(request.user.id) if request.user.is_authenticated else 'anonymous',
                        object_repr=f"{request.path} - {response.status_code}",
                        changes={
                            'path': request.path,
                            'method': request.method,
                            'status_code': response.status_code,
                            'timestamp': request._audit_info['timestamp'].isoformat()
                        },
                        ip_address=request._audit_info['ip_address'],
                        user_agent=request._audit_info['user_agent']
                    )
        except Exception as e:
            logger.error(f"Audit logging error: {e}")
    
    def _determine_event_type(self, path, status_code):
        """
        Determine event type based on path and status code
        """
        if '/login/' in path:
            return 'LOGIN' if status_code in [200, 302] else 'LOGIN_FAILED'
        elif '/logout/' in path:
            return 'LOGOUT'
        elif '/password/' in path:
            return 'PASSWORD_CHANGE' if status_code in [200, 302] else 'PASSWORD_CHANGE_FAILED'
        elif '/mfa/' in path:
            return 'MFA_SETUP' if status_code in [200, 302] else 'MFA_SETUP_FAILED'
        
        return None


class CSRFExemptMiddleware(MiddlewareMixin):
    """
    Middleware to handle CSRF exemptions for API endpoints
    """
    
    def process_request(self, request):
        """
        Exempt certain API endpoints from CSRF protection
        """
        # API endpoints that should be exempt from CSRF
        csrf_exempt_paths = [
            '/api/auth/token/',
            '/api/oauth2/token/',
        ]
        
        if any(request.path.startswith(path) for path in csrf_exempt_paths):
            setattr(request, '_dont_enforce_csrf_checks', True)
        
        return None


class SecurityHeadersMiddleware(MiddlewareMixin):
    """
    Middleware to add security headers
    """
    
    def process_response(self, request, response):
        """
        Add security headers to response
        """
        # Add security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # Add HSTS header for HTTPS
        if request.is_secure():
            response['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains; preload'
        
        # Add CSP header for authentication pages
        if request.path.startswith('/auth/'):
            response['Content-Security-Policy'] = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data:; "
                "font-src 'self'; "
                "connect-src 'self'"
            )
        
        return response