"""
Authentication-related models for School ERP system
"""
from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.core.validators import RegexValidator
import uuid
import secrets
import hashlib
from datetime import timedelta

User = get_user_model()


class MFADevice(models.Model):
    """
    Multi-Factor Authentication device model
    """
    DEVICE_TYPES = (
        ('totp', _('TOTP (Time-based One-Time Password)')),
        ('sms', _('SMS')),
        ('email', _('Email')),
        ('backup', _('Backup Codes')),
    )
    
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='mfa_devices',
        verbose_name=_('User')
    )
    
    device_type = models.CharField(
        max_length=20,
        choices=DEVICE_TYPES,
        verbose_name=_('Device Type')
    )
    
    name = models.CharField(
        max_length=100,
        verbose_name=_('Device Name'),
        help_text=_('Human-readable name for this device')
    )
    
    secret_key = models.CharField(
        max_length=255,
        verbose_name=_('Secret Key'),
        help_text=_('Encrypted secret key for this device')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    
    is_primary = models.BooleanField(
        default=False,
        verbose_name=_('Is Primary Device')
    )
    
    last_used = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Last Used')
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('Updated At')
    )

    class Meta:
        verbose_name = _('MFA Device')
        verbose_name_plural = _('MFA Devices')
        unique_together = ['user', 'name']
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['device_type']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.name} ({self.get_device_type_display()})"
    
    def save(self, *args, **kwargs):
        # Ensure only one primary device per user
        if self.is_primary:
            MFADevice.objects.filter(
                user=self.user,
                is_primary=True
            ).exclude(pk=self.pk).update(is_primary=False)
        super().save(*args, **kwargs)


class MFABackupCode(models.Model):
    """
    Backup codes for MFA recovery
    """
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='mfa_backup_codes',
        verbose_name=_('User')
    )
    
    code = models.CharField(
        max_length=20,
        verbose_name=_('Backup Code')
    )
    
    is_used = models.BooleanField(
        default=False,
        verbose_name=_('Is Used')
    )
    
    used_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Used At')
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )

    class Meta:
        verbose_name = _('MFA Backup Code')
        verbose_name_plural = _('MFA Backup Codes')
        unique_together = ['user', 'code']
        indexes = [
            models.Index(fields=['user', 'is_used']),
        ]

    def __str__(self):
        return f"{self.user.username} - Backup Code"
    
    @classmethod
    def generate_codes(cls, user, count=10):
        """
        Generate backup codes for a user
        """
        codes = []
        for _ in range(count):
            code = secrets.token_hex(4).upper()
            backup_code = cls.objects.create(user=user, code=code)
            codes.append(code)
        return codes


class LoginAttempt(models.Model):
    """
    Track login attempts for security monitoring
    """
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='login_attempts',
        verbose_name=_('User')
    )
    
    username = models.CharField(
        max_length=150,
        verbose_name=_('Username')
    )
    
    ip_address = models.GenericIPAddressField(
        verbose_name=_('IP Address')
    )
    
    user_agent = models.TextField(
        blank=True,
        verbose_name=_('User Agent')
    )
    
    success = models.BooleanField(
        default=False,
        verbose_name=_('Success')
    )
    
    failure_reason = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_('Failure Reason')
    )
    
    mfa_required = models.BooleanField(
        default=False,
        verbose_name=_('MFA Required')
    )
    
    mfa_success = models.BooleanField(
        default=False,
        verbose_name=_('MFA Success')
    )
    
    timestamp = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Timestamp')
    )

    class Meta:
        verbose_name = _('Login Attempt')
        verbose_name_plural = _('Login Attempts')
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['ip_address', 'timestamp']),
            models.Index(fields=['success', 'timestamp']),
        ]

    def __str__(self):
        status = 'Success' if self.success else 'Failed'
        return f"{self.username} - {status} - {self.timestamp}"


class PasswordPolicy(models.Model):
    """
    Password policy configuration
    """
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )
    
    school = models.OneToOneField(
        'core.School',
        on_delete=models.CASCADE,
        related_name='password_policy',
        verbose_name=_('School')
    )
    
    min_length = models.IntegerField(
        default=8,
        verbose_name=_('Minimum Length')
    )
    
    require_uppercase = models.BooleanField(
        default=True,
        verbose_name=_('Require Uppercase')
    )
    
    require_lowercase = models.BooleanField(
        default=True,
        verbose_name=_('Require Lowercase')
    )
    
    require_numbers = models.BooleanField(
        default=True,
        verbose_name=_('Require Numbers')
    )
    
    require_special_chars = models.BooleanField(
        default=True,
        verbose_name=_('Require Special Characters')
    )
    
    max_age_days = models.IntegerField(
        default=90,
        verbose_name=_('Maximum Age (Days)'),
        help_text=_('Password expires after this many days')
    )
    
    history_count = models.IntegerField(
        default=5,
        verbose_name=_('History Count'),
        help_text=_('Number of previous passwords to remember')
    )
    
    lockout_attempts = models.IntegerField(
        default=5,
        verbose_name=_('Lockout Attempts'),
        help_text=_('Number of failed attempts before lockout')
    )
    
    lockout_duration_minutes = models.IntegerField(
        default=30,
        verbose_name=_('Lockout Duration (Minutes)')
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('Updated At')
    )

    class Meta:
        verbose_name = _('Password Policy')
        verbose_name_plural = _('Password Policies')

    def __str__(self):
        return f"Password Policy - {self.school.name}"
    
    def validate_password(self, password):
        """
        Validate password against policy
        """
        errors = []
        
        if len(password) < self.min_length:
            errors.append(f"Password must be at least {self.min_length} characters long")
        
        if self.require_uppercase and not any(c.isupper() for c in password):
            errors.append("Password must contain at least one uppercase letter")
        
        if self.require_lowercase and not any(c.islower() for c in password):
            errors.append("Password must contain at least one lowercase letter")
        
        if self.require_numbers and not any(c.isdigit() for c in password):
            errors.append("Password must contain at least one number")
        
        if self.require_special_chars and not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            errors.append("Password must contain at least one special character")
        
        return errors


class PasswordHistory(models.Model):
    """
    Track password history for users
    """
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='password_history',
        verbose_name=_('User')
    )
    
    password_hash = models.CharField(
        max_length=255,
        verbose_name=_('Password Hash')
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )

    class Meta:
        verbose_name = _('Password History')
        verbose_name_plural = _('Password Histories')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'created_at']),
        ]

    def __str__(self):
        return f"{self.user.username} - Password History"
    
    @classmethod
    def add_password(cls, user, password):
        """
        Add password to history
        """
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        cls.objects.create(user=user, password_hash=password_hash)
        
        # Clean up old history based on policy
        try:
            if hasattr(user, 'employee') and user.employee and user.employee.school:
                policy = user.employee.school.password_policy
                history_count = policy.history_count
            else:
                history_count = 5  # Default
            
            # Keep only the most recent entries
            old_entries = cls.objects.filter(user=user).order_by('-created_at')[history_count:]
            cls.objects.filter(id__in=[entry.id for entry in old_entries]).delete()
        except:
            pass
    
    @classmethod
    def check_password_reuse(cls, user, password):
        """
        Check if password was used recently
        """
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        return cls.objects.filter(user=user, password_hash=password_hash).exists()


class SessionSecurity(models.Model):
    """
    Enhanced session security tracking
    """
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='security_sessions',
        verbose_name=_('User')
    )
    
    session_key = models.CharField(
        max_length=40,
        unique=True,
        verbose_name=_('Session Key')
    )
    
    ip_address = models.GenericIPAddressField(
        verbose_name=_('IP Address')
    )
    
    user_agent = models.TextField(
        blank=True,
        verbose_name=_('User Agent')
    )
    
    location = models.CharField(
        max_length=200,
        blank=True,
        verbose_name=_('Location')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    
    last_activity = models.DateTimeField(
        auto_now=True,
        verbose_name=_('Last Activity')
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )

    class Meta:
        verbose_name = _('Session Security')
        verbose_name_plural = _('Session Securities')
        ordering = ['-last_activity']
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['session_key']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.ip_address}"
    
    def is_expired(self):
        """
        Check if session is expired
        """
        from django.conf import settings
        session_age = getattr(settings, 'SESSION_COOKIE_AGE', 86400)  # 24 hours default
        return (timezone.now() - self.last_activity).seconds > session_age