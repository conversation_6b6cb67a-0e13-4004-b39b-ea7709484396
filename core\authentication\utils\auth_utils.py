"""
Authentication utilities for School ERP system
"""
import secrets
import hashlib
import base64
import json
import qrcode
import io
from datetime import datetime, timedelta
from django.conf import settings
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils.translation import gettext_lazy as _
from cryptography.fernet import Fernet
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


class MFAUtils:
    """
    Multi-Factor Authentication utilities
    """
    
    @staticmethod
    def generate_totp_secret():
        """
        Generate a new TOTP secret key
        """
        return base64.b32encode(secrets.token_bytes(20)).decode('utf-8')
    
    @staticmethod
    def generate_qr_code(user, secret, issuer_name="School ERP"):
        """
        Generate QR code for TOTP setup
        """
        try:
            import pyotp
            
            totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
                name=user.email,
                issuer_name=issuer_name
            )
            
            qr = qrcode.QRCode(version=1, box_size=10, border=5)
            qr.add_data(totp_uri)
            qr.make(fit=True)
            
            img = qr.make_image(fill_color="black", back_color="white")
            
            # Convert to base64 for web display
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            buffer.seek(0)
            
            return base64.b64encode(buffer.getvalue()).decode()
        except ImportError:
            logger.warning("pyotp not installed, QR code generation skipped")
            return None
    
    @staticmethod
    def verify_totp_token(secret, token, valid_window=1):
        """
        Verify TOTP token
        """
        try:
            import pyotp
            
            totp = pyotp.TOTP(secret)
            return totp.verify(token, valid_window=valid_window)
        except ImportError:
            logger.warning("pyotp not installed, TOTP verification skipped")
            return True  # Allow in development
        except Exception as e:
            logger.error(f"TOTP verification error: {e}")
            return False
    
    @staticmethod
    def send_sms_token(phone_number, token):
        """
        Send SMS token (placeholder - integrate with SMS provider)
        """
        try:
            # Placeholder for SMS integration
            # In production, integrate with Twilio, AWS SNS, etc.
            logger.info(f"SMS token {token} would be sent to {phone_number}")
            return True
        except Exception as e:
            logger.error(f"SMS sending error: {e}")
            return False
    
    @staticmethod
    def send_email_token(email, token, user_name=""):
        """
        Send email token
        """
        try:
            subject = _("Your verification code")
            message = render_to_string('auth/email_token.html', {
                'token': token,
                'user_name': user_name,
                'valid_minutes': 5
            })
            
            send_mail(
                subject,
                message,
                settings.DEFAULT_FROM_EMAIL,
                [email],
                html_message=message
            )
            return True
        except Exception as e:
            logger.error(f"Email token sending error: {e}")
            return False
    
    @staticmethod
    def generate_backup_codes(count=10):
        """
        Generate backup codes
        """
        codes = []
        for _ in range(count):
            code = secrets.token_hex(4).upper()
            codes.append(code)
        return codes


class PasswordUtils:
    """
    Password utilities and validation
    """
    
    @staticmethod
    def validate_password_strength(password, policy=None):
        """
        Validate password against policy
        """
        errors = []
        
        if not policy:
            # Default policy
            min_length = 8
            require_uppercase = True
            require_lowercase = True
            require_numbers = True
            require_special_chars = True
        else:
            min_length = policy.min_length
            require_uppercase = policy.require_uppercase
            require_lowercase = policy.require_lowercase
            require_numbers = policy.require_numbers
            require_special_chars = policy.require_special_chars
        
        if len(password) < min_length:
            errors.append(f"Password must be at least {min_length} characters long")
        
        if require_uppercase and not any(c.isupper() for c in password):
            errors.append("Password must contain at least one uppercase letter")
        
        if require_lowercase and not any(c.islower() for c in password):
            errors.append("Password must contain at least one lowercase letter")
        
        if require_numbers and not any(c.isdigit() for c in password):
            errors.append("Password must contain at least one number")
        
        if require_special_chars and not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            errors.append("Password must contain at least one special character")
        
        return errors
    
    @staticmethod
    def check_password_history(user, password, history_count=5):
        """
        Check if password was used recently
        """
        from core.authentication.models.auth_models import PasswordHistory
        
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        recent_passwords = PasswordHistory.objects.filter(
            user=user
        ).order_by('-created_at')[:history_count]
        
        for history in recent_passwords:
            if history.password_hash == password_hash:
                return False
        
        return True
    
    @staticmethod
    def generate_secure_password(length=12):
        """
        Generate a secure password
        """
        import string
        
        # Ensure at least one character from each required category
        password = [
            secrets.choice(string.ascii_uppercase),
            secrets.choice(string.ascii_lowercase),
            secrets.choice(string.digits),
            secrets.choice("!@#$%^&*()_+-=[]{}|;:,.<>?")
        ]
        
        # Fill the rest randomly
        all_chars = string.ascii_letters + string.digits + "!@#$%^&*()_+-=[]{}|;:,.<>?"
        for _ in range(length - 4):
            password.append(secrets.choice(all_chars))
        
        # Shuffle the password
        secrets.SystemRandom().shuffle(password)
        return ''.join(password)


class SecurityUtils:
    """
    General security utilities
    """
    
    @staticmethod
    def get_client_ip(request):
        """
        Get client IP address from request
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    @staticmethod
    def get_user_agent(request):
        """
        Get user agent from request
        """
        return request.META.get('HTTP_USER_AGENT', '')
    
    @staticmethod
    def is_suspicious_login(user, ip_address, user_agent):
        """
        Check if login attempt is suspicious
        """
        from core.authentication.models.auth_models import LoginAttempt
        
        # Check for recent successful logins from different IPs
        recent_logins = LoginAttempt.objects.filter(
            user=user,
            success=True,
            timestamp__gte=timezone.now() - timedelta(days=7)
        ).values_list('ip_address', flat=True).distinct()
        
        if recent_logins and ip_address not in recent_logins:
            return True
        
        # Check for multiple failed attempts from this IP
        failed_attempts = LoginAttempt.objects.filter(
            ip_address=ip_address,
            success=False,
            timestamp__gte=timezone.now() - timedelta(hours=1)
        ).count()
        
        if failed_attempts >= 3:
            return True
        
        return False
    
    @staticmethod
    def is_account_locked(user):
        """
        Check if user account is locked due to failed attempts
        """
        from core.authentication.models.auth_models import LoginAttempt
        
        try:
            # Get password policy for lockout settings
            if hasattr(user, 'employee') and user.employee and user.employee.school:
                policy = user.employee.school.password_policy
                lockout_attempts = policy.lockout_attempts
                lockout_duration = policy.lockout_duration_minutes
            else:
                lockout_attempts = 5
                lockout_duration = 30
            
            # Check recent failed attempts
            cutoff_time = timezone.now() - timedelta(minutes=lockout_duration)
            failed_attempts = LoginAttempt.objects.filter(
                user=user,
                success=False,
                timestamp__gte=cutoff_time
            ).count()
            
            return failed_attempts >= lockout_attempts
        except:
            return False
    
    @staticmethod
    def encrypt_data(data, key=None):
        """
        Encrypt sensitive data
        """
        try:
            if not key:
                key = settings.ENCRYPTION_KEY
            
            if not key:
                return data  # Return unencrypted in development
            
            f = Fernet(key.encode() if isinstance(key, str) else key)
            return f.encrypt(data.encode()).decode()
        except Exception as e:
            logger.error(f"Encryption error: {e}")
            return data
    
    @staticmethod
    def decrypt_data(encrypted_data, key=None):
        """
        Decrypt sensitive data
        """
        try:
            if not key:
                key = settings.ENCRYPTION_KEY
            
            if not key:
                return encrypted_data  # Return as-is in development
            
            f = Fernet(key.encode() if isinstance(key, str) else key)
            return f.decrypt(encrypted_data.encode()).decode()
        except Exception as e:
            logger.error(f"Decryption error: {e}")
            return encrypted_data
    
    @staticmethod
    def generate_secure_token(length=32):
        """
        Generate a secure random token
        """
        return secrets.token_urlsafe(length)
    
    @staticmethod
    def hash_data(data, salt=None):
        """
        Hash data with optional salt
        """
        if salt is None:
            salt = secrets.token_hex(16)
        
        hash_obj = hashlib.pbkdf2_hmac('sha256', data.encode(), salt.encode(), 100000)
        return {
            'hash': hash_obj.hex(),
            'salt': salt
        }
    
    @staticmethod
    def verify_hash(data, stored_hash, salt):
        """
        Verify hashed data
        """
        hash_obj = hashlib.pbkdf2_hmac('sha256', data.encode(), salt.encode(), 100000)
        return hash_obj.hex() == stored_hash


class JWTUtils:
    """
    JWT token utilities
    """
    
    @staticmethod
    def create_token(user, expires_in=3600):
        """
        Create JWT token for user
        """
        try:
            import jwt
            
            payload = {
                'user_id': str(user.id),
                'username': user.username,
                'exp': datetime.utcnow() + timedelta(seconds=expires_in),
                'iat': datetime.utcnow(),
                'type': 'access'
            }
            
            token = jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')
            return token
        except ImportError:
            logger.warning("PyJWT not installed, token creation skipped")
            return None
        except Exception as e:
            logger.error(f"Token creation error: {e}")
            return None
    
    @staticmethod
    def verify_token(token):
        """
        Verify JWT token
        """
        try:
            import jwt
            
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=['HS256'])
            return payload
        except ImportError:
            logger.warning("PyJWT not installed, token verification skipped")
            return None
        except jwt.ExpiredSignatureError:
            logger.warning("Token has expired")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"Invalid token: {e}")
            return None
        except Exception as e:
            logger.error(f"Token verification error: {e}")
            return None
    
    @staticmethod
    def create_refresh_token(user):
        """
        Create refresh token
        """
        try:
            import jwt
            
            payload = {
                'user_id': str(user.id),
                'exp': datetime.utcnow() + timedelta(days=7),
                'iat': datetime.utcnow(),
                'type': 'refresh'
            }
            
            token = jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')
            return token
        except ImportError:
            logger.warning("PyJWT not installed, refresh token creation skipped")
            return None
        except Exception as e:
            logger.error(f"Refresh token creation error: {e}")
            return None


class OAuth2Utils:
    """
    OAuth 2.0 utilities
    """
    
    @staticmethod
    def generate_authorization_code():
        """
        Generate OAuth 2.0 authorization code
        """
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def generate_client_credentials():
        """
        Generate OAuth 2.0 client credentials
        """
        client_id = secrets.token_urlsafe(16)
        client_secret = secrets.token_urlsafe(32)
        return client_id, client_secret
    
    @staticmethod
    def validate_redirect_uri(uri, allowed_uris):
        """
        Validate OAuth 2.0 redirect URI
        """
        return uri in allowed_uris
    
    @staticmethod
    def create_access_token(client_id, user_id, scope, expires_in=3600):
        """
        Create OAuth 2.0 access token
        """
        payload = {
            'client_id': client_id,
            'user_id': user_id,
            'scope': scope,
            'exp': datetime.utcnow() + timedelta(seconds=expires_in),
            'iat': datetime.utcnow(),
            'token_type': 'Bearer'
        }
        
        return JWTUtils.create_token({'payload': payload}, expires_in)


class SessionUtils:
    """
    Session security utilities
    """
    
    @staticmethod
    def create_secure_session(request, user):
        """
        Create secure session with tracking
        """
        from core.authentication.models.auth_models import SessionSecurity
        
        # Create session security record
        SessionSecurity.objects.create(
            user=user,
            session_key=request.session.session_key,
            ip_address=SecurityUtils.get_client_ip(request),
            user_agent=SecurityUtils.get_user_agent(request)
        )
        
        # Set session security flags
        request.session.set_expiry(settings.SESSION_COOKIE_AGE)
        request.session['user_id'] = str(user.id)
        request.session['login_time'] = timezone.now().isoformat()
        request.session['ip_address'] = SecurityUtils.get_client_ip(request)
    
    @staticmethod
    def validate_session_security(request):
        """
        Validate session security
        """
        from core.authentication.models.auth_models import SessionSecurity
        
        if not request.user.is_authenticated:
            return False
        
        try:
            session_security = SessionSecurity.objects.get(
                session_key=request.session.session_key,
                is_active=True
            )
            
            # Check IP address consistency
            current_ip = SecurityUtils.get_client_ip(request)
            if session_security.ip_address != current_ip:
                logger.warning(f"IP address mismatch for user {request.user.username}")
                return False
            
            # Update last activity
            session_security.last_activity = timezone.now()
            session_security.save()
            
            return True
        except SessionSecurity.DoesNotExist:
            return False
    
    @staticmethod
    def invalidate_user_sessions(user, except_session_key=None):
        """
        Invalidate all user sessions except current one
        """
        from core.authentication.models.auth_models import SessionSecurity
        from django.contrib.sessions.models import Session
        
        sessions = SessionSecurity.objects.filter(user=user, is_active=True)
        if except_session_key:
            sessions = sessions.exclude(session_key=except_session_key)
        
        for session_security in sessions:
            try:
                session = Session.objects.get(session_key=session_security.session_key)
                session.delete()
            except Session.DoesNotExist:
                pass
            
            session_security.is_active = False
            session_security.save()