"""
Enhanced authentication views for School ERP system
"""
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, logout, authenticate
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.views.generic import TemplateView, View
from django.http import JsonResponse, HttpResponse
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.conf import settings
from django.core.cache import cache
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
import json
import secrets
from datetime import timedelta

from core.authentication.models.auth_models import (
    MFADevice, MFABackupCode, LoginAttempt, 
    PasswordPolicy, PasswordHistory, SessionSecurity
)
from core.authentication.utils.auth_utils import (
    MFAUtils, PasswordUtils, SecurityUtils, 
    JWTUtils, SessionUtils
)

User = get_user_model()


class EnhancedLoginView(View):
    """
    Enhanced login view with MFA support
    """
    template_name = 'auth/login.html'
    
    def get(self, request):
        if request.user.is_authenticated:
            return redirect('accounts:dashboard')
        
        context = {
            'mfa_required': False,
            'backup_codes_available': False
        }
        return render(request, self.template_name, context)
    
    def post(self, request):
        username = request.POST.get('username')
        password = request.POST.get('password')
        mfa_token = request.POST.get('mfa_token')
        backup_code = request.POST.get('backup_code')
        remember_me = request.POST.get('remember_me')
        
        if not username or not password:
            messages.error(request, _('Username and password are required'))
            return render(request, self.template_name)
        
        # Log login attempt
        ip_address = SecurityUtils.get_client_ip(request)
        user_agent = SecurityUtils.get_user_agent(request)
        
        try:
            user = User.objects.get(username=username)
        except User.DoesNotExist:
            user = None
        
        # Check if account is locked
        if user and SecurityUtils.is_account_locked(user):
            LoginAttempt.objects.create(
                user=user,
                username=username,
                ip_address=ip_address,
                user_agent=user_agent,
                success=False,
                failure_reason='account_locked'
            )
            messages.error(request, _('Account is temporarily locked due to multiple failed attempts'))
            return render(request, self.template_name)
        
        # Authenticate user
        authenticated_user = authenticate(request, username=username, password=password)
        
        if not authenticated_user:
            LoginAttempt.objects.create(
                user=user,
                username=username,
                ip_address=ip_address,
                user_agent=user_agent,
                success=False,
                failure_reason='invalid_credentials'
            )
            messages.error(request, _('Invalid username or password'))
            return render(request, self.template_name)
        
        # Check if MFA is required
        mfa_devices = MFADevice.objects.filter(user=authenticated_user, is_active=True)
        if mfa_devices.exists():
            # MFA is required
            if not mfa_token and not backup_code:
                # Store partial authentication
                request.session['partial_auth_user_id'] = str(authenticated_user.id)
                request.session['partial_auth_timestamp'] = timezone.now().isoformat()
                
                LoginAttempt.objects.create(
                    user=authenticated_user,
                    username=username,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    success=False,
                    failure_reason='mfa_required',
                    mfa_required=True
                )
                
                context = {
                    'mfa_required': True,
                    'backup_codes_available': MFABackupCode.objects.filter(
                        user=authenticated_user, is_used=False
                    ).exists(),
                    'username': username
                }
                return render(request, self.template_name, context)
            
            # Verify MFA token or backup code
            mfa_success = False
            
            if backup_code:
                # Verify backup code
                try:
                    backup = MFABackupCode.objects.get(
                        user=authenticated_user,
                        code=backup_code,
                        is_used=False
                    )
                    backup.is_used = True
                    backup.used_at = timezone.now()
                    backup.save()
                    mfa_success = True
                except MFABackupCode.DoesNotExist:
                    pass
            
            elif mfa_token:
                # Verify TOTP token
                for device in mfa_devices:
                    if device.device_type == 'totp':
                        secret = SecurityUtils.decrypt_data(device.secret_key)
                        if MFAUtils.verify_totp_token(secret, mfa_token):
                            device.last_used = timezone.now()
                            device.save()
                            mfa_success = True
                            break
            
            if not mfa_success:
                LoginAttempt.objects.create(
                    user=authenticated_user,
                    username=username,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    success=False,
                    failure_reason='invalid_mfa',
                    mfa_required=True
                )
                messages.error(request, _('Invalid verification code'))
                context = {
                    'mfa_required': True,
                    'backup_codes_available': MFABackupCode.objects.filter(
                        user=authenticated_user, is_used=False
                    ).exists(),
                    'username': username
                }
                return render(request, self.template_name, context)
        
        # Successful login
        login(request, authenticated_user)
        
        # Set session expiry based on remember me
        if not remember_me:
            request.session.set_expiry(0)  # Browser session
        
        # Create secure session
        SessionUtils.create_secure_session(request, authenticated_user)
        
        # Log successful login
        LoginAttempt.objects.create(
            user=authenticated_user,
            username=username,
            ip_address=ip_address,
            user_agent=user_agent,
            success=True,
            mfa_required=mfa_devices.exists(),
            mfa_success=mfa_devices.exists()
        )
        
        # Check for suspicious login
        if SecurityUtils.is_suspicious_login(authenticated_user, ip_address, user_agent):
            # Send security alert email
            try:
                MFAUtils.send_email_token(
                    authenticated_user.email,
                    f"New login from {ip_address}",
                    authenticated_user.get_full_name()
                )
            except:
                pass
        
        # Clear partial authentication
        if 'partial_auth_user_id' in request.session:
            del request.session['partial_auth_user_id']
        if 'partial_auth_timestamp' in request.session:
            del request.session['partial_auth_timestamp']
        
        messages.success(request, _('Welcome back!'))
        next_url = request.GET.get('next', 'accounts:dashboard')
        return redirect(next_url)


class MFASetupView(LoginRequiredMixin, View):
    """
    MFA setup view
    """
    template_name = 'auth/mfa_setup.html'
    
    def get(self, request):
        user = request.user
        devices = MFADevice.objects.filter(user=user, is_active=True)
        
        context = {
            'devices': devices,
            'has_backup_codes': MFABackupCode.objects.filter(
                user=user, is_used=False
            ).exists()
        }
        return render(request, self.template_name, context)
    
    def post(self, request):
        action = request.POST.get('action')
        
        if action == 'setup_totp':
            return self._setup_totp(request)
        elif action == 'verify_totp':
            return self._verify_totp(request)
        elif action == 'generate_backup_codes':
            return self._generate_backup_codes(request)
        elif action == 'remove_device':
            return self._remove_device(request)
        
        return redirect('auth:mfa_setup')
    
    def _setup_totp(self, request):
        """
        Setup TOTP device
        """
        device_name = request.POST.get('device_name', 'Authenticator App')
        
        # Generate secret
        secret = MFAUtils.generate_totp_secret()
        encrypted_secret = SecurityUtils.encrypt_data(secret)
        
        # Store in session for verification
        request.session['totp_secret'] = secret
        request.session['totp_device_name'] = device_name
        
        # Generate QR code
        qr_code = MFAUtils.generate_qr_code(request.user, secret)
        
        context = {
            'setup_step': 'verify',
            'secret': secret,
            'qr_code': qr_code,
            'device_name': device_name
        }
        return render(request, self.template_name, context)
    
    def _verify_totp(self, request):
        """
        Verify TOTP setup
        """
        token = request.POST.get('token')
        secret = request.session.get('totp_secret')
        device_name = request.session.get('totp_device_name')
        
        if not secret or not token:
            messages.error(request, _('Invalid setup session'))
            return redirect('auth:mfa_setup')
        
        if MFAUtils.verify_totp_token(secret, token):
            # Create MFA device
            encrypted_secret = SecurityUtils.encrypt_data(secret)
            MFADevice.objects.create(
                user=request.user,
                device_type='totp',
                name=device_name,
                secret_key=encrypted_secret,
                is_primary=not MFADevice.objects.filter(user=request.user).exists()
            )
            
            # Clear session
            del request.session['totp_secret']
            del request.session['totp_device_name']
            
            messages.success(request, _('TOTP device setup successfully'))
        else:
            messages.error(request, _('Invalid verification code'))
            context = {
                'setup_step': 'verify',
                'secret': secret,
                'qr_code': MFAUtils.generate_qr_code(request.user, secret),
                'device_name': device_name
            }
            return render(request, self.template_name, context)
        
        return redirect('auth:mfa_setup')
    
    def _generate_backup_codes(self, request):
        """
        Generate backup codes
        """
        # Remove existing unused backup codes
        MFABackupCode.objects.filter(user=request.user, is_used=False).delete()
        
        # Generate new codes
        codes = MFABackupCode.generate_codes(request.user)
        
        context = {
            'backup_codes': codes,
            'show_backup_codes': True
        }
        messages.success(request, _('New backup codes generated'))
        return render(request, self.template_name, context)
    
    def _remove_device(self, request):
        """
        Remove MFA device
        """
        device_id = request.POST.get('device_id')
        
        try:
            device = MFADevice.objects.get(id=device_id, user=request.user)
            device.delete()
            messages.success(request, _('Device removed successfully'))
        except MFADevice.DoesNotExist:
            messages.error(request, _('Device not found'))
        
        return redirect('auth:mfa_setup')


class PasswordChangeView(LoginRequiredMixin, View):
    """
    Enhanced password change view with policy enforcement
    """
    template_name = 'auth/password_change.html'
    
    def get(self, request):
        # Get password policy
        policy = self._get_password_policy(request.user)
        
        context = {
            'policy': policy
        }
        return render(request, self.template_name, context)
    
    def post(self, request):
        current_password = request.POST.get('current_password')
        new_password = request.POST.get('new_password')
        confirm_password = request.POST.get('confirm_password')
        
        if not all([current_password, new_password, confirm_password]):
            messages.error(request, _('All fields are required'))
            return render(request, self.template_name)
        
        # Verify current password
        if not request.user.check_password(current_password):
            messages.error(request, _('Current password is incorrect'))
            return render(request, self.template_name)
        
        # Check password confirmation
        if new_password != confirm_password:
            messages.error(request, _('New passwords do not match'))
            return render(request, self.template_name)
        
        # Get password policy
        policy = self._get_password_policy(request.user)
        
        # Validate password strength
        if policy:
            errors = policy.validate_password(new_password)
            if errors:
                for error in errors:
                    messages.error(request, error)
                return render(request, self.template_name)
        
        # Check password history
        if not PasswordUtils.check_password_history(request.user, new_password):
            messages.error(request, _('Password was used recently. Please choose a different password'))
            return render(request, self.template_name)
        
        # Change password
        request.user.set_password(new_password)
        request.user.save()
        
        # Add to password history
        PasswordHistory.add_password(request.user, new_password)
        
        # Invalidate other sessions
        SessionUtils.invalidate_user_sessions(request.user, request.session.session_key)
        
        messages.success(request, _('Password changed successfully'))
        return redirect('accounts:dashboard')
    
    def _get_password_policy(self, user):
        """
        Get password policy for user
        """
        try:
            if hasattr(user, 'employee') and user.employee and user.employee.school:
                return user.employee.school.password_policy
        except:
            pass
        return None


class SessionManagementView(LoginRequiredMixin, View):
    """
    Session management view
    """
    template_name = 'auth/sessions.html'
    
    def get(self, request):
        sessions = SessionSecurity.objects.filter(
            user=request.user,
            is_active=True
        ).order_by('-last_activity')
        
        current_session_key = request.session.session_key
        
        context = {
            'sessions': sessions,
            'current_session_key': current_session_key
        }
        return render(request, self.template_name, context)
    
    def post(self, request):
        action = request.POST.get('action')
        
        if action == 'terminate_session':
            session_id = request.POST.get('session_id')
            try:
                session_security = SessionSecurity.objects.get(
                    id=session_id,
                    user=request.user
                )
                
                # Don't allow terminating current session
                if session_security.session_key != request.session.session_key:
                    from django.contrib.sessions.models import Session
                    try:
                        session = Session.objects.get(session_key=session_security.session_key)
                        session.delete()
                    except Session.DoesNotExist:
                        pass
                    
                    session_security.is_active = False
                    session_security.save()
                    
                    messages.success(request, _('Session terminated successfully'))
                else:
                    messages.error(request, _('Cannot terminate current session'))
            except SessionSecurity.DoesNotExist:
                messages.error(request, _('Session not found'))
        
        elif action == 'terminate_all':
            SessionUtils.invalidate_user_sessions(request.user, request.session.session_key)
            messages.success(request, _('All other sessions terminated'))
        
        return redirect('auth:sessions')


class APITokenView(LoginRequiredMixin, APIView):
    """
    API token management
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """
        Get user's API tokens
        """
        # In a real implementation, you'd have a Token model
        # For now, return empty list
        return Response({'tokens': []})
    
    def post(self, request):
        """
        Create new API token
        """
        token_name = request.data.get('name', 'API Token')
        expires_in = request.data.get('expires_in', 3600)  # 1 hour default
        
        # Create JWT token
        token = JWTUtils.create_token(request.user, expires_in)
        
        if token:
            return Response({
                'token': token,
                'name': token_name,
                'expires_in': expires_in
            }, status=status.HTTP_201_CREATED)
        else:
            return Response({
                'error': 'Failed to create token'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def delete(self, request, token_id):
        """
        Revoke API token
        """
        # In a real implementation, you'd revoke the token
        return Response({'message': 'Token revoked'})


class OAuth2AuthorizeView(View):
    """
    OAuth 2.0 authorization endpoint
    """
    template_name = 'auth/oauth2_authorize.html'
    
    def get(self, request):
        client_id = request.GET.get('client_id')
        redirect_uri = request.GET.get('redirect_uri')
        response_type = request.GET.get('response_type')
        scope = request.GET.get('scope', 'read')
        state = request.GET.get('state')
        
        if not all([client_id, redirect_uri, response_type]):
            return JsonResponse({'error': 'invalid_request'}, status=400)
        
        if response_type != 'code':
            return JsonResponse({'error': 'unsupported_response_type'}, status=400)
        
        # In a real implementation, validate client_id and redirect_uri
        # For now, show authorization page
        
        context = {
            'client_id': client_id,
            'redirect_uri': redirect_uri,
            'scope': scope,
            'state': state
        }
        return render(request, self.template_name, context)
    
    def post(self, request):
        if not request.user.is_authenticated:
            return redirect('auth:login')
        
        action = request.POST.get('action')
        client_id = request.POST.get('client_id')
        redirect_uri = request.POST.get('redirect_uri')
        scope = request.POST.get('scope')
        state = request.POST.get('state')
        
        if action == 'authorize':
            # Generate authorization code
            from .auth_utils import OAuth2Utils
            auth_code = OAuth2Utils.generate_authorization_code()
            
            # Store authorization code (in real implementation, use database)
            cache.set(f'oauth2_code_{auth_code}', {
                'user_id': str(request.user.id),
                'client_id': client_id,
                'scope': scope
            }, timeout=600)  # 10 minutes
            
            # Redirect with authorization code
            redirect_url = f"{redirect_uri}?code={auth_code}"
            if state:
                redirect_url += f"&state={state}"
            
            return redirect(redirect_url)
        
        else:
            # User denied authorization
            redirect_url = f"{redirect_uri}?error=access_denied"
            if state:
                redirect_url += f"&state={state}"
            
            return redirect(redirect_url)


class OAuth2TokenView(View):
    """
    OAuth 2.0 token endpoint
    """
    
    @method_decorator(csrf_exempt)
    def dispatch(self, *args, **kwargs):
        return super().dispatch(*args, **kwargs)
    
    def post(self, request):
        grant_type = request.POST.get('grant_type')
        
        if grant_type == 'authorization_code':
            return self._handle_authorization_code(request)
        elif grant_type == 'refresh_token':
            return self._handle_refresh_token(request)
        else:
            return JsonResponse({'error': 'unsupported_grant_type'}, status=400)
    
    def _handle_authorization_code(self, request):
        """
        Handle authorization code grant
        """
        code = request.POST.get('code')
        client_id = request.POST.get('client_id')
        client_secret = request.POST.get('client_secret')
        redirect_uri = request.POST.get('redirect_uri')
        
        if not all([code, client_id, client_secret]):
            return JsonResponse({'error': 'invalid_request'}, status=400)
        
        # Validate authorization code
        code_data = cache.get(f'oauth2_code_{code}')
        if not code_data:
            return JsonResponse({'error': 'invalid_grant'}, status=400)
        
        # In real implementation, validate client credentials
        
        # Get user
        try:
            user = User.objects.get(id=code_data['user_id'])
        except User.DoesNotExist:
            return JsonResponse({'error': 'invalid_grant'}, status=400)
        
        # Create tokens
        from .auth_utils import OAuth2Utils
        access_token = OAuth2Utils.create_access_token(
            client_id, str(user.id), code_data['scope']
        )
        refresh_token = JWTUtils.create_refresh_token(user)
        
        # Remove authorization code
        cache.delete(f'oauth2_code_{code}')
        
        return JsonResponse({
            'access_token': access_token,
            'token_type': 'Bearer',
            'expires_in': 3600,
            'refresh_token': refresh_token,
            'scope': code_data['scope']
        })
    
    def _handle_refresh_token(self, request):
        """
        Handle refresh token grant
        """
        refresh_token = request.POST.get('refresh_token')
        
        if not refresh_token:
            return JsonResponse({'error': 'invalid_request'}, status=400)
        
        # Verify refresh token
        payload = JWTUtils.verify_token(refresh_token)
        if not payload or payload.get('type') != 'refresh':
            return JsonResponse({'error': 'invalid_grant'}, status=400)
        
        # Get user
        try:
            user = User.objects.get(id=payload['user_id'])
        except User.DoesNotExist:
            return JsonResponse({'error': 'invalid_grant'}, status=400)
        
        # Create new access token
        access_token = JWTUtils.create_token(user)
        
        return JsonResponse({
            'access_token': access_token,
            'token_type': 'Bearer',
            'expires_in': 3600
        })


@login_required
def logout_view(request):
    """
    Enhanced logout view
    """
    # Mark session as inactive
    try:
        session_security = SessionSecurity.objects.get(
            session_key=request.session.session_key,
            is_active=True
        )
        session_security.is_active = False
        session_security.save()
    except SessionSecurity.DoesNotExist:
        pass
    
    logout(request)
    messages.success(request, _('You have been logged out successfully'))
    return redirect('auth:login')