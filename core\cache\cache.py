"""
Redis Caching System for School ERP
"""
import json
import pickle
import hashlib
from typing import Any, Optional, Union, List, Dict
from datetime import datetime, timedelta
from functools import wraps

from django.core.cache import cache
from django.core.cache.backends.redis import RedisCache
from django.conf import settings
from django.db.models import Model, QuerySet
from django.core.serializers import serialize
from django.utils import timezone
import redis
import logging

logger = logging.getLogger(__name__)


class CacheManager:
    """
    Centralized cache management system
    """
    
    # Cache key prefixes for different data types
    PREFIXES = {
        'model': 'model',
        'query': 'query',
        'view': 'view',
        'session': 'session',
        'user': 'user',
        'school': 'school',
        'academic': 'academic',
        'financial': 'financial',
        'report': 'report'
    }
    
    # Default cache timeouts (in seconds)
    TIMEOUTS = {
        'short': 300,      # 5 minutes
        'medium': 1800,    # 30 minutes
        'long': 3600,      # 1 hour
        'daily': 86400,    # 24 hours
        'weekly': 604800,  # 7 days
    }
    
    def __init__(self):
        self.redis_client = self._get_redis_client()
    
    def _get_redis_client(self) -> redis.Redis:
        """Get Redis client instance"""
        try:
            if hasattr(settings, 'CACHES') and 'default' in settings.CACHES:
                cache_config = settings.CACHES['default']
                if 'redis' in cache_config.get('BACKEND', '').lower():
                    location = cache_config.get('LOCATION', 'redis://127.0.0.1:6379/1')
                    return redis.from_url(location)
            
            # Fallback to default Redis connection
            return redis.Redis(host='localhost', port=6379, db=1, decode_responses=True)
        except Exception as e:
            logger.warning(f"Redis connection failed: {e}. Using Django cache fallback.")
            return None
    
    def generate_key(self, prefix: str, *args, **kwargs) -> str:
        """
        Generate a consistent cache key
        """
        key_parts = [self.PREFIXES.get(prefix, prefix)]
        
        # Add positional arguments
        for arg in args:
            if isinstance(arg, (str, int)):
                key_parts.append(str(arg))
            elif isinstance(arg, Model):
                key_parts.append(f"{arg._meta.label}_{arg.pk}")
            else:
                key_parts.append(str(hash(str(arg))))
        
        # Add keyword arguments
        for key, value in sorted(kwargs.items()):
            key_parts.append(f"{key}_{value}")
        
        # Create hash for very long keys
        key = ":".join(key_parts)
        if len(key) > 200:
            key_hash = hashlib.md5(key.encode()).hexdigest()
            key = f"{key_parts[0]}:hash:{key_hash}"
        
        return key
    
    def set(self, key: str, value: Any, timeout: Union[str, int] = 'medium') -> bool:
        """
        Set cache value with timeout
        """
        try:
            if isinstance(timeout, str):
                timeout = self.TIMEOUTS.get(timeout, self.TIMEOUTS['medium'])
            
            # Serialize complex objects
            if isinstance(value, (QuerySet, Model)):
                value = self._serialize_django_object(value)
            elif not isinstance(value, (str, int, float, bool, list, dict)):
                value = pickle.dumps(value)
            
            if self.redis_client:
                return self.redis_client.setex(key, timeout, json.dumps(value) if isinstance(value, (dict, list)) else value)
            else:
                return cache.set(key, value, timeout)
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get cache value
        """
        try:
            if self.redis_client:
                value = self.redis_client.get(key)
                if value is not None:
                    try:
                        return json.loads(value)
                    except (json.JSONDecodeError, TypeError):
                        return value
            else:
                value = cache.get(key)
                if value is not None:
                    return value
            
            return default
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            return default
    
    def delete(self, key: str) -> bool:
        """
        Delete cache key
        """
        try:
            if self.redis_client:
                return bool(self.redis_client.delete(key))
            else:
                cache.delete(key)
                return True
        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
            return False
    
    def delete_pattern(self, pattern: str) -> int:
        """
        Delete all keys matching pattern
        """
        try:
            if self.redis_client:
                keys = self.redis_client.keys(pattern)
                if keys:
                    return self.redis_client.delete(*keys)
                return 0
            else:
                # Django cache doesn't support pattern deletion
                logger.warning("Pattern deletion not supported with Django cache backend")
                return 0
        except Exception as e:
            logger.error(f"Cache pattern delete error for pattern {pattern}: {e}")
            return 0
    
    def clear_all(self) -> bool:
        """
        Clear all cache
        """
        try:
            if self.redis_client:
                return self.redis_client.flushdb()
            else:
                cache.clear()
                return True
        except Exception as e:
            logger.error(f"Cache clear error: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics
        """
        try:
            if self.redis_client:
                info = self.redis_client.info()
                return {
                    'connected_clients': info.get('connected_clients', 0),
                    'used_memory': info.get('used_memory_human', '0B'),
                    'keyspace_hits': info.get('keyspace_hits', 0),
                    'keyspace_misses': info.get('keyspace_misses', 0),
                    'total_commands_processed': info.get('total_commands_processed', 0),
                }
            else:
                return {'backend': 'django_cache', 'stats': 'not_available'}
        except Exception as e:
            logger.error(f"Cache stats error: {e}")
            return {'error': str(e)}
    
    def _serialize_django_object(self, obj: Union[Model, QuerySet]) -> str:
        """
        Serialize Django model or queryset
        """
        if isinstance(obj, QuerySet):
            return serialize('json', obj)
        elif isinstance(obj, Model):
            return serialize('json', [obj])
        else:
            return str(obj)


# Global cache manager instance
cache_manager = CacheManager()


def cache_result(timeout: Union[str, int] = 'medium', key_prefix: str = 'view'):
    """
    Decorator to cache function results
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = cache_manager.generate_key(
                key_prefix, 
                func.__name__, 
                *args, 
                **kwargs
            )
            
            # Try to get from cache
            result = cache_manager.get(cache_key)
            if result is not None:
                logger.debug(f"Cache hit for {cache_key}")
                return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache_manager.set(cache_key, result, timeout)
            logger.debug(f"Cache set for {cache_key}")
            
            return result
        return wrapper
    return decorator


def invalidate_cache(pattern: str):
    """
    Invalidate cache by pattern
    """
    return cache_manager.delete_pattern(pattern)


class ModelCacheManager:
    """
    Model-specific caching utilities
    """
    
    @staticmethod
    def get_model_cache_key(model_class, pk: Any) -> str:
        """Generate cache key for model instance"""
        return cache_manager.generate_key('model', model_class._meta.label, pk)
    
    @staticmethod
    def get_queryset_cache_key(queryset: QuerySet, **filters) -> str:
        """Generate cache key for queryset"""
        model_label = queryset.model._meta.label
        query_hash = hashlib.md5(str(queryset.query).encode()).hexdigest()[:8]
        return cache_manager.generate_key('query', model_label, query_hash, **filters)
    
    @staticmethod
    def cache_model_instance(instance: Model, timeout: Union[str, int] = 'long'):
        """Cache model instance"""
        key = ModelCacheManager.get_model_cache_key(instance.__class__, instance.pk)
        return cache_manager.set(key, instance, timeout)
    
    @staticmethod
    def get_cached_model_instance(model_class, pk: Any):
        """Get cached model instance"""
        key = ModelCacheManager.get_model_cache_key(model_class, pk)
        return cache_manager.get(key)
    
    @staticmethod
    def invalidate_model_cache(model_class, pk: Any = None):
        """Invalidate model cache"""
        if pk:
            key = ModelCacheManager.get_model_cache_key(model_class, pk)
            return cache_manager.delete(key)
        else:
            pattern = f"model:{model_class._meta.label}:*"
            return cache_manager.delete_pattern(pattern)


class SchoolCacheManager:
    """
    School-specific caching for multi-tenant data
    """
    
    @staticmethod
    def get_school_cache_key(school_id: int, data_type: str, *args) -> str:
        """Generate school-specific cache key"""
        return cache_manager.generate_key('school', school_id, data_type, *args)
    
    @staticmethod
    def cache_school_data(school_id: int, data_type: str, data: Any, timeout: Union[str, int] = 'medium'):
        """Cache school-specific data"""
        key = SchoolCacheManager.get_school_cache_key(school_id, data_type)
        return cache_manager.set(key, data, timeout)
    
    @staticmethod
    def get_cached_school_data(school_id: int, data_type: str):
        """Get cached school data"""
        key = SchoolCacheManager.get_school_cache_key(school_id, data_type)
        return cache_manager.get(key)
    
    @staticmethod
    def invalidate_school_cache(school_id: int, data_type: str = None):
        """Invalidate school cache"""
        if data_type:
            key = SchoolCacheManager.get_school_cache_key(school_id, data_type)
            return cache_manager.delete(key)
        else:
            pattern = f"school:{school_id}:*"
            return cache_manager.delete_pattern(pattern)


class ReportCacheManager:
    """
    Report-specific caching for expensive queries
    """
    
    @staticmethod
    def get_report_cache_key(report_type: str, school_id: int, **params) -> str:
        """Generate report cache key"""
        return cache_manager.generate_key('report', report_type, school_id, **params)
    
    @staticmethod
    def cache_report_data(report_type: str, school_id: int, data: Any, timeout: Union[str, int] = 'daily', **params):
        """Cache report data"""
        key = ReportCacheManager.get_report_cache_key(report_type, school_id, **params)
        return cache_manager.set(key, data, timeout)
    
    @staticmethod
    def get_cached_report_data(report_type: str, school_id: int, **params):
        """Get cached report data"""
        key = ReportCacheManager.get_report_cache_key(report_type, school_id, **params)
        return cache_manager.get(key)
    
    @staticmethod
    def invalidate_report_cache(report_type: str = None, school_id: int = None):
        """Invalidate report cache"""
        if report_type and school_id:
            pattern = f"report:{report_type}:{school_id}:*"
        elif school_id:
            pattern = f"report:*:{school_id}:*"
        else:
            pattern = "report:*"
        
        return cache_manager.delete_pattern(pattern)


# Cache invalidation signals
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver


@receiver(post_save)
def invalidate_model_cache_on_save(sender, instance, **kwargs):
    """Invalidate model cache when instance is saved"""
    try:
        # Invalidate specific instance cache
        ModelCacheManager.invalidate_model_cache(sender, instance.pk)
        
        # Invalidate school-specific cache if model has school field
        if hasattr(instance, 'school_id'):
            SchoolCacheManager.invalidate_school_cache(instance.school_id)
        
        # Invalidate related report caches
        ReportCacheManager.invalidate_report_cache(school_id=getattr(instance, 'school_id', None))
        
    except Exception as e:
        logger.error(f"Cache invalidation error on save: {e}")


@receiver(post_delete)
def invalidate_model_cache_on_delete(sender, instance, **kwargs):
    """Invalidate model cache when instance is deleted"""
    try:
        # Invalidate specific instance cache
        ModelCacheManager.invalidate_model_cache(sender, instance.pk)
        
        # Invalidate school-specific cache if model has school field
        if hasattr(instance, 'school_id'):
            SchoolCacheManager.invalidate_school_cache(instance.school_id)
        
        # Invalidate related report caches
        ReportCacheManager.invalidate_report_cache(school_id=getattr(instance, 'school_id', None))
        
    except Exception as e:
        logger.error(f"Cache invalidation error on delete: {e}")


# Performance monitoring utilities
class CachePerformanceMonitor:
    """
    Monitor cache performance and hit rates
    """
    
    @staticmethod
    def get_hit_rate() -> float:
        """Calculate cache hit rate"""
        try:
            stats = cache_manager.get_stats()
            hits = stats.get('keyspace_hits', 0)
            misses = stats.get('keyspace_misses', 0)
            total = hits + misses
            
            if total > 0:
                return (hits / total) * 100
            return 0.0
        except Exception as e:
            logger.error(f"Hit rate calculation error: {e}")
            return 0.0
    
    @staticmethod
    def get_memory_usage() -> Dict[str, Any]:
        """Get cache memory usage"""
        try:
            stats = cache_manager.get_stats()
            return {
                'used_memory': stats.get('used_memory', 'unknown'),
                'connected_clients': stats.get('connected_clients', 0),
                'total_commands': stats.get('total_commands_processed', 0)
            }
        except Exception as e:
            logger.error(f"Memory usage calculation error: {e}")
            return {'error': str(e)}
    
    @staticmethod
    def log_cache_performance():
        """Log cache performance metrics"""
        try:
            hit_rate = CachePerformanceMonitor.get_hit_rate()
            memory_usage = CachePerformanceMonitor.get_memory_usage()
            
            logger.info(f"Cache Performance - Hit Rate: {hit_rate:.2f}%, Memory: {memory_usage}")
        except Exception as e:
            logger.error(f"Performance logging error: {e}")


# Export main components
__all__ = [
    'cache_manager',
    'cache_result',
    'invalidate_cache',
    'ModelCacheManager',
    'SchoolCacheManager', 
    'ReportCacheManager',
    'CachePerformanceMonitor'
]