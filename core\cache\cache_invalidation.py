"""
Cache Invalidation Logic for School ERP System
"""
from typing import Dict, List, Set, Any, Optional
from django.db.models import Model
from django.apps import apps
import logging

from .cache import cache_manager, SchoolCacheManager, ReportCacheManager

logger = logging.getLogger(__name__)


class CacheInvalidationManager:
    """
    Manages cache invalidation strategies for different models and scenarios
    """
    
    # Define cache dependencies - when model X changes, invalidate cache for models Y
    CACHE_DEPENDENCIES = {
        'core.School': [
            'students.*',
            'academics.*', 
            'finance.*',
            'hr.*',
            'transportation.*',
            'library.*',
            'health.*',
            'inventory.*'
        ],
        'core.AcademicYear': [
            'academics.*',
            'students.StudentEnrollment',
            'finance.FeeStructure',
            'finance.StudentFee'
        ],
        'students.Student': [
            'academics.Attendance',
            'academics.Grade',
            'finance.StudentFee',
            'finance.Payment',
            'transportation.StudentTransportation',
            'library.BookBorrowing',
            'health.HealthProfile'
        ],
        'academics.Grade': [
            'students.StudentEnrollment',
            'academics.Class',
            'finance.FeeStructure'
        ],
        'academics.Subject': [
            'academics.Class',
            'academics.Attendance',
            'academics.Grade'
        ],
        'academics.Class': [
            'academics.Attendance',
            'academics.Grade',
            'academics.Timetable'
        ],
        'hr.Employee': [
            'academics.Class',
            'transportation.Driver',
            'hr.Attendance',
            'hr.Payroll'
        ],
        'finance.FeeStructure': [
            'finance.StudentFee'
        ],
        'transportation.Route': [
            'transportation.StudentTransportation',
            'transportation.TransportationFee',
            'transportation.GPSTracking'
        ],
        'library.Book': [
            'library.BookBorrowing'
        ]
    }
    
    # Define cache tags for different data types
    CACHE_TAGS = {
        'dashboard': ['dashboard', 'stats', 'summary'],
        'academic': ['grades', 'attendance', 'schedule', 'classes'],
        'financial': ['fees', 'payments', 'budget', 'reports'],
        'student': ['students', 'enrollment', 'profile'],
        'staff': ['employees', 'payroll', 'attendance'],
        'transportation': ['routes', 'vehicles', 'tracking'],
        'library': ['books', 'borrowing', 'catalog'],
        'reports': ['analytics', 'statistics', 'charts']
    }
    
    def __init__(self):
        self.invalidation_queue = []
    
    def invalidate_model_cache(self, model_instance: Model, cascade: bool = True):
        """
        Invalidate cache for a specific model instance and related data
        """
        try:
            model_label = f"{model_instance._meta.app_label}.{model_instance._meta.model_name}"
            school_id = getattr(model_instance, 'school_id', None)
            
            # Invalidate direct model cache
            self._invalidate_direct_cache(model_instance)
            
            # Invalidate school-specific cache
            if school_id:
                self._invalidate_school_cache(school_id, model_label)
            
            # Cascade invalidation to dependent models
            if cascade:
                self._cascade_invalidation(model_label, school_id)
            
            # Invalidate tagged cache
            self._invalidate_tagged_cache(model_label)
            
            logger.info(f"Cache invalidated for {model_label} (ID: {model_instance.pk})")
            
        except Exception as e:
            logger.error(f"Cache invalidation error for {model_instance}: {e}")
    
    def _invalidate_direct_cache(self, model_instance: Model):
        """Invalidate direct model instance cache"""
        from .cache import ModelCacheManager
        
        # Invalidate specific instance
        ModelCacheManager.invalidate_model_cache(
            model_instance.__class__, 
            model_instance.pk
        )
        
        # Invalidate all instances of this model type
        ModelCacheManager.invalidate_model_cache(model_instance.__class__)
    
    def _invalidate_school_cache(self, school_id: int, model_label: str):
        """Invalidate school-specific cache"""
        # Invalidate general school data
        SchoolCacheManager.invalidate_school_cache(school_id)
        
        # Invalidate specific data types
        data_types = self._get_data_types_for_model(model_label)
        for data_type in data_types:
            SchoolCacheManager.invalidate_school_cache(school_id, data_type)
    
    def _cascade_invalidation(self, model_label: str, school_id: Optional[int]):
        """Cascade invalidation to dependent models"""
        dependencies = self.CACHE_DEPENDENCIES.get(model_label, [])
        
        for dependency in dependencies:
            if dependency.endswith('.*'):
                # Invalidate entire app cache
                app_label = dependency.replace('.*', '')
                pattern = f"model:{app_label}.*:*"
                cache_manager.delete_pattern(pattern)
            else:
                # Invalidate specific model cache
                pattern = f"model:{dependency}:*"
                cache_manager.delete_pattern(pattern)
        
        # Invalidate reports that might be affected
        if school_id:
            ReportCacheManager.invalidate_report_cache(school_id=school_id)
    
    def _invalidate_tagged_cache(self, model_label: str):
        """Invalidate cache by tags"""
        tags = self._get_tags_for_model(model_label)
        
        for tag in tags:
            pattern = f"*:{tag}:*"
            cache_manager.delete_pattern(pattern)
    
    def _get_data_types_for_model(self, model_label: str) -> List[str]:
        """Get data types that should be invalidated for a model"""
        app_label, model_name = model_label.split('.')
        
        data_type_mapping = {
            'students': ['students', 'enrollment', 'profile'],
            'academics': ['academic', 'grades', 'attendance', 'schedule'],
            'finance': ['financial', 'fees', 'payments'],
            'hr': ['staff', 'payroll', 'employees'],
            'transportation': ['transportation', 'routes', 'vehicles'],
            'library': ['library', 'books', 'borrowing'],
            'health': ['health', 'medical'],
            'inventory': ['inventory', 'assets']
        }
        
        return data_type_mapping.get(app_label, [app_label])
    
    def _get_tags_for_model(self, model_label: str) -> List[str]:
        """Get cache tags that should be invalidated for a model"""
        app_label, model_name = model_label.split('.')
        
        # Always invalidate dashboard and general stats
        tags = ['dashboard', 'stats']
        
        # Add specific tags based on app
        if app_label in self.CACHE_TAGS:
            tags.extend(self.CACHE_TAGS[app_label])
        
        return tags
    
    def bulk_invalidate(self, model_instances: List[Model]):
        """
        Bulk invalidate cache for multiple model instances
        """
        try:
            # Group by model type and school
            grouped_instances = {}
            
            for instance in model_instances:
                model_label = f"{instance._meta.app_label}.{instance._meta.model_name}"
                school_id = getattr(instance, 'school_id', None)
                
                key = (model_label, school_id)
                if key not in grouped_instances:
                    grouped_instances[key] = []
                grouped_instances[key].append(instance)
            
            # Invalidate cache for each group
            for (model_label, school_id), instances in grouped_instances.items():
                self._bulk_invalidate_group(model_label, school_id, instances)
            
            logger.info(f"Bulk cache invalidation completed for {len(model_instances)} instances")
            
        except Exception as e:
            logger.error(f"Bulk cache invalidation error: {e}")
    
    def _bulk_invalidate_group(self, model_label: str, school_id: Optional[int], instances: List[Model]):
        """Invalidate cache for a group of similar instances"""
        # Invalidate all instances at once
        pattern = f"model:{model_label}:*"
        cache_manager.delete_pattern(pattern)
        
        # Invalidate school cache
        if school_id:
            SchoolCacheManager.invalidate_school_cache(school_id)
        
        # Invalidate reports
        if school_id:
            ReportCacheManager.invalidate_report_cache(school_id=school_id)
    
    def schedule_invalidation(self, model_instance: Model, delay_seconds: int = 0):
        """
        Schedule cache invalidation for later execution
        """
        from django.utils import timezone
        from datetime import timedelta
        
        invalidation_time = timezone.now() + timedelta(seconds=delay_seconds)
        
        self.invalidation_queue.append({
            'instance': model_instance,
            'scheduled_time': invalidation_time,
            'executed': False
        })
        
        logger.info(f"Cache invalidation scheduled for {model_instance} at {invalidation_time}")
    
    def process_scheduled_invalidations(self):
        """
        Process scheduled cache invalidations
        """
        from django.utils import timezone
        
        now = timezone.now()
        processed = 0
        
        for item in self.invalidation_queue:
            if not item['executed'] and item['scheduled_time'] <= now:
                try:
                    self.invalidate_model_cache(item['instance'])
                    item['executed'] = True
                    processed += 1
                except Exception as e:
                    logger.error(f"Scheduled invalidation error: {e}")
        
        # Clean up executed items
        self.invalidation_queue = [
            item for item in self.invalidation_queue 
            if not item['executed']
        ]
        
        if processed > 0:
            logger.info(f"Processed {processed} scheduled cache invalidations")
        
        return processed


class SmartCacheInvalidation:
    """
    Smart cache invalidation based on data relationships and usage patterns
    """
    
    def __init__(self):
        self.invalidation_manager = CacheInvalidationManager()
        self.usage_stats = {}
    
    def track_cache_usage(self, cache_key: str, hit: bool):
        """Track cache usage statistics"""
        if cache_key not in self.usage_stats:
            self.usage_stats[cache_key] = {
                'hits': 0,
                'misses': 0,
                'last_access': None
            }
        
        if hit:
            self.usage_stats[cache_key]['hits'] += 1
        else:
            self.usage_stats[cache_key]['misses'] += 1
        
        from django.utils import timezone
        self.usage_stats[cache_key]['last_access'] = timezone.now()
    
    def get_cache_efficiency(self, cache_key: str) -> float:
        """Calculate cache efficiency for a specific key"""
        if cache_key not in self.usage_stats:
            return 0.0
        
        stats = self.usage_stats[cache_key]
        total = stats['hits'] + stats['misses']
        
        if total == 0:
            return 0.0
        
        return (stats['hits'] / total) * 100
    
    def should_invalidate_cache(self, model_instance: Model) -> bool:
        """
        Determine if cache should be invalidated based on smart criteria
        """
        model_label = f"{model_instance._meta.app_label}.{model_instance._meta.model_name}"
        
        # Always invalidate for critical models
        critical_models = [
            'students.Student',
            'academics.Grade', 
            'finance.Payment',
            'hr.Employee'
        ]
        
        if model_label in critical_models:
            return True
        
        # Check if related cache has low efficiency
        related_keys = self._get_related_cache_keys(model_instance)
        for key in related_keys:
            if self.get_cache_efficiency(key) < 50:  # Less than 50% hit rate
                return True
        
        return False
    
    def _get_related_cache_keys(self, model_instance: Model) -> List[str]:
        """Get cache keys related to a model instance"""
        # This would be implemented based on your specific cache key patterns
        model_label = f"{model_instance._meta.app_label}.{model_instance._meta.model_name}"
        school_id = getattr(model_instance, 'school_id', None)
        
        keys = []
        
        # Add model-specific keys
        keys.append(f"model:{model_label}:{model_instance.pk}")
        
        # Add school-specific keys
        if school_id:
            keys.append(f"school:{school_id}:*")
        
        return keys
    
    def optimize_cache_strategy(self):
        """
        Optimize cache strategy based on usage patterns
        """
        recommendations = []
        
        for cache_key, stats in self.usage_stats.items():
            efficiency = self.get_cache_efficiency(cache_key)
            
            if efficiency < 30:
                recommendations.append({
                    'key': cache_key,
                    'action': 'consider_removal',
                    'reason': f'Low efficiency: {efficiency:.1f}%'
                })
            elif efficiency > 90:
                recommendations.append({
                    'key': cache_key,
                    'action': 'increase_timeout',
                    'reason': f'High efficiency: {efficiency:.1f}%'
                })
        
        return recommendations


# Global invalidation manager instance
invalidation_manager = CacheInvalidationManager()
smart_invalidation = SmartCacheInvalidation()


# Utility functions
def invalidate_user_cache(user_id: int):
    """Invalidate all cache related to a specific user"""
    pattern = f"user:{user_id}:*"
    return cache_manager.delete_pattern(pattern)


def invalidate_school_cache(school_id: int):
    """Invalidate all cache related to a specific school"""
    return SchoolCacheManager.invalidate_school_cache(school_id)


def invalidate_academic_year_cache(academic_year_id: int):
    """Invalidate cache related to academic year"""
    pattern = f"*:academic_year:{academic_year_id}:*"
    return cache_manager.delete_pattern(pattern)


def warm_up_cache():
    """
    Warm up cache with frequently accessed data
    """
    try:
        from core.models import School
        from students.models import Student
        from academics.models import Grade, Subject
        
        # Warm up school data
        schools = School.objects.filter(is_active=True)
        for school in schools:
            SchoolCacheManager.cache_school_data(
                school.id, 
                'basic_info', 
                {
                    'name': school.name,
                    'code': school.code,
                    'student_capacity': school.student_capacity
                }
            )
        
        # Warm up frequently accessed counts
        for school in schools:
            student_count = Student.objects.filter(school=school, status='active').count()
            SchoolCacheManager.cache_school_data(
                school.id,
                'student_count',
                student_count
            )
        
        logger.info("Cache warm-up completed successfully")
        
    except Exception as e:
        logger.error(f"Cache warm-up error: {e}")


# Export main components
__all__ = [
    'CacheInvalidationManager',
    'SmartCacheInvalidation',
    'invalidation_manager',
    'smart_invalidation',
    'invalidate_user_cache',
    'invalidate_school_cache',
    'invalidate_academic_year_cache',
    'warm_up_cache'
]