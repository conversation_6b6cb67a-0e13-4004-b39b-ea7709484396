"""
Calendar models for storing calendar preferences and events.
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone

User = get_user_model()


class CalendarPreference(models.Model):
    """
    User calendar preferences.
    """
    CALENDAR_CHOICES = [
        ('gregorian', _('Gregorian')),
        ('hijri', _('Hijri')),
    ]
    
    WEEKDAY_CHOICES = [
        (0, _('Monday')),
        (1, _('Tuesday')),
        (2, _('Wednesday')),
        (3, _('Thursday')),
        (4, _('Friday')),
        (5, _('Saturday')),
        (6, _('Sunday')),
    ]
    
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='calendar_preference',
        verbose_name=_('User')
    )
    
    primary_calendar = models.Char<PERSON>ield(
        max_length=20,
        choices=CALENDAR_CHOICES,
        default='gregorian',
        verbose_name=_('Primary Calendar'),
        help_text=_('Default calendar system to use')
    )
    
    show_dual_dates = models.BooleanField(
        default=True,
        verbose_name=_('Show Dual Dates'),
        help_text=_('Show both Gregorian and Hijri dates')
    )
    
    hijri_adjustment = models.IntegerField(
        default=0,
        validators=[MinValueValidator(-2), MaxValueValidator(2)],
        verbose_name=_('Hijri Adjustment'),
        help_text=_('Days to adjust Hijri calculations (-2 to +2)')
    )
    
    first_day_of_week = models.IntegerField(
        choices=WEEKDAY_CHOICES,
        default=6,  # Sunday
        verbose_name=_('First Day of Week'),
        help_text=_('First day of the week in calendar display')
    )
    
    weekend_days = models.JSONField(
        default=list,
        verbose_name=_('Weekend Days'),
        help_text=_('List of weekend day numbers (0=Monday, 6=Sunday)')
    )
    
    date_format = models.CharField(
        max_length=50,
        default='%d/%m/%Y',
        verbose_name=_('Date Format'),
        help_text=_('Preferred date format')
    )
    
    time_format = models.CharField(
        max_length=20,
        default='%H:%M',
        verbose_name=_('Time Format'),
        help_text=_('Preferred time format (12h or 24h)')
    )
    
    timezone = models.CharField(
        max_length=50,
        default='UTC',
        verbose_name=_('Timezone'),
        help_text=_('User timezone')
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = _('Calendar Preference')
        verbose_name_plural = _('Calendar Preferences')
    
    def __str__(self):
        return f"{self.user.username} - {self.get_primary_calendar_display()}"
    
    def get_weekend_days_display(self):
        """Get display names for weekend days."""
        if not self.weekend_days:
            return []
        
        day_names = dict(self.WEEKDAY_CHOICES)
        return [day_names.get(day, str(day)) for day in self.weekend_days]
    
    def is_weekend(self, date_obj):
        """Check if a date falls on weekend."""
        return date_obj.weekday() in (self.weekend_days or [])


class CalendarEvent(models.Model):
    """
    Calendar events that can be displayed in different calendar systems.
    """
    EVENT_TYPES = [
        ('holiday', _('Holiday')),
        ('academic', _('Academic')),
        ('administrative', _('Administrative')),
        ('religious', _('Religious')),
        ('national', _('National')),
        ('school', _('School Event')),
        ('exam', _('Examination')),
        ('vacation', _('Vacation')),
        ('meeting', _('Meeting')),
        ('other', _('Other')),
    ]
    
    CALENDAR_TYPES = [
        ('gregorian', _('Gregorian')),
        ('hijri', _('Hijri')),
        ('both', _('Both')),
    ]
    
    RECURRENCE_TYPES = [
        ('none', _('No Recurrence')),
        ('daily', _('Daily')),
        ('weekly', _('Weekly')),
        ('monthly', _('Monthly')),
        ('yearly', _('Yearly')),
        ('custom', _('Custom')),
    ]
    
    title = models.CharField(
        max_length=200,
        verbose_name=_('Title')
    )
    
    title_ar = models.CharField(
        max_length=200,
        blank=True,
        verbose_name=_('Title (Arabic)')
    )
    
    description = models.TextField(
        blank=True,
        verbose_name=_('Description')
    )
    
    description_ar = models.TextField(
        blank=True,
        verbose_name=_('Description (Arabic)')
    )
    
    event_type = models.CharField(
        max_length=20,
        choices=EVENT_TYPES,
        default='other',
        verbose_name=_('Event Type')
    )
    
    calendar_type = models.CharField(
        max_length=20,
        choices=CALENDAR_TYPES,
        default='gregorian',
        verbose_name=_('Calendar Type'),
        help_text=_('Which calendar system this event belongs to')
    )
    
    start_date = models.DateField(
        verbose_name=_('Start Date')
    )
    
    end_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_('End Date')
    )
    
    start_time = models.TimeField(
        blank=True,
        null=True,
        verbose_name=_('Start Time')
    )
    
    end_time = models.TimeField(
        blank=True,
        null=True,
        verbose_name=_('End Time')
    )
    
    is_all_day = models.BooleanField(
        default=True,
        verbose_name=_('All Day Event')
    )
    
    # Hijri date fields (for Hijri events)
    hijri_year = models.IntegerField(
        blank=True,
        null=True,
        verbose_name=_('Hijri Year')
    )
    
    hijri_month = models.IntegerField(
        blank=True,
        null=True,
        validators=[MinValueValidator(1), MaxValueValidator(12)],
        verbose_name=_('Hijri Month')
    )
    
    hijri_day = models.IntegerField(
        blank=True,
        null=True,
        validators=[MinValueValidator(1), MaxValueValidator(30)],
        verbose_name=_('Hijri Day')
    )
    
    # Recurrence settings
    recurrence_type = models.CharField(
        max_length=20,
        choices=RECURRENCE_TYPES,
        default='none',
        verbose_name=_('Recurrence Type')
    )
    
    recurrence_interval = models.IntegerField(
        default=1,
        validators=[MinValueValidator(1)],
        verbose_name=_('Recurrence Interval'),
        help_text=_('Repeat every X days/weeks/months/years')
    )
    
    recurrence_end_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_('Recurrence End Date')
    )
    
    # Display settings
    color = models.CharField(
        max_length=7,
        default='#007bff',
        verbose_name=_('Color'),
        help_text=_('Hex color code for event display')
    )
    
    is_public = models.BooleanField(
        default=True,
        verbose_name=_('Public Event'),
        help_text=_('Whether this event is visible to all users')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Active')
    )
    
    # Metadata
    school = models.ForeignKey(
        'core.School',
        on_delete=models.CASCADE,
        related_name='calendar_events',
        verbose_name=_('School')
    )
    
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='created_calendar_events',
        verbose_name=_('Created By')
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = _('Calendar Event')
        verbose_name_plural = _('Calendar Events')
        ordering = ['start_date', 'start_time']
        indexes = [
            models.Index(fields=['start_date', 'end_date']),
            models.Index(fields=['calendar_type', 'event_type']),
            models.Index(fields=['school', 'is_active']),
        ]
    
    def __str__(self):
        return f"{self.title} - {self.start_date}"
    
    def clean(self):
        """Validate the event data."""
        from django.core.exceptions import ValidationError
        
        if self.end_date and self.start_date > self.end_date:
            raise ValidationError(_('End date must be after start date.'))
        
        if not self.is_all_day and self.start_time and self.end_time:
            if self.start_time >= self.end_time:
                raise ValidationError(_('End time must be after start time.'))
        
        if self.calendar_type == 'hijri' and not all([self.hijri_year, self.hijri_month, self.hijri_day]):
            raise ValidationError(_('Hijri date fields are required for Hijri events.'))
    
    def get_display_title(self, language='en'):
        """Get event title in specified language."""
        if language == 'ar' and self.title_ar:
            return self.title_ar
        return self.title
    
    def get_display_description(self, language='en'):
        """Get event description in specified language."""
        if language == 'ar' and self.description_ar:
            return self.description_ar
        return self.description
    
    def get_duration_days(self):
        """Get event duration in days."""
        if self.end_date:
            return (self.end_date - self.start_date).days + 1
        return 1
    
    def is_multi_day(self):
        """Check if event spans multiple days."""
        return self.end_date and self.end_date != self.start_date
    
    def get_hijri_date_display(self, language='ar'):
        """Get Hijri date display."""
        if not all([self.hijri_year, self.hijri_month, self.hijri_day]):
            return ''
        
        from core.calendar.calendar_utils import HijriCalendar
        
        # Create a date object for formatting
        gregorian_date = HijriCalendar.hijri_to_gregorian(
            self.hijri_year, self.hijri_month, self.hijri_day
        )
        
        return HijriCalendar.format_hijri_date(gregorian_date, language, include_day=False)
    
    def get_next_occurrence(self, after_date=None):
        """Get next occurrence of recurring event."""
        if self.recurrence_type == 'none':
            return None
        
        if after_date is None:
            after_date = timezone.now().date()
        
        if after_date < self.start_date:
            return self.start_date
        
        if self.recurrence_end_date and after_date > self.recurrence_end_date:
            return None
        
        from datetime import timedelta
        from dateutil.relativedelta import relativedelta
        
        current_date = self.start_date
        
        while current_date <= after_date:
            if self.recurrence_type == 'daily':
                current_date += timedelta(days=self.recurrence_interval)
            elif self.recurrence_type == 'weekly':
                current_date += timedelta(weeks=self.recurrence_interval)
            elif self.recurrence_type == 'monthly':
                current_date += relativedelta(months=self.recurrence_interval)
            elif self.recurrence_type == 'yearly':
                current_date += relativedelta(years=self.recurrence_interval)
            else:
                break
        
        if self.recurrence_end_date and current_date > self.recurrence_end_date:
            return None
        
        return current_date
    
    def get_occurrences_in_range(self, start_date, end_date):
        """Get all occurrences of this event in the given date range."""
        occurrences = []
        
        # Add the original event if it falls in range
        if start_date <= self.start_date <= end_date:
            occurrences.append(self.start_date)
        
        # Add recurring occurrences
        if self.recurrence_type != 'none':
            current_date = self.get_next_occurrence(start_date)
            
            while current_date and current_date <= end_date:
                occurrences.append(current_date)
                current_date = self.get_next_occurrence(current_date)
        
        return sorted(occurrences)


class CalendarEventAttendee(models.Model):
    """
    Event attendees for calendar events.
    """
    RESPONSE_CHOICES = [
        ('pending', _('Pending')),
        ('accepted', _('Accepted')),
        ('declined', _('Declined')),
        ('tentative', _('Tentative')),
    ]
    
    event = models.ForeignKey(
        CalendarEvent,
        on_delete=models.CASCADE,
        related_name='attendees',
        verbose_name=_('Event')
    )
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='calendar_event_attendances',
        verbose_name=_('User')
    )
    
    response = models.CharField(
        max_length=20,
        choices=RESPONSE_CHOICES,
        default='pending',
        verbose_name=_('Response')
    )
    
    response_date = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('Response Date')
    )
    
    notes = models.TextField(
        blank=True,
        verbose_name=_('Notes')
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = _('Calendar Event Attendee')
        verbose_name_plural = _('Calendar Event Attendees')
        unique_together = ['event', 'user']
    
    def __str__(self):
        return f"{self.user.username} - {self.event.title} ({self.get_response_display()})"


class CalendarEventReminder(models.Model):
    """
    Reminders for calendar events.
    """
    REMINDER_TYPES = [
        ('email', _('Email')),
        ('sms', _('SMS')),
        ('notification', _('In-App Notification')),
        ('popup', _('Popup')),
    ]
    
    TIME_UNITS = [
        ('minutes', _('Minutes')),
        ('hours', _('Hours')),
        ('days', _('Days')),
        ('weeks', _('Weeks')),
    ]
    
    event = models.ForeignKey(
        CalendarEvent,
        on_delete=models.CASCADE,
        related_name='reminders',
        verbose_name=_('Event')
    )
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='calendar_reminders',
        verbose_name=_('User')
    )
    
    reminder_type = models.CharField(
        max_length=20,
        choices=REMINDER_TYPES,
        default='notification',
        verbose_name=_('Reminder Type')
    )
    
    time_before = models.IntegerField(
        default=15,
        validators=[MinValueValidator(1)],
        verbose_name=_('Time Before'),
        help_text=_('How long before the event to send reminder')
    )
    
    time_unit = models.CharField(
        max_length=10,
        choices=TIME_UNITS,
        default='minutes',
        verbose_name=_('Time Unit')
    )
    
    is_sent = models.BooleanField(
        default=False,
        verbose_name=_('Sent')
    )
    
    sent_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('Sent At')
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = _('Calendar Event Reminder')
        verbose_name_plural = _('Calendar Event Reminders')
        unique_together = ['event', 'user', 'reminder_type']
    
    def __str__(self):
        return f"{self.user.username} - {self.event.title} ({self.time_before} {self.time_unit})"
    
    def get_reminder_datetime(self):
        """Calculate when the reminder should be sent."""
        from datetime import timedelta
        
        event_datetime = timezone.make_aware(
            timezone.datetime.combine(self.event.start_date, self.event.start_time or timezone.datetime.min.time())
        )
        
        if self.time_unit == 'minutes':
            delta = timedelta(minutes=self.time_before)
        elif self.time_unit == 'hours':
            delta = timedelta(hours=self.time_before)
        elif self.time_unit == 'days':
            delta = timedelta(days=self.time_before)
        elif self.time_unit == 'weeks':
            delta = timedelta(weeks=self.time_before)
        else:
            delta = timedelta(minutes=self.time_before)
        
        return event_datetime - delta