"""
Calendar utilities for supporting multiple calendar systems.
Provides support for Gregorian and Hijri (Islamic) calendars.
"""

from datetime import datetime, date, timedelta
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
import math


class HijriCalendar:
    """
    Hijri (Islamic) calendar utilities.
    Based on the Umm al-Qura calendar used in Saudi Arabia.
    """
    
    # Hijri month names in Arabic
    HIJRI_MONTHS_AR = [
        'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
        'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
    ]
    
    # Hijri month names in English
    HIJRI_MONTHS_EN = [
        'Muharram', 'Safar', 'Rabi\' al-awwal', 'Rabi\' al-thani', 
        '<PERSON><PERSON> al-awwal', '<PERSON><PERSON> al-than<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>\'ban', 
        '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON> <PERSON>-<PERSON>\'dah', '<PERSON><PERSON> al-Hijjah'
    ]
    
    # Hijri day names in Arabic
    HIJRI_DAYS_AR = [
        'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'
    ]
    
    # Hijri day names in English
    HIJRI_DAYS_EN = [
        'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
    ]
    
    @classmethod
    def gregorian_to_hijri(cls, gregorian_date):
        """
        Convert Gregorian date to Hijri date.
        Returns tuple (year, month, day).
        """
        if isinstance(gregorian_date, datetime):
            gregorian_date = gregorian_date.date()
        
        # Julian day number
        if gregorian_date.month <= 2:
            year = gregorian_date.year - 1
            month = gregorian_date.month + 12
        else:
            year = gregorian_date.year
            month = gregorian_date.month
        
        a = int(year / 100)
        b = 2 - a + int(a / 4)
        
        if year < 1583 or (year == 1582 and month < 10) or (year == 1582 and month == 10 and gregorian_date.day < 15):
            b = 0
        
        jd = int(365.25 * (year + 4716)) + int(30.6001 * (month + 1)) + gregorian_date.day + b - 1524
        
        # Convert Julian day to Hijri
        jd = jd - 1948440 + 10632
        n = int((jd - 1) / 10631)
        jd = jd - 10631 * n + 354
        j = (int((10985 - jd) / 5316)) * (int((50 * jd) / 17719)) + (int(jd / 5670)) * (int((43 * jd) / 15238))
        jd = jd - (int((30 - j) / 15)) * (int((17719 * j) / 50)) - (int(j / 16)) * (int((15238 * j) / 43)) + 29
        month = int((24 * jd) / 709)
        day = jd - int((709 * month) / 24)
        year = 30 * n + j - 30
        
        return (int(year), int(month), int(day))
    
    @classmethod
    def hijri_to_gregorian(cls, hijri_year, hijri_month, hijri_day):
        """
        Convert Hijri date to Gregorian date.
        Returns datetime.date object.
        """
        # Calculate Julian day number
        jd = int((11 * hijri_year + 3) / 30) + 354 * hijri_year + 30 * hijri_month - int((hijri_month - 1) / 2) + hijri_day + 1948440 - 385
        
        if jd > 2299161:
            l = jd + 68569
            n = int((4 * l) / 146097)
            l = l - int((146097 * n + 3) / 4)
            i = int((4000 * (l + 1)) / 1461001)
            l = l - int((1461 * i) / 4) + 31
            j = int((80 * l) / 2447)
            day = l - int((2447 * j) / 80)
            l = int(j / 11)
            month = j + 2 - 12 * l
            year = 100 * (n - 49) + i + l
        else:
            j = jd + 1402
            k = int((j - 1) / 1461)
            l = j - 1461 * k
            n = int((l - 1) / 365) - int(l / 1461)
            i = l - 365 * n + 30
            j = int((80 * i) / 2447)
            day = i - int((2447 * j) / 80)
            i = int(j / 11)
            month = j + 2 - 12 * i
            year = 4 * k + n + i - 4716
        
        return date(int(year), int(month), int(day))
    
    @classmethod
    def get_hijri_month_name(cls, month, language='ar'):
        """Get Hijri month name in specified language."""
        if language == 'ar':
            return cls.HIJRI_MONTHS_AR[month - 1] if 1 <= month <= 12 else ''
        else:
            return cls.HIJRI_MONTHS_EN[month - 1] if 1 <= month <= 12 else ''
    
    @classmethod
    def get_hijri_day_name(cls, date_obj, language='ar'):
        """Get Hijri day name for the given date."""
        if language == 'ar':
            return cls.HIJRI_DAYS_AR[date_obj.weekday()]
        else:
            return cls.HIJRI_DAYS_EN[date_obj.weekday()]
    
    @classmethod
    def format_hijri_date(cls, gregorian_date, language='ar', include_day=True):
        """Format date in Hijri calendar."""
        hijri_year, hijri_month, hijri_day = cls.gregorian_to_hijri(gregorian_date)
        month_name = cls.get_hijri_month_name(hijri_month, language)
        
        if language == 'ar':
            from core.localization import NumberFormatter
            day_str = NumberFormatter.to_arabic_indic(str(hijri_day))
            year_str = NumberFormatter.to_arabic_indic(str(hijri_year))
            
            if include_day:
                day_name = cls.get_hijri_day_name(gregorian_date, language)
                return f"{day_name} {day_str} {month_name} {year_str} هـ"
            else:
                return f"{day_str} {month_name} {year_str} هـ"
        else:
            if include_day:
                day_name = cls.get_hijri_day_name(gregorian_date, language)
                return f"{day_name}, {hijri_day} {month_name} {hijri_year} AH"
            else:
                return f"{hijri_day} {month_name} {hijri_year} AH"
    
    @classmethod
    def is_hijri_leap_year(cls, hijri_year):
        """Check if a Hijri year is a leap year."""
        return (hijri_year * 11 + 14) % 30 < 11
    
    @classmethod
    def get_hijri_days_in_month(cls, hijri_year, hijri_month):
        """Get number of days in a Hijri month."""
        if hijri_month in [1, 3, 5, 7, 9, 11]:
            return 30
        elif hijri_month in [2, 4, 6, 8, 10]:
            return 29
        else:  # month 12
            return 30 if cls.is_hijri_leap_year(hijri_year) else 29


class CalendarConverter:
    """
    Utility class for calendar conversions and dual calendar display.
    """
    
    @staticmethod
    def get_dual_date_display(gregorian_date, language='ar'):
        """Get dual calendar display (Gregorian and Hijri)."""
        if isinstance(gregorian_date, datetime):
            gregorian_date = gregorian_date.date()
        
        # Gregorian date
        if language == 'ar':
            from core.localization import DateFormatter
            gregorian_str = DateFormatter.format_date_arabic(gregorian_date)
        else:
            gregorian_str = gregorian_date.strftime('%B %d, %Y')
        
        # Hijri date
        hijri_str = HijriCalendar.format_hijri_date(gregorian_date, language, include_day=False)
        
        if language == 'ar':
            return f"{gregorian_str} م / {hijri_str}"
        else:
            return f"{gregorian_str} / {hijri_str}"
    
    @staticmethod
    def get_calendar_events_for_date(date_obj, calendar_type='both'):
        """Get calendar events for a specific date."""
        events = []
        
        if calendar_type in ['gregorian', 'both']:
            # Add Gregorian calendar events
            gregorian_events = CalendarConverter._get_gregorian_events(date_obj)
            events.extend(gregorian_events)
        
        if calendar_type in ['hijri', 'both']:
            # Add Hijri calendar events
            hijri_events = CalendarConverter._get_hijri_events(date_obj)
            events.extend(hijri_events)
        
        return events
    
    @staticmethod
    def _get_gregorian_events(date_obj):
        """Get Gregorian calendar events."""
        events = []
        month_day = (date_obj.month, date_obj.day)
        
        # Common Gregorian holidays
        gregorian_holidays = {
            (1, 1): {'name_en': 'New Year\'s Day', 'name_ar': 'رأس السنة الميلادية'},
            (12, 25): {'name_en': 'Christmas Day', 'name_ar': 'عيد الميلاد المجيد'},
            (9, 23): {'name_en': 'Saudi National Day', 'name_ar': 'اليوم الوطني السعودي'},
        }
        
        if month_day in gregorian_holidays:
            events.append({
                'type': 'gregorian',
                'name': gregorian_holidays[month_day],
                'date': date_obj
            })
        
        return events
    
    @staticmethod
    def _get_hijri_events(date_obj):
        """Get Hijri calendar events."""
        events = []
        hijri_year, hijri_month, hijri_day = HijriCalendar.gregorian_to_hijri(date_obj)
        month_day = (hijri_month, hijri_day)
        
        # Common Islamic holidays
        islamic_holidays = {
            (1, 1): {'name_en': 'Islamic New Year', 'name_ar': 'رأس السنة الهجرية'},
            (1, 10): {'name_en': 'Day of Ashura', 'name_ar': 'يوم عاشوراء'},
            (3, 12): {'name_en': 'Mawlid al-Nabi', 'name_ar': 'المولد النبوي الشريف'},
            (7, 27): {'name_en': 'Isra and Mi\'raj', 'name_ar': 'الإسراء والمعراج'},
            (9, 1): {'name_en': 'First Day of Ramadan', 'name_ar': 'أول أيام رمضان'},
            (10, 1): {'name_en': 'Eid al-Fitr', 'name_ar': 'عيد الفطر المبارك'},
            (12, 10): {'name_en': 'Eid al-Adha', 'name_ar': 'عيد الأضحى المبارك'},
        }
        
        if month_day in islamic_holidays:
            events.append({
                'type': 'hijri',
                'name': islamic_holidays[month_day],
                'date': date_obj,
                'hijri_date': (hijri_year, hijri_month, hijri_day)
            })
        
        return events


class CalendarPreferences:
    """
    User calendar preferences management.
    """
    
    DEFAULT_PREFERENCES = {
        'primary_calendar': 'gregorian',  # 'gregorian' or 'hijri'
        'show_dual_dates': True,
        'hijri_adjustment': 0,  # Days to adjust Hijri calculations
        'weekend_days': [5, 6],  # Friday and Saturday (0=Monday, 6=Sunday)
        'first_day_of_week': 6,  # Sunday (0=Monday, 6=Sunday)
    }
    
    @classmethod
    def get_user_preferences(cls, user):
        """Get calendar preferences for a user."""
        if hasattr(user, 'calendar_preferences'):
            prefs = user.calendar_preferences
            return {
                'primary_calendar': prefs.get('primary_calendar', cls.DEFAULT_PREFERENCES['primary_calendar']),
                'show_dual_dates': prefs.get('show_dual_dates', cls.DEFAULT_PREFERENCES['show_dual_dates']),
                'hijri_adjustment': prefs.get('hijri_adjustment', cls.DEFAULT_PREFERENCES['hijri_adjustment']),
                'weekend_days': prefs.get('weekend_days', cls.DEFAULT_PREFERENCES['weekend_days']),
                'first_day_of_week': prefs.get('first_day_of_week', cls.DEFAULT_PREFERENCES['first_day_of_week']),
            }
        return cls.DEFAULT_PREFERENCES.copy()
    
    @classmethod
    def set_user_preferences(cls, user, preferences):
        """Set calendar preferences for a user."""
        if not hasattr(user, 'calendar_preferences'):
            user.calendar_preferences = {}
        
        user.calendar_preferences.update(preferences)
        user.save()
    
    @classmethod
    def get_formatted_date(cls, date_obj, user, language='ar'):
        """Get formatted date according to user preferences."""
        preferences = cls.get_user_preferences(user)
        
        if preferences['primary_calendar'] == 'hijri':
            if preferences['show_dual_dates']:
                return CalendarConverter.get_dual_date_display(date_obj, language)
            else:
                return HijriCalendar.format_hijri_date(date_obj, language)
        else:
            if preferences['show_dual_dates']:
                return CalendarConverter.get_dual_date_display(date_obj, language)
            else:
                if language == 'ar':
                    from core.localization import DateFormatter
                    return DateFormatter.format_date_arabic(date_obj)
                else:
                    return date_obj.strftime('%B %d, %Y')


def calendar_context(request):
    """
    Context processor to provide calendar-related data to templates.
    """
    context = {}
    
    # Current date information
    today = timezone.now().date()
    context['today'] = today
    context['current_hijri'] = HijriCalendar.gregorian_to_hijri(today)
    
    # Calendar preferences for authenticated users
    if request.user.is_authenticated:
        preferences = CalendarPreferences.get_user_preferences(request.user)
        context['calendar_preferences'] = preferences
        
        # Formatted dates according to user preferences
        context['today_formatted'] = CalendarPreferences.get_formatted_date(
            today, request.user, 
            language=getattr(request, 'LANGUAGE_CODE', 'ar')
        )
        
        # Dual date display
        context['today_dual'] = CalendarConverter.get_dual_date_display(
            today, 
            language=getattr(request, 'LANGUAGE_CODE', 'ar')
        )
    else:
        # Default preferences for anonymous users
        context['calendar_preferences'] = CalendarPreferences.DEFAULT_PREFERENCES
        context['today_formatted'] = CalendarConverter.get_dual_date_display(today, 'ar')
        context['today_dual'] = context['today_formatted']
    
    # Calendar events for today
    context['today_events'] = CalendarConverter.get_calendar_events_for_date(today)
    
    # Weekend information
    context['weekend_days'] = context['calendar_preferences']['weekend_days']
    context['is_weekend'] = today.weekday() in context['weekend_days']
    
    # Hijri month and year names
    hijri_year, hijri_month, hijri_day = context['current_hijri']
    context['current_hijri_month_name'] = HijriCalendar.get_hijri_month_name(
        hijri_month, 
        language=getattr(request, 'LANGUAGE_CODE', 'ar')
    )
    
    return context


class CalendarWidget:
    """
    Calendar widget for displaying dates in multiple calendar systems.
    """
    
    @staticmethod
    def generate_month_calendar(year, month, calendar_type='gregorian', language='ar'):
        """Generate calendar data for a specific month."""
        if calendar_type == 'hijri':
            return CalendarWidget._generate_hijri_month(year, month, language)
        else:
            return CalendarWidget._generate_gregorian_month(year, month, language)
    
    @staticmethod
    def _generate_gregorian_month(year, month, language='ar'):
        """Generate Gregorian month calendar."""
        from calendar import monthcalendar, month_name
        
        # Get calendar matrix
        cal = monthcalendar(year, month)
        
        # Month name
        if language == 'ar':
            from core.localization import DateFormatter
            month_names = DateFormatter.ARABIC_MONTHS
            month_name_display = month_names[month - 1]
        else:
            month_name_display = month_name[month]
        
        # Year display
        if language == 'ar':
            from core.localization import NumberFormatter
            year_display = NumberFormatter.to_arabic_indic(str(year))
        else:
            year_display = str(year)
        
        return {
            'type': 'gregorian',
            'year': year,
            'month': month,
            'year_display': year_display,
            'month_name': month_name_display,
            'calendar_matrix': cal,
            'language': language
        }
    
    @staticmethod
    def _generate_hijri_month(hijri_year, hijri_month, language='ar'):
        """Generate Hijri month calendar."""
        # Get first day of Hijri month in Gregorian
        first_gregorian = HijriCalendar.hijri_to_gregorian(hijri_year, hijri_month, 1)
        
        # Get number of days in Hijri month
        days_in_month = HijriCalendar.get_hijri_days_in_month(hijri_year, hijri_month)
        
        # Build calendar matrix
        cal = []
        current_week = [0] * first_gregorian.weekday()  # Fill beginning with zeros
        
        for day in range(1, days_in_month + 1):
            current_week.append(day)
            if len(current_week) == 7:
                cal.append(current_week)
                current_week = []
        
        # Fill last week if needed
        if current_week:
            while len(current_week) < 7:
                current_week.append(0)
            cal.append(current_week)
        
        # Month name
        month_name_display = HijriCalendar.get_hijri_month_name(hijri_month, language)
        
        # Year display
        if language == 'ar':
            from core.localization import NumberFormatter
            year_display = NumberFormatter.to_arabic_indic(str(hijri_year)) + ' هـ'
        else:
            year_display = f"{hijri_year} AH"
        
        return {
            'type': 'hijri',
            'year': hijri_year,
            'month': hijri_month,
            'year_display': year_display,
            'month_name': month_name_display,
            'calendar_matrix': cal,
            'language': language
        }