from django.conf import settings
from django.utils import timezone
from django.db.models import Count, Q
from django.core.cache import cache
from students.models import Student, Class, Grade
from hr.models import Employee, Department
from academics.models import Subject, Teacher
from core.models import AcademicYear, School
from core.localization.localization import get_localization_context
from core.school_utils import get_user_schools, get_current_school


def school_context(request):
    """
    Context processor to provide common school data across all templates with advanced caching and performance optimization
    """
    context = {}
    
    # School selection context with multi-level caching
    if request.user.is_authenticated:
        user_id = request.user.id
        
        # Create hierarchical cache keys for better cache management
        user_context_cache_key = f"user_school_context_{user_id}_{request.user.is_superuser}"
        cached_context = cache.get(user_context_cache_key)
        
        if cached_context and isinstance(cached_context, dict):
            # Validate cached data freshness
            cache_timestamp = cached_context.get('_cache_timestamp')
            if cache_timestamp and (timezone.now() - cache_timestamp).total_seconds() < 600:  # 10 minutes
                context.update(cached_context)
                # Update access time for LRU-like behavior
                cached_context['_last_access'] = timezone.now()
                cache.set(user_context_cache_key, cached_context, 900)  # Extend TTL
            else:
                # Cache expired, clear it
                cache.delete(user_context_cache_key)
                cached_context = None
        
        if not cached_context:
            # Get current school and available schools with optimized queries
            current_school = get_current_school(request)
            
            # Use cached user schools if available
            user_schools_cache_key = f"user_schools_optimized_{user_id}_{request.user.is_superuser}"
            available_schools = cache.get(user_schools_cache_key)
            
            if available_schools is None:
                available_schools = get_user_schools(request.user)
                # Optimize QuerySet with select_related for better performance
                if hasattr(available_schools, 'select_related'):
                    available_schools = available_schools.select_related()
                cache.set(user_schools_cache_key, available_schools, 900)  # 15 minutes
            
            # Calculate school metrics efficiently
            if hasattr(available_schools, 'count'):
                school_count = available_schools.count()
                has_multiple = school_count > 1
            else:
                school_count = len(available_schools)
                has_multiple = school_count > 1
            
            school_context_data = {
                'current_school': current_school,
                'available_schools': available_schools,
                'has_multiple_schools': has_multiple,
                'school_count': school_count,
                '_cache_timestamp': timezone.now(),
                '_last_access': timezone.now(),
            }
            
            # School basic information from current school or default
            if current_school:
                school_context_data.update({
                    'school_name': current_school.name,
                    'school_name_ar': getattr(current_school, 'name_ar', current_school.name),
                    'school_code': current_school.code,
                    'school_id': current_school.id,
                })
            else:
                school_context_data.update({
                    'school_name': getattr(settings, 'SCHOOL_NAME', 'School ERP System'),
                    'school_name_ar': getattr(settings, 'SCHOOL_NAME_AR', 'نظام إدارة المدرسة'),
                    'school_code': 'DEFAULT',
                    'school_id': None,
                })
            
            context.update(school_context_data)
            
            # Cache for 15 minutes with metadata
            cache.set(user_context_cache_key, school_context_data, 900)
    else:
        # Optimized caching for unauthenticated users
        anon_context_cache_key = "anonymous_school_context_v2"
        cached_anon_context = cache.get(anon_context_cache_key)
        
        if cached_anon_context:
            context.update(cached_anon_context)
        else:
            anon_context_data = {
                'current_school': None,
                'available_schools': School.objects.none(),
                'has_multiple_schools': False,
                'school_count': 0,
                'school_name': getattr(settings, 'SCHOOL_NAME', 'School ERP System'),
                'school_name_ar': getattr(settings, 'SCHOOL_NAME_AR', 'نظام إدارة المدرسة'),
                'school_code': 'DEFAULT',
                'school_id': None,
                '_cache_timestamp': timezone.now(),
            }
            context.update(anon_context_data)
            
            # Cache for 1 hour (longer cache for static data)
            cache.set(anon_context_cache_key, anon_context_data, 3600)
    
    # Cache current year globally
    current_year_cache_key = "current_year"
    current_year = cache.get(current_year_cache_key)
    if current_year is None:
        current_year = timezone.now().year
        # Cache until end of year
        cache.set(current_year_cache_key, current_year, 86400 * 365)
    context['current_year'] = current_year
    
    # Current academic year with optimized caching
    school_id = context.get('current_school').id if context.get('current_school') else 'default'
    academic_year_cache_key = f"current_academic_year_{school_id}"
    current_academic_year = cache.get(academic_year_cache_key)
    
    if current_academic_year is None:
        try:
            if context.get('current_school'):
                current_academic_year = AcademicYear.objects.select_related('school').filter(
                    school=context['current_school'],
                    is_current=True
                ).first()
            else:
                current_academic_year = AcademicYear.objects.select_related('school').filter(
                    is_current=True
                ).first()
            
            # Cache for 2 hours (academic year doesn't change frequently)
            cache.set(academic_year_cache_key, current_academic_year, 7200)
        except Exception as e:
            logger.error(f"Error fetching academic year: {e}")
            current_academic_year = None
    
    context['current_academic_year'] = current_academic_year
    
    # Optimized statistics with batch queries and longer caching
    if request.user.is_authenticated and context.get('current_school'):
        school_id = context['current_school'].id
        stats_cache_key = f'school_stats_optimized_{school_id}'
        stats = cache.get(stats_cache_key)
        
        if not stats:
            try:
                # Use a single query with aggregation for better performance
                from django.db.models import Count, Q
                
                # Batch all counts in optimized queries
                school_filter = Q(school=context['current_school'], is_active=True)
                
                stats = {
                    'total_students': Student.objects.filter(school_filter).count(),
                    'total_teachers': Teacher.objects.filter(school_filter).count(),
                    'total_classes': Class.objects.filter(school_filter).count(),
                    'total_subjects': Subject.objects.filter(school_filter).count(),
                    'total_departments': Department.objects.filter(school_filter).count(),
                }
                
                # Cache for 20 minutes (longer cache for better performance)
                cache.set(stats_cache_key, stats, 1200)
            except Exception as e:
                logger.error(f"Error calculating school statistics: {e}")
                # Fallback stats with caching to prevent repeated errors
                stats = {
                    'total_students': 0,
                    'total_teachers': 0,
                    'total_classes': 0,
                    'total_subjects': 0,
                    'total_departments': 0,
                }
                # Cache error state for 5 minutes to prevent repeated failures
                cache.set(stats_cache_key, stats, 300)
        
        context.update(stats)
    
    return context


def dashboard_context(request):
    """
    Context processor for dashboard-specific data
    """
    context = {}
    
    if request.user.is_authenticated:
        today = timezone.now().date()
        
        # User-specific dashboard data
        if hasattr(request.user, 'student_profile'):
            student = request.user.student_profile
            context['user_student'] = student
            context['user_class'] = student.current_class
            
        elif hasattr(request.user, 'teacher_profile'):
            teacher = request.user.teacher_profile
            context['user_teacher'] = teacher
            context['teacher_classes'] = Class.objects.filter(class_teacher=request.user, is_active=True)
            
        elif hasattr(request.user, 'employee_profile'):
            employee = request.user.employee_profile
            context['user_employee'] = employee
            
        # Recent activities and notifications
        context['recent_students'] = Student.objects.filter(
            is_active=True,
            created_at__gte=today - timezone.timedelta(days=7)
        ).order_by('-created_at')[:5]
        
    return context


def navigation_context(request):
    """
    Context processor for navigation-specific data
    """
    context = {}
    
    if request.user.is_authenticated:
        # User permissions for navigation
        context['can_manage_students'] = request.user.has_perm('students.view_student')
        context['can_manage_hr'] = request.user.has_perm('hr.view_employee')
        context['can_manage_academics'] = request.user.has_perm('academics.view_subject')
        context['can_view_reports'] = request.user.has_perm('reports.view_report')
        context['can_manage_finance'] = request.user.has_perm('finance.view_account')
        
        # Quick stats for navigation badges
        if context['can_manage_students']:
            context['pending_registrations'] = Student.objects.filter(
                is_active=False
            ).count()
            
        if context['can_manage_hr']:
            from hr.models import LeaveRequest
            context['pending_leave_requests'] = LeaveRequest.objects.filter(
                status='pending'
            ).count()
    
    return context


def localization_context(request):
    """
    Context processor for localization data
    """
    return get_localization_context()
