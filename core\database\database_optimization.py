"""
Database Optimization and Query Performance for School ERP System
"""
from typing import Dict, List, Any, Optional, Tuple
from django.db import models, connection
from django.db.models import QuerySet, Q, Prefetch
from django.core.management.base import BaseCommand
from django.apps import apps
import logging
import time
from contextlib import contextmanager

logger = logging.getLogger(__name__)


class QueryOptimizer:
    """
    Query optimization utilities for better database performance
    """
    
    # Common select_related patterns for models
    SELECT_RELATED_PATTERNS = {
        'students.Student': ['parent', 'parent__user', 'school'],
        'students.StudentEnrollment': ['student', 'student__parent', 'grade', 'academic_year'],
        'academics.Class': ['teacher', 'teacher__user', 'grade', 'subject', 'academic_year'],
        'academics.Attendance': ['student', 'class_instance', 'class_instance__teacher'],
        'academics.Grade': ['student', 'class_instance', 'class_instance__subject'],
        'finance.StudentFee': ['student', 'student__parent', 'fee_structure', 'academic_year'],
        'finance.Payment': ['student_fee', 'student_fee__student'],
        'hr.Employee': ['user', 'department', 'school'],
        'hr.Payroll': ['employee', 'employee__user', 'employee__department'],
        'transportation.StudentTransportation': ['student', 'route', 'pickup_stop', 'dropoff_stop'],
        'transportation.GPSTracking': ['vehicle', 'vehicle__school'],
        'library.BookBorrowing': ['book', 'student', 'student__parent'],
        'health.HealthProfile': ['student', 'student__parent']
    }
    
    # Common prefetch_related patterns
    PREFETCH_RELATED_PATTERNS = {
        'students.Student': ['documents', 'enrollments', 'fees', 'transportation_assignments'],
        'academics.Class': ['students', 'attendance_records', 'grades'],
        'academics.Grade': ['students'],
        'finance.FeeStructure': ['student_fees'],
        'transportation.Route': ['student_assignments', 'stops', 'gps_tracking'],
        'library.Book': ['authors', 'borrowings'],
        'hr.Employee': ['payroll_records', 'attendance_records']
    }
    
    @classmethod
    def optimize_queryset(cls, queryset: QuerySet) -> QuerySet:
        """
        Automatically optimize queryset with appropriate select_related and prefetch_related
        """
        model_label = f"{queryset.model._meta.app_label}.{queryset.model._meta.model_name}"
        
        # Apply select_related optimizations
        select_related = cls.SELECT_RELATED_PATTERNS.get(model_label, [])
        if select_related:
            queryset = queryset.select_related(*select_related)
        
        # Apply prefetch_related optimizations
        prefetch_related = cls.PREFETCH_RELATED_PATTERNS.get(model_label, [])
        if prefetch_related:
            queryset = queryset.prefetch_related(*prefetch_related)
        
        return queryset
    
    @classmethod
    def get_optimized_queryset(cls, model_class, **filters) -> QuerySet:
        """
        Get an optimized queryset for a model with filters
        """
        queryset = model_class.objects.filter(**filters)
        return cls.optimize_queryset(queryset)
    
    @classmethod
    def bulk_create_optimized(cls, model_class, objects: List[models.Model], batch_size: int = 1000):
        """
        Optimized bulk create with batching
        """
        created_objects = []
        
        for i in range(0, len(objects), batch_size):
            batch = objects[i:i + batch_size]
            created_batch = model_class.objects.bulk_create(batch, batch_size=batch_size)
            created_objects.extend(created_batch)
        
        return created_objects
    
    @classmethod
    def bulk_update_optimized(cls, objects: List[models.Model], fields: List[str], batch_size: int = 1000):
        """
        Optimized bulk update with batching
        """
        if not objects:
            return 0
        
        model_class = objects[0].__class__
        updated_count = 0
        
        for i in range(0, len(objects), batch_size):
            batch = objects[i:i + batch_size]
            updated_count += model_class.objects.bulk_update(batch, fields, batch_size=batch_size)
        
        return updated_count


class DatabaseIndexManager:
    """
    Manage database indexes for optimal performance
    """
    
    # Recommended indexes for common query patterns
    RECOMMENDED_INDEXES = {
        'students_student': [
            ['school_id', 'status'],
            ['parent_id'],
            ['student_id'],
            ['admission_date'],
            ['school_id', 'admission_date'],
            ['status', 'created_at']
        ],
        'students_studentenrollment': [
            ['student_id', 'academic_year_id'],
            ['grade_id', 'status'],
            ['school_id', 'academic_year_id', 'status']
        ],
        'academics_class': [
            ['teacher_id'],
            ['grade_id', 'academic_year_id'],
            ['school_id', 'is_active'],
            ['subject_id', 'grade_id']
        ],
        'academics_attendance': [
            ['student_id', 'date'],
            ['class_instance_id', 'date'],
            ['school_id', 'date', 'status'],
            ['date', 'status']
        ],
        'academics_grade': [
            ['student_id', 'class_instance_id'],
            ['class_instance_id', 'created_at'],
            ['school_id', 'created_at']
        ],
        'finance_studentfee': [
            ['student_id', 'academic_year_id'],
            ['school_id', 'status'],
            ['due_date', 'status'],
            ['academic_year_id', 'status']
        ],
        'finance_payment': [
            ['student_fee_id'],
            ['school_id', 'payment_date'],
            ['payment_date', 'status'],
            ['reference_number']
        ],
        'hr_employee': [
            ['user_id'],
            ['school_id', 'status'],
            ['department_id', 'status'],
            ['employee_id']
        ],
        'transportation_studenttransportation': [
            ['student_id'],
            ['route_id', 'status'],
            ['school_id', 'status'],
            ['pickup_stop_id', 'dropoff_stop_id']
        ],
        'library_bookborrowing': [
            ['book_id', 'status'],
            ['student_id', 'borrowed_date'],
            ['school_id', 'status'],
            ['due_date', 'status']
        ]
    }
    
    @classmethod
    def get_missing_indexes(cls) -> Dict[str, List[List[str]]]:
        """
        Identify missing indexes that should be created
        """
        missing_indexes = {}
        
        with connection.cursor() as cursor:
            # Get existing indexes
            cursor.execute("""
                SELECT 
                    tablename,
                    indexname,
                    indexdef
                FROM pg_indexes 
                WHERE schemaname = 'public'
                ORDER BY tablename, indexname;
            """)
            
            existing_indexes = {}
            for row in cursor.fetchall():
                table_name = row[0]
                if table_name not in existing_indexes:
                    existing_indexes[table_name] = []
                existing_indexes[table_name].append(row[2])  # indexdef
        
        # Check recommended indexes
        for table_name, recommended in cls.RECOMMENDED_INDEXES.items():
            table_indexes = existing_indexes.get(table_name, [])
            missing = []
            
            for index_fields in recommended:
                index_exists = any(
                    all(field in index_def for field in index_fields)
                    for index_def in table_indexes
                )
                
                if not index_exists:
                    missing.append(index_fields)
            
            if missing:
                missing_indexes[table_name] = missing
        
        return missing_indexes
    
    @classmethod
    def create_missing_indexes(cls, dry_run: bool = True) -> List[str]:
        """
        Create missing database indexes
        """
        missing_indexes = cls.get_missing_indexes()
        sql_statements = []
        
        for table_name, indexes in missing_indexes.items():
            for index_fields in indexes:
                index_name = f"idx_{table_name}_{'_'.join(index_fields)}"
                fields_str = ', '.join(index_fields)
                
                sql = f"CREATE INDEX CONCURRENTLY {index_name} ON {table_name} ({fields_str});"
                sql_statements.append(sql)
                
                if not dry_run:
                    try:
                        with connection.cursor() as cursor:
                            cursor.execute(sql)
                        logger.info(f"Created index: {index_name}")
                    except Exception as e:
                        logger.error(f"Failed to create index {index_name}: {e}")
        
        return sql_statements
    
    @classmethod
    def analyze_query_performance(cls, sql: str, params: List[Any] = None) -> Dict[str, Any]:
        """
        Analyze query performance using EXPLAIN ANALYZE
        """
        with connection.cursor() as cursor:
            explain_sql = f"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {sql}"
            
            start_time = time.time()
            cursor.execute(explain_sql, params)
            execution_time = time.time() - start_time
            
            result = cursor.fetchone()[0][0]  # Get JSON result
            
            return {
                'execution_time': execution_time,
                'planning_time': result.get('Planning Time', 0),
                'execution_time_db': result.get('Execution Time', 0),
                'total_cost': result.get('Plan', {}).get('Total Cost', 0),
                'rows': result.get('Plan', {}).get('Actual Rows', 0),
                'plan': result
            }


class QueryPerformanceMonitor:
    """
    Monitor and log slow queries for optimization
    """
    
    def __init__(self, slow_query_threshold: float = 1.0):
        self.slow_query_threshold = slow_query_threshold
        self.query_stats = {}
    
    @contextmanager
    def monitor_query(self, query_name: str):
        """
        Context manager to monitor query execution time
        """
        start_time = time.time()
        start_queries = len(connection.queries)
        
        try:
            yield
        finally:
            end_time = time.time()
            execution_time = end_time - start_time
            query_count = len(connection.queries) - start_queries
            
            # Log slow queries
            if execution_time > self.slow_query_threshold:
                logger.warning(
                    f"Slow query detected: {query_name} took {execution_time:.3f}s "
                    f"with {query_count} database queries"
                )
            
            # Update statistics
            if query_name not in self.query_stats:
                self.query_stats[query_name] = {
                    'count': 0,
                    'total_time': 0,
                    'max_time': 0,
                    'min_time': float('inf')
                }
            
            stats = self.query_stats[query_name]
            stats['count'] += 1
            stats['total_time'] += execution_time
            stats['max_time'] = max(stats['max_time'], execution_time)
            stats['min_time'] = min(stats['min_time'], execution_time)
    
    def get_performance_report(self) -> Dict[str, Any]:
        """
        Get performance report for all monitored queries
        """
        report = {}
        
        for query_name, stats in self.query_stats.items():
            avg_time = stats['total_time'] / stats['count'] if stats['count'] > 0 else 0
            
            report[query_name] = {
                'count': stats['count'],
                'average_time': avg_time,
                'max_time': stats['max_time'],
                'min_time': stats['min_time'] if stats['min_time'] != float('inf') else 0,
                'total_time': stats['total_time']
            }
        
        return report
    
    def get_slowest_queries(self, limit: int = 10) -> List[Tuple[str, Dict[str, Any]]]:
        """
        Get the slowest queries by average execution time
        """
        queries_with_avg = []
        
        for query_name, stats in self.query_stats.items():
            avg_time = stats['total_time'] / stats['count'] if stats['count'] > 0 else 0
            queries_with_avg.append((query_name, {**stats, 'average_time': avg_time}))
        
        # Sort by average time descending
        queries_with_avg.sort(key=lambda x: x[1]['average_time'], reverse=True)
        
        return queries_with_avg[:limit]


class ConnectionPoolManager:
    """
    Manage database connection pooling for better performance
    """
    
    @staticmethod
    def get_connection_stats() -> Dict[str, Any]:
        """
        Get database connection statistics
        """
        with connection.cursor() as cursor:
            # Get connection count
            cursor.execute("""
                SELECT 
                    count(*) as total_connections,
                    count(*) FILTER (WHERE state = 'active') as active_connections,
                    count(*) FILTER (WHERE state = 'idle') as idle_connections
                FROM pg_stat_activity 
                WHERE datname = current_database();
            """)
            
            conn_stats = cursor.fetchone()
            
            # Get database size
            cursor.execute("""
                SELECT pg_size_pretty(pg_database_size(current_database())) as db_size;
            """)
            
            db_size = cursor.fetchone()[0]
            
            return {
                'total_connections': conn_stats[0],
                'active_connections': conn_stats[1],
                'idle_connections': conn_stats[2],
                'database_size': db_size
            }
    
    @staticmethod
    def get_slow_queries(limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get slow queries from pg_stat_statements (if available)
        """
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT 
                        query,
                        calls,
                        total_time,
                        mean_time,
                        rows
                    FROM pg_stat_statements 
                    ORDER BY mean_time DESC 
                    LIMIT %s;
                """, [limit])
                
                columns = [col[0] for col in cursor.description]
                return [dict(zip(columns, row)) for row in cursor.fetchall()]
                
        except Exception as e:
            logger.warning(f"pg_stat_statements not available: {e}")
            return []
    
    @staticmethod
    def reset_query_stats():
        """
        Reset query statistics (requires pg_stat_statements)
        """
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT pg_stat_statements_reset();")
                logger.info("Query statistics reset successfully")
        except Exception as e:
            logger.warning(f"Could not reset query statistics: {e}")


# Global instances
query_optimizer = QueryOptimizer()
db_index_manager = DatabaseIndexManager()
performance_monitor = QueryPerformanceMonitor()
connection_pool_manager = ConnectionPoolManager()


# Utility functions for common optimizations
def optimize_student_queries(school_id: int) -> QuerySet:
    """Get optimized student queryset for a school"""
    from students.models import Student
    
    return Student.objects.filter(
        school_id=school_id
    ).select_related(
        'parent', 'parent__user', 'school'
    ).prefetch_related(
        'documents', 'enrollments', 'fees'
    )


def optimize_class_queries(academic_year_id: int) -> QuerySet:
    """Get optimized class queryset for an academic year"""
    from academics.models import Class
    
    return Class.objects.filter(
        academic_year_id=academic_year_id
    ).select_related(
        'teacher', 'teacher__user', 'grade', 'subject'
    ).prefetch_related(
        'students', 'attendance_records'
    )


def optimize_financial_queries(school_id: int, academic_year_id: int) -> QuerySet:
    """Get optimized financial queryset"""
    from finance.models import StudentFee
    
    return StudentFee.objects.filter(
        school_id=school_id,
        academic_year_id=academic_year_id
    ).select_related(
        'student', 'student__parent', 'fee_structure'
    ).prefetch_related(
        'payments'
    )


# Export main components
__all__ = [
    'QueryOptimizer',
    'DatabaseIndexManager', 
    'QueryPerformanceMonitor',
    'ConnectionPoolManager',
    'query_optimizer',
    'db_index_manager',
    'performance_monitor',
    'connection_pool_manager',
    'optimize_student_queries',
    'optimize_class_queries',
    'optimize_financial_queries'
]