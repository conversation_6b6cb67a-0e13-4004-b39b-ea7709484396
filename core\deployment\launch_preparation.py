"""
Production Launch Preparation System for School ERP
"""
import logging
import os
import subprocess
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from django.conf import settings
from django.core.management import call_command
from django.core.cache import cache
from django.db import connection
from django.utils import timezone

logger = logging.getLogger(__name__)


class LaunchPreparationManager:
    """
    Manages production launch preparation and go-live procedures
    """
    
    def __init__(self):
        self.checklist_items = self._load_launch_checklist()
        self.preparation_status = {}
    
    def run_pre_launch_checks(self) -> Dict[str, Any]:
        """Run comprehensive pre-launch checks"""
        results = {
            'overall_status': 'pending',
            'checks': {},
            'critical_issues': [],
            'warnings': [],
            'timestamp': timezone.now().isoformat()
        }
        
        # Database checks
        results['checks']['database'] = self._check_database_readiness()
        
        # Security checks
        results['checks']['security'] = self._check_security_configuration()
        
        # Performance checks
        results['checks']['performance'] = self._check_performance_readiness()
        
        # Data integrity checks
        results['checks']['data_integrity'] = self._check_data_integrity()
        
        # System configuration checks
        results['checks']['system_config'] = self._check_system_configuration()
        
        # Backup and recovery checks
        results['checks']['backup_recovery'] = self._check_backup_systems()
        
        # Monitoring checks
        results['checks']['monitoring'] = self._check_monitoring_systems()
        
        # Determine overall status
        critical_failures = [
            check for check in results['checks'].values() 
            if check.get('status') == 'failed' and check.get('critical', False)
        ]
        
        if critical_failures:
            results['overall_status'] = 'not_ready'
            results['critical_issues'] = [check.get('message', 'Unknown issue') for check in critical_failures]
        else:
            warnings = [
                check for check in results['checks'].values() 
                if check.get('status') == 'warning'
            ]
            results['warnings'] = [check.get('message', 'Unknown warning') for check in warnings]
            results['overall_status'] = 'ready' if not warnings else 'ready_with_warnings'
        
        return results    
 
    def _check_database_readiness(self) -> Dict[str, Any]:
        """Check database readiness for production"""
        try:
            # Test database connectivity
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            
            # Check for pending migrations
            from django.core.management.commands.migrate import Command
            migration_executor = Command().migration_executor_class(connection)
            migration_plan = migration_executor.migration_plan(migration_executor.loader.graph.leaf_nodes())
            
            if migration_plan:
                return {
                    'status': 'failed',
                    'critical': True,
                    'message': f'Pending migrations found: {len(migration_plan)} migrations need to be applied'
                }
            
            # Check database size and performance
            with connection.cursor() as cursor:
                cursor.execute("SELECT pg_size_pretty(pg_database_size(current_database()))")
                db_size = cursor.fetchone()[0]
                
                cursor.execute("SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public'")
                table_count = cursor.fetchone()[0]
            
            return {
                'status': 'passed',
                'message': f'Database ready - Size: {db_size}, Tables: {table_count}',
                'details': {
                    'size': db_size,
                    'table_count': table_count,
                    'pending_migrations': 0
                }
            }
            
        except Exception as e:
            return {
                'status': 'failed',
                'critical': True,
                'message': f'Database check failed: {str(e)}'
            }
    
    def _check_security_configuration(self) -> Dict[str, Any]:
        """Check security configuration"""
        issues = []
        warnings = []
        
        # Check DEBUG setting
        if getattr(settings, 'DEBUG', True):
            issues.append('DEBUG is enabled - must be False in production')
        
        # Check SECRET_KEY
        secret_key = getattr(settings, 'SECRET_KEY', '')
        if not secret_key or len(secret_key) < 50:
            issues.append('SECRET_KEY is too short or missing')
        
        # Check ALLOWED_HOSTS
        allowed_hosts = getattr(settings, 'ALLOWED_HOSTS', [])
        if not allowed_hosts or '*' in allowed_hosts:
            issues.append('ALLOWED_HOSTS not properly configured')
        
        # Check HTTPS settings
        if not getattr(settings, 'SECURE_SSL_REDIRECT', False):
            warnings.append('SECURE_SSL_REDIRECT not enabled')
        
        if not getattr(settings, 'SECURE_HSTS_SECONDS', 0):
            warnings.append('HSTS not configured')
        
        if issues:
            return {
                'status': 'failed',
                'critical': True,
                'message': f'Security issues found: {", ".join(issues)}'
            }
        elif warnings:
            return {
                'status': 'warning',
                'message': f'Security warnings: {", ".join(warnings)}'
            }
        else:
            return {
                'status': 'passed',
                'message': 'Security configuration looks good'
            }
    
    def _check_performance_readiness(self) -> Dict[str, Any]:
        """Check performance readiness"""
        try:
            # Check cache configuration
            cache.set('performance_test', 'ok', timeout=60)
            if cache.get('performance_test') != 'ok':
                return {
                    'status': 'failed',
                    'critical': True,
                    'message': 'Cache system not working properly'
                }
            
            # Check database performance
            start_time = timezone.now()
            with connection.cursor() as cursor:
                cursor.execute("SELECT count(*) FROM django_session")
            query_time = (timezone.now() - start_time).total_seconds()
            
            if query_time > 1.0:
                return {
                    'status': 'warning',
                    'message': f'Database query performance slow: {query_time:.2f}s'
                }
            
            return {
                'status': 'passed',
                'message': f'Performance checks passed - Query time: {query_time:.3f}s'
            }
            
        except Exception as e:
            return {
                'status': 'failed',
                'critical': False,
                'message': f'Performance check failed: {str(e)}'
            } 
   
    def _check_data_integrity(self) -> Dict[str, Any]:
        """Check data integrity"""
        try:
            # Check for orphaned records
            from students.models import Student, Parent
            from academics.models import StudentEnrollment
            
            orphaned_students = Student.objects.filter(parent__isnull=True).count()
            orphaned_enrollments = StudentEnrollment.objects.filter(student__isnull=True).count()
            
            issues = []
            if orphaned_students > 0:
                issues.append(f'{orphaned_students} students without parents')
            if orphaned_enrollments > 0:
                issues.append(f'{orphaned_enrollments} orphaned enrollments')
            
            if issues:
                return {
                    'status': 'warning',
                    'message': f'Data integrity issues: {", ".join(issues)}'
                }
            
            return {
                'status': 'passed',
                'message': 'Data integrity checks passed'
            }
            
        except Exception as e:
            return {
                'status': 'failed',
                'critical': False,
                'message': f'Data integrity check failed: {str(e)}'
            }
    
    def _check_system_configuration(self) -> Dict[str, Any]:
        """Check system configuration"""
        config_issues = []
        
        # Check required settings
        required_settings = [
            'DATABASES',
            'CACHES',
            'EMAIL_BACKEND',
            'TIME_ZONE',
            'LANGUAGE_CODE'
        ]
        
        for setting in required_settings:
            if not hasattr(settings, setting):
                config_issues.append(f'Missing setting: {setting}')
        
        # Check media and static files configuration
        if not getattr(settings, 'MEDIA_ROOT', ''):
            config_issues.append('MEDIA_ROOT not configured')
        
        if not getattr(settings, 'STATIC_ROOT', ''):
            config_issues.append('STATIC_ROOT not configured')
        
        if config_issues:
            return {
                'status': 'failed',
                'critical': True,
                'message': f'Configuration issues: {", ".join(config_issues)}'
            }
        
        return {
            'status': 'passed',
            'message': 'System configuration is complete'
        }
    
    def _check_backup_systems(self) -> Dict[str, Any]:
        """Check backup and recovery systems"""
        try:
            # Check if backup directory exists
            backup_dir = getattr(settings, 'BACKUP_DIR', '/backups')
            
            if not os.path.exists(backup_dir):
                return {
                    'status': 'warning',
                    'message': f'Backup directory does not exist: {backup_dir}'
                }
            
            # Check for recent backups
            backup_files = [f for f in os.listdir(backup_dir) if f.endswith('.sql') or f.endswith('.dump')]
            
            if not backup_files:
                return {
                    'status': 'warning',
                    'message': 'No backup files found'
                }
            
            return {
                'status': 'passed',
                'message': f'Backup system ready - {len(backup_files)} backup files found'
            }
            
        except Exception as e:
            return {
                'status': 'failed',
                'critical': False,
                'message': f'Backup system check failed: {str(e)}'
            }
    
    def _check_monitoring_systems(self) -> Dict[str, Any]:
        """Check monitoring systems"""
        try:
            # Check if monitoring is enabled
            monitoring_enabled = getattr(settings, 'PERFORMANCE_MONITORING_ENABLED', False)
            
            if not monitoring_enabled:
                return {
                    'status': 'warning',
                    'message': 'Performance monitoring not enabled'
                }
            
            # Test monitoring endpoints
            from core.monitoring.alerting import check_and_send_alerts
            alert_count = check_and_send_alerts()
            
            return {
                'status': 'passed',
                'message': f'Monitoring systems operational - {alert_count} alerts processed'
            }
            
        except Exception as e:
            return {
                'status': 'failed',
                'critical': False,
                'message': f'Monitoring check failed: {str(e)}'
            } 
   
    def _load_launch_checklist(self) -> List[Dict[str, Any]]:
        """Load production launch checklist"""
        return [
            {
                'category': 'Database',
                'items': [
                    'All migrations applied',
                    'Database indexes optimized',
                    'Connection pooling configured',
                    'Backup system tested',
                    'Data integrity verified'
                ]
            },
            {
                'category': 'Security',
                'items': [
                    'DEBUG disabled',
                    'SECRET_KEY configured',
                    'ALLOWED_HOSTS set',
                    'HTTPS enabled',
                    'Security headers configured',
                    'User permissions reviewed'
                ]
            },
            {
                'category': 'Performance',
                'items': [
                    'Caching system enabled',
                    'Static files optimized',
                    'Database queries optimized',
                    'Load testing completed',
                    'Performance monitoring active'
                ]
            },
            {
                'category': 'Monitoring',
                'items': [
                    'Application monitoring enabled',
                    'Error tracking configured',
                    'Alert rules configured',
                    'Log aggregation setup',
                    'Health check endpoints active'
                ]
            },
            {
                'category': 'Data',
                'items': [
                    'Initial data loaded',
                    'Sample data removed',
                    'Data validation completed',
                    'Data migration tested',
                    'Data backup verified'
                ]
            },
            {
                'category': 'Documentation',
                'items': [
                    'User guides completed',
                    'API documentation updated',
                    'Admin procedures documented',
                    'Troubleshooting guide ready',
                    'Training materials prepared'
                ]
            }
        ]
    
    def execute_go_live_procedure(self) -> Dict[str, Any]:
        """Execute go-live procedure"""
        procedure_steps = [
            'run_final_checks',
            'backup_current_data',
            'deploy_application',
            'run_post_deployment_tests',
            'enable_monitoring',
            'notify_stakeholders'
        ]
        
        results = {
            'status': 'in_progress',
            'steps': {},
            'start_time': timezone.now().isoformat()
        }
        
        for step in procedure_steps:
            try:
                step_result = getattr(self, f'_{step}')()
                results['steps'][step] = step_result
                
                if step_result.get('status') == 'failed':
                    results['status'] = 'failed'
                    results['failed_step'] = step
                    break
                    
            except Exception as e:
                results['steps'][step] = {
                    'status': 'failed',
                    'error': str(e)
                }
                results['status'] = 'failed'
                results['failed_step'] = step
                break
        
        if results['status'] == 'in_progress':
            results['status'] = 'completed'
        
        results['end_time'] = timezone.now().isoformat()
        return results
    
    def _run_final_checks(self) -> Dict[str, Any]:
        """Run final pre-deployment checks"""
        return self.run_pre_launch_checks()
    
    def _backup_current_data(self) -> Dict[str, Any]:
        """Backup current data before deployment"""
        try:
            backup_filename = f"pre_deployment_backup_{timezone.now().strftime('%Y%m%d_%H%M%S')}.sql"
            
            # Run database backup
            call_command('dbbackup', '--output-filename', backup_filename)
            
            return {
                'status': 'passed',
                'message': f'Backup created: {backup_filename}'
            }
            
        except Exception as e:
            return {
                'status': 'failed',
                'message': f'Backup failed: {str(e)}'
            }
    
    def _deploy_application(self) -> Dict[str, Any]:
        """Deploy application"""
        try:
            # Collect static files
            call_command('collectstatic', '--noinput')
            
            # Clear caches
            cache.clear()
            
            return {
                'status': 'passed',
                'message': 'Application deployed successfully'
            }
            
        except Exception as e:
            return {
                'status': 'failed',
                'message': f'Deployment failed: {str(e)}'
            }
    
    def _run_post_deployment_tests(self) -> Dict[str, Any]:
        """Run post-deployment tests"""
        try:
            # Run basic system tests
            test_results = []
            
            # Test database connectivity
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            test_results.append('Database connectivity: OK')
            
            # Test cache
            cache.set('deployment_test', 'ok', timeout=60)
            if cache.get('deployment_test') == 'ok':
                test_results.append('Cache system: OK')
            else:
                test_results.append('Cache system: FAILED')
            
            return {
                'status': 'passed',
                'message': 'Post-deployment tests completed',
                'details': test_results
            }
            
        except Exception as e:
            return {
                'status': 'failed',
                'message': f'Post-deployment tests failed: {str(e)}'
            }
    
    def _enable_monitoring(self) -> Dict[str, Any]:
        """Enable monitoring systems"""
        try:
            # Enable performance monitoring
            from core.performance.monitoring import monitoring_service
            monitoring_service.start_monitoring()
            
            return {
                'status': 'passed',
                'message': 'Monitoring systems enabled'
            }
            
        except Exception as e:
            return {
                'status': 'failed',
                'message': f'Monitoring setup failed: {str(e)}'
            }
    
    def _notify_stakeholders(self) -> Dict[str, Any]:
        """Notify stakeholders of go-live"""
        try:
            # Send go-live notifications
            # This would integrate with the communications system
            
            return {
                'status': 'passed',
                'message': 'Stakeholders notified of go-live'
            }
            
        except Exception as e:
            return {
                'status': 'failed',
                'message': f'Stakeholder notification failed: {str(e)}'
            }