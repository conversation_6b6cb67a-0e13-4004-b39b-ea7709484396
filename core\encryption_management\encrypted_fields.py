"""
Encrypted model fields for Django
"""
from django.db import models
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from .encryption import FieldEncryption, DataMasking
import logging

logger = logging.getLogger(__name__)


class EncryptedFieldMixin:
    """
    Mixin for encrypted fields
    """
    
    def __init__(self, field_type='general', mask_display=False, *args, **kwargs):
        self.field_type = field_type
        self.mask_display = mask_display
        self.encryption = FieldEncryption(field_type)
        super().__init__(*args, **kwargs)
    
    def get_prep_value(self, value):
        """
        Encrypt value before saving to database
        """
        if value is None:
            return value
        
        try:
            # Convert to string if not already
            str_value = str(value)
            if str_value:
                return self.encryption.encrypt(str_value)
            return str_value
        except Exception as e:
            logger.error(f"Failed to encrypt field value: {e}")
            raise ValidationError(_("Failed to encrypt field value"))
    
    def from_db_value(self, value, expression, connection):
        """
        Decrypt value when loading from database
        """
        if value is None:
            return value
        
        try:
            return self.encryption.decrypt(value)
        except Exception as e:
            logger.error(f"Failed to decrypt field value: {e}")
            # Return encrypted value if decryption fails
            return value
    
    def to_python(self, value):
        """
        Convert value to Python type
        """
        if value is None:
            return value
        
        # If value is already decrypted, return as is
        if isinstance(value, str) and not self._is_encrypted(value):
            return value
        
        try:
            return self.encryption.decrypt(value)
        except Exception:
            # If decryption fails, assume it's already decrypted
            return value
    
    def _is_encrypted(self, value):
        """
        Check if value appears to be encrypted
        """
        try:
            # Encrypted values are base64 encoded, so they should be longer
            # and contain only valid base64 characters
            import base64
            base64.b64decode(value)
            return len(value) > 20  # Encrypted values are typically longer
        except Exception:
            return False


class EncryptedCharField(EncryptedFieldMixin, models.CharField):
    """
    Encrypted CharField
    """
    
    def __init__(self, *args, **kwargs):
        # Encrypted fields need more space
        if 'max_length' not in kwargs:
            kwargs['max_length'] = 500
        elif kwargs['max_length'] < 200:
            kwargs['max_length'] = 500
        
        super().__init__(*args, **kwargs)
    
    def formfield(self, **kwargs):
        """
        Return form field for this model field
        """
        if self.mask_display:
            kwargs['widget'] = models.CharField().formfield().widget
            kwargs['widget'].attrs.update({'type': 'password'})
        
        return super().formfield(**kwargs)


class EncryptedTextField(EncryptedFieldMixin, models.TextField):
    """
    Encrypted TextField
    """
    
    def formfield(self, **kwargs):
        """
        Return form field for this model field
        """
        if self.mask_display:
            kwargs['widget'] = models.Textarea(attrs={'class': 'encrypted-field'})
        
        return super().formfield(**kwargs)


class EncryptedEmailField(EncryptedFieldMixin, models.EmailField):
    """
    Encrypted EmailField
    """
    
    def __init__(self, *args, **kwargs):
        if 'max_length' not in kwargs:
            kwargs['max_length'] = 500
        
        super().__init__(*args, **kwargs)
    
    def get_display_value(self):
        """
        Get masked display value
        """
        if self.mask_display and hasattr(self, 'value'):
            return DataMasking.mask_email(self.value)
        return self.value


class EncryptedPhoneField(EncryptedFieldMixin, models.CharField):
    """
    Encrypted phone number field
    """
    
    def __init__(self, *args, **kwargs):
        if 'max_length' not in kwargs:
            kwargs['max_length'] = 500
        
        super().__init__(*args, **kwargs)
    
    def get_display_value(self):
        """
        Get masked display value
        """
        if self.mask_display and hasattr(self, 'value'):
            return DataMasking.mask_phone(self.value)
        return self.value


class EncryptedIDField(EncryptedFieldMixin, models.CharField):
    """
    Encrypted ID number field (SSN, National ID, etc.)
    """
    
    def __init__(self, *args, **kwargs):
        kwargs['field_type'] = 'pii'  # Always use PII encryption
        if 'max_length' not in kwargs:
            kwargs['max_length'] = 500
        
        super().__init__(*args, **kwargs)
    
    def get_display_value(self):
        """
        Get masked display value
        """
        if self.mask_display and hasattr(self, 'value'):
            return DataMasking.mask_id_number(self.value)
        return self.value


class EncryptedFinancialField(EncryptedFieldMixin, models.DecimalField):
    """
    Encrypted financial field
    """
    
    def __init__(self, *args, **kwargs):
        kwargs['field_type'] = 'financial'
        super().__init__(*args, **kwargs)
    
    def get_prep_value(self, value):
        """
        Encrypt decimal value before saving
        """
        if value is None:
            return value
        
        try:
            # Convert decimal to string for encryption
            str_value = str(value)
            return self.encryption.encrypt(str_value)
        except Exception as e:
            logger.error(f"Failed to encrypt financial field: {e}")
            raise ValidationError(_("Failed to encrypt financial value"))
    
    def from_db_value(self, value, expression, connection):
        """
        Decrypt and convert to decimal
        """
        if value is None:
            return value
        
        try:
            decrypted = self.encryption.decrypt(value)
            from decimal import Decimal
            return Decimal(decrypted)
        except Exception as e:
            logger.error(f"Failed to decrypt financial field: {e}")
            return value
    
    def to_python(self, value):
        """
        Convert to decimal
        """
        if value is None:
            return value
        
        from decimal import Decimal
        
        if isinstance(value, Decimal):
            return value
        
        try:
            # Try to decrypt if it's encrypted
            if isinstance(value, str) and self._is_encrypted(value):
                decrypted = self.encryption.decrypt(value)
                return Decimal(decrypted)
            else:
                return Decimal(str(value))
        except Exception:
            try:
                return Decimal(str(value))
            except Exception as e:
                logger.error(f"Failed to convert to decimal: {e}")
                return value
    
    def get_display_value(self):
        """
        Get masked display value
        """
        if self.mask_display and hasattr(self, 'value'):
            return DataMasking.mask_financial_amount(str(self.value))
        return self.value


class EncryptedMedicalField(EncryptedFieldMixin, models.TextField):
    """
    Encrypted medical information field
    """
    
    def __init__(self, *args, **kwargs):
        kwargs['field_type'] = 'medical'
        super().__init__(*args, **kwargs)


class EncryptedJSONField(EncryptedFieldMixin, models.JSONField):
    """
    Encrypted JSON field
    """
    
    def get_prep_value(self, value):
        """
        Encrypt JSON value before saving
        """
        if value is None:
            return value
        
        try:
            import json
            json_str = json.dumps(value)
            return self.encryption.encrypt(json_str)
        except Exception as e:
            logger.error(f"Failed to encrypt JSON field: {e}")
            raise ValidationError(_("Failed to encrypt JSON value"))
    
    def from_db_value(self, value, expression, connection):
        """
        Decrypt and parse JSON
        """
        if value is None:
            return value
        
        try:
            import json
            decrypted = self.encryption.decrypt(value)
            return json.loads(decrypted)
        except Exception as e:
            logger.error(f"Failed to decrypt JSON field: {e}")
            return value
    
    def to_python(self, value):
        """
        Convert to Python object
        """
        if value is None:
            return value
        
        if isinstance(value, (dict, list)):
            return value
        
        try:
            import json
            # Try to decrypt if it's encrypted
            if isinstance(value, str) and self._is_encrypted(value):
                decrypted = self.encryption.decrypt(value)
                return json.loads(decrypted)
            else:
                return json.loads(value)
        except Exception:
            return value


class SearchableEncryptedField(EncryptedCharField):
    """
    Encrypted field that supports limited searching via hashed values
    """
    
    def __init__(self, *args, **kwargs):
        self.create_hash_field = kwargs.pop('create_hash_field', True)
        super().__init__(*args, **kwargs)
    
    def contribute_to_class(self, cls, name, **kwargs):
        """
        Add hash field for searching
        """
        super().contribute_to_class(cls, name, **kwargs)
        
        if self.create_hash_field:
            # Create a hash field for searching
            hash_field_name = f"{name}_hash"
            hash_field = models.CharField(
                max_length=64,
                null=True,
                blank=True,
                editable=False,
                db_index=True
            )
            hash_field.contribute_to_class(cls, hash_field_name)
    
    def pre_save(self, model_instance, add):
        """
        Update hash field before saving
        """
        value = super().pre_save(model_instance, add)
        
        if self.create_hash_field and value:
            # Create hash for searching
            import hashlib
            hash_value = hashlib.sha256(value.encode()).hexdigest()
            hash_field_name = f"{self.name}_hash"
            setattr(model_instance, hash_field_name, hash_value)
        
        return value


class EncryptedModelMixin:
    """
    Mixin for models with encrypted fields
    """
    
    def get_encrypted_fields(self):
        """
        Get list of encrypted fields in this model
        """
        encrypted_fields = []
        for field in self._meta.fields:
            if isinstance(field, EncryptedFieldMixin):
                encrypted_fields.append(field.name)
        return encrypted_fields
    
    def get_masked_data(self):
        """
        Get model data with sensitive fields masked
        """
        data = {}
        for field in self._meta.fields:
            field_name = field.name
            field_value = getattr(self, field_name)
            
            if isinstance(field, EncryptedFieldMixin) and field.mask_display:
                if isinstance(field, EncryptedEmailField):
                    data[field_name] = DataMasking.mask_email(field_value)
                elif isinstance(field, EncryptedPhoneField):
                    data[field_name] = DataMasking.mask_phone(field_value)
                elif isinstance(field, EncryptedIDField):
                    data[field_name] = DataMasking.mask_id_number(field_value)
                elif isinstance(field, EncryptedFinancialField):
                    data[field_name] = DataMasking.mask_financial_amount(str(field_value))
                else:
                    # Generic masking
                    if field_value and len(str(field_value)) > 4:
                        masked = str(field_value)[:2] + '*' * (len(str(field_value)) - 4) + str(field_value)[-2:]
                        data[field_name] = masked
                    else:
                        data[field_name] = '*' * len(str(field_value)) if field_value else field_value
            else:
                data[field_name] = field_value
        
        return data
    
    def rotate_encryption_keys(self, old_key, new_key):
        """
        Rotate encryption keys for this model instance
        """
        from .encryption import KeyManager
        
        for field in self._meta.fields:
            if isinstance(field, EncryptedFieldMixin):
                field_name = field.name
                encrypted_value = getattr(self, field_name)
                
                if encrypted_value:
                    try:
                        # Re-encrypt with new key
                        new_encrypted = KeyManager.rotate_key(
                            old_key, new_key, encrypted_value
                        )
                        setattr(self, field_name, new_encrypted)
                    except Exception as e:
                        logger.error(f"Failed to rotate key for field {field_name}: {e}")
        
        self.save()


# Utility functions for working with encrypted fields
def search_encrypted_field(model_class, field_name, search_value):
    """
    Search for encrypted field using hash
    """
    import hashlib
    
    # Create hash of search value
    search_hash = hashlib.sha256(search_value.encode()).hexdigest()
    hash_field_name = f"{field_name}_hash"
    
    # Search using hash field
    filter_kwargs = {hash_field_name: search_hash}
    return model_class.objects.filter(**filter_kwargs)


def bulk_encrypt_field(model_class, field_name, batch_size=100):
    """
    Bulk encrypt existing unencrypted field data
    """
    from django.db import transaction
    
    field = model_class._meta.get_field(field_name)
    if not isinstance(field, EncryptedFieldMixin):
        raise ValueError(f"Field {field_name} is not an encrypted field")
    
    # Process in batches
    total_updated = 0
    queryset = model_class.objects.all()
    
    for i in range(0, queryset.count(), batch_size):
        batch = queryset[i:i + batch_size]
        
        with transaction.atomic():
            for instance in batch:
                field_value = getattr(instance, field_name)
                
                # Check if already encrypted
                if field_value and not field._is_encrypted(field_value):
                    # Re-save to trigger encryption
                    instance.save(update_fields=[field_name])
                    total_updated += 1
    
    return total_updated