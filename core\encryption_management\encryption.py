"""
Data encryption utilities for School ERP system
"""
import os
import base64
import hashlib
import secrets
from typing import Op<PERSON>, Dict, Any, Union
from django.conf import settings
from django.core.exceptions import ImproperlyConfigured
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)


class EncryptionManager:
    """
    Central encryption manager for the School ERP system
    """
    
    def __init__(self):
        self._fernet_key = None
        self._aes_key = None
        self._initialized = False
        self._initialize()
    
    def _initialize(self):
        """
        Initialize encryption keys
        """
        try:
            # Get encryption key from settings
            encryption_key = getattr(settings, 'ENCRYPTION_KEY', None)
            
            if not encryption_key:
                # Check if we're in testing mode
                import sys
                if 'test' in sys.argv or settings.DEBUG:
                    # Generate a temporary key for development/testing
                    encryption_key = Fernet.generate_key().decode()
                    logger.warning("Using temporary encryption key for development/testing")
                else:
                    raise ImproperlyConfigured(
                        "ENCRYPTION_KEY must be set in production environment"
                    )
            
            # Initialize Fernet encryption
            if isinstance(encryption_key, str):
                encryption_key = encryption_key.encode()
            
            self._fernet_key = Fernet(encryption_key)
            
            # Initialize AES key from the same source
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=b'school_erp_salt',  # In production, use random salt per encryption
                iterations=100000,
                backend=default_backend()
            )
            self._aes_key = kdf.derive(encryption_key)
            
            self._initialized = True
            
        except Exception as e:
            logger.error(f"Failed to initialize encryption: {e}")
            raise
    
    def encrypt_field(self, data: str, field_type: str = 'general') -> str:
        """
        Encrypt a field value
        
        Args:
            data: Data to encrypt
            field_type: Type of field (for different encryption strategies)
            
        Returns:
            Encrypted data as base64 string
        """
        if not self._initialized:
            raise RuntimeError("Encryption manager not initialized")
        
        if not data:
            return data
        
        try:
            if field_type == 'pii':  # Personally Identifiable Information
                return self._encrypt_pii(data)
            elif field_type == 'financial':  # Financial data
                return self._encrypt_financial(data)
            elif field_type == 'medical':  # Medical records
                return self._encrypt_medical(data)
            else:  # General encryption
                return self._encrypt_general(data)
                
        except Exception as e:
            logger.error(f"Encryption failed for field type {field_type}: {e}")
            raise
    
    def decrypt_field(self, encrypted_data: str, field_type: str = 'general') -> str:
        """
        Decrypt a field value
        
        Args:
            encrypted_data: Encrypted data as base64 string
            field_type: Type of field (for different decryption strategies)
            
        Returns:
            Decrypted data
        """
        if not self._initialized:
            raise RuntimeError("Encryption manager not initialized")
        
        if not encrypted_data:
            return encrypted_data
        
        try:
            if field_type == 'pii':
                return self._decrypt_pii(encrypted_data)
            elif field_type == 'financial':
                return self._decrypt_financial(encrypted_data)
            elif field_type == 'medical':
                return self._decrypt_medical(encrypted_data)
            else:
                return self._decrypt_general(encrypted_data)
                
        except Exception as e:
            logger.error(f"Decryption failed for field type {field_type}: {e}")
            raise
    
    def _encrypt_general(self, data: str) -> str:
        """
        General purpose encryption using Fernet
        """
        encrypted = self._fernet_key.encrypt(data.encode())
        return base64.b64encode(encrypted).decode()
    
    def _decrypt_general(self, encrypted_data: str) -> str:
        """
        General purpose decryption using Fernet
        """
        encrypted_bytes = base64.b64decode(encrypted_data.encode())
        decrypted = self._fernet_key.decrypt(encrypted_bytes)
        return decrypted.decode()
    
    def _encrypt_pii(self, data: str) -> str:
        """
        Encrypt PII data with additional security measures
        """
        # Add random salt for PII
        salt = secrets.token_bytes(16)
        
        # Use AES-256-GCM for PII
        iv = secrets.token_bytes(12)  # GCM mode uses 96-bit IV
        cipher = Cipher(
            algorithms.AES(self._aes_key),
            modes.GCM(iv),
            backend=default_backend()
        )
        encryptor = cipher.encryptor()
        
        ciphertext = encryptor.update(data.encode()) + encryptor.finalize()
        
        # Combine salt, iv, tag, and ciphertext
        encrypted_data = salt + iv + encryptor.tag + ciphertext
        return base64.b64encode(encrypted_data).decode()
    
    def _decrypt_pii(self, encrypted_data: str) -> str:
        """
        Decrypt PII data
        """
        encrypted_bytes = base64.b64decode(encrypted_data.encode())
        
        # Extract components
        salt = encrypted_bytes[:16]
        iv = encrypted_bytes[16:28]
        tag = encrypted_bytes[28:44]
        ciphertext = encrypted_bytes[44:]
        
        # Decrypt
        cipher = Cipher(
            algorithms.AES(self._aes_key),
            modes.GCM(iv, tag),
            backend=default_backend()
        )
        decryptor = cipher.decryptor()
        
        decrypted = decryptor.update(ciphertext) + decryptor.finalize()
        return decrypted.decode()
    
    def _encrypt_financial(self, data: str) -> str:
        """
        Encrypt financial data with audit trail
        """
        # For financial data, we use the same encryption as PII
        # but we could add additional logging/auditing here
        encrypted = self._encrypt_pii(data)
        
        # Log financial data encryption (without the actual data)
        logger.info(f"Financial data encrypted at {timezone.now()}")
        
        return encrypted
    
    def _decrypt_financial(self, encrypted_data: str) -> str:
        """
        Decrypt financial data with audit trail
        """
        # Log financial data decryption
        logger.info(f"Financial data decrypted at {timezone.now()}")
        
        return self._decrypt_pii(encrypted_data)
    
    def _encrypt_medical(self, data: str) -> str:
        """
        Encrypt medical data with highest security
        """
        # Medical data uses the strongest encryption available
        return self._encrypt_pii(data)
    
    def _decrypt_medical(self, encrypted_data: str) -> str:
        """
        Decrypt medical data
        """
        return self._decrypt_pii(encrypted_data)
    
    def hash_data(self, data: str, salt: Optional[bytes] = None) -> Dict[str, str]:
        """
        Hash data for storage (one-way)
        
        Args:
            data: Data to hash
            salt: Optional salt (generated if not provided)
            
        Returns:
            Dictionary with hash and salt
        """
        if salt is None:
            salt = secrets.token_bytes(32)
        
        # Use PBKDF2 with SHA-256
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
            backend=default_backend()
        )
        
        hash_bytes = kdf.derive(data.encode())
        
        return {
            'hash': base64.b64encode(hash_bytes).decode(),
            'salt': base64.b64encode(salt).decode()
        }
    
    def verify_hash(self, data: str, stored_hash: str, salt: str) -> bool:
        """
        Verify hashed data
        
        Args:
            data: Original data
            stored_hash: Stored hash
            salt: Salt used for hashing
            
        Returns:
            True if data matches hash
        """
        try:
            salt_bytes = base64.b64decode(salt.encode())
            hash_result = self.hash_data(data, salt_bytes)
            return hash_result['hash'] == stored_hash
        except Exception as e:
            logger.error(f"Hash verification failed: {e}")
            return False


class FieldEncryption:
    """
    Field-level encryption for Django models
    """
    
    def __init__(self, field_type: str = 'general'):
        self.field_type = field_type
        self.encryption_manager = EncryptionManager()
    
    def encrypt(self, value: str) -> str:
        """
        Encrypt field value
        """
        if not value:
            return value
        
        return self.encryption_manager.encrypt_field(value, self.field_type)
    
    def decrypt(self, encrypted_value: str) -> str:
        """
        Decrypt field value
        """
        if not encrypted_value:
            return encrypted_value
        
        return self.encryption_manager.decrypt_field(encrypted_value, self.field_type)


class KeyManager:
    """
    Encryption key management
    """
    
    @staticmethod
    def generate_key() -> str:
        """
        Generate a new encryption key
        """
        return Fernet.generate_key().decode()
    
    @staticmethod
    def rotate_key(old_key: str, new_key: str, encrypted_data: str) -> str:
        """
        Rotate encryption key by re-encrypting data
        
        Args:
            old_key: Current encryption key
            new_key: New encryption key
            encrypted_data: Data encrypted with old key
            
        Returns:
            Data encrypted with new key
        """
        try:
            # Decrypt with old key
            old_fernet = Fernet(old_key.encode())
            decrypted_data = old_fernet.decrypt(base64.b64decode(encrypted_data))
            
            # Encrypt with new key
            new_fernet = Fernet(new_key.encode())
            new_encrypted = new_fernet.encrypt(decrypted_data)
            
            return base64.b64encode(new_encrypted).decode()
            
        except Exception as e:
            logger.error(f"Key rotation failed: {e}")
            raise
    
    @staticmethod
    def derive_key_from_password(password: str, salt: bytes) -> bytes:
        """
        Derive encryption key from password
        
        Args:
            password: Password to derive key from
            salt: Salt for key derivation
            
        Returns:
            Derived key
        """
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
            backend=default_backend()
        )
        
        return kdf.derive(password.encode())


class DataMasking:
    """
    Data masking utilities for displaying sensitive data
    """
    
    @staticmethod
    def mask_email(email: str) -> str:
        """
        Mask email address
        """
        if not email or '@' not in email:
            return email
        
        local, domain = email.split('@', 1)
        
        if len(local) <= 2:
            masked_local = '*' * len(local)
        else:
            masked_local = local[0] + '*' * (len(local) - 2) + local[-1]
        
        return f"{masked_local}@{domain}"
    
    @staticmethod
    def mask_phone(phone: str) -> str:
        """
        Mask phone number
        """
        if not phone:
            return phone
        
        # Remove non-digit characters
        digits = ''.join(filter(str.isdigit, phone))
        
        if len(digits) < 4:
            return '*' * len(digits)
        
        # Show first 2 and last 2 digits
        return digits[:2] + '*' * (len(digits) - 4) + digits[-2:]
    
    @staticmethod
    def mask_id_number(id_number: str) -> str:
        """
        Mask ID number
        """
        if not id_number:
            return id_number
        
        if len(id_number) <= 4:
            return '*' * len(id_number)
        
        # Show first 2 and last 2 characters
        return id_number[:2] + '*' * (len(id_number) - 4) + id_number[-2:]
    
    @staticmethod
    def mask_financial_amount(amount: str) -> str:
        """
        Mask financial amount
        """
        if not amount:
            return amount
        
        # For financial amounts, show currency and masked digits
        if '.' in amount:
            integer_part, decimal_part = amount.split('.')
            masked_integer = '*' * len(integer_part)
            return f"{masked_integer}.{decimal_part}"
        else:
            return '*' * len(amount)


class SecureTransfer:
    """
    Secure data transfer utilities
    """
    
    def __init__(self):
        self.encryption_manager = EncryptionManager()
    
    def prepare_for_transfer(self, data: Dict[str, Any], 
                           sensitive_fields: list = None) -> Dict[str, Any]:
        """
        Prepare data for secure transfer
        
        Args:
            data: Data dictionary
            sensitive_fields: List of fields to encrypt
            
        Returns:
            Data with sensitive fields encrypted
        """
        if not sensitive_fields:
            return data
        
        prepared_data = data.copy()
        
        for field in sensitive_fields:
            if field in prepared_data and prepared_data[field]:
                # Determine field type for appropriate encryption
                field_type = self._determine_field_type(field)
                prepared_data[field] = self.encryption_manager.encrypt_field(
                    str(prepared_data[field]), field_type
                )
        
        return prepared_data
    
    def process_received_data(self, data: Dict[str, Any], 
                            encrypted_fields: list = None) -> Dict[str, Any]:
        """
        Process received encrypted data
        
        Args:
            data: Data dictionary with encrypted fields
            encrypted_fields: List of fields to decrypt
            
        Returns:
            Data with encrypted fields decrypted
        """
        if not encrypted_fields:
            return data
        
        processed_data = data.copy()
        
        for field in encrypted_fields:
            if field in processed_data and processed_data[field]:
                field_type = self._determine_field_type(field)
                try:
                    processed_data[field] = self.encryption_manager.decrypt_field(
                        processed_data[field], field_type
                    )
                except Exception as e:
                    logger.error(f"Failed to decrypt field {field}: {e}")
                    # Keep encrypted value if decryption fails
        
        return processed_data
    
    def _determine_field_type(self, field_name: str) -> str:
        """
        Determine field type based on field name
        """
        field_name_lower = field_name.lower()
        
        if any(term in field_name_lower for term in ['ssn', 'national_id', 'passport', 'id_number']):
            return 'pii'
        elif any(term in field_name_lower for term in ['salary', 'fee', 'amount', 'balance', 'payment']):
            return 'financial'
        elif any(term in field_name_lower for term in ['medical', 'health', 'condition', 'allergy']):
            return 'medical'
        else:
            return 'general'


# Global encryption manager instance
encryption_manager = EncryptionManager()


# Utility functions for easy access
def encrypt_data(data: str, field_type: str = 'general') -> str:
    """
    Encrypt data using global encryption manager
    """
    return encryption_manager.encrypt_field(data, field_type)


def decrypt_data(encrypted_data: str, field_type: str = 'general') -> str:
    """
    Decrypt data using global encryption manager
    """
    return encryption_manager.decrypt_field(encrypted_data, field_type)


def hash_password(password: str) -> Dict[str, str]:
    """
    Hash password for storage
    """
    return encryption_manager.hash_data(password)


def verify_password(password: str, stored_hash: str, salt: str) -> bool:
    """
    Verify password against stored hash
    """
    return encryption_manager.verify_hash(password, stored_hash, salt)