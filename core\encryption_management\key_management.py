"""
Encryption key management system for School ERP
"""
import os
import json
import secrets
import base64
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from django.conf import settings
from django.core.management.base import BaseCommand
from django.core.cache import cache
from django.utils import timezone
from cryptography.fernet import <PERSON>rne<PERSON>
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.backends import default_backend
import logging

logger = logging.getLogger(__name__)


class KeyManagementSystem:
    """
    Centralized key management system
    """
    
    def __init__(self):
        self.key_store_path = getattr(settings, 'KEY_STORE_PATH', 'keys/')
        self.master_key_env = getattr(settings, 'MASTER_KEY_ENV', 'MASTER_ENCRYPTION_KEY')
        self.key_rotation_days = getattr(settings, 'KEY_ROTATION_DAYS', 90)
        self._ensure_key_store_exists()
    
    def _ensure_key_store_exists(self):
        """
        Ensure key store directory exists
        """
        if not os.path.exists(self.key_store_path):
            os.makedirs(self.key_store_path, mode=0o700)
    
    def generate_master_key(self) -> str:
        """
        Generate a new master encryption key
        """
        return Fernet.generate_key().decode()
    
    def get_master_key(self) -> str:
        """
        Get the master encryption key
        """
        master_key = os.environ.get(self.master_key_env)
        
        if not master_key:
            # Check if we're in testing mode
            import sys
            if 'test' in sys.argv or settings.DEBUG:
                # Generate temporary key for development/testing
                master_key = self.generate_master_key()
                logger.warning("Using temporary master key for development/testing")
            else:
                raise ValueError("Master encryption key not found in environment")
        
        return master_key
    
    def create_data_encryption_key(self, key_id: str, purpose: str = 'general') -> Dict:
        """
        Create a new data encryption key
        
        Args:
            key_id: Unique identifier for the key
            purpose: Purpose of the key (general, pii, financial, medical)
            
        Returns:
            Dictionary with key information
        """
        # Generate new key
        dek = Fernet.generate_key()
        
        # Encrypt DEK with master key
        master_key = self.get_master_key()
        master_fernet = Fernet(master_key.encode())
        encrypted_dek = master_fernet.encrypt(dek)
        
        # Create key metadata
        key_info = {
            'key_id': key_id,
            'purpose': purpose,
            'created_at': timezone.now().isoformat(),
            'expires_at': (timezone.now() + timedelta(days=self.key_rotation_days)).isoformat(),
            'encrypted_key': base64.b64encode(encrypted_dek).decode(),
            'status': 'active',
            'version': 1
        }
        
        # Store key
        self._store_key(key_id, key_info)
        
        logger.info(f"Created new data encryption key: {key_id}")
        return key_info
    
    def get_data_encryption_key(self, key_id: str) -> Optional[bytes]:
        """
        Get and decrypt a data encryption key
        
        Args:
            key_id: Key identifier
            
        Returns:
            Decrypted key bytes or None if not found
        """
        # Try cache first
        cache_key = f"dek_{key_id}"
        cached_key = cache.get(cache_key)
        if cached_key:
            return cached_key
        
        # Load from storage
        key_info = self._load_key(key_id)
        if not key_info:
            return None
        
        # Check if key is active and not expired
        if key_info['status'] != 'active':
            logger.warning(f"Key {key_id} is not active")
            return None
        
        expires_at = datetime.fromisoformat(key_info['expires_at'].replace('Z', '+00:00'))
        if timezone.now() > expires_at:
            logger.warning(f"Key {key_id} has expired")
            return None
        
        # Decrypt key
        master_key = self.get_master_key()
        master_fernet = Fernet(master_key.encode())
        
        try:
            encrypted_dek = base64.b64decode(key_info['encrypted_key'])
            dek = master_fernet.decrypt(encrypted_dek)
            
            # Cache for 1 hour
            cache.set(cache_key, dek, 3600)
            
            return dek
        except Exception as e:
            logger.error(f"Failed to decrypt key {key_id}: {e}")
            return None
    
    def rotate_key(self, key_id: str) -> Dict:
        """
        Rotate a data encryption key
        
        Args:
            key_id: Key identifier to rotate
            
        Returns:
            New key information
        """
        # Load current key
        current_key_info = self._load_key(key_id)
        if not current_key_info:
            raise ValueError(f"Key {key_id} not found")
        
        # Mark current key as rotated
        current_key_info['status'] = 'rotated'
        current_key_info['rotated_at'] = timezone.now().isoformat()
        self._store_key(key_id, current_key_info)
        
        # Create new key with incremented version
        new_version = current_key_info['version'] + 1
        new_key_id = f"{key_id}_v{new_version}"
        
        new_key_info = self.create_data_encryption_key(
            new_key_id, 
            current_key_info['purpose']
        )
        new_key_info['version'] = new_version
        new_key_info['previous_key_id'] = key_id
        
        # Update key mapping
        self._update_key_mapping(key_id, new_key_id)
        
        logger.info(f"Rotated key {key_id} to {new_key_id}")
        return new_key_info
    
    def list_keys(self, purpose: Optional[str] = None, 
                  status: Optional[str] = None) -> List[Dict]:
        """
        List all keys with optional filtering
        
        Args:
            purpose: Filter by purpose
            status: Filter by status
            
        Returns:
            List of key information dictionaries
        """
        keys = []
        
        if not os.path.exists(self.key_store_path):
            return keys
        
        for filename in os.listdir(self.key_store_path):
            if filename.endswith('.key'):
                key_id = filename[:-4]  # Remove .key extension
                key_info = self._load_key(key_id)
                
                if key_info:
                    # Apply filters
                    if purpose and key_info.get('purpose') != purpose:
                        continue
                    if status and key_info.get('status') != status:
                        continue
                    
                    keys.append(key_info)
        
        return sorted(keys, key=lambda x: x['created_at'], reverse=True)
    
    def delete_key(self, key_id: str, force: bool = False) -> bool:
        """
        Delete a key (mark as deleted or permanently remove)
        
        Args:
            key_id: Key identifier
            force: If True, permanently delete the key file
            
        Returns:
            True if successful
        """
        key_info = self._load_key(key_id)
        if not key_info:
            return False
        
        if force:
            # Permanently delete key file
            key_file = os.path.join(self.key_store_path, f"{key_id}.key")
            if os.path.exists(key_file):
                os.remove(key_file)
                logger.info(f"Permanently deleted key {key_id}")
        else:
            # Mark as deleted
            key_info['status'] = 'deleted'
            key_info['deleted_at'] = timezone.now().isoformat()
            self._store_key(key_id, key_info)
            logger.info(f"Marked key {key_id} as deleted")
        
        # Clear from cache
        cache.delete(f"dek_{key_id}")
        
        return True
    
    def backup_keys(self, backup_path: str, password: str) -> bool:
        """
        Create encrypted backup of all keys
        
        Args:
            backup_path: Path to store backup
            password: Password to encrypt backup
            
        Returns:
            True if successful
        """
        try:
            # Collect all keys
            all_keys = {}
            for filename in os.listdir(self.key_store_path):
                if filename.endswith('.key'):
                    key_id = filename[:-4]
                    key_info = self._load_key(key_id)
                    if key_info:
                        all_keys[key_id] = key_info
            
            # Create backup data
            backup_data = {
                'created_at': timezone.now().isoformat(),
                'keys': all_keys,
                'version': '1.0'
            }
            
            # Encrypt backup with password
            salt = secrets.token_bytes(16)
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
                backend=default_backend()
            )
            key = kdf.derive(password.encode())
            
            fernet = Fernet(base64.urlsafe_b64encode(key))
            encrypted_backup = fernet.encrypt(json.dumps(backup_data).encode())
            
            # Save backup
            backup_info = {
                'salt': base64.b64encode(salt).decode(),
                'data': base64.b64encode(encrypted_backup).decode()
            }
            
            with open(backup_path, 'w') as f:
                json.dump(backup_info, f, indent=2)
            
            logger.info(f"Created key backup at {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create key backup: {e}")
            return False
    
    def restore_keys(self, backup_path: str, password: str) -> bool:
        """
        Restore keys from encrypted backup
        
        Args:
            backup_path: Path to backup file
            password: Password to decrypt backup
            
        Returns:
            True if successful
        """
        try:
            # Load backup
            with open(backup_path, 'r') as f:
                backup_info = json.load(f)
            
            # Decrypt backup
            salt = base64.b64decode(backup_info['salt'])
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
                backend=default_backend()
            )
            key = kdf.derive(password.encode())
            
            fernet = Fernet(base64.urlsafe_b64encode(key))
            encrypted_data = base64.b64decode(backup_info['data'])
            decrypted_data = fernet.decrypt(encrypted_data)
            
            backup_data = json.loads(decrypted_data.decode())
            
            # Restore keys
            restored_count = 0
            for key_id, key_info in backup_data['keys'].items():
                self._store_key(key_id, key_info)
                restored_count += 1
            
            logger.info(f"Restored {restored_count} keys from backup")
            return True
            
        except Exception as e:
            logger.error(f"Failed to restore keys from backup: {e}")
            return False
    
    def _store_key(self, key_id: str, key_info: Dict):
        """
        Store key information to file
        """
        key_file = os.path.join(self.key_store_path, f"{key_id}.key")
        
        with open(key_file, 'w') as f:
            json.dump(key_info, f, indent=2)
        
        # Set restrictive permissions
        os.chmod(key_file, 0o600)
    
    def _load_key(self, key_id: str) -> Optional[Dict]:
        """
        Load key information from file
        """
        key_file = os.path.join(self.key_store_path, f"{key_id}.key")
        
        if not os.path.exists(key_file):
            return None
        
        try:
            with open(key_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load key {key_id}: {e}")
            return None
    
    def _update_key_mapping(self, old_key_id: str, new_key_id: str):
        """
        Update key mapping for rotation
        """
        mapping_file = os.path.join(self.key_store_path, 'key_mappings.json')
        
        mappings = {}
        if os.path.exists(mapping_file):
            try:
                with open(mapping_file, 'r') as f:
                    mappings = json.load(f)
            except Exception:
                pass
        
        mappings[old_key_id] = new_key_id
        
        with open(mapping_file, 'w') as f:
            json.dump(mappings, f, indent=2)
        
        os.chmod(mapping_file, 0o600)


class HSMIntegration:
    """
    Hardware Security Module integration (placeholder)
    """
    
    def __init__(self, hsm_config: Dict):
        self.hsm_config = hsm_config
        self.enabled = hsm_config.get('enabled', False)
    
    def generate_key(self, key_spec: Dict) -> str:
        """
        Generate key using HSM
        """
        if not self.enabled:
            raise NotImplementedError("HSM not enabled")
        
        # Placeholder for HSM integration
        # In production, this would interface with actual HSM
        logger.info("HSM key generation requested")
        return Fernet.generate_key().decode()
    
    def encrypt_with_hsm(self, data: bytes, key_id: str) -> bytes:
        """
        Encrypt data using HSM
        """
        if not self.enabled:
            raise NotImplementedError("HSM not enabled")
        
        # Placeholder for HSM encryption
        logger.info(f"HSM encryption requested for key {key_id}")
        return data
    
    def decrypt_with_hsm(self, encrypted_data: bytes, key_id: str) -> bytes:
        """
        Decrypt data using HSM
        """
        if not self.enabled:
            raise NotImplementedError("HSM not enabled")
        
        # Placeholder for HSM decryption
        logger.info(f"HSM decryption requested for key {key_id}")
        return encrypted_data


class KeyRotationScheduler:
    """
    Automated key rotation scheduler
    """
    
    def __init__(self, kms: KeyManagementSystem):
        self.kms = kms
    
    def check_keys_for_rotation(self) -> List[str]:
        """
        Check which keys need rotation
        
        Returns:
            List of key IDs that need rotation
        """
        keys_to_rotate = []
        
        for key_info in self.kms.list_keys(status='active'):
            expires_at = datetime.fromisoformat(key_info['expires_at'].replace('Z', '+00:00'))
            
            # Rotate if key expires within 7 days
            if timezone.now() + timedelta(days=7) >= expires_at:
                keys_to_rotate.append(key_info['key_id'])
        
        return keys_to_rotate
    
    def rotate_expired_keys(self) -> Dict[str, str]:
        """
        Rotate all keys that need rotation
        
        Returns:
            Dictionary mapping old key IDs to new key IDs
        """
        rotated_keys = {}
        keys_to_rotate = self.check_keys_for_rotation()
        
        for key_id in keys_to_rotate:
            try:
                new_key_info = self.kms.rotate_key(key_id)
                rotated_keys[key_id] = new_key_info['key_id']
                logger.info(f"Rotated key {key_id} to {new_key_info['key_id']}")
            except Exception as e:
                logger.error(f"Failed to rotate key {key_id}: {e}")
        
        return rotated_keys


# Global key management system instance
kms = KeyManagementSystem()


# Utility functions
def get_encryption_key(key_id: str = 'default') -> Optional[bytes]:
    """
    Get encryption key by ID
    """
    return kms.get_data_encryption_key(key_id)


def create_encryption_key(key_id: str, purpose: str = 'general') -> Dict:
    """
    Create new encryption key
    """
    return kms.create_data_encryption_key(key_id, purpose)


def rotate_encryption_key(key_id: str) -> Dict:
    """
    Rotate encryption key
    """
    return kms.rotate_key(key_id)