"""
System Integration Module for School ERP
Handles cross-module workflows and system-wide integration
"""
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from django.db import transaction
from django.utils import timezone
from django.core.cache import cache
from django.contrib.auth.models import User

from students.models import Student, Parent, StudentEnrollment
from academics.models import Grade, Class, Attendance, Subject
from finance.models import StudentFee, Payment, FeeStructure
from hr.models import Employee, Department
from transportation.models import StudentTransportation, Route, Vehicle
from library.models import BookBorrowing, Book
from health.models import HealthProfile, MedicalIncident
from communications.models import Notification
from core.models import School, AcademicYear

logger = logging.getLogger(__name__)


class SystemIntegrator:
    """
    Main system integration coordinator
    """
    
    def __init__(self):
        self.workflows = {}
        self.integration_status = {}
        self.workflow_history = []
    
    def register_workflow(self, name: str, workflow: 'BaseWorkflow'):
        """Register a cross-module workflow"""
        self.workflows[name] = workflow
        logger.info(f"Registered workflow: {name}")
    
    def execute_workflow(self, workflow_name: str, **kwargs) -> Dict[str, Any]:
        """Execute a registered workflow"""
        if workflow_name not in self.workflows:
            raise ValueError(f"Workflow '{workflow_name}' not found")
        
        workflow = self.workflows[workflow_name]
        
        try:
            result = workflow.execute(**kwargs)
            self._log_workflow_execution(workflow_name, result, success=True)
            return result
        except Exception as e:
            self._log_workflow_execution(workflow_name, {'error': str(e)}, success=False)
            raise
    
    def get_integration_status(self) -> Dict[str, Any]:
        """Get overall system integration status"""
        return {
            'registered_workflows': list(self.workflows.keys()),
            'workflow_count': len(self.workflows),
            'execution_history': len(self.workflow_history),
            'system_health': self._check_system_health(),
            'last_execution': self.workflow_history[-1] if self.workflow_history else None
        }
    
    def _log_workflow_execution(self, workflow_name: str, result: Dict[str, Any], success: bool):
        """Log workflow execution"""
        log_entry = {
            'workflow': workflow_name,
            'timestamp': timezone.now(),
            'success': success,
            'result': result
        }
        
        self.workflow_history.append(log_entry)
        
        # Keep only last 100 executions
        if len(self.workflow_history) > 100:
            self.workflow_history = self.workflow_history[-100:]
        
        # Cache for monitoring
        cache.set(f"workflow_execution_{workflow_name}", log_entry, timeout=3600)
    
    def _check_system_health(self) -> Dict[str, str]:
        """Check system health across modules"""
        health_status = {}
        
        try:
            # Database connectivity
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            health_status['database'] = 'healthy'
        except Exception:
            health_status['database'] = 'unhealthy'
        
        try:
            # Cache connectivity
            cache.set('health_check', 'ok', timeout=60)
            if cache.get('health_check') == 'ok':
                health_status['cache'] = 'healthy'
            else:
                health_status['cache'] = 'unhealthy'
        except Exception:
            health_status['cache'] = 'unhealthy'
        
        # Module health checks
        modules = ['students', 'academics', 'finance', 'hr', 'transportation', 'library', 'health']
        for module in modules:
            try:
                # Basic model access test
                if module == 'students':
                    Student.objects.count()
                elif module == 'academics':
                    Grade.objects.count()
                elif module == 'finance':
                    StudentFee.objects.count()
                # Add other module checks as needed
                
                health_status[module] = 'healthy'
            except Exception:
                health_status[module] = 'unhealthy'
        
        return health_status


class BaseWorkflow:
    """
    Base class for cross-module workflows
    """
    
    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
    
    def execute(self, **kwargs) -> Dict[str, Any]:
        """Execute the workflow"""
        raise NotImplementedError
    
    def validate_inputs(self, **kwargs) -> bool:
        """Validate workflow inputs"""
        return True
    
    def rollback(self, **kwargs) -> Dict[str, Any]:
        """Rollback workflow changes if needed"""
        return {'status': 'rollback_not_implemented'}


class StudentEnrollmentWorkflow(BaseWorkflow):
    """
    Complete student enrollment workflow across all modules
    """
    
    def __init__(self):
        super().__init__(
            name="student_enrollment",
            description="Complete student enrollment process across all modules"
        )
    
    def execute(self, **kwargs) -> Dict[str, Any]:
        """Execute student enrollment workflow"""
        student_data = kwargs.get('student_data', {})
        school_id = kwargs.get('school_id')
        academic_year_id = kwargs.get('academic_year_id')
        
        if not all([student_data, school_id, academic_year_id]):
            raise ValueError("Missing required parameters for student enrollment")
        
        try:
            with transaction.atomic():
                # Step 1: Create/get parent
                parent = self._create_or_get_parent(student_data.get('parent_data', {}), school_id)
                
                # Step 2: Create student
                student = self._create_student(student_data, parent, school_id)
                
                # Step 3: Create enrollment
                enrollment = self._create_enrollment(student, student_data.get('grade_id'), academic_year_id)
                
                # Step 4: Assign fees
                fees = self._assign_fees(student, academic_year_id)
                
                # Step 5: Create health profile
                health_profile = self._create_health_profile(student, student_data.get('health_data', {}))
                
                # Step 6: Assign transportation (if requested)
                transportation = None
                if student_data.get('transportation_required'):
                    transportation = self._assign_transportation(student, student_data.get('route_id'))
                
                # Step 7: Send notifications
                self._send_enrollment_notifications(student, parent)
                
                return {
                    'status': 'success',
                    'student_id': student.id,
                    'parent_id': parent.id,
                    'enrollment_id': enrollment.id,
                    'fees_assigned': len(fees),
                    'health_profile_id': health_profile.id,
                    'transportation_assigned': transportation is not None
                }
                
        except Exception as e:
            logger.error(f"Student enrollment workflow failed: {e}")
            raise
    
    def _create_or_get_parent(self, parent_data: Dict[str, Any], school_id: int) -> Parent:
        """Create or retrieve parent"""
        email = parent_data.get('email')
        if email:
            try:
                user = User.objects.get(email=email)
                parent = Parent.objects.get(user=user, school_id=school_id)
                return parent
            except (User.DoesNotExist, Parent.DoesNotExist):
                pass
        
        # Create new parent
        user = User.objects.create_user(
            username=parent_data.get('username', email or f"parent_{timezone.now().timestamp()}"),
            email=email or '',
            first_name=parent_data.get('first_name', ''),
            last_name=parent_data.get('last_name', ''),
            password=parent_data.get('password', 'temp_password_123')
        )
        
        parent = Parent.objects.create(
            school_id=school_id,
            user=user,
            father_name=parent_data.get('father_name', ''),
            mother_name=parent_data.get('mother_name', ''),
            phone=parent_data.get('phone', ''),
            email=email or '',
            address=parent_data.get('address', '')
        )
        
        return parent
    
    def _create_student(self, student_data: Dict[str, Any], parent: Parent, school_id: int) -> Student:
        """Create student record"""
        return Student.objects.create(
            school_id=school_id,
            student_id=student_data.get('student_id'),
            first_name=student_data.get('first_name'),
            last_name=student_data.get('last_name'),
            date_of_birth=student_data.get('date_of_birth'),
            gender=student_data.get('gender'),
            nationality=student_data.get('nationality', 'US'),
            parent=parent,
            admission_date=timezone.now().date(),
            status='active'
        )
    
    def _create_enrollment(self, student: Student, grade_id: int, academic_year_id: int) -> StudentEnrollment:
        """Create student enrollment"""
        return StudentEnrollment.objects.create(
            school=student.school,
            student=student,
            academic_year_id=academic_year_id,
            grade_id=grade_id,
            enrollment_date=timezone.now().date(),
            status='active'
        )
    
    def _assign_fees(self, student: Student, academic_year_id: int) -> List[StudentFee]:
        """Assign fees to student"""
        enrollment = student.enrollments.filter(academic_year_id=academic_year_id).first()
        if not enrollment:
            return []
        
        fee_structures = FeeStructure.objects.filter(
            school=student.school,
            academic_year_id=academic_year_id,
            grade=enrollment.grade,
            is_active=True
        )
        
        fees = []
        for fee_structure in fee_structures:
            fee = StudentFee.objects.create(
                school=student.school,
                student=student,
                fee_structure=fee_structure,
                academic_year_id=academic_year_id,
                total_amount=fee_structure.get_total_fee(),
                paid_amount=0,
                due_date=timezone.now().date() + timedelta(days=30),
                status='pending'
            )
            fees.append(fee)
        
        return fees
    
    def _create_health_profile(self, student: Student, health_data: Dict[str, Any]) -> HealthProfile:
        """Create health profile"""
        return HealthProfile.objects.create(
            school=student.school,
            student=student,
            blood_type=health_data.get('blood_type', ''),
            allergies=health_data.get('allergies', ''),
            medications=health_data.get('medications', ''),
            emergency_contact_name=health_data.get('emergency_contact_name', ''),
            emergency_contact_phone=health_data.get('emergency_contact_phone', '')
        )
    
    def _assign_transportation(self, student: Student, route_id: Optional[int]) -> Optional[StudentTransportation]:
        """Assign transportation"""
        if not route_id:
            return None
        
        try:
            route = Route.objects.get(id=route_id, school=student.school)
            return StudentTransportation.objects.create(
                school=student.school,
                student=student,
                route=route,
                status='active'
            )
        except Route.DoesNotExist:
            logger.warning(f"Route {route_id} not found")
            return None
    
    def _send_enrollment_notifications(self, student: Student, parent: Parent):
        """Send enrollment notifications"""
        try:
            Notification.objects.create(
                school=student.school,
                recipient=parent.user,
                title=f"Welcome to {student.school.name}",
                message=f"Your child {student.get_full_name()} has been successfully enrolled.",
                notification_type='enrollment',
                is_read=False
            )
        except Exception as e:
            logger.error(f"Failed to send enrollment notifications: {e}")


class AcademicYearTransitionWorkflow(BaseWorkflow):
    """
    Academic year transition workflow
    """
    
    def __init__(self):
        super().__init__(
            name="academic_year_transition",
            description="Transition students and data to new academic year"
        )
    
    def execute(self, **kwargs) -> Dict[str, Any]:
        """Execute academic year transition"""
        current_year_id = kwargs.get('current_year_id')
        next_year_id = kwargs.get('next_year_id')
        school_id = kwargs.get('school_id')
        
        if not all([current_year_id, next_year_id, school_id]):
            raise ValueError("Missing required parameters for academic year transition")
        
        try:
            with transaction.atomic():
                # Step 1: Promote students
                promotions = self._promote_students(current_year_id, next_year_id, school_id)
                
                # Step 2: Update class assignments
                class_updates = self._update_class_assignments(next_year_id, school_id)
                
                # Step 3: Generate fee structures
                fee_structures = self._generate_fee_structures(next_year_id, school_id)
                
                # Step 4: Update transportation
                transport_updates = self._update_transportation(next_year_id, school_id)
                
                # Step 5: Archive data
                archive_status = self._archive_previous_year(current_year_id)
                
                # Step 6: Send notifications
                self._send_transition_notifications(school_id, next_year_id)
                
                return {
                    'status': 'success',
                    'promotions': promotions,
                    'class_updates': class_updates,
                    'fee_structures_created': len(fee_structures),
                    'transport_updates': transport_updates,
                    'archive_status': archive_status
                }
                
        except Exception as e:
            logger.error(f"Academic year transition failed: {e}")
            raise
    
    def _promote_students(self, current_year_id: int, next_year_id: int, school_id: int) -> Dict[str, int]:
        """Promote students to next grade"""
        promotions = {'promoted': 0, 'held_back': 0, 'graduated': 0}
        
        enrollments = StudentEnrollment.objects.filter(
            school_id=school_id,
            academic_year_id=current_year_id,
            status='active'
        )
        
        for enrollment in enrollments:
            should_promote = self._should_promote_student(enrollment)
            
            if should_promote:
                next_grade = self._get_next_grade(enrollment.grade)
                if next_grade:
                    StudentEnrollment.objects.create(
                        school_id=school_id,
                        student=enrollment.student,
                        academic_year_id=next_year_id,
                        grade=next_grade,
                        enrollment_date=timezone.now().date(),
                        status='active'
                    )
                    promotions['promoted'] += 1
                else:
                    enrollment.student.status = 'graduated'
                    enrollment.student.save()
                    promotions['graduated'] += 1
            else:
                StudentEnrollment.objects.create(
                    school_id=school_id,
                    student=enrollment.student,
                    academic_year_id=next_year_id,
                    grade=enrollment.grade,
                    enrollment_date=timezone.now().date(),
                    status='active'
                )
                promotions['held_back'] += 1
            
            enrollment.status = 'completed'
            enrollment.save()
        
        return promotions
    
    def _should_promote_student(self, enrollment: StudentEnrollment) -> bool:
        """Determine if student should be promoted"""
        # Simple promotion logic based on attendance
        total_days = Attendance.objects.filter(
            student=enrollment.student,
            class_instance__academic_year=enrollment.academic_year
        ).count()
        
        if total_days == 0:
            return True  # No attendance data, promote by default
        
        present_days = Attendance.objects.filter(
            student=enrollment.student,
            class_instance__academic_year=enrollment.academic_year,
            status='present'
        ).count()
        
        attendance_rate = (present_days / total_days) * 100
        return attendance_rate >= 75  # 75% attendance required
    
    def _get_next_grade(self, current_grade: Grade) -> Optional[Grade]:
        """Get next grade for promotion"""
        try:
            return Grade.objects.get(
                school=current_grade.school,
                level=current_grade.level + 1,
                is_active=True
            )
        except Grade.DoesNotExist:
            return None
    
    def _update_class_assignments(self, academic_year_id: int, school_id: int) -> Dict[str, int]:
        """Update class assignments for new year"""
        # Implementation for class updates
        return {'classes_updated': 0, 'new_classes': 0}
    
    def _generate_fee_structures(self, academic_year_id: int, school_id: int) -> List[FeeStructure]:
        """Generate fee structures for new year"""
        # Implementation for fee structure generation
        return []
    
    def _update_transportation(self, academic_year_id: int, school_id: int) -> Dict[str, int]:
        """Update transportation assignments"""
        # Implementation for transportation updates
        return {'assignments_updated': 0}
    
    def _archive_previous_year(self, academic_year_id: int) -> Dict[str, Any]:
        """Archive previous year data"""
        # Implementation for data archiving
        return {'status': 'archived', 'records': 0}
    
    def _send_transition_notifications(self, school_id: int, academic_year_id: int):
        """Send transition notifications"""
        # Implementation for notifications
        pass


class SystemAnalytics:
    """
    System-wide analytics and reporting
    """
    
    def __init__(self):
        self.analytics_cache = {}
    
    def get_system_overview(self, school_id: int) -> Dict[str, Any]:
        """Get comprehensive system overview"""
        cache_key = f"system_overview_{school_id}"
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        overview = {
            'students': self._get_student_analytics(school_id),
            'academics': self._get_academic_analytics(school_id),
            'finance': self._get_financial_analytics(school_id),
            'transportation': self._get_transportation_analytics(school_id),
            'library': self._get_library_analytics(school_id),
            'health': self._get_health_analytics(school_id),
            'hr': self._get_hr_analytics(school_id),
            'timestamp': timezone.now().isoformat()
        }
        
        # Cache for 1 hour
        cache.set(cache_key, overview, timeout=3600)
        return overview
    
    def _get_student_analytics(self, school_id: int) -> Dict[str, Any]:
        """Get student analytics"""
        return {
            'total_students': Student.objects.filter(school_id=school_id, status='active').count(),
            'new_enrollments_this_month': Student.objects.filter(
                school_id=school_id,
                admission_date__gte=timezone.now().date().replace(day=1)
            ).count(),
            'by_grade': list(
                StudentEnrollment.objects.filter(
                    school_id=school_id,
                    status='active'
                ).values('grade__name').annotate(count=models.Count('id'))
            )
        }
    
    def _get_academic_analytics(self, school_id: int) -> Dict[str, Any]:
        """Get academic analytics"""
        return {
            'total_classes': Class.objects.filter(school_id=school_id, is_active=True).count(),
            'total_subjects': Subject.objects.filter(school_id=school_id, is_active=True).count(),
            'attendance_rate': self._calculate_attendance_rate(school_id)
        }
    
    def _get_financial_analytics(self, school_id: int) -> Dict[str, Any]:
        """Get financial analytics"""
        from django.db.models import Sum
        
        total_fees = StudentFee.objects.filter(school_id=school_id).aggregate(
            total=Sum('total_amount')
        )['total'] or 0
        
        paid_fees = StudentFee.objects.filter(school_id=school_id).aggregate(
            paid=Sum('paid_amount')
        )['paid'] or 0
        
        return {
            'total_fees': float(total_fees),
            'collected_fees': float(paid_fees),
            'collection_rate': (paid_fees / total_fees * 100) if total_fees > 0 else 0,
            'pending_payments': StudentFee.objects.filter(
                school_id=school_id,
                status__in=['pending', 'partial']
            ).count()
        }
    
    def _get_transportation_analytics(self, school_id: int) -> Dict[str, Any]:
        """Get transportation analytics"""
        return {
            'total_routes': Route.objects.filter(school_id=school_id, status='active').count(),
            'total_vehicles': Vehicle.objects.filter(school_id=school_id, status='active').count(),
            'students_using_transport': StudentTransportation.objects.filter(
                school_id=school_id,
                status='active'
            ).count()
        }
    
    def _get_library_analytics(self, school_id: int) -> Dict[str, Any]:
        """Get library analytics"""
        return {
            'total_books': Book.objects.filter(school_id=school_id, status='available').count(),
            'books_borrowed': BookBorrowing.objects.filter(
                school_id=school_id,
                status='borrowed'
            ).count(),
            'overdue_books': BookBorrowing.objects.filter(
                school_id=school_id,
                status='borrowed',
                due_date__lt=timezone.now().date()
            ).count()
        }
    
    def _get_health_analytics(self, school_id: int) -> Dict[str, Any]:
        """Get health analytics"""
        return {
            'health_profiles': HealthProfile.objects.filter(school_id=school_id).count(),
            'medical_incidents_this_month': MedicalIncident.objects.filter(
                school_id=school_id,
                incident_date__gte=timezone.now().date().replace(day=1)
            ).count()
        }
    
    def _get_hr_analytics(self, school_id: int) -> Dict[str, Any]:
        """Get HR analytics"""
        return {
            'total_employees': Employee.objects.filter(school_id=school_id, status='active').count(),
            'departments': Department.objects.filter(school_id=school_id).count()
        }
    
    def _calculate_attendance_rate(self, school_id: int) -> float:
        """Calculate overall attendance rate"""
        from django.db.models import Count
        
        attendance_stats = Attendance.objects.filter(
            school_id=school_id,
            date__gte=timezone.now().date() - timedelta(days=30)
        ).aggregate(
            total=Count('id'),
            present=Count('id', filter=models.Q(status='present'))
        )
        
        total = attendance_stats['total']
        present = attendance_stats['present']
        
        return (present / total * 100) if total > 0 else 0


# Global instances
system_integrator = SystemIntegrator()
system_analytics = SystemAnalytics()

# Register default workflows
system_integrator.register_workflow('student_enrollment', StudentEnrollmentWorkflow())
system_integrator.register_workflow('academic_year_transition', AcademicYearTransitionWorkflow())


# Utility functions
def execute_student_enrollment(student_data: Dict[str, Any], school_id: int, academic_year_id: int) -> Dict[str, Any]:
    """Execute student enrollment workflow"""
    return system_integrator.execute_workflow(
        'student_enrollment',
        student_data=student_data,
        school_id=school_id,
        academic_year_id=academic_year_id
    )


def execute_academic_year_transition(current_year_id: int, next_year_id: int, school_id: int) -> Dict[str, Any]:
    """Execute academic year transition workflow"""
    return system_integrator.execute_workflow(
        'academic_year_transition',
        current_year_id=current_year_id,
        next_year_id=next_year_id,
        school_id=school_id
    )


def get_system_health() -> Dict[str, Any]:
    """Get system health status"""
    return system_integrator.get_integration_status()


def get_system_analytics(school_id: int) -> Dict[str, Any]:
    """Get system analytics"""
    return system_analytics.get_system_overview(school_id)


# Export main components
__all__ = [
    'SystemIntegrator',
    'BaseWorkflow',
    'StudentEnrollmentWorkflow',
    'AcademicYearTransitionWorkflow',
    'SystemAnalytics',
    'system_integrator',
    'system_analytics',
    'execute_student_enrollment',
    'execute_academic_year_transition',
    'get_system_health',
    'get_system_analytics'
]