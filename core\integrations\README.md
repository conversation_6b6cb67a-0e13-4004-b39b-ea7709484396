# School ERP Integration System

This document provides comprehensive information about the School ERP integration system, which enables seamless connectivity with third-party services including payment gateways, email services, SMS gateways, and cloud storage providers.

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Supported Integrations](#supported-integrations)
4. [Installation & Setup](#installation--setup)
5. [Configuration](#configuration)
6. [Usage Examples](#usage-examples)
7. [API Reference](#api-reference)
8. [Testing](#testing)
9. [Security](#security)
10. [Monitoring & Analytics](#monitoring--analytics)
11. [Troubleshooting](#troubleshooting)
12. [Contributing](#contributing)

## Overview

The integration system provides a unified framework for connecting with external services. It features:

- **Modular Architecture**: Easy to add new providers and services
- **Security First**: Encrypted credential storage and secure communication
- **Comprehensive Logging**: Detailed activity logs and analytics
- **Real-time Monitoring**: Health checks and performance metrics
- **Webhook Support**: Handle incoming webhooks from integrated services
- **Rate Limiting Awareness**: Respect provider rate limits
- **Multi-tenant Support**: Isolated integrations per school/organization

## Architecture

### Core Components

```
┌─────────────────────────────────────────────────────────────┐
│                    Integration Layer                         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐│
│  │  Payment    │ │   Email     │ │    SMS      │ │ Storage ││
│  │  Gateways   │ │  Services   │ │  Gateways   │ │Services ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘│
├─────────────────────────────────────────────────────────────┤
│                    Service Layer                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐│
│  │Integration  │ │  Analytics  │ │  Webhook    │ │Encryption││
│  │  Manager    │ │   Service   │ │  Handler    │ │ Service ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘│
├─────────────────────────────────────────────────────────────┤
│                     Data Layer                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐│
│  │Integration  │ │Integration  │ │Integration  │ │Integration││
│  │  Provider   │ │    Log      │ │  Webhook    │ │Analytics││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘│
└─────────────────────────────────────────────────────────────┘
```

### Key Models

- **IntegrationProvider**: Defines available third-party services
- **Integration**: Configured instance of a provider for a specific use case
- **IntegrationLog**: Activity logs for monitoring and debugging
- **IntegrationWebhook**: Incoming webhook data from providers
- **IntegrationAnalytics**: Performance and usage statistics

## Supported Integrations

### Payment Gateways

| Provider | Features | Status |
|----------|----------|--------|
| Stripe | Payments, Refunds, Subscriptions, Webhooks | ✅ Active |
| PayPal | Payments, Refunds, Webhooks | ✅ Active |
| Razorpay | Payments, Refunds, Webhooks | ✅ Active |

### Email Services

| Provider | Features | Status |
|----------|----------|--------|
| SendGrid | Transactional Email, Templates, Analytics | ✅ Active |
| Mailgun | Transactional Email, Templates, Analytics | ✅ Active |
| Amazon SES | Transactional Email, Templates | ✅ Active |
| SMTP | Generic SMTP Support | ✅ Active |

### SMS Gateways

| Provider | Features | Status |
|----------|----------|--------|
| Twilio | SMS, MMS, Voice, Webhooks | ✅ Active |
| Vonage (Nexmo) | SMS, Delivery Receipts, Webhooks | ✅ Active |
| Textlocal | SMS, Group Messaging | ✅ Active |
| MSG91 | SMS, OTP, Delivery Reports | ✅ Active |
| WhatsApp Business | Text, Media, Templates, Webhooks | ✅ Active |

### Cloud Storage

| Provider | Features | Status |
|----------|----------|--------|
| Amazon S3 | File Upload/Download, Presigned URLs | ✅ Active |
| Google Cloud Storage | File Upload/Download, Signed URLs | ✅ Active |
| Azure Blob Storage | File Upload/Download, SAS URLs | ✅ Active |
| Dropbox | File Upload/Download, Shared Links | ✅ Active |

## Installation & Setup

### Prerequisites

```bash
# Required Python packages
pip install cryptography
pip install requests
pip install celery
pip install redis

# Optional packages for specific providers
pip install boto3  # For AWS services
pip install google-cloud-storage  # For Google Cloud
pip install azure-storage-blob  # For Azure
pip install dropbox  # For Dropbox
```

### Database Migration

```bash
python manage.py makemigrations integrations
python manage.py migrate
```

### Setup Default Providers

```bash
python manage.py setup_integration_providers
```

### URL Configuration

Add to your main `urls.py`:

```python
from django.urls import path, include

urlpatterns = [
    # ... other patterns
    path('integrations/', include('core.integrations.urls')),
]
```

## Configuration

### Environment Variables

```bash
# Encryption key for credentials (optional, will use SECRET_KEY if not set)
INTEGRATION_ENCRYPTION_KEY=your-encryption-key-here

# Site URL for webhook callbacks
SITE_URL=https://your-school-erp.com
```

### Django Settings

```python
# settings.py

# Add to INSTALLED_APPS
INSTALLED_APPS = [
    # ... other apps
    'core.integrations',
]

# Celery configuration for async tasks
CELERY_BROKER_URL = 'redis://localhost:6379/0'
CELERY_RESULT_BACKEND = 'redis://localhost:6379/0'

# Cache configuration for analytics
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}
```

## Usage Examples

### Creating an Integration

```python
from core.integrations.models import IntegrationProvider, Integration

# Get the provider
stripe_provider = IntegrationProvider.objects.get(name='stripe')

# Create integration
integration = Integration.objects.create(
    provider=stripe_provider,
    name='Main Payment Gateway',
    description='Primary Stripe integration for fee payments',
    credentials={
        'api_key': 'sk_test_your_stripe_key_here'
    },
    settings={
        'currency': 'USD',
        'webhook_url': 'https://your-site.com/integrations/webhook/'
    },
    created_by=request.user
)
```

### Using Payment Gateway

```python
from core.integrations.services import IntegrationManager

# Get the integration
integration = Integration.objects.get(name='Main Payment Gateway')

# Get the service
payment_service = IntegrationManager.get_service(integration)

# Create payment
result = payment_service.create_payment(
    amount=100.00,
    currency='USD',
    description='School fee payment',
    customer_data={
        'student_id': '12345',
        'student_name': 'John Doe'
    }
)

print(f"Payment ID: {result['id']}")
```

### Sending Email

```python
# Get email integration
email_integration = Integration.objects.get(
    provider__name='sendgrid',
    name='School Email Service'
)

# Get email service
email_service = IntegrationManager.get_service(email_integration)

# Send email
result = email_service.send_email(
    to_emails=['<EMAIL>'],
    subject='Student Progress Report',
    content='<h1>Monthly Progress Report</h1><p>Your child is doing well...</p>',
    from_email='<EMAIL>'
)

print(f"Email sent: {result['message_id']}")
```

### Sending SMS

```python
# Get SMS integration
sms_integration = Integration.objects.get(
    provider__name='twilio',
    name='School SMS Gateway'
)

# Get SMS service
sms_service = IntegrationManager.get_service(sms_integration)

# Send SMS
result = sms_service.send_sms(
    to_number='+**********',
    message='Your child has been marked absent today. Please contact the school.',
    from_number='+**********'
)

print(f"SMS sent: {result['sid']}")
```

### File Upload to Cloud Storage

```python
# Get storage integration
storage_integration = Integration.objects.get(
    provider__name='amazon_s3',
    name='School File Storage'
)

# Get storage service
storage_service = IntegrationManager.get_service(storage_integration)

# Upload file
with open('student_report.pdf', 'rb') as file:
    result = storage_service.upload_file(
        file_path='reports/2024/student_report.pdf',
        content=file,
        content_type='application/pdf'
    )

print(f"File uploaded: {result['url']}")
```

## API Reference

### REST API Endpoints

#### Providers

```http
GET /integrations/api/providers/
GET /integrations/api/providers/{id}/
GET /integrations/api/providers/types/
```

#### Integrations

```http
GET /integrations/api/integrations/
POST /integrations/api/integrations/
GET /integrations/api/integrations/{id}/
PUT /integrations/api/integrations/{id}/
DELETE /integrations/api/integrations/{id}/

POST /integrations/api/integrations/{id}/test_connection/
GET /integrations/api/integrations/{id}/analytics/
GET /integrations/api/integrations/{id}/health/
GET /integrations/api/integrations/{id}/logs/
POST /integrations/api/integrations/{id}/sync/
```

#### Webhooks

```http
POST /integrations/webhook/{integration_id}/
```

#### Analytics

```http
GET /integrations/api/analytics/
POST /integrations/api/test-all/
```

### Python API

#### Integration Manager

```python
from core.integrations.services import IntegrationManager

# Test integration
success, message = IntegrationManager.test_integration(integration)

# Get service instance
service = IntegrationManager.get_service(integration)

# Sync integration data
success, message = IntegrationManager.sync_integration_data(integration)
```

#### Analytics Service

```python
from core.integrations.analytics import integration_analytics_service

# Update daily analytics
analytics = integration_analytics_service.update_daily_analytics(integration)

# Generate summary
summary = integration_analytics_service.generate_integration_summary(
    integration, days=30
)

# Generate health report
health = integration_analytics_service.generate_health_report(integration)

# Generate usage forecast
forecast = integration_analytics_service.generate_usage_forecast(
    integration, days_ahead=30
)
```

## Testing

### Running Tests

```bash
# Run all integration tests
python manage.py run_integration_tests

# Run specific test types
python manage.py run_integration_tests --test-type unit
python manage.py run_integration_tests --test-type integration
python manage.py run_integration_tests --test-type performance
python manage.py run_integration_tests --test-type security

# Test specific provider type
python manage.py run_integration_tests --provider-type payment

# Generate detailed report
python manage.py run_integration_tests --generate-report --output-file test_report.json

# Test all configured integrations
python manage.py test_integrations

# Test specific integration
python manage.py test_integrations --integration-id <uuid>
```

### Unit Tests

```bash
# Run Django unit tests
python manage.py test core.integrations.tests
```

### Manual Testing

```python
# Test connection
from core.integrations.models import Integration
from core.integrations.services import IntegrationManager

integration = Integration.objects.get(name='Your Integration')
success, message = IntegrationManager.test_integration(integration)
print(f"Test result: {success} - {message}")
```

## Security

### Credential Encryption

All integration credentials are automatically encrypted before storage using AES encryption:

```python
from core.integrations.encryption import IntegrationEncryption

encryption = IntegrationEncryption()

# Encrypt sensitive data
encrypted = encryption.encrypt('sensitive_api_key')

# Decrypt when needed
decrypted = encryption.decrypt(encrypted)
```

### Access Control

- Users can only access integrations they created
- Admin users can access all integrations
- API endpoints enforce proper authentication
- Webhook endpoints validate signatures when possible

### Best Practices

1. **Rotate API Keys Regularly**: Update integration credentials periodically
2. **Use Environment Variables**: Store sensitive configuration in environment variables
3. **Monitor Webhook Signatures**: Always verify webhook signatures when supported
4. **Limit Permissions**: Use API keys with minimal required permissions
5. **Regular Security Audits**: Review integration logs for suspicious activity

## Monitoring & Analytics

### Health Monitoring

```python
# Check integration health
health_report = integration_analytics_service.generate_health_report(integration)

print(f"Health Status: {health_report['health_status']}")
print(f"Success Rate: {health_report['health_score']}%")
```

### Performance Analytics

```python
# Get performance summary
summary = integration_analytics_service.generate_integration_summary(
    integration, days=30
)

print(f"Total Requests: {summary['summary_stats']['total_requests']}")
print(f"Success Rate: {summary['summary_stats']['success_rate']}%")
print(f"Avg Response Time: {summary['summary_stats']['avg_response_time_ms']}ms")
```

### Usage Forecasting

```python
# Generate usage forecast
forecast = integration_analytics_service.generate_usage_forecast(
    integration, days_ahead=30
)

print(f"Predicted daily requests: {forecast['forecast'][0]['predicted_requests']}")
print(f"Confidence: {forecast['confidence_metrics']['confidence_percentage']}%")
```

### Dashboard Access

Visit `/integrations/` for the web-based dashboard with:

- Integration status overview
- Real-time health monitoring
- Performance charts and graphs
- Error analysis and alerts
- Usage trends and forecasting

## Troubleshooting

### Common Issues

#### Connection Failures

```python
# Check integration status
integration = Integration.objects.get(name='Your Integration')
print(f"Status: {integration.status}")
print(f"Last Error: {integration.last_error}")

# Test connection
success, message = IntegrationManager.test_integration(integration)
print(f"Connection Test: {success} - {message}")
```

#### Webhook Issues

1. **Verify webhook URL is accessible**
2. **Check webhook signature validation**
3. **Review webhook logs in integration dashboard**
4. **Ensure proper content-type headers**

#### Rate Limiting

```python
# Check rate limits
provider = integration.provider
rate_limits = provider.rate_limits

print(f"Requests per second: {rate_limits.get('requests_per_second', 'N/A')}")
print(f"Requests per hour: {rate_limits.get('requests_per_hour', 'N/A')}")
```

#### Performance Issues

```python
# Check analytics for performance issues
analytics = IntegrationAnalytics.objects.filter(
    integration=integration,
    date__gte=timezone.now().date() - timedelta(days=7)
)

for analytic in analytics:
    if analytic.avg_response_time_ms > 5000:  # 5 seconds
        print(f"Slow response on {analytic.date}: {analytic.avg_response_time_ms}ms")
```

### Debug Mode

Enable verbose logging for debugging:

```python
import logging

# Enable debug logging for integrations
logging.getLogger('core.integrations').setLevel(logging.DEBUG)
```

### Log Analysis

```python
# Get recent error logs
error_logs = IntegrationLog.objects.filter(
    integration=integration,
    level='error',
    timestamp__gte=timezone.now() - timedelta(hours=24)
).order_by('-timestamp')

for log in error_logs:
    print(f"{log.timestamp}: {log.message}")
    if log.details:
        print(f"Details: {log.details}")
```

## Contributing

### Adding New Providers

1. **Create Provider Service Class**:

```python
# core/integrations/my_new_service.py
from .services import BaseIntegrationService

class MyNewService(BaseIntegrationService):
    def test_connection(self):
        # Implement connection test
        pass
    
    def authenticate(self):
        # Implement authentication
        pass
    
    def my_service_method(self, param1, param2):
        # Implement service-specific methods
        pass
```

2. **Add to Factory**:

```python
# Update appropriate factory class
class MyServiceFactory:
    SERVICE_CLASSES = {
        'my_new_service': MyNewService,
        # ... existing services
    }
```

3. **Create Provider Configuration**:

```python
# Add to setup_integration_providers.py
{
    'name': 'my_new_service',
    'display_name': 'My New Service',
    'provider_type': 'my_type',
    'description': 'Description of the service',
    'base_url': 'https://api.mynewservice.com',
    'required_credentials': ['api_key'],
    'supported_features': ['feature1', 'feature2']
}
```

4. **Write Tests**:

```python
# Add tests to tests.py
class MyNewServiceTestCase(TestCase):
    def test_service_functionality(self):
        # Test your service
        pass
```

### Code Style

- Follow PEP 8 guidelines
- Use type hints where appropriate
- Write comprehensive docstrings
- Include error handling and logging
- Add unit tests for all new functionality

### Pull Request Process

1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Add tests and documentation
5. Run the test suite
6. Submit a pull request

## License

This integration system is part of the School ERP project and follows the same licensing terms.

## Support

For support and questions:

1. Check the troubleshooting section
2. Review integration logs and analytics
3. Test connections using management commands
4. Create an issue in the project repository

---

**Last Updated**: January 2025
**Version**: 1.0.0