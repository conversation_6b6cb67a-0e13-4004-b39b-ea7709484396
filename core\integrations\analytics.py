"""
Integration analytics service for School ERP
"""

import json
import logging
from datetime import datetime, timedelta
from django.utils import timezone
from django.db.models import Count, Avg, Sum, Q
from django.core.cache import cache
from .models import (
    Integration, IntegrationLog, IntegrationAnalytics, 
    IntegrationWebhook, IntegrationSchedule
)

logger = logging.getLogger(__name__)


class IntegrationAnalyticsService:
    """
    Service for generating integration analytics and reports
    """
    
    def __init__(self):
        self.cache_timeout = 3600  # 1 hour
    
    def update_daily_analytics(self, integration, date=None):
        """Update daily analytics for an integration"""
        if date is None:
            date = timezone.now().date()
        
        # Get or create analytics record
        analytics, created = IntegrationAnalytics.objects.get_or_create(
            integration=integration,
            date=date,
            defaults={
                'total_requests': 0,
                'successful_requests': 0,
                'failed_requests': 0,
                'avg_response_time_ms': 0,
                'min_response_time_ms': 0,
                'max_response_time_ms': 0,
                'data_sent_bytes': 0,
                'data_received_bytes': 0,
                'error_types': {},
                'hourly_distribution': {},
                'endpoint_usage': {}
            }
        )
        
        # Get logs for the date
        logs = IntegrationLog.objects.filter(
            integration=integration,
            timestamp__date=date,
            action_type='request'
        )
        
        # Calculate basic statistics
        total_requests = logs.count()
        successful_requests = logs.filter(status_code__lt=400).count()
        failed_requests = logs.filter(status_code__gte=400).count()
        
        # Calculate response times
        response_times = [
            log.duration_ms for log in logs 
            if log.duration_ms is not None
        ]
        
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
        else:
            avg_response_time = 0
            min_response_time = 0
            max_response_time = 0
        
        # Calculate data transfer (simplified estimation)
        data_sent = sum(
            len(json.dumps(log.request_data)) 
            for log in logs if log.request_data
        )
        data_received = sum(
            len(json.dumps(log.response_data)) 
            for log in logs if log.response_data
        )
        
        # Hourly distribution
        hourly_distribution = {}
        for log in logs:
            hour = str(log.timestamp.hour)
            hourly_distribution[hour] = hourly_distribution.get(hour, 0) + 1
        
        # Error types analysis
        error_types = {}
        error_logs = logs.filter(level='error')
        for log in error_logs:
            if log.status_code:
                error_type = f"HTTP_{log.status_code}"
            else:
                error_type = "CONNECTION_ERROR"
            error_types[error_type] = error_types.get(error_type, 0) + 1
        
        # Endpoint usage analysis
        endpoint_usage = {}
        for log in logs:
            # Extract endpoint from request details
            endpoint = self._extract_endpoint_from_log(log)
            if endpoint:
                endpoint_usage[endpoint] = endpoint_usage.get(endpoint, 0) + 1
        
        # Update analytics record
        analytics.total_requests = total_requests
        analytics.successful_requests = successful_requests
        analytics.failed_requests = failed_requests
        analytics.avg_response_time_ms = avg_response_time
        analytics.min_response_time_ms = min_response_time
        analytics.max_response_time_ms = max_response_time
        analytics.data_sent_bytes = data_sent
        analytics.data_received_bytes = data_received
        analytics.error_types = error_types
        analytics.hourly_distribution = hourly_distribution
        analytics.endpoint_usage = endpoint_usage
        analytics.save()
        
        return analytics
    
    def _extract_endpoint_from_log(self, log):
        """Extract endpoint from log message or details"""
        try:
            # Try to extract from message (format: "GET /endpoint")
            if ' /' in log.message:
                parts = log.message.split(' ')
                for part in parts:
                    if part.startswith('/'):
                        return part
            
            # Try to extract from details
            if log.details and 'endpoint' in log.details:
                return log.details['endpoint']
            
            return 'unknown'
        except:
            return 'unknown'
    
    def generate_integration_summary(self, integration, days=30):
        """Generate summary statistics for an integration"""
        cache_key = f"integration_summary_{integration.id}_{days}"
        cached_result = cache.get(cache_key)
        
        if cached_result:
            return cached_result
        
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)
        
        # Get analytics for the period
        analytics = IntegrationAnalytics.objects.filter(
            integration=integration,
            date__range=[start_date, end_date]
        ).order_by('date')
        
        # Aggregate data
        total_requests = sum(a.total_requests for a in analytics)
        total_successful = sum(a.successful_requests for a in analytics)
        total_failed = sum(a.failed_requests for a in analytics)
        total_data_sent = sum(a.data_sent_bytes for a in analytics)
        total_data_received = sum(a.data_received_bytes for a in analytics)
        
        # Calculate averages
        avg_response_time = 0
        if analytics:
            avg_response_time = sum(
                a.avg_response_time_ms for a in analytics if a.avg_response_time_ms
            ) / len([a for a in analytics if a.avg_response_time_ms])
        
        # Success rate
        success_rate = (total_successful / total_requests * 100) if total_requests > 0 else 0
        
        # Trend analysis
        daily_data = []
        for analytic in analytics:
            daily_data.append({
                'date': analytic.date.isoformat(),
                'requests': analytic.total_requests,
                'success_rate': analytic.success_rate,
                'avg_response_time': analytic.avg_response_time_ms,
                'errors': analytic.failed_requests
            })
        
        # Error summary
        error_summary = {}
        for analytic in analytics:
            for error_type, count in analytic.error_types.items():
                error_summary[error_type] = error_summary.get(error_type, 0) + count
        
        # Most used endpoints
        endpoint_summary = {}
        for analytic in analytics:
            for endpoint, count in analytic.endpoint_usage.items():
                endpoint_summary[endpoint] = endpoint_summary.get(endpoint, 0) + count
        
        # Sort endpoints by usage
        top_endpoints = sorted(
            endpoint_summary.items(), 
            key=lambda x: x[1], 
            reverse=True
        )[:10]
        
        summary = {
            'integration': {
                'id': str(integration.id),
                'name': integration.name,
                'provider': integration.provider.display_name,
                'status': integration.status,
                'created_at': integration.created_at.isoformat()
            },
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'days': days
            },
            'summary_stats': {
                'total_requests': total_requests,
                'successful_requests': total_successful,
                'failed_requests': total_failed,
                'success_rate': round(success_rate, 2),
                'avg_response_time_ms': round(avg_response_time, 2),
                'total_data_sent_bytes': total_data_sent,
                'total_data_received_bytes': total_data_received,
                'total_data_transfer_mb': round((total_data_sent + total_data_received) / 1024 / 1024, 2)
            },
            'daily_trends': daily_data,
            'error_analysis': {
                'total_errors': total_failed,
                'error_types': error_summary,
                'error_rate': round((total_failed / total_requests * 100) if total_requests > 0 else 0, 2)
            },
            'endpoint_usage': {
                'total_endpoints': len(endpoint_summary),
                'top_endpoints': [{'endpoint': ep, 'count': count} for ep, count in top_endpoints]
            }
        }
        
        # Cache the result
        cache.set(cache_key, summary, self.cache_timeout)
        
        return summary
    
    def generate_provider_comparison(self, provider_type, days=30):
        """Generate comparison report for integrations of the same provider type"""
        cache_key = f"provider_comparison_{provider_type}_{days}"
        cached_result = cache.get(cache_key)
        
        if cached_result:
            return cached_result
        
        # Get all integrations for the provider type
        integrations = Integration.objects.filter(
            provider__provider_type=provider_type,
            status='active'
        )
        
        if not integrations:
            return {'error': f'No active integrations found for provider type: {provider_type}'}
        
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)
        
        comparison_data = []
        
        for integration in integrations:
            # Get analytics for this integration
            analytics = IntegrationAnalytics.objects.filter(
                integration=integration,
                date__range=[start_date, end_date]
            )
            
            # Calculate totals
            total_requests = sum(a.total_requests for a in analytics)
            total_successful = sum(a.successful_requests for a in analytics)
            total_failed = sum(a.failed_requests for a in analytics)
            
            # Calculate averages
            avg_response_time = 0
            if analytics:
                response_times = [a.avg_response_time_ms for a in analytics if a.avg_response_time_ms]
                if response_times:
                    avg_response_time = sum(response_times) / len(response_times)
            
            success_rate = (total_successful / total_requests * 100) if total_requests > 0 else 0
            
            comparison_data.append({
                'integration_id': str(integration.id),
                'integration_name': integration.name,
                'provider_name': integration.provider.display_name,
                'total_requests': total_requests,
                'success_rate': round(success_rate, 2),
                'avg_response_time_ms': round(avg_response_time, 2),
                'total_errors': total_failed,
                'status': integration.status
            })
        
        # Sort by total requests
        comparison_data.sort(key=lambda x: x['total_requests'], reverse=True)
        
        # Calculate overall statistics
        overall_requests = sum(item['total_requests'] for item in comparison_data)
        overall_errors = sum(item['total_errors'] for item in comparison_data)
        overall_success_rate = ((overall_requests - overall_errors) / overall_requests * 100) if overall_requests > 0 else 0
        
        avg_response_times = [item['avg_response_time_ms'] for item in comparison_data if item['avg_response_time_ms']]
        overall_avg_response_time = sum(avg_response_times) / len(avg_response_times) if avg_response_times else 0
        
        result = {
            'provider_type': provider_type,
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'days': days
            },
            'overall_stats': {
                'total_integrations': len(comparison_data),
                'total_requests': overall_requests,
                'overall_success_rate': round(overall_success_rate, 2),
                'overall_avg_response_time_ms': round(overall_avg_response_time, 2),
                'total_errors': overall_errors
            },
            'integrations': comparison_data
        }
        
        # Cache the result
        cache.set(cache_key, result, self.cache_timeout)
        
        return result
    
    def generate_health_report(self, integration):
        """Generate health report for an integration"""
        cache_key = f"integration_health_{integration.id}"
        cached_result = cache.get(cache_key)
        
        if cached_result:
            return cached_result
        
        # Get recent data (last 7 days)
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=7)
        
        analytics = IntegrationAnalytics.objects.filter(
            integration=integration,
            date__range=[start_date, end_date]
        ).order_by('-date')
        
        # Calculate health metrics
        total_requests = sum(a.total_requests for a in analytics)
        total_errors = sum(a.failed_requests for a in analytics)
        
        # Success rate
        success_rate = ((total_requests - total_errors) / total_requests * 100) if total_requests > 0 else 0
        
        # Response time trend
        response_times = [a.avg_response_time_ms for a in analytics if a.avg_response_time_ms]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        # Error rate trend
        error_rates = []
        for analytic in analytics:
            if analytic.total_requests > 0:
                error_rate = (analytic.failed_requests / analytic.total_requests) * 100
                error_rates.append(error_rate)
        
        avg_error_rate = sum(error_rates) / len(error_rates) if error_rates else 0
        
        # Determine health status
        health_status = 'healthy'
        health_issues = []
        
        if success_rate < 95:
            health_status = 'warning' if success_rate >= 90 else 'critical'
            health_issues.append(f'Low success rate: {success_rate:.1f}%')
        
        if avg_response_time > 5000:  # 5 seconds
            health_status = 'warning' if health_status == 'healthy' else health_status
            health_issues.append(f'High response time: {avg_response_time:.0f}ms')
        
        if avg_error_rate > 10:
            health_status = 'critical'
            health_issues.append(f'High error rate: {avg_error_rate:.1f}%')
        
        # Recent errors
        recent_errors = IntegrationLog.objects.filter(
            integration=integration,
            level='error',
            timestamp__gte=timezone.now() - timedelta(hours=24)
        ).order_by('-timestamp')[:10]
        
        error_details = []
        for error in recent_errors:
            error_details.append({
                'timestamp': error.timestamp.isoformat(),
                'message': error.message,
                'details': error.details
            })
        
        # Uptime calculation (simplified)
        uptime_percentage = success_rate  # Simplified calculation
        
        health_report = {
            'integration': {
                'id': str(integration.id),
                'name': integration.name,
                'provider': integration.provider.display_name,
                'status': integration.status
            },
            'health_status': health_status,
            'health_score': round(success_rate, 1),
            'health_issues': health_issues,
            'metrics': {
                'success_rate': round(success_rate, 2),
                'avg_response_time_ms': round(avg_response_time, 2),
                'error_rate': round(avg_error_rate, 2),
                'uptime_percentage': round(uptime_percentage, 2),
                'total_requests_7d': total_requests,
                'total_errors_7d': total_errors
            },
            'recent_errors': error_details,
            'last_updated': timezone.now().isoformat()
        }
        
        # Cache for 15 minutes
        cache.set(cache_key, health_report, 900)
        
        return health_report
    
    def generate_usage_forecast(self, integration, days_ahead=30):
        """Generate usage forecast for an integration"""
        # Get historical data (last 90 days)
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=90)
        
        analytics = IntegrationAnalytics.objects.filter(
            integration=integration,
            date__range=[start_date, end_date]
        ).order_by('date')
        
        if len(analytics) < 7:  # Need at least a week of data
            return {'error': 'Insufficient data for forecasting'}
        
        # Simple linear trend calculation
        daily_requests = [a.total_requests for a in analytics]
        
        # Calculate trend using simple linear regression
        n = len(daily_requests)
        x_values = list(range(n))
        
        # Calculate slope and intercept
        sum_x = sum(x_values)
        sum_y = sum(daily_requests)
        sum_xy = sum(x * y for x, y in zip(x_values, daily_requests))
        sum_x2 = sum(x * x for x in x_values)
        
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
        intercept = (sum_y - slope * sum_x) / n
        
        # Generate forecast
        forecast_data = []
        for i in range(days_ahead):
            future_date = end_date + timedelta(days=i + 1)
            predicted_requests = max(0, int(slope * (n + i) + intercept))
            
            forecast_data.append({
                'date': future_date.isoformat(),
                'predicted_requests': predicted_requests
            })
        
        # Calculate confidence metrics
        actual_vs_predicted = []
        for i, actual in enumerate(daily_requests):
            predicted = slope * i + intercept
            actual_vs_predicted.append(abs(actual - predicted))
        
        avg_error = sum(actual_vs_predicted) / len(actual_vs_predicted)
        confidence = max(0, 100 - (avg_error / max(daily_requests) * 100))
        
        return {
            'integration': {
                'id': str(integration.id),
                'name': integration.name
            },
            'forecast_period': {
                'start_date': (end_date + timedelta(days=1)).isoformat(),
                'end_date': (end_date + timedelta(days=days_ahead)).isoformat(),
                'days': days_ahead
            },
            'historical_data': {
                'period_days': len(analytics),
                'avg_daily_requests': round(sum(daily_requests) / len(daily_requests), 1),
                'trend_slope': round(slope, 2)
            },
            'forecast': forecast_data,
            'confidence_metrics': {
                'confidence_percentage': round(confidence, 1),
                'avg_prediction_error': round(avg_error, 1),
                'trend_direction': 'increasing' if slope > 0 else 'decreasing' if slope < 0 else 'stable'
            }
        }
    
    def generate_cost_analysis(self, integration, days=30):
        """Generate cost analysis for an integration"""
        # This would integrate with provider pricing APIs
        # For now, we'll provide a framework
        
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)
        
        analytics = IntegrationAnalytics.objects.filter(
            integration=integration,
            date__range=[start_date, end_date]
        )
        
        total_requests = sum(a.total_requests for a in analytics)
        total_data_transfer = sum(a.data_sent_bytes + a.data_received_bytes for a in analytics)
        
        # Placeholder cost calculation (would be provider-specific)
        estimated_cost = self._calculate_estimated_cost(
            integration.provider.provider_type,
            total_requests,
            total_data_transfer
        )
        
        return {
            'integration': {
                'id': str(integration.id),
                'name': integration.name,
                'provider': integration.provider.display_name
            },
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'days': days
            },
            'usage_metrics': {
                'total_requests': total_requests,
                'total_data_transfer_mb': round(total_data_transfer / 1024 / 1024, 2),
                'avg_daily_requests': round(total_requests / days, 1)
            },
            'cost_analysis': estimated_cost
        }
    
    def _calculate_estimated_cost(self, provider_type, requests, data_transfer_bytes):
        """Calculate estimated cost based on provider type and usage"""
        # Placeholder pricing (would be updated with real provider pricing)
        pricing = {
            'payment': {
                'per_request': 0.01,  # $0.01 per request
                'per_mb': 0.001       # $0.001 per MB
            },
            'email': {
                'per_request': 0.0001,  # $0.0001 per email
                'per_mb': 0.0001
            },
            'sms': {
                'per_request': 0.05,    # $0.05 per SMS
                'per_mb': 0.001
            },
            'storage': {
                'per_request': 0.0004,  # $0.0004 per request
                'per_mb': 0.023         # $0.023 per GB per month
            }
        }
        
        provider_pricing = pricing.get(provider_type, pricing['email'])
        
        request_cost = requests * provider_pricing['per_request']
        data_cost = (data_transfer_bytes / 1024 / 1024) * provider_pricing['per_mb']
        total_cost = request_cost + data_cost
        
        return {
            'request_cost': round(request_cost, 4),
            'data_transfer_cost': round(data_cost, 4),
            'total_estimated_cost': round(total_cost, 4),
            'currency': 'USD',
            'note': 'Estimated costs based on typical provider pricing'
        }


# Global analytics service instance
integration_analytics_service = IntegrationAnalyticsService()