"""
Cloud storage integrations for School ERP
"""

import json
import boto3
import logging
from datetime import datetime, timedelta
from django.utils import timezone
from django.conf import settings
from django.core.files.storage import default_storage
from .services import BaseIntegrationService

logger = logging.getLogger(__name__)


class AmazonS3Storage(BaseIntegrationService):
    """
    Amazon S3 cloud storage integration
    """
    
    def __init__(self, integration):
        super().__init__(integration)
        self.s3_client = None
        self.bucket_name = integration.settings.get('bucket_name')
    
    def test_connection(self):
        """Test S3 connection"""
        try:
            self.authenticate()
            self.s3_client.head_bucket(Bucket=self.bucket_name)
            return True, "Connection successful"
        except Exception as e:
            return False, str(e)
    
    def authenticate(self):
        """Authenticate with AWS S3"""
        access_key = self.get_credential('access_key_id')
        secret_key = self.get_credential('secret_access_key')
        region = self.integration.settings.get('region', 'us-east-1')
        
        if not access_key or not secret_key:
            raise ValueError("AWS credentials not configured")
        
        if not self.bucket_name:
            raise ValueError("S3 bucket name not configured")
        
        self.s3_client = boto3.client(
            's3',
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            region_name=region
        )
        
        return True
    
    def upload_file(self, file_path, content, content_type=None, metadata=None):
        """Upload file to S3"""
        self.authenticate()
        
        try:
            extra_args = {}
            
            if content_type:
                extra_args['ContentType'] = content_type
            
            if metadata:
                extra_args['Metadata'] = metadata
            
            # Set public read if configured
            if self.integration.settings.get('public_read', False):
                extra_args['ACL'] = 'public-read'
            
            if hasattr(content, 'read'):
                # File-like object
                self.s3_client.upload_fileobj(
                    content,
                    self.bucket_name,
                    file_path,
                    ExtraArgs=extra_args
                )
            else:
                # Bytes content
                self.s3_client.put_object(
                    Bucket=self.bucket_name,
                    Key=file_path,
                    Body=content,
                    **extra_args
                )
            
            # Generate URL
            url = f"https://{self.bucket_name}.s3.amazonaws.com/{file_path}"
            
            return {
                'success': True,
                'url': url,
                'file_path': file_path,
                'bucket': self.bucket_name
            }
        
        except Exception as e:
            logger.error(f"Error uploading file to S3: {e}")
            return {'success': False, 'error': str(e)}
    
    def download_file(self, file_path):
        """Download file from S3"""
        self.authenticate()
        
        try:
            response = self.s3_client.get_object(
                Bucket=self.bucket_name,
                Key=file_path
            )
            
            return {
                'success': True,
                'content': response['Body'].read(),
                'content_type': response.get('ContentType'),
                'metadata': response.get('Metadata', {})
            }
        
        except Exception as e:
            logger.error(f"Error downloading file from S3: {e}")
            return {'success': False, 'error': str(e)}
    
    def delete_file(self, file_path):
        """Delete file from S3"""
        self.authenticate()
        
        try:
            self.s3_client.delete_object(
                Bucket=self.bucket_name,
                Key=file_path
            )
            
            return {'success': True, 'message': 'File deleted successfully'}
        
        except Exception as e:
            logger.error(f"Error deleting file from S3: {e}")
            return {'success': False, 'error': str(e)}
    
    def list_files(self, prefix=None, max_keys=1000):
        """List files in S3 bucket"""
        self.authenticate()
        
        try:
            kwargs = {
                'Bucket': self.bucket_name,
                'MaxKeys': max_keys
            }
            
            if prefix:
                kwargs['Prefix'] = prefix
            
            response = self.s3_client.list_objects_v2(**kwargs)
            
            files = []
            for obj in response.get('Contents', []):
                files.append({
                    'key': obj['Key'],
                    'size': obj['Size'],
                    'last_modified': obj['LastModified'],
                    'etag': obj['ETag'].strip('"')
                })
            
            return {
                'success': True,
                'files': files,
                'is_truncated': response.get('IsTruncated', False)
            }
        
        except Exception as e:
            logger.error(f"Error listing S3 files: {e}")
            return {'success': False, 'error': str(e)}
    
    def generate_presigned_url(self, file_path, expiration=3600, method='GET'):
        """Generate presigned URL for S3 object"""
        self.authenticate()
        
        try:
            operation = 'get_object' if method == 'GET' else 'put_object'
            
            url = self.s3_client.generate_presigned_url(
                operation,
                Params={'Bucket': self.bucket_name, 'Key': file_path},
                ExpiresIn=expiration
            )
            
            return {'success': True, 'url': url, 'expires_in': expiration}
        
        except Exception as e:
            logger.error(f"Error generating presigned URL: {e}")
            return {'success': False, 'error': str(e)}


class GoogleCloudStorage(BaseIntegrationService):
    """
    Google Cloud Storage integration
    """
    
    def __init__(self, integration):
        super().__init__(integration)
        self.storage_client = None
        self.bucket_name = integration.settings.get('bucket_name')
    
    def test_connection(self):
        """Test Google Cloud Storage connection"""
        try:
            self.authenticate()
            bucket = self.storage_client.bucket(self.bucket_name)
            bucket.exists()
            return True, "Connection successful"
        except Exception as e:
            return False, str(e)
    
    def authenticate(self):
        """Authenticate with Google Cloud Storage"""
        try:
            from google.cloud import storage
            from google.oauth2 import service_account
            
            service_account_key = self.get_credential('service_account_key')
            project_id = self.integration.settings.get('project_id')
            
            if not service_account_key or not project_id:
                raise ValueError("Google Cloud credentials not configured")
            
            if not self.bucket_name:
                raise ValueError("GCS bucket name not configured")
            
            # Parse service account key
            if isinstance(service_account_key, str):
                service_account_key = json.loads(service_account_key)
            
            credentials = service_account.Credentials.from_service_account_info(
                service_account_key
            )
            
            self.storage_client = storage.Client(
                project=project_id,
                credentials=credentials
            )
            
            return True
        
        except ImportError:
            raise ValueError("Google Cloud Storage library not installed")
    
    def upload_file(self, file_path, content, content_type=None, metadata=None):
        """Upload file to Google Cloud Storage"""
        self.authenticate()
        
        try:
            bucket = self.storage_client.bucket(self.bucket_name)
            blob = bucket.blob(file_path)
            
            if content_type:
                blob.content_type = content_type
            
            if metadata:
                blob.metadata = metadata
            
            if hasattr(content, 'read'):
                blob.upload_from_file(content)
            else:
                blob.upload_from_string(content)
            
            # Make public if configured
            if self.integration.settings.get('public_read', False):
                blob.make_public()
            
            return {
                'success': True,
                'url': blob.public_url if blob.public_url else blob.self_link,
                'file_path': file_path,
                'bucket': self.bucket_name
            }
        
        except Exception as e:
            logger.error(f"Error uploading file to GCS: {e}")
            return {'success': False, 'error': str(e)}
    
    def download_file(self, file_path):
        """Download file from Google Cloud Storage"""
        self.authenticate()
        
        try:
            bucket = self.storage_client.bucket(self.bucket_name)
            blob = bucket.blob(file_path)
            
            content = blob.download_as_bytes()
            
            return {
                'success': True,
                'content': content,
                'content_type': blob.content_type,
                'metadata': blob.metadata or {}
            }
        
        except Exception as e:
            logger.error(f"Error downloading file from GCS: {e}")
            return {'success': False, 'error': str(e)}
    
    def delete_file(self, file_path):
        """Delete file from Google Cloud Storage"""
        self.authenticate()
        
        try:
            bucket = self.storage_client.bucket(self.bucket_name)
            blob = bucket.blob(file_path)
            blob.delete()
            
            return {'success': True, 'message': 'File deleted successfully'}
        
        except Exception as e:
            logger.error(f"Error deleting file from GCS: {e}")
            return {'success': False, 'error': str(e)}
    
    def list_files(self, prefix=None, max_results=1000):
        """List files in Google Cloud Storage bucket"""
        self.authenticate()
        
        try:
            bucket = self.storage_client.bucket(self.bucket_name)
            blobs = bucket.list_blobs(prefix=prefix, max_results=max_results)
            
            files = []
            for blob in blobs:
                files.append({
                    'name': blob.name,
                    'size': blob.size,
                    'created': blob.time_created,
                    'updated': blob.updated,
                    'content_type': blob.content_type,
                    'etag': blob.etag
                })
            
            return {'success': True, 'files': files}
        
        except Exception as e:
            logger.error(f"Error listing GCS files: {e}")
            return {'success': False, 'error': str(e)}
    
    def generate_signed_url(self, file_path, expiration=3600, method='GET'):
        """Generate signed URL for Google Cloud Storage object"""
        self.authenticate()
        
        try:
            bucket = self.storage_client.bucket(self.bucket_name)
            blob = bucket.blob(file_path)
            
            expiration_time = timezone.now() + timedelta(seconds=expiration)
            
            url = blob.generate_signed_url(
                expiration=expiration_time,
                method=method
            )
            
            return {'success': True, 'url': url, 'expires_in': expiration}
        
        except Exception as e:
            logger.error(f"Error generating signed URL: {e}")
            return {'success': False, 'error': str(e)}


class AzureBlobStorage(BaseIntegrationService):
    """
    Azure Blob Storage integration
    """
    
    def __init__(self, integration):
        super().__init__(integration)
        self.blob_service_client = None
        self.container_name = integration.settings.get('container_name')
    
    def test_connection(self):
        """Test Azure Blob Storage connection"""
        try:
            self.authenticate()
            container_client = self.blob_service_client.get_container_client(
                self.container_name
            )
            container_client.get_container_properties()
            return True, "Connection successful"
        except Exception as e:
            return False, str(e)
    
    def authenticate(self):
        """Authenticate with Azure Blob Storage"""
        try:
            from azure.storage.blob import BlobServiceClient
            
            account_name = self.get_credential('account_name')
            account_key = self.get_credential('account_key')
            
            if not account_name or not account_key:
                raise ValueError("Azure storage credentials not configured")
            
            if not self.container_name:
                raise ValueError("Azure container name not configured")
            
            account_url = f"https://{account_name}.blob.core.windows.net"
            
            self.blob_service_client = BlobServiceClient(
                account_url=account_url,
                credential=account_key
            )
            
            return True
        
        except ImportError:
            raise ValueError("Azure Storage library not installed")
    
    def upload_file(self, file_path, content, content_type=None, metadata=None):
        """Upload file to Azure Blob Storage"""
        self.authenticate()
        
        try:
            blob_client = self.blob_service_client.get_blob_client(
                container=self.container_name,
                blob=file_path
            )
            
            kwargs = {}
            if content_type:
                kwargs['content_type'] = content_type
            if metadata:
                kwargs['metadata'] = metadata
            
            if hasattr(content, 'read'):
                blob_client.upload_blob(content, overwrite=True, **kwargs)
            else:
                blob_client.upload_blob(content, overwrite=True, **kwargs)
            
            # Generate URL
            url = blob_client.url
            
            return {
                'success': True,
                'url': url,
                'file_path': file_path,
                'container': self.container_name
            }
        
        except Exception as e:
            logger.error(f"Error uploading file to Azure: {e}")
            return {'success': False, 'error': str(e)}
    
    def download_file(self, file_path):
        """Download file from Azure Blob Storage"""
        self.authenticate()
        
        try:
            blob_client = self.blob_service_client.get_blob_client(
                container=self.container_name,
                blob=file_path
            )
            
            download_stream = blob_client.download_blob()
            content = download_stream.readall()
            
            properties = blob_client.get_blob_properties()
            
            return {
                'success': True,
                'content': content,
                'content_type': properties.content_settings.content_type,
                'metadata': properties.metadata or {}
            }
        
        except Exception as e:
            logger.error(f"Error downloading file from Azure: {e}")
            return {'success': False, 'error': str(e)}
    
    def delete_file(self, file_path):
        """Delete file from Azure Blob Storage"""
        self.authenticate()
        
        try:
            blob_client = self.blob_service_client.get_blob_client(
                container=self.container_name,
                blob=file_path
            )
            
            blob_client.delete_blob()
            
            return {'success': True, 'message': 'File deleted successfully'}
        
        except Exception as e:
            logger.error(f"Error deleting file from Azure: {e}")
            return {'success': False, 'error': str(e)}
    
    def list_files(self, prefix=None, max_results=1000):
        """List files in Azure Blob Storage container"""
        self.authenticate()
        
        try:
            container_client = self.blob_service_client.get_container_client(
                self.container_name
            )
            
            kwargs = {'results_per_page': max_results}
            if prefix:
                kwargs['name_starts_with'] = prefix
            
            blob_list = container_client.list_blobs(**kwargs)
            
            files = []
            for blob in blob_list:
                files.append({
                    'name': blob.name,
                    'size': blob.size,
                    'created': blob.creation_time,
                    'last_modified': blob.last_modified,
                    'content_type': blob.content_settings.content_type if blob.content_settings else None,
                    'etag': blob.etag
                })
            
            return {'success': True, 'files': files}
        
        except Exception as e:
            logger.error(f"Error listing Azure files: {e}")
            return {'success': False, 'error': str(e)}
    
    def generate_sas_url(self, file_path, expiration=3600, permissions='r'):
        """Generate SAS URL for Azure Blob Storage object"""
        self.authenticate()
        
        try:
            from azure.storage.blob import generate_blob_sas, BlobSasPermissions
            from datetime import datetime, timedelta
            
            account_name = self.get_credential('account_name')
            account_key = self.get_credential('account_key')
            
            # Set permissions
            if permissions == 'r':
                sas_permissions = BlobSasPermissions(read=True)
            elif permissions == 'w':
                sas_permissions = BlobSasPermissions(write=True)
            else:
                sas_permissions = BlobSasPermissions(read=True, write=True)
            
            # Generate SAS token
            sas_token = generate_blob_sas(
                account_name=account_name,
                container_name=self.container_name,
                blob_name=file_path,
                account_key=account_key,
                permission=sas_permissions,
                expiry=datetime.utcnow() + timedelta(seconds=expiration)
            )
            
            # Construct URL
            url = f"https://{account_name}.blob.core.windows.net/{self.container_name}/{file_path}?{sas_token}"
            
            return {'success': True, 'url': url, 'expires_in': expiration}
        
        except Exception as e:
            logger.error(f"Error generating SAS URL: {e}")
            return {'success': False, 'error': str(e)}


class DropboxStorage(BaseIntegrationService):
    """
    Dropbox storage integration
    """
    
    def __init__(self, integration):
        super().__init__(integration)
        self.dbx = None
    
    def test_connection(self):
        """Test Dropbox connection"""
        try:
            self.authenticate()
            self.dbx.users_get_current_account()
            return True, "Connection successful"
        except Exception as e:
            return False, str(e)
    
    def authenticate(self):
        """Authenticate with Dropbox"""
        try:
            import dropbox
            
            access_token = self.get_credential('access_token')
            
            if not access_token:
                raise ValueError("Dropbox access token not configured")
            
            self.dbx = dropbox.Dropbox(access_token)
            return True
        
        except ImportError:
            raise ValueError("Dropbox library not installed")
    
    def upload_file(self, file_path, content, mode='overwrite'):
        """Upload file to Dropbox"""
        self.authenticate()
        
        try:
            import dropbox
            
            # Ensure path starts with /
            if not file_path.startswith('/'):
                file_path = '/' + file_path
            
            write_mode = dropbox.files.WriteMode.overwrite
            if mode == 'add':
                write_mode = dropbox.files.WriteMode.add
            
            if hasattr(content, 'read'):
                content = content.read()
            
            metadata = self.dbx.files_upload(
                content,
                file_path,
                mode=write_mode,
                autorename=True
            )
            
            return {
                'success': True,
                'file_path': metadata.path_display,
                'id': metadata.id,
                'size': metadata.size
            }
        
        except Exception as e:
            logger.error(f"Error uploading file to Dropbox: {e}")
            return {'success': False, 'error': str(e)}
    
    def download_file(self, file_path):
        """Download file from Dropbox"""
        self.authenticate()
        
        try:
            # Ensure path starts with /
            if not file_path.startswith('/'):
                file_path = '/' + file_path
            
            metadata, response = self.dbx.files_download(file_path)
            
            return {
                'success': True,
                'content': response.content,
                'metadata': {
                    'name': metadata.name,
                    'size': metadata.size,
                    'modified': metadata.client_modified
                }
            }
        
        except Exception as e:
            logger.error(f"Error downloading file from Dropbox: {e}")
            return {'success': False, 'error': str(e)}
    
    def delete_file(self, file_path):
        """Delete file from Dropbox"""
        self.authenticate()
        
        try:
            # Ensure path starts with /
            if not file_path.startswith('/'):
                file_path = '/' + file_path
            
            self.dbx.files_delete_v2(file_path)
            
            return {'success': True, 'message': 'File deleted successfully'}
        
        except Exception as e:
            logger.error(f"Error deleting file from Dropbox: {e}")
            return {'success': False, 'error': str(e)}
    
    def list_files(self, folder_path='', recursive=False):
        """List files in Dropbox folder"""
        self.authenticate()
        
        try:
            # Ensure path starts with / or is empty
            if folder_path and not folder_path.startswith('/'):
                folder_path = '/' + folder_path
            
            if not folder_path:
                folder_path = ''
            
            result = self.dbx.files_list_folder(
                folder_path,
                recursive=recursive
            )
            
            files = []
            for entry in result.entries:
                if hasattr(entry, 'size'):  # It's a file
                    files.append({
                        'name': entry.name,
                        'path': entry.path_display,
                        'size': entry.size,
                        'modified': entry.client_modified,
                        'id': entry.id
                    })
            
            return {'success': True, 'files': files}
        
        except Exception as e:
            logger.error(f"Error listing Dropbox files: {e}")
            return {'success': False, 'error': str(e)}
    
    def create_shared_link(self, file_path, expires=None):
        """Create shared link for Dropbox file"""
        self.authenticate()
        
        try:
            import dropbox
            
            # Ensure path starts with /
            if not file_path.startswith('/'):
                file_path = '/' + file_path
            
            settings = dropbox.sharing.SharedLinkSettings(
                requested_visibility=dropbox.sharing.RequestedVisibility.public
            )
            
            if expires:
                settings.expires = expires
            
            shared_link = self.dbx.sharing_create_shared_link_with_settings(
                file_path,
                settings
            )
            
            return {
                'success': True,
                'url': shared_link.url,
                'expires': shared_link.expires
            }
        
        except Exception as e:
            logger.error(f"Error creating Dropbox shared link: {e}")
            return {'success': False, 'error': str(e)}


class CloudStorageFactory:
    """
    Factory class for creating cloud storage instances
    """
    
    STORAGE_CLASSES = {
        'amazon_s3': AmazonS3Storage,
        'google_cloud': GoogleCloudStorage,
        'azure_blob': AzureBlobStorage,
        'dropbox': DropboxStorage,
    }
    
    @classmethod
    def create_storage(cls, integration):
        """Create cloud storage instance"""
        provider_name = integration.provider.name.lower()
        storage_class = cls.STORAGE_CLASSES.get(provider_name)
        
        if not storage_class:
            raise ValueError(f"Unsupported cloud storage: {provider_name}")
        
        return storage_class(integration)
    
    @classmethod
    def get_supported_storages(cls):
        """Get list of supported cloud storage services"""
        return list(cls.STORAGE_CLASSES.keys())