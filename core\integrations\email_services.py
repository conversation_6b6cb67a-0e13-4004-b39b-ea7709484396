"""
Email service integrations for School ERP
"""

import json
import base64
import logging
from datetime import datetime
from django.utils import timezone
from django.conf import settings
from django.template.loader import render_to_string
from .services import BaseIntegrationService

logger = logging.getLogger(__name__)


class SendGridEmailService(BaseIntegrationService):
    """
    SendGrid email service integration
    """
    
    def test_connection(self):
        """Test SendGrid connection"""
        try:
            self.authenticate()
            response = self.make_request('GET', '/user/account')
            return True, "Connection successful"
        except Exception as e:
            return False, str(e)
    
    def authenticate(self):
        """Authenticate with SendGrid"""
        api_key = self.get_credential('api_key')
        if not api_key:
            raise ValueError("SendGrid API key not configured")
        
        self.session.headers['Authorization'] = f'Bearer {api_key}'
        return True
    
    def send_email(self, to_emails, subject, content, from_email=None, 
                   template_id=None, template_data=None, attachments=None):
        """Send email via SendGrid"""
        self.authenticate()
        
        # Prepare recipients
        if isinstance(to_emails, str):
            to_emails = [to_emails]
        
        personalizations = [{
            'to': [{'email': email} for email in to_emails]
        }]
        
        if template_data:
            personalizations[0]['dynamic_template_data'] = template_data
        
        # Prepare email payload
        payload = {
            'personalizations': personalizations,
            'from': {
                'email': from_email or self.integration.settings.get('default_from_email'),
                'name': self.integration.settings.get('from_name', 'School ERP')
            },
            'subject': subject
        }
        
        # Add content
        if template_id:
            payload['template_id'] = template_id
        else:
            payload['content'] = [{
                'type': 'text/html',
                'value': content
            }]
        
        # Add attachments
        if attachments:
            payload['attachments'] = []
            for attachment in attachments:
                payload['attachments'].append({
                    'content': base64.b64encode(attachment['content']).decode(),
                    'type': attachment.get('type', 'application/octet-stream'),
                    'filename': attachment['filename'],
                    'disposition': attachment.get('disposition', 'attachment')
                })
        
        response = self.make_request('POST', '/mail/send', json=payload)
        
        # SendGrid returns 202 for successful sends
        if response.status_code == 202:
            return {
                'success': True,
                'message_id': response.headers.get('X-Message-Id'),
                'status': 'sent'
            }
        
        return response.json()
    
    def get_email_stats(self, start_date=None, end_date=None):
        """Get email statistics from SendGrid"""
        self.authenticate()
        
        params = {}
        if start_date:
            params['start_date'] = start_date.strftime('%Y-%m-%d')
        if end_date:
            params['end_date'] = end_date.strftime('%Y-%m-%d')
        
        response = self.make_request('GET', '/stats', params=params)
        return response.json()
    
    def create_template(self, name, subject, html_content, text_content=None):
        """Create email template in SendGrid"""
        self.authenticate()
        
        payload = {
            'name': name,
            'generation': 'dynamic'
        }
        
        # Create template
        response = self.make_request('POST', '/templates', json=payload)
        template_data = response.json()
        template_id = template_data['id']
        
        # Create template version
        version_payload = {
            'template_id': template_id,
            'active': 1,
            'name': f"{name} Version 1",
            'subject': subject,
            'html_content': html_content,
            'plain_content': text_content or '',
            'generate_plain_content': True
        }
        
        version_response = self.make_request(
            'POST', 
            f'/templates/{template_id}/versions',
            json=version_payload
        )
        
        return {
            'template_id': template_id,
            'version_id': version_response.json()['id']
        }
    
    def verify_webhook_signature(self, payload, signature, public_key):
        """Verify SendGrid webhook signature"""
        try:
            # SendGrid uses elliptic curve cryptography for webhook verification
            # This would require additional cryptographic libraries
            # For now, return True (implement proper verification in production)
            return True
        except Exception as e:
            logger.error(f"Error verifying SendGrid webhook signature: {e}")
            return False


class MailgunEmailService(BaseIntegrationService):
    """
    Mailgun email service integration
    """
    
    def test_connection(self):
        """Test Mailgun connection"""
        try:
            self.authenticate()
            domain = self.get_credential('domain')
            response = self.make_request('GET', f'/domains/{domain}')
            return True, "Connection successful"
        except Exception as e:
            return False, str(e)
    
    def authenticate(self):
        """Authenticate with Mailgun"""
        api_key = self.get_credential('api_key')
        if not api_key:
            raise ValueError("Mailgun API key not configured")
        
        self.session.auth = ('api', api_key)
        return True
    
    def send_email(self, to_emails, subject, content, from_email=None, 
                   template=None, template_variables=None, attachments=None):
        """Send email via Mailgun"""
        self.authenticate()
        
        domain = self.get_credential('domain')
        if not domain:
            raise ValueError("Mailgun domain not configured")
        
        # Prepare recipients
        if isinstance(to_emails, str):
            to_emails = [to_emails]
        
        # Prepare form data
        data = {
            'from': from_email or self.integration.settings.get('default_from_email'),
            'to': to_emails,
            'subject': subject,
            'html': content
        }
        
        # Add template variables
        if template_variables:
            for key, value in template_variables.items():
                data[f'v:{key}'] = value
        
        # Add template
        if template:
            data['template'] = template
        
        # Prepare files for attachments
        files = []
        if attachments:
            for attachment in attachments:
                files.append(('attachment', (
                    attachment['filename'],
                    attachment['content'],
                    attachment.get('type', 'application/octet-stream')
                )))
        
        response = self.make_request(
            'POST', 
            f'/domains/{domain}/messages',
            data=data,
            files=files if files else None
        )
        
        return response.json()
    
    def get_email_stats(self, event=None, start_date=None, end_date=None):
        """Get email statistics from Mailgun"""
        self.authenticate()
        
        domain = self.get_credential('domain')
        params = {}
        
        if event:
            params['event'] = event
        if start_date:
            params['begin'] = start_date.strftime('%a, %d %b %Y %H:%M:%S GMT')
        if end_date:
            params['end'] = end_date.strftime('%a, %d %b %Y %H:%M:%S GMT')
        
        response = self.make_request(
            'GET', 
            f'/domains/{domain}/stats/total',
            params=params
        )
        return response.json()
    
    def create_template(self, name, description, template_content):
        """Create email template in Mailgun"""
        self.authenticate()
        
        domain = self.get_credential('domain')
        
        payload = {
            'name': name,
            'description': description,
            'template': template_content
        }
        
        response = self.make_request(
            'POST',
            f'/domains/{domain}/templates',
            data=payload
        )
        
        return response.json()
    
    def verify_webhook_signature(self, timestamp, token, signature, webhook_key):
        """Verify Mailgun webhook signature"""
        try:
            import hmac
            import hashlib
            
            expected_signature = hmac.new(
                webhook_key.encode('utf-8'),
                f'{timestamp}{token}'.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            return hmac.compare_digest(expected_signature, signature)
        except Exception as e:
            logger.error(f"Error verifying Mailgun webhook signature: {e}")
            return False


class AmazonSESEmailService(BaseIntegrationService):
    """
    Amazon SES email service integration
    """
    
    def __init__(self, integration):
        super().__init__(integration)
        self.region = integration.settings.get('region', 'us-east-1')
    
    def test_connection(self):
        """Test Amazon SES connection"""
        try:
            self.authenticate()
            response = self.make_request('POST', '/', data={
                'Action': 'GetSendQuota',
                'Version': '2010-12-01'
            })
            return True, "Connection successful"
        except Exception as e:
            return False, str(e)
    
    def authenticate(self):
        """Authenticate with Amazon SES"""
        access_key = self.get_credential('access_key_id')
        secret_key = self.get_credential('secret_access_key')
        
        if not access_key or not secret_key:
            raise ValueError("AWS credentials not configured")
        
        # AWS SES requires AWS Signature Version 4
        # This is a simplified implementation
        # In production, use boto3 or implement proper AWS signing
        
        self.session.headers.update({
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Amzn-Authorization': f'AWS4-HMAC-SHA256 Credential={access_key}'
        })
        
        return True
    
    def send_email(self, to_emails, subject, content, from_email=None, 
                   reply_to=None, return_path=None):
        """Send email via Amazon SES"""
        self.authenticate()
        
        # Prepare recipients
        if isinstance(to_emails, str):
            to_emails = [to_emails]
        
        # Prepare form data for SES API
        data = {
            'Action': 'SendEmail',
            'Version': '2010-12-01',
            'Source': from_email or self.integration.settings.get('default_from_email'),
            'Message.Subject.Data': subject,
            'Message.Body.Html.Data': content
        }
        
        # Add destinations
        for i, email in enumerate(to_emails, 1):
            data[f'Destination.ToAddresses.member.{i}'] = email
        
        # Add optional fields
        if reply_to:
            data['ReplyToAddresses.member.1'] = reply_to
        
        if return_path:
            data['ReturnPath'] = return_path
        
        response = self.make_request('POST', '/', data=data)
        
        # Parse XML response (simplified)
        if response.status_code == 200:
            return {
                'success': True,
                'message_id': 'extracted_from_xml_response',
                'status': 'sent'
            }
        
        return {'success': False, 'error': response.text}
    
    def get_send_quota(self):
        """Get sending quota from Amazon SES"""
        self.authenticate()
        
        data = {
            'Action': 'GetSendQuota',
            'Version': '2010-12-01'
        }
        
        response = self.make_request('POST', '/', data=data)
        return response.text  # Would need XML parsing in production
    
    def get_send_statistics(self):
        """Get sending statistics from Amazon SES"""
        self.authenticate()
        
        data = {
            'Action': 'GetSendStatistics',
            'Version': '2010-12-01'
        }
        
        response = self.make_request('POST', '/', data=data)
        return response.text  # Would need XML parsing in production


class SMTPEmailService(BaseIntegrationService):
    """
    Generic SMTP email service integration
    """
    
    def test_connection(self):
        """Test SMTP connection"""
        try:
            import smtplib
            
            host = self.get_credential('smtp_host')
            port = int(self.get_credential('smtp_port') or 587)
            username = self.get_credential('smtp_username')
            password = self.get_credential('smtp_password')
            use_tls = self.integration.settings.get('use_tls', True)
            
            server = smtplib.SMTP(host, port)
            if use_tls:
                server.starttls()
            
            if username and password:
                server.login(username, password)
            
            server.quit()
            return True, "Connection successful"
        except Exception as e:
            return False, str(e)
    
    def authenticate(self):
        """SMTP doesn't use HTTP authentication"""
        return True
    
    def send_email(self, to_emails, subject, content, from_email=None, attachments=None):
        """Send email via SMTP"""
        try:
            import smtplib
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart
            from email.mime.base import MIMEBase
            from email import encoders
            
            # Get SMTP settings
            host = self.get_credential('smtp_host')
            port = int(self.get_credential('smtp_port') or 587)
            username = self.get_credential('smtp_username')
            password = self.get_credential('smtp_password')
            use_tls = self.integration.settings.get('use_tls', True)
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = from_email or self.integration.settings.get('default_from_email')
            msg['Subject'] = subject
            
            # Add recipients
            if isinstance(to_emails, str):
                to_emails = [to_emails]
            msg['To'] = ', '.join(to_emails)
            
            # Add body
            msg.attach(MIMEText(content, 'html'))
            
            # Add attachments
            if attachments:
                for attachment in attachments:
                    part = MIMEBase('application', 'octet-stream')
                    part.set_payload(attachment['content'])
                    encoders.encode_base64(part)
                    part.add_header(
                        'Content-Disposition',
                        f'attachment; filename= {attachment["filename"]}'
                    )
                    msg.attach(part)
            
            # Send email
            server = smtplib.SMTP(host, port)
            if use_tls:
                server.starttls()
            
            if username and password:
                server.login(username, password)
            
            server.sendmail(msg['From'], to_emails, msg.as_string())
            server.quit()
            
            return {
                'success': True,
                'message_id': f'smtp_{int(timezone.now().timestamp())}',
                'status': 'sent'
            }
        
        except Exception as e:
            logger.error(f"Error sending SMTP email: {e}")
            return {'success': False, 'error': str(e)}


class EmailServiceFactory:
    """
    Factory class for creating email service instances
    """
    
    SERVICE_CLASSES = {
        'sendgrid': SendGridEmailService,
        'mailgun': MailgunEmailService,
        'amazon_ses': AmazonSESEmailService,
        'smtp': SMTPEmailService,
    }
    
    @classmethod
    def create_service(cls, integration):
        """Create email service instance"""
        provider_name = integration.provider.name.lower()
        service_class = cls.SERVICE_CLASSES.get(provider_name)
        
        if not service_class:
            raise ValueError(f"Unsupported email service: {provider_name}")
        
        return service_class(integration)
    
    @classmethod
    def get_supported_services(cls):
        """Get list of supported email services"""
        return list(cls.SERVICE_CLASSES.keys())