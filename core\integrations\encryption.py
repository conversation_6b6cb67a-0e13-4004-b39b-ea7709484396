"""
Encryption utilities for integration credentials
"""

import base64
import os
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


class IntegrationEncryption:
    """
    Encryption service for integration credentials
    """
    
    def __init__(self):
        self.key = self._get_or_create_key()
        self.cipher = Fernet(self.key)
    
    def _get_or_create_key(self):
        """Get or create encryption key"""
        # Try to get key from settings
        encryption_key = getattr(settings, 'INTEGRATION_ENCRYPTION_KEY', None)
        
        if encryption_key:
            return encryption_key.encode()
        
        # Try to get key from environment
        encryption_key = os.environ.get('INTEGRATION_ENCRYPTION_KEY')
        
        if encryption_key:
            return encryption_key.encode()
        
        # Generate key from secret key
        password = settings.SECRET_KEY.encode()
        salt = b'school_erp_integrations'  # Static salt for consistency
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        
        key = base64.urlsafe_b64encode(kdf.derive(password))
        return key
    
    def encrypt(self, plaintext):
        """Encrypt plaintext string"""
        if not plaintext:
            return ''
        
        try:
            if isinstance(plaintext, str):
                plaintext = plaintext.encode('utf-8')
            
            encrypted = self.cipher.encrypt(plaintext)
            return base64.urlsafe_b64encode(encrypted).decode('utf-8')
        
        except Exception as e:
            logger.error(f"Error encrypting data: {e}")
            raise
    
    def decrypt(self, ciphertext):
        """Decrypt ciphertext string"""
        if not ciphertext:
            return ''
        
        try:
            if isinstance(ciphertext, str):
                ciphertext = base64.urlsafe_b64decode(ciphertext.encode('utf-8'))
            
            decrypted = self.cipher.decrypt(ciphertext)
            return decrypted.decode('utf-8')
        
        except Exception as e:
            logger.error(f"Error decrypting data: {e}")
            raise
    
    def encrypt_dict(self, data_dict):
        """Encrypt all values in a dictionary"""
        encrypted_dict = {}
        
        for key, value in data_dict.items():
            if isinstance(value, (str, int, float)):
                encrypted_dict[key] = self.encrypt(str(value))
            else:
                encrypted_dict[key] = value
        
        return encrypted_dict
    
    def decrypt_dict(self, encrypted_dict):
        """Decrypt all values in a dictionary"""
        decrypted_dict = {}
        
        for key, value in encrypted_dict.items():
            if isinstance(value, str):
                try:
                    decrypted_dict[key] = self.decrypt(value)
                except:
                    # If decryption fails, assume it's not encrypted
                    decrypted_dict[key] = value
            else:
                decrypted_dict[key] = value
        
        return decrypted_dict