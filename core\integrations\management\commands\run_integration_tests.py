"""
Management command to run comprehensive integration tests
"""

import time
import json
from django.core.management.base import BaseCommand, CommandError
from django.test import TestCase
from django.test.utils import setup_test_environment, teardown_test_environment
from django.test.runner import DiscoverRunner
from django.utils import timezone
from django.contrib.auth import get_user_model
from core.integrations.models import (
    IntegrationProvider, Integration, IntegrationLog, 
    IntegrationAnalytics
)
from core.integrations.services import IntegrationManager
from core.integrations.analytics import integration_analytics_service

User = get_user_model()


class Command(BaseCommand):
    help = 'Run comprehensive integration tests'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--test-type',
            type=str,
            choices=['unit', 'integration', 'performance', 'security', 'all'],
            default='all',
            help='Type of tests to run'
        )
        parser.add_argument(
            '--provider-type',
            type=str,
            help='Test only specific provider type'
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed output'
        )
        parser.add_argument(
            '--generate-report',
            action='store_true',
            help='Generate detailed test report'
        )
        parser.add_argument(
            '--output-file',
            type=str,
            help='Output file for test report'
        )
    
    def handle(self, *args, **options):
        self.verbosity = options.get('verbosity', 1)
        self.verbose = options.get('verbose', False)
        self.test_type = options.get('test_type', 'all')
        
        self.stdout.write(
            self.style.SUCCESS(f'Running {self.test_type} integration tests...')
        )
        
        # Setup test environment
        setup_test_environment()
        
        try:
            # Run tests based on type
            if self.test_type == 'all':
                results = self.run_all_tests(options)
            elif self.test_type == 'unit':
                results = self.run_unit_tests(options)
            elif self.test_type == 'integration':
                results = self.run_integration_tests(options)
            elif self.test_type == 'performance':
                results = self.run_performance_tests(options)
            elif self.test_type == 'security':
                results = self.run_security_tests(options)
            
            # Generate report if requested
            if options.get('generate_report'):
                self.generate_test_report(results, options)
            
            # Print summary
            self.print_test_summary(results)
            
        finally:
            teardown_test_environment()
    
    def run_all_tests(self, options):
        """Run all types of tests"""
        results = {
            'unit': self.run_unit_tests(options),
            'integration': self.run_integration_tests(options),
            'performance': self.run_performance_tests(options),
            'security': self.run_security_tests(options)
        }
        return results
    
    def run_unit_tests(self, options):
        """Run unit tests"""
        self.stdout.write('\n--- Running Unit Tests ---')
        
        results = {
            'type': 'unit',
            'tests': [],
            'total': 0,
            'passed': 0,
            'failed': 0,
            'duration': 0
        }
        
        start_time = time.time()
        
        # Test model creation and validation
        results['tests'].append(self.test_model_creation())
        results['tests'].append(self.test_model_validation())
        results['tests'].append(self.test_encryption_decryption())
        results['tests'].append(self.test_service_initialization())
        results['tests'].append(self.test_factory_methods())
        
        results['duration'] = time.time() - start_time
        results['total'] = len(results['tests'])
        results['passed'] = len([t for t in results['tests'] if t['passed']])
        results['failed'] = results['total'] - results['passed']
        
        return results
    
    def run_integration_tests(self, options):
        """Run integration tests"""
        self.stdout.write('\n--- Running Integration Tests ---')
        
        results = {
            'type': 'integration',
            'tests': [],
            'total': 0,
            'passed': 0,
            'failed': 0,
            'duration': 0
        }
        
        start_time = time.time()
        
        # Test actual integrations if available
        integrations = Integration.objects.filter(is_enabled=True)
        
        if options.get('provider_type'):
            integrations = integrations.filter(
                provider__provider_type=options['provider_type']
            )
        
        for integration in integrations:
            results['tests'].append(self.test_integration_connection(integration))
            results['tests'].append(self.test_integration_analytics(integration))
        
        # Test API endpoints
        results['tests'].append(self.test_api_endpoints())
        results['tests'].append(self.test_webhook_handling())
        
        results['duration'] = time.time() - start_time
        results['total'] = len(results['tests'])
        results['passed'] = len([t for t in results['tests'] if t['passed']])
        results['failed'] = results['total'] - results['passed']
        
        return results
    
    def run_performance_tests(self, options):
        """Run performance tests"""
        self.stdout.write('\n--- Running Performance Tests ---')
        
        results = {
            'type': 'performance',
            'tests': [],
            'total': 0,
            'passed': 0,
            'failed': 0,
            'duration': 0
        }
        
        start_time = time.time()
        
        results['tests'].append(self.test_bulk_operations_performance())
        results['tests'].append(self.test_analytics_calculation_performance())
        results['tests'].append(self.test_concurrent_access_performance())
        results['tests'].append(self.test_memory_usage())
        
        results['duration'] = time.time() - start_time
        results['total'] = len(results['tests'])
        results['passed'] = len([t for t in results['tests'] if t['passed']])
        results['failed'] = results['total'] - results['passed']
        
        return results
    
    def run_security_tests(self, options):
        """Run security tests"""
        self.stdout.write('\n--- Running Security Tests ---')
        
        results = {
            'type': 'security',
            'tests': [],
            'total': 0,
            'passed': 0,
            'failed': 0,
            'duration': 0
        }
        
        start_time = time.time()
        
        results['tests'].append(self.test_credential_encryption())
        results['tests'].append(self.test_access_control())
        results['tests'].append(self.test_webhook_signature_verification())
        results['tests'].append(self.test_rate_limiting_awareness())
        results['tests'].append(self.test_sensitive_data_handling())
        
        results['duration'] = time.time() - start_time
        results['total'] = len(results['tests'])
        results['passed'] = len([t for t in results['tests'] if t['passed']])
        results['failed'] = results['total'] - results['passed']
        
        return results
    
    def test_model_creation(self):
        """Test model creation"""
        test_name = "Model Creation"
        start_time = time.time()
        
        try:
            # Test provider creation
            provider = IntegrationProvider.objects.create(
                name='test_provider_' + str(int(time.time())),
                display_name='Test Provider',
                provider_type='payment',
                base_url='https://api.test.com'
            )
            
            # Test integration creation
            user = User.objects.first()
            if not user:
                user = User.objects.create_user(
                    username='testuser_' + str(int(time.time())),
                    email='<EMAIL>',
                    password='testpass123'
                )
            
            integration = Integration.objects.create(
                provider=provider,
                name='Test Integration',
                created_by=user
            )
            
            # Cleanup
            integration.delete()
            provider.delete()
            
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✓ {test_name} ({duration:.3f}s)')
            
            return {
                'name': test_name,
                'passed': True,
                'duration': duration,
                'message': 'Models created successfully'
            }
        
        except Exception as e:
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✗ {test_name} ({duration:.3f}s): {str(e)}')
            
            return {
                'name': test_name,
                'passed': False,
                'duration': duration,
                'message': str(e)
            }
    
    def test_model_validation(self):
        """Test model validation"""
        test_name = "Model Validation"
        start_time = time.time()
        
        try:
            # Test required field validation
            try:
                IntegrationProvider.objects.create(
                    display_name='Test Provider',
                    provider_type='payment'
                    # Missing required 'name' field
                )
                raise Exception("Validation should have failed")
            except Exception as e:
                if "name" not in str(e).lower():
                    raise Exception("Wrong validation error")
            
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✓ {test_name} ({duration:.3f}s)')
            
            return {
                'name': test_name,
                'passed': True,
                'duration': duration,
                'message': 'Model validation working correctly'
            }
        
        except Exception as e:
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✗ {test_name} ({duration:.3f}s): {str(e)}')
            
            return {
                'name': test_name,
                'passed': False,
                'duration': duration,
                'message': str(e)
            }
    
    def test_encryption_decryption(self):
        """Test encryption and decryption"""
        test_name = "Encryption/Decryption"
        start_time = time.time()
        
        try:
            from core.integrations.encryption import IntegrationEncryption
            
            encryption = IntegrationEncryption()
            
            # Test string encryption
            plaintext = "test_secret_key_12345"
            ciphertext = encryption.encrypt(plaintext)
            decrypted = encryption.decrypt(ciphertext)
            
            if plaintext != decrypted:
                raise Exception("Decryption failed")
            
            if plaintext == ciphertext:
                raise Exception("Encryption failed")
            
            # Test dictionary encryption
            data = {'api_key': 'secret', 'timeout': 30}
            encrypted_data = encryption.encrypt_dict(data)
            decrypted_data = encryption.decrypt_dict(encrypted_data)
            
            if data != decrypted_data:
                raise Exception("Dictionary encryption/decryption failed")
            
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✓ {test_name} ({duration:.3f}s)')
            
            return {
                'name': test_name,
                'passed': True,
                'duration': duration,
                'message': 'Encryption/decryption working correctly'
            }
        
        except Exception as e:
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✗ {test_name} ({duration:.3f}s): {str(e)}')
            
            return {
                'name': test_name,
                'passed': False,
                'duration': duration,
                'message': str(e)
            }
    
    def test_service_initialization(self):
        """Test service initialization"""
        test_name = "Service Initialization"
        start_time = time.time()
        
        try:
            from core.integrations.services import BaseIntegrationService
            
            # Create test provider and integration
            provider = IntegrationProvider.objects.create(
                name='test_service_' + str(int(time.time())),
                display_name='Test Service Provider',
                provider_type='payment',
                base_url='https://api.test.com'
            )
            
            user = User.objects.first()
            if not user:
                user = User.objects.create_user(
                    username='testuser_service_' + str(int(time.time())),
                    email='<EMAIL>',
                    password='testpass123'
                )
            
            integration = Integration.objects.create(
                provider=provider,
                name='Test Service Integration',
                created_by=user
            )
            
            # Test service initialization
            service = BaseIntegrationService(integration)
            
            if service.integration != integration:
                raise Exception("Service integration not set correctly")
            
            if service.provider != provider:
                raise Exception("Service provider not set correctly")
            
            # Cleanup
            integration.delete()
            provider.delete()
            
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✓ {test_name} ({duration:.3f}s)')
            
            return {
                'name': test_name,
                'passed': True,
                'duration': duration,
                'message': 'Service initialization working correctly'
            }
        
        except Exception as e:
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✗ {test_name} ({duration:.3f}s): {str(e)}')
            
            return {
                'name': test_name,
                'passed': False,
                'duration': duration,
                'message': str(e)
            }
    
    def test_factory_methods(self):
        """Test factory methods"""
        test_name = "Factory Methods"
        start_time = time.time()
        
        try:
            from core.integrations.payment_gateways import PaymentGatewayFactory
            from core.integrations.email_services import EmailServiceFactory
            from core.integrations.sms_gateways import SMSGatewayFactory
            from core.integrations.cloud_storage import CloudStorageFactory
            
            # Test supported gateways/services
            payment_gateways = PaymentGatewayFactory.get_supported_gateways()
            email_services = EmailServiceFactory.get_supported_services()
            sms_gateways = SMSGatewayFactory.get_supported_gateways()
            storage_services = CloudStorageFactory.get_supported_storages()
            
            if not payment_gateways:
                raise Exception("No payment gateways supported")
            
            if not email_services:
                raise Exception("No email services supported")
            
            if not sms_gateways:
                raise Exception("No SMS gateways supported")
            
            if not storage_services:
                raise Exception("No storage services supported")
            
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✓ {test_name} ({duration:.3f}s)')
            
            return {
                'name': test_name,
                'passed': True,
                'duration': duration,
                'message': f'Factory methods working: {len(payment_gateways)} payment, {len(email_services)} email, {len(sms_gateways)} SMS, {len(storage_services)} storage'
            }
        
        except Exception as e:
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✗ {test_name} ({duration:.3f}s): {str(e)}')
            
            return {
                'name': test_name,
                'passed': False,
                'duration': duration,
                'message': str(e)
            }
    
    def test_integration_connection(self, integration):
        """Test integration connection"""
        test_name = f"Connection Test - {integration.name}"
        start_time = time.time()
        
        try:
            success, message = IntegrationManager.test_integration(integration)
            
            duration = time.time() - start_time
            
            if success:
                if self.verbose:
                    self.stdout.write(f'  ✓ {test_name} ({duration:.3f}s)')
                
                return {
                    'name': test_name,
                    'passed': True,
                    'duration': duration,
                    'message': message
                }
            else:
                if self.verbose:
                    self.stdout.write(f'  ✗ {test_name} ({duration:.3f}s): {message}')
                
                return {
                    'name': test_name,
                    'passed': False,
                    'duration': duration,
                    'message': message
                }
        
        except Exception as e:
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✗ {test_name} ({duration:.3f}s): {str(e)}')
            
            return {
                'name': test_name,
                'passed': False,
                'duration': duration,
                'message': str(e)
            }
    
    def test_integration_analytics(self, integration):
        """Test integration analytics"""
        test_name = f"Analytics Test - {integration.name}"
        start_time = time.time()
        
        try:
            # Update analytics
            analytics = integration_analytics_service.update_daily_analytics(integration)
            
            # Generate summary
            summary = integration_analytics_service.generate_integration_summary(
                integration, days=7
            )
            
            if not summary:
                raise Exception("Failed to generate analytics summary")
            
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✓ {test_name} ({duration:.3f}s)')
            
            return {
                'name': test_name,
                'passed': True,
                'duration': duration,
                'message': f'Analytics generated: {summary["summary_stats"]["total_requests"]} requests'
            }
        
        except Exception as e:
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✗ {test_name} ({duration:.3f}s): {str(e)}')
            
            return {
                'name': test_name,
                'passed': False,
                'duration': duration,
                'message': str(e)
            }
    
    def test_api_endpoints(self):
        """Test API endpoints"""
        test_name = "API Endpoints"
        start_time = time.time()
        
        try:
            from django.test import Client
            from django.contrib.auth import get_user_model
            
            User = get_user_model()
            
            # Create test user
            user = User.objects.create_user(
                username='api_test_user_' + str(int(time.time())),
                email='<EMAIL>',
                password='testpass123'
            )
            
            client = Client()
            client.force_login(user)
            
            # Test provider list endpoint
            response = client.get('/integrations/api/providers/')
            if response.status_code != 200:
                raise Exception(f"Provider list API failed: {response.status_code}")
            
            # Test integration list endpoint
            response = client.get('/integrations/api/integrations/')
            if response.status_code != 200:
                raise Exception(f"Integration list API failed: {response.status_code}")
            
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✓ {test_name} ({duration:.3f}s)')
            
            return {
                'name': test_name,
                'passed': True,
                'duration': duration,
                'message': 'API endpoints responding correctly'
            }
        
        except Exception as e:
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✗ {test_name} ({duration:.3f}s): {str(e)}')
            
            return {
                'name': test_name,
                'passed': False,
                'duration': duration,
                'message': str(e)
            }
    
    def test_webhook_handling(self):
        """Test webhook handling"""
        test_name = "Webhook Handling"
        start_time = time.time()
        
        try:
            from django.test import Client
            import json
            
            # Create test integration
            provider = IntegrationProvider.objects.create(
                name='webhook_test_' + str(int(time.time())),
                display_name='Webhook Test Provider',
                provider_type='payment',
                base_url='https://api.test.com'
            )
            
            user = User.objects.first()
            if not user:
                user = User.objects.create_user(
                    username='webhook_user_' + str(int(time.time())),
                    email='<EMAIL>',
                    password='testpass123'
                )
            
            integration = Integration.objects.create(
                provider=provider,
                name='Webhook Test Integration',
                created_by=user
            )
            
            client = Client()
            
            webhook_data = {
                'type': 'test_event',
                'data': {'test': 'value'}
            }
            
            response = client.post(
                f'/integrations/webhook/{integration.id}/',
                data=json.dumps(webhook_data),
                content_type='application/json'
            )
            
            if response.status_code != 200:
                raise Exception(f"Webhook handler failed: {response.status_code}")
            
            # Verify webhook was created
            from core.integrations.models import IntegrationWebhook
            webhook = IntegrationWebhook.objects.filter(integration=integration).first()
            
            if not webhook:
                raise Exception("Webhook record not created")
            
            # Cleanup
            integration.delete()
            provider.delete()
            
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✓ {test_name} ({duration:.3f}s)')
            
            return {
                'name': test_name,
                'passed': True,
                'duration': duration,
                'message': 'Webhook handling working correctly'
            }
        
        except Exception as e:
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✗ {test_name} ({duration:.3f}s): {str(e)}')
            
            return {
                'name': test_name,
                'passed': False,
                'duration': duration,
                'message': str(e)
            }
    
    def test_bulk_operations_performance(self):
        """Test bulk operations performance"""
        test_name = "Bulk Operations Performance"
        start_time = time.time()
        
        try:
            # Create test integration
            provider = IntegrationProvider.objects.create(
                name='bulk_test_' + str(int(time.time())),
                display_name='Bulk Test Provider',
                provider_type='payment',
                base_url='https://api.test.com'
            )
            
            user = User.objects.first()
            if not user:
                user = User.objects.create_user(
                    username='bulk_user_' + str(int(time.time())),
                    email='<EMAIL>',
                    password='testpass123'
                )
            
            integration = Integration.objects.create(
                provider=provider,
                name='Bulk Test Integration',
                created_by=user
            )
            
            # Test bulk log creation
            bulk_start = time.time()
            
            logs = []
            for i in range(1000):
                logs.append(IntegrationLog(
                    integration=integration,
                    level='info',
                    action_type='request',
                    message=f'Test log {i}',
                    status_code=200,
                    duration_ms=100
                ))
            
            IntegrationLog.objects.bulk_create(logs)
            
            bulk_duration = time.time() - bulk_start
            
            # Should complete within reasonable time
            if bulk_duration > 5.0:  # 5 seconds
                raise Exception(f"Bulk operation too slow: {bulk_duration:.2f}s")
            
            # Cleanup
            integration.delete()
            provider.delete()
            
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✓ {test_name} ({duration:.3f}s)')
            
            return {
                'name': test_name,
                'passed': True,
                'duration': duration,
                'message': f'Bulk created 1000 logs in {bulk_duration:.3f}s'
            }
        
        except Exception as e:
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✗ {test_name} ({duration:.3f}s): {str(e)}')
            
            return {
                'name': test_name,
                'passed': False,
                'duration': duration,
                'message': str(e)
            }
    
    def test_analytics_calculation_performance(self):
        """Test analytics calculation performance"""
        test_name = "Analytics Calculation Performance"
        start_time = time.time()
        
        try:
            # Use existing integration or create one
            integration = Integration.objects.first()
            if not integration:
                provider = IntegrationProvider.objects.create(
                    name='analytics_test_' + str(int(time.time())),
                    display_name='Analytics Test Provider',
                    provider_type='payment',
                    base_url='https://api.test.com'
                )
                
                user = User.objects.first()
                if not user:
                    user = User.objects.create_user(
                        username='analytics_user_' + str(int(time.time())),
                        email='<EMAIL>',
                        password='testpass123'
                    )
                
                integration = Integration.objects.create(
                    provider=provider,
                    name='Analytics Test Integration',
                    created_by=user
                )
            
            # Create test logs
            logs = []
            for i in range(1000):
                logs.append(IntegrationLog(
                    integration=integration,
                    level='info',
                    action_type='request',
                    message=f'Test log {i}',
                    status_code=200 if i % 10 != 0 else 400,
                    duration_ms=100 + (i % 500)
                ))
            
            IntegrationLog.objects.bulk_create(logs)
            
            # Test analytics calculation
            calc_start = time.time()
            analytics = integration_analytics_service.update_daily_analytics(integration)
            calc_duration = time.time() - calc_start
            
            # Should complete within reasonable time
            if calc_duration > 2.0:  # 2 seconds
                raise Exception(f"Analytics calculation too slow: {calc_duration:.2f}s")
            
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✓ {test_name} ({duration:.3f}s)')
            
            return {
                'name': test_name,
                'passed': True,
                'duration': duration,
                'message': f'Calculated analytics for 1000 logs in {calc_duration:.3f}s'
            }
        
        except Exception as e:
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✗ {test_name} ({duration:.3f}s): {str(e)}')
            
            return {
                'name': test_name,
                'passed': False,
                'duration': duration,
                'message': str(e)
            }
    
    def test_concurrent_access_performance(self):
        """Test concurrent access performance"""
        test_name = "Concurrent Access Performance"
        start_time = time.time()
        
        try:
            import threading
            
            # Use existing integration or create one
            integration = Integration.objects.first()
            if not integration:
                provider = IntegrationProvider.objects.create(
                    name='concurrent_test_' + str(int(time.time())),
                    display_name='Concurrent Test Provider',
                    provider_type='payment',
                    base_url='https://api.test.com'
                )
                
                user = User.objects.first()
                if not user:
                    user = User.objects.create_user(
                        username='concurrent_user_' + str(int(time.time())),
                        email='<EMAIL>',
                        password='testpass123'
                    )
                
                integration = Integration.objects.create(
                    provider=provider,
                    name='Concurrent Test Integration',
                    created_by=user
                )
            
            results = []
            errors = []
            
            def update_stats():
                try:
                    for _ in range(10):
                        integration.update_stats(success=True)
                    results.append('success')
                except Exception as e:
                    errors.append(str(e))
            
            # Create multiple threads
            threads = []
            for _ in range(5):
                thread = threading.Thread(target=update_stats)
                threads.append(thread)
            
            # Start all threads
            for thread in threads:
                thread.start()
            
            # Wait for all threads to complete
            for thread in threads:
                thread.join()
            
            if errors:
                raise Exception(f"Concurrent access errors: {errors}")
            
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✓ {test_name} ({duration:.3f}s)')
            
            return {
                'name': test_name,
                'passed': True,
                'duration': duration,
                'message': f'Concurrent access completed: {len(results)} threads'
            }
        
        except Exception as e:
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✗ {test_name} ({duration:.3f}s): {str(e)}')
            
            return {
                'name': test_name,
                'passed': False,
                'duration': duration,
                'message': str(e)
            }
    
    def test_memory_usage(self):
        """Test memory usage"""
        test_name = "Memory Usage"
        start_time = time.time()
        
        try:
            import psutil
            import os
            
            process = psutil.Process(os.getpid())
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # Perform memory-intensive operations
            large_data = []
            for i in range(10000):
                large_data.append({
                    'id': i,
                    'data': f'test_data_{i}' * 100
                })
            
            peak_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # Clear data
            del large_data
            
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            memory_increase = peak_memory - initial_memory
            memory_cleanup = peak_memory - final_memory
            
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✓ {test_name} ({duration:.3f}s)')
            
            return {
                'name': test_name,
                'passed': True,
                'duration': duration,
                'message': f'Memory: {initial_memory:.1f}MB → {peak_memory:.1f}MB → {final_memory:.1f}MB'
            }
        
        except ImportError:
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ~ {test_name} ({duration:.3f}s): psutil not available')
            
            return {
                'name': test_name,
                'passed': True,
                'duration': duration,
                'message': 'psutil not available, skipped'
            }
        
        except Exception as e:
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✗ {test_name} ({duration:.3f}s): {str(e)}')
            
            return {
                'name': test_name,
                'passed': False,
                'duration': duration,
                'message': str(e)
            }
    
    def test_credential_encryption(self):
        """Test credential encryption"""
        test_name = "Credential Encryption"
        start_time = time.time()
        
        try:
            from core.integrations.encryption import IntegrationEncryption
            
            encryption = IntegrationEncryption()
            
            # Test with various credential types
            credentials = {
                'api_key': 'sk_test_12345abcdef',
                'secret_key': 'very_secret_key_123',
                'token': 'bearer_token_xyz',
                'password': 'complex_password_!@#'
            }
            
            # Encrypt credentials
            encrypted_creds = encryption.encrypt_dict(credentials)
            
            # Verify encryption
            for key, value in credentials.items():
                if encrypted_creds[key] == value:
                    raise Exception(f"Credential '{key}' not encrypted")
            
            # Decrypt and verify
            decrypted_creds = encryption.decrypt_dict(encrypted_creds)
            
            if credentials != decrypted_creds:
                raise Exception("Decryption failed")
            
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✓ {test_name} ({duration:.3f}s)')
            
            return {
                'name': test_name,
                'passed': True,
                'duration': duration,
                'message': f'Encrypted/decrypted {len(credentials)} credentials'
            }
        
        except Exception as e:
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✗ {test_name} ({duration:.3f}s): {str(e)}')
            
            return {
                'name': test_name,
                'passed': False,
                'duration': duration,
                'message': str(e)
            }
    
    def test_access_control(self):
        """Test access control"""
        test_name = "Access Control"
        start_time = time.time()
        
        try:
            from django.test import Client
            
            # Create two users
            user1 = User.objects.create_user(
                username='user1_' + str(int(time.time())),
                email='<EMAIL>',
                password='testpass123'
            )
            
            user2 = User.objects.create_user(
                username='user2_' + str(int(time.time())),
                email='<EMAIL>',
                password='testpass123'
            )
            
            # Create integration for user1
            provider = IntegrationProvider.objects.create(
                name='access_test_' + str(int(time.time())),
                display_name='Access Test Provider',
                provider_type='payment',
                base_url='https://api.test.com'
            )
            
            integration = Integration.objects.create(
                provider=provider,
                name='User1 Integration',
                created_by=user1
            )
            
            # Test user1 can access their integration
            client = Client()
            client.force_login(user1)
            
            response = client.get('/integrations/api/integrations/')
            if response.status_code != 200:
                raise Exception("User1 cannot access integrations")
            
            if len(response.json()) != 1:
                raise Exception("User1 sees wrong number of integrations")
            
            # Test user2 cannot access user1's integration
            client.force_login(user2)
            
            response = client.get('/integrations/api/integrations/')
            if response.status_code != 200:
                raise Exception("User2 cannot access integrations endpoint")
            
            if len(response.json()) != 0:
                raise Exception("User2 can see other user's integrations")
            
            # Test direct access to integration
            response = client.get(f'/integrations/api/integrations/{integration.id}/')
            if response.status_code != 404:
                raise Exception("User2 can access other user's integration directly")
            
            # Cleanup
            integration.delete()
            provider.delete()
            user1.delete()
            user2.delete()
            
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✓ {test_name} ({duration:.3f}s)')
            
            return {
                'name': test_name,
                'passed': True,
                'duration': duration,
                'message': 'Access control working correctly'
            }
        
        except Exception as e:
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✗ {test_name} ({duration:.3f}s): {str(e)}')
            
            return {
                'name': test_name,
                'passed': False,
                'duration': duration,
                'message': str(e)
            }
    
    def test_webhook_signature_verification(self):
        """Test webhook signature verification"""
        test_name = "Webhook Signature Verification"
        start_time = time.time()
        
        try:
            from core.integrations.payment_gateways import StripePaymentGateway
            import hmac
            import hashlib
            
            # Create Stripe integration
            provider = IntegrationProvider.objects.create(
                name='stripe_sig_test_' + str(int(time.time())),
                display_name='Stripe Signature Test',
                provider_type='payment',
                base_url='https://api.stripe.com/v1'
            )
            
            user = User.objects.first()
            if not user:
                user = User.objects.create_user(
                    username='stripe_user_' + str(int(time.time())),
                    email='<EMAIL>',
                    password='testpass123'
                )
            
            integration = Integration.objects.create(
                provider=provider,
                name='Stripe Signature Test',
                created_by=user
            )
            
            gateway = StripePaymentGateway(integration)
            
            # Test signature verification
            payload = b'{"test": "data"}'
            secret = 'whsec_test_secret'
            
            # Create valid signature
            expected_signature = hmac.new(
                secret.encode('utf-8'),
                payload,
                hashlib.sha256
            ).hexdigest()
            
            # Test valid signature
            is_valid = gateway.verify_webhook_signature(
                payload,
                f'sha256={expected_signature}',
                secret
            )
            
            if not is_valid:
                raise Exception("Valid signature rejected")
            
            # Test invalid signature
            is_valid = gateway.verify_webhook_signature(
                payload,
                'sha256=invalid_signature',
                secret
            )
            
            if is_valid:
                raise Exception("Invalid signature accepted")
            
            # Cleanup
            integration.delete()
            provider.delete()
            
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✓ {test_name} ({duration:.3f}s)')
            
            return {
                'name': test_name,
                'passed': True,
                'duration': duration,
                'message': 'Webhook signature verification working correctly'
            }
        
        except Exception as e:
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✗ {test_name} ({duration:.3f}s): {str(e)}')
            
            return {
                'name': test_name,
                'passed': False,
                'duration': duration,
                'message': str(e)
            }
    
    def test_rate_limiting_awareness(self):
        """Test rate limiting awareness"""
        test_name = "Rate Limiting Awareness"
        start_time = time.time()
        
        try:
            # Create provider with rate limits
            provider = IntegrationProvider.objects.create(
                name='rate_limit_test_' + str(int(time.time())),
                display_name='Rate Limit Test Provider',
                provider_type='payment',
                base_url='https://api.test.com',
                rate_limits={
                    'requests_per_second': 10,
                    'requests_per_hour': 1000,
                    'requests_per_day': 10000
                }
            )
            
            user = User.objects.first()
            if not user:
                user = User.objects.create_user(
                    username='rate_user_' + str(int(time.time())),
                    email='<EMAIL>',
                    password='testpass123'
                )
            
            integration = Integration.objects.create(
                provider=provider,
                name='Rate Limit Test Integration',
                created_by=user
            )
            
            from core.integrations.services import BaseIntegrationService
            service = BaseIntegrationService(integration)
            
            # Verify rate limits are accessible
            rate_limits = service.provider.rate_limits
            
            if not rate_limits:
                raise Exception("Rate limits not accessible")
            
            if rate_limits['requests_per_second'] != 10:
                raise Exception("Rate limit value incorrect")
            
            # Cleanup
            integration.delete()
            provider.delete()
            
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✓ {test_name} ({duration:.3f}s)')
            
            return {
                'name': test_name,
                'passed': True,
                'duration': duration,
                'message': 'Rate limiting awareness working correctly'
            }
        
        except Exception as e:
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✗ {test_name} ({duration:.3f}s): {str(e)}')
            
            return {
                'name': test_name,
                'passed': False,
                'duration': duration,
                'message': str(e)
            }
    
    def test_sensitive_data_handling(self):
        """Test sensitive data handling"""
        test_name = "Sensitive Data Handling"
        start_time = time.time()
        
        try:
            # Create integration
            provider = IntegrationProvider.objects.create(
                name='sensitive_test_' + str(int(time.time())),
                display_name='Sensitive Data Test Provider',
                provider_type='payment',
                base_url='https://api.test.com'
            )
            
            user = User.objects.first()
            if not user:
                user = User.objects.create_user(
                    username='sensitive_user_' + str(int(time.time())),
                    email='<EMAIL>',
                    password='testpass123'
                )
            
            integration = Integration.objects.create(
                provider=provider,
                name='Sensitive Data Test Integration',
                created_by=user
            )
            
            from core.integrations.services import BaseIntegrationService
            service = BaseIntegrationService(integration)
            
            # Log activity with sensitive data
            service.log_activity(
                level='info',
                action_type='request',
                message='API request made',
                request_data={
                    'api_key': 'sk_test_secret_key_123',
                    'amount': 100,
                    'currency': 'USD',
                    'card_number': '****************'
                }
            )
            
            # Verify log was created
            log = IntegrationLog.objects.get(integration=integration)
            
            # Message should not contain sensitive data
            sensitive_terms = ['sk_test_secret_key_123', '****************']
            for term in sensitive_terms:
                if term in log.message:
                    raise Exception(f"Sensitive data '{term}' found in log message")
            
            # Request data should contain the data (encrypted at rest)
            if 'api_key' not in log.request_data:
                raise Exception("Request data not logged")
            
            # Cleanup
            integration.delete()
            provider.delete()
            
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✓ {test_name} ({duration:.3f}s)')
            
            return {
                'name': test_name,
                'passed': True,
                'duration': duration,
                'message': 'Sensitive data handling working correctly'
            }
        
        except Exception as e:
            duration = time.time() - start_time
            
            if self.verbose:
                self.stdout.write(f'  ✗ {test_name} ({duration:.3f}s): {str(e)}')
            
            return {
                'name': test_name,
                'passed': False,
                'duration': duration,
                'message': str(e)
            }
    
    def generate_test_report(self, results, options):
        """Generate detailed test report"""
        report_data = {
            'timestamp': timezone.now().isoformat(),
            'test_type': self.test_type,
            'results': results
        }
        
        # Calculate overall statistics
        if isinstance(results, dict) and 'unit' in results:
            # Multiple test types
            total_tests = sum(r['total'] for r in results.values())
            total_passed = sum(r['passed'] for r in results.values())
            total_failed = sum(r['failed'] for r in results.values())
            total_duration = sum(r['duration'] for r in results.values())
        else:
            # Single test type
            total_tests = results['total']
            total_passed = results['passed']
            total_failed = results['failed']
            total_duration = results['duration']
        
        report_data['summary'] = {
            'total_tests': total_tests,
            'passed': total_passed,
            'failed': total_failed,
            'success_rate': (total_passed / total_tests * 100) if total_tests > 0 else 0,
            'total_duration': total_duration
        }
        
        # Generate report
        report_json = json.dumps(report_data, indent=2)
        
        # Save to file if specified
        output_file = options.get('output_file')
        if output_file:
            with open(output_file, 'w') as f:
                f.write(report_json)
            
            self.stdout.write(f'Test report saved to: {output_file}')
        else:
            # Print to stdout
            self.stdout.write('\n--- TEST REPORT ---')
            self.stdout.write(report_json)
    
    def print_test_summary(self, results):
        """Print test summary"""
        self.stdout.write('\n' + '='*60)
        self.stdout.write('INTEGRATION TEST SUMMARY')
        self.stdout.write('='*60)
        
        if isinstance(results, dict) and 'unit' in results:
            # Multiple test types
            for test_type, result in results.items():
                self.stdout.write(f'\n{test_type.upper()} TESTS:')
                self.stdout.write(f'  Total: {result["total"]}')
                self.stdout.write(
                    self.style.SUCCESS(f'  Passed: {result["passed"]}')
                )
                if result['failed'] > 0:
                    self.stdout.write(
                        self.style.ERROR(f'  Failed: {result["failed"]}')
                    )
                self.stdout.write(f'  Duration: {result["duration"]:.3f}s')
            
            # Overall summary
            total_tests = sum(r['total'] for r in results.values())
            total_passed = sum(r['passed'] for r in results.values())
            total_failed = sum(r['failed'] for r in results.values())
            total_duration = sum(r['duration'] for r in results.values())
            
            self.stdout.write(f'\nOVERALL:')
            self.stdout.write(f'  Total Tests: {total_tests}')
            self.stdout.write(
                self.style.SUCCESS(f'  Total Passed: {total_passed}')
            )
            if total_failed > 0:
                self.stdout.write(
                    self.style.ERROR(f'  Total Failed: {total_failed}')
                )
            
            success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
            self.stdout.write(f'  Success Rate: {success_rate:.1f}%')
            self.stdout.write(f'  Total Duration: {total_duration:.3f}s')
        
        else:
            # Single test type
            self.stdout.write(f'Test Type: {results["type"].upper()}')
            self.stdout.write(f'Total Tests: {results["total"]}')
            self.stdout.write(
                self.style.SUCCESS(f'Passed: {results["passed"]}')
            )
            if results['failed'] > 0:
                self.stdout.write(
                    self.style.ERROR(f'Failed: {results["failed"]}')
                )
            
            success_rate = (results['passed'] / results['total'] * 100) if results['total'] > 0 else 0
            self.stdout.write(f'Success Rate: {success_rate:.1f}%')
            self.stdout.write(f'Duration: {results["duration"]:.3f}s')
            
            # Show failed tests
            if results['failed'] > 0:
                self.stdout.write('\nFAILED TESTS:')
                for test in results['tests']:
                    if not test['passed']:
                        self.stdout.write(
                            self.style.ERROR(f'  - {test["name"]}: {test["message"]}')
                        )
        
        self.stdout.write('='*60)