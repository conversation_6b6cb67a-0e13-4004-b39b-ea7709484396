"""
Management command to set up default integration providers
"""

from django.core.management.base import BaseCommand
from core.integrations.models import IntegrationProvider


class Command(BaseCommand):
    help = 'Set up default integration providers'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--update',
            action='store_true',
            help='Update existing providers'
        )
    
    def handle(self, *args, **options):
        self.stdout.write('Setting up default integration providers...')
        
        providers_data = self.get_default_providers()
        created_count = 0
        updated_count = 0
        
        for provider_data in providers_data:
            provider, created = IntegrationProvider.objects.get_or_create(
                name=provider_data['name'],
                defaults=provider_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created: {provider.display_name}')
                )
            elif options.get('update'):
                # Update existing provider
                for key, value in provider_data.items():
                    if key != 'name':  # Don't update the name
                        setattr(provider, key, value)
                provider.save()
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'Updated: {provider.display_name}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\nCompleted: {created_count} created, {updated_count} updated'
            )
        )
    
    def get_default_providers(self):
        """Get default provider configurations"""
        return [
            # Payment Gateways
            {
                'name': 'stripe',
                'display_name': 'Stripe',
                'provider_type': 'payment',
                'description': 'Stripe payment processing platform',
                'website_url': 'https://stripe.com',
                'documentation_url': 'https://stripe.com/docs/api',
                'base_url': 'https://api.stripe.com/v1',
                'api_version': 'v1',
                'required_credentials': ['api_key'],
                'optional_credentials': ['webhook_secret'],
                'supported_features': [
                    'payments', 'refunds', 'subscriptions', 'webhooks'
                ],
                'rate_limits': {
                    'requests_per_second': 100,
                    'requests_per_hour': 1000
                }
            },
            {
                'name': 'paypal',
                'display_name': 'PayPal',
                'provider_type': 'payment',
                'description': 'PayPal payment processing',
                'website_url': 'https://paypal.com',
                'documentation_url': 'https://developer.paypal.com/docs/api/',
                'base_url': 'https://api.paypal.com',
                'api_version': 'v2',
                'required_credentials': ['client_id', 'client_secret'],
                'optional_credentials': ['webhook_id'],
                'supported_features': ['payments', 'refunds', 'webhooks'],
                'rate_limits': {
                    'requests_per_second': 10,
                    'requests_per_hour': 1000
                }
            },
            {
                'name': 'razorpay',
                'display_name': 'Razorpay',
                'provider_type': 'payment',
                'description': 'Razorpay payment gateway for India',
                'website_url': 'https://razorpay.com',
                'documentation_url': 'https://razorpay.com/docs/api/',
                'base_url': 'https://api.razorpay.com/v1',
                'api_version': 'v1',
                'required_credentials': ['key_id', 'key_secret'],
                'optional_credentials': ['webhook_secret'],
                'supported_features': ['payments', 'refunds', 'webhooks'],
                'rate_limits': {
                    'requests_per_second': 20,
                    'requests_per_hour': 2000
                }
            },
            
            # Email Services
            {
                'name': 'sendgrid',
                'display_name': 'SendGrid',
                'provider_type': 'email',
                'description': 'SendGrid email delivery service',
                'website_url': 'https://sendgrid.com',
                'documentation_url': 'https://docs.sendgrid.com/api-reference',
                'base_url': 'https://api.sendgrid.com/v3',
                'api_version': 'v3',
                'required_credentials': ['api_key'],
                'optional_credentials': ['webhook_secret'],
                'supported_features': [
                    'transactional_email', 'templates', 'analytics', 'webhooks'
                ],
                'rate_limits': {
                    'emails_per_second': 10,
                    'emails_per_day': 100000
                }
            },
            {
                'name': 'mailgun',
                'display_name': 'Mailgun',
                'provider_type': 'email',
                'description': 'Mailgun email service',
                'website_url': 'https://mailgun.com',
                'documentation_url': 'https://documentation.mailgun.com/en/latest/api_reference.html',
                'base_url': 'https://api.mailgun.net/v3',
                'api_version': 'v3',
                'required_credentials': ['api_key', 'domain'],
                'optional_credentials': ['webhook_signing_key'],
                'supported_features': [
                    'transactional_email', 'templates', 'analytics', 'webhooks'
                ],
                'rate_limits': {
                    'emails_per_second': 10,
                    'emails_per_hour': 10000
                }
            },
            {
                'name': 'amazon_ses',
                'display_name': 'Amazon SES',
                'provider_type': 'email',
                'description': 'Amazon Simple Email Service',
                'website_url': 'https://aws.amazon.com/ses/',
                'documentation_url': 'https://docs.aws.amazon.com/ses/latest/APIReference/',
                'base_url': 'https://email.us-east-1.amazonaws.com',
                'api_version': '2010-12-01',
                'required_credentials': ['access_key_id', 'secret_access_key'],
                'optional_credentials': ['region'],
                'supported_features': ['transactional_email', 'templates'],
                'rate_limits': {
                    'emails_per_second': 14,
                    'emails_per_day': 200
                }
            },
            
            # SMS Gateways
            {
                'name': 'twilio',
                'display_name': 'Twilio',
                'provider_type': 'sms',
                'description': 'Twilio SMS and communication platform',
                'website_url': 'https://twilio.com',
                'documentation_url': 'https://www.twilio.com/docs/sms/api',
                'base_url': 'https://api.twilio.com/2010-04-01',
                'api_version': '2010-04-01',
                'required_credentials': ['account_sid', 'auth_token'],
                'optional_credentials': ['webhook_auth_token'],
                'supported_features': ['sms', 'mms', 'voice', 'webhooks'],
                'rate_limits': {
                    'messages_per_second': 1,
                    'messages_per_hour': 3600
                }
            },
            {
                'name': 'nexmo',
                'display_name': 'Vonage (Nexmo)',
                'provider_type': 'sms',
                'description': 'Vonage SMS API (formerly Nexmo)',
                'website_url': 'https://vonage.com',
                'documentation_url': 'https://developer.vonage.com/messaging/sms/overview',
                'base_url': 'https://rest.nexmo.com',
                'api_version': 'v1',
                'required_credentials': ['api_key', 'api_secret'],
                'optional_credentials': ['signature_secret'],
                'supported_features': ['sms', 'delivery_receipts', 'webhooks'],
                'rate_limits': {
                    'messages_per_second': 1,
                    'messages_per_hour': 3600
                }
            },
            {
                'name': 'whatsapp',
                'display_name': 'WhatsApp Business API',
                'provider_type': 'sms',
                'description': 'WhatsApp Business messaging platform',
                'website_url': 'https://business.whatsapp.com',
                'documentation_url': 'https://developers.facebook.com/docs/whatsapp',
                'base_url': 'https://graph.facebook.com/v17.0',
                'api_version': 'v17.0',
                'required_credentials': ['access_token', 'phone_number_id'],
                'optional_credentials': ['app_secret'],
                'supported_features': [
                    'text_messages', 'media_messages', 'templates', 'webhooks'
                ],
                'rate_limits': {
                    'messages_per_second': 20,
                    'messages_per_day': 1000
                }
            },
            
            # Cloud Storage
            {
                'name': 'amazon_s3',
                'display_name': 'Amazon S3',
                'provider_type': 'storage',
                'description': 'Amazon Simple Storage Service',
                'website_url': 'https://aws.amazon.com/s3/',
                'documentation_url': 'https://docs.aws.amazon.com/s3/latest/API/',
                'base_url': 'https://s3.amazonaws.com',
                'api_version': '2006-03-01',
                'required_credentials': ['access_key_id', 'secret_access_key'],
                'optional_credentials': ['region', 'bucket_name'],
                'supported_features': [
                    'file_upload', 'file_download', 'file_delete', 'presigned_urls'
                ],
                'rate_limits': {
                    'requests_per_second': 3500,
                    'requests_per_hour': 5500
                }
            },
            {
                'name': 'google_cloud',
                'display_name': 'Google Cloud Storage',
                'provider_type': 'storage',
                'description': 'Google Cloud Storage service',
                'website_url': 'https://cloud.google.com/storage',
                'documentation_url': 'https://cloud.google.com/storage/docs/json_api',
                'base_url': 'https://storage.googleapis.com',
                'api_version': 'v1',
                'required_credentials': ['service_account_key'],
                'optional_credentials': ['project_id', 'bucket_name'],
                'supported_features': [
                    'file_upload', 'file_download', 'file_delete', 'signed_urls'
                ],
                'rate_limits': {
                    'requests_per_second': 1000,
                    'requests_per_day': 5000000
                }
            },
            {
                'name': 'azure_blob',
                'display_name': 'Azure Blob Storage',
                'provider_type': 'storage',
                'description': 'Microsoft Azure Blob Storage',
                'website_url': 'https://azure.microsoft.com/en-us/services/storage/blobs/',
                'documentation_url': 'https://docs.microsoft.com/en-us/rest/api/storageservices/blob-service-rest-api',
                'base_url': 'https://{account}.blob.core.windows.net',
                'api_version': '2020-10-02',
                'required_credentials': ['account_name', 'account_key'],
                'optional_credentials': ['container_name'],
                'supported_features': [
                    'file_upload', 'file_download', 'file_delete', 'sas_urls'
                ],
                'rate_limits': {
                    'requests_per_second': 2000,
                    'requests_per_hour': 20000
                }
            }
        ]