"""
Management command to test all integrations
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from core.integrations.models import Integration, IntegrationProvider
from core.integrations.services import IntegrationManager
from core.integrations.analytics import integration_analytics_service


class Command(BaseCommand):
    help = 'Test all configured integrations'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--provider-type',
            type=str,
            help='Test only integrations of specific provider type'
        )
        parser.add_argument(
            '--integration-id',
            type=str,
            help='Test specific integration by ID'
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed output'
        )
        parser.add_argument(
            '--update-analytics',
            action='store_true',
            help='Update analytics after testing'
        )
    
    def handle(self, *args, **options):
        self.verbosity = options.get('verbosity', 1)
        self.verbose = options.get('verbose', False)
        
        # Get integrations to test
        integrations = self.get_integrations_to_test(options)
        
        if not integrations:
            self.stdout.write(
                self.style.WARNING('No integrations found to test')
            )
            return
        
        self.stdout.write(
            self.style.SUCCESS(f'Testing {len(integrations)} integration(s)...')
        )
        
        results = []
        
        for integration in integrations:
            result = self.test_integration(integration)
            results.append(result)
            
            if options.get('update_analytics'):
                self.update_integration_analytics(integration)
        
        # Summary
        self.print_summary(results)
    
    def get_integrations_to_test(self, options):
        """Get list of integrations to test based on options"""
        if options.get('integration_id'):
            try:
                return [Integration.objects.get(id=options['integration_id'])]
            except Integration.DoesNotExist:
                raise CommandError(f"Integration with ID {options['integration_id']} not found")
        
        queryset = Integration.objects.filter(is_enabled=True)
        
        if options.get('provider_type'):
            queryset = queryset.filter(provider__provider_type=options['provider_type'])
        
        return list(queryset.select_related('provider'))
    
    def test_integration(self, integration):
        """Test a single integration"""
        self.stdout.write(f'\nTesting: {integration.name} ({integration.provider.display_name})')
        
        start_time = timezone.now()
        
        try:
            success, message = IntegrationManager.test_integration(integration)
            
            duration = (timezone.now() - start_time).total_seconds()
            
            if success:
                self.stdout.write(
                    self.style.SUCCESS(f'  ✓ {message} ({duration:.2f}s)')
                )
                status = 'success'
            else:
                self.stdout.write(
                    self.style.ERROR(f'  ✗ {message} ({duration:.2f}s)')
                )
                status = 'failed'
            
            if self.verbose:
                self.print_integration_details(integration)
            
            return {
                'integration': integration,
                'success': success,
                'message': message,
                'duration': duration,
                'status': status
            }
        
        except Exception as e:
            duration = (timezone.now() - start_time).total_seconds()
            error_message = str(e)
            
            self.stdout.write(
                self.style.ERROR(f'  ✗ Error: {error_message} ({duration:.2f}s)')
            )
            
            return {
                'integration': integration,
                'success': False,
                'message': error_message,
                'duration': duration,
                'status': 'error'
            }
    
    def print_integration_details(self, integration):
        """Print detailed integration information"""
        self.stdout.write(f'    Provider: {integration.provider.display_name}')
        self.stdout.write(f'    Type: {integration.provider.get_provider_type_display()}')
        self.stdout.write(f'    Status: {integration.get_status_display()}')
        self.stdout.write(f'    Created: {integration.created_at.strftime("%Y-%m-%d %H:%M")}')
        self.stdout.write(f'    Total Requests: {integration.total_requests}')
        self.stdout.write(f'    Success Rate: {integration.success_rate:.1f}%')
        
        if integration.last_error:
            self.stdout.write(f'    Last Error: {integration.last_error}')
    
    def update_integration_analytics(self, integration):
        """Update analytics for integration"""
        try:
            analytics = integration_analytics_service.update_daily_analytics(integration)
            self.stdout.write(f'    Analytics updated: {analytics.total_requests} requests today')
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'    Failed to update analytics: {e}')
            )
    
    def print_summary(self, results):
        """Print test summary"""
        total = len(results)
        successful = len([r for r in results if r['success']])
        failed = total - successful
        
        self.stdout.write('\n' + '='*50)
        self.stdout.write('TEST SUMMARY')
        self.stdout.write('='*50)
        
        self.stdout.write(f'Total Integrations: {total}')
        self.stdout.write(
            self.style.SUCCESS(f'Successful: {successful}')
        )
        
        if failed > 0:
            self.stdout.write(
                self.style.ERROR(f'Failed: {failed}')
            )
        
        # Average duration
        avg_duration = sum(r['duration'] for r in results) / total if total > 0 else 0
        self.stdout.write(f'Average Duration: {avg_duration:.2f}s')
        
        # Failed integrations details
        if failed > 0:
            self.stdout.write('\nFAILED INTEGRATIONS:')
            for result in results:
                if not result['success']:
                    self.stdout.write(
                        self.style.ERROR(
                            f'  - {result["integration"].name}: {result["message"]}'
                        )
                    )