"""
Third-party integration models for School ERP
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.validators import URLValidator
from django.core.exceptions import ValidationError
import uuid
import json

User = get_user_model()


class IntegrationProvider(models.Model):
    """
    Model to store third-party integration providers
    """
    
    PROVIDER_TYPES = [
        ('payment', 'Payment Gateway'),
        ('email', 'Email Service'),
        ('sms', 'SMS Gateway'),
        ('storage', 'Cloud Storage'),
        ('analytics', 'Analytics Service'),
        ('communication', 'Communication Platform'),
        ('learning', 'Learning Management System'),
        ('identity', 'Identity Provider'),
        ('other', 'Other'),
    ]
    
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('maintenance', 'Under Maintenance'),
        ('deprecated', 'Deprecated'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, unique=True)
    display_name = models.CharField(max_length=150)
    provider_type = models.CharField(max_length=20, choices=PROVIDER_TYPES)
    description = models.TextField(blank=True)
    
    # Provider information
    website_url = models.URLField(blank=True)
    documentation_url = models.URLField(blank=True)
    support_url = models.URLField(blank=True)
    logo_url = models.URLField(blank=True)
    
    # Configuration
    base_url = models.URLField(blank=True, help_text="Base API URL for the provider")
    api_version = models.CharField(max_length=20, blank=True)
    required_credentials = models.JSONField(
        default=list,
        help_text="List of required credential fields"
    )
    optional_credentials = models.JSONField(
        default=list,
        help_text="List of optional credential fields"
    )
    
    # Features and capabilities
    supported_features = models.JSONField(
        default=list,
        help_text="List of features supported by this provider"
    )
    rate_limits = models.JSONField(
        default=dict,
        help_text="Rate limiting information"
    )
    
    # Status and metadata
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    is_enabled = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Integration Provider"
        verbose_name_plural = "Integration Providers"
        ordering = ['provider_type', 'name']
    
    def __str__(self):
        return f"{self.display_name} ({self.get_provider_type_display()})"


class Integration(models.Model):
    """
    Model to store configured integrations
    """
    
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('error', 'Error'),
        ('testing', 'Testing'),
        ('pending', 'Pending Setup'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    provider = models.ForeignKey(IntegrationProvider, on_delete=models.CASCADE, related_name='integrations')
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    
    # Configuration
    credentials = models.JSONField(
        default=dict,
        help_text="Encrypted credentials for the integration"
    )
    settings = models.JSONField(
        default=dict,
        help_text="Integration-specific settings"
    )
    
    # Status and monitoring
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    is_enabled = models.BooleanField(default=True)
    last_sync_at = models.DateTimeField(null=True, blank=True)
    last_error = models.TextField(blank=True)
    error_count = models.PositiveIntegerField(default=0)
    
    # Usage statistics
    total_requests = models.PositiveIntegerField(default=0)
    successful_requests = models.PositiveIntegerField(default=0)
    failed_requests = models.PositiveIntegerField(default=0)
    
    # Metadata
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_integrations')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Integration"
        verbose_name_plural = "Integrations"
        unique_together = ['provider', 'name']
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} ({self.provider.display_name})"
    
    @property
    def success_rate(self):
        """Calculate success rate percentage"""
        if self.total_requests == 0:
            return 0
        return (self.successful_requests / self.total_requests) * 100
    
    def update_stats(self, success=True):
        """Update request statistics"""
        self.total_requests += 1
        if success:
            self.successful_requests += 1
            self.error_count = 0  # Reset error count on success
        else:
            self.failed_requests += 1
            self.error_count += 1
        
        self.save(update_fields=['total_requests', 'successful_requests', 'failed_requests', 'error_count'])
    
    def test_connection(self):
        """Test the integration connection"""
        # This would be implemented by specific integration handlers
        return True, "Connection test not implemented"


class IntegrationLog(models.Model):
    """
    Model to log integration activities
    """
    
    LOG_LEVELS = [
        ('debug', 'Debug'),
        ('info', 'Info'),
        ('warning', 'Warning'),
        ('error', 'Error'),
        ('critical', 'Critical'),
    ]
    
    ACTION_TYPES = [
        ('request', 'API Request'),
        ('response', 'API Response'),
        ('sync', 'Data Sync'),
        ('webhook', 'Webhook'),
        ('error', 'Error'),
        ('config', 'Configuration Change'),
        ('test', 'Connection Test'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    integration = models.ForeignKey(Integration, on_delete=models.CASCADE, related_name='logs')
    
    # Log details
    level = models.CharField(max_length=10, choices=LOG_LEVELS)
    action_type = models.CharField(max_length=20, choices=ACTION_TYPES)
    message = models.TextField()
    details = models.JSONField(default=dict, blank=True)
    
    # Request/Response data
    request_data = models.JSONField(default=dict, blank=True)
    response_data = models.JSONField(default=dict, blank=True)
    status_code = models.PositiveIntegerField(null=True, blank=True)
    duration_ms = models.PositiveIntegerField(null=True, blank=True)
    
    # Context
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    
    # Metadata
    timestamp = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = "Integration Log"
        verbose_name_plural = "Integration Logs"
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['integration', 'timestamp']),
            models.Index(fields=['level', 'timestamp']),
            models.Index(fields=['action_type', 'timestamp']),
        ]
    
    def __str__(self):
        return f"{self.integration.name} - {self.get_level_display()} - {self.timestamp}"


class IntegrationWebhook(models.Model):
    """
    Model to handle incoming webhooks from integrations
    """
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processed', 'Processed'),
        ('failed', 'Failed'),
        ('ignored', 'Ignored'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    integration = models.ForeignKey(Integration, on_delete=models.CASCADE, related_name='webhooks')
    
    # Webhook data
    event_type = models.CharField(max_length=100)
    payload = models.JSONField()
    headers = models.JSONField(default=dict)
    
    # Processing
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    processed_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(blank=True)
    retry_count = models.PositiveIntegerField(default=0)
    
    # Security
    signature = models.CharField(max_length=255, blank=True)
    is_verified = models.BooleanField(default=False)
    source_ip = models.GenericIPAddressField(null=True, blank=True)
    
    # Metadata
    received_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = "Integration Webhook"
        verbose_name_plural = "Integration Webhooks"
        ordering = ['-received_at']
        indexes = [
            models.Index(fields=['integration', 'status']),
            models.Index(fields=['event_type', 'received_at']),
        ]
    
    def __str__(self):
        return f"{self.integration.name} - {self.event_type} - {self.received_at}"


class IntegrationMapping(models.Model):
    """
    Model to store field mappings between systems
    """
    
    MAPPING_TYPES = [
        ('field', 'Field Mapping'),
        ('value', 'Value Mapping'),
        ('transform', 'Data Transform'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    integration = models.ForeignKey(Integration, on_delete=models.CASCADE, related_name='mappings')
    
    # Mapping configuration
    mapping_type = models.CharField(max_length=20, choices=MAPPING_TYPES)
    source_field = models.CharField(max_length=200)
    target_field = models.CharField(max_length=200)
    
    # Transformation rules
    transform_rules = models.JSONField(
        default=dict,
        help_text="Rules for transforming data between systems"
    )
    
    # Validation
    is_required = models.BooleanField(default=False)
    validation_rules = models.JSONField(default=dict)
    
    # Metadata
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Integration Mapping"
        verbose_name_plural = "Integration Mappings"
        unique_together = ['integration', 'source_field', 'target_field']
        ordering = ['integration', 'source_field']
    
    def __str__(self):
        return f"{self.integration.name}: {self.source_field} -> {self.target_field}"


class IntegrationSchedule(models.Model):
    """
    Model to schedule integration tasks
    """
    
    SCHEDULE_TYPES = [
        ('sync', 'Data Sync'),
        ('backup', 'Backup'),
        ('cleanup', 'Cleanup'),
        ('report', 'Report Generation'),
        ('webhook', 'Webhook Processing'),
    ]
    
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('paused', 'Paused'),
        ('disabled', 'Disabled'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    integration = models.ForeignKey(Integration, on_delete=models.CASCADE, related_name='schedules')
    
    # Schedule configuration
    name = models.CharField(max_length=100)
    schedule_type = models.CharField(max_length=20, choices=SCHEDULE_TYPES)
    cron_expression = models.CharField(
        max_length=100,
        help_text="Cron expression for scheduling (e.g., '0 */6 * * *' for every 6 hours)"
    )
    
    # Task configuration
    task_config = models.JSONField(
        default=dict,
        help_text="Configuration for the scheduled task"
    )
    
    # Status and monitoring
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    last_run_at = models.DateTimeField(null=True, blank=True)
    next_run_at = models.DateTimeField(null=True, blank=True)
    run_count = models.PositiveIntegerField(default=0)
    success_count = models.PositiveIntegerField(default=0)
    failure_count = models.PositiveIntegerField(default=0)
    
    # Metadata
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Integration Schedule"
        verbose_name_plural = "Integration Schedules"
        ordering = ['integration', 'name']
    
    def __str__(self):
        return f"{self.integration.name} - {self.name}"
    
    @property
    def success_rate(self):
        """Calculate success rate percentage"""
        if self.run_count == 0:
            return 0
        return (self.success_count / self.run_count) * 100


class IntegrationAnalytics(models.Model):
    """
    Model to store integration analytics data
    """
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    integration = models.ForeignKey(Integration, on_delete=models.CASCADE, related_name='analytics')
    date = models.DateField()
    
    # Request statistics
    total_requests = models.PositiveIntegerField(default=0)
    successful_requests = models.PositiveIntegerField(default=0)
    failed_requests = models.PositiveIntegerField(default=0)
    
    # Performance metrics
    avg_response_time_ms = models.FloatField(default=0)
    min_response_time_ms = models.PositiveIntegerField(default=0)
    max_response_time_ms = models.PositiveIntegerField(default=0)
    
    # Data transfer
    data_sent_bytes = models.BigIntegerField(default=0)
    data_received_bytes = models.BigIntegerField(default=0)
    
    # Error analysis
    error_types = models.JSONField(default=dict)
    
    # Usage patterns
    hourly_distribution = models.JSONField(default=dict)
    endpoint_usage = models.JSONField(default=dict)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Integration Analytics"
        verbose_name_plural = "Integration Analytics"
        unique_together = ['integration', 'date']
        ordering = ['-date']
    
    def __str__(self):
        return f"{self.integration.name} - {self.date}"
    
    @property
    def success_rate(self):
        """Calculate success rate percentage"""
        if self.total_requests == 0:
            return 0
        return (self.successful_requests / self.total_requests) * 100