"""
Payment gateway integrations for School ERP
"""

import json
import hashlib
import hmac
import logging
from decimal import Decimal
from datetime import datetime
from django.utils import timezone
from django.conf import settings
from .services import BaseIntegrationService

logger = logging.getLogger(__name__)


class StripePaymentGateway(BaseIntegrationService):
    """
    Stripe payment gateway integration
    """
    
    def test_connection(self):
        """Test Stripe connection"""
        try:
            self.authenticate()
            response = self.make_request('GET', '/account')
            return True, "Connection successful"
        except Exception as e:
            return False, str(e)
    
    def authenticate(self):
        """Authenticate with Stripe"""
        api_key = self.get_credential('api_key')
        if not api_key:
            raise ValueError("Stripe API key not configured")
        
        self.session.headers['Authorization'] = f'Bearer {api_key}'
        return True
    
    def create_payment_intent(self, amount, currency='usd', description=None, customer_data=None):
        """Create Stripe payment intent"""
        self.authenticate()
        
        # Convert amount to cents
        amount_cents = int(Decimal(str(amount)) * 100)
        
        payload = {
            'amount': amount_cents,
            'currency': currency.lower(),
            'description': description or 'School fee payment',
            'automatic_payment_methods': {'enabled': True}
        }
        
        if customer_data:
            payload['metadata'] = customer_data
        
        response = self.make_request('POST', '/payment_intents', json=payload)
        return response.json()
    
    def confirm_payment_intent(self, payment_intent_id, payment_method_id):
        """Confirm Stripe payment intent"""
        self.authenticate()
        
        payload = {
            'payment_method': payment_method_id
        }
        
        response = self.make_request(
            'POST', 
            f'/payment_intents/{payment_intent_id}/confirm',
            json=payload
        )
        return response.json()
    
    def get_payment_intent(self, payment_intent_id):
        """Get Stripe payment intent"""
        self.authenticate()
        
        response = self.make_request('GET', f'/payment_intents/{payment_intent_id}')
        return response.json()
    
    def create_refund(self, payment_intent_id, amount=None, reason=None):
        """Create Stripe refund"""
        self.authenticate()
        
        payload = {
            'payment_intent': payment_intent_id
        }
        
        if amount:
            payload['amount'] = int(Decimal(str(amount)) * 100)
        
        if reason:
            payload['reason'] = reason
        
        response = self.make_request('POST', '/refunds', json=payload)
        return response.json()
    
    def create_customer(self, email, name=None, phone=None, metadata=None):
        """Create Stripe customer"""
        self.authenticate()
        
        payload = {
            'email': email
        }
        
        if name:
            payload['name'] = name
        
        if phone:
            payload['phone'] = phone
        
        if metadata:
            payload['metadata'] = metadata
        
        response = self.make_request('POST', '/customers', json=payload)
        return response.json()
    
    def get_customer(self, customer_id):
        """Get Stripe customer"""
        self.authenticate()
        
        response = self.make_request('GET', f'/customers/{customer_id}')
        return response.json()
    
    def verify_webhook_signature(self, payload, signature, endpoint_secret):
        """Verify Stripe webhook signature"""
        try:
            expected_signature = hmac.new(
                endpoint_secret.encode('utf-8'),
                payload,
                hashlib.sha256
            ).hexdigest()
            
            return hmac.compare_digest(f'sha256={expected_signature}', signature)
        except Exception as e:
            logger.error(f"Error verifying Stripe webhook signature: {e}")
            return False


class PayPalPaymentGateway(BaseIntegrationService):
    """
    PayPal payment gateway integration
    """
    
    def __init__(self, integration):
        super().__init__(integration)
        self.access_token = None
        self.token_expires_at = None
    
    def test_connection(self):
        """Test PayPal connection"""
        try:
            self.authenticate()
            return True, "Connection successful"
        except Exception as e:
            return False, str(e)
    
    def authenticate(self):
        """Authenticate with PayPal"""
        client_id = self.get_credential('client_id')
        client_secret = self.get_credential('client_secret')
        
        if not client_id or not client_secret:
            raise ValueError("PayPal credentials not configured")
        
        # Check if token is still valid
        if (self.access_token and self.token_expires_at and 
            timezone.now() < self.token_expires_at):
            self.session.headers['Authorization'] = f'Bearer {self.access_token}'
            return True
        
        # Get new access token
        auth_response = self.session.post(
            f"{self.provider.base_url}/v1/oauth2/token",
            auth=(client_id, client_secret),
            headers={'Accept': 'application/json'},
            data={'grant_type': 'client_credentials'}
        )
        
        auth_response.raise_for_status()
        auth_data = auth_response.json()
        
        self.access_token = auth_data['access_token']
        expires_in = auth_data.get('expires_in', 3600)
        self.token_expires_at = timezone.now() + timezone.timedelta(seconds=expires_in - 60)
        
        self.session.headers['Authorization'] = f'Bearer {self.access_token}'
        return True
    
    def create_order(self, amount, currency='USD', description=None, return_url=None, cancel_url=None):
        """Create PayPal order"""
        self.authenticate()
        
        payload = {
            'intent': 'CAPTURE',
            'purchase_units': [{
                'amount': {
                    'currency_code': currency,
                    'value': str(amount)
                },
                'description': description or 'School fee payment'
            }],
            'application_context': {
                'return_url': return_url or f"{settings.SITE_URL}/payments/paypal/return/",
                'cancel_url': cancel_url or f"{settings.SITE_URL}/payments/paypal/cancel/"
            }
        }
        
        response = self.make_request('POST', '/v2/checkout/orders', json=payload)
        return response.json()
    
    def capture_order(self, order_id):
        """Capture PayPal order"""
        self.authenticate()
        
        response = self.make_request('POST', f'/v2/checkout/orders/{order_id}/capture')
        return response.json()
    
    def get_order(self, order_id):
        """Get PayPal order"""
        self.authenticate()
        
        response = self.make_request('GET', f'/v2/checkout/orders/{order_id}')
        return response.json()
    
    def refund_capture(self, capture_id, amount=None, note=None):
        """Refund PayPal capture"""
        self.authenticate()
        
        payload = {}
        
        if amount:
            payload['amount'] = {
                'value': str(amount),
                'currency_code': 'USD'  # Should be dynamic based on original payment
            }
        
        if note:
            payload['note_to_payer'] = note
        
        response = self.make_request(
            'POST', 
            f'/v2/payments/captures/{capture_id}/refund',
            json=payload
        )
        return response.json()
    
    def verify_webhook_signature(self, headers, payload, webhook_id):
        """Verify PayPal webhook signature"""
        try:
            # PayPal webhook verification is more complex and requires
            # additional API calls to verify the certificate
            # This is a simplified version
            auth_algo = headers.get('PAYPAL-AUTH-ALGO')
            transmission_id = headers.get('PAYPAL-TRANSMISSION-ID')
            cert_id = headers.get('PAYPAL-CERT-ID')
            transmission_sig = headers.get('PAYPAL-TRANSMISSION-SIG')
            transmission_time = headers.get('PAYPAL-TRANSMISSION-TIME')
            
            if not all([auth_algo, transmission_id, cert_id, transmission_sig, transmission_time]):
                return False
            
            # In a real implementation, you would verify the certificate
            # and signature using PayPal's verification API
            return True
        except Exception as e:
            logger.error(f"Error verifying PayPal webhook signature: {e}")
            return False


class RazorpayPaymentGateway(BaseIntegrationService):
    """
    Razorpay payment gateway integration
    """
    
    def test_connection(self):
        """Test Razorpay connection"""
        try:
            self.authenticate()
            response = self.make_request('GET', '/orders?count=1')
            return True, "Connection successful"
        except Exception as e:
            return False, str(e)
    
    def authenticate(self):
        """Authenticate with Razorpay"""
        key_id = self.get_credential('key_id')
        key_secret = self.get_credential('key_secret')
        
        if not key_id or not key_secret:
            raise ValueError("Razorpay credentials not configured")
        
        self.session.auth = (key_id, key_secret)
        return True
    
    def create_order(self, amount, currency='INR', receipt=None, notes=None):
        """Create Razorpay order"""
        self.authenticate()
        
        # Convert amount to paise (smallest currency unit)
        amount_paise = int(Decimal(str(amount)) * 100)
        
        payload = {
            'amount': amount_paise,
            'currency': currency,
            'receipt': receipt or f'receipt_{int(timezone.now().timestamp())}',
            'notes': notes or {}
        }
        
        response = self.make_request('POST', '/orders', json=payload)
        return response.json()
    
    def get_order(self, order_id):
        """Get Razorpay order"""
        self.authenticate()
        
        response = self.make_request('GET', f'/orders/{order_id}')
        return response.json()
    
    def capture_payment(self, payment_id, amount):
        """Capture Razorpay payment"""
        self.authenticate()
        
        amount_paise = int(Decimal(str(amount)) * 100)
        
        payload = {
            'amount': amount_paise
        }
        
        response = self.make_request('POST', f'/payments/{payment_id}/capture', json=payload)
        return response.json()
    
    def get_payment(self, payment_id):
        """Get Razorpay payment"""
        self.authenticate()
        
        response = self.make_request('GET', f'/payments/{payment_id}')
        return response.json()
    
    def create_refund(self, payment_id, amount=None, notes=None):
        """Create Razorpay refund"""
        self.authenticate()
        
        payload = {}
        
        if amount:
            payload['amount'] = int(Decimal(str(amount)) * 100)
        
        if notes:
            payload['notes'] = notes
        
        response = self.make_request('POST', f'/payments/{payment_id}/refund', json=payload)
        return response.json()
    
    def verify_webhook_signature(self, payload, signature, webhook_secret):
        """Verify Razorpay webhook signature"""
        try:
            expected_signature = hmac.new(
                webhook_secret.encode('utf-8'),
                payload,
                hashlib.sha256
            ).hexdigest()
            
            return hmac.compare_digest(expected_signature, signature)
        except Exception as e:
            logger.error(f"Error verifying Razorpay webhook signature: {e}")
            return False


class PaymentGatewayFactory:
    """
    Factory class for creating payment gateway instances
    """
    
    GATEWAY_CLASSES = {
        'stripe': StripePaymentGateway,
        'paypal': PayPalPaymentGateway,
        'razorpay': RazorpayPaymentGateway,
    }
    
    @classmethod
    def create_gateway(cls, integration):
        """Create payment gateway instance"""
        provider_name = integration.provider.name.lower()
        gateway_class = cls.GATEWAY_CLASSES.get(provider_name)
        
        if not gateway_class:
            raise ValueError(f"Unsupported payment gateway: {provider_name}")
        
        return gateway_class(integration)
    
    @classmethod
    def get_supported_gateways(cls):
        """Get list of supported payment gateways"""
        return list(cls.GATEWAY_CLASSES.keys())