"""
Serializers for integration API
"""

from rest_framework import serializers
from .models import (
    IntegrationProvider, Integration, IntegrationLog, 
    IntegrationWebhook, IntegrationMapping, IntegrationAnalytics,
    IntegrationSchedule
)


class IntegrationProviderSerializer(serializers.ModelSerializer):
    """
    Serializer for integration providers
    """
    provider_type_display = serializers.CharField(
        source='get_provider_type_display', 
        read_only=True
    )
    status_display = serializers.CharField(
        source='get_status_display', 
        read_only=True
    )
    
    class Meta:
        model = IntegrationProvider
        fields = [
            'id', 'name', 'display_name', 'provider_type', 'provider_type_display',
            'description', 'website_url', 'documentation_url', 'support_url',
            'logo_url', 'base_url', 'api_version', 'required_credentials',
            'optional_credentials', 'supported_features', 'rate_limits',
            'status', 'status_display', 'is_enabled', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class IntegrationSerializer(serializers.ModelSerializer):
    """
    Serializer for integrations
    """
    provider_name = serializers.CharField(source='provider.display_name', read_only=True)
    provider_type = serializers.CharField(source='provider.provider_type', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    success_rate = serializers.FloatField(read_only=True)
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    
    # Exclude sensitive credentials from serialization
    credentials = serializers.JSONField(write_only=True, required=False)
    
    class Meta:
        model = Integration
        fields = [
            'id', 'provider', 'provider_name', 'provider_type', 'name', 'description',
            'credentials', 'settings', 'status', 'status_display', 'is_enabled',
            'last_sync_at', 'last_error', 'error_count', 'total_requests',
            'successful_requests', 'failed_requests', 'success_rate',
            'created_by', 'created_by_username', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'last_sync_at', 'last_error', 'error_count', 'total_requests',
            'successful_requests', 'failed_requests', 'created_at', 'updated_at'
        ]
    
    def validate_credentials(self, value):
        """Validate credentials against provider requirements"""
        if not value:
            return value
        
        provider = self.instance.provider if self.instance else None
        if not provider and 'provider' in self.initial_data:
            try:
                provider = IntegrationProvider.objects.get(
                    id=self.initial_data['provider']
                )
            except IntegrationProvider.DoesNotExist:
                raise serializers.ValidationError("Invalid provider")
        
        if provider:
            # Check required credentials
            required_creds = provider.required_credentials or []
            for cred in required_creds:
                if cred not in value:
                    raise serializers.ValidationError(
                        f"Required credential '{cred}' is missing"
                    )
        
        return value


class IntegrationLogSerializer(serializers.ModelSerializer):
    """
    Serializer for integration logs
    """
    integration_name = serializers.CharField(source='integration.name', read_only=True)
    level_display = serializers.CharField(source='get_level_display', read_only=True)
    action_type_display = serializers.CharField(source='get_action_type_display', read_only=True)
    user_username = serializers.CharField(source='user.username', read_only=True)
    
    class Meta:
        model = IntegrationLog
        fields = [
            'id', 'integration', 'integration_name', 'level', 'level_display',
            'action_type', 'action_type_display', 'message', 'details',
            'request_data', 'response_data', 'status_code', 'duration_ms',
            'user', 'user_username', 'ip_address', 'user_agent', 'timestamp'
        ]
        read_only_fields = ['id', 'timestamp']


class IntegrationWebhookSerializer(serializers.ModelSerializer):
    """
    Serializer for integration webhooks
    """
    integration_name = serializers.CharField(source='integration.name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = IntegrationWebhook
        fields = [
            'id', 'integration', 'integration_name', 'event_type', 'payload',
            'headers', 'status', 'status_display', 'processed_at', 'error_message',
            'retry_count', 'signature', 'is_verified', 'source_ip', 'received_at'
        ]
        read_only_fields = ['id', 'received_at']


class IntegrationMappingSerializer(serializers.ModelSerializer):
    """
    Serializer for integration mappings
    """
    integration_name = serializers.CharField(source='integration.name', read_only=True)
    mapping_type_display = serializers.CharField(source='get_mapping_type_display', read_only=True)
    
    class Meta:
        model = IntegrationMapping
        fields = [
            'id', 'integration', 'integration_name', 'mapping_type', 'mapping_type_display',
            'source_field', 'target_field', 'transform_rules', 'is_required',
            'validation_rules', 'description', 'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class IntegrationAnalyticsSerializer(serializers.ModelSerializer):
    """
    Serializer for integration analytics
    """
    integration_name = serializers.CharField(source='integration.name', read_only=True)
    success_rate = serializers.FloatField(read_only=True)
    
    class Meta:
        model = IntegrationAnalytics
        fields = [
            'id', 'integration', 'integration_name', 'date', 'total_requests',
            'successful_requests', 'failed_requests', 'success_rate',
            'avg_response_time_ms', 'min_response_time_ms', 'max_response_time_ms',
            'data_sent_bytes', 'data_received_bytes', 'error_types',
            'hourly_distribution', 'endpoint_usage', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class IntegrationScheduleSerializer(serializers.ModelSerializer):
    """
    Serializer for integration schedules
    """
    integration_name = serializers.CharField(source='integration.name', read_only=True)
    schedule_type_display = serializers.CharField(source='get_schedule_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    success_rate = serializers.FloatField(read_only=True)
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    
    class Meta:
        model = IntegrationSchedule
        fields = [
            'id', 'integration', 'integration_name', 'name', 'schedule_type',
            'schedule_type_display', 'cron_expression', 'task_config', 'status',
            'status_display', 'last_run_at', 'next_run_at', 'run_count',
            'success_count', 'failure_count', 'success_rate', 'created_by',
            'created_by_username', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'last_run_at', 'next_run_at', 'run_count', 'success_count',
            'failure_count', 'created_at', 'updated_at'
        ]


class IntegrationSummarySerializer(serializers.Serializer):
    """
    Serializer for integration summary data
    """
    integration = serializers.DictField()
    period = serializers.DictField()
    summary_stats = serializers.DictField()
    daily_trends = serializers.ListField()
    error_analysis = serializers.DictField()
    endpoint_usage = serializers.DictField()


class IntegrationHealthSerializer(serializers.Serializer):
    """
    Serializer for integration health report
    """
    integration = serializers.DictField()
    health_status = serializers.CharField()
    health_score = serializers.FloatField()
    health_issues = serializers.ListField()
    metrics = serializers.DictField()
    recent_errors = serializers.ListField()
    last_updated = serializers.CharField()


class IntegrationTestResultSerializer(serializers.Serializer):
    """
    Serializer for integration test results
    """
    success = serializers.BooleanField()
    message = serializers.CharField()
    status = serializers.CharField()
    tested_at = serializers.CharField()


class ProviderComparisonSerializer(serializers.Serializer):
    """
    Serializer for provider comparison data
    """
    provider_type = serializers.CharField()
    period = serializers.DictField()
    overall_stats = serializers.DictField()
    integrations = serializers.ListField()


class UsageForecastSerializer(serializers.Serializer):
    """
    Serializer for usage forecast data
    """
    integration = serializers.DictField()
    forecast_period = serializers.DictField()
    historical_data = serializers.DictField()
    forecast = serializers.ListField()
    confidence_metrics = serializers.DictField()


class CostAnalysisSerializer(serializers.Serializer):
    """
    Serializer for cost analysis data
    """
    integration = serializers.DictField()
    period = serializers.DictField()
    usage_metrics = serializers.DictField()
    cost_analysis = serializers.DictField()