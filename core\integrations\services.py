"""
Integration services for School ERP third-party integrations
"""

import json
import logging
import requests
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from django.utils import timezone
from django.conf import settings
from django.core.cache import cache
from .models import Integration, IntegrationLog, IntegrationAnalytics
from .encryption import IntegrationEncryption

logger = logging.getLogger(__name__)


class BaseIntegrationService(ABC):
    """
    Base class for all integration services
    """
    
    def __init__(self, integration):
        self.integration = integration
        self.provider = integration.provider
        self.encryption = IntegrationEncryption()
        self.session = requests.Session()
        self._setup_session()
    
    def _setup_session(self):
        """Setup HTTP session with common headers and timeouts"""
        version = getattr(settings, 'VERSION', '1.0.0')
        self.session.headers.update({
            'User-Agent': f'SchoolERP-Integration/{version}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        self.session.timeout = 30
    
    @abstractmethod
    def test_connection(self):
        """Test the integration connection"""
        pass
    
    @abstractmethod
    def authenticate(self):
        """Authenticate with the service"""
        pass
    
    def get_credential(self, key):
        """Get decrypted credential"""
        encrypted_value = self.integration.credentials.get(key)
        if encrypted_value:
            return self.encryption.decrypt(encrypted_value)
        return None
    
    def set_credential(self, key, value):
        """Set encrypted credential"""
        encrypted_value = self.encryption.encrypt(value)
        self.integration.credentials[key] = encrypted_value
        self.integration.save()
    
    def log_activity(self, level, action_type, message, **kwargs):
        """Log integration activity"""
        IntegrationLog.objects.create(
            integration=self.integration,
            level=level,
            action_type=action_type,
            message=message,
            details=kwargs.get('details', {}),
            request_data=kwargs.get('request_data', {}),
            response_data=kwargs.get('response_data', {}),
            status_code=kwargs.get('status_code'),
            duration_ms=kwargs.get('duration_ms'),
            user=kwargs.get('user'),
            ip_address=kwargs.get('ip_address'),
            user_agent=kwargs.get('user_agent')
        )
    
    def make_request(self, method, endpoint, **kwargs):
        """Make HTTP request with logging and error handling"""
        url = f"{self.provider.base_url.rstrip('/')}/{endpoint.lstrip('/')}"
        start_time = timezone.now()
        
        try:
            response = self.session.request(method, url, **kwargs)
            duration_ms = int((timezone.now() - start_time).total_seconds() * 1000)
            
            # Log request
            self.log_activity(
                level='info',
                action_type='request',
                message=f"{method} {endpoint}",
                request_data=kwargs.get('json', {}),
                response_data=response.json() if response.content else {},
                status_code=response.status_code,
                duration_ms=duration_ms
            )
            
            # Update statistics
            self.integration.update_stats(success=response.status_code < 400)
            
            response.raise_for_status()
            return response
        
        except requests.exceptions.RequestException as e:
            duration_ms = int((timezone.now() - start_time).total_seconds() * 1000)
            
            # Log error
            self.log_activity(
                level='error',
                action_type='error',
                message=f"Request failed: {str(e)}",
                details={'error': str(e), 'url': url},
                duration_ms=duration_ms
            )
            
            # Update statistics
            self.integration.update_stats(success=False)
            self.integration.last_error = str(e)
            self.integration.save()
            
            raise


class PaymentGatewayService(BaseIntegrationService):
    """
    Service for payment gateway integrations
    """
    
    def test_connection(self):
        """Test payment gateway connection"""
        try:
            response = self.make_request('GET', '/health')
            return True, "Connection successful"
        except Exception as e:
            return False, str(e)
    
    def authenticate(self):
        """Authenticate with payment gateway"""
        api_key = self.get_credential('api_key')
        if not api_key:
            raise ValueError("API key not configured")
        
        self.session.headers['Authorization'] = f'Bearer {api_key}'
        return True
    
    def create_payment(self, amount, currency, description, customer_data=None):
        """Create a payment intent"""
        self.authenticate()
        
        payload = {
            'amount': amount,
            'currency': currency,
            'description': description,
            'customer': customer_data or {}
        }
        
        response = self.make_request('POST', '/payments', json=payload)
        return response.json()
    
    def get_payment_status(self, payment_id):
        """Get payment status"""
        self.authenticate()
        
        response = self.make_request('GET', f'/payments/{payment_id}')
        return response.json()
    
    def refund_payment(self, payment_id, amount=None, reason=None):
        """Refund a payment"""
        self.authenticate()
        
        payload = {
            'payment_id': payment_id,
            'amount': amount,
            'reason': reason or 'Requested by customer'
        }
        
        response = self.make_request('POST', '/refunds', json=payload)
        return response.json()


class EmailServiceIntegration(BaseIntegrationService):
    """
    Service for email service integrations
    """
    
    def test_connection(self):
        """Test email service connection"""
        try:
            self.authenticate()
            response = self.make_request('GET', '/account')
            return True, "Connection successful"
        except Exception as e:
            return False, str(e)
    
    def authenticate(self):
        """Authenticate with email service"""
        api_key = self.get_credential('api_key')
        if not api_key:
            raise ValueError("API key not configured")
        
        self.session.headers['Authorization'] = f'Bearer {api_key}'
        return True
    
    def send_email(self, to_emails, subject, content, from_email=None, template_id=None):
        """Send email"""
        self.authenticate()
        
        payload = {
            'to': to_emails if isinstance(to_emails, list) else [to_emails],
            'subject': subject,
            'content': content,
            'from': from_email or self.integration.settings.get('default_from_email')
        }
        
        if template_id:
            payload['template_id'] = template_id
        
        response = self.make_request('POST', '/send', json=payload)
        return response.json()
    
    def get_email_status(self, message_id):
        """Get email delivery status"""
        self.authenticate()
        
        response = self.make_request('GET', f'/messages/{message_id}')
        return response.json()
    
    def create_template(self, name, subject, html_content, text_content=None):
        """Create email template"""
        self.authenticate()
        
        payload = {
            'name': name,
            'subject': subject,
            'html_content': html_content,
            'text_content': text_content or ''
        }
        
        response = self.make_request('POST', '/templates', json=payload)
        return response.json()


class SMSGatewayService(BaseIntegrationService):
    """
    Service for SMS gateway integrations
    """
    
    def test_connection(self):
        """Test SMS gateway connection"""
        try:
            self.authenticate()
            response = self.make_request('GET', '/balance')
            return True, f"Connection successful. Balance: {response.json().get('balance', 'N/A')}"
        except Exception as e:
            return False, str(e)
    
    def authenticate(self):
        """Authenticate with SMS gateway"""
        api_key = self.get_credential('api_key')
        api_secret = self.get_credential('api_secret')
        
        if not api_key or not api_secret:
            raise ValueError("API credentials not configured")
        
        self.session.auth = (api_key, api_secret)
        return True
    
    def send_sms(self, to_number, message, from_number=None):
        """Send SMS"""
        self.authenticate()
        
        payload = {
            'to': to_number,
            'message': message,
            'from': from_number or self.integration.settings.get('default_from_number')
        }
        
        response = self.make_request('POST', '/messages', json=payload)
        return response.json()
    
    def get_sms_status(self, message_id):
        """Get SMS delivery status"""
        self.authenticate()
        
        response = self.make_request('GET', f'/messages/{message_id}')
        return response.json()
    
    def get_balance(self):
        """Get account balance"""
        self.authenticate()
        
        response = self.make_request('GET', '/balance')
        return response.json()


class CloudStorageService(BaseIntegrationService):
    """
    Service for cloud storage integrations
    """
    
    def test_connection(self):
        """Test cloud storage connection"""
        try:
            self.authenticate()
            response = self.make_request('GET', '/buckets')
            return True, "Connection successful"
        except Exception as e:
            return False, str(e)
    
    def authenticate(self):
        """Authenticate with cloud storage"""
        access_key = self.get_credential('access_key')
        secret_key = self.get_credential('secret_key')
        
        if not access_key or not secret_key:
            raise ValueError("Storage credentials not configured")
        
        # Implementation would depend on specific storage provider
        self.session.headers['Authorization'] = f'AWS4-HMAC-SHA256 {access_key}'
        return True
    
    def upload_file(self, file_path, content, content_type=None):
        """Upload file to storage"""
        self.authenticate()
        
        files = {'file': (file_path, content, content_type)}
        response = self.make_request('POST', '/upload', files=files)
        return response.json()
    
    def download_file(self, file_path):
        """Download file from storage"""
        self.authenticate()
        
        response = self.make_request('GET', f'/download/{file_path}')
        return response.content
    
    def delete_file(self, file_path):
        """Delete file from storage"""
        self.authenticate()
        
        response = self.make_request('DELETE', f'/files/{file_path}')
        return response.json()
    
    def list_files(self, prefix=None):
        """List files in storage"""
        self.authenticate()
        
        params = {'prefix': prefix} if prefix else {}
        response = self.make_request('GET', '/files', params=params)
        return response.json()


class IntegrationManager:
    """
    Manager class for handling integrations
    """
    
    SERVICE_CLASSES = {
        'payment': PaymentGatewayService,
        'email': EmailServiceIntegration,
        'sms': SMSGatewayService,
        'storage': CloudStorageService,
    }
    
    # Import specific service factories
    @classmethod
    def _get_payment_service(cls, integration):
        from .payment_gateways import PaymentGatewayFactory
        return PaymentGatewayFactory.create_gateway(integration)
    
    @classmethod
    def _get_email_service(cls, integration):
        from .email_services import EmailServiceFactory
        return EmailServiceFactory.create_service(integration)
    
    @classmethod
    def _get_sms_service(cls, integration):
        from .sms_gateways import SMSGatewayFactory
        return SMSGatewayFactory.create_gateway(integration)
    
    @classmethod
    def _get_storage_service(cls, integration):
        from .cloud_storage import CloudStorageFactory
        return CloudStorageFactory.create_storage(integration)
    
    @classmethod
    def get_service(cls, integration):
        """Get service instance for integration"""
        provider_type = integration.provider.provider_type
        
        # Use specific factory methods for better service selection
        if provider_type == 'payment':
            return cls._get_payment_service(integration)
        elif provider_type == 'email':
            return cls._get_email_service(integration)
        elif provider_type == 'sms':
            return cls._get_sms_service(integration)
        elif provider_type == 'storage':
            return cls._get_storage_service(integration)
        else:
            # Fallback to generic service classes
            service_class = cls.SERVICE_CLASSES.get(provider_type)
            if not service_class:
                raise ValueError(f"No service class found for provider type: {provider_type}")
            return service_class(integration)
    
    @classmethod
    def test_integration(cls, integration):
        """Test an integration"""
        try:
            service = cls.get_service(integration)
            success, message = service.test_connection()
            
            if success:
                integration.status = 'active'
                integration.last_error = ''
            else:
                integration.status = 'error'
                integration.last_error = message
            
            integration.save()
            return success, message
        
        except Exception as e:
            integration.status = 'error'
            integration.last_error = str(e)
            integration.save()
            return False, str(e)
    
    @classmethod
    def sync_integration_data(cls, integration):
        """Sync data for an integration"""
        try:
            service = cls.get_service(integration)
            
            # Implementation would depend on integration type
            # This is a placeholder for sync logic
            
            integration.last_sync_at = timezone.now()
            integration.save()
            
            return True, "Sync completed successfully"
        
        except Exception as e:
            logger.error(f"Error syncing integration {integration.id}: {e}")
            return False, str(e)


class IntegrationAnalyticsService:
    """
    Service for integration analytics
    """
    
    def update_daily_analytics(self, integration, date=None):
        """Update daily analytics for an integration"""
        if date is None:
            date = timezone.now().date()
        
        # Get or create analytics record
        analytics, created = IntegrationAnalytics.objects.get_or_create(
            integration=integration,
            date=date,
            defaults={
                'total_requests': 0,
                'successful_requests': 0,
                'failed_requests': 0,
                'avg_response_time_ms': 0,
                'data_sent_bytes': 0,
                'data_received_bytes': 0,
                'error_types': {},
                'hourly_distribution': {},
                'endpoint_usage': {}
            }
        )
        
        # Get logs for the date
        logs = IntegrationLog.objects.filter(
            integration=integration,
            timestamp__date=date,
            action_type='request'
        )
        
        # Calculate statistics
        total_requests = logs.count()
        successful_requests = logs.filter(status_code__lt=400).count()
        failed_requests = logs.filter(status_code__gte=400).count()
        
        # Calculate response times
        response_times = [
            log.duration_ms for log in logs 
            if log.duration_ms is not None
        ]
        
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
        else:
            avg_response_time = 0
            min_response_time = 0
            max_response_time = 0
        
        # Hourly distribution
        hourly_distribution = {}
        for log in logs:
            hour = log.timestamp.hour
            hourly_distribution[str(hour)] = hourly_distribution.get(str(hour), 0) + 1
        
        # Error types
        error_types = {}
        error_logs = logs.filter(level='error')
        for log in error_logs:
            error_type = f"HTTP_{log.status_code}" if log.status_code else "CONNECTION_ERROR"
            error_types[error_type] = error_types.get(error_type, 0) + 1
        
        # Update analytics record
        analytics.total_requests = total_requests
        analytics.successful_requests = successful_requests
        analytics.failed_requests = failed_requests
        analytics.avg_response_time_ms = avg_response_time
        analytics.min_response_time_ms = min_response_time
        analytics.max_response_time_ms = max_response_time
        analytics.error_types = error_types
        analytics.hourly_distribution = hourly_distribution
        analytics.save()
        
        return analytics
    
    def generate_integration_report(self, integration, days=30):
        """Generate comprehensive report for an integration"""
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)
        
        # Get analytics for the period
        analytics = IntegrationAnalytics.objects.filter(
            integration=integration,
            date__range=[start_date, end_date]
        ).order_by('date')
        
        # Aggregate data
        total_requests = sum(a.total_requests for a in analytics)
        total_successful = sum(a.successful_requests for a in analytics)
        total_failed = sum(a.failed_requests for a in analytics)
        
        # Calculate trends
        daily_data = []
        for analytic in analytics:
            daily_data.append({
                'date': analytic.date.isoformat(),
                'requests': analytic.total_requests,
                'success_rate': analytic.success_rate,
                'avg_response_time': analytic.avg_response_time_ms
            })
        
        # Error summary
        error_summary = {}
        for analytic in analytics:
            for error_type, count in analytic.error_types.items():
                error_summary[error_type] = error_summary.get(error_type, 0) + count
        
        report = {
            'integration': {
                'id': str(integration.id),
                'name': integration.name,
                'provider': integration.provider.display_name,
                'status': integration.status
            },
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'days': days
            },
            'summary': {
                'total_requests': total_requests,
                'successful_requests': total_successful,
                'failed_requests': total_failed,
                'success_rate': (total_successful / total_requests * 100) if total_requests > 0 else 0
            },
            'daily_data': daily_data,
            'error_summary': error_summary
        }
        
        return report


# Global service instances
integration_manager = IntegrationManager()
integration_analytics_service = IntegrationAnalyticsService()