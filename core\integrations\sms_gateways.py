"""
SMS gateway integrations for School ERP
"""

import json
import hmac
import hashlib
import logging
from datetime import datetime
from django.utils import timezone
from django.conf import settings
from .services import BaseIntegrationService

logger = logging.getLogger(__name__)


class TwilioSMSGateway(BaseIntegrationService):
    """
    Twilio SMS gateway integration
    """
    
    def test_connection(self):
        """Test Twilio connection"""
        try:
            self.authenticate()
            response = self.make_request('GET', '/Accounts.json')
            return True, "Connection successful"
        except Exception as e:
            return False, str(e)
    
    def authenticate(self):
        """Authenticate with <PERSON>wi<PERSON>"""
        account_sid = self.get_credential('account_sid')
        auth_token = self.get_credential('auth_token')
        
        if not account_sid or not auth_token:
            raise ValueError("Twilio credentials not configured")
        
        self.session.auth = (account_sid, auth_token)
        return True
    
    def send_sms(self, to_number, message, from_number=None, media_urls=None):
        """Send SMS via Twilio"""
        self.authenticate()
        
        account_sid = self.get_credential('account_sid')
        
        data = {
            'To': to_number,
            'Body': message,
            'From': from_number or self.integration.settings.get('default_from_number')
        }
        
        # Add media URLs for MMS
        if media_urls:
            if isinstance(media_urls, str):
                media_urls = [media_urls]
            for i, url in enumerate(media_urls):
                data[f'MediaUrl'] = url
        
        response = self.make_request(
            'POST',
            f'/Accounts/{account_sid}/Messages.json',
            data=data
        )
        
        return response.json()
    
    def get_message_status(self, message_sid):
        """Get SMS message status"""
        self.authenticate()
        
        account_sid = self.get_credential('account_sid')
        
        response = self.make_request(
            'GET',
            f'/Accounts/{account_sid}/Messages/{message_sid}.json'
        )
        
        return response.json()
    
    def get_account_balance(self):
        """Get Twilio account balance"""
        self.authenticate()
        
        account_sid = self.get_credential('account_sid')
        
        response = self.make_request(
            'GET',
            f'/Accounts/{account_sid}/Balance.json'
        )
        
        return response.json()
    
    def get_message_history(self, date_sent=None, to_number=None, from_number=None):
        """Get SMS message history"""
        self.authenticate()
        
        account_sid = self.get_credential('account_sid')
        params = {}
        
        if date_sent:
            params['DateSent'] = date_sent.strftime('%Y-%m-%d')
        if to_number:
            params['To'] = to_number
        if from_number:
            params['From'] = from_number
        
        response = self.make_request(
            'GET',
            f'/Accounts/{account_sid}/Messages.json',
            params=params
        )
        
        return response.json()
    
    def verify_webhook_signature(self, url, params, signature, auth_token):
        """Verify Twilio webhook signature"""
        try:
            # Create the signature
            data = url
            for key in sorted(params.keys()):
                data += f'{key}{params[key]}'
            
            expected_signature = base64.b64encode(
                hmac.new(
                    auth_token.encode('utf-8'),
                    data.encode('utf-8'),
                    hashlib.sha1
                ).digest()
            ).decode()
            
            return hmac.compare_digest(expected_signature, signature)
        except Exception as e:
            logger.error(f"Error verifying Twilio webhook signature: {e}")
            return False


class NexmoSMSGateway(BaseIntegrationService):
    """
    Nexmo (Vonage) SMS gateway integration
    """
    
    def test_connection(self):
        """Test Nexmo connection"""
        try:
            self.authenticate()
            response = self.make_request('GET', '/account/get-balance')
            return True, f"Connection successful. Balance: {response.json().get('value', 'N/A')}"
        except Exception as e:
            return False, str(e)
    
    def authenticate(self):
        """Authenticate with Nexmo"""
        api_key = self.get_credential('api_key')
        api_secret = self.get_credential('api_secret')
        
        if not api_key or not api_secret:
            raise ValueError("Nexmo credentials not configured")
        
        # Nexmo uses API key and secret in request parameters
        self.session.params.update({
            'api_key': api_key,
            'api_secret': api_secret
        })
        
        return True
    
    def send_sms(self, to_number, message, from_number=None, message_type='text'):
        """Send SMS via Nexmo"""
        self.authenticate()
        
        data = {
            'to': to_number,
            'text': message,
            'from': from_number or self.integration.settings.get('default_from_number'),
            'type': message_type
        }
        
        response = self.make_request('POST', '/sms/json', json=data)
        return response.json()
    
    def get_message_status(self, message_id):
        """Get SMS message status"""
        self.authenticate()
        
        params = {'message-id': message_id}
        response = self.make_request('GET', '/search/message', params=params)
        return response.json()
    
    def get_account_balance(self):
        """Get Nexmo account balance"""
        self.authenticate()
        
        response = self.make_request('GET', '/account/get-balance')
        return response.json()
    
    def get_pricing(self, country_code):
        """Get SMS pricing for country"""
        self.authenticate()
        
        response = self.make_request('GET', f'/account/get-pricing/outbound/sms/{country_code}')
        return response.json()
    
    def verify_webhook_signature(self, params, signature, signature_secret):
        """Verify Nexmo webhook signature"""
        try:
            # Sort parameters and create query string
            sorted_params = sorted(params.items())
            query_string = '&'.join([f'{k}={v}' for k, v in sorted_params])
            
            expected_signature = hmac.new(
                signature_secret.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            return hmac.compare_digest(expected_signature, signature)
        except Exception as e:
            logger.error(f"Error verifying Nexmo webhook signature: {e}")
            return False


class TextlocalSMSGateway(BaseIntegrationService):
    """
    Textlocal SMS gateway integration
    """
    
    def test_connection(self):
        """Test Textlocal connection"""
        try:
            self.authenticate()
            response = self.make_request('POST', '/balance/', data={})
            return True, f"Connection successful. Balance: {response.json().get('balance', 'N/A')}"
        except Exception as e:
            return False, str(e)
    
    def authenticate(self):
        """Authenticate with Textlocal"""
        api_key = self.get_credential('api_key')
        
        if not api_key:
            raise ValueError("Textlocal API key not configured")
        
        # Textlocal uses API key in request data
        self.api_key = api_key
        return True
    
    def send_sms(self, to_numbers, message, sender=None, schedule_time=None):
        """Send SMS via Textlocal"""
        self.authenticate()
        
        # Prepare recipients
        if isinstance(to_numbers, str):
            to_numbers = [to_numbers]
        
        data = {
            'apikey': self.api_key,
            'numbers': ','.join(to_numbers),
            'message': message,
            'sender': sender or self.integration.settings.get('default_sender', 'TXTLCL')
        }
        
        if schedule_time:
            data['schedule_time'] = int(schedule_time.timestamp())
        
        response = self.make_request('POST', '/send/', data=data)
        return response.json()
    
    def send_group_sms(self, group_id, message, sender=None):
        """Send SMS to a group via Textlocal"""
        self.authenticate()
        
        data = {
            'apikey': self.api_key,
            'group_id': group_id,
            'message': message,
            'sender': sender or self.integration.settings.get('default_sender', 'TXTLCL')
        }
        
        response = self.make_request('POST', '/send/', data=data)
        return response.json()
    
    def get_balance(self):
        """Get Textlocal account balance"""
        self.authenticate()
        
        data = {'apikey': self.api_key}
        response = self.make_request('POST', '/balance/', data=data)
        return response.json()
    
    def get_message_history(self, start=0, limit=1000):
        """Get SMS message history"""
        self.authenticate()
        
        data = {
            'apikey': self.api_key,
            'start': start,
            'limit': limit
        }
        
        response = self.make_request('POST', '/get_history/', data=data)
        return response.json()
    
    def create_group(self, name):
        """Create contact group"""
        self.authenticate()
        
        data = {
            'apikey': self.api_key,
            'name': name
        }
        
        response = self.make_request('POST', '/create_group/', data=data)
        return response.json()
    
    def add_contact_to_group(self, group_id, number, first_name=None, last_name=None):
        """Add contact to group"""
        self.authenticate()
        
        data = {
            'apikey': self.api_key,
            'group_id': group_id,
            'numbers': number
        }
        
        if first_name:
            data['first_name'] = first_name
        if last_name:
            data['last_name'] = last_name
        
        response = self.make_request('POST', '/add_contacts/', data=data)
        return response.json()


class MSG91SMSGateway(BaseIntegrationService):
    """
    MSG91 SMS gateway integration
    """
    
    def test_connection(self):
        """Test MSG91 connection"""
        try:
            self.authenticate()
            response = self.make_request('GET', '/balance.php')
            return True, f"Connection successful. Balance: {response.text}"
        except Exception as e:
            return False, str(e)
    
    def authenticate(self):
        """Authenticate with MSG91"""
        auth_key = self.get_credential('auth_key')
        
        if not auth_key:
            raise ValueError("MSG91 auth key not configured")
        
        self.session.headers['authkey'] = auth_key
        return True
    
    def send_sms(self, to_numbers, message, sender=None, route=4, country=91):
        """Send SMS via MSG91"""
        self.authenticate()
        
        # Prepare recipients
        if isinstance(to_numbers, str):
            to_numbers = [to_numbers]
        
        data = {
            'mobiles': ','.join(to_numbers),
            'message': message,
            'sender': sender or self.integration.settings.get('default_sender'),
            'route': route,
            'country': country
        }
        
        response = self.make_request('POST', '/sendhttp.php', data=data)
        return {'success': True, 'response': response.text}
    
    def send_otp(self, mobile, otp, sender=None, template_id=None):
        """Send OTP via MSG91"""
        self.authenticate()
        
        data = {
            'mobile': mobile,
            'otp': otp,
            'sender': sender or self.integration.settings.get('default_sender')
        }
        
        if template_id:
            data['template_id'] = template_id
        
        response = self.make_request('POST', '/sendotp.php', data=data)
        return {'success': True, 'response': response.text}
    
    def verify_otp(self, mobile, otp):
        """Verify OTP via MSG91"""
        self.authenticate()
        
        data = {
            'mobile': mobile,
            'otp': otp
        }
        
        response = self.make_request('POST', '/verifyRequestOTP.php', data=data)
        return {'success': True, 'response': response.text}
    
    def get_balance(self):
        """Get MSG91 account balance"""
        self.authenticate()
        
        response = self.make_request('GET', '/balance.php')
        return {'balance': response.text}
    
    def get_delivery_report(self, message_id):
        """Get delivery report"""
        self.authenticate()
        
        params = {'id': message_id}
        response = self.make_request('GET', '/getdelivery.php', params=params)
        return {'report': response.text}


class WhatsAppBusinessAPI(BaseIntegrationService):
    """
    WhatsApp Business API integration
    """
    
    def test_connection(self):
        """Test WhatsApp Business API connection"""
        try:
            self.authenticate()
            response = self.make_request('GET', '/health')
            return True, "Connection successful"
        except Exception as e:
            return False, str(e)
    
    def authenticate(self):
        """Authenticate with WhatsApp Business API"""
        access_token = self.get_credential('access_token')
        
        if not access_token:
            raise ValueError("WhatsApp access token not configured")
        
        self.session.headers['Authorization'] = f'Bearer {access_token}'
        return True
    
    def send_message(self, to_number, message_type='text', **kwargs):
        """Send WhatsApp message"""
        self.authenticate()
        
        phone_number_id = self.get_credential('phone_number_id')
        
        payload = {
            'messaging_product': 'whatsapp',
            'to': to_number,
            'type': message_type
        }
        
        if message_type == 'text':
            payload['text'] = {'body': kwargs.get('text', '')}
        elif message_type == 'template':
            payload['template'] = {
                'name': kwargs.get('template_name'),
                'language': {'code': kwargs.get('language_code', 'en')},
                'components': kwargs.get('components', [])
            }
        elif message_type == 'image':
            payload['image'] = {
                'link': kwargs.get('image_url'),
                'caption': kwargs.get('caption', '')
            }
        elif message_type == 'document':
            payload['document'] = {
                'link': kwargs.get('document_url'),
                'filename': kwargs.get('filename'),
                'caption': kwargs.get('caption', '')
            }
        
        response = self.make_request(
            'POST',
            f'/{phone_number_id}/messages',
            json=payload
        )
        
        return response.json()
    
    def send_template_message(self, to_number, template_name, language_code='en', components=None):
        """Send WhatsApp template message"""
        return self.send_message(
            to_number=to_number,
            message_type='template',
            template_name=template_name,
            language_code=language_code,
            components=components or []
        )
    
    def get_message_status(self, message_id):
        """Get WhatsApp message status"""
        self.authenticate()
        
        response = self.make_request('GET', f'/messages/{message_id}')
        return response.json()
    
    def verify_webhook_signature(self, payload, signature, app_secret):
        """Verify WhatsApp webhook signature"""
        try:
            expected_signature = hmac.new(
                app_secret.encode('utf-8'),
                payload,
                hashlib.sha256
            ).hexdigest()
            
            return hmac.compare_digest(f'sha256={expected_signature}', signature)
        except Exception as e:
            logger.error(f"Error verifying WhatsApp webhook signature: {e}")
            return False


class SMSGatewayFactory:
    """
    Factory class for creating SMS gateway instances
    """
    
    GATEWAY_CLASSES = {
        'twilio': TwilioSMSGateway,
        'nexmo': NexmoSMSGateway,
        'textlocal': TextlocalSMSGateway,
        'msg91': MSG91SMSGateway,
        'whatsapp': WhatsAppBusinessAPI,
    }
    
    @classmethod
    def create_gateway(cls, integration):
        """Create SMS gateway instance"""
        provider_name = integration.provider.name.lower()
        gateway_class = cls.GATEWAY_CLASSES.get(provider_name)
        
        if not gateway_class:
            raise ValueError(f"Unsupported SMS gateway: {provider_name}")
        
        return gateway_class(integration)
    
    @classmethod
    def get_supported_gateways(cls):
        """Get list of supported SMS gateways"""
        return list(cls.GATEWAY_CLASSES.keys())