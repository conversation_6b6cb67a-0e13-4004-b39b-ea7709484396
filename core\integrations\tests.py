"""
Tests for integration functionality in School ERP
"""

import json
from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone

from .models import (
    IntegrationProvider, Integration, IntegrationLog, 
    IntegrationWebhook, IntegrationMapping, IntegrationAnalytics
)
from .services import (
    PaymentGatewayService, EmailServiceIntegration, 
    SMSGatewayService, IntegrationManager
)
from .encryption import IntegrationEncryption

User = get_user_model()


class IntegrationModelTestCase(TestCase):
    """
    Test integration models
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
        
        self.provider = IntegrationProvider.objects.create(
            name='test_payment',
            display_name='Test Payment Gateway',
            provider_type='payment',
            description='Test payment provider',
            base_url='https://api.testpayment.com',
            required_credentials=['api_key', 'secret_key'],
            supported_features=['payments', 'refunds']
        )
    
    def test_integration_provider_creation(self):
        """Test creating integration provider"""
        self.assertEqual(self.provider.name, 'test_payment')
        self.assertEqual(self.provider.provider_type, 'payment')
        self.assertEqual(self.provider.status, 'active')
        self.assertTrue(self.provider.is_enabled)
    
    def test_integration_creation(self):
        """Test creating integration"""
        integration = Integration.objects.create(
            provider=self.provider,
            name='Main Payment Gateway',
            description='Primary payment processing',
            credentials={'api_key': 'encrypted_key', 'secret_key': 'encrypted_secret'},
            settings={'currency': 'USD', 'webhook_url': 'https://example.com/webhook'},
            created_by=self.user
        )
        
        self.assertEqual(integration.name, 'Main Payment Gateway')
        self.assertEqual(integration.status, 'pending')
        self.assertEqual(integration.success_rate, 0)
        self.assertEqual(integration.created_by, self.user)
    
    def test_integration_stats_update(self):
        """Test updating integration statistics"""
        integration = Integration.objects.create(
            provider=self.provider,
            name='Test Integration',
            created_by=self.user
        )
        
        # Test successful request
        integration.update_stats(success=True)
        self.assertEqual(integration.total_requests, 1)
        self.assertEqual(integration.successful_requests, 1)
        self.assertEqual(integration.failed_requests, 0)
        self.assertEqual(integration.success_rate, 100)
        
        # Test failed request
        integration.update_stats(success=False)
        self.assertEqual(integration.total_requests, 2)
        self.assertEqual(integration.successful_requests, 1)
        self.assertEqual(integration.failed_requests, 1)
        self.assertEqual(integration.success_rate, 50)
    
    def test_integration_log_creation(self):
        """Test creating integration log"""
        integration = Integration.objects.create(
            provider=self.provider,
            name='Test Integration',
            created_by=self.user
        )
        
        log = IntegrationLog.objects.create(
            integration=integration,
            level='info',
            action_type='request',
            message='API request made',
            request_data={'endpoint': '/payments'},
            response_data={'status': 'success'},
            status_code=200,
            duration_ms=150,
            user=self.user
        )
        
        self.assertEqual(log.integration, integration)
        self.assertEqual(log.level, 'info')
        self.assertEqual(log.action_type, 'request')
        self.assertEqual(log.status_code, 200)
        self.assertEqual(log.duration_ms, 150)
    
    def test_integration_webhook_creation(self):
        """Test creating integration webhook"""
        integration = Integration.objects.create(
            provider=self.provider,
            name='Test Integration',
            created_by=self.user
        )
        
        webhook = IntegrationWebhook.objects.create(
            integration=integration,
            event_type='payment.completed',
            payload={'payment_id': '12345', 'amount': 100.00},
            headers={'Content-Type': 'application/json'},
            signature='test_signature',
            source_ip='***********'
        )
        
        self.assertEqual(webhook.integration, integration)
        self.assertEqual(webhook.event_type, 'payment.completed')
        self.assertEqual(webhook.status, 'pending')
        self.assertFalse(webhook.is_verified)
    
    def test_integration_mapping_creation(self):
        """Test creating integration mapping"""
        integration = Integration.objects.create(
            provider=self.provider,
            name='Test Integration',
            created_by=self.user
        )
        
        mapping = IntegrationMapping.objects.create(
            integration=integration,
            mapping_type='field',
            source_field='student_name',
            target_field='customer_name',
            transform_rules={'uppercase': True},
            is_required=True
        )
        
        self.assertEqual(mapping.integration, integration)
        self.assertEqual(mapping.mapping_type, 'field')
        self.assertEqual(mapping.source_field, 'student_name')
        self.assertEqual(mapping.target_field, 'customer_name')
        self.assertTrue(mapping.is_required)


class IntegrationEncryptionTestCase(TestCase):
    """
    Test integration encryption
    """
    
    def setUp(self):
        self.encryption = IntegrationEncryption()
    
    def test_encrypt_decrypt_string(self):
        """Test encrypting and decrypting strings"""
        plaintext = "test_api_key_12345"
        
        # Encrypt
        ciphertext = self.encryption.encrypt(plaintext)
        self.assertNotEqual(plaintext, ciphertext)
        self.assertIsInstance(ciphertext, str)
        
        # Decrypt
        decrypted = self.encryption.decrypt(ciphertext)
        self.assertEqual(plaintext, decrypted)
    
    def test_encrypt_decrypt_empty_string(self):
        """Test encrypting and decrypting empty strings"""
        plaintext = ""
        
        ciphertext = self.encryption.encrypt(plaintext)
        self.assertEqual(ciphertext, "")
        
        decrypted = self.encryption.decrypt(ciphertext)
        self.assertEqual(decrypted, "")
    
    def test_encrypt_decrypt_dict(self):
        """Test encrypting and decrypting dictionaries"""
        data = {
            'api_key': 'secret_key_123',
            'api_secret': 'secret_value_456',
            'timeout': 30,
            'enabled': True
        }
        
        # Encrypt dictionary
        encrypted_data = self.encryption.encrypt_dict(data)
        
        # Check that string values are encrypted
        self.assertNotEqual(data['api_key'], encrypted_data['api_key'])
        self.assertNotEqual(data['api_secret'], encrypted_data['api_secret'])
        
        # Check that non-string values are preserved
        self.assertEqual(data['timeout'], encrypted_data['timeout'])
        self.assertEqual(data['enabled'], encrypted_data['enabled'])
        
        # Decrypt dictionary
        decrypted_data = self.encryption.decrypt_dict(encrypted_data)
        
        # Check that all values are correctly decrypted
        self.assertEqual(data['api_key'], decrypted_data['api_key'])
        self.assertEqual(data['api_secret'], decrypted_data['api_secret'])
        self.assertEqual(data['timeout'], decrypted_data['timeout'])
        self.assertEqual(data['enabled'], decrypted_data['enabled'])


class IntegrationServiceTestCase(TestCase):
    """
    Test integration services
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
        
        self.payment_provider = IntegrationProvider.objects.create(
            name='stripe',
            display_name='Stripe Payment Gateway',
            provider_type='payment',
            base_url='https://api.stripe.com/v1',
            required_credentials=['api_key']
        )
        
        self.payment_integration = Integration.objects.create(
            provider=self.payment_provider,
            name='Main Stripe Integration',
            credentials={'api_key': 'test_encrypted_key'},
            created_by=self.user
        )
    
    def test_payment_gateway_service_initialization(self):
        """Test payment gateway service initialization"""
        service = PaymentGatewayService(self.payment_integration)
        
        self.assertEqual(service.integration, self.payment_integration)
        self.assertEqual(service.provider, self.payment_provider)
        self.assertIsNotNone(service.session)
    
    @patch('requests.Session.request')
    def test_payment_gateway_make_request(self, mock_request):
        """Test making HTTP requests through payment gateway service"""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'status': 'success'}
        mock_response.content = b'{"status": "success"}'
        mock_request.return_value = mock_response
        
        service = PaymentGatewayService(self.payment_integration)
        
        # Make request
        response = service.make_request('GET', '/test')
        
        # Verify request was made
        mock_request.assert_called_once()
        self.assertEqual(response.status_code, 200)
        
        # Verify log was created
        self.assertTrue(IntegrationLog.objects.filter(
            integration=self.payment_integration,
            action_type='request'
        ).exists())
    
    @patch('requests.Session.request')
    def test_payment_gateway_request_error(self, mock_request):
        """Test handling request errors in payment gateway service"""
        # Mock failed response
        mock_request.side_effect = Exception("Connection failed")
        
        service = PaymentGatewayService(self.payment_integration)
        
        # Make request and expect exception
        with self.assertRaises(Exception):
            service.make_request('GET', '/test')
        
        # Verify error log was created
        self.assertTrue(IntegrationLog.objects.filter(
            integration=self.payment_integration,
            level='error'
        ).exists())
        
        # Verify integration stats were updated
        self.payment_integration.refresh_from_db()
        self.assertEqual(self.payment_integration.failed_requests, 1)
    
    def test_integration_manager_get_service(self):
        """Test getting service from integration manager"""
        service = IntegrationManager.get_service(self.payment_integration)
        
        self.assertIsInstance(service, PaymentGatewayService)
        self.assertEqual(service.integration, self.payment_integration)
    
    def test_integration_manager_unsupported_type(self):
        """Test getting service for unsupported provider type"""
        unsupported_provider = IntegrationProvider.objects.create(
            name='unsupported',
            display_name='Unsupported Provider',
            provider_type='unsupported',
            base_url='https://api.unsupported.com'
        )
        
        unsupported_integration = Integration.objects.create(
            provider=unsupported_provider,
            name='Unsupported Integration',
            created_by=self.user
        )
        
        with self.assertRaises(ValueError):
            IntegrationManager.get_service(unsupported_integration)
    
    @patch.object(PaymentGatewayService, 'test_connection')
    def test_integration_manager_test_integration(self, mock_test):
        """Test testing integration through manager"""
        # Mock successful test
        mock_test.return_value = (True, "Connection successful")
        
        success, message = IntegrationManager.test_integration(self.payment_integration)
        
        self.assertTrue(success)
        self.assertEqual(message, "Connection successful")
        
        # Verify integration status was updated
        self.payment_integration.refresh_from_db()
        self.assertEqual(self.payment_integration.status, 'active')
        self.assertEqual(self.payment_integration.last_error, '')
    
    @patch.object(PaymentGatewayService, 'test_connection')
    def test_integration_manager_test_integration_failure(self, mock_test):
        """Test testing integration failure through manager"""
        # Mock failed test
        mock_test.return_value = (False, "Connection failed")
        
        success, message = IntegrationManager.test_integration(self.payment_integration)
        
        self.assertFalse(success)
        self.assertEqual(message, "Connection failed")
        
        # Verify integration status was updated
        self.payment_integration.refresh_from_db()
        self.assertEqual(self.payment_integration.status, 'error')
        self.assertEqual(self.payment_integration.last_error, 'Connection failed')


class EmailServiceIntegrationTestCase(TestCase):
    """
    Test email service integration
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
        
        self.email_provider = IntegrationProvider.objects.create(
            name='sendgrid',
            display_name='SendGrid Email Service',
            provider_type='email',
            base_url='https://api.sendgrid.com/v3',
            required_credentials=['api_key']
        )
        
        self.email_integration = Integration.objects.create(
            provider=self.email_provider,
            name='Main Email Service',
            credentials={'api_key': 'test_encrypted_key'},
            settings={'default_from_email': '<EMAIL>'},
            created_by=self.user
        )
    
    @patch('requests.Session.request')
    def test_send_email(self, mock_request):
        """Test sending email through email service"""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 202
        mock_response.json.return_value = {'message_id': '12345'}
        mock_response.content = b'{"message_id": "12345"}'
        mock_request.return_value = mock_response
        
        service = EmailServiceIntegration(self.email_integration)
        
        # Send email
        result = service.send_email(
            to_emails=['<EMAIL>'],
            subject='Test Email',
            content='This is a test email'
        )
        
        # Verify response
        self.assertEqual(result['message_id'], '12345')
        
        # Verify request was made with correct data
        mock_request.assert_called()
        call_args = mock_request.call_args
        self.assertEqual(call_args[0][0], 'POST')  # Method
        self.assertIn('/send', call_args[0][1])  # URL
        
        # Verify request payload
        payload = call_args[1]['json']
        self.assertEqual(payload['to'], ['<EMAIL>'])
        self.assertEqual(payload['subject'], 'Test Email')
        self.assertEqual(payload['content'], 'This is a test email')


class SMSGatewayServiceTestCase(TestCase):
    """
    Test SMS gateway service
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
        
        self.sms_provider = IntegrationProvider.objects.create(
            name='twilio',
            display_name='Twilio SMS Gateway',
            provider_type='sms',
            base_url='https://api.twilio.com/2010-04-01',
            required_credentials=['api_key', 'api_secret']
        )
        
        self.sms_integration = Integration.objects.create(
            provider=self.sms_provider,
            name='Main SMS Gateway',
            credentials={'api_key': 'test_key', 'api_secret': 'test_secret'},
            settings={'default_from_number': '+**********'},
            created_by=self.user
        )
    
    @patch('requests.Session.request')
    def test_send_sms(self, mock_request):
        """Test sending SMS through SMS gateway"""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 201
        mock_response.json.return_value = {'sid': 'SM12345', 'status': 'queued'}
        mock_response.content = b'{"sid": "SM12345", "status": "queued"}'
        mock_request.return_value = mock_response
        
        service = SMSGatewayService(self.sms_integration)
        
        # Send SMS
        result = service.send_sms(
            to_number='+**********',
            message='Test SMS message'
        )
        
        # Verify response
        self.assertEqual(result['sid'], 'SM12345')
        self.assertEqual(result['status'], 'queued')
        
        # Verify request was made
        mock_request.assert_called()
        call_args = mock_request.call_args
        self.assertEqual(call_args[0][0], 'POST')  # Method
        
        # Verify request payload
        payload = call_args[1]['json']
        self.assertEqual(payload['to'], '+**********')
        self.assertEqual(payload['message'], 'Test SMS message')
        self.assertEqual(payload['from'], '+**********')


class PaymentGatewayIntegrationTestCase(TestCase):
    """
    Test specific payment gateway integrations
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
    
    def test_stripe_payment_gateway(self):
        """Test Stripe payment gateway integration"""
        from .payment_gateways import StripePaymentGateway
        
        provider = IntegrationProvider.objects.create(
            name='stripe',
            display_name='Stripe',
            provider_type='payment',
            base_url='https://api.stripe.com/v1'
        )
        
        integration = Integration.objects.create(
            provider=provider,
            name='Stripe Integration',
            credentials={'api_key': 'sk_test_12345'},
            created_by=self.user
        )
        
        gateway = StripePaymentGateway(integration)
        self.assertEqual(gateway.integration, integration)
        self.assertEqual(gateway.provider, provider)
    
    @patch('requests.Session.request')
    def test_stripe_create_payment_intent(self, mock_request):
        """Test creating Stripe payment intent"""
        from .payment_gateways import StripePaymentGateway
        
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'id': 'pi_12345',
            'amount': 10000,
            'currency': 'usd',
            'status': 'requires_payment_method'
        }
        mock_response.content = b'{"id": "pi_12345"}'
        mock_request.return_value = mock_response
        
        provider = IntegrationProvider.objects.create(
            name='stripe',
            display_name='Stripe',
            provider_type='payment',
            base_url='https://api.stripe.com/v1'
        )
        
        integration = Integration.objects.create(
            provider=provider,
            name='Stripe Integration',
            credentials={'api_key': 'sk_test_12345'},
            created_by=self.user
        )
        
        gateway = StripePaymentGateway(integration)
        
        # Create payment intent
        result = gateway.create_payment_intent(
            amount=100.00,
            currency='usd',
            description='Test payment'
        )
        
        # Verify response
        self.assertEqual(result['id'], 'pi_12345')
        self.assertEqual(result['amount'], 10000)
        self.assertEqual(result['currency'], 'usd')
        
        # Verify request was made correctly
        mock_request.assert_called()
        call_args = mock_request.call_args
        self.assertEqual(call_args[0][0], 'POST')
        self.assertIn('/payment_intents', call_args[0][1])
        
        # Verify payload
        payload = call_args[1]['json']
        self.assertEqual(payload['amount'], 10000)  # Amount in cents
        self.assertEqual(payload['currency'], 'usd')
        self.assertEqual(payload['description'], 'Test payment')
    
    def test_paypal_payment_gateway(self):
        """Test PayPal payment gateway integration"""
        from .payment_gateways import PayPalPaymentGateway
        
        provider = IntegrationProvider.objects.create(
            name='paypal',
            display_name='PayPal',
            provider_type='payment',
            base_url='https://api.paypal.com'
        )
        
        integration = Integration.objects.create(
            provider=provider,
            name='PayPal Integration',
            credentials={'client_id': 'test_client_id', 'client_secret': 'test_secret'},
            created_by=self.user
        )
        
        gateway = PayPalPaymentGateway(integration)
        self.assertEqual(gateway.integration, integration)
        self.assertEqual(gateway.provider, provider)
    
    def test_razorpay_payment_gateway(self):
        """Test Razorpay payment gateway integration"""
        from .payment_gateways import RazorpayPaymentGateway
        
        provider = IntegrationProvider.objects.create(
            name='razorpay',
            display_name='Razorpay',
            provider_type='payment',
            base_url='https://api.razorpay.com/v1'
        )
        
        integration = Integration.objects.create(
            provider=provider,
            name='Razorpay Integration',
            credentials={'key_id': 'rzp_test_12345', 'key_secret': 'test_secret'},
            created_by=self.user
        )
        
        gateway = RazorpayPaymentGateway(integration)
        self.assertEqual(gateway.integration, integration)
        self.assertEqual(gateway.provider, provider)


class EmailServiceIntegrationDetailTestCase(TestCase):
    """
    Test specific email service integrations
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
    
    def test_sendgrid_email_service(self):
        """Test SendGrid email service integration"""
        from .email_services import SendGridEmailService
        
        provider = IntegrationProvider.objects.create(
            name='sendgrid',
            display_name='SendGrid',
            provider_type='email',
            base_url='https://api.sendgrid.com/v3'
        )
        
        integration = Integration.objects.create(
            provider=provider,
            name='SendGrid Integration',
            credentials={'api_key': 'SG.test_key'},
            settings={'default_from_email': '<EMAIL>'},
            created_by=self.user
        )
        
        service = SendGridEmailService(integration)
        self.assertEqual(service.integration, integration)
        self.assertEqual(service.provider, provider)
    
    def test_mailgun_email_service(self):
        """Test Mailgun email service integration"""
        from .email_services import MailgunEmailService
        
        provider = IntegrationProvider.objects.create(
            name='mailgun',
            display_name='Mailgun',
            provider_type='email',
            base_url='https://api.mailgun.net/v3'
        )
        
        integration = Integration.objects.create(
            provider=provider,
            name='Mailgun Integration',
            credentials={'api_key': 'key-12345', 'domain': 'school.com'},
            created_by=self.user
        )
        
        service = MailgunEmailService(integration)
        self.assertEqual(service.integration, integration)
        self.assertEqual(service.provider, provider)
    
    def test_amazon_ses_email_service(self):
        """Test Amazon SES email service integration"""
        from .email_services import AmazonSESEmailService
        
        provider = IntegrationProvider.objects.create(
            name='amazon_ses',
            display_name='Amazon SES',
            provider_type='email',
            base_url='https://email.us-east-1.amazonaws.com'
        )
        
        integration = Integration.objects.create(
            provider=provider,
            name='Amazon SES Integration',
            credentials={'access_key_id': 'AKIA12345', 'secret_access_key': 'secret'},
            settings={'region': 'us-east-1'},
            created_by=self.user
        )
        
        service = AmazonSESEmailService(integration)
        self.assertEqual(service.integration, integration)
        self.assertEqual(service.provider, provider)
        self.assertEqual(service.region, 'us-east-1')


class SMSGatewayIntegrationDetailTestCase(TestCase):
    """
    Test specific SMS gateway integrations
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
    
    def test_twilio_sms_gateway(self):
        """Test Twilio SMS gateway integration"""
        from .sms_gateways import TwilioSMSGateway
        
        provider = IntegrationProvider.objects.create(
            name='twilio',
            display_name='Twilio',
            provider_type='sms',
            base_url='https://api.twilio.com/2010-04-01'
        )
        
        integration = Integration.objects.create(
            provider=provider,
            name='Twilio Integration',
            credentials={'account_sid': 'AC12345', 'auth_token': 'token123'},
            settings={'default_from_number': '+**********'},
            created_by=self.user
        )
        
        gateway = TwilioSMSGateway(integration)
        self.assertEqual(gateway.integration, integration)
        self.assertEqual(gateway.provider, provider)
    
    def test_nexmo_sms_gateway(self):
        """Test Nexmo SMS gateway integration"""
        from .sms_gateways import NexmoSMSGateway
        
        provider = IntegrationProvider.objects.create(
            name='nexmo',
            display_name='Nexmo',
            provider_type='sms',
            base_url='https://rest.nexmo.com'
        )
        
        integration = Integration.objects.create(
            provider=provider,
            name='Nexmo Integration',
            credentials={'api_key': 'key123', 'api_secret': 'secret123'},
            created_by=self.user
        )
        
        gateway = NexmoSMSGateway(integration)
        self.assertEqual(gateway.integration, integration)
        self.assertEqual(gateway.provider, provider)
    
    def test_whatsapp_business_api(self):
        """Test WhatsApp Business API integration"""
        from .sms_gateways import WhatsAppBusinessAPI
        
        provider = IntegrationProvider.objects.create(
            name='whatsapp',
            display_name='WhatsApp Business',
            provider_type='sms',
            base_url='https://graph.facebook.com/v17.0'
        )
        
        integration = Integration.objects.create(
            provider=provider,
            name='WhatsApp Integration',
            credentials={'access_token': 'token123', 'phone_number_id': '123456789'},
            created_by=self.user
        )
        
        api = WhatsAppBusinessAPI(integration)
        self.assertEqual(api.integration, integration)
        self.assertEqual(api.provider, provider)


class CloudStorageIntegrationTestCase(TestCase):
    """
    Test cloud storage integrations
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
    
    def test_amazon_s3_storage(self):
        """Test Amazon S3 storage integration"""
        from .cloud_storage import AmazonS3Storage
        
        provider = IntegrationProvider.objects.create(
            name='amazon_s3',
            display_name='Amazon S3',
            provider_type='storage',
            base_url='https://s3.amazonaws.com'
        )
        
        integration = Integration.objects.create(
            provider=provider,
            name='S3 Integration',
            credentials={'access_key_id': 'AKIA12345', 'secret_access_key': 'secret'},
            settings={'bucket_name': 'school-erp-bucket', 'region': 'us-east-1'},
            created_by=self.user
        )
        
        storage = AmazonS3Storage(integration)
        self.assertEqual(storage.integration, integration)
        self.assertEqual(storage.provider, provider)
        self.assertEqual(storage.bucket_name, 'school-erp-bucket')
    
    def test_google_cloud_storage(self):
        """Test Google Cloud Storage integration"""
        from .cloud_storage import GoogleCloudStorage
        
        provider = IntegrationProvider.objects.create(
            name='google_cloud',
            display_name='Google Cloud Storage',
            provider_type='storage',
            base_url='https://storage.googleapis.com'
        )
        
        integration = Integration.objects.create(
            provider=provider,
            name='GCS Integration',
            credentials={'service_account_key': '{"type": "service_account"}'},
            settings={'bucket_name': 'school-erp-bucket', 'project_id': 'school-project'},
            created_by=self.user
        )
        
        storage = GoogleCloudStorage(integration)
        self.assertEqual(storage.integration, integration)
        self.assertEqual(storage.provider, provider)
        self.assertEqual(storage.bucket_name, 'school-erp-bucket')
    
    def test_azure_blob_storage(self):
        """Test Azure Blob Storage integration"""
        from .cloud_storage import AzureBlobStorage
        
        provider = IntegrationProvider.objects.create(
            name='azure_blob',
            display_name='Azure Blob Storage',
            provider_type='storage',
            base_url='https://schoolerp.blob.core.windows.net'
        )
        
        integration = Integration.objects.create(
            provider=provider,
            name='Azure Integration',
            credentials={'account_name': 'schoolerp', 'account_key': 'key123'},
            settings={'container_name': 'school-files'},
            created_by=self.user
        )
        
        storage = AzureBlobStorage(integration)
        self.assertEqual(storage.integration, integration)
        self.assertEqual(storage.provider, provider)
        self.assertEqual(storage.container_name, 'school-files')


class IntegrationAnalyticsTestCase(TestCase):
    """
    Test integration analytics functionality
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
        
        self.provider = IntegrationProvider.objects.create(
            name='test_provider',
            display_name='Test Provider',
            provider_type='payment',
            base_url='https://api.test.com'
        )
        
        self.integration = Integration.objects.create(
            provider=self.provider,
            name='Test Integration',
            created_by=self.user
        )
    
    def test_daily_analytics_creation(self):
        """Test creating daily analytics"""
        from .analytics import integration_analytics_service
        
        # Create some test logs
        IntegrationLog.objects.create(
            integration=self.integration,
            level='info',
            action_type='request',
            message='GET /test',
            status_code=200,
            duration_ms=150
        )
        
        IntegrationLog.objects.create(
            integration=self.integration,
            level='error',
            action_type='request',
            message='POST /test',
            status_code=400,
            duration_ms=300
        )
        
        # Update analytics
        analytics = integration_analytics_service.update_daily_analytics(self.integration)
        
        self.assertEqual(analytics.total_requests, 2)
        self.assertEqual(analytics.successful_requests, 1)
        self.assertEqual(analytics.failed_requests, 1)
        self.assertEqual(analytics.success_rate, 50.0)
        self.assertEqual(analytics.avg_response_time_ms, 225.0)
    
    def test_integration_summary_generation(self):
        """Test generating integration summary"""
        from .analytics import integration_analytics_service
        
        # Create analytics data
        IntegrationAnalytics.objects.create(
            integration=self.integration,
            date=timezone.now().date(),
            total_requests=100,
            successful_requests=95,
            failed_requests=5,
            avg_response_time_ms=200
        )
        
        # Generate summary
        summary = integration_analytics_service.generate_integration_summary(
            self.integration, days=7
        )
        
        self.assertEqual(summary['integration']['name'], 'Test Integration')
        self.assertEqual(summary['summary_stats']['total_requests'], 100)
        self.assertEqual(summary['summary_stats']['success_rate'], 95.0)
        self.assertEqual(summary['summary_stats']['avg_response_time_ms'], 200.0)
    
    def test_health_report_generation(self):
        """Test generating health report"""
        from .analytics import integration_analytics_service
        
        # Create analytics data for healthy integration
        IntegrationAnalytics.objects.create(
            integration=self.integration,
            date=timezone.now().date(),
            total_requests=1000,
            successful_requests=980,
            failed_requests=20,
            avg_response_time_ms=150
        )
        
        # Generate health report
        health_report = integration_analytics_service.generate_health_report(self.integration)
        
        self.assertEqual(health_report['health_status'], 'healthy')
        self.assertEqual(health_report['metrics']['success_rate'], 98.0)
        self.assertEqual(health_report['metrics']['total_requests_7d'], 1000)
    
    def test_usage_forecast_generation(self):
        """Test generating usage forecast"""
        from .analytics import integration_analytics_service
        import datetime
        
        # Create historical data
        base_date = timezone.now().date() - timedelta(days=30)
        for i in range(30):
            date = base_date + timedelta(days=i)
            requests = 100 + i * 2  # Increasing trend
            
            IntegrationAnalytics.objects.create(
                integration=self.integration,
                date=date,
                total_requests=requests,
                successful_requests=requests - 1,
                failed_requests=1
            )
        
        # Generate forecast
        forecast = integration_analytics_service.generate_usage_forecast(
            self.integration, days_ahead=7
        )
        
        self.assertIn('forecast', forecast)
        self.assertEqual(len(forecast['forecast']), 7)
        self.assertIn('confidence_metrics', forecast)
        self.assertEqual(forecast['confidence_metrics']['trend_direction'], 'increasing')


class IntegrationWebhookTestCase(TestCase):
    """
    Test integration webhook functionality
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
        
        self.provider = IntegrationProvider.objects.create(
            name='stripe',
            display_name='Stripe',
            provider_type='payment',
            base_url='https://api.stripe.com/v1'
        )
        
        self.integration = Integration.objects.create(
            provider=self.provider,
            name='Stripe Integration',
            created_by=self.user
        )
    
    def test_webhook_creation(self):
        """Test creating webhook"""
        webhook = IntegrationWebhook.objects.create(
            integration=self.integration,
            event_type='payment_intent.succeeded',
            payload={
                'id': 'pi_12345',
                'object': 'payment_intent',
                'amount': 10000,
                'currency': 'usd',
                'status': 'succeeded'
            },
            headers={
                'Content-Type': 'application/json',
                'Stripe-Signature': 'test_signature'
            },
            signature='test_signature',
            source_ip='***********'
        )
        
        self.assertEqual(webhook.integration, self.integration)
        self.assertEqual(webhook.event_type, 'payment_intent.succeeded')
        self.assertEqual(webhook.status, 'pending')
        self.assertFalse(webhook.is_verified)
    
    def test_webhook_signature_verification(self):
        """Test webhook signature verification"""
        from .payment_gateways import StripePaymentGateway
        
        gateway = StripePaymentGateway(self.integration)
        
        # Test valid signature
        payload = b'{"test": "data"}'
        secret = 'test_secret'
        
        import hmac
        import hashlib
        expected_signature = hmac.new(
            secret.encode('utf-8'),
            payload,
            hashlib.sha256
        ).hexdigest()
        
        is_valid = gateway.verify_webhook_signature(
            payload, 
            f'sha256={expected_signature}', 
            secret
        )
        
        self.assertTrue(is_valid)
        
        # Test invalid signature
        is_valid = gateway.verify_webhook_signature(
            payload, 
            'sha256=invalid_signature', 
            secret
        )
        
        self.assertFalse(is_valid)


class IntegrationFactoryTestCase(TestCase):
    """
    Test integration factory classes
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
    
    def test_payment_gateway_factory(self):
        """Test payment gateway factory"""
        from .payment_gateways import PaymentGatewayFactory
        
        # Test supported gateways
        supported = PaymentGatewayFactory.get_supported_gateways()
        self.assertIn('stripe', supported)
        self.assertIn('paypal', supported)
        self.assertIn('razorpay', supported)
        
        # Test creating Stripe gateway
        provider = IntegrationProvider.objects.create(
            name='stripe',
            display_name='Stripe',
            provider_type='payment',
            base_url='https://api.stripe.com/v1'
        )
        
        integration = Integration.objects.create(
            provider=provider,
            name='Stripe Integration',
            created_by=self.user
        )
        
        gateway = PaymentGatewayFactory.create_gateway(integration)
        self.assertIsNotNone(gateway)
        
        # Test unsupported gateway
        unsupported_provider = IntegrationProvider.objects.create(
            name='unsupported_gateway',
            display_name='Unsupported Gateway',
            provider_type='payment',
            base_url='https://api.unsupported.com'
        )
        
        unsupported_integration = Integration.objects.create(
            provider=unsupported_provider,
            name='Unsupported Integration',
            created_by=self.user
        )
        
        with self.assertRaises(ValueError):
            PaymentGatewayFactory.create_gateway(unsupported_integration)
    
    def test_email_service_factory(self):
        """Test email service factory"""
        from .email_services import EmailServiceFactory
        
        # Test supported services
        supported = EmailServiceFactory.get_supported_services()
        self.assertIn('sendgrid', supported)
        self.assertIn('mailgun', supported)
        self.assertIn('amazon_ses', supported)
        self.assertIn('smtp', supported)
        
        # Test creating SendGrid service
        provider = IntegrationProvider.objects.create(
            name='sendgrid',
            display_name='SendGrid',
            provider_type='email',
            base_url='https://api.sendgrid.com/v3'
        )
        
        integration = Integration.objects.create(
            provider=provider,
            name='SendGrid Integration',
            created_by=self.user
        )
        
        service = EmailServiceFactory.create_service(integration)
        self.assertIsNotNone(service)
    
    def test_sms_gateway_factory(self):
        """Test SMS gateway factory"""
        from .sms_gateways import SMSGatewayFactory
        
        # Test supported gateways
        supported = SMSGatewayFactory.get_supported_gateways()
        self.assertIn('twilio', supported)
        self.assertIn('nexmo', supported)
        self.assertIn('whatsapp', supported)
        
        # Test creating Twilio gateway
        provider = IntegrationProvider.objects.create(
            name='twilio',
            display_name='Twilio',
            provider_type='sms',
            base_url='https://api.twilio.com/2010-04-01'
        )
        
        integration = Integration.objects.create(
            provider=provider,
            name='Twilio Integration',
            created_by=self.user
        )
        
        gateway = SMSGatewayFactory.create_gateway(integration)
        self.assertIsNotNone(gateway)
    
    def test_cloud_storage_factory(self):
        """Test cloud storage factory"""
        from .cloud_storage import CloudStorageFactory
        
        # Test supported storages
        supported = CloudStorageFactory.get_supported_storages()
        self.assertIn('amazon_s3', supported)
        self.assertIn('google_cloud', supported)
        self.assertIn('azure_blob', supported)
        self.assertIn('dropbox', supported)
        
        # Test creating S3 storage
        provider = IntegrationProvider.objects.create(
            name='amazon_s3',
            display_name='Amazon S3',
            provider_type='storage',
            base_url='https://s3.amazonaws.com'
        )
        
        integration = Integration.objects.create(
            provider=provider,
            name='S3 Integration',
            created_by=self.user
        )
        
        storage = CloudStorageFactory.create_storage(integration)
        self.assertIsNotNone(storage)


class IntegrationPerformanceTestCase(TestCase):
    """
    Test integration performance and load handling
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
        
        self.provider = IntegrationProvider.objects.create(
            name='test_provider',
            display_name='Test Provider',
            provider_type='payment',
            base_url='https://api.test.com'
        )
        
        self.integration = Integration.objects.create(
            provider=self.provider,
            name='Test Integration',
            created_by=self.user
        )
    
    def test_bulk_log_creation(self):
        """Test creating multiple logs efficiently"""
        import time
        
        start_time = time.time()
        
        # Create 100 logs
        logs = []
        for i in range(100):
            logs.append(IntegrationLog(
                integration=self.integration,
                level='info',
                action_type='request',
                message=f'Request {i}',
                status_code=200,
                duration_ms=100 + i
            ))
        
        IntegrationLog.objects.bulk_create(logs)
        
        end_time = time.time()
        
        # Should complete in reasonable time
        self.assertLess(end_time - start_time, 1.0)
        
        # Verify all logs were created
        self.assertEqual(IntegrationLog.objects.filter(integration=self.integration).count(), 100)
    
    def test_analytics_calculation_performance(self):
        """Test analytics calculation performance"""
        from .analytics import integration_analytics_service
        import time
        
        # Create test data
        logs = []
        for i in range(1000):
            logs.append(IntegrationLog(
                integration=self.integration,
                level='info',
                action_type='request',
                message=f'Request {i}',
                status_code=200 if i % 10 != 0 else 400,  # 10% error rate
                duration_ms=100 + (i % 500)
            ))
        
        IntegrationLog.objects.bulk_create(logs)
        
        # Measure analytics calculation time
        start_time = time.time()
        analytics = integration_analytics_service.update_daily_analytics(self.integration)
        end_time = time.time()
        
        # Should complete in reasonable time
        self.assertLess(end_time - start_time, 2.0)
        
        # Verify calculations are correct
        self.assertEqual(analytics.total_requests, 1000)
        self.assertEqual(analytics.successful_requests, 900)
        self.assertEqual(analytics.failed_requests, 100)
        self.assertEqual(analytics.success_rate, 90.0)


class IntegrationSecurityTestCase(TestCase):
    """
    Test integration security features
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
        
        self.provider = IntegrationProvider.objects.create(
            name='test_provider',
            display_name='Test Provider',
            provider_type='payment',
            base_url='https://api.test.com'
        )
    
    def test_credential_encryption(self):
        """Test that credentials are properly encrypted"""
        from .encryption import IntegrationEncryption
        
        encryption = IntegrationEncryption()
        
        # Test sensitive data encryption
        sensitive_data = {
            'api_key': 'sk_live_12345',
            'api_secret': 'secret_value_67890',
            'webhook_secret': 'whsec_test123'
        }
        
        encrypted_data = encryption.encrypt_dict(sensitive_data)
        
        # Verify that sensitive values are encrypted
        self.assertNotEqual(sensitive_data['api_key'], encrypted_data['api_key'])
        self.assertNotEqual(sensitive_data['api_secret'], encrypted_data['api_secret'])
        self.assertNotEqual(sensitive_data['webhook_secret'], encrypted_data['webhook_secret'])
        
        # Verify decryption works correctly
        decrypted_data = encryption.decrypt_dict(encrypted_data)
        self.assertEqual(sensitive_data, decrypted_data)
    
    def test_integration_access_control(self):
        """Test integration access control"""
        # Create integration
        integration = Integration.objects.create(
            provider=self.provider,
            name='Test Integration',
            credentials={'api_key': 'encrypted_key'},
            created_by=self.user
        )
        
        # Test that only the creator can access
        self.assertEqual(integration.created_by, self.user)
        
        # Create another user
        other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='otherpass123',
            user_type='teacher'
        )
        
        # Other user should not be able to create integrations for this provider
        # (This would be enforced at the view level)
        self.assertNotEqual(integration.created_by, other_user)
    
    def test_webhook_signature_validation(self):
        """Test webhook signature validation"""
        from .payment_gateways import StripePaymentGateway
        
        integration = Integration.objects.create(
            provider=self.provider,
            name='Test Integration',
            created_by=self.user
        )
        
        gateway = StripePaymentGateway(integration)
        
        # Test with valid signature
        payload = b'{"test": "webhook_data"}'
        secret = 'webhook_secret_key'
        
        import hmac
        import hashlib
        valid_signature = hmac.new(
            secret.encode('utf-8'),
            payload,
            hashlib.sha256
        ).hexdigest()
        
        # Should validate correctly
        is_valid = gateway.verify_webhook_signature(
            payload, 
            f'sha256={valid_signature}', 
            secret
        )
        self.assertTrue(is_valid)
        
        # Test with invalid signature
        is_valid = gateway.verify_webhook_signature(
            payload, 
            'sha256=invalid_signature', 
            secret
        )
        self.assertFalse(is_valid)
    
    def test_rate_limiting_tracking(self):
        """Test rate limiting tracking"""
        integration = Integration.objects.create(
            provider=self.provider,
            name='Test Integration',
            created_by=self.user
        )
        
        # Simulate multiple requests
        for i in range(10):
            integration.update_stats(success=True)
        
        # Check that stats are tracked
        self.assertEqual(integration.total_requests, 10)
        self.assertEqual(integration.successful_requests, 10)
        
        # In a real implementation, rate limiting would be enforced
        # based on these statistics and provider limits
        integration = Integration.objects.create(
            provider=provider,
            name='S3 Storage',
            credentials={'access_key_id': 'AKIA12345', 'secret_access_key': 'secret'},
            settings={'bucket_name': 'school-erp-files', 'region': 'us-east-1'},
            created_by=self.user
        )
        
        storage = AmazonS3Storage(integration)
        self.assertEqual(storage.integration, integration)
        self.assertEqual(storage.provider, provider)
        self.assertEqual(storage.bucket_name, 'school-erp-files')
    
    def test_google_cloud_storage(self):
        """Test Google Cloud Storage integration"""
        from .cloud_storage import GoogleCloudStorage
        
        provider = IntegrationProvider.objects.create(
            name='google_cloud',
            display_name='Google Cloud Storage',
            provider_type='storage',
            base_url='https://storage.googleapis.com'
        )
        
        integration = Integration.objects.create(
            provider=provider,
            name='GCS Storage',
            credentials={'service_account_key': '{"type": "service_account"}'},
            settings={'bucket_name': 'school-erp-files', 'project_id': 'school-project'},
            created_by=self.user
        )
        
        storage = GoogleCloudStorage(integration)
        self.assertEqual(storage.integration, integration)
        self.assertEqual(storage.provider, provider)
        self.assertEqual(storage.bucket_name, 'school-erp-files')
    
    def test_azure_blob_storage(self):
        """Test Azure Blob Storage integration"""
        from .cloud_storage import AzureBlobStorage
        
        provider = IntegrationProvider.objects.create(
            name='azure_blob',
            display_name='Azure Blob Storage',
            provider_type='storage',
            base_url='https://schoolerp.blob.core.windows.net'
        )
        
        integration = Integration.objects.create(
            provider=provider,
            name='Azure Storage',
            credentials={'account_name': 'schoolerp', 'account_key': 'key123'},
            settings={'container_name': 'files'},
            created_by=self.user
        )
        
        storage = AzureBlobStorage(integration)
        self.assertEqual(storage.integration, integration)
        self.assertEqual(storage.provider, provider)
        self.assertEqual(storage.container_name, 'files')
    
    def test_dropbox_storage(self):
        """Test Dropbox storage integration"""
        from .cloud_storage import DropboxStorage
        
        provider = IntegrationProvider.objects.create(
            name='dropbox',
            display_name='Dropbox',
            provider_type='storage',
            base_url='https://api.dropboxapi.com'
        )
        
        integration = Integration.objects.create(
            provider=provider,
            name='Dropbox Storage',
            credentials={'access_token': 'token123'},
            created_by=self.user
        )
        
        storage = DropboxStorage(integration)
        self.assertEqual(storage.integration, integration)
        self.assertEqual(storage.provider, provider)


class IntegrationAnalyticsTestCase(TestCase):
    """
    Test integration analytics functionality
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
        
        self.provider = IntegrationProvider.objects.create(
            name='test_provider',
            display_name='Test Provider',
            provider_type='payment',
            base_url='https://api.test.com'
        )
        
        self.integration = Integration.objects.create(
            provider=self.provider,
            name='Test Integration',
            created_by=self.user
        )
    
    def test_integration_analytics_creation(self):
        """Test creating integration analytics"""
        analytics = IntegrationAnalytics.objects.create(
            integration=self.integration,
            date=timezone.now().date(),
            total_requests=100,
            successful_requests=95,
            failed_requests=5,
            avg_response_time_ms=250.5,
            min_response_time_ms=100,
            max_response_time_ms=500,
            data_sent_bytes=1024,
            data_received_bytes=2048,
            error_types={'HTTP_500': 3, 'HTTP_404': 2},
            hourly_distribution={'9': 20, '10': 30, '11': 25, '12': 25},
            endpoint_usage={'/payments': 60, '/refunds': 40}
        )
        
        self.assertEqual(analytics.integration, self.integration)
        self.assertEqual(analytics.total_requests, 100)
        self.assertEqual(analytics.successful_requests, 95)
        self.assertEqual(analytics.failed_requests, 5)
        self.assertEqual(analytics.success_rate, 95.0)
        self.assertEqual(analytics.avg_response_time_ms, 250.5)
    
    def test_update_daily_analytics(self):
        """Test updating daily analytics"""
        from .analytics import IntegrationAnalyticsService
        
        # Create some test logs
        today = timezone.now().date()
        
        # Create request logs
        IntegrationLog.objects.create(
            integration=self.integration,
            level='info',
            action_type='request',
            message='GET /payments',
            status_code=200,
            duration_ms=150,
            timestamp=timezone.now()
        )
        
        IntegrationLog.objects.create(
            integration=self.integration,
            level='error',
            action_type='request',
            message='POST /payments',
            status_code=500,
            duration_ms=300,
            timestamp=timezone.now()
        )
        
        # Update analytics
        service = IntegrationAnalyticsService()
        analytics = service.update_daily_analytics(self.integration, today)
        
        self.assertEqual(analytics.total_requests, 2)
        self.assertEqual(analytics.successful_requests, 1)
        self.assertEqual(analytics.failed_requests, 1)
        self.assertEqual(analytics.success_rate, 50.0)
        self.assertEqual(analytics.avg_response_time_ms, 225.0)  # (150 + 300) / 2
    
    def test_generate_integration_summary(self):
        """Test generating integration summary"""
        from .analytics import IntegrationAnalyticsService
        
        # Create test analytics data
        today = timezone.now().date()
        yesterday = today - timezone.timedelta(days=1)
        
        IntegrationAnalytics.objects.create(
            integration=self.integration,
            date=today,
            total_requests=50,
            successful_requests=48,
            failed_requests=2,
            avg_response_time_ms=200
        )
        
        IntegrationAnalytics.objects.create(
            integration=self.integration,
            date=yesterday,
            total_requests=60,
            successful_requests=55,
            failed_requests=5,
            avg_response_time_ms=250
        )
        
        # Generate summary
        service = IntegrationAnalyticsService()
        summary = service.generate_integration_summary(self.integration, days=2)
        
        self.assertEqual(summary['integration']['name'], 'Test Integration')
        self.assertEqual(summary['summary_stats']['total_requests'], 110)
        self.assertEqual(summary['summary_stats']['successful_requests'], 103)
        self.assertEqual(summary['summary_stats']['failed_requests'], 7)
        self.assertAlmostEqual(summary['summary_stats']['success_rate'], 93.64, places=1)
        self.assertEqual(len(summary['daily_trends']), 2)
    
    def test_generate_health_report(self):
        """Test generating integration health report"""
        from .analytics import IntegrationAnalyticsService
        
        # Create test analytics data with good health
        today = timezone.now().date()
        
        IntegrationAnalytics.objects.create(
            integration=self.integration,
            date=today,
            total_requests=100,
            successful_requests=98,
            failed_requests=2,
            avg_response_time_ms=150
        )
        
        # Generate health report
        service = IntegrationAnalyticsService()
        report = service.generate_health_report(self.integration)
        
        self.assertEqual(report['integration']['name'], 'Test Integration')
        self.assertEqual(report['health_status'], 'healthy')
        self.assertEqual(report['health_score'], 98.0)
        self.assertEqual(len(report['health_issues']), 0)
        self.assertEqual(report['metrics']['success_rate'], 98.0)
        self.assertEqual(report['metrics']['avg_response_time_ms'], 150.0)
    
    def test_generate_usage_forecast(self):
        """Test generating usage forecast"""
        from .analytics import IntegrationAnalyticsService
        
        # Create historical data for forecasting
        base_date = timezone.now().date() - timezone.timedelta(days=30)
        
        for i in range(30):
            date = base_date + timezone.timedelta(days=i)
            requests = 100 + i * 2  # Increasing trend
            
            IntegrationAnalytics.objects.create(
                integration=self.integration,
                date=date,
                total_requests=requests,
                successful_requests=requests - 1,
                failed_requests=1
            )
        
        # Generate forecast
        service = IntegrationAnalyticsService()
        forecast = service.generate_usage_forecast(self.integration, days_ahead=7)
        
        self.assertEqual(forecast['integration']['name'], 'Test Integration')
        self.assertEqual(len(forecast['forecast']), 7)
        self.assertEqual(forecast['forecast_period']['days'], 7)
        self.assertIn('confidence_percentage', forecast['confidence_metrics'])
        self.assertIn('trend_direction', forecast['confidence_metrics'])
    
    def test_generate_cost_analysis(self):
        """Test generating cost analysis"""
        from .analytics import IntegrationAnalyticsService
        
        # Create test analytics data
        today = timezone.now().date()
        
        IntegrationAnalytics.objects.create(
            integration=self.integration,
            date=today,
            total_requests=1000,
            successful_requests=950,
            failed_requests=50,
            data_sent_bytes=1024 * 1024,  # 1 MB
            data_received_bytes=2 * 1024 * 1024  # 2 MB
        )
        
        # Generate cost analysis
        service = IntegrationAnalyticsService()
        analysis = service.generate_cost_analysis(self.integration, days=1)
        
        self.assertEqual(analysis['integration']['name'], 'Test Integration')
        self.assertEqual(analysis['usage_metrics']['total_requests'], 1000)
        self.assertEqual(analysis['usage_metrics']['total_data_transfer_mb'], 3.0)
        self.assertIn('total_estimated_cost', analysis['cost_analysis'])
        self.assertEqual(analysis['cost_analysis']['currency'], 'USD')


class IntegrationFactoryTestCase(TestCase):
    """
    Test integration factory classes
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
    
    def test_payment_gateway_factory(self):
        """Test payment gateway factory"""
        from .payment_gateways import PaymentGatewayFactory
        
        # Test supported gateways
        supported = PaymentGatewayFactory.get_supported_gateways()
        self.assertIn('stripe', supported)
        self.assertIn('paypal', supported)
        self.assertIn('razorpay', supported)
        
        # Test creating Stripe gateway
        provider = IntegrationProvider.objects.create(
            name='stripe',
            display_name='Stripe',
            provider_type='payment',
            base_url='https://api.stripe.com/v1'
        )
        
        integration = Integration.objects.create(
            provider=provider,
            name='Stripe Integration',
            created_by=self.user
        )
        
        gateway = PaymentGatewayFactory.create_gateway(integration)
        self.assertIsNotNone(gateway)
        
        # Test unsupported gateway
        unsupported_provider = IntegrationProvider.objects.create(
            name='unsupported_gateway',
            display_name='Unsupported Gateway',
            provider_type='payment',
            base_url='https://api.unsupported.com'
        )
        
        unsupported_integration = Integration.objects.create(
            provider=unsupported_provider,
            name='Unsupported Integration',
            created_by=self.user
        )
        
        with self.assertRaises(ValueError):
            PaymentGatewayFactory.create_gateway(unsupported_integration)
    
    def test_email_service_factory(self):
        """Test email service factory"""
        from .email_services import EmailServiceFactory
        
        # Test supported services
        supported = EmailServiceFactory.get_supported_services()
        self.assertIn('sendgrid', supported)
        self.assertIn('mailgun', supported)
        self.assertIn('amazon_ses', supported)
        self.assertIn('smtp', supported)
    
    def test_sms_gateway_factory(self):
        """Test SMS gateway factory"""
        from .sms_gateways import SMSGatewayFactory
        
        # Test supported gateways
        supported = SMSGatewayFactory.get_supported_gateways()
        self.assertIn('twilio', supported)
        self.assertIn('nexmo', supported)
        self.assertIn('whatsapp', supported)
    
    def test_cloud_storage_factory(self):
        """Test cloud storage factory"""
        from .cloud_storage import CloudStorageFactory
        
        # Test supported storages
        supported = CloudStorageFactory.get_supported_storages()
        self.assertIn('amazon_s3', supported)
        self.assertIn('google_cloud', supported)
        self.assertIn('azure_blob', supported)
        self.assertIn('dropbox', supported)


if __name__ == '__main__':
    import django
    from django.conf import settings
    from django.test.utils import get_runner
    
    django.setup()
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    failures = test_runner.run_tests(['core.integrations.tests'])


class IntegrationAnalyticsTestCase(TestCase):
    """
    Test integration analytics functionality
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
        
        self.provider = IntegrationProvider.objects.create(
            name='test_provider',
            display_name='Test Provider',
            provider_type='payment',
            base_url='https://api.test.com'
        )
        
        self.integration = Integration.objects.create(
            provider=self.provider,
            name='Test Integration',
            created_by=self.user
        )
    
    def test_daily_analytics_update(self):
        """Test updating daily analytics"""
        from .analytics import integration_analytics_service
        
        # Create some test logs
        IntegrationLog.objects.create(
            integration=self.integration,
            level='info',
            action_type='request',
            message='GET /test',
            status_code=200,
            duration_ms=150
        )
        
        IntegrationLog.objects.create(
            integration=self.integration,
            level='error',
            action_type='request',
            message='POST /test',
            status_code=400,
            duration_ms=200
        )
        
        # Update analytics
        analytics = integration_analytics_service.update_daily_analytics(self.integration)
        
        # Verify analytics
        self.assertEqual(analytics.total_requests, 2)
        self.assertEqual(analytics.successful_requests, 1)
        self.assertEqual(analytics.failed_requests, 1)
        self.assertEqual(analytics.success_rate, 50.0)
        self.assertEqual(analytics.avg_response_time_ms, 175.0)
    
    def test_integration_summary_generation(self):
        """Test generating integration summary"""
        from .analytics import integration_analytics_service
        
        # Create analytics record
        IntegrationAnalytics.objects.create(
            integration=self.integration,
            date=timezone.now().date(),
            total_requests=100,
            successful_requests=95,
            failed_requests=5,
            avg_response_time_ms=200
        )
        
        # Generate summary
        summary = integration_analytics_service.generate_integration_summary(
            self.integration, days=30
        )
        
        # Verify summary structure
        self.assertIn('integration', summary)
        self.assertIn('period', summary)
        self.assertIn('summary_stats', summary)
        self.assertIn('daily_trends', summary)
        self.assertIn('error_analysis', summary)
        
        # Verify summary data
        self.assertEqual(summary['integration']['name'], 'Test Integration')
        self.assertEqual(summary['summary_stats']['total_requests'], 100)
        self.assertEqual(summary['summary_stats']['success_rate'], 95.0)
    
    def test_health_report_generation(self):
        """Test generating health report"""
        from .analytics import integration_analytics_service
        
        # Create analytics with good health
        IntegrationAnalytics.objects.create(
            integration=self.integration,
            date=timezone.now().date(),
            total_requests=1000,
            successful_requests=980,
            failed_requests=20,
            avg_response_time_ms=150
        )
        
        # Generate health report
        health_report = integration_analytics_service.generate_health_report(
            self.integration
        )
        
        # Verify health report structure
        self.assertIn('integration', health_report)
        self.assertIn('health_status', health_report)
        self.assertIn('health_score', health_report)
        self.assertIn('metrics', health_report)
        
        # Verify health status
        self.assertEqual(health_report['health_status'], 'healthy')
        self.assertEqual(health_report['health_score'], 98.0)
    
    def test_usage_forecast_generation(self):
        """Test generating usage forecast"""
        from .analytics import integration_analytics_service
        from datetime import timedelta
        
        # Create historical data
        base_date = timezone.now().date() - timedelta(days=30)
        for i in range(30):
            IntegrationAnalytics.objects.create(
                integration=self.integration,
                date=base_date + timedelta(days=i),
                total_requests=100 + i,  # Increasing trend
                successful_requests=95 + i,
                failed_requests=5
            )
        
        # Generate forecast
        forecast = integration_analytics_service.generate_usage_forecast(
            self.integration, days_ahead=7
        )
        
        # Verify forecast structure
        self.assertIn('integration', forecast)
        self.assertIn('forecast_period', forecast)
        self.assertIn('historical_data', forecast)
        self.assertIn('forecast', forecast)
        self.assertIn('confidence_metrics', forecast)
        
        # Verify forecast data
        self.assertEqual(len(forecast['forecast']), 7)
        self.assertGreater(forecast['confidence_metrics']['confidence_percentage'], 0)


class IntegrationAPITestCase(TestCase):
    """
    Test integration API endpoints
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
        
        self.provider = IntegrationProvider.objects.create(
            name='test_provider',
            display_name='Test Provider',
            provider_type='payment',
            base_url='https://api.test.com',
            required_credentials=['api_key']
        )
        
        self.integration = Integration.objects.create(
            provider=self.provider,
            name='Test Integration',
            credentials={'api_key': 'encrypted_key'},
            created_by=self.user
        )
    
    def test_provider_list_api(self):
        """Test provider list API endpoint"""
        from rest_framework.test import APIClient
        
        client = APIClient()
        client.force_authenticate(user=self.user)
        
        response = client.get('/integrations/api/providers/')
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['name'], 'test_provider')
    
    def test_integration_list_api(self):
        """Test integration list API endpoint"""
        from rest_framework.test import APIClient
        
        client = APIClient()
        client.force_authenticate(user=self.user)
        
        response = client.get('/integrations/api/integrations/')
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['name'], 'Test Integration')
    
    def test_integration_create_api(self):
        """Test integration creation API endpoint"""
        from rest_framework.test import APIClient
        
        client = APIClient()
        client.force_authenticate(user=self.user)
        
        data = {
            'provider': str(self.provider.id),
            'name': 'New Integration',
            'description': 'Test integration',
            'credentials': {'api_key': 'new_key'},
            'settings': {'timeout': 30}
        }
        
        response = client.post('/integrations/api/integrations/', data, format='json')
        
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data['name'], 'New Integration')
        
        # Verify integration was created
        self.assertTrue(Integration.objects.filter(name='New Integration').exists())
    
    @patch.object(IntegrationManager, 'test_integration')
    def test_integration_test_connection_api(self, mock_test):
        """Test integration test connection API endpoint"""
        from rest_framework.test import APIClient
        
        mock_test.return_value = (True, "Connection successful")
        
        client = APIClient()
        client.force_authenticate(user=self.user)
        
        response = client.post(f'/integrations/api/integrations/{self.integration.id}/test_connection/')
        
        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['message'], "Connection successful")
    
    def test_integration_analytics_api(self):
        """Test integration analytics API endpoint"""
        from rest_framework.test import APIClient
        
        # Create analytics data
        IntegrationAnalytics.objects.create(
            integration=self.integration,
            date=timezone.now().date(),
            total_requests=100,
            successful_requests=95,
            failed_requests=5
        )
        
        client = APIClient()
        client.force_authenticate(user=self.user)
        
        response = client.get(f'/integrations/api/integrations/{self.integration.id}/analytics/')
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('summary_stats', response.data)
        self.assertEqual(response.data['summary_stats']['total_requests'], 100)
    
    def test_integration_health_api(self):
        """Test integration health API endpoint"""
        from rest_framework.test import APIClient
        
        # Create analytics data
        IntegrationAnalytics.objects.create(
            integration=self.integration,
            date=timezone.now().date(),
            total_requests=1000,
            successful_requests=980,
            failed_requests=20
        )
        
        client = APIClient()
        client.force_authenticate(user=self.user)
        
        response = client.get(f'/integrations/api/integrations/{self.integration.id}/health/')
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('health_status', response.data)
        self.assertIn('health_score', response.data)
    
    def test_integration_logs_api(self):
        """Test integration logs API endpoint"""
        from rest_framework.test import APIClient
        
        # Create test logs
        IntegrationLog.objects.create(
            integration=self.integration,
            level='info',
            action_type='request',
            message='Test log message',
            user=self.user
        )
        
        client = APIClient()
        client.force_authenticate(user=self.user)
        
        response = client.get(f'/integrations/api/integrations/{self.integration.id}/logs/')
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('results', response.data)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['message'], 'Test log message')


class IntegrationWebhookTestCase(TestCase):
    """
    Test integration webhook functionality
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
        
        self.provider = IntegrationProvider.objects.create(
            name='stripe',
            display_name='Stripe',
            provider_type='payment',
            base_url='https://api.stripe.com/v1'
        )
        
        self.integration = Integration.objects.create(
            provider=self.provider,
            name='Stripe Integration',
            created_by=self.user
        )
    
    def test_webhook_handler(self):
        """Test webhook handler endpoint"""
        from django.test import Client
        import json
        
        client = Client()
        
        webhook_data = {
            'type': 'payment_intent.succeeded',
            'data': {
                'object': {
                    'id': 'pi_12345',
                    'amount': 10000,
                    'currency': 'usd'
                }
            }
        }
        
        response = client.post(
            f'/integrations/webhook/{self.integration.id}/',
            data=json.dumps(webhook_data),
            content_type='application/json',
            HTTP_STRIPE_SIGNATURE='test_signature'
        )
        
        self.assertEqual(response.status_code, 200)
        
        # Verify webhook was created
        webhook = IntegrationWebhook.objects.get(integration=self.integration)
        self.assertEqual(webhook.event_type, 'payment_intent.succeeded')
        self.assertEqual(webhook.payload['data']['object']['id'], 'pi_12345')
        self.assertEqual(webhook.signature, 'test_signature')
        self.assertEqual(webhook.status, 'pending')
    
    def test_webhook_handler_invalid_integration(self):
        """Test webhook handler with invalid integration ID"""
        from django.test import Client
        import json
        import uuid
        
        client = Client()
        
        webhook_data = {'type': 'test_event'}
        
        response = client.post(
            f'/integrations/webhook/{uuid.uuid4()}/',
            data=json.dumps(webhook_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 404)


class IntegrationFactoryTestCase(TestCase):
    """
    Test integration factory classes
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
    
    def test_payment_gateway_factory(self):
        """Test payment gateway factory"""
        from .payment_gateways import PaymentGatewayFactory, StripePaymentGateway
        
        provider = IntegrationProvider.objects.create(
            name='stripe',
            display_name='Stripe',
            provider_type='payment',
            base_url='https://api.stripe.com/v1'
        )
        
        integration = Integration.objects.create(
            provider=provider,
            name='Stripe Integration',
            created_by=self.user
        )
        
        gateway = PaymentGatewayFactory.create_gateway(integration)
        self.assertIsInstance(gateway, StripePaymentGateway)
    
    def test_email_service_factory(self):
        """Test email service factory"""
        from .email_services import EmailServiceFactory, SendGridEmailService
        
        provider = IntegrationProvider.objects.create(
            name='sendgrid',
            display_name='SendGrid',
            provider_type='email',
            base_url='https://api.sendgrid.com/v3'
        )
        
        integration = Integration.objects.create(
            provider=provider,
            name='SendGrid Integration',
            created_by=self.user
        )
        
        service = EmailServiceFactory.create_service(integration)
        self.assertIsInstance(service, SendGridEmailService)
    
    def test_sms_gateway_factory(self):
        """Test SMS gateway factory"""
        from .sms_gateways import SMSGatewayFactory, TwilioSMSGateway
        
        provider = IntegrationProvider.objects.create(
            name='twilio',
            display_name='Twilio',
            provider_type='sms',
            base_url='https://api.twilio.com/2010-04-01'
        )
        
        integration = Integration.objects.create(
            provider=provider,
            name='Twilio Integration',
            created_by=self.user
        )
        
        gateway = SMSGatewayFactory.create_gateway(integration)
        self.assertIsInstance(gateway, TwilioSMSGateway)
    
    def test_cloud_storage_factory(self):
        """Test cloud storage factory"""
        from .cloud_storage import CloudStorageFactory, AmazonS3Storage
        
        provider = IntegrationProvider.objects.create(
            name='amazon_s3',
            display_name='Amazon S3',
            provider_type='storage',
            base_url='https://s3.amazonaws.com'
        )
        
        integration = Integration.objects.create(
            provider=provider,
            name='S3 Integration',
            created_by=self.user
        )
        
        storage = CloudStorageFactory.create_storage(integration)
        self.assertIsInstance(storage, AmazonS3Storage)
    
    def test_unsupported_provider_factory(self):
        """Test factory with unsupported provider"""
        from .payment_gateways import PaymentGatewayFactory
        
        provider = IntegrationProvider.objects.create(
            name='unsupported_gateway',
            display_name='Unsupported Gateway',
            provider_type='payment',
            base_url='https://api.unsupported.com'
        )
        
        integration = Integration.objects.create(
            provider=provider,
            name='Unsupported Integration',
            created_by=self.user
        )
        
        with self.assertRaises(ValueError):
            PaymentGatewayFactory.create_gateway(integration)


class IntegrationManagementCommandTestCase(TestCase):
    """
    Test integration management commands
    """
    
    def test_setup_integration_providers_command(self):
        """Test setup integration providers command"""
        from django.core.management import call_command
        from io import StringIO
        
        out = StringIO()
        call_command('setup_integration_providers', stdout=out)
        
        # Verify providers were created
        self.assertTrue(IntegrationProvider.objects.filter(name='stripe').exists())
        self.assertTrue(IntegrationProvider.objects.filter(name='sendgrid').exists())
        self.assertTrue(IntegrationProvider.objects.filter(name='twilio').exists())
        
        # Verify output
        output = out.getvalue()
        self.assertIn('Setting up default integration providers', output)
        self.assertIn('Completed:', output)
    
    def test_test_integrations_command(self):
        """Test test integrations command"""
        from django.core.management import call_command
        from io import StringIO
        
        # Create test integration
        provider = IntegrationProvider.objects.create(
            name='test_provider',
            display_name='Test Provider',
            provider_type='payment',
            base_url='https://api.test.com'
        )
        
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
        
        Integration.objects.create(
            provider=provider,
            name='Test Integration',
            is_enabled=True,
            created_by=user
        )
        
        out = StringIO()
        
        with patch.object(IntegrationManager, 'test_integration') as mock_test:
            mock_test.return_value = (True, "Connection successful")
            
            call_command('test_integrations', stdout=out)
            
            # Verify command ran
            output = out.getvalue()
            self.assertIn('Testing 1 integration(s)', output)
            self.assertIn('TEST SUMMARY', output)


class IntegrationPerformanceTestCase(TestCase):
    """
    Test integration performance and load handling
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
        
        self.provider = IntegrationProvider.objects.create(
            name='test_provider',
            display_name='Test Provider',
            provider_type='payment',
            base_url='https://api.test.com'
        )
        
        self.integration = Integration.objects.create(
            provider=self.provider,
            name='Test Integration',
            created_by=self.user
        )
    
    def test_bulk_log_creation_performance(self):
        """Test performance of bulk log creation"""
        import time
        
        start_time = time.time()
        
        # Create 1000 logs
        logs = []
        for i in range(1000):
            logs.append(IntegrationLog(
                integration=self.integration,
                level='info',
                action_type='request',
                message=f'Test log {i}',
                status_code=200,
                duration_ms=100 + i
            ))
        
        IntegrationLog.objects.bulk_create(logs)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Should complete within reasonable time (adjust threshold as needed)
        self.assertLess(duration, 5.0)  # 5 seconds
        
        # Verify logs were created
        self.assertEqual(IntegrationLog.objects.filter(integration=self.integration).count(), 1000)
    
    def test_analytics_calculation_performance(self):
        """Test performance of analytics calculation"""
        from .analytics import integration_analytics_service
        import time
        
        # Create test logs
        logs = []
        for i in range(1000):
            logs.append(IntegrationLog(
                integration=self.integration,
                level='info',
                action_type='request',
                message=f'Test log {i}',
                status_code=200 if i % 10 != 0 else 400,  # 10% error rate
                duration_ms=100 + (i % 500)
            ))
        
        IntegrationLog.objects.bulk_create(logs)
        
        start_time = time.time()
        
        # Update analytics
        analytics = integration_analytics_service.update_daily_analytics(self.integration)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Should complete within reasonable time
        self.assertLess(duration, 2.0)  # 2 seconds
        
        # Verify analytics are correct
        self.assertEqual(analytics.total_requests, 1000)
        self.assertEqual(analytics.successful_requests, 900)
        self.assertEqual(analytics.failed_requests, 100)
        self.assertEqual(analytics.success_rate, 90.0)
    
    def test_concurrent_integration_access(self):
        """Test concurrent access to integration"""
        import threading
        import time
        
        results = []
        errors = []
        
        def update_stats():
            try:
                for _ in range(100):
                    self.integration.update_stats(success=True)
                results.append('success')
            except Exception as e:
                errors.append(str(e))
        
        # Create multiple threads
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=update_stats)
            threads.append(thread)
        
        # Start all threads
        start_time = time.time()
        for thread in threads:
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Verify no errors occurred
        self.assertEqual(len(errors), 0)
        self.assertEqual(len(results), 10)
        
        # Verify final stats
        self.integration.refresh_from_db()
        self.assertEqual(self.integration.total_requests, 1000)
        self.assertEqual(self.integration.successful_requests, 1000)
        
        # Should complete within reasonable time
        self.assertLess(duration, 10.0)  # 10 seconds


class IntegrationSecurityTestCase(TestCase):
    """
    Test integration security features
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
        
        self.other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='testpass123',
            user_type='teacher'
        )
        
        self.provider = IntegrationProvider.objects.create(
            name='test_provider',
            display_name='Test Provider',
            provider_type='payment',
            base_url='https://api.test.com'
        )
        
        self.integration = Integration.objects.create(
            provider=self.provider,
            name='Test Integration',
            credentials={'api_key': 'secret_key'},
            created_by=self.user
        )
    
    def test_credential_encryption(self):
        """Test that credentials are properly encrypted"""
        # Verify credentials are encrypted in database
        integration_from_db = Integration.objects.get(id=self.integration.id)
        encrypted_key = integration_from_db.credentials['api_key']
        
        # Should not be the original value
        self.assertNotEqual(encrypted_key, 'secret_key')
        
        # Should be able to decrypt
        from .services import BaseIntegrationService
        service = BaseIntegrationService(integration_from_db)
        decrypted_key = service.get_credential('api_key')
        self.assertEqual(decrypted_key, 'secret_key')
    
    def test_integration_access_control(self):
        """Test that users can only access their own integrations"""
        from rest_framework.test import APIClient
        
        client = APIClient()
        client.force_authenticate(user=self.other_user)
        
        # Should not be able to access other user's integration
        response = client.get('/integrations/api/integrations/')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 0)  # No integrations for other user
        
        # Should not be able to access specific integration
        response = client.get(f'/integrations/api/integrations/{self.integration.id}/')
        self.assertEqual(response.status_code, 404)
    
    def test_webhook_signature_verification(self):
        """Test webhook signature verification"""
        from .payment_gateways import StripePaymentGateway
        
        stripe_provider = IntegrationProvider.objects.create(
            name='stripe',
            display_name='Stripe',
            provider_type='payment',
            base_url='https://api.stripe.com/v1'
        )
        
        stripe_integration = Integration.objects.create(
            provider=stripe_provider,
            name='Stripe Integration',
            created_by=self.user
        )
        
        gateway = StripePaymentGateway(stripe_integration)
        
        # Test valid signature
        payload = b'{"test": "data"}'
        secret = 'whsec_test_secret'
        
        import hmac
        import hashlib
        expected_signature = hmac.new(
            secret.encode('utf-8'),
            payload,
            hashlib.sha256
        ).hexdigest()
        
        is_valid = gateway.verify_webhook_signature(
            payload, 
            f'sha256={expected_signature}', 
            secret
        )
        self.assertTrue(is_valid)
        
        # Test invalid signature
        is_valid = gateway.verify_webhook_signature(
            payload, 
            'sha256=invalid_signature', 
            secret
        )
        self.assertFalse(is_valid)
    
    def test_rate_limiting_awareness(self):
        """Test that integrations are aware of rate limits"""
        # Update provider with rate limits
        self.provider.rate_limits = {
            'requests_per_second': 10,
            'requests_per_hour': 1000
        }
        self.provider.save()
        
        # Verify rate limits are accessible
        from .services import BaseIntegrationService
        service = BaseIntegrationService(self.integration)
        
        rate_limits = service.provider.rate_limits
        self.assertEqual(rate_limits['requests_per_second'], 10)
        self.assertEqual(rate_limits['requests_per_hour'], 1000)
    
    def test_sensitive_data_logging(self):
        """Test that sensitive data is not logged"""
        from .services import BaseIntegrationService
        
        service = BaseIntegrationService(self.integration)
        
        # Log activity with sensitive data
        service.log_activity(
            level='info',
            action_type='request',
            message='API request made',
            request_data={
                'api_key': 'secret_key_123',
                'amount': 100,
                'currency': 'USD'
            }
        )
        
        # Verify log was created
        log = IntegrationLog.objects.get(integration=self.integration)
        
        # Sensitive data should be in request_data (encrypted at rest)
        # but message should not contain sensitive information
        self.assertNotIn('secret_key_123', log.message)
        self.assertEqual(log.request_data['api_key'], 'secret_key_123')


# Run all tests
if __name__ == '__main__':
    import django
    from django.conf import settings
    from django.test.utils import get_runner
    
    django.setup()
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    failures = test_runner.run_tests(['core.integrations.tests'])