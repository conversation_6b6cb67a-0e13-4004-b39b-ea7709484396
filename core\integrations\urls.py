"""
URL configuration for integrations app
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# Create router for API endpoints
router = DefaultRouter()
router.register(r'providers', views.IntegrationProviderViewSet, basename='integrationprovider')
router.register(r'integrations', views.IntegrationViewSet, basename='integration')

app_name = 'integrations'

urlpatterns = [
    # API endpoints
    path('api/', include(router.urls)),
    
    # Dashboard views
    path('', views.IntegrationDashboardView.as_view(), name='dashboard'),
    path('<uuid:pk>/', views.IntegrationDetailView.as_view(), name='detail'),
    
    # Webhook endpoints
    path('webhook/<uuid:integration_id>/', views.webhook_handler, name='webhook'),
    
    # Analytics API
    path('api/analytics/', views.integration_analytics_api, name='analytics_api'),
    path('api/test-all/', views.test_all_integrations, name='test_all'),
]