"""
Views for integration management
"""

import json
from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from django.contrib.admin.views.decorators import staff_member_required
from django.utils.decorators import method_decorator
from django.views.generic import ListView, DetailView
from django.core.paginator import Paginator
from django.db.models import Q, Count, Avg
from django.utils import timezone
from datetime import timedelta

from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from .models import (
    IntegrationProvider, Integration, IntegrationLog, 
    IntegrationWebhook, IntegrationAnalytics
)
from .serializers import (
    IntegrationProviderSerializer, IntegrationSerializer,
    IntegrationLogSerializer, IntegrationAnalyticsSerializer
)
from .services import IntegrationManager
from .analytics import integration_analytics_service


class IntegrationProviderViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for integration providers
    """
    queryset = IntegrationProvider.objects.filter(is_enabled=True)
    serializer_class = IntegrationProviderSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by provider type
        provider_type = self.request.query_params.get('type')
        if provider_type:
            queryset = queryset.filter(provider_type=provider_type)
        
        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        return queryset.order_by('provider_type', 'name')
    
    @action(detail=False, methods=['get'])
    def types(self, request):
        """Get available provider types"""
        types = IntegrationProvider.objects.values_list(
            'provider_type', flat=True
        ).distinct()
        
        return Response({
            'types': [
                {'value': t, 'label': dict(IntegrationProvider.PROVIDER_TYPES)[t]}
                for t in types
            ]
        })


class IntegrationViewSet(viewsets.ModelViewSet):
    """
    ViewSet for integrations
    """
    serializer_class = IntegrationSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return Integration.objects.filter(
            created_by=self.request.user
        ).select_related('provider')
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    @action(detail=True, methods=['post'])
    def test_connection(self, request, pk=None):
        """Test integration connection"""
        integration = self.get_object()
        
        try:
            success, message = IntegrationManager.test_integration(integration)
            
            return Response({
                'success': success,
                'message': message,
                'status': integration.status,
                'tested_at': timezone.now().isoformat()
            })
        
        except Exception as e:
            return Response({
                'success': False,
                'message': str(e),
                'status': 'error'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['get'])
    def analytics(self, request, pk=None):
        """Get integration analytics"""
        integration = self.get_object()
        days = int(request.query_params.get('days', 30))
        
        try:
            summary = integration_analytics_service.generate_integration_summary(
                integration, days=days
            )
            return Response(summary)
        
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['get'])
    def health(self, request, pk=None):
        """Get integration health report"""
        integration = self.get_object()
        
        try:
            health_report = integration_analytics_service.generate_health_report(
                integration
            )
            return Response(health_report)
        
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['get'])
    def logs(self, request, pk=None):
        """Get integration logs"""
        integration = self.get_object()
        
        # Filter parameters
        level = request.query_params.get('level')
        action_type = request.query_params.get('action_type')
        days = int(request.query_params.get('days', 7))
        
        # Build queryset
        logs = IntegrationLog.objects.filter(
            integration=integration,
            timestamp__gte=timezone.now() - timedelta(days=days)
        )
        
        if level:
            logs = logs.filter(level=level)
        
        if action_type:
            logs = logs.filter(action_type=action_type)
        
        logs = logs.order_by('-timestamp')
        
        # Paginate
        page_size = int(request.query_params.get('page_size', 50))
        paginator = Paginator(logs, page_size)
        page_number = request.query_params.get('page', 1)
        page_obj = paginator.get_page(page_number)
        
        serializer = IntegrationLogSerializer(page_obj, many=True)
        
        return Response({
            'results': serializer.data,
            'count': paginator.count,
            'num_pages': paginator.num_pages,
            'current_page': page_obj.number,
            'has_next': page_obj.has_next(),
            'has_previous': page_obj.has_previous()
        })
    
    @action(detail=True, methods=['post'])
    def sync(self, request, pk=None):
        """Sync integration data"""
        integration = self.get_object()
        
        try:
            success, message = IntegrationManager.sync_integration_data(integration)
            
            return Response({
                'success': success,
                'message': message,
                'synced_at': timezone.now().isoformat()
            })
        
        except Exception as e:
            return Response({
                'success': False,
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@method_decorator([login_required, staff_member_required], name='dispatch')
class IntegrationDashboardView(ListView):
    """
    Dashboard view for integration management
    """
    template_name = 'integrations/dashboard.html'
    context_object_name = 'integrations'
    paginate_by = 20
    
    def get_queryset(self):
        return Integration.objects.select_related('provider').order_by('-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Summary statistics
        total_integrations = Integration.objects.count()
        active_integrations = Integration.objects.filter(status='active').count()
        error_integrations = Integration.objects.filter(status='error').count()
        
        # Provider type distribution
        provider_stats = Integration.objects.values(
            'provider__provider_type'
        ).annotate(
            count=Count('id')
        ).order_by('-count')
        
        # Recent activity
        recent_logs = IntegrationLog.objects.select_related(
            'integration'
        ).order_by('-timestamp')[:10]
        
        # Health summary
        health_summary = self.get_health_summary()
        
        context.update({
            'total_integrations': total_integrations,
            'active_integrations': active_integrations,
            'error_integrations': error_integrations,
            'provider_stats': provider_stats,
            'recent_logs': recent_logs,
            'health_summary': health_summary
        })
        
        return context
    
    def get_health_summary(self):
        """Get overall health summary"""
        integrations = Integration.objects.filter(status='active')
        
        healthy_count = 0
        warning_count = 0
        critical_count = 0
        
        for integration in integrations:
            try:
                health_report = integration_analytics_service.generate_health_report(
                    integration
                )
                
                if health_report['health_status'] == 'healthy':
                    healthy_count += 1
                elif health_report['health_status'] == 'warning':
                    warning_count += 1
                else:
                    critical_count += 1
            
            except:
                critical_count += 1
        
        return {
            'healthy': healthy_count,
            'warning': warning_count,
            'critical': critical_count,
            'total': len(integrations)
        }


@method_decorator([login_required, staff_member_required], name='dispatch')
class IntegrationDetailView(DetailView):
    """
    Detail view for a specific integration
    """
    model = Integration
    template_name = 'integrations/detail.html'
    context_object_name = 'integration'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        integration = self.object
        
        # Analytics summary
        try:
            analytics_summary = integration_analytics_service.generate_integration_summary(
                integration, days=30
            )
            context['analytics_summary'] = analytics_summary
        except:
            context['analytics_summary'] = None
        
        # Health report
        try:
            health_report = integration_analytics_service.generate_health_report(
                integration
            )
            context['health_report'] = health_report
        except:
            context['health_report'] = None
        
        # Recent logs
        recent_logs = IntegrationLog.objects.filter(
            integration=integration
        ).order_by('-timestamp')[:20]
        context['recent_logs'] = recent_logs
        
        # Recent webhooks
        recent_webhooks = IntegrationWebhook.objects.filter(
            integration=integration
        ).order_by('-received_at')[:10]
        context['recent_webhooks'] = recent_webhooks
        
        return context


@csrf_exempt
@require_http_methods(["POST"])
def webhook_handler(request, integration_id):
    """
    Generic webhook handler for integrations
    """
    try:
        integration = get_object_or_404(Integration, id=integration_id)
        
        # Parse request data
        payload = json.loads(request.body) if request.body else {}
        headers = dict(request.META)
        
        # Create webhook record
        webhook = IntegrationWebhook.objects.create(
            integration=integration,
            event_type=payload.get('type', 'unknown'),
            payload=payload,
            headers={
                k: v for k, v in headers.items() 
                if k.startswith('HTTP_') or k in ['CONTENT_TYPE', 'CONTENT_LENGTH']
            },
            signature=headers.get('HTTP_STRIPE_SIGNATURE', ''),
            source_ip=request.META.get('REMOTE_ADDR'),
            status='pending'
        )
        
        # TODO: Process webhook asynchronously
        # process_webhook.delay(webhook.id)
        
        return JsonResponse({
            'status': 'received',
            'webhook_id': str(webhook.id)
        })
    
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=400)


@login_required
@staff_member_required
def integration_analytics_api(request):
    """
    API endpoint for integration analytics data
    """
    if request.method != 'GET':
        return JsonResponse({'error': 'Method not allowed'}, status=405)
    
    # Get parameters
    integration_id = request.GET.get('integration_id')
    provider_type = request.GET.get('provider_type')
    days = int(request.GET.get('days', 30))
    report_type = request.GET.get('type', 'summary')
    
    try:
        if integration_id:
            # Single integration analytics
            integration = get_object_or_404(Integration, id=integration_id)
            
            if report_type == 'health':
                data = integration_analytics_service.generate_health_report(integration)
            elif report_type == 'forecast':
                data = integration_analytics_service.generate_usage_forecast(
                    integration, days_ahead=days
                )
            else:
                data = integration_analytics_service.generate_integration_summary(
                    integration, days=days
                )
        
        elif provider_type:
            # Provider comparison
            data = integration_analytics_service.generate_provider_comparison(
                provider_type, days=days
            )
        
        else:
            # Overall analytics
            integrations = Integration.objects.filter(status='active')
            data = {
                'total_integrations': integrations.count(),
                'integrations': []
            }
            
            for integration in integrations[:10]:  # Limit to 10 for performance
                summary = integration_analytics_service.generate_integration_summary(
                    integration, days=days
                )
                data['integrations'].append(summary)
        
        return JsonResponse(data)
    
    except Exception as e:
        return JsonResponse({
            'error': str(e)
        }, status=500)


@login_required
@staff_member_required
def test_all_integrations(request):
    """
    API endpoint to test all integrations
    """
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)
    
    integrations = Integration.objects.filter(is_enabled=True)
    results = []
    
    for integration in integrations:
        try:
            success, message = IntegrationManager.test_integration(integration)
            results.append({
                'integration_id': str(integration.id),
                'integration_name': integration.name,
                'provider': integration.provider.display_name,
                'success': success,
                'message': message,
                'status': integration.status
            })
        except Exception as e:
            results.append({
                'integration_id': str(integration.id),
                'integration_name': integration.name,
                'provider': integration.provider.display_name,
                'success': False,
                'message': str(e),
                'status': 'error'
            })
    
    # Summary
    total = len(results)
    successful = len([r for r in results if r['success']])
    failed = total - successful
    
    return JsonResponse({
        'summary': {
            'total': total,
            'successful': successful,
            'failed': failed,
            'success_rate': (successful / total * 100) if total > 0 else 0
        },
        'results': results,
        'tested_at': timezone.now().isoformat()
    })