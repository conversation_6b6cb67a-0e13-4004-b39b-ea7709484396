"""
Localization utilities for the School ERP system.
Provides utilities for language switching, RTL support, and localization management.
"""

from django.conf import settings
from django.utils import translation
from django.utils.translation import gettext_lazy as _
from django.http import HttpResponseRedirect
from django.urls import reverse
from django.shortcuts import redirect
import re


class LocalizationManager:
    """Manager class for handling localization operations."""
    
    RTL_LANGUAGES = ['ar', 'he', 'fa', 'ur']
    
    @classmethod
    def get_available_languages(cls):
        """Get list of available languages."""
        return settings.LANGUAGES
    
    @classmethod
    def get_current_language(cls):
        """Get current active language."""
        return translation.get_language()
    
    @classmethod
    def is_rtl_language(cls, language_code=None):
        """Check if the given language (or current language) is RTL."""
        if language_code is None:
            language_code = cls.get_current_language()
        return language_code in cls.RTL_LANGUAGES
    
    @classmethod
    def get_language_direction(cls, language_code=None):
        """Get text direction for the given language."""
        return 'rtl' if cls.is_rtl_language(language_code) else 'ltr'
    
    @classmethod
    def get_language_name(cls, language_code):
        """Get display name for language code."""
        for code, name in settings.LANGUAGES:
            if code == language_code:
                return name
        return language_code
    
    @classmethod
    def activate_language(cls, language_code):
        """Activate a specific language."""
        if language_code in [code for code, name in settings.LANGUAGES]:
            translation.activate(language_code)
            return True
        return False
    
    @classmethod
    def get_localized_url(cls, url, language_code):
        """Get localized version of URL."""
        # Remove existing language prefix if present
        url_pattern = r'^/(?:' + '|'.join([code for code, name in settings.LANGUAGES]) + r')/'
        clean_url = re.sub(url_pattern, '/', url)
        
        # Add new language prefix
        if language_code != settings.LANGUAGE_CODE:
            return f'/{language_code}{clean_url}'
        return clean_url


def switch_language(request, language_code):
    """
    View function to switch language.
    Usage: url(r'^switch-language/(?P<language_code>\\w+)/$', switch_language, name='switch_language')
    """
    if LocalizationManager.activate_language(language_code):
        # Store language preference in session
        request.session['django_language'] = language_code
        # Force session save only if it has the save method (not in tests)
        if hasattr(request.session, 'save'):
            request.session.save()
        
        # Get redirect URL from request or use referer
        next_url = request.GET.get('next') or request.META.get('HTTP_REFERER', '/')
        
        # Localize the redirect URL
        localized_url = LocalizationManager.get_localized_url(next_url, language_code)
        
        return HttpResponseRedirect(localized_url)
    
    # If language switch failed, redirect to home
    return redirect('/')


class RTLSupport:
    """Utilities for RTL (Right-to-Left) layout support."""
    
    @staticmethod
    def get_css_classes(language_code=None):
        """Get CSS classes for RTL support."""
        if LocalizationManager.is_rtl_language(language_code):
            return 'rtl-layout text-right'
        return 'ltr-layout text-left'
    
    @staticmethod
    def get_bootstrap_classes(language_code=None):
        """Get Bootstrap classes adjusted for RTL."""
        if LocalizationManager.is_rtl_language(language_code):
            return {
                'float_left': 'float-right',
                'float_right': 'float-left',
                'text_left': 'text-right',
                'text_right': 'text-left',
                'ml_auto': 'mr-auto',
                'mr_auto': 'ml-auto',
                'pl_3': 'pr-3',
                'pr_3': 'pl-3',
            }
        return {
            'float_left': 'float-left',
            'float_right': 'float-right',
            'text_left': 'text-left',
            'text_right': 'text-right',
            'ml_auto': 'ml-auto',
            'mr_auto': 'mr-auto',
            'pl_3': 'pl-3',
            'pr_3': 'pr-3',
        }


class NumberFormatter:
    """Utilities for number formatting in different locales."""
    
    ARABIC_INDIC_DIGITS = '٠١٢٣٤٥٦٧٨٩'
    WESTERN_DIGITS = '0123456789'
    
    @classmethod
    def to_arabic_indic(cls, number_str):
        """Convert Western digits to Arabic-Indic digits."""
        result = str(number_str)
        for western, arabic in zip(cls.WESTERN_DIGITS, cls.ARABIC_INDIC_DIGITS):
            result = result.replace(western, arabic)
        return result
    
    @classmethod
    def to_western_digits(cls, number_str):
        """Convert Arabic-Indic digits to Western digits."""
        result = str(number_str)
        for arabic, western in zip(cls.ARABIC_INDIC_DIGITS, cls.WESTERN_DIGITS):
            result = result.replace(arabic, western)
        return result
    
    @classmethod
    def format_number(cls, number, language_code=None):
        """Format number according to language preferences."""
        if language_code is None:
            language_code = LocalizationManager.get_current_language()
        
        formatted = str(number)
        
        if language_code == 'ar':
            formatted = cls.to_arabic_indic(formatted)
        
        return formatted


class DateFormatter:
    """Utilities for date formatting in different locales."""
    
    ARABIC_MONTHS = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ]
    
    ARABIC_DAYS = [
        'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'
    ]
    
    @staticmethod
    def get_date_format(language_code=None):
        """Get date format for the given language."""
        if language_code is None:
            language_code = LocalizationManager.get_current_language()
        
        formats = {
            'en': '%Y-%m-%d',
            'ar': '%d/%m/%Y',
        }
        
        return formats.get(language_code, '%Y-%m-%d')
    
    @staticmethod
    def get_datetime_format(language_code=None):
        """Get datetime format for the given language."""
        if language_code is None:
            language_code = LocalizationManager.get_current_language()
        
        formats = {
            'en': '%Y-%m-%d %H:%M:%S',
            'ar': '%d/%m/%Y %H:%M:%S',
        }
        
        return formats.get(language_code, '%Y-%m-%d %H:%M:%S')
    
    @classmethod
    def format_date_arabic(cls, date_obj):
        """Format date in Arabic with Arabic month names."""
        if not date_obj:
            return ''
        
        day = NumberFormatter.to_arabic_indic(str(date_obj.day))
        month = cls.ARABIC_MONTHS[date_obj.month - 1]
        year = NumberFormatter.to_arabic_indic(str(date_obj.year))
        
        return f"{day} {month} {year}"
    
    @classmethod
    def format_datetime_arabic(cls, datetime_obj):
        """Format datetime in Arabic with Arabic month names."""
        if not datetime_obj:
            return ''
        
        date_part = cls.format_date_arabic(datetime_obj.date())
        hour = NumberFormatter.to_arabic_indic(str(datetime_obj.hour).zfill(2))
        minute = NumberFormatter.to_arabic_indic(str(datetime_obj.minute).zfill(2))
        
        return f"{date_part} - {hour}:{minute}"
    
    @classmethod
    def get_day_name_arabic(cls, date_obj):
        """Get Arabic day name for the given date."""
        if not date_obj:
            return ''
        
        # Python's weekday() returns 0 for Monday, 6 for Sunday
        # Our ARABIC_DAYS array is ordered Monday to Sunday
        return cls.ARABIC_DAYS[date_obj.weekday()]


# Template tags and filters will be created separately
def get_localization_context():
    """Get localization context for templates."""
    current_language = LocalizationManager.get_current_language()
    
    return {
        'LANGUAGE_CODE': current_language,
        'LANGUAGE_BIDI': LocalizationManager.is_rtl_language(current_language),
        'LANGUAGE_DIRECTION': LocalizationManager.get_language_direction(current_language),
        'AVAILABLE_LANGUAGES': LocalizationManager.get_available_languages(),
        'RTL_CSS_CLASSES': RTLSupport.get_css_classes(current_language),
        'BOOTSTRAP_CLASSES': RTLSupport.get_bootstrap_classes(current_language),
    }