"""
Views for localization management interface.
"""

from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods
from django.utils.translation import gettext_lazy as _
from django.utils import translation
from django.conf import settings
from django.core.management import call_command
from django.db import transaction
import os
import json
import polib
from io import StringIO
from .localization import LocalizationManager
from core.calendar.calendar_models import CalendarPreference


def is_admin_or_translator(user):
    """Check if user is admin or has translation permissions."""
    return user.is_superuser or user.groups.filter(name='Translators').exists()


@login_required
@user_passes_test(is_admin_or_translator)
def localization_dashboard(request):
    """Localization management dashboard."""
    context = {
        'available_languages': LocalizationManager.get_available_languages(),
        'current_language': LocalizationManager.get_current_language(),
        'translation_stats': get_translation_statistics(),
        'recent_updates': get_recent_translation_updates(),
    }
    return render(request, 'core/localization/dashboard.html', context)


@login_required
@user_passes_test(is_admin_or_translator)
def translation_editor(request, language_code=None):
    """Translation editor interface."""
    if language_code is None:
        language_code = 'ar'  # Default to Arabic
    
    if language_code not in [code for code, name in settings.LANGUAGES]:
        messages.error(request, _('Invalid language code.'))
        return redirect('core:localization_dashboard')
    
    po_file_path = os.path.join(settings.BASE_DIR, 'locale', language_code, 'LC_MESSAGES', 'django.po')
    
    if not os.path.exists(po_file_path):
        messages.error(request, _('Translation file not found for this language.'))
        return redirect('core:localization_dashboard')
    
    try:
        po_file = polib.pofile(po_file_path)
        
        # Filter entries based on search and status
        search_query = request.GET.get('search', '')
        status_filter = request.GET.get('status', 'all')
        
        entries = []
        for entry in po_file:
            if search_query and search_query.lower() not in entry.msgid.lower():
                continue
            
            if status_filter == 'untranslated' and entry.msgstr:
                continue
            elif status_filter == 'translated' and not entry.msgstr:
                continue
            elif status_filter == 'fuzzy' and 'fuzzy' not in entry.flags:
                continue
            
            entries.append({
                'msgid': entry.msgid,
                'msgstr': entry.msgstr,
                'msgctxt': entry.msgctxt,
                'flags': entry.flags,
                'occurrences': entry.occurrences,
                'comment': entry.comment,
                'is_fuzzy': 'fuzzy' in entry.flags,
                'is_translated': bool(entry.msgstr),
            })
        
        context = {
            'language_code': language_code,
            'language_name': dict(settings.LANGUAGES)[language_code],
            'entries': entries,
            'search_query': search_query,
            'status_filter': status_filter,
            'total_entries': len(po_file),
            'translated_entries': len([e for e in po_file if e.msgstr]),
            'fuzzy_entries': len([e for e in po_file if 'fuzzy' in e.flags]),
        }
        
        return render(request, 'core/localization/translation_editor.html', context)
        
    except Exception as e:
        messages.error(request, f'Error loading translation file: {str(e)}')
        return redirect('core:localization_dashboard')


@login_required
@user_passes_test(is_admin_or_translator)
@require_http_methods(["POST"])
def update_translation(request):
    """Update a single translation entry."""
    language_code = request.POST.get('language_code')
    msgid = request.POST.get('msgid')
    msgstr = request.POST.get('msgstr')
    
    if not all([language_code, msgid]):
        return JsonResponse({'success': False, 'error': 'Missing required fields'})
    
    po_file_path = os.path.join(settings.BASE_DIR, 'locale', language_code, 'LC_MESSAGES', 'django.po')
    
    try:
        po_file = polib.pofile(po_file_path)
        
        # Find the entry
        entry = po_file.find(msgid)
        if entry:
            entry.msgstr = msgstr
            # Remove fuzzy flag if translation is provided
            if msgstr and 'fuzzy' in entry.flags:
                entry.flags.remove('fuzzy')
            
            po_file.save()
            
            return JsonResponse({
                'success': True,
                'message': _('Translation updated successfully')
            })
        else:
            return JsonResponse({
                'success': False,
                'error': _('Translation entry not found')
            })
            
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Error updating translation: {str(e)}'
        })


@login_required
@user_passes_test(is_admin_or_translator)
@require_http_methods(["POST"])
def compile_translations(request):
    """Compile translation files."""
    try:
        # Capture command output
        output = StringIO()
        call_command('compilemessages', stdout=output)
        
        messages.success(request, _('Translations compiled successfully'))
        return JsonResponse({
            'success': True,
            'message': _('Translations compiled successfully'),
            'output': output.getvalue()
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Error compiling translations: {str(e)}'
        })


@login_required
@user_passes_test(is_admin_or_translator)
def translation_analytics(request):
    """Translation analytics and statistics."""
    analytics_data = {}
    
    for language_code, language_name in settings.LANGUAGES:
        po_file_path = os.path.join(settings.BASE_DIR, 'locale', language_code, 'LC_MESSAGES', 'django.po')
        
        if os.path.exists(po_file_path):
            try:
                po_file = polib.pofile(po_file_path)
                
                total = len(po_file)
                translated = len([e for e in po_file if e.msgstr])
                fuzzy = len([e for e in po_file if 'fuzzy' in e.flags])
                untranslated = total - translated
                
                analytics_data[language_code] = {
                    'name': language_name,
                    'total': total,
                    'translated': translated,
                    'untranslated': untranslated,
                    'fuzzy': fuzzy,
                    'completion_percentage': round((translated / total * 100) if total > 0 else 0, 2),
                }
                
            except Exception as e:
                analytics_data[language_code] = {
                    'name': language_name,
                    'error': str(e)
                }
    
    context = {
        'analytics_data': analytics_data,
        'chart_data': prepare_chart_data(analytics_data),
    }
    
    return render(request, 'core/localization/analytics.html', context)


@login_required
def calendar_preferences(request):
    """User calendar preferences management."""
    preferences, created = CalendarPreference.objects.get_or_create(
        user=request.user,
        defaults={
            'primary_calendar': 'gregorian',
            'show_dual_dates': True,
            'hijri_adjustment': 0,
            'show_islamic_events': True,
        }
    )
    
    if request.method == 'POST':
        preferences.primary_calendar = request.POST.get('primary_calendar', 'gregorian')
        preferences.show_dual_dates = request.POST.get('show_dual_dates') == 'on'
        preferences.hijri_adjustment = int(request.POST.get('hijri_adjustment', 0))
        preferences.show_islamic_events = request.POST.get('show_islamic_events') == 'on'
        preferences.weekend_days = request.POST.get('weekend_days', 'friday,saturday')
        
        preferences.save()
        messages.success(request, _('Calendar preferences updated successfully'))
        return redirect('core:calendar_preferences')
    
    context = {
        'preferences': preferences,
        'calendar_choices': CalendarPreference.CALENDAR_CHOICES,
    }
    
    return render(request, 'core/localization/calendar_preferences.html', context)


@login_required
@user_passes_test(is_admin_or_translator)
def export_translations(request, language_code):
    """Export translations for a specific language."""
    if language_code not in [code for code, name in settings.LANGUAGES]:
        messages.error(request, _('Invalid language code.'))
        return redirect('core:localization_dashboard')
    
    po_file_path = os.path.join(settings.BASE_DIR, 'locale', language_code, 'LC_MESSAGES', 'django.po')
    
    if not os.path.exists(po_file_path):
        messages.error(request, _('Translation file not found.'))
        return redirect('core:localization_dashboard')
    
    try:
        with open(po_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        response = HttpResponse(content, content_type='text/plain; charset=utf-8')
        response['Content-Disposition'] = f'attachment; filename="django_{language_code}.po"'
        return response
        
    except Exception as e:
        messages.error(request, f'Error exporting translations: {str(e)}')
        return redirect('core:localization_dashboard')


@login_required
@user_passes_test(is_admin_or_translator)
def import_translations(request):
    """Import translations from uploaded file."""
    if request.method == 'POST' and request.FILES.get('translation_file'):
        language_code = request.POST.get('language_code')
        translation_file = request.FILES['translation_file']
        
        if language_code not in [code for code, name in settings.LANGUAGES]:
            messages.error(request, _('Invalid language code.'))
            return redirect('core:localization_dashboard')
        
        try:
            # Read uploaded file content
            content = translation_file.read().decode('utf-8')
            
            # Parse PO file content
            po_file = polib.pofile(content)
            
            # Save to the appropriate location
            po_file_path = os.path.join(settings.BASE_DIR, 'locale', language_code, 'LC_MESSAGES', 'django.po')
            po_file.save(po_file_path)
            
            messages.success(request, _('Translations imported successfully'))
            
        except Exception as e:
            messages.error(request, f'Error importing translations: {str(e)}')
    
    return redirect('core:localization_dashboard')


@login_required
@user_passes_test(is_admin_or_translator)
def translation_validation(request):
    """Validate translations for consistency and completeness."""
    validation_results = {}
    
    for language_code, language_name in settings.LANGUAGES:
        if language_code == 'en':  # Skip English as it's the source language
            continue
            
        po_file_path = os.path.join(settings.BASE_DIR, 'locale', language_code, 'LC_MESSAGES', 'django.po')
        
        if os.path.exists(po_file_path):
            try:
                po_file = polib.pofile(po_file_path)
                issues = []
                
                for entry in po_file:
                    # Check for missing translations
                    if not entry.msgstr and entry.msgid:
                        issues.append({
                            'type': 'missing_translation',
                            'msgid': entry.msgid,
                            'message': _('Missing translation')
                        })
                    
                    # Check for fuzzy translations
                    if 'fuzzy' in entry.flags:
                        issues.append({
                            'type': 'fuzzy_translation',
                            'msgid': entry.msgid,
                            'message': _('Fuzzy translation needs review')
                        })
                    
                    # Check for placeholder consistency
                    if entry.msgstr and '%' in entry.msgid:
                        msgid_placeholders = set([p for p in entry.msgid.split() if p.startswith('%')])
                        msgstr_placeholders = set([p for p in entry.msgstr.split() if p.startswith('%')])
                        
                        if msgid_placeholders != msgstr_placeholders:
                            issues.append({
                                'type': 'placeholder_mismatch',
                                'msgid': entry.msgid,
                                'msgstr': entry.msgstr,
                                'message': _('Placeholder mismatch between source and translation')
                            })
                
                validation_results[language_code] = {
                    'name': language_name,
                    'issues': issues,
                    'total_issues': len(issues),
                }
                
            except Exception as e:
                validation_results[language_code] = {
                    'name': language_name,
                    'error': str(e)
                }
    
    context = {
        'validation_results': validation_results,
    }
    
    return render(request, 'core/localization/validation.html', context)


@login_required
@user_passes_test(is_admin_or_translator)
@require_http_methods(["POST"])
def add_language(request):
    """Add a new language to the system."""
    try:
        data = json.loads(request.body)
        language_code = data.get('code', '').lower().strip()
        language_name = data.get('name', '').strip()
        native_name = data.get('native_name', '').strip()
        is_rtl = data.get('is_rtl', False)
        
        if not language_code or not language_name:
            return JsonResponse({
                'success': False,
                'error': _('Language code and name are required')
            })
        
        # Check if language already exists
        existing_languages = [code for code, name in settings.LANGUAGES]
        if language_code in existing_languages:
            return JsonResponse({
                'success': False,
                'error': _('Language already exists')
            })
        
        # Create locale directory structure
        locale_dir = os.path.join(settings.BASE_DIR, 'locale', language_code, 'LC_MESSAGES')
        os.makedirs(locale_dir, exist_ok=True)
        
        # Create initial PO file by copying from English template
        en_po_path = os.path.join(settings.BASE_DIR, 'locale', 'en', 'LC_MESSAGES', 'django.po')
        new_po_path = os.path.join(locale_dir, 'django.po')
        
        if os.path.exists(en_po_path):
            # Copy and modify the English PO file
            po_file = polib.pofile(en_po_path)
            
            # Update header information
            po_file.metadata['Language'] = language_code
            po_file.metadata['Language-Team'] = f'{language_name} <{language_code}@schoolerp.com>'
            po_file.metadata['Last-Translator'] = f'School ERP Team <<EMAIL>>'
            
            # Clear all translations (keep only msgid)
            for entry in po_file:
                entry.msgstr = ''
                if 'fuzzy' in entry.flags:
                    entry.flags.remove('fuzzy')
            
            po_file.save(new_po_path)
        else:
            # Create a basic PO file
            po_file = polib.POFile()
            po_file.metadata = {
                'Project-Id-Version': 'School ERP System 1.0',
                'Report-Msgid-Bugs-To': '<EMAIL>',
                'Language': language_code,
                'Language-Team': f'{language_name} <{language_code}@schoolerp.com>',
                'MIME-Version': '1.0',
                'Content-Type': 'text/plain; charset=UTF-8',
                'Content-Transfer-Encoding': '8bit',
            }
            po_file.save(new_po_path)
        
        # Update Django settings (this would require a restart in production)
        # For now, we'll just return success
        
        return JsonResponse({
            'success': True,
            'message': _('Language added successfully. Please restart the server to activate it.')
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Error adding language: {str(e)}'
        })


@login_required
@user_passes_test(is_admin_or_translator)
@require_http_methods(["DELETE"])
def delete_language(request, language_code):
    """Delete a language from the system."""
    try:
        # Prevent deletion of default language
        if language_code == settings.LANGUAGE_CODE:
            return JsonResponse({
                'success': False,
                'error': _('Cannot delete the default language')
            })
        
        # Check if language exists
        existing_languages = [code for code, name in settings.LANGUAGES]
        if language_code not in existing_languages:
            return JsonResponse({
                'success': False,
                'error': _('Language does not exist')
            })
        
        # Remove locale directory
        locale_dir = os.path.join(settings.BASE_DIR, 'locale', language_code)
        if os.path.exists(locale_dir):
            import shutil
            shutil.rmtree(locale_dir)
        
        return JsonResponse({
            'success': True,
            'message': _('Language deleted successfully. Please restart the server to complete removal.')
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Error deleting language: {str(e)}'
        })


@login_required
@user_passes_test(is_admin_or_translator)
@require_http_methods(["POST"])
def scan_missing_translations(request):
    """Scan for missing translations across all languages."""
    try:
        missing_count = 0
        
        # Run Django's makemessages command to update PO files
        output = StringIO()
        call_command('makemessages', '--all', '--ignore=venv', stdout=output)
        
        # Count missing translations
        for language_code, language_name in settings.LANGUAGES:
            if language_code == 'en':
                continue
                
            po_file_path = os.path.join(settings.BASE_DIR, 'locale', language_code, 'LC_MESSAGES', 'django.po')
            
            if os.path.exists(po_file_path):
                po_file = polib.pofile(po_file_path)
                missing_count += len([e for e in po_file if not e.msgstr and e.msgid])
        
        return JsonResponse({
            'success': True,
            'missing_count': missing_count,
            'message': _('Scan completed successfully')
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Error scanning translations: {str(e)}'
        })


@login_required
@user_passes_test(is_admin_or_translator)
@require_http_methods(["POST"])
def validate_all_translations(request):
    """Validate all translations for consistency."""
    try:
        all_errors = []
        
        for language_code, language_name in settings.LANGUAGES:
            if language_code == 'en':
                continue
                
            po_file_path = os.path.join(settings.BASE_DIR, 'locale', language_code, 'LC_MESSAGES', 'django.po')
            
            if os.path.exists(po_file_path):
                po_file = polib.pofile(po_file_path)
                
                for entry in po_file:
                    # Check for placeholder consistency
                    if entry.msgstr and '%' in entry.msgid:
                        msgid_placeholders = set([p for p in entry.msgid.split() if p.startswith('%')])
                        msgstr_placeholders = set([p for p in entry.msgstr.split() if p.startswith('%')])
                        
                        if msgid_placeholders != msgstr_placeholders:
                            all_errors.append({
                                'language': language_name,
                                'msgid': entry.msgid,
                                'error': 'Placeholder mismatch'
                            })
                    
                    # Check for HTML tag consistency
                    if entry.msgstr and '<' in entry.msgid and '>' in entry.msgid:
                        import re
                        msgid_tags = set(re.findall(r'<[^>]+>', entry.msgid))
                        msgstr_tags = set(re.findall(r'<[^>]+>', entry.msgstr))
                        
                        if msgid_tags != msgstr_tags:
                            all_errors.append({
                                'language': language_name,
                                'msgid': entry.msgid,
                                'error': 'HTML tag mismatch'
                            })
        
        return JsonResponse({
            'success': True,
            'errors': all_errors,
            'message': _('Validation completed')
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Error validating translations: {str(e)}'
        })


@login_required
@user_passes_test(is_admin_or_translator)
def export_all_translations(request):
    """Export all translation files as a ZIP archive."""
    try:
        import zipfile
        from django.http import HttpResponse
        from io import BytesIO
        
        # Create a ZIP file in memory
        zip_buffer = BytesIO()
        
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            for language_code, language_name in settings.LANGUAGES:
                po_file_path = os.path.join(settings.BASE_DIR, 'locale', language_code, 'LC_MESSAGES', 'django.po')
                
                if os.path.exists(po_file_path):
                    zip_file.write(po_file_path, f'{language_code}_django.po')
        
        zip_buffer.seek(0)
        
        response = HttpResponse(zip_buffer.getvalue(), content_type='application/zip')
        response['Content-Disposition'] = 'attachment; filename="all_translations.zip"'
        
        return response
        
    except Exception as e:
        messages.error(request, f'Error exporting translations: {str(e)}')
        return redirect('core:localization_dashboard')


@login_required
@user_passes_test(is_admin_or_translator)
def localization_test_runner(request):
    """Run localization tests and display results."""
    test_results = []
    
    try:
        # Test 1: Check if all languages have PO files
        for language_code, language_name in settings.LANGUAGES:
            po_file_path = os.path.join(settings.BASE_DIR, 'locale', language_code, 'LC_MESSAGES', 'django.po')
            
            test_results.append({
                'test_name': f'PO file exists for {language_name}',
                'status': 'PASS' if os.path.exists(po_file_path) else 'FAIL',
                'details': f'File: {po_file_path}' if os.path.exists(po_file_path) else 'File not found'
            })
        
        # Test 2: Check translation completeness
        for language_code, language_name in settings.LANGUAGES:
            if language_code == 'en':
                continue
                
            po_file_path = os.path.join(settings.BASE_DIR, 'locale', language_code, 'LC_MESSAGES', 'django.po')
            
            if os.path.exists(po_file_path):
                po_file = polib.pofile(po_file_path)
                total = len(po_file)
                translated = len([e for e in po_file if e.msgstr])
                percentage = (translated / total * 100) if total > 0 else 0
                
                status = 'PASS' if percentage >= 80 else 'WARN' if percentage >= 50 else 'FAIL'
                
                test_results.append({
                    'test_name': f'Translation completeness for {language_name}',
                    'status': status,
                    'details': f'{translated}/{total} strings translated ({percentage:.1f}%)'
                })
        
        # Test 3: Check for RTL language support
        rtl_languages = ['ar', 'he', 'fa', 'ur']
        for language_code, language_name in settings.LANGUAGES:
            if language_code in rtl_languages:
                rtl_css_path = os.path.join(settings.BASE_DIR, 'static', 'css', 'rtl.css')
                
                test_results.append({
                    'test_name': f'RTL CSS support for {language_name}',
                    'status': 'PASS' if os.path.exists(rtl_css_path) else 'FAIL',
                    'details': 'RTL CSS file exists' if os.path.exists(rtl_css_path) else 'RTL CSS file missing'
                })
        
        # Test 4: Check compiled MO files
        for language_code, language_name in settings.LANGUAGES:
            mo_file_path = os.path.join(settings.BASE_DIR, 'locale', language_code, 'LC_MESSAGES', 'django.mo')
            
            test_results.append({
                'test_name': f'Compiled MO file for {language_name}',
                'status': 'PASS' if os.path.exists(mo_file_path) else 'WARN',
                'details': 'MO file exists' if os.path.exists(mo_file_path) else 'MO file not compiled'
            })
        
        # Test 5: Check template tag loading
        try:
            from .templatetags.localization_tags import format_number, format_date_localized
            test_results.append({
                'test_name': 'Localization template tags',
                'status': 'PASS',
                'details': 'Template tags loaded successfully'
            })
        except ImportError as e:
            test_results.append({
                'test_name': 'Localization template tags',
                'status': 'FAIL',
                'details': f'Import error: {str(e)}'
            })
        
    except Exception as e:
        test_results.append({
            'test_name': 'Test runner error',
            'status': 'FAIL',
            'details': str(e)
        })
    
    context = {
        'test_results': test_results,
        'total_tests': len(test_results),
        'passed_tests': len([t for t in test_results if t['status'] == 'PASS']),
        'failed_tests': len([t for t in test_results if t['status'] == 'FAIL']),
        'warning_tests': len([t for t in test_results if t['status'] == 'WARN']),
    }
    
    return render(request, 'core/localization/test_runner.html', context)


def get_translation_statistics():
    """Get translation statistics for all languages."""
    stats = {}
    
    for language_code, language_name in settings.LANGUAGES:
        po_file_path = os.path.join(settings.BASE_DIR, 'locale', language_code, 'LC_MESSAGES', 'django.po')
        
        if os.path.exists(po_file_path):
            try:
                po_file = polib.pofile(po_file_path)
                total = len(po_file)
                translated = len([e for e in po_file if e.msgstr])
                
                stats[language_code] = {
                    'name': language_name,
                    'total': total,
                    'translated': translated,
                    'percentage': round((translated / total * 100) if total > 0 else 0, 1),
                }
            except:
                stats[language_code] = {
                    'name': language_name,
                    'total': 0,
                    'translated': 0,
                    'percentage': 0,
                }
    
    return stats


def get_recent_translation_updates():
    """Get recent translation updates (mock data for now)."""
    # In a real implementation, this would track translation changes
    return [
        {
            'language': 'Arabic',
            'msgid': 'Student',
            'updated_by': 'Admin',
            'updated_at': '2024-01-15 10:30:00',
        },
        {
            'language': 'Arabic',
            'msgid': 'Dashboard',
            'updated_by': 'Translator',
            'updated_at': '2024-01-15 09:15:00',
        },
    ]


def prepare_chart_data(analytics_data):
    """Prepare data for charts."""
    chart_data = {
        'languages': [],
        'completion_percentages': [],
        'total_entries': [],
        'translated_entries': [],
    }
    
    for language_code, data in analytics_data.items():
        if 'error' not in data:
            chart_data['languages'].append(data['name'])
            chart_data['completion_percentages'].append(data.get('completion_percentage', data.get('percentage', 0)))
            chart_data['total_entries'].append(data['total'])
            chart_data['translated_entries'].append(data['translated'])
    
    return chart_data

@login_required
@user_passes_test(is_admin_or_translator)
def export_analytics(request):
    """Export analytics data in various formats."""
    format_type = request.GET.get('format', 'pdf')
    
    # Get analytics data
    analytics_data = {}
    for language_code, language_name in settings.LANGUAGES:
        po_file_path = os.path.join(settings.BASE_DIR, 'locale', language_code, 'LC_MESSAGES', 'django.po')
        
        if os.path.exists(po_file_path):
            try:
                po_file = polib.pofile(po_file_path)
                
                total = len(po_file)
                translated = len([e for e in po_file if e.msgstr])
                fuzzy = len([e for e in po_file if 'fuzzy' in e.flags])
                untranslated = total - translated
                
                analytics_data[language_code] = {
                    'name': language_name,
                    'total': total,
                    'translated': translated,
                    'untranslated': untranslated,
                    'fuzzy': fuzzy,
                    'completion_percentage': round((translated / total * 100) if total > 0 else 0, 2),
                }
                
            except Exception as e:
                analytics_data[language_code] = {
                    'name': language_name,
                    'error': str(e)
                }
    
    if format_type == 'excel':
        return export_analytics_excel(analytics_data)
    elif format_type == 'pdf':
        return export_analytics_pdf(analytics_data)
    else:
        return JsonResponse({'error': 'Invalid format'}, status=400)


def export_analytics_excel(analytics_data):
    """Export analytics data to Excel format."""
    try:
        import openpyxl
        from openpyxl.styles import Font, PatternFill, Alignment
        from django.http import HttpResponse
        from io import BytesIO
        
        # Create workbook
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Translation Analytics"
        
        # Headers
        headers = ['Language', 'Total Strings', 'Translated', 'Untranslated', 'Fuzzy', 'Completion %']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
        
        # Data rows
        for row, (language_code, data) in enumerate(analytics_data.items(), 2):
            if 'error' not in data:
                ws.cell(row=row, column=1, value=data['name'])
                ws.cell(row=row, column=2, value=data['total'])
                ws.cell(row=row, column=3, value=data['translated'])
                ws.cell(row=row, column=4, value=data['untranslated'])
                ws.cell(row=row, column=5, value=data['fuzzy'])
                ws.cell(row=row, column=6, value=f"{data['completion_percentage']}%")
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
        
        # Save to BytesIO
        excel_buffer = BytesIO()
        wb.save(excel_buffer)
        excel_buffer.seek(0)
        
        response = HttpResponse(
            excel_buffer.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="translation_analytics.xlsx"'
        return response
        
    except ImportError:
        return JsonResponse({'error': 'openpyxl not installed'}, status=500)
    except Exception as e:
        return JsonResponse({'error': f'Error generating Excel: {str(e)}'}, status=500)


def export_analytics_pdf(analytics_data):
    """Export analytics data to PDF format."""
    try:
        from reportlab.lib import colors
        from reportlab.lib.pagesizes import letter, A4
        from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from django.http import HttpResponse
        from io import BytesIO
        import datetime
        
        # Create PDF buffer
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4)
        
        # Styles
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=1  # Center alignment
        )
        
        # Content
        story = []
        
        # Title
        title = Paragraph("Translation Analytics Report", title_style)
        story.append(title)
        
        # Date
        date_para = Paragraph(f"Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M')}", styles['Normal'])
        story.append(date_para)
        story.append(Spacer(1, 20))
        
        # Table data
        table_data = [['Language', 'Total', 'Translated', 'Untranslated', 'Fuzzy', 'Completion %']]
        
        for language_code, data in analytics_data.items():
            if 'error' not in data:
                table_data.append([
                    data['name'],
                    str(data['total']),
                    str(data['translated']),
                    str(data['untranslated']),
                    str(data['fuzzy']),
                    f"{data['completion_percentage']}%"
                ])
        
        # Create table
        table = Table(table_data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(table)
        
        # Build PDF
        doc.build(story)
        
        # Return response
        buffer.seek(0)
        response = HttpResponse(buffer.getvalue(), content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="translation_analytics.pdf"'
        return response
        
    except ImportError:
        return JsonResponse({'error': 'reportlab not installed'}, status=500)
    except Exception as e:
        return JsonResponse({'error': f'Error generating PDF: {str(e)}'}, status=500)


@login_required
@user_passes_test(is_admin_or_translator)
def translation_testing_tools(request):
    """Translation testing and validation tools."""
    test_results = []
    
    try:
        # Test 1: Check translation file integrity
        for language_code, language_name in settings.LANGUAGES:
            po_file_path = os.path.join(settings.BASE_DIR, 'locale', language_code, 'LC_MESSAGES', 'django.po')
            
            if os.path.exists(po_file_path):
                try:
                    po_file = polib.pofile(po_file_path)
                    test_results.append({
                        'test_name': f'File integrity - {language_name}',
                        'status': 'PASS',
                        'details': f'Successfully loaded {len(po_file)} entries'
                    })
                except Exception as e:
                    test_results.append({
                        'test_name': f'File integrity - {language_name}',
                        'status': 'FAIL',
                        'details': f'Error loading file: {str(e)}'
                    })
            else:
                test_results.append({
                    'test_name': f'File existence - {language_name}',
                    'status': 'FAIL',
                    'details': 'Translation file not found'
                })
        
        # Test 2: Check for placeholder consistency
        for language_code, language_name in settings.LANGUAGES:
            if language_code == 'en':
                continue
                
            po_file_path = os.path.join(settings.BASE_DIR, 'locale', language_code, 'LC_MESSAGES', 'django.po')
            
            if os.path.exists(po_file_path):
                po_file = polib.pofile(po_file_path)
                placeholder_errors = 0
                
                for entry in po_file:
                    if entry.msgstr and '%' in entry.msgid:
                        import re
                        msgid_placeholders = set(re.findall(r'%\([^)]+\)s|%[sd]', entry.msgid))
                        msgstr_placeholders = set(re.findall(r'%\([^)]+\)s|%[sd]', entry.msgstr))
                        
                        if msgid_placeholders != msgstr_placeholders:
                            placeholder_errors += 1
                
                if placeholder_errors == 0:
                    test_results.append({
                        'test_name': f'Placeholder consistency - {language_name}',
                        'status': 'PASS',
                        'details': 'All placeholders are consistent'
                    })
                else:
                    test_results.append({
                        'test_name': f'Placeholder consistency - {language_name}',
                        'status': 'FAIL',
                        'details': f'{placeholder_errors} entries with placeholder mismatches'
                    })
        
        # Test 3: Check translation completeness thresholds
        for language_code, language_name in settings.LANGUAGES:
            if language_code == 'en':
                continue
                
            po_file_path = os.path.join(settings.BASE_DIR, 'locale', language_code, 'LC_MESSAGES', 'django.po')
            
            if os.path.exists(po_file_path):
                po_file = polib.pofile(po_file_path)
                total = len(po_file)
                translated = len([e for e in po_file if e.msgstr])
                percentage = (translated / total * 100) if total > 0 else 0
                
                if percentage >= 90:
                    status = 'PASS'
                    details = f'Excellent completion: {percentage:.1f}%'
                elif percentage >= 70:
                    status = 'WARN'
                    details = f'Good completion: {percentage:.1f}%'
                else:
                    status = 'FAIL'
                    details = f'Low completion: {percentage:.1f}%'
                
                test_results.append({
                    'test_name': f'Completion threshold - {language_name}',
                    'status': status,
                    'details': details
                })
        
    except Exception as e:
        test_results.append({
            'test_name': 'Testing framework error',
            'status': 'FAIL',
            'details': str(e)
        })
    
    context = {
        'test_results': test_results,
        'total_tests': len(test_results),
        'passed_tests': len([t for t in test_results if t['status'] == 'PASS']),
        'failed_tests': len([t for t in test_results if t['status'] == 'FAIL']),
        'warning_tests': len([t for t in test_results if t['status'] == 'WARN']),
    }
    
    return render(request, 'core/localization/testing_tools.html', context)