"""
Management command for performance optimization tasks.
Includes cache warming, session cleanup, and performance monitoring.
"""

import logging
from django.core.management.base import BaseCommand
from django.core.cache import cache
from django.utils import timezone
from django.contrib.auth import get_user_model
from core.models import School
from core.performance_optimization import <PERSON><PERSON><PERSON><PERSON><PERSON>, warm_user_cache
from core.session_optimization import SessionOptimizer, SessionMetrics

logger = logging.getLogger(__name__)
User = get_user_model()


class Command(BaseCommand):
    help = 'Optimize system performance through cache warming and cleanup'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--warm-cache',
            action='store_true',
            help='Warm up caches for all schools and active users'
        )
        
        parser.add_argument(
            '--cleanup-sessions',
            action='store_true',
            help='Clean up expired sessions and session cache'
        )
        
        parser.add_argument(
            '--stats',
            action='store_true',
            help='Display performance statistics'
        )
        
        parser.add_argument(
            '--school-id',
            type=int,
            help='Warm cache for specific school ID'
        )
        
        parser.add_argument(
            '--user-id',
            type=int,
            help='Warm cache for specific user ID'
        )
        
        parser.add_argument(
            '--batch-size',
            type=int,
            default=100,
            help='Batch size for operations (default: 100)'
        )
    
    def handle(self, *args, **options):
        """Handle the management command."""
        start_time = timezone.now()
        
        self.stdout.write(
            self.style.SUCCESS(f'Starting performance optimization at {start_time}')
        )
        
        if options['warm_cache']:
            self.warm_caches(options)
        
        if options['cleanup_sessions']:
            self.cleanup_sessions(options)
        
        if options['stats']:
            self.display_stats()
        
        end_time = timezone.now()
        duration = (end_time - start_time).total_seconds()
        
        self.stdout.write(
            self.style.SUCCESS(f'Performance optimization completed in {duration:.2f} seconds')
        )
    
    def warm_caches(self, options):
        """Warm up system caches."""
        self.stdout.write('Warming up caches...')
        
        try:
            if options.get('school_id'):
                # Warm cache for specific school
                school = School.objects.get(id=options['school_id'])
                CacheManager.warm_school_cache(school)
                self.stdout.write(
                    self.style.SUCCESS(f'Cache warmed for school: {school.name}')
                )
            
            elif options.get('user_id'):
                # Warm cache for specific user
                user = User.objects.get(id=options['user_id'])
                warm_user_cache(user)
                self.stdout.write(
                    self.style.SUCCESS(f'Cache warmed for user: {user.username}')
                )
            
            else:
                # Warm cache for all active schools
                schools = School.objects.filter(is_active=True)
                school_count = 0
                
                for school in schools:
                    try:
                        CacheManager.warm_school_cache(school)
                        school_count += 1
                        
                        if school_count % 10 == 0:
                            self.stdout.write(f'Processed {school_count} schools...')
                    
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f'Error warming cache for school {school.id}: {e}')
                        )
                
                self.stdout.write(
                    self.style.SUCCESS(f'Cache warmed for {school_count} schools')
                )
                
                # Warm cache for active users (limited batch)
                active_users = User.objects.filter(
                    is_active=True,
                    last_login__gte=timezone.now() - timezone.timedelta(days=30)
                )[:options['batch_size']]
                
                user_count = 0
                for user in active_users:
                    try:
                        warm_user_cache(user)
                        user_count += 1
                        
                        if user_count % 20 == 0:
                            self.stdout.write(f'Processed {user_count} users...')
                    
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f'Error warming cache for user {user.id}: {e}')
                        )
                
                self.stdout.write(
                    self.style.SUCCESS(f'Cache warmed for {user_count} active users')
                )
        
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error warming caches: {e}')
            )
    
    def cleanup_sessions(self, options):
        """Clean up expired sessions and session cache."""
        self.stdout.write('Cleaning up sessions...')
        
        try:
            batch_size = options['batch_size']
            total_cleaned = 0
            
            while True:
                cleaned_count = SessionOptimizer.batch_cleanup_expired_sessions(batch_size)
                total_cleaned += cleaned_count
                
                if cleaned_count == 0:
                    break
                
                self.stdout.write(f'Cleaned {cleaned_count} sessions in this batch...')
            
            self.stdout.write(
                self.style.SUCCESS(f'Total sessions cleaned: {total_cleaned}')
            )
            
            # Clear cache entries that might be orphaned
            self.stdout.write('Clearing orphaned cache entries...')
            
            # This is a simplified cleanup - in production you'd want more sophisticated cache management
            cache.clear()
            
            self.stdout.write(
                self.style.SUCCESS('Cache cleared successfully')
            )
        
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error cleaning up sessions: {e}')
            )
    
    def display_stats(self):
        """Display performance statistics."""
        self.stdout.write('Performance Statistics:')
        self.stdout.write('=' * 50)
        
        try:
            # Session statistics
            session_stats = SessionMetrics.get_session_stats()
            
            self.stdout.write('Session Statistics:')
            self.stdout.write(f'  Total sessions: {session_stats.get("total_sessions", "N/A")}')
            self.stdout.write(f'  Active sessions: {session_stats.get("active_sessions", "N/A")}')
            self.stdout.write(f'  Expired sessions: {session_stats.get("expired_sessions", "N/A")}')
            
            # School statistics
            school_count = School.objects.filter(is_active=True).count()
            self.stdout.write(f'\nSchool Statistics:')
            self.stdout.write(f'  Active schools: {school_count}')
            
            # User statistics
            active_users = User.objects.filter(is_active=True).count()
            recent_users = User.objects.filter(
                is_active=True,
                last_login__gte=timezone.now() - timezone.timedelta(days=7)
            ).count()
            
            self.stdout.write(f'\nUser Statistics:')
            self.stdout.write(f'  Active users: {active_users}')
            self.stdout.write(f'  Recent users (7 days): {recent_users}')
            
            # Cache statistics (simplified)
            self.stdout.write(f'\nCache Information:')
            self.stdout.write(f'  Cache backend: {cache.__class__.__name__}')
            self.stdout.write(f'  Timestamp: {timezone.now()}')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error displaying statistics: {e}')
            )
    
    def optimize_database_queries(self):
        """Optimize database queries and indexes."""
        self.stdout.write('Optimizing database queries...')
        
        try:
            # This would contain database-specific optimizations
            # For now, just log the intent
            self.stdout.write('Database optimization completed')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error optimizing database: {e}')
            )