"""
Custom middleware for the School ERP system.
"""

import logging
import time
from django.utils import timezone
from django.utils.deprecation import MiddlewareMixin
from django.contrib.auth.models import AnonymousUser
from django.http import JsonResponse, HttpResponseRedirect
from django.utils.translation import activate
from django.conf import settings
from django.urls import reverse
from django.contrib import messages
from django.core.cache import cache
from core.models import School, AuditLog
from core.localization.localization import LocalizationManager
from core.school_utils import (
    get_user_schools, user_has_school_access, get_current_school,
    validate_session_school_data, cleanup_invalid_school_session,
    set_current_school, refresh_school_session
)
from core.performance_optimization import CacheManager, monitor_performance
from core.session_optimization import SessionOptimizer, SessionSecurityOptimizer

logger = logging.getLogger(__name__)


class SchoolSelectionMiddleware(MiddlewareMixin):
    """
    Middleware to handle automatic school selection and context injection.
    Redirects users to school selection page if no school is selected.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.exempt_urls = [
            '/admin/', '/accounts/login/', '/accounts/logout/', '/accounts/register/',
            '/core/school/select/', '/core/school/switch/', '/core/school/current/', '/static/', '/media/',
            '/favicon.ico', '/api/health/', '/api/status/'
        ]
    
    def __call__(self, request):
        # Skip middleware for exempt URLs and unauthenticated users
        if self.should_skip(request):
            return self.get_response(request)
        
        # Get or set school context
        school = self.get_school_context(request)
        if not school:
            # Check if this is a POST request to school selection (user just selected a school)
            school_select_url = reverse('core:school_select')
            if request.path == school_select_url and request.method == 'POST':
                # Let the school selection view handle the POST request
                response = self.get_response(request)
                return response
            
            # Redirect to school selection page
            if request.path != school_select_url:
                # Store the intended URL to redirect back after school selection
                next_url = request.get_full_path()
                return HttpResponseRedirect(f"{school_select_url}?next={next_url}")
        
        # Add school to request
        request.school = school
        return self.get_response(request)
    
    def should_skip(self, request):
        """Check if middleware should be skipped for this request."""
        # Skip for unauthenticated users
        if not request.user.is_authenticated:
            return True
        
        # Skip for exempt URLs
        for exempt_url in self.exempt_urls:
            if request.path.startswith(exempt_url):
                return True
        
        # Skip for AJAX requests to certain endpoints
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            ajax_exempt_patterns = ['/api/', '/ajax/']
            for pattern in ajax_exempt_patterns:
                if pattern in request.path:
                    return True
        
        return False
    
    @monitor_performance
    def get_school_context(self, request):
        """Get school context from session with enhanced validation, security checks, and optimized caching."""
        try:
            # Create hierarchical cache keys for better performance
            user_id = request.user.id
            session_key = getattr(request.session, 'session_key', 'no_session')[:8]
            cache_key = f"school_context_{user_id}_{session_key}"
            
            # Try to get from cache first with longer TTL for better performance
            cached_data = cache.get(cache_key)
            if cached_data and isinstance(cached_data, dict):
                cached_school = cached_data.get('school')
                cache_timestamp = cached_data.get('timestamp')
                
                # Verify cached school is still valid and not too old
                if (isinstance(cached_school, School) and cached_school.is_active and
                    cache_timestamp and (timezone.now() - cache_timestamp).total_seconds() < 600):  # 10 minutes
                    
                    # Quick validation without full security check for performance
                    if hasattr(request, '_school_context_validated'):
                        return cached_school
                    
                    # Perform lightweight security validation once per request
                    from core.school_utils import validate_session_security, log_security_event, get_client_ip
                    is_secure, warning_msg = validate_session_security(request)
                    
                    if is_secure:
                        request._school_context_validated = True
                        # Update cache access time for LRU-like behavior
                        cached_data['last_access'] = timezone.now()
                        cache.set(cache_key, cached_data, 900)  # 15 minutes
                        return cached_school
                    else:
                        # Clear invalid cache
                        cache.delete(cache_key)
                        log_security_event(request.user, 'SESSION_SECURITY_VIOLATION', warning_msg, ip_address=get_client_ip(request))
                        cleanup_invalid_school_session(request)
                        return None
            
            # Validate session security for non-cached requests
            from core.school_utils import validate_session_security, log_security_event, get_client_ip
            is_secure, warning_msg = validate_session_security(request)
            
            if not is_secure:
                log_security_event(request.user, 'SESSION_SECURITY_VIOLATION', warning_msg, ip_address=get_client_ip(request))
                cleanup_invalid_school_session(request)
                return None
            
            # Use the enhanced get_current_school function which includes validation
            school = get_current_school(request)
            
            if school:
                # Batch security check with caching for better performance
                access_cache_key = f"school_access_{user_id}_{school.id}"
                has_access = cache.get(access_cache_key)
                
                if has_access is None:
                    from core.school_utils import validate_school_access_permission
                    has_access, error_msg = validate_school_access_permission(request.user, school, 'read')
                    # Cache access permission for 5 minutes
                    cache.set(access_cache_key, has_access, 300)
                    
                    if not has_access:
                        log_security_event(request.user, 'SCHOOL_ACCESS_REVOKED', f"Access revoked for school {school.name}: {error_msg}", school=school, ip_address=get_client_ip(request))
                        cleanup_invalid_school_session(request)
                        return None
                elif not has_access:
                    # Cached negative result
                    cleanup_invalid_school_session(request)
                    return None
                
                # Cache the validated school with metadata for better performance
                cache_data = {
                    'school': school,
                    'timestamp': timezone.now(),
                    'last_access': timezone.now(),
                    'user_id': user_id
                }
                cache.set(cache_key, cache_data, 900)  # 15 minutes
                request._school_context_validated = True
                
                # Optionally refresh session if it's getting close to expiration (async-like)
                from core.school_utils import get_school_session_info
                session_info = get_school_session_info(request)
                
                if (session_info.get('time_remaining') and 
                    session_info['time_remaining'].total_seconds() < 3600):  # Less than 1 hour remaining
                    # Use a background task or defer this to avoid blocking the request
                    try:
                        refresh_school_session(request)
                        logger.info(f"Refreshed expiring session for user {request.user.username}")
                    except Exception as e:
                        logger.warning(f"Failed to refresh session: {e}")
                
                return school
            
            # If no valid school in session, try to get from user's available schools with optimized caching
            user_schools_cache_key = f"user_schools_{user_id}_{request.user.is_superuser}"
            available_schools = cache.get(user_schools_cache_key)
            
            if available_schools is None:
                available_schools = get_user_schools(request.user)
                # Cache user schools for 15 minutes
                cache.set(user_schools_cache_key, available_schools, 900)
            
            school_count = available_schools.count() if hasattr(available_schools, 'count') else len(available_schools)
            
            if school_count == 1:
                # If user has access to only one school, auto-select it
                school = available_schools.first() if hasattr(available_schools, 'first') else available_schools[0]
                if set_current_school(request, school):
                    # Cache the auto-selected school
                    cache_data = {
                        'school': school,
                        'timestamp': timezone.now(),
                        'last_access': timezone.now(),
                        'user_id': user_id,
                        'auto_selected': True
                    }
                    cache.set(cache_key, cache_data, 900)
                    logger.info(f"Auto-selected single school for {request.user.username}: {school.name}")
                    return school
            elif school_count > 1:
                # Multiple schools available, user needs to select
                return None
            else:
                # No schools available, this shouldn't happen in normal operation
                logger.warning(f"User {request.user.username} has no school access")
                log_security_event(request.user, 'NO_SCHOOL_ACCESS', f"User has no school access", ip_address=get_client_ip(request))
                return None
                
        except Exception as e:
            logger.error(f"Error getting school context: {e}")
            # Clean up potentially corrupted session data and cache
            cleanup_invalid_school_session(request)
            cache_key = f"school_context_{user_id}_{session_key}"
            cache.delete(cache_key)
            return None
    



class SchoolContextMiddleware(MiddlewareMixin):
    """
    Middleware to add school context data to all requests.
    This runs after SchoolSelectionMiddleware to enhance the request with additional context.
    """
    
    def process_request(self, request):
        """Add school context data to request."""
        if hasattr(request, 'school') and request.school:
            request.school_context = {
                'current_school': request.school,
                'school_name': request.school.name,
                'school_code': request.school.code,
                'school_id': str(request.school.id),
            }
        else:
            request.school_context = {
                'current_school': None,
                'school_name': None,
                'school_code': None,
                'school_id': None,
            }
        
        return None


class AuditMiddleware(MiddlewareMixin):
    """
    Middleware to log user actions for audit purposes.
    """
    
    def process_request(self, request):
        """Store request start time for performance tracking."""
        request._audit_start_time = time.time()
        return None
    
    def process_response(self, request, response):
        """Log the request for audit purposes."""
        if not hasattr(request, 'user') or isinstance(request.user, AnonymousUser):
            return response
        
        # Skip logging for certain paths
        skip_paths = ['/static/', '/media/', '/favicon.ico', '/admin/jsi18n/']
        if any(request.path.startswith(path) for path in skip_paths):
            return response
        
        # Skip logging for GET requests to avoid too much noise
        if request.method == 'GET':
            return response
        
        try:
            # Create audit log entry with only valid fields
            AuditLog.objects.create(
                user=request.user if request.user.is_authenticated else None,
                action=f"{request.method} {request.path}",
                model_name='Request',
                object_id=request.path,
                object_repr=f"{request.method} {request.path}",
                ip_address=self.get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                school=getattr(request, 'school', None)
            )
        except Exception as e:
            logger.error(f"Error creating audit log: {e}")
        
        return response
    
    def get_client_ip(self, request):
        """Get client IP address from request."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class LanguageMiddleware(MiddlewareMixin):
    """
    Enhanced language middleware for better localization support.
    """
    
    def process_request(self, request):
        """Detect and activate appropriate language."""
        language = self.get_language_from_request(request)
        
        if language and LocalizationManager.activate_language(language):
            activate(language)
            request.LANGUAGE_CODE = language
        
        return None
    
    def get_language_from_request(self, request):
        """
        Determine language from various sources in order of priority:
        1. URL parameter (?lang=ar)
        2. Session
        3. User preference (if authenticated)
        4. Accept-Language header
        5. Default language
        """
        # 1. URL parameter
        if 'lang' in request.GET:
            lang = request.GET['lang']
            if lang in [code for code, name in settings.LANGUAGES]:
                request.session['django_language'] = lang
                return lang
        
        # 2. Session
        if 'django_language' in request.session:
            lang = request.session['django_language']
            if lang in [code for code, name in settings.LANGUAGES]:
                return lang
        
        # 3. User preference
        if hasattr(request, 'user') and request.user.is_authenticated:
            try:
                if hasattr(request.user, 'profile') and request.user.profile.preferred_language:
                    lang = request.user.profile.preferred_language
                    if lang in [code for code, name in settings.LANGUAGES]:
                        return lang
            except:
                pass
        
        # 4. Accept-Language header
        accept_language = request.META.get('HTTP_ACCEPT_LANGUAGE', '')
        for lang_code, _ in settings.LANGUAGES:
            if lang_code in accept_language:
                return lang_code
        
        # 5. Default language
        return settings.LANGUAGE_CODE


class SecurityMiddleware(MiddlewareMixin):
    """
    Additional security middleware for the School ERP system.
    """
    
    def process_request(self, request):
        """Apply security checks."""
        # Add security headers
        return None
    
    def process_response(self, request, response):
        """Add security headers to response."""
        # Add security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # Add CSP header for production
        if not settings.DEBUG:
            response['Content-Security-Policy'] = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval' "
                "https://cdn.jsdelivr.net https://code.jquery.com "
                "https://cdnjs.cloudflare.com; "
                "style-src 'self' 'unsafe-inline' "
                "https://cdn.jsdelivr.net https://fonts.googleapis.com "
                "https://cdnjs.cloudflare.com; "
                "font-src 'self' https://fonts.gstatic.com "
                "https://cdnjs.cloudflare.com; "
                "img-src 'self' data: https:; "
                "connect-src 'self';"
            )
        
        return response


class PerformanceMiddleware(MiddlewareMixin):
    """
    Middleware to track and optimize performance.
    """
    
    def process_request(self, request):
        """Start performance tracking."""
        request._performance_start = time.time()
        return None
    
    def process_response(self, request, response):
        """Log slow requests."""
        if hasattr(request, '_performance_start'):
            duration = time.time() - request._performance_start
            
            # Log slow requests (> 2 seconds)
            if duration > 2.0:
                logger.warning(
                    f"Slow request: {request.method} {request.path} "
                    f"took {duration:.2f}s"
                )
            
            # Add performance header in debug mode
            if settings.DEBUG:
                response['X-Response-Time'] = f"{duration:.3f}s"
        
        return response


class ErrorHandlingMiddleware(MiddlewareMixin):
    """
    Middleware for centralized error handling.
    """
    
    def process_exception(self, request, exception):
        """Handle exceptions gracefully."""
        logger.exception(f"Unhandled exception in {request.path}: {exception}")
        
        # Return JSON response for API requests
        if request.path.startswith('/api/'):
            return JsonResponse({
                'error': 'Internal server error',
                'message': str(exception) if settings.DEBUG else 'An error occurred'
            }, status=500)
        
        # Let Django handle other exceptions normally
        return None