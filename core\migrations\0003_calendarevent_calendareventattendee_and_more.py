# Generated by Django 5.2.4 on 2025-08-06 14:15

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="CalendarEvent",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200, verbose_name="Title")),
                (
                    "title_ar",
                    models.CharField(
                        blank=True, max_length=200, verbose_name="Title (Arabic)"
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                (
                    "description_ar",
                    models.TextField(blank=True, verbose_name="Description (Arabic)"),
                ),
                (
                    "event_type",
                    models.Char<PERSON>ield(
                        choices=[
                            ("holiday", "Holiday"),
                            ("academic", "Academic"),
                            ("administrative", "Administrative"),
                            ("religious", "Religious"),
                            ("national", "National"),
                            ("school", "School Event"),
                            ("exam", "Examination"),
                            ("vacation", "Vacation"),
                            ("meeting", "Meeting"),
                            ("other", "Other"),
                        ],
                        default="other",
                        max_length=20,
                        verbose_name="Event Type",
                    ),
                ),
                (
                    "calendar_type",
                    models.CharField(
                        choices=[
                            ("gregorian", "Gregorian"),
                            ("hijri", "Hijri"),
                            ("both", "Both"),
                        ],
                        default="gregorian",
                        help_text="Which calendar system this event belongs to",
                        max_length=20,
                        verbose_name="Calendar Type",
                    ),
                ),
                ("start_date", models.DateField(verbose_name="Start Date")),
                (
                    "end_date",
                    models.DateField(blank=True, null=True, verbose_name="End Date"),
                ),
                (
                    "start_time",
                    models.TimeField(blank=True, null=True, verbose_name="Start Time"),
                ),
                (
                    "end_time",
                    models.TimeField(blank=True, null=True, verbose_name="End Time"),
                ),
                (
                    "is_all_day",
                    models.BooleanField(default=True, verbose_name="All Day Event"),
                ),
                (
                    "hijri_year",
                    models.IntegerField(
                        blank=True, null=True, verbose_name="Hijri Year"
                    ),
                ),
                (
                    "hijri_month",
                    models.IntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(12),
                        ],
                        verbose_name="Hijri Month",
                    ),
                ),
                (
                    "hijri_day",
                    models.IntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(30),
                        ],
                        verbose_name="Hijri Day",
                    ),
                ),
                (
                    "recurrence_type",
                    models.CharField(
                        choices=[
                            ("none", "No Recurrence"),
                            ("daily", "Daily"),
                            ("weekly", "Weekly"),
                            ("monthly", "Monthly"),
                            ("yearly", "Yearly"),
                            ("custom", "Custom"),
                        ],
                        default="none",
                        max_length=20,
                        verbose_name="Recurrence Type",
                    ),
                ),
                (
                    "recurrence_interval",
                    models.IntegerField(
                        default=1,
                        help_text="Repeat every X days/weeks/months/years",
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="Recurrence Interval",
                    ),
                ),
                (
                    "recurrence_end_date",
                    models.DateField(
                        blank=True, null=True, verbose_name="Recurrence End Date"
                    ),
                ),
                (
                    "color",
                    models.CharField(
                        default="#007bff",
                        help_text="Hex color code for event display",
                        max_length=7,
                        verbose_name="Color",
                    ),
                ),
                (
                    "is_public",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this event is visible to all users",
                        verbose_name="Public Event",
                    ),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="Active")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_calendar_events",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="calendar_events",
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
            ],
            options={
                "verbose_name": "Calendar Event",
                "verbose_name_plural": "Calendar Events",
                "ordering": ["start_date", "start_time"],
            },
        ),
        migrations.CreateModel(
            name="CalendarEventAttendee",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "response",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("accepted", "Accepted"),
                            ("declined", "Declined"),
                            ("tentative", "Tentative"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="Response",
                    ),
                ),
                (
                    "response_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Response Date"
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "event",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="attendees",
                        to="core.calendarevent",
                        verbose_name="Event",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="calendar_event_attendances",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "Calendar Event Attendee",
                "verbose_name_plural": "Calendar Event Attendees",
            },
        ),
        migrations.CreateModel(
            name="CalendarEventReminder",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "reminder_type",
                    models.CharField(
                        choices=[
                            ("email", "Email"),
                            ("sms", "SMS"),
                            ("notification", "In-App Notification"),
                            ("popup", "Popup"),
                        ],
                        default="notification",
                        max_length=20,
                        verbose_name="Reminder Type",
                    ),
                ),
                (
                    "time_before",
                    models.IntegerField(
                        default=15,
                        help_text="How long before the event to send reminder",
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="Time Before",
                    ),
                ),
                (
                    "time_unit",
                    models.CharField(
                        choices=[
                            ("minutes", "Minutes"),
                            ("hours", "Hours"),
                            ("days", "Days"),
                            ("weeks", "Weeks"),
                        ],
                        default="minutes",
                        max_length=10,
                        verbose_name="Time Unit",
                    ),
                ),
                ("is_sent", models.BooleanField(default=False, verbose_name="Sent")),
                (
                    "sent_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="Sent At"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "event",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reminders",
                        to="core.calendarevent",
                        verbose_name="Event",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="calendar_reminders",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "Calendar Event Reminder",
                "verbose_name_plural": "Calendar Event Reminders",
            },
        ),
        migrations.CreateModel(
            name="CalendarPreference",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "primary_calendar",
                    models.CharField(
                        choices=[("gregorian", "Gregorian"), ("hijri", "Hijri")],
                        default="gregorian",
                        help_text="Default calendar system to use",
                        max_length=20,
                        verbose_name="Primary Calendar",
                    ),
                ),
                (
                    "show_dual_dates",
                    models.BooleanField(
                        default=True,
                        help_text="Show both Gregorian and Hijri dates",
                        verbose_name="Show Dual Dates",
                    ),
                ),
                (
                    "hijri_adjustment",
                    models.IntegerField(
                        default=0,
                        help_text="Days to adjust Hijri calculations (-2 to +2)",
                        validators=[
                            django.core.validators.MinValueValidator(-2),
                            django.core.validators.MaxValueValidator(2),
                        ],
                        verbose_name="Hijri Adjustment",
                    ),
                ),
                (
                    "first_day_of_week",
                    models.IntegerField(
                        choices=[
                            (0, "Monday"),
                            (1, "Tuesday"),
                            (2, "Wednesday"),
                            (3, "Thursday"),
                            (4, "Friday"),
                            (5, "Saturday"),
                            (6, "Sunday"),
                        ],
                        default=6,
                        help_text="First day of the week in calendar display",
                        verbose_name="First Day of Week",
                    ),
                ),
                (
                    "weekend_days",
                    models.JSONField(
                        default=list,
                        help_text="List of weekend day numbers (0=Monday, 6=Sunday)",
                        verbose_name="Weekend Days",
                    ),
                ),
                (
                    "date_format",
                    models.CharField(
                        default="%d/%m/%Y",
                        help_text="Preferred date format",
                        max_length=50,
                        verbose_name="Date Format",
                    ),
                ),
                (
                    "time_format",
                    models.CharField(
                        default="%H:%M",
                        help_text="Preferred time format (12h or 24h)",
                        max_length=20,
                        verbose_name="Time Format",
                    ),
                ),
                (
                    "timezone",
                    models.CharField(
                        default="UTC",
                        help_text="User timezone",
                        max_length=50,
                        verbose_name="Timezone",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="calendar_preference",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "Calendar Preference",
                "verbose_name_plural": "Calendar Preferences",
            },
        ),
        migrations.AddIndex(
            model_name="calendarevent",
            index=models.Index(
                fields=["start_date", "end_date"], name="core_calend_start_d_c45d81_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="calendarevent",
            index=models.Index(
                fields=["calendar_type", "event_type"],
                name="core_calend_calenda_4328ca_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="calendarevent",
            index=models.Index(
                fields=["school", "is_active"], name="core_calend_school__3d03ec_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="calendareventattendee",
            unique_together={("event", "user")},
        ),
        migrations.AlterUniqueTogether(
            name="calendareventreminder",
            unique_together={("event", "user", "reminder_type")},
        ),
    ]
