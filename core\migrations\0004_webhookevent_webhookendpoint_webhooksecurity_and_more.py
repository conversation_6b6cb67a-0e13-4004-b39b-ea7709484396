# Generated by Django 5.2.5 on 2025-08-07 18:35

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0003_calendarevent_calendareventattendee_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="WebhookEvent",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                ("description", models.TextField()),
                (
                    "payload_schema",
                    models.JSONField(
                        default=dict, help_text="JSON schema for the event payload"
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Webhook Event",
                "verbose_name_plural": "Webhook Events",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="WebhookEndpoint",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Descriptive name for the webhook", max_length=255
                    ),
                ),
                (
                    "url",
                    models.URLField(
                        help_text="Target URL for webhook delivery",
                        validators=[django.core.validators.URLValidator()],
                    ),
                ),
                (
                    "secret",
                    models.CharField(
                        help_text="Secret key for webhook signature verification",
                        max_length=255,
                    ),
                ),
                (
                    "events",
                    models.JSONField(
                        default=list,
                        help_text="List of events this webhook should receive",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("inactive", "Inactive"),
                            ("disabled", "Disabled"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("timeout_seconds", models.PositiveIntegerField(default=30)),
                ("max_retries", models.PositiveIntegerField(default=3)),
                ("retry_delay_seconds", models.PositiveIntegerField(default=60)),
                (
                    "headers",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Additional HTTP headers to send with webhook",
                    ),
                ),
                (
                    "filters",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Filters to apply before sending webhook",
                    ),
                ),
                ("description", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("total_deliveries", models.PositiveIntegerField(default=0)),
                ("successful_deliveries", models.PositiveIntegerField(default=0)),
                ("failed_deliveries", models.PositiveIntegerField(default=0)),
                ("last_delivery_at", models.DateTimeField(blank=True, null=True)),
                ("last_success_at", models.DateTimeField(blank=True, null=True)),
                ("last_failure_at", models.DateTimeField(blank=True, null=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_webhooks",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Webhook Endpoint",
                "verbose_name_plural": "Webhook Endpoints",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="WebhookSecurity",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "allowed_ips",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="List of allowed IP addresses (empty means all IPs allowed)",
                    ),
                ),
                ("rate_limit_per_minute", models.PositiveIntegerField(default=60)),
                ("rate_limit_per_hour", models.PositiveIntegerField(default=1000)),
                ("verify_ssl", models.BooleanField(default=True)),
                ("require_signature", models.BooleanField(default=True)),
                (
                    "signature_algorithm",
                    models.CharField(
                        choices=[("sha256", "SHA-256"), ("sha1", "SHA-1")],
                        default="sha256",
                        max_length=20,
                    ),
                ),
                ("exponential_backoff", models.BooleanField(default=True)),
                ("max_retry_delay_seconds", models.PositiveIntegerField(default=3600)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "endpoint",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="security",
                        to="core.webhookendpoint",
                    ),
                ),
            ],
            options={
                "verbose_name": "Webhook Security",
                "verbose_name_plural": "Webhook Security",
            },
        ),
        migrations.CreateModel(
            name="WebhookDelivery",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("event_type", models.CharField(max_length=100)),
                ("event_id", models.CharField(max_length=255)),
                ("payload", models.JSONField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("success", "Success"),
                            ("failed", "Failed"),
                            ("retrying", "Retrying"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("attempt_count", models.PositiveIntegerField(default=0)),
                ("max_attempts", models.PositiveIntegerField(default=3)),
                (
                    "response_status_code",
                    models.PositiveIntegerField(blank=True, null=True),
                ),
                ("response_headers", models.JSONField(blank=True, default=dict)),
                ("response_body", models.TextField(blank=True)),
                ("error_message", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("first_attempt_at", models.DateTimeField(blank=True, null=True)),
                ("last_attempt_at", models.DateTimeField(blank=True, null=True)),
                ("delivered_at", models.DateTimeField(blank=True, null=True)),
                ("next_retry_at", models.DateTimeField(blank=True, null=True)),
                ("request_headers", models.JSONField(blank=True, default=dict)),
                ("request_signature", models.CharField(blank=True, max_length=255)),
                ("duration_ms", models.PositiveIntegerField(blank=True, null=True)),
                (
                    "endpoint",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="deliveries",
                        to="core.webhookendpoint",
                    ),
                ),
            ],
            options={
                "verbose_name": "Webhook Delivery",
                "verbose_name_plural": "Webhook Deliveries",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["status", "next_retry_at"],
                        name="core_webhoo_status_6a0fea_idx",
                    ),
                    models.Index(
                        fields=["event_type", "created_at"],
                        name="core_webhoo_event_t_eb2d40_idx",
                    ),
                    models.Index(
                        fields=["endpoint", "status"],
                        name="core_webhoo_endpoin_0c9ad6_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="WebhookAnalytics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateField()),
                ("total_deliveries", models.PositiveIntegerField(default=0)),
                ("successful_deliveries", models.PositiveIntegerField(default=0)),
                ("failed_deliveries", models.PositiveIntegerField(default=0)),
                ("avg_response_time_ms", models.FloatField(default=0)),
                ("min_response_time_ms", models.PositiveIntegerField(default=0)),
                ("max_response_time_ms", models.PositiveIntegerField(default=0)),
                ("event_type_stats", models.JSONField(default=dict)),
                ("error_types", models.JSONField(default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "endpoint",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="analytics",
                        to="core.webhookendpoint",
                    ),
                ),
            ],
            options={
                "verbose_name": "Webhook Analytics",
                "verbose_name_plural": "Webhook Analytics",
                "ordering": ["-date"],
                "unique_together": {("endpoint", "date")},
            },
        ),
    ]
