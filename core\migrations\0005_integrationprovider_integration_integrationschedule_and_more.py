# Generated by Django 5.2.5 on 2025-08-07 18:58

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0004_webhookevent_webhookendpoint_webhooksecurity_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="IntegrationProvider",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                ("display_name", models.CharField(max_length=150)),
                (
                    "provider_type",
                    models.CharField(
                        choices=[
                            ("payment", "Payment Gateway"),
                            ("email", "Email Service"),
                            ("sms", "SMS Gateway"),
                            ("storage", "Cloud Storage"),
                            ("analytics", "Analytics Service"),
                            ("communication", "Communication Platform"),
                            ("learning", "Learning Management System"),
                            ("identity", "Identity Provider"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField(blank=True)),
                ("website_url", models.URLField(blank=True)),
                ("documentation_url", models.URLField(blank=True)),
                ("support_url", models.URLField(blank=True)),
                ("logo_url", models.URLField(blank=True)),
                (
                    "base_url",
                    models.URLField(
                        blank=True, help_text="Base API URL for the provider"
                    ),
                ),
                ("api_version", models.CharField(blank=True, max_length=20)),
                (
                    "required_credentials",
                    models.JSONField(
                        default=list, help_text="List of required credential fields"
                    ),
                ),
                (
                    "optional_credentials",
                    models.JSONField(
                        default=list, help_text="List of optional credential fields"
                    ),
                ),
                (
                    "supported_features",
                    models.JSONField(
                        default=list,
                        help_text="List of features supported by this provider",
                    ),
                ),
                (
                    "rate_limits",
                    models.JSONField(
                        default=dict, help_text="Rate limiting information"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("inactive", "Inactive"),
                            ("maintenance", "Under Maintenance"),
                            ("deprecated", "Deprecated"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                ("is_enabled", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Integration Provider",
                "verbose_name_plural": "Integration Providers",
                "ordering": ["provider_type", "name"],
            },
        ),
        migrations.CreateModel(
            name="Integration",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True)),
                (
                    "credentials",
                    models.JSONField(
                        default=dict,
                        help_text="Encrypted credentials for the integration",
                    ),
                ),
                (
                    "settings",
                    models.JSONField(
                        default=dict, help_text="Integration-specific settings"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("inactive", "Inactive"),
                            ("error", "Error"),
                            ("testing", "Testing"),
                            ("pending", "Pending Setup"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("is_enabled", models.BooleanField(default=True)),
                ("last_sync_at", models.DateTimeField(blank=True, null=True)),
                ("last_error", models.TextField(blank=True)),
                ("error_count", models.PositiveIntegerField(default=0)),
                ("total_requests", models.PositiveIntegerField(default=0)),
                ("successful_requests", models.PositiveIntegerField(default=0)),
                ("failed_requests", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_integrations",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "provider",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="integrations",
                        to="core.integrationprovider",
                    ),
                ),
            ],
            options={
                "verbose_name": "Integration",
                "verbose_name_plural": "Integrations",
                "ordering": ["-created_at"],
                "unique_together": {("provider", "name")},
            },
        ),
        migrations.CreateModel(
            name="IntegrationSchedule",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                (
                    "schedule_type",
                    models.CharField(
                        choices=[
                            ("sync", "Data Sync"),
                            ("backup", "Backup"),
                            ("cleanup", "Cleanup"),
                            ("report", "Report Generation"),
                            ("webhook", "Webhook Processing"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "cron_expression",
                    models.CharField(
                        help_text="Cron expression for scheduling (e.g., '0 */6 * * *' for every 6 hours)",
                        max_length=100,
                    ),
                ),
                (
                    "task_config",
                    models.JSONField(
                        default=dict, help_text="Configuration for the scheduled task"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("paused", "Paused"),
                            ("disabled", "Disabled"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                ("last_run_at", models.DateTimeField(blank=True, null=True)),
                ("next_run_at", models.DateTimeField(blank=True, null=True)),
                ("run_count", models.PositiveIntegerField(default=0)),
                ("success_count", models.PositiveIntegerField(default=0)),
                ("failure_count", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "integration",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="schedules",
                        to="core.integration",
                    ),
                ),
            ],
            options={
                "verbose_name": "Integration Schedule",
                "verbose_name_plural": "Integration Schedules",
                "ordering": ["integration", "name"],
            },
        ),
        migrations.CreateModel(
            name="IntegrationAnalytics",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("date", models.DateField()),
                ("total_requests", models.PositiveIntegerField(default=0)),
                ("successful_requests", models.PositiveIntegerField(default=0)),
                ("failed_requests", models.PositiveIntegerField(default=0)),
                ("avg_response_time_ms", models.FloatField(default=0)),
                ("min_response_time_ms", models.PositiveIntegerField(default=0)),
                ("max_response_time_ms", models.PositiveIntegerField(default=0)),
                ("data_sent_bytes", models.BigIntegerField(default=0)),
                ("data_received_bytes", models.BigIntegerField(default=0)),
                ("error_types", models.JSONField(default=dict)),
                ("hourly_distribution", models.JSONField(default=dict)),
                ("endpoint_usage", models.JSONField(default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "integration",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="analytics",
                        to="core.integration",
                    ),
                ),
            ],
            options={
                "verbose_name": "Integration Analytics",
                "verbose_name_plural": "Integration Analytics",
                "ordering": ["-date"],
                "unique_together": {("integration", "date")},
            },
        ),
        migrations.CreateModel(
            name="IntegrationLog",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "level",
                    models.CharField(
                        choices=[
                            ("debug", "Debug"),
                            ("info", "Info"),
                            ("warning", "Warning"),
                            ("error", "Error"),
                            ("critical", "Critical"),
                        ],
                        max_length=10,
                    ),
                ),
                (
                    "action_type",
                    models.CharField(
                        choices=[
                            ("request", "API Request"),
                            ("response", "API Response"),
                            ("sync", "Data Sync"),
                            ("webhook", "Webhook"),
                            ("error", "Error"),
                            ("config", "Configuration Change"),
                            ("test", "Connection Test"),
                        ],
                        max_length=20,
                    ),
                ),
                ("message", models.TextField()),
                ("details", models.JSONField(blank=True, default=dict)),
                ("request_data", models.JSONField(blank=True, default=dict)),
                ("response_data", models.JSONField(blank=True, default=dict)),
                ("status_code", models.PositiveIntegerField(blank=True, null=True)),
                ("duration_ms", models.PositiveIntegerField(blank=True, null=True)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True)),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                (
                    "integration",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="logs",
                        to="core.integration",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Integration Log",
                "verbose_name_plural": "Integration Logs",
                "ordering": ["-timestamp"],
                "indexes": [
                    models.Index(
                        fields=["integration", "timestamp"],
                        name="core_integr_integra_ffe2d0_idx",
                    ),
                    models.Index(
                        fields=["level", "timestamp"],
                        name="core_integr_level_3a9d50_idx",
                    ),
                    models.Index(
                        fields=["action_type", "timestamp"],
                        name="core_integr_action__112511_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="IntegrationMapping",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "mapping_type",
                    models.CharField(
                        choices=[
                            ("field", "Field Mapping"),
                            ("value", "Value Mapping"),
                            ("transform", "Data Transform"),
                        ],
                        max_length=20,
                    ),
                ),
                ("source_field", models.CharField(max_length=200)),
                ("target_field", models.CharField(max_length=200)),
                (
                    "transform_rules",
                    models.JSONField(
                        default=dict,
                        help_text="Rules for transforming data between systems",
                    ),
                ),
                ("is_required", models.BooleanField(default=False)),
                ("validation_rules", models.JSONField(default=dict)),
                ("description", models.TextField(blank=True)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "integration",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="mappings",
                        to="core.integration",
                    ),
                ),
            ],
            options={
                "verbose_name": "Integration Mapping",
                "verbose_name_plural": "Integration Mappings",
                "ordering": ["integration", "source_field"],
                "unique_together": {("integration", "source_field", "target_field")},
            },
        ),
        migrations.CreateModel(
            name="IntegrationWebhook",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("event_type", models.CharField(max_length=100)),
                ("payload", models.JSONField()),
                ("headers", models.JSONField(default=dict)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("processed", "Processed"),
                            ("failed", "Failed"),
                            ("ignored", "Ignored"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("processed_at", models.DateTimeField(blank=True, null=True)),
                ("error_message", models.TextField(blank=True)),
                ("retry_count", models.PositiveIntegerField(default=0)),
                ("signature", models.CharField(blank=True, max_length=255)),
                ("is_verified", models.BooleanField(default=False)),
                ("source_ip", models.GenericIPAddressField(blank=True, null=True)),
                ("received_at", models.DateTimeField(auto_now_add=True)),
                (
                    "integration",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="webhooks",
                        to="core.integration",
                    ),
                ),
            ],
            options={
                "verbose_name": "Integration Webhook",
                "verbose_name_plural": "Integration Webhooks",
                "ordering": ["-received_at"],
                "indexes": [
                    models.Index(
                        fields=["integration", "status"],
                        name="core_integr_integra_abba5e_idx",
                    ),
                    models.Index(
                        fields=["event_type", "received_at"],
                        name="core_integr_event_t_00101d_idx",
                    ),
                ],
            },
        ),
    ]
