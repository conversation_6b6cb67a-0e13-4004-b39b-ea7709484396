"""
Core mixins for the School ERP system.
"""

from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.exceptions import PermissionDenied
from django.shortcuts import get_object_or_404
from .models import School


class SchoolMixin:
    """Mixin to handle school access for views"""
    
    def get_school(self):
        """Get the current user's school"""
        if hasattr(self.request.user, 'school'):
            return self.request.user.school
        # For now, return the first school if no specific school is set
        return School.objects.first()
    
    def dispatch(self, request, *args, **kwargs):
        """Override dispatch to ensure school access"""
        if not request.user.is_authenticated:
            return super().dispatch(request, *args, **kwargs)
        
        school = self.get_school()
        if not school:
            raise PermissionDenied("No school access")
        
        # Add school to request and user for easy access
        request.school = school
        request.user.school = school  # Add school attribute to user
        return super().dispatch(request, *args, **kwargs)


class SchoolRequiredMixin(LoginRequiredMixin, SchoolMixin):
    """Mixin that requires both authentication and school access"""
    pass


class AdminRequiredMixin(SchoolRequiredMixin):
    """Mixin that requires admin access"""
    
    def dispatch(self, request, *args, **kwargs):
        response = super().dispatch(request, *args, **kwargs)
        
        if not request.user.is_staff and not getattr(request.user, 'user_type', None) == 'admin':
            raise PermissionDenied("Admin access required")
        
        return response