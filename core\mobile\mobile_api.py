"""
Mobile-specific API views for School ERP System.
Provides optimized endpoints for mobile applications.
"""

from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views import View
from django.core.paginator import Paginator
from django.db.models import Q, Count, Avg
from django.utils import timezone
from django.conf import settings
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.pagination import PageNumberPagination
import json
from datetime import datetime, timedelta
from core.models import School, AuditLog
from students.models import Student, Class
from academics.models import Subject, StudentAttendance, Grade
from communications.models import Notification
from accounts.models import User


class MobilePagination(PageNumberPagination):
    """Custom pagination for mobile API responses."""
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100
    
    def get_paginated_response(self, data):
        return Response({
            'count': self.page.paginator.count,
            'next': self.get_next_link(),
            'previous': self.get_previous_link(),
            'page_size': self.page_size,
            'total_pages': self.page.paginator.num_pages,
            'current_page': self.page.number,
            'results': data
        })


class MobileAPIView(APIView):
    """Base class for mobile API views."""
    permission_classes = [IsAuthenticated]
    pagination_class = MobilePagination
    
    def get_school(self):
        """Get the current user's school."""
        return getattr(self.request.user, 'school', None)
    
    def get_mobile_context(self):
        """Get common mobile context data."""
        return {
            'timestamp': timezone.now().isoformat(),
            'user_id': self.request.user.id,
            'school_id': self.get_school().id if self.get_school() else None,
            'language': self.request.LANGUAGE_CODE,
        }
    
    def mobile_response(self, data, status_code=200, message=None):
        """Standard mobile API response format."""
        response_data = {
            'success': status_code < 400,
            'status_code': status_code,
            'data': data,
            'meta': self.get_mobile_context()
        }
        
        if message:
            response_data['message'] = message
            
        return Response(response_data, status=status_code)


@method_decorator(login_required, name='dispatch')
class DashboardStatsAPI(MobileAPIView):
    """Mobile dashboard statistics API."""
    
    def get(self, request):
        """Get dashboard statistics optimized for mobile."""
        school = self.get_school()
        if not school:
            return self.mobile_response(
                {}, 
                status_code=400, 
                message="No school associated with user"
            )
        
        today = timezone.now().date()
        
        # Get basic statistics
        total_students = Student.objects.filter(school=school, is_active=True).count()
        
        # Today's attendance statistics
        attendance_today = StudentAttendance.objects.filter(
            school=school,
            date=today
        ).aggregate(
            present=Count('id', filter=Q(status='present')),
            absent=Count('id', filter=Q(status='absent')),
            late=Count('id', filter=Q(status='late')),
            excused=Count('id', filter=Q(status='excused'))
        )
        
        # Recent activity count
        recent_activities = AuditLog.objects.filter(
            school=school,
            created_at__gte=timezone.now() - timedelta(days=7)
        ).count()
        
        # Unread notifications
        unread_notifications = Notification.objects.filter(
            recipient=request.user,
            is_read=False
        ).count()
        
        # Class statistics
        classes_count = Class.objects.filter(school=school, is_active=True).count()
        
        # Subject statistics
        subjects_count = Subject.objects.filter(school=school, is_active=True).count()
        
        stats = {
            'total_students': total_students,
            'present_today': attendance_today['present'] or 0,
            'absent_today': attendance_today['absent'] or 0,
            'late_today': attendance_today['late'] or 0,
            'excused_today': attendance_today['excused'] or 0,
            'attendance_rate': round(
                (attendance_today['present'] or 0) / max(total_students, 1) * 100, 1
            ),
            'recent_activities': recent_activities,
            'unread_notifications': unread_notifications,
            'total_classes': classes_count,
            'total_subjects': subjects_count,
            'last_updated': timezone.now().isoformat(),
        }
        
        return self.mobile_response(stats)


@method_decorator(login_required, name='dispatch')
class StudentListAPI(MobileAPIView):
    """Mobile student list API with search and filtering."""
    
    def get(self, request):
        """Get paginated student list for mobile."""
        school = self.get_school()
        if not school:
            return self.mobile_response(
                [], 
                status_code=400, 
                message="No school associated with user"
            )
        
        # Get query parameters
        search = request.GET.get('search', '').strip()
        class_id = request.GET.get('class_id')
        grade_id = request.GET.get('grade_id')
        status_filter = request.GET.get('status', 'active')
        
        # Base queryset
        queryset = Student.objects.filter(school=school)
        
        # Apply filters
        if status_filter == 'active':
            queryset = queryset.filter(is_active=True)
        elif status_filter == 'inactive':
            queryset = queryset.filter(is_active=False)
        
        if search:
            queryset = queryset.filter(
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search) |
                Q(student_id__icontains=search) |
                Q(email__icontains=search)
            )
        
        if class_id:
            queryset = queryset.filter(current_class_id=class_id)
        
        if grade_id:
            queryset = queryset.filter(current_class__grade_id=grade_id)
        
        # Order by name
        queryset = queryset.select_related(
            'current_class', 
            'current_class__grade'
        ).order_by('first_name', 'last_name')
        
        # Paginate
        paginator = Paginator(queryset, 20)
        page_number = request.GET.get('page', 1)
        page_obj = paginator.get_page(page_number)
        
        # Serialize data
        students = []
        for student in page_obj:
            students.append({
                'id': student.id,
                'student_id': student.student_id,
                'full_name': student.get_full_name(),
                'first_name': student.first_name,
                'last_name': student.last_name,
                'email': student.email,
                'phone': student.phone,
                'class_name': student.current_class.name if student.current_class else None,
                'grade_name': student.current_class.grade.name if student.current_class and student.current_class.grade else None,
                'is_active': student.is_active,
                'photo_url': student.photo.url if student.photo else None,
                'date_of_birth': student.date_of_birth.isoformat() if student.date_of_birth else None,
                'gender': student.gender,
                'created_at': student.created_at.isoformat(),
            })
        
        response_data = {
            'students': students,
            'pagination': {
                'count': paginator.count,
                'num_pages': paginator.num_pages,
                'current_page': page_obj.number,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous(),
                'next_page': page_obj.next_page_number() if page_obj.has_next() else None,
                'previous_page': page_obj.previous_page_number() if page_obj.has_previous() else None,
            }
        }
        
        return self.mobile_response(response_data)


@method_decorator(login_required, name='dispatch')
class AttendanceAPI(MobileAPIView):
    """Mobile attendance API for taking and viewing attendance."""
    
    def get(self, request):
        """Get attendance data for mobile view."""
        school = self.get_school()
        if not school:
            return self.mobile_response(
                {}, 
                status_code=400, 
                message="No school associated with user"
            )
        
        date_str = request.GET.get('date', timezone.now().date().isoformat())
        class_id = request.GET.get('class_id')
        
        try:
            date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return self.mobile_response(
                {}, 
                status_code=400, 
                message="Invalid date format. Use YYYY-MM-DD"
            )
        
        # Get attendance records
        attendance_query = StudentAttendance.objects.filter(
            school=school,
            date=date
        ).select_related('student', 'student__current_class')
        
        if class_id:
            attendance_query = attendance_query.filter(student__current_class_id=class_id)
        
        attendance_records = {}
        for record in attendance_query:
            attendance_records[record.student.id] = {
                'id': record.id,
                'status': record.status,
                'notes': record.notes,
                'marked_by': record.marked_by.get_full_name() if record.marked_by else None,
                'marked_at': record.marked_at.isoformat() if record.marked_at else None,
            }
        
        # Get students for the class/school
        students_query = Student.objects.filter(school=school, is_active=True)
        if class_id:
            students_query = students_query.filter(current_class_id=class_id)
        
        students = []
        for student in students_query.select_related('current_class'):
            attendance_record = attendance_records.get(student.id, {})
            students.append({
                'id': student.id,
                'student_id': student.student_id,
                'full_name': student.get_full_name(),
                'class_name': student.current_class.name if student.current_class else None,
                'photo_url': student.photo.url if student.photo else None,
                'attendance': {
                    'status': attendance_record.get('status', 'not_marked'),
                    'notes': attendance_record.get('notes', ''),
                    'marked_by': attendance_record.get('marked_by'),
                    'marked_at': attendance_record.get('marked_at'),
                }
            })
        
        response_data = {
            'date': date.isoformat(),
            'students': students,
            'summary': {
                'total': len(students),
                'present': len([s for s in students if s['attendance']['status'] == 'present']),
                'absent': len([s for s in students if s['attendance']['status'] == 'absent']),
                'late': len([s for s in students if s['attendance']['status'] == 'late']),
                'excused': len([s for s in students if s['attendance']['status'] == 'excused']),
                'not_marked': len([s for s in students if s['attendance']['status'] == 'not_marked']),
            }
        }
        
        return self.mobile_response(response_data)
    
    def post(self, request):
        """Submit attendance data from mobile."""
        school = self.get_school()
        if not school:
            return self.mobile_response(
                {}, 
                status_code=400, 
                message="No school associated with user"
            )
        
        try:
            data = json.loads(request.body)
        except json.JSONDecodeError:
            return self.mobile_response(
                {}, 
                status_code=400, 
                message="Invalid JSON data"
            )
        
        date_str = data.get('date')
        attendance_data = data.get('attendance', [])
        
        if not date_str or not attendance_data:
            return self.mobile_response(
                {}, 
                status_code=400, 
                message="Date and attendance data are required"
            )
        
        try:
            date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return self.mobile_response(
                {}, 
                status_code=400, 
                message="Invalid date format. Use YYYY-MM-DD"
            )
        
        # Process attendance records
        created_count = 0
        updated_count = 0
        errors = []
        
        for record in attendance_data:
            student_id = record.get('student_id')
            status = record.get('status')
            notes = record.get('notes', '')
            
            if not student_id or not status:
                errors.append(f"Missing student_id or status for record")
                continue
            
            try:
                student = Student.objects.get(id=student_id, school=school)
                
                attendance, created = StudentAttendance.objects.update_or_create(
                    student=student,
                    date=date,
                    school=school,
                    defaults={
                        'status': status,
                        'notes': notes,
                        'marked_by': request.user,
                        'marked_at': timezone.now(),
                    }
                )
                
                if created:
                    created_count += 1
                else:
                    updated_count += 1
                    
            except Student.DoesNotExist:
                errors.append(f"Student with ID {student_id} not found")
            except Exception as e:
                errors.append(f"Error processing student {student_id}: {str(e)}")
        
        response_data = {
            'created': created_count,
            'updated': updated_count,
            'errors': errors,
            'total_processed': created_count + updated_count,
        }
        
        message = f"Processed {created_count + updated_count} attendance records"
        if errors:
            message += f" with {len(errors)} errors"
        
        return self.mobile_response(response_data, message=message)


@method_decorator(login_required, name='dispatch')
class NotificationsAPI(MobileAPIView):
    """Mobile notifications API."""
    
    def get(self, request):
        """Get user notifications for mobile."""
        # Get query parameters
        unread_only = request.GET.get('unread_only', 'false').lower() == 'true'
        
        # Base queryset
        queryset = Notification.objects.filter(recipient=request.user)
        
        if unread_only:
            queryset = queryset.filter(is_read=False)
        
        # Order by creation date (newest first)
        queryset = queryset.order_by('-created_at')
        
        # Paginate
        paginator = Paginator(queryset, 20)
        page_number = request.GET.get('page', 1)
        page_obj = paginator.get_page(page_number)
        
        # Serialize notifications
        notifications = []
        for notification in page_obj:
            notifications.append({
                'id': notification.id,
                'title': notification.title,
                'message': notification.message,
                'type': notification.notification_type,
                'is_read': notification.is_read,
                'created_at': notification.created_at.isoformat(),
                'read_at': notification.read_at.isoformat() if notification.read_at else None,
                'action_url': notification.action_url,
                'icon': self.get_notification_icon(notification.notification_type),
            })
        
        response_data = {
            'notifications': notifications,
            'pagination': {
                'count': paginator.count,
                'num_pages': paginator.num_pages,
                'current_page': page_obj.number,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous(),
            },
            'unread_count': Notification.objects.filter(
                recipient=request.user, 
                is_read=False
            ).count(),
        }
        
        return self.mobile_response(response_data)
    
    def post(self, request):
        """Mark notifications as read."""
        try:
            data = json.loads(request.body)
        except json.JSONDecodeError:
            return self.mobile_response(
                {}, 
                status_code=400, 
                message="Invalid JSON data"
            )
        
        notification_ids = data.get('notification_ids', [])
        mark_all = data.get('mark_all', False)
        
        if mark_all:
            # Mark all notifications as read
            updated = Notification.objects.filter(
                recipient=request.user,
                is_read=False
            ).update(
                is_read=True,
                read_at=timezone.now()
            )
        elif notification_ids:
            # Mark specific notifications as read
            updated = Notification.objects.filter(
                id__in=notification_ids,
                recipient=request.user,
                is_read=False
            ).update(
                is_read=True,
                read_at=timezone.now()
            )
        else:
            return self.mobile_response(
                {}, 
                status_code=400, 
                message="Either notification_ids or mark_all must be provided"
            )
        
        return self.mobile_response(
            {'updated_count': updated},
            message=f"Marked {updated} notifications as read"
        )
    
    def get_notification_icon(self, notification_type):
        """Get icon for notification type."""
        icons = {
            'info': 'fas fa-info-circle',
            'success': 'fas fa-check-circle',
            'warning': 'fas fa-exclamation-triangle',
            'error': 'fas fa-times-circle',
            'message': 'fas fa-envelope',
            'attendance': 'fas fa-clipboard-check',
            'grade': 'fas fa-star',
            'payment': 'fas fa-credit-card',
            'announcement': 'fas fa-bullhorn',
        }
        return icons.get(notification_type, 'fas fa-bell')


@method_decorator(login_required, name='dispatch')
class SyncAPI(MobileAPIView):
    """Mobile data synchronization API."""
    
    def post(self, request):
        """Sync mobile data with server."""
        try:
            data = json.loads(request.body)
        except json.JSONDecodeError:
            return self.mobile_response(
                {}, 
                status_code=400, 
                message="Invalid JSON data"
            )
        
        sync_type = data.get('type')
        sync_data = data.get('data', {})
        last_sync = data.get('last_sync')
        
        if not sync_type:
            return self.mobile_response(
                {}, 
                status_code=400, 
                message="Sync type is required"
            )
        
        # Parse last sync timestamp
        last_sync_dt = None
        if last_sync:
            try:
                last_sync_dt = datetime.fromisoformat(last_sync.replace('Z', '+00:00'))
            except ValueError:
                pass
        
        # Handle different sync types
        if sync_type == 'attendance':
            result = self.sync_attendance_data(sync_data, last_sync_dt)
        elif sync_type == 'grades':
            result = self.sync_grades_data(sync_data, last_sync_dt)
        elif sync_type == 'students':
            result = self.sync_students_data(sync_data, last_sync_dt)
        else:
            return self.mobile_response(
                {}, 
                status_code=400, 
                message=f"Unsupported sync type: {sync_type}"
            )
        
        return self.mobile_response(result)
    
    def sync_attendance_data(self, data, last_sync):
        """Sync attendance data."""
        # Implementation for attendance sync
        return {
            'synced_records': 0,
            'conflicts': [],
            'last_sync': timezone.now().isoformat(),
        }
    
    def sync_grades_data(self, data, last_sync):
        """Sync grades data."""
        # Implementation for grades sync
        return {
            'synced_records': 0,
            'conflicts': [],
            'last_sync': timezone.now().isoformat(),
        }
    
    def sync_students_data(self, data, last_sync):
        """Sync students data."""
        # Implementation for students sync
        return {
            'synced_records': 0,
            'conflicts': [],
            'last_sync': timezone.now().isoformat(),
        }


# Function-based API views for simpler endpoints
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def mobile_user_profile(request):
    """Get user profile data for mobile."""
    user = request.user
    
    profile_data = {
        'id': user.id,
        'username': user.username,
        'email': user.email,
        'first_name': user.first_name,
        'last_name': user.last_name,
        'full_name': user.get_full_name(),
        'is_staff': user.is_staff,
        'is_superuser': user.is_superuser,
        'date_joined': user.date_joined.isoformat(),
        'last_login': user.last_login.isoformat() if user.last_login else None,
        'school': {
            'id': user.school.id,
            'name': user.school.name,
            'name_ar': getattr(user.school, 'name_ar', ''),
        } if hasattr(user, 'school') and user.school else None,
        'permissions': list(user.get_all_permissions()),
        'groups': [group.name for group in user.groups.all()],
    }
    
    return Response({
        'success': True,
        'data': profile_data,
        'meta': {
            'timestamp': timezone.now().isoformat(),
            'language': request.LANGUAGE_CODE,
        }
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def mobile_app_config(request):
    """Get mobile app configuration."""
    config = {
        'app_version': '1.0.0',
        'api_version': 'v1',
        'features': {
            'offline_mode': True,
            'push_notifications': True,
            'biometric_auth': True,
            'dark_mode': True,
            'multi_language': True,
        },
        'settings': {
            'cache_duration': 3600,  # 1 hour
            'sync_interval': 300,    # 5 minutes
            'max_offline_days': 7,
            'supported_languages': [code for code, name in settings.LANGUAGES],
            'default_language': settings.LANGUAGE_CODE,
        },
        'endpoints': {
            'dashboard_stats': '/api/mobile/dashboard/stats/',
            'students': '/api/mobile/students/',
            'attendance': '/api/mobile/attendance/',
            'notifications': '/api/mobile/notifications/',
            'sync': '/api/mobile/sync/',
            'profile': '/api/mobile/profile/',
        }
    }
    
    return Response({
        'success': True,
        'data': config,
        'meta': {
            'timestamp': timezone.now().isoformat(),
            'language': request.LANGUAGE_CODE,
        }
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def mobile_feedback(request):
    """Submit mobile app feedback."""
    try:
        data = json.loads(request.body)
    except json.JSONDecodeError:
        return Response({
            'success': False,
            'message': 'Invalid JSON data'
        }, status=400)
    
    feedback_type = data.get('type', 'general')
    message = data.get('message', '')
    rating = data.get('rating')
    device_info = data.get('device_info', {})
    
    if not message:
        return Response({
            'success': False,
            'message': 'Feedback message is required'
        }, status=400)
    
    # Save feedback (you might want to create a Feedback model)
    # For now, we'll just log it
    import logging
    logger = logging.getLogger('mobile_feedback')
    logger.info(f"Mobile feedback from {request.user.username}: {message}")
    
    return Response({
        'success': True,
        'message': 'Thank you for your feedback!',
        'data': {
            'feedback_id': f"fb_{timezone.now().timestamp()}",
            'submitted_at': timezone.now().isoformat(),
        }
    })