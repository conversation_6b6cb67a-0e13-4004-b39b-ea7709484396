"""
Mobile authentication utilities for School ERP System.
Provides JWT token management and mobile-specific authentication.
"""

from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.models import User
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views import View
from django.utils import timezone
from django.conf import settings
from rest_framework_simplejwt.tokens import RefreshToken, AccessToken
from rest_framework_simplejwt.exceptions import TokenError, InvalidToken
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
import json
import hashlib
import secrets
from datetime import datetime, timedelta
from accounts.models import User


class MobileAuthMixin:
    """Mixin for mobile authentication utilities."""
    
    def generate_mobile_tokens(self, user):
        """Generate JWT tokens for mobile authentication."""
        refresh = RefreshToken.for_user(user)
        access = refresh.access_token
        
        # Add custom claims
        access['user_id'] = user.id
        access['username'] = user.username
        access['school_id'] = user.school.id if hasattr(user, 'school') and user.school else None
        access['is_mobile'] = True
        
        return {
            'access_token': str(access),
            'refresh_token': str(refresh),
            'access_expires_at': (timezone.now() + timedelta(seconds=settings.SIMPLE_JWT['ACCESS_TOKEN_LIFETIME'].total_seconds())).isoformat(),
            'refresh_expires_at': (timezone.now() + timedelta(seconds=settings.SIMPLE_JWT['REFRESH_TOKEN_LIFETIME'].total_seconds())).isoformat(),
        }
    
    def validate_mobile_token(self, token):
        """Validate mobile JWT token."""
        try:
            access_token = AccessToken(token)
            user_id = access_token.get('user_id')
            is_mobile = access_token.get('is_mobile', False)
            
            if not is_mobile:
                return None, "Token not issued for mobile use"
            
            try:
                user = User.objects.get(id=user_id, is_active=True)
                return user, None
            except User.DoesNotExist:
                return None, "User not found or inactive"
                
        except (TokenError, InvalidToken) as e:
            return None, str(e)
    
    def get_device_fingerprint(self, request):
        """Generate device fingerprint for security."""
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        accept_language = request.META.get('HTTP_ACCEPT_LANGUAGE', '')
        accept_encoding = request.META.get('HTTP_ACCEPT_ENCODING', '')
        
        fingerprint_data = f"{user_agent}:{accept_language}:{accept_encoding}"
        return hashlib.sha256(fingerprint_data.encode()).hexdigest()[:32]


@method_decorator(csrf_exempt, name='dispatch')
class MobileLoginAPI(View, MobileAuthMixin):
    """Mobile login API endpoint."""
    
    def post(self, request):
        """Handle mobile login request."""
        try:
            data = json.loads(request.body)
        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'message': 'Invalid JSON data'
            }, status=400)
        
        username = data.get('username', '').strip()
        password = data.get('password', '')
        device_info = data.get('device_info', {})
        remember_me = data.get('remember_me', False)
        
        if not username or not password:
            return JsonResponse({
                'success': False,
                'message': 'Username and password are required'
            }, status=400)
        
        # Authenticate user
        user = authenticate(request, username=username, password=password)
        
        if user is None:
            return JsonResponse({
                'success': False,
                'message': 'Invalid username or password'
            }, status=401)
        
        if not user.is_active:
            return JsonResponse({
                'success': False,
                'message': 'Account is disabled'
            }, status=401)
        
        # Generate tokens
        tokens = self.generate_mobile_tokens(user)
        
        # Generate device fingerprint
        device_fingerprint = self.get_device_fingerprint(request)
        
        # Update user's last login
        user.last_login = timezone.now()
        user.save(update_fields=['last_login'])
        
        # Log the login attempt
        self.log_mobile_login(user, device_info, device_fingerprint, success=True)
        
        response_data = {
            'success': True,
            'message': 'Login successful',
            'data': {
                'user': {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'full_name': user.get_full_name(),
                    'is_staff': user.is_staff,
                    'school': {
                        'id': user.school.id,
                        'name': user.school.name,
                        'name_ar': getattr(user.school, 'name_ar', ''),
                    } if hasattr(user, 'school') and user.school else None,
                },
                'tokens': tokens,
                'device_fingerprint': device_fingerprint,
                'session_expires_at': tokens['access_expires_at'],
            }
        }
        
        return JsonResponse(response_data)
    
    def log_mobile_login(self, user, device_info, device_fingerprint, success=True):
        """Log mobile login attempt."""
        from core.models import AuditLog
        
        AuditLog.objects.create(
            user=user,
            school=getattr(user, 'school', None),
            action='mobile_login',
            model_name='User',
            object_id=user.id,
            changes={
                'device_info': device_info,
                'device_fingerprint': device_fingerprint,
                'success': success,
                'timestamp': timezone.now().isoformat(),
            }
        )


@method_decorator(csrf_exempt, name='dispatch')
class MobileLogoutAPI(View, MobileAuthMixin):
    """Mobile logout API endpoint."""
    
    def post(self, request):
        """Handle mobile logout request."""
        try:
            data = json.loads(request.body)
        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'message': 'Invalid JSON data'
            }, status=400)
        
        refresh_token = data.get('refresh_token')
        
        if not refresh_token:
            return JsonResponse({
                'success': False,
                'message': 'Refresh token is required'
            }, status=400)
        
        try:
            # Blacklist the refresh token
            token = RefreshToken(refresh_token)
            token.blacklist()
            
            return JsonResponse({
                'success': True,
                'message': 'Logout successful'
            })
            
        except (TokenError, InvalidToken):
            return JsonResponse({
                'success': False,
                'message': 'Invalid refresh token'
            }, status=400)


@method_decorator(csrf_exempt, name='dispatch')
class MobileTokenRefreshAPI(View, MobileAuthMixin):
    """Mobile token refresh API endpoint."""
    
    def post(self, request):
        """Handle mobile token refresh request."""
        try:
            data = json.loads(request.body)
        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'message': 'Invalid JSON data'
            }, status=400)
        
        refresh_token = data.get('refresh_token')
        
        if not refresh_token:
            return JsonResponse({
                'success': False,
                'message': 'Refresh token is required'
            }, status=400)
        
        try:
            # Validate and refresh token
            refresh = RefreshToken(refresh_token)
            user_id = refresh.get('user_id')
            
            # Get user
            try:
                user = User.objects.get(id=user_id, is_active=True)
            except User.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'message': 'User not found or inactive'
                }, status=401)
            
            # Generate new tokens
            tokens = self.generate_mobile_tokens(user)
            
            # Blacklist old refresh token
            refresh.blacklist()
            
            return JsonResponse({
                'success': True,
                'message': 'Token refreshed successfully',
                'data': {
                    'tokens': tokens,
                }
            })
            
        except (TokenError, InvalidToken) as e:
            return JsonResponse({
                'success': False,
                'message': f'Invalid refresh token: {str(e)}'
            }, status=401)


@api_view(['POST'])
@permission_classes([AllowAny])
def mobile_password_reset(request):
    """Mobile password reset request."""
    try:
        data = json.loads(request.body)
    except json.JSONDecodeError:
        return Response({
            'success': False,
            'message': 'Invalid JSON data'
        }, status=400)
    
    email = data.get('email', '').strip().lower()
    
    if not email:
        return Response({
            'success': False,
            'message': 'Email is required'
        }, status=400)
    
    try:
        user = User.objects.get(email=email, is_active=True)
        
        # Generate password reset token
        reset_token = secrets.token_urlsafe(32)
        
        # Store reset token (you might want to create a PasswordResetToken model)
        # For now, we'll use cache
        from django.core.cache import cache
        cache.set(f'password_reset_{reset_token}', user.id, timeout=3600)  # 1 hour
        
        # Send password reset email (implement email sending)
        # send_password_reset_email(user, reset_token)
        
        return Response({
            'success': True,
            'message': 'Password reset instructions sent to your email',
            'data': {
                'reset_token': reset_token,  # Remove this in production
                'expires_at': (timezone.now() + timedelta(hours=1)).isoformat(),
            }
        })
        
    except User.DoesNotExist:
        # Don't reveal if email exists or not
        return Response({
            'success': True,
            'message': 'If the email exists, password reset instructions have been sent'
        })


@api_view(['POST'])
@permission_classes([AllowAny])
def mobile_password_reset_confirm(request):
    """Confirm mobile password reset."""
    try:
        data = json.loads(request.body)
    except json.JSONDecodeError:
        return Response({
            'success': False,
            'message': 'Invalid JSON data'
        }, status=400)
    
    reset_token = data.get('reset_token')
    new_password = data.get('new_password')
    
    if not reset_token or not new_password:
        return Response({
            'success': False,
            'message': 'Reset token and new password are required'
        }, status=400)
    
    # Validate password strength
    if len(new_password) < 8:
        return Response({
            'success': False,
            'message': 'Password must be at least 8 characters long'
        }, status=400)
    
    # Get user from reset token
    from django.core.cache import cache
    user_id = cache.get(f'password_reset_{reset_token}')
    
    if not user_id:
        return Response({
            'success': False,
            'message': 'Invalid or expired reset token'
        }, status=400)
    
    try:
        user = User.objects.get(id=user_id, is_active=True)
        
        # Set new password
        user.set_password(new_password)
        user.save()
        
        # Delete reset token
        cache.delete(f'password_reset_{reset_token}')
        
        # Log password change
        from core.models import AuditLog
        AuditLog.objects.create(
            user=user,
            school=getattr(user, 'school', None),
            action='password_reset',
            model_name='User',
            object_id=user.id,
            changes={
                'reset_via': 'mobile_app',
                'timestamp': timezone.now().isoformat(),
            }
        )
        
        return Response({
            'success': True,
            'message': 'Password reset successfully'
        })
        
    except User.DoesNotExist:
        return Response({
            'success': False,
            'message': 'User not found'
        }, status=400)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def mobile_change_password(request):
    """Change password from mobile app."""
    try:
        data = json.loads(request.body)
    except json.JSONDecodeError:
        return Response({
            'success': False,
            'message': 'Invalid JSON data'
        }, status=400)
    
    current_password = data.get('current_password')
    new_password = data.get('new_password')
    
    if not current_password or not new_password:
        return Response({
            'success': False,
            'message': 'Current password and new password are required'
        }, status=400)
    
    # Verify current password
    if not request.user.check_password(current_password):
        return Response({
            'success': False,
            'message': 'Current password is incorrect'
        }, status=400)
    
    # Validate new password strength
    if len(new_password) < 8:
        return Response({
            'success': False,
            'message': 'New password must be at least 8 characters long'
        }, status=400)
    
    # Set new password
    request.user.set_password(new_password)
    request.user.save()
    
    # Log password change
    from core.models import AuditLog
    AuditLog.objects.create(
        user=request.user,
        school=getattr(request.user, 'school', None),
        action='password_change',
        model_name='User',
        object_id=request.user.id,
        changes={
            'changed_via': 'mobile_app',
            'timestamp': timezone.now().isoformat(),
        }
    )
    
    return Response({
        'success': True,
        'message': 'Password changed successfully'
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def mobile_auth_status(request):
    """Check mobile authentication status."""
    return Response({
        'success': True,
        'data': {
            'authenticated': True,
            'user_id': request.user.id,
            'username': request.user.username,
            'session_valid': True,
            'timestamp': timezone.now().isoformat(),
        }
    })