"""
URL patterns for mobile API endpoints.
"""

from django.urls import path, include
from .mobile_api import *

app_name = 'mobile_api'

urlpatterns = [
    # Dashboard and statistics
    path('dashboard/stats/', mobile_api.DashboardStatsAPI.as_view(), name='dashboard_stats'),
    
    # Student management
    path('students/', mobile_api.StudentListAPI.as_view(), name='student_list'),
    
    # Attendance management
    path('attendance/', mobile_api.AttendanceAPI.as_view(), name='attendance'),
    
    # Notifications
    path('notifications/', mobile_api.NotificationsAPI.as_view(), name='notifications'),
    
    # Data synchronization
    path('sync/', mobile_api.SyncAPI.as_view(), name='sync'),
    
    # User profile
    path('profile/', mobile_api.mobile_user_profile, name='profile'),
    
    # App configuration
    path('config/', mobile_api.mobile_app_config, name='config'),
    
    # Feedback
    path('feedback/', mobile_api.mobile_feedback, name='feedback'),
]