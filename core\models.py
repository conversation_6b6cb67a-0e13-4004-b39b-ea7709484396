from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import RegexValidator
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
import uuid

User = get_user_model()


class BaseModel(models.Model):
    """
    Abstract base model with common fields for multi-tenancy
    """
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )
    
    school = models.ForeignKey(
        'core.School',
        on_delete=models.CASCADE,
        verbose_name=_('School'),
        help_text=_('School this record belongs to')
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('Updated At')
    )
    
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='%(class)s_created',
        verbose_name=_('Created By')
    )
    
    updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='%(class)s_updated',
        verbose_name=_('Updated By')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )

    class Meta:
        abstract = True
        indexes = [
            models.Index(fields=['school', 'created_at']),
            models.Index(fields=['school', 'is_active']),
        ]

    def save(self, *args, **kwargs):
        # Auto-set school from user if not provided
        if not self.school_id and hasattr(self, '_current_user') and self._current_user:
            if hasattr(self._current_user, 'employee') and self._current_user.employee:
                self.school = self._current_user.employee.school
        
        # Set created_by and updated_by
        if hasattr(self, '_current_user') and self._current_user:
            if not self.pk:
                self.created_by = self._current_user
            self.updated_by = self._current_user
            
        super().save(*args, **kwargs)


class School(models.Model):
    """
    School information model - Root model for multi-tenancy
    """
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )
    
    name = models.CharField(
        max_length=200,
        verbose_name=_('School Name')
    )

    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('School Name (Arabic)')
    )

    code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('School Code'),
        help_text=_('Unique identifier for the school')
    )

    address = models.TextField(
        verbose_name=_('Address')
    )

    phone = models.CharField(
        max_length=20,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$')],
        verbose_name=_('Phone Number')
    )

    email = models.EmailField(
        verbose_name=_('Email')
    )

    website = models.URLField(
        blank=True,
        null=True,
        verbose_name=_('Website')
    )

    logo = models.ImageField(
        upload_to='school/logos/',
        blank=True,
        null=True,
        verbose_name=_('Logo')
    )

    principal_name = models.CharField(
        max_length=100,
        verbose_name=_('Principal Name')
    )

    established_date = models.DateField(
        verbose_name=_('Established Date')
    )

    license_number = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('License Number')
    )

    tax_number = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('Tax Number')
    )
    
    settings = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('School Settings'),
        help_text=_('JSON field for school-specific configuration')
    )
    
    timezone = models.CharField(
        max_length=50,
        default='UTC',
        verbose_name=_('Timezone')
    )
    
    currency = models.CharField(
        max_length=3,
        default='USD',
        verbose_name=_('Currency Code')
    )
    
    academic_year_start_month = models.IntegerField(
        default=9,
        verbose_name=_('Academic Year Start Month'),
        help_text=_('Month when academic year starts (1-12)')
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('Updated At')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )

    class Meta:
        verbose_name = _('School')
        verbose_name_plural = _('Schools')
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return self.name
    
    def clean(self):
        if self.academic_year_start_month < 1 or self.academic_year_start_month > 12:
            raise ValidationError(_('Academic year start month must be between 1 and 12'))


class AcademicYear(BaseModel):
    """
    Academic year model
    """
    name = models.CharField(
        max_length=50,
        verbose_name=_('Academic Year Name')
    )

    start_date = models.DateField(
        verbose_name=_('Start Date')
    )

    end_date = models.DateField(
        verbose_name=_('End Date')
    )

    is_current = models.BooleanField(
        default=False,
        verbose_name=_('Is Current Year')
    )
    
    registration_start_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Registration Start Date')
    )
    
    registration_end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Registration End Date')
    )

    class Meta:
        verbose_name = _('Academic Year')
        verbose_name_plural = _('Academic Years')
        ordering = ['-start_date']
        unique_together = ['school', 'name']
        indexes = [
            models.Index(fields=['school', 'is_current']),
            models.Index(fields=['school', 'start_date']),
        ]

    def __str__(self):
        return f"{self.school.name} - {self.name}"

    def clean(self):
        if self.start_date >= self.end_date:
            raise ValidationError(_('Start date must be before end date'))
        
        if self.registration_start_date and self.registration_end_date:
            if self.registration_start_date >= self.registration_end_date:
                raise ValidationError(_('Registration start date must be before registration end date'))

    def save(self, *args, **kwargs):
        if self.is_current:
            # Ensure only one academic year is current per school
            AcademicYear.objects.filter(
                school=self.school,
                is_current=True
            ).exclude(pk=self.pk).update(is_current=False)
        super().save(*args, **kwargs)


class Semester(BaseModel):
    """
    Semester model
    """
    academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        related_name='semesters',
        verbose_name=_('Academic Year')
    )

    name = models.CharField(
        max_length=50,
        verbose_name=_('Semester Name')
    )
    
    name_ar = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('Semester Name (Arabic)')
    )

    start_date = models.DateField(
        verbose_name=_('Start Date')
    )

    end_date = models.DateField(
        verbose_name=_('End Date')
    )

    is_current = models.BooleanField(
        default=False,
        verbose_name=_('Is Current Semester')
    )
    
    exam_start_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Exam Start Date')
    )
    
    exam_end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Exam End Date')
    )

    class Meta:
        verbose_name = _('Semester')
        verbose_name_plural = _('Semesters')
        ordering = ['academic_year', 'start_date']
        unique_together = ['academic_year', 'name']
        indexes = [
            models.Index(fields=['school', 'is_current']),
            models.Index(fields=['academic_year', 'start_date']),
        ]

    def __str__(self):
        return f"{self.academic_year.name} - {self.name}"
    
    def clean(self):
        if self.start_date >= self.end_date:
            raise ValidationError(_('Start date must be before end date'))
        
        if self.exam_start_date and self.exam_end_date:
            if self.exam_start_date >= self.exam_end_date:
                raise ValidationError(_('Exam start date must be before exam end date'))
    
    def save(self, *args, **kwargs):
        if self.is_current:
            # Ensure only one semester is current per academic year
            Semester.objects.filter(
                academic_year=self.academic_year,
                is_current=True
            ).exclude(pk=self.pk).update(is_current=False)
        super().save(*args, **kwargs)



class AuditLog(models.Model):
    """
    Audit log model for tracking changes
    """
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )
    
    school = models.ForeignKey(
        'core.School',
        on_delete=models.CASCADE,
        verbose_name=_('School')
    )
    
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('User')
    )
    
    action = models.CharField(
        max_length=50,
        verbose_name=_('Action'),
        choices=[
            ('CREATE', _('Create')),
            ('UPDATE', _('Update')),
            ('DELETE', _('Delete')),
            ('LOGIN', _('Login')),
            ('LOGOUT', _('Logout')),
        ]
    )
    
    model_name = models.CharField(
        max_length=100,
        verbose_name=_('Model Name')
    )
    
    object_id = models.CharField(
        max_length=255,
        verbose_name=_('Object ID')
    )
    
    object_repr = models.CharField(
        max_length=200,
        verbose_name=_('Object Representation')
    )
    
    changes = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Changes')
    )
    
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name=_('IP Address')
    )
    
    user_agent = models.TextField(
        blank=True,
        verbose_name=_('User Agent')
    )
    
    timestamp = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Timestamp')
    )

    class Meta:
        verbose_name = _('Audit Log')
        verbose_name_plural = _('Audit Logs')
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['school', 'timestamp']),
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['model_name', 'object_id']),
        ]

    def __str__(self):
        return f"{self.user} - {self.action} - {self.model_name} - {self.timestamp}"


# Import webhook models to make them available to Django
from .webhooks.models import (
    WebhookEndpoint, WebhookDelivery, WebhookEvent, 
    WebhookAnalytics, WebhookSecurity
)

# Import integration models to make them available to Django
from .integrations.models import (
    IntegrationProvider, Integration, IntegrationLog,
    IntegrationWebhook, IntegrationMapping, IntegrationSchedule,
    IntegrationAnalytics
)
