"""
Alerting System for School ERP Performance Monitoring
"""
import logging
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
from django.template.loader import render_to_string
from django.core.mail import send_mail
from django.contrib.auth.models import User

logger = logging.getLogger(__name__)


class AlertManager:
    """
    Centralized alert management system
    """
    
    SEVERITY_LEVELS = {
        'info': 1,
        'warning': 2,
        'error': 3,
        'critical': 4
    }
    
    def __init__(self):
        self.alert_channels = {
            'email': EmailAlertChannel(),
            'log': LogAlertChannel(),
            'cache': CacheAlertChannel()
        }
        self.alert_rules = []
        self.cooldown_periods = {
            'info': 300,      # 5 minutes
            'warning': 600,   # 10 minutes
            'error': 1800,    # 30 minutes
            'critical': 3600  # 1 hour
        }
    
    def add_alert_rule(self, rule: 'AlertRule'):
        """Add an alert rule"""
        self.alert_rules.append(rule)
    
    def check_alerts(self, metrics: Dict[str, Any]) -> List['Alert']:
        """Check all alert rules against current metrics"""
        triggered_alerts = []
        
        for rule in self.alert_rules:
            if rule.should_trigger(metrics):
                alert = Alert(
                    rule_name=rule.name,
                    severity=rule.severity,
                    message=rule.get_message(metrics),
                    metrics=metrics,
                    timestamp=timezone.now()
                )
                
                # Check cooldown
                if self._is_in_cooldown(alert):
                    continue
                
                triggered_alerts.append(alert)
                self._update_cooldown(alert)
        
        return triggered_alerts
    
    def send_alerts(self, alerts: List['Alert']):
        """Send alerts through configured channels"""
        for alert in alerts:
            for channel_name, channel in self.alert_channels.items():
                try:
                    channel.send_alert(alert)
                    logger.info(f"Alert sent via {channel_name}: {alert.message}")
                except Exception as e:
                    logger.error(f"Failed to send alert via {channel_name}: {e}")
    
    def _is_in_cooldown(self, alert: 'Alert') -> bool:
        """Check if alert is in cooldown period"""
        cache_key = f"alert_cooldown_{alert.rule_name}"
        last_sent = cache.get(cache_key)
        
        if last_sent is None:
            return False
        
        cooldown_period = self.cooldown_periods.get(alert.severity, 300)
        time_since_last = (timezone.now() - last_sent).total_seconds()
        
        return time_since_last < cooldown_period
    
    def _update_cooldown(self, alert: 'Alert'):
        """Update cooldown timestamp for alert"""
        cache_key = f"alert_cooldown_{alert.rule_name}"
        cooldown_period = self.cooldown_periods.get(alert.severity, 300)
        cache.set(cache_key, timezone.now(), timeout=cooldown_period)


class Alert:
    """
    Represents an alert
    """
    
    def __init__(self, rule_name: str, severity: str, message: str, 
                 metrics: Dict[str, Any], timestamp: datetime):
        self.rule_name = rule_name
        self.severity = severity
        self.message = message
        self.metrics = metrics
        self.timestamp = timestamp
        self.id = f"{rule_name}_{timestamp.strftime('%Y%m%d_%H%M%S')}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert alert to dictionary"""
        return {
            'id': self.id,
            'rule_name': self.rule_name,
            'severity': self.severity,
            'message': self.message,
            'timestamp': self.timestamp.isoformat(),
            'metrics': self.metrics
        }


class AlertRule:
    """
    Base class for alert rules
    """
    
    def __init__(self, name: str, severity: str, description: str = ""):
        self.name = name
        self.severity = severity
        self.description = description
    
    def should_trigger(self, metrics: Dict[str, Any]) -> bool:
        """Check if this rule should trigger an alert"""
        raise NotImplementedError
    
    def get_message(self, metrics: Dict[str, Any]) -> str:
        """Get alert message for current metrics"""
        raise NotImplementedError


class ThresholdAlertRule(AlertRule):
    """
    Alert rule based on metric thresholds
    """
    
    def __init__(self, name: str, severity: str, metric_path: str, 
                 threshold: float, operator: str = 'gt', description: str = ""):
        super().__init__(name, severity, description)
        self.metric_path = metric_path
        self.threshold = threshold
        self.operator = operator
    
    def should_trigger(self, metrics: Dict[str, Any]) -> bool:
        """Check if threshold is exceeded"""
        value = self._get_metric_value(metrics, self.metric_path)
        
        if value is None:
            return False
        
        if self.operator == 'gt':
            return value > self.threshold
        elif self.operator == 'lt':
            return value < self.threshold
        elif self.operator == 'eq':
            return value == self.threshold
        elif self.operator == 'gte':
            return value >= self.threshold
        elif self.operator == 'lte':
            return value <= self.threshold
        
        return False
    
    def get_message(self, metrics: Dict[str, Any]) -> str:
        """Get threshold alert message"""
        value = self._get_metric_value(metrics, self.metric_path)
        return f"{self.description or self.name}: {self.metric_path} is {value} (threshold: {self.threshold})"
    
    def _get_metric_value(self, metrics: Dict[str, Any], path: str) -> Optional[float]:
        """Get metric value from nested dictionary"""
        keys = path.split('.')
        current = metrics
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        
        try:
            return float(current)
        except (ValueError, TypeError):
            return None


class CompositeAlertRule(AlertRule):
    """
    Alert rule that combines multiple conditions
    """
    
    def __init__(self, name: str, severity: str, conditions: List[Dict[str, Any]], 
                 operator: str = 'and', description: str = ""):
        super().__init__(name, severity, description)
        self.conditions = conditions
        self.operator = operator  # 'and' or 'or'
    
    def should_trigger(self, metrics: Dict[str, Any]) -> bool:
        """Check if composite conditions are met"""
        results = []
        
        for condition in self.conditions:
            metric_path = condition['metric_path']
            threshold = condition['threshold']
            op = condition.get('operator', 'gt')
            
            value = self._get_metric_value(metrics, metric_path)
            if value is None:
                results.append(False)
                continue
            
            if op == 'gt':
                results.append(value > threshold)
            elif op == 'lt':
                results.append(value < threshold)
            elif op == 'gte':
                results.append(value >= threshold)
            elif op == 'lte':
                results.append(value <= threshold)
            else:
                results.append(False)
        
        if self.operator == 'and':
            return all(results)
        else:  # 'or'
            return any(results)
    
    def get_message(self, metrics: Dict[str, Any]) -> str:
        """Get composite alert message"""
        condition_messages = []
        
        for condition in self.conditions:
            metric_path = condition['metric_path']
            threshold = condition['threshold']
            value = self._get_metric_value(metrics, metric_path)
            condition_messages.append(f"{metric_path}: {value} (threshold: {threshold})")
        
        return f"{self.description or self.name}: {' AND ' if self.operator == 'and' else ' OR '}.join(condition_messages)"
    
    def _get_metric_value(self, metrics: Dict[str, Any], path: str) -> Optional[float]:
        """Get metric value from nested dictionary"""
        keys = path.split('.')
        current = metrics
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        
        try:
            return float(current)
        except (ValueError, TypeError):
            return None


class AlertChannel:
    """
    Base class for alert channels
    """
    
    def send_alert(self, alert: Alert):
        """Send alert through this channel"""
        raise NotImplementedError


class EmailAlertChannel(AlertChannel):
    """
    Email alert channel
    """
    
    def __init__(self):
        self.enabled = getattr(settings, 'EMAIL_ALERTS_ENABLED', False)
        self.recipients = getattr(settings, 'ALERT_EMAIL_RECIPIENTS', [])
        self.from_email = getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>')
    
    def send_alert(self, alert: Alert):
        """Send alert via email"""
        if not self.enabled or not self.recipients:
            return
        
        subject = f"[{alert.severity.upper()}] School ERP Alert: {alert.rule_name}"
        
        # Create HTML email content
        html_content = self._create_html_content(alert)
        text_content = self._create_text_content(alert)
        
        try:
            send_mail(
                subject=subject,
                message=text_content,
                from_email=self.from_email,
                recipient_list=self.recipients,
                html_message=html_content,
                fail_silently=False
            )
        except Exception as e:
            logger.error(f"Failed to send email alert: {e}")
            raise
    
    def _create_html_content(self, alert: Alert) -> str:
        """Create HTML email content"""
        severity_colors = {
            'info': '#17a2b8',
            'warning': '#ffc107',
            'error': '#dc3545',
            'critical': '#dc3545'
        }
        
        color = severity_colors.get(alert.severity, '#6c757d')
        
        return f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .alert-header {{ background-color: {color}; color: white; padding: 15px; border-radius: 5px; }}
                .alert-content {{ padding: 20px; border: 1px solid #ddd; border-radius: 5px; margin-top: 10px; }}
                .metric-table {{ width: 100%; border-collapse: collapse; margin-top: 15px; }}
                .metric-table th, .metric-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                .metric-table th {{ background-color: #f2f2f2; }}
                .footer {{ margin-top: 20px; font-size: 12px; color: #666; }}
            </style>
        </head>
        <body>
            <div class="alert-header">
                <h2>🚨 School ERP Alert: {alert.severity.upper()}</h2>
            </div>
            
            <div class="alert-content">
                <p><strong>Alert Rule:</strong> {alert.rule_name}</p>
                <p><strong>Message:</strong> {alert.message}</p>
                <p><strong>Time:</strong> {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}</p>
                <p><strong>Severity:</strong> <span style="color: {color}; font-weight: bold;">{alert.severity.upper()}</span></p>
                
                <h3>System Metrics:</h3>
                {self._format_metrics_html(alert.metrics)}
            </div>
            
            <div class="footer">
                <p><em>This is an automated alert from the School ERP monitoring system.</em></p>
                <p>Please investigate and take appropriate action if necessary.</p>
            </div>
        </body>
        </html>
        """
    
    def _create_text_content(self, alert: Alert) -> str:
        """Create plain text email content"""
        return f"""
🚨 SCHOOL ERP ALERT: {alert.severity.upper()}

Alert Rule: {alert.rule_name}
Message: {alert.message}
Time: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}
Severity: {alert.severity.upper()}

System Metrics:
{self._format_metrics_text(alert.metrics)}

This is an automated alert from the School ERP monitoring system.
Please investigate and take appropriate action if necessary.
        """
    
    def _format_metrics_html(self, metrics: Dict[str, Any]) -> str:
        """Format metrics as HTML table"""
        html = '<table class="metric-table">'
        html += '<tr><th>Metric</th><th>Value</th></tr>'
        
        def add_metrics(data, prefix=""):
            nonlocal html
            for key, value in data.items():
                full_key = f"{prefix}.{key}" if prefix else key
                if isinstance(value, dict):
                    add_metrics(value, full_key)
                else:
                    html += f'<tr><td>{full_key}</td><td>{value}</td></tr>'
        
        add_metrics(metrics)
        html += '</table>'
        return html
    
    def _format_metrics_text(self, metrics: Dict[str, Any], indent: int = 0) -> str:
        """Format metrics for plain text display"""
        result = []
        prefix = "  " * indent
        
        for key, value in metrics.items():
            if isinstance(value, dict):
                result.append(f"{prefix}{key}:")
                result.append(self._format_metrics_text(value, indent + 1))
            else:
                result.append(f"{prefix}{key}: {value}")
        
        return "\n".join(result)


class LogAlertChannel(AlertChannel):
    """
    Log alert channel
    """
    
    def send_alert(self, alert: Alert):
        """Send alert to logs"""
        log_level = {
            'info': logging.INFO,
            'warning': logging.WARNING,
            'error': logging.ERROR,
            'critical': logging.CRITICAL
        }.get(alert.severity, logging.WARNING)
        
        logger.log(log_level, f"ALERT [{alert.severity.upper()}] {alert.rule_name}: {alert.message}")


class CacheAlertChannel(AlertChannel):
    """
    Cache alert channel for dashboard display
    """
    
    def send_alert(self, alert: Alert):
        """Store alert in cache for dashboard"""
        cache_key = "system_alerts"
        alerts = cache.get(cache_key, [])
        
        # Add new alert
        alerts.append(alert.to_dict())
        
        # Keep only last 100 alerts
        if len(alerts) > 100:
            alerts = alerts[-100:]
        
        # Store for 24 hours
        cache.set(cache_key, alerts, timeout=86400)


class SlackAlertChannel(AlertChannel):
    """
    Slack alert channel (optional)
    """
    
    def __init__(self):
        self.webhook_url = getattr(settings, 'SLACK_WEBHOOK_URL', None)
        self.enabled = bool(self.webhook_url)
    
    def send_alert(self, alert: Alert):
        """Send alert to Slack"""
        if not self.enabled:
            return
        
        try:
            import requests
            
            color_map = {
                'info': '#36a64f',
                'warning': '#ff9500',
                'error': '#ff0000',
                'critical': '#ff0000'
            }
            
            payload = {
                "attachments": [
                    {
                        "color": color_map.get(alert.severity, '#36a64f'),
                        "title": f"School ERP Alert: {alert.rule_name}",
                        "text": alert.message,
                        "fields": [
                            {
                                "title": "Severity",
                                "value": alert.severity.upper(),
                                "short": True
                            },
                            {
                                "title": "Time",
                                "value": alert.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC'),
                                "short": True
                            }
                        ],
                        "footer": "School ERP Monitoring",
                        "ts": int(alert.timestamp.timestamp())
                    }
                ]
            }
            
            response = requests.post(self.webhook_url, json=payload, timeout=10)
            response.raise_for_status()
            
        except Exception as e:
            logger.error(f"Failed to send Slack alert: {e}")
            raise


class MetricCollector:
    """
    Collect metrics for monitoring
    """
    
    def __init__(self):
        self.collectors = []
    
    def add_collector(self, collector):
        """Add a metric collector"""
        self.collectors.append(collector)
    
    def collect_all_metrics(self) -> Dict[str, Any]:
        """Collect all metrics"""
        metrics = {}
        
        for collector in self.collectors:
            try:
                collector_metrics = collector.collect()
                metrics.update(collector_metrics)
            except Exception as e:
                logger.error(f"Error collecting metrics from {collector.__class__.__name__}: {e}")
        
        return metrics


class SystemMetricsCollector:
    """
    Collect system metrics
    """
    
    def collect(self) -> Dict[str, Any]:
        """Collect system metrics"""
        try:
            import psutil
            
            return {
                'system': {
                    'cpu_percent': psutil.cpu_percent(interval=1),
                    'memory_percent': psutil.virtual_memory().percent,
                    'disk_percent': psutil.disk_usage('/').percent,
                    'load_average': psutil.getloadavg()[0] if hasattr(psutil, 'getloadavg') else 0,
                    'boot_time': psutil.boot_time(),
                    'process_count': len(psutil.pids())
                }
            }
        except ImportError:
            logger.warning("psutil not available for system metrics")
            return {}
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
            return {}


class DatabaseMetricsCollector:
    """
    Collect database metrics
    """
    
    def collect(self) -> Dict[str, Any]:
        """Collect database metrics"""
        from django.db import connection
        
        try:
            with connection.cursor() as cursor:
                # Get connection count
                cursor.execute("""
                    SELECT count(*) as total_connections,
                           count(*) FILTER (WHERE state = 'active') as active_connections,
                           count(*) FILTER (WHERE state = 'idle') as idle_connections
                    FROM pg_stat_activity
                    WHERE datname = current_database()
                """)
                
                result = cursor.fetchone()
                
                # Get database size
                cursor.execute("SELECT pg_size_pretty(pg_database_size(current_database()))")
                db_size = cursor.fetchone()[0]
                
                return {
                    'database': {
                        'total_connections': result[0],
                        'active_connections': result[1],
                        'idle_connections': result[2],
                        'connection_usage_percent': (result[1] / max(result[0], 1)) * 100,
                        'database_size': db_size
                    }
                }
        except Exception as e:
            logger.error(f"Error collecting database metrics: {e}")
            return {}


class ApplicationMetricsCollector:
    """
    Collect application-specific metrics
    """
    
    def collect(self) -> Dict[str, Any]:
        """Collect application metrics"""
        try:
            from django.contrib.auth.models import User
            from students.models import Student
            from academics.models import Class
            
            # Get active users today
            today = timezone.now().date()
            active_users_today = User.objects.filter(
                last_login__date=today
            ).count()
            
            return {
                'application': {
                    'total_users': User.objects.count(),
                    'active_users_today': active_users_today,
                    'total_students': Student.objects.filter(status='active').count(),
                    'total_classes': Class.objects.filter(is_active=True).count(),
                    'timestamp': timezone.now().isoformat()
                }
            }
        except Exception as e:
            logger.error(f"Error collecting application metrics: {e}")
            return {}


# Global alert manager instance
alert_manager = AlertManager()

# Add default alert rules
alert_manager.add_alert_rule(
    ThresholdAlertRule(
        name="high_cpu_usage",
        severity="warning",
        metric_path="system.cpu_percent",
        threshold=80,
        description="High CPU usage detected"
    )
)

alert_manager.add_alert_rule(
    ThresholdAlertRule(
        name="critical_cpu_usage",
        severity="critical",
        metric_path="system.cpu_percent",
        threshold=95,
        description="Critical CPU usage detected"
    )
)

alert_manager.add_alert_rule(
    ThresholdAlertRule(
        name="high_memory_usage",
        severity="warning",
        metric_path="system.memory_percent",
        threshold=85,
        description="High memory usage detected"
    )
)

alert_manager.add_alert_rule(
    ThresholdAlertRule(
        name="critical_memory_usage",
        severity="critical",
        metric_path="system.memory_percent",
        threshold=95,
        description="Critical memory usage detected"
    )
)

alert_manager.add_alert_rule(
    ThresholdAlertRule(
        name="high_disk_usage",
        severity="error",
        metric_path="system.disk_percent",
        threshold=90,
        description="High disk usage detected"
    )
)

alert_manager.add_alert_rule(
    ThresholdAlertRule(
        name="high_db_connections",
        severity="warning",
        metric_path="database.connection_usage_percent",
        threshold=80,
        description="High database connection usage"
    )
)

alert_manager.add_alert_rule(
    CompositeAlertRule(
        name="system_overload",
        severity="critical",
        conditions=[
            {'metric_path': 'system.cpu_percent', 'threshold': 90, 'operator': 'gt'},
            {'metric_path': 'system.memory_percent', 'threshold': 90, 'operator': 'gt'}
        ],
        operator='and',
        description="System overload: High CPU and memory usage"
    )
)

# Global metric collector
metric_collector = MetricCollector()
metric_collector.add_collector(SystemMetricsCollector())
metric_collector.add_collector(DatabaseMetricsCollector())
metric_collector.add_collector(ApplicationMetricsCollector())


def check_and_send_alerts():
    """Check metrics and send alerts if needed"""
    try:
        metrics = metric_collector.collect_all_metrics()
        alerts = alert_manager.check_alerts(metrics)
        
        if alerts:
            alert_manager.send_alerts(alerts)
            logger.info(f"Sent {len(alerts)} alerts")
        
        return len(alerts)
    except Exception as e:
        logger.error(f"Error in alert checking: {e}")
        return 0


def get_recent_alerts(hours: int = 24) -> List[Dict[str, Any]]:
    """Get recent alerts from cache"""
    alerts = cache.get("system_alerts", [])
    cutoff_time = timezone.now() - timedelta(hours=hours)
    
    recent_alerts = []
    for alert in alerts:
        try:
            alert_time = datetime.fromisoformat(alert['timestamp'].replace('Z', '+00:00'))
            if alert_time >= cutoff_time:
                recent_alerts.append(alert)
        except (KeyError, ValueError):
            continue
    
    return recent_alerts


def clear_alerts():
    """Clear all alerts from cache"""
    cache.delete("system_alerts")
    logger.info("All alerts cleared from cache")


def add_custom_alert_rule(name: str, severity: str, metric_path: str, 
                         threshold: float, operator: str = 'gt', description: str = ""):
    """Add a custom alert rule"""
    rule = ThresholdAlertRule(
        name=name,
        severity=severity,
        metric_path=metric_path,
        threshold=threshold,
        operator=operator,
        description=description
    )
    alert_manager.add_alert_rule(rule)
    logger.info(f"Added custom alert rule: {name}")


# Export main components
__all__ = [
    'AlertManager',
    'Alert',
    'AlertRule',
    'ThresholdAlertRule',
    'CompositeAlertRule',
    'AlertChannel',
    'EmailAlertChannel',
    'LogAlertChannel',
    'CacheAlertChannel',
    'SlackAlertChannel',
    'MetricCollector',
    'SystemMetricsCollector',
    'DatabaseMetricsCollector',
    'ApplicationMetricsCollector',
    'alert_manager',
    'metric_collector',
    'check_and_send_alerts',
    'get_recent_alerts',
    'clear_alerts',
    'add_custom_alert_rule'
]