"""
Monitoring Dashboards for School ERP System
"""
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from django.shortcuts import render
from django.http import JsonResponse
from django.views import View
from django.contrib.admin.views.decorators import staff_member_required
from django.utils.decorators import method_decorator
from django.utils import timezone
from django.core.cache import cache
from django.db import connection

from .alerting import metric_collector, alert_manager


@method_decorator(staff_member_required, name='dispatch')
class MonitoringDashboardView(View):
    """
    Main monitoring dashboard view
    """
    
    def get(self, request):
        """Render monitoring dashboard"""
        return render(request, 'monitoring/dashboard.html', {
            'title': 'System Monitoring Dashboard'
        })


@method_decorator(staff_member_required, name='dispatch')
class MetricsAPIView(View):
    """
    API endpoint for real-time metrics
    """
    
    def get(self, request):
        """Get current system metrics"""
        try:
            # Collect current metrics
            metrics = metric_collector.collect_all_metrics()
            
            # Get recent alerts
            alerts = cache.get("system_alerts", [])
            recent_alerts = [
                alert for alert in alerts
                if self._is_recent_alert(alert, hours=24)
            ]
            
            # Get historical data
            historical_data = self._get_historical_metrics()
            
            return JsonResponse({
                'status': 'success',
                'timestamp': timezone.now().isoformat(),
                'metrics': metrics,
                'alerts': recent_alerts,
                'historical': historical_data
            })
            
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': str(e),
                'timestamp': timezone.now().isoformat()
            }, status=500)
    
    def _is_recent_alert(self, alert: Dict[str, Any], hours: int = 24) -> bool:
        """Check if alert is recent"""
        try:
            alert_time = datetime.fromisoformat(alert['timestamp'].replace('Z', '+00:00'))
            cutoff_time = timezone.now() - timedelta(hours=hours)
            return alert_time >= cutoff_time
        except (KeyError, ValueError):
            return False
    
    def _get_historical_metrics(self) -> Dict[str, List[Dict[str, Any]]]:
        """Get historical metrics data"""
        # Get cached historical data
        historical_data = cache.get("historical_metrics", {})
        
        # If no historical data, create sample data structure
        if not historical_data:
            historical_data = {
                'cpu_usage': [],
                'memory_usage': [],
                'disk_usage': [],
                'response_times': [],
                'active_users': []
            }
        
        return historical_data


@method_decorator(staff_member_required, name='dispatch')
class AlertsAPIView(View):
    """
    API endpoint for alerts management
    """
    
    def get(self, request):
        """Get alerts with filtering"""
        try:
            # Get query parameters
            severity = request.GET.get('severity')
            hours = int(request.GET.get('hours', 24))
            limit = int(request.GET.get('limit', 50))
            
            # Get alerts from cache
            all_alerts = cache.get("system_alerts", [])
            
            # Filter alerts
            filtered_alerts = []
            cutoff_time = timezone.now() - timedelta(hours=hours)
            
            for alert in all_alerts:
                # Check time filter
                try:
                    alert_time = datetime.fromisoformat(alert['timestamp'].replace('Z', '+00:00'))
                    if alert_time < cutoff_time:
                        continue
                except (KeyError, ValueError):
                    continue
                
                # Check severity filter
                if severity and alert.get('severity') != severity:
                    continue
                
                filtered_alerts.append(alert)
            
            # Sort by timestamp (newest first)
            filtered_alerts.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
            
            # Apply limit
            filtered_alerts = filtered_alerts[:limit]
            
            return JsonResponse({
                'status': 'success',
                'alerts': filtered_alerts,
                'total_count': len(filtered_alerts),
                'timestamp': timezone.now().isoformat()
            })
            
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': str(e),
                'timestamp': timezone.now().isoformat()
            }, status=500)
    
    def post(self, request):
        """Acknowledge or dismiss alerts"""
        try:
            data = json.loads(request.body)
            action = data.get('action')
            alert_ids = data.get('alert_ids', [])
            
            if action == 'acknowledge':
                self._acknowledge_alerts(alert_ids, request.user)
                return JsonResponse({
                    'status': 'success',
                    'message': f'Acknowledged {len(alert_ids)} alerts'
                })
            
            elif action == 'dismiss':
                self._dismiss_alerts(alert_ids)
                return JsonResponse({
                    'status': 'success',
                    'message': f'Dismissed {len(alert_ids)} alerts'
                })
            
            else:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Invalid action'
                }, status=400)
                
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            }, status=500)
    
    def _acknowledge_alerts(self, alert_ids: List[str], user):
        """Acknowledge alerts"""
        alerts = cache.get("system_alerts", [])
        
        for alert in alerts:
            if alert.get('id') in alert_ids:
                alert['acknowledged'] = True
                alert['acknowledged_by'] = user.username
                alert['acknowledged_at'] = timezone.now().isoformat()
        
        cache.set("system_alerts", alerts, timeout=86400)
    
    def _dismiss_alerts(self, alert_ids: List[str]):
        """Dismiss alerts"""
        alerts = cache.get("system_alerts", [])
        alerts = [alert for alert in alerts if alert.get('id') not in alert_ids]
        cache.set("system_alerts", alerts, timeout=86400)


@method_decorator(staff_member_required, name='dispatch')
class PerformanceReportView(View):
    """
    Performance report view
    """
    
    def get(self, request):
        """Get performance report"""
        try:
            # Get time range
            days = int(request.GET.get('days', 7))
            end_date = timezone.now()
            start_date = end_date - timedelta(days=days)
            
            # Generate report
            report = self._generate_performance_report(start_date, end_date)
            
            return JsonResponse({
                'status': 'success',
                'report': report,
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': days
                }
            })
            
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            }, status=500)
    
    def _generate_performance_report(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Generate performance report for date range"""
        
        # Get current metrics for comparison
        current_metrics = metric_collector.collect_all_metrics()
        
        # Get alerts in period
        all_alerts = cache.get("system_alerts", [])
        period_alerts = []
        
        for alert in all_alerts:
            try:
                alert_time = datetime.fromisoformat(alert['timestamp'].replace('Z', '+00:00'))
                if start_date <= alert_time <= end_date:
                    period_alerts.append(alert)
            except (KeyError, ValueError):
                continue
        
        # Calculate alert statistics
        alert_stats = self._calculate_alert_stats(period_alerts)
        
        # Get database performance stats
        db_stats = self._get_database_performance_stats()
        
        # Get system uptime and availability
        availability_stats = self._calculate_availability_stats(period_alerts, start_date, end_date)
        
        return {
            'summary': {
                'total_alerts': len(period_alerts),
                'critical_alerts': len([a for a in period_alerts if a.get('severity') == 'critical']),
                'warning_alerts': len([a for a in period_alerts if a.get('severity') == 'warning']),
                'availability_percentage': availability_stats['availability_percentage'],
                'average_response_time': availability_stats.get('avg_response_time', 0)
            },
            'current_metrics': current_metrics,
            'alert_statistics': alert_stats,
            'database_performance': db_stats,
            'availability': availability_stats,
            'recommendations': self._generate_recommendations(current_metrics, period_alerts)
        }
    
    def _calculate_alert_stats(self, alerts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate alert statistics"""
        if not alerts:
            return {'total': 0, 'by_severity': {}, 'by_rule': {}}
        
        by_severity = {}
        by_rule = {}
        
        for alert in alerts:
            severity = alert.get('severity', 'unknown')
            rule_name = alert.get('rule_name', 'unknown')
            
            by_severity[severity] = by_severity.get(severity, 0) + 1
            by_rule[rule_name] = by_rule.get(rule_name, 0) + 1
        
        return {
            'total': len(alerts),
            'by_severity': by_severity,
            'by_rule': by_rule
        }
    
    def _get_database_performance_stats(self) -> Dict[str, Any]:
        """Get database performance statistics"""
        try:
            with connection.cursor() as cursor:
                # Get database size
                cursor.execute("SELECT pg_size_pretty(pg_database_size(current_database()))")
                db_size = cursor.fetchone()[0]
                
                # Get table count
                cursor.execute("""
                    SELECT count(*) FROM information_schema.tables 
                    WHERE table_schema = 'public'
                """)
                table_count = cursor.fetchone()[0]
                
                # Get connection stats
                cursor.execute("""
                    SELECT count(*) as total,
                           count(*) FILTER (WHERE state = 'active') as active
                    FROM pg_stat_activity
                    WHERE datname = current_database()
                """)
                conn_stats = cursor.fetchone()
                
                return {
                    'database_size': db_size,
                    'table_count': table_count,
                    'total_connections': conn_stats[0],
                    'active_connections': conn_stats[1]
                }
                
        except Exception as e:
            return {'error': str(e)}
    
    def _calculate_availability_stats(self, alerts: List[Dict[str, Any]], 
                                    start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Calculate system availability statistics"""
        total_minutes = (end_date - start_date).total_seconds() / 60
        
        # Calculate downtime from critical alerts
        downtime_minutes = 0
        critical_alerts = [a for a in alerts if a.get('severity') == 'critical']
        
        # Assume each critical alert represents 5 minutes of degraded service
        downtime_minutes = len(critical_alerts) * 5
        
        uptime_minutes = max(0, total_minutes - downtime_minutes)
        availability_percentage = (uptime_minutes / total_minutes) * 100 if total_minutes > 0 else 100
        
        return {
            'total_minutes': total_minutes,
            'uptime_minutes': uptime_minutes,
            'downtime_minutes': downtime_minutes,
            'availability_percentage': round(availability_percentage, 2),
            'critical_incidents': len(critical_alerts)
        }
    
    def _generate_recommendations(self, metrics: Dict[str, Any], 
                                alerts: List[Dict[str, Any]]) -> List[str]:
        """Generate performance recommendations"""
        recommendations = []
        
        # Check system metrics
        system_metrics = metrics.get('system', {})
        
        if system_metrics.get('cpu_percent', 0) > 70:
            recommendations.append("Consider upgrading CPU or optimizing high-CPU processes")
        
        if system_metrics.get('memory_percent', 0) > 80:
            recommendations.append("Consider adding more RAM or optimizing memory usage")
        
        if system_metrics.get('disk_percent', 0) > 85:
            recommendations.append("Disk space is running low - consider cleanup or expansion")
        
        # Check database metrics
        db_metrics = metrics.get('database', {})
        if db_metrics.get('connection_usage_percent', 0) > 70:
            recommendations.append("Database connection usage is high - consider connection pooling")
        
        # Check alert patterns
        critical_alerts = [a for a in alerts if a.get('severity') == 'critical']
        if len(critical_alerts) > 5:
            recommendations.append("High number of critical alerts - review system stability")
        
        if not recommendations:
            recommendations.append("System performance is within normal parameters")
        
        return recommendations


class DashboardDataCollector:
    """
    Collect and format data for dashboard display
    """
    
    @staticmethod
    def get_dashboard_data() -> Dict[str, Any]:
        """Get all dashboard data"""
        try:
            # Get current metrics
            metrics = metric_collector.collect_all_metrics()
            
            # Get recent alerts
            alerts = cache.get("system_alerts", [])
            recent_alerts = [
                alert for alert in alerts[-10:]  # Last 10 alerts
            ]
            
            # Format for dashboard
            return {
                'system_health': DashboardDataCollector._format_system_health(metrics),
                'recent_alerts': recent_alerts,
                'quick_stats': DashboardDataCollector._format_quick_stats(metrics),
                'status_indicators': DashboardDataCollector._get_status_indicators(metrics, alerts)
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'system_health': {},
                'recent_alerts': [],
                'quick_stats': {},
                'status_indicators': {}
            }
    
    @staticmethod
    def _format_system_health(metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Format system health metrics"""
        system = metrics.get('system', {})
        database = metrics.get('database', {})
        
        return {
            'cpu': {
                'value': system.get('cpu_percent', 0),
                'status': DashboardDataCollector._get_status(system.get('cpu_percent', 0), 70, 90),
                'unit': '%'
            },
            'memory': {
                'value': system.get('memory_percent', 0),
                'status': DashboardDataCollector._get_status(system.get('memory_percent', 0), 80, 95),
                'unit': '%'
            },
            'disk': {
                'value': system.get('disk_percent', 0),
                'status': DashboardDataCollector._get_status(system.get('disk_percent', 0), 85, 95),
                'unit': '%'
            },
            'database_connections': {
                'value': database.get('active_connections', 0),
                'total': database.get('total_connections', 0),
                'status': DashboardDataCollector._get_status(
                    database.get('connection_usage_percent', 0), 70, 90
                ),
                'unit': 'connections'
            }
        }
    
    @staticmethod
    def _format_quick_stats(metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Format quick statistics"""
        app_metrics = metrics.get('application', {})
        
        return {
            'total_users': app_metrics.get('total_users', 0),
            'active_users_today': app_metrics.get('active_users_today', 0),
            'total_students': app_metrics.get('total_students', 0),
            'total_classes': app_metrics.get('total_classes', 0)
        }
    
    @staticmethod
    def _get_status_indicators(metrics: Dict[str, Any], alerts: List[Dict[str, Any]]) -> Dict[str, str]:
        """Get overall status indicators"""
        # Count recent critical alerts
        recent_critical = len([
            a for a in alerts[-5:]  # Last 5 alerts
            if a.get('severity') == 'critical'
        ])
        
        system = metrics.get('system', {})
        
        # Determine overall status
        if recent_critical > 0 or system.get('cpu_percent', 0) > 95:
            overall_status = 'critical'
        elif (system.get('cpu_percent', 0) > 80 or 
              system.get('memory_percent', 0) > 85 or
              system.get('disk_percent', 0) > 90):
            overall_status = 'warning'
        else:
            overall_status = 'healthy'
        
        return {
            'overall': overall_status,
            'database': 'healthy' if metrics.get('database', {}).get('active_connections', 0) < 50 else 'warning',
            'application': 'healthy'  # Could be enhanced with more app-specific checks
        }
    
    @staticmethod
    def _get_status(value: float, warning_threshold: float, critical_threshold: float) -> str:
        """Get status based on thresholds"""
        if value >= critical_threshold:
            return 'critical'
        elif value >= warning_threshold:
            return 'warning'
        else:
            return 'healthy'


# Export main components
__all__ = [
    'MonitoringDashboardView',
    'MetricsAPIView',
    'AlertsAPIView',
    'PerformanceReportView',
    'DashboardDataCollector'
]