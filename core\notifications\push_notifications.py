"""
Push notification system for School ERP mobile applications.
Supports Web Push API, Firebase Cloud Messaging (FCM), and Apple Push Notification Service (APNS).
"""

from django.conf import settings
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.db import models
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
import json
import requests
from datetime import datetime, timedelta
import logging
from typing import List, Dict, Optional, Union
from dataclasses import dataclass
from enum import Enum

User = get_user_model()
logger = logging.getLogger(__name__)


class NotificationPriority(models.TextChoices):
    """Priority levels for push notifications."""
    LOW = 'low', _('Low')
    NORMAL = 'normal', _('Normal')
    HIGH = 'high', _('High')
    URGENT = 'urgent', _('Urgent')


class NotificationCategory(models.TextChoices):
    """Categories for push notifications."""
    GENERAL = 'general', _('General')
    ATTENDANCE = 'attendance', _('Attendance')
    GRADES = 'grades', _('Grades')
    ANNOUNCEMENTS = 'announcements', _('Announcements')
    EMERGENCY = 'emergency', _('Emergency')
    PAYMENT = 'payment', _('Payment')
    EVENTS = 'events', _('Events')
    MESSAGES = 'messages', _('Messages')


class PushSubscription(models.Model):
    """Model to store push notification subscriptions."""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='push_subscriptions')
    endpoint = models.URLField(max_length=500)
    p256dh_key = models.CharField(max_length=255)
    auth_key = models.CharField(max_length=255)
    device_type = models.CharField(max_length=20, choices=[
        ('web', 'Web Browser'),
        ('android', 'Android'),
        ('ios', 'iOS'),
    ])
    device_info = models.JSONField(default=dict, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    last_used = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['user', 'endpoint']
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['device_type', 'is_active']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.device_type}"


class PushNotification(models.Model):
    """Model to store push notification records."""
    title = models.CharField(max_length=255)
    body = models.TextField()
    icon = models.URLField(blank=True, null=True)
    image = models.URLField(blank=True, null=True)
    badge = models.URLField(blank=True, null=True)
    category = models.CharField(
        max_length=20, 
        choices=NotificationCategory.choices,
        default=NotificationCategory.GENERAL
    )
    priority = models.CharField(
        max_length=10,
        choices=NotificationPriority.choices,
        default=NotificationPriority.NORMAL
    )
    data = models.JSONField(default=dict, blank=True)
    action_url = models.URLField(blank=True, null=True)
    
    # Targeting
    recipients = models.ManyToManyField(User, through='PushNotificationRecipient')
    target_all_users = models.BooleanField(default=False)
    target_roles = models.JSONField(default=list, blank=True)
    target_schools = models.JSONField(default=list, blank=True)
    
    # Scheduling
    scheduled_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    
    # Status
    is_sent = models.BooleanField(default=False)
    sent_at = models.DateTimeField(null=True, blank=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_notifications')
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['category', 'priority']),
            models.Index(fields=['scheduled_at', 'is_sent']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.title} ({self.category})"
    
    def clean(self):
        if self.scheduled_at and self.scheduled_at <= timezone.now():
            raise ValidationError(_('Scheduled time must be in the future'))
        
        if self.expires_at and self.expires_at <= timezone.now():
            raise ValidationError(_('Expiration time must be in the future'))
        
        if self.scheduled_at and self.expires_at and self.scheduled_at >= self.expires_at:
            raise ValidationError(_('Scheduled time must be before expiration time'))


class PushNotificationRecipient(models.Model):
    """Through model for notification recipients with delivery status."""
    notification = models.ForeignKey(PushNotification, on_delete=models.CASCADE)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    subscription = models.ForeignKey(PushSubscription, on_delete=models.CASCADE, null=True, blank=True)
    
    # Delivery status
    is_delivered = models.BooleanField(default=False)
    delivered_at = models.DateTimeField(null=True, blank=True)
    delivery_attempts = models.PositiveIntegerField(default=0)
    last_attempt_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(blank=True)
    
    # User interaction
    is_clicked = models.BooleanField(default=False)
    clicked_at = models.DateTimeField(null=True, blank=True)
    is_dismissed = models.BooleanField(default=False)
    dismissed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        unique_together = ['notification', 'user', 'subscription']
        indexes = [
            models.Index(fields=['notification', 'is_delivered']),
            models.Index(fields=['user', 'delivered_at']),
        ]


@dataclass
class PushMessage:
    """Data class for push notification messages."""
    title: str
    body: str
    icon: Optional[str] = None
    image: Optional[str] = None
    badge: Optional[str] = None
    data: Optional[Dict] = None
    actions: Optional[List[Dict]] = None
    tag: Optional[str] = None
    renotify: bool = False
    require_interaction: bool = False
    silent: bool = False
    vibrate: Optional[List[int]] = None
    timestamp: Optional[int] = None


class PushNotificationService:
    """Service class for managing push notifications."""
    
    def __init__(self):
        self.vapid_private_key = getattr(settings, 'VAPID_PRIVATE_KEY', None)
        self.vapid_public_key = getattr(settings, 'VAPID_PUBLIC_KEY', None)
        self.vapid_claims = getattr(settings, 'VAPID_CLAIMS', {})
        self.fcm_server_key = getattr(settings, 'FCM_SERVER_KEY', None)
        self.apns_key_id = getattr(settings, 'APNS_KEY_ID', None)
        self.apns_team_id = getattr(settings, 'APNS_TEAM_ID', None)
        self.apns_private_key = getattr(settings, 'APNS_PRIVATE_KEY', None)
    
    def create_notification(
        self,
        title: str,
        body: str,
        recipients: Optional[List[User]] = None,
        category: str = NotificationCategory.GENERAL,
        priority: str = NotificationPriority.NORMAL,
        data: Optional[Dict] = None,
        action_url: Optional[str] = None,
        icon: Optional[str] = None,
        image: Optional[str] = None,
        scheduled_at: Optional[datetime] = None,
        expires_at: Optional[datetime] = None,
        created_by: Optional[User] = None,
        target_all_users: bool = False,
        target_roles: Optional[List[str]] = None,
        target_schools: Optional[List[int]] = None
    ) -> PushNotification:
        """Create a new push notification."""
        
        notification = PushNotification.objects.create(
            title=title,
            body=body,
            category=category,
            priority=priority,
            data=data or {},
            action_url=action_url,
            icon=icon,
            image=image,
            scheduled_at=scheduled_at,
            expires_at=expires_at,
            created_by=created_by,
            target_all_users=target_all_users,
            target_roles=target_roles or [],
            target_schools=target_schools or []
        )
        
        # Add specific recipients
        if recipients:
            for user in recipients:
                notification.recipients.add(user)
        
        # If scheduled, don't send immediately
        if not scheduled_at:
            self.send_notification(notification)
        
        return notification
    
    def send_notification(self, notification: PushNotification) -> Dict[str, int]:
        """Send a push notification to all targeted users."""
        
        if notification.is_sent:
            logger.warning(f"Notification {notification.id} already sent")
            return {'success': 0, 'failed': 0, 'skipped': 1}
        
        # Check if notification has expired
        if notification.expires_at and notification.expires_at <= timezone.now():
            logger.warning(f"Notification {notification.id} has expired")
            return {'success': 0, 'failed': 0, 'expired': 1}
        
        # Get target users
        target_users = self._get_target_users(notification)
        
        success_count = 0
        failed_count = 0
        
        for user in target_users:
            # Get active subscriptions for user
            subscriptions = PushSubscription.objects.filter(
                user=user,
                is_active=True
            )
            
            for subscription in subscriptions:
                try:
                    # Create recipient record
                    recipient, created = PushNotificationRecipient.objects.get_or_create(
                        notification=notification,
                        user=user,
                        subscription=subscription
                    )
                    
                    if recipient.is_delivered:
                        continue  # Already delivered to this subscription
                    
                    # Send to specific device type
                    if subscription.device_type == 'web':
                        success = self._send_web_push(notification, subscription)
                    elif subscription.device_type == 'android':
                        success = self._send_fcm_push(notification, subscription)
                    elif subscription.device_type == 'ios':
                        success = self._send_apns_push(notification, subscription)
                    else:
                        success = False
                    
                    # Update recipient status
                    recipient.delivery_attempts += 1
                    recipient.last_attempt_at = timezone.now()
                    
                    if success:
                        recipient.is_delivered = True
                        recipient.delivered_at = timezone.now()
                        success_count += 1
                    else:
                        failed_count += 1
                    
                    recipient.save()
                    
                except Exception as e:
                    logger.error(f"Failed to send notification to {user.username}: {str(e)}")
                    failed_count += 1
        
        # Mark notification as sent
        notification.is_sent = True
        notification.sent_at = timezone.now()
        notification.save()
        
        logger.info(f"Notification {notification.id} sent: {success_count} success, {failed_count} failed")
        
        return {
            'success': success_count,
            'failed': failed_count,
            'total_users': len(target_users)
        }
    
    def _get_target_users(self, notification: PushNotification) -> List[User]:
        """Get list of users to send notification to."""
        
        if notification.target_all_users:
            return list(User.objects.filter(is_active=True))
        
        users = set()
        
        # Add specific recipients
        users.update(notification.recipients.all())
        
        # Add users by role
        if notification.target_roles:
            role_users = User.objects.filter(
                groups__name__in=notification.target_roles,
                is_active=True
            )
            users.update(role_users)
        
        # Add users by school
        if notification.target_schools:
            school_users = User.objects.filter(
                school_id__in=notification.target_schools,
                is_active=True
            )
            users.update(school_users)
        
        return list(users)
    
    def _send_web_push(self, notification: PushNotification, subscription: PushSubscription) -> bool:
        """Send push notification via Web Push API."""
        
        try:
            from pywebpush import webpush, WebPushException
            
            # Prepare message
            message = PushMessage(
                title=notification.title,
                body=notification.body,
                icon=notification.icon,
                image=notification.image,
                badge=notification.badge,
                data=notification.data,
                tag=f"notification_{notification.id}",
                require_interaction=notification.priority in [NotificationPriority.HIGH, NotificationPriority.URGENT],
                vibrate=[100, 50, 100] if notification.priority == NotificationPriority.URGENT else None
            )
            
            # Send push notification
            webpush(
                subscription_info={
                    "endpoint": subscription.endpoint,
                    "keys": {
                        "p256dh": subscription.p256dh_key,
                        "auth": subscription.auth_key
                    }
                },
                data=json.dumps(message.__dict__),
                vapid_private_key=self.vapid_private_key,
                vapid_claims=self.vapid_claims
            )
            
            return True
            
        except WebPushException as e:
            logger.error(f"Web push failed: {str(e)}")
            if e.response and e.response.status_code in [410, 413, 429]:
                # Subscription is invalid, deactivate it
                subscription.is_active = False
                subscription.save()
            return False
        except Exception as e:
            logger.error(f"Web push error: {str(e)}")
            return False
    
    def _send_fcm_push(self, notification: PushNotification, subscription: PushSubscription) -> bool:
        """Send push notification via Firebase Cloud Messaging."""
        
        if not self.fcm_server_key:
            logger.error("FCM server key not configured")
            return False
        
        try:
            headers = {
                'Authorization': f'key={self.fcm_server_key}',
                'Content-Type': 'application/json',
            }
            
            payload = {
                'to': subscription.endpoint.split('/')[-1],  # FCM token
                'notification': {
                    'title': notification.title,
                    'body': notification.body,
                    'icon': notification.icon,
                    'image': notification.image,
                    'click_action': notification.action_url,
                },
                'data': notification.data,
                'priority': 'high' if notification.priority in [NotificationPriority.HIGH, NotificationPriority.URGENT] else 'normal',
                'android': {
                    'notification': {
                        'channel_id': f'school_erp_{notification.category}',
                        'priority': 'high' if notification.priority == NotificationPriority.URGENT else 'default',
                        'vibrate_timings': ['0.1s', '0.05s', '0.1s'] if notification.priority == NotificationPriority.URGENT else None,
                    }
                }
            }
            
            response = requests.post(
                'https://fcm.googleapis.com/fcm/send',
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get('success', 0) > 0
            else:
                logger.error(f"FCM push failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"FCM push error: {str(e)}")
            return False
    
    def _send_apns_push(self, notification: PushNotification, subscription: PushSubscription) -> bool:
        """Send push notification via Apple Push Notification Service."""
        
        if not all([self.apns_key_id, self.apns_team_id, self.apns_private_key]):
            logger.error("APNS credentials not configured")
            return False
        
        try:
            import jwt
            from datetime import datetime
            
            # Generate JWT token for APNS
            headers = {
                'alg': 'ES256',
                'kid': self.apns_key_id,
            }
            
            payload = {
                'iss': self.apns_team_id,
                'iat': datetime.utcnow().timestamp()
            }
            
            token = jwt.encode(payload, self.apns_private_key, algorithm='ES256', headers=headers)
            
            # Prepare APNS payload
            apns_payload = {
                'aps': {
                    'alert': {
                        'title': notification.title,
                        'body': notification.body,
                    },
                    'badge': 1,
                    'sound': 'default',
                    'category': notification.category,
                    'thread-id': f'school_erp_{notification.category}',
                },
                'data': notification.data,
                'url': notification.action_url,
            }
            
            # Set priority
            if notification.priority == NotificationPriority.URGENT:
                apns_payload['aps']['sound'] = 'critical'
                apns_payload['aps']['critical'] = 1
            
            headers = {
                'authorization': f'bearer {token}',
                'apns-topic': 'com.schoolerp.app',  # Your app bundle ID
                'apns-priority': '10' if notification.priority in [NotificationPriority.HIGH, NotificationPriority.URGENT] else '5',
                'apns-push-type': 'alert',
            }
            
            # Send to APNS
            device_token = subscription.endpoint.split('/')[-1]  # APNS device token
            url = f'https://api.push.apple.com/3/device/{device_token}'
            
            response = requests.post(
                url,
                headers=headers,
                json=apns_payload,
                timeout=30
            )
            
            return response.status_code == 200
            
        except Exception as e:
            logger.error(f"APNS push error: {str(e)}")
            return False
    
    def subscribe_user(
        self,
        user: User,
        endpoint: str,
        p256dh_key: str,
        auth_key: str,
        device_type: str = 'web',
        device_info: Optional[Dict] = None
    ) -> PushSubscription:
        """Subscribe a user to push notifications."""
        
        subscription, created = PushSubscription.objects.update_or_create(
            user=user,
            endpoint=endpoint,
            defaults={
                'p256dh_key': p256dh_key,
                'auth_key': auth_key,
                'device_type': device_type,
                'device_info': device_info or {},
                'is_active': True,
                'last_used': timezone.now(),
            }
        )
        
        if created:
            logger.info(f"New push subscription created for {user.username}")
        else:
            logger.info(f"Push subscription updated for {user.username}")
        
        return subscription
    
    def unsubscribe_user(self, user: User, endpoint: str) -> bool:
        """Unsubscribe a user from push notifications."""
        
        try:
            subscription = PushSubscription.objects.get(
                user=user,
                endpoint=endpoint
            )
            subscription.is_active = False
            subscription.save()
            
            logger.info(f"Push subscription deactivated for {user.username}")
            return True
            
        except PushSubscription.DoesNotExist:
            logger.warning(f"Push subscription not found for {user.username}")
            return False
    
    def get_user_subscriptions(self, user: User) -> List[PushSubscription]:
        """Get all active subscriptions for a user."""
        
        return list(PushSubscription.objects.filter(
            user=user,
            is_active=True
        ))
    
    def send_scheduled_notifications(self) -> Dict[str, int]:
        """Send all scheduled notifications that are due."""
        
        now = timezone.now()
        due_notifications = PushNotification.objects.filter(
            scheduled_at__lte=now,
            is_sent=False
        ).exclude(
            expires_at__lte=now
        )
        
        total_success = 0
        total_failed = 0
        processed = 0
        
        for notification in due_notifications:
            result = self.send_notification(notification)
            total_success += result.get('success', 0)
            total_failed += result.get('failed', 0)
            processed += 1
        
        logger.info(f"Processed {processed} scheduled notifications: {total_success} success, {total_failed} failed")
        
        return {
            'processed': processed,
            'success': total_success,
            'failed': total_failed
        }
    
    def cleanup_expired_notifications(self, days: int = 30) -> int:
        """Clean up old notification records."""
        
        cutoff_date = timezone.now() - timedelta(days=days)
        
        # Delete old notification records
        deleted_notifications = PushNotification.objects.filter(
            created_at__lt=cutoff_date
        ).delete()[0]
        
        # Deactivate old subscriptions
        old_subscriptions = PushSubscription.objects.filter(
            last_used__lt=cutoff_date,
            is_active=True
        )
        deactivated_subscriptions = old_subscriptions.update(is_active=False)
        
        logger.info(f"Cleaned up {deleted_notifications} notifications and deactivated {deactivated_subscriptions} subscriptions")
        
        return deleted_notifications
    
    def get_notification_analytics(self, days: int = 30) -> Dict:
        """Get analytics for push notifications."""
        
        cutoff_date = timezone.now() - timedelta(days=days)
        
        # Get notification stats
        total_notifications = PushNotification.objects.filter(
            created_at__gte=cutoff_date
        ).count()
        
        sent_notifications = PushNotification.objects.filter(
            created_at__gte=cutoff_date,
            is_sent=True
        ).count()
        
        # Get delivery stats
        total_recipients = PushNotificationRecipient.objects.filter(
            notification__created_at__gte=cutoff_date
        ).count()
        
        delivered_recipients = PushNotificationRecipient.objects.filter(
            notification__created_at__gte=cutoff_date,
            is_delivered=True
        ).count()
        
        clicked_recipients = PushNotificationRecipient.objects.filter(
            notification__created_at__gte=cutoff_date,
            is_clicked=True
        ).count()
        
        # Get category stats
        category_stats = {}
        for category, _ in NotificationCategory.choices:
            count = PushNotification.objects.filter(
                created_at__gte=cutoff_date,
                category=category
            ).count()
            category_stats[category] = count
        
        return {
            'total_notifications': total_notifications,
            'sent_notifications': sent_notifications,
            'send_rate': (sent_notifications / max(total_notifications, 1)) * 100,
            'total_recipients': total_recipients,
            'delivered_recipients': delivered_recipients,
            'delivery_rate': (delivered_recipients / max(total_recipients, 1)) * 100,
            'clicked_recipients': clicked_recipients,
            'click_rate': (clicked_recipients / max(delivered_recipients, 1)) * 100,
            'category_stats': category_stats,
        }


# Global service instance
push_service = PushNotificationService()