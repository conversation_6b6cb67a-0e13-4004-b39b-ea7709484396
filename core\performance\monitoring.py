"""
Performance Monitoring System for School ERP
"""
import time
import logging
import psutil
import threading
from datetime import datetime, timedelta
from collections import defaultdict, deque
from django.conf import settings
from django.core.cache import cache
from django.db import connection
from django.utils import timezone
from django.core.management.base import BaseCommand
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.contrib.admin.views.decorators import staff_member_required
from django.utils.decorators import method_decorator
from django.views import View

logger = logging.getLogger(__name__)


class PerformanceMetrics:
    """Collect and store performance metrics"""
    
    def __init__(self):
        self.metrics = defaultdict(deque)
        self.max_history = 1000  # Keep last 1000 measurements
        self.lock = threading.Lock()
    
    def record_metric(self, metric_name, value, timestamp=None):
        """Record a performance metric"""
        if timestamp is None:
            timestamp = timezone.now()
        
        with self.lock:
            self.metrics[metric_name].append({
                'value': value,
                'timestamp': timestamp
            })
            
            # Keep only recent measurements
            if len(self.metrics[metric_name]) > self.max_history:
                self.metrics[metric_name].popleft()
    
    def get_metric_stats(self, metric_name, duration_minutes=60):
        """Get statistics for a metric over specified duration"""
        cutoff_time = timezone.now() - timedelta(minutes=duration_minutes)
        
        with self.lock:
            recent_values = [
                m['value'] for m in self.metrics[metric_name]
                if m['timestamp'] >= cutoff_time
            ]
        
        if not recent_values:
            return None
        
        return {
            'count': len(recent_values),
            'min': min(recent_values),
            'max': max(recent_values),
            'avg': sum(recent_values) / len(recent_values),
            'latest': recent_values[-1] if recent_values else None
        }
    
    def get_all_metrics(self, duration_minutes=60):
        """Get all metrics statistics"""
        return {
            metric_name: self.get_metric_stats(metric_name, duration_minutes)
            for metric_name in self.metrics.keys()
        }


# Global metrics instance
performance_metrics = PerformanceMetrics()


class PerformanceMiddleware:
    """Middleware to track request performance"""
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        start_time = time.time()
        
        # Process request
        response = self.get_response(request)
        
        # Calculate response time
        response_time = time.time() - start_time
        
        # Record metrics
        performance_metrics.record_metric('response_time', response_time * 1000)  # Convert to ms
        performance_metrics.record_metric('request_count', 1)
        
        # Record by view/URL pattern
        view_name = getattr(request.resolver_match, 'view_name', 'unknown') if hasattr(request, 'resolver_match') else 'unknown'
        performance_metrics.record_metric(f'response_time_{view_name}', response_time * 1000)
        
        # Record HTTP status codes
        performance_metrics.record_metric(f'status_{response.status_code}', 1)
        
        # Add performance header
        response['X-Response-Time'] = f"{response_time * 1000:.2f}ms"
        
        return response


class DatabasePerformanceMonitor:
    """Monitor database performance"""
    
    @staticmethod
    def get_connection_stats():
        """Get database connection statistics"""
        try:
            with connection.cursor() as cursor:
                # Get connection count (PostgreSQL specific)
                cursor.execute("""
                    SELECT count(*) as total_connections,
                           count(*) FILTER (WHERE state = 'active') as active_connections,
                           count(*) FILTER (WHERE state = 'idle') as idle_connections
                    FROM pg_stat_activity
                    WHERE datname = current_database()
                """)
                result = cursor.fetchone()
                
                return {
                    'total_connections': result[0],
                    'active_connections': result[1],
                    'idle_connections': result[2]
                }
        except Exception as e:
            logger.error(f"Error getting database connection stats: {e}")
            return None
    
    @staticmethod
    def get_slow_queries(limit=10):
        """Get slow running queries"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT query, state, query_start, now() - query_start as duration
                    FROM pg_stat_activity
                    WHERE state = 'active'
                    AND query NOT LIKE '%pg_stat_activity%'
                    ORDER BY query_start
                    LIMIT %s
                """, [limit])
                
                columns = [col[0] for col in cursor.description]
                return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"Error getting slow queries: {e}")
            return []
    
    @staticmethod
    def get_table_stats():
        """Get table statistics"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT schemaname, tablename, n_tup_ins, n_tup_upd, n_tup_del, n_live_tup, n_dead_tup
                    FROM pg_stat_user_tables
                    ORDER BY n_live_tup DESC
                    LIMIT 20
                """)
                
                columns = [col[0] for col in cursor.description]
                return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"Error getting table stats: {e}")
            return []


class SystemResourceMonitor:
    """Monitor system resources"""
    
    @staticmethod
    def get_cpu_usage():
        """Get CPU usage percentage"""
        return psutil.cpu_percent(interval=1)
    
    @staticmethod
    def get_memory_usage():
        """Get memory usage statistics"""
        memory = psutil.virtual_memory()
        return {
            'total': memory.total,
            'available': memory.available,
            'used': memory.used,
            'percentage': memory.percent
        }
    
    @staticmethod
    def get_disk_usage():
        """Get disk usage statistics"""
        disk = psutil.disk_usage('/')
        return {
            'total': disk.total,
            'used': disk.used,
            'free': disk.free,
            'percentage': (disk.used / disk.total) * 100
        }
    
    @staticmethod
    def get_network_stats():
        """Get network statistics"""
        net_io = psutil.net_io_counters()
        return {
            'bytes_sent': net_io.bytes_sent,
            'bytes_recv': net_io.bytes_recv,
            'packets_sent': net_io.packets_sent,
            'packets_recv': net_io.packets_recv
        }


class PerformanceCollector:
    """Collect all performance metrics"""
    
    def __init__(self):
        self.db_monitor = DatabasePerformanceMonitor()
        self.system_monitor = SystemResourceMonitor()
    
    def collect_all_metrics(self):
        """Collect all performance metrics"""
        metrics = {}
        
        try:
            # System metrics
            metrics['cpu_usage'] = self.system_monitor.get_cpu_usage()
            metrics['memory_usage'] = self.system_monitor.get_memory_usage()
            metrics['disk_usage'] = self.system_monitor.get_disk_usage()
            metrics['network_stats'] = self.system_monitor.get_network_stats()
            
            # Database metrics
            metrics['db_connections'] = self.db_monitor.get_connection_stats()
            metrics['slow_queries'] = self.db_monitor.get_slow_queries()
            metrics['table_stats'] = self.db_monitor.get_table_stats()
            
            # Application metrics
            metrics['app_metrics'] = performance_metrics.get_all_metrics()
            
            # Cache metrics
            if hasattr(cache, '_cache'):
                cache_stats = getattr(cache._cache, 'get_stats', lambda: {})()
                metrics['cache_stats'] = cache_stats
            
        except Exception as e:
            logger.error(f"Error collecting performance metrics: {e}")
        
        return metrics
    
    def store_metrics(self, metrics):
        """Store metrics in cache for later retrieval"""
        cache_key = f"performance_metrics_{timezone.now().strftime('%Y%m%d_%H%M')}"
        cache.set(cache_key, metrics, timeout=3600)  # Store for 1 hour
        
        # Also record key metrics in our performance tracker
        if 'cpu_usage' in metrics:
            performance_metrics.record_metric('cpu_usage', metrics['cpu_usage'])
        
        if 'memory_usage' in metrics and metrics['memory_usage']:
            performance_metrics.record_metric('memory_usage', metrics['memory_usage']['percentage'])
        
        if 'db_connections' in metrics and metrics['db_connections']:
            performance_metrics.record_metric('db_active_connections', metrics['db_connections']['active_connections'])


class PerformanceAlerts:
    """Handle performance alerts and notifications"""
    
    def __init__(self):
        self.thresholds = {
            'cpu_usage': 80,  # 80% CPU usage
            'memory_usage': 85,  # 85% memory usage
            'disk_usage': 90,  # 90% disk usage
            'response_time': 2000,  # 2 seconds response time
            'db_connections': 80,  # 80% of max connections
        }
        self.alert_cooldown = {}  # Prevent spam alerts
    
    def check_thresholds(self, metrics):
        """Check if any metrics exceed thresholds"""
        alerts = []
        current_time = timezone.now()
        
        # CPU usage alert
        if metrics.get('cpu_usage', 0) > self.thresholds['cpu_usage']:
            if self._should_alert('cpu_usage', current_time):
                alerts.append({
                    'type': 'cpu_usage',
                    'message': f"High CPU usage: {metrics['cpu_usage']:.1f}%",
                    'severity': 'warning',
                    'value': metrics['cpu_usage']
                })
        
        # Memory usage alert
        memory = metrics.get('memory_usage', {})
        if memory.get('percentage', 0) > self.thresholds['memory_usage']:
            if self._should_alert('memory_usage', current_time):
                alerts.append({
                    'type': 'memory_usage',
                    'message': f"High memory usage: {memory['percentage']:.1f}%",
                    'severity': 'warning',
                    'value': memory['percentage']
                })
        
        # Disk usage alert
        disk = metrics.get('disk_usage', {})
        if disk.get('percentage', 0) > self.thresholds['disk_usage']:
            if self._should_alert('disk_usage', current_time):
                alerts.append({
                    'type': 'disk_usage',
                    'message': f"High disk usage: {disk['percentage']:.1f}%",
                    'severity': 'critical',
                    'value': disk['percentage']
                })
        
        # Response time alert
        app_metrics = metrics.get('app_metrics', {})
        response_time_stats = app_metrics.get('response_time')
        if response_time_stats and response_time_stats['avg'] > self.thresholds['response_time']:
            if self._should_alert('response_time', current_time):
                alerts.append({
                    'type': 'response_time',
                    'message': f"High response time: {response_time_stats['avg']:.1f}ms",
                    'severity': 'warning',
                    'value': response_time_stats['avg']
                })
        
        return alerts
    
    def _should_alert(self, alert_type, current_time):
        """Check if we should send an alert (cooldown logic)"""
        last_alert = self.alert_cooldown.get(alert_type)
        if last_alert is None or (current_time - last_alert).total_seconds() > 300:  # 5 minute cooldown
            self.alert_cooldown[alert_type] = current_time
            return True
        return False
    
    def send_alerts(self, alerts):
        """Send alerts to administrators"""
        if not alerts:
            return
        
        # Log alerts
        for alert in alerts:
            logger.warning(f"Performance Alert: {alert['message']}")
        
        # Store alerts in cache for dashboard
        cache_key = "performance_alerts"
        existing_alerts = cache.get(cache_key, [])
        
        # Add timestamp to alerts
        for alert in alerts:
            alert['timestamp'] = timezone.now()
        
        existing_alerts.extend(alerts)
        
        # Keep only last 50 alerts
        if len(existing_alerts) > 50:
            existing_alerts = existing_alerts[-50:]
        
        cache.set(cache_key, existing_alerts, timeout=86400)  # Store for 24 hours


class PerformanceMonitoringService:
    """Main service for performance monitoring"""
    
    def __init__(self):
        self.collector = PerformanceCollector()
        self.alerts = PerformanceAlerts()
        self.running = False
        self.thread = None
    
    def start_monitoring(self):
        """Start the performance monitoring service"""
        if self.running:
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.thread.start()
        logger.info("Performance monitoring service started")
    
    def stop_monitoring(self):
        """Stop the performance monitoring service"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)
        logger.info("Performance monitoring service stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.running:
            try:
                # Collect metrics
                metrics = self.collector.collect_all_metrics()
                
                # Store metrics
                self.collector.store_metrics(metrics)
                
                # Check for alerts
                alerts = self.alerts.check_thresholds(metrics)
                if alerts:
                    self.alerts.send_alerts(alerts)
                
                # Wait before next collection
                time.sleep(60)  # Collect every minute
                
            except Exception as e:
                logger.error(f"Error in performance monitoring loop: {e}")
                time.sleep(60)  # Wait before retrying


# Global monitoring service instance
monitoring_service = PerformanceMonitoringService()


@method_decorator(staff_member_required, name='dispatch')
class PerformanceMonitoringView(View):
    """View for performance monitoring dashboard"""
    
    def get(self, request):
        """Get current performance metrics"""
        collector = PerformanceCollector()
        metrics = collector.collect_all_metrics()
        
        # Get recent alerts
        alerts = cache.get("performance_alerts", [])
        recent_alerts = [
            alert for alert in alerts
            if (timezone.now() - alert['timestamp']).total_seconds() < 3600  # Last hour
        ]
        
        return JsonResponse({
            'metrics': metrics,
            'alerts': recent_alerts,
            'timestamp': timezone.now().isoformat()
        })


@require_http_methods(["GET"])
@staff_member_required
def performance_health_check(request):
    """Health check endpoint for monitoring systems"""
    try:
        # Basic health checks
        health_status = {
            'status': 'healthy',
            'timestamp': timezone.now().isoformat(),
            'checks': {}
        }
        
        # Database check
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                health_status['checks']['database'] = 'healthy'
        except Exception as e:
            health_status['checks']['database'] = f'unhealthy: {str(e)}'
            health_status['status'] = 'unhealthy'
        
        # Cache check
        try:
            cache.set('health_check', 'ok', timeout=60)
            if cache.get('health_check') == 'ok':
                health_status['checks']['cache'] = 'healthy'
            else:
                health_status['checks']['cache'] = 'unhealthy: cache not working'
                health_status['status'] = 'degraded'
        except Exception as e:
            health_status['checks']['cache'] = f'unhealthy: {str(e)}'
            health_status['status'] = 'unhealthy'
        
        # System resources check
        try:
            cpu_usage = SystemResourceMonitor.get_cpu_usage()
            memory_usage = SystemResourceMonitor.get_memory_usage()
            
            if cpu_usage > 90:
                health_status['checks']['cpu'] = f'critical: {cpu_usage:.1f}%'
                health_status['status'] = 'unhealthy'
            elif cpu_usage > 80:
                health_status['checks']['cpu'] = f'warning: {cpu_usage:.1f}%'
                if health_status['status'] == 'healthy':
                    health_status['status'] = 'degraded'
            else:
                health_status['checks']['cpu'] = f'healthy: {cpu_usage:.1f}%'
            
            if memory_usage['percentage'] > 90:
                health_status['checks']['memory'] = f'critical: {memory_usage["percentage"]:.1f}%'
                health_status['status'] = 'unhealthy'
            elif memory_usage['percentage'] > 85:
                health_status['checks']['memory'] = f'warning: {memory_usage["percentage"]:.1f}%'
                if health_status['status'] == 'healthy':
                    health_status['status'] = 'degraded'
            else:
                health_status['checks']['memory'] = f'healthy: {memory_usage["percentage"]:.1f}%'
                
        except Exception as e:
            health_status['checks']['system'] = f'unhealthy: {str(e)}'
            health_status['status'] = 'unhealthy'
        
        # Set appropriate HTTP status code
        if health_status['status'] == 'healthy':
            status_code = 200
        elif health_status['status'] == 'degraded':
            status_code = 200  # Still operational
        else:
            status_code = 503  # Service unavailable
        
        return JsonResponse(health_status, status=status_code)
        
    except Exception as e:
        return JsonResponse({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': timezone.now().isoformat()
        }, status=503)


class PerformanceManagementCommand(BaseCommand):
    """Management command for performance monitoring"""
    
    help = 'Manage performance monitoring service'
    
    def add_arguments(self, parser):
        parser.add_argument(
            'action',
            choices=['start', 'stop', 'status', 'collect'],
            help='Action to perform'
        )
    
    def handle(self, *args, **options):
        action = options['action']
        
        if action == 'start':
            monitoring_service.start_monitoring()
            self.stdout.write(
                self.style.SUCCESS('Performance monitoring service started')
            )
        
        elif action == 'stop':
            monitoring_service.stop_monitoring()
            self.stdout.write(
                self.style.SUCCESS('Performance monitoring service stopped')
            )
        
        elif action == 'status':
            status = 'running' if monitoring_service.running else 'stopped'
            self.stdout.write(f'Performance monitoring service is {status}')
        
        elif action == 'collect':
            collector = PerformanceCollector()
            metrics = collector.collect_all_metrics()
            collector.store_metrics(metrics)
            
            self.stdout.write('Current performance metrics:')
            self.stdout.write(f"CPU Usage: {metrics.get('cpu_usage', 'N/A')}%")
            
            memory = metrics.get('memory_usage', {})
            self.stdout.write(f"Memory Usage: {memory.get('percentage', 'N/A')}%")
            
            disk = metrics.get('disk_usage', {})
            self.stdout.write(f"Disk Usage: {disk.get('percentage', 'N/A')}%")
            
            db_conn = metrics.get('db_connections', {})
            self.stdout.write(f"DB Connections: {db_conn.get('active_connections', 'N/A')}")


# Auto-start monitoring service when module is imported
if getattr(settings, 'PERFORMANCE_MONITORING_ENABLED', True):
    monitoring_service.start_monitoring()