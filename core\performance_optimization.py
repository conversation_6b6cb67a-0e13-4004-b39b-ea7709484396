"""
Performance optimization utilities for the School ERP system.
Includes database query optimization, caching strategies, and performance monitoring.
"""

import logging
import time
from functools import wraps
from django.core.cache import cache
from django.db import connection
from django.db.models import Prefetch, Q
from django.utils import timezone
from django.conf import settings
from core.models import School

logger = logging.getLogger(__name__)


class QueryOptimizer:
    """
    Utility class for optimizing database queries with caching and prefetching.
    """
    
    @staticmethod
    def optimize_school_queries(queryset):
        """
        Optimize school-related queries with proper select_related and prefetch_related.
        
        Args:
            queryset: Django QuerySet
            
        Returns:
            Optimized QuerySet
        """
        return queryset.select_related(
            'school'
        ).prefetch_related(
            'school__academic_years'
        )
    
    @staticmethod
    def optimize_user_queries(queryset):
        """
        Optimize user-related queries with proper relationships.
        
        Args:
            queryset: Django QuerySet
            
        Returns:
            Optimized QuerySet
        """
        return queryset.select_related(
            'user',
            'user__student_profile',
            'user__employee'
        ).prefetch_related(
            'user__groups',
            'user__user_permissions'
        )
    
    @staticmethod
    def get_school_filtered_queryset(model_class, school, additional_filters=None):
        """
        Get optimized queryset filtered by school with caching.
        
        Args:
            model_class: Django model class
            school: School instance
            additional_filters: Additional Q objects for filtering
            
        Returns:
            Optimized and filtered QuerySet
        """
        cache_key = f"school_queryset_{model_class.__name__}_{school.id}"
        if additional_filters:
            cache_key += f"_{hash(str(additional_filters))}"
        
        cached_queryset = cache.get(cache_key)
        if cached_queryset is not None:
            return cached_queryset
        
        # Build base query
        queryset = model_class.objects.filter(school=school, is_active=True)
        
        # Apply additional filters
        if additional_filters:
            queryset = queryset.filter(additional_filters)
        
        # Apply optimizations based on model
        if hasattr(model_class, 'school'):
            queryset = QueryOptimizer.optimize_school_queries(queryset)
        
        # Cache for 10 minutes
        cache.set(cache_key, queryset, 600)
        
        return queryset


class CacheManager:
    """
    Advanced cache management for school-related data.
    """
    
    # Cache key prefixes for different data types
    CACHE_PREFIXES = {
        'school_context': 'sc',
        'user_schools': 'us',
        'school_stats': 'ss',
        'academic_year': 'ay',
        'permissions': 'perm',
        'session_data': 'sess'
    }
    
    # Cache TTL settings (in seconds)
    CACHE_TTL = {
        'short': 300,      # 5 minutes
        'medium': 900,     # 15 minutes
        'long': 3600,      # 1 hour
        'very_long': 86400 # 24 hours
    }
    
    @classmethod
    def get_cache_key(cls, prefix, *args):
        """
        Generate standardized cache key.
        
        Args:
            prefix: Cache prefix from CACHE_PREFIXES
            *args: Additional key components
            
        Returns:
            Formatted cache key
        """
        prefix_code = cls.CACHE_PREFIXES.get(prefix, prefix)
        key_parts = [prefix_code] + [str(arg) for arg in args]
        return '_'.join(key_parts)
    
    @classmethod
    def set_with_metadata(cls, key, value, ttl, metadata=None):
        """
        Set cache value with metadata for better cache management.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds
            metadata: Additional metadata dict
        """
        cache_data = {
            'value': value,
            'timestamp': timezone.now(),
            'ttl': ttl,
            'metadata': metadata or {}
        }
        cache.set(key, cache_data, ttl)
    
    @classmethod
    def get_with_validation(cls, key, max_age=None):
        """
        Get cache value with age validation.
        
        Args:
            key: Cache key
            max_age: Maximum age in seconds (optional)
            
        Returns:
            Cached value or None if invalid/expired
        """
        cached_data = cache.get(key)
        if not cached_data or not isinstance(cached_data, dict):
            return None
        
        # Check age if specified
        if max_age:
            timestamp = cached_data.get('timestamp')
            if timestamp and (timezone.now() - timestamp).total_seconds() > max_age:
                cache.delete(key)
                return None
        
        return cached_data.get('value')
    
    @classmethod
    def invalidate_pattern(cls, pattern):
        """
        Invalidate cache keys matching a pattern.
        Note: This is a simplified implementation. In production,
        consider using Redis with pattern matching or cache tagging.
        
        Args:
            pattern: Pattern to match (simplified string matching)
        """
        # This is a placeholder for pattern-based cache invalidation
        # In a real implementation, you'd use Redis SCAN or similar
        logger.info(f"Cache invalidation requested for pattern: {pattern}")
    
    @classmethod
    def warm_school_cache(cls, school):
        """
        Pre-warm cache for a specific school with commonly accessed data.
        
        Args:
            school: School instance
        """
        try:
            # Pre-cache school statistics
            from students.models import Student
            from academics.models import Teacher, Subject, Class
            from hr.models import Department
            
            stats_key = cls.get_cache_key('school_stats', school.id)
            stats = {
                'total_students': Student.objects.filter(school=school, is_active=True).count(),
                'total_teachers': Teacher.objects.filter(school=school, is_active=True).count(),
                'total_classes': Class.objects.filter(school=school, is_active=True).count(),
                'total_subjects': Subject.objects.filter(school=school, is_active=True).count(),
                'total_departments': school.department_set.filter(is_active=True).count(),
            }
            cls.set_with_metadata(stats_key, stats, cls.CACHE_TTL['medium'], {'school_id': school.id})
            
            # Pre-cache academic year
            from core.models import AcademicYear
            ay_key = cls.get_cache_key('academic_year', school.id)
            current_ay = AcademicYear.objects.filter(school=school, is_current=True).first()
            cls.set_with_metadata(ay_key, current_ay, cls.CACHE_TTL['long'], {'school_id': school.id})
            
            logger.info(f"Cache warmed for school: {school.name}")
            
        except Exception as e:
            logger.error(f"Error warming cache for school {school.id}: {e}")
    
    @classmethod
    def get_school_statistics(cls, school):
        """
        Get comprehensive school statistics with caching
        """
        stats_key = cls.get_cache_key('school_stats', school.id)
        
        # Try to get from cache first
        cached_stats = cls.get_with_metadata(stats_key)
        if cached_stats:
            return cached_stats
        
        # Generate fresh statistics
        from students.models import Student, Class
        from academics.models import Subject, Teacher
        
        try:
            stats = {
                'total_students': Student.objects.filter(school=school, is_active=True).count(),
                'total_teachers': Teacher.objects.filter(school=school, is_active=True).count(),
                'total_classes': Class.objects.filter(school=school, is_active=True).count(),
                'total_subjects': Subject.objects.filter(school=school, is_active=True).count(),
                'total_departments': school.department_set.filter(is_active=True).count(),
                'active_academic_years': school.academic_years.filter(is_active=True).count(),
                'last_updated': timezone.now().isoformat(),
            }
            cls.set_with_metadata(stats_key, stats, cls.CACHE_TTL['medium'], {'school_id': school.id})
            return stats
            
        except Exception as e:
            logger.error(f"Error generating school statistics: {e}")
            return {
                'total_students': 0,
                'total_teachers': 0,
                'total_classes': 0,
                'total_subjects': 0,
                'total_departments': 0,
                'active_academic_years': 0,
                'last_updated': timezone.now().isoformat(),
            }
    
    @classmethod
    def get_user_school_cache(cls, user):
        """
        Get cached user school data with optimized queries
        """
        cache_key = cls.get_cache_key('user_schools', user.id)
        
        cached_data = cls.get_with_metadata(cache_key)
        if cached_data:
            return cached_data
        
        # Generate fresh user school data
        from core.school_utils import get_user_schools
        
        try:
            schools = get_user_schools(user)
            school_data = []
            
            for school in schools:
                school_info = {
                    'id': str(school.id),
                    'name': school.name,
                    'code': school.code,
                    'address': school.address,
                    'is_active': school.is_active,
                }
                school_data.append(school_info)
            
            cls.set_with_metadata(cache_key, school_data, cls.CACHE_TTL['short'], {'user_id': user.id})
            return school_data
            
        except Exception as e:
            logger.error(f"Error caching user schools: {e}")
            return []
    
    @classmethod
    def invalidate_school_cache(cls, school):
        """
        Invalidate all cache entries related to a school
        """
        patterns = [
            cls.get_cache_key('school_stats', school.id),
            cls.get_cache_key('school_context', school.id),
            cls.get_cache_key('academic_year', school.id),
        ]
        
        for pattern in patterns:
            cls.delete(pattern)
        
        logger.info(f"Invalidated cache for school: {school.name}")


class PerformanceMonitor:
    """
    Performance monitoring and optimization utilities.
    """
    
    @staticmethod
    def monitor_queries(func):
        """
        Decorator to monitor database queries for a function.
        
        Args:
            func: Function to monitor
            
        Returns:
            Decorated function
        """
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Reset query count
            initial_queries = len(connection.queries)
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                
                # Calculate metrics
                end_time = time.time()
                execution_time = end_time - start_time
                query_count = len(connection.queries) - initial_queries
                
                # Log performance metrics
                if settings.DEBUG or execution_time > 1.0:  # Log slow operations
                    logger.info(
                        f"Performance: {func.__name__} - "
                        f"Time: {execution_time:.3f}s, "
                        f"Queries: {query_count}"
                    )
                
                # Log slow queries in debug mode
                if settings.DEBUG and query_count > 10:
                    logger.warning(f"High query count in {func.__name__}: {query_count} queries")
                
                return result
                
            except Exception as e:
                end_time = time.time()
                execution_time = end_time - start_time
                logger.error(
                    f"Performance: {func.__name__} failed - "
                    f"Time: {execution_time:.3f}s, "
                    f"Error: {e}"
                )
                raise
        
        return wrapper
    
    @staticmethod
    def log_slow_queries(threshold=0.5):
        """
        Log queries that exceed the specified threshold.
        
        Args:
            threshold: Time threshold in seconds
        """
        if not settings.DEBUG:
            return
        
        for query in connection.queries:
            query_time = float(query['time'])
            if query_time > threshold:
                logger.warning(
                    f"Slow query ({query_time:.3f}s): {query['sql'][:200]}..."
                )
    
    @staticmethod
    def get_cache_stats():
        """
        Get cache performance statistics.
        
        Returns:
            Dict with cache statistics
        """
        # This is a simplified implementation
        # In production, you'd integrate with your cache backend's stats
        return {
            'cache_backend': settings.CACHES['default']['BACKEND'],
            'timestamp': timezone.now(),
            'note': 'Detailed cache stats require cache backend integration'
        }


class DatabaseOptimizer:
    """
    Database-specific optimization utilities.
    """
    
    @staticmethod
    def optimize_school_filtering(queryset, school):
        """
        Optimize queries that filter by school.
        
        Args:
            queryset: Django QuerySet
            school: School instance
            
        Returns:
            Optimized QuerySet
        """
        # Use select_related for foreign key relationships
        if hasattr(queryset.model, 'school'):
            queryset = queryset.select_related('school')
        
        # Add school filter early in the query
        queryset = queryset.filter(school=school)
        
        # Use database indexes effectively
        if hasattr(queryset.model, 'is_active'):
            queryset = queryset.filter(is_active=True)
        
        return queryset
    
    @staticmethod
    def batch_school_operations(operations, batch_size=100):
        """
        Execute school-related operations in batches for better performance.
        
        Args:
            operations: List of operations to execute
            batch_size: Size of each batch
            
        Returns:
            List of results
        """
        results = []
        
        for i in range(0, len(operations), batch_size):
            batch = operations[i:i + batch_size]
            batch_results = []
            
            try:
                for operation in batch:
                    result = operation()
                    batch_results.append(result)
                
                results.extend(batch_results)
                
            except Exception as e:
                logger.error(f"Error in batch operation: {e}")
                # Continue with next batch
                continue
        
        return results
    
    @staticmethod
    def prefetch_school_related_data(schools_queryset):
        """
        Prefetch related data for schools to minimize queries.
        
        Args:
            schools_queryset: QuerySet of schools
            
        Returns:
            QuerySet with prefetched data
        """
        return schools_queryset.prefetch_related(
            'academic_years',
            Prefetch(
                'student_set',
                queryset=Student.objects.filter(is_active=True).select_related('current_class')
            ),
            Prefetch(
                'teacher_set',
                queryset=Teacher.objects.filter(is_active=True).select_related('user')
            )
        )


# Utility functions for common performance optimizations

def cached_school_context(request, ttl=900):
    """
    Get school context with caching.
    
    Args:
        request: Django HttpRequest
        ttl: Cache TTL in seconds
        
    Returns:
        School context dict
    """
    if not request.user.is_authenticated:
        return {}
    
    cache_key = CacheManager.get_cache_key('school_context', request.user.id)
    context = CacheManager.get_with_validation(cache_key, max_age=ttl)
    
    if context is None:
        from core.school_utils import get_school_context_data
        context = get_school_context_data(request)
        CacheManager.set_with_metadata(cache_key, context, ttl)
    
    return context


def optimize_school_queryset(model_class, school, filters=None):
    """
    Get optimized queryset for school-filtered data.
    
    Args:
        model_class: Django model class
        school: School instance
        filters: Additional filters (Q objects)
        
    Returns:
        Optimized QuerySet
    """
    return QueryOptimizer.get_school_filtered_queryset(model_class, school, filters)


def warm_user_cache(user):
    """
    Pre-warm cache for a specific user.
    
    Args:
        user: Django User instance
    """
    try:
        from core.school_utils import get_user_schools
        
        # Pre-cache user schools
        schools = get_user_schools(user)
        
        # Warm cache for each school the user has access to
        for school in schools:
            CacheManager.warm_school_cache(school)
        
        logger.info(f"Cache warmed for user: {user.username}")
        
    except Exception as e:
        logger.error(f"Error warming cache for user {user.id}: {e}")


# Performance monitoring decorators
monitor_performance = PerformanceMonitor.monitor_queries