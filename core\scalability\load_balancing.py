"""
Load Balancing and Auto-scaling for School ERP System
"""
import time
import threading
import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
from django.http import HttpResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
import json

logger = logging.getLogger(__name__)


class LoadBalancer:
    """
    Simple load balancer for distributing requests
    """
    
    def __init__(self):
        self.servers = []
        self.current_index = 0
        self.health_checks = {}
        self.lock = threading.Lock()
    
    def add_server(self, server_url: str, weight: int = 1):
        """Add a server to the load balancer"""
        with self.lock:
            server = {
                'url': server_url,
                'weight': weight,
                'active_connections': 0,
                'total_requests': 0,
                'response_time': 0,
                'healthy': True,
                'last_health_check': timezone.now()
            }
            self.servers.append(server)
            logger.info(f"Added server to load balancer: {server_url}")
    
    def remove_server(self, server_url: str):
        """Remove a server from the load balancer"""
        with self.lock:
            self.servers = [s for s in self.servers if s['url'] != server_url]
            logger.info(f"Removed server from load balancer: {server_url}")
    
    def get_next_server(self, strategy: str = 'round_robin') -> Optional[Dict[str, Any]]:
        """Get the next server based on load balancing strategy"""
        with self.lock:
            healthy_servers = [s for s in self.servers if s['healthy']]
            
            if not healthy_servers:
                logger.error("No healthy servers available")
                return None
            
            if strategy == 'round_robin':
                return self._round_robin_selection(healthy_servers)
            elif strategy == 'least_connections':
                return self._least_connections_selection(healthy_servers)
            elif strategy == 'weighted':
                return self._weighted_selection(healthy_servers)
            elif strategy == 'response_time':
                return self._response_time_selection(healthy_servers)
            else:
                return self._round_robin_selection(healthy_servers)
    
    def _round_robin_selection(self, servers: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Round-robin server selection"""
        if not servers:
            return None
        
        server = servers[self.current_index % len(servers)]
        self.current_index = (self.current_index + 1) % len(servers)
        return server
    
    def _least_connections_selection(self, servers: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Select server with least active connections"""
        return min(servers, key=lambda s: s['active_connections'])
    
    def _weighted_selection(self, servers: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Weighted server selection"""
        total_weight = sum(s['weight'] for s in servers)
        if total_weight == 0:
            return servers[0]
        
        # Simple weighted selection (could be improved with proper weighted round-robin)
        import random
        weights = [s['weight'] / total_weight for s in servers]
        return random.choices(servers, weights=weights)[0]
    
    def _response_time_selection(self, servers: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Select server with best response time"""
        return min(servers, key=lambda s: s['response_time'] or float('inf'))
    
    def update_server_stats(self, server_url: str, response_time: float, success: bool):
        """Update server statistics"""
        with self.lock:
            for server in self.servers:
                if server['url'] == server_url:
                    server['total_requests'] += 1
                    server['response_time'] = response_time
                    if not success:
                        server['healthy'] = False
                        logger.warning(f"Server marked as unhealthy: {server_url}")
                    break
    
    def increment_connections(self, server_url: str):
        """Increment active connections for a server"""
        with self.lock:
            for server in self.servers:
                if server['url'] == server_url:
                    server['active_connections'] += 1
                    break
    
    def decrement_connections(self, server_url: str):
        """Decrement active connections for a server"""
        with self.lock:
            for server in self.servers:
                if server['url'] == server_url:
                    server['active_connections'] = max(0, server['active_connections'] - 1)
                    break
    
    def get_server_stats(self) -> List[Dict[str, Any]]:
        """Get statistics for all servers"""
        with self.lock:
            return [
                {
                    'url': s['url'],
                    'weight': s['weight'],
                    'active_connections': s['active_connections'],
                    'total_requests': s['total_requests'],
                    'response_time': s['response_time'],
                    'healthy': s['healthy'],
                    'last_health_check': s['last_health_check'].isoformat()
                }
                for s in self.servers
            ]


class HealthChecker:
    """
    Health checker for monitoring server health
    """
    
    def __init__(self, load_balancer: LoadBalancer):
        self.load_balancer = load_balancer
        self.running = False
        self.thread = None
        self.check_interval = 30  # seconds
    
    def start(self):
        """Start health checking"""
        if self.running:
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._health_check_loop, daemon=True)
        self.thread.start()
        logger.info("Health checker started")
    
    def stop(self):
        """Stop health checking"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)
        logger.info("Health checker stopped")
    
    def _health_check_loop(self):
        """Main health check loop"""
        while self.running:
            try:
                self._check_all_servers()
                time.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"Error in health check loop: {e}")
                time.sleep(self.check_interval)
    
    def _check_all_servers(self):
        """Check health of all servers"""
        for server in self.load_balancer.servers:
            try:
                is_healthy = self._check_server_health(server['url'])
                
                with self.load_balancer.lock:
                    server['healthy'] = is_healthy
                    server['last_health_check'] = timezone.now()
                
                if not is_healthy:
                    logger.warning(f"Server health check failed: {server['url']}")
                
            except Exception as e:
                logger.error(f"Health check error for {server['url']}: {e}")
                with self.load_balancer.lock:
                    server['healthy'] = False
    
    def _check_server_health(self, server_url: str) -> bool:
        """Check health of a single server"""
        try:
            import requests
            
            # Simple health check - could be enhanced with custom health endpoints
            health_url = f"{server_url.rstrip('/')}/health/"
            response = requests.get(health_url, timeout=5)
            
            return response.status_code == 200
            
        except Exception as e:
            logger.debug(f"Health check failed for {server_url}: {e}")
            return False


class AutoScaler:
    """
    Auto-scaling system for dynamic resource management
    """
    
    def __init__(self):
        self.scaling_rules = []
        self.scaling_history = []
        self.cooldown_period = 300  # 5 minutes
        self.last_scaling_action = None
        self.lock = threading.Lock()
    
    def add_scaling_rule(self, rule: 'ScalingRule'):
        """Add a scaling rule"""
        self.scaling_rules.append(rule)
        logger.info(f"Added scaling rule: {rule.name}")
    
    def check_scaling_conditions(self, metrics: Dict[str, Any]) -> List['ScalingAction']:
        """Check if any scaling conditions are met"""
        actions = []
        
        with self.lock:
            # Check cooldown period
            if (self.last_scaling_action and 
                (timezone.now() - self.last_scaling_action).total_seconds() < self.cooldown_period):
                return actions
            
            for rule in self.scaling_rules:
                if rule.should_trigger(metrics):
                    action = rule.get_scaling_action(metrics)
                    if action:
                        actions.append(action)
        
        return actions
    
    def execute_scaling_actions(self, actions: List['ScalingAction']):
        """Execute scaling actions"""
        for action in actions:
            try:
                success = action.execute()
                
                with self.lock:
                    self.scaling_history.append({
                        'action': action.action_type,
                        'target': action.target,
                        'timestamp': timezone.now(),
                        'success': success,
                        'details': action.details
                    })
                    
                    if success:
                        self.last_scaling_action = timezone.now()
                
                logger.info(f"Scaling action executed: {action.action_type} - Success: {success}")
                
            except Exception as e:
                logger.error(f"Failed to execute scaling action {action.action_type}: {e}")
    
    def get_scaling_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get scaling history"""
        with self.lock:
            return [
                {
                    'action': h['action'],
                    'target': h['target'],
                    'timestamp': h['timestamp'].isoformat(),
                    'success': h['success'],
                    'details': h['details']
                }
                for h in self.scaling_history[-limit:]
            ]


class ScalingRule:
    """
    Base class for scaling rules
    """
    
    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
    
    def should_trigger(self, metrics: Dict[str, Any]) -> bool:
        """Check if this rule should trigger scaling"""
        raise NotImplementedError
    
    def get_scaling_action(self, metrics: Dict[str, Any]) -> Optional['ScalingAction']:
        """Get the scaling action to execute"""
        raise NotImplementedError


class CPUScalingRule(ScalingRule):
    """
    CPU-based scaling rule
    """
    
    def __init__(self, name: str, cpu_threshold: float, action_type: str, 
                 duration_minutes: int = 5):
        super().__init__(name, f"Scale {action_type} when CPU > {cpu_threshold}%")
        self.cpu_threshold = cpu_threshold
        self.action_type = action_type  # 'scale_up' or 'scale_down'
        self.duration_minutes = duration_minutes
        self.trigger_start = None
    
    def should_trigger(self, metrics: Dict[str, Any]) -> bool:
        """Check CPU threshold"""
        cpu_usage = metrics.get('system', {}).get('cpu_percent', 0)
        
        if self.action_type == 'scale_up':
            condition_met = cpu_usage > self.cpu_threshold
        else:  # scale_down
            condition_met = cpu_usage < self.cpu_threshold
        
        if condition_met:
            if self.trigger_start is None:
                self.trigger_start = timezone.now()
            
            # Check if condition has been met for required duration
            duration = (timezone.now() - self.trigger_start).total_seconds() / 60
            return duration >= self.duration_minutes
        else:
            self.trigger_start = None
            return False
    
    def get_scaling_action(self, metrics: Dict[str, Any]) -> Optional['ScalingAction']:
        """Get CPU-based scaling action"""
        cpu_usage = metrics.get('system', {}).get('cpu_percent', 0)
        
        return ScalingAction(
            action_type=self.action_type,
            target='application_servers',
            details={
                'trigger': 'cpu_usage',
                'current_cpu': cpu_usage,
                'threshold': self.cpu_threshold,
                'rule': self.name
            }
        )


class ScalingAction:
    """
    Represents a scaling action to be executed
    """
    
    def __init__(self, action_type: str, target: str, details: Dict[str, Any]):
        self.action_type = action_type  # 'scale_up', 'scale_down'
        self.target = target  # 'application_servers', 'database', etc.
        self.details = details
        self.timestamp = timezone.now()
    
    def execute(self) -> bool:
        """Execute the scaling action"""
        try:
            if self.action_type == 'scale_up':
                return self._scale_up()
            elif self.action_type == 'scale_down':
                return self._scale_down()
            else:
                logger.error(f"Unknown scaling action: {self.action_type}")
                return False
        except Exception as e:
            logger.error(f"Scaling action execution failed: {e}")
            return False
    
    def _scale_up(self) -> bool:
        """Scale up resources"""
        if self.target == 'application_servers':
            return self._scale_up_app_servers()
        elif self.target == 'database':
            return self._scale_up_database()
        else:
            logger.warning(f"Scale up not implemented for target: {self.target}")
            return False
    
    def _scale_down(self) -> bool:
        """Scale down resources"""
        if self.target == 'application_servers':
            return self._scale_down_app_servers()
        elif self.target == 'database':
            return self._scale_down_database()
        else:
            logger.warning(f"Scale down not implemented for target: {self.target}")
            return False
    
    def _scale_up_app_servers(self) -> bool:
        """Scale up application servers"""
        # This would integrate with container orchestration (Docker, Kubernetes)
        # or cloud services (AWS Auto Scaling, etc.)
        logger.info("Scaling up application servers")
        
        # Placeholder implementation
        # In real implementation, this would:
        # 1. Check current server count
        # 2. Launch new server instances
        # 3. Add them to load balancer
        # 4. Wait for health checks to pass
        
        return True
    
    def _scale_down_app_servers(self) -> bool:
        """Scale down application servers"""
        logger.info("Scaling down application servers")
        
        # Placeholder implementation
        # In real implementation, this would:
        # 1. Identify servers to remove
        # 2. Drain connections gracefully
        # 3. Remove from load balancer
        # 4. Terminate server instances
        
        return True
    
    def _scale_up_database(self) -> bool:
        """Scale up database resources"""
        logger.info("Scaling up database resources")
        
        # This could involve:
        # 1. Increasing connection pool size
        # 2. Adding read replicas
        # 3. Upgrading instance size
        
        return True
    
    def _scale_down_database(self) -> bool:
        """Scale down database resources"""
        logger.info("Scaling down database resources")
        
        # This could involve:
        # 1. Reducing connection pool size
        # 2. Removing read replicas
        # 3. Downgrading instance size
        
        return True


class ResourceMonitor:
    """
    Monitor resource usage for scaling decisions
    """
    
    def __init__(self):
        self.metrics_history = []
        self.max_history = 1000
        self.lock = threading.Lock()
    
    def record_metrics(self, metrics: Dict[str, Any]):
        """Record metrics for analysis"""
        with self.lock:
            self.metrics_history.append({
                'timestamp': timezone.now(),
                'metrics': metrics
            })
            
            # Keep only recent history
            if len(self.metrics_history) > self.max_history:
                self.metrics_history = self.metrics_history[-self.max_history:]
    
    def get_average_metrics(self, duration_minutes: int = 10) -> Dict[str, Any]:
        """Get average metrics over specified duration"""
        cutoff_time = timezone.now() - timedelta(minutes=duration_minutes)
        
        with self.lock:
            recent_metrics = [
                m['metrics'] for m in self.metrics_history
                if m['timestamp'] >= cutoff_time
            ]
        
        if not recent_metrics:
            return {}
        
        # Calculate averages
        avg_metrics = {}
        
        # System metrics
        cpu_values = [m.get('system', {}).get('cpu_percent', 0) for m in recent_metrics]
        memory_values = [m.get('system', {}).get('memory_percent', 0) for m in recent_metrics]
        
        if cpu_values:
            avg_metrics['avg_cpu_percent'] = sum(cpu_values) / len(cpu_values)
        if memory_values:
            avg_metrics['avg_memory_percent'] = sum(memory_values) / len(memory_values)
        
        # Database metrics
        db_conn_values = [
            m.get('database', {}).get('active_connections', 0) 
            for m in recent_metrics
        ]
        if db_conn_values:
            avg_metrics['avg_db_connections'] = sum(db_conn_values) / len(db_conn_values)
        
        return avg_metrics
    
    def detect_trends(self, metric_name: str, duration_minutes: int = 30) -> str:
        """Detect trends in metrics (increasing, decreasing, stable)"""
        cutoff_time = timezone.now() - timedelta(minutes=duration_minutes)
        
        with self.lock:
            recent_data = [
                (m['timestamp'], m['metrics'])
                for m in self.metrics_history
                if m['timestamp'] >= cutoff_time
            ]
        
        if len(recent_data) < 3:
            return 'insufficient_data'
        
        # Extract metric values
        values = []
        for timestamp, metrics in recent_data:
            value = self._extract_metric_value(metrics, metric_name)
            if value is not None:
                values.append(value)
        
        if len(values) < 3:
            return 'insufficient_data'
        
        # Simple trend detection
        first_half = values[:len(values)//2]
        second_half = values[len(values)//2:]
        
        first_avg = sum(first_half) / len(first_half)
        second_avg = sum(second_half) / len(second_half)
        
        change_percent = ((second_avg - first_avg) / first_avg) * 100 if first_avg > 0 else 0
        
        if change_percent > 10:
            return 'increasing'
        elif change_percent < -10:
            return 'decreasing'
        else:
            return 'stable'
    
    def _extract_metric_value(self, metrics: Dict[str, Any], metric_path: str) -> Optional[float]:
        """Extract metric value from nested dictionary"""
        keys = metric_path.split('.')
        current = metrics
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        
        try:
            return float(current)
        except (ValueError, TypeError):
            return None


# Global instances
load_balancer = LoadBalancer()
health_checker = HealthChecker(load_balancer)
auto_scaler = AutoScaler()
resource_monitor = ResourceMonitor()

# Add default scaling rules
auto_scaler.add_scaling_rule(
    CPUScalingRule(
        name="cpu_scale_up",
        cpu_threshold=80,
        action_type="scale_up",
        duration_minutes=5
    )
)

auto_scaler.add_scaling_rule(
    CPUScalingRule(
        name="cpu_scale_down",
        cpu_threshold=30,
        action_type="scale_down",
        duration_minutes=10
    )
)


# Health check endpoint
@require_http_methods(["GET"])
@csrf_exempt
def health_check(request):
    """Health check endpoint for load balancer"""
    try:
        # Basic health checks
        from django.db import connection
        
        # Database check
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        
        # Cache check
        cache.set('health_check', 'ok', timeout=60)
        if cache.get('health_check') != 'ok':
            raise Exception("Cache not working")
        
        return HttpResponse(
            json.dumps({
                'status': 'healthy',
                'timestamp': timezone.now().isoformat()
            }),
            content_type='application/json'
        )
        
    except Exception as e:
        return HttpResponse(
            json.dumps({
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': timezone.now().isoformat()
            }),
            content_type='application/json',
            status=503
        )


# Export main components
__all__ = [
    'LoadBalancer',
    'HealthChecker',
    'AutoScaler',
    'ScalingRule',
    'CPUScalingRule',
    'ScalingAction',
    'ResourceMonitor',
    'load_balancer',
    'health_checker',
    'auto_scaler',
    'resource_monitor',
    'health_check'
]