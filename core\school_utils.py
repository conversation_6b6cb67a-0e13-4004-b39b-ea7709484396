"""
Utility functions for school selection and management with performance optimizations.
"""

import logging
from datetime import datetime, timedelta
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.conf import settings
from django.core.cache import cache
from django.db import connection
from core.models import School

logger = logging.getLogger(__name__)
User = get_user_model()

# Session keys for school-related data
SESSION_SCHOOL_ID_KEY = 'selected_school_id'
SESSION_SCHOOL_TIMESTAMP_KEY = 'school_selection_timestamp'
SESSION_SCHOOL_VALIDATION_KEY = 'school_validation_hash'

# Session timeout for school selection (in hours)
SCHOOL_SESSION_TIMEOUT_HOURS = getattr(settings, 'SCHOOL_SESSION_TIMEOUT_HOURS', 24)


def get_user_schools(user):
    """
    Get schools that the user has access to based on their role and permissions with advanced caching and query optimization.
    
    Args:
        user: Django User instance
        
    Returns:
        QuerySet of School objects the user can access
    """
    if not user or not user.is_authenticated:
        return School.objects.none()
    
    # Create hierarchical cache key with user attributes for better cache invalidation
    user_attrs_hash = hash(f"{user.id}_{user.is_superuser}_{user.is_staff}_{getattr(user, 'user_type', 'none')}")
    cache_key = f"user_schools_v2_{user.id}_{user_attrs_hash}"
    
    # Try multi-level cache (L1: in-memory, L2: cache backend)
    cached_schools = cache.get(cache_key)
    
    if cached_schools is not None:
        # Validate cached data is still fresh
        if isinstance(cached_schools, dict):
            cache_timestamp = cached_schools.get('timestamp')
            if cache_timestamp and (timezone.now() - cache_timestamp).total_seconds() < 1800:  # 30 minutes
                return cached_schools.get('schools', School.objects.none())
            else:
                # Cache expired, remove it
                cache.delete(cache_key)
        else:
            # Legacy cache format, return as-is but update cache
            cache_data = {
                'schools': cached_schools,
                'timestamp': timezone.now(),
                'user_id': user.id
            }
            cache.set(cache_key, cache_data, 1800)  # 30 minutes
            return cached_schools
    
    if user.is_superuser:
        # Superusers can access all active schools with optimized query
        schools = School.objects.filter(
            is_active=True
        ).order_by('name')
    else:
        # For regular users, determine school access with optimized queries
        schools = School.objects.none()
        
        try:
            # Use optimized single query approach with joins
            user_type = getattr(user, 'user_type', None)
            
            # Build query conditions efficiently
            from django.db.models import Q
            school_conditions = Q(is_active=True)
            user_school_ids = set()
            
            # Check user relationships in batch
            if hasattr(user, 'student_profile') and user.student_profile:
                try:
                    user_school_ids.add(user.student_profile.school.id)
                except AttributeError:
                    pass
            
            if hasattr(user, 'employee') and user.employee:
                try:
                    user_school_ids.add(user.employee.school.id)
                except AttributeError:
                    pass
            
            if hasattr(user, 'teacher_profile') and user.teacher_profile:
                try:
                    user_school_ids.add(user.teacher_profile.school.id)
                except AttributeError:
                    pass
            
            # If user has specific school associations, use them
            if user_school_ids:
                school_conditions &= Q(id__in=user_school_ids)
                schools = School.objects.filter(school_conditions).select_related().order_by('name')
            # For privileged user types, allow access to all schools
            elif user_type in ['teacher', 'staff', 'admin', 'accountant', 'principal', 'librarian'] or user.is_staff:
                schools = School.objects.filter(school_conditions).select_related().order_by('name')
            else:
                # For other user types, provide limited access
                schools = School.objects.filter(school_conditions).select_related()[:1]
                
        except Exception as e:
            logger.error(f"Error determining user schools for {user.username}: {e}")
            # Fallback to first active school with error caching
            schools = School.objects.filter(is_active=True).select_related()[:1]
            
            # Cache error state for shorter time to allow recovery
            error_cache_data = {
                'schools': schools,
                'timestamp': timezone.now(),
                'user_id': user.id,
                'error': True
            }
            cache.set(cache_key, error_cache_data, 300)  # 5 minutes for errors
            return schools
    
    # Cache with metadata for better cache management
    cache_data = {
        'schools': schools,
        'timestamp': timezone.now(),
        'user_id': user.id,
        'is_superuser': user.is_superuser,
        'user_type': getattr(user, 'user_type', None)
    }
    
    # Cache for 30 minutes (longer cache for better performance)
    cache.set(cache_key, cache_data, 1800)
    
    return schools


def user_has_school_access(user, school):
    """
    Check if user has access to the specified school.
    
    Args:
        user: Django User instance
        school: School instance
        
    Returns:
        bool: True if user has access, False otherwise
    """
    if not user or not user.is_authenticated:
        return False
    
    if user.is_superuser:
        return True
    
    user_schools = get_user_schools(user)
    return user_schools.filter(id=school.id).exists()


def validate_school_access_permission(user, school, operation='read'):
    """
    Validate user permissions for specific school operations.
    
    Args:
        user: Django User instance
        school: School instance
        operation: Type of operation ('read', 'write', 'admin')
        
    Returns:
        tuple: (bool, str) - (has_permission, error_message)
    """
    if not user or not user.is_authenticated:
        return False, "Authentication required"
    
    if not school or not school.is_active:
        return False, "Invalid or inactive school"
    
    # Superusers have all permissions
    if user.is_superuser:
        return True, ""
    
    # Check basic school access
    if not user_has_school_access(user, school):
        return False, "No access to this school"
    
    # Check operation-specific permissions
    if operation == 'admin':
        # Only admin users and superusers can perform admin operations
        if not (user.is_staff or getattr(user, 'user_type', None) in ['admin', 'principal']):
            return False, "Administrative privileges required"
    
    elif operation == 'write':
        # Teachers, staff, admin can write
        if not (user.is_staff or getattr(user, 'user_type', None) in ['teacher', 'staff', 'admin', 'principal', 'librarian']):
            return False, "Write privileges required"
    
    # 'read' operation is allowed for all authenticated users with school access
    return True, ""


def get_user_role_in_school(user, school):
    """
    Determine user's role within a specific school.
    
    Args:
        user: Django User instance
        school: School instance
        
    Returns:
        str: User role ('student', 'teacher', 'staff', 'admin', 'parent', 'superuser', 'none')
    """
    if not user or not user.is_authenticated:
        return 'none'
    
    if user.is_superuser:
        return 'superuser'
    
    if not user_has_school_access(user, school):
        return 'none'
    
    # Check user type attribute
    user_type = getattr(user, 'user_type', None)
    if user_type:
        return user_type
    
    # Check related profiles
    try:
        if hasattr(user, 'student_profile') and user.student_profile:
            if user.student_profile.school == school:
                return 'student'
        
        if hasattr(user, 'employee') and user.employee:
            if user.employee.school == school:
                return user.employee.position or 'staff'
        
        if hasattr(user, 'teacher_profile') and user.teacher_profile:
            if user.teacher_profile.school == school:
                return 'teacher'
        
        if hasattr(user, 'parent_profile') and user.parent_profile:
            if user.parent_profile.school == school:
                return 'parent'
    except Exception as e:
        logger.warning(f"Error determining user role: {e}")
    
    # Default to staff if user has access but no specific role
    return 'staff' if user.is_staff else 'none'


def check_api_access_permission(user, school, endpoint_type='general'):
    """
    Check if user has permission to access specific API endpoints.
    
    Args:
        user: Django User instance
        school: School instance
        endpoint_type: Type of API endpoint ('general', 'library', 'academic', 'finance', 'admin')
        
    Returns:
        tuple: (bool, str) - (has_permission, error_message)
    """
    if not user or not user.is_authenticated:
        return False, "Authentication required"
    
    # Basic school access check
    has_access, error_msg = validate_school_access_permission(user, school, 'read')
    if not has_access:
        return False, error_msg
    
    user_role = get_user_role_in_school(user, school)
    
    # Define role-based API access permissions
    api_permissions = {
        'general': ['student', 'teacher', 'staff', 'admin', 'parent', 'superuser'],
        'library': ['student', 'teacher', 'staff', 'admin', 'librarian', 'superuser'],
        'academic': ['student', 'teacher', 'staff', 'admin', 'superuser'],
        'finance': ['staff', 'admin', 'accountant', 'superuser'],
        'admin': ['admin', 'principal', 'superuser'],
        'hr': ['staff', 'admin', 'hr_manager', 'superuser'],
        'reports': ['teacher', 'staff', 'admin', 'superuser']
    }
    
    allowed_roles = api_permissions.get(endpoint_type, ['superuser'])
    
    if user_role not in allowed_roles:
        return False, f"Insufficient privileges for {endpoint_type} operations"
    
    return True, ""


def validate_session_security(request):
    """
    Validate session security and detect potential security issues.
    
    Args:
        request: Django HttpRequest instance
        
    Returns:
        tuple: (bool, str) - (is_secure, warning_message)
    """
    if not request.user.is_authenticated:
        return False, "User not authenticated"
    
    try:
        # Check for session hijacking indicators
        current_ip = get_client_ip(request)
        session_ip = request.session.get('session_ip')
        
        if session_ip and session_ip != current_ip:
            logger.warning(f"IP address change detected for user {request.user.username}: {session_ip} -> {current_ip}")
            return False, "Session security violation detected"
        
        # Store current IP if not set
        if not session_ip:
            request.session['session_ip'] = current_ip
        
        # Check user agent consistency
        current_ua = request.META.get('HTTP_USER_AGENT', '')
        session_ua = request.session.get('session_user_agent')
        
        if session_ua and session_ua != current_ua:
            logger.warning(f"User agent change detected for user {request.user.username}")
            return False, "Session security violation detected"
        
        # Store current user agent if not set
        if not session_ua:
            request.session['session_user_agent'] = current_ua
        
        # Check session age
        session_start = request.session.get('session_start_time')
        if not session_start:
            request.session['session_start_time'] = timezone.now().isoformat()
        else:
            try:
                start_time = datetime.fromisoformat(session_start)
                session_age = timezone.now() - start_time
                
                # Warn if session is very old (more than 24 hours)
                if session_age > timedelta(hours=24):
                    return True, "Long-running session detected"
            except ValueError:
                logger.warning("Invalid session start time format")
        
        return True, ""
        
    except Exception as e:
        logger.error(f"Error validating session security: {e}")
        return False, "Session validation error"


def get_client_ip(request):
    """
    Get client IP address from request headers.
    
    Args:
        request: Django HttpRequest instance
        
    Returns:
        str: Client IP address
    """
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()
    else:
        ip = request.META.get('REMOTE_ADDR', '')
    return ip


def log_security_event(user, event_type, details, school=None, ip_address=None):
    """
    Log security-related events for audit purposes.
    
    Args:
        user: Django User instance
        event_type: Type of security event
        details: Event details
        school: School instance (optional)
        ip_address: IP address (optional)
    """
    try:
        from core.models import AuditLog
        
        AuditLog.objects.create(
            user=user,
            action=f"SECURITY: {event_type}",
            model_name='Security',
            object_id=str(user.id) if user else 'anonymous',
            object_repr=details,
            ip_address=ip_address or '',
            school=school
        )
        
        logger.warning(f"Security event - {event_type}: {details} (User: {user.username if user else 'anonymous'})")
        
    except Exception as e:
        logger.error(f"Error logging security event: {e}")


def require_school_permission(operation='read'):
    """
    Decorator to require specific school permissions for views.
    
    Args:
        operation: Required operation level ('read', 'write', 'admin')
    """
    def decorator(view_func):
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                log_security_event(None, 'UNAUTHORIZED_ACCESS', f"Unauthenticated access to {request.path}", ip_address=get_client_ip(request))
                return JsonResponse({'error': 'Authentication required'}, status=401)
            
            school = getattr(request, 'school', None)
            if not school:
                log_security_event(request.user, 'NO_SCHOOL_CONTEXT', f"No school context for {request.path}", ip_address=get_client_ip(request))
                return JsonResponse({'error': 'School context required'}, status=400)
            
            has_permission, error_msg = validate_school_access_permission(request.user, school, operation)
            if not has_permission:
                log_security_event(request.user, 'PERMISSION_DENIED', f"Access denied to {request.path}: {error_msg}", school=school, ip_address=get_client_ip(request))
                return JsonResponse({'error': error_msg}, status=403)
            
            # Validate session security
            is_secure, warning_msg = validate_session_security(request)
            if not is_secure:
                log_security_event(request.user, 'SESSION_SECURITY_VIOLATION', warning_msg, school=school, ip_address=get_client_ip(request))
                return JsonResponse({'error': 'Session security violation'}, status=403)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def require_api_permission(endpoint_type='general'):
    """
    Decorator to require specific API permissions.
    
    Args:
        endpoint_type: Type of API endpoint
    """
    def decorator(view_func):
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                log_security_event(None, 'API_UNAUTHORIZED_ACCESS', f"Unauthenticated API access to {request.path}", ip_address=get_client_ip(request))
                return JsonResponse({'error': 'Authentication required'}, status=401)
            
            school = getattr(request, 'school', None)
            if not school:
                log_security_event(request.user, 'API_NO_SCHOOL_CONTEXT', f"No school context for API {request.path}", ip_address=get_client_ip(request))
                return JsonResponse({'error': 'School context required'}, status=400)
            
            has_permission, error_msg = check_api_access_permission(request.user, school, endpoint_type)
            if not has_permission:
                log_security_event(request.user, 'API_PERMISSION_DENIED', f"API access denied to {request.path}: {error_msg}", school=school, ip_address=get_client_ip(request))
                return JsonResponse({'error': error_msg}, status=403)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def validate_session_school_data(request):
    """
    Validate school data stored in session for integrity and expiration.
    
    Args:
        request: Django HttpRequest instance
        
    Returns:
        bool: True if session data is valid, False otherwise
    """
    if not request.user.is_authenticated:
        return False
    
    try:
        # Check if required session keys exist
        school_id = request.session.get(SESSION_SCHOOL_ID_KEY)
        timestamp = request.session.get(SESSION_SCHOOL_TIMESTAMP_KEY)
        
        if not school_id or not timestamp:
            return False
        
        # Validate timestamp format and expiration
        try:
            selection_time = datetime.fromisoformat(timestamp)
            current_time = timezone.now()
            
            # Check if session has expired
            if current_time - selection_time > timedelta(hours=SCHOOL_SESSION_TIMEOUT_HOURS):
                logger.info(f"School session expired for user {request.user.username}")
                return False
                
        except (ValueError, TypeError) as e:
            logger.warning(f"Invalid timestamp in school session: {e}")
            return False
        
        # Validate school ID format (UUID or integer)
        try:
            # Try to validate as UUID first (for newer systems)
            import uuid
            uuid.UUID(str(school_id))
        except (ValueError, TypeError):
            try:
                # Fallback to integer validation (for legacy systems)
                int(school_id)
            except (ValueError, TypeError):
                logger.warning(f"Invalid school ID format in session: {school_id}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"Error validating session school data: {e}")
        return False


def cleanup_invalid_school_session(request):
    """
    Clean up invalid or expired school selection data from session.
    
    Args:
        request: Django HttpRequest instance
    """
    try:
        session_keys_to_remove = [
            SESSION_SCHOOL_ID_KEY,
            SESSION_SCHOOL_TIMESTAMP_KEY,
            SESSION_SCHOOL_VALIDATION_KEY
        ]
        
        for key in session_keys_to_remove:
            if key in request.session:
                del request.session[key]
        
        # Clear school from request if present
        if hasattr(request, 'school'):
            request.school = None
            
        logger.info(f"Cleaned up invalid school session for user {request.user.username}")
        
    except Exception as e:
        logger.error(f"Error cleaning up school session: {e}")


def get_current_school(request):
    """
    Get the currently selected school from request or session with advanced validation and multi-level caching.
    
    Args:
        request: Django HttpRequest instance
        
    Returns:
        School instance or None
    """
    # First check if school is already set on request (L0 cache)
    if hasattr(request, 'school') and request.school:
        return request.school
    
    # Try to get from session with validation
    if request.user.is_authenticated:
        user_id = request.user.id
        session_key = getattr(request.session, 'session_key', 'no_session')
        
        # Create hierarchical cache keys for better performance
        cache_key = f"current_school_v2_{user_id}_{session_key[:8]}"
        
        # Try L1 cache first with metadata validation
        cached_data = cache.get(cache_key)
        if cached_data and isinstance(cached_data, dict):
            cached_school = cached_data.get('school')
            cache_timestamp = cached_data.get('timestamp')
            
            if (isinstance(cached_school, School) and cached_school.is_active and
                cache_timestamp and (timezone.now() - cache_timestamp).total_seconds() < 900):  # 15 minutes
                
                # Update access time for LRU-like behavior
                cached_data['last_access'] = timezone.now()
                cache.set(cache_key, cached_data, 1200)  # Extend to 20 minutes
                return cached_school
            else:
                # Cache expired or invalid, clear it
                cache.delete(cache_key)
        
        # Validate session data with optimized validation
        if not validate_session_school_data(request):
            cleanup_invalid_school_session(request)
            cache.delete(cache_key)
            return None
        
        school_id = request.session.get(SESSION_SCHOOL_ID_KEY)
        if school_id:
            try:
                # Use optimized query with select_related
                school = School.objects.select_related().get(id=school_id, is_active=True)
                
                # Batch access validation with caching
                access_cache_key = f"school_access_{user_id}_{school.id}"
                has_access = cache.get(access_cache_key)
                
                if has_access is None:
                    has_access = user_has_school_access(request.user, school)
                    # Cache access result for 10 minutes
                    cache.set(access_cache_key, has_access, 600)
                
                if has_access:
                    # Cache the school with metadata for better management
                    cache_data = {
                        'school': school,
                        'timestamp': timezone.now(),
                        'last_access': timezone.now(),
                        'user_id': user_id,
                        'session_key': session_key[:8]
                    }
                    cache.set(cache_key, cache_data, 1200)  # 20 minutes
                    return school
                else:
                    logger.warning(f"User {request.user.username} lost access to school {school.name}")
                    cleanup_invalid_school_session(request)
                    cache.delete(cache_key)
                    cache.delete(access_cache_key)
                    
            except School.DoesNotExist:
                logger.warning(f"School with ID {school_id} no longer exists")
                cleanup_invalid_school_session(request)
                cache.delete(cache_key)
                
                # Cache negative result to prevent repeated DB queries
                negative_cache_data = {
                    'school': None,
                    'timestamp': timezone.now(),
                    'error': 'school_not_found',
                    'user_id': user_id
                }
                cache.set(cache_key, negative_cache_data, 300)  # 5 minutes for negative cache
            except Exception as e:
                logger.error(f"Error fetching current school: {e}")
                cleanup_invalid_school_session(request)
                cache.delete(cache_key)
    
    return None


def set_current_school(request, school):
    """
    Set the current school in the user's session with proper validation and metadata.
    
    Args:
        request: Django HttpRequest instance
        school: School instance
        
    Returns:
        bool: True if successful, False otherwise
    """
    if not request.user.is_authenticated:
        logger.warning("Attempted to set school for unauthenticated user")
        return False
    
    # Validate school instance
    if not isinstance(school, School):
        logger.error("Invalid school instance provided")
        return False
    
    if not school.is_active:
        logger.warning(f"Attempted to set inactive school: {school.name}")
        return False
    
    # Verify user has access to this school
    if not user_has_school_access(request.user, school):
        logger.warning(f"User {request.user.username} denied access to school {school.name}")
        return False
    
    try:
        # Set school data in session with metadata
        request.session[SESSION_SCHOOL_ID_KEY] = str(school.id)
        request.session[SESSION_SCHOOL_TIMESTAMP_KEY] = timezone.now().isoformat()
        
        # Create validation hash for integrity checking
        validation_data = f"{school.id}_{request.user.id}_{timezone.now().date()}"
        request.session[SESSION_SCHOOL_VALIDATION_KEY] = hash(validation_data)
        
        # Set school on request for immediate use
        request.school = school
        
        logger.info(f"Successfully set school {school.name} for user {request.user.username}")
        return True
        
    except Exception as e:
        logger.error(f"Error setting current school: {e}")
        return False


def clear_current_school(request):
    """
    Clear the current school selection from session with proper cleanup.
    
    Args:
        request: Django HttpRequest instance
    """
    try:
        # Remove all school-related session data
        session_keys_to_remove = [
            SESSION_SCHOOL_ID_KEY,
            SESSION_SCHOOL_TIMESTAMP_KEY,
            SESSION_SCHOOL_VALIDATION_KEY
        ]
        
        for key in session_keys_to_remove:
            if key in request.session:
                del request.session[key]
        
        # Clear school from request
        if hasattr(request, 'school'):
            request.school = None
        
        # Clear school context if present
        if hasattr(request, 'school_context'):
            request.school_context = {
                'current_school': None,
                'school_name': None,
                'school_code': None,
                'school_id': None,
            }
        
        logger.info(f"Cleared school selection for user {getattr(request.user, 'username', 'anonymous')}")
        
    except Exception as e:
        logger.error(f"Error clearing current school: {e}")


def get_school_session_info(request):
    """
    Get detailed information about the current school session.
    
    Args:
        request: Django HttpRequest instance
        
    Returns:
        dict: Session information including validity, timestamp, etc.
    """
    if not request.user.is_authenticated:
        return {
            'has_session': False,
            'is_valid': False,
            'school_id': None,
            'timestamp': None,
            'time_remaining': None,
            'error': 'User not authenticated'
        }
    
    try:
        school_id = request.session.get(SESSION_SCHOOL_ID_KEY)
        timestamp_str = request.session.get(SESSION_SCHOOL_TIMESTAMP_KEY)
        
        if not school_id or not timestamp_str:
            return {
                'has_session': False,
                'is_valid': False,
                'school_id': None,
                'timestamp': None,
                'time_remaining': None,
                'error': 'No school session data'
            }
        
        # Parse timestamp
        try:
            timestamp = datetime.fromisoformat(timestamp_str)
        except ValueError:
            return {
                'has_session': True,
                'is_valid': False,
                'school_id': school_id,
                'timestamp': None,
                'time_remaining': None,
                'error': 'Invalid timestamp format'
            }
        
        # Calculate time remaining
        current_time = timezone.now()
        elapsed_time = current_time - timestamp
        timeout_delta = timedelta(hours=SCHOOL_SESSION_TIMEOUT_HOURS)
        time_remaining = timeout_delta - elapsed_time
        
        is_valid = validate_session_school_data(request)
        
        return {
            'has_session': True,
            'is_valid': is_valid,
            'school_id': school_id,
            'timestamp': timestamp,
            'time_remaining': time_remaining if time_remaining.total_seconds() > 0 else timedelta(0),
            'error': None if is_valid else 'Session expired or invalid'
        }
        
    except Exception as e:
        logger.error(f"Error getting school session info: {e}")
        return {
            'has_session': False,
            'is_valid': False,
            'school_id': None,
            'timestamp': None,
            'time_remaining': None,
            'error': str(e)
        }


def refresh_school_session(request):
    """
    Refresh the current school session timestamp to extend its validity.
    
    Args:
        request: Django HttpRequest instance
        
    Returns:
        bool: True if successfully refreshed, False otherwise
    """
    if not request.user.is_authenticated:
        return False
    
    current_school = get_current_school(request)
    if not current_school:
        return False
    
    try:
        # Update timestamp to current time
        request.session[SESSION_SCHOOL_TIMESTAMP_KEY] = timezone.now().isoformat()
        
        # Update validation hash
        validation_data = f"{current_school.id}_{request.user.id}_{timezone.now().date()}"
        request.session[SESSION_SCHOOL_VALIDATION_KEY] = hash(validation_data)
        
        logger.info(f"Refreshed school session for user {request.user.username}")
        return True
        
    except Exception as e:
        logger.error(f"Error refreshing school session: {e}")
        return False


def cleanup_expired_sessions():
    """
    Utility function to clean up expired school sessions from the database.
    This can be called by a management command or scheduled task.
    
    Note: This function would need to be implemented with direct database access
    to session data, which is complex. For now, it serves as a placeholder
    for future implementation.
    """
    # This would require direct access to session storage
    # Implementation would depend on session backend (database, cache, etc.)
    logger.info("Session cleanup functionality placeholder - implement based on session backend")


def get_school_context_data(request):
    """
    Get comprehensive school context data for templates with session information.
    
    Args:
        request: Django HttpRequest instance
        
    Returns:
        dict: Context data for templates
    """
    context = {
        'current_school': None,
        'schools': School.objects.none(),
        'has_school_selection': False,
        'school_count': 0,
        'session_info': None,
    }
    
    if request.user.is_authenticated:
        schools = get_user_schools(request.user)
        context.update({
            'schools': schools,
            'school_count': schools.count(),
            'has_school_selection': schools.count() > 1,
        })
        
        current_school = get_current_school(request)
        if current_school:
            context['current_school'] = current_school
        
        # Add session information
        context['session_info'] = get_school_session_info(request)
    
    return context