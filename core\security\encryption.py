"""
Encryption utilities for School ERP System
"""
import base64
import hashlib
import secrets
from typing import Union, Optional, Tuple
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from django.conf import settings
from django.core.exceptions import ImproperlyConfigured
import logging

logger = logging.getLogger(__name__)


class EncryptionManager:
    """
    Centralized encryption management for the School ERP system
    """
    
    def __init__(self):
        self._fernet_key = None
        self._private_key = None
        self._public_key = None
        self._initialize_keys()
    
    def _initialize_keys(self):
        """Initialize encryption keys"""
        try:
            # Get or generate Fernet key for symmetric encryption
            fernet_key = getattr(settings, 'FERNET_KEY', None)
            if fernet_key:
                self._fernet_key = Fernet(fernet_key.encode() if isinstance(fernet_key, str) else fernet_key)
            else:
                # Generate a new key (should be stored securely in production)
                key = Fernet.generate_key()
                self._fernet_key = Fernet(key)
                logger.warning("Generated new Fernet key. Store this securely: %s", key.decode())
            
            # Initialize RSA keys for asymmetric encryption
            self._initialize_rsa_keys()
            
        except Exception as e:
            logger.error(f"Failed to initialize encryption keys: {e}")
            raise ImproperlyConfigured("Encryption initialization failed")
    
    def _initialize_rsa_keys(self):
        """Initialize RSA key pair"""
        try:
            # Try to load existing keys from settings
            private_key_pem = getattr(settings, 'RSA_PRIVATE_KEY', None)
            public_key_pem = getattr(settings, 'RSA_PUBLIC_KEY', None)
            
            if private_key_pem and public_key_pem:
                self._private_key = serialization.load_pem_private_key(
                    private_key_pem.encode(),
                    password=None
                )
                self._public_key = serialization.load_pem_public_key(
                    public_key_pem.encode()
                )
            else:
                # Generate new RSA key pair
                self._private_key = rsa.generate_private_key(
                    public_exponent=65537,
                    key_size=2048
                )
                self._public_key = self._private_key.public_key()
                
                # Log the keys (should be stored securely in production)
                private_pem = self._private_key.private_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PrivateFormat.PKCS8,
                    encryption_algorithm=serialization.NoEncryption()
                )
                
                public_pem = self._public_key.public_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PublicFormat.SubjectPublicKeyInfo
                )
                
                logger.warning("Generated new RSA keys. Store these securely:")
                logger.warning("Private key: %s", private_pem.decode())
                logger.warning("Public key: %s", public_pem.decode())
                
        except Exception as e:
            logger.error(f"Failed to initialize RSA keys: {e}")
    
    def encrypt_symmetric(self, data: Union[str, bytes]) -> str:
        """
        Encrypt data using symmetric encryption (Fernet)
        Returns base64 encoded encrypted data
        """
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            encrypted_data = self._fernet_key.encrypt(data)
            return base64.b64encode(encrypted_data).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Symmetric encryption failed: {e}")
            raise
    
    def decrypt_symmetric(self, encrypted_data: str) -> str:
        """
        Decrypt data using symmetric encryption (Fernet)
        """
        try:
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
            decrypted_data = self._fernet_key.decrypt(encrypted_bytes)
            return decrypted_data.decode('utf-8')
            
        except Exception as e:
            logger.error(f"Symmetric decryption failed: {e}")
            raise
    
    def encrypt_asymmetric(self, data: Union[str, bytes]) -> str:
        """
        Encrypt data using asymmetric encryption (RSA public key)
        Returns base64 encoded encrypted data
        """
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            encrypted_data = self._public_key.encrypt(
                data,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            
            return base64.b64encode(encrypted_data).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Asymmetric encryption failed: {e}")
            raise
    
    def decrypt_asymmetric(self, encrypted_data: str) -> str:
        """
        Decrypt data using asymmetric encryption (RSA private key)
        """
        try:
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
            
            decrypted_data = self._private_key.decrypt(
                encrypted_bytes,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            
            return decrypted_data.decode('utf-8')
            
        except Exception as e:
            logger.error(f"Asymmetric decryption failed: {e}")
            raise
    
    def hash_password(self, password: str, salt: Optional[bytes] = None) -> Tuple[str, str]:
        """
        Hash a password using PBKDF2
        Returns (hashed_password, salt) as base64 encoded strings
        """
        try:
            if salt is None:
                salt = secrets.token_bytes(32)
            elif isinstance(salt, str):
                salt = base64.b64decode(salt.encode('utf-8'))
            
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            
            hashed_password = kdf.derive(password.encode('utf-8'))
            
            return (
                base64.b64encode(hashed_password).decode('utf-8'),
                base64.b64encode(salt).decode('utf-8')
            )
            
        except Exception as e:
            logger.error(f"Password hashing failed: {e}")
            raise
    
    def verify_password(self, password: str, hashed_password: str, salt: str) -> bool:
        """
        Verify a password against its hash
        """
        try:
            computed_hash, _ = self.hash_password(password, salt)
            return secrets.compare_digest(computed_hash, hashed_password)
            
        except Exception as e:
            logger.error(f"Password verification failed: {e}")
            return False
    
    def generate_token(self, length: int = 32) -> str:
        """
        Generate a secure random token
        """
        return secrets.token_urlsafe(length)
    
    def hash_data(self, data: Union[str, bytes], algorithm: str = 'sha256') -> str:
        """
        Hash data using specified algorithm
        """
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            if algorithm == 'sha256':
                hash_obj = hashlib.sha256(data)
            elif algorithm == 'sha512':
                hash_obj = hashlib.sha512(data)
            elif algorithm == 'md5':
                hash_obj = hashlib.md5(data)
            else:
                raise ValueError(f"Unsupported hash algorithm: {algorithm}")
            
            return hash_obj.hexdigest()
            
        except Exception as e:
            logger.error(f"Data hashing failed: {e}")
            raise


class FieldEncryption:
    """
    Field-level encryption for sensitive data
    """
    
    def __init__(self, encryption_manager: EncryptionManager):
        self.encryption_manager = encryption_manager
    
    def encrypt_field(self, value: str, field_name: str = "") -> str:
        """
        Encrypt a field value
        """
        try:
            if not value:
                return value
            
            # Add field identifier for additional security
            data_to_encrypt = f"{field_name}:{value}" if field_name else value
            return self.encryption_manager.encrypt_symmetric(data_to_encrypt)
            
        except Exception as e:
            logger.error(f"Field encryption failed for {field_name}: {e}")
            raise
    
    def decrypt_field(self, encrypted_value: str, field_name: str = "") -> str:
        """
        Decrypt a field value
        """
        try:
            if not encrypted_value:
                return encrypted_value
            
            decrypted_data = self.encryption_manager.decrypt_symmetric(encrypted_value)
            
            # Remove field identifier if present
            if field_name and decrypted_data.startswith(f"{field_name}:"):
                return decrypted_data[len(field_name) + 1:]
            
            return decrypted_data
            
        except Exception as e:
            logger.error(f"Field decryption failed for {field_name}: {e}")
            raise


class DataMasking:
    """
    Data masking utilities for sensitive information
    """
    
    @staticmethod
    def mask_email(email: str) -> str:
        """
        Mask email address
        Example: <EMAIL> -> j***@example.com
        """
        if not email or '@' not in email:
            return email
        
        local, domain = email.split('@', 1)
        if len(local) <= 1:
            return email
        
        masked_local = local[0] + '*' * (len(local) - 1)
        return f"{masked_local}@{domain}"
    
    @staticmethod
    def mask_phone(phone: str) -> str:
        """
        Mask phone number
        Example: +1234567890 -> +123***7890
        """
        if not phone or len(phone) < 4:
            return phone
        
        if len(phone) <= 6:
            return phone[:2] + '*' * (len(phone) - 4) + phone[-2:]
        
        return phone[:3] + '*' * (len(phone) - 6) + phone[-3:]
    
    @staticmethod
    def mask_id_number(id_number: str) -> str:
        """
        Mask ID number
        Example: 123456789 -> 123***789
        """
        if not id_number or len(id_number) < 6:
            return id_number
        
        return id_number[:3] + '*' * (len(id_number) - 6) + id_number[-3:]
    
    @staticmethod
    def mask_credit_card(card_number: str) -> str:
        """
        Mask credit card number
        Example: 1234567890123456 -> ****-****-****-3456
        """
        if not card_number:
            return card_number
        
        # Remove any existing formatting
        clean_number = ''.join(filter(str.isdigit, card_number))
        
        if len(clean_number) < 4:
            return card_number
        
        masked = '*' * (len(clean_number) - 4) + clean_number[-4:]
        
        # Add formatting for readability
        if len(clean_number) == 16:
            return f"****-****-****-{clean_number[-4:]}"
        
        return masked
    
    @staticmethod
    def mask_name(name: str) -> str:
        """
        Mask personal name
        Example: John Doe -> J*** D***
        """
        if not name:
            return name
        
        parts = name.split()
        masked_parts = []
        
        for part in parts:
            if len(part) <= 1:
                masked_parts.append(part)
            else:
                masked_parts.append(part[0] + '*' * (len(part) - 1))
        
        return ' '.join(masked_parts)


# Global encryption manager instance
encryption_manager = EncryptionManager()
field_encryption = FieldEncryption(encryption_manager)


# Utility functions
def encrypt_sensitive_data(data: str, field_name: str = "") -> str:
    """Encrypt sensitive data"""
    return field_encryption.encrypt_field(data, field_name)


def decrypt_sensitive_data(encrypted_data: str, field_name: str = "") -> str:
    """Decrypt sensitive data"""
    return field_encryption.decrypt_field(encrypted_data, field_name)


def generate_secure_token(length: int = 32) -> str:
    """Generate a secure random token"""
    return encryption_manager.generate_token(length)


def hash_sensitive_data(data: str, algorithm: str = 'sha256') -> str:
    """Hash sensitive data"""
    return encryption_manager.hash_data(data, algorithm)


# Export main components
__all__ = [
    'EncryptionManager',
    'FieldEncryption',
    'DataMasking',
    'encryption_manager',
    'field_encryption',
    'encrypt_sensitive_data',
    'decrypt_sensitive_data',
    'generate_secure_token',
    'hash_sensitive_data'
]