"""
Session optimization utilities for the School ERP system.
Includes efficient session management, cleanup, and performance monitoring.
"""

import logging
import json
from datetime import datetime, timedelta
from django.core.cache import cache
from django.utils import timezone
from django.conf import settings
from django.contrib.sessions.models import Session
from django.db import transaction
from core.performance_optimization import <PERSON><PERSON><PERSON>ana<PERSON>, monitor_performance

logger = logging.getLogger(__name__)


class SessionOptimizer:
    """
    Optimized session management for school selection and user context.
    """
    
    # Session keys
    SCHOOL_ID_KEY = 'selected_school_id'
    SCHOOL_TIMESTAMP_KEY = 'school_selection_timestamp'
    SCHOOL_VALIDATION_KEY = 'school_validation_hash'
    SESSION_METADATA_KEY = 'session_metadata'
    
    # Cache settings
    SESSION_CACHE_TTL = 1800  # 30 minutes
    VALIDATION_CACHE_TTL = 300  # 5 minutes
    
    @classmethod
    def get_session_cache_key(cls, session_key, suffix=''):
        """
        Generate standardized session cache key.
        
        Args:
            session_key: Django session key
            suffix: Optional suffix for specific data
            
        Returns:
            Formatted cache key
        """
        return f"session_opt_{session_key[:12]}_{suffix}" if suffix else f"session_opt_{session_key[:12]}"
    
    @classmethod
    @monitor_performance
    def get_optimized_session_data(cls, request):
        """
        Get session data with caching and optimization.
        
        Args:
            request: Django HttpRequest instance
            
        Returns:
            Dict with session data
        """
        if not hasattr(request, 'session') or not request.session.session_key:
            return {}
        
        session_key = request.session.session_key
        cache_key = cls.get_session_cache_key(session_key, 'data')
        
        # Try cache first
        cached_data = cache.get(cache_key)
        if cached_data and isinstance(cached_data, dict):
            # Validate cache freshness
            cache_timestamp = cached_data.get('_cache_timestamp')
            if cache_timestamp and (timezone.now() - cache_timestamp).total_seconds() < cls.SESSION_CACHE_TTL:
                return cached_data
        
        # Build session data from Django session
        session_data = {
            'school_id': request.session.get(cls.SCHOOL_ID_KEY),
            'school_timestamp': request.session.get(cls.SCHOOL_TIMESTAMP_KEY),
            'school_validation': request.session.get(cls.SCHOOL_VALIDATION_KEY),
            'metadata': request.session.get(cls.SESSION_METADATA_KEY, {}),
            '_cache_timestamp': timezone.now(),
            '_session_key': session_key
        }
        
        # Cache the session data
        cache.set(cache_key, session_data, cls.SESSION_CACHE_TTL)
        
        return session_data
    
    @classmethod
    @monitor_performance
    def set_optimized_session_data(cls, request, data):
        """
        Set session data with caching optimization.
        
        Args:
            request: Django HttpRequest instance
            data: Dict with session data to set
        """
        if not hasattr(request, 'session'):
            return False
        
        try:
            # Update Django session
            for key, value in data.items():
                if not key.startswith('_'):  # Skip internal cache keys
                    request.session[key] = value
            
            # Update cache
            session_key = request.session.session_key
            if session_key:
                cache_key = cls.get_session_cache_key(session_key, 'data')
                cached_data = data.copy()
                cached_data['_cache_timestamp'] = timezone.now()
                cached_data['_session_key'] = session_key
                cache.set(cache_key, cached_data, cls.SESSION_CACHE_TTL)
            
            return True
            
        except Exception as e:
            logger.error(f"Error setting optimized session data: {e}")
            return False
    
    @classmethod
    def validate_session_integrity(cls, request):
        """
        Validate session integrity with caching.
        
        Args:
            request: Django HttpRequest instance
            
        Returns:
            Tuple (is_valid, error_message)
        """
        if not request.user.is_authenticated:
            return False, "User not authenticated"
        
        session_key = getattr(request.session, 'session_key', None)
        if not session_key:
            return False, "No session key"
        
        # Check validation cache first
        validation_cache_key = cls.get_session_cache_key(session_key, 'validation')
        cached_validation = cache.get(validation_cache_key)
        
        if cached_validation is not None:
            return cached_validation.get('is_valid', False), cached_validation.get('error', '')
        
        # Perform validation
        try:
            session_data = cls.get_optimized_session_data(request)
            
            # Check required fields
            school_id = session_data.get('school_id')
            timestamp_str = session_data.get('school_timestamp')
            
            if not school_id or not timestamp_str:
                result = (False, "Missing session data")
            else:
                # Validate timestamp
                try:
                    timestamp = datetime.fromisoformat(timestamp_str)
                    age = timezone.now() - timestamp
                    max_age = timedelta(hours=getattr(settings, 'SCHOOL_SESSION_TIMEOUT_HOURS', 24))
                    
                    if age > max_age:
                        result = (False, "Session expired")
                    else:
                        result = (True, "")
                        
                except (ValueError, TypeError):
                    result = (False, "Invalid timestamp")
            
            # Cache validation result
            validation_data = {
                'is_valid': result[0],
                'error': result[1],
                'timestamp': timezone.now()
            }
            cache.set(validation_cache_key, validation_data, cls.VALIDATION_CACHE_TTL)
            
            return result
            
        except Exception as e:
            logger.error(f"Error validating session integrity: {e}")
            return False, str(e)
    
    @classmethod
    def cleanup_session_cache(cls, session_key):
        """
        Clean up cache entries for a specific session.
        
        Args:
            session_key: Django session key
        """
        try:
            cache_keys = [
                cls.get_session_cache_key(session_key, 'data'),
                cls.get_session_cache_key(session_key, 'validation'),
                cls.get_session_cache_key(session_key, 'metadata')
            ]
            
            for key in cache_keys:
                cache.delete(key)
            
            logger.debug(f"Cleaned up session cache for {session_key[:12]}")
            
        except Exception as e:
            logger.error(f"Error cleaning up session cache: {e}")
    
    @classmethod
    @monitor_performance
    def batch_cleanup_expired_sessions(cls, batch_size=100):
        """
        Clean up expired sessions in batches for better performance.
        
        Args:
            batch_size: Number of sessions to process per batch
            
        Returns:
            Number of sessions cleaned up
        """
        try:
            # Get expired sessions
            expired_sessions = Session.objects.filter(
                expire_date__lt=timezone.now()
            )[:batch_size]
            
            cleaned_count = 0
            
            with transaction.atomic():
                for session in expired_sessions:
                    try:
                        # Clean up cache entries
                        cls.cleanup_session_cache(session.session_key)
                        
                        # Delete session
                        session.delete()
                        cleaned_count += 1
                        
                    except Exception as e:
                        logger.error(f"Error cleaning up session {session.session_key}: {e}")
                        continue
            
            if cleaned_count > 0:
                logger.info(f"Cleaned up {cleaned_count} expired sessions")
            
            return cleaned_count
            
        except Exception as e:
            logger.error(f"Error in batch session cleanup: {e}")
            return 0


class SessionMetrics:
    """
    Session performance metrics and monitoring.
    """
    
    @staticmethod
    def get_session_stats():
        """
        Get session statistics for monitoring.
        
        Returns:
            Dict with session statistics
        """
        try:
            total_sessions = Session.objects.count()
            expired_sessions = Session.objects.filter(
                expire_date__lt=timezone.now()
            ).count()
            
            # Get cache statistics (simplified)
            cache_stats = {
                'total_sessions': total_sessions,
                'expired_sessions': expired_sessions,
                'active_sessions': total_sessions - expired_sessions,
                'timestamp': timezone.now()
            }
            
            return cache_stats
            
        except Exception as e:
            logger.error(f"Error getting session stats: {e}")
            return {
                'error': str(e),
                'timestamp': timezone.now()
            }
    
    @staticmethod
    def monitor_session_performance(request):
        """
        Monitor session performance for a specific request.
        
        Args:
            request: Django HttpRequest instance
            
        Returns:
            Dict with performance metrics
        """
        metrics = {
            'has_session': hasattr(request, 'session'),
            'session_key_length': len(getattr(request.session, 'session_key', '')) if hasattr(request, 'session') else 0,
            'is_authenticated': request.user.is_authenticated,
            'timestamp': timezone.now()
        }
        
        if hasattr(request, 'session') and request.session.session_key:
            # Check cache hit rate
            session_key = request.session.session_key
            cache_key = SessionOptimizer.get_session_cache_key(session_key, 'data')
            
            metrics['cache_hit'] = cache.get(cache_key) is not None
            metrics['session_age'] = None
            
            # Calculate session age if possible
            try:
                session_data = SessionOptimizer.get_optimized_session_data(request)
                timestamp_str = session_data.get('school_timestamp')
                if timestamp_str:
                    timestamp = datetime.fromisoformat(timestamp_str)
                    metrics['session_age'] = (timezone.now() - timestamp).total_seconds()
            except Exception:
                pass
        
        return metrics


class SessionSecurityOptimizer:
    """
    Security-focused session optimization.
    """
    
    @staticmethod
    def validate_session_security(request, cache_result=True):
        """
        Validate session security with caching for performance.
        
        Args:
            request: Django HttpRequest instance
            cache_result: Whether to cache the validation result
            
        Returns:
            Tuple (is_secure, warning_message)
        """
        if not request.user.is_authenticated:
            return False, "User not authenticated"
        
        session_key = getattr(request.session, 'session_key', None)
        if not session_key:
            return False, "No session key"
        
        # Check security validation cache
        if cache_result:
            security_cache_key = f"session_security_{request.user.id}_{session_key[:8]}"
            cached_result = cache.get(security_cache_key)
            if cached_result:
                return cached_result.get('is_secure', False), cached_result.get('warning', '')
        
        try:
            # Validate IP consistency
            current_ip = cls._get_client_ip(request)
            session_ip = request.session.get('session_ip')
            
            if session_ip and session_ip != current_ip:
                result = (False, "IP address change detected")
            else:
                # Store IP if not set
                if not session_ip:
                    request.session['session_ip'] = current_ip
                
                # Validate user agent consistency
                current_ua = request.META.get('HTTP_USER_AGENT', '')
                session_ua = request.session.get('session_user_agent')
                
                if session_ua and session_ua != current_ua:
                    result = (False, "User agent change detected")
                else:
                    # Store user agent if not set
                    if not session_ua:
                        request.session['session_user_agent'] = current_ua
                    
                    result = (True, "")
            
            # Cache security validation result
            if cache_result:
                security_data = {
                    'is_secure': result[0],
                    'warning': result[1],
                    'timestamp': timezone.now()
                }
                cache.set(security_cache_key, security_data, 300)  # 5 minutes
            
            return result
            
        except Exception as e:
            logger.error(f"Error validating session security: {e}")
            return False, str(e)
    
    @staticmethod
    def _get_client_ip(request):
        """
        Get client IP address from request.
        
        Args:
            request: Django HttpRequest instance
            
        Returns:
            Client IP address string
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '')
        return ip
    
    @classmethod
    def enhance_session_security(cls, request):
        """
        Enhance session security with additional metadata.
        
        Args:
            request: Django HttpRequest instance
        """
        try:
            # Add security metadata to session
            security_metadata = {
                'created_at': timezone.now().isoformat(),
                'ip_address': cls._get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                'user_id': request.user.id if request.user.is_authenticated else None
            }
            
            request.session['security_metadata'] = security_metadata
            
        except Exception as e:
            logger.error(f"Error enhancing session security: {e}")


# Utility functions for session optimization

def get_optimized_session_data(request):
    """
    Get session data with optimization.
    
    Args:
        request: Django HttpRequest instance
        
    Returns:
        Dict with session data
    """
    return SessionOptimizer.get_optimized_session_data(request)


def validate_session_with_cache(request):
    """
    Validate session with caching.
    
    Args:
        request: Django HttpRequest instance
        
    Returns:
        Tuple (is_valid, error_message)
    """
    return SessionOptimizer.validate_session_integrity(request)


def cleanup_user_session_cache(user_id, session_key):
    """
    Clean up session cache for a specific user.
    
    Args:
        user_id: User ID
        session_key: Session key
    """
    try:
        # Clean up user-specific caches
        user_cache_keys = [
            f"user_school_context_{user_id}",
            f"user_schools_v2_{user_id}",
            f"school_context_{user_id}",
            f"session_security_{user_id}_{session_key[:8]}"
        ]
        
        for key in user_cache_keys:
            cache.delete(key)
        
        # Clean up session-specific caches
        SessionOptimizer.cleanup_session_cache(session_key)
        
        logger.debug(f"Cleaned up session cache for user {user_id}")
        
    except Exception as e:
        logger.error(f"Error cleaning up user session cache: {e}")


# Performance monitoring decorator for session operations
def monitor_session_performance(func):
    """
    Decorator to monitor session operation performance.
    
    Args:
        func: Function to monitor
        
    Returns:
        Decorated function
    """
    return monitor_performance(func)