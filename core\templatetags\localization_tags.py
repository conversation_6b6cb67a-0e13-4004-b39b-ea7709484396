"""
Template tags for localization support.
"""

from django import template
from django.utils import translation
from django.utils.safestring import mark_safe
from django.urls import reverse
from core.localization.localization import LocalizationManager, RTLSupport, NumberFormatter, DateFormatter

register = template.Library()


@register.simple_tag
def get_current_language():
    """Get current active language code."""
    return LocalizationManager.get_current_language()


@register.simple_tag
def get_language_name(language_code=None):
    """Get display name for language code."""
    if language_code is None:
        language_code = LocalizationManager.get_current_language()
    return LocalizationManager.get_language_name(language_code)


@register.simple_tag
def is_rtl():
    """Check if current language is RTL."""
    return LocalizationManager.is_rtl_language()


@register.simple_tag
def language_direction():
    """Get text direction for current language."""
    return LocalizationManager.get_language_direction()


@register.simple_tag
def rtl_css_classes():
    """Get CSS classes for RTL support."""
    return RTLSupport.get_css_classes()


@register.simple_tag
def bootstrap_class(class_name):
    """Get Bootstrap class adjusted for RTL."""
    classes = RTLSupport.get_bootstrap_classes()
    return classes.get(class_name, class_name)


@register.filter
def format_number(value, language_code=None):
    """Format number according to language preferences."""
    return NumberFormatter.format_number(value, language_code)


@register.filter
def arabic_digits(value):
    """Convert Western digits to Arabic-Indic digits."""
    return NumberFormatter.to_arabic_indic(value)


@register.filter
def western_digits(value):
    """Convert Arabic-Indic digits to Western digits."""
    return NumberFormatter.to_western_digits(value)


@register.simple_tag
def language_switch_url(language_code, next_url=None):
    """Generate URL for language switching."""
    url = reverse('core:switch_language', args=[language_code])
    if next_url:
        url += f'?next={next_url}'
    return url


@register.inclusion_tag('core/language_switcher.html', takes_context=True)
def language_switcher(context):
    """Render language switcher widget."""
    request = context['request']
    current_language = LocalizationManager.get_current_language()
    available_languages = LocalizationManager.get_available_languages()
    
    return {
        'current_language': current_language,
        'current_language_name': LocalizationManager.get_language_name(current_language),
        'available_languages': available_languages,
        'current_url': request.get_full_path(),
        'request': request,
    }


@register.simple_tag
def localized_static(path):
    """Get localized static file path."""
    current_language = LocalizationManager.get_current_language()
    
    # Check if localized version exists
    if current_language != 'en':
        localized_path = path.replace('.css', f'.{current_language}.css')
        localized_path = localized_path.replace('.js', f'.{current_language}.js')
        return localized_path
    
    return path


@register.simple_tag(takes_context=True)
def rtl_aware_class(context, base_class):
    """Get RTL-aware CSS class."""
    if LocalizationManager.is_rtl_language():
        # Map common classes to RTL equivalents
        rtl_mappings = {
            'text-left': 'text-right',
            'text-right': 'text-left',
            'float-left': 'float-right',
            'float-right': 'float-left',
            'ml-': 'mr-',
            'mr-': 'ml-',
            'pl-': 'pr-',
            'pr-': 'pl-',
        }
        
        for ltr_class, rtl_class in rtl_mappings.items():
            if ltr_class in base_class:
                base_class = base_class.replace(ltr_class, rtl_class)
    
    return base_class


@register.simple_tag
def get_date_format():
    """Get date format for current language."""
    return DateFormatter.get_date_format()


@register.simple_tag
def get_datetime_format():
    """Get datetime format for current language."""
    return DateFormatter.get_datetime_format()


@register.filter
def format_date_localized(date_obj):
    """Format date according to current language preferences."""
    if not date_obj:
        return ''
    
    current_language = LocalizationManager.get_current_language()
    
    if current_language == 'ar':
        return DateFormatter.format_date_arabic(date_obj)
    else:
        date_format = DateFormatter.get_date_format()
        return date_obj.strftime(date_format)


@register.filter
def format_datetime_localized(datetime_obj):
    """Format datetime according to current language preferences."""
    if not datetime_obj:
        return ''
    
    current_language = LocalizationManager.get_current_language()
    
    if current_language == 'ar':
        return DateFormatter.format_datetime_arabic(datetime_obj)
    else:
        datetime_format = DateFormatter.get_datetime_format()
        return datetime_obj.strftime(datetime_format)


@register.filter
def arabic_day_name(date_obj):
    """Get Arabic day name for the given date."""
    return DateFormatter.get_day_name_arabic(date_obj)


@register.filter
def arabic_month_name(date_obj):
    """Get Arabic month name for the given date."""
    if not date_obj:
        return ''
    return DateFormatter.ARABIC_MONTHS[date_obj.month - 1]


# Calendar template tags
@register.simple_tag
def hijri_date(gregorian_date):
    """Convert Gregorian date to Hijri date."""
    from core.calendar.calendar_utils import HijriCalendar
    return HijriCalendar.gregorian_to_hijri(gregorian_date)


@register.filter
def format_hijri(hijri_date_dict, language_code=None):
    """Format Hijri date dictionary."""
    from core.calendar.calendar_utils import HijriCalendar
    return HijriCalendar.format_hijri_date(hijri_date_dict, language_code)


@register.simple_tag
def dual_date(gregorian_date, language_code=None):
    """Get dual calendar display."""
    from core.calendar.calendar_utils import CalendarConverter
    return CalendarConverter.get_dual_date_display(gregorian_date, language_code)


@register.simple_tag
def hijri_month_name(month_number, language_code=None):
    """Get Hijri month name by number."""
    from core.calendar.calendar_utils import HijriCalendar
    return HijriCalendar.get_hijri_month_name(month_number, language_code)


@register.simple_tag
def is_ramadan(date_obj=None):
    """Check if date is in Ramadan."""
    from core.calendar.calendar_utils import HijriCalendar
    return HijriCalendar.is_ramadan(date_obj)


@register.simple_tag
def is_islamic_holiday(date_obj):
    """Check if date is an Islamic holiday."""
    from core.calendar.calendar_utils import CalendarEvents
    return CalendarEvents.is_islamic_holiday(date_obj)


@register.simple_tag
def next_islamic_holiday():
    """Get next Islamic holiday."""
    from core.calendar.calendar_utils import CalendarEvents
    return CalendarEvents.get_next_islamic_holiday()


@register.simple_tag
def academic_week_number(date_obj):
    """Get academic week number for date."""
    from core.calendar.calendar_utils import AcademicCalendar
    return AcademicCalendar.get_academic_week_number(date_obj)


@register.simple_tag
def get_text_align():
    """Get text alignment for current language."""
    return 'right' if LocalizationManager.is_rtl_language() else 'left'


@register.simple_tag
def get_opposite_align():
    """Get opposite text alignment for current language."""
    return 'left' if LocalizationManager.is_rtl_language() else 'right'


@register.inclusion_tag('core/rtl_css.html')
def rtl_css():
    """Include RTL CSS if needed."""
    return {
        'is_rtl': LocalizationManager.is_rtl_language(),
        'language_code': LocalizationManager.get_current_language(),
    }