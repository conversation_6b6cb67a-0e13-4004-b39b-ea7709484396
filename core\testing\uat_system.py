"""
User Acceptance Testing (UAT) System for School ERP
"""
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from django.db import models, transaction
from django.utils import timezone
from django.contrib.auth.models import User
from django.core.cache import cache
from django.http import JsonResponse
from django.views import View
from django.contrib.admin.views.decorators import staff_member_required
from django.utils.decorators import method_decorator

logger = logging.getLogger(__name__)


class UATTestCase(models.Model):
    """
    User Acceptance Test Case model
    """
    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical')
    ]
    
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('ready', 'Ready for Testing'),
        ('in_progress', 'In Progress'),
        ('passed', 'Passed'),
        ('failed', 'Failed'),
        ('blocked', 'Blocked'),
        ('skipped', 'Skipped')
    ]
    
    MODULE_CHOICES = [
        ('students', 'Student Management'),
        ('academics', 'Academic Management'),
        ('finance', 'Financial Management'),
        ('hr', 'Human Resources'),
        ('transportation', 'Transportation'),
        ('library', 'Library Management'),
        ('health', 'Health Management'),
        ('communications', 'Communications'),
        ('reports', 'Reports & Analytics'),
        ('system', 'System Administration')
    ]
    
    title = models.CharField(max_length=200)
    description = models.TextField()
    module = models.CharField(max_length=50, choices=MODULE_CHOICES)
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='medium')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    
    # Test details
    preconditions = models.TextField(blank=True)
    test_steps = models.JSONField(default=list)
    expected_result = models.TextField()
    actual_result = models.TextField(blank=True)
    
    # Execution details
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_tests')
    executed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='executed_tests')
    execution_date = models.DateTimeField(null=True, blank=True)
    execution_duration = models.DurationField(null=True, blank=True)
    
    # Metadata
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_tests')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Attachments and evidence
    screenshots = models.JSONField(default=list)  # List of screenshot URLs
    comments = models.TextField(blank=True)
    
    class Meta:
        db_table = 'uat_test_cases'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.title} ({self.get_status_display()})"
    
    def execute_test(self, user: User, actual_result: str, status: str, 
                    screenshots: List[str] = None, comments: str = "") -> Dict[str, Any]:
        """Execute the test case"""
        start_time = timezone.now()
        
        self.executed_by = user
        self.execution_date = start_time
        self.actual_result = actual_result
        self.status = status
        self.screenshots = screenshots or []
        self.comments = comments
        
        # Calculate duration if test is completed
        if status in ['passed', 'failed']:
            self.execution_duration = timezone.now() - start_time
        
        self.save()
        
        # Log execution
        UATExecution.objects.create(
            test_case=self,
            executed_by=user,
            status=status,
            actual_result=actual_result,
            execution_date=start_time,
            screenshots=screenshots or [],
            comments=comments
        )
        
        return {
            'status': 'success',
            'test_case_id': self.id,
            'execution_status': status,
            'execution_date': start_time.isoformat()
        }


class UATExecution(models.Model):
    """
    UAT Test Execution history
    """
    test_case = models.ForeignKey(UATTestCase, on_delete=models.CASCADE, related_name='executions')
    executed_by = models.ForeignKey(User, on_delete=models.CASCADE)
    execution_date = models.DateTimeField()
    status = models.CharField(max_length=20, choices=UATTestCase.STATUS_CHOICES)
    actual_result = models.TextField()
    screenshots = models.JSONField(default=list)
    comments = models.TextField(blank=True)
    
    class Meta:
        db_table = 'uat_executions'
        ordering = ['-execution_date']
    
    def __str__(self):
        return f"{self.test_case.title} - {self.get_status_display()} ({self.execution_date})"


class UATBugReport(models.Model):
    """
    Bug reports from UAT testing
    """
    SEVERITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical')
    ]
    
    BUG_STATUS_CHOICES = [
        ('open', 'Open'),
        ('in_progress', 'In Progress'),
        ('resolved', 'Resolved'),
        ('closed', 'Closed'),
        ('rejected', 'Rejected')
    ]
    
    title = models.CharField(max_length=200)
    description = models.TextField()
    test_case = models.ForeignKey(UATTestCase, on_delete=models.CASCADE, related_name='bug_reports')
    severity = models.CharField(max_length=20, choices=SEVERITY_CHOICES, default='medium')
    status = models.CharField(max_length=20, choices=BUG_STATUS_CHOICES, default='open')
    
    # Bug details
    steps_to_reproduce = models.TextField()
    expected_behavior = models.TextField()
    actual_behavior = models.TextField()
    environment_info = models.JSONField(default=dict)
    
    # Assignment and tracking
    reported_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='reported_bugs')
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_bugs')
    resolved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='resolved_bugs')
    
    # Timestamps
    reported_date = models.DateTimeField(auto_now_add=True)
    resolved_date = models.DateTimeField(null=True, blank=True)
    
    # Attachments
    screenshots = models.JSONField(default=list)
    attachments = models.JSONField(default=list)
    
    class Meta:
        db_table = 'uat_bug_reports'
        ordering = ['-reported_date']
    
    def __str__(self):
        return f"Bug: {self.title} ({self.get_severity_display()})"
    
    def resolve_bug(self, user: User, resolution_notes: str = "") -> Dict[str, Any]:
        """Mark bug as resolved"""
        self.status = 'resolved'
        self.resolved_by = user
        self.resolved_date = timezone.now()
        
        if resolution_notes:
            self.description += f"\n\nResolution Notes: {resolution_notes}"
        
        self.save()
        
        return {
            'status': 'success',
            'bug_id': self.id,
            'resolved_date': self.resolved_date.isoformat()
        }


class UATTestSuite(models.Model):
    """
    Collection of UAT test cases
    """
    name = models.CharField(max_length=200)
    description = models.TextField()
    test_cases = models.ManyToManyField(UATTestCase, related_name='test_suites')
    
    # Execution tracking
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'uat_test_suites'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.name
    
    def get_execution_summary(self) -> Dict[str, Any]:
        """Get execution summary for this test suite"""
        test_cases = self.test_cases.all()
        total_tests = test_cases.count()
        
        if total_tests == 0:
            return {'total': 0, 'passed': 0, 'failed': 0, 'pending': 0, 'pass_rate': 0}
        
        passed = test_cases.filter(status='passed').count()
        failed = test_cases.filter(status='failed').count()
        pending = test_cases.exclude(status__in=['passed', 'failed']).count()
        
        pass_rate = (passed / total_tests) * 100 if total_tests > 0 else 0
        
        return {
            'total': total_tests,
            'passed': passed,
            'failed': failed,
            'pending': pending,
            'pass_rate': round(pass_rate, 2)
        }


class UATManager:
    """
    UAT Management System
    """
    
    def __init__(self):
        self.test_scenarios = self._load_default_test_scenarios()
    
    def create_test_case(self, test_data: Dict[str, Any], created_by: User) -> UATTestCase:
        """Create a new UAT test case"""
        test_case = UATTestCase.objects.create(
            title=test_data['title'],
            description=test_data['description'],
            module=test_data['module'],
            priority=test_data.get('priority', 'medium'),
            preconditions=test_data.get('preconditions', ''),
            test_steps=test_data.get('test_steps', []),
            expected_result=test_data['expected_result'],
            created_by=created_by
        )
        
        logger.info(f"Created UAT test case: {test_case.title}")
        return test_case
    
    def execute_test_case(self, test_case_id: int, execution_data: Dict[str, Any], 
                         executed_by: User) -> Dict[str, Any]:
        """Execute a UAT test case"""
        try:
            test_case = UATTestCase.objects.get(id=test_case_id)
            
            result = test_case.execute_test(
                user=executed_by,
                actual_result=execution_data['actual_result'],
                status=execution_data['status'],
                screenshots=execution_data.get('screenshots', []),
                comments=execution_data.get('comments', '')
            )
            
            # Create bug report if test failed
            if execution_data['status'] == 'failed':
                self._create_bug_report_from_failed_test(test_case, execution_data, executed_by)
            
            return result
            
        except UATTestCase.DoesNotExist:
            return {'status': 'error', 'message': 'Test case not found'}
    
    def create_bug_report(self, bug_data: Dict[str, Any], reported_by: User) -> UATBugReport:
        """Create a bug report"""
        bug_report = UATBugReport.objects.create(
            title=bug_data['title'],
            description=bug_data['description'],
            test_case_id=bug_data.get('test_case_id'),
            severity=bug_data.get('severity', 'medium'),
            steps_to_reproduce=bug_data['steps_to_reproduce'],
            expected_behavior=bug_data['expected_behavior'],
            actual_behavior=bug_data['actual_behavior'],
            environment_info=bug_data.get('environment_info', {}),
            reported_by=reported_by,
            screenshots=bug_data.get('screenshots', []),
            attachments=bug_data.get('attachments', [])
        )
        
        logger.info(f"Created bug report: {bug_report.title}")
        return bug_report
    
    def get_uat_dashboard_data(self) -> Dict[str, Any]:
        """Get UAT dashboard data"""
        # Test case statistics
        total_tests = UATTestCase.objects.count()
        passed_tests = UATTestCase.objects.filter(status='passed').count()
        failed_tests = UATTestCase.objects.filter(status='failed').count()
        pending_tests = UATTestCase.objects.exclude(status__in=['passed', 'failed']).count()
        
        # Bug statistics
        total_bugs = UATBugReport.objects.count()
        open_bugs = UATBugReport.objects.filter(status='open').count()
        critical_bugs = UATBugReport.objects.filter(severity='critical', status='open').count()
        
        # Module coverage
        module_coverage = list(
            UATTestCase.objects.values('module').annotate(
                total=models.Count('id'),
                passed=models.Count('id', filter=models.Q(status='passed')),
                failed=models.Count('id', filter=models.Q(status='failed'))
            )
        )
        
        # Recent activity
        recent_executions = UATExecution.objects.select_related('test_case', 'executed_by').order_by('-execution_date')[:10]
        recent_bugs = UATBugReport.objects.select_related('reported_by', 'test_case').order_by('-reported_date')[:10]
        
        return {
            'test_statistics': {
                'total': total_tests,
                'passed': passed_tests,
                'failed': failed_tests,
                'pending': pending_tests,
                'pass_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0
            },
            'bug_statistics': {
                'total': total_bugs,
                'open': open_bugs,
                'critical': critical_bugs
            },
            'module_coverage': module_coverage,
            'recent_executions': [
                {
                    'test_case': exec.test_case.title,
                    'status': exec.status,
                    'executed_by': exec.executed_by.get_full_name(),
                    'execution_date': exec.execution_date.isoformat()
                }
                for exec in recent_executions
            ],
            'recent_bugs': [
                {
                    'title': bug.title,
                    'severity': bug.severity,
                    'reported_by': bug.reported_by.get_full_name(),
                    'reported_date': bug.reported_date.isoformat()
                }
                for bug in recent_bugs
            ]
        }
    
    def generate_uat_report(self, date_from: datetime = None, date_to: datetime = None) -> Dict[str, Any]:
        """Generate comprehensive UAT report"""
        if not date_from:
            date_from = timezone.now() - timedelta(days=30)
        if not date_to:
            date_to = timezone.now()
        
        # Filter executions by date range
        executions = UATExecution.objects.filter(
            execution_date__range=[date_from, date_to]
        )
        
        # Test execution summary
        execution_summary = {
            'total_executions': executions.count(),
            'passed': executions.filter(status='passed').count(),
            'failed': executions.filter(status='failed').count(),
            'blocked': executions.filter(status='blocked').count(),
            'skipped': executions.filter(status='skipped').count()
        }
        
        # Bug summary
        bugs = UATBugReport.objects.filter(
            reported_date__range=[date_from, date_to]
        )
        
        bug_summary = {
            'total_bugs': bugs.count(),
            'critical': bugs.filter(severity='critical').count(),
            'high': bugs.filter(severity='high').count(),
            'medium': bugs.filter(severity='medium').count(),
            'low': bugs.filter(severity='low').count(),
            'resolved': bugs.filter(status='resolved').count()
        }
        
        # Module-wise analysis
        module_analysis = {}
        for module, _ in UATTestCase.MODULE_CHOICES:
            module_tests = UATTestCase.objects.filter(module=module)
            module_executions = executions.filter(test_case__module=module)
            
            module_analysis[module] = {
                'total_tests': module_tests.count(),
                'executions': module_executions.count(),
                'passed': module_executions.filter(status='passed').count(),
                'failed': module_executions.filter(status='failed').count(),
                'bugs': bugs.filter(test_case__module=module).count()
            }
        
        return {
            'report_period': {
                'from': date_from.isoformat(),
                'to': date_to.isoformat()
            },
            'execution_summary': execution_summary,
            'bug_summary': bug_summary,
            'module_analysis': module_analysis,
            'generated_at': timezone.now().isoformat()
        }
    
    def _create_bug_report_from_failed_test(self, test_case: UATTestCase, 
                                          execution_data: Dict[str, Any], reported_by: User):
        """Create bug report from failed test"""
        bug_data = {
            'title': f"Bug from test: {test_case.title}",
            'description': f"Test case failed: {test_case.description}",
            'test_case_id': test_case.id,
            'severity': 'medium',
            'steps_to_reproduce': '\n'.join([f"{i+1}. {step}" for i, step in enumerate(test_case.test_steps)]),
            'expected_behavior': test_case.expected_result,
            'actual_behavior': execution_data['actual_result'],
            'screenshots': execution_data.get('screenshots', [])
        }
        
        self.create_bug_report(bug_data, reported_by)
    
    def _load_default_test_scenarios(self) -> Dict[str, List[Dict[str, Any]]]:
        """Load default test scenarios for each module"""
        return {
            'students': [
                {
                    'title': 'Student Registration Process',
                    'description': 'Test complete student registration workflow',
                    'priority': 'high',
                    'preconditions': 'Admin user logged in, Academic year exists',
                    'test_steps': [
                        'Navigate to Student Registration',
                        'Fill in student details',
                        'Add parent information',
                        'Upload required documents',
                        'Submit registration'
                    ],
                    'expected_result': 'Student successfully registered with ID generated'
                },
                {
                    'title': 'Student Profile Update',
                    'description': 'Test student profile information update',
                    'priority': 'medium',
                    'preconditions': 'Student exists in system',
                    'test_steps': [
                        'Search for student',
                        'Open student profile',
                        'Update contact information',
                        'Save changes'
                    ],
                    'expected_result': 'Student profile updated successfully'
                }
            ],
            'academics': [
                {
                    'title': 'Grade Entry and Calculation',
                    'description': 'Test grade entry and GPA calculation',
                    'priority': 'critical',
                    'preconditions': 'Class exists, Students enrolled',
                    'test_steps': [
                        'Navigate to Grade Entry',
                        'Select class and assessment',
                        'Enter grades for all students',
                        'Submit grades'
                    ],
                    'expected_result': 'Grades saved and GPA calculated correctly'
                }
            ],
            'finance': [
                {
                    'title': 'Fee Payment Processing',
                    'description': 'Test fee payment workflow',
                    'priority': 'critical',
                    'preconditions': 'Student has pending fees',
                    'test_steps': [
                        'Navigate to Fee Payment',
                        'Select student and fees',
                        'Process payment',
                        'Generate receipt'
                    ],
                    'expected_result': 'Payment processed and receipt generated'
                }
            ]
        }


@method_decorator(staff_member_required, name='dispatch')
class UATDashboardView(View):
    """UAT Dashboard API View"""
    
    def get(self, request):
        """Get UAT dashboard data"""
        try:
            uat_manager = UATManager()
            dashboard_data = uat_manager.get_uat_dashboard_data()
            
            return JsonResponse({
                'status': 'success',
                'data': dashboard_data
            })
            
        except Exception as e:
            logger.error(f"Error getting UAT dashboard data: {e}")
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            }, status=500)


@method_decorator(staff_member_required, name='dispatch')
class UATReportView(View):
    """UAT Report Generation View"""
    
    def get(self, request):
        """Generate UAT report"""
        try:
            # Get date range from query parameters
            date_from_str = request.GET.get('date_from')
            date_to_str = request.GET.get('date_to')
            
            date_from = None
            date_to = None
            
            if date_from_str:
                date_from = datetime.fromisoformat(date_from_str)
            if date_to_str:
                date_to = datetime.fromisoformat(date_to_str)
            
            uat_manager = UATManager()
            report_data = uat_manager.generate_uat_report(date_from, date_to)
            
            return JsonResponse({
                'status': 'success',
                'report': report_data
            })
            
        except Exception as e:
            logger.error(f"Error generating UAT report: {e}")
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            }, status=500)


# Global UAT manager instance
uat_manager = UATManager()


# Utility functions
def create_default_test_cases(created_by: User) -> Dict[str, int]:
    """Create default test cases for all modules"""
    created_counts = {}
    
    for module, test_scenarios in uat_manager.test_scenarios.items():
        count = 0
        for scenario in test_scenarios:
            scenario['module'] = module
            uat_manager.create_test_case(scenario, created_by)
            count += 1
        created_counts[module] = count
    
    return created_counts


def get_uat_summary() -> Dict[str, Any]:
    """Get UAT summary statistics"""
    return uat_manager.get_uat_dashboard_data()


# Export main components
__all__ = [
    'UATTestCase',
    'UATExecution',
    'UATBugReport',
    'UATTestSuite',
    'UATManager',
    'UATDashboardView',
    'UATReportView',
    'uat_manager',
    'create_default_test_cases',
    'get_uat_summary'
]