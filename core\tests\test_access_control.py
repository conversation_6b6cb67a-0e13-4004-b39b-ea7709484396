"""
Tests for access control system
"""
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, Permission
from django.core.exceptions import PermissionDenied

from core.models import School
from core.access_control import (
    <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ResourceAccessControl,
    require_role, require_permission, get_role_manager
)

User = get_user_model()


class AccessControlTestCase(TestCase):
    """
    Base test case for access control tests
    """
    
    def setUp(self):
        """
        Set up test data
        """
        # Create test school
        self.school = School.objects.create(
            name='Test School',
            code='TEST001',
            address='Test Address',
            phone='+1234567890',
            email='<EMAIL>',
            principal_name='Test Principal',
            established_date='2020-01-01'
        )
        
        # Create test users
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.teacher_user = User.objects.create_user(
            username='teacher',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.student_user = User.objects.create_user(
            username='student',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.parent_user = User.objects.create_user(
            username='parent',
            email='<EMAIL>',
            password='testpass123'
        )


class RoleManagerTestCase(AccessControlTestCase):
    """
    Test role manager functionality
    """
    
    def test_role_manager_initialization(self):
        """
        Test role manager can be initialized
        """
        role_manager = RoleManager(self.school)
        self.assertEqual(role_manager.school, self.school)
    
    def test_create_role(self):
        """
        Test creating a role
        """
        role_manager = RoleManager(self.school)
        
        # Create teacher role
        group = role_manager.create_role('TEACHER', self.school)
        
        self.assertIsInstance(group, Group)
        self.assertEqual(group.name, f"{self.school.code}_TEACHER")
    
    def test_assign_role_to_user(self):
        """
        Test assigning role to user
        """
        role_manager = RoleManager(self.school)
        
        # Assign teacher role
        role_manager.assign_role_to_user(self.teacher_user, 'TEACHER', self.school)
        
        # Check if user has the role
        self.assertTrue(role_manager.user_has_role(self.teacher_user, 'TEACHER', self.school))
    
    def test_remove_role_from_user(self):
        """
        Test removing role from user
        """
        role_manager = RoleManager(self.school)
        
        # Assign and then remove role
        role_manager.assign_role_to_user(self.teacher_user, 'TEACHER', self.school)
        self.assertTrue(role_manager.user_has_role(self.teacher_user, 'TEACHER', self.school))
        
        role_manager.remove_role_from_user(self.teacher_user, 'TEACHER', self.school)
        self.assertFalse(role_manager.user_has_role(self.teacher_user, 'TEACHER', self.school))
    
    def test_get_user_roles(self):
        """
        Test getting user roles
        """
        role_manager = RoleManager(self.school)
        
        # Assign multiple roles
        role_manager.assign_role_to_user(self.teacher_user, 'TEACHER', self.school)
        role_manager.assign_role_to_user(self.teacher_user, 'HEAD_TEACHER', self.school)
        
        # Get roles
        roles = role_manager.get_user_roles(self.teacher_user, self.school)
        
        self.assertIn('TEACHER', roles)
        self.assertIn('HEAD_TEACHER', roles)
    
    def test_get_users_with_role(self):
        """
        Test getting users with specific role
        """
        role_manager = RoleManager(self.school)
        
        # Assign role to user
        role_manager.assign_role_to_user(self.teacher_user, 'TEACHER', self.school)
        
        # Get users with role
        users = role_manager.get_users_with_role('TEACHER', self.school)
        
        self.assertIn(self.teacher_user, users)
    
    def test_role_definitions(self):
        """
        Test role definitions are properly defined
        """
        role_manager = RoleManager()
        
        # Check that essential roles exist
        essential_roles = ['SCHOOL_ADMIN', 'TEACHER', 'STUDENT', 'PARENT']
        
        for role_code in essential_roles:
            self.assertIn(role_code, role_manager.ROLE_DEFINITIONS)
            role_def = role_manager.ROLE_DEFINITIONS[role_code]
            self.assertIn('name', role_def)
            self.assertIn('description', role_def)
            self.assertIn('permissions', role_def)
            self.assertIn('is_school_specific', role_def)


class PermissionCheckerTestCase(AccessControlTestCase):
    """
    Test permission checker functionality
    """
    
    def test_permission_checker_initialization(self):
        """
        Test permission checker can be initialized
        """
        checker = PermissionChecker(self.teacher_user, self.school)
        self.assertEqual(checker.user, self.teacher_user)
        self.assertEqual(checker.school, self.school)
    
    def test_superuser_permissions(self):
        """
        Test superuser has all permissions
        """
        # Make user superuser
        self.admin_user.is_superuser = True
        self.admin_user.save()
        
        checker = PermissionChecker(self.admin_user, self.school)
        
        # Superuser should have any permission
        self.assertTrue(checker.has_permission('any_permission'))
        self.assertTrue(checker.can_access_resource('student', 'view'))
        self.assertTrue(checker.can_access_resource('teacher', 'change'))
    
    def test_role_based_permissions(self):
        """
        Test role-based permission checking
        """
        # Assign teacher role
        role_manager = RoleManager(self.school)
        role_manager.assign_role_to_user(self.teacher_user, 'TEACHER', self.school)
        
        checker = PermissionChecker(self.teacher_user, self.school)
        
        # Teacher should have student view permissions
        self.assertTrue(checker.can_access_resource('student', 'view'))
    
    def test_permission_denied_for_unauthorized_user(self):
        """
        Test permission denied for unauthorized user
        """
        checker = PermissionChecker(self.student_user, self.school)
        
        # Student should not have admin permissions
        self.assertFalse(checker.can_access_resource('user', 'add'))
        self.assertFalse(checker.has_permission('manage_users'))


class ResourceAccessControlTestCase(AccessControlTestCase):
    """
    Test resource access control functionality
    """
    
    def test_resource_access_control_initialization(self):
        """
        Test resource access control can be initialized
        """
        rac = ResourceAccessControl(self.teacher_user, self.school)
        self.assertEqual(rac.user, self.teacher_user)
        self.assertEqual(rac.school, self.school)
        self.assertIsInstance(rac.permission_checker, PermissionChecker)


class AccessControlDecoratorsTestCase(AccessControlTestCase):
    """
    Test access control decorators
    """
    
    def test_require_role_decorator(self):
        """
        Test require_role decorator
        """
        # Create a mock view function
        @require_role('TEACHER')
        def mock_view(request):
            return "Success"
        
        # Create mock request
        class MockRequest:
            def __init__(self, user, school):
                self.user = user
                self.school = school
        
        # Test with user who has the role
        role_manager = RoleManager(self.school)
        role_manager.assign_role_to_user(self.teacher_user, 'TEACHER', self.school)
        
        request = MockRequest(self.teacher_user, self.school)
        result = mock_view(request)
        self.assertEqual(result, "Success")
        
        # Test with user who doesn't have the role
        request = MockRequest(self.student_user, self.school)
        with self.assertRaises(PermissionDenied):
            mock_view(request)
    
    def test_require_permission_decorator(self):
        """
        Test require_permission decorator
        """
        @require_permission('view_student')
        def mock_view(request):
            return "Success"
        
        class MockRequest:
            def __init__(self, user, school):
                self.user = user
                self.school = school
        
        # Test with superuser
        self.admin_user.is_superuser = True
        self.admin_user.save()
        
        request = MockRequest(self.admin_user, self.school)
        result = mock_view(request)
        self.assertEqual(result, "Success")
        
        # Test with regular user (should fail)
        request = MockRequest(self.student_user, self.school)
        with self.assertRaises(PermissionDenied):
            mock_view(request)


class UtilityFunctionsTestCase(AccessControlTestCase):
    """
    Test utility functions
    """
    
    def test_get_role_manager(self):
        """
        Test get_role_manager utility function
        """
        role_manager = get_role_manager(self.school)
        self.assertIsInstance(role_manager, RoleManager)
        self.assertEqual(role_manager.school, self.school)
    
    def test_setup_default_roles(self):
        """
        Test setup_default_roles utility function
        """
        from core.access_control import setup_default_roles
        
        # Setup default roles
        setup_default_roles(self.school)
        
        # Check that some essential roles were created
        essential_roles = ['SCHOOL_ADMIN', 'TEACHER', 'STUDENT']
        
        for role_code in essential_roles:
            group_name = f"{self.school.code}_{role_code}"
            self.assertTrue(Group.objects.filter(name=group_name).exists())
    
    def test_assign_user_role_utility(self):
        """
        Test assign_user_role utility function
        """
        from core.access_control import assign_user_role
        
        # Assign role using utility function
        assign_user_role(self.teacher_user, 'TEACHER', self.school)
        
        # Check if role was assigned
        role_manager = RoleManager(self.school)
        self.assertTrue(role_manager.user_has_role(self.teacher_user, 'TEACHER', self.school))


class RoleHierarchyTestCase(AccessControlTestCase):
    """
    Test role hierarchy and inheritance
    """
    
    def test_admin_role_permissions(self):
        """
        Test admin role has broad permissions
        """
        role_manager = RoleManager(self.school)
        role_manager.assign_role_to_user(self.admin_user, 'SCHOOL_ADMIN', self.school)
        
        checker = PermissionChecker(self.admin_user, self.school)
        
        # Admin should have access to most resources
        self.assertTrue(checker.can_access_resource('student', 'view'))
        self.assertTrue(checker.can_access_resource('student', 'change'))
        self.assertTrue(checker.can_access_resource('teacher', 'view'))
        self.assertTrue(checker.can_access_resource('teacher', 'change'))
    
    def test_teacher_role_permissions(self):
        """
        Test teacher role has appropriate permissions
        """
        role_manager = RoleManager(self.school)
        role_manager.assign_role_to_user(self.teacher_user, 'TEACHER', self.school)
        
        checker = PermissionChecker(self.teacher_user, self.school)
        
        # Teacher should have student view/change permissions
        self.assertTrue(checker.can_access_resource('student', 'view'))
        # But may not have user management permissions
        self.assertFalse(checker.can_access_resource('user', 'add'))
    
    def test_student_role_permissions(self):
        """
        Test student role has limited permissions
        """
        role_manager = RoleManager(self.school)
        role_manager.assign_role_to_user(self.student_user, 'STUDENT', self.school)
        
        checker = PermissionChecker(self.student_user, self.school)
        
        # Student should have very limited permissions
        self.assertFalse(checker.can_access_resource('teacher', 'view'))
        self.assertFalse(checker.can_access_resource('student', 'change'))


if __name__ == '__main__':
    import unittest
    unittest.main()