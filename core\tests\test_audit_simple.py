"""
Simple audit system tests
"""
from django.test import TestCase
from core.audit_system import <PERSON><PERSON><PERSON><PERSON><PERSON>, ComplianceMonitor, AuditReportGenerator
from core.models import School


class SimpleAuditTestCase(TestCase):
    """
    Simple audit system tests
    """
    
    def setUp(self):
        """
        Set up test data
        """
        # Create test school
        self.school = School.objects.create(
            name='Test School',
            code='TEST001',
            address='Test Address',
            phone='+1234567890',
            email='<EMAIL>',
            principal_name='Test Principal',
            established_date='2020-01-01'
        )
    
    def test_audit_logger_initialization(self):
        """
        Test audit logger can be initialized
        """
        logger = AuditLogger(self.school)
        self.assertEqual(logger.school, self.school)
        self.assertIsNotNone(logger.config)
    
    def test_compliance_monitor_initialization(self):
        """
        Test compliance monitor can be initialized
        """
        monitor = ComplianceMonitor(self.school)
        self.assertEqual(monitor.school, self.school)
    
    def test_report_generator_initialization(self):
        """
        Test report generator can be initialized
        """
        generator = AuditReportGenerator(self.school)
        self.assertEqual(generator.school, self.school)