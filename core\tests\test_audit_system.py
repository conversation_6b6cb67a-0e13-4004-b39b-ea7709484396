"""
Tests for audit system
"""
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

from core.models import School
from core.audit_models import (
    AuditEvent, AuditTrail, ComplianceRule, ComplianceViolation,
    AuditReport, AuditConfiguration
)
from core.audit_system import AuditLogger, ComplianceMonitor, AuditReportGenerator

User = get_user_model()


class AuditSystemTestCase(TestCase):
    """
    Base test case for audit system tests
    """
    
    def setUp(self):
        """
        Set up test data
        """
        # Create test school
        self.school = School.objects.create(
            name='Test School',
            code='TEST001',
            address='Test Address',
            phone='+1234567890',
            email='<EMAIL>',
            principal_name='Test Principal',
            established_date='2020-01-01'
        )
        
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )
        
        # Create audit configuration
        self.audit_config = AuditConfiguration.objects.create(
            school=self.school,
            retention_days=365,
            audit_create_events=True,
            audit_update_events=True,
            audit_delete_events=True,
            enable_compliance_monitoring=True
        )


class AuditEventTestCase(AuditSystemTestCase):
    """
    Test audit event model
    """
    
    def test_create_audit_event(self):
        """
        Test creating audit event
        """
        event = AuditEvent.objects.create(
            school=self.school,
            user=self.user,
            event_type='CREATE',
            event_category='Student Management',
            event_description='Created new student record',
            ip_address='***********',
            user_agent='Test Agent',
            severity='MEDIUM'
        )
        
        self.assertEqual(event.school, self.school)
        self.assertEqual(event.user, self.user)
        self.assertEqual(event.event_type, 'CREATE')
        self.assertEqual(event.severity, 'MEDIUM')
        self.assertTrue(event.is_successful)
    
    def test_audit_event_with_changes(self):
        """
        Test audit event with change tracking
        """
        old_values = {'name': 'John Doe', 'grade': '10'}
        new_values = {'name': 'John Smith', 'grade': '11'}
        changed_fields = ['name', 'grade']
        
        event = AuditEvent.objects.create(
            school=self.school,
            user=self.user,
            event_type='UPDATE',
            event_category='Student Management',
            event_description='Updated student record',
            old_values=old_values,
            new_values=new_values,
            changed_fields=changed_fields
        )
        
        self.assertEqual(event.old_values, old_values)
        self.assertEqual(event.new_values, new_values)
        self.assertEqual(event.changed_fields, changed_fields)
        
        # Test changes summary
        summary = event.get_changes_summary()
        self.assertIn('name: John Doe → John Smith', summary)
        self.assertIn('grade: 10 → 11', summary)
    
    def test_mark_as_reviewed(self):
        """
        Test marking audit event as reviewed
        """
        event = AuditEvent.objects.create(
            school=self.school,
            user=self.user,
            event_type='DELETE',
            event_category='Student Management',
            event_description='Deleted student record',
            requires_review=True
        )
        
        self.assertFalse(event.is_reviewed)
        
        # Mark as reviewed
        event.mark_as_reviewed(self.user, "Reviewed and approved")
        
        self.assertTrue(event.is_reviewed)
        self.assertEqual(event.reviewed_by, self.user)
        self.assertIsNotNone(event.reviewed_at)
        self.assertEqual(event.review_notes, "Reviewed and approved")


class AuditTrailTestCase(AuditSystemTestCase):
    """
    Test audit trail functionality
    """
    
    def test_create_audit_trail(self):
        """
        Test creating audit trail
        """
        trail = AuditTrail.objects.create(
            school=self.school,
            trail_name='Student Registration Process',
            description='Complete student registration workflow',
            created_by=self.user
        )
        
        self.assertEqual(trail.school, self.school)
        self.assertEqual(trail.trail_name, 'Student Registration Process')
        self.assertEqual(trail.created_by, self.user)
    
    def test_audit_trail_with_events(self):
        """
        Test audit trail with multiple events
        """
        # Create trail
        trail = AuditTrail.objects.create(
            school=self.school,
            trail_name='Student Update Process',
            created_by=self.user
        )
        
        # Create events
        event1 = AuditEvent.objects.create(
            school=self.school,
            user=self.user,
            event_type='VIEW',
            event_category='Student Management',
            event_description='Viewed student record'
        )
        
        event2 = AuditEvent.objects.create(
            school=self.school,
            user=self.user,
            event_type='UPDATE',
            event_category='Student Management',
            event_description='Updated student record'
        )
        
        # Add events to trail
        from core.audit_models import AuditTrailEvent
        AuditTrailEvent.objects.create(trail=trail, event=event1, sequence_number=1)
        AuditTrailEvent.objects.create(trail=trail, event=event2, sequence_number=2)
        
        # Check trail has events
        self.assertEqual(trail.events.count(), 2)


class ComplianceRuleTestCase(AuditSystemTestCase):
    """
    Test compliance rule functionality
    """
    
    def test_create_compliance_rule(self):
        """
        Test creating compliance rule
        """
        rule = ComplianceRule.objects.create(
            school=self.school,
            name='Sensitive Data Access Rule',
            description='Monitor access to sensitive student data',
            rule_type='DATA_ACCESS',
            conditions={
                'event_type': 'VIEW',
                'event_category': 'Student Management',
                'is_sensitive': True
            },
            actions=['log', 'notify'],
            severity='HIGH',
            created_by=self.user
        )
        
        self.assertEqual(rule.school, self.school)
        self.assertEqual(rule.name, 'Sensitive Data Access Rule')
        self.assertEqual(rule.rule_type, 'DATA_ACCESS')
        self.assertEqual(rule.severity, 'HIGH')
        self.assertTrue(rule.is_active)


class ComplianceViolationTestCase(AuditSystemTestCase):
    """
    Test compliance violation functionality
    """
    
    def test_create_compliance_violation(self):
        """
        Test creating compliance violation
        """
        # Create rule
        rule = ComplianceRule.objects.create(
            school=self.school,
            name='Test Rule',
            description='Test compliance rule',
            rule_type='DATA_ACCESS',
            created_by=self.user
        )
        
        # Create audit event
        event = AuditEvent.objects.create(
            school=self.school,
            user=self.user,
            event_type='VIEW',
            event_category='Student Management',
            event_description='Viewed sensitive data'
        )
        
        # Create violation
        violation = ComplianceViolation.objects.create(
            school=self.school,
            rule=rule,
            audit_event=event,
            violation_description='Unauthorized access to sensitive data',
            severity='HIGH'
        )
        
        self.assertEqual(violation.school, self.school)
        self.assertEqual(violation.rule, rule)
        self.assertEqual(violation.audit_event, event)
        self.assertEqual(violation.status, 'OPEN')
        self.assertEqual(violation.severity, 'HIGH')
    
    def test_resolve_violation(self):
        """
        Test resolving compliance violation
        """
        # Create rule and event
        rule = ComplianceRule.objects.create(
            school=self.school,
            name='Test Rule',
            description='Test rule',
            rule_type='DATA_ACCESS',
            created_by=self.user
        )
        
        event = AuditEvent.objects.create(
            school=self.school,
            user=self.user,
            event_type='VIEW',
            event_category='Test',
            event_description='Test event'
        )
        
        # Create violation
        violation = ComplianceViolation.objects.create(
            school=self.school,
            rule=rule,
            audit_event=event,
            violation_description='Test violation',
            severity='MEDIUM'
        )
        
        self.assertEqual(violation.status, 'OPEN')
        
        # Resolve violation
        violation.resolve(self.user, "False positive - user has proper authorization")
        
        self.assertEqual(violation.status, 'RESOLVED')
        self.assertEqual(violation.resolved_by, self.user)
        self.assertIsNotNone(violation.resolved_at)
        self.assertEqual(violation.resolution_notes, "False positive - user has proper authorization")


class AuditReportTestCase(AuditSystemTestCase):
    """
    Test audit report functionality
    """
    
    def test_create_audit_report(self):
        """
        Test creating audit report
        """
        date_from = timezone.now() - timedelta(days=30)
        date_to = timezone.now()
        
        report = AuditReport.objects.create(
            school=self.school,
            name='Monthly Activity Report',
            report_type='ACTIVITY_SUMMARY',
            description='Summary of all activities for the month',
            filters={'event_type': ['CREATE', 'UPDATE', 'DELETE']},
            date_from=date_from,
            date_to=date_to,
            format='PDF',
            generated_by=self.user
        )
        
        self.assertEqual(report.school, self.school)
        self.assertEqual(report.name, 'Monthly Activity Report')
        self.assertEqual(report.report_type, 'ACTIVITY_SUMMARY')
        self.assertEqual(report.format, 'PDF')
        self.assertEqual(report.generated_by, self.user)


class AuditConfigurationTestCase(AuditSystemTestCase):
    """
    Test audit configuration
    """
    
    def test_audit_configuration_creation(self):
        """
        Test audit configuration is created correctly
        """
        self.assertEqual(self.audit_config.school, self.school)
        self.assertEqual(self.audit_config.retention_days, 365)
        self.assertTrue(self.audit_config.audit_create_events)
        self.assertTrue(self.audit_config.audit_update_events)
        self.assertTrue(self.audit_config.audit_delete_events)
        self.assertTrue(self.audit_config.enable_compliance_monitoring)
    
    def test_audit_configuration_defaults(self):
        """
        Test audit configuration default values
        """
        # Create new school without explicit config
        new_school = School.objects.create(
            name='New School',
            code='NEW001',
            address='New Address',
            phone='+1234567891',
            email='<EMAIL>',
            principal_name='New Principal',
            established_date='2021-01-01'
        )
        
        config = AuditConfiguration.objects.create(school=new_school)
        
        self.assertEqual(config.retention_days, 2555)  # 7 years default
        self.assertTrue(config.audit_create_events)
        self.assertTrue(config.audit_update_events)
        self.assertTrue(config.audit_delete_events)
        self.assertFalse(config.audit_view_events)  # Default False
        self.assertTrue(config.mask_sensitive_data)
        self.assertTrue(config.enable_compliance_monitoring)


class AuditLoggerTestCase(AuditSystemTestCase):
    """
    Test audit logger functionality
    """
    
    def test_audit_logger_initialization(self):
        """
        Test audit logger can be initialized
        """
        logger = AuditLogger(self.school)
        self.assertEqual(logger.school, self.school)
    
    def test_log_event(self):
        """
        Test logging an audit event
        """
        logger = AuditLogger(self.school)
        
        # Mock request object
        class MockRequest:
            def __init__(self):
                self.user = self.user
                self.META = {
                    'REMOTE_ADDR': '***********',
                    'HTTP_USER_AGENT': 'Test Agent'
                }
                self.path = '/test/path'
                self.method = 'POST'
                self.session = {'session_key': 'test_session'}
        
        request = MockRequest()
        
        # Log event
        event = logger.log_event(
            request=request,
            event_type='CREATE',
            event_category='Test Category',
            description='Test event description',
            severity='MEDIUM'
        )
        
        self.assertIsInstance(event, AuditEvent)
        self.assertEqual(event.school, self.school)
        self.assertEqual(event.user, self.user)
        self.assertEqual(event.event_type, 'CREATE')
        self.assertEqual(event.event_category, 'Test Category')
        self.assertEqual(event.ip_address, '***********')
        self.assertEqual(event.user_agent, 'Test Agent')
        self.assertEqual(event.severity, 'MEDIUM')


class ComplianceMonitorTestCase(AuditSystemTestCase):
    """
    Test compliance monitor functionality
    """
    
    def test_compliance_monitor_initialization(self):
        """
        Test compliance monitor can be initialized
        """
        monitor = ComplianceMonitor(self.school)
        self.assertEqual(monitor.school, self.school)
    
    def test_check_compliance(self):
        """
        Test compliance checking
        """
        monitor = ComplianceMonitor(self.school)
        
        # Create a test event
        event = AuditEvent.objects.create(
            school=self.school,
            user=self.user,
            event_type='VIEW',
            event_category='Student Management',
            event_description='Viewed student record',
            is_sensitive=True
        )
        
        # Create a compliance rule
        rule = ComplianceRule.objects.create(
            school=self.school,
            name='Sensitive Data Access',
            description='Monitor sensitive data access',
            rule_type='DATA_ACCESS',
            conditions={'is_sensitive': True},
            severity='HIGH',
            created_by=self.user
        )
        
        # Check compliance (this would normally trigger rule evaluation)
        violations = monitor.check_event_compliance(event)
        
        # For now, this returns empty list as rule evaluation is not implemented
        self.assertIsInstance(violations, list)


class AuditReportGeneratorTestCase(AuditSystemTestCase):
    """
    Test audit report generator
    """
    
    def test_report_generator_initialization(self):
        """
        Test report generator can be initialized
        """
        generator = AuditReportGenerator(self.school)
        self.assertEqual(generator.school, self.school)
    
    def test_generate_activity_summary(self):
        """
        Test generating activity summary report
        """
        generator = AuditReportGenerator(self.school)
        
        # Create some test events
        AuditEvent.objects.create(
            school=self.school,
            user=self.user,
            event_type='CREATE',
            event_category='Student Management',
            event_description='Created student'
        )
        
        AuditEvent.objects.create(
            school=self.school,
            user=self.user,
            event_type='UPDATE',
            event_category='Student Management',
            event_description='Updated student'
        )
        
        date_from = timezone.now() - timedelta(days=1)
        date_to = timezone.now()
        
        # Generate report data
        report_data = generator.generate_activity_summary(date_from, date_to)
        
        self.assertIsInstance(report_data, dict)
        self.assertIn('total_events', report_data)
        self.assertIn('events_by_type', report_data)
        self.assertIn('events_by_category', report_data)
        self.assertEqual(report_data['total_events'], 2)


if __name__ == '__main__':
    import unittest
    unittest.main()