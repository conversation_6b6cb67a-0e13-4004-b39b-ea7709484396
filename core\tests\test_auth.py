"""
Authentication and authorization tests for core module
"""
from datetime import date
from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model, authenticate
from django.contrib.sessions.middleware import SessionMiddleware
from django.core.exceptions import PermissionDenied
from unittest.mock import patch, MagicMock
from .models import School, AcademicYear
from core.authentication.backends.backends import SchoolAuthBackend, MultiFactorAuthBackend, TokenAuthBackend
from .permissions import (
    SchoolPermission, IsSchoolAdmin, IsTeacher, IsStudent, IsParent,
    has_school_permission, require_school_permission
)
from .middleware import SchoolMiddleware, AuditMiddleware

User = get_user_model()


class SchoolAuthBackendTest(TestCase):
    """Test SchoolAuthBackend"""
    
    def setUp(self):
        self.school = School.objects.create(
            name='Test School',
            code='TEST001',
            address='123 Test Street',
            phone='+1234567890',
            email='<EMAIL>',
            principal_name='<PERSON>',
            established_date=date(2020, 1, 1)
        )
        
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.backend = SchoolAuthBackend()
    
    def test_authenticate_with_username(self):
        """Test authentication with username"""
        user = self.backend.authenticate(
            None, 
            username='testuser', 
            password='testpass123'
        )
        self.assertEqual(user, self.user)
    
    def test_authenticate_with_email(self):
        """Test authentication with email"""
        user = self.backend.authenticate(
            None, 
            username='<EMAIL>', 
            password='testpass123'
        )
        self.assertEqual(user, self.user)
    
    def test_authenticate_with_wrong_password(self):
        """Test authentication with wrong password"""
        user = self.backend.authenticate(
            None, 
            username='testuser', 
            password='wrongpass'
        )
        self.assertIsNone(user)
    
    def test_authenticate_with_nonexistent_user(self):
        """Test authentication with nonexistent user"""
        user = self.backend.authenticate(
            None, 
            username='nonexistent', 
            password='testpass123'
        )
        self.assertIsNone(user)
    
    def test_authenticate_inactive_user(self):
        """Test authentication with inactive user"""
        self.user.is_active = False
        self.user.save()
        
        user = self.backend.authenticate(
            None, 
            username='testuser', 
            password='testpass123'
        )
        self.assertIsNone(user)
    
    def test_get_user(self):
        """Test get_user method"""
        user = self.backend.get_user(self.user.id)
        self.assertEqual(user, self.user)
    
    def test_get_nonexistent_user(self):
        """Test get_user with nonexistent ID"""
        user = self.backend.get_user(99999)
        self.assertIsNone(user)


class MultiFactorAuthBackendTest(TestCase):
    """Test MultiFactorAuthBackend"""
    
    def setUp(self):
        self.school = School.objects.create(
            name='Test School',
            code='TEST001',
            address='123 Test Street',
            phone='+1234567890',
            email='<EMAIL>',
            principal_name='John Doe',
            established_date=date(2020, 1, 1)
        )
        
        self.superuser = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='adminpass123'
        )
        
        self.regular_user = User.objects.create_user(
            username='user',
            email='<EMAIL>',
            password='userpass123'
        )
        
        self.backend = MultiFactorAuthBackend()
        self.factory = RequestFactory()
    
    def test_mfa_required_for_superuser(self):
        """Test that MFA is required for superusers"""
        self.assertTrue(self.backend._is_mfa_required(self.superuser))
    
    def test_mfa_not_required_for_regular_user(self):
        """Test that MFA is not required for regular users"""
        self.assertFalse(self.backend._is_mfa_required(self.regular_user))
    
    @patch('core.backends.ModelBackend.authenticate')
    def test_authenticate_without_mfa_for_regular_user(self, mock_auth):
        """Test authentication without MFA for regular user"""
        mock_auth.return_value = self.regular_user
        
        request = self.factory.post('/login/')
        request.session = {}
        
        user = self.backend.authenticate(
            request,
            username='user',
            password='userpass123'
        )
        
        self.assertEqual(user, self.regular_user)
    
    @patch('core.backends.ModelBackend.authenticate')
    def test_authenticate_superuser_without_mfa_token(self, mock_auth):
        """Test authentication of superuser without MFA token raises exception"""
        mock_auth.return_value = self.superuser
        
        request = self.factory.post('/login/')
        request.session = {}
        
        with self.assertRaises(PermissionDenied):
            self.backend.authenticate(
                request,
                username='admin',
                password='adminpass123'
            )


class TokenAuthBackendTest(TestCase):
    """Test TokenAuthBackend"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.backend = TokenAuthBackend()
    
    def test_authenticate_without_token(self):
        """Test authentication without token"""
        user = self.backend.authenticate(None)
        self.assertIsNone(user)
    
    def test_validate_token_format_valid(self):
        """Test token format validation with valid token"""
        valid_token = "header.payload.signature"
        self.assertTrue(self.backend._validate_token_format(valid_token))
    
    def test_validate_token_format_invalid(self):
        """Test token format validation with invalid token"""
        invalid_token = "invalid.token"
        self.assertFalse(self.backend._validate_token_format(invalid_token))


class SchoolPermissionTest(TestCase):
    """Test SchoolPermission class"""
    
    def setUp(self):
        self.school1 = School.objects.create(
            name='School 1',
            code='SCH001',
            address='123 Test Street',
            phone='+1234567890',
            email='<EMAIL>',
            principal_name='Principal 1',
            established_date=date(2020, 1, 1)
        )
        
        self.school2 = School.objects.create(
            name='School 2',
            code='SCH002',
            address='456 Test Avenue',
            phone='+1234567891',
            email='<EMAIL>',
            principal_name='Principal 2',
            established_date=date(2020, 1, 1)
        )
        
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.superuser = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='adminpass123'
        )
        
        self.permission = SchoolPermission()
        self.factory = RequestFactory()
    
    def test_unauthenticated_user_no_permission(self):
        """Test that unauthenticated users have no permission"""
        request = self.factory.get('/')
        request.user = MagicMock()
        request.user.is_authenticated = False
        
        self.assertFalse(self.permission.has_permission(request, None))
    
    def test_superuser_has_permission(self):
        """Test that superuser has permission"""
        request = self.factory.get('/')
        request.user = self.superuser
        
        self.assertTrue(self.permission.has_permission(request, None))
    
    def test_user_without_school_no_permission(self):
        """Test that user without school has no permission"""
        request = self.factory.get('/')
        request.user = self.user
        
        self.assertFalse(self.permission.has_permission(request, None))
    
    def test_superuser_object_permission(self):
        """Test that superuser has object permission"""
        request = self.factory.get('/')
        request.user = self.superuser
        
        # Create a mock object with school
        obj = MagicMock()
        obj.school = self.school1
        
        self.assertTrue(self.permission.has_object_permission(request, None, obj))
    
    def test_object_without_school_attribute(self):
        """Test object permission for objects without school attribute"""
        request = self.factory.get('/')
        request.user = self.user
        
        # Mock user school
        with patch.object(self.permission, '_get_user_school', return_value=self.school1):
            obj = MagicMock()
            # Object without school attribute
            del obj.school
            
            self.assertTrue(self.permission.has_object_permission(request, None, obj))


class PermissionClassTest(TestCase):
    """Test various permission classes"""
    
    def setUp(self):
        self.school = School.objects.create(
            name='Test School',
            code='TEST001',
            address='123 Test Street',
            phone='+1234567890',
            email='<EMAIL>',
            principal_name='John Doe',
            established_date=date(2020, 1, 1)
        )
        
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.superuser = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='adminpass123'
        )
        
        self.factory = RequestFactory()
    
    def test_is_school_admin_permission(self):
        """Test IsSchoolAdmin permission"""
        permission = IsSchoolAdmin()
        
        # Unauthenticated user
        request = self.factory.get('/')
        request.user = MagicMock()
        request.user.is_authenticated = False
        self.assertFalse(permission.has_permission(request, None))
        
        # Superuser
        request.user = self.superuser
        self.assertTrue(permission.has_permission(request, None))
        
        # Regular user without employee
        request.user = self.user
        self.assertFalse(permission.has_permission(request, None))
    
    def test_is_teacher_permission(self):
        """Test IsTeacher permission"""
        permission = IsTeacher()
        
        # Superuser
        request = self.factory.get('/')
        request.user = self.superuser
        self.assertTrue(permission.has_permission(request, None))
        
        # Regular user without employee
        request.user = self.user
        self.assertFalse(permission.has_permission(request, None))
    
    def test_is_student_permission(self):
        """Test IsStudent permission"""
        permission = IsStudent()
        
        # Superuser
        request = self.factory.get('/')
        request.user = self.superuser
        self.assertTrue(permission.has_permission(request, None))
        
        # Regular user without student
        request.user = self.user
        self.assertFalse(permission.has_permission(request, None))
    
    def test_is_parent_permission(self):
        """Test IsParent permission"""
        permission = IsParent()
        
        # Superuser
        request = self.factory.get('/')
        request.user = self.superuser
        self.assertTrue(permission.has_permission(request, None))
        
        # Regular user without parent
        request.user = self.user
        self.assertFalse(permission.has_permission(request, None))


class PermissionUtilityTest(TestCase):
    """Test permission utility functions"""
    
    def setUp(self):
        self.school1 = School.objects.create(
            name='School 1',
            code='SCH001',
            address='123 Test Street',
            phone='+1234567890',
            email='<EMAIL>',
            principal_name='Principal 1',
            established_date=date(2020, 1, 1)
        )
        
        self.school2 = School.objects.create(
            name='School 2',
            code='SCH002',
            address='456 Test Avenue',
            phone='+1234567891',
            email='<EMAIL>',
            principal_name='Principal 2',
            established_date=date(2020, 1, 1)
        )
        
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.superuser = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='adminpass123'
        )
    
    def test_has_school_permission_superuser(self):
        """Test has_school_permission for superuser"""
        self.assertTrue(has_school_permission(self.superuser, self.school1))
        self.assertTrue(has_school_permission(self.superuser, self.school2))
    
    def test_has_school_permission_regular_user(self):
        """Test has_school_permission for regular user without school"""
        self.assertFalse(has_school_permission(self.user, self.school1))
        self.assertFalse(has_school_permission(self.user, self.school2))
    
    def test_require_school_permission_success(self):
        """Test require_school_permission with valid permission"""
        # Should not raise exception for superuser
        try:
            require_school_permission(self.superuser, self.school1)
        except PermissionDenied:
            self.fail("require_school_permission raised PermissionDenied unexpectedly")
    
    def test_require_school_permission_failure(self):
        """Test require_school_permission with invalid permission"""
        # Should raise exception for regular user
        with self.assertRaises(PermissionDenied):
            require_school_permission(self.user, self.school1)


class SchoolMiddlewareTest(TestCase):
    """Test SchoolMiddleware"""
    
    def setUp(self):
        self.school = School.objects.create(
            name='Test School',
            code='TEST001',
            address='123 Test Street',
            phone='+1234567890',
            email='<EMAIL>',
            principal_name='John Doe',
            established_date=date(2020, 1, 1)
        )
        
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.factory = RequestFactory()
        self.middleware = SchoolMiddleware(lambda r: None)
    
    def test_school_from_header(self):
        """Test setting school from header"""
        request = self.factory.get('/', HTTP_X_SCHOOL_CODE='TEST001')
        request.user = MagicMock()
        request.user.is_authenticated = False
        
        self.middleware.process_request(request)
        
        self.assertEqual(request.school, self.school)
    
    def test_school_from_subdomain(self):
        """Test setting school from subdomain"""
        from django.test.utils import override_settings
        
        with override_settings(ALLOWED_HOSTS=['test001.example.com']):
            request = self.factory.get('/', HTTP_HOST='test001.example.com')
            request.user = MagicMock()
            request.user.is_authenticated = False
            
            self.middleware.process_request(request)
            
            self.assertEqual(request.school, self.school)
    
    def test_no_school_found(self):
        """Test when no school is found"""
        request = self.factory.get('/')
        request.user = MagicMock()
        request.user.is_authenticated = False
        
        self.middleware.process_request(request)
        
        self.assertIsNone(request.school)


class AuditMiddlewareTest(TestCase):
    """Test AuditMiddleware"""
    
    def setUp(self):
        self.school = School.objects.create(
            name='Test School',
            code='TEST001',
            address='123 Test Street',
            phone='+1234567890',
            email='<EMAIL>',
            principal_name='John Doe',
            established_date=date(2020, 1, 1)
        )
        
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.factory = RequestFactory()
        self.middleware = AuditMiddleware(lambda r: None)
    
    def test_get_action_from_method(self):
        """Test action determination from HTTP method"""
        self.assertEqual(self.middleware._get_action('POST', 200), 'CREATE')
        self.assertEqual(self.middleware._get_action('PUT', 200), 'UPDATE')
        self.assertEqual(self.middleware._get_action('PATCH', 200), 'UPDATE')
        self.assertEqual(self.middleware._get_action('DELETE', 200), 'DELETE')
        self.assertIsNone(self.middleware._get_action('POST', 400))  # Failed request
    
    def test_extract_model_info_api(self):
        """Test model info extraction from API URL"""
        request = self.factory.post('/api/students/123/')
        model_name, object_id = self.middleware._extract_model_info(request)
        
        self.assertEqual(model_name, 'Student')
        self.assertEqual(object_id, '123')
    
    def test_extract_model_info_regular(self):
        """Test model info extraction from regular URL"""
        request = self.factory.post('/students/456/edit/')
        model_name, object_id = self.middleware._extract_model_info(request)
        
        self.assertEqual(model_name, 'Student')
        self.assertEqual(object_id, '456')
    
    def test_get_object_repr(self):
        """Test object representation generation"""
        repr_str = self.middleware._get_object_repr('Student', '123', self.school)
        self.assertEqual(repr_str, 'Student #123')
    
    def test_extract_changes_json(self):
        """Test changes extraction from JSON request"""
        import json
        
        request = self.factory.post(
            '/api/students/',
            data=json.dumps({'name': 'John Doe', 'age': 15}),
            content_type='application/json'
        )
        
        changes = self.middleware._extract_changes(request)
        self.assertEqual(changes, {'name': 'John Doe', 'age': 15})
    
    def test_extract_changes_form(self):
        """Test changes extraction from form request"""
        request = self.factory.post(
            '/students/',
            data={'name': 'John Doe', 'age': '15', 'csrfmiddlewaretoken': 'token'}
        )
        
        changes = self.middleware._extract_changes(request)
        # CSRF token should be removed
        self.assertNotIn('csrfmiddlewaretoken', changes)
        self.assertIn('name', changes)
        self.assertIn('age', changes)