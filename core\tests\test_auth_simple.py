"""
Simple authentication tests
"""
from django.test import TestCase
from django.contrib.auth import get_user_model
from core.auth_utils import MFAUtils, PasswordUtils, SecurityUtils

User = get_user_model()


class SimpleAuthTestCase(TestCase):
    """
    Simple authentication tests
    """
    
    def test_mfa_utils(self):
        """
        Test MFA utilities
        """
        # Test TOTP secret generation
        secret = MFAUtils.generate_totp_secret()
        self.assertIsInstance(secret, str)
        self.assertEqual(len(secret), 32)
        
        # Test backup code generation
        codes = MFAUtils.generate_backup_codes(count=5)
        self.assertEqual(len(codes), 5)
    
    def test_password_utils(self):
        """
        Test password utilities
        """
        # Test password strength validation
        strong_password = 'TestPass123!'
        errors = PasswordUtils.validate_password_strength(strong_password)
        self.assertEqual(len(errors), 0)
        
        weak_password = 'weak'
        errors = PasswordUtils.validate_password_strength(weak_password)
        self.assertGreater(len(errors), 0)
        
        # Test secure password generation
        password = PasswordUtils.generate_secure_password(length=12)
        self.assertEqual(len(password), 12)
    
    def test_security_utils(self):
        """
        Test security utilities
        """
        # Test token generation
        token = SecurityUtils.generate_secure_token(length=32)
        self.assertIsInstance(token, str)
        
        # Test data hashing
        data = "test data"
        hash_result = SecurityUtils.hash_data(data)
        self.assertIn('hash', hash_result)
        self.assertIn('salt', hash_result)
        
        # Test hash verification
        is_valid = SecurityUtils.verify_hash(data, hash_result['hash'], hash_result['salt'])
        self.assertTrue(is_valid)