"""
Tests for authentication system
"""
import pytest
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from django.core.cache import cache
from unittest.mock import patch, MagicMock
from datetime import timed<PERSON><PERSON>

from core.models import School
from core.auth_models import (
    MFADevice, MFABackupCode, LoginAttempt, 
    PasswordPolicy, PasswordHistory, SessionSecurity
)
from core.auth_utils import (
    MFAUtils, PasswordUtils, SecurityUtils, 
    JWTUtils, SessionUtils
)

User = get_user_model()


class AuthenticationTestCase(TestCase):
    """
    Base test case for authentication tests
    """
    
    def setUp(self):
        """
        Set up test data
        """
        self.client = Client()
        
        # Create test school
        self.school = School.objects.create(
            name='Test School',
            code='TEST001',
            address='Test Address',
            phone='+1234567890',
            email='<EMAIL>',
            principal_name='Test Principal',
            established_date='2020-01-01'
        )
        
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )
        
        # Create password policy
        self.password_policy = PasswordPolicy.objects.create(
            school=self.school,
            min_length=8,
            require_uppercase=True,
            require_lowercase=True,
            require_numbers=True,
            require_special_chars=True,
            max_age_days=90,
            history_count=5,
            lockout_attempts=5,
            lockout_duration_minutes=30
        )


class MFAUtilsTestCase(AuthenticationTestCase):
    """
    Test MFA utilities
    """
    
    def test_generate_totp_secret(self):
        """
        Test TOTP secret generation
        """
        secret = MFAUtils.generate_totp_secret()
        self.assertIsInstance(secret, str)
        self.assertEqual(len(secret), 32)  # Base32 encoded 20 bytes
    
    @patch('pyotp.TOTP')
    def test_verify_totp_token(self, mock_totp):
        """
        Test TOTP token verification
        """
        mock_totp_instance = MagicMock()
        mock_totp_instance.verify.return_value = True
        mock_totp.return_value = mock_totp_instance
        
        secret = 'TESTSECRET123456'
        token = '123456'
        
        result = MFAUtils.verify_totp_token(secret, token)
        self.assertTrue(result)
        mock_totp.assert_called_once_with(secret)
        mock_totp_instance.verify.assert_called_once_with(token, valid_window=1)
    
    def test_generate_backup_codes(self):
        """
        Test backup code generation
        """
        codes = MFAUtils.generate_backup_codes(count=5)
        self.assertEqual(len(codes), 5)
        for code in codes:
            self.assertIsInstance(code, str)
            self.assertEqual(len(code), 8)  # 4 bytes hex = 8 chars


class PasswordUtilsTestCase(AuthenticationTestCase):
    """
    Test password utilities
    """
    
    def test_validate_password_strength_valid(self):
        """
        Test password strength validation with valid password
        """
        password = 'TestPass123!'
        errors = PasswordUtils.validate_password_strength(password, self.password_policy)
        self.assertEqual(len(errors), 0)
    
    def test_validate_password_strength_invalid(self):
        """
        Test password strength validation with invalid password
        """
        password = 'weak'
        errors = PasswordUtils.validate_password_strength(password, self.password_policy)
        self.assertGreater(len(errors), 0)
    
    def test_check_password_history(self):
        """
        Test password history checking
        """
        # Add password to history
        PasswordHistory.add_password(self.user, 'oldpassword123')
        
        # Check if old password is detected
        result = PasswordUtils.check_password_history(self.user, 'oldpassword123')
        self.assertFalse(result)
        
        # Check if new password is allowed
        result = PasswordUtils.check_password_history(self.user, 'newpassword123')
        self.assertTrue(result)
    
    def test_generate_secure_password(self):
        """
        Test secure password generation
        """
        password = PasswordUtils.generate_secure_password(length=12)
        self.assertEqual(len(password), 12)
        
        # Check if password meets requirements
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
        
        self.assertTrue(has_upper)
        self.assertTrue(has_lower)
        self.assertTrue(has_digit)
        self.assertTrue(has_special)


class SecurityUtilsTestCase(AuthenticationTestCase):
    """
    Test security utilities
    """
    
    def test_get_client_ip(self):
        """
        Test client IP extraction
        """
        # Mock request with X-Forwarded-For header
        request = MagicMock()
        request.META = {'HTTP_X_FORWARDED_FOR': '***********,********'}
        
        ip = SecurityUtils.get_client_ip(request)
        self.assertEqual(ip, '***********')
        
        # Mock request with REMOTE_ADDR
        request.META = {'REMOTE_ADDR': '***********'}
        ip = SecurityUtils.get_client_ip(request)
        self.assertEqual(ip, '***********')
    
    def test_is_suspicious_login(self):
        """
        Test suspicious login detection
        """
        # Create successful login from different IP
        LoginAttempt.objects.create(
            user=self.user,
            username=self.user.username,
            ip_address='***********',
            user_agent='Test Agent',
            success=True,
            timestamp=timezone.now() - timedelta(days=1)
        )
        
        # Check if login from different IP is suspicious
        result = SecurityUtils.is_suspicious_login(
            self.user, '***********', 'Test Agent'
        )
        self.assertTrue(result)
        
        # Check if login from same IP is not suspicious
        result = SecurityUtils.is_suspicious_login(
            self.user, '***********', 'Test Agent'
        )
        self.assertFalse(result)
    
    def test_is_account_locked(self):
        """
        Test account lockout detection
        """
        # Create multiple failed attempts
        for i in range(6):
            LoginAttempt.objects.create(
                user=self.user,
                username=self.user.username,
                ip_address='***********',
                user_agent='Test Agent',
                success=False,
                timestamp=timezone.now() - timedelta(minutes=i)
            )
        
        result = SecurityUtils.is_account_locked(self.user)
        self.assertTrue(result)


class JWTUtilsTestCase(AuthenticationTestCase):
    """
    Test JWT utilities
    """
    
    @patch('jwt.encode')
    def test_create_token(self, mock_encode):
        """
        Test JWT token creation
        """
        mock_encode.return_value = 'test.jwt.token'
        
        token = JWTUtils.create_token(self.user, expires_in=3600)
        self.assertEqual(token, 'test.jwt.token')
        mock_encode.assert_called_once()
    
    @patch('jwt.decode')
    def test_verify_token(self, mock_decode):
        """
        Test JWT token verification
        """
        mock_decode.return_value = {'user_id': str(self.user.id), 'username': self.user.username}
        
        payload = JWTUtils.verify_token('test.jwt.token')
        self.assertEqual(payload['user_id'], str(self.user.id))
        mock_decode.assert_called_once()


class MFADeviceTestCase(AuthenticationTestCase):
    """
    Test MFA device model
    """
    
    def test_create_mfa_device(self):
        """
        Test MFA device creation
        """
        device = MFADevice.objects.create(
            user=self.user,
            device_type='totp',
            name='Test Device',
            secret_key='encrypted_secret',
            is_primary=True
        )
        
        self.assertEqual(device.user, self.user)
        self.assertEqual(device.device_type, 'totp')
        self.assertTrue(device.is_primary)
    
    def test_primary_device_uniqueness(self):
        """
        Test that only one device can be primary
        """
        # Create first primary device
        device1 = MFADevice.objects.create(
            user=self.user,
            device_type='totp',
            name='Device 1',
            secret_key='secret1',
            is_primary=True
        )
        
        # Create second primary device
        device2 = MFADevice.objects.create(
            user=self.user,
            device_type='totp',
            name='Device 2',
            secret_key='secret2',
            is_primary=True
        )
        
        # Refresh first device from database
        device1.refresh_from_db()
        
        # First device should no longer be primary
        self.assertFalse(device1.is_primary)
        self.assertTrue(device2.is_primary)


class MFABackupCodeTestCase(AuthenticationTestCase):
    """
    Test MFA backup code model
    """
    
    def test_generate_backup_codes(self):
        """
        Test backup code generation
        """
        codes = MFABackupCode.generate_codes(self.user, count=5)
        self.assertEqual(len(codes), 5)
        
        # Check that codes are stored in database
        db_codes = MFABackupCode.objects.filter(user=self.user)
        self.assertEqual(db_codes.count(), 5)
    
    def test_backup_code_usage(self):
        """
        Test backup code usage
        """
        codes = MFABackupCode.generate_codes(self.user, count=1)
        code = codes[0]
        
        # Get backup code from database
        backup_code = MFABackupCode.objects.get(user=self.user, code=code)
        self.assertFalse(backup_code.is_used)
        
        # Mark as used
        backup_code.is_used = True
        backup_code.used_at = timezone.now()
        backup_code.save()
        
        # Verify it's marked as used
        backup_code.refresh_from_db()
        self.assertTrue(backup_code.is_used)
        self.assertIsNotNone(backup_code.used_at)


class LoginAttemptTestCase(AuthenticationTestCase):
    """
    Test login attempt model
    """
    
    def test_create_login_attempt(self):
        """
        Test login attempt creation
        """
        attempt = LoginAttempt.objects.create(
            user=self.user,
            username=self.user.username,
            ip_address='***********',
            user_agent='Test Agent',
            success=True
        )
        
        self.assertEqual(attempt.user, self.user)
        self.assertTrue(attempt.success)
        self.assertEqual(attempt.ip_address, '***********')


class PasswordPolicyTestCase(AuthenticationTestCase):
    """
    Test password policy model
    """
    
    def test_validate_password(self):
        """
        Test password validation against policy
        """
        # Valid password
        errors = self.password_policy.validate_password('TestPass123!')
        self.assertEqual(len(errors), 0)
        
        # Invalid password - too short
        errors = self.password_policy.validate_password('Test1!')
        self.assertGreater(len(errors), 0)
        
        # Invalid password - no uppercase
        errors = self.password_policy.validate_password('testpass123!')
        self.assertGreater(len(errors), 0)


class PasswordHistoryTestCase(AuthenticationTestCase):
    """
    Test password history model
    """
    
    def test_add_password(self):
        """
        Test adding password to history
        """
        PasswordHistory.add_password(self.user, 'testpassword123')
        
        history = PasswordHistory.objects.filter(user=self.user)
        self.assertEqual(history.count(), 1)
    
    def test_check_password_reuse(self):
        """
        Test password reuse checking
        """
        password = 'testpassword123'
        PasswordHistory.add_password(self.user, password)
        
        # Check if password reuse is detected
        result = PasswordHistory.check_password_reuse(self.user, password)
        self.assertTrue(result)
        
        # Check if different password is allowed
        result = PasswordHistory.check_password_reuse(self.user, 'differentpassword123')
        self.assertFalse(result)


class SessionSecurityTestCase(AuthenticationTestCase):
    """
    Test session security model
    """
    
    def test_create_session_security(self):
        """
        Test session security creation
        """
        session = SessionSecurity.objects.create(
            user=self.user,
            session_key='test_session_key',
            ip_address='***********',
            user_agent='Test Agent'
        )
        
        self.assertEqual(session.user, self.user)
        self.assertEqual(session.session_key, 'test_session_key')
        self.assertTrue(session.is_active)
    
    def test_session_expiry(self):
        """
        Test session expiry detection
        """
        # Create session with old last_activity
        session = SessionSecurity.objects.create(
            user=self.user,
            session_key='test_session_key',
            ip_address='***********',
            user_agent='Test Agent'
        )
        
        # Manually set old last_activity
        session.last_activity = timezone.now() - timedelta(hours=25)
        session.save()
        
        # Check if session is expired
        self.assertTrue(session.is_expired())


@pytest.mark.django_db
class AuthenticationIntegrationTestCase:
    """
    Integration tests for authentication system
    """
    
    def test_login_flow(self):
        """
        Test complete login flow
        """
        client = Client()
        
        # Create test user
        user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        # Test login
        response = client.post('/auth/login/', {
            'username': 'testuser',
            'password': 'testpass123'
        })
        
        # Should redirect after successful login
        assert response.status_code in [200, 302]
    
    def test_mfa_flow(self):
        """
        Test MFA flow
        """
        client = Client()
        
        # Create test user with MFA device
        user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        MFADevice.objects.create(
            user=user,
            device_type='totp',
            name='Test Device',
            secret_key='encrypted_secret'
        )
        
        # Test login - should require MFA
        response = client.post('/auth/login/', {
            'username': 'testuser',
            'password': 'testpass123'
        })
        
        # Should show MFA form
        assert 'mfa_required' in str(response.content) or response.status_code == 302
    
    def test_password_change_flow(self):
        """
        Test password change flow
        """
        client = Client()
        
        # Create and login user
        user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        client.force_login(user)
        
        # Test password change
        response = client.post('/auth/password/change/', {
            'current_password': 'testpass123',
            'new_password': 'NewPass123!',
            'confirm_password': 'NewPass123!'
        })
        
        # Should redirect after successful change
        assert response.status_code in [200, 302]
        
        # Verify password was changed
        user.refresh_from_db()
        assert user.check_password('NewPass123!')


if __name__ == '__main__':
    pytest.main([__file__])