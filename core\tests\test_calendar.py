"""
Tests for calendar utilities and functionality.
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import date, datetime, timedelta
from unittest.mock import patch

from core.calendar.calendar_utils import Hij<PERSON><PERSON><PERSON><PERSON><PERSON>, CalendarConverter, CalendarPreferences, CalendarWidget
from core.calendar.calendar_models import CalendarPreference, CalendarEvent, CalendarEventAttendee, CalendarEventReminder

User = get_user_model()


class HijriCalendarTest(TestCase):
    """Test Hijri calendar utilities."""
    
    def test_gregorian_to_hijri_conversion(self):
        """Test conversion from Gregorian to Hijri date."""
        # Test known conversion: 2023-01-01 should be around 1444-06-08
        gregorian_date = date(2023, 1, 1)
        hijri_year, hijri_month, hijri_day = HijriCalendar.gregorian_to_hijri(gregorian_date)
        
        # The conversion should be approximately correct (within a day or two)
        self.assertEqual(hijri_year, 1444)
        self.assertIn(hijri_month, [5, 6, 7])  # Allow some variance
        self.assertGreater(hijri_day, 1)
        self.assertLess(hijri_day, 30)
    
    def test_hijri_to_gregorian_conversion(self):
        """Test conversion from Hijri to Gregorian date."""
        # Test conversion: 1444-06-08 should be around 2023-01-01
        gregorian_date = HijriCalendar.hijri_to_gregorian(1444, 6, 8)
        
        # Should be close to 2023-01-01
        self.assertEqual(gregorian_date.year, 2023)
        self.assertIn(gregorian_date.month, [12, 1, 2])  # Allow some variance
    
    def test_round_trip_conversion(self):
        """Test that converting Gregorian->Hijri->Gregorian gives similar result."""
        original_date = date(2023, 6, 15)
        
        # Convert to Hijri and back
        hijri_year, hijri_month, hijri_day = HijriCalendar.gregorian_to_hijri(original_date)
        converted_back = HijriCalendar.hijri_to_gregorian(hijri_year, hijri_month, hijri_day)
        
        # Should be within a few days of the original
        difference = abs((converted_back - original_date).days)
        self.assertLessEqual(difference, 2)
    
    def test_get_hijri_month_name(self):
        """Test getting Hijri month names."""
        # Test Arabic names
        self.assertEqual(HijriCalendar.get_hijri_month_name(1, 'ar'), 'محرم')
        self.assertEqual(HijriCalendar.get_hijri_month_name(9, 'ar'), 'رمضان')
        self.assertEqual(HijriCalendar.get_hijri_month_name(12, 'ar'), 'ذو الحجة')
        
        # Test English names
        self.assertEqual(HijriCalendar.get_hijri_month_name(1, 'en'), 'Muharram')
        self.assertEqual(HijriCalendar.get_hijri_month_name(9, 'en'), 'Ramadan')
        
        # Test invalid month
        self.assertEqual(HijriCalendar.get_hijri_month_name(13, 'ar'), '')
        self.assertEqual(HijriCalendar.get_hijri_month_name(0, 'en'), '')
    
    def test_format_hijri_date(self):
        """Test formatting Hijri dates."""
        test_date = date(2023, 1, 1)
        
        # Test Arabic formatting
        arabic_format = HijriCalendar.format_hijri_date(test_date, 'ar', include_day=True)
        self.assertIn('هـ', arabic_format)
        self.assertTrue(any(month in arabic_format for month in HijriCalendar.HIJRI_MONTHS_AR))
        
        # Test English formatting
        english_format = HijriCalendar.format_hijri_date(test_date, 'en', include_day=True)
        self.assertIn('AH', english_format)
        self.assertTrue(any(month in english_format for month in HijriCalendar.HIJRI_MONTHS_EN))
        
        # Test without day name
        no_day_format = HijriCalendar.format_hijri_date(test_date, 'ar', include_day=False)
        self.assertNotIn('الاثنين', no_day_format)  # Should not contain day name
    
    def test_is_hijri_leap_year(self):
        """Test Hijri leap year detection."""
        # Test some known leap years (approximate)
        self.assertTrue(HijriCalendar.is_hijri_leap_year(1444))
        
        # Test the leap year calculation logic
        for year in range(1440, 1450):
            result = HijriCalendar.is_hijri_leap_year(year)
            self.assertIsInstance(result, bool)
    
    def test_get_hijri_days_in_month(self):
        """Test getting days in Hijri months."""
        # Test odd months (should have 30 days)
        self.assertEqual(HijriCalendar.get_hijri_days_in_month(1444, 1), 30)
        self.assertEqual(HijriCalendar.get_hijri_days_in_month(1444, 3), 30)
        
        # Test even months (should have 29 days)
        self.assertEqual(HijriCalendar.get_hijri_days_in_month(1444, 2), 29)
        self.assertEqual(HijriCalendar.get_hijri_days_in_month(1444, 4), 29)
        
        # Test month 12 (depends on leap year)
        leap_year_days = HijriCalendar.get_hijri_days_in_month(1444, 12)
        non_leap_year_days = HijriCalendar.get_hijri_days_in_month(1443, 12)
        
        self.assertIn(leap_year_days, [29, 30])
        self.assertIn(non_leap_year_days, [29, 30])


class CalendarConverterTest(TestCase):
    """Test calendar converter utilities."""
    
    def test_get_dual_date_display(self):
        """Test dual date display formatting."""
        test_date = date(2023, 1, 1)
        
        # Test Arabic dual display
        arabic_dual = CalendarConverter.get_dual_date_display(test_date, 'ar')
        self.assertIn('م', arabic_dual)  # Gregorian marker
        self.assertIn('هـ', arabic_dual)  # Hijri marker
        self.assertIn('/', arabic_dual)  # Separator
        
        # Test English dual display
        english_dual = CalendarConverter.get_dual_date_display(test_date, 'en')
        self.assertIn('AH', english_dual)  # Hijri marker
        self.assertIn('/', english_dual)  # Separator
    
    def test_get_calendar_events_for_date(self):
        """Test getting calendar events for a specific date."""
        # Test New Year's Day
        new_year = date(2023, 1, 1)
        events = CalendarConverter.get_calendar_events_for_date(new_year, 'gregorian')
        
        # Should find New Year's Day event
        new_year_events = [e for e in events if 'New Year' in e['name']['name_en']]
        self.assertTrue(len(new_year_events) > 0)
        
        # Test Saudi National Day
        national_day = date(2023, 9, 23)
        events = CalendarConverter.get_calendar_events_for_date(national_day, 'gregorian')
        
        # Should find National Day event
        national_events = [e for e in events if 'National Day' in e['name']['name_en']]
        self.assertTrue(len(national_events) > 0)
        
        # Test both calendar types
        both_events = CalendarConverter.get_calendar_events_for_date(new_year, 'both')
        gregorian_only = CalendarConverter.get_calendar_events_for_date(new_year, 'gregorian')
        
        # Both should include at least as many events as Gregorian only
        self.assertGreaterEqual(len(both_events), len(gregorian_only))


class CalendarPreferencesTest(TestCase):
    """Test calendar preferences utilities."""
    
    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass'
        )
    
    def test_get_default_preferences(self):
        """Test getting default preferences for user without saved preferences."""
        preferences = CalendarPreferences.get_user_preferences(self.user)
        
        self.assertEqual(preferences['primary_calendar'], 'gregorian')
        self.assertTrue(preferences['show_dual_dates'])
        self.assertEqual(preferences['hijri_adjustment'], 0)
        self.assertEqual(preferences['weekend_days'], [5, 6])
        self.assertEqual(preferences['first_day_of_week'], 6)
    
    def test_set_and_get_preferences(self):
        """Test setting and getting user preferences."""
        new_preferences = {
            'primary_calendar': 'hijri',
            'show_dual_dates': False,
            'hijri_adjustment': 1,
            'weekend_days': [4, 5],
            'first_day_of_week': 0,
        }
        
        CalendarPreferences.set_user_preferences(self.user, new_preferences)
        retrieved_preferences = CalendarPreferences.get_user_preferences(self.user)
        
        self.assertEqual(retrieved_preferences['primary_calendar'], 'hijri')
        self.assertFalse(retrieved_preferences['show_dual_dates'])
        self.assertEqual(retrieved_preferences['hijri_adjustment'], 1)
        self.assertEqual(retrieved_preferences['weekend_days'], [4, 5])
        self.assertEqual(retrieved_preferences['first_day_of_week'], 0)
    
    def test_get_formatted_date(self):
        """Test formatted date according to user preferences."""
        test_date = date(2023, 1, 1)
        
        # Test with default preferences (Gregorian primary, dual dates)
        formatted = CalendarPreferences.get_formatted_date(test_date, self.user, 'ar')
        self.assertIn('/', formatted)  # Should show dual dates
        
        # Test with Hijri primary, no dual dates
        CalendarPreferences.set_user_preferences(self.user, {
            'primary_calendar': 'hijri',
            'show_dual_dates': False
        })
        
        formatted_hijri = CalendarPreferences.get_formatted_date(test_date, self.user, 'ar')
        self.assertIn('هـ', formatted_hijri)  # Should show Hijri
        self.assertNotIn('م', formatted_hijri)  # Should not show Gregorian marker


class CalendarWidgetTest(TestCase):
    """Test calendar widget utilities."""
    
    def test_generate_gregorian_month(self):
        """Test generating Gregorian month calendar."""
        calendar_data = CalendarWidget.generate_month_calendar(2023, 1, 'gregorian', 'en')
        
        self.assertEqual(calendar_data['type'], 'gregorian')
        self.assertEqual(calendar_data['year'], 2023)
        self.assertEqual(calendar_data['month'], 1)
        self.assertEqual(calendar_data['language'], 'en')
        self.assertIn('January', calendar_data['month_name'])
        self.assertIsInstance(calendar_data['calendar_matrix'], list)
        
        # Check that calendar matrix has weeks
        self.assertGreater(len(calendar_data['calendar_matrix']), 0)
        
        # Check that each week has 7 days
        for week in calendar_data['calendar_matrix']:
            self.assertEqual(len(week), 7)
    
    def test_generate_hijri_month(self):
        """Test generating Hijri month calendar."""
        calendar_data = CalendarWidget.generate_month_calendar(1444, 6, 'hijri', 'ar')
        
        self.assertEqual(calendar_data['type'], 'hijri')
        self.assertEqual(calendar_data['year'], 1444)
        self.assertEqual(calendar_data['month'], 6)
        self.assertEqual(calendar_data['language'], 'ar')
        self.assertIn('هـ', calendar_data['year_display'])
        self.assertIsInstance(calendar_data['calendar_matrix'], list)
        
        # Check that calendar matrix has weeks
        self.assertGreater(len(calendar_data['calendar_matrix']), 0)
        
        # Check that each week has 7 days
        for week in calendar_data['calendar_matrix']:
            self.assertEqual(len(week), 7)
    
    def test_arabic_month_names(self):
        """Test Arabic month names in calendar widget."""
        calendar_data = CalendarWidget.generate_month_calendar(2023, 1, 'gregorian', 'ar')
        
        # Should contain Arabic month name
        self.assertIn('يناير', calendar_data['month_name'])
        
        # Test Hijri month names
        hijri_calendar = CalendarWidget.generate_month_calendar(1444, 9, 'hijri', 'ar')
        self.assertIn('رمضان', hijri_calendar['month_name'])


class CalendarPreferenceModelTest(TestCase):
    """Test CalendarPreference model."""
    
    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass'
        )
    
    def test_create_calendar_preference(self):
        """Test creating calendar preference."""
        preference = CalendarPreference.objects.create(
            user=self.user,
            primary_calendar='hijri',
            show_dual_dates=False,
            hijri_adjustment=1,
            first_day_of_week=0,
            weekend_days=[4, 5]
        )
        
        self.assertEqual(preference.user, self.user)
        self.assertEqual(preference.primary_calendar, 'hijri')
        self.assertFalse(preference.show_dual_dates)
        self.assertEqual(preference.hijri_adjustment, 1)
        self.assertEqual(preference.first_day_of_week, 0)
        self.assertEqual(preference.weekend_days, [4, 5])
    
    def test_calendar_preference_str(self):
        """Test string representation of calendar preference."""
        preference = CalendarPreference.objects.create(
            user=self.user,
            primary_calendar='hijri'
        )
        
        expected_str = f"{self.user.username} - Hijri"
        self.assertEqual(str(preference), expected_str)
    
    def test_get_weekend_days_display(self):
        """Test getting weekend days display."""
        preference = CalendarPreference.objects.create(
            user=self.user,
            weekend_days=[4, 5, 6]  # Friday, Saturday, Sunday
        )
        
        weekend_display = preference.get_weekend_days_display()
        self.assertIn('Friday', weekend_display)
        self.assertIn('Saturday', weekend_display)
        self.assertIn('Sunday', weekend_display)
    
    def test_is_weekend(self):
        """Test weekend detection."""
        preference = CalendarPreference.objects.create(
            user=self.user,
            weekend_days=[4, 5]  # Friday, Saturday
        )
        
        # Test Friday (weekday 4)
        friday = date(2023, 1, 6)  # This is a Friday
        self.assertTrue(preference.is_weekend(friday))
        
        # Test Monday (weekday 0)
        monday = date(2023, 1, 2)  # This is a Monday
        self.assertFalse(preference.is_weekend(monday))


class CalendarEventModelTest(TestCase):
    """Test CalendarEvent model."""
    
    def setUp(self):
        """Set up test data."""
        from core.models import School
        
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass'
        )
        
        self.school = School.objects.create(
            name='Test School',
            address='Test Address',
            phone='1234567890',
            email='<EMAIL>'
        )
    
    def test_create_calendar_event(self):
        """Test creating calendar event."""
        event = CalendarEvent.objects.create(
            title='Test Event',
            title_ar='حدث تجريبي',
            description='Test Description',
            event_type='academic',
            calendar_type='gregorian',
            start_date=date(2023, 1, 15),
            end_date=date(2023, 1, 16),
            school=self.school,
            created_by=self.user
        )
        
        self.assertEqual(event.title, 'Test Event')
        self.assertEqual(event.title_ar, 'حدث تجريبي')
        self.assertEqual(event.event_type, 'academic')
        self.assertEqual(event.calendar_type, 'gregorian')
        self.assertEqual(event.start_date, date(2023, 1, 15))
        self.assertEqual(event.end_date, date(2023, 1, 16))
        self.assertEqual(event.school, self.school)
        self.assertEqual(event.created_by, self.user)
    
    def test_calendar_event_str(self):
        """Test string representation of calendar event."""
        event = CalendarEvent.objects.create(
            title='Test Event',
            start_date=date(2023, 1, 15),
            school=self.school,
            created_by=self.user
        )
        
        expected_str = "Test Event - 2023-01-15"
        self.assertEqual(str(event), expected_str)
    
    def test_get_display_title(self):
        """Test getting display title in different languages."""
        event = CalendarEvent.objects.create(
            title='Test Event',
            title_ar='حدث تجريبي',
            start_date=date(2023, 1, 15),
            school=self.school,
            created_by=self.user
        )
        
        # Test English title
        self.assertEqual(event.get_display_title('en'), 'Test Event')
        
        # Test Arabic title
        self.assertEqual(event.get_display_title('ar'), 'حدث تجريبي')
        
        # Test fallback when Arabic title is not available
        event.title_ar = ''
        self.assertEqual(event.get_display_title('ar'), 'Test Event')
    
    def test_get_duration_days(self):
        """Test getting event duration in days."""
        # Single day event
        single_day_event = CalendarEvent.objects.create(
            title='Single Day Event',
            start_date=date(2023, 1, 15),
            school=self.school,
            created_by=self.user
        )
        self.assertEqual(single_day_event.get_duration_days(), 1)
        
        # Multi-day event
        multi_day_event = CalendarEvent.objects.create(
            title='Multi Day Event',
            start_date=date(2023, 1, 15),
            end_date=date(2023, 1, 17),
            school=self.school,
            created_by=self.user
        )
        self.assertEqual(multi_day_event.get_duration_days(), 3)
    
    def test_is_multi_day(self):
        """Test multi-day event detection."""
        # Single day event
        single_day_event = CalendarEvent.objects.create(
            title='Single Day Event',
            start_date=date(2023, 1, 15),
            school=self.school,
            created_by=self.user
        )
        self.assertFalse(single_day_event.is_multi_day())
        
        # Multi-day event
        multi_day_event = CalendarEvent.objects.create(
            title='Multi Day Event',
            start_date=date(2023, 1, 15),
            end_date=date(2023, 1, 17),
            school=self.school,
            created_by=self.user
        )
        self.assertTrue(multi_day_event.is_multi_day())
    
    def test_hijri_event_validation(self):
        """Test validation for Hijri events."""
        from django.core.exceptions import ValidationError
        
        # Hijri event without Hijri date fields should fail validation
        hijri_event = CalendarEvent(
            title='Hijri Event',
            calendar_type='hijri',
            start_date=date(2023, 1, 15),
            school=self.school,
            created_by=self.user
        )
        
        with self.assertRaises(ValidationError):
            hijri_event.clean()
        
        # Hijri event with proper Hijri date fields should pass
        hijri_event.hijri_year = 1444
        hijri_event.hijri_month = 6
        hijri_event.hijri_day = 15
        
        try:
            hijri_event.clean()
        except ValidationError:
            self.fail("Hijri event with proper date fields should not raise ValidationError")


class CalendarEventAttendeeModelTest(TestCase):
    """Test CalendarEventAttendee model."""
    
    def setUp(self):
        """Set up test data."""
        from core.models import School
        
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass'
        )
        
        self.attendee_user = User.objects.create_user(
            username='attendee',
            email='<EMAIL>',
            password='testpass'
        )
        
        self.school = School.objects.create(
            name='Test School',
            address='Test Address',
            phone='1234567890',
            email='<EMAIL>'
        )
        
        self.event = CalendarEvent.objects.create(
            title='Test Event',
            start_date=date(2023, 1, 15),
            school=self.school,
            created_by=self.user
        )
    
    def test_create_event_attendee(self):
        """Test creating event attendee."""
        attendee = CalendarEventAttendee.objects.create(
            event=self.event,
            user=self.attendee_user,
            response='accepted',
            notes='Looking forward to it!'
        )
        
        self.assertEqual(attendee.event, self.event)
        self.assertEqual(attendee.user, self.attendee_user)
        self.assertEqual(attendee.response, 'accepted')
        self.assertEqual(attendee.notes, 'Looking forward to it!')
    
    def test_attendee_str(self):
        """Test string representation of event attendee."""
        attendee = CalendarEventAttendee.objects.create(
            event=self.event,
            user=self.attendee_user,
            response='accepted'
        )
        
        expected_str = f"{self.attendee_user.username} - {self.event.title} (Accepted)"
        self.assertEqual(str(attendee), expected_str)


class CalendarEventReminderModelTest(TestCase):
    """Test CalendarEventReminder model."""
    
    def setUp(self):
        """Set up test data."""
        from core.models import School
        
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass'
        )
        
        self.school = School.objects.create(
            name='Test School',
            address='Test Address',
            phone='1234567890',
            email='<EMAIL>'
        )
        
        self.event = CalendarEvent.objects.create(
            title='Test Event',
            start_date=date(2023, 1, 15),
            start_time=datetime.strptime('10:00', '%H:%M').time(),
            school=self.school,
            created_by=self.user
        )
    
    def test_create_event_reminder(self):
        """Test creating event reminder."""
        reminder = CalendarEventReminder.objects.create(
            event=self.event,
            user=self.user,
            reminder_type='email',
            time_before=30,
            time_unit='minutes'
        )
        
        self.assertEqual(reminder.event, self.event)
        self.assertEqual(reminder.user, self.user)
        self.assertEqual(reminder.reminder_type, 'email')
        self.assertEqual(reminder.time_before, 30)
        self.assertEqual(reminder.time_unit, 'minutes')
        self.assertFalse(reminder.is_sent)
    
    def test_reminder_str(self):
        """Test string representation of event reminder."""
        reminder = CalendarEventReminder.objects.create(
            event=self.event,
            user=self.user,
            reminder_type='email',
            time_before=30,
            time_unit='minutes'
        )
        
        expected_str = f"{self.user.username} - {self.event.title} (30 minutes)"
        self.assertEqual(str(reminder), expected_str)
    
    @patch('django.utils.timezone.now')
    def test_get_reminder_datetime(self, mock_now):
        """Test calculating reminder datetime."""
        # Mock current time
        mock_now.return_value = timezone.make_aware(datetime(2023, 1, 15, 8, 0))
        
        reminder = CalendarEventReminder.objects.create(
            event=self.event,
            user=self.user,
            reminder_type='email',
            time_before=30,
            time_unit='minutes'
        )
        
        reminder_datetime = reminder.get_reminder_datetime()
        
        # Should be 30 minutes before event start time (10:00)
        expected_datetime = timezone.make_aware(datetime(2023, 1, 15, 9, 30))
        self.assertEqual(reminder_datetime, expected_datetime)