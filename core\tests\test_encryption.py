"""
Tests for encryption system
"""
from django.test import TestCase
from django.conf import settings
from core.encryption import (
    EncryptionManager, FieldEncryption, KeyManager, 
    DataMasking, SecureTransfer
)
from core.encrypted_fields import (
    EncryptedCharField, EncryptedEmailField, EncryptedPhoneField,
    EncryptedIDField, EncryptedFinancialField
)
from core.key_management import KeyManagementSystem
import tempfile
import os


class EncryptionManagerTestCase(TestCase):
    """
    Test encryption manager
    """
    
    def setUp(self):
        self.encryption_manager = EncryptionManager()
    
    def test_general_encryption(self):
        """
        Test general encryption and decryption
        """
        original_data = "This is test data"
        
        # Encrypt
        encrypted = self.encryption_manager.encrypt_field(original_data, 'general')
        self.assertNotEqual(encrypted, original_data)
        self.assertIsInstance(encrypted, str)
        
        # Decrypt
        decrypted = self.encryption_manager.decrypt_field(encrypted, 'general')
        self.assertEqual(decrypted, original_data)
    
    def test_pii_encryption(self):
        """
        Test PII encryption and decryption
        """
        original_data = "123-45-6789"
        
        # Encrypt
        encrypted = self.encryption_manager.encrypt_field(original_data, 'pii')
        self.assertNotEqual(encrypted, original_data)
        
        # Decrypt
        decrypted = self.encryption_manager.decrypt_field(encrypted, 'pii')
        self.assertEqual(decrypted, original_data)
    
    def test_financial_encryption(self):
        """
        Test financial data encryption
        """
        original_data = "1000.50"
        
        # Encrypt
        encrypted = self.encryption_manager.encrypt_field(original_data, 'financial')
        self.assertNotEqual(encrypted, original_data)
        
        # Decrypt
        decrypted = self.encryption_manager.decrypt_field(encrypted, 'financial')
        self.assertEqual(decrypted, original_data)
    
    def test_hash_data(self):
        """
        Test data hashing
        """
        data = "password123"
        
        hash_result = self.encryption_manager.hash_data(data)
        self.assertIn('hash', hash_result)
        self.assertIn('salt', hash_result)
        
        # Verify hash
        is_valid = self.encryption_manager.verify_hash(
            data, hash_result['hash'], hash_result['salt']
        )
        self.assertTrue(is_valid)
        
        # Test with wrong data
        is_valid = self.encryption_manager.verify_hash(
            "wrongpassword", hash_result['hash'], hash_result['salt']
        )
        self.assertFalse(is_valid)


class FieldEncryptionTestCase(TestCase):
    """
    Test field encryption
    """
    
    def test_general_field_encryption(self):
        """
        Test general field encryption
        """
        field_encryption = FieldEncryption('general')
        original_value = "test value"
        
        encrypted = field_encryption.encrypt(original_value)
        decrypted = field_encryption.decrypt(encrypted)
        
        self.assertEqual(decrypted, original_value)
    
    def test_pii_field_encryption(self):
        """
        Test PII field encryption
        """
        field_encryption = FieldEncryption('pii')
        original_value = "123-45-6789"
        
        encrypted = field_encryption.encrypt(original_value)
        decrypted = field_encryption.decrypt(encrypted)
        
        self.assertEqual(decrypted, original_value)


class DataMaskingTestCase(TestCase):
    """
    Test data masking utilities
    """
    
    def test_mask_email(self):
        """
        Test email masking
        """
        email = "<EMAIL>"
        masked = DataMasking.mask_email(email)
        
        self.assertNotEqual(masked, email)
        self.assertIn('@example.com', masked)
        self.assertIn('*', masked)
    
    def test_mask_phone(self):
        """
        Test phone number masking
        """
        phone = "1234567890"
        masked = DataMasking.mask_phone(phone)
        
        self.assertNotEqual(masked, phone)
        self.assertIn('*', masked)
        self.assertTrue(masked.startswith('12'))
        self.assertTrue(masked.endswith('90'))
    
    def test_mask_id_number(self):
        """
        Test ID number masking
        """
        id_number = "123456789"
        masked = DataMasking.mask_id_number(id_number)
        
        self.assertNotEqual(masked, id_number)
        self.assertIn('*', masked)
        self.assertTrue(masked.startswith('12'))
        self.assertTrue(masked.endswith('89'))
    
    def test_mask_financial_amount(self):
        """
        Test financial amount masking
        """
        amount = "1234.56"
        masked = DataMasking.mask_financial_amount(amount)
        
        self.assertNotEqual(masked, amount)
        self.assertIn('*', masked)
        self.assertTrue(masked.endswith('.56'))


class SecureTransferTestCase(TestCase):
    """
    Test secure transfer utilities
    """
    
    def setUp(self):
        self.secure_transfer = SecureTransfer()
    
    def test_prepare_for_transfer(self):
        """
        Test data preparation for transfer
        """
        data = {
            'name': 'John Doe',
            'email': '<EMAIL>',
            'phone': '1234567890',
            'public_info': 'This is public'
        }
        
        sensitive_fields = ['email', 'phone']
        
        prepared = self.secure_transfer.prepare_for_transfer(data, sensitive_fields)
        
        # Sensitive fields should be encrypted
        self.assertNotEqual(prepared['email'], data['email'])
        self.assertNotEqual(prepared['phone'], data['phone'])
        
        # Non-sensitive fields should remain unchanged
        self.assertEqual(prepared['name'], data['name'])
        self.assertEqual(prepared['public_info'], data['public_info'])
    
    def test_process_received_data(self):
        """
        Test processing received encrypted data
        """
        # First prepare data
        original_data = {
            'email': '<EMAIL>',
            'phone': '1234567890'
        }
        
        encrypted_fields = ['email', 'phone']
        
        prepared = self.secure_transfer.prepare_for_transfer(original_data, encrypted_fields)
        
        # Then process it back
        processed = self.secure_transfer.process_received_data(prepared, encrypted_fields)
        
        self.assertEqual(processed['email'], original_data['email'])
        self.assertEqual(processed['phone'], original_data['phone'])


class KeyManagementTestCase(TestCase):
    """
    Test key management system
    """
    
    def setUp(self):
        # Use temporary directory for testing
        self.temp_dir = tempfile.mkdtemp()
        self.kms = KeyManagementSystem()
        self.kms.key_store_path = self.temp_dir
    
    def tearDown(self):
        # Clean up temporary directory
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_create_data_encryption_key(self):
        """
        Test creating data encryption key
        """
        key_info = self.kms.create_data_encryption_key('test_key', 'general')
        
        self.assertEqual(key_info['key_id'], 'test_key')
        self.assertEqual(key_info['purpose'], 'general')
        self.assertEqual(key_info['status'], 'active')
        self.assertIn('encrypted_key', key_info)
    
    def test_get_data_encryption_key(self):
        """
        Test retrieving data encryption key
        """
        # Create key first
        self.kms.create_data_encryption_key('test_key', 'general')
        
        # Retrieve key
        key = self.kms.get_data_encryption_key('test_key')
        self.assertIsNotNone(key)
        self.assertIsInstance(key, bytes)
    
    def test_list_keys(self):
        """
        Test listing keys
        """
        # Create some keys
        self.kms.create_data_encryption_key('key1', 'general')
        self.kms.create_data_encryption_key('key2', 'pii')
        
        # List all keys
        all_keys = self.kms.list_keys()
        self.assertEqual(len(all_keys), 2)
        
        # List by purpose
        pii_keys = self.kms.list_keys(purpose='pii')
        self.assertEqual(len(pii_keys), 1)
        self.assertEqual(pii_keys[0]['purpose'], 'pii')
    
    def test_key_rotation(self):
        """
        Test key rotation
        """
        # Create initial key
        original_key = self.kms.create_data_encryption_key('test_key', 'general')
        
        # Rotate key
        new_key = self.kms.rotate_key('test_key')
        
        self.assertNotEqual(new_key['key_id'], original_key['key_id'])
        self.assertEqual(new_key['version'], 2)
        self.assertEqual(new_key['previous_key_id'], 'test_key')
    
    def test_delete_key(self):
        """
        Test key deletion
        """
        # Create key
        self.kms.create_data_encryption_key('test_key', 'general')
        
        # Delete key (soft delete)
        result = self.kms.delete_key('test_key')
        self.assertTrue(result)
        
        # Key should still exist but marked as deleted
        key_info = self.kms._load_key('test_key')
        self.assertEqual(key_info['status'], 'deleted')
    
    def test_backup_and_restore(self):
        """
        Test key backup and restore
        """
        # Create some keys
        self.kms.create_data_encryption_key('key1', 'general')
        self.kms.create_data_encryption_key('key2', 'pii')
        
        # Create backup
        backup_path = os.path.join(self.temp_dir, 'backup.json')
        password = 'backup_password'
        
        result = self.kms.backup_keys(backup_path, password)
        self.assertTrue(result)
        self.assertTrue(os.path.exists(backup_path))
        
        # Clear keys
        self.kms.delete_key('key1', force=True)
        self.kms.delete_key('key2', force=True)
        
        # Restore from backup
        result = self.kms.restore_keys(backup_path, password)
        self.assertTrue(result)
        
        # Check keys are restored
        keys = self.kms.list_keys()
        self.assertEqual(len(keys), 2)


class EncryptedFieldsTestCase(TestCase):
    """
    Test encrypted model fields
    """
    
    def test_encrypted_char_field(self):
        """
        Test encrypted char field
        """
        field = EncryptedCharField(field_type='general')
        
        original_value = "test value"
        
        # Test encryption (prep_value)
        encrypted = field.get_prep_value(original_value)
        self.assertNotEqual(encrypted, original_value)
        
        # Test decryption (from_db_value)
        decrypted = field.from_db_value(encrypted, None, None)
        self.assertEqual(decrypted, original_value)
    
    def test_encrypted_email_field(self):
        """
        Test encrypted email field
        """
        field = EncryptedEmailField(field_type='pii')
        
        original_email = "<EMAIL>"
        
        encrypted = field.get_prep_value(original_email)
        decrypted = field.from_db_value(encrypted, None, None)
        
        self.assertEqual(decrypted, original_email)
    
    def test_encrypted_phone_field(self):
        """
        Test encrypted phone field
        """
        field = EncryptedPhoneField(field_type='pii')
        
        original_phone = "1234567890"
        
        encrypted = field.get_prep_value(original_phone)
        decrypted = field.from_db_value(encrypted, None, None)
        
        self.assertEqual(decrypted, original_phone)


if __name__ == '__main__':
    import unittest
    unittest.main()