"""
Simple encryption tests
"""
from django.test import TestCase
from core.encryption import DataMasking


class SimpleEncryptionTestCase(TestCase):
    """
    Simple encryption tests
    """
    
    def test_data_masking(self):
        """
        Test data masking utilities
        """
        # Test email masking
        email = "<EMAIL>"
        masked = DataMasking.mask_email(email)
        self.assertNotEqual(masked, email)
        self.assertIn('@example.com', masked)
        self.assertIn('*', masked)
        
        # Test phone masking
        phone = "1234567890"
        masked = DataMasking.mask_phone(phone)
        self.assertNotEqual(masked, phone)
        self.assertIn('*', masked)
        
        # Test ID masking
        id_number = "123456789"
        masked = DataMasking.mask_id_number(id_number)
        self.assertNotEqual(masked, id_number)
        self.assertIn('*', masked)