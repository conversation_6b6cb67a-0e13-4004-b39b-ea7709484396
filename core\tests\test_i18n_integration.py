"""
Integration tests for internationalization functionality.
"""

from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import translation

User = get_user_model()


class I18nIntegrationTest(TestCase):
    """Integration tests for i18n functionality."""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_language_switching_url(self):
        """Test language switching URL works."""
        # Test switching to Arabic
        response = self.client.get('/core/switch-language/ar/')
        self.assertEqual(response.status_code, 302)
        
        # Test switching to English
        response = self.client.get('/core/switch-language/en/')
        self.assertEqual(response.status_code, 302)
    
    def test_language_persistence_in_session(self):
        """Test language preference is stored in session."""
        # Test the middleware directly since URL routing might have issues
        from core.middleware import LanguageMiddleware
        from django.test import RequestFactory
        
        factory = RequestFactory()
        middleware = LanguageMiddleware(lambda r: None)
        
        # Test URL parameter
        request = factory.get('/?lang=ar')
        request.user = self.user
        request.session = {}
        
        language = middleware.get_language_from_request(request)
        self.assertEqual(language, 'ar')
        self.assertEqual(request.session['django_language'], 'ar')
        
        # Test session persistence
        request2 = factory.get('/')
        request2.user = self.user
        request2.session = {'django_language': 'ar'}
        
        language2 = middleware.get_language_from_request(request2)
        self.assertEqual(language2, 'ar')
    
    def test_invalid_language_code(self):
        """Test handling of invalid language codes."""
        response = self.client.get('/core/switch-language/invalid/')
        # Should redirect (not crash)
        self.assertEqual(response.status_code, 302)
    
    def test_language_middleware_url_parameter(self):
        """Test language detection from URL parameter."""
        # This would require a view that uses the middleware
        # For now, we'll test the middleware directly
        from core.middleware import LanguageMiddleware
        from django.test import RequestFactory
        
        factory = RequestFactory()
        middleware = LanguageMiddleware(lambda r: None)
        
        # Test URL parameter
        request = factory.get('/?lang=ar')
        request.user = self.user
        request.session = {}
        
        language = middleware.get_language_from_request(request)
        self.assertEqual(language, 'ar')
        self.assertEqual(request.session['django_language'], 'ar')
    
    def test_rtl_css_classes_utility(self):
        """Test RTL CSS classes utility."""
        from core.localization import RTLSupport
        
        # Test Arabic (RTL)
        classes = RTLSupport.get_css_classes('ar')
        self.assertIn('rtl-layout', classes)
        
        # Test English (LTR)
        classes = RTLSupport.get_css_classes('en')
        self.assertIn('ltr-layout', classes)
    
    def test_number_formatting_utilities(self):
        """Test number formatting utilities."""
        from core.localization import NumberFormatter
        
        # Test Arabic-Indic conversion
        result = NumberFormatter.to_arabic_indic('12345')
        self.assertEqual(result, '١٢٣٤٥')
        
        # Test Western digits conversion
        result = NumberFormatter.to_western_digits('١٢٣٤٥')
        self.assertEqual(result, '12345')
        
        # Test format by language
        result = NumberFormatter.format_number(12345, 'ar')
        self.assertEqual(result, '١٢٣٤٥')
        
        result = NumberFormatter.format_number(12345, 'en')
        self.assertEqual(result, '12345')
    
    def test_date_formatting_utilities(self):
        """Test date formatting utilities."""
        from core.localization import DateFormatter
        
        # Test date formats
        en_format = DateFormatter.get_date_format('en')
        self.assertEqual(en_format, '%Y-%m-%d')
        
        ar_format = DateFormatter.get_date_format('ar')
        self.assertEqual(ar_format, '%d/%m/%Y')
        
        # Test datetime formats
        en_datetime = DateFormatter.get_datetime_format('en')
        self.assertEqual(en_datetime, '%Y-%m-%d %H:%M:%S')
        
        ar_datetime = DateFormatter.get_datetime_format('ar')
        self.assertEqual(ar_datetime, '%d/%m/%Y %H:%M:%S')
    
    def test_localization_context_processor(self):
        """Test localization context processor."""
        from core.localization.localization import get_localization_context
        
        # Activate Arabic
        translation.activate('ar')
        context = get_localization_context()
        
        self.assertEqual(context['LANGUAGE_CODE'], 'ar')
        self.assertTrue(context['LANGUAGE_BIDI'])
        self.assertEqual(context['LANGUAGE_DIRECTION'], 'rtl')
        
        # Activate English
        translation.activate('en')
        context = get_localization_context()
        
        self.assertEqual(context['LANGUAGE_CODE'], 'en')
        self.assertFalse(context['LANGUAGE_BIDI'])
        self.assertEqual(context['LANGUAGE_DIRECTION'], 'ltr')