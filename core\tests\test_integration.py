"""
Integration tests for core functionality
"""
from django.test import TestCase, TransactionTestCase
from django.test.client import Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.core.cache import cache
from datetime import date
from .models import School, AcademicYear
from .tasks import send_email_notification, cleanup_old_logs

User = get_user_model()


class CoreIntegrationTest(TestCase):
    """Integration tests for core functionality"""
    
    def setUp(self):
        self.client = Client()
        self.school = School.objects.create(
            name='Integration Test School',
            code='INTTEST',
            address='123 Integration Street',
            phone='+1234567890',
            email='<EMAIL>',
            principal_name='Integration Principal',
            established_date=date(2020, 1, 1)
        )
        
        self.user = User.objects.create_user(
            username='integrationuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_school_context_middleware(self):
        """Test that school context is properly set"""
        # Make request with school header (without login to avoid session issues)
        response = self.client.get('/', HTTP_X_SCHOOL_CODE='INTTEST')
        
        # Check that request was processed (even if view doesn't exist)
        # The middleware should have set the school context
        self.assertIsNotNone(response)
    
    def test_cache_integration(self):
        """Test cache integration"""
        from .utils import CacheManager
        from django.conf import settings
        
        # Skip cache test if using dummy cache
        if 'dummy' in settings.CACHES['default']['BACKEND'].lower():
            self.skipTest("Skipping cache test with dummy cache backend")
        
        # Clear cache
        cache.clear()
        
        # Test cache operations
        CacheManager.set('integration', 'test_key', value='test_value')
        result = CacheManager.get('integration', 'test_key')
        
        self.assertEqual(result, 'test_value')
        
        # Test cache deletion
        CacheManager.delete('integration', 'test_key')
        result = CacheManager.get('integration', 'test_key')
        
        self.assertIsNone(result)
    
    def test_academic_year_workflow(self):
        """Test academic year creation workflow"""
        # Create academic year
        academic_year = AcademicYear.objects.create(
            school=self.school,
            name='2023-2024',
            start_date=date(2023, 9, 1),
            end_date=date(2024, 6, 30),
            is_current=True
        )
        
        # Verify it was created correctly
        self.assertEqual(academic_year.school, self.school)
        self.assertTrue(academic_year.is_current)
        
        # Create another academic year as current
        academic_year2 = AcademicYear.objects.create(
            school=self.school,
            name='2024-2025',
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        # Refresh first academic year
        academic_year.refresh_from_db()
        
        # Verify only one is current
        self.assertFalse(academic_year.is_current)
        self.assertTrue(academic_year2.is_current)


class TaskIntegrationTest(TransactionTestCase):
    """Integration tests for Celery tasks"""
    
    def setUp(self):
        self.school = School.objects.create(
            name='Task Test School',
            code='TASKTEST',
            address='123 Task Street',
            phone='+1234567890',
            email='<EMAIL>',
            principal_name='Task Principal',
            established_date=date(2020, 1, 1)
        )
    
    def test_email_task_integration(self):
        """Test email task integration"""
        from unittest.mock import patch
        
        with patch('core.tasks.send_mail') as mock_send_mail:
            mock_send_mail.return_value = True
            
            # Call the task directly (not async for testing)
            result = send_email_notification(
                '<EMAIL>',
                'Test Subject',
                'Test Message'
            )
            
            self.assertIn('Email <NAME_EMAIL>', result)
            mock_send_mail.assert_called_once()
    
    def test_cleanup_task_integration(self):
        """Test cleanup task integration"""
        from .models import AuditLog
        from datetime import timedelta
        from django.utils import timezone
        
        # Create old audit log
        old_log = AuditLog.objects.create(
            school=self.school,
            action='CREATE',
            model_name='Test',
            object_id='123',
            object_repr='Test Object'
        )
        
        # Manually set old timestamp
        old_timestamp = timezone.now() - timedelta(days=400)
        AuditLog.objects.filter(id=old_log.id).update(timestamp=old_timestamp)
        
        # Run cleanup task
        result = cleanup_old_logs()
        
        self.assertIn('Cleaned up', result)
        # Verify log was deleted
        self.assertFalse(AuditLog.objects.filter(id=old_log.id).exists())


class PermissionIntegrationTest(TestCase):
    """Integration tests for permission system"""
    
    def setUp(self):
        self.school1 = School.objects.create(
            name='Permission School 1',
            code='PERM001',
            address='123 Permission Street',
            phone='+1234567890',
            email='<EMAIL>',
            principal_name='Permission Principal 1',
            established_date=date(2020, 1, 1)
        )
        
        self.school2 = School.objects.create(
            name='Permission School 2',
            code='PERM002',
            address='456 Permission Avenue',
            phone='+1234567891',
            email='<EMAIL>',
            principal_name='Permission Principal 2',
            established_date=date(2020, 1, 1)
        )
        
        self.user = User.objects.create_user(
            username='permissionuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.superuser = User.objects.create_superuser(
            username='superuser',
            email='<EMAIL>',
            password='superpass123'
        )
    
    def test_school_permission_integration(self):
        """Test school permission integration"""
        from .permissions import has_school_permission
        
        # Superuser should have access to all schools
        self.assertTrue(has_school_permission(self.superuser, self.school1))
        self.assertTrue(has_school_permission(self.superuser, self.school2))
        
        # Regular user without school association should not have access
        self.assertFalse(has_school_permission(self.user, self.school1))
        self.assertFalse(has_school_permission(self.user, self.school2))


class UtilityIntegrationTest(TestCase):
    """Integration tests for utility functions"""
    
    def setUp(self):
        cache.clear()
    
    def test_id_generator_integration(self):
        """Test ID generator integration"""
        from .utils import IDGenerator
        from django.conf import settings
        
        # Skip ID generator test if using dummy cache (sequences won't work)
        if 'dummy' in settings.CACHES['default']['BACKEND'].lower():
            self.skipTest("Skipping ID generator test with dummy cache backend")
        
        # Test student ID generation
        student_id1 = IDGenerator.generate_student_id('TEST', 2023)
        student_id2 = IDGenerator.generate_student_id('TEST', 2023)
        
        # Should be sequential
        self.assertNotEqual(student_id1, student_id2)
        self.assertTrue(student_id1.startswith('TEST23'))
        self.assertTrue(student_id2.startswith('TEST23'))
        
        # Extract sequence numbers
        seq1 = int(student_id1[-4:])
        seq2 = int(student_id2[-4:])
        self.assertEqual(seq2, seq1 + 1)
    
    def test_security_utils_integration(self):
        """Test security utilities integration"""
        from .utils import SecurityUtils
        
        # Test token generation
        token1 = SecurityUtils.generate_secure_token()
        token2 = SecurityUtils.generate_secure_token()
        
        self.assertNotEqual(token1, token2)
        self.assertGreater(len(token1), 0)
        self.assertGreater(len(token2), 0)
        
        # Test password hashing
        password = "integration_test_password"
        hash1, salt1 = SecurityUtils.hash_password(password)
        hash2, salt2 = SecurityUtils.hash_password(password)
        
        # Different salts should produce different hashes
        self.assertNotEqual(hash1, hash2)
        self.assertNotEqual(salt1, salt2)
        
        # But verification should work for both
        self.assertTrue(SecurityUtils.verify_password(password, hash1, salt1))
        self.assertTrue(SecurityUtils.verify_password(password, hash2, salt2))
        
        # Wrong password should fail
        self.assertFalse(SecurityUtils.verify_password("wrong", hash1, salt1))