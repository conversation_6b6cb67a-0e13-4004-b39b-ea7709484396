"""
Tests for localization functionality.
"""

import pytest
from django.test import TestCase, RequestFactory
from accounts.models import User
from django.utils import translation
from django.conf import settings
from django.template import Context, Template
from django.http import HttpResponse

from core.localization import (
    LocalizationManager, RTLSupport, NumberF<PERSON>atter, 
    DateFormatter, switch_language
)
from core.middleware import LanguageMiddleware


class LocalizationManagerTest(TestCase):
    """Test LocalizationManager functionality."""
    
    def setUp(self):
        self.factory = RequestFactory()
    
    def test_get_available_languages(self):
        """Test getting available languages."""
        languages = LocalizationManager.get_available_languages()
        self.assertEqual(languages, settings.LANGUAGES)
        self.assertIn(('en', 'English'), languages)
        self.assertIn(('ar', 'العربية'), languages)
    
    def test_get_current_language(self):
        """Test getting current language."""
        # Test default language
        current = LocalizationManager.get_current_language()
        self.assertIn(current, [code for code, name in settings.LANGUAGES])
        
        # Test after activating Arabic
        translation.activate('ar')
        current = LocalizationManager.get_current_language()
        self.assertEqual(current, 'ar')
        
        # Reset to default
        translation.activate(settings.LANGUAGE_CODE)
    
    def test_is_rtl_language(self):
        """Test RTL language detection."""
        # Test Arabic (RTL)
        self.assertTrue(LocalizationManager.is_rtl_language('ar'))
        
        # Test English (LTR)
        self.assertFalse(LocalizationManager.is_rtl_language('en'))
        
        # Test with current language
        translation.activate('ar')
        self.assertTrue(LocalizationManager.is_rtl_language())
        
        translation.activate('en')
        self.assertFalse(LocalizationManager.is_rtl_language())
    
    def test_get_language_direction(self):
        """Test language direction detection."""
        self.assertEqual(LocalizationManager.get_language_direction('ar'), 'rtl')
        self.assertEqual(LocalizationManager.get_language_direction('en'), 'ltr')
    
    def test_get_language_name(self):
        """Test getting language display name."""
        self.assertEqual(LocalizationManager.get_language_name('en'), 'English')
        self.assertEqual(LocalizationManager.get_language_name('ar'), 'العربية')
    
    def test_activate_language(self):
        """Test language activation."""
        # Test valid language
        result = LocalizationManager.activate_language('ar')
        self.assertTrue(result)
        self.assertEqual(translation.get_language(), 'ar')
        
        # Test invalid language
        result = LocalizationManager.activate_language('invalid')
        self.assertFalse(result)
    
    def test_get_localized_url(self):
        """Test URL localization."""
        # Test English (default) URL
        url = LocalizationManager.get_localized_url('/dashboard/', 'en')
        self.assertEqual(url, '/dashboard/')
        
        # Test Arabic URL
        url = LocalizationManager.get_localized_url('/dashboard/', 'ar')
        self.assertEqual(url, '/ar/dashboard/')
        
        # Test removing existing language prefix
        url = LocalizationManager.get_localized_url('/ar/dashboard/', 'en')
        self.assertEqual(url, '/dashboard/')


class RTLSupportTest(TestCase):
    """Test RTL support functionality."""
    
    def test_get_css_classes(self):
        """Test CSS classes for RTL support."""
        # Test Arabic (RTL)
        classes = RTLSupport.get_css_classes('ar')
        self.assertIn('rtl-layout', classes)
        self.assertIn('text-right', classes)
        
        # Test English (LTR)
        classes = RTLSupport.get_css_classes('en')
        self.assertIn('ltr-layout', classes)
        self.assertIn('text-left', classes)
    
    def test_get_bootstrap_classes(self):
        """Test Bootstrap classes for RTL."""
        # Test Arabic (RTL)
        classes = RTLSupport.get_bootstrap_classes('ar')
        self.assertEqual(classes['float_left'], 'float-right')
        self.assertEqual(classes['float_right'], 'float-left')
        self.assertEqual(classes['text_left'], 'text-right')
        self.assertEqual(classes['text_right'], 'text-left')
        
        # Test English (LTR)
        classes = RTLSupport.get_bootstrap_classes('en')
        self.assertEqual(classes['float_left'], 'float-left')
        self.assertEqual(classes['float_right'], 'float-right')
        self.assertEqual(classes['text_left'], 'text-left')
        self.assertEqual(classes['text_right'], 'text-right')


class NumberFormatterTest(TestCase):
    """Test number formatting functionality."""
    
    def test_to_arabic_indic(self):
        """Test conversion to Arabic-Indic digits."""
        result = NumberFormatter.to_arabic_indic('123456789')
        self.assertEqual(result, '١٢٣٤٥٦٧٨٩')
        
        result = NumberFormatter.to_arabic_indic('2025')
        self.assertEqual(result, '٢٠٢٥')
    
    def test_to_western_digits(self):
        """Test conversion to Western digits."""
        result = NumberFormatter.to_western_digits('١٢٣٤٥٦٧٨٩')
        self.assertEqual(result, '123456789')
        
        result = NumberFormatter.to_western_digits('٢٠٢٥')
        self.assertEqual(result, '2025')
    
    def test_format_number(self):
        """Test number formatting by language."""
        # Test Arabic formatting
        result = NumberFormatter.format_number(12345, 'ar')
        self.assertEqual(result, '١٢٣٤٥')
        
        # Test English formatting
        result = NumberFormatter.format_number(12345, 'en')
        self.assertEqual(result, '12345')


class DateFormatterTest(TestCase):
    """Test date formatting functionality."""
    
    def test_get_date_format(self):
        """Test getting date format by language."""
        # Test English format
        format_str = DateFormatter.get_date_format('en')
        self.assertEqual(format_str, '%Y-%m-%d')
        
        # Test Arabic format
        format_str = DateFormatter.get_date_format('ar')
        self.assertEqual(format_str, '%d/%m/%Y')
    
    def test_get_datetime_format(self):
        """Test getting datetime format by language."""
        # Test English format
        format_str = DateFormatter.get_datetime_format('en')
        self.assertEqual(format_str, '%Y-%m-%d %H:%M:%S')
        
        # Test Arabic format
        format_str = DateFormatter.get_datetime_format('ar')
        self.assertEqual(format_str, '%d/%m/%Y %H:%M:%S')


class LanguageMiddlewareTest(TestCase):
    """Test language middleware functionality."""
    
    def setUp(self):
        self.factory = RequestFactory()
        self.middleware = LanguageMiddleware(lambda r: HttpResponse())
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass'
        )
    
    def test_get_language_from_url_parameter(self):
        """Test language detection from URL parameter."""
        request = self.factory.get('/?lang=ar')
        request.user = self.user
        request.session = {}
        
        language = self.middleware.get_language_from_request(request)
        self.assertEqual(language, 'ar')
        self.assertEqual(request.session['django_language'], 'ar')
    
    def test_get_language_from_session(self):
        """Test language detection from session."""
        request = self.factory.get('/')
        request.user = self.user
        request.session = {'django_language': 'ar'}
        
        language = self.middleware.get_language_from_request(request)
        self.assertEqual(language, 'ar')
    
    def test_get_language_from_accept_header(self):
        """Test language detection from Accept-Language header."""
        request = self.factory.get('/', HTTP_ACCEPT_LANGUAGE='ar,en;q=0.9')
        request.user = self.user
        request.session = {}
        
        language = self.middleware.get_language_from_request(request)
        self.assertEqual(language, 'ar')
    
    def test_default_language(self):
        """Test fallback to default language."""
        request = self.factory.get('/')
        request.user = self.user
        request.session = {}
        
        language = self.middleware.get_language_from_request(request)
        self.assertEqual(language, settings.LANGUAGE_CODE)


class SwitchLanguageViewTest(TestCase):
    """Test language switching view."""
    
    def setUp(self):
        self.factory = RequestFactory()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass'
        )
    
    def test_switch_to_arabic(self):
        """Test switching to Arabic language."""
        request = self.factory.get('/')
        request.user = self.user
        request.session = {}
        request.META = {}
        
        response = switch_language(request, 'ar')
        
        self.assertEqual(response.status_code, 302)
        self.assertEqual(request.session['django_language'], 'ar')
    
    def test_switch_to_english(self):
        """Test switching to English language."""
        request = self.factory.get('/')
        request.user = self.user
        request.session = {}
        request.META = {}
        
        response = switch_language(request, 'en')
        
        self.assertEqual(response.status_code, 302)
        self.assertEqual(request.session['django_language'], 'en')
    
    def test_invalid_language_code(self):
        """Test switching to invalid language."""
        request = self.factory.get('/')
        request.user = self.user
        request.session = {}
        request.META = {}
        
        response = switch_language(request, 'invalid')
        
        # Should redirect to dashboard on invalid language
        self.assertEqual(response.status_code, 302)


class TemplateTagsTest(TestCase):
    """Test localization template tags."""
    
    def test_get_current_language_tag(self):
        """Test get_current_language template tag."""
        template = Template('{% load localization_tags %}{% get_current_language %}')
        rendered = template.render(Context())
        self.assertIn(rendered, [code for code, name in settings.LANGUAGES])
    
    def test_is_rtl_tag(self):
        """Test is_rtl template tag."""
        # Test with Arabic
        translation.activate('ar')
        template = Template('{% load localization_tags %}{% is_rtl %}')
        rendered = template.render(Context())
        self.assertEqual(rendered, 'True')
        
        # Test with English
        translation.activate('en')
        template = Template('{% load localization_tags %}{% is_rtl %}')
        rendered = template.render(Context())
        self.assertEqual(rendered, 'False')
    
    def test_language_direction_tag(self):
        """Test language_direction template tag."""
        # Test with Arabic
        translation.activate('ar')
        template = Template('{% load localization_tags %}{% language_direction %}')
        rendered = template.render(Context())
        self.assertEqual(rendered, 'rtl')
        
        # Test with English
        translation.activate('en')
        template = Template('{% load localization_tags %}{% language_direction %}')
        rendered = template.render(Context())
        self.assertEqual(rendered, 'ltr')
    
    def test_format_number_filter(self):
        """Test format_number template filter."""
        template = Template('{% load localization_tags %}{{ number|format_number:"ar" }}')
        rendered = template.render(Context({'number': 12345}))
        self.assertEqual(rendered, '١٢٣٤٥')
    
    def test_arabic_digits_filter(self):
        """Test arabic_digits template filter."""
        template = Template('{% load localization_tags %}{{ number|arabic_digits }}')
        rendered = template.render(Context({'number': '12345'}))
        self.assertEqual(rendered, '١٢٣٤٥')
    
    def test_western_digits_filter(self):
        """Test western_digits template filter."""
        template = Template('{% load localization_tags %}{{ number|western_digits }}')
        rendered = template.render(Context({'number': '١٢٣٤٥'}))
        self.assertEqual(rendered, '12345')


@pytest.mark.django_db
class LocalizationIntegrationTest(TestCase):
    """Integration tests for localization functionality."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass'
        )
        self.client.login(username='testuser', password='testpass')
    
    def test_language_switching_integration(self):
        """Test complete language switching workflow."""
        # Switch to Arabic
        response = self.client.get('/core/switch-language/ar/')
        self.assertEqual(response.status_code, 302)
        
        # Verify session contains Arabic language
        session = self.client.session
        self.assertEqual(session['django_language'], 'ar')
        
        # Switch back to English
        response = self.client.get('/core/switch-language/en/')
        self.assertEqual(response.status_code, 302)
        
        # Verify session contains English language
        session = self.client.session
        self.assertEqual(session['django_language'], 'en')
    
    def test_rtl_css_inclusion(self):
        """Test RTL CSS is included for Arabic pages."""
        # Set language to Arabic
        self.client.get('/core/switch-language/ar/')
        
        # Request a page (assuming dashboard exists)
        try:
            response = self.client.get('/')
            if response.status_code == 200:
                # Check if RTL styles are included
                content = response.content.decode()
                self.assertIn('direction: rtl', content)
        except:
            # Skip if dashboard doesn't exist yet
            pass
    
    def test_number_formatting_in_templates(self):
        """Test number formatting works in templates."""
        # This would require actual templates to test
        # For now, we'll test the underlying functionality
        formatted = NumberFormatter.format_number(12345, 'ar')
        self.assertEqual(formatted, '١٢٣٤٥')
        
        formatted = NumberFormatter.format_number(12345, 'en')
        self.assertEqual(formatted, '12345')