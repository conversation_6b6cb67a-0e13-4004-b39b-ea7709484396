"""
Tests for mobile API endpoints.
"""

from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
import json
from datetime import date, timedelta
from core.models import School
from students.models import Student, Class, Grade
from academics.models import StudentAttendance
from communications.models import Notification

User = get_user_model()


class MobileAPITestCase(APITestCase):
    """Base test case for mobile API tests."""
    
    def setUp(self):
        """Set up test data."""
        # Create school
        self.school = School.objects.create(
            name="Test School",
            name_ar="مدرسة الاختبار",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            established_date=date(2020, 1, 1)
        )
        
        # Create user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )
        self.user.school = self.school
        self.user.save()
        
        # Create grade and class
        self.grade = Grade.objects.create(
            name="Grade 1",
            name_ar="الصف الأول",
            school=self.school
        )
        
        self.class_obj = Class.objects.create(
            name="Class 1A",
            name_ar="الفصل 1أ",
            grade=self.grade,
            school=self.school,
            academic_year=self.school.current_academic_year
        )
        
        # Create students
        self.student1 = Student.objects.create(
            student_id="STU001",
            first_name="John",
            last_name="Doe",
            email="<EMAIL>",
            current_class=self.class_obj,
            school=self.school
        )
        
        self.student2 = Student.objects.create(
            student_id="STU002",
            first_name="Jane",
            last_name="Smith",
            email="<EMAIL>",
            current_class=self.class_obj,
            school=self.school
        )
        
        # Generate JWT token
        self.refresh = RefreshToken.for_user(self.user)
        self.access_token = str(self.refresh.access_token)
        
        # Set up API client with authentication
        self.client = APIClient()
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')


class MobileAuthAPITest(TestCase):
    """Test mobile authentication API endpoints."""
    
    def setUp(self):
        """Set up test data."""
        self.school = School.objects.create(
            name="Test School",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            established_date=date(2020, 1, 1)
        )
        
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user.school = self.school
        self.user.save()
        
        self.client = Client()
    
    def test_mobile_login_success(self):
        """Test successful mobile login."""
        data = {
            'username': 'testuser',
            'password': 'testpass123',
            'device_info': {
                'device_type': 'mobile',
                'os': 'iOS',
                'version': '15.0'
            }
        }
        
        response = self.client.post(
            '/api/mobile/auth/login/',
            data=json.dumps(data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertTrue(response_data['success'])
        self.assertIn('tokens', response_data['data'])
        self.assertIn('access_token', response_data['data']['tokens'])
        self.assertIn('refresh_token', response_data['data']['tokens'])
    
    def test_mobile_login_invalid_credentials(self):
        """Test mobile login with invalid credentials."""
        data = {
            'username': 'testuser',
            'password': 'wrongpassword'
        }
        
        response = self.client.post(
            '/api/mobile/auth/login/',
            data=json.dumps(data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 401)
        response_data = response.json()
        self.assertFalse(response_data['success'])
    
    def test_mobile_login_missing_fields(self):
        """Test mobile login with missing fields."""
        data = {
            'username': 'testuser'
            # Missing password
        }
        
        response = self.client.post(
            '/api/mobile/auth/login/',
            data=json.dumps(data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 400)
        response_data = response.json()
        self.assertFalse(response_data['success'])
    
    def test_mobile_token_refresh(self):
        """Test mobile token refresh."""
        # First login to get tokens
        login_data = {
            'username': 'testuser',
            'password': 'testpass123'
        }
        
        login_response = self.client.post(
            '/api/mobile/auth/login/',
            data=json.dumps(login_data),
            content_type='application/json'
        )
        
        tokens = login_response.json()['data']['tokens']
        
        # Now refresh the token
        refresh_data = {
            'refresh_token': tokens['refresh_token']
        }
        
        response = self.client.post(
            '/api/mobile/auth/token/refresh/',
            data=json.dumps(refresh_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertTrue(response_data['success'])
        self.assertIn('tokens', response_data['data'])


class DashboardStatsAPITest(MobileAPITestCase):
    """Test dashboard statistics API."""
    
    def test_get_dashboard_stats(self):
        """Test getting dashboard statistics."""
        # Create some attendance records
        today = timezone.now().date()
        StudentAttendance.objects.create(
            student=self.student1,
            date=today,
            status='present',
            school=self.school
        )
        StudentAttendance.objects.create(
            student=self.student2,
            date=today,
            status='absent',
            school=self.school
        )
        
        response = self.client.get('/api/mobile/dashboard/stats/')
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertIn('total_students', data['data'])
        self.assertIn('present_today', data['data'])
        self.assertIn('absent_today', data['data'])
        self.assertEqual(data['data']['total_students'], 2)
        self.assertEqual(data['data']['present_today'], 1)
        self.assertEqual(data['data']['absent_today'], 1)


class StudentListAPITest(MobileAPITestCase):
    """Test student list API."""
    
    def test_get_student_list(self):
        """Test getting student list."""
        response = self.client.get('/api/mobile/students/')
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertIn('students', data['data'])
        self.assertEqual(len(data['data']['students']), 2)
        
        # Check student data structure
        student = data['data']['students'][0]
        self.assertIn('id', student)
        self.assertIn('student_id', student)
        self.assertIn('full_name', student)
        self.assertIn('class_name', student)
    
    def test_search_students(self):
        """Test searching students."""
        response = self.client.get('/api/mobile/students/?search=John')
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertEqual(len(data['data']['students']), 1)
        self.assertEqual(data['data']['students'][0]['first_name'], 'John')
    
    def test_filter_students_by_class(self):
        """Test filtering students by class."""
        response = self.client.get(f'/api/mobile/students/?class_id={self.class_obj.id}')
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertEqual(len(data['data']['students']), 2)


class AttendanceAPITest(MobileAPITestCase):
    """Test attendance API."""
    
    def test_get_attendance_data(self):
        """Test getting attendance data."""
        today = timezone.now().date()
        
        # Create attendance record
        StudentAttendance.objects.create(
            student=self.student1,
            date=today,
            status='present',
            school=self.school
        )
        
        response = self.client.get(f'/api/mobile/attendance/?date={today.isoformat()}')
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertIn('students', data['data'])
        self.assertIn('summary', data['data'])
        
        # Check summary
        summary = data['data']['summary']
        self.assertEqual(summary['total'], 2)
        self.assertEqual(summary['present'], 1)
        self.assertEqual(summary['not_marked'], 1)
    
    def test_submit_attendance_data(self):
        """Test submitting attendance data."""
        today = timezone.now().date()
        
        attendance_data = {
            'date': today.isoformat(),
            'attendance': [
                {
                    'student_id': self.student1.id,
                    'status': 'present',
                    'notes': 'On time'
                },
                {
                    'student_id': self.student2.id,
                    'status': 'absent',
                    'notes': 'Sick'
                }
            ]
        }
        
        response = self.client.post(
            '/api/mobile/attendance/',
            data=json.dumps(attendance_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertEqual(data['data']['created'], 2)
        
        # Verify attendance records were created
        attendance_count = StudentAttendance.objects.filter(
            date=today,
            school=self.school
        ).count()
        self.assertEqual(attendance_count, 2)


class NotificationsAPITest(MobileAPITestCase):
    """Test notifications API."""
    
    def setUp(self):
        """Set up test data."""
        super().setUp()
        
        # Create notifications
        self.notification1 = Notification.objects.create(
            recipient=self.user,
            title="Test Notification 1",
            message="This is a test notification",
            notification_type="info"
        )
        
        self.notification2 = Notification.objects.create(
            recipient=self.user,
            title="Test Notification 2",
            message="This is another test notification",
            notification_type="warning",
            is_read=True
        )
    
    def test_get_notifications(self):
        """Test getting notifications."""
        response = self.client.get('/api/mobile/notifications/')
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertIn('notifications', data['data'])
        self.assertEqual(len(data['data']['notifications']), 2)
        self.assertEqual(data['data']['unread_count'], 1)
    
    def test_get_unread_notifications_only(self):
        """Test getting only unread notifications."""
        response = self.client.get('/api/mobile/notifications/?unread_only=true')
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertEqual(len(data['data']['notifications']), 1)
        self.assertFalse(data['data']['notifications'][0]['is_read'])
    
    def test_mark_notifications_as_read(self):
        """Test marking notifications as read."""
        mark_data = {
            'notification_ids': [self.notification1.id]
        }
        
        response = self.client.post(
            '/api/mobile/notifications/',
            data=json.dumps(mark_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertEqual(data['data']['updated_count'], 1)
        
        # Verify notification was marked as read
        self.notification1.refresh_from_db()
        self.assertTrue(self.notification1.is_read)
    
    def test_mark_all_notifications_as_read(self):
        """Test marking all notifications as read."""
        mark_data = {
            'mark_all': True
        }
        
        response = self.client.post(
            '/api/mobile/notifications/',
            data=json.dumps(mark_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertEqual(data['data']['updated_count'], 1)  # Only 1 unread notification


class MobileUserProfileAPITest(MobileAPITestCase):
    """Test mobile user profile API."""
    
    def test_get_user_profile(self):
        """Test getting user profile."""
        response = self.client.get('/api/mobile/profile/')
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        
        profile = data['data']
        self.assertEqual(profile['username'], 'testuser')
        self.assertEqual(profile['email'], '<EMAIL>')
        self.assertEqual(profile['first_name'], 'Test')
        self.assertEqual(profile['last_name'], 'User')
        self.assertIn('school', profile)
        self.assertEqual(profile['school']['name'], 'Test School')


class MobileAppConfigAPITest(MobileAPITestCase):
    """Test mobile app configuration API."""
    
    def test_get_app_config(self):
        """Test getting app configuration."""
        response = self.client.get('/api/mobile/config/')
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        
        config = data['data']
        self.assertIn('app_version', config)
        self.assertIn('api_version', config)
        self.assertIn('features', config)
        self.assertIn('settings', config)
        self.assertIn('endpoints', config)
        
        # Check features
        features = config['features']
        self.assertIn('offline_mode', features)
        self.assertIn('push_notifications', features)
        self.assertIn('biometric_auth', features)


class MobileFeedbackAPITest(MobileAPITestCase):
    """Test mobile feedback API."""
    
    def test_submit_feedback(self):
        """Test submitting feedback."""
        feedback_data = {
            'type': 'bug_report',
            'message': 'Found a bug in the attendance feature',
            'rating': 4,
            'device_info': {
                'device_type': 'mobile',
                'os': 'iOS',
                'version': '15.0'
            }
        }
        
        response = self.client.post(
            '/api/mobile/feedback/',
            data=json.dumps(feedback_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertIn('feedback_id', data['data'])
    
    def test_submit_feedback_missing_message(self):
        """Test submitting feedback without message."""
        feedback_data = {
            'type': 'general',
            'rating': 5
        }
        
        response = self.client.post(
            '/api/mobile/feedback/',
            data=json.dumps(feedback_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 400)
        data = response.json()
        self.assertFalse(data['success'])