from django.urls import path
from . import views
from .localization.localization import switch_language
from .localization import localization_views

app_name = 'core'

urlpatterns = [
    # Health Check
    path('api/health-check/', views.health_check, name='health_check'),
    
    # School Selection
    path('school/select/', views.school_select, name='school_select'),
    path('school/switch/', views.switch_school, name='switch_school'),
    path('school/current/', views.get_current_school_api, name='get_current_school'),
    
    # School Session Management
    path('school/session/refresh/', views.refresh_school_session_api, name='refresh_school_session'),
    path('school/session/clear/', views.clear_school_session_api, name='clear_school_session'),
    path('school/session/status/', views.get_school_session_status, name='get_school_session_status'),
    
    # Language switching
    path('switch-language/<str:language_code>/', switch_language, name='switch_language'),
    
    # Localization Management
    path('localization/', localization_views.localization_dashboard, name='localization_dashboard'),
    path('localization/editor/<str:language_code>/', localization_views.translation_editor, name='translation_editor'),
    path('localization/update/', localization_views.update_translation, name='update_translation'),
    path('localization/compile/', localization_views.compile_translations, name='compile_translations'),
    path('localization/analytics/', localization_views.translation_analytics, name='translation_analytics'),
    path('localization/validation/', localization_views.translation_validation, name='translation_validation'),
    path('localization/export/<str:language_code>/', localization_views.export_translations, name='export_translations'),
    path('localization/import/', localization_views.import_translations, name='import_translations'),
    path('localization/add-language/', localization_views.add_language, name='add_language'),
    path('localization/delete-language/<str:language_code>/', localization_views.delete_language, name='delete_language'),
    path('localization/scan-missing/', localization_views.scan_missing_translations, name='scan_missing_translations'),
    path('localization/validate-all/', localization_views.validate_all_translations, name='validate_all_translations'),
    path('localization/export-all/', localization_views.export_all_translations, name='export_all_translations'),
    path('localization/test-runner/', localization_views.localization_test_runner, name='localization_test_runner'),
    path('localization/export-analytics/', localization_views.export_analytics, name='export_analytics'),
    path('localization/testing-tools/', localization_views.translation_testing_tools, name='translation_testing_tools'),
    path('calendar-preferences/', localization_views.calendar_preferences, name='calendar_preferences'),
    # Admin Area
    path('', views.AdminDashboardView.as_view(), name='dashboard'),
    path('admin-dashboard/', views.AdminDashboardView.as_view(), name='admin_dashboard'),
    path('sync/', views.SyncView.as_view(), name='sync'),
    path('backup/', views.BackupView.as_view(), name='backup'),
    path('history-report/', views.HistoryReportView.as_view(), name='history_report'),
    path('report-designer/', views.ReportDesignerView.as_view(), name='report_designer'),
    path('structure-admin/', views.StructureAdministrativeView.as_view(), name='structure_admin'),
    path('quick-support/', views.QuickSupportView.as_view(), name='quick_support'),

    # User Management
    path('users/create/', views.UserCreateView.as_view(), name='user_create'),

    # General Site Settings
    path('settings/', views.SettingsView.as_view(), name='settings'),
    path('login-page-settings/', views.LoginPageSettingsView.as_view(), name='login_page_settings'),

    # School Information
    path('school-info/', views.SchoolInfoView.as_view(), name='school_info'),
    path('school-data/', views.SchoolDataView.as_view(), name='school_data'),
    path('school-data-settings/', views.SchoolDataSettingsView.as_view(), name='school_data_settings'),
    path('nationality/', views.NationalityView.as_view(), name='nationality'),
    path('currency/', views.CurrencyView.as_view(), name='currency'),

    # Taxation System
    path('tax-settings/', views.EgyptianTaxSystemSettingsView.as_view(), name='tax_settings'),
    path('zakat-income/', views.ZakatAndIncomeView.as_view(), name='zakat_income'),

    # Email Settings
    path('email-settings/', views.EmailSettingsView.as_view(), name='email_settings'),
    path('sent-emails/', views.SentEmailView.as_view(), name='sent_emails'),
    path('email-format-settings/', views.EmailFormatSettingsView.as_view(), name='email_format_settings'),

    # Students Receiver
    path('students-receiver/', views.StudentsReceiverView.as_view(), name='students_receiver'),
    path('receiver-reports/', views.StudentReceiverReportsView.as_view(), name='receiver_reports'),
    path('show-receiver-report/', views.ShowReceiverReportView.as_view(), name='show_receiver_report'),

    # Service Providers - SMS
    path('sms-settings/', views.SMSSettingsView.as_view(), name='sms_settings'),
    path('message-templates/', views.MessageTemplatesView.as_view(), name='message_templates'),
    path('sent-messages/', views.SentMessagesView.as_view(), name='sent_messages'),
    path('auto-messages/', views.AutoMessagesView.as_view(), name='auto_messages'),
    path('sms-report/', views.SMSReportView.as_view(), name='sms_report'),

    # Service Providers - WhatsApp
    path('whatsapp-settings/', views.WhatsAppSettingsView.as_view(), name='whatsapp_settings'),

    # Service Providers - Payment
    path('payment-provider/', views.PaymentProviderView.as_view(), name='payment_provider'),
]
