import logging
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.views.generic import TemplateView, CreateView
from django.http import HttpResponse, JsonResponse
from django.urls import reverse_lazy
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.models import User
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_protect
from django.utils import timezone
from .models import School
from .school_utils import (
    get_user_schools, 
    user_has_school_access, 
    get_current_school, 
    set_current_school,
    get_school_session_info,
    refresh_school_session,
    clear_current_school
)

logger = logging.getLogger(__name__)

def health_check(request):
    """Simple health check endpoint for connectivity testing"""
    return JsonResponse({'status': 'ok', 'timestamp': timezone.now().isoformat()})

def placeholder_view(request):
    return HttpResponse("Core settings - Coming soon!")

# Admin Area Views
class AdminDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'core/admin_dashboard.html'

class UserCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    """Create new user view"""
    model = User
    form_class = UserCreationForm
    template_name = 'core/user_create.html'
    permission_required = 'auth.add_user'
    success_url = reverse_lazy('core:dashboard')

    def form_valid(self, form):
        messages.success(self.request, _('User created successfully!'))
        return super().form_valid(form)

class SyncView(LoginRequiredMixin, TemplateView):
    template_name = 'core/sync.html'

class BackupView(LoginRequiredMixin, TemplateView):
    template_name = 'core/backup.html'

class HistoryReportView(LoginRequiredMixin, TemplateView):
    template_name = 'core/history_report.html'

class ReportDesignerView(LoginRequiredMixin, TemplateView):
    template_name = 'core/report_designer.html'

class StructureAdministrativeView(LoginRequiredMixin, TemplateView):
    template_name = 'core/structure_admin.html'

class QuickSupportView(LoginRequiredMixin, TemplateView):
    template_name = 'core/quick_support.html'

# General Site Settings Views
class SettingsView(LoginRequiredMixin, TemplateView):
    template_name = 'core/settings.html'

class LoginPageSettingsView(LoginRequiredMixin, TemplateView):
    template_name = 'core/login_page_settings.html'

# School Information Views
class SchoolInfoView(LoginRequiredMixin, TemplateView):
    template_name = 'core/school_info.html'

class SchoolDataView(LoginRequiredMixin, TemplateView):
    template_name = 'core/school_data.html'

class SchoolDataSettingsView(LoginRequiredMixin, TemplateView):
    template_name = 'core/school_data_settings.html'

class NationalityView(LoginRequiredMixin, TemplateView):
    template_name = 'core/nationality.html'

class CurrencyView(LoginRequiredMixin, TemplateView):
    template_name = 'core/currency.html'

# Taxation System Views
class EgyptianTaxSystemSettingsView(LoginRequiredMixin, TemplateView):
    template_name = 'core/tax_settings.html'

class ZakatAndIncomeView(LoginRequiredMixin, TemplateView):
    template_name = 'core/zakat_income.html'

# Email Settings Views
class EmailSettingsView(LoginRequiredMixin, TemplateView):
    template_name = 'core/email_settings.html'

class SentEmailView(LoginRequiredMixin, TemplateView):
    template_name = 'core/sent_emails.html'

class EmailFormatSettingsView(LoginRequiredMixin, TemplateView):
    template_name = 'core/email_format_settings.html'

# Students Receiver Views
class StudentsReceiverView(LoginRequiredMixin, TemplateView):
    template_name = 'core/students_receiver.html'

class StudentReceiverReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'core/receiver_reports.html'

class ShowReceiverReportView(LoginRequiredMixin, TemplateView):
    template_name = 'core/show_receiver_report.html'

# Service Providers - SMS Views
class SMSSettingsView(LoginRequiredMixin, TemplateView):
    template_name = 'core/sms_settings.html'

class MessageTemplatesView(LoginRequiredMixin, TemplateView):
    template_name = 'core/message_templates.html'

class SentMessagesView(LoginRequiredMixin, TemplateView):
    template_name = 'core/sent_messages.html'

class AutoMessagesView(LoginRequiredMixin, TemplateView):
    template_name = 'core/auto_messages.html'

class SMSReportView(LoginRequiredMixin, TemplateView):
    template_name = 'core/sms_report.html'

# Service Providers - WhatsApp Views
class WhatsAppSettingsView(LoginRequiredMixin, TemplateView):
    template_name = 'core/whatsapp_settings.html'

# Service Providers - Payment Views
class PaymentProviderView(LoginRequiredMixin, TemplateView):
    template_name = 'core/payment_provider.html'


# School Selection Views
@login_required
@csrf_protect
def school_select(request):
    """
    Professional school selection interface with card-based layout
    """
    if request.method == 'POST':
        school_id = request.POST.get('school_id')
        if school_id:
            try:
                school = School.objects.get(id=school_id, is_active=True)
                
                # Verify user has access to this school
                if not user_has_school_access(request.user, school):
                    messages.error(request, _('You do not have access to this school.'))
                    return redirect('core:school_select')
                
                # Set school in session
                if set_current_school(request, school):
                    messages.success(request, _('School changed to {}').format(school.name))
                    next_url = request.POST.get('next') or request.GET.get('next', '/dashboard/')
                    return redirect(next_url)
                else:
                    messages.error(request, _('Failed to select school. Please try again.'))
                    
            except School.DoesNotExist:
                messages.error(request, _('Invalid school selection.'))
            except Exception as e:
                messages.error(request, _('An error occurred while selecting school.'))
                logger.error(f"Unexpected error in school selection: {e}")
    
    # Get available schools based on user permissions
    schools = get_user_schools(request.user)
    selected_school = get_current_school(request)
    
    # If user has only one school, auto-select it
    if schools.count() == 1 and not selected_school:
        school = schools.first()
        if set_current_school(request, school):
            next_url = request.GET.get('next', '/dashboard/')
            return redirect(next_url)
    
    context = {
        'schools': schools,
        'selected_school': selected_school,
        'next': request.GET.get('next', '/dashboard/'),
        'school_count': schools.count(),
    }
    
    return render(request, 'core/school_select.html', context)


@login_required
@require_http_methods(["POST"])
def switch_school(request):
    """
    AJAX endpoint for seamless school switching with enhanced security
    """
    from .school_utils import (
        validate_school_access_permission, validate_session_security,
        log_security_event, get_client_ip
    )
    
    # Validate session security
    is_secure, warning_msg = validate_session_security(request)
    if not is_secure:
        log_security_event(request.user, 'SCHOOL_SWITCH_SECURITY_VIOLATION', warning_msg, ip_address=get_client_ip(request))
        return JsonResponse({
            'success': False, 
            'message': _('Session security violation detected')
        }, status=403)
    
    school_id = request.POST.get('school_id')
    
    if not school_id:
        log_security_event(request.user, 'SCHOOL_SWITCH_MISSING_ID', 'School ID missing in switch request', ip_address=get_client_ip(request))
        return JsonResponse({
            'success': False, 
            'message': _('School ID is required')
        })
    
    try:
        school = School.objects.get(id=school_id, is_active=True)
        
        # Enhanced permission validation
        has_permission, error_msg = validate_school_access_permission(request.user, school, 'read')
        if not has_permission:
            log_security_event(request.user, 'SCHOOL_SWITCH_DENIED', f"Access denied to school {school.name}: {error_msg}", school=school, ip_address=get_client_ip(request))
            return JsonResponse({
                'success': False, 
                'message': _(error_msg)
            }, status=403)
        
        # Set school in session
        if set_current_school(request, school):
            log_security_event(request.user, 'SCHOOL_SWITCH_SUCCESS', f"Successfully switched to school {school.name}", school=school, ip_address=get_client_ip(request))
            return JsonResponse({
                'success': True,
                'message': _('Successfully switched to {}').format(school.name),
                'school_name': school.name,
                'school_code': school.code,
                'school_id': str(school.id)
            })
        else:
            log_security_event(request.user, 'SCHOOL_SWITCH_FAILED', f"Failed to set school session for {school.name}", school=school, ip_address=get_client_ip(request))
            return JsonResponse({
                'success': False, 
                'message': _('Failed to switch school. Please try again.')
            })
            
    except School.DoesNotExist:
        log_security_event(request.user, 'SCHOOL_SWITCH_INVALID_ID', f"Invalid school ID: {school_id}", ip_address=get_client_ip(request))
        return JsonResponse({
            'success': False, 
            'message': _('Invalid school selection')
        })
    except Exception as e:
        log_security_event(request.user, 'SCHOOL_SWITCH_ERROR', f"Unexpected error: {str(e)}", ip_address=get_client_ip(request))
        logger.error(f"Error in school switching: {e}")
        return JsonResponse({
            'success': False, 
            'message': _('An error occurred while switching schools')
        })


@login_required
@require_http_methods(["GET"])
def get_current_school_api(request):
    """
    API endpoint for retrieving current school information with session details
    """
    current_school = get_current_school(request)
    session_info = get_school_session_info(request)
    
    if current_school:
        return JsonResponse({
            'success': True,
            'school': {
                'id': str(current_school.id),
                'name': current_school.name,
                'code': current_school.code,
                'address': current_school.address,
                'phone': current_school.phone,
                'email': current_school.email,
            },
            'session': {
                'is_valid': session_info.get('is_valid', False),
                'time_remaining_hours': session_info.get('time_remaining').total_seconds() / 3600 if session_info.get('time_remaining') else 0,
                'timestamp': session_info.get('timestamp').isoformat() if session_info.get('timestamp') else None,
            }
        })
    else:
        return JsonResponse({
            'success': False,
            'message': _('No school currently selected'),
            'session': session_info
        })


@login_required
@require_http_methods(["POST"])
def refresh_school_session_api(request):
    """
    API endpoint for refreshing the current school session
    """
    if refresh_school_session(request):
        session_info = get_school_session_info(request)
        return JsonResponse({
            'success': True,
            'message': _('School session refreshed successfully'),
            'session': {
                'is_valid': session_info.get('is_valid', False),
                'time_remaining_hours': session_info.get('time_remaining').total_seconds() / 3600 if session_info.get('time_remaining') else 0,
                'timestamp': session_info.get('timestamp').isoformat() if session_info.get('timestamp') else None,
            }
        })
    else:
        return JsonResponse({
            'success': False,
            'message': _('Failed to refresh school session')
        })


@login_required
@require_http_methods(["POST"])
def clear_school_session_api(request):
    """
    API endpoint for clearing the current school session
    """
    try:
        clear_current_school(request)
        return JsonResponse({
            'success': True,
            'message': _('School session cleared successfully')
        })
    except Exception as e:
        logger.error(f"Error clearing school session: {e}")
        return JsonResponse({
            'success': False,
            'message': _('Failed to clear school session')
        })


@login_required
@require_http_methods(["GET"])
def get_school_session_status(request):
    """
    API endpoint for getting detailed school session status
    """
    session_info = get_school_session_info(request)
    current_school = get_current_school(request)
    
    return JsonResponse({
        'success': True,
        'session': session_info,
        'current_school': {
            'id': str(current_school.id),
            'name': current_school.name,
            'code': current_school.code,
        } if current_school else None,
        'available_schools': [
            {
                'id': str(school.id),
                'name': school.name,
                'code': school.code,
            }
            for school in get_user_schools(request.user)
        ]
    })
