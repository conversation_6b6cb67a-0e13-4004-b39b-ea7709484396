"""
Webhook models for School ERP
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.validators import URLValidator
import uuid
import json

User = get_user_model()


class WebhookEndpoint(models.Model):
    """
    Model to store webhook endpoint configurations
    """
    
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('disabled', 'Disabled'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255, help_text="Descriptive name for the webhook")
    url = models.URLField(validators=[URLValidator()], help_text="Target URL for webhook delivery")
    secret = models.CharField(max_length=255, help_text="Secret key for webhook signature verification")
    
    # Event configuration
    events = models.JSONField(
        default=list,
        help_text="List of events this webhook should receive"
    )
    
    # Status and configuration
    status = models.Char<PERSON>ield(max_length=20, choices=STATUS_CHOICES, default='active')
    is_active = models.BooleanField(default=True)
    
    # HTTP configuration
    timeout_seconds = models.PositiveIntegerField(default=30)
    max_retries = models.PositiveIntegerField(default=3)
    retry_delay_seconds = models.PositiveIntegerField(default=60)
    
    # Headers and authentication
    headers = models.JSONField(
        default=dict,
        blank=True,
        help_text="Additional HTTP headers to send with webhook"
    )
    
    # Filtering
    filters = models.JSONField(
        default=dict,
        blank=True,
        help_text="Filters to apply before sending webhook"
    )
    
    # Metadata
    description = models.TextField(blank=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_webhooks')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Statistics
    total_deliveries = models.PositiveIntegerField(default=0)
    successful_deliveries = models.PositiveIntegerField(default=0)
    failed_deliveries = models.PositiveIntegerField(default=0)
    last_delivery_at = models.DateTimeField(null=True, blank=True)
    last_success_at = models.DateTimeField(null=True, blank=True)
    last_failure_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        verbose_name = "Webhook Endpoint"
        verbose_name_plural = "Webhook Endpoints"
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} ({self.url})"
    
    @property
    def success_rate(self):
        """Calculate success rate percentage"""
        if self.total_deliveries == 0:
            return 0
        return (self.successful_deliveries / self.total_deliveries) * 100
    
    def is_event_subscribed(self, event_type):
        """Check if webhook is subscribed to specific event type"""
        if '*' in self.events:
            return True
        
        for subscribed_event in self.events:
            if subscribed_event == event_type:
                return True
            # Check for wildcard patterns like 'teacher.*'
            if subscribed_event.endswith('.*'):
                prefix = subscribed_event[:-2]  # Remove '.*'
                if event_type.startswith(prefix + '.'):
                    return True
        
        return False
    
    def should_deliver(self, event_data):
        """Check if webhook should be delivered based on filters"""
        if not self.filters:
            return True
        
        # Apply filters (simplified implementation)
        for filter_key, filter_value in self.filters.items():
            if filter_key in event_data:
                if event_data[filter_key] != filter_value:
                    return False
        
        return True
    
    def update_delivery_stats(self, success=True):
        """Update delivery statistics"""
        self.total_deliveries += 1
        self.last_delivery_at = timezone.now()
        
        if success:
            self.successful_deliveries += 1
            self.last_success_at = timezone.now()
        else:
            self.failed_deliveries += 1
            self.last_failure_at = timezone.now()
        
        self.save(update_fields=[
            'total_deliveries', 'successful_deliveries', 'failed_deliveries',
            'last_delivery_at', 'last_success_at', 'last_failure_at'
        ])


class WebhookDelivery(models.Model):
    """
    Model to track webhook delivery attempts
    """
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('success', 'Success'),
        ('failed', 'Failed'),
        ('retrying', 'Retrying'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    endpoint = models.ForeignKey(WebhookEndpoint, on_delete=models.CASCADE, related_name='deliveries')
    
    # Event information
    event_type = models.CharField(max_length=100)
    event_id = models.CharField(max_length=255)
    payload = models.JSONField()
    
    # Delivery information
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    attempt_count = models.PositiveIntegerField(default=0)
    max_attempts = models.PositiveIntegerField(default=3)
    
    # Response information
    response_status_code = models.PositiveIntegerField(null=True, blank=True)
    response_headers = models.JSONField(default=dict, blank=True)
    response_body = models.TextField(blank=True)
    error_message = models.TextField(blank=True)
    
    # Timing
    created_at = models.DateTimeField(auto_now_add=True)
    first_attempt_at = models.DateTimeField(null=True, blank=True)
    last_attempt_at = models.DateTimeField(null=True, blank=True)
    delivered_at = models.DateTimeField(null=True, blank=True)
    next_retry_at = models.DateTimeField(null=True, blank=True)
    
    # Request information
    request_headers = models.JSONField(default=dict, blank=True)
    request_signature = models.CharField(max_length=255, blank=True)
    duration_ms = models.PositiveIntegerField(null=True, blank=True)
    
    class Meta:
        verbose_name = "Webhook Delivery"
        verbose_name_plural = "Webhook Deliveries"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'next_retry_at']),
            models.Index(fields=['event_type', 'created_at']),
            models.Index(fields=['endpoint', 'status']),
        ]
    
    def __str__(self):
        return f"{self.event_type} -> {self.endpoint.name} ({self.status})"
    
    @property
    def is_successful(self):
        """Check if delivery was successful"""
        return self.status == 'success'
    
    @property
    def can_retry(self):
        """Check if delivery can be retried"""
        return (self.status in ['pending', 'failed', 'retrying'] and 
                self.attempt_count < self.max_attempts)
    
    def mark_success(self, response_status_code, response_headers=None, response_body='', duration_ms=None):
        """Mark delivery as successful"""
        self.status = 'success'
        self.response_status_code = response_status_code
        self.response_headers = response_headers or {}
        self.response_body = response_body
        self.duration_ms = duration_ms
        self.delivered_at = timezone.now()
        self.save()
        
        # Update endpoint statistics
        self.endpoint.update_delivery_stats(success=True)
    
    def mark_failed(self, error_message, response_status_code=None, response_headers=None, response_body='', duration_ms=None):
        """Mark delivery as failed"""
        self.status = 'failed'
        self.error_message = error_message
        self.response_status_code = response_status_code
        self.response_headers = response_headers or {}
        self.response_body = response_body
        self.duration_ms = duration_ms
        
        # Set next retry time if retries are available
        if self.can_retry:
            self.status = 'retrying'
            retry_delay = self.endpoint.retry_delay_seconds * (2 ** self.attempt_count)  # Exponential backoff
            self.next_retry_at = timezone.now() + timezone.timedelta(seconds=retry_delay)
        
        self.save()
        
        # Update endpoint statistics if no more retries
        if not self.can_retry:
            self.endpoint.update_delivery_stats(success=False)


class WebhookEvent(models.Model):
    """
    Model to define available webhook events
    """
    
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField()
    payload_schema = models.JSONField(
        default=dict,
        help_text="JSON schema for the event payload"
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Webhook Event"
        verbose_name_plural = "Webhook Events"
        ordering = ['name']
    
    def __str__(self):
        return self.name


class WebhookAnalytics(models.Model):
    """
    Model to store webhook analytics data
    """
    
    endpoint = models.ForeignKey(WebhookEndpoint, on_delete=models.CASCADE, related_name='analytics')
    date = models.DateField()
    
    # Daily statistics
    total_deliveries = models.PositiveIntegerField(default=0)
    successful_deliveries = models.PositiveIntegerField(default=0)
    failed_deliveries = models.PositiveIntegerField(default=0)
    
    # Performance metrics
    avg_response_time_ms = models.FloatField(default=0)
    min_response_time_ms = models.PositiveIntegerField(default=0)
    max_response_time_ms = models.PositiveIntegerField(default=0)
    
    # Event type breakdown
    event_type_stats = models.JSONField(default=dict)
    
    # Error analysis
    error_types = models.JSONField(default=dict)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Webhook Analytics"
        verbose_name_plural = "Webhook Analytics"
        unique_together = ['endpoint', 'date']
        ordering = ['-date']
    
    def __str__(self):
        return f"{self.endpoint.name} - {self.date}"
    
    @property
    def success_rate(self):
        """Calculate success rate percentage"""
        if self.total_deliveries == 0:
            return 0
        return (self.successful_deliveries / self.total_deliveries) * 100


class WebhookSecurity(models.Model):
    """
    Model to store webhook security configurations
    """
    
    endpoint = models.OneToOneField(WebhookEndpoint, on_delete=models.CASCADE, related_name='security')
    
    # IP whitelist
    allowed_ips = models.JSONField(
        default=list,
        blank=True,
        help_text="List of allowed IP addresses (empty means all IPs allowed)"
    )
    
    # Rate limiting
    rate_limit_per_minute = models.PositiveIntegerField(default=60)
    rate_limit_per_hour = models.PositiveIntegerField(default=1000)
    
    # Security features
    verify_ssl = models.BooleanField(default=True)
    require_signature = models.BooleanField(default=True)
    signature_algorithm = models.CharField(
        max_length=20,
        choices=[
            ('sha256', 'SHA-256'),
            ('sha1', 'SHA-1'),
        ],
        default='sha256'
    )
    
    # Retry configuration
    exponential_backoff = models.BooleanField(default=True)
    max_retry_delay_seconds = models.PositiveIntegerField(default=3600)  # 1 hour
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Webhook Security"
        verbose_name_plural = "Webhook Security"
    
    def __str__(self):
        return f"Security for {self.endpoint.name}"
    
    def is_ip_allowed(self, ip_address):
        """Check if IP address is allowed"""
        if not self.allowed_ips:
            return True
        return ip_address in self.allowed_ips