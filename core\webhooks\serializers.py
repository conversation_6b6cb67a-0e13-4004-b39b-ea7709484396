"""
Webhook serializers for School ERP API
"""

from rest_framework import serializers
from django.utils import timezone
from .models import WebhookEndpoint, WebhookDelivery, WebhookEvent, WebhookAnalytics, WebhookSecurity
import secrets
import string


class WebhookEndpointSerializer(serializers.ModelSerializer):
    """
    Serializer for webhook endpoints
    """
    
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    success_rate = serializers.ReadOnlyField()
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = WebhookEndpoint
        fields = [
            'id', 'name', 'url', 'secret', 'events', 'status', 'status_display',
            'is_active', 'timeout_seconds', 'max_retries', 'retry_delay_seconds',
            'headers', 'filters', 'description', 'created_by_name',
            'total_deliveries', 'successful_deliveries', 'failed_deliveries',
            'success_rate', 'last_delivery_at', 'last_success_at', 'last_failure_at',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'total_deliveries', 'successful_deliveries', 'failed_deliveries',
            'last_delivery_at', 'last_success_at', 'last_failure_at',
            'created_at', 'updated_at'
        ]
        extra_kwargs = {
            'secret': {'write_only': True}
        }
    
    def create(self, validated_data):
        """
        Generate secret if not provided
        """
        if not validated_data.get('secret'):
            # Generate a secure random secret
            alphabet = string.ascii_letters + string.digits
            validated_data['secret'] = ''.join(secrets.choice(alphabet) for _ in range(32))
        
        return super().create(validated_data)
    
    def validate_events(self, value):
        """
        Validate events list
        """
        if not isinstance(value, list):
            raise serializers.ValidationError("Events must be a list")
        
        if not value:
            raise serializers.ValidationError("At least one event must be specified")
        
        # Validate event names (basic validation)
        for event in value:
            if not isinstance(event, str):
                raise serializers.ValidationError("Event names must be strings")
            
            if len(event) > 100:
                raise serializers.ValidationError("Event names cannot exceed 100 characters")
        
        return value
    
    def validate_headers(self, value):
        """
        Validate custom headers
        """
        if not isinstance(value, dict):
            raise serializers.ValidationError("Headers must be a dictionary")
        
        # Check for forbidden headers
        forbidden_headers = [
            'content-type', 'user-agent', 'x-webhook-event',
            'x-webhook-delivery', 'x-webhook-timestamp', 'x-webhook-signature'
        ]
        
        for header_name in value.keys():
            if header_name.lower() in forbidden_headers:
                raise serializers.ValidationError(f"Header '{header_name}' is reserved and cannot be set")
        
        return value
    
    def validate_timeout_seconds(self, value):
        """
        Validate timeout value
        """
        if value < 1 or value > 300:  # 1 second to 5 minutes
            raise serializers.ValidationError("Timeout must be between 1 and 300 seconds")
        
        return value
    
    def validate_max_retries(self, value):
        """
        Validate max retries value
        """
        if value < 0 or value > 10:
            raise serializers.ValidationError("Max retries must be between 0 and 10")
        
        return value


class WebhookEndpointCreateSerializer(WebhookEndpointSerializer):
    """
    Serializer for creating webhook endpoints (includes secret in response)
    """
    
    class Meta(WebhookEndpointSerializer.Meta):
        extra_kwargs = {}  # Don't hide secret on creation
    
    def to_representation(self, instance):
        """
        Include secret in creation response only
        """
        data = super().to_representation(instance)
        
        # Only include secret if this is a new instance
        if hasattr(self, '_created') and self._created:
            data['secret'] = instance.secret
        
        return data
    
    def create(self, validated_data):
        """
        Mark instance as created for secret inclusion
        """
        instance = super().create(validated_data)
        self._created = True
        return instance


class WebhookDeliverySerializer(serializers.ModelSerializer):
    """
    Serializer for webhook deliveries
    """
    
    endpoint_name = serializers.CharField(source='endpoint.name', read_only=True)
    endpoint_url = serializers.CharField(source='endpoint.url', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    is_successful = serializers.ReadOnlyField()
    can_retry = serializers.ReadOnlyField()
    
    class Meta:
        model = WebhookDelivery
        fields = [
            'id', 'endpoint', 'endpoint_name', 'endpoint_url', 'event_type',
            'event_id', 'payload', 'status', 'status_display', 'attempt_count',
            'max_attempts', 'response_status_code', 'response_headers',
            'response_body', 'error_message', 'request_headers', 'request_signature',
            'duration_ms', 'is_successful', 'can_retry', 'created_at',
            'first_attempt_at', 'last_attempt_at', 'delivered_at', 'next_retry_at'
        ]
        read_only_fields = [
            'id', 'response_status_code', 'response_headers', 'response_body',
            'error_message', 'request_headers', 'request_signature', 'duration_ms',
            'created_at', 'first_attempt_at', 'last_attempt_at', 'delivered_at',
            'next_retry_at'
        ]


class WebhookDeliverySummarySerializer(serializers.ModelSerializer):
    """
    Lightweight serializer for delivery summaries
    """
    
    endpoint_name = serializers.CharField(source='endpoint.name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = WebhookDelivery
        fields = [
            'id', 'endpoint_name', 'event_type', 'status', 'status_display',
            'attempt_count', 'created_at', 'delivered_at'
        ]


class WebhookEventSerializer(serializers.ModelSerializer):
    """
    Serializer for webhook events
    """
    
    class Meta:
        model = WebhookEvent
        fields = [
            'id', 'name', 'description', 'payload_schema', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def validate_name(self, value):
        """
        Validate event name format
        """
        if not value.replace('_', '').replace('.', '').isalnum():
            raise serializers.ValidationError(
                "Event name can only contain letters, numbers, underscores, and dots"
            )
        
        return value
    
    def validate_payload_schema(self, value):
        """
        Validate JSON schema format
        """
        if not isinstance(value, dict):
            raise serializers.ValidationError("Payload schema must be a valid JSON object")
        
        # Basic JSON Schema validation
        if 'type' not in value:
            value['type'] = 'object'
        
        return value


class WebhookAnalyticsSerializer(serializers.ModelSerializer):
    """
    Serializer for webhook analytics
    """
    
    endpoint_name = serializers.CharField(source='endpoint.name', read_only=True)
    success_rate = serializers.ReadOnlyField()
    
    class Meta:
        model = WebhookAnalytics
        fields = [
            'id', 'endpoint', 'endpoint_name', 'date', 'total_deliveries',
            'successful_deliveries', 'failed_deliveries', 'success_rate',
            'avg_response_time_ms', 'min_response_time_ms', 'max_response_time_ms',
            'event_type_stats', 'error_types', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class WebhookSecuritySerializer(serializers.ModelSerializer):
    """
    Serializer for webhook security settings
    """
    
    class Meta:
        model = WebhookSecurity
        fields = [
            'id', 'endpoint', 'allowed_ips', 'rate_limit_per_minute',
            'rate_limit_per_hour', 'verify_ssl', 'require_signature',
            'signature_algorithm', 'exponential_backoff', 'max_retry_delay_seconds',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def validate_allowed_ips(self, value):
        """
        Validate IP addresses in allowed list
        """
        if not isinstance(value, list):
            raise serializers.ValidationError("Allowed IPs must be a list")
        
        import ipaddress
        
        for ip in value:
            try:
                ipaddress.ip_address(ip)
            except ValueError:
                raise serializers.ValidationError(f"Invalid IP address: {ip}")
        
        return value
    
    def validate_rate_limit_per_minute(self, value):
        """
        Validate per-minute rate limit
        """
        if value < 1 or value > 1000:
            raise serializers.ValidationError("Rate limit per minute must be between 1 and 1000")
        
        return value
    
    def validate_rate_limit_per_hour(self, value):
        """
        Validate per-hour rate limit
        """
        if value < 1 or value > 10000:
            raise serializers.ValidationError("Rate limit per hour must be between 1 and 10000")
        
        return value


class WebhookTriggerSerializer(serializers.Serializer):
    """
    Serializer for webhook trigger requests
    """
    
    event_type = serializers.CharField(max_length=100)
    event_data = serializers.JSONField()
    event_id = serializers.CharField(max_length=255, required=False)
    
    def validate_event_type(self, value):
        """
        Validate event type format
        """
        if not value.replace('_', '').replace('.', '').isalnum():
            raise serializers.ValidationError(
                "Event type can only contain letters, numbers, underscores, and dots"
            )
        
        return value
    
    def validate_event_data(self, value):
        """
        Validate event data
        """
        if not isinstance(value, dict):
            raise serializers.ValidationError("Event data must be a JSON object")
        
        return value


class WebhookStatusSerializer(serializers.Serializer):
    """
    Serializer for webhook system status
    """
    
    system_status = serializers.CharField()
    timestamp = serializers.DateTimeField()
    endpoints = serializers.DictField()
    deliveries_24h = serializers.DictField()
    top_events_24h = serializers.ListField()


class WebhookReportSerializer(serializers.Serializer):
    """
    Serializer for webhook reports
    """
    
    endpoint = serializers.DictField()
    period = serializers.DictField()
    summary = serializers.DictField()
    daily_data = serializers.ListField()
    event_summary = serializers.DictField()
    error_summary = serializers.DictField()


class WebhookManagementSerializer(serializers.Serializer):
    """
    Serializer for webhook management operations
    """
    
    operation = serializers.ChoiceField(choices=[
        'retry_all_failed',
        'update_analytics',
        'cleanup_old_deliveries'
    ])
    days_to_keep = serializers.IntegerField(required=False, min_value=1, max_value=365)
    
    def validate(self, data):
        """
        Validate operation-specific parameters
        """
        operation = data.get('operation')
        
        if operation == 'cleanup_old_deliveries' and 'days_to_keep' not in data:
            data['days_to_keep'] = 90  # Default to 90 days
        
        return data


class WebhookTestSerializer(serializers.Serializer):
    """
    Serializer for webhook test requests
    """
    
    test_data = serializers.JSONField(required=False)
    
    def validate_test_data(self, value):
        """
        Validate test data
        """
        if value is not None and not isinstance(value, dict):
            raise serializers.ValidationError("Test data must be a JSON object")
        
        return value or {
            'test': True,
            'message': 'This is a test webhook delivery',
            'timestamp': timezone.now().isoformat()
        }