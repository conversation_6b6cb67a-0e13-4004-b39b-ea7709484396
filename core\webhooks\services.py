"""
Webhook services for School ERP
"""

import json
import hmac
import hashlib
import logging
import requests
from datetime import datetime, timedelta
from django.utils import timezone
from django.core.cache import cache
from django.conf import settings
from celery import shared_task
from .models import WebhookEndpoint, WebhookDelivery, WebhookAnalytics

logger = logging.getLogger(__name__)


class WebhookService:
    """
    Service class for managing webhook operations
    """
    
    def __init__(self):
        self.timeout = 30
        self.max_retries = 3
    
    def trigger_webhook(self, event_type, event_data, event_id=None):
        """
        Trigger webhooks for a specific event
        """
        # Get all active endpoints subscribed to this event
        endpoints = WebhookEndpoint.objects.filter(
            is_active=True,
            status='active'
        )
        
        triggered_count = 0
        
        for endpoint in endpoints:
            if endpoint.is_event_subscribed(event_type):
                if endpoint.should_deliver(event_data):
                    # Create delivery record
                    delivery = WebhookDelivery.objects.create(
                        endpoint=endpoint,
                        event_type=event_type,
                        event_id=event_id or f"{event_type}_{timezone.now().timestamp()}",
                        payload=event_data,
                        max_attempts=endpoint.max_retries
                    )
                    
                    # Queue for delivery
                    self.queue_delivery(delivery.id)
                    triggered_count += 1
        
        logger.info(f"Triggered {triggered_count} webhooks for event: {event_type}")
        return triggered_count
    
    def queue_delivery(self, delivery_id):
        """
        Queue webhook delivery for processing
        """
        # Use Celery to process webhook delivery asynchronously
        process_webhook_delivery.delay(delivery_id)
    
    def deliver_webhook(self, delivery_id):
        """
        Deliver a specific webhook
        """
        try:
            delivery = WebhookDelivery.objects.get(id=delivery_id)
        except WebhookDelivery.DoesNotExist:
            logger.error(f"Webhook delivery {delivery_id} not found")
            return False
        
        if not delivery.endpoint.is_active:
            logger.warning(f"Webhook endpoint {delivery.endpoint.name} is not active")
            return False
        
        # Increment attempt count
        delivery.attempt_count += 1
        if delivery.first_attempt_at is None:
            delivery.first_attempt_at = timezone.now()
        delivery.last_attempt_at = timezone.now()
        delivery.save()
        
        # Prepare request
        headers = self._prepare_headers(delivery)
        payload = self._prepare_payload(delivery)
        
        start_time = timezone.now()
        
        try:
            # Make HTTP request
            response = requests.post(
                delivery.endpoint.url,
                json=payload,
                headers=headers,
                timeout=delivery.endpoint.timeout_seconds,
                verify=delivery.endpoint.security.verify_ssl if hasattr(delivery.endpoint, 'security') else True
            )
            
            duration_ms = int((timezone.now() - start_time).total_seconds() * 1000)
            
            # Check if successful
            if 200 <= response.status_code < 300:
                delivery.mark_success(
                    response_status_code=response.status_code,
                    response_headers=dict(response.headers),
                    response_body=response.text[:1000],  # Limit response body size
                    duration_ms=duration_ms
                )
                logger.info(f"Webhook delivered successfully: {delivery.id}")
                return True
            else:
                delivery.mark_failed(
                    error_message=f"HTTP {response.status_code}: {response.text[:500]}",
                    response_status_code=response.status_code,
                    response_headers=dict(response.headers),
                    response_body=response.text[:1000],
                    duration_ms=duration_ms
                )
                logger.warning(f"Webhook delivery failed with status {response.status_code}: {delivery.id}")
                return False
        
        except requests.exceptions.Timeout:
            delivery.mark_failed(
                error_message="Request timeout",
                duration_ms=delivery.endpoint.timeout_seconds * 1000
            )
            logger.warning(f"Webhook delivery timed out: {delivery.id}")
            return False
        
        except requests.exceptions.ConnectionError as e:
            delivery.mark_failed(error_message=f"Connection error: {str(e)}")
            logger.warning(f"Webhook delivery connection error: {delivery.id}")
            return False
        
        except Exception as e:
            delivery.mark_failed(error_message=f"Unexpected error: {str(e)}")
            logger.error(f"Webhook delivery unexpected error: {delivery.id} - {e}")
            return False
    
    def _prepare_headers(self, delivery):
        """
        Prepare HTTP headers for webhook request
        """
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'SchoolERP-Webhook/1.0',
            'X-Webhook-Event': delivery.event_type,
            'X-Webhook-Delivery': str(delivery.id),
            'X-Webhook-Timestamp': str(int(timezone.now().timestamp()))
        }
        
        # Add custom headers from endpoint configuration
        if delivery.endpoint.headers:
            headers.update(delivery.endpoint.headers)
        
        # Add signature if required
        if hasattr(delivery.endpoint, 'security') and delivery.endpoint.security.require_signature:
            signature = self._generate_signature(delivery)
            headers['X-Webhook-Signature'] = signature
        
        # Store request headers for logging
        delivery.request_headers = headers
        delivery.save()
        
        return headers
    
    def _prepare_payload(self, delivery):
        """
        Prepare payload for webhook request
        """
        payload = {
            'event': delivery.event_type,
            'event_id': delivery.event_id,
            'timestamp': timezone.now().isoformat(),
            'data': delivery.payload
        }
        
        return payload
    
    def _generate_signature(self, delivery):
        """
        Generate HMAC signature for webhook security
        """
        payload_json = json.dumps(self._prepare_payload(delivery), sort_keys=True)
        secret = delivery.endpoint.secret.encode('utf-8')
        
        # Get signature algorithm
        algorithm = 'sha256'
        if hasattr(delivery.endpoint, 'security'):
            algorithm = delivery.endpoint.security.signature_algorithm
        
        if algorithm == 'sha1':
            signature = hmac.new(secret, payload_json.encode('utf-8'), hashlib.sha1).hexdigest()
            return f"sha1={signature}"
        else:
            signature = hmac.new(secret, payload_json.encode('utf-8'), hashlib.sha256).hexdigest()
            return f"sha256={signature}"
    
    def retry_failed_deliveries(self):
        """
        Retry failed webhook deliveries that are eligible for retry
        """
        # Get deliveries that need retry
        now = timezone.now()
        failed_deliveries = WebhookDelivery.objects.filter(
            status='retrying',
            next_retry_at__lte=now
        )
        
        retry_count = 0
        for delivery in failed_deliveries:
            if delivery.can_retry:
                self.queue_delivery(delivery.id)
                retry_count += 1
        
        logger.info(f"Queued {retry_count} webhook deliveries for retry")
        return retry_count
    
    def get_endpoint_statistics(self, endpoint_id):
        """
        Get statistics for a specific webhook endpoint
        """
        try:
            endpoint = WebhookEndpoint.objects.get(id=endpoint_id)
        except WebhookEndpoint.DoesNotExist:
            return None
        
        # Get recent deliveries
        recent_deliveries = WebhookDelivery.objects.filter(
            endpoint=endpoint,
            created_at__gte=timezone.now() - timedelta(days=30)
        )
        
        stats = {
            'endpoint_name': endpoint.name,
            'total_deliveries': endpoint.total_deliveries,
            'successful_deliveries': endpoint.successful_deliveries,
            'failed_deliveries': endpoint.failed_deliveries,
            'success_rate': endpoint.success_rate,
            'last_delivery_at': endpoint.last_delivery_at,
            'last_success_at': endpoint.last_success_at,
            'last_failure_at': endpoint.last_failure_at,
            'recent_deliveries': recent_deliveries.count(),
            'avg_response_time': self._calculate_avg_response_time(recent_deliveries)
        }
        
        return stats
    
    def _calculate_avg_response_time(self, deliveries):
        """
        Calculate average response time for deliveries
        """
        response_times = [
            d.duration_ms for d in deliveries 
            if d.duration_ms is not None and d.status == 'success'
        ]
        
        if not response_times:
            return 0
        
        return sum(response_times) / len(response_times)


class WebhookAnalyticsService:
    """
    Service for webhook analytics and reporting
    """
    
    def update_daily_analytics(self, endpoint_id, date=None):
        """
        Update daily analytics for an endpoint
        """
        if date is None:
            date = timezone.now().date()
        
        try:
            endpoint = WebhookEndpoint.objects.get(id=endpoint_id)
        except WebhookEndpoint.DoesNotExist:
            return None
        
        # Get or create analytics record
        analytics, created = WebhookAnalytics.objects.get_or_create(
            endpoint=endpoint,
            date=date,
            defaults={
                'total_deliveries': 0,
                'successful_deliveries': 0,
                'failed_deliveries': 0,
                'avg_response_time_ms': 0,
                'event_type_stats': {},
                'error_types': {}
            }
        )
        
        # Get deliveries for the date
        deliveries = WebhookDelivery.objects.filter(
            endpoint=endpoint,
            created_at__date=date
        )
        
        # Calculate statistics
        total_deliveries = deliveries.count()
        successful_deliveries = deliveries.filter(status='success').count()
        failed_deliveries = deliveries.filter(status='failed').count()
        
        # Calculate response times
        successful_with_time = deliveries.filter(
            status='success',
            duration_ms__isnull=False
        )
        
        if successful_with_time.exists():
            response_times = [d.duration_ms for d in successful_with_time]
            avg_response_time = sum(response_times) / len(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
        else:
            avg_response_time = 0
            min_response_time = 0
            max_response_time = 0
        
        # Event type breakdown
        event_type_stats = {}
        for delivery in deliveries:
            event_type = delivery.event_type
            if event_type not in event_type_stats:
                event_type_stats[event_type] = {'total': 0, 'success': 0, 'failed': 0}
            
            event_type_stats[event_type]['total'] += 1
            if delivery.status == 'success':
                event_type_stats[event_type]['success'] += 1
            elif delivery.status == 'failed':
                event_type_stats[event_type]['failed'] += 1
        
        # Error type analysis
        error_types = {}
        failed_deliveries_qs = deliveries.filter(status='failed')
        for delivery in failed_deliveries_qs:
            if delivery.response_status_code:
                error_key = f"HTTP_{delivery.response_status_code}"
            else:
                error_key = "CONNECTION_ERROR"
            
            error_types[error_key] = error_types.get(error_key, 0) + 1
        
        # Update analytics record
        analytics.total_deliveries = total_deliveries
        analytics.successful_deliveries = successful_deliveries
        analytics.failed_deliveries = failed_deliveries
        analytics.avg_response_time_ms = avg_response_time
        analytics.min_response_time_ms = min_response_time
        analytics.max_response_time_ms = max_response_time
        analytics.event_type_stats = event_type_stats
        analytics.error_types = error_types
        analytics.save()
        
        return analytics
    
    def generate_endpoint_report(self, endpoint_id, days=30):
        """
        Generate comprehensive report for an endpoint
        """
        try:
            endpoint = WebhookEndpoint.objects.get(id=endpoint_id)
        except WebhookEndpoint.DoesNotExist:
            return None
        
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)
        
        # Get analytics for the period
        analytics = WebhookAnalytics.objects.filter(
            endpoint=endpoint,
            date__range=[start_date, end_date]
        ).order_by('date')
        
        # Aggregate data
        total_deliveries = sum(a.total_deliveries for a in analytics)
        total_successful = sum(a.successful_deliveries for a in analytics)
        total_failed = sum(a.failed_deliveries for a in analytics)
        
        # Calculate trends
        daily_data = []
        for analytic in analytics:
            daily_data.append({
                'date': analytic.date.isoformat(),
                'deliveries': analytic.total_deliveries,
                'success_rate': analytic.success_rate,
                'avg_response_time': analytic.avg_response_time_ms
            })
        
        # Event type summary
        event_summary = {}
        for analytic in analytics:
            for event_type, stats in analytic.event_type_stats.items():
                if event_type not in event_summary:
                    event_summary[event_type] = {'total': 0, 'success': 0, 'failed': 0}
                
                event_summary[event_type]['total'] += stats['total']
                event_summary[event_type]['success'] += stats['success']
                event_summary[event_type]['failed'] += stats['failed']
        
        # Error summary
        error_summary = {}
        for analytic in analytics:
            for error_type, count in analytic.error_types.items():
                error_summary[error_type] = error_summary.get(error_type, 0) + count
        
        report = {
            'endpoint': {
                'id': str(endpoint.id),
                'name': endpoint.name,
                'url': endpoint.url,
                'status': endpoint.status
            },
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'days': days
            },
            'summary': {
                'total_deliveries': total_deliveries,
                'successful_deliveries': total_successful,
                'failed_deliveries': total_failed,
                'success_rate': (total_successful / total_deliveries * 100) if total_deliveries > 0 else 0
            },
            'daily_data': daily_data,
            'event_summary': event_summary,
            'error_summary': error_summary
        }
        
        return report


class WebhookSecurityService:
    """
    Service for webhook security operations
    """
    
    def verify_signature(self, payload, signature, secret, algorithm='sha256'):
        """
        Verify webhook signature
        """
        if not signature or not secret:
            return False
        
        # Parse signature
        if signature.startswith('sha256='):
            expected_signature = signature[7:]
            algorithm = 'sha256'
        elif signature.startswith('sha1='):
            expected_signature = signature[5:]
            algorithm = 'sha1'
        else:
            return False
        
        # Generate expected signature
        if algorithm == 'sha1':
            computed_signature = hmac.new(
                secret.encode('utf-8'),
                payload.encode('utf-8'),
                hashlib.sha1
            ).hexdigest()
        else:
            computed_signature = hmac.new(
                secret.encode('utf-8'),
                payload.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
        
        # Compare signatures
        return hmac.compare_digest(expected_signature, computed_signature)
    
    def check_rate_limit(self, endpoint_id, ip_address=None):
        """
        Check if request is within rate limits
        """
        try:
            endpoint = WebhookEndpoint.objects.get(id=endpoint_id)
        except WebhookEndpoint.DoesNotExist:
            return False
        
        if not hasattr(endpoint, 'security'):
            return True
        
        security = endpoint.security
        now = timezone.now()
        
        # Check per-minute rate limit
        minute_key = f"webhook_rate_limit_{endpoint_id}_{now.strftime('%Y%m%d_%H%M')}"
        minute_count = cache.get(minute_key, 0)
        
        if minute_count >= security.rate_limit_per_minute:
            return False
        
        # Check per-hour rate limit
        hour_key = f"webhook_rate_limit_{endpoint_id}_{now.strftime('%Y%m%d_%H')}"
        hour_count = cache.get(hour_key, 0)
        
        if hour_count >= security.rate_limit_per_hour:
            return False
        
        # Increment counters
        cache.set(minute_key, minute_count + 1, 60)  # 1 minute
        cache.set(hour_key, hour_count + 1, 3600)  # 1 hour
        
        return True
    
    def is_ip_allowed(self, endpoint_id, ip_address):
        """
        Check if IP address is allowed for the endpoint
        """
        try:
            endpoint = WebhookEndpoint.objects.get(id=endpoint_id)
        except WebhookEndpoint.DoesNotExist:
            return False
        
        if not hasattr(endpoint, 'security'):
            return True
        
        return endpoint.security.is_ip_allowed(ip_address)


# Celery tasks for asynchronous webhook processing
@shared_task(bind=True, max_retries=3)
def process_webhook_delivery(self, delivery_id):
    """
    Celery task to process webhook delivery
    """
    service = WebhookService()
    
    try:
        success = service.deliver_webhook(delivery_id)
        
        if not success:
            # Check if we should retry
            delivery = WebhookDelivery.objects.get(id=delivery_id)
            if delivery.can_retry:
                # Calculate retry delay with exponential backoff
                retry_delay = delivery.endpoint.retry_delay_seconds * (2 ** delivery.attempt_count)
                
                # Limit maximum retry delay
                if hasattr(delivery.endpoint, 'security'):
                    max_delay = delivery.endpoint.security.max_retry_delay_seconds
                    retry_delay = min(retry_delay, max_delay)
                
                # Schedule retry
                self.retry(countdown=retry_delay)
    
    except Exception as exc:
        logger.error(f"Error processing webhook delivery {delivery_id}: {exc}")
        raise self.retry(exc=exc, countdown=60)


@shared_task
def retry_failed_webhooks():
    """
    Celery task to retry failed webhook deliveries
    """
    service = WebhookService()
    return service.retry_failed_deliveries()


@shared_task
def update_webhook_analytics():
    """
    Celery task to update webhook analytics
    """
    analytics_service = WebhookAnalyticsService()
    
    # Update analytics for all active endpoints
    endpoints = WebhookEndpoint.objects.filter(is_active=True)
    updated_count = 0
    
    for endpoint in endpoints:
        try:
            analytics_service.update_daily_analytics(endpoint.id)
            updated_count += 1
        except Exception as e:
            logger.error(f"Error updating analytics for endpoint {endpoint.id}: {e}")
    
    logger.info(f"Updated analytics for {updated_count} webhook endpoints")
    return updated_count


# Global service instances
webhook_service = WebhookService()
webhook_analytics_service = WebhookAnalyticsService()
webhook_security_service = WebhookSecurityService()