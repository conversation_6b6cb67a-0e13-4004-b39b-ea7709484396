"""
Tests for webhook functionality in School ERP
"""

import json
import hmac
import hashlib
from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken

from .models import WebhookEndpoint, WebhookDelivery, WebhookEvent, WebhookAnalytics
from .services import WebhookService, WebhookAnalyticsService, WebhookSecurityService
from .serializers import WebhookEndpointSerializer, WebhookDeliverySerializer

User = get_user_model()


class WebhookModelTestCase(TestCase):
    """
    Test webhook models
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
    
    def test_webhook_endpoint_creation(self):
        """Test creating webhook endpoint"""
        endpoint = WebhookEndpoint.objects.create(
            name='Test Webhook',
            url='https://example.com/webhook',
            secret='test_secret',
            events=['student.created', 'student.updated'],
            created_by=self.user
        )
        
        self.assertEqual(endpoint.name, 'Test Webhook')
        self.assertEqual(endpoint.url, 'https://example.com/webhook')
        self.assertEqual(endpoint.status, 'active')
        self.assertTrue(endpoint.is_active)
        self.assertEqual(endpoint.success_rate, 0)
    
    def test_webhook_endpoint_event_subscription(self):
        """Test event subscription checking"""
        endpoint = WebhookEndpoint.objects.create(
            name='Test Webhook',
            url='https://example.com/webhook',
            secret='test_secret',
            events=['student.created', 'teacher.*'],
            created_by=self.user
        )
        
        self.assertTrue(endpoint.is_event_subscribed('student.created'))
        self.assertFalse(endpoint.is_event_subscribed('student.deleted'))
        self.assertTrue(endpoint.is_event_subscribed('teacher.created'))  # Wildcard
    
    def test_webhook_delivery_creation(self):
        """Test creating webhook delivery"""
        endpoint = WebhookEndpoint.objects.create(
            name='Test Webhook',
            url='https://example.com/webhook',
            secret='test_secret',
            events=['student.created'],
            created_by=self.user
        )
        
        delivery = WebhookDelivery.objects.create(
            endpoint=endpoint,
            event_type='student.created',
            event_id='test_event_123',
            payload={'student_id': 1, 'name': 'John Doe'}
        )
        
        self.assertEqual(delivery.status, 'pending')
        self.assertEqual(delivery.attempt_count, 0)
        self.assertTrue(delivery.can_retry)
        self.assertFalse(delivery.is_successful)
    
    def test_webhook_delivery_success(self):
        """Test marking delivery as successful"""
        endpoint = WebhookEndpoint.objects.create(
            name='Test Webhook',
            url='https://example.com/webhook',
            secret='test_secret',
            events=['student.created'],
            created_by=self.user
        )
        
        delivery = WebhookDelivery.objects.create(
            endpoint=endpoint,
            event_type='student.created',
            event_id='test_event_123',
            payload={'student_id': 1}
        )
        
        delivery.mark_success(200, {'content-type': 'application/json'}, 'OK', 150)
        
        self.assertEqual(delivery.status, 'success')
        self.assertEqual(delivery.response_status_code, 200)
        self.assertEqual(delivery.duration_ms, 150)
        self.assertTrue(delivery.is_successful)
        self.assertIsNotNone(delivery.delivered_at)
        
        # Check endpoint stats updated
        endpoint.refresh_from_db()
        self.assertEqual(endpoint.total_deliveries, 1)
        self.assertEqual(endpoint.successful_deliveries, 1)
        self.assertEqual(endpoint.success_rate, 100)
    
    def test_webhook_delivery_failure(self):
        """Test marking delivery as failed"""
        endpoint = WebhookEndpoint.objects.create(
            name='Test Webhook',
            url='https://example.com/webhook',
            secret='test_secret',
            events=['student.created'],
            created_by=self.user
        )
        
        delivery = WebhookDelivery.objects.create(
            endpoint=endpoint,
            event_type='student.created',
            event_id='test_event_123',
            payload={'student_id': 1}
        )
        
        delivery.mark_failed('Connection timeout', 0, {}, '', 30000)
        
        self.assertEqual(delivery.status, 'retrying')  # Should be retrying since can_retry is True
        self.assertEqual(delivery.error_message, 'Connection timeout')
        self.assertTrue(delivery.can_retry)
        self.assertIsNotNone(delivery.next_retry_at)


class WebhookServiceTestCase(TestCase):
    """
    Test webhook services
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
        
        self.endpoint = WebhookEndpoint.objects.create(
            name='Test Webhook',
            url='https://example.com/webhook',
            secret='test_secret',
            events=['student.created', 'student.updated'],
            created_by=self.user
        )
        
        self.service = WebhookService()
    
    def test_trigger_webhook(self):
        """Test triggering webhooks"""
        event_data = {'student_id': 1, 'name': 'John Doe'}
        
        count = self.service.trigger_webhook('student.created', event_data, 'test_event_123')
        
        self.assertEqual(count, 1)
        
        # Check delivery was created
        delivery = WebhookDelivery.objects.get(event_id='test_event_123')
        self.assertEqual(delivery.endpoint, self.endpoint)
        self.assertEqual(delivery.event_type, 'student.created')
        self.assertEqual(delivery.payload, event_data)
    
    def test_trigger_webhook_no_subscribers(self):
        """Test triggering webhook with no subscribers"""
        event_data = {'teacher_id': 1, 'name': 'Jane Smith'}
        
        count = self.service.trigger_webhook('teacher.deleted', event_data)
        
        self.assertEqual(count, 0)
        self.assertFalse(WebhookDelivery.objects.filter(event_type='teacher.deleted').exists())
    
    @patch('requests.post')
    def test_deliver_webhook_success(self, mock_post):
        """Test successful webhook delivery"""
        # Mock successful HTTP response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = 'OK'
        mock_response.headers = {'content-type': 'application/json'}
        mock_post.return_value = mock_response
        
        # Create delivery
        delivery = WebhookDelivery.objects.create(
            endpoint=self.endpoint,
            event_type='student.created',
            event_id='test_event_123',
            payload={'student_id': 1}
        )
        
        # Deliver webhook
        success = self.service.deliver_webhook(delivery.id)
        
        self.assertTrue(success)
        
        # Check delivery was marked as successful
        delivery.refresh_from_db()
        self.assertEqual(delivery.status, 'success')
        self.assertEqual(delivery.response_status_code, 200)
        self.assertIsNotNone(delivery.delivered_at)
    
    @patch('requests.post')
    def test_deliver_webhook_failure(self, mock_post):
        """Test failed webhook delivery"""
        # Mock failed HTTP response
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.text = 'Internal Server Error'
        mock_response.headers = {}
        mock_post.return_value = mock_response
        
        # Create delivery
        delivery = WebhookDelivery.objects.create(
            endpoint=self.endpoint,
            event_type='student.created',
            event_id='test_event_123',
            payload={'student_id': 1}
        )
        
        # Deliver webhook
        success = self.service.deliver_webhook(delivery.id)
        
        self.assertFalse(success)
        
        # Check delivery was marked as failed
        delivery.refresh_from_db()
        self.assertEqual(delivery.status, 'retrying')
        self.assertEqual(delivery.response_status_code, 500)
        self.assertIn('HTTP 500', delivery.error_message)
    
    def test_generate_signature(self):
        """Test webhook signature generation"""
        delivery = WebhookDelivery.objects.create(
            endpoint=self.endpoint,
            event_type='student.created',
            event_id='test_event_123',
            payload={'student_id': 1}
        )
        
        signature = self.service._generate_signature(delivery)
        
        self.assertTrue(signature.startswith('sha256='))
        
        # Verify signature
        payload_json = json.dumps(self.service._prepare_payload(delivery), sort_keys=True)
        expected_signature = hmac.new(
            'test_secret'.encode('utf-8'),
            payload_json.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        self.assertEqual(signature, f'sha256={expected_signature}')


class WebhookAnalyticsServiceTestCase(TestCase):
    """
    Test webhook analytics service
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
        
        self.endpoint = WebhookEndpoint.objects.create(
            name='Test Webhook',
            url='https://example.com/webhook',
            secret='test_secret',
            events=['student.created'],
            created_by=self.user
        )
        
        self.service = WebhookAnalyticsService()
    
    def test_update_daily_analytics(self):
        """Test updating daily analytics"""
        # Create some deliveries
        delivery1 = WebhookDelivery.objects.create(
            endpoint=self.endpoint,
            event_type='student.created',
            event_id='test_1',
            payload={'student_id': 1}
        )
        delivery1.mark_success(200, {}, 'OK', 150)
        
        delivery2 = WebhookDelivery.objects.create(
            endpoint=self.endpoint,
            event_type='student.created',
            event_id='test_2',
            payload={'student_id': 2}
        )
        delivery2.mark_failed('Timeout', 0, {}, '', 30000)
        
        # Update analytics
        analytics = self.service.update_daily_analytics(self.endpoint.id)
        
        self.assertIsNotNone(analytics)
        self.assertEqual(analytics.total_deliveries, 2)
        self.assertEqual(analytics.successful_deliveries, 1)
        self.assertEqual(analytics.failed_deliveries, 1)
        self.assertEqual(analytics.success_rate, 50)
        self.assertEqual(analytics.avg_response_time_ms, 150)
    
    def test_generate_endpoint_report(self):
        """Test generating endpoint report"""
        # Create analytics data
        analytics = WebhookAnalytics.objects.create(
            endpoint=self.endpoint,
            date=timezone.now().date(),
            total_deliveries=10,
            successful_deliveries=8,
            failed_deliveries=2,
            avg_response_time_ms=200,
            event_type_stats={'student.created': {'total': 10, 'success': 8, 'failed': 2}},
            error_types={'HTTP_500': 2}
        )
        
        # Generate report
        report = self.service.generate_endpoint_report(self.endpoint.id, 7)
        
        self.assertIsNotNone(report)
        self.assertEqual(report['endpoint']['name'], 'Test Webhook')
        self.assertEqual(report['summary']['total_deliveries'], 10)
        self.assertEqual(report['summary']['success_rate'], 80)
        self.assertIn('student.created', report['event_summary'])
        self.assertIn('HTTP_500', report['error_summary'])


class WebhookSecurityServiceTestCase(TestCase):
    """
    Test webhook security service
    """
    
    def setUp(self):
        self.service = WebhookSecurityService()
    
    def test_verify_signature_sha256(self):
        """Test SHA-256 signature verification"""
        payload = '{"test": "data"}'
        secret = 'test_secret'
        
        # Generate signature
        signature = hmac.new(
            secret.encode('utf-8'),
            payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        # Test verification
        self.assertTrue(self.service.verify_signature(payload, f'sha256={signature}', secret))
        self.assertFalse(self.service.verify_signature(payload, f'sha256=invalid', secret))
    
    def test_verify_signature_sha1(self):
        """Test SHA-1 signature verification"""
        payload = '{"test": "data"}'
        secret = 'test_secret'
        
        # Generate signature
        signature = hmac.new(
            secret.encode('utf-8'),
            payload.encode('utf-8'),
            hashlib.sha1
        ).hexdigest()
        
        # Test verification
        self.assertTrue(self.service.verify_signature(payload, f'sha1={signature}', secret))
        self.assertFalse(self.service.verify_signature(payload, f'sha1=invalid', secret))


class WebhookAPITestCase(APITestCase):
    """
    Test webhook API endpoints
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
        
        self.client = APIClient()
        
        # Get JWT token
        refresh = RefreshToken.for_user(self.user)
        self.access_token = str(refresh.access_token)
        
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
    
    def test_create_webhook_endpoint(self):
        """Test creating webhook endpoint via API"""
        url = reverse('webhooks:webhookendpoint-list')
        data = {
            'name': 'Test Webhook',
            'url': 'https://example.com/webhook',
            'events': ['student.created', 'student.updated'],
            'description': 'Test webhook endpoint'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'Test Webhook')
        self.assertIn('secret', response.data)  # Secret should be included in creation response
        
        # Check endpoint was created
        endpoint = WebhookEndpoint.objects.get(name='Test Webhook')
        self.assertEqual(endpoint.created_by, self.user)
    
    def test_list_webhook_endpoints(self):
        """Test listing webhook endpoints"""
        # Create test endpoint
        WebhookEndpoint.objects.create(
            name='Test Webhook',
            url='https://example.com/webhook',
            secret='test_secret',
            events=['student.created'],
            created_by=self.user
        )
        
        url = reverse('webhooks:webhookendpoint-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], 'Test Webhook')
        self.assertNotIn('secret', response.data['results'][0])  # Secret should be hidden in list
    
    def test_webhook_endpoint_test(self):
        """Test webhook endpoint test functionality"""
        endpoint = WebhookEndpoint.objects.create(
            name='Test Webhook',
            url='https://example.com/webhook',
            secret='test_secret',
            events=['webhook.test'],
            created_by=self.user
        )
        
        url = reverse('webhooks:webhookendpoint-test', kwargs={'pk': endpoint.id})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('deliveries_created', response.data)
        
        # Check delivery was created
        self.assertTrue(WebhookDelivery.objects.filter(
            endpoint=endpoint,
            event_type='webhook.test'
        ).exists())
    
    def test_webhook_endpoint_statistics(self):
        """Test webhook endpoint statistics"""
        endpoint = WebhookEndpoint.objects.create(
            name='Test Webhook',
            url='https://example.com/webhook',
            secret='test_secret',
            events=['student.created'],
            created_by=self.user
        )
        
        url = reverse('webhooks:webhookendpoint-statistics', kwargs={'pk': endpoint.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('endpoint_name', response.data)
        self.assertIn('total_deliveries', response.data)
        self.assertIn('success_rate', response.data)
    
    def test_trigger_webhook(self):
        """Test triggering webhook via API"""
        WebhookEndpoint.objects.create(
            name='Test Webhook',
            url='https://example.com/webhook',
            secret='test_secret',
            events=['student.created'],
            created_by=self.user
        )
        
        url = reverse('webhooks:trigger')
        data = {
            'event_type': 'student.created',
            'event_data': {'student_id': 1, 'name': 'John Doe'},
            'event_id': 'test_trigger_123'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['event_type'], 'student.created')
        self.assertGreater(response.data['deliveries_created'], 0)
        
        # Check delivery was created
        self.assertTrue(WebhookDelivery.objects.filter(
            event_id='test_trigger_123'
        ).exists())
    
    def test_webhook_status(self):
        """Test webhook system status endpoint"""
        url = reverse('webhooks:status')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('system_status', response.data)
        self.assertIn('endpoints', response.data)
        self.assertIn('deliveries_24h', response.data)
    
    def test_webhook_receive(self):
        """Test webhook receive endpoint"""
        url = reverse('webhooks:receive')
        data = {'test': 'webhook payload'}
        
        # This endpoint should allow unauthenticated access
        self.client.credentials()  # Remove authentication
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('message', response.data)
    
    def test_webhook_management(self):
        """Test webhook management operations"""
        url = reverse('webhooks:manage')
        data = {'operation': 'retry_all_failed'}
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['operation'], 'retry_all_failed')
        self.assertIn('retry_count', response.data)


class WebhookSerializerTestCase(TestCase):
    """
    Test webhook serializers
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
    
    def test_webhook_endpoint_serializer(self):
        """Test webhook endpoint serializer"""
        data = {
            'name': 'Test Webhook',
            'url': 'https://example.com/webhook',
            'events': ['student.created', 'student.updated'],
            'description': 'Test webhook'
        }
        
        serializer = WebhookEndpointSerializer(data=data)
        self.assertTrue(serializer.is_valid())
        
        # Test creation
        endpoint = serializer.save(created_by=self.user)
        self.assertEqual(endpoint.name, 'Test Webhook')
        self.assertIsNotNone(endpoint.secret)  # Secret should be auto-generated
    
    def test_webhook_endpoint_serializer_validation(self):
        """Test webhook endpoint serializer validation"""
        # Test invalid events
        data = {
            'name': 'Test Webhook',
            'url': 'https://example.com/webhook',
            'events': [],  # Empty events list
        }
        
        serializer = WebhookEndpointSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('events', serializer.errors)
        
        # Test invalid timeout
        data['events'] = ['student.created']
        data['timeout_seconds'] = 500  # Too high
        
        serializer = WebhookEndpointSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('timeout_seconds', serializer.errors)
    
    def test_webhook_delivery_serializer(self):
        """Test webhook delivery serializer"""
        endpoint = WebhookEndpoint.objects.create(
            name='Test Webhook',
            url='https://example.com/webhook',
            secret='test_secret',
            events=['student.created'],
            created_by=self.user
        )
        
        delivery = WebhookDelivery.objects.create(
            endpoint=endpoint,
            event_type='student.created',
            event_id='test_123',
            payload={'student_id': 1}
        )
        
        serializer = WebhookDeliverySerializer(delivery)
        data = serializer.data
        
        self.assertEqual(data['event_type'], 'student.created')
        self.assertEqual(data['endpoint_name'], 'Test Webhook')
        self.assertEqual(data['status'], 'pending')
        self.assertTrue(data['can_retry'])
        self.assertFalse(data['is_successful'])


if __name__ == '__main__':
    import django
    from django.conf import settings
    from django.test.utils import get_runner
    
    django.setup()
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    failures = test_runner.run_tests(['core.webhooks.tests'])