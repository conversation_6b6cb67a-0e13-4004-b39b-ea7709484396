"""
Webhook URL configuration for School ERP
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    WebhookEndpointViewSet, WebhookDeliveryViewSet, WebhookEventViewSet,
    WebhookAnalyticsViewSet, WebhookTriggerView, WebhookReceiveView,
    WebhookStatusView, WebhookManagementView
)

# Create router for viewsets
router = DefaultRouter()
router.register(r'endpoints', WebhookEndpointViewSet)
router.register(r'deliveries', WebhookDeliveryViewSet)
router.register(r'events', WebhookEventViewSet)
router.register(r'analytics', WebhookAnalyticsViewSet)

app_name = 'webhooks'

urlpatterns = [
    # ViewSet URLs
    path('', include(router.urls)),
    
    # Additional webhook endpoints
    path('trigger/', WebhookTriggerView.as_view(), name='trigger'),
    path('receive/', WebhookReceiveView.as_view(), name='receive'),
    path('status/', WebhookStatusView.as_view(), name='status'),
    path('manage/', WebhookManagementView.as_view(), name='manage'),
]