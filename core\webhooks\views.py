"""
Webhook views and API endpoints for School ERP
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.http import JsonResponse
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter

from .models import WebhookEndpoint, WebhookDelivery, WebhookEvent, WebhookAnalytics
from .serializers import (
    WebhookEndpointSerializer, WebhookDeliverySerializer, 
    WebhookEventSerializer, WebhookAnalyticsSerializer
)
from .services import webhook_service, webhook_analytics_service, webhook_security_service
from core.api.views import BaseAPIViewSet
import logging

logger = logging.getLogger(__name__)


class WebhookEndpointViewSet(BaseAPIViewSet):
    """
    ViewSet for managing webhook endpoints
    """
    
    queryset = WebhookEndpoint.objects.all()
    serializer_class = WebhookEndpointSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'is_active']
    search_fields = ['name', 'url', 'description']
    ordering_fields = ['name', 'created_at', 'last_delivery_at']
    ordering = ['-created_at']
    
    def perform_create(self, serializer):
        """
        Set created_by when creating webhook endpoint
        """
        serializer.save(created_by=self.request.user)
    
    @action(detail=True, methods=['post'])
    def test(self, request, pk=None):
        """
        Test webhook endpoint with a sample payload
        """
        endpoint = self.get_object()
        
        # Create test event data
        test_data = {
            'test': True,
            'message': 'This is a test webhook delivery',
            'timestamp': timezone.now().isoformat(),
            'user': request.user.username
        }
        
        # Trigger test webhook
        try:
            delivery_count = webhook_service.trigger_webhook(
                'webhook.test',
                test_data,
                f"test_{timezone.now().timestamp()}"
            )
            
            return Response({
                'message': 'Test webhook triggered successfully',
                'deliveries_created': delivery_count
            })
        
        except Exception as e:
            logger.error(f"Error testing webhook {endpoint.id}: {e}")
            return Response(
                {'error': 'Failed to trigger test webhook'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['get'])
    def statistics(self, request, pk=None):
        """
        Get statistics for webhook endpoint
        """
        endpoint = self.get_object()
        stats = webhook_service.get_endpoint_statistics(endpoint.id)
        
        if stats:
            return Response(stats)
        else:
            return Response(
                {'error': 'Unable to retrieve statistics'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['get'])
    def deliveries(self, request, pk=None):
        """
        Get recent deliveries for webhook endpoint
        """
        endpoint = self.get_object()
        
        # Get query parameters
        limit = int(request.GET.get('limit', 50))
        status_filter = request.GET.get('status')
        
        # Build queryset
        deliveries = WebhookDelivery.objects.filter(endpoint=endpoint)
        
        if status_filter:
            deliveries = deliveries.filter(status=status_filter)
        
        deliveries = deliveries.order_by('-created_at')[:limit]
        
        # Serialize and return
        serializer = WebhookDeliverySerializer(deliveries, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def retry_failed(self, request, pk=None):
        """
        Retry all failed deliveries for this endpoint
        """
        endpoint = self.get_object()
        
        # Get failed deliveries that can be retried
        failed_deliveries = WebhookDelivery.objects.filter(
            endpoint=endpoint,
            status__in=['failed', 'retrying']
        )
        
        retry_count = 0
        for delivery in failed_deliveries:
            if delivery.can_retry:
                webhook_service.queue_delivery(delivery.id)
                retry_count += 1
        
        return Response({
            'message': f'Queued {retry_count} deliveries for retry',
            'retry_count': retry_count
        })
    
    @action(detail=True, methods=['get'])
    def report(self, request, pk=None):
        """
        Generate comprehensive report for webhook endpoint
        """
        endpoint = self.get_object()
        days = int(request.GET.get('days', 30))
        
        report = webhook_analytics_service.generate_endpoint_report(endpoint.id, days)
        
        if report:
            return Response(report)
        else:
            return Response(
                {'error': 'Unable to generate report'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class WebhookDeliveryViewSet(BaseAPIViewSet):
    """
    ViewSet for webhook deliveries (read-only)
    """
    
    queryset = WebhookDelivery.objects.all()
    serializer_class = WebhookDeliverySerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'event_type', 'endpoint']
    search_fields = ['event_type', 'event_id']
    ordering_fields = ['created_at', 'last_attempt_at', 'delivered_at']
    ordering = ['-created_at']
    
    # Make this viewset read-only
    http_method_names = ['get', 'head', 'options']
    
    @action(detail=True, methods=['post'])
    def retry(self, request, pk=None):
        """
        Retry a specific webhook delivery
        """
        delivery = self.get_object()
        
        if not delivery.can_retry:
            return Response(
                {'error': 'Delivery cannot be retried'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Queue for retry
        webhook_service.queue_delivery(delivery.id)
        
        return Response({
            'message': 'Delivery queued for retry',
            'delivery_id': str(delivery.id)
        })


class WebhookEventViewSet(BaseAPIViewSet):
    """
    ViewSet for webhook events
    """
    
    queryset = WebhookEvent.objects.all()
    serializer_class = WebhookEventSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['is_active']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']


class WebhookAnalyticsViewSet(BaseAPIViewSet):
    """
    ViewSet for webhook analytics (read-only)
    """
    
    queryset = WebhookAnalytics.objects.all()
    serializer_class = WebhookAnalyticsSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['endpoint', 'date']
    ordering_fields = ['date', 'total_deliveries', 'success_rate']
    ordering = ['-date']
    
    # Make this viewset read-only
    http_method_names = ['get', 'head', 'options']


class WebhookTriggerView(APIView):
    """
    API view to manually trigger webhooks
    """
    
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """
        Trigger webhooks for a specific event
        """
        event_type = request.data.get('event_type')
        event_data = request.data.get('event_data', {})
        event_id = request.data.get('event_id')
        
        if not event_type:
            return Response(
                {'error': 'event_type is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Add user context to event data
            event_data['triggered_by'] = {
                'user_id': request.user.id,
                'username': request.user.username,
                'timestamp': timezone.now().isoformat()
            }
            
            # Trigger webhooks
            delivery_count = webhook_service.trigger_webhook(
                event_type,
                event_data,
                event_id
            )
            
            return Response({
                'message': 'Webhooks triggered successfully',
                'event_type': event_type,
                'deliveries_created': delivery_count
            })
        
        except Exception as e:
            logger.error(f"Error triggering webhooks: {e}")
            return Response(
                {'error': 'Failed to trigger webhooks'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class WebhookReceiveView(APIView):
    """
    Generic webhook receiver for testing and development
    """
    
    permission_classes = []  # Allow unauthenticated access for webhook testing
    
    def post(self, request):
        """
        Receive and log webhook payload
        """
        # Get client IP
        client_ip = self._get_client_ip(request)
        
        # Log webhook receipt
        logger.info(f"Webhook received from {client_ip}: {request.data}")
        
        # Store in cache for debugging (optional)
        cache_key = f"webhook_received_{timezone.now().timestamp()}"
        cache.set(cache_key, {
            'ip': client_ip,
            'headers': dict(request.headers),
            'data': request.data,
            'timestamp': timezone.now().isoformat()
        }, 3600)  # Store for 1 hour
        
        return Response({
            'message': 'Webhook received successfully',
            'timestamp': timezone.now().isoformat()
        })
    
    def _get_client_ip(self, request):
        """
        Get client IP address
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0]
        return request.META.get('REMOTE_ADDR')


class WebhookStatusView(APIView):
    """
    API view to get webhook system status
    """
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """
        Get webhook system status and statistics
        """
        # Get overall statistics
        total_endpoints = WebhookEndpoint.objects.count()
        active_endpoints = WebhookEndpoint.objects.filter(is_active=True).count()
        
        # Get recent delivery statistics
        from datetime import timedelta
        last_24h = timezone.now() - timedelta(hours=24)
        
        recent_deliveries = WebhookDelivery.objects.filter(created_at__gte=last_24h)
        total_recent = recent_deliveries.count()
        successful_recent = recent_deliveries.filter(status='success').count()
        failed_recent = recent_deliveries.filter(status='failed').count()
        pending_recent = recent_deliveries.filter(status__in=['pending', 'retrying']).count()
        
        # Calculate success rate
        success_rate = (successful_recent / total_recent * 100) if total_recent > 0 else 0
        
        # Get top event types
        from django.db.models import Count
        top_events = (recent_deliveries
                     .values('event_type')
                     .annotate(count=Count('id'))
                     .order_by('-count')[:5])
        
        status_data = {
            'system_status': 'operational',
            'timestamp': timezone.now().isoformat(),
            'endpoints': {
                'total': total_endpoints,
                'active': active_endpoints,
                'inactive': total_endpoints - active_endpoints
            },
            'deliveries_24h': {
                'total': total_recent,
                'successful': successful_recent,
                'failed': failed_recent,
                'pending': pending_recent,
                'success_rate': round(success_rate, 2)
            },
            'top_events_24h': list(top_events)
        }
        
        return Response(status_data)


class WebhookManagementView(APIView):
    """
    API view for webhook management operations
    """
    
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """
        Perform webhook management operations
        """
        operation = request.data.get('operation')
        
        if operation == 'retry_all_failed':
            # Retry all failed deliveries
            retry_count = webhook_service.retry_failed_deliveries()
            return Response({
                'message': f'Queued {retry_count} failed deliveries for retry',
                'operation': operation,
                'retry_count': retry_count
            })
        
        elif operation == 'update_analytics':
            # Update analytics for all endpoints
            from .services import update_webhook_analytics
            task = update_webhook_analytics.delay()
            
            return Response({
                'message': 'Analytics update queued',
                'operation': operation,
                'task_id': task.id
            })
        
        elif operation == 'cleanup_old_deliveries':
            # Clean up old delivery records
            days_to_keep = int(request.data.get('days_to_keep', 90))
            cutoff_date = timezone.now() - timedelta(days=days_to_keep)
            
            deleted_count = WebhookDelivery.objects.filter(
                created_at__lt=cutoff_date
            ).delete()[0]
            
            return Response({
                'message': f'Cleaned up {deleted_count} old delivery records',
                'operation': operation,
                'deleted_count': deleted_count,
                'cutoff_date': cutoff_date.isoformat()
            })
        
        else:
            return Response(
                {'error': f'Unknown operation: {operation}'},
                status=status.HTTP_400_BAD_REQUEST
            )