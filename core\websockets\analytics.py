"""
WebSocket analytics and monitoring for School ERP
"""

import json
import logging
from datetime import datetime, timedelta
from django.core.cache import cache
from django.utils import timezone
from channels.db import database_sync_to_async
from asgiref.sync import sync_to_async
from collections import defaultdict

logger = logging.getLogger(__name__)


class WebSocketAnalytics:
    """
    Analytics collector for WebSocket connections and messages
    """
    
    def __init__(self):
        self.cache_prefix = 'ws_analytics'
    
    async def record_connection(self, user, path):
        """
        Record WebSocket connection
        """
        now = timezone.now()
        
        # Record connection event
        connection_data = {
            'user_id': user.id if user else None,
            'user_type': user.user_type if user else None,
            'path': path,
            'timestamp': now.isoformat(),
            'event_type': 'connection'
        }
        
        # Store in different time buckets
        await self._record_event('connections', connection_data)
        
        # Update active connections count
        await self._update_active_connections(user, 1)
        
        # Update daily stats
        await self._update_daily_stats('connections', 1)
        
        logger.info(f"WebSocket connection recorded: {user} on {path}")
    
    async def record_disconnection(self, user, close_code):
        """
        Record WebSocket disconnection
        """
        now = timezone.now()
        
        # Record disconnection event
        disconnection_data = {
            'user_id': user.id if user else None,
            'user_type': user.user_type if user else None,
            'close_code': close_code,
            'timestamp': now.isoformat(),
            'event_type': 'disconnection'
        }
        
        # Store disconnection event
        await self._record_event('disconnections', disconnection_data)
        
        # Update active connections count
        await self._update_active_connections(user, -1)
        
        # Update daily stats
        await self._update_daily_stats('disconnections', 1)
        
        logger.info(f"WebSocket disconnection recorded: {user}, code: {close_code}")
    
    async def record_message(self, user, message_type, direction):
        """
        Record WebSocket message
        """
        now = timezone.now()
        
        # Record message event
        message_data = {
            'user_id': user.id if user else None,
            'user_type': user.user_type if user else None,
            'message_type': message_type,
            'direction': direction,  # 'sent' or 'received'
            'timestamp': now.isoformat(),
            'event_type': 'message'
        }
        
        # Store message event
        await self._record_event('messages', message_data)
        
        # Update message counters
        await self._update_message_counters(user, message_type, direction)
        
        # Update daily stats
        await self._update_daily_stats('messages', 1)
    
    async def record_error(self, user, error_type, error_message):
        """
        Record WebSocket error
        """
        now = timezone.now()
        
        # Record error event
        error_data = {
            'user_id': user.id if user else None,
            'user_type': user.user_type if user else None,
            'error_type': error_type,
            'error_message': error_message,
            'timestamp': now.isoformat(),
            'event_type': 'error'
        }
        
        # Store error event
        await self._record_event('errors', error_data)
        
        # Update daily stats
        await self._update_daily_stats('errors', 1)
        
        logger.warning(f"WebSocket error recorded: {user}, {error_type}: {error_message}")
    
    async def _record_event(self, event_type, event_data):
        """
        Record event in cache with time-based keys
        """
        now = datetime.now()
        
        # Hourly bucket
        hourly_key = f"{self.cache_prefix}_{event_type}_hourly_{now.strftime('%Y%m%d_%H')}"
        hourly_events = await sync_to_async(cache.get)(hourly_key, [])
        hourly_events.append(event_data)
        
        # Keep only last 100 events per hour
        if len(hourly_events) > 100:
            hourly_events = hourly_events[-100:]
        
        await sync_to_async(cache.set)(hourly_key, hourly_events, 3600)  # 1 hour
        
        # Daily bucket (summary only)
        daily_key = f"{self.cache_prefix}_{event_type}_daily_{now.strftime('%Y%m%d')}"
        daily_count = await sync_to_async(cache.get)(daily_key, 0)
        await sync_to_async(cache.set)(daily_key, daily_count + 1, 86400)  # 24 hours
    
    async def _update_active_connections(self, user, delta):
        """
        Update active connections count
        """
        # Global active connections
        global_key = f"{self.cache_prefix}_active_connections"
        current_count = await sync_to_async(cache.get)(global_key, 0)
        new_count = max(0, current_count + delta)
        await sync_to_async(cache.set)(global_key, new_count, None)  # No expiration
        
        # User-specific active connections
        if user:
            user_key = f"{self.cache_prefix}_active_connections_user_{user.id}"
            user_count = await sync_to_async(cache.get)(user_key, 0)
            new_user_count = max(0, user_count + delta)
            await sync_to_async(cache.set)(user_key, new_user_count, 3600)  # 1 hour
            
            # Role-specific active connections
            role_key = f"{self.cache_prefix}_active_connections_role_{user.user_type}"
            role_count = await sync_to_async(cache.get)(role_key, 0)
            new_role_count = max(0, role_count + delta)
            await sync_to_async(cache.set)(role_key, new_role_count, 3600)  # 1 hour
    
    async def _update_message_counters(self, user, message_type, direction):
        """
        Update message counters
        """
        now = datetime.now()
        
        # Global message counter
        global_key = f"{self.cache_prefix}_messages_{direction}_{now.strftime('%Y%m%d_%H')}"
        global_count = await sync_to_async(cache.get)(global_key, 0)
        await sync_to_async(cache.set)(global_key, global_count + 1, 3600)  # 1 hour
        
        # Message type counter
        type_key = f"{self.cache_prefix}_message_types_{message_type}_{now.strftime('%Y%m%d_%H')}"
        type_count = await sync_to_async(cache.get)(type_key, 0)
        await sync_to_async(cache.set)(type_key, type_count + 1, 3600)  # 1 hour
        
        # User message counter
        if user:
            user_key = f"{self.cache_prefix}_user_messages_{user.id}_{now.strftime('%Y%m%d_%H')}"
            user_count = await sync_to_async(cache.get)(user_key, 0)
            await sync_to_async(cache.set)(user_key, user_count + 1, 3600)  # 1 hour
    
    async def _update_daily_stats(self, stat_type, delta):
        """
        Update daily statistics
        """
        today = datetime.now().strftime('%Y%m%d')
        daily_key = f"{self.cache_prefix}_daily_stats_{today}"
        
        daily_stats = await sync_to_async(cache.get)(daily_key, {
            'connections': 0,
            'disconnections': 0,
            'messages': 0,
            'errors': 0
        })
        
        daily_stats[stat_type] = daily_stats.get(stat_type, 0) + delta
        await sync_to_async(cache.set)(daily_key, daily_stats, 86400)  # 24 hours
    
    async def get_active_connections(self):
        """
        Get current active connections count
        """
        global_key = f"{self.cache_prefix}_active_connections"
        return await sync_to_async(cache.get)(global_key, 0)
    
    async def get_active_connections_by_role(self):
        """
        Get active connections grouped by user role
        """
        roles = ['admin', 'teacher', 'student', 'parent', 'staff']
        connections_by_role = {}
        
        for role in roles:
            role_key = f"{self.cache_prefix}_active_connections_role_{role}"
            connections_by_role[role] = await sync_to_async(cache.get)(role_key, 0)
        
        return connections_by_role
    
    async def get_hourly_stats(self, hours_back=24):
        """
        Get hourly statistics for the last N hours
        """
        now = datetime.now()
        stats = []
        
        for i in range(hours_back):
            hour = now - timedelta(hours=i)
            hour_str = hour.strftime('%Y%m%d_%H')
            
            # Get stats for this hour
            connections_key = f"{self.cache_prefix}_connections_daily_{hour.strftime('%Y%m%d')}"
            messages_sent_key = f"{self.cache_prefix}_messages_sent_{hour_str}"
            messages_received_key = f"{self.cache_prefix}_messages_received_{hour_str}"
            errors_key = f"{self.cache_prefix}_errors_daily_{hour.strftime('%Y%m%d')}"
            
            hour_stats = {
                'hour': hour_str,
                'connections': cache.get(connections_key, 0) if i == 0 else 0,  # Only current day
                'messages_sent': cache.get(messages_sent_key, 0),
                'messages_received': cache.get(messages_received_key, 0),
                'errors': cache.get(errors_key, 0) if i == 0 else 0  # Only current day
            }
            
            stats.append(hour_stats)
        
        return list(reversed(stats))  # Most recent first
    
    async def get_daily_stats(self, days_back=7):
        """
        Get daily statistics for the last N days
        """
        now = datetime.now()
        stats = []
        
        for i in range(days_back):
            day = now - timedelta(days=i)
            day_str = day.strftime('%Y%m%d')
            
            daily_key = f"{self.cache_prefix}_daily_stats_{day_str}"
            day_stats = cache.get(daily_key, {
                'connections': 0,
                'disconnections': 0,
                'messages': 0,
                'errors': 0
            })
            
            day_stats['date'] = day_str
            stats.append(day_stats)
        
        return list(reversed(stats))  # Most recent first
    
    async def get_message_type_stats(self, hours_back=24):
        """
        Get message type statistics
        """
        now = datetime.now()
        message_types = defaultdict(int)
        
        for i in range(hours_back):
            hour = now - timedelta(hours=i)
            hour_str = hour.strftime('%Y%m%d_%H')
            
            # Get all message type keys for this hour
            pattern = f"{self.cache_prefix}_message_types_*_{hour_str}"
            
            # This is a simplified approach - in production you might want to
            # store message types in a separate cache key
            common_types = ['chat_message', 'notification', 'typing_indicator', 
                          'subscribe', 'unsubscribe', 'mark_read']
            
            for msg_type in common_types:
                type_key = f"{self.cache_prefix}_message_types_{msg_type}_{hour_str}"
                count = await sync_to_async(cache.get)(type_key, 0)
                message_types[msg_type] += count
        
        return dict(message_types)
    
    async def get_user_activity(self, user_id, hours_back=24):
        """
        Get activity statistics for a specific user
        """
        now = datetime.now()
        activity = {
            'connections': 0,
            'messages_sent': 0,
            'messages_received': 0,
            'active_time_minutes': 0
        }
        
        for i in range(hours_back):
            hour = now - timedelta(hours=i)
            hour_str = hour.strftime('%Y%m%d_%H')
            
            # User messages
            user_messages_key = f"{self.cache_prefix}_user_messages_{user_id}_{hour_str}"
            activity['messages_sent'] += await sync_to_async(cache.get)(user_messages_key, 0)
        
        # Current active connections
        user_connections_key = f"{self.cache_prefix}_active_connections_user_{user_id}"
        activity['current_connections'] = await sync_to_async(cache.get)(user_connections_key, 0)
        
        return activity
    
    async def get_performance_metrics(self):
        """
        Get WebSocket performance metrics
        """
        return {
            'active_connections': await self.get_active_connections(),
            'connections_by_role': await self.get_active_connections_by_role(),
            'hourly_stats': await self.get_hourly_stats(24),
            'daily_stats': await self.get_daily_stats(7),
            'message_types': await self.get_message_type_stats(24)
        }
    
    async def cleanup_old_data(self, days_to_keep=30):
        """
        Clean up old analytics data
        """
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        # This would need to be implemented based on your cache backend
        # For now, just log the cleanup action
        logger.info(f"WebSocket analytics cleanup: removing data older than {cutoff_date}")


class WebSocketMonitor:
    """
    Real-time monitoring for WebSocket connections
    """
    
    def __init__(self):
        self.analytics = WebSocketAnalytics()
        self.alert_thresholds = {
            'max_connections': 1000,
            'max_errors_per_hour': 100,
            'max_messages_per_minute': 1000
        }
    
    async def check_connection_health(self):
        """
        Check overall WebSocket connection health
        """
        active_connections = await self.analytics.get_active_connections()
        
        health_status = {
            'status': 'healthy',
            'active_connections': active_connections,
            'alerts': []
        }
        
        # Check connection threshold
        if active_connections > self.alert_thresholds['max_connections']:
            health_status['status'] = 'warning'
            health_status['alerts'].append({
                'type': 'high_connections',
                'message': f'High number of active connections: {active_connections}',
                'threshold': self.alert_thresholds['max_connections']
            })
        
        # Check error rate
        hourly_stats = await self.analytics.get_hourly_stats(1)
        if hourly_stats:
            errors_last_hour = hourly_stats[0]['errors']
            if errors_last_hour > self.alert_thresholds['max_errors_per_hour']:
                health_status['status'] = 'critical'
                health_status['alerts'].append({
                    'type': 'high_error_rate',
                    'message': f'High error rate: {errors_last_hour} errors in last hour',
                    'threshold': self.alert_thresholds['max_errors_per_hour']
                })
        
        return health_status
    
    async def get_connection_distribution(self):
        """
        Get distribution of connections across different dimensions
        """
        return {
            'by_role': await self.analytics.get_active_connections_by_role(),
            'total': await self.analytics.get_active_connections()
        }
    
    async def generate_report(self, period='daily'):
        """
        Generate WebSocket usage report
        """
        if period == 'daily':
            stats = await self.analytics.get_daily_stats(1)
        else:
            stats = await self.analytics.get_hourly_stats(24)
        
        return {
            'period': period,
            'stats': stats,
            'performance_metrics': await self.analytics.get_performance_metrics(),
            'health_status': await self.check_connection_health()
        }


# Global analytics instances
websocket_analytics = WebSocketAnalytics()
websocket_monitor = WebSocketMonitor()