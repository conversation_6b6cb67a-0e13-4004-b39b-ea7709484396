"""
WebSocket authentication for School ERP
"""

import jwt
import logging
from urllib.parse import parse_qs
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AnonymousUser
from django.conf import settings
from django.core.cache import cache
from channels.db import database_sync_to_async
from rest_framework_simplejwt.tokens import UntypedToken
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError

logger = logging.getLogger(__name__)
User = get_user_model()


class WebSocketJWTAuthentication:
    """
    JWT authentication for WebSocket connections
    """
    
    async def authenticate(self, scope):
        """
        Authenticate WebSocket connection using JWT token
        """
        # Try to get token from query parameters
        token = self.get_token_from_query(scope)
        
        if not token:
            # Try to get token from headers
            token = self.get_token_from_headers(scope)
        
        if not token:
            logger.warning("WebSocket authentication failed: No token provided")
            return None
        
        try:
            # Validate token
            validated_token = UntypedToken(token)
            user_id = validated_token['user_id']
            
            # Get user from database
            user = await self.get_user(user_id)
            
            if user and user.is_active:
                # Cache successful authentication
                cache_key = f"ws_auth_{user_id}_{token[:10]}"
                cache.set(cache_key, True, 300)  # 5 minutes
                
                logger.info(f"WebSocket authentication successful for user: {user}")
                return user
            else:
                logger.warning(f"WebSocket authentication failed: User {user_id} not found or inactive")
                return None
        
        except (InvalidToken, TokenError, KeyError) as e:
            logger.warning(f"WebSocket authentication failed: Invalid token - {e}")
            return None
        except Exception as e:
            logger.error(f"WebSocket authentication error: {e}")
            return None
    
    def get_token_from_query(self, scope):
        """
        Extract JWT token from query parameters
        """
        query_string = scope.get('query_string', b'').decode()
        query_params = parse_qs(query_string)
        
        # Check for 'token' parameter
        if 'token' in query_params:
            return query_params['token'][0]
        
        # Check for 'access_token' parameter
        if 'access_token' in query_params:
            return query_params['access_token'][0]
        
        return None
    
    def get_token_from_headers(self, scope):
        """
        Extract JWT token from headers
        """
        headers = dict(scope.get('headers', []))
        
        # Check Authorization header
        auth_header = headers.get(b'authorization')
        if auth_header:
            auth_header = auth_header.decode()
            if auth_header.startswith('Bearer '):
                return auth_header[7:]  # Remove 'Bearer ' prefix
        
        # Check custom WebSocket token header
        ws_token_header = headers.get(b'sec-websocket-protocol')
        if ws_token_header:
            protocols = ws_token_header.decode().split(', ')
            for protocol in protocols:
                if protocol.startswith('access_token.'):
                    return protocol[13:]  # Remove 'access_token.' prefix
        
        return None
    
    @database_sync_to_async
    def get_user(self, user_id):
        """
        Get user from database
        """
        try:
            return User.objects.get(id=user_id)
        except User.DoesNotExist:
            return None


class WebSocketSessionAuthentication:
    """
    Session-based authentication for WebSocket connections
    """
    
    async def authenticate(self, scope):
        """
        Authenticate WebSocket connection using Django session
        """
        # Get session from scope (set by Django's session middleware)
        session = scope.get('session')
        
        if not session:
            return None
        
        # Get user ID from session
        user_id = session.get('_auth_user_id')
        
        if not user_id:
            return None
        
        try:
            user = await self.get_user(user_id)
            
            if user and user.is_active:
                return user
            else:
                return None
        
        except Exception as e:
            logger.error(f"WebSocket session authentication error: {e}")
            return None
    
    @database_sync_to_async
    def get_user(self, user_id):
        """
        Get user from database
        """
        try:
            return User.objects.get(id=user_id)
        except User.DoesNotExist:
            return None


class WebSocketAPIKeyAuthentication:
    """
    API key authentication for WebSocket connections (for service-to-service)
    """
    
    async def authenticate(self, scope):
        """
        Authenticate WebSocket connection using API key
        """
        api_key = self.get_api_key(scope)
        
        if not api_key:
            return None
        
        # Validate API key
        if await self.validate_api_key(api_key):
            # Return a service user or None for API key auth
            return await self.get_service_user()
        
        return None
    
    def get_api_key(self, scope):
        """
        Extract API key from headers or query parameters
        """
        # Try headers first
        headers = dict(scope.get('headers', []))
        api_key_header = headers.get(b'x-api-key')
        if api_key_header:
            return api_key_header.decode()
        
        # Try query parameters
        query_string = scope.get('query_string', b'').decode()
        query_params = parse_qs(query_string)
        
        if 'api_key' in query_params:
            return query_params['api_key'][0]
        
        return None
    
    @database_sync_to_async
    def validate_api_key(self, api_key):
        """
        Validate API key
        """
        # Check against configured API keys
        valid_api_keys = getattr(settings, 'WEBSOCKET_API_KEYS', [])
        return api_key in valid_api_keys
    
    @database_sync_to_async
    def get_service_user(self):
        """
        Get or create service user for API key authentication
        """
        try:
            user, created = User.objects.get_or_create(
                username='websocket_service',
                defaults={
                    'email': '<EMAIL>',
                    'first_name': 'WebSocket',
                    'last_name': 'Service',
                    'user_type': 'admin',
                    'is_active': True
                }
            )
            return user
        except Exception as e:
            logger.error(f"Error getting service user: {e}")
            return None


class WebSocketMultiAuthentication:
    """
    Multi-method authentication for WebSocket connections
    """
    
    def __init__(self):
        self.authenticators = [
            WebSocketJWTAuthentication(),
            WebSocketSessionAuthentication(),
            WebSocketAPIKeyAuthentication()
        ]
    
    async def authenticate(self, scope):
        """
        Try multiple authentication methods
        """
        for authenticator in self.authenticators:
            try:
                user = await authenticator.authenticate(scope)
                if user:
                    logger.info(f"WebSocket authenticated via {authenticator.__class__.__name__}")
                    return user
            except Exception as e:
                logger.error(f"Authentication error with {authenticator.__class__.__name__}: {e}")
                continue
        
        logger.warning("WebSocket authentication failed with all methods")
        return None


class WebSocketAuthMiddleware:
    """
    Middleware to add authenticated user to WebSocket scope
    """
    
    def __init__(self, inner):
        self.inner = inner
        self.auth = WebSocketMultiAuthentication()
    
    async def __call__(self, scope, receive, send):
        """
        Add user to scope
        """
        if scope['type'] == 'websocket':
            # Authenticate user
            user = await self.auth.authenticate(scope)
            scope['user'] = user or AnonymousUser()
        
        return await self.inner(scope, receive, send)


def WebSocketAuthMiddlewareStack(inner):
    """
    WebSocket middleware stack with authentication
    """
    return WebSocketAuthMiddleware(inner)


class WebSocketRateLimiter:
    """
    Rate limiting for WebSocket connections
    """
    
    def __init__(self, max_connections_per_user=5, max_messages_per_minute=60):
        self.max_connections_per_user = max_connections_per_user
        self.max_messages_per_minute = max_messages_per_minute
    
    async def check_connection_limit(self, user):
        """
        Check if user has exceeded connection limit
        """
        if not user or user.is_anonymous:
            return True  # Allow anonymous connections (they'll be rejected later)
        
        cache_key = f"ws_connections_{user.id}"
        current_connections = cache.get(cache_key, 0)
        
        if current_connections >= self.max_connections_per_user:
            logger.warning(f"WebSocket connection limit exceeded for user {user}")
            return False
        
        # Increment connection count
        cache.set(cache_key, current_connections + 1, 300)  # 5 minutes
        return True
    
    async def check_message_limit(self, user):
        """
        Check if user has exceeded message rate limit
        """
        if not user or user.is_anonymous:
            return False
        
        cache_key = f"ws_messages_{user.id}"
        current_messages = cache.get(cache_key, 0)
        
        if current_messages >= self.max_messages_per_minute:
            logger.warning(f"WebSocket message rate limit exceeded for user {user}")
            return False
        
        # Increment message count
        cache.set(cache_key, current_messages + 1, 60)  # 1 minute
        return True
    
    async def decrement_connection_count(self, user):
        """
        Decrement connection count when user disconnects
        """
        if not user or user.is_anonymous:
            return
        
        cache_key = f"ws_connections_{user.id}"
        current_connections = cache.get(cache_key, 0)
        
        if current_connections > 0:
            cache.set(cache_key, current_connections - 1, 300)


# Global rate limiter instance
websocket_rate_limiter = WebSocketRateLimiter()