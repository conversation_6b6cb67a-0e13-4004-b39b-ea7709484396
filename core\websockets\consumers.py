"""
WebSocket consumers for School ERP real-time communication
"""

import json
import logging
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.cache import cache
from .authentication import WebSocketJWTAuthentication
from .permissions import WebSocketPermissionMixin
from .analytics import WebSocketAnalytics

logger = logging.getLogger(__name__)
User = get_user_model()


class BaseWebSocketConsumer(WebSocketPermissionMixin, AsyncWebsocketConsumer):
    """
    Base WebSocket consumer with authentication and common functionality
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user = None
        self.user_id = None
        self.groups = []
        self.analytics = WebSocketAnalytics()
    
    async def connect(self):
        """
        Handle WebSocket connection
        """
        # Authenticate user
        self.user = await self.authenticate_user()
        
        if not self.user:
            logger.warning(f"WebSocket connection rejected: Authentication failed")
            await self.close(code=4001)  # Unauthorized
            return
        
        self.user_id = self.user.id
        
        # Check permissions
        if not await self.has_permission():
            logger.warning(f"WebSocket connection rejected: Permission denied for user {self.user}")
            await self.close(code=4003)  # Forbidden
            return
        
        # Accept connection
        await self.accept()
        
        # Join user-specific group
        user_group = f"user_{self.user_id}"
        await self.channel_layer.group_add(user_group, self.channel_name)
        self.groups.append(user_group)
        
        # Join role-specific group
        role_group = f"role_{self.user.user_type}"
        await self.channel_layer.group_add(role_group, self.channel_name)
        self.groups.append(role_group)
        
        # Record connection analytics
        await self.analytics.record_connection(self.user, self.scope['path'])
        
        # Send welcome message
        await self.send(text_data=json.dumps({
            'type': 'connection_established',
            'message': 'WebSocket connection established',
            'user_id': self.user_id,
            'timestamp': timezone.now().isoformat()
        }))
        
        logger.info(f"WebSocket connected: User {self.user} on {self.scope['path']}")
    
    async def disconnect(self, close_code):
        """
        Handle WebSocket disconnection
        """
        # Leave all groups
        for group in self.groups:
            await self.channel_layer.group_discard(group, self.channel_name)
        
        # Record disconnection analytics
        if self.user:
            await self.analytics.record_disconnection(self.user, close_code)
            logger.info(f"WebSocket disconnected: User {self.user}, code: {close_code}")
    
    async def receive(self, text_data):
        """
        Handle incoming WebSocket messages
        """
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            # Record message analytics
            await self.analytics.record_message(self.user, message_type, 'received')
            
            # Route message based on type
            handler_name = f"handle_{message_type}"
            if hasattr(self, handler_name):
                handler = getattr(self, handler_name)
                await handler(data)
            else:
                await self.send_error(f"Unknown message type: {message_type}")
        
        except json.JSONDecodeError:
            await self.send_error("Invalid JSON format")
        except Exception as e:
            logger.error(f"Error handling WebSocket message: {e}")
            await self.send_error("Internal server error")
    
    async def authenticate_user(self):
        """
        Authenticate user from WebSocket connection
        """
        auth = WebSocketJWTAuthentication()
        return await auth.authenticate(self.scope)
    
    async def send_error(self, message, error_code=None):
        """
        Send error message to client
        """
        await self.send(text_data=json.dumps({
            'type': 'error',
            'message': message,
            'error_code': error_code,
            'timestamp': timezone.now().isoformat()
        }))
    
    async def send_notification(self, event):
        """
        Send notification to client
        """
        await self.send(text_data=json.dumps({
            'type': 'notification',
            'data': event['data'],
            'timestamp': timezone.now().isoformat()
        }))
    
    async def send_update(self, event):
        """
        Send data update to client
        """
        await self.send(text_data=json.dumps({
            'type': 'update',
            'data': event['data'],
            'timestamp': timezone.now().isoformat()
        }))


class NotificationConsumer(BaseWebSocketConsumer):
    """
    WebSocket consumer for real-time notifications
    """
    
    async def connect(self):
        """
        Connect to notification stream
        """
        await super().connect()
        
        if self.user:
            # Join notification groups
            notification_group = f"notifications_{self.user_id}"
            await self.channel_layer.group_add(notification_group, self.channel_name)
            self.groups.append(notification_group)
            
            # Join school-wide notifications if applicable
            if hasattr(self.user, 'school') and self.user.school:
                school_group = f"school_{self.user.school.id}_notifications"
                await self.channel_layer.group_add(school_group, self.channel_name)
                self.groups.append(school_group)
    
    async def handle_mark_read(self, data):
        """
        Handle marking notification as read
        """
        notification_id = data.get('notification_id')
        if notification_id:
            await self.mark_notification_read(notification_id)
            await self.send(text_data=json.dumps({
                'type': 'notification_marked_read',
                'notification_id': notification_id,
                'timestamp': timezone.now().isoformat()
            }))
    
    async def handle_get_unread_count(self, data):
        """
        Handle request for unread notification count
        """
        count = await self.get_unread_notification_count()
        await self.send(text_data=json.dumps({
            'type': 'unread_count',
            'count': count,
            'timestamp': timezone.now().isoformat()
        }))
    
    @database_sync_to_async
    def mark_notification_read(self, notification_id):
        """
        Mark notification as read in database
        """
        # This would interact with your notification model
        # For now, just cache the read status
        cache_key = f"notification_read_{self.user_id}_{notification_id}"
        cache.set(cache_key, True, 86400)  # 24 hours
    
    @database_sync_to_async
    def get_unread_notification_count(self):
        """
        Get unread notification count for user
        """
        # This would query your notification model
        # For now, return a placeholder count
        return 5


class ChatConsumer(BaseWebSocketConsumer):
    """
    WebSocket consumer for real-time chat
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.room_name = None
        self.room_group_name = None
    
    async def connect(self):
        """
        Connect to chat room
        """
        # Handle URL route parameters safely for testing
        url_route = self.scope.get('url_route', {})
        kwargs = url_route.get('kwargs', {})
        self.room_name = kwargs.get('room_name', 'default')
        self.room_group_name = f'chat_{self.room_name}'
        
        await super().connect()
        
        if self.user:
            # Check if user can access this chat room
            if await self.can_access_room(self.room_name):
                await self.channel_layer.group_add(
                    self.room_group_name,
                    self.channel_name
                )
                self.groups.append(self.room_group_name)
                
                # Notify room of user joining
                await self.channel_layer.group_send(
                    self.room_group_name,
                    {
                        'type': 'user_joined',
                        'user': self.user.username,
                        'user_id': self.user_id
                    }
                )
            else:
                await self.close(code=4003)  # Forbidden
    
    async def handle_chat_message(self, data):
        """
        Handle chat message
        """
        message = data.get('message', '').strip()
        if not message:
            await self.send_error("Message cannot be empty")
            return
        
        # Save message to database
        message_id = await self.save_chat_message(message)
        
        # Broadcast message to room
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'chat_message',
                'message': message,
                'user': self.user.username,
                'user_id': self.user_id,
                'message_id': message_id,
                'timestamp': timezone.now().isoformat()
            }
        )
    
    async def handle_typing_indicator(self, data):
        """
        Handle typing indicator
        """
        is_typing = data.get('is_typing', False)
        
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'typing_indicator',
                'user': self.user.username,
                'user_id': self.user_id,
                'is_typing': is_typing
            }
        )
    
    async def chat_message(self, event):
        """
        Send chat message to WebSocket
        """
        await self.send(text_data=json.dumps({
            'type': 'chat_message',
            'message': event['message'],
            'user': event['user'],
            'user_id': event['user_id'],
            'message_id': event['message_id'],
            'timestamp': event['timestamp']
        }))
    
    async def typing_indicator(self, event):
        """
        Send typing indicator to WebSocket
        """
        # Don't send typing indicator back to sender
        if event['user_id'] != self.user_id:
            await self.send(text_data=json.dumps({
                'type': 'typing_indicator',
                'user': event['user'],
                'user_id': event['user_id'],
                'is_typing': event['is_typing']
            }))
    
    async def user_joined(self, event):
        """
        Send user joined notification
        """
        if event['user_id'] != self.user_id:
            await self.send(text_data=json.dumps({
                'type': 'user_joined',
                'user': event['user'],
                'user_id': event['user_id']
            }))
    
    @database_sync_to_async
    def can_access_room(self, room_name):
        """
        Check if user can access chat room
        """
        # Implement room access logic based on your requirements
        # For now, allow access to all authenticated users
        return True
    
    @database_sync_to_async
    def save_chat_message(self, message):
        """
        Save chat message to database
        """
        # This would save to your chat message model
        # For now, return a placeholder ID
        return f"msg_{timezone.now().timestamp()}"


class LiveUpdatesConsumer(BaseWebSocketConsumer):
    """
    WebSocket consumer for live data updates
    """
    
    async def connect(self):
        """
        Connect to live updates stream
        """
        await super().connect()
        
        if self.user:
            # Join live updates group
            updates_group = f"live_updates_{self.user_id}"
            await self.channel_layer.group_add(updates_group, self.channel_name)
            self.groups.append(updates_group)
            
            # Join module-specific groups based on user permissions
            await self.join_module_groups()
    
    async def join_module_groups(self):
        """
        Join module-specific update groups based on user permissions
        """
        user_type = self.user.user_type
        
        # Students get student-related updates
        if user_type == 'student':
            student_group = f"student_updates_{self.user_id}"
            await self.channel_layer.group_add(student_group, self.channel_name)
            self.groups.append(student_group)
        
        # Teachers get class and academic updates
        elif user_type == 'teacher':
            teacher_group = f"teacher_updates_{self.user_id}"
            await self.channel_layer.group_add(teacher_group, self.channel_name)
            self.groups.append(teacher_group)
        
        # Admins get all updates
        elif user_type == 'admin':
            admin_group = "admin_updates"
            await self.channel_layer.group_add(admin_group, self.channel_name)
            self.groups.append(admin_group)
    
    async def handle_subscribe(self, data):
        """
        Handle subscription to specific update types
        """
        update_types = data.get('update_types', [])
        
        for update_type in update_types:
            if await self.can_subscribe_to_update_type(update_type):
                group_name = f"updates_{update_type}"
                await self.channel_layer.group_add(group_name, self.channel_name)
                self.groups.append(group_name)
        
        await self.send(text_data=json.dumps({
            'type': 'subscription_confirmed',
            'update_types': update_types,
            'timestamp': timezone.now().isoformat()
        }))
    
    async def handle_unsubscribe(self, data):
        """
        Handle unsubscription from specific update types
        """
        update_types = data.get('update_types', [])
        
        for update_type in update_types:
            group_name = f"updates_{update_type}"
            await self.channel_layer.group_discard(group_name, self.channel_name)
            if group_name in self.groups:
                self.groups.remove(group_name)
        
        await self.send(text_data=json.dumps({
            'type': 'unsubscription_confirmed',
            'update_types': update_types,
            'timestamp': timezone.now().isoformat()
        }))
    
    async def data_update(self, event):
        """
        Send data update to client
        """
        await self.send(text_data=json.dumps({
            'type': 'data_update',
            'update_type': event['update_type'],
            'data': event['data'],
            'timestamp': timezone.now().isoformat()
        }))
    
    @database_sync_to_async
    def can_subscribe_to_update_type(self, update_type):
        """
        Check if user can subscribe to specific update type
        """
        # Define permissions for different update types
        permissions = {
            'student_grades': ['student', 'teacher', 'admin'],
            'attendance': ['student', 'teacher', 'admin'],
            'announcements': ['student', 'teacher', 'admin', 'parent'],
            'financial': ['admin', 'accountant'],
            'system': ['admin']
        }
        
        allowed_roles = permissions.get(update_type, [])
        return self.user.user_type in allowed_roles


class SystemMonitoringConsumer(BaseWebSocketConsumer):
    """
    WebSocket consumer for system monitoring (admin only)
    """
    
    async def has_permission(self):
        """
        Only allow admin users
        """
        return self.user and self.user.user_type == 'admin'
    
    async def connect(self):
        """
        Connect to system monitoring stream
        """
        await super().connect()
        
        if self.user:
            # Join system monitoring group
            monitoring_group = "system_monitoring"
            await self.channel_layer.group_add(monitoring_group, self.channel_name)
            self.groups.append(monitoring_group)
    
    async def handle_get_system_status(self, data):
        """
        Handle request for system status
        """
        status = await self.get_system_status()
        await self.send(text_data=json.dumps({
            'type': 'system_status',
            'data': status,
            'timestamp': timezone.now().isoformat()
        }))
    
    async def system_alert(self, event):
        """
        Send system alert to client
        """
        await self.send(text_data=json.dumps({
            'type': 'system_alert',
            'alert': event['alert'],
            'severity': event['severity'],
            'timestamp': timezone.now().isoformat()
        }))
    
    @database_sync_to_async
    def get_system_status(self):
        """
        Get current system status
        """
        # This would collect actual system metrics
        return {
            'cpu_usage': 45.2,
            'memory_usage': 67.8,
            'active_users': 150,
            'api_requests_per_minute': 45,
            'database_connections': 12
        }