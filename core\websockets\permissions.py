"""
WebSocket permissions for School ERP
"""

import logging
from django.core.cache import cache
from channels.db import database_sync_to_async

logger = logging.getLogger(__name__)


class WebSocketPermissionMixin:
    """
    Mixin to add permission checking to WebSocket consumers
    """
    
    async def has_permission(self):
        """
        Check if user has permission to connect to this WebSocket
        Override in subclasses for specific permission logic
        """
        return self.user and self.user.is_authenticated
    
    async def has_object_permission(self, obj):
        """
        Check if user has permission to access specific object
        Override in subclasses for object-level permissions
        """
        return await self.has_permission()


class WebSocketBasePermission:
    """
    Base permission class for WebSocket connections
    """
    
    async def has_permission(self, user, consumer):
        """
        Check if user has permission
        """
        return user and user.is_authenticated
    
    async def has_object_permission(self, user, consumer, obj):
        """
        Check if user has object-level permission
        """
        return await self.has_permission(user, consumer)


class IsAuthenticated(WebSocketBasePermission):
    """
    Permission that requires user to be authenticated
    """
    
    async def has_permission(self, user, consumer):
        return user and user.is_authenticated


class IsAdminUser(WebSocketBasePermission):
    """
    Permission that requires user to be admin
    """
    
    async def has_permission(self, user, consumer):
        return user and user.is_authenticated and user.user_type == 'admin'


class IsTeacherOrAdmin(WebSocketBasePermission):
    """
    Permission that requires user to be teacher or admin
    """
    
    async def has_permission(self, user, consumer):
        return (user and user.is_authenticated and 
                user.user_type in ['teacher', 'admin'])


class IsStudentOwnerOrTeacherOrAdmin(WebSocketBasePermission):
    """
    Permission for student-related data access
    """
    
    async def has_permission(self, user, consumer):
        return user and user.is_authenticated
    
    async def has_object_permission(self, user, consumer, obj):
        if not user or not user.is_authenticated:
            return False
        
        # Admin and teachers can access all student data
        if user.user_type in ['admin', 'teacher']:
            return True
        
        # Students can only access their own data
        if user.user_type == 'student':
            return hasattr(obj, 'student') and obj.student.user == user
        
        # Parents can access their children's data
        if user.user_type == 'parent':
            return await self.is_parent_of_student(user, obj)
        
        return False
    
    @database_sync_to_async
    def is_parent_of_student(self, user, obj):
        """
        Check if user is parent of the student
        """
        # This would check parent-student relationship
        # Implementation depends on your parent-student model
        return False


class IsOwnerOrReadOnly(WebSocketBasePermission):
    """
    Permission that allows owners to edit, others to read only
    """
    
    async def has_permission(self, user, consumer):
        return user and user.is_authenticated
    
    async def has_object_permission(self, user, consumer, obj):
        if not user or not user.is_authenticated:
            return False
        
        # Check if user is owner
        if hasattr(obj, 'user') and obj.user == user:
            return True
        
        if hasattr(obj, 'created_by') and obj.created_by == user:
            return True
        
        # For read-only access, check if user has general permission
        return user.user_type in ['admin', 'teacher']


class SchoolMemberPermission(WebSocketBasePermission):
    """
    Permission that requires user to be member of the same school
    """
    
    async def has_permission(self, user, consumer):
        return user and user.is_authenticated and hasattr(user, 'school')
    
    async def has_object_permission(self, user, consumer, obj):
        if not await self.has_permission(user, consumer):
            return False
        
        # Check if object belongs to same school
        if hasattr(obj, 'school'):
            return obj.school == user.school
        
        return True


class RoleBasedPermission(WebSocketBasePermission):
    """
    Permission based on user roles
    """
    
    def __init__(self, allowed_roles):
        self.allowed_roles = allowed_roles
    
    async def has_permission(self, user, consumer):
        return (user and user.is_authenticated and 
                user.user_type in self.allowed_roles)


class TimeBasedPermission(WebSocketBasePermission):
    """
    Permission that checks time-based access
    """
    
    def __init__(self, allowed_hours=None):
        # allowed_hours: list of hours (0-23) when access is allowed
        self.allowed_hours = allowed_hours or list(range(24))
    
    async def has_permission(self, user, consumer):
        if not user or not user.is_authenticated:
            return False
        
        from django.utils import timezone
        current_hour = timezone.now().hour
        
        return current_hour in self.allowed_hours


class RateLimitedPermission(WebSocketBasePermission):
    """
    Permission with rate limiting
    """
    
    def __init__(self, max_connections=5, time_window=300):
        self.max_connections = max_connections
        self.time_window = time_window  # seconds
    
    async def has_permission(self, user, consumer):
        if not user or not user.is_authenticated:
            return False
        
        # Check rate limit
        cache_key = f"ws_rate_limit_{user.id}"
        current_count = cache.get(cache_key, 0)
        
        if current_count >= self.max_connections:
            logger.warning(f"Rate limit exceeded for user {user}")
            return False
        
        # Increment counter
        cache.set(cache_key, current_count + 1, self.time_window)
        return True


class FeaturePermission(WebSocketBasePermission):
    """
    Permission based on feature flags
    """
    
    def __init__(self, feature_name):
        self.feature_name = feature_name
    
    async def has_permission(self, user, consumer):
        if not user or not user.is_authenticated:
            return False
        
        # Check if feature is enabled for user
        return await self.is_feature_enabled(user, self.feature_name)
    
    @database_sync_to_async
    def is_feature_enabled(self, user, feature_name):
        """
        Check if feature is enabled for user
        """
        # This would check your feature flag system
        # For now, return True for all features
        return True


class CompositePermission(WebSocketBasePermission):
    """
    Permission that combines multiple permissions with AND logic
    """
    
    def __init__(self, permissions):
        self.permissions = permissions
    
    async def has_permission(self, user, consumer):
        for permission in self.permissions:
            if not await permission.has_permission(user, consumer):
                return False
        return True
    
    async def has_object_permission(self, user, consumer, obj):
        for permission in self.permissions:
            if not await permission.has_object_permission(user, consumer, obj):
                return False
        return True


class OrPermission(WebSocketBasePermission):
    """
    Permission that combines multiple permissions with OR logic
    """
    
    def __init__(self, permissions):
        self.permissions = permissions
    
    async def has_permission(self, user, consumer):
        for permission in self.permissions:
            if await permission.has_permission(user, consumer):
                return True
        return False
    
    async def has_object_permission(self, user, consumer, obj):
        for permission in self.permissions:
            if await permission.has_object_permission(user, consumer, obj):
                return True
        return False


class WebSocketPermissionChecker:
    """
    Utility class to check permissions for WebSocket consumers
    """
    
    def __init__(self, permissions=None):
        self.permissions = permissions or [IsAuthenticated()]
    
    async def check_permissions(self, user, consumer):
        """
        Check all permissions
        """
        for permission in self.permissions:
            if not await permission.has_permission(user, consumer):
                logger.warning(f"Permission denied for user {user} by {permission.__class__.__name__}")
                return False
        return True
    
    async def check_object_permissions(self, user, consumer, obj):
        """
        Check object-level permissions
        """
        for permission in self.permissions:
            if not await permission.has_object_permission(user, consumer, obj):
                logger.warning(f"Object permission denied for user {user} by {permission.__class__.__name__}")
                return False
        return True


# Predefined permission sets for common use cases
NOTIFICATION_PERMISSIONS = [IsAuthenticated()]
CHAT_PERMISSIONS = [IsAuthenticated(), SchoolMemberPermission()]
ADMIN_PERMISSIONS = [IsAdminUser()]
TEACHER_PERMISSIONS = [IsTeacherOrAdmin()]
STUDENT_PERMISSIONS = [IsStudentOwnerOrTeacherOrAdmin()]

# Permission decorators for WebSocket consumers
def require_permissions(*permissions):
    """
    Decorator to require specific permissions for WebSocket consumer methods
    """
    def decorator(func):
        async def wrapper(self, *args, **kwargs):
            checker = WebSocketPermissionChecker(list(permissions))
            
            if not await checker.check_permissions(self.user, self):
                await self.send_error("Permission denied", error_code="PERMISSION_DENIED")
                return
            
            return await func(self, *args, **kwargs)
        
        return wrapper
    return decorator


def require_object_permissions(*permissions):
    """
    Decorator to require object-level permissions
    """
    def decorator(func):
        async def wrapper(self, data, *args, **kwargs):
            # Extract object from data (implementation depends on your needs)
            obj = data.get('object')  # This would need to be customized
            
            if obj:
                checker = WebSocketPermissionChecker(list(permissions))
                
                if not await checker.check_object_permissions(self.user, self, obj):
                    await self.send_error("Object permission denied", error_code="OBJECT_PERMISSION_DENIED")
                    return
            
            return await func(self, data, *args, **kwargs)
        
        return wrapper
    return decorator