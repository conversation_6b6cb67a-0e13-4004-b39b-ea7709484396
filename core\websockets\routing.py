"""
WebSocket routing configuration for School ERP
"""

from django.urls import re_path, path
from channels.routing import Protocol<PERSON><PERSON><PERSON><PERSON><PERSON>, URLRouter
from channels.security.websocket import AllowedHostsOriginValidator
from .authentication import WebSocketAuthMiddlewareStack
from .consumers import (
    NotificationConsumer,
    ChatConsumer,
    LiveUpdatesConsumer,
    SystemMonitoringConsumer
)

# WebSocket URL patterns
websocket_urlpatterns = [
    # Notifications WebSocket
    re_path(r'ws/notifications/$', NotificationConsumer.as_asgi()),
    
    # Chat WebSocket with room support
    re_path(r'ws/chat/(?P<room_name>\w+)/$', ChatConsumer.as_asgi()),
    
    # Live updates WebSocket
    re_path(r'ws/updates/$', LiveUpdatesConsumer.as_asgi()),
    
    # System monitoring WebSocket (admin only)
    re_path(r'ws/monitoring/$', SystemMonitoringConsumer.as_asgi()),
    
    # Legacy support (can be removed later)
    re_path(r'ws/legacy/notifications/$', NotificationConsumer.as_asgi()),
]

# Main application routing
application = ProtocolTypeRouter({
    'websocket': AllowedHostsOriginValidator(
        WebSocketAuthMiddlewareStack(
            URLRouter(websocket_urlpatterns)
        )
    ),
})

# Export for use in ASGI configuration
websocket_application = application