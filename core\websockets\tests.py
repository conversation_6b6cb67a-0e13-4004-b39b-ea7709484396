"""
Tests for WebSocket functionality in School ERP
"""

import json
import pytest
from channels.testing import Webso<PERSON>Communicator
from channels.db import database_sync_to_async
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core.cache import cache
from rest_framework_simplejwt.tokens import RefreshToken
from unittest.mock import patch, MagicMock

from .consumers import (
    NotificationConsumer, ChatConsumer, 
    LiveUpdatesConsumer, SystemMonitoringConsumer
)
from .authentication import WebSocketJWTAuthentication
from .permissions import IsAuthenticated, IsAdminUser
from .analytics import WebSocketAnalytics
from .utils import (
    notification_sender, data_updater, 
    chat_manager, system_monitor
)

User = get_user_model()


class WebSocketAuthenticationTestCase(TestCase):
    """
    Test WebSocket authentication functionality
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='student'
        )
        self.auth = WebSocketJWTAuthentication()
        
        # Generate JWT token
        refresh = RefreshToken.for_user(self.user)
        self.access_token = str(refresh.access_token)
    
    @pytest.mark.asyncio
    async def test_authenticate_with_valid_token_in_query(self):
        """Test authentication with valid JWT token in query parameters"""
        scope = {
            'type': 'websocket',
            'query_string': f'token={self.access_token}'.encode(),
            'headers': []
        }
        
        user = await self.auth.authenticate(scope)
        self.assertEqual(user, self.user)
    
    @pytest.mark.asyncio
    async def test_authenticate_with_valid_token_in_header(self):
        """Test authentication with valid JWT token in headers"""
        scope = {
            'type': 'websocket',
            'query_string': b'',
            'headers': [
                (b'authorization', f'Bearer {self.access_token}'.encode())
            ]
        }
        
        user = await self.auth.authenticate(scope)
        self.assertEqual(user, self.user)
    
    @pytest.mark.asyncio
    async def test_authenticate_with_invalid_token(self):
        """Test authentication with invalid JWT token"""
        scope = {
            'type': 'websocket',
            'query_string': b'token=invalid_token',
            'headers': []
        }
        
        user = await self.auth.authenticate(scope)
        self.assertIsNone(user)
    
    @pytest.mark.asyncio
    async def test_authenticate_without_token(self):
        """Test authentication without token"""
        scope = {
            'type': 'websocket',
            'query_string': b'',
            'headers': []
        }
        
        user = await self.auth.authenticate(scope)
        self.assertIsNone(user)


class WebSocketConsumerTestCase(TestCase):
    """
    Test WebSocket consumer functionality
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='student'
        )
        
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='adminpass123',
            user_type='admin'
        )
        
        # Generate JWT tokens
        refresh = RefreshToken.for_user(self.user)
        self.access_token = str(refresh.access_token)
        
        admin_refresh = RefreshToken.for_user(self.admin_user)
        self.admin_token = str(admin_refresh.access_token)
        
        cache.clear()
    
    @pytest.mark.asyncio
    async def test_notification_consumer_connection(self):
        """Test NotificationConsumer connection"""
        communicator = WebsocketCommunicator(
            NotificationConsumer.as_asgi(),
            f"/ws/notifications/?token={self.access_token}"
        )
        
        connected, subprotocol = await communicator.connect()
        self.assertTrue(connected)
        
        # Should receive welcome message
        response = await communicator.receive_json_from()
        self.assertEqual(response['type'], 'connection_established')
        self.assertEqual(response['user_id'], self.user.id)
        
        await communicator.disconnect()
    
    @pytest.mark.asyncio
    async def test_notification_consumer_unauthorized(self):
        """Test NotificationConsumer rejects unauthorized connections"""
        communicator = WebsocketCommunicator(
            NotificationConsumer.as_asgi(),
            "/ws/notifications/"
        )
        
        connected, subprotocol = await communicator.connect()
        self.assertFalse(connected)
    
    @pytest.mark.asyncio
    async def test_chat_consumer_connection(self):
        """Test ChatConsumer connection"""
        communicator = WebsocketCommunicator(
            ChatConsumer.as_asgi(),
            f"/ws/chat/general/?token={self.access_token}"
        )
        
        connected, subprotocol = await communicator.connect()
        self.assertTrue(connected)
        
        # Should receive welcome message
        response = await communicator.receive_json_from()
        self.assertEqual(response['type'], 'connection_established')
        
        await communicator.disconnect()
    
    @pytest.mark.asyncio
    async def test_chat_message_handling(self):
        """Test chat message handling"""
        communicator = WebsocketCommunicator(
            ChatConsumer.as_asgi(),
            f"/ws/chat/general/?token={self.access_token}"
        )
        
        connected, subprotocol = await communicator.connect()
        self.assertTrue(connected)
        
        # Skip welcome message
        await communicator.receive_json_from()
        
        # Send chat message
        await communicator.send_json_to({
            'type': 'chat_message',
            'message': 'Hello, world!'
        })
        
        # Should receive the message back
        response = await communicator.receive_json_from()
        self.assertEqual(response['type'], 'chat_message')
        self.assertEqual(response['message'], 'Hello, world!')
        self.assertEqual(response['user'], self.user.username)
        
        await communicator.disconnect()
    
    @pytest.mark.asyncio
    async def test_system_monitoring_consumer_admin_only(self):
        """Test SystemMonitoringConsumer allows only admin users"""
        # Test with regular user (should be rejected)
        communicator = WebsocketCommunicator(
            SystemMonitoringConsumer.as_asgi(),
            f"/ws/monitoring/?token={self.access_token}"
        )
        
        connected, subprotocol = await communicator.connect()
        self.assertFalse(connected)
        
        # Test with admin user (should be accepted)
        admin_communicator = WebsocketCommunicator(
            SystemMonitoringConsumer.as_asgi(),
            f"/ws/monitoring/?token={self.admin_token}"
        )
        
        connected, subprotocol = await admin_communicator.connect()
        self.assertTrue(connected)
        
        await admin_communicator.disconnect()
    
    @pytest.mark.asyncio
    async def test_live_updates_consumer_subscription(self):
        """Test LiveUpdatesConsumer subscription functionality"""
        communicator = WebsocketCommunicator(
            LiveUpdatesConsumer.as_asgi(),
            f"/ws/updates/?token={self.access_token}"
        )
        
        connected, subprotocol = await communicator.connect()
        self.assertTrue(connected)
        
        # Skip welcome message
        await communicator.receive_json_from()
        
        # Subscribe to updates
        await communicator.send_json_to({
            'type': 'subscribe',
            'update_types': ['student_grades', 'attendance']
        })
        
        # Should receive subscription confirmation
        response = await communicator.receive_json_from()
        self.assertEqual(response['type'], 'subscription_confirmed')
        self.assertIn('student_grades', response['update_types'])
        self.assertIn('attendance', response['update_types'])
        
        await communicator.disconnect()


class WebSocketAnalyticsTestCase(TestCase):
    """
    Test WebSocket analytics functionality
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='teacher'
        )
        self.analytics = WebSocketAnalytics()
        cache.clear()
    
    @pytest.mark.asyncio
    async def test_record_connection(self):
        """Test recording WebSocket connection"""
        await self.analytics.record_connection(self.user, '/ws/notifications/')
        
        # Check if connection was recorded
        active_connections = await self.analytics.get_active_connections()
        self.assertGreater(active_connections, 0)
        
        # Check role-specific connections
        connections_by_role = await self.analytics.get_active_connections_by_role()
        self.assertGreater(connections_by_role['teacher'], 0)
    
    @pytest.mark.asyncio
    async def test_record_message(self):
        """Test recording WebSocket message"""
        await self.analytics.record_message(self.user, 'chat_message', 'sent')
        
        # Check message type stats
        message_stats = await self.analytics.get_message_type_stats(1)
        self.assertIn('chat_message', message_stats)
        self.assertGreater(message_stats['chat_message'], 0)
    
    @pytest.mark.asyncio
    async def test_get_user_activity(self):
        """Test getting user activity statistics"""
        # Record some activity
        await self.analytics.record_connection(self.user, '/ws/notifications/')
        await self.analytics.record_message(self.user, 'notification', 'received')
        
        # Get user activity
        activity = await self.analytics.get_user_activity(self.user.id, 1)
        
        self.assertIn('messages_sent', activity)
        self.assertIn('current_connections', activity)
    
    @pytest.mark.asyncio
    async def test_performance_metrics(self):
        """Test getting performance metrics"""
        # Record some data
        await self.analytics.record_connection(self.user, '/ws/notifications/')
        await self.analytics.record_message(self.user, 'test_message', 'sent')
        
        # Get performance metrics
        metrics = await self.analytics.get_performance_metrics()
        
        self.assertIn('active_connections', metrics)
        self.assertIn('connections_by_role', metrics)
        self.assertIn('hourly_stats', metrics)
        self.assertIn('daily_stats', metrics)
        self.assertIn('message_types', metrics)


class WebSocketUtilsTestCase(TestCase):
    """
    Test WebSocket utility functions
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='student'
        )
    
    def test_notification_sender(self):
        """Test WebSocket notification sender"""
        from core.websockets.utils import WebSocketNotificationSender
        from unittest.mock import AsyncMock
        
        # Create a new notification sender instance
        test_sender = WebSocketNotificationSender()
        
        # Mock the channel layer directly on the instance
        mock_layer = MagicMock()
        # Make group_send an async mock
        mock_layer.group_send = AsyncMock()
        test_sender.channel_layer = mock_layer
        
        # Test sending notification to user
        result = test_sender.send_to_user(
            self.user.id, 
            'test_notification', 
            {'message': 'Test message'}
        )
        
        self.assertTrue(result)
        mock_layer.group_send.assert_called_once()
    
    def test_data_updater(self):
        """Test WebSocket data updater"""
        from core.websockets.utils import WebSocketDataUpdater
        from unittest.mock import AsyncMock
        
        # Create a new data updater instance
        test_updater = WebSocketDataUpdater()
        
        # Mock the channel layer directly on the instance
        mock_layer = MagicMock()
        mock_layer.group_send = AsyncMock()
        test_updater.channel_layer = mock_layer
        
        # Test sending data update
        result = test_updater.send_update(
            'student_grade',
            {'grade': 'A', 'subject': 'Math'},
            target_type='user',
            target_id=self.user.id
        )
        
        self.assertTrue(result)
        mock_layer.group_send.assert_called_once()
    
    def test_chat_manager(self):
        """Test WebSocket chat manager"""
        from core.websockets.utils import WebSocketChatManager
        from unittest.mock import AsyncMock
        
        # Create a new chat manager instance
        test_manager = WebSocketChatManager()
        
        # Mock the channel layer directly on the instance
        mock_layer = MagicMock()
        mock_layer.group_send = AsyncMock()
        test_manager.channel_layer = mock_layer
        
        # Test sending chat message
        result = test_manager.send_message_to_room(
            'general',
            'Hello everyone!',
            self.user.id,
            self.user.username
        )
        
        self.assertTrue(result)
        mock_layer.group_send.assert_called_once()
    
    def test_system_monitor(self):
        """Test WebSocket system monitor"""
        from core.websockets.utils import WebSocketSystemMonitor
        from unittest.mock import AsyncMock
        
        # Create a new system monitor instance
        test_monitor = WebSocketSystemMonitor()
        
        # Mock the channel layer directly on the instance
        mock_layer = MagicMock()
        mock_layer.group_send = AsyncMock()
        test_monitor.channel_layer = mock_layer
        
        # Test sending system alert
        result = test_monitor.send_system_alert(
            {'message': 'High CPU usage detected'},
            'warning'
        )
        
        self.assertTrue(result)
        mock_layer.group_send.assert_called_once()
    
    def test_get_user_websocket_groups(self):
        """Test getting user WebSocket groups"""
        from .utils import get_user_websocket_groups
        
        groups = get_user_websocket_groups(self.user)
        
        self.assertIn(f'user_{self.user.id}', groups)
        self.assertIn(f'role_{self.user.user_type}', groups)
    
    def test_validate_websocket_message(self):
        """Test WebSocket message validation"""
        from .utils import validate_websocket_message
        
        # Valid message
        valid_message = {'type': 'test_message', 'data': 'test'}
        is_valid, error = validate_websocket_message(valid_message)
        self.assertTrue(is_valid)
        self.assertIsNone(error)
        
        # Invalid message (no type)
        invalid_message = {'data': 'test'}
        is_valid, error = validate_websocket_message(invalid_message)
        self.assertFalse(is_valid)
        self.assertIsNotNone(error)
        
        # Invalid message (not dict)
        is_valid, error = validate_websocket_message("not a dict")
        self.assertFalse(is_valid)
        self.assertIsNotNone(error)
    
    def test_serialize_for_websocket(self):
        """Test WebSocket data serialization"""
        from .utils import serialize_for_websocket
        
        data = {
            'type': 'test',
            'message': 'Hello',
            'timestamp': '2025-01-01T00:00:00Z'
        }
        
        serialized = serialize_for_websocket(data)
        self.assertIsInstance(serialized, str)
        
        # Should be valid JSON
        deserialized = json.loads(serialized)
        self.assertEqual(deserialized['type'], 'test')
        self.assertEqual(deserialized['message'], 'Hello')


class WebSocketPermissionsTestCase(TestCase):
    """
    Test WebSocket permissions
    """
    
    def setUp(self):
        self.student = User.objects.create_user(
            username='student',
            email='<EMAIL>',
            password='testpass123',
            user_type='student'
        )
        
        self.teacher = User.objects.create_user(
            username='teacher',
            email='<EMAIL>',
            password='testpass123',
            user_type='teacher'
        )
        
        self.admin = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='testpass123',
            user_type='admin'
        )
    
    @pytest.mark.asyncio
    async def test_is_authenticated_permission(self):
        """Test IsAuthenticated permission"""
        permission = IsAuthenticated()
        
        # Authenticated user should pass
        result = await permission.has_permission(self.student, None)
        self.assertTrue(result)
        
        # None user should fail
        result = await permission.has_permission(None, None)
        self.assertFalse(result)
    
    @pytest.mark.asyncio
    async def test_is_admin_user_permission(self):
        """Test IsAdminUser permission"""
        permission = IsAdminUser()
        
        # Admin user should pass
        result = await permission.has_permission(self.admin, None)
        self.assertTrue(result)
        
        # Non-admin user should fail
        result = await permission.has_permission(self.student, None)
        self.assertFalse(result)
        
        # None user should fail
        result = await permission.has_permission(None, None)
        self.assertFalse(result)


class WebSocketIntegrationTestCase(TestCase):
    """
    Integration tests for WebSocket functionality
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            user_type='student'
        )
        
        # Generate JWT token
        refresh = RefreshToken.for_user(self.user)
        self.access_token = str(refresh.access_token)
        
        cache.clear()
    
    @pytest.mark.asyncio
    async def test_full_notification_flow(self):
        """Test complete notification flow"""
        # Connect to notification WebSocket
        communicator = WebsocketCommunicator(
            NotificationConsumer.as_asgi(),
            f"/ws/notifications/?token={self.access_token}"
        )
        
        connected, subprotocol = await communicator.connect()
        self.assertTrue(connected)
        
        # Skip welcome message
        await communicator.receive_json_from()
        
        # Test marking notification as read
        await communicator.send_json_to({
            'type': 'mark_read',
            'notification_id': 'test_notification_123'
        })
        
        # Should receive confirmation
        response = await communicator.receive_json_from()
        self.assertEqual(response['type'], 'notification_marked_read')
        self.assertEqual(response['notification_id'], 'test_notification_123')
        
        # Test getting unread count
        await communicator.send_json_to({
            'type': 'get_unread_count'
        })
        
        # Should receive unread count
        response = await communicator.receive_json_from()
        self.assertEqual(response['type'], 'unread_count')
        self.assertIn('count', response)
        
        await communicator.disconnect()
    
    @pytest.mark.asyncio
    async def test_chat_room_interaction(self):
        """Test chat room interaction between multiple users"""
        # Create second user
        user2 = await database_sync_to_async(User.objects.create_user)(
            username='testuser2',
            email='<EMAIL>',
            password='testpass123',
            user_type='student'
        )
        
        refresh2 = RefreshToken.for_user(user2)
        access_token2 = str(refresh2.access_token)
        
        # Connect both users to same chat room
        communicator1 = WebsocketCommunicator(
            ChatConsumer.as_asgi(),
            f"/ws/chat/testroom/?token={self.access_token}"
        )
        
        communicator2 = WebsocketCommunicator(
            ChatConsumer.as_asgi(),
            f"/ws/chat/testroom/?token={access_token2}"
        )
        
        # Connect both
        connected1, _ = await communicator1.connect()
        connected2, _ = await communicator2.connect()
        
        self.assertTrue(connected1)
        self.assertTrue(connected2)
        
        # Skip welcome messages
        await communicator1.receive_json_from()
        await communicator2.receive_json_from()
        
        # User2 should receive user1 joined message
        user_joined_msg = await communicator2.receive_json_from()
        self.assertEqual(user_joined_msg['type'], 'user_joined')
        
        # User1 sends message
        await communicator1.send_json_to({
            'type': 'chat_message',
            'message': 'Hello from user1!'
        })
        
        # Both users should receive the message
        msg1 = await communicator1.receive_json_from()
        msg2 = await communicator2.receive_json_from()
        
        self.assertEqual(msg1['type'], 'chat_message')
        self.assertEqual(msg2['type'], 'chat_message')
        self.assertEqual(msg1['message'], 'Hello from user1!')
        self.assertEqual(msg2['message'], 'Hello from user1!')
        
        await communicator1.disconnect()
        await communicator2.disconnect()
    
    @pytest.mark.asyncio
    async def test_error_handling(self):
        """Test WebSocket error handling"""
        communicator = WebsocketCommunicator(
            NotificationConsumer.as_asgi(),
            f"/ws/notifications/?token={self.access_token}"
        )
        
        connected, subprotocol = await communicator.connect()
        self.assertTrue(connected)
        
        # Skip welcome message
        await communicator.receive_json_from()
        
        # Send invalid JSON
        await communicator.send_to(text_data="invalid json")
        
        # Should receive error message
        response = await communicator.receive_json_from()
        self.assertEqual(response['type'], 'error')
        self.assertIn('Invalid JSON format', response['message'])
        
        # Send message with unknown type
        await communicator.send_json_to({
            'type': 'unknown_message_type',
            'data': 'test'
        })
        
        # Should receive error message
        response = await communicator.receive_json_from()
        self.assertEqual(response['type'], 'error')
        self.assertIn('Unknown message type', response['message'])
        
        await communicator.disconnect()


if __name__ == '__main__':
    import django
    from django.conf import settings
    from django.test.utils import get_runner
    
    django.setup()
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    failures = test_runner.run_tests(['core.websockets.tests'])