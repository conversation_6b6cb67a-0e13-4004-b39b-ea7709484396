"""
WebSocket utilities and helpers for School ERP
"""

import json
import logging
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core.serializers.json import DjangoJSONEncoder

logger = logging.getLogger(__name__)
User = get_user_model()


class WebSocketNotificationSender:
    """
    Utility class to send notifications via WebSocket
    """
    
    def __init__(self):
        self.channel_layer = get_channel_layer()
    
    def send_to_user(self, user_id, notification_type, data):
        """
        Send notification to a specific user
        """
        if not self.channel_layer:
            logger.warning("Channel layer not configured, cannot send WebSocket notification")
            return False
        
        group_name = f"notifications_{user_id}"
        
        try:
            async_to_sync(self.channel_layer.group_send)(
                group_name,
                {
                    'type': 'send_notification',
                    'data': {
                        'notification_type': notification_type,
                        'data': data,
                        'timestamp': timezone.now().isoformat()
                    }
                }
            )
            logger.info(f"WebSocket notification sent to user {user_id}: {notification_type}")
            return True
        
        except Exception as e:
            logger.error(f"Failed to send WebSocket notification to user {user_id}: {e}")
            return False
    
    def send_to_role(self, role, notification_type, data):
        """
        Send notification to all users with a specific role
        """
        if not self.channel_layer:
            logger.warning("Channel layer not configured, cannot send WebSocket notification")
            return False
        
        group_name = f"role_{role}"
        
        try:
            async_to_sync(self.channel_layer.group_send)(
                group_name,
                {
                    'type': 'send_notification',
                    'data': {
                        'notification_type': notification_type,
                        'data': data,
                        'timestamp': timezone.now().isoformat()
                    }
                }
            )
            logger.info(f"WebSocket notification sent to role {role}: {notification_type}")
            return True
        
        except Exception as e:
            logger.error(f"Failed to send WebSocket notification to role {role}: {e}")
            return False
    
    def send_to_school(self, school_id, notification_type, data):
        """
        Send notification to all users in a school
        """
        if not self.channel_layer:
            logger.warning("Channel layer not configured, cannot send WebSocket notification")
            return False
        
        group_name = f"school_{school_id}_notifications"
        
        try:
            async_to_sync(self.channel_layer.group_send)(
                group_name,
                {
                    'type': 'send_notification',
                    'data': {
                        'notification_type': notification_type,
                        'data': data,
                        'timestamp': timezone.now().isoformat()
                    }
                }
            )
            logger.info(f"WebSocket notification sent to school {school_id}: {notification_type}")
            return True
        
        except Exception as e:
            logger.error(f"Failed to send WebSocket notification to school {school_id}: {e}")
            return False
    
    def broadcast(self, notification_type, data):
        """
        Broadcast notification to all connected users
        """
        if not self.channel_layer:
            logger.warning("Channel layer not configured, cannot send WebSocket notification")
            return False
        
        group_name = "broadcast"
        
        try:
            async_to_sync(self.channel_layer.group_send)(
                group_name,
                {
                    'type': 'send_notification',
                    'data': {
                        'notification_type': notification_type,
                        'data': data,
                        'timestamp': timezone.now().isoformat()
                    }
                }
            )
            logger.info(f"WebSocket notification broadcasted: {notification_type}")
            return True
        
        except Exception as e:
            logger.error(f"Failed to broadcast WebSocket notification: {e}")
            return False


class WebSocketDataUpdater:
    """
    Utility class to send real-time data updates via WebSocket
    """
    
    def __init__(self):
        self.channel_layer = get_channel_layer()
    
    def send_update(self, update_type, data, target_type='user', target_id=None):
        """
        Send data update via WebSocket
        """
        if not self.channel_layer:
            logger.warning("Channel layer not configured, cannot send WebSocket update")
            return False
        
        # Determine group name based on target
        if target_type == 'user' and target_id:
            group_name = f"live_updates_{target_id}"
        elif target_type == 'role' and target_id:
            group_name = f"role_{target_id}"
        elif target_type == 'school' and target_id:
            group_name = f"school_{target_id}_updates"
        elif target_type == 'update_type':
            group_name = f"updates_{update_type}"
        else:
            group_name = "live_updates"
        
        try:
            async_to_sync(self.channel_layer.group_send)(
                group_name,
                {
                    'type': 'data_update',
                    'update_type': update_type,
                    'data': data
                }
            )
            logger.info(f"WebSocket data update sent: {update_type} to {group_name}")
            return True
        
        except Exception as e:
            logger.error(f"Failed to send WebSocket data update: {e}")
            return False
    
    def send_student_grade_update(self, student_id, grade_data):
        """
        Send student grade update
        """
        return self.send_update(
            'student_grade',
            grade_data,
            target_type='user',
            target_id=student_id
        )
    
    def send_attendance_update(self, class_id, attendance_data):
        """
        Send attendance update to class
        """
        return self.send_update(
            'attendance',
            attendance_data,
            target_type='update_type'
        )
    
    def send_announcement_update(self, announcement_data, target_role=None):
        """
        Send announcement update
        """
        if target_role:
            return self.send_update(
                'announcement',
                announcement_data,
                target_type='role',
                target_id=target_role
            )
        else:
            return self.send_update('announcement', announcement_data)


class WebSocketChatManager:
    """
    Utility class to manage chat functionality
    """
    
    def __init__(self):
        self.channel_layer = get_channel_layer()
    
    def send_message_to_room(self, room_name, message, sender_user_id, sender_username):
        """
        Send chat message to room
        """
        if not self.channel_layer:
            logger.warning("Channel layer not configured, cannot send chat message")
            return False
        
        group_name = f"chat_{room_name}"
        
        try:
            async_to_sync(self.channel_layer.group_send)(
                group_name,
                {
                    'type': 'chat_message',
                    'message': message,
                    'user': sender_username,
                    'user_id': sender_user_id,
                    'message_id': f"msg_{timezone.now().timestamp()}",
                    'timestamp': timezone.now().isoformat()
                }
            )
            logger.info(f"Chat message sent to room {room_name}")
            return True
        
        except Exception as e:
            logger.error(f"Failed to send chat message to room {room_name}: {e}")
            return False
    
    def send_typing_indicator(self, room_name, user_id, username, is_typing):
        """
        Send typing indicator to room
        """
        if not self.channel_layer:
            return False
        
        group_name = f"chat_{room_name}"
        
        try:
            async_to_sync(self.channel_layer.group_send)(
                group_name,
                {
                    'type': 'typing_indicator',
                    'user': username,
                    'user_id': user_id,
                    'is_typing': is_typing
                }
            )
            return True
        
        except Exception as e:
            logger.error(f"Failed to send typing indicator: {e}")
            return False


class WebSocketSystemMonitor:
    """
    Utility class to send system monitoring updates
    """
    
    def __init__(self):
        self.channel_layer = get_channel_layer()
    
    def send_system_alert(self, alert_data, severity='info'):
        """
        Send system alert to monitoring WebSocket
        """
        if not self.channel_layer:
            return False
        
        group_name = "system_monitoring"
        
        try:
            async_to_sync(self.channel_layer.group_send)(
                group_name,
                {
                    'type': 'system_alert',
                    'alert': alert_data,
                    'severity': severity
                }
            )
            logger.info(f"System alert sent: {severity}")
            return True
        
        except Exception as e:
            logger.error(f"Failed to send system alert: {e}")
            return False
    
    def send_performance_update(self, metrics):
        """
        Send performance metrics update
        """
        if not self.channel_layer:
            return False
        
        group_name = "system_monitoring"
        
        try:
            async_to_sync(self.channel_layer.group_send)(
                group_name,
                {
                    'type': 'performance_update',
                    'metrics': metrics
                }
            )
            return True
        
        except Exception as e:
            logger.error(f"Failed to send performance update: {e}")
            return False


class WebSocketJSONEncoder(DjangoJSONEncoder):
    """
    Custom JSON encoder for WebSocket messages
    """
    
    def default(self, obj):
        """
        Handle additional object types for WebSocket serialization
        """
        if hasattr(obj, 'to_dict'):
            return obj.to_dict()
        
        return super().default(obj)


def serialize_for_websocket(data):
    """
    Serialize data for WebSocket transmission
    """
    try:
        return json.dumps(data, cls=WebSocketJSONEncoder, ensure_ascii=False)
    except (TypeError, ValueError) as e:
        logger.error(f"Failed to serialize data for WebSocket: {e}")
        return json.dumps({'error': 'Serialization failed'})


def validate_websocket_message(message_data, required_fields=None):
    """
    Validate WebSocket message format
    """
    if not isinstance(message_data, dict):
        return False, "Message must be a JSON object"
    
    if 'type' not in message_data:
        return False, "Message must have a 'type' field"
    
    if required_fields:
        for field in required_fields:
            if field not in message_data:
                return False, f"Missing required field: {field}"
    
    return True, None


def get_user_websocket_groups(user):
    """
    Get list of WebSocket groups a user should be added to
    """
    groups = []
    
    if user and user.is_authenticated:
        # User-specific group
        groups.append(f"user_{user.id}")
        
        # Role-specific group
        groups.append(f"role_{user.user_type}")
        
        # School-specific groups (if applicable)
        if hasattr(user, 'school') and user.school:
            groups.append(f"school_{user.school.id}_notifications")
            groups.append(f"school_{user.school.id}_updates")
        
        # Special groups for admins
        if user.user_type == 'admin':
            groups.append("admin_updates")
            groups.append("system_monitoring")
    
    return groups


def cleanup_websocket_groups(user):
    """
    Clean up WebSocket groups when user disconnects
    """
    # This would be called when a user disconnects
    # Implementation depends on your specific needs
    pass


# Global utility instances
notification_sender = WebSocketNotificationSender()
data_updater = WebSocketDataUpdater()
chat_manager = WebSocketChatManager()
system_monitor = WebSocketSystemMonitor()


# Convenience functions for common operations
def notify_user(user_id, notification_type, data):
    """
    Convenience function to notify a user
    """
    return notification_sender.send_to_user(user_id, notification_type, data)


def notify_role(role, notification_type, data):
    """
    Convenience function to notify users by role
    """
    return notification_sender.send_to_role(role, notification_type, data)


def update_data(update_type, data, target_type='broadcast', target_id=None):
    """
    Convenience function to send data updates
    """
    return data_updater.send_update(update_type, data, target_type, target_id)


def send_chat_message(room_name, message, sender_user_id, sender_username):
    """
    Convenience function to send chat message
    """
    return chat_manager.send_message_to_room(room_name, message, sender_user_id, sender_username)


def send_system_alert(alert_data, severity='info'):
    """
    Convenience function to send system alert
    """
    return system_monitor.send_system_alert(alert_data, severity)