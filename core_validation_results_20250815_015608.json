{"summary": {"total_tests": 9, "passed_tests": 7, "failed_tests": 2, "success_rate": 77.77777777777779, "duration": 8.236286, "key_fixes": {"Department Model": true, "School Context": false, "IntegrityError Resolution": true, "Performance Optimization": true}}, "results": [{"test": "Department Creation", "success": true, "message": "Department created: Test Department", "timestamp": "2025-08-15T01:56:00.014933"}, {"test": "Teacher-Department Relationship", "success": true, "message": "Teacher assigned to department: Test Department", "timestamp": "2025-08-15T01:56:01.128469"}, {"test": "User Login", "success": true, "message": "User logged in for context test", "timestamp": "2025-08-15T01:56:05.893246"}, {"test": "School Selection", "success": false, "message": "School selection response: 400", "timestamp": "2025-08-15T01:56:07.924513"}, {"test": "AJAX School Switch", "success": false, "message": "AJAX switch status: 400", "timestamp": "2025-08-15T01:56:08.011417"}, {"test": "Subject Creation with School", "success": true, "message": "Subject created: Test Subject", "timestamp": "2025-08-15T01:56:08.083617"}, {"test": "Model School Context", "success": true, "message": "Models properly maintain school context", "timestamp": "2025-08-15T01:56:08.084333"}, {"test": "Performance Module Import", "success": true, "message": "Performance optimization module imports successfully", "timestamp": "2025-08-15T01:56:08.091251"}, {"test": "School Utils Performance", "success": true, "message": "School utils return 14 schools", "timestamp": "2025-08-15T01:56:08.144134"}]}