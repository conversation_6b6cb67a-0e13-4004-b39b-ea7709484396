#!/usr/bin/env python
"""
Debug script to test account balance calculations
"""
import os
import sys
import django
from decimal import Decimal
from datetime import date

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

from django.contrib.auth.models import User
from core.models import School
from finance.models import Account, AccountType, Transaction, TransactionEntry

def debug_balance_calculation():
    """Debug the balance calculation issue"""
    
    # Create test data
    school = School.objects.create(
        name="Test School",
        address="123 Test St",
        phone="************",
        email="<EMAIL>",
        principal_name="Test Principal",
        established_date=date.today()
    )
    
    user = User.objects.create_user(
        username="testuser",
        email="<EMAIL>",
        password="testpass123"
    )
    
    # Create account type
    asset_type = AccountType.objects.create(
        school=school,
        name="Assets",
        type="asset"
    )
    
    # Create cash account with opening balance
    cash_account = Account.objects.create(
        school=school,
        code="1110",
        name="Cash",
        account_type=asset_type,
        opening_balance=Decimal('1000.00')
    )
    
    print(f"Cash account created:")
    print(f"  Opening balance: {cash_account.opening_balance}")
    print(f"  Current balance: {cash_account.current_balance}")
    print(f"  Balance type: {cash_account.balance_type}")
    
    # Create transaction
    transaction = Transaction.objects.create(
        school=school,
        transaction_date=date.today(),
        transaction_type='manual',
        description='Test transaction',
        total_amount=Decimal('500.00'),
        created_by=user
    )
    
    # Create debit entry
    debit_entry = TransactionEntry.objects.create(
        school=school,
        transaction=transaction,
        account=cash_account,
        entry_date=date.today(),
        description='Cash receipt',
        debit_amount=Decimal('500.00'),
        created_by=user
    )
    
    print(f"\nBefore posting:")
    print(f"  Cash account balance: {cash_account.current_balance}")
    print(f"  Entry is_posted: {debit_entry.is_posted}")
    
    # Post the entry
    debit_entry.post()
    
    # Refresh account from database
    cash_account.refresh_from_db()
    
    print(f"\nAfter posting:")
    print(f"  Cash account balance: {cash_account.current_balance}")
    print(f"  Entry is_posted: {debit_entry.is_posted}")
    print(f"  Expected balance: 1500.00")
    
    # Clean up
    school.delete()

if __name__ == "__main__":
    debug_balance_calculation()
