# School ERP System API Documentation

Welcome to the School ERP System API documentation. This comprehensive guide provides everything you need to integrate with and extend the School ERP System.

## 📋 Table of Contents

1. [Getting Started](#getting-started)
2. [Authentication](#authentication)
3. [API Endpoints](#api-endpoints)
4. [Data Models](#data-models)
5. [Error Handling](#error-handling)
6. [Rate Limiting](#rate-limiting)
7. [SDKs and Libraries](#sdks-and-libraries)
8. [Examples](#examples)
9. [Changelog](#changelog)

---

## Getting Started

### Base URL
```
Production: https://api.yourschool.edu/v1/
Staging: https://staging-api.yourschool.edu/v1/
Development: http://localhost:8000/api/v1/
```

### API Version
Current Version: **v1**

All API requests should include the version in the URL path. We maintain backward compatibility and provide advance notice of any breaking changes.

### Content Type
All requests and responses use JSON format:
```
Content-Type: application/json
Accept: application/json
```

### HTTP Methods
The API follows RESTful conventions:
- `GET` - Retrieve resources
- `POST` - Create new resources
- `PUT` - Update entire resources
- `PATCH` - Partial resource updates
- `DELETE` - Remove resources

---

## Authentication

### API Key Authentication
For server-to-server communication, use API key authentication:

```http
GET /api/v1/students/
Authorization: Api-Key YOUR_API_KEY
```

### JWT Token Authentication
For user-specific operations, use JWT tokens:

```http
GET /api/v1/students/me/
Authorization: Bearer YOUR_JWT_TOKEN
```

### OAuth 2.0
For third-party integrations:

```http
GET /api/v1/students/
Authorization: Bearer YOUR_OAUTH_TOKEN
```

### Getting API Credentials

#### API Keys
1. Login as administrator
2. Navigate to Settings > API Management
3. Click "Generate New API Key"
4. Set permissions and expiration
5. Copy and securely store the key

#### JWT Tokens
```http
POST /api/v1/auth/login/
Content-Type: application/json

{
    "username": "your_username",
    "password": "your_password"
}
```

Response:
```json
{
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expires_in": 3600,
    "token_type": "Bearer"
}
```

---

## API Endpoints

### Core Endpoints

#### Schools
- `GET /api/v1/schools/` - List schools
- `GET /api/v1/schools/{id}/` - Get school details
- `POST /api/v1/schools/` - Create school (admin only)
- `PUT /api/v1/schools/{id}/` - Update school
- `DELETE /api/v1/schools/{id}/` - Delete school

#### Academic Years
- `GET /api/v1/academic-years/` - List academic years
- `GET /api/v1/academic-years/{id}/` - Get academic year details
- `POST /api/v1/academic-years/` - Create academic year
- `PUT /api/v1/academic-years/{id}/` - Update academic year

### Student Management

#### Students
- `GET /api/v1/students/` - List students
- `GET /api/v1/students/{id}/` - Get student details
- `POST /api/v1/students/` - Create student
- `PUT /api/v1/students/{id}/` - Update student
- `DELETE /api/v1/students/{id}/` - Delete student
- `GET /api/v1/students/me/` - Get current student's info

#### Parents
- `GET /api/v1/parents/` - List parents
- `GET /api/v1/parents/{id}/` - Get parent details
- `GET /api/v1/parents/{id}/children/` - Get parent's children

#### Student Documents
- `GET /api/v1/students/{id}/documents/` - List student documents
- `POST /api/v1/students/{id}/documents/` - Upload document
- `GET /api/v1/documents/{id}/` - Get document details
- `DELETE /api/v1/documents/{id}/` - Delete document

### Academic Management

#### Grades
- `GET /api/v1/grades/` - List grades
- `GET /api/v1/grades/{id}/` - Get grade details
- `POST /api/v1/grades/` - Create grade
- `PUT /api/v1/grades/{id}/` - Update grade

#### Subjects
- `GET /api/v1/subjects/` - List subjects
- `GET /api/v1/subjects/{id}/` - Get subject details
- `POST /api/v1/subjects/` - Create subject
- `PUT /api/v1/subjects/{id}/` - Update subject

#### Classes
- `GET /api/v1/classes/` - List classes
- `GET /api/v1/classes/{id}/` - Get class details
- `GET /api/v1/classes/{id}/students/` - Get class students
- `POST /api/v1/classes/{id}/students/` - Add student to class

#### Attendance
- `GET /api/v1/attendance/` - List attendance records
- `POST /api/v1/attendance/` - Mark attendance
- `GET /api/v1/students/{id}/attendance/` - Get student attendance
- `GET /api/v1/classes/{id}/attendance/` - Get class attendance

#### Assignments
- `GET /api/v1/assignments/` - List assignments
- `GET /api/v1/assignments/{id}/` - Get assignment details
- `POST /api/v1/assignments/` - Create assignment
- `GET /api/v1/assignments/{id}/submissions/` - Get submissions

#### Grades (Academic Results)
- `GET /api/v1/academic-grades/` - List academic grades
- `POST /api/v1/academic-grades/` - Create grade entry
- `GET /api/v1/students/{id}/grades/` - Get student grades
- `GET /api/v1/classes/{id}/grades/` - Get class grades

### Financial Management

#### Fee Structures
- `GET /api/v1/fee-structures/` - List fee structures
- `GET /api/v1/fee-structures/{id}/` - Get fee structure details
- `POST /api/v1/fee-structures/` - Create fee structure

#### Student Fees
- `GET /api/v1/student-fees/` - List student fees
- `GET /api/v1/students/{id}/fees/` - Get student's fees
- `POST /api/v1/student-fees/` - Assign fees to student

#### Payments
- `GET /api/v1/payments/` - List payments
- `POST /api/v1/payments/` - Process payment
- `GET /api/v1/students/{id}/payments/` - Get student payments

#### Accounts
- `GET /api/v1/accounts/` - List chart of accounts
- `GET /api/v1/accounts/{id}/` - Get account details
- `GET /api/v1/accounts/{id}/balance/` - Get account balance

### Human Resources

#### Employees
- `GET /api/v1/employees/` - List employees
- `GET /api/v1/employees/{id}/` - Get employee details
- `POST /api/v1/employees/` - Create employee
- `PUT /api/v1/employees/{id}/` - Update employee

#### Departments
- `GET /api/v1/departments/` - List departments
- `GET /api/v1/departments/{id}/` - Get department details
- `GET /api/v1/departments/{id}/employees/` - Get department employees

#### Payroll
- `GET /api/v1/payroll/` - List payroll records
- `POST /api/v1/payroll/process/` - Process payroll
- `GET /api/v1/employees/{id}/payroll/` - Get employee payroll

#### Leave Management
- `GET /api/v1/leave-requests/` - List leave requests
- `POST /api/v1/leave-requests/` - Create leave request
- `PUT /api/v1/leave-requests/{id}/approve/` - Approve leave

### Transportation

#### Vehicles
- `GET /api/v1/vehicles/` - List vehicles
- `GET /api/v1/vehicles/{id}/` - Get vehicle details
- `POST /api/v1/vehicles/` - Create vehicle
- `GET /api/v1/vehicles/{id}/location/` - Get vehicle location

#### Routes
- `GET /api/v1/routes/` - List routes
- `GET /api/v1/routes/{id}/` - Get route details
- `GET /api/v1/routes/{id}/students/` - Get route students
- `GET /api/v1/routes/{id}/stops/` - Get route stops

#### Drivers
- `GET /api/v1/drivers/` - List drivers
- `GET /api/v1/drivers/{id}/` - Get driver details
- `GET /api/v1/drivers/{id}/routes/` - Get driver routes

#### GPS Tracking
- `GET /api/v1/gps-tracking/` - List GPS records
- `POST /api/v1/gps-tracking/` - Create GPS record
- `GET /api/v1/vehicles/{id}/tracking/` - Get vehicle tracking

### Library Management

#### Books
- `GET /api/v1/books/` - List books
- `GET /api/v1/books/{id}/` - Get book details
- `POST /api/v1/books/` - Add book
- `GET /api/v1/books/search/` - Search books

#### Book Borrowing
- `GET /api/v1/borrowings/` - List borrowings
- `POST /api/v1/borrowings/` - Borrow book
- `PUT /api/v1/borrowings/{id}/return/` - Return book
- `GET /api/v1/students/{id}/borrowings/` - Get student borrowings

#### Authors and Categories
- `GET /api/v1/authors/` - List authors
- `GET /api/v1/categories/` - List categories
- `GET /api/v1/categories/{id}/books/` - Get category books

### Health Management

#### Health Profiles
- `GET /api/v1/health-profiles/` - List health profiles
- `GET /api/v1/students/{id}/health/` - Get student health profile
- `PUT /api/v1/students/{id}/health/` - Update health profile

#### Medical Incidents
- `GET /api/v1/medical-incidents/` - List incidents
- `POST /api/v1/medical-incidents/` - Report incident
- `GET /api/v1/students/{id}/incidents/` - Get student incidents

### Communications

#### Announcements
- `GET /api/v1/announcements/` - List announcements
- `POST /api/v1/announcements/` - Create announcement
- `GET /api/v1/announcements/{id}/` - Get announcement details

#### Messages
- `GET /api/v1/messages/` - List messages
- `POST /api/v1/messages/` - Send message
- `GET /api/v1/messages/{id}/` - Get message details
- `PUT /api/v1/messages/{id}/read/` - Mark as read

#### Notifications
- `GET /api/v1/notifications/` - List notifications
- `POST /api/v1/notifications/` - Send notification
- `PUT /api/v1/notifications/{id}/read/` - Mark as read

---

## Data Models

### Student Model
```json
{
    "id": 1,
    "student_id": "STU001",
    "first_name": "John",
    "last_name": "Doe",
    "date_of_birth": "2010-01-15",
    "gender": "male",
    "nationality": "US",
    "email": "<EMAIL>",
    "phone": "+**********",
    "address": "123 Main St, City, State",
    "parent": {
        "id": 1,
        "father_name": "Robert Doe",
        "mother_name": "Jane Doe",
        "email": "<EMAIL>",
        "phone": "+**********"
    },
    "school": 1,
    "admission_date": "2023-09-01",
    "status": "active",
    "created_at": "2023-08-15T10:30:00Z",
    "updated_at": "2023-09-01T14:20:00Z"
}
```

### Class Model
```json
{
    "id": 1,
    "name": "Mathematics - Grade 5A",
    "section": "A",
    "grade": {
        "id": 1,
        "name": "Grade 5",
        "level": 5
    },
    "subject": {
        "id": 1,
        "name": "Mathematics",
        "code": "MATH5"
    },
    "teacher": {
        "id": 1,
        "name": "Dr. Smith",
        "email": "<EMAIL>"
    },
    "academic_year": {
        "id": 1,
        "name": "2023-2024",
        "is_current": true
    },
    "capacity": 30,
    "current_enrollment": 25,
    "is_active": true,
    "created_at": "2023-08-01T09:00:00Z"
}
```

### Assignment Model
```json
{
    "id": 1,
    "title": "Algebra Problem Set 1",
    "description": "Complete problems 1-20 from chapter 3",
    "class": 1,
    "teacher": 1,
    "assigned_date": "2023-10-01",
    "due_date": "2023-10-08",
    "total_marks": 100,
    "submission_type": "online",
    "instructions": "Show all work and submit as PDF",
    "attachments": [
        {
            "id": 1,
            "filename": "problem_set_1.pdf",
            "url": "/media/assignments/problem_set_1.pdf"
        }
    ],
    "is_published": true,
    "created_at": "2023-09-28T10:00:00Z"
}
```

### Grade Model
```json
{
    "id": 1,
    "student": 1,
    "assignment": 1,
    "class": 1,
    "marks_obtained": 85,
    "total_marks": 100,
    "percentage": 85.0,
    "grade": "B+",
    "comments": "Good work, but check calculation in problem 15",
    "graded_by": 1,
    "graded_at": "2023-10-10T14:30:00Z",
    "is_published": true
}
```

### Payment Model
```json
{
    "id": 1,
    "student_fee": 1,
    "amount": 500.00,
    "payment_method": "online",
    "payment_date": "2023-10-01",
    "reference_number": "PAY123456",
    "transaction_id": "TXN789012",
    "status": "completed",
    "receipt_url": "/media/receipts/receipt_123456.pdf",
    "created_at": "2023-10-01T10:15:00Z"
}
```

---

## Error Handling

### HTTP Status Codes

#### Success Codes
- `200 OK` - Request successful
- `201 Created` - Resource created successfully
- `204 No Content` - Request successful, no content returned

#### Client Error Codes
- `400 Bad Request` - Invalid request data
- `401 Unauthorized` - Authentication required
- `403 Forbidden` - Insufficient permissions
- `404 Not Found` - Resource not found
- `409 Conflict` - Resource conflict
- `422 Unprocessable Entity` - Validation errors

#### Server Error Codes
- `500 Internal Server Error` - Server error
- `502 Bad Gateway` - Gateway error
- `503 Service Unavailable` - Service temporarily unavailable

### Error Response Format

```json
{
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "The request data is invalid",
        "details": {
            "email": ["This field is required"],
            "date_of_birth": ["Date must be in YYYY-MM-DD format"]
        },
        "timestamp": "2023-10-01T10:30:00Z",
        "request_id": "req_123456789"
    }
}
```

### Common Error Codes

| Code | Description |
|------|-------------|
| `AUTHENTICATION_FAILED` | Invalid credentials |
| `PERMISSION_DENIED` | Insufficient permissions |
| `VALIDATION_ERROR` | Request data validation failed |
| `RESOURCE_NOT_FOUND` | Requested resource doesn't exist |
| `DUPLICATE_RESOURCE` | Resource already exists |
| `RATE_LIMIT_EXCEEDED` | Too many requests |
| `MAINTENANCE_MODE` | System under maintenance |

---

## Rate Limiting

### Rate Limits
- **Authenticated requests**: 1000 requests per hour
- **Unauthenticated requests**: 100 requests per hour
- **File uploads**: 50 requests per hour
- **Bulk operations**: 10 requests per hour

### Rate Limit Headers
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1635724800
X-RateLimit-Window: 3600
```

### Rate Limit Exceeded Response
```json
{
    "error": {
        "code": "RATE_LIMIT_EXCEEDED",
        "message": "Rate limit exceeded. Try again in 3600 seconds.",
        "retry_after": 3600
    }
}
```

---

## Pagination

### Request Parameters
- `page` - Page number (default: 1)
- `page_size` - Items per page (default: 20, max: 100)
- `ordering` - Sort field (prefix with `-` for descending)

### Example Request
```http
GET /api/v1/students/?page=2&page_size=50&ordering=-created_at
```

### Response Format
```json
{
    "count": 150,
    "next": "https://api.yourschool.edu/v1/students/?page=3&page_size=50",
    "previous": "https://api.yourschool.edu/v1/students/?page=1&page_size=50",
    "results": [
        {
            "id": 1,
            "student_id": "STU001",
            "first_name": "John",
            "last_name": "Doe"
        }
    ]
}
```

---

## Filtering and Search

### Query Parameters
- `search` - Full-text search across relevant fields
- `{field}` - Exact match filter
- `{field}__contains` - Contains filter
- `{field}__gte` - Greater than or equal
- `{field}__lte` - Less than or equal
- `{field}__in` - In list filter

### Examples
```http
# Search students by name
GET /api/v1/students/?search=john

# Filter by grade
GET /api/v1/students/?grade=5

# Filter by date range
GET /api/v1/students/?admission_date__gte=2023-01-01&admission_date__lte=2023-12-31

# Multiple filters
GET /api/v1/students/?grade=5&status=active&search=john
```

---

## Webhooks

### Supported Events
- `student.created` - New student registered
- `student.updated` - Student information updated
- `grade.published` - New grade published
- `payment.completed` - Payment processed
- `attendance.marked` - Attendance recorded
- `assignment.submitted` - Assignment submitted

### Webhook Configuration
```http
POST /api/v1/webhooks/
{
    "url": "https://your-app.com/webhooks/school-erp",
    "events": ["student.created", "grade.published"],
    "secret": "your_webhook_secret",
    "is_active": true
}
```

### Webhook Payload
```json
{
    "event": "student.created",
    "timestamp": "2023-10-01T10:30:00Z",
    "data": {
        "id": 1,
        "student_id": "STU001",
        "first_name": "John",
        "last_name": "Doe"
    },
    "webhook_id": "wh_123456789"
}
```

---

## SDKs and Libraries

### Official SDKs
- **Python**: `pip install school-erp-sdk`
- **JavaScript/Node.js**: `npm install school-erp-sdk`
- **PHP**: `composer require school-erp/sdk`
- **Java**: Available on Maven Central
- **C#/.NET**: Available on NuGet

### Python SDK Example
```python
from school_erp_sdk import SchoolERPClient

client = SchoolERPClient(
    api_key='your_api_key',
    base_url='https://api.yourschool.edu/v1/'
)

# Get all students
students = client.students.list()

# Get specific student
student = client.students.get(1)

# Create new student
new_student = client.students.create({
    'student_id': 'STU002',
    'first_name': 'Jane',
    'last_name': 'Smith',
    'date_of_birth': '2010-05-20'
})
```

### JavaScript SDK Example
```javascript
import { SchoolERPClient } from 'school-erp-sdk';

const client = new SchoolERPClient({
    apiKey: 'your_api_key',
    baseURL: 'https://api.yourschool.edu/v1/'
});

// Get all students
const students = await client.students.list();

// Get specific student
const student = await client.students.get(1);

// Create new student
const newStudent = await client.students.create({
    student_id: 'STU002',
    first_name: 'Jane',
    last_name: 'Smith',
    date_of_birth: '2010-05-20'
});
```

---

## Examples

### Complete Student Registration Flow

#### 1. Create Parent Account
```http
POST /api/v1/parents/
Content-Type: application/json
Authorization: Api-Key YOUR_API_KEY

{
    "father_name": "Robert Doe",
    "mother_name": "Jane Doe",
    "email": "<EMAIL>",
    "phone": "+**********",
    "address": "123 Main St, City, State"
}
```

#### 2. Register Student
```http
POST /api/v1/students/
Content-Type: application/json
Authorization: Api-Key YOUR_API_KEY

{
    "student_id": "STU001",
    "first_name": "John",
    "last_name": "Doe",
    "date_of_birth": "2010-01-15",
    "gender": "male",
    "nationality": "US",
    "parent": 1,
    "admission_date": "2023-09-01",
    "status": "active"
}
```

#### 3. Enroll in Classes
```http
POST /api/v1/classes/1/students/
Content-Type: application/json
Authorization: Api-Key YOUR_API_KEY

{
    "student": 1,
    "enrollment_date": "2023-09-01"
}
```

### Grade Management Example

#### 1. Create Assignment
```http
POST /api/v1/assignments/
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN

{
    "title": "Math Quiz 1",
    "description": "Basic algebra problems",
    "class": 1,
    "due_date": "2023-10-15",
    "total_marks": 50,
    "submission_type": "online"
}
```

#### 2. Submit Grade
```http
POST /api/v1/academic-grades/
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN

{
    "student": 1,
    "assignment": 1,
    "marks_obtained": 45,
    "total_marks": 50,
    "comments": "Excellent work!"
}
```

### Payment Processing Example

#### 1. Get Student Fees
```http
GET /api/v1/students/1/fees/
Authorization: Bearer YOUR_JWT_TOKEN
```

#### 2. Process Payment
```http
POST /api/v1/payments/
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN

{
    "student_fee": 1,
    "amount": 500.00,
    "payment_method": "credit_card",
    "reference_number": "CC123456"
}
```

---

## Testing

### Test Environment
- **Base URL**: `https://sandbox-api.yourschool.edu/v1/`
- **Test API Key**: Available in developer dashboard
- **Test Data**: Pre-populated with sample data

### Postman Collection
Download our Postman collection for easy API testing:
- [School ERP API Collection](https://api.yourschool.edu/postman/collection.json)

### OpenAPI Specification
- **Swagger UI**: [https://api.yourschool.edu/docs/](https://api.yourschool.edu/docs/)
- **OpenAPI JSON**: [https://api.yourschool.edu/openapi.json](https://api.yourschool.edu/openapi.json)

---

## Changelog

### Version 1.2.0 (2023-10-01)
**Added:**
- New health management endpoints
- Webhook support for real-time notifications
- Bulk operations for student management
- Advanced filtering and search capabilities

**Changed:**
- Improved error response format
- Enhanced rate limiting with per-endpoint limits
- Updated authentication flow

**Fixed:**
- Pagination issues with large datasets
- Timezone handling in date fields

### Version 1.1.0 (2023-08-01)
**Added:**
- Transportation management endpoints
- Library management API
- File upload support for assignments
- Real-time GPS tracking endpoints

**Changed:**
- Increased rate limits for authenticated users
- Improved API response times

### Version 1.0.0 (2023-06-01)
**Added:**
- Initial API release
- Core student management endpoints
- Academic management features
- Financial management API
- Basic authentication and authorization

---

## Support

### Developer Support
- **Email**: <EMAIL>
- **Documentation**: [https://docs.api.yourschool.edu](https://docs.api.yourschool.edu)
- **Status Page**: [https://status.api.yourschool.edu](https://status.api.yourschool.edu)
- **Community Forum**: [https://community.yourschool.edu](https://community.yourschool.edu)

### SLA and Uptime
- **Uptime Guarantee**: 99.9%
- **Response Time**: < 200ms (95th percentile)
- **Support Response**: < 24 hours for technical issues

---

*This documentation is automatically updated with each API release. For the latest version, always refer to the online documentation.*

**API Version**: 1.2.0  
**Documentation Updated**: [Current Date]  
**Next Planned Release**: 1.3.0 (December 2023)