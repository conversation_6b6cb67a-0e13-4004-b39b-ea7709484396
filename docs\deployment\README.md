# School ERP System Deployment Guide

This comprehensive guide covers all aspects of deploying the School ERP System in various environments, from development to production.

## 📋 Table of Contents

1. [Deployment Overview](#deployment-overview)
2. [System Requirements](#system-requirements)
3. [Environment Setup](#environment-setup)
4. [Docker Deployment](#docker-deployment)
5. [Production Deployment](#production-deployment)
6. [CI/CD Pipeline](#cicd-pipeline)
7. [Monitoring and Logging](#monitoring-and-logging)
8. [Backup and Recovery](#backup-and-recovery)
9. [Security Configuration](#security-configuration)
10. [Troubleshooting](#troubleshooting)

---

## Deployment Overview

### Supported Deployment Methods

1. **Docker Containers** (Recommended)
   - Easy scaling and management
   - Consistent environments
   - Built-in orchestration support

2. **Traditional Server Deployment**
   - Direct installation on servers
   - Custom configuration options
   - Legacy system integration

3. **Cloud Platform Deployment**
   - AWS, Azure, Google Cloud
   - Managed services integration
   - Auto-scaling capabilities

4. **Kubernetes Deployment**
   - Container orchestration
   - High availability
   - Advanced scaling options

### Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   Web Servers   │    │    Database     │
│    (Nginx)      │────│   (Django)      │────│  (PostgreSQL)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │     Cache       │              │
         └──────────────│    (Redis)      │──────────────┘
                        └─────────────────┘
                                 │
                        ┌─────────────────┐
                        │   File Storage  │
                        │   (MinIO/S3)    │
                        └─────────────────┘
```

---

## System Requirements

### Minimum Requirements

#### Development Environment
- **CPU**: 2 cores
- **RAM**: 4GB
- **Storage**: 20GB SSD
- **OS**: Ubuntu 20.04+, CentOS 8+, or Windows 10+

#### Production Environment (Small School)
- **CPU**: 4 cores
- **RAM**: 8GB
- **Storage**: 100GB SSD
- **OS**: Ubuntu 20.04 LTS (Recommended)

#### Production Environment (Large School)
- **CPU**: 8+ cores
- **RAM**: 16GB+
- **Storage**: 500GB+ SSD
- **OS**: Ubuntu 20.04 LTS

### Software Dependencies

#### Core Requirements
- **Python**: 3.9+
- **PostgreSQL**: 13+
- **Redis**: 6.0+
- **Nginx**: 1.18+
- **Node.js**: 16+ (for frontend assets)

#### Optional Components
- **Docker**: 20.10+
- **Docker Compose**: 1.29+
- **Kubernetes**: 1.21+
- **Elasticsearch**: 7.10+ (for advanced search)
- **MinIO**: Latest (for file storage)

---

## Environment Setup

### Development Environment

#### 1. Clone Repository
```bash
git clone https://github.com/yourschool/school-erp.git
cd school-erp
```

#### 2. Setup Python Environment
```bash
# Create virtual environment
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

#### 3. Database Setup
```bash
# Install PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# Create database
sudo -u postgres createdb school_erp
sudo -u postgres createuser school_user
sudo -u postgres psql -c "ALTER USER school_user PASSWORD 'your_password';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE school_erp TO school_user;"
```

#### 4. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env
```

#### 5. Initialize Application
```bash
# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Collect static files
python manage.py collectstatic

# Load sample data (optional)
python manage.py loaddata fixtures/sample_data.json
```

#### 6. Start Development Server
```bash
python manage.py runserver
```

### Staging Environment

#### 1. Server Preparation
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y python3-pip python3-venv nginx postgresql redis-server git
```

#### 2. Application Setup
```bash
# Create application user
sudo useradd -m -s /bin/bash schoolerp
sudo usermod -aG sudo schoolerp

# Switch to application user
sudo su - schoolerp

# Clone and setup application
git clone https://github.com/yourschool/school-erp.git
cd school-erp
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

#### 3. Database Configuration
```bash
# Configure PostgreSQL
sudo -u postgres createdb school_erp_staging
sudo -u postgres createuser school_staging_user
sudo -u postgres psql -c "ALTER USER school_staging_user PASSWORD 'staging_password';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE school_erp_staging TO school_staging_user;"
```

#### 4. Web Server Configuration
```bash
# Configure Nginx
sudo nano /etc/nginx/sites-available/school-erp-staging

# Enable site
sudo ln -s /etc/nginx/sites-available/school-erp-staging /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

---

## Docker Deployment

### Docker Compose Setup

#### 1. Docker Compose File
```yaml
# docker-compose.yml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DEBUG=False
      - DATABASE_URL=*****************************************/school_erp
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    volumes:
      - ./media:/app/media
      - ./static:/app/static

  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=school_erp
      - POSTGRES_USER=school_user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./static:/var/www/static
      - ./media:/var/www/media
    depends_on:
      - web

  celery:
    build: .
    command: celery -A school_erp worker -l info
    environment:
      - DATABASE_URL=*****************************************/school_erp
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis

volumes:
  postgres_data:
```

#### 2. Dockerfile
```dockerfile
FROM python:3.9-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# Set work directory
WORKDIR /app

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        postgresql-client \
        build-essential \
        libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt

# Copy project
COPY . /app/

# Collect static files
RUN python manage.py collectstatic --noinput

# Create non-root user
RUN adduser --disabled-password --gecos '' appuser
RUN chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 8000

# Run application
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "school_erp.wsgi:application"]
```

#### 3. Deployment Commands
```bash
# Build and start services
docker-compose up -d --build

# Run migrations
docker-compose exec web python manage.py migrate

# Create superuser
docker-compose exec web python manage.py createsuperuser

# View logs
docker-compose logs -f web

# Scale services
docker-compose up -d --scale web=3
```

### Production Docker Setup

#### 1. Multi-stage Dockerfile
```dockerfile
# Multi-stage build for production
FROM node:16-alpine AS frontend-builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

FROM python:3.9-slim AS backend-builder
WORKDIR /app
RUN apt-get update && apt-get install -y build-essential libpq-dev
COPY requirements.txt .
RUN pip install --user -r requirements.txt

FROM python:3.9-slim AS production
WORKDIR /app

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    libpq5 \
    && rm -rf /var/lib/apt/lists/*

# Copy Python dependencies
COPY --from=backend-builder /root/.local /root/.local
ENV PATH=/root/.local/bin:$PATH

# Copy frontend assets
COPY --from=frontend-builder /app/dist ./static/

# Copy application
COPY . .

# Create non-root user
RUN adduser --disabled-password --gecos '' appuser
RUN chown -R appuser:appuser /app
USER appuser

EXPOSE 8000
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "4", "school_erp.wsgi:application"]
```

#### 2. Production Docker Compose
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  web:
    build:
      context: .
      target: production
    restart: unless-stopped
    environment:
      - DEBUG=False
      - ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
      - DATABASE_URL=postgresql://school_user:${DB_PASSWORD}@db:5432/school_erp
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
    depends_on:
      - db
      - redis
    volumes:
      - media_volume:/app/media
      - static_volume:/app/static

  db:
    image: postgres:13
    restart: unless-stopped
    environment:
      - POSTGRES_DB=school_erp
      - POSTGRES_USER=school_user
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    command: postgres -c 'max_connections=200'

  redis:
    image: redis:6-alpine
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

  nginx:
    image: nginx:alpine
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - static_volume:/var/www/static
      - media_volume:/var/www/media
    depends_on:
      - web

  celery:
    build:
      context: .
      target: production
    restart: unless-stopped
    command: celery -A school_erp worker -l info --concurrency=4
    environment:
      - DATABASE_URL=postgresql://school_user:${DB_PASSWORD}@db:5432/school_erp
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    volumes:
      - media_volume:/app/media

  celery-beat:
    build:
      context: .
      target: production
    restart: unless-stopped
    command: celery -A school_erp beat -l info
    environment:
      - DATABASE_URL=postgresql://school_user:${DB_PASSWORD}@db:5432/school_erp
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis

volumes:
  postgres_data:
  redis_data:
  media_volume:
  static_volume:
```

---

## Production Deployment

### Server Setup

#### 1. Initial Server Configuration
```bash
#!/bin/bash
# server-setup.sh

# Update system
apt update && apt upgrade -y

# Install essential packages
apt install -y curl wget git unzip software-properties-common

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
usermod -aG docker $USER

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Configure firewall
ufw allow ssh
ufw allow 80
ufw allow 443
ufw --force enable

# Setup swap (if needed)
fallocate -l 2G /swapfile
chmod 600 /swapfile
mkswap /swapfile
swapon /swapfile
echo '/swapfile none swap sw 0 0' | tee -a /etc/fstab

# Configure automatic updates
apt install -y unattended-upgrades
dpkg-reconfigure -plow unattended-upgrades
```

#### 2. SSL Certificate Setup
```bash
#!/bin/bash
# ssl-setup.sh

# Install Certbot
apt install -y certbot python3-certbot-nginx

# Obtain SSL certificate
certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Setup auto-renewal
crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

#### 3. Application Deployment
```bash
#!/bin/bash
# deploy.sh

# Set variables
APP_DIR="/opt/school-erp"
BACKUP_DIR="/opt/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Create directories
mkdir -p $APP_DIR $BACKUP_DIR

# Clone or update repository
if [ -d "$APP_DIR/.git" ]; then
    cd $APP_DIR
    git pull origin main
else
    git clone https://github.com/yourschool/school-erp.git $APP_DIR
    cd $APP_DIR
fi

# Backup database
docker-compose exec -T db pg_dump -U school_user school_erp > $BACKUP_DIR/db_backup_$DATE.sql

# Build and deploy
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml build --no-cache
docker-compose -f docker-compose.prod.yml up -d

# Run migrations
docker-compose -f docker-compose.prod.yml exec -T web python manage.py migrate

# Collect static files
docker-compose -f docker-compose.prod.yml exec -T web python manage.py collectstatic --noinput

# Health check
sleep 30
if curl -f http://localhost/health/; then
    echo "Deployment successful!"
else
    echo "Deployment failed! Rolling back..."
    docker-compose -f docker-compose.prod.yml down
    # Restore from backup if needed
fi
```

### Load Balancer Configuration

#### Nginx Configuration
```nginx
# nginx/nginx.conf
upstream school_erp {
    server web:8000;
}

server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /etc/nginx/ssl/fullchain.pem;
    ssl_certificate_key /etc/nginx/ssl/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    client_max_body_size 100M;

    location / {
        proxy_pass http://school_erp;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static/ {
        alias /var/www/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location /media/ {
        alias /var/www/media/;
        expires 1y;
        add_header Cache-Control "public";
    }

    location /health/ {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
```

---

## CI/CD Pipeline

### GitHub Actions Workflow

#### 1. Main Workflow
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:6
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Run tests
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379/0
      run: |
        python manage.py test
        python -m pytest tests/ -v --cov=.
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Login to Docker Hub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
    
    - name: Build and push
      uses: docker/build-push-action@v4
      with:
        context: .
        push: true
        tags: yourschool/school-erp:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          cd /opt/school-erp
          docker-compose -f docker-compose.prod.yml pull
          docker-compose -f docker-compose.prod.yml up -d
          docker-compose -f docker-compose.prod.yml exec -T web python manage.py migrate
          docker-compose -f docker-compose.prod.yml exec -T web python manage.py collectstatic --noinput
```

#### 2. Testing Workflow
```yaml
# .github/workflows/test.yml
name: Run Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.9, 3.10, 3.11]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: Lint with flake8
      run: |
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
    
    - name: Test with pytest
      run: |
        pytest tests/ -v --cov=. --cov-report=xml
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
```

### Deployment Scripts

#### 1. Zero-Downtime Deployment
```bash
#!/bin/bash
# zero-downtime-deploy.sh

set -e

APP_DIR="/opt/school-erp"
BACKUP_DIR="/opt/backups"
DATE=$(date +%Y%m%d_%H%M%S)

cd $APP_DIR

echo "Starting zero-downtime deployment..."

# Create backup
echo "Creating backup..."
docker-compose -f docker-compose.prod.yml exec -T db pg_dump -U school_user school_erp > $BACKUP_DIR/db_backup_$DATE.sql

# Pull latest images
echo "Pulling latest images..."
docker-compose -f docker-compose.prod.yml pull

# Start new containers with different names
echo "Starting new containers..."
docker-compose -f docker-compose.prod.yml -p school-erp-new up -d

# Wait for new containers to be ready
echo "Waiting for new containers to be ready..."
sleep 30

# Health check
if ! curl -f http://localhost:8001/health/; then
    echo "Health check failed! Rolling back..."
    docker-compose -f docker-compose.prod.yml -p school-erp-new down
    exit 1
fi

# Switch traffic to new containers
echo "Switching traffic..."
# Update load balancer configuration here

# Stop old containers
echo "Stopping old containers..."
docker-compose -f docker-compose.prod.yml -p school-erp down

# Rename new containers
echo "Finalizing deployment..."
docker-compose -f docker-compose.prod.yml -p school-erp-new down
docker-compose -f docker-compose.prod.yml up -d

echo "Deployment completed successfully!"
```

#### 2. Rollback Script
```bash
#!/bin/bash
# rollback.sh

set -e

APP_DIR="/opt/school-erp"
BACKUP_DIR="/opt/backups"

if [ -z "$1" ]; then
    echo "Usage: $0 <backup_date>"
    echo "Available backups:"
    ls -la $BACKUP_DIR/db_backup_*.sql
    exit 1
fi

BACKUP_DATE=$1
BACKUP_FILE="$BACKUP_DIR/db_backup_$BACKUP_DATE.sql"

if [ ! -f "$BACKUP_FILE" ]; then
    echo "Backup file not found: $BACKUP_FILE"
    exit 1
fi

cd $APP_DIR

echo "Rolling back to backup: $BACKUP_DATE"

# Stop current services
docker-compose -f docker-compose.prod.yml down

# Restore database
echo "Restoring database..."
docker-compose -f docker-compose.prod.yml up -d db
sleep 10
docker-compose -f docker-compose.prod.yml exec -T db dropdb -U school_user school_erp
docker-compose -f docker-compose.prod.yml exec -T db createdb -U school_user school_erp
docker-compose -f docker-compose.prod.yml exec -T db psql -U school_user school_erp < $BACKUP_FILE

# Start services
docker-compose -f docker-compose.prod.yml up -d

echo "Rollback completed!"
```

---

## Monitoring and Logging

### Application Monitoring

#### 1. Health Check Endpoint
```python
# health/views.py
from django.http import JsonResponse
from django.db import connection
from django.core.cache import cache
import redis

def health_check(request):
    """Comprehensive health check endpoint"""
    status = {
        'status': 'healthy',
        'timestamp': timezone.now().isoformat(),
        'checks': {}
    }
    
    # Database check
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        status['checks']['database'] = 'healthy'
    except Exception as e:
        status['checks']['database'] = f'unhealthy: {str(e)}'
        status['status'] = 'unhealthy'
    
    # Cache check
    try:
        cache.set('health_check', 'ok', 30)
        cache.get('health_check')
        status['checks']['cache'] = 'healthy'
    except Exception as e:
        status['checks']['cache'] = f'unhealthy: {str(e)}'
        status['status'] = 'unhealthy'
    
    # Celery check
    try:
        from celery import current_app
        inspect = current_app.control.inspect()
        stats = inspect.stats()
        if stats:
            status['checks']['celery'] = 'healthy'
        else:
            status['checks']['celery'] = 'no workers'
    except Exception as e:
        status['checks']['celery'] = f'unhealthy: {str(e)}'
    
    return JsonResponse(status)
```

#### 2. Prometheus Metrics
```python
# monitoring/metrics.py
from prometheus_client import Counter, Histogram, Gauge
from django.middleware.base import BaseMiddleware

# Metrics
REQUEST_COUNT = Counter('django_requests_total', 'Total requests', ['method', 'endpoint', 'status'])
REQUEST_LATENCY = Histogram('django_request_duration_seconds', 'Request latency')
ACTIVE_USERS = Gauge('django_active_users', 'Active users')

class PrometheusMiddleware(BaseMiddleware):
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)

    def __call__(self, request):
        start_time = time.time()
        
        response = self.get_response(request)
        
        # Record metrics
        REQUEST_COUNT.labels(
            method=request.method,
            endpoint=request.path,
            status=response.status_code
        ).inc()
        
        REQUEST_LATENCY.observe(time.time() - start_time)
        
        return response
```

### Logging Configuration

#### 1. Django Logging Settings
```python
# settings/logging.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
        'json': {
            '()': 'pythonjsonlogger.jsonlogger.JsonFormatter',
            'format': '%(levelname)s %(asctime)s %(module)s %(process)d %(thread)d %(message)s'
        }
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/school-erp/django.log',
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
            'formatter': 'json',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
        'syslog': {
            'level': 'INFO',
            'class': 'logging.handlers.SysLogHandler',
            'address': '/dev/log',
            'formatter': 'json',
        }
    },
    'root': {
        'handlers': ['console', 'file'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['file', 'syslog'],
            'level': 'INFO',
            'propagate': False,
        },
        'school_erp': {
            'handlers': ['file', 'console'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}
```

#### 2. Log Aggregation with ELK Stack
```yaml
# docker-compose.monitoring.yml
version: '3.8'

services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.14.0
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"

  logstash:
    image: docker.elastic.co/logstash/logstash:7.14.0
    volumes:
      - ./logstash/pipeline:/usr/share/logstash/pipeline
      - ./logstash/config:/usr/share/logstash/config
    ports:
      - "5044:5044"
    depends_on:
      - elasticsearch

  kibana:
    image: docker.elastic.co/kibana/kibana:7.14.0
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch

  filebeat:
    image: docker.elastic.co/beats/filebeat:7.14.0
    user: root
    volumes:
      - ./filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/log/school-erp:/var/log/school-erp:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    depends_on:
      - logstash

volumes:
  elasticsearch_data:
```

---

## Backup and Recovery

### Database Backup Strategy

#### 1. Automated Backup Script
```bash
#!/bin/bash
# backup-database.sh

set -e

# Configuration
DB_NAME="school_erp"
DB_USER="school_user"
BACKUP_DIR="/opt/backups/database"
RETENTION_DAYS=30
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Create backup
echo "Creating database backup..."
docker-compose exec -T db pg_dump -U $DB_USER -h localhost $DB_NAME | gzip > $BACKUP_DIR/backup_$DATE.sql.gz

# Verify backup
if [ -f "$BACKUP_DIR/backup_$DATE.sql.gz" ]; then
    echo "Backup created successfully: backup_$DATE.sql.gz"
    
    # Test backup integrity
    gunzip -t $BACKUP_DIR/backup_$DATE.sql.gz
    if [ $? -eq 0 ]; then
        echo "Backup integrity verified"
    else
        echo "Backup integrity check failed!"
        exit 1
    fi
else
    echo "Backup creation failed!"
    exit 1
fi

# Upload to cloud storage (optional)
if [ ! -z "$AWS_S3_BUCKET" ]; then
    echo "Uploading to S3..."
    aws s3 cp $BACKUP_DIR/backup_$DATE.sql.gz s3://$AWS_S3_BUCKET/database-backups/
fi

# Clean old backups
echo "Cleaning old backups..."
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +$RETENTION_DAYS -delete

echo "Backup process completed"
```

#### 2. Backup Cron Job
```bash
# Add to crontab
# Daily backup at 2 AM
0 2 * * * /opt/scripts/backup-database.sh >> /var/log/backup.log 2>&1

# Weekly full backup at 3 AM on Sundays
0 3 * * 0 /opt/scripts/full-backup.sh >> /var/log/backup.log 2>&1
```

### File System Backup

#### 1. Media Files Backup
```bash
#!/bin/bash
# backup-media.sh

set -e

MEDIA_DIR="/opt/school-erp/media"
BACKUP_DIR="/opt/backups/media"
DATE=$(date +%Y%m%d_%H%M%S)

# Create incremental backup
rsync -av --link-dest=$BACKUP_DIR/latest $MEDIA_DIR/ $BACKUP_DIR/backup_$DATE/

# Update latest symlink
rm -f $BACKUP_DIR/latest
ln -s backup_$DATE $BACKUP_DIR/latest

# Upload to cloud storage
if [ ! -z "$AWS_S3_BUCKET" ]; then
    aws s3 sync $BACKUP_DIR/backup_$DATE/ s3://$AWS_S3_BUCKET/media-backups/backup_$DATE/
fi

echo "Media backup completed: backup_$DATE"
```

### Disaster Recovery

#### 1. Recovery Script
```bash
#!/bin/bash
# disaster-recovery.sh

set -e

if [ -z "$1" ]; then
    echo "Usage: $0 <backup_date>"
    exit 1
fi

BACKUP_DATE=$1
BACKUP_DIR="/opt/backups"
APP_DIR="/opt/school-erp"

echo "Starting disaster recovery for backup: $BACKUP_DATE"

# Stop services
cd $APP_DIR
docker-compose -f docker-compose.prod.yml down

# Restore database
echo "Restoring database..."
if [ -f "$BACKUP_DIR/database/backup_$BACKUP_DATE.sql.gz" ]; then
    docker-compose -f docker-compose.prod.yml up -d db
    sleep 10
    
    # Drop and recreate database
    docker-compose -f docker-compose.prod.yml exec -T db dropdb -U school_user school_erp
    docker-compose -f docker-compose.prod.yml exec -T db createdb -U school_user school_erp
    
    # Restore from backup
    gunzip -c $BACKUP_DIR/database/backup_$BACKUP_DATE.sql.gz | \
        docker-compose -f docker-compose.prod.yml exec -T db psql -U school_user school_erp
    
    echo "Database restored successfully"
else
    echo "Database backup not found!"
    exit 1
fi

# Restore media files
echo "Restoring media files..."
if [ -d "$BACKUP_DIR/media/backup_$BACKUP_DATE" ]; then
    rm -rf $APP_DIR/media/*
    cp -r $BACKUP_DIR/media/backup_$BACKUP_DATE/* $APP_DIR/media/
    echo "Media files restored successfully"
else
    echo "Media backup not found!"
fi

# Start services
echo "Starting services..."
docker-compose -f docker-compose.prod.yml up -d

# Verify recovery
sleep 30
if curl -f http://localhost/health/; then
    echo "Disaster recovery completed successfully!"
else
    echo "Recovery verification failed!"
    exit 1
fi
```

---

## Security Configuration

### SSL/TLS Configuration

#### 1. SSL Certificate Management
```bash
#!/bin/bash
# ssl-management.sh

# Install Certbot
apt install -y certbot python3-certbot-nginx

# Obtain certificate
certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Setup auto-renewal
cat > /etc/cron.d/certbot << EOF
0 12 * * * root test -x /usr/bin/certbot -a \! -d /run/systemd/system && perl -e 'sleep int(rand(43200))' && certbot -q renew
EOF
```

#### 2. Security Headers
```nginx
# nginx/security.conf
# Security headers
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
add_header Strict-Transport-Security "max-age=********; includeSubDomains" always;

# Hide server information
server_tokens off;

# Rate limiting
limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;

location /accounts/login/ {
    limit_req zone=login burst=5 nodelay;
    proxy_pass http://school_erp;
}

location /api/ {
    limit_req zone=api burst=20 nodelay;
    proxy_pass http://school_erp;
}
```

### Firewall Configuration

#### 1. UFW Setup
```bash
#!/bin/bash
# firewall-setup.sh

# Reset firewall
ufw --force reset

# Default policies
ufw default deny incoming
ufw default allow outgoing

# Allow SSH (change port if needed)
ufw allow 22/tcp

# Allow HTTP and HTTPS
ufw allow 80/tcp
ufw allow 443/tcp

# Allow specific IPs for admin access
ufw allow from ***********/24 to any port 22

# Enable firewall
ufw --force enable

# Show status
ufw status verbose
```

#### 2. Fail2Ban Configuration
```ini
# /etc/fail2ban/jail.local
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
port = http,https
logpath = /var/log/nginx/error.log

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 10

[django-auth]
enabled = true
filter = django-auth
port = http,https
logpath = /var/log/school-erp/django.log
maxretry = 5
```

---

## Troubleshooting

### Common Issues

#### 1. Database Connection Issues
```bash
# Check database status
docker-compose exec db pg_isready -U school_user

# View database logs
docker-compose logs db

# Connect to database manually
docker-compose exec db psql -U school_user school_erp

# Check connections
docker-compose exec db psql -U school_user -c "SELECT * FROM pg_stat_activity;"
```

#### 2. Memory Issues
```bash
# Check memory usage
free -h
docker stats

# Check for memory leaks
docker-compose exec web python manage.py shell
>>> import psutil
>>> psutil.virtual_memory()

# Restart services if needed
docker-compose restart web celery
```

#### 3. Performance Issues
```bash
# Check slow queries
docker-compose exec db psql -U school_user school_erp -c "
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;"

# Check Redis performance
docker-compose exec redis redis-cli info stats

# Monitor application performance
docker-compose exec web python manage.py shell
>>> from django.db import connection
>>> print(connection.queries)
```

### Debugging Tools

#### 1. Debug Container
```dockerfile
# Dockerfile.debug
FROM python:3.9-slim

# Install debugging tools
RUN apt-get update && apt-get install -y \
    htop \
    strace \
    tcpdump \
    netcat \
    curl \
    vim

# Copy application
COPY . /app
WORKDIR /app

# Install dependencies
RUN pip install -r requirements.txt
RUN pip install ipdb django-debug-toolbar

CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]
```

#### 2. Log Analysis Scripts
```bash
#!/bin/bash
# analyze-logs.sh

LOG_FILE="/var/log/school-erp/django.log"

echo "=== Error Summary ==="
grep -i error $LOG_FILE | tail -20

echo "=== Performance Issues ==="
grep -i "slow" $LOG_FILE | tail -10

echo "=== Recent Activity ==="
tail -50 $LOG_FILE

echo "=== Top Error Types ==="
grep -i error $LOG_FILE | awk '{print $4}' | sort | uniq -c | sort -nr | head -10
```

### Maintenance Tasks

#### 1. Database Maintenance
```bash
#!/bin/bash
# db-maintenance.sh

# Vacuum and analyze
docker-compose exec db psql -U school_user school_erp -c "VACUUM ANALYZE;"

# Reindex
docker-compose exec db psql -U school_user school_erp -c "REINDEX DATABASE school_erp;"

# Update statistics
docker-compose exec db psql -U school_user school_erp -c "ANALYZE;"

# Check database size
docker-compose exec db psql -U school_user school_erp -c "
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public' 
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;"
```

#### 2. Cache Maintenance
```bash
#!/bin/bash
# cache-maintenance.sh

# Clear Django cache
docker-compose exec web python manage.py shell -c "
from django.core.cache import cache
cache.clear()
print('Django cache cleared')
"

# Redis maintenance
docker-compose exec redis redis-cli FLUSHDB
docker-compose exec redis redis-cli BGREWRITEAOF

echo "Cache maintenance completed"
```

---

## Support and Documentation

### Getting Help
- **Documentation**: [https://docs.yourschool.edu/deployment/](https://docs.yourschool.edu/deployment/)
- **Support Email**: <EMAIL>
- **Community Forum**: [https://community.yourschool.edu/deployment](https://community.yourschool.edu/deployment)
- **Emergency Support**: +1 (555) 123-4567

### Additional Resources
- **Docker Documentation**: [https://docs.docker.com/](https://docs.docker.com/)
- **Django Deployment**: [https://docs.djangoproject.com/en/stable/howto/deployment/](https://docs.djangoproject.com/en/stable/howto/deployment/)
- **PostgreSQL Documentation**: [https://www.postgresql.org/docs/](https://www.postgresql.org/docs/)
- **Nginx Documentation**: [https://nginx.org/en/docs/](https://nginx.org/en/docs/)

---

*This deployment guide is regularly updated to reflect best practices and new features. For the latest version, always refer to the official documentation.*

**Last Updated**: [Current Date]  
**Version**: 2.0  
**Supported Platforms**: Ubuntu 20.04+, CentOS 8+, Docker 20.10+