# School ERP Performance Guide

## 📋 Table of Contents

1. [Overview](#overview)
2. [Caching System](#caching-system)
3. [Monitoring & Alerting](#monitoring--alerting)
4. [Database Optimization](#database-optimization)
5. [Scalability Features](#scalability-features)
6. [Configuration](#configuration)
7. [Usage Examples](#usage-examples)
8. [Troubleshooting](#troubleshooting)

---

## Overview

The School ERP system includes comprehensive performance optimization and monitoring capabilities:

- **Redis Caching System** - Multi-level caching with automatic invalidation
- **Real-time Monitoring** - System metrics, alerts, and dashboards
- **Database Optimization** - Query optimization and indexing
- **Scalability Features** - Load balancing and auto-scaling

---

## Caching System

### Location
- **Main Module**: `core/performance/cache.py`
- **Invalidation**: `core/cache_invalidation.py`

### Features
- Redis-based caching with Django fallback
- Model, query, view, and report caching
- Automatic cache invalidation on model changes
- School-specific cache isolation
- Performance monitoring

### Usage

#### Basic Caching
```python
from core.performance.cache import cache_manager

# Set cache
cache_manager.set('key', 'value', timeout='medium')

# Get cache
value = cache_manager.get('key', default='default_value')

# Delete cache
cache_manager.delete('key')
```

#### Decorator Caching
```python
from core.performance.cache import cache_result

@cache_result(timeout='long', key_prefix='student')
def get_student_data(student_id):
    return Student.objects.get(id=student_id)
```

#### Model Caching
```python
from core.performance.cache import ModelCacheManager

# Cache model instance
ModelCacheManager.cache_model_instance(student, timeout='long')

# Get cached instance
cached_student = ModelCacheManager.get_cached_model_instance(Student, student_id)
```

#### School-specific Caching
```python
from core.performance.cache import SchoolCacheManager

# Cache school data
SchoolCacheManager.cache_school_data(school_id, 'dashboard_stats', data)

# Get school data
stats = SchoolCacheManager.get_cached_school_data(school_id, 'dashboard_stats')
```

### Configuration
```python
# settings.py
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}
```

---

## Monitoring & Alerting

### Location
- **Monitoring**: `core/performance/monitoring.py`
- **Alerting**: `core/monitoring/alerting.py`
- **Dashboards**: `core/monitoring/dashboards.py`

### Features
- Real-time system metrics collection
- Multi-channel alerting (email, log, cache, Slack)
- Configurable alert rules and thresholds
- Web-based monitoring dashboards
- Performance history and trends

### Usage

#### Check and Send Alerts
```python
from core.monitoring.alerting import check_and_send_alerts

# Check metrics and send alerts if needed
alerts_sent = check_and_send_alerts()
```

#### Add Custom Alert Rule
```python
from core.monitoring.alerting import add_custom_alert_rule

add_custom_alert_rule(
    name="high_response_time",
    severity="warning",
    metric_path="app_metrics.response_time.avg",
    threshold=2000,  # 2 seconds
    description="High average response time detected"
)
```

#### Get Recent Alerts
```python
from core.monitoring.alerting import get_recent_alerts

# Get alerts from last 24 hours
recent_alerts = get_recent_alerts(hours=24)
```

#### Manual Metrics Collection
```python
from core.monitoring.alerting import metric_collector

# Collect all current metrics
metrics = metric_collector.collect_all_metrics()
```

### Alert Configuration
```python
# settings.py
EMAIL_ALERTS_ENABLED = True
ALERT_EMAIL_RECIPIENTS = ['<EMAIL>', '<EMAIL>']
SLACK_WEBHOOK_URL = 'https://hooks.slack.com/services/...'  # Optional
```

### Management Commands
```bash
# Start/stop monitoring service
python manage.py performance start
python manage.py performance stop
python manage.py performance status
python manage.py performance collect
```

### Dashboard URLs
- **Monitoring Dashboard**: `/admin/monitoring/dashboard/`
- **Metrics API**: `/admin/monitoring/api/metrics/`
- **Alerts API**: `/admin/monitoring/api/alerts/`
- **Performance Report**: `/admin/monitoring/api/performance-report/`

---

## Database Optimization

### Location
- **Main Module**: `core/performance/database_optimization.py`

### Features
- Automated query optimization with select_related/prefetch_related
- Database index recommendations and creation
- Query performance monitoring
- Connection pool management
- Bulk operations optimization

### Usage

#### Query Optimization
```python
from core.performance.database_optimization import QueryOptimizer

# Optimize queryset automatically
queryset = Student.objects.filter(school=school)
optimized_queryset = QueryOptimizer.optimize_queryset(queryset)

# Get optimized queryset directly
students = QueryOptimizer.get_optimized_queryset(Student, school=school)
```

#### Predefined Optimized Queries
```python
from core.performance.database_optimization import (
    optimize_student_queries,
    optimize_class_queries,
    optimize_financial_queries
)

# Get optimized student queryset
students = optimize_student_queries(school_id=1)

# Get optimized class queryset
classes = optimize_class_queries(academic_year_id=1)

# Get optimized financial queryset
fees = optimize_financial_queries(school_id=1, academic_year_id=1)
```

#### Bulk Operations
```python
from core.performance.database_optimization import QueryOptimizer

# Bulk create with batching
students = [Student(...) for _ in range(1000)]
created_students = QueryOptimizer.bulk_create_optimized(Student, students, batch_size=500)

# Bulk update with batching
QueryOptimizer.bulk_update_optimized(students, ['status', 'updated_at'], batch_size=500)
```

#### Index Management
```python
from core.performance.database_optimization import DatabaseIndexManager

# Check for missing indexes
missing_indexes = DatabaseIndexManager.get_missing_indexes()

# Create missing indexes (dry run)
sql_statements = DatabaseIndexManager.create_missing_indexes(dry_run=True)

# Create missing indexes (execute)
sql_statements = DatabaseIndexManager.create_missing_indexes(dry_run=False)
```

#### Performance Monitoring
```python
from core.performance.database_optimization import performance_monitor

# Monitor query execution
with performance_monitor.monitor_query('student_list'):
    students = Student.objects.filter(school=school).select_related('parent')

# Get performance report
report = performance_monitor.get_performance_report()

# Get slowest queries
slowest = performance_monitor.get_slowest_queries(limit=10)
```

---

## Scalability Features

### Location
- **Main Module**: `core/scalability/load_balancing.py`

### Features
- Load balancing with multiple strategies
- Automated health checking
- CPU-based auto-scaling
- Resource monitoring and trend detection
- Scaling history and audit trail

### Usage

#### Load Balancer
```python
from core.scalability.load_balancing import load_balancer

# Add servers
load_balancer.add_server('http://server1.example.com', weight=1)
load_balancer.add_server('http://server2.example.com', weight=2)

# Get next server
server = load_balancer.get_next_server(strategy='least_connections')

# Update server stats
load_balancer.update_server_stats('http://server1.example.com', response_time=0.5, success=True)
```

#### Health Checking
```python
from core.scalability.load_balancing import health_checker

# Start health checking
health_checker.start()

# Stop health checking
health_checker.stop()
```

#### Auto-scaling
```python
from core.scalability.load_balancing import auto_scaler, CPUScalingRule

# Add custom scaling rule
rule = CPUScalingRule(
    name="custom_scale_up",
    cpu_threshold=75,
    action_type="scale_up",
    duration_minutes=3
)
auto_scaler.add_scaling_rule(rule)

# Check scaling conditions
metrics = {'system': {'cpu_percent': 85}}
actions = auto_scaler.check_scaling_conditions(metrics)

# Execute scaling actions
auto_scaler.execute_scaling_actions(actions)
```

#### Resource Monitoring
```python
from core.scalability.load_balancing import resource_monitor

# Record metrics
metrics = {'system': {'cpu_percent': 75, 'memory_percent': 60}}
resource_monitor.record_metrics(metrics)

# Get average metrics
avg_metrics = resource_monitor.get_average_metrics(duration_minutes=10)

# Detect trends
trend = resource_monitor.detect_trends('system.cpu_percent', duration_minutes=30)
```

### Health Check Endpoint
- **URL**: `/health/`
- **Method**: GET
- **Response**: JSON with system health status

---

## Configuration

### Redis Configuration
```python
# settings.py
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}
```

### Performance Monitoring
```python
# settings.py
PERFORMANCE_MONITORING_ENABLED = True

# Add performance middleware
MIDDLEWARE = [
    # ... other middleware
    'core.performance.monitoring.PerformanceMiddleware',
    # ... other middleware
]
```

### Alert Configuration
```python
# settings.py
EMAIL_ALERTS_ENABLED = True
ALERT_EMAIL_RECIPIENTS = [
    '<EMAIL>',
    '<EMAIL>'
]

# Optional Slack integration
SLACK_WEBHOOK_URL = 'https://hooks.slack.com/services/YOUR/WEBHOOK/URL'
```

### Database Optimization
```python
# settings.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'school_erp',
        'USER': 'postgres',
        'PASSWORD': 'password',
        'HOST': 'localhost',
        'PORT': '5432',
        'OPTIONS': {
            'MAX_CONNS': 20,  # Connection pooling
        }
    }
}
```

---

## Usage Examples

### Complete Performance Setup
```python
# 1. Enable caching
from core.performance.cache import cache_manager

# 2. Start monitoring
from core.performance.monitoring import monitoring_service
monitoring_service.start_monitoring()

# 3. Set up alerting
from core.monitoring.alerting import add_custom_alert_rule
add_custom_alert_rule(
    name="high_student_load",
    severity="warning",
    metric_path="application.total_students",
    threshold=1000,
    description="High student count detected"
)

# 4. Optimize database queries
from core.performance.database_optimization import optimize_student_queries
students = optimize_student_queries(school_id=1)

# 5. Set up load balancing
from core.scalability.load_balancing import load_balancer
load_balancer.add_server('http://app1.school.com')
load_balancer.add_server('http://app2.school.com')
```

### Daily Operations
```python
# Check system health
from core.monitoring.alerting import check_and_send_alerts
alerts_sent = check_and_send_alerts()

# Get performance metrics
from core.monitoring.alerting import metric_collector
current_metrics = metric_collector.collect_all_metrics()

# Check cache performance
from core.performance.cache import CachePerformanceMonitor
hit_rate = CachePerformanceMonitor.get_hit_rate()
memory_usage = CachePerformanceMonitor.get_memory_usage()

# Review database performance
from core.performance.database_optimization import performance_monitor
db_report = performance_monitor.get_performance_report()
```

---

## Troubleshooting

### Common Issues

#### High Memory Usage
```python
# Check cache memory usage
from core.performance.cache import cache_manager
stats = cache_manager.get_stats()
print(f"Cache memory: {stats.get('used_memory', 'unknown')}")

# Clear cache if needed
cache_manager.clear_all()
```

#### Slow Database Queries
```python
# Check for missing indexes
from core.performance.database_optimization import DatabaseIndexManager
missing = DatabaseIndexManager.get_missing_indexes()

# Create recommended indexes
if missing:
    DatabaseIndexManager.create_missing_indexes(dry_run=False)
```

#### Alert Spam
```python
# Check alert cooldown settings
from core.monitoring.alerting import alert_manager
print(alert_manager.cooldown_periods)

# Clear alerts if needed
from core.monitoring.alerting import clear_alerts
clear_alerts()
```

#### Performance Degradation
```python
# Check system metrics
from core.monitoring.alerting import metric_collector
metrics = metric_collector.collect_all_metrics()

# Review performance trends
from core.scalability.load_balancing import resource_monitor
cpu_trend = resource_monitor.detect_trends('system.cpu_percent')
memory_trend = resource_monitor.detect_trends('system.memory_percent')
```

### Monitoring Commands
```bash
# Check performance monitoring status
python manage.py performance status

# Collect current metrics
python manage.py performance collect

# Check database indexes
python manage.py check_indexes

# View cache statistics
python manage.py cache_stats
```

### Log Files
- **Performance logs**: `logs/performance.log`
- **Alert logs**: `logs/alerts.log`
- **Database logs**: `logs/database.log`

---

## Performance Metrics

### Target Metrics
- **Cache Hit Rate**: >80%
- **Database Query Time**: <100ms (optimized queries)
- **Response Time**: <500ms (cached responses)
- **CPU Usage**: <80% (normal operations)
- **Memory Usage**: <85% (normal operations)
- **Database Connections**: <80% of pool size

### Monitoring Frequency
- **System Metrics**: Every 60 seconds
- **Database Metrics**: Every 60 seconds
- **Application Metrics**: Every 60 seconds
- **Health Checks**: Every 30 seconds
- **Alert Checks**: Every 60 seconds

---

*This guide covers the complete performance optimization and monitoring system. For additional help, check the troubleshooting section or contact the development team.*