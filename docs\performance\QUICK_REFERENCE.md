# Performance System Quick Reference

## 🚀 Quick Start Commands

### Start Monitoring
```bash
python manage.py performance start
```

### Check System Health
```python
from core.monitoring.alerting import check_and_send_alerts
alerts_sent = check_and_send_alerts()
```

### Cache Data
```python
from core.performance.cache import cache_result

@cache_result(timeout='medium')
def expensive_function():
    return heavy_computation()
```

---

## 📊 Key Modules & Locations

| Feature | Location | Purpose |
|---------|----------|---------|
| **Caching** | `core/performance/cache.py` | Redis caching system |
| **Monitoring** | `core/performance/monitoring.py` | Performance metrics |
| **Alerting** | `core/monitoring/alerting.py` | Alert management |
| **Dashboards** | `core/monitoring/dashboards.py` | Web monitoring |
| **DB Optimization** | `core/performance/database_optimization.py` | Query optimization |
| **Load Balancing** | `core/scalability/load_balancing.py` | Scalability features |

---

## ⚡ Common Usage Patterns

### Caching
```python
# Basic caching
from core.performance.cache import cache_manager
cache_manager.set('key', data, timeout='long')
data = cache_manager.get('key')

# Model caching
from core.performance.cache import ModelCacheManager
ModelCacheManager.cache_model_instance(student)
cached = ModelCacheManager.get_cached_model_instance(Student, pk)

# School-specific caching
from core.performance.cache import SchoolCacheManager
SchoolCacheManager.cache_school_data(school_id, 'stats', data)
```

### Monitoring
```python
# Get current metrics
from core.monitoring.alerting import metric_collector
metrics = metric_collector.collect_all_metrics()

# Add custom alert
from core.monitoring.alerting import add_custom_alert_rule
add_custom_alert_rule('high_cpu', 'warning', 'system.cpu_percent', 80)

# Get recent alerts
from core.monitoring.alerting import get_recent_alerts
alerts = get_recent_alerts(hours=24)
```

### Database Optimization
```python
# Optimize queries
from core.performance.database_optimization import optimize_student_queries
students = optimize_student_queries(school_id=1)

# Check missing indexes
from core.performance.database_optimization import DatabaseIndexManager
missing = DatabaseIndexManager.get_missing_indexes()
```

---

## 🔧 Configuration Snippets

### Redis Cache
```python
# settings.py
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
    }
}
```

### Email Alerts
```python
# settings.py
EMAIL_ALERTS_ENABLED = True
ALERT_EMAIL_RECIPIENTS = ['<EMAIL>']
```

### Performance Middleware
```python
# settings.py
MIDDLEWARE = [
    'core.performance.monitoring.PerformanceMiddleware',
    # ... other middleware
]
```

---

## 📈 Monitoring URLs

| URL | Purpose |
|-----|---------|
| `/admin/monitoring/dashboard/` | Main monitoring dashboard |
| `/admin/monitoring/api/metrics/` | Current metrics API |
| `/admin/monitoring/api/alerts/` | Alerts management API |
| `/health/` | Health check endpoint |

---

## 🚨 Alert Severity Levels

| Level | Threshold | Cooldown |
|-------|-----------|----------|
| **Info** | Informational | 5 minutes |
| **Warning** | Needs attention | 10 minutes |
| **Error** | Requires action | 30 minutes |
| **Critical** | Immediate action | 1 hour |

---

## 📊 Default Alert Rules

| Rule | Metric | Threshold | Severity |
|------|--------|-----------|----------|
| High CPU | `system.cpu_percent` | 80% | Warning |
| Critical CPU | `system.cpu_percent` | 95% | Critical |
| High Memory | `system.memory_percent` | 85% | Warning |
| High Disk | `system.disk_percent` | 90% | Error |
| High DB Connections | `database.connection_usage_percent` | 80% | Warning |

---

## 🛠️ Management Commands

```bash
# Performance monitoring
python manage.py performance start|stop|status|collect

# Database optimization
python manage.py check_indexes
python manage.py create_indexes

# Cache management
python manage.py clear_cache
python manage.py cache_stats
```

---

## 🎯 Performance Targets

| Metric | Target | Critical |
|--------|--------|----------|
| **Cache Hit Rate** | >80% | <60% |
| **Response Time** | <500ms | >2s |
| **DB Query Time** | <100ms | >1s |
| **CPU Usage** | <80% | >95% |
| **Memory Usage** | <85% | >95% |
| **Disk Usage** | <90% | >95% |

---

## 🔍 Troubleshooting Quick Fixes

### High Memory
```python
from core.performance.cache import cache_manager
cache_manager.clear_all()
```

### Slow Queries
```python
from core.performance.database_optimization import DatabaseIndexManager
DatabaseIndexManager.create_missing_indexes(dry_run=False)
```

### Alert Spam
```python
from core.monitoring.alerting import clear_alerts
clear_alerts()
```

### Check System Health
```python
from core.monitoring.alerting import metric_collector
metrics = metric_collector.collect_all_metrics()
print(f"CPU: {metrics['system']['cpu_percent']}%")
print(f"Memory: {metrics['system']['memory_percent']}%")
```

---

*For detailed documentation, see `docs/performance/PERFORMANCE_GUIDE.md`*