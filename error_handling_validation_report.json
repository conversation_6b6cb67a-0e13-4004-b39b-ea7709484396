{"timestamp": "2025-08-14T05:37:34.703905", "summary": {"total": 16, "passed": 16, "failed": 0, "errors": 0, "success_rate": 100.0}, "results": [{"test_name": "File Exists", "status": "PASS", "details": "static/js/toast-notifications.js found"}, {"test_name": "File Content", "status": "PASS", "details": "All required content found in static/js/toast-notifications.js"}, {"test_name": "File Exists", "status": "PASS", "details": "static/js/error-recovery.js found"}, {"test_name": "File Content", "status": "PASS", "details": "All required content found in static/js/error-recovery.js"}, {"test_name": "File Exists", "status": "PASS", "details": "templates/base.html found"}, {"test_name": "File Content", "status": "PASS", "details": "All required content found in templates/base.html"}, {"test_name": "File Exists", "status": "PASS", "details": "templates/library/borrowing_system.html found"}, {"test_name": "File Content", "status": "PASS", "details": "All required content found in templates/library/borrowing_system.html"}, {"test_name": "File Exists", "status": "PASS", "details": "library/views.py found"}, {"test_name": "File Content", "status": "PASS", "details": "All required content found in library/views.py"}, {"test_name": "File Exists", "status": "PASS", "details": "core/views.py found"}, {"test_name": "File Content", "status": "PASS", "details": "All required content found in core/views.py"}, {"test_name": "File Exists", "status": "PASS", "details": "core/urls.py found"}, {"test_name": "File Content", "status": "PASS", "details": "All required content found in core/urls.py"}, {"test_name": "File Content", "status": "PASS", "details": "All required content found in static/js/toast-notifications.js"}, {"test_name": "File Content", "status": "PASS", "details": "All required content found in static/js/error-recovery.js"}]}