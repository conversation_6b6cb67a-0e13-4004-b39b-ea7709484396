"""
Budget Management Services

This module provides comprehensive budget management services including:
- Budget planning and creation
- Budget approval workflows
- Budget monitoring and variance analysis
- Budget alerts and notifications
- Budget reporting and analytics
"""

import logging
from datetime import date, datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Tuple
from django.db import transaction
from django.db.models import Sum, Q, F
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.contrib.auth.models import User
from django.template.loader import render_to_string
from django.core.mail import send_mail
from django.conf import settings

from .models import (
    Budget, BudgetItem, BudgetAlert, BudgetRevision, BudgetApprovalWorkflow,
    Account, CostCenter, FinancialYear, TransactionEntry
)

logger = logging.getLogger(__name__)


class BudgetService:
    """
    Core budget management service providing comprehensive budget functionality
    """
    
    @staticmethod
    def create_budget(school, name: str, budget_type: str, financial_year, 
                     start_date: date, end_date: date, description: str = None,
                     name_ar: str = None, created_by: User = None) -> Budget:
        """
        Create a new budget with validation
        """
        try:
            with transaction.atomic():
                # Validate dates
                if start_date >= end_date:
                    raise ValidationError("End date must be after start date")
                
                # Check for overlapping budgets of same type
                overlapping = Budget.objects.filter(
                    school=school,
                    budget_type=budget_type,
                    financial_year=financial_year,
                    status__in=['active', 'approved']
                ).filter(
                    Q(start_date__lte=end_date, end_date__gte=start_date)
                )
                
                if overlapping.exists():
                    raise ValidationError(
                        f"A {budget_type} budget already exists for this period"
                    )
                
                # Create budget
                budget = Budget.objects.create(
                    school=school,
                    name=name,
                    name_ar=name_ar,
                    budget_type=budget_type,
                    financial_year=financial_year,
                    start_date=start_date,
                    end_date=end_date,
                    description=description,
                    created_by=created_by,
                    status='draft'
                )
                
                logger.info(f"Budget created: {budget.name} for {school.name}")
                return budget
                
        except Exception as e:
            logger.error(f"Error creating budget: {str(e)}")
            raise
    
    @staticmethod
    def add_budget_items(budget: Budget, items_data: List[Dict]) -> List[BudgetItem]:
        """
        Add multiple budget items to a budget
        """
        try:
            with transaction.atomic():
                budget_items = []
                total_allocated = Decimal('0')
                
                for item_data in items_data:
                    account = Account.objects.get(
                        id=item_data['account_id'],
                        school=budget.school
                    )
                    
                    cost_center = None
                    if item_data.get('cost_center_id'):
                        cost_center = CostCenter.objects.get(
                            id=item_data['cost_center_id'],
                            school=budget.school
                        )
                    
                    allocated_amount = Decimal(str(item_data['allocated_amount']))
                    
                    # Validate account is not a header account
                    if account.is_header:
                        raise ValidationError(
                            f"Cannot allocate budget to header account: {account.name}"
                        )
                    
                    budget_item = BudgetItem.objects.create(
                        school=budget.school,
                        budget=budget,
                        account=account,
                        cost_center=cost_center,
                        allocated_amount=allocated_amount,
                        notes=item_data.get('notes', ''),
                        created_by=budget.created_by
                    )
                    
                    budget_items.append(budget_item)
                    total_allocated += allocated_amount
                
                # Update budget total
                budget.total_budget = total_allocated
                budget.save(update_fields=['total_budget'])
                
                logger.info(f"Added {len(budget_items)} items to budget {budget.name}")
                return budget_items
                
        except Exception as e:
            logger.error(f"Error adding budget items: {str(e)}")
            raise
    
    @staticmethod
    def submit_for_approval(budget: Budget, submitted_by: User) -> bool:
        """
        Submit budget for approval
        """
        try:
            if budget.status != 'draft':
                raise ValidationError("Only draft budgets can be submitted for approval")
            
            if not budget.items.exists():
                raise ValidationError("Budget must have at least one item before submission")
            
            with transaction.atomic():
                budget.status = 'pending_approval'
                budget.save(update_fields=['status'])
                
                # Create approval workflow record
                BudgetApprovalWorkflow.objects.create(
                    school=budget.school,
                    budget=budget,
                    approver=submitted_by,  # Will be updated when actual approver is assigned
                    status='pending',
                    created_by=submitted_by
                )
                
                # Send notification to approvers
                BudgetService._notify_approvers(budget)
                
                logger.info(f"Budget {budget.name} submitted for approval")
                return True
                
        except Exception as e:
            logger.error(f"Error submitting budget for approval: {str(e)}")
            raise
    
    @staticmethod
    def approve_budget(budget: Budget, approved_by: User, comments: str = None) -> bool:
        """
        Approve a budget
        """
        try:
            if budget.status != 'pending_approval':
                raise ValidationError("Only pending budgets can be approved")
            
            with transaction.atomic():
                budget.status = 'approved'
                budget.approved_by = approved_by
                budget.approved_at = timezone.now()
                budget.save(update_fields=['status', 'approved_by', 'approved_at'])
                
                # Update approval workflow
                workflow = BudgetApprovalWorkflow.objects.get(
                    budget=budget,
                    status='pending'
                )
                workflow.approver = approved_by
                workflow.reviewed_at = timezone.now()
                workflow.status = 'approved'
                workflow.action_taken = 'approve'
                workflow.comments = comments
                workflow.save()
                
                # Activate budget if within date range
                today = date.today()
                if budget.start_date <= today <= budget.end_date:
                    budget.status = 'active'
                    budget.save(update_fields=['status'])
                
                logger.info(f"Budget {budget.name} approved by {approved_by.username}")
                return True
                
        except Exception as e:
            logger.error(f"Error approving budget: {str(e)}")
            raise
    
    @staticmethod
    def reject_budget(budget: Budget, rejected_by: User, reason: str) -> bool:
        """
        Reject a budget
        """
        try:
            if budget.status != 'pending_approval':
                raise ValidationError("Only pending budgets can be rejected")
            
            with transaction.atomic():
                budget.status = 'draft'  # Return to draft for revision
                budget.save(update_fields=['status'])
                
                # Update approval workflow
                workflow = BudgetApprovalWorkflow.objects.get(
                    budget=budget,
                    status='pending'
                )
                workflow.approver = rejected_by
                workflow.reviewed_at = timezone.now()
                workflow.status = 'rejected'
                workflow.action_taken = 'reject'
                workflow.comments = reason
                workflow.save()
                
                logger.info(f"Budget {budget.name} rejected by {rejected_by.username}")
                return True
                
        except Exception as e:
            logger.error(f"Error rejecting budget: {str(e)}")
            raise
    
    @staticmethod
    def _notify_approvers(budget: Budget):
        """
        Send notification to budget approvers
        """
        try:
            # Get users with budget approval permissions
            approvers = User.objects.filter(
                groups__permissions__codename='approve_budget',
                is_active=True
            ).distinct()
            
            if not approvers.exists():
                logger.warning("No budget approvers found")
                return
            
            subject = f"Budget Approval Required: {budget.name}"
            message = render_to_string('finance/emails/budget_approval_request.html', {
                'budget': budget,
                'approval_url': f"{settings.SITE_URL}/finance/budgets/{budget.id}/approve/"
            })
            
            recipient_emails = [user.email for user in approvers if user.email]
            
            if recipient_emails:
                send_mail(
                    subject=subject,
                    message=message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=recipient_emails,
                    html_message=message
                )
                
                logger.info(f"Approval notifications sent for budget {budget.name}")
            
        except Exception as e:
            logger.error(f"Error sending approval notifications: {str(e)}")


class BudgetMonitoringService:
    """
    Service for monitoring budget performance and generating alerts
    """
    
    @staticmethod
    def update_budget_actuals(budget: Budget) -> Dict:
        """
        Update actual spending amounts for all budget items
        """
        try:
            updated_items = []
            total_spent = Decimal('0')
            
            for item in budget.items.all():
                old_spent = item.spent_amount
                item.update_spent_amount()
                
                if item.spent_amount != old_spent:
                    updated_items.append({
                        'account': item.account.name,
                        'old_amount': old_spent,
                        'new_amount': item.spent_amount,
                        'variance': item.variance
                    })
                
                total_spent += item.spent_amount
            
            # Check for budget alerts
            BudgetMonitoringService._check_budget_alerts(budget)
            
            logger.info(f"Updated actuals for budget {budget.name}")
            
            return {
                'budget_id': budget.id,
                'total_allocated': budget.get_total_allocated(),
                'total_spent': total_spent,
                'variance': budget.get_variance(),
                'utilization': budget.get_utilization_percentage(),
                'updated_items': updated_items
            }
            
        except Exception as e:
            logger.error(f"Error updating budget actuals: {str(e)}")
            raise
    
    @staticmethod
    def generate_variance_report(budget: Budget) -> Dict:
        """
        Generate detailed variance analysis report
        """
        try:
            items_analysis = []
            
            for item in budget.items.select_related('account', 'cost_center').all():
                variance_amount = item.variance
                variance_percentage = 0
                
                if item.allocated_amount > 0:
                    variance_percentage = (variance_amount / item.allocated_amount * 100)
                
                status = 'on_track'
                if variance_percentage < -10:  # Over budget by more than 10%
                    status = 'over_budget'
                elif variance_percentage < -5:  # Over budget by 5-10%
                    status = 'warning'
                elif variance_percentage > 20:  # Under budget by more than 20%
                    status = 'under_utilized'
                
                items_analysis.append({
                    'account_code': item.account.code,
                    'account_name': item.account.name,
                    'cost_center': item.cost_center.name if item.cost_center else None,
                    'allocated_amount': item.allocated_amount,
                    'spent_amount': item.spent_amount,
                    'variance_amount': variance_amount,
                    'variance_percentage': variance_percentage,
                    'utilization_percentage': item.utilization_percentage,
                    'status': status
                })
            
            # Overall budget analysis
            total_allocated = budget.get_total_allocated()
            total_spent = budget.get_total_spent()
            overall_variance = budget.get_variance()
            overall_utilization = budget.get_utilization_percentage()
            
            return {
                'budget': {
                    'id': budget.id,
                    'name': budget.name,
                    'type': budget.budget_type,
                    'period': f"{budget.start_date} to {budget.end_date}",
                    'status': budget.status
                },
                'summary': {
                    'total_allocated': total_allocated,
                    'total_spent': total_spent,
                    'variance_amount': overall_variance,
                    'utilization_percentage': overall_utilization,
                    'days_remaining': (budget.end_date - date.today()).days
                },
                'items': items_analysis,
                'generated_at': timezone.now()
            }
            
        except Exception as e:
            logger.error(f"Error generating variance report: {str(e)}")
            raise
    
    @staticmethod
    def _check_budget_alerts(budget: Budget):
        """
        Check and trigger budget alerts based on configured thresholds
        """
        try:
            alerts = BudgetAlert.objects.filter(
                budget=budget,
                is_active=True
            )
            
            for alert in alerts:
                should_trigger = False
                current_utilization = budget.get_utilization_percentage()
                
                if alert.alert_type == 'utilization_threshold':
                    if current_utilization >= alert.threshold_percentage:
                        should_trigger = True
                
                elif alert.alert_type == 'variance_threshold':
                    variance_percentage = abs(budget.get_variance() / budget.get_total_allocated() * 100)
                    if variance_percentage >= alert.threshold_percentage:
                        should_trigger = True
                
                elif alert.alert_type == 'amount_threshold':
                    if budget.get_total_spent() >= alert.threshold_amount:
                        should_trigger = True
                
                if should_trigger:
                    BudgetMonitoringService._send_budget_alert(alert, budget)
            
        except Exception as e:
            logger.error(f"Error checking budget alerts: {str(e)}")
    
    @staticmethod
    def _send_budget_alert(alert: BudgetAlert, budget: Budget):
        """
        Send budget alert notification
        """
        try:
            subject = f"Budget Alert: {budget.name}"
            
            context = {
                'alert': alert,
                'budget': budget,
                'utilization': budget.get_utilization_percentage(),
                'variance': budget.get_variance(),
                'total_spent': budget.get_total_spent()
            }
            
            message = render_to_string('finance/emails/budget_alert.html', context)
            
            # Parse recipients (could be emails or user IDs)
            recipients = []
            for recipient in alert.recipients.split(','):
                recipient = recipient.strip()
                if '@' in recipient:
                    recipients.append(recipient)
                else:
                    try:
                        user = User.objects.get(username=recipient)
                        if user.email:
                            recipients.append(user.email)
                    except User.DoesNotExist:
                        continue
            
            if recipients:
                send_mail(
                    subject=subject,
                    message=message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=recipients,
                    html_message=message
                )
                
                # Update alert last sent time
                alert.last_sent_at = timezone.now()
                alert.save(update_fields=['last_sent_at'])
                
                logger.info(f"Budget alert sent for {budget.name}")
            
        except Exception as e:
            logger.error(f"Error sending budget alert: {str(e)}")


class BudgetReportingService:
    """
    Service for generating budget reports and analytics
    """
    
    @staticmethod
    def generate_budget_summary_report(school, financial_year=None) -> Dict:
        """
        Generate comprehensive budget summary report
        """
        try:
            budgets_query = Budget.objects.filter(school=school)
            
            if financial_year:
                budgets_query = budgets_query.filter(financial_year=financial_year)
            
            budgets = budgets_query.select_related('financial_year').prefetch_related('items')
            
            summary_data = {
                'total_budgets': budgets.count(),
                'active_budgets': budgets.filter(status='active').count(),
                'pending_approval': budgets.filter(status='pending_approval').count(),
                'total_allocated': Decimal('0'),
                'total_spent': Decimal('0'),
                'budgets': []
            }
            
            for budget in budgets:
                allocated = budget.get_total_allocated()
                spent = budget.get_total_spent()
                
                summary_data['total_allocated'] += allocated
                summary_data['total_spent'] += spent
                
                summary_data['budgets'].append({
                    'id': budget.id,
                    'name': budget.name,
                    'type': budget.get_budget_type_display(),
                    'status': budget.get_status_display(),
                    'period': f"{budget.start_date} to {budget.end_date}",
                    'allocated': allocated,
                    'spent': spent,
                    'variance': budget.get_variance(),
                    'utilization': budget.get_utilization_percentage()
                })
            
            # Calculate overall metrics
            if summary_data['total_allocated'] > 0:
                summary_data['overall_utilization'] = (
                    summary_data['total_spent'] / summary_data['total_allocated'] * 100
                )
            else:
                summary_data['overall_utilization'] = 0
            
            summary_data['overall_variance'] = (
                summary_data['total_allocated'] - summary_data['total_spent']
            )
            
            return summary_data
            
        except Exception as e:
            logger.error(f"Error generating budget summary report: {str(e)}")
            raise
    
    @staticmethod
    def generate_budget_performance_dashboard(school) -> Dict:
        """
        Generate budget performance dashboard data
        """
        try:
            # Get active budgets
            active_budgets = Budget.objects.filter(
                school=school,
                status='active'
            ).prefetch_related('items')
            
            dashboard_data = {
                'overview': {
                    'total_active_budgets': active_budgets.count(),
                    'total_allocated': Decimal('0'),
                    'total_spent': Decimal('0'),
                    'budgets_over_threshold': 0,
                    'budgets_under_utilized': 0
                },
                'budget_performance': [],
                'top_variances': [],
                'alerts_summary': {
                    'critical': 0,
                    'warning': 0,
                    'info': 0
                }
            }
            
            variances = []
            
            for budget in active_budgets:
                allocated = budget.get_total_allocated()
                spent = budget.get_total_spent()
                utilization = budget.get_utilization_percentage()
                
                dashboard_data['overview']['total_allocated'] += allocated
                dashboard_data['overview']['total_spent'] += spent
                
                # Check thresholds
                if utilization > 90:
                    dashboard_data['overview']['budgets_over_threshold'] += 1
                elif utilization < 50:
                    dashboard_data['overview']['budgets_under_utilized'] += 1
                
                # Collect variance data
                variance_amount = budget.get_variance()
                if allocated > 0:
                    variance_percentage = abs(variance_amount / allocated * 100)
                    variances.append({
                        'budget_name': budget.name,
                        'variance_amount': variance_amount,
                        'variance_percentage': variance_percentage,
                        'utilization': utilization
                    })
                
                dashboard_data['budget_performance'].append({
                    'name': budget.name,
                    'allocated': allocated,
                    'spent': spent,
                    'utilization': utilization,
                    'status': 'critical' if utilization > 95 else 'warning' if utilization > 80 else 'good'
                })
            
            # Sort and get top variances
            variances.sort(key=lambda x: x['variance_percentage'], reverse=True)
            dashboard_data['top_variances'] = variances[:5]
            
            # Calculate overall utilization
            if dashboard_data['overview']['total_allocated'] > 0:
                dashboard_data['overview']['overall_utilization'] = (
                    dashboard_data['overview']['total_spent'] / 
                    dashboard_data['overview']['total_allocated'] * 100
                )
            else:
                dashboard_data['overview']['overall_utilization'] = 0
            
            return dashboard_data
            
        except Exception as e:
            logger.error(f"Error generating budget performance dashboard: {str(e)}")
            raise