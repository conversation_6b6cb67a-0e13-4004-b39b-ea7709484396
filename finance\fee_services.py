"""
Fee Management Services for School ERP System
Implements fee calculation algorithms, invoice generation, and fee collection reporting
"""
from django.db import transaction
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.db.models import Q, Sum, Count, Avg
from decimal import Decimal
from datetime import date, datetime, timedelta
from typing import List, Dict, Optional, Tuple
import logging

from .models import (
    FeeType, GradeFee, StudentFee, Payment, PaymentItem, Invoice, InvoiceItem,
    Account, Transaction, TransactionEntry
)
from students.models import Student, Grade
from core.models import AcademicYear

logger = logging.getLogger(__name__)


class FeeCalculationService:
    """Service class for fee calculation algorithms"""
    
    @staticmethod
    def calculate_student_fees(student: Student, academic_year: AcademicYear = None) -> Dict:
        """
        Calculate all fees for a student for a given academic year
        Returns detailed breakdown of fees, discounts, and totals
        """
        try:
            if not academic_year:
                academic_year = AcademicYear.objects.filter(
                    school=student.school,
                    is_current=True
                ).first()
            
            if not academic_year:
                raise ValidationError(_('No active academic year found'))
            
            # Get student's current grade through current class
            current_class = student.current_class
            if not current_class:
                raise ValidationError(_('Student has no assigned class'))
            
            current_grade = current_class.grade
            
            # Get all grade fees for the student's grade and academic year
            grade_fees = GradeFee.objects.filter(
                grade=current_grade,
                academic_year=academic_year,
                is_active=True
            ).select_related('fee_type', 'fee_type__account')
            
            fee_breakdown = {
                'student': student,
                'academic_year': academic_year,
                'grade': current_grade,
                'fees': [],
                'total_amount': Decimal('0'),
                'total_discount': Decimal('0'),
                'net_amount': Decimal('0'),
                'mandatory_fees': Decimal('0'),
                'optional_fees': Decimal('0')
            }
            
            for grade_fee in grade_fees:
                # Check if student already has this fee assigned
                student_fee, created = StudentFee.objects.get_or_create(
                    student=student,
                    grade_fee=grade_fee,
                    defaults={
                        'amount': grade_fee.amount,
                        'due_date': grade_fee.due_date or FeeCalculationService._calculate_default_due_date(grade_fee),
                        'school': student.school,
                        'created_by_id': 1  # System user
                    }
                )
                
                # Apply any applicable discounts
                discount_amount = FeeCalculationService._calculate_fee_discount(student, grade_fee)
                if discount_amount > 0:
                    student_fee.discount_amount = discount_amount
                    student_fee.save(update_fields=['discount_amount'])
                
                fee_info = {
                    'fee_type': grade_fee.fee_type,
                    'amount': student_fee.amount,
                    'discount_amount': student_fee.discount_amount,
                    'net_amount': student_fee.net_amount,
                    'due_date': student_fee.due_date,
                    'is_mandatory': grade_fee.fee_type.is_mandatory,
                    'is_paid': student_fee.is_paid,
                    'student_fee': student_fee
                }
                
                fee_breakdown['fees'].append(fee_info)
                fee_breakdown['total_amount'] += student_fee.amount
                fee_breakdown['total_discount'] += student_fee.discount_amount
                
                if grade_fee.fee_type.is_mandatory:
                    fee_breakdown['mandatory_fees'] += student_fee.net_amount
                else:
                    fee_breakdown['optional_fees'] += student_fee.net_amount
            
            fee_breakdown['net_amount'] = fee_breakdown['total_amount'] - fee_breakdown['total_discount']
            
            logger.info(f"Calculated fees for student {student.student_id}: {fee_breakdown['net_amount']}")
            return fee_breakdown
            
        except Exception as e:
            logger.error(f"Error calculating student fees: {str(e)}")
            raise
    
    @staticmethod
    def _calculate_default_due_date(grade_fee: GradeFee) -> date:
        """Calculate default due date for a fee"""
        if grade_fee.due_date:
            return grade_fee.due_date
        
        # Default to 30 days from academic year start
        return grade_fee.academic_year.start_date + timedelta(days=30)
    
    @staticmethod
    def _calculate_fee_discount(student: Student, grade_fee: GradeFee) -> Decimal:
        """
        Calculate applicable discount for a student fee
        This can be extended to include various discount rules
        """
        discount_amount = Decimal('0')
        
        # Example discount rules (can be extended):
        # 1. Sibling discount
        siblings_count = Student.objects.filter(
            school=student.school,
            parent=student.parent
        ).exclude(id=student.id, is_graduated=True).count()
        
        if siblings_count > 0:
            # 5% discount for each sibling (max 20%)
            sibling_discount_rate = min(siblings_count * 0.05, 0.20)
            discount_amount += grade_fee.amount * Decimal(str(sibling_discount_rate))
        
        # 2. Early payment discount (if paid before due date)
        # This would be applied during payment processing
        
        # 3. Merit-based discount
        # This could be based on academic performance
        
        return discount_amount.quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_grade_fee_summary(grade: Grade, academic_year: AcademicYear) -> Dict:
        """Calculate fee summary for all students in a grade"""
        try:
            students = Student.objects.filter(
                current_class__grade=grade,
                school=grade.school
            ).exclude(is_graduated=True)
            
            summary = {
                'grade': grade,
                'academic_year': academic_year,
                'total_students': students.count(),
                'total_fees_amount': Decimal('0'),
                'total_paid_amount': Decimal('0'),
                'total_outstanding': Decimal('0'),
                'payment_rate': Decimal('0'),
                'fee_types': []
            }
            
            # Get all fee types for this grade
            grade_fees = GradeFee.objects.filter(
                grade=grade,
                academic_year=academic_year,
                is_active=True
            ).select_related('fee_type')
            
            for grade_fee in grade_fees:
                student_fees = StudentFee.objects.filter(
                    grade_fee=grade_fee,
                    student__in=students
                )
                
                total_amount = student_fees.aggregate(
                    total=Sum('amount')
                )['total'] or Decimal('0')
                
                paid_amount = student_fees.filter(is_paid=True).aggregate(
                    total=Sum('amount')
                )['total'] or Decimal('0')
                
                fee_type_info = {
                    'fee_type': grade_fee.fee_type,
                    'total_amount': total_amount,
                    'paid_amount': paid_amount,
                    'outstanding': total_amount - paid_amount,
                    'students_assigned': student_fees.count(),
                    'students_paid': student_fees.filter(is_paid=True).count()
                }
                
                summary['fee_types'].append(fee_type_info)
                summary['total_fees_amount'] += total_amount
                summary['total_paid_amount'] += paid_amount
            
            summary['total_outstanding'] = summary['total_fees_amount'] - summary['total_paid_amount']
            
            if summary['total_fees_amount'] > 0:
                summary['payment_rate'] = (summary['total_paid_amount'] / summary['total_fees_amount'] * 100).quantize(Decimal('0.01'))
            
            return summary
            
        except Exception as e:
            logger.error(f"Error calculating grade fee summary: {str(e)}")
            raise


class InvoiceGenerationService:
    """Service class for invoice generation system"""
    
    @staticmethod
    def generate_student_invoice(student: Student, fee_types: List[int] = None, 
                               academic_year: AcademicYear = None, due_date: date = None) -> Invoice:
        """
        Generate invoice for student fees
        """
        try:
            with transaction.atomic():
                if not academic_year:
                    academic_year = AcademicYear.objects.filter(
                        school=student.school,
                        is_current=True
                    ).first()
                
                if not academic_year:
                    raise ValidationError(_('No active academic year found'))
                
                # Calculate student fees
                fee_breakdown = FeeCalculationService.calculate_student_fees(student, academic_year)
                
                # Filter fees if specific fee types requested
                fees_to_invoice = fee_breakdown['fees']
                if fee_types:
                    fees_to_invoice = [
                        fee for fee in fees_to_invoice 
                        if fee['fee_type'].id in fee_types and not fee['is_paid']
                    ]
                
                if not fees_to_invoice:
                    raise ValidationError(_('No unpaid fees found for invoice generation'))
                
                # Create invoice
                invoice = Invoice.objects.create(
                    school=student.school,
                    invoice_number=InvoiceGenerationService._generate_invoice_number(student.school),
                    student=student,
                    invoice_type='fees',
                    invoice_date=date.today(),
                    due_date=due_date or (date.today() + timedelta(days=30)),
                    subtotal=Decimal('0'),
                    tax_amount=Decimal('0'),
                    discount_amount=Decimal('0'),
                    total_amount=Decimal('0'),
                    status='draft',
                    created_by_id=1  # System user
                )
                
                # Create invoice items
                subtotal = Decimal('0')
                total_discount = Decimal('0')
                
                for fee_info in fees_to_invoice:
                    InvoiceItem.objects.create(
                        school=student.school,
                        invoice=invoice,
                        description=f"{fee_info['fee_type'].name} - {academic_year.name}",
                        quantity=1,
                        unit_price=fee_info['amount'],
                        total_price=fee_info['amount'],
                        fee_type=fee_info['fee_type'],
                        created_by_id=1
                    )
                    
                    subtotal += fee_info['amount']
                    total_discount += fee_info['discount_amount']
                
                # Update invoice totals
                invoice.subtotal = subtotal
                invoice.discount_amount = total_discount
                invoice.total_amount = subtotal - total_discount
                invoice.save(update_fields=['subtotal', 'discount_amount', 'total_amount'])
                
                logger.info(f"Generated invoice {invoice.invoice_number} for student {student.student_id}")
                return invoice
                
        except Exception as e:
            logger.error(f"Error generating invoice: {str(e)}")
            raise
    
    @staticmethod
    def _generate_invoice_number(school) -> str:
        """Generate unique invoice number"""
        year = datetime.now().year
        prefix = f"INV-{year}-"
        
        # Get the last invoice for this year
        last_invoice = Invoice.objects.filter(
            school=school,
            invoice_number__startswith=prefix
        ).order_by('-invoice_number').first()
        
        if last_invoice:
            try:
                last_number = int(last_invoice.invoice_number.split('-')[-1])
                next_number = last_number + 1
            except (ValueError, IndexError):
                next_number = 1
        else:
            next_number = 1
        
        return f"{prefix}{next_number:06d}"
    
    @staticmethod
    def generate_bulk_invoices(grade: Grade, academic_year: AcademicYear = None, 
                             fee_types: List[int] = None) -> List[Invoice]:
        """Generate invoices for all students in a grade"""
        try:
            students = Student.objects.filter(
                current_class__grade=grade,
                school=grade.school
            ).exclude(is_graduated=True)
            
            invoices = []
            for student in students:
                try:
                    invoice = InvoiceGenerationService.generate_student_invoice(
                        student, fee_types, academic_year
                    )
                    invoices.append(invoice)
                except ValidationError as e:
                    logger.warning(f"Skipped invoice for student {student.student_id}: {str(e)}")
                    continue
            
            logger.info(f"Generated {len(invoices)} invoices for grade {grade.name}")
            return invoices
            
        except Exception as e:
            logger.error(f"Error generating bulk invoices: {str(e)}")
            raise


class PaymentTrackingService:
    """Service class for payment tracking and receipt management"""
    
    @staticmethod
    def process_payment(student: Student, amount: Decimal, payment_method: str,
                       fee_allocations: List[Dict], reference_number: str = None,
                       notes: str = None, received_by_id: int = 1) -> Payment:
        """
        Process a payment and allocate it to specific fees
        fee_allocations: [{'student_fee_id': int, 'amount': Decimal}, ...]
        """
        try:
            with transaction.atomic():
                # Validate payment amount
                total_allocated = sum(allocation['amount'] for allocation in fee_allocations)
                if total_allocated != amount:
                    raise ValidationError(
                        _('Payment amount ({}) does not match allocated amount ({})').format(
                            amount, total_allocated
                        )
                    )
                
                # Create payment record
                payment = Payment.objects.create(
                    school=student.school,
                    student=student,
                    receipt_number=PaymentTrackingService._generate_receipt_number(student.school),
                    amount=amount,
                    payment_date=date.today(),
                    payment_method=payment_method,
                    reference_number=reference_number,
                    notes=notes,
                    received_by_id=received_by_id,
                    created_by_id=received_by_id
                )
                
                # Create payment items and update student fees
                for allocation in fee_allocations:
                    student_fee = StudentFee.objects.get(
                        id=allocation['student_fee_id'],
                        student=student
                    )
                    
                    # Create payment item
                    PaymentItem.objects.create(
                        school=student.school,
                        payment=payment,
                        student_fee=student_fee,
                        amount=allocation['amount'],
                        created_by_id=received_by_id
                    )
                    
                    # Update student fee payment status
                    total_paid = PaymentItem.objects.filter(
                        student_fee=student_fee
                    ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
                    
                    if total_paid >= student_fee.net_amount:
                        student_fee.is_paid = True
                        student_fee.paid_date = date.today()
                    
                    student_fee.save(update_fields=['is_paid', 'paid_date'])
                
                # Create accounting entries
                PaymentTrackingService._create_payment_accounting_entries(payment)
                
                logger.info(f"Processed payment {payment.receipt_number} for student {student.student_id}")
                return payment
                
        except Exception as e:
            logger.error(f"Error processing payment: {str(e)}")
            raise
    
    @staticmethod
    def _generate_receipt_number(school) -> str:
        """Generate unique receipt number"""
        year = datetime.now().year
        prefix = f"RCP-{year}-"
        
        # Get the last payment for this year
        last_payment = Payment.objects.filter(
            school=school,
            receipt_number__startswith=prefix
        ).order_by('-receipt_number').first()
        
        if last_payment:
            try:
                last_number = int(last_payment.receipt_number.split('-')[-1])
                next_number = last_number + 1
            except (ValueError, IndexError):
                next_number = 1
        else:
            next_number = 1
        
        return f"{prefix}{next_number:06d}"
    
    @staticmethod
    def _create_payment_accounting_entries(payment: Payment):
        """Create double-entry accounting entries for payment"""
        try:
            # Get cash/bank account (assuming code 1110 for cash)
            cash_account = Account.objects.filter(
                school=payment.school,
                code='1110',  # Cash account
                is_active=True
            ).first()
            
            if not cash_account:
                logger.warning(f"Cash account not found for payment {payment.receipt_number}")
                return
            
            # Create transaction for payment
            from .services import DoubleEntryBookkeepingService
            
            # Group payment items by fee type to create appropriate entries
            fee_type_amounts = {}
            for item in payment.items.select_related('student_fee__grade_fee__fee_type'):
                fee_type = item.student_fee.grade_fee.fee_type
                if fee_type.id not in fee_type_amounts:
                    fee_type_amounts[fee_type.id] = {
                        'fee_type': fee_type,
                        'amount': Decimal('0')
                    }
                fee_type_amounts[fee_type.id]['amount'] += item.amount
            
            # Create transaction entries
            entries_data = []
            
            # Debit cash account
            entries_data.append({
                'account_id': cash_account.id,
                'description': f"Payment received from {payment.student.full_name}",
                'debit_amount': payment.amount,
                'credit_amount': 0,
                'reference': payment.receipt_number
            })
            
            # Credit revenue accounts for each fee type
            for fee_data in fee_type_amounts.values():
                entries_data.append({
                    'account_id': fee_data['fee_type'].account.id,
                    'description': f"Fee payment - {fee_data['fee_type'].name}",
                    'debit_amount': 0,
                    'credit_amount': fee_data['amount'],
                    'reference': payment.receipt_number
                })
            
            # Create the transaction
            transaction_data = {
                'transaction_date': payment.payment_date,
                'transaction_type': 'receipt',
                'description': f"Fee payment from {payment.student.full_name}",
                'reference': payment.receipt_number,
                'total_amount': payment.amount,
                'requires_approval': False
            }
            
            DoubleEntryBookkeepingService.create_transaction(
                payment.school,
                payment.received_by,
                transaction_data,
                entries_data
            )
            
        except Exception as e:
            logger.error(f"Error creating accounting entries for payment {payment.receipt_number}: {str(e)}")
            # Don't raise exception as payment is already processed
    
    @staticmethod
    def get_student_payment_history(student: Student, academic_year: AcademicYear = None) -> Dict:
        """Get comprehensive payment history for a student"""
        try:
            payments_query = Payment.objects.filter(student=student)
            
            if academic_year:
                # Filter by academic year date range
                payments_query = payments_query.filter(
                    payment_date__gte=academic_year.start_date,
                    payment_date__lte=academic_year.end_date
                )
            
            payments = payments_query.select_related('received_by').prefetch_related(
                'items__student_fee__grade_fee__fee_type'
            ).order_by('-payment_date')
            
            total_paid = payments.aggregate(total=Sum('amount'))['total'] or Decimal('0')
            
            payment_history = {
                'student': student,
                'academic_year': academic_year,
                'payments': payments,
                'total_paid': total_paid,
                'payment_count': payments.count(),
                'payment_methods': payments.values('payment_method').annotate(
                    count=Count('id'),
                    total=Sum('amount')
                )
            }
            
            return payment_history
            
        except Exception as e:
            logger.error(f"Error getting payment history: {str(e)}")
            raise


class FeeCollectionReportingService:
    """Service class for fee collection reporting"""
    
    @staticmethod
    def generate_collection_summary_report(school, academic_year: AcademicYear = None,
                                         start_date: date = None, end_date: date = None) -> Dict:
        """Generate comprehensive fee collection summary report"""
        try:
            if not academic_year:
                academic_year = AcademicYear.objects.filter(
                    school=school,
                    is_current=True
                ).first()
            
            if not start_date:
                start_date = academic_year.start_date if academic_year else date.today().replace(month=1, day=1)
            
            if not end_date:
                end_date = academic_year.end_date if academic_year else date.today()
            
            # Get all student fees for the period
            student_fees = StudentFee.objects.filter(
                school=school,
                created_at__date__gte=start_date,
                created_at__date__lte=end_date
            ).select_related('student', 'grade_fee__fee_type', 'grade_fee__grade')
            
            # Get all payments for the period
            payments = Payment.objects.filter(
                school=school,
                payment_date__gte=start_date,
                payment_date__lte=end_date
            ).select_related('student')
            
            report = {
                'school': school,
                'academic_year': academic_year,
                'period': {'start_date': start_date, 'end_date': end_date},
                'summary': {
                    'total_fees_amount': Decimal('0'),
                    'total_collected': Decimal('0'),
                    'total_outstanding': Decimal('0'),
                    'collection_rate': Decimal('0'),
                    'total_students': 0,
                    'students_with_outstanding': 0
                },
                'by_grade': [],
                'by_fee_type': [],
                'by_payment_method': [],
                'recent_payments': [],
                'overdue_fees': []
            }
            
            # Calculate summary statistics
            total_fees = student_fees.aggregate(
                total_amount=Sum('amount'),
                total_discount=Sum('discount_amount')
            )
            
            report['summary']['total_fees_amount'] = (
                total_fees['total_amount'] or Decimal('0')
            ) - (total_fees['total_discount'] or Decimal('0'))
            
            report['summary']['total_collected'] = payments.aggregate(
                total=Sum('amount')
            )['total'] or Decimal('0')
            
            report['summary']['total_outstanding'] = (
                report['summary']['total_fees_amount'] - report['summary']['total_collected']
            )
            
            if report['summary']['total_fees_amount'] > 0:
                report['summary']['collection_rate'] = (
                    report['summary']['total_collected'] / report['summary']['total_fees_amount'] * 100
                ).quantize(Decimal('0.01'))
            
            # Students statistics
            report['summary']['total_students'] = student_fees.values('student').distinct().count()
            report['summary']['students_with_outstanding'] = student_fees.filter(
                is_paid=False
            ).values('student').distinct().count()
            
            # By grade analysis
            grade_stats = student_fees.values(
                'grade_fee__grade__name'
            ).annotate(
                total_amount=Sum('amount'),
                total_discount=Sum('discount_amount'),
                students_count=Count('student', distinct=True)
            )
            
            for stat in grade_stats:
                grade_payments = payments.filter(
                    student__current_class__grade__name=stat['grade_fee__grade__name']
                ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
                
                net_amount = (stat['total_amount'] or Decimal('0')) - (stat['total_discount'] or Decimal('0'))
                
                report['by_grade'].append({
                    'grade': stat['grade_fee__grade__name'],
                    'total_fees': net_amount,
                    'collected': grade_payments,
                    'outstanding': net_amount - grade_payments,
                    'students_count': stat['students_count'],
                    'collection_rate': (grade_payments / net_amount * 100).quantize(Decimal('0.01')) if net_amount > 0 else Decimal('0')
                })
            
            # By fee type analysis
            fee_type_stats = student_fees.values(
                'grade_fee__fee_type__name'
            ).annotate(
                total_amount=Sum('amount'),
                total_discount=Sum('discount_amount'),
                students_count=Count('student', distinct=True)
            )
            
            for stat in fee_type_stats:
                fee_type_payments = PaymentItem.objects.filter(
                    student_fee__grade_fee__fee_type__name=stat['grade_fee__fee_type__name'],
                    payment__payment_date__gte=start_date,
                    payment__payment_date__lte=end_date
                ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
                
                net_amount = (stat['total_amount'] or Decimal('0')) - (stat['total_discount'] or Decimal('0'))
                
                report['by_fee_type'].append({
                    'fee_type': stat['grade_fee__fee_type__name'],
                    'total_fees': net_amount,
                    'collected': fee_type_payments,
                    'outstanding': net_amount - fee_type_payments,
                    'students_count': stat['students_count']
                })
            
            # By payment method analysis
            payment_method_stats = payments.values('payment_method').annotate(
                count=Count('id'),
                total=Sum('amount')
            )
            
            report['by_payment_method'] = list(payment_method_stats)
            
            # Recent payments (last 10)
            report['recent_payments'] = payments.order_by('-payment_date')[:10]
            
            # Overdue fees
            overdue_fees = student_fees.filter(
                is_paid=False,
                due_date__lt=date.today()
            ).select_related('student', 'grade_fee__fee_type').order_by('due_date')[:20]
            
            report['overdue_fees'] = overdue_fees
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating collection summary report: {str(e)}")
            raise
    
    @staticmethod
    def generate_outstanding_fees_report(school, grade: Grade = None) -> Dict:
        """Generate report of all outstanding fees"""
        try:
            outstanding_fees = StudentFee.objects.filter(
                school=school,
                is_paid=False
            ).select_related('student', 'grade_fee__fee_type', 'grade_fee__grade')
            
            if grade:
                outstanding_fees = outstanding_fees.filter(grade_fee__grade=grade)
            
            report = {
                'school': school,
                'grade': grade,
                'generated_at': timezone.now(),
                'outstanding_fees': outstanding_fees.order_by('due_date', 'student__last_name'),
                'summary': {
                    'total_amount': outstanding_fees.aggregate(
                        total=Sum('amount') - Sum('discount_amount')
                    )['total'] or Decimal('0'),
                    'count': outstanding_fees.count(),
                    'students_affected': outstanding_fees.values('student').distinct().count()
                }
            }
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating outstanding fees report: {str(e)}")
            raise
    
    @staticmethod
    def generate_payment_analytics(school, academic_year: AcademicYear = None) -> Dict:
        """Generate payment analytics and trends"""
        try:
            if not academic_year:
                academic_year = AcademicYear.objects.filter(
                    school=school,
                    is_current=True
                ).first()
            
            if not academic_year:
                raise ValidationError(_('No active academic year found'))
            
            payments = Payment.objects.filter(
                school=school,
                payment_date__gte=academic_year.start_date,
                payment_date__lte=academic_year.end_date
            )
            
            analytics = {
                'academic_year': academic_year,
                'total_payments': payments.count(),
                'total_amount': payments.aggregate(total=Sum('amount'))['total'] or Decimal('0'),
                'average_payment': payments.aggregate(avg=Avg('amount'))['avg'] or Decimal('0'),
                'monthly_trends': [],
                'payment_method_distribution': [],
                'peak_payment_days': []
            }
            
            # Monthly trends
            from django.db.models import Extract
            monthly_data = payments.annotate(
                month=Extract('payment_date', 'month')
            ).values('month').annotate(
                count=Count('id'),
                total=Sum('amount')
            ).order_by('month')
            
            analytics['monthly_trends'] = list(monthly_data)
            
            # Payment method distribution
            method_data = payments.values('payment_method').annotate(
                count=Count('id'),
                total=Sum('amount'),
                percentage=Count('id') * 100.0 / payments.count() if payments.count() > 0 else 0
            )
            
            analytics['payment_method_distribution'] = list(method_data)
            
            # Peak payment days (day of week)
            day_data = payments.annotate(
                weekday=Extract('payment_date', 'week_day')
            ).values('weekday').annotate(
                count=Count('id'),
                total=Sum('amount')
            ).order_by('-count')
            
            analytics['peak_payment_days'] = list(day_data)
            
            return analytics
            
        except Exception as e:
            logger.error(f"Error generating payment analytics: {str(e)}")
            raise