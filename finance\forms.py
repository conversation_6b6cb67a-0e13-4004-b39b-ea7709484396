from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Fieldset, Row, Column, Submit, HTML
from crispy_forms.bootstrap import FormActions
from .models import (
    Account, AccountType, CostCenter, JournalEntry, FinancialYear,
    Bank, Invoice, InvoiceItem, Payment, PaymentItem, StudentFee, FeeType, GradeFee,
    Transaction, TransactionEntry, TransactionAuditLog, Budget, BudgetItem, BudgetAlert
)
from students.models import Student


class AccountForm(forms.ModelForm):
    """
    Enhanced account creation and update form
    """
    class Meta:
        model = Account
        fields = [
            'account_type', 'parent', 'code', 'name', 'name_ar',
            'description', 'is_header', 'allow_manual_entries', 
            'is_reconcilable', 'opening_balance'
        ]
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'opening_balance': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        self.school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        # Filter parent accounts to only header accounts of same type
        if self.school:
            self.fields['parent'].queryset = Account.objects.filter(
                school=self.school,
                is_header=True,
                is_active=True
            ).select_related('account_type')
        
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            Fieldset(
                _('Basic Information'),
                Row(
                    Column('account_type', css_class='form-group col-md-6 mb-3'),
                    Column('parent', css_class='form-group col-md-6 mb-3'),
                ),
                Row(
                    Column('code', css_class='form-group col-md-6 mb-3'),
                    Column('name', css_class='form-group col-md-6 mb-3'),
                ),
                'name_ar',
                'description',
            ),
            Fieldset(
                _('Account Settings'),
                Row(
                    Column('is_header', css_class='form-group col-md-4 mb-3'),
                    Column('allow_manual_entries', css_class='form-group col-md-4 mb-3'),
                    Column('is_reconcilable', css_class='form-group col-md-4 mb-3'),
                ),
                'opening_balance',
            ),
            FormActions(
                Submit('submit', _('Save Account'), css_class='btn btn-primary'),
                HTML('<a href="{% url "finance:accounts_tree" %}" class="btn btn-secondary ms-2">' + str(_('Cancel')) + '</a>'),
            )
        )

    def clean_code(self):
        """Validate account code"""
        code = self.cleaned_data.get('code')
        if not code:
            return code
        
        from .services import AccountValidationService
        
        exclude_id = self.instance.id if self.instance.pk else None
        is_valid, error_msg = AccountValidationService.validate_account_code(
            self.school, code, exclude_id
        )
        
        if not is_valid:
            raise ValidationError(error_msg)
        
        return code

    def clean(self):
        """Validate account hierarchy and settings"""
        cleaned_data = super().clean()
        parent = cleaned_data.get('parent')
        is_header = cleaned_data.get('is_header')
        account_type = cleaned_data.get('account_type')
        
        # Validate parent account type consistency
        if parent and account_type and parent.account_type != account_type:
            raise ValidationError(_('Account type must match parent account type'))
        
        # Header accounts cannot have opening balance
        if is_header and cleaned_data.get('opening_balance', 0) != 0:
            raise ValidationError(_('Header accounts cannot have opening balance'))
        
        return cleaned_data


class AccountArchiveForm(forms.Form):
    """Form for archiving accounts"""
    reason = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        label=_('Archive Reason'),
        help_text=_('Please provide a reason for archiving this account')
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            'reason',
            FormActions(
                Submit('submit', _('Archive Account'), css_class='btn btn-warning'),
                HTML('<button type="button" class="btn btn-secondary ms-2" data-bs-dismiss="modal">' + str(_('Cancel')) + '</button>'),
            )
        )


class JournalEntryForm(forms.ModelForm):
    """
    Journal entry form
    """
    class Meta:
        model = JournalEntry
        fields = [
            'reference_number', 'entry_date', 'entry_type', 'description',
            'account', 'cost_center', 'debit_amount', 'credit_amount'
        ]
        widgets = {
            'entry_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'debit_amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'credit_amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'

    def clean(self):
        cleaned_data = super().clean()
        debit_amount = cleaned_data.get('debit_amount', 0)
        credit_amount = cleaned_data.get('credit_amount', 0)

        if debit_amount > 0 and credit_amount > 0:
            raise ValidationError(_('An entry cannot have both debit and credit amounts'))
        if debit_amount == 0 and credit_amount == 0:
            raise ValidationError(_('An entry must have either a debit or credit amount'))

        return cleaned_data


class PaymentForm(forms.ModelForm):
    """
    Payment form
    """
    class Meta:
        model = Payment
        fields = [
            'student', 'receipt_number', 'amount', 'payment_date',
            'payment_method', 'reference_number', 'notes'
        ]
        widgets = {
            'payment_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            Fieldset(
                _('Payment Information'),
                Row(
                    Column('student', css_class='form-group col-md-6 mb-3'),
                    Column('receipt_number', css_class='form-group col-md-6 mb-3'),
                ),
                Row(
                    Column('amount', css_class='form-group col-md-6 mb-3'),
                    Column('payment_date', css_class='form-group col-md-6 mb-3'),
                ),
                Row(
                    Column('payment_method', css_class='form-group col-md-6 mb-3'),
                    Column('reference_number', css_class='form-group col-md-6 mb-3'),
                ),
                'notes',
            ),
            FormActions(
                Submit('submit', _('Record Payment'), css_class='btn btn-primary'),
            )
        )


class InvoiceForm(forms.ModelForm):
    """
    Invoice form
    """
    class Meta:
        model = Invoice
        fields = [
            'student', 'invoice_type', 'invoice_date', 'due_date',
            'subtotal', 'tax_amount', 'discount_amount', 'notes'
        ]
        widgets = {
            'invoice_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'due_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'subtotal': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'tax_amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'discount_amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class CostCenterForm(forms.ModelForm):
    """
    Cost center form
    """
    class Meta:
        model = CostCenter
        fields = ['code', 'name', 'name_ar', 'description', 'manager', 'budget_amount']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'budget_amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
        }


class BudgetForm(forms.ModelForm):
    """
    Budget creation and editing form
    """
    class Meta:
        model = Budget
        fields = [
            'name', 'name_ar', 'budget_type', 'financial_year', 
            'start_date', 'end_date', 'description'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'name_ar': forms.TextInput(attrs={'class': 'form-control'}),
            'budget_type': forms.Select(attrs={'class': 'form-control'}),
            'financial_year': forms.Select(attrs={'class': 'form-control'}),
            'start_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if school:
            self.fields['financial_year'].queryset = FinancialYear.objects.filter(
                school=school, is_active=True
            ).order_by('-start_date')

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        
        if start_date and end_date:
            if start_date >= end_date:
                raise forms.ValidationError(_('End date must be after start date'))
        
        return cleaned_data


class BudgetItemForm(forms.ModelForm):
    """
    Budget item form for detailed budget allocation
    """
    class Meta:
        model = BudgetItem
        fields = ['account', 'cost_center', 'allocated_amount', 'notes']
        widgets = {
            'account': forms.Select(attrs={'class': 'form-control'}),
            'cost_center': forms.Select(attrs={'class': 'form-control'}),
            'allocated_amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 2, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if school:
            self.fields['account'].queryset = Account.objects.filter(
                school=school, is_active=True, is_header=False
            ).order_by('code')
            
            self.fields['cost_center'].queryset = CostCenter.objects.filter(
                school=school, is_active=True
            ).order_by('code')


class BudgetApprovalForm(forms.Form):
    """
    Budget approval form
    """
    APPROVAL_ACTIONS = (
        ('approve', _('Approve')),
        ('reject', _('Reject')),
        ('request_changes', _('Request Changes')),
    )
    
    action = forms.ChoiceField(
        choices=APPROVAL_ACTIONS,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    comments = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
        required=False,
        help_text=_('Optional comments for the approval decision')
    )


class BudgetMonitoringFilterForm(forms.Form):
    """
    Budget monitoring filter form
    """
    budget = forms.ModelChoiceField(
        queryset=Budget.objects.none(),
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'}),
        empty_label=_('All Budgets')
    )
    
    budget_type = forms.ChoiceField(
        choices=[('', _('All Types'))] + list(Budget.BUDGET_TYPES),
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    status = forms.ChoiceField(
        choices=[('', _('All Statuses'))] + list(Budget.BUDGET_STATUS),
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    variance_threshold = forms.DecimalField(
        required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
        help_text=_('Show items with variance above this percentage')
    )

    def __init__(self, *args, **kwargs):
        school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if school:
            self.fields['budget'].queryset = Budget.objects.filter(
                school=school, is_active=True
            ).order_by('-start_date')


class BudgetAlertForm(forms.ModelForm):
    """
    Budget alert configuration form
    """
    class Meta:
        model = BudgetAlert
        fields = [
            'budget', 'alert_type', 'threshold_percentage', 
            'threshold_amount', 'notification_method', 'recipients'
        ]
        widgets = {
            'budget': forms.Select(attrs={'class': 'form-control'}),
            'alert_type': forms.Select(attrs={'class': 'form-control'}),
            'threshold_percentage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'threshold_amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'notification_method': forms.Select(attrs={'class': 'form-control'}),
            'recipients': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class BankForm(forms.ModelForm):
    """
    Bank account form
    """
    class Meta:
        model = Bank
        fields = [
            'name', 'name_ar', 'account_number', 'account_name',
            'branch', 'swift_code', 'iban', 'opening_balance', 'account'
        ]
        widgets = {
            'opening_balance': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class FeeTypeForm(forms.ModelForm):
    """
    Fee type form
    """
    class Meta:
        model = FeeType
        fields = ['name', 'name_ar', 'description', 'account', 'is_mandatory']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class GradeFeeForm(forms.ModelForm):
    """
    Grade fee form
    """
    class Meta:
        model = GradeFee
        fields = ['grade', 'fee_type', 'amount', 'academic_year']
        widgets = {
            'amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class StudentFeeForm(forms.ModelForm):
    """
    Student fee form
    """
    class Meta:
        model = StudentFee
        fields = ['student', 'grade_fee', 'amount', 'due_date', 'discount_amount', 'notes']
        widgets = {
            'due_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'discount_amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class FinancialYearForm(forms.ModelForm):
    """
    Financial year form
    """
    class Meta:
        model = FinancialYear
        fields = ['name', 'start_date', 'end_date', 'is_current']
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date:
            if start_date >= end_date:
                raise ValidationError(_('Start date must be before end date.'))

        return cleaned_data


class TransactionForm(forms.ModelForm):
    """
    Transaction form for double-entry bookkeeping
    """
    class Meta:
        model = Transaction
        fields = [
            'transaction_date', 'transaction_type', 'description', 'reference',
            'total_amount', 'requires_approval'
        ]
        widgets = {
            'transaction_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'total_amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        self.school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            Fieldset(
                _('Transaction Information'),
                Row(
                    Column('transaction_date', css_class='form-group col-md-6 mb-3'),
                    Column('transaction_type', css_class='form-group col-md-6 mb-3'),
                ),
                'description',
                Row(
                    Column('reference', css_class='form-group col-md-6 mb-3'),
                    Column('total_amount', css_class='form-group col-md-6 mb-3'),
                ),
                'requires_approval',
            ),
            FormActions(
                Submit('submit', _('Save Transaction'), css_class='btn btn-primary'),
                HTML('<a href="{% url "finance:transactions_list" %}" class="btn btn-secondary ms-2">' + str(_('Cancel')) + '</a>'),
            )
        )

    def clean_total_amount(self):
        """Validate total amount is positive"""
        total_amount = self.cleaned_data.get('total_amount')
        if total_amount and total_amount <= 0:
            raise ValidationError(_('Total amount must be greater than zero'))
        return total_amount


class TransactionEntryForm(forms.ModelForm):
    """
    Transaction entry form for individual debit/credit entries
    """
    class Meta:
        model = TransactionEntry
        fields = [
            'account', 'description', 'debit_amount', 'credit_amount',
            'cost_center', 'reference'
        ]
        widgets = {
            'description': forms.Textarea(attrs={'rows': 2, 'class': 'form-control'}),
            'debit_amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'credit_amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        self.school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        # Filter accounts to only detail accounts (non-header) that allow manual entries
        if self.school:
            self.fields['account'].queryset = Account.objects.filter(
                school=self.school,
                is_header=False,
                is_active=True,
                allow_manual_entries=True
            ).select_related('account_type')
            
            self.fields['cost_center'].queryset = CostCenter.objects.filter(
                school=self.school,
                is_active=True
            )

    def clean(self):
        """Validate entry has either debit or credit, but not both"""
        cleaned_data = super().clean()
        debit_amount = cleaned_data.get('debit_amount', 0)
        credit_amount = cleaned_data.get('credit_amount', 0)

        if debit_amount > 0 and credit_amount > 0:
            raise ValidationError(_('An entry cannot have both debit and credit amounts'))
        
        if debit_amount == 0 and credit_amount == 0:
            raise ValidationError(_('An entry must have either a debit or credit amount'))

        return cleaned_data


class TransactionEntryFormSet(forms.BaseInlineFormSet):
    """
    Formset for transaction entries with validation
    """
    def clean(self):
        """Validate that transaction is balanced"""
        if any(self.errors):
            return
        
        total_debits = 0
        total_credits = 0
        
        for form in self.forms:
            if form.cleaned_data and not form.cleaned_data.get('DELETE', False):
                total_debits += form.cleaned_data.get('debit_amount', 0)
                total_credits += form.cleaned_data.get('credit_amount', 0)
        
        if abs(total_debits - total_credits) > 0.01:  # Allow for small rounding differences
            raise ValidationError(_('Transaction must be balanced: Total debits must equal total credits'))
        
        if total_debits == 0 and total_credits == 0:
            raise ValidationError(_('Transaction must have at least one entry'))


# Create the formset
TransactionEntryInlineFormSet = forms.inlineformset_factory(
    Transaction,
    TransactionEntry,
    form=TransactionEntryForm,
    formset=TransactionEntryFormSet,
    extra=2,
    min_num=2,
    validate_min=True,
    can_delete=True
)


class JournalEntryFormSet(forms.BaseFormSet):
    """
    Formset for creating multiple journal entries at once
    """
    def clean(self):
        """Validate that entries are balanced"""
        if any(self.errors):
            return
        
        total_debits = 0
        total_credits = 0
        
        for form in self.forms:
            if form.cleaned_data and not form.cleaned_data.get('DELETE', False):
                total_debits += form.cleaned_data.get('debit_amount', 0)
                total_credits += form.cleaned_data.get('credit_amount', 0)
        
        if abs(total_debits - total_credits) > 0.01:
            raise ValidationError(_('Journal entries must be balanced: Total debits must equal total credits'))


class TransactionApprovalForm(forms.Form):
    """
    Form for approving transactions
    """
    approval_notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        label=_('Approval Notes'),
        help_text=_('Optional notes about the approval')
    )
    
    action = forms.ChoiceField(
        choices=[
            ('approve', _('Approve')),
            ('reject', _('Reject')),
        ],
        widget=forms.RadioSelect(),
        label=_('Action')
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            'action',
            'approval_notes',
            FormActions(
                Submit('submit', _('Submit'), css_class='btn btn-primary'),
            )
        )


class TransactionSearchForm(forms.Form):
    """
    Form for searching and filtering transactions
    """
    transaction_id = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': _('Transaction ID')}),
        label=_('Transaction ID')
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        label=_('Date From')
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        label=_('Date To')
    )
    
    transaction_type = forms.ChoiceField(
        required=False,
        choices=[('', _('All Types'))] + list(Transaction.TRANSACTION_TYPES),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Transaction Type')
    )
    
    status = forms.ChoiceField(
        required=False,
        choices=[('', _('All Statuses'))] + list(Transaction.STATUS_CHOICES),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Status')
    )
    
    account = forms.ModelChoiceField(
        required=False,
        queryset=Account.objects.none(),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Account')
    )

    def __init__(self, *args, **kwargs):
        school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if school:
            self.fields['account'].queryset = Account.objects.filter(
                school=school,
                is_active=True
            ).select_related('account_type')
        
        self.helper = FormHelper()
        self.helper.form_method = 'get'
        self.helper.layout = Layout(
            Row(
                Column('transaction_id', css_class='form-group col-md-4 mb-3'),
                Column('transaction_type', css_class='form-group col-md-4 mb-3'),
                Column('status', css_class='form-group col-md-4 mb-3'),
            ),
            Row(
                Column('date_from', css_class='form-group col-md-4 mb-3'),
                Column('date_to', css_class='form-group col-md-4 mb-3'),
                Column('account', css_class='form-group col-md-4 mb-3'),
            ),
            FormActions(
                Submit('submit', _('Search'), css_class='btn btn-primary'),
                HTML('<a href="?" class="btn btn-secondary ms-2">' + str(_('Clear')) + '</a>'),
            )
        )


# Quick payment form for fees payment page
class QuickPaymentForm(forms.Form):
    """
    Quick payment form for processing multiple fee payments
    """
    student = forms.ModelChoiceField(
        queryset=Student.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Student')
    )
    
    payment_method = forms.ChoiceField(
        choices=Payment.PAYMENT_METHODS,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Payment Method')
    )
    
    amount_paid = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        widget=forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
        label=_('Amount Paid')
    )
    
    reference_number = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control'}),
        label=_('Reference Number')
    )
    
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        label=_('Notes')
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class DoubleEntryTransactionForm(forms.Form):
    """
    Form for creating double-entry transactions with multiple entries
    """
    transaction_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        label=_('Transaction Date')
    )
    
    transaction_type = forms.ChoiceField(
        choices=list(Transaction.TRANSACTION_TYPES),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Transaction Type'),
        initial='manual'
    )
    
    description = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        label=_('Description')
    )
    
    reference = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control'}),
        label=_('Reference'),
        help_text=_('External reference number or document')
    )
    
    requires_approval = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        label=_('Requires Approval'),
        help_text=_('Check if this transaction requires approval before posting')
    )

    def __init__(self, *args, **kwargs):
        self.school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_id = 'transaction-form'
        self.helper.layout = Layout(
            Fieldset(
                _('Transaction Information'),
                Row(
                    Column('transaction_date', css_class='form-group col-md-6 mb-3'),
                    Column('transaction_type', css_class='form-group col-md-6 mb-3'),
                ),
                'description',
                Row(
                    Column('reference', css_class='form-group col-md-8 mb-3'),
                    Column('requires_approval', css_class='form-group col-md-4 mb-3'),
                ),
            ),
            HTML('<div id="transaction-entries-section">'),
            HTML('<h5 class="mt-4 mb-3">' + str(_('Transaction Entries')) + '</h5>'),
            HTML('<div id="entries-container"></div>'),
            HTML('<button type="button" id="add-entry-btn" class="btn btn-secondary mb-3">' + str(_('Add Entry')) + '</button>'),
            HTML('<div class="row mt-3">'),
            HTML('<div class="col-md-6">'),
            HTML('<strong>' + str(_('Total Debits:')) + ' <span id="total-debits">0.00</span></strong>'),
            HTML('</div>'),
            HTML('<div class="col-md-6">'),
            HTML('<strong>' + str(_('Total Credits:')) + ' <span id="total-credits">0.00</span></strong>'),
            HTML('</div>'),
            HTML('</div>'),
            HTML('<div class="alert alert-info mt-2" id="balance-status">'),
            HTML('<i class="fas fa-info-circle"></i> ' + str(_('Transaction must be balanced (debits = credits)'))),
            HTML('</div>'),
            HTML('</div>'),
            FormActions(
                Submit('submit', _('Save Transaction'), css_class='btn btn-primary', id='save-transaction-btn'),
                HTML('<a href="{% url "finance:transactions_list" %}" class="btn btn-secondary ms-2">' + str(_('Cancel')) + '</a>'),
            )
        )


class TransactionEntryInlineForm(forms.Form):
    """
    Inline form for transaction entries
    """
    account = forms.ModelChoiceField(
        queryset=Account.objects.none(),
        widget=forms.Select(attrs={'class': 'form-control entry-account'}),
        label=_('Account')
    )
    
    description = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control entry-description'}),
        label=_('Description')
    )
    
    debit_amount = forms.DecimalField(
        required=False,
        max_digits=15,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'step': '0.01', 
            'class': 'form-control entry-debit',
            'placeholder': '0.00'
        }),
        label=_('Debit Amount')
    )
    
    credit_amount = forms.DecimalField(
        required=False,
        max_digits=15,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'step': '0.01', 
            'class': 'form-control entry-credit',
            'placeholder': '0.00'
        }),
        label=_('Credit Amount')
    )
    
    cost_center = forms.ModelChoiceField(
        required=False,
        queryset=CostCenter.objects.none(),
        widget=forms.Select(attrs={'class': 'form-control entry-cost-center'}),
        label=_('Cost Center')
    )
    
    reference = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control entry-reference'}),
        label=_('Reference')
    )

    def __init__(self, *args, **kwargs):
        school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if school:
            self.fields['account'].queryset = Account.objects.filter(
                school=school,
                is_header=False,
                is_active=True,
                allow_manual_entries=True
            ).select_related('account_type').order_by('code')
            
            self.fields['cost_center'].queryset = CostCenter.objects.filter(
                school=school,
                is_active=True
            ).order_by('code')

    def clean(self):
        """Validate entry has either debit or credit, but not both"""
        cleaned_data = super().clean()
        debit_amount = cleaned_data.get('debit_amount', 0) or 0
        credit_amount = cleaned_data.get('credit_amount', 0) or 0

        if debit_amount > 0 and credit_amount > 0:
            raise ValidationError(_('An entry cannot have both debit and credit amounts'))
        
        if debit_amount == 0 and credit_amount == 0:
            raise ValidationError(_('An entry must have either a debit or credit amount'))

        return cleaned_data


class BulkTransactionApprovalForm(forms.Form):
    """
    Form for bulk approval of transactions
    """
    transaction_ids = forms.CharField(
        widget=forms.HiddenInput(),
        label=_('Transaction IDs')
    )
    
    action = forms.ChoiceField(
        choices=[
            ('approve', _('Approve Selected')),
            ('reject', _('Reject Selected')),
        ],
        widget=forms.RadioSelect(),
        label=_('Action')
    )
    
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        label=_('Notes'),
        help_text=_('Optional notes for all selected transactions')
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            'transaction_ids',
            'action',
            'notes',
            FormActions(
                Submit('submit', _('Process Selected'), css_class='btn btn-primary'),
            )
        )

    def clean_transaction_ids(self):
        """Validate transaction IDs"""
        transaction_ids = self.cleaned_data.get('transaction_ids', '')
        if not transaction_ids:
            raise ValidationError(_('No transactions selected'))
        
        try:
            ids = [int(id.strip()) for id in transaction_ids.split(',') if id.strip()]
            if not ids:
                raise ValidationError(_('No valid transaction IDs provided'))
            return ids
        except ValueError:
            raise ValidationError(_('Invalid transaction IDs format'))


class AccountLedgerReportForm(forms.Form):
    """
    Form for generating account ledger reports
    """
    account = forms.ModelChoiceField(
        queryset=Account.objects.none(),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Account')
    )
    
    start_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        label=_('Start Date')
    )
    
    end_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        label=_('End Date')
    )
    
    include_opening_balance = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        label=_('Include Opening Balance')
    )
    
    format = forms.ChoiceField(
        choices=[
            ('html', _('HTML')),
            ('pdf', _('PDF')),
            ('excel', _('Excel')),
        ],
        initial='html',
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Format')
    )

    def __init__(self, *args, **kwargs):
        school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if school:
            self.fields['account'].queryset = Account.objects.filter(
                school=school,
                is_header=False,
                is_active=True
            ).select_related('account_type').order_by('code')
        
        # Set default dates (current year)
        from datetime import date
        current_year = date.today().year
        self.fields['start_date'].initial = date(current_year, 1, 1)
        self.fields['end_date'].initial = date.today()
        
        self.helper = FormHelper()
        self.helper.form_method = 'get'
        self.helper.layout = Layout(
            Row(
                Column('account', css_class='form-group col-md-12 mb-3'),
            ),
            Row(
                Column('start_date', css_class='form-group col-md-6 mb-3'),
                Column('end_date', css_class='form-group col-md-6 mb-3'),
            ),
            Row(
                Column('include_opening_balance', css_class='form-group col-md-6 mb-3'),
                Column('format', css_class='form-group col-md-6 mb-3'),
            ),
            FormActions(
                Submit('submit', _('Generate Report'), css_class='btn btn-primary'),
            )
        )

    def clean(self):
        """Validate date range"""
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date:
            if start_date > end_date:
                raise ValidationError(_('Start date must be before or equal to end date'))

        return cleaned_data


class JournalRegisterReportForm(forms.Form):
    """
    Form for generating journal register reports
    """
    start_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        label=_('Start Date')
    )
    
    end_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        label=_('End Date')
    )
    
    transaction_type = forms.ChoiceField(
        required=False,
        choices=[('', _('All Types'))] + list(Transaction.TRANSACTION_TYPES),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Transaction Type')
    )
    
    account_type = forms.ModelChoiceField(
        required=False,
        queryset=AccountType.objects.none(),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Account Type')
    )
    
    format = forms.ChoiceField(
        choices=[
            ('html', _('HTML')),
            ('pdf', _('PDF')),
            ('excel', _('Excel')),
        ],
        initial='html',
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Format')
    )

    def __init__(self, *args, **kwargs):
        school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if school:
            self.fields['account_type'].queryset = AccountType.objects.filter(
                school=school
            ).order_by('name')
        
        # Set default dates (current month)
        from datetime import date
        today = date.today()
        self.fields['start_date'].initial = date(today.year, today.month, 1)
        self.fields['end_date'].initial = today
        
        self.helper = FormHelper()
        self.helper.form_method = 'get'
        self.helper.layout = Layout(
            Row(
                Column('start_date', css_class='form-group col-md-6 mb-3'),
                Column('end_date', css_class='form-group col-md-6 mb-3'),
            ),
            Row(
                Column('transaction_type', css_class='form-group col-md-6 mb-3'),
                Column('account_type', css_class='form-group col-md-6 mb-3'),
            ),
            Row(
                Column('format', css_class='form-group col-md-6 mb-3'),
            ),
            FormActions(
                Submit('submit', _('Generate Report'), css_class='btn btn-primary'),
            )
        )

    def clean(self):
        """Validate date range"""
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date:
            if start_date > end_date:
                raise ValidationError(_('Start date must be before or equal to end date'))

        return cleaned_data