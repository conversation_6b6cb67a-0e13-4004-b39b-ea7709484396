# Generated by Django 5.2.4 on 2025-08-01 11:08

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        ("finance", "0007_alter_bank_current_balance"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="PaymentGateway",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("name", models.CharField(max_length=100, verbose_name="Gateway Name")),
                (
                    "gateway_type",
                    models.CharField(
                        choices=[
                            ("stripe", "Stripe"),
                            ("paypal", "PayPal"),
                            ("square", "Square"),
                            ("razorpay", "Razorpay"),
                            ("paytabs", "PayTabs"),
                            ("mada", "Mada"),
                            ("visa", "Visa"),
                            ("mastercard", "Mastercard"),
                            ("bank_transfer", "Bank Transfer"),
                            ("custom", "Custom Gateway"),
                        ],
                        max_length=20,
                        verbose_name="Gateway Type",
                    ),
                ),
                (
                    "api_key",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="API Key"
                    ),
                ),
                (
                    "secret_key",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="Secret Key"
                    ),
                ),
                (
                    "webhook_url",
                    models.URLField(blank=True, null=True, verbose_name="Webhook URL"),
                ),
                (
                    "is_sandbox",
                    models.BooleanField(default=True, verbose_name="Is Sandbox Mode"),
                ),
                (
                    "is_enabled",
                    models.BooleanField(default=True, verbose_name="Is Enabled"),
                ),
                (
                    "configuration",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Gateway-specific configuration parameters",
                        verbose_name="Additional Configuration",
                    ),
                ),
                (
                    "transaction_fee_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=5,
                        verbose_name="Transaction Fee Percentage",
                    ),
                ),
                (
                    "transaction_fee_fixed",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        verbose_name="Fixed Transaction Fee",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Payment Gateway",
                "verbose_name_plural": "Payment Gateways",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="PaymentTransaction",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "transaction_id",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="Transaction ID"
                    ),
                ),
                (
                    "gateway_transaction_id",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="Gateway Transaction ID",
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=10,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="Amount",
                    ),
                ),
                (
                    "gateway_fee",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        verbose_name="Gateway Fee",
                    ),
                ),
                (
                    "net_amount",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="Net Amount"
                    ),
                ),
                (
                    "currency",
                    models.CharField(
                        default="SAR", max_length=3, verbose_name="Currency"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("processing", "Processing"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("cancelled", "Cancelled"),
                            ("refunded", "Refunded"),
                            ("partially_refunded", "Partially Refunded"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "gateway_response",
                    models.JSONField(
                        blank=True, default=dict, verbose_name="Gateway Response"
                    ),
                ),
                (
                    "processed_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Processed At"
                    ),
                ),
                (
                    "failure_reason",
                    models.TextField(
                        blank=True, null=True, verbose_name="Failure Reason"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "gateway",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transactions",
                        to="finance.paymentgateway",
                        verbose_name="Payment Gateway",
                    ),
                ),
                (
                    "payment",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transactions",
                        to="finance.payment",
                        verbose_name="Payment",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Payment Transaction",
                "verbose_name_plural": "Payment Transactions",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="PaymentRefund",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "refund_id",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="Refund ID"
                    ),
                ),
                (
                    "gateway_refund_id",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="Gateway Refund ID",
                    ),
                ),
                (
                    "refund_type",
                    models.CharField(
                        choices=[
                            ("full", "Full Refund"),
                            ("partial", "Partial Refund"),
                        ],
                        max_length=10,
                        verbose_name="Refund Type",
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=10,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="Refund Amount",
                    ),
                ),
                ("reason", models.TextField(verbose_name="Refund Reason")),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("processing", "Processing"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "processed_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Processed At"
                    ),
                ),
                (
                    "gateway_response",
                    models.JSONField(
                        blank=True, default=dict, verbose_name="Gateway Response"
                    ),
                ),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_refunds",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Approved By",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "requested_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="requested_refunds",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Requested By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "payment_transaction",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="refunds",
                        to="finance.paymenttransaction",
                        verbose_name="Payment Transaction",
                    ),
                ),
            ],
            options={
                "verbose_name": "Payment Refund",
                "verbose_name_plural": "Payment Refunds",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="PaymentAnalytics",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("date", models.DateField(verbose_name="Date")),
                (
                    "total_payments",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Total Payments"
                    ),
                ),
                (
                    "total_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        verbose_name="Total Amount",
                    ),
                ),
                (
                    "successful_payments",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Successful Payments"
                    ),
                ),
                (
                    "failed_payments",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Failed Payments"
                    ),
                ),
                (
                    "refunded_payments",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Refunded Payments"
                    ),
                ),
                (
                    "refunded_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        verbose_name="Refunded Amount",
                    ),
                ),
                (
                    "gateway_fees",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        verbose_name="Gateway Fees",
                    ),
                ),
                (
                    "net_revenue",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        verbose_name="Net Revenue",
                    ),
                ),
                (
                    "payment_methods",
                    models.JSONField(
                        default=dict, verbose_name="Payment Methods Breakdown"
                    ),
                ),
                (
                    "gateway_breakdown",
                    models.JSONField(default=dict, verbose_name="Gateway Breakdown"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Payment Analytics",
                "verbose_name_plural": "Payment Analytics",
                "ordering": ["-date"],
                "unique_together": {("school", "date")},
            },
        ),
        migrations.CreateModel(
            name="PaymentReminder",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "reminder_type",
                    models.CharField(
                        choices=[
                            ("email", "Email"),
                            ("sms", "SMS"),
                            ("whatsapp", "WhatsApp"),
                            ("push", "Push Notification"),
                        ],
                        max_length=20,
                        verbose_name="Reminder Type",
                    ),
                ),
                ("scheduled_date", models.DateTimeField(verbose_name="Scheduled Date")),
                (
                    "sent_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Sent Date"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("scheduled", "Scheduled"),
                            ("sent", "Sent"),
                            ("failed", "Failed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="scheduled",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                ("message_template", models.TextField(verbose_name="Message Template")),
                (
                    "recipient",
                    models.CharField(
                        help_text="Email address, phone number, or user ID",
                        max_length=255,
                        verbose_name="Recipient",
                    ),
                ),
                (
                    "attempts",
                    models.PositiveIntegerField(default=0, verbose_name="Attempts"),
                ),
                (
                    "max_attempts",
                    models.PositiveIntegerField(default=3, verbose_name="Max Attempts"),
                ),
                (
                    "error_message",
                    models.TextField(
                        blank=True, null=True, verbose_name="Error Message"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "student_fee",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reminders",
                        to="finance.studentfee",
                        verbose_name="Student Fee",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Payment Reminder",
                "verbose_name_plural": "Payment Reminders",
                "ordering": ["scheduled_date"],
                "indexes": [
                    models.Index(
                        fields=["school", "status"],
                        name="finance_pay_school__3f0746_idx",
                    ),
                    models.Index(
                        fields=["school", "scheduled_date"],
                        name="finance_pay_school__2f506f_idx",
                    ),
                    models.Index(
                        fields=["student_fee"], name="finance_pay_student_8acf80_idx"
                    ),
                ],
            },
        ),
        migrations.AddIndex(
            model_name="paymenttransaction",
            index=models.Index(
                fields=["school", "status"], name="finance_pay_school__81c5f4_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="paymenttransaction",
            index=models.Index(
                fields=["school", "gateway"], name="finance_pay_school__27f193_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="paymenttransaction",
            index=models.Index(
                fields=["transaction_id"], name="finance_pay_transac_eb634d_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="paymenttransaction",
            index=models.Index(
                fields=["gateway_transaction_id"], name="finance_pay_gateway_41ca54_idx"
            ),
        ),
    ]
