# Generated by Django 5.2.4 on 2025-08-02 09:45

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        ("finance", "0009_budget_budgetitem"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="BudgetAlert",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "alert_type",
                    models.CharField(
                        choices=[
                            ("utilization", "Budget Utilization Alert"),
                            ("variance", "Budget Variance Alert"),
                            ("overrun", "Budget Overrun Alert"),
                            ("deadline", "Budget Deadline Alert"),
                        ],
                        max_length=20,
                        verbose_name="Alert Type",
                    ),
                ),
                (
                    "threshold_percentage",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Alert when utilization exceeds this percentage",
                        max_digits=5,
                        null=True,
                        verbose_name="Threshold Percentage",
                    ),
                ),
                (
                    "threshold_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Alert when variance exceeds this amount",
                        max_digits=15,
                        null=True,
                        verbose_name="Threshold Amount",
                    ),
                ),
                (
                    "notification_method",
                    models.CharField(
                        choices=[
                            ("email", "Email"),
                            ("sms", "SMS"),
                            ("whatsapp", "WhatsApp"),
                            ("push", "Push Notification"),
                            ("all", "All Methods"),
                        ],
                        default="email",
                        max_length=20,
                        verbose_name="Notification Method",
                    ),
                ),
                (
                    "recipients",
                    models.TextField(
                        help_text="Email addresses or phone numbers, separated by commas",
                        verbose_name="Recipients",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("triggered", "Triggered"),
                            ("resolved", "Resolved"),
                            ("disabled", "Disabled"),
                        ],
                        default="active",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "last_triggered",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Last Triggered"
                    ),
                ),
                (
                    "trigger_count",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Trigger Count"
                    ),
                ),
                (
                    "budget",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="alerts",
                        to="finance.budget",
                        verbose_name="Budget",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Budget Alert",
                "verbose_name_plural": "Budget Alerts",
                "ordering": ["budget", "alert_type"],
                "indexes": [
                    models.Index(
                        fields=["school", "status"],
                        name="finance_bud_school__c8c8f2_idx",
                    ),
                    models.Index(
                        fields=["budget", "alert_type"],
                        name="finance_bud_budget__51989c_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="BudgetApprovalWorkflow",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "workflow_step",
                    models.PositiveIntegerField(
                        default=1, verbose_name="Workflow Step"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("in_review", "In Review"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "action_taken",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("approve", "Approve"),
                            ("reject", "Reject"),
                            ("request_changes", "Request Changes"),
                        ],
                        max_length=20,
                        null=True,
                        verbose_name="Action Taken",
                    ),
                ),
                (
                    "comments",
                    models.TextField(blank=True, null=True, verbose_name="Comments"),
                ),
                (
                    "reviewed_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Reviewed At"
                    ),
                ),
                (
                    "due_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Due Date"
                    ),
                ),
                (
                    "approver",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="budget_approvals",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Approver",
                    ),
                ),
                (
                    "budget",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="approval_workflows",
                        to="finance.budget",
                        verbose_name="Budget",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Budget Approval Workflow",
                "verbose_name_plural": "Budget Approval Workflows",
                "ordering": ["budget", "workflow_step"],
                "unique_together": {("budget", "workflow_step", "approver")},
            },
        ),
        migrations.CreateModel(
            name="BudgetRevision",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "revision_number",
                    models.CharField(max_length=20, verbose_name="Revision Number"),
                ),
                (
                    "revision_type",
                    models.CharField(
                        choices=[
                            ("increase", "Budget Increase"),
                            ("decrease", "Budget Decrease"),
                            ("reallocation", "Budget Reallocation"),
                            ("adjustment", "Budget Adjustment"),
                        ],
                        max_length=20,
                        verbose_name="Revision Type",
                    ),
                ),
                ("reason", models.TextField(verbose_name="Reason for Revision")),
                (
                    "previous_amount",
                    models.DecimalField(
                        decimal_places=2, max_digits=15, verbose_name="Previous Amount"
                    ),
                ),
                (
                    "new_amount",
                    models.DecimalField(
                        decimal_places=2, max_digits=15, verbose_name="New Amount"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("pending_approval", "Pending Approval"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("implemented", "Implemented"),
                        ],
                        default="draft",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "approved_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Approved At"
                    ),
                ),
                (
                    "implemented_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Implemented At"
                    ),
                ),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_budget_revisions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Approved By",
                    ),
                ),
                (
                    "budget",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="revisions",
                        to="finance.budget",
                        verbose_name="Budget",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "requested_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="budget_revision_requests",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Requested By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Budget Revision",
                "verbose_name_plural": "Budget Revisions",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["school", "status"],
                        name="finance_bud_school__eff6b3_idx",
                    ),
                    models.Index(
                        fields=["budget", "revision_number"],
                        name="finance_bud_budget__e47cbb_idx",
                    ),
                ],
            },
        ),
    ]
