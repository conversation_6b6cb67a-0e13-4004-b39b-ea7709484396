from django.db import models
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from django.core.validators import MinValueValidator
from django.db.models import Q, Sum
from decimal import Decimal
from core.models import BaseModel, AcademicYear
from students.models import Student, Grade


class AccountManager(models.Manager):
    """Custom manager for Account model"""
    
    def active(self):
        """Return only active accounts"""
        return self.filter(is_active=True, archived_at__isnull=True)
    
    def archived(self):
        """Return only archived accounts"""
        return self.filter(archived_at__isnull=False)
    
    def header_accounts(self):
        """Return only header accounts"""
        return self.filter(is_header=True)
    
    def detail_accounts(self):
        """Return only detail accounts (non-header)"""
        return self.filter(is_header=False)
    
    def by_type(self, account_type):
        """Return accounts by type"""
        return self.filter(account_type__type=account_type)
    
    def root_accounts(self):
        """Return root level accounts (no parent)"""
        return self.filter(parent__isnull=True)
    
    def build_tree(self):
        """Build account tree structure"""
        accounts = list(self.active().select_related('parent', 'account_type'))
        account_dict = {acc.id: acc for acc in accounts}
        
        # Add children to each account
        for account in accounts:
            account.children_list = []
        
        for account in accounts:
            if account.parent_id and account.parent_id in account_dict:
                parent = account_dict[account.parent_id]
                parent.children_list.append(account)
        
        # Return root accounts with their children
        return [acc for acc in accounts if acc.parent_id is None]
    
    def get_account_balances(self, as_of_date=None):
        """Get balances for all accounts"""
        from datetime import date
        
        if as_of_date is None:
            as_of_date = date.today()
        
        accounts = self.active().select_related('account_type')
        balances = {}
        
        for account in accounts:
            balances[account.id] = account.get_balance(as_of_date)
        
        return balances


class AccountType(BaseModel):
    """
    Account type model for chart of accounts
    """
    ACCOUNT_TYPES = (
        ('asset', _('Asset')),
        ('liability', _('Liability')),
        ('equity', _('Equity')),
        ('revenue', _('Revenue')),
        ('expense', _('Expense')),
    )

    name = models.CharField(
        max_length=100,
        verbose_name=_('Account Type Name')
    )

    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Account Type Name (Arabic)')
    )

    type = models.CharField(
        max_length=20,
        choices=ACCOUNT_TYPES,
        verbose_name=_('Type')
    )

    class Meta:
        verbose_name = _('Account Type')
        verbose_name_plural = _('Account Types')
        ordering = ['name']

    def __str__(self):
        return self.name


class Account(BaseModel):
    """
    Chart of accounts model with enhanced hierarchy and controls
    """
    code = models.CharField(
        max_length=20,
        verbose_name=_('Account Code'),
        help_text=_('Unique account code (e.g., 1000, 1100)')
    )

    name = models.CharField(
        max_length=200,
        verbose_name=_('Account Name')
    )

    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Account Name (Arabic)')
    )

    account_type = models.ForeignKey(
        AccountType,
        on_delete=models.CASCADE,
        related_name='accounts',
        verbose_name=_('Account Type')
    )

    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='children',
        verbose_name=_('Parent Account')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    is_header = models.BooleanField(
        default=False,
        verbose_name=_('Is Header Account'),
        help_text=_('Header accounts cannot have transactions posted to them')
    )

    # Enhanced fields for account controls
    level = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Account Level'),
        help_text=_('Hierarchy level (0 for root accounts)')
    )

    path = models.CharField(
        max_length=500,
        blank=True,
        verbose_name=_('Account Path'),
        help_text=_('Full path from root to this account')
    )

    is_system_account = models.BooleanField(
        default=False,
        verbose_name=_('Is System Account'),
        help_text=_('System accounts cannot be deleted')
    )

    allow_manual_entries = models.BooleanField(
        default=True,
        verbose_name=_('Allow Manual Entries'),
        help_text=_('Allow manual journal entries to this account')
    )

    is_reconcilable = models.BooleanField(
        default=False,
        verbose_name=_('Is Reconcilable'),
        help_text=_('Account requires reconciliation (e.g., bank accounts)')
    )

    opening_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Opening Balance')
    )

    current_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_('Current Balance')
    )

    # Archiving fields
    archived_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Archived At')
    )

    archived_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='archived_accounts',
        verbose_name=_('Archived By')
    )

    archive_reason = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Archive Reason')
    )

    objects = AccountManager()

    class Meta:
        verbose_name = _('Account')
        verbose_name_plural = _('Accounts')
        ordering = ['code']
        unique_together = [['school', 'code']]
        indexes = [
            models.Index(fields=['school', 'code']),
            models.Index(fields=['school', 'account_type']),
            models.Index(fields=['school', 'parent']),
            models.Index(fields=['school', 'is_active']),
            models.Index(fields=['school', 'archived_at']),
        ]

    def __str__(self):
        return f"{self.code} - {self.name}"

    def clean(self):
        from django.core.exceptions import ValidationError
        
        # Validate account code format
        if not self.code.isdigit():
            raise ValidationError(_('Account code must contain only digits'))
        
        # Validate parent account
        if self.parent:
            if self.parent.id == self.id:
                raise ValidationError(_('Account cannot be its own parent'))
            
            if not self.parent.is_header:
                raise ValidationError(_('Parent account must be a header account'))
            
            # Check for circular references
            if self._check_circular_reference():
                raise ValidationError(_('Circular reference detected in account hierarchy'))
        
        # Header accounts cannot have transactions
        if self.is_header and hasattr(self, 'journal_entries') and self.journal_entries.exists():
            raise ValidationError(_('Header accounts cannot have journal entries'))

    def save(self, *args, **kwargs):
        # Calculate level and path
        if self.parent:
            self.level = self.parent.level + 1
            self.path = f"{self.parent.path}/{self.code}" if self.parent.path else self.code
        else:
            self.level = 0
            self.path = self.code

        # Initialize current balance if not set
        if self.current_balance is None:
            if self.opening_balance is not None:
                self.current_balance = self.opening_balance
            else:
                self.current_balance = Decimal('0')

        super().save(*args, **kwargs)
        
        # Update children paths if path changed
        if self.children.exists():
            self._update_children_paths()

    def _check_circular_reference(self):
        """Check for circular references in account hierarchy"""
        visited = set()
        current = self.parent
        
        while current:
            if current.id in visited or current.id == self.id:
                return True
            visited.add(current.id)
            current = current.parent
        
        return False

    def _update_children_paths(self):
        """Update paths for all child accounts"""
        for child in self.children.all():
            child.path = f"{self.path}/{child.code}"
            child.save(update_fields=['path'])

    @property
    def full_name(self):
        """Return full account name with code"""
        return f"{self.code} - {self.name}"

    @property
    def is_archived(self):
        """Check if account is archived"""
        return self.archived_at is not None

    @property
    def balance_type(self):
        """Return the normal balance type for this account"""
        if self.account_type.type in ['asset', 'expense']:
            return 'debit'
        else:
            return 'credit'

    def get_balance(self, as_of_date=None):
        """Calculate account balance as of a specific date"""
        from django.db.models import Sum, Q
        from datetime import date
        
        if as_of_date is None:
            as_of_date = date.today()
        
        # Get all journal entries for this account up to the date
        entries = self.journal_entries.filter(
            entry_date__lte=as_of_date,
            is_posted=True
        )
        
        debits = entries.aggregate(total=Sum('debit_amount'))['total'] or 0
        credits = entries.aggregate(total=Sum('credit_amount'))['total'] or 0
        
        # Calculate balance based on account type
        if self.balance_type == 'debit':
            return self.opening_balance + debits - credits
        else:
            return self.opening_balance + credits - debits

    def get_children_recursive(self):
        """Get all child accounts recursively"""
        children = list(self.children.all())
        for child in self.children.all():
            children.extend(child.get_children_recursive())
        return children

    def can_be_deleted(self):
        """Check if account can be deleted"""
        if self.is_system_account:
            return False, _('System accounts cannot be deleted')
        
        if self.journal_entries.exists():
            return False, _('Account has journal entries and cannot be deleted')
        
        if self.children.exists():
            return False, _('Account has child accounts and cannot be deleted')
        
        return True, None

    def archive(self, user, reason=None):
        """Archive the account"""
        from django.utils import timezone
        
        can_archive, error_msg = self.can_be_archived()
        if not can_archive:
            raise ValidationError(error_msg)
        
        self.archived_at = timezone.now()
        self.archived_by = user
        self.archive_reason = reason
        self.is_active = False
        self.save(update_fields=['archived_at', 'archived_by', 'archive_reason', 'is_active'])

    def unarchive(self):
        """Unarchive the account"""
        self.archived_at = None
        self.archived_by = None
        self.archive_reason = None
        self.is_active = True
        self.save(update_fields=['archived_at', 'archived_by', 'archive_reason', 'is_active'])

    def can_be_archived(self):
        """Check if account can be archived"""
        if self.is_system_account:
            return False, _('System accounts cannot be archived')
        
        # Check if any child accounts are active
        if self.children.filter(is_active=True).exists():
            return False, _('Account has active child accounts and cannot be archived')
        
        return True, None


class FeeType(BaseModel):
    """
    Fee type model
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('Fee Type Name')
    )

    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Fee Type Name (Arabic)')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    account = models.ForeignKey(
        Account,
        on_delete=models.CASCADE,
        related_name='fee_types',
        verbose_name=_('Account')
    )

    is_mandatory = models.BooleanField(
        default=True,
        verbose_name=_('Is Mandatory')
    )

    class Meta:
        verbose_name = _('Fee Type')
        verbose_name_plural = _('Fee Types')
        ordering = ['name']

    def __str__(self):
        return self.name


class GradeFee(BaseModel):
    """
    Grade-specific fee structure
    """
    grade = models.ForeignKey(
        Grade,
        on_delete=models.CASCADE,
        related_name='fees',
        verbose_name=_('Grade')
    )

    fee_type = models.ForeignKey(
        FeeType,
        on_delete=models.CASCADE,
        related_name='grade_fees',
        verbose_name=_('Fee Type')
    )

    academic_year = models.ForeignKey(
        AcademicYear,
        on_delete=models.CASCADE,
        related_name='grade_fees',
        verbose_name=_('Academic Year')
    )

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('Amount')
    )

    due_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_('Due Date')
    )

    class Meta:
        verbose_name = _('Grade Fee')
        verbose_name_plural = _('Grade Fees')
        unique_together = ['grade', 'fee_type', 'academic_year']
        ordering = ['grade', 'fee_type']

    def __str__(self):
        return f"{self.grade} - {self.fee_type} ({self.amount})"


class StudentFee(BaseModel):
    """
    Student fee assignment model
    """
    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='fees',
        verbose_name=_('Student')
    )

    grade_fee = models.ForeignKey(
        GradeFee,
        on_delete=models.CASCADE,
        related_name='student_fees',
        verbose_name=_('Grade Fee')
    )

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('Amount')
    )

    discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name=_('Discount Amount')
    )

    due_date = models.DateField(
        verbose_name=_('Due Date')
    )

    is_paid = models.BooleanField(
        default=False,
        verbose_name=_('Is Paid')
    )

    paid_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_('Paid Date')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    class Meta:
        verbose_name = _('Student Fee')
        verbose_name_plural = _('Student Fees')
        unique_together = ['student', 'grade_fee']
        ordering = ['student', 'due_date']

    def __str__(self):
        return f"{self.student} - {self.grade_fee.fee_type} ({self.amount})"

    @property
    def net_amount(self):
        return self.amount - self.discount_amount


class Payment(BaseModel):
    """
    Payment model
    """
    PAYMENT_METHODS = (
        ('cash', _('Cash')),
        ('bank_transfer', _('Bank Transfer')),
        ('check', _('Check')),
        ('credit_card', _('Credit Card')),
        ('online', _('Online Payment')),
    )

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='payments',
        verbose_name=_('Student')
    )

    receipt_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('Receipt Number')
    )

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('Amount')
    )

    payment_date = models.DateField(
        verbose_name=_('Payment Date')
    )

    payment_method = models.CharField(
        max_length=20,
        choices=PAYMENT_METHODS,
        verbose_name=_('Payment Method')
    )

    reference_number = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Reference Number')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    received_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='received_payments',
        verbose_name=_('Received By')
    )

    class Meta:
        verbose_name = _('Payment')
        verbose_name_plural = _('Payments')
        ordering = ['-payment_date']

    def __str__(self):
        return f"{self.receipt_number} - {self.student} ({self.amount})"


class PaymentItem(BaseModel):
    """
    Payment item model - links payments to specific fees
    """
    payment = models.ForeignKey(
        Payment,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name=_('Payment')
    )

    student_fee = models.ForeignKey(
        StudentFee,
        on_delete=models.CASCADE,
        related_name='payment_items',
        verbose_name=_('Student Fee')
    )

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('Amount')
    )

    class Meta:
        verbose_name = _('Payment Item')
        verbose_name_plural = _('Payment Items')
        ordering = ['payment', 'student_fee']

    def __str__(self):
        return f"{self.payment.receipt_number} - {self.student_fee.grade_fee.fee_type}"


class PaymentGateway(BaseModel):
    """
    Payment gateway configuration model
    """
    GATEWAY_TYPES = (
        ('stripe', _('Stripe')),
        ('paypal', _('PayPal')),
        ('square', _('Square')),
        ('razorpay', _('Razorpay')),
        ('paytabs', _('PayTabs')),
        ('mada', _('Mada')),
        ('visa', _('Visa')),
        ('mastercard', _('Mastercard')),
        ('bank_transfer', _('Bank Transfer')),
        ('custom', _('Custom Gateway')),
    )

    name = models.CharField(
        max_length=100,
        verbose_name=_('Gateway Name')
    )

    gateway_type = models.CharField(
        max_length=20,
        choices=GATEWAY_TYPES,
        verbose_name=_('Gateway Type')
    )

    api_key = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_('API Key')
    )

    secret_key = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_('Secret Key')
    )

    webhook_url = models.URLField(
        blank=True,
        null=True,
        verbose_name=_('Webhook URL')
    )

    is_sandbox = models.BooleanField(
        default=True,
        verbose_name=_('Is Sandbox Mode')
    )

    is_enabled = models.BooleanField(
        default=True,
        verbose_name=_('Is Enabled')
    )

    configuration = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Additional Configuration'),
        help_text=_('Gateway-specific configuration parameters')
    )

    transaction_fee_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        verbose_name=_('Transaction Fee Percentage')
    )

    transaction_fee_fixed = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_('Fixed Transaction Fee')
    )

    class Meta:
        verbose_name = _('Payment Gateway')
        verbose_name_plural = _('Payment Gateways')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.gateway_type})"

    def calculate_fees(self, amount):
        """Calculate gateway fees for a given amount"""
        percentage_fee = amount * (self.transaction_fee_percentage / 100)
        total_fee = percentage_fee + self.transaction_fee_fixed
        return total_fee


class PaymentTransaction(BaseModel):
    """
    Payment transaction model for tracking gateway transactions
    """
    TRANSACTION_STATUS = (
        ('pending', _('Pending')),
        ('processing', _('Processing')),
        ('completed', _('Completed')),
        ('failed', _('Failed')),
        ('cancelled', _('Cancelled')),
        ('refunded', _('Refunded')),
        ('partially_refunded', _('Partially Refunded')),
    )

    payment = models.ForeignKey(
        Payment,
        on_delete=models.CASCADE,
        related_name='transactions',
        verbose_name=_('Payment')
    )

    gateway = models.ForeignKey(
        PaymentGateway,
        on_delete=models.CASCADE,
        related_name='transactions',
        verbose_name=_('Payment Gateway')
    )

    transaction_id = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_('Transaction ID')
    )

    gateway_transaction_id = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_('Gateway Transaction ID')
    )

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('Amount')
    )

    gateway_fee = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_('Gateway Fee')
    )

    net_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Net Amount')
    )

    currency = models.CharField(
        max_length=3,
        default='SAR',
        verbose_name=_('Currency')
    )

    status = models.CharField(
        max_length=20,
        choices=TRANSACTION_STATUS,
        default='pending',
        verbose_name=_('Status')
    )

    gateway_response = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Gateway Response')
    )

    processed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Processed At')
    )

    failure_reason = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Failure Reason')
    )

    class Meta:
        verbose_name = _('Payment Transaction')
        verbose_name_plural = _('Payment Transactions')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['school', 'status']),
            models.Index(fields=['school', 'gateway']),
            models.Index(fields=['transaction_id']),
            models.Index(fields=['gateway_transaction_id']),
        ]

    def __str__(self):
        return f"{self.transaction_id} - {self.gateway.name} ({self.amount})"

    def save(self, *args, **kwargs):
        if not self.transaction_id:
            self.transaction_id = self.generate_transaction_id()
        
        # Calculate net amount
        if self.gateway_fee is None:
            self.gateway_fee = self.gateway.calculate_fees(self.amount)
        
        self.net_amount = self.amount - self.gateway_fee
        
        super().save(*args, **kwargs)

    def generate_transaction_id(self):
        """Generate unique transaction ID"""
        import uuid
        return f"TXN-{uuid.uuid4().hex[:12].upper()}"


class PaymentRefund(BaseModel):
    """
    Payment refund model
    """
    REFUND_STATUS = (
        ('pending', _('Pending')),
        ('processing', _('Processing')),
        ('completed', _('Completed')),
        ('failed', _('Failed')),
        ('cancelled', _('Cancelled')),
    )

    REFUND_TYPES = (
        ('full', _('Full Refund')),
        ('partial', _('Partial Refund')),
    )

    payment_transaction = models.ForeignKey(
        PaymentTransaction,
        on_delete=models.CASCADE,
        related_name='refunds',
        verbose_name=_('Payment Transaction')
    )

    refund_id = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_('Refund ID')
    )

    gateway_refund_id = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_('Gateway Refund ID')
    )

    refund_type = models.CharField(
        max_length=10,
        choices=REFUND_TYPES,
        verbose_name=_('Refund Type')
    )

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('Refund Amount')
    )

    reason = models.TextField(
        verbose_name=_('Refund Reason')
    )

    status = models.CharField(
        max_length=20,
        choices=REFUND_STATUS,
        default='pending',
        verbose_name=_('Status')
    )

    requested_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='requested_refunds',
        verbose_name=_('Requested By')
    )

    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_refunds',
        verbose_name=_('Approved By')
    )

    processed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Processed At')
    )

    gateway_response = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Gateway Response')
    )

    class Meta:
        verbose_name = _('Payment Refund')
        verbose_name_plural = _('Payment Refunds')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.refund_id} - {self.amount}"

    def save(self, *args, **kwargs):
        if not self.refund_id:
            self.refund_id = self.generate_refund_id()
        super().save(*args, **kwargs)

    def generate_refund_id(self):
        """Generate unique refund ID"""
        import uuid
        return f"REF-{uuid.uuid4().hex[:12].upper()}"


class PaymentReminder(BaseModel):
    """
    Payment reminder model for automated reminders
    """
    REMINDER_TYPES = (
        ('email', _('Email')),
        ('sms', _('SMS')),
        ('whatsapp', _('WhatsApp')),
        ('push', _('Push Notification')),
    )

    REMINDER_STATUS = (
        ('scheduled', _('Scheduled')),
        ('sent', _('Sent')),
        ('failed', _('Failed')),
        ('cancelled', _('Cancelled')),
    )

    student_fee = models.ForeignKey(
        StudentFee,
        on_delete=models.CASCADE,
        related_name='reminders',
        verbose_name=_('Student Fee')
    )

    reminder_type = models.CharField(
        max_length=20,
        choices=REMINDER_TYPES,
        verbose_name=_('Reminder Type')
    )

    scheduled_date = models.DateTimeField(
        verbose_name=_('Scheduled Date')
    )

    sent_date = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Sent Date')
    )

    status = models.CharField(
        max_length=20,
        choices=REMINDER_STATUS,
        default='scheduled',
        verbose_name=_('Status')
    )

    message_template = models.TextField(
        verbose_name=_('Message Template')
    )

    recipient = models.CharField(
        max_length=255,
        verbose_name=_('Recipient'),
        help_text=_('Email address, phone number, or user ID')
    )

    attempts = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Attempts')
    )

    max_attempts = models.PositiveIntegerField(
        default=3,
        verbose_name=_('Max Attempts')
    )

    error_message = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Error Message')
    )

    class Meta:
        verbose_name = _('Payment Reminder')
        verbose_name_plural = _('Payment Reminders')
        ordering = ['scheduled_date']
        indexes = [
            models.Index(fields=['school', 'status']),
            models.Index(fields=['school', 'scheduled_date']),
            models.Index(fields=['student_fee']),
        ]

    def __str__(self):
        return f"Reminder for {self.student_fee.student} - {self.student_fee.grade_fee.fee_type}"


class Budget(BaseModel):
    """
    Budget model for financial planning and monitoring
    """
    BUDGET_TYPES = (
        ('annual', _('Annual Budget')),
        ('quarterly', _('Quarterly Budget')),
        ('monthly', _('Monthly Budget')),
        ('project', _('Project Budget')),
    )

    BUDGET_STATUS = (
        ('draft', _('Draft')),
        ('pending_approval', _('Pending Approval')),
        ('approved', _('Approved')),
        ('active', _('Active')),
        ('closed', _('Closed')),
        ('cancelled', _('Cancelled')),
    )

    name = models.CharField(
        max_length=200,
        verbose_name=_('Budget Name')
    )

    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Budget Name (Arabic)')
    )

    budget_type = models.CharField(
        max_length=20,
        choices=BUDGET_TYPES,
        verbose_name=_('Budget Type')
    )

    financial_year = models.ForeignKey(
        'FinancialYear',
        on_delete=models.CASCADE,
        related_name='budgets',
        verbose_name=_('Financial Year')
    )

    start_date = models.DateField(
        verbose_name=_('Start Date')
    )

    end_date = models.DateField(
        verbose_name=_('End Date')
    )

    total_budget = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Total Budget Amount')
    )

    status = models.CharField(
        max_length=20,
        choices=BUDGET_STATUS,
        default='draft',
        verbose_name=_('Status')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    # Approval workflow
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_budgets',
        verbose_name=_('Approved By')
    )

    approved_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Approved At')
    )

    class Meta:
        verbose_name = _('Budget')
        verbose_name_plural = _('Budgets')
        ordering = ['-start_date']
        unique_together = [['school', 'name', 'financial_year']]

    def __str__(self):
        return f"{self.name} ({self.financial_year})"

    @property
    def is_active(self):
        """Check if budget is currently active"""
        today = date.today()
        return (self.status == 'active' and 
                self.start_date <= today <= self.end_date)

    def get_total_allocated(self):
        """Get total allocated amount from budget items"""
        return self.items.aggregate(
            total=Sum('allocated_amount')
        )['total'] or Decimal('0')

    def get_total_spent(self):
        """Get total spent amount from actual transactions"""
        return self.items.aggregate(
            total=Sum('spent_amount')
        )['total'] or Decimal('0')

    def get_variance(self):
        """Get budget variance (allocated - spent)"""
        return self.get_total_allocated() - self.get_total_spent()

    def get_utilization_percentage(self):
        """Get budget utilization percentage"""
        allocated = self.get_total_allocated()
        if allocated == 0:
            return 0
        spent = self.get_total_spent()
        return (spent / allocated * 100).quantize(Decimal('0.01'))


class BudgetItem(BaseModel):
    """
    Budget item model for detailed budget allocation
    """
    budget = models.ForeignKey(
        Budget,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name=_('Budget')
    )

    account = models.ForeignKey(
        Account,
        on_delete=models.CASCADE,
        related_name='budget_items',
        verbose_name=_('Account')
    )

    cost_center = models.ForeignKey(
        'CostCenter',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='budget_items',
        verbose_name=_('Cost Center')
    )

    allocated_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Allocated Amount')
    )

    spent_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Spent Amount')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    class Meta:
        verbose_name = _('Budget Item')
        verbose_name_plural = _('Budget Items')
        unique_together = [['budget', 'account', 'cost_center']]
        ordering = ['account__code']

    def __str__(self):
        return f"{self.budget.name} - {self.account.name}"

    @property
    def variance(self):
        """Get variance for this budget item"""
        return self.allocated_amount - self.spent_amount

    @property
    def utilization_percentage(self):
        """Get utilization percentage for this budget item"""
        if self.allocated_amount == 0:
            return 0
        return (self.spent_amount / self.allocated_amount * 100).quantize(Decimal('0.01'))

    def update_spent_amount(self):
        """Update spent amount from actual transactions"""
        from django.db.models import Q
        
        # Get all transaction entries for this account and cost center within budget period
        entries = TransactionEntry.objects.filter(
            account=self.account,
            entry_date__gte=self.budget.start_date,
            entry_date__lte=self.budget.end_date,
            is_posted=True
        )
        
        if self.cost_center:
            entries = entries.filter(cost_center=self.cost_center)
        
        # Calculate spent amount based on account type
        if self.account.account_type.type in ['expense']:
            # For expense accounts, sum debits
            spent = entries.aggregate(total=Sum('debit_amount'))['total'] or Decimal('0')
        elif self.account.account_type.type in ['revenue']:
            # For revenue accounts, sum credits
            spent = entries.aggregate(total=Sum('credit_amount'))['total'] or Decimal('0')
        else:
            # For other accounts, use net amount
            debits = entries.aggregate(total=Sum('debit_amount'))['total'] or Decimal('0')
            credits = entries.aggregate(total=Sum('credit_amount'))['total'] or Decimal('0')
            spent = abs(debits - credits)
        
        self.spent_amount = spent
        self.save(update_fields=['spent_amount'])


class PaymentAnalytics(BaseModel):
    """
    Payment analytics model for tracking payment metrics
    """
    date = models.DateField(
        verbose_name=_('Date')
    )

    total_payments = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Total Payments')
    )

    total_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Total Amount')
    )

    successful_payments = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Successful Payments')
    )

    failed_payments = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Failed Payments')
    )

    refunded_payments = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Refunded Payments')
    )

    refunded_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Refunded Amount')
    )

    gateway_fees = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Gateway Fees')
    )

    net_revenue = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Net Revenue')
    )

    payment_methods = models.JSONField(
        default=dict,
        verbose_name=_('Payment Methods Breakdown')
    )

    gateway_breakdown = models.JSONField(
        default=dict,
        verbose_name=_('Gateway Breakdown')
    )

    class Meta:
        verbose_name = _('Payment Analytics')
        verbose_name_plural = _('Payment Analytics')
        unique_together = ['school', 'date']
        ordering = ['-date']

    def __str__(self):
        return f"Analytics for {self.date} - {self.total_amount}"








class CostCenter(BaseModel):
    """
    Cost center model for tracking expenses by department/project
    """
    code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Cost Center Code')
    )

    name = models.CharField(
        max_length=200,
        verbose_name=_('Cost Center Name')
    )

    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Cost Center Name (Arabic)')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    manager = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_cost_centers',
        verbose_name=_('Manager')
    )

    budget_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Budget Amount')
    )

    class Meta:
        verbose_name = _('Cost Center')
        verbose_name_plural = _('Cost Centers')
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name}"

    @property
    def total_expenses(self):
        """Calculate total expenses for this cost center"""
        from django.db.models import Sum
        # Check both JournalEntry and TransactionEntry for expenses
        journal_expenses = JournalEntry.objects.filter(
            cost_center=self,
            is_posted=True,
            debit_amount__gt=0
        ).aggregate(total=Sum('debit_amount'))['total'] or 0
        
        transaction_expenses = TransactionEntry.objects.filter(
            cost_center=self,
            is_posted=True,
            debit_amount__gt=0
        ).aggregate(total=Sum('debit_amount'))['total'] or 0
        
        return journal_expenses + transaction_expenses

    @property
    def budget_utilization(self):
        """Calculate budget utilization percentage"""
        if self.budget_amount > 0:
            return (self.total_expenses / self.budget_amount) * 100
        return 0


class Transaction(BaseModel):
    """
    Transaction model for double-entry bookkeeping
    Represents a complete transaction with multiple entries
    """
    TRANSACTION_TYPES = (
        ('manual', _('Manual Transaction')),
        ('automatic', _('Automatic Transaction')),
        ('adjustment', _('Adjustment Transaction')),
        ('closing', _('Closing Transaction')),
        ('payment', _('Payment Transaction')),
        ('receipt', _('Receipt Transaction')),
        ('journal', _('Journal Transaction')),
    )

    STATUS_CHOICES = (
        ('draft', _('Draft')),
        ('pending_approval', _('Pending Approval')),
        ('approved', _('Approved')),
        ('posted', _('Posted')),
        ('cancelled', _('Cancelled')),
    )

    transaction_id = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('Transaction ID'),
        help_text=_('Unique transaction identifier')
    )

    transaction_date = models.DateField(
        verbose_name=_('Transaction Date')
    )

    transaction_type = models.CharField(
        max_length=20,
        choices=TRANSACTION_TYPES,
        default='manual',
        verbose_name=_('Transaction Type')
    )

    description = models.TextField(
        verbose_name=_('Description')
    )

    reference = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Reference'),
        help_text=_('External reference number or document')
    )

    total_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Total Amount'),
        help_text=_('Total transaction amount (should equal sum of debits/credits)')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        verbose_name=_('Status')
    )

    # Approval workflow fields
    requires_approval = models.BooleanField(
        default=False,
        verbose_name=_('Requires Approval'),
        help_text=_('Whether this transaction requires approval before posting')
    )

    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_transactions',
        verbose_name=_('Approved By')
    )

    approved_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Approved At')
    )

    approval_notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Approval Notes')
    )

    # Posting fields
    posted_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='posted_transactions',
        verbose_name=_('Posted By')
    )

    posted_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Posted At')
    )

    # Audit trail
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_transactions',
        verbose_name=_('Created By')
    )

    modified_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='modified_transactions',
        verbose_name=_('Modified By')
    )

    class Meta:
        verbose_name = _('Transaction')
        verbose_name_plural = _('Transactions')
        ordering = ['-transaction_date', '-created_at']
        indexes = [
            models.Index(fields=['school', 'transaction_date']),
            models.Index(fields=['school', 'status']),
            models.Index(fields=['school', 'transaction_type']),
            models.Index(fields=['transaction_id']),
        ]

    def __str__(self):
        return f"{self.transaction_id} - {self.description[:50]}"

    def clean(self):
        from django.core.exceptions import ValidationError
        
        # Validate transaction date
        if hasattr(self, 'entries') and self.entries.exists():
            # Check if all entries have the same date as transaction
            entry_dates = set(self.entries.values_list('entry_date', flat=True))
            if len(entry_dates) > 1 or (entry_dates and list(entry_dates)[0] != self.transaction_date):
                raise ValidationError(_('All transaction entries must have the same date as the transaction'))

    def save(self, *args, **kwargs):
        # Generate transaction ID if not provided
        if not self.transaction_id:
            self.transaction_id = self.generate_transaction_id()
        
        # Set modified_by if updating
        if self.pk and hasattr(self, '_current_user'):
            self.modified_by = self._current_user
        
        super().save(*args, **kwargs)

    def generate_transaction_id(self):
        """Generate unique transaction ID"""
        from datetime import datetime
        import uuid
        
        # Format: TXN-YYYY-XXXXXX (where X is sequential number)
        year = datetime.now().year
        prefix = f"TXN-{year}-"
        
        # Get the last transaction for this year
        last_transaction = Transaction.objects.filter(
            school=self.school,
            transaction_id__startswith=prefix
        ).order_by('-transaction_id').first()
        
        if last_transaction:
            try:
                last_number = int(last_transaction.transaction_id.split('-')[-1])
                next_number = last_number + 1
            except (ValueError, IndexError):
                next_number = 1
        else:
            next_number = 1
        
        return f"{prefix}{next_number:06d}"

    @property
    def is_balanced(self):
        """Check if transaction is balanced (debits = credits)"""
        if not hasattr(self, 'entries') or not self.entries.exists():
            return False
        
        from django.db.models import Sum
        totals = self.entries.aggregate(
            total_debits=Sum('debit_amount'),
            total_credits=Sum('credit_amount')
        )
        
        total_debits = totals['total_debits'] or 0
        total_credits = totals['total_credits'] or 0
        
        return abs(total_debits - total_credits) < 0.01  # Allow for small rounding differences

    @property
    def total_debits(self):
        """Get total debit amount"""
        from django.db.models import Sum
        return self.entries.aggregate(total=Sum('debit_amount'))['total'] or 0

    @property
    def total_credits(self):
        """Get total credit amount"""
        from django.db.models import Sum
        return self.entries.aggregate(total=Sum('credit_amount'))['total'] or 0

    def can_be_posted(self):
        """Check if transaction can be posted"""
        if self.status == 'posted':
            return False, _('Transaction is already posted')
        
        if self.status == 'cancelled':
            return False, _('Cancelled transactions cannot be posted')
        
        if not self.is_balanced:
            return False, _('Transaction is not balanced (debits ≠ credits)')
        
        if not self.entries.exists():
            return False, _('Transaction has no entries')
        
        if self.requires_approval and self.status != 'approved':
            return False, _('Transaction requires approval before posting')
        
        # Check if all accounts allow manual entries (for manual transactions)
        if self.transaction_type == 'manual':
            invalid_accounts = self.entries.filter(account__allow_manual_entries=False)
            if invalid_accounts.exists():
                account_names = ', '.join(invalid_accounts.values_list('account__name', flat=True))
                return False, _('Some accounts do not allow manual entries: {}').format(account_names)
        
        return True, None

    def post(self, user):
        """Post the transaction"""
        from django.utils import timezone
        from django.core.exceptions import ValidationError
        
        can_post, error_msg = self.can_be_posted()
        if not can_post:
            raise ValidationError(error_msg)
        
        # Update transaction status
        self.status = 'posted'
        self.posted_by = user
        self.posted_at = timezone.now()
        self.save(update_fields=['status', 'posted_by', 'posted_at'])
        
        # Update account balances
        for entry in self.entries.all():
            entry.post()
        
        # Create audit log entry
        self.create_audit_log('posted', user)

    def approve(self, user, notes=None):
        """Approve the transaction"""
        from django.utils import timezone
        from django.core.exceptions import ValidationError
        
        if self.status != 'pending_approval':
            raise ValidationError(_('Only pending transactions can be approved'))
        
        self.status = 'approved'
        self.approved_by = user
        self.approved_at = timezone.now()
        self.approval_notes = notes
        self.save(update_fields=['status', 'approved_by', 'approved_at', 'approval_notes'])
        
        # Create audit log entry
        self.create_audit_log('approved', user, notes)

    def cancel(self, user, reason=None):
        """Cancel the transaction"""
        from django.core.exceptions import ValidationError
        
        if self.status == 'posted':
            raise ValidationError(_('Posted transactions cannot be cancelled'))
        
        self.status = 'cancelled'
        self.save(update_fields=['status'])
        
        # Create audit log entry
        self.create_audit_log('cancelled', user, reason)

    def create_audit_log(self, action, user, notes=None):
        """Create audit log entry"""
        TransactionAuditLog.objects.create(
            transaction=self,
            action=action,
            user=user,
            notes=notes,
            school=self.school,
            created_by=user
        )


class TransactionEntry(BaseModel):
    """
    Transaction entry model for individual debit/credit entries
    Part of double-entry bookkeeping system
    """
    transaction = models.ForeignKey(
        Transaction,
        on_delete=models.CASCADE,
        related_name='entries',
        verbose_name=_('Transaction')
    )

    account = models.ForeignKey(
        Account,
        on_delete=models.CASCADE,
        related_name='transaction_entries',
        verbose_name=_('Account')
    )

    entry_date = models.DateField(
        verbose_name=_('Entry Date'),
        help_text=_('Should match transaction date')
    )

    description = models.TextField(
        verbose_name=_('Description'),
        help_text=_('Specific description for this entry')
    )

    debit_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Debit Amount')
    )

    credit_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Credit Amount')
    )

    cost_center = models.ForeignKey(
        CostCenter,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='transaction_entries',
        verbose_name=_('Cost Center')
    )

    # Additional fields for tracking
    reference = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Entry Reference')
    )

    is_posted = models.BooleanField(
        default=False,
        verbose_name=_('Is Posted'),
        help_text=_('Whether this entry has been posted to the account')
    )

    posted_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Posted At')
    )

    class Meta:
        verbose_name = _('Transaction Entry')
        verbose_name_plural = _('Transaction Entries')
        ordering = ['transaction', 'id']
        indexes = [
            models.Index(fields=['school', 'account', 'entry_date']),
            models.Index(fields=['school', 'is_posted']),
            models.Index(fields=['transaction', 'account']),
        ]

    def __str__(self):
        amount = self.debit_amount if self.debit_amount > 0 else self.credit_amount
        entry_type = 'Dr' if self.debit_amount > 0 else 'Cr'
        return f"{self.account.code} - {entry_type} {amount}"

    def clean(self):
        from django.core.exceptions import ValidationError
        
        # Validate that entry has either debit or credit, but not both
        if self.debit_amount > 0 and self.credit_amount > 0:
            raise ValidationError(_('An entry cannot have both debit and credit amounts'))
        
        if self.debit_amount == 0 and self.credit_amount == 0:
            raise ValidationError(_('An entry must have either a debit or credit amount'))
        
        # Validate that entry date matches transaction date
        if self.transaction_id and self.entry_date != self.transaction.transaction_date:
            raise ValidationError(_('Entry date must match transaction date'))
        
        # Validate that account is not a header account
        if self.account and self.account.is_header:
            raise ValidationError(_('Cannot post entries to header accounts'))
        
        # Validate account allows manual entries (for manual transactions)
        if (self.transaction and self.transaction.transaction_type == 'manual' 
            and self.account and not self.account.allow_manual_entries):
            raise ValidationError(_('Account does not allow manual entries'))

    def save(self, *args, **kwargs):
        # Set entry date to match transaction date if not set
        if self.transaction_id and not self.entry_date:
            self.entry_date = self.transaction.transaction_date
        
        super().save(*args, **kwargs)

    @property
    def amount(self):
        """Get the entry amount (debit or credit)"""
        return self.debit_amount if self.debit_amount > 0 else self.credit_amount

    @property
    def entry_type(self):
        """Get entry type (debit or credit)"""
        return 'debit' if self.debit_amount > 0 else 'credit'

    def post(self):
        """Post this entry to the account balance"""
        from django.utils import timezone

        if self.is_posted:
            return

        # Update account balance
        if self.debit_amount > 0:
            if self.account.balance_type == 'debit':
                self.account.current_balance += self.debit_amount
            else:
                self.account.current_balance -= self.debit_amount
        else:
            if self.account.balance_type == 'credit':
                self.account.current_balance += self.credit_amount
            else:
                self.account.current_balance -= self.credit_amount

        self.account.save(update_fields=['current_balance'])

        # Mark entry as posted
        self.is_posted = True
        self.posted_at = timezone.now()
        self.save(update_fields=['is_posted', 'posted_at'])


class TransactionAuditLog(BaseModel):
    """
    Audit log for transaction changes
    """
    ACTIONS = (
        ('created', _('Created')),
        ('modified', _('Modified')),
        ('approved', _('Approved')),
        ('posted', _('Posted')),
        ('cancelled', _('Cancelled')),
        ('deleted', _('Deleted')),
    )

    transaction = models.ForeignKey(
        Transaction,
        on_delete=models.CASCADE,
        related_name='audit_logs',
        verbose_name=_('Transaction')
    )

    action = models.CharField(
        max_length=20,
        choices=ACTIONS,
        verbose_name=_('Action')
    )

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='transaction_audit_logs',
        verbose_name=_('User')
    )

    timestamp = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Timestamp')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name=_('IP Address')
    )

    changes = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Changes'),
        help_text=_('JSON representation of changes made')
    )

    class Meta:
        verbose_name = _('Transaction Audit Log')
        verbose_name_plural = _('Transaction Audit Logs')
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.transaction.transaction_id} - {self.action} by {self.user.username}"


class JournalEntry(BaseModel):
    """
    Journal entry model for double-entry bookkeeping
    """
    ENTRY_TYPES = (
        ('manual', _('Manual Entry')),
        ('automatic', _('Automatic Entry')),
        ('adjustment', _('Adjustment Entry')),
        ('closing', _('Closing Entry')),
    )

    reference_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('Reference Number')
    )

    entry_date = models.DateField(
        verbose_name=_('Entry Date')
    )

    entry_type = models.CharField(
        max_length=20,
        choices=ENTRY_TYPES,
        default='manual',
        verbose_name=_('Entry Type')
    )

    description = models.TextField(
        verbose_name=_('Description')
    )

    account = models.ForeignKey(
        Account,
        on_delete=models.CASCADE,
        related_name='journal_entries',
        verbose_name=_('Account')
    )

    cost_center = models.ForeignKey(
        CostCenter,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='journal_entries',
        verbose_name=_('Cost Center')
    )

    debit_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Debit Amount')
    )

    credit_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Credit Amount')
    )

    is_posted = models.BooleanField(
        default=False,
        verbose_name=_('Is Posted')
    )

    posted_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='posted_entries',
        verbose_name=_('Posted By')
    )

    posted_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Posted At')
    )

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_entries',
        verbose_name=_('Created By')
    )

    class Meta:
        verbose_name = _('Journal Entry')
        verbose_name_plural = _('Journal Entries')
        ordering = ['-entry_date', '-created_at']

    def __str__(self):
        return f"{self.reference_number} - {self.description[:50]}"

    def clean(self):
        from django.core.exceptions import ValidationError
        if self.debit_amount > 0 and self.credit_amount > 0:
            raise ValidationError(_('An entry cannot have both debit and credit amounts'))
        if self.debit_amount == 0 and self.credit_amount == 0:
            raise ValidationError(_('An entry must have either a debit or credit amount'))


class FinancialYear(BaseModel):
    """
    Financial year model
    """
    name = models.CharField(
        max_length=50,
        verbose_name=_('Financial Year Name')
    )

    start_date = models.DateField(
        verbose_name=_('Start Date')
    )

    end_date = models.DateField(
        verbose_name=_('End Date')
    )

    is_current = models.BooleanField(
        default=False,
        verbose_name=_('Is Current Year')
    )

    is_closed = models.BooleanField(
        default=False,
        verbose_name=_('Is Closed')
    )

    class Meta:
        verbose_name = _('Financial Year')
        verbose_name_plural = _('Financial Years')
        ordering = ['-start_date']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if self.is_current:
            # Ensure only one financial year is current
            FinancialYear.objects.filter(is_current=True).update(is_current=False)
        super().save(*args, **kwargs)


class Bank(BaseModel):
    """
    Bank model for managing bank accounts
    """
    name = models.CharField(
        max_length=200,
        verbose_name=_('Bank Name')
    )

    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Bank Name (Arabic)')
    )

    account_number = models.CharField(
        max_length=50,
        verbose_name=_('Account Number')
    )

    account_name = models.CharField(
        max_length=200,
        verbose_name=_('Account Name')
    )

    branch = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Branch')
    )

    swift_code = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('SWIFT Code')
    )

    iban = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('IBAN')
    )

    opening_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Opening Balance')
    )

    current_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_('Current Balance')
    )

    account = models.ForeignKey(
        Account,
        on_delete=models.CASCADE,
        related_name='bank_accounts',
        verbose_name=_('Linked Account')
    )

    def save(self, *args, **kwargs):
        # Initialize current balance if not set
        if self.current_balance is None:
            if self.opening_balance is not None:
                self.current_balance = self.opening_balance
            else:
                self.current_balance = Decimal('0')

        super().save(*args, **kwargs)

    class Meta:
        verbose_name = _('Bank Account')
        verbose_name_plural = _('Bank Accounts')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} - {self.account_number}"


class Invoice(BaseModel):
    """
    Invoice model for generating student invoices
    """
    INVOICE_TYPES = (
        ('tuition', _('Tuition Invoice')),
        ('fees', _('Fees Invoice')),
        ('other', _('Other Invoice')),
    )

    STATUS_CHOICES = (
        ('draft', _('Draft')),
        ('sent', _('Sent')),
        ('paid', _('Paid')),
        ('overdue', _('Overdue')),
        ('cancelled', _('Cancelled')),
    )

    invoice_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('Invoice Number')
    )

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='invoices',
        verbose_name=_('Student')
    )

    invoice_type = models.CharField(
        max_length=20,
        choices=INVOICE_TYPES,
        verbose_name=_('Invoice Type')
    )

    invoice_date = models.DateField(
        verbose_name=_('Invoice Date')
    )

    due_date = models.DateField(
        verbose_name=_('Due Date')
    )

    subtotal = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Subtotal')
    )

    tax_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_('Tax Amount')
    )

    discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_('Discount Amount')
    )

    total_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Total Amount')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        verbose_name=_('Status')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_invoices',
        verbose_name=_('Created By')
    )

    class Meta:
        verbose_name = _('Invoice')
        verbose_name_plural = _('Invoices')
        ordering = ['-invoice_date']

    def __str__(self):
        return f"{self.invoice_number} - {self.student.full_name}"

    @property
    def is_overdue(self):
        from datetime import date
        return self.status in ['sent'] and self.due_date < date.today()

    def save(self, *args, **kwargs):
        # Auto-calculate total amount
        self.total_amount = self.subtotal + self.tax_amount - self.discount_amount
        super().save(*args, **kwargs)


class InvoiceItem(BaseModel):
    """
    Invoice line items model
    """
    invoice = models.ForeignKey(
        Invoice,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name=_('Invoice')
    )

    description = models.CharField(
        max_length=200,
        verbose_name=_('Description')
    )

    quantity = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=1,
        verbose_name=_('Quantity')
    )

    unit_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Unit Price')
    )

    total_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Total Price')
    )

    fee_type = models.ForeignKey(
        FeeType,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Fee Type')
    )

    class Meta:
        verbose_name = _('Invoice Item')
        verbose_name_plural = _('Invoice Items')
        ordering = ['invoice', 'id']

    def __str__(self):
        return f"{self.invoice.invoice_number} - {self.description}"

    def save(self, *args, **kwargs):
        # Auto-calculate total price
        self.total_price = self.quantity * self.unit_price
        super().save(*args, **kwargs)

    def save(self, *args, **kwargs):
        if not self.reference_number:
            self.reference_number = self.generate_reference_number()
        super().save(*args, **kwargs)

    def generate_reference_number(self):
        """Generate unique reference number"""
        import uuid
        return f"JE-{uuid.uuid4().hex[:8].upper()}"


class BudgetAlert(BaseModel):
    """
    Budget alert model for automated budget monitoring and notifications
    """
    ALERT_TYPES = (
        ('utilization', _('Budget Utilization Alert')),
        ('variance', _('Budget Variance Alert')),
        ('overrun', _('Budget Overrun Alert')),
        ('deadline', _('Budget Deadline Alert')),
    )

    NOTIFICATION_METHODS = (
        ('email', _('Email')),
        ('sms', _('SMS')),
        ('whatsapp', _('WhatsApp')),
        ('push', _('Push Notification')),
        ('all', _('All Methods')),
    )

    ALERT_STATUS = (
        ('active', _('Active')),
        ('triggered', _('Triggered')),
        ('resolved', _('Resolved')),
        ('disabled', _('Disabled')),
    )

    budget = models.ForeignKey(
        Budget,
        on_delete=models.CASCADE,
        related_name='alerts',
        verbose_name=_('Budget')
    )

    alert_type = models.CharField(
        max_length=20,
        choices=ALERT_TYPES,
        verbose_name=_('Alert Type')
    )

    threshold_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_('Threshold Percentage'),
        help_text=_('Alert when utilization exceeds this percentage')
    )

    threshold_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_('Threshold Amount'),
        help_text=_('Alert when variance exceeds this amount')
    )

    notification_method = models.CharField(
        max_length=20,
        choices=NOTIFICATION_METHODS,
        default='email',
        verbose_name=_('Notification Method')
    )

    recipients = models.TextField(
        verbose_name=_('Recipients'),
        help_text=_('Email addresses or phone numbers, separated by commas')
    )

    status = models.CharField(
        max_length=20,
        choices=ALERT_STATUS,
        default='active',
        verbose_name=_('Status')
    )

    last_triggered = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Last Triggered')
    )

    trigger_count = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Trigger Count')
    )

    class Meta:
        verbose_name = _('Budget Alert')
        verbose_name_plural = _('Budget Alerts')
        ordering = ['budget', 'alert_type']
        indexes = [
            models.Index(fields=['school', 'status']),
            models.Index(fields=['budget', 'alert_type']),
        ]

    def __str__(self):
        return f"{self.budget.name} - {self.get_alert_type_display()}"

    def check_and_trigger(self):
        """Check if alert conditions are met and trigger if necessary"""
        from django.utils import timezone
        
        should_trigger = False
        current_value = None
        
        if self.alert_type == 'utilization':
            current_value = self.budget.get_utilization_percentage()
            should_trigger = (self.threshold_percentage and 
                            current_value >= self.threshold_percentage)
        
        elif self.alert_type == 'variance':
            current_value = abs(self.budget.get_variance())
            should_trigger = (self.threshold_amount and 
                            current_value >= self.threshold_amount)
        
        elif self.alert_type == 'overrun':
            current_value = self.budget.get_total_spent()
            budget_amount = self.budget.get_total_allocated()
            should_trigger = current_value > budget_amount
        
        elif self.alert_type == 'deadline':
            from datetime import date, timedelta
            days_until_end = (self.budget.end_date - date.today()).days
            should_trigger = days_until_end <= (self.threshold_amount or 7)
        
        if should_trigger and self.status == 'active':
            self.trigger_alert(current_value)
    
    def trigger_alert(self, current_value=None):
        """Trigger the alert and send notifications"""
        from django.utils import timezone
        
        self.status = 'triggered'
        self.last_triggered = timezone.now()
        self.trigger_count += 1
        self.save()
        
        # Send notifications (implement in services)
        from .services import BudgetAlertService
        BudgetAlertService.send_alert_notification(self, current_value)


class BudgetRevision(BaseModel):
    """
    Budget revision model for tracking budget changes
    """
    REVISION_TYPES = (
        ('increase', _('Budget Increase')),
        ('decrease', _('Budget Decrease')),
        ('reallocation', _('Budget Reallocation')),
        ('adjustment', _('Budget Adjustment')),
    )

    REVISION_STATUS = (
        ('draft', _('Draft')),
        ('pending_approval', _('Pending Approval')),
        ('approved', _('Approved')),
        ('rejected', _('Rejected')),
        ('implemented', _('Implemented')),
    )

    budget = models.ForeignKey(
        Budget,
        on_delete=models.CASCADE,
        related_name='revisions',
        verbose_name=_('Budget')
    )

    revision_number = models.CharField(
        max_length=20,
        verbose_name=_('Revision Number')
    )

    revision_type = models.CharField(
        max_length=20,
        choices=REVISION_TYPES,
        verbose_name=_('Revision Type')
    )

    reason = models.TextField(
        verbose_name=_('Reason for Revision')
    )

    previous_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Previous Amount')
    )

    new_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('New Amount')
    )

    status = models.CharField(
        max_length=20,
        choices=REVISION_STATUS,
        default='draft',
        verbose_name=_('Status')
    )

    requested_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='budget_revision_requests',
        verbose_name=_('Requested By')
    )

    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_budget_revisions',
        verbose_name=_('Approved By')
    )

    approved_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Approved At')
    )

    implemented_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Implemented At')
    )

    class Meta:
        verbose_name = _('Budget Revision')
        verbose_name_plural = _('Budget Revisions')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['school', 'status']),
            models.Index(fields=['budget', 'revision_number']),
        ]

    def __str__(self):
        return f"{self.budget.name} - Rev {self.revision_number}"

    def save(self, *args, **kwargs):
        if not self.revision_number:
            self.revision_number = self.generate_revision_number()
        super().save(*args, **kwargs)

    def generate_revision_number(self):
        """Generate revision number"""
        last_revision = BudgetRevision.objects.filter(
            budget=self.budget
        ).order_by('-created_at').first()
        
        if last_revision:
            try:
                last_num = int(last_revision.revision_number.split('-')[-1])
                return f"REV-{last_num + 1:03d}"
            except (ValueError, IndexError):
                return "REV-001"
        else:
            return "REV-001"

    @property
    def variance_amount(self):
        """Calculate variance amount"""
        return self.new_amount - self.previous_amount

    @property
    def variance_percentage(self):
        """Calculate variance percentage"""
        if self.previous_amount == 0:
            return Decimal('0')
        return (self.variance_amount / self.previous_amount * 100).quantize(Decimal('0.01'))


class BudgetApprovalWorkflow(BaseModel):
    """
    Budget approval workflow model
    """
    WORKFLOW_STATUS = (
        ('pending', _('Pending')),
        ('in_review', _('In Review')),
        ('approved', _('Approved')),
        ('rejected', _('Rejected')),
        ('cancelled', _('Cancelled')),
    )

    APPROVAL_ACTIONS = (
        ('approve', _('Approve')),
        ('reject', _('Reject')),
        ('request_changes', _('Request Changes')),
    )

    budget = models.ForeignKey(
        Budget,
        on_delete=models.CASCADE,
        related_name='approval_workflows',
        verbose_name=_('Budget')
    )

    workflow_step = models.PositiveIntegerField(
        default=1,
        verbose_name=_('Workflow Step')
    )

    approver = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='budget_approvals',
        verbose_name=_('Approver')
    )

    status = models.CharField(
        max_length=20,
        choices=WORKFLOW_STATUS,
        default='pending',
        verbose_name=_('Status')
    )

    action_taken = models.CharField(
        max_length=20,
        choices=APPROVAL_ACTIONS,
        null=True,
        blank=True,
        verbose_name=_('Action Taken')
    )

    comments = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Comments')
    )

    reviewed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Reviewed At')
    )

    due_date = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Due Date')
    )

    class Meta:
        verbose_name = _('Budget Approval Workflow')
        verbose_name_plural = _('Budget Approval Workflows')
        ordering = ['budget', 'workflow_step']
        unique_together = [['budget', 'workflow_step', 'approver']]

    def __str__(self):
        return f"{self.budget.name} - Step {self.workflow_step} - {self.approver.username}"