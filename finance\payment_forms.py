from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from .models import (
    PaymentGateway, PaymentTransaction, PaymentRefund, 
    PaymentReminder, Payment, StudentFee
)
from students.models import Student


class PaymentGatewayForm(forms.ModelForm):
    """Form for payment gateway configuration"""
    
    class Meta:
        model = PaymentGateway
        fields = [
            'name', 'gateway_type', 'api_key', 'secret_key', 'webhook_url',
            'is_sandbox', 'is_enabled', 'transaction_fee_percentage',
            'transaction_fee_fixed', 'configuration'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'gateway_type': forms.Select(attrs={'class': 'form-control'}),
            'api_key': forms.PasswordInput(attrs={'class': 'form-control'}),
            'secret_key': forms.PasswordInput(attrs={'class': 'form-control'}),
            'webhook_url': forms.URLInput(attrs={'class': 'form-control'}),
            'transaction_fee_percentage': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'transaction_fee_fixed': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'configuration': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Enter JSON configuration'
            }),
        }
    
    def clean_configuration(self):
        """Validate JSON configuration"""
        import json
        configuration = self.cleaned_data.get('configuration')
        if configuration:
            try:
                json.loads(configuration)
            except json.JSONDecodeError:
                raise ValidationError(_('Invalid JSON format'))
        return configuration


class PaymentProcessingForm(forms.Form):
    """Form for processing payments"""
    
    student = forms.ModelChoiceField(
        queryset=Student.objects.none(),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Student')
    )
    
    gateway = forms.ModelChoiceField(
        queryset=PaymentGateway.objects.none(),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Payment Gateway')
    )
    
    amount = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        min_value=0.01,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'step': '0.01',
            'min': '0.01'
        }),
        label=_('Amount')
    )
    
    payment_method = forms.ChoiceField(
        choices=Payment.PAYMENT_METHODS,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Payment Method')
    )
    
    reference_number = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control'}),
        label=_('Reference Number')
    )
    
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        label=_('Notes')
    )
    
    def __init__(self, school=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if school:
            self.fields['student'].queryset = Student.objects.filter(
                school=school, is_active=True
            )
            self.fields['gateway'].queryset = PaymentGateway.objects.filter(
                school=school, is_enabled=True, is_active=True
            )


class PaymentRefundForm(forms.ModelForm):
    """Form for creating payment refunds"""
    
    class Meta:
        model = PaymentRefund
        fields = ['refund_type', 'amount', 'reason']
        widgets = {
            'refund_type': forms.Select(attrs={'class': 'form-control'}),
            'amount': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0.01'
            }),
            'reason': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
        }
    
    def __init__(self, payment_transaction=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.payment_transaction = payment_transaction
        
        if payment_transaction:
            # Set maximum refund amount
            total_refunded = payment_transaction.refunds.filter(
                status='completed'
            ).aggregate(total=models.Sum('amount'))['total'] or 0
            
            max_amount = payment_transaction.amount - total_refunded
            self.fields['amount'].widget.attrs['max'] = str(max_amount)
            self.fields['amount'].help_text = _(
                f'Maximum refundable amount: {max_amount}'
            )
    
    def clean_amount(self):
        """Validate refund amount"""
        amount = self.cleaned_data.get('amount')
        if self.payment_transaction and amount:
            from django.db.models import Sum
            total_refunded = self.payment_transaction.refunds.filter(
                status='completed'
            ).aggregate(total=Sum('amount'))['total'] or 0
            
            if total_refunded + amount > self.payment_transaction.amount:
                raise ValidationError(
                    _('Refund amount exceeds available balance')
                )
        
        return amount
class PaymentReminderForm(forms.ModelForm):
    """Form for creating payment reminders"""
    
    class Meta:
        model = PaymentReminder
        fields = [
            'reminder_type', 'scheduled_date', 'message_template',
            'max_attempts'
        ]
        widgets = {
            'reminder_type': forms.Select(attrs={'class': 'form-control'}),
            'scheduled_date': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local'
            }),
            'message_template': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Use {student_name}, {amount}, {due_date} as placeholders'
            }),
            'max_attempts': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'max': '10',
                'value': '3'
            }),
        }
    
    def clean_scheduled_date(self):
        """Validate scheduled date is in the future"""
        from django.utils import timezone
        scheduled_date = self.cleaned_data.get('scheduled_date')
        if scheduled_date and scheduled_date <= timezone.now():
            raise ValidationError(_('Scheduled date must be in the future'))
        return scheduled_date


class BulkPaymentReminderForm(forms.Form):
    """Form for creating bulk payment reminders"""
    
    grade = forms.ModelChoiceField(
        queryset=None,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Grade (Optional)')
    )
    
    fee_type = forms.ModelChoiceField(
        queryset=None,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Fee Type (Optional)')
    )
    
    overdue_only = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        label=_('Overdue Fees Only')
    )
    
    reminder_type = forms.ChoiceField(
        choices=PaymentReminder.REMINDER_TYPES,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Reminder Type')
    )
    
    scheduled_date = forms.DateTimeField(
        widget=forms.DateTimeInput(attrs={
            'class': 'form-control',
            'type': 'datetime-local'
        }),
        label=_('Scheduled Date')
    )
    
    message_template = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 4,
            'placeholder': 'Use {student_name}, {amount}, {due_date} as placeholders'
        }),
        label=_('Message Template')
    )
    
    def __init__(self, school=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if school:
            from students.models import Grade
            from .models import FeeType
            
            self.fields['grade'].queryset = Grade.objects.filter(
                school=school, is_active=True
            )
            self.fields['fee_type'].queryset = FeeType.objects.filter(
                school=school, is_active=True
            )


class PaymentAnalyticsFilterForm(forms.Form):
    """Form for filtering payment analytics"""
    
    start_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label=_('Start Date')
    )
    
    end_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label=_('End Date')
    )
    
    gateway = forms.ModelChoiceField(
        queryset=PaymentGateway.objects.none(),
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Payment Gateway (Optional)')
    )
    
    payment_method = forms.ChoiceField(
        choices=[('', _('All Methods'))] + list(Payment.PAYMENT_METHODS),
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Payment Method (Optional)')
    )
    
    def __init__(self, school=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if school:
            self.fields['gateway'].queryset = PaymentGateway.objects.filter(
                school=school, is_active=True
            )
    
    def clean(self):
        """Validate date range"""
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        
        if start_date and end_date and start_date > end_date:
            raise ValidationError(_('Start date must be before end date'))
        
        return cleaned_data


class PaymentSearchForm(forms.Form):
    """Form for searching payments"""
    
    search_query = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by student name, receipt number, or reference'
        }),
        label=_('Search')
    )
    
    payment_method = forms.ChoiceField(
        choices=[('', _('All Methods'))] + list(Payment.PAYMENT_METHODS),
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Payment Method')
    )
    
    status = forms.ChoiceField(
        choices=[
            ('', _('All Statuses')),
            ('completed', _('Completed')),
            ('pending', _('Pending')),
            ('failed', _('Failed')),
            ('refunded', _('Refunded'))
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Status')
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label=_('From Date')
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label=_('To Date')
    )
    
    def clean(self):
        """Validate date range"""
        cleaned_data = super().clean()
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')
        
        if date_from and date_to and date_from > date_to:
            raise ValidationError(_('From date must be before to date'))
        
        return cleaned_data