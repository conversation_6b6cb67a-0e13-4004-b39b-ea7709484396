from django.db import transaction
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.conf import settings
from decimal import Decimal
import logging
from typing import Dict, Any, Optional
from .models import (
    PaymentGateway, PaymentTransaction, PaymentRefund, 
    PaymentReminder, PaymentAnalytics, Payment, StudentFee
)

logger = logging.getLogger(__name__)


class PaymentGatewayService:
    """Service for handling payment gateway operations"""
    
    @staticmethod
    def get_available_gateways(school):
        """Get all enabled payment gateways for a school"""
        return PaymentGateway.objects.filter(
            school=school,
            is_enabled=True,
            is_active=True
        ).order_by('name')
    
    @staticmethod
    def process_payment(payment, gateway, **kwargs):
        """Process payment through specified gateway"""
        try:
            with transaction.atomic():
                # Create payment transaction record
                payment_transaction = PaymentTransaction.objects.create(
                    payment=payment,
                    gateway=gateway,
                    amount=payment.amount,
                    school=payment.school
                )
                
                # Process based on gateway type
                if gateway.gateway_type == 'stripe':
                    result = PaymentGatewayService._process_stripe_payment(
                        payment_transaction, **kwargs
                    )
                elif gateway.gateway_type == 'paypal':
                    result = PaymentGatewayService._process_paypal_payment(
                        payment_transaction, **kwargs
                    )
                elif gateway.gateway_type == 'bank_transfer':
                    result = PaymentGatewayService._process_bank_transfer(
                        payment_transaction, **kwargs
                    )
                else:
                    result = PaymentGatewayService._process_generic_payment(
                        payment_transaction, **kwargs
                    )
                
                return result
                
        except Exception as e:
            logger.error(f"Payment processing failed: {str(e)}")
            raise ValidationError(f"Payment processing failed: {str(e)}")
    
    @staticmethod
    def _process_stripe_payment(payment_transaction, **kwargs):
        """Process Stripe payment"""
        try:
            # Simulate Stripe API call
            gateway = payment_transaction.gateway
            
            # Mock Stripe response
            response = {
                'id': f'pi_{payment_transaction.transaction_id}',
                'status': 'succeeded',
                'amount': int(payment_transaction.amount * 100),
                'currency': payment_transaction.currency.lower(),
                'created': timezone.now().timestamp()
            }
            
            payment_transaction.gateway_transaction_id = response['id']
            payment_transaction.status = 'completed'
            payment_transaction.gateway_response = response
            payment_transaction.processed_at = timezone.now()
            payment_transaction.save()
            
            return {'success': True, 'transaction': payment_transaction}
            
        except Exception as e:
            payment_transaction.status = 'failed'
            payment_transaction.failure_reason = str(e)
            payment_transaction.save()
            return {'success': False, 'error': str(e)}
    
    @staticmethod
    def _process_paypal_payment(payment_transaction, **kwargs):
        """Process PayPal payment"""
        try:
            # Mock PayPal response
            response = {
                'id': f'PAYID-{payment_transaction.transaction_id}',
                'state': 'approved',
                'amount': {
                    'total': str(payment_transaction.amount),
                    'currency': payment_transaction.currency
                }
            }
            
            payment_transaction.gateway_transaction_id = response['id']
            payment_transaction.status = 'completed'
            payment_transaction.gateway_response = response
            payment_transaction.processed_at = timezone.now()
            payment_transaction.save()
            
            return {'success': True, 'transaction': payment_transaction}
            
        except Exception as e:
            payment_transaction.status = 'failed'
            payment_transaction.failure_reason = str(e)
            payment_transaction.save()
            return {'success': False, 'error': str(e)}
    
    @staticmethod
    def _process_bank_transfer(payment_transaction, **kwargs):
        """Process bank transfer payment"""
        # Bank transfers are typically manual verification
        payment_transaction.status = 'pending'
        payment_transaction.gateway_response = {
            'type': 'bank_transfer',
            'reference': kwargs.get('reference_number', ''),
            'bank_details': kwargs.get('bank_details', {})
        }
        payment_transaction.save()
        
        return {'success': True, 'transaction': payment_transaction, 'requires_verification': True}
    
    @staticmethod
    def _process_generic_payment(payment_transaction, **kwargs):
        """Process generic payment gateway"""
        payment_transaction.status = 'completed'
        payment_transaction.gateway_response = kwargs
        payment_transaction.processed_at = timezone.now()
        payment_transaction.save()
        
        return {'success': True, 'transaction': payment_transaction}


class PaymentRefundService:
    """Service for handling payment refunds"""
    
    @staticmethod
    def create_refund(payment_transaction, amount, reason, requested_by, refund_type='partial'):
        """Create a refund request"""
        try:
            with transaction.atomic():
                # Validate refund amount
                from django.db.models import Sum
                total_refunded = payment_transaction.refunds.filter(
                    status='completed'
                ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
                
                if total_refunded + amount > payment_transaction.amount:
                    raise ValidationError("Refund amount exceeds available balance")
                
                refund = PaymentRefund.objects.create(
                    payment_transaction=payment_transaction,
                    refund_type=refund_type,
                    amount=amount,
                    reason=reason,
                    requested_by=requested_by,
                    school=payment_transaction.school
                )
                
                return refund
                
        except Exception as e:
            logger.error(f"Refund creation failed: {str(e)}")
            raise ValidationError(f"Refund creation failed: {str(e)}")
    
    @staticmethod
    def process_refund(refund, approved_by):
        """Process a refund through the payment gateway"""
        try:
            with transaction.atomic():
                refund.approved_by = approved_by
                refund.status = 'processing'
                refund.save()
                
                gateway = refund.payment_transaction.gateway
                
                # Process based on gateway type
                if gateway.gateway_type == 'stripe':
                    result = PaymentRefundService._process_stripe_refund(refund)
                elif gateway.gateway_type == 'paypal':
                    result = PaymentRefundService._process_paypal_refund(refund)
                else:
                    result = PaymentRefundService._process_generic_refund(refund)
                
                if result['success']:
                    refund.status = 'completed'
                    refund.processed_at = timezone.now()
                    refund.gateway_response = result.get('response', {})
                else:
                    refund.status = 'failed'
                
                refund.save()
                return result
                
        except Exception as e:
            logger.error(f"Refund processing failed: {str(e)}")
            refund.status = 'failed'
            refund.save()
            raise ValidationError(f"Refund processing failed: {str(e)}")
    
    @staticmethod
    def _process_stripe_refund(refund):
        """Process Stripe refund"""
        # Mock Stripe refund API call
        response = {
            'id': f're_{refund.refund_id}',
            'status': 'succeeded',
            'amount': int(refund.amount * 100),
            'charge': refund.payment_transaction.gateway_transaction_id
        }
        
        refund.gateway_refund_id = response['id']
        return {'success': True, 'response': response}
    
    @staticmethod
    def _process_paypal_refund(refund):
        """Process PayPal refund"""
        # Mock PayPal refund API call
        response = {
            'id': f'REFUND-{refund.refund_id}',
            'state': 'completed',
            'amount': {
                'total': str(refund.amount),
                'currency': 'SAR'
            }
        }
        
        refund.gateway_refund_id = response['id']
        return {'success': True, 'response': response}
    
    @staticmethod
    def _process_generic_refund(refund):
        """Process generic refund"""
        return {'success': True, 'response': {'status': 'processed'}}


class PaymentReminderService:
    """Service for handling payment reminders"""
    
    @staticmethod
    def create_reminder(student_fee, reminder_type, scheduled_date, message_template):
        """Create a payment reminder"""
        try:
            # Determine recipient based on reminder type
            if reminder_type == 'email':
                recipient = student_fee.student.email or getattr(student_fee.student, 'parent_email', None)
            elif reminder_type in ['sms', 'whatsapp']:
                recipient = student_fee.student.phone or getattr(student_fee.student, 'parent_phone', None)
            else:
                recipient = str(student_fee.student.id)
            
            if not recipient:
                raise ValidationError(f"No {reminder_type} contact available for student")
            
            reminder = PaymentReminder.objects.create(
                student_fee=student_fee,
                reminder_type=reminder_type,
                scheduled_date=scheduled_date,
                message_template=message_template,
                recipient=recipient,
                school=student_fee.school
            )
            
            return reminder
            
        except Exception as e:
            logger.error(f"Reminder creation failed: {str(e)}")
            raise ValidationError(f"Reminder creation failed: {str(e)}")
    
    @staticmethod
    def send_reminder(reminder):
        """Send a payment reminder"""
        try:
            if reminder.attempts >= reminder.max_attempts:
                reminder.status = 'failed'
                reminder.error_message = "Maximum attempts reached"
                reminder.save()
                return False
            
            reminder.attempts += 1
            
            # Send based on reminder type
            if reminder.reminder_type == 'email':
                success = PaymentReminderService._send_email_reminder(reminder)
            elif reminder.reminder_type == 'sms':
                success = PaymentReminderService._send_sms_reminder(reminder)
            elif reminder.reminder_type == 'whatsapp':
                success = PaymentReminderService._send_whatsapp_reminder(reminder)
            else:
                success = PaymentReminderService._send_push_reminder(reminder)
            
            if success:
                reminder.status = 'sent'
                reminder.sent_date = timezone.now()
            else:
                reminder.status = 'failed' if reminder.attempts >= reminder.max_attempts else 'scheduled'
            
            reminder.save()
            return success
            
        except Exception as e:
            logger.error(f"Reminder sending failed: {str(e)}")
            reminder.status = 'failed'
            reminder.error_message = str(e)
            reminder.save()
            return False
    
    @staticmethod
    def _send_email_reminder(reminder):
        """Send email reminder"""
        # Mock email sending
        logger.info(f"Sending email reminder to {reminder.recipient}")
        return True
    
    @staticmethod
    def _send_sms_reminder(reminder):
        """Send SMS reminder"""
        # Mock SMS sending
        logger.info(f"Sending SMS reminder to {reminder.recipient}")
        return True
    
    @staticmethod
    def _send_whatsapp_reminder(reminder):
        """Send WhatsApp reminder"""
        # Mock WhatsApp sending
        logger.info(f"Sending WhatsApp reminder to {reminder.recipient}")
        return True
    
    @staticmethod
    def _send_push_reminder(reminder):
        """Send push notification reminder"""
        # Mock push notification
        logger.info(f"Sending push reminder to {reminder.recipient}")
        return True


class PaymentAnalyticsService:
    """Service for payment analytics and reporting"""
    
    @staticmethod
    def generate_daily_analytics(school, date):
        """Generate daily payment analytics"""
        from django.db.models import Sum, Count
        
        # Get payments for the date
        payments = Payment.objects.filter(
            school=school,
            payment_date=date
        )
        
        # Get payment transactions
        transactions = PaymentTransaction.objects.filter(
            payment__in=payments
        )
        
        # Calculate metrics
        total_payments = payments.count()
        total_amount = payments.aggregate(total=Sum('amount'))['total'] or Decimal('0')
        
        successful_payments = transactions.filter(status='completed').count()
        failed_payments = transactions.filter(status='failed').count()
        
        refunds = PaymentRefund.objects.filter(
            payment_transaction__payment__in=payments,
            status='completed'
        )
        refunded_payments = refunds.count()
        refunded_amount = refunds.aggregate(total=Sum('amount'))['total'] or Decimal('0')
        
        gateway_fees = transactions.aggregate(total=Sum('gateway_fee'))['total'] or Decimal('0')
        net_revenue = total_amount - refunded_amount - gateway_fees
        
        # Payment methods breakdown
        payment_methods = {}
        for method in payments.values('payment_method').annotate(
            count=Count('id'),
            total=Sum('amount')
        ):
            payment_methods[method['payment_method']] = {
                'count': method['count'],
                'total': float(method['total'])
            }
        
        # Gateway breakdown
        gateway_breakdown = {}
        for gateway_data in transactions.values('gateway__name').annotate(
            count=Count('id'),
            total=Sum('amount')
        ):
            gateway_breakdown[gateway_data['gateway__name']] = {
                'count': gateway_data['count'],
                'total': float(gateway_data['total'])
            }
        
        # Create or update analytics record
        analytics, created = PaymentAnalytics.objects.update_or_create(
            school=school,
            date=date,
            defaults={
                'total_payments': total_payments,
                'total_amount': total_amount,
                'successful_payments': successful_payments,
                'failed_payments': failed_payments,
                'refunded_payments': refunded_payments,
                'refunded_amount': refunded_amount,
                'gateway_fees': gateway_fees,
                'net_revenue': net_revenue,
                'payment_methods': payment_methods,
                'gateway_breakdown': gateway_breakdown,
            }
        )
        
        return analytics
    
    @staticmethod
    def get_payment_trends(school, start_date, end_date):
        """Get payment trends for a date range"""
        analytics = PaymentAnalytics.objects.filter(
            school=school,
            date__range=[start_date, end_date]
        ).order_by('date')
        
        return {
            'dates': [a.date.isoformat() for a in analytics],
            'total_amounts': [float(a.total_amount) for a in analytics],
            'successful_payments': [a.successful_payments for a in analytics],
            'failed_payments': [a.failed_payments for a in analytics],
            'net_revenue': [float(a.net_revenue) for a in analytics],
        }
    
    @staticmethod
    def get_gateway_performance(school, start_date, end_date):
        """Get gateway performance metrics"""
        transactions = PaymentTransaction.objects.filter(
            school=school,
            created_at__date__range=[start_date, end_date]
        ).select_related('gateway')
        
        performance = {}
        for transaction in transactions:
            gateway_name = transaction.gateway.name
            if gateway_name not in performance:
                performance[gateway_name] = {
                    'total_transactions': 0,
                    'successful_transactions': 0,
                    'failed_transactions': 0,
                    'total_amount': Decimal('0'),
                    'total_fees': Decimal('0'),
                    'success_rate': 0
                }
            
            performance[gateway_name]['total_transactions'] += 1
            performance[gateway_name]['total_amount'] += transaction.amount
            performance[gateway_name]['total_fees'] += transaction.gateway_fee
            
            if transaction.status == 'completed':
                performance[gateway_name]['successful_transactions'] += 1
            elif transaction.status == 'failed':
                performance[gateway_name]['failed_transactions'] += 1
        
        # Calculate success rates
        for gateway_name, data in performance.items():
            if data['total_transactions'] > 0:
                data['success_rate'] = (
                    data['successful_transactions'] / data['total_transactions']
                ) * 100
        
        return performance