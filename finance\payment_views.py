from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.views.generic import (
    TemplateView, ListView, DetailView, CreateView, 
    UpdateView, DeleteView, View
)
from django.contrib import messages
from django.urls import reverse_lazy
from django.db.models import Q, Sum, Count
from django.http import HttpResponse, JsonResponse
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.core.exceptions import ValidationError
from datetime import datetime, timedelta
from decimal import Decimal
from .models import (
    PaymentGateway, PaymentTransaction, PaymentRefund, 
    PaymentReminder, PaymentAnalytics, Payment, StudentFee
)
from .payment_forms import (
    PaymentGatewayForm, PaymentProcessingForm, PaymentRefundForm,
    PaymentReminderForm, BulkPaymentReminderForm, PaymentAnalyticsFilterForm,
    PaymentSearchForm
)
from .payment_services import (
    PaymentGatewayService, PaymentRefundService, 
    PaymentReminderService, PaymentAnalyticsService
)


class PaymentDashboardView(LoginRequiredMixin, TemplateView):
    """Payment processing dashboard"""
    template_name = 'finance/payment/dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            # Get current month data
            current_month = timezone.now().replace(day=1)
            next_month = (current_month + timedelta(days=32)).replace(day=1)
            
            # Payment statistics
            context['total_payments_today'] = Payment.objects.filter(
                school=school,
                payment_date=timezone.now().date()
            ).count()
            
            context['total_amount_today'] = Payment.objects.filter(
                school=school,
                payment_date=timezone.now().date()
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
            
            # Transaction statistics
            transactions_today = PaymentTransaction.objects.filter(
                school=school,
                created_at__date=timezone.now().date()
            )
            
            context['successful_transactions'] = transactions_today.filter(
                status='completed'
            ).count()
            
            context['failed_transactions'] = transactions_today.filter(
                status='failed'
            ).count()
            
            # Pending refunds
            context['pending_refunds'] = PaymentRefund.objects.filter(
                payment_transaction__school=school,
                status='pending'
            ).count()
            
            # Active gateways
            context['active_gateways'] = PaymentGateway.objects.filter(
                school=school,
                is_enabled=True,
                is_active=True
            ).count()
            
            # Recent transactions
            context['recent_transactions'] = PaymentTransaction.objects.filter(
                school=school
            ).select_related('payment', 'gateway').order_by('-created_at')[:10]
            
            # Payment method breakdown
            payment_methods = Payment.objects.filter(
                school=school,
                payment_date__gte=current_month,
                payment_date__lt=next_month
            ).values('payment_method').annotate(
                count=Count('id'),
                total=Sum('amount')
            )
            
            context['payment_method_data'] = {
                'labels': [pm['payment_method'] for pm in payment_methods],
                'data': [float(pm['total']) for pm in payment_methods]
            }
        
        return context


class PaymentGatewayListView(LoginRequiredMixin, ListView):
    """List all payment gateways"""
    model = PaymentGateway
    template_name = 'finance/payment/gateway_list.html'
    context_object_name = 'gateways'
    paginate_by = 20
    
    def get_queryset(self):
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        if not school:
            return PaymentGateway.objects.none()
        
        return PaymentGateway.objects.filter(school=school).order_by('name')


class PaymentGatewayCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    """Create new payment gateway"""
    model = PaymentGateway
    form_class = PaymentGatewayForm
    template_name = 'finance/payment/gateway_form.html'
    permission_required = 'finance.add_paymentgateway'
    success_url = reverse_lazy('finance:payment_gateways')
    
    def form_valid(self, form):
        if hasattr(self.request.user, 'employee'):
            form.instance.school = self.request.user.employee.school
        messages.success(self.request, _('Payment gateway created successfully!'))
        return super().form_valid(form)


class PaymentGatewayUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    """Update payment gateway"""
    model = PaymentGateway
    form_class = PaymentGatewayForm
    template_name = 'finance/payment/gateway_form.html'
    permission_required = 'finance.change_paymentgateway'
    success_url = reverse_lazy('finance:payment_gateways')
    
    def get_queryset(self):
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        if not school:
            return PaymentGateway.objects.none()
        return PaymentGateway.objects.filter(school=school)
    
    def form_valid(self, form):
        messages.success(self.request, _('Payment gateway updated successfully!'))
        return super().form_valid(form)


class PaymentProcessingView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    """Process payments through gateways"""
    template_name = 'finance/payment/process_payment.html'
    permission_required = 'finance.add_payment'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            context['form'] = PaymentProcessingForm(school=school)
            context['available_gateways'] = PaymentGatewayService.get_available_gateways(school)
        
        return context
    
    def post(self, request, *args, **kwargs):
        school = request.user.employee.school if hasattr(request.user, 'employee') else None
        form = PaymentProcessingForm(school=school, data=request.POST)
        
        if form.is_valid():
            try:
                # Create payment record
                payment = Payment.objects.create(
                    student=form.cleaned_data['student'],
                    amount=form.cleaned_data['amount'],
                    payment_date=timezone.now().date(),
                    payment_method=form.cleaned_data['payment_method'],
                    reference_number=form.cleaned_data['reference_number'],
                    notes=form.cleaned_data['notes'],
                    received_by=request.user,
                    school=school
                )
                
                # Process through gateway
                gateway = form.cleaned_data['gateway']
                result = PaymentGatewayService.process_payment(
                    payment, gateway,
                    reference_number=form.cleaned_data['reference_number']
                )
                
                if result['success']:
                    messages.success(request, _('Payment processed successfully!'))
                    return redirect('finance:payment_detail', pk=payment.pk)
                else:
                    messages.error(request, f"Payment failed: {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                messages.error(request, f"Payment processing failed: {str(e)}")
        
        context = self.get_context_data()
        context['form'] = form
        return render(request, self.template_name, context)


class PaymentTransactionListView(LoginRequiredMixin, ListView):
    """List payment transactions"""
    model = PaymentTransaction
    template_name = 'finance/payment/transaction_list.html'
    context_object_name = 'transactions'
    paginate_by = 20
    
    def get_queryset(self):
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        if not school:
            return PaymentTransaction.objects.none()
        
        queryset = PaymentTransaction.objects.filter(
            school=school
        ).select_related('payment', 'gateway').order_by('-created_at')
        
        # Apply filters
        status = self.request.GET.get('status')
        gateway_id = self.request.GET.get('gateway')
        
        if status:
            queryset = queryset.filter(status=status)
        if gateway_id:
            queryset = queryset.filter(gateway_id=gateway_id)
        
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            context['gateways'] = PaymentGateway.objects.filter(school=school)
            context['status_choices'] = PaymentTransaction.TRANSACTION_STATUS
        
        return context


class PaymentTransactionDetailView(LoginRequiredMixin, DetailView):
    """Payment transaction detail view"""
    model = PaymentTransaction
    template_name = 'finance/payment/transaction_detail.html'
    context_object_name = 'transaction'
    
    def get_queryset(self):
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        if not school:
            return PaymentTransaction.objects.none()
        return PaymentTransaction.objects.filter(school=school)


class PaymentRefundListView(LoginRequiredMixin, ListView):
    """List payment refunds"""
    model = PaymentRefund
    template_name = 'finance/payment/refund_list.html'
    context_object_name = 'refunds'
    paginate_by = 20
    
    def get_queryset(self):
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        if not school:
            return PaymentRefund.objects.none()
        
        return PaymentRefund.objects.filter(
            payment_transaction__school=school
        ).select_related('payment_transaction', 'requested_by').order_by('-created_at')


class PaymentRefundCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    """Create payment refund"""
    model = PaymentRefund
    form_class = PaymentRefundForm
    template_name = 'finance/payment/refund_form.html'
    permission_required = 'finance.add_paymentrefund'
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        transaction_id = self.kwargs.get('transaction_id')
        if transaction_id:
            kwargs['payment_transaction'] = get_object_or_404(
                PaymentTransaction, 
                id=transaction_id,
                school=self.request.user.employee.school
            )
        return kwargs
    
    def form_valid(self, form):
        try:
            transaction_id = self.kwargs.get('transaction_id')
            payment_transaction = get_object_or_404(
                PaymentTransaction,
                id=transaction_id,
                school=self.request.user.employee.school
            )
            
            refund = PaymentRefundService.create_refund(
                payment_transaction=payment_transaction,
                amount=form.cleaned_data['amount'],
                reason=form.cleaned_data['reason'],
                requested_by=self.request.user,
                refund_type=form.cleaned_data['refund_type']
            )
            
            messages.success(self.request, _('Refund request created successfully!'))
            return redirect('finance:refund_detail', pk=refund.pk)
            
        except ValidationError as e:
            messages.error(self.request, str(e))
            return self.form_invalid(form)


class PaymentRefundProcessView(LoginRequiredMixin, PermissionRequiredMixin, View):
    """Process payment refund"""
    permission_required = 'finance.change_paymentrefund'
    
    def post(self, request, pk):
        refund = get_object_or_404(
            PaymentRefund,
            pk=pk,
            payment_transaction__school=request.user.employee.school
        )
        
        try:
            result = PaymentRefundService.process_refund(refund, request.user)
            
            if result['success']:
                messages.success(request, _('Refund processed successfully!'))
            else:
                messages.error(request, f"Refund processing failed: {result.get('error', 'Unknown error')}")
                
        except ValidationError as e:
            messages.error(request, str(e))
        
        return redirect('finance:refund_detail', pk=pk)


class PaymentReminderListView(LoginRequiredMixin, ListView):
    """List payment reminders"""
    model = PaymentReminder
    template_name = 'finance/payment/reminder_list.html'
    context_object_name = 'reminders'
    paginate_by = 20
    
    def get_queryset(self):
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        if not school:
            return PaymentReminder.objects.none()
        
        return PaymentReminder.objects.filter(
            school=school
        ).select_related('student_fee').order_by('-scheduled_date')


class PaymentReminderCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    """Create payment reminder"""
    model = PaymentReminder
    form_class = PaymentReminderForm
    template_name = 'finance/payment/reminder_form.html'
    permission_required = 'finance.add_paymentreminder'
    success_url = reverse_lazy('finance:payment_reminders')
    
    def form_valid(self, form):
        try:
            student_fee_id = self.kwargs.get('student_fee_id')
            student_fee = get_object_or_404(
                StudentFee,
                id=student_fee_id,
                school=self.request.user.employee.school
            )
            
            reminder = PaymentReminderService.create_reminder(
                student_fee=student_fee,
                reminder_type=form.cleaned_data['reminder_type'],
                scheduled_date=form.cleaned_data['scheduled_date'],
                message_template=form.cleaned_data['message_template']
            )
            
            messages.success(self.request, _('Payment reminder created successfully!'))
            return redirect('finance:payment_reminders')
            
        except ValidationError as e:
            messages.error(self.request, str(e))
            return self.form_invalid(form)


class BulkPaymentReminderView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    """Create bulk payment reminders"""
    template_name = 'finance/payment/bulk_reminder.html'
    permission_required = 'finance.add_paymentreminder'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            context['form'] = BulkPaymentReminderForm(school=school)
        
        return context
    
    def post(self, request, *args, **kwargs):
        school = request.user.employee.school if hasattr(request.user, 'employee') else None
        form = BulkPaymentReminderForm(school=school, data=request.POST)
        
        if form.is_valid():
            try:
                # Get student fees based on filters
                student_fees = StudentFee.objects.filter(
                    school=school,
                    is_paid=False
                )
                
                if form.cleaned_data['grade']:
                    student_fees = student_fees.filter(
                        grade_fee__grade=form.cleaned_data['grade']
                    )
                
                if form.cleaned_data['fee_type']:
                    student_fees = student_fees.filter(
                        grade_fee__fee_type=form.cleaned_data['fee_type']
                    )
                
                if form.cleaned_data['overdue_only']:
                    student_fees = student_fees.filter(
                        due_date__lt=timezone.now().date()
                    )
                
                # Create reminders
                created_count = 0
                for student_fee in student_fees:
                    try:
                        PaymentReminderService.create_reminder(
                            student_fee=student_fee,
                            reminder_type=form.cleaned_data['reminder_type'],
                            scheduled_date=form.cleaned_data['scheduled_date'],
                            message_template=form.cleaned_data['message_template']
                        )
                        created_count += 1
                    except ValidationError:
                        continue  # Skip if reminder creation fails
                
                messages.success(
                    request, 
                    _(f'{created_count} payment reminders created successfully!')
                )
                return redirect('finance:payment_reminders')
                
            except Exception as e:
                messages.error(request, f"Bulk reminder creation failed: {str(e)}")
        
        context = self.get_context_data()
        context['form'] = form
        return render(request, self.template_name, context)


class PaymentAnalyticsView(LoginRequiredMixin, TemplateView):
    """Payment analytics and reporting"""
    template_name = 'finance/payment/analytics.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            # Default to last 30 days
            end_date = timezone.now().date()
            start_date = end_date - timedelta(days=30)
            
            form = PaymentAnalyticsFilterForm(school=school, initial={
                'start_date': start_date,
                'end_date': end_date
            })
            
            if self.request.GET:
                form = PaymentAnalyticsFilterForm(school=school, data=self.request.GET)
                if form.is_valid():
                    start_date = form.cleaned_data['start_date']
                    end_date = form.cleaned_data['end_date']
            
            context['form'] = form
            
            # Get analytics data
            context['payment_trends'] = PaymentAnalyticsService.get_payment_trends(
                school, start_date, end_date
            )
            
            context['gateway_performance'] = PaymentAnalyticsService.get_gateway_performance(
                school, start_date, end_date
            )
            
            # Summary statistics
            analytics = PaymentAnalytics.objects.filter(
                school=school,
                date__range=[start_date, end_date]
            ).aggregate(
                total_payments=Sum('total_payments'),
                total_amount=Sum('total_amount'),
                successful_payments=Sum('successful_payments'),
                failed_payments=Sum('failed_payments'),
                refunded_amount=Sum('refunded_amount'),
                gateway_fees=Sum('gateway_fees'),
                net_revenue=Sum('net_revenue')
            )
            
            context['summary'] = {
                'total_payments': analytics['total_payments'] or 0,
                'total_amount': analytics['total_amount'] or Decimal('0'),
                'successful_payments': analytics['successful_payments'] or 0,
                'failed_payments': analytics['failed_payments'] or 0,
                'refunded_amount': analytics['refunded_amount'] or Decimal('0'),
                'gateway_fees': analytics['gateway_fees'] or Decimal('0'),
                'net_revenue': analytics['net_revenue'] or Decimal('0'),
            }
            
            # Calculate success rate
            total_transactions = context['summary']['successful_payments'] + context['summary']['failed_payments']
            if total_transactions > 0:
                context['summary']['success_rate'] = (
                    context['summary']['successful_payments'] / total_transactions
                ) * 100
            else:
                context['summary']['success_rate'] = 0
        
        return context