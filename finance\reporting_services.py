"""
Financial Reporting Services for School ERP System
Provides comprehensive financial reporting capabilities including:
- Balance Sheet generation
- Profit & Loss statements
- Cash Flow reports
- Budget vs Actual reporting
- Financial dashboards
"""

from django.db import models
from django.db.models import Sum, Q, F, Case, When, Value, DecimalField
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from decimal import Decimal
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging

from .models import (
    Account, AccountType, Transaction, TransactionEntry, Budget, BudgetItem,
    FinancialYear, CostCenter, Payment, StudentFee
)

logger = logging.getLogger(__name__)


class FinancialReportingService:
    """Main service class for financial reporting"""
    
    def __init__(self, school):
        self.school = school
    
    def generate_balance_sheet(self, as_of_date: date = None, 
                             include_zero_balances: bool = False) -> Dict:
        """
        Generate Balance Sheet report
        
        Args:
            as_of_date: Date for the balance sheet (defaults to today)
            include_zero_balances: Whether to include accounts with zero balances
            
        Returns:
            Dict containing balance sheet data
        """
        try:
            if as_of_date is None:
                as_of_date = date.today()
            
            logger.info(f"Generating balance sheet for {self.school.name} as of {as_of_date}")
            
            # Get all balance sheet accounts (Assets, Liabilities, Equity)
            balance_sheet_types = ['asset', 'liability', 'equity']
            accounts = Account.objects.filter(
                school=self.school,
                account_type__type__in=balance_sheet_types,
                is_active=True,
                archived_at__isnull=True
            ).select_related('account_type', 'parent').order_by('code')
            
            balance_sheet = {
                'title': _('Balance Sheet'),
                'school': self.school.name,
                'as_of_date': as_of_date,
                'assets': {
                    'current_assets': [],
                    'fixed_assets': [],
                    'other_assets': [],
                    'total_assets': Decimal('0')
                },
                'liabilities': {
                    'current_liabilities': [],
                    'long_term_liabilities': [],
                    'total_liabilities': Decimal('0')
                },
                'equity': {
                    'items': [],
                    'total_equity': Decimal('0')
                },
                'total_liabilities_equity': Decimal('0')
            }
            
            for account in accounts:
                if account.is_header:
                    continue
                
                balance = account.get_balance(as_of_date)
                
                if not include_zero_balances and balance == 0:
                    continue
                
                account_data = {
                    'code': account.code,
                    'name': account.name,
                    'name_ar': account.name_ar or '',
                    'balance': balance,
                    'level': account.level
                }
                
                # Categorize accounts
                if account.account_type.type == 'asset':
                    if 'current' in account.name.lower() or account.code.startswith('11'):
                        balance_sheet['assets']['current_assets'].append(account_data)
                    elif 'fixed' in account.name.lower() or account.code.startswith('12'):
                        balance_sheet['assets']['fixed_assets'].append(account_data)
                    else:
                        balance_sheet['assets']['other_assets'].append(account_data)
                    balance_sheet['assets']['total_assets'] += balance
                
                elif account.account_type.type == 'liability':
                    if 'current' in account.name.lower() or account.code.startswith('21'):
                        balance_sheet['liabilities']['current_liabilities'].append(account_data)
                    else:
                        balance_sheet['liabilities']['long_term_liabilities'].append(account_data)
                    balance_sheet['liabilities']['total_liabilities'] += balance
                
                elif account.account_type.type == 'equity':
                    balance_sheet['equity']['items'].append(account_data)
                    balance_sheet['equity']['total_equity'] += balance
            
            balance_sheet['total_liabilities_equity'] = (
                balance_sheet['liabilities']['total_liabilities'] + 
                balance_sheet['equity']['total_equity']
            )
            
            # Calculate balance sheet balance check
            balance_sheet['is_balanced'] = abs(
                balance_sheet['assets']['total_assets'] - 
                balance_sheet['total_liabilities_equity']
            ) < 0.01
            
            logger.info(f"Balance sheet generated successfully for {self.school.name}")
            return balance_sheet
            
        except Exception as e:
            logger.error(f"Error generating balance sheet: {str(e)}")
            raise
    
    def generate_profit_loss_statement(self, start_date: date, end_date: date = None,
                                     include_zero_balances: bool = False) -> Dict:
        """
        Generate Profit & Loss Statement
        
        Args:
            start_date: Start date for the period
            end_date: End date for the period (defaults to today)
            include_zero_balances: Whether to include accounts with zero balances
            
        Returns:
            Dict containing P&L statement data
        """
        try:
            if end_date is None:
                end_date = date.today()
            
            logger.info(f"Generating P&L statement for {self.school.name} from {start_date} to {end_date}")
            
            # Get revenue and expense accounts
            pl_types = ['revenue', 'expense']
            accounts = Account.objects.filter(
                school=self.school,
                account_type__type__in=pl_types,
                is_active=True,
                archived_at__isnull=True
            ).select_related('account_type').order_by('code')
            
            pl_statement = {
                'title': _('Profit & Loss Statement'),
                'school': self.school.name,
                'period': {
                    'start_date': start_date,
                    'end_date': end_date
                },
                'revenue': {
                    'operating_revenue': [],
                    'other_revenue': [],
                    'total_revenue': Decimal('0')
                },
                'expenses': {
                    'operating_expenses': [],
                    'administrative_expenses': [],
                    'other_expenses': [],
                    'total_expenses': Decimal('0')
                },
                'gross_profit': Decimal('0'),
                'net_income': Decimal('0')
            }
            
            for account in accounts:
                if account.is_header:
                    continue
                
                # Get period balance for revenue/expense accounts
                balance = self._get_period_balance(account, start_date, end_date)
                
                if not include_zero_balances and balance == 0:
                    continue
                
                account_data = {
                    'code': account.code,
                    'name': account.name,
                    'name_ar': account.name_ar or '',
                    'balance': balance,
                    'level': account.level
                }
                
                if account.account_type.type == 'revenue':
                    if 'tuition' in account.name.lower() or 'fee' in account.name.lower():
                        pl_statement['revenue']['operating_revenue'].append(account_data)
                    else:
                        pl_statement['revenue']['other_revenue'].append(account_data)
                    pl_statement['revenue']['total_revenue'] += balance
                
                elif account.account_type.type == 'expense':
                    if ('salary' in account.name.lower() or 'wage' in account.name.lower() or
                        'teaching' in account.name.lower()):
                        pl_statement['expenses']['operating_expenses'].append(account_data)
                    elif ('admin' in account.name.lower() or 'office' in account.name.lower()):
                        pl_statement['expenses']['administrative_expenses'].append(account_data)
                    else:
                        pl_statement['expenses']['other_expenses'].append(account_data)
                    pl_statement['expenses']['total_expenses'] += balance
            
            # Calculate profit metrics
            pl_statement['gross_profit'] = (
                pl_statement['revenue']['total_revenue'] - 
                pl_statement['expenses']['total_expenses']
            )
            pl_statement['net_income'] = pl_statement['gross_profit']
            
            logger.info(f"P&L statement generated successfully for {self.school.name}")
            return pl_statement
            
        except Exception as e:
            logger.error(f"Error generating P&L statement: {str(e)}")
            raise
    
    def generate_cash_flow_statement(self, start_date: date, end_date: date = None) -> Dict:
        """
        Generate Cash Flow Statement
        
        Args:
            start_date: Start date for the period
            end_date: End date for the period (defaults to today)
            
        Returns:
            Dict containing cash flow statement data
        """
        try:
            if end_date is None:
                end_date = date.today()
            
            logger.info(f"Generating cash flow statement for {self.school.name} from {start_date} to {end_date}")
            
            # Get cash and cash equivalent accounts
            cash_accounts = Account.objects.filter(
                school=self.school,
                is_active=True,
                archived_at__isnull=True
            ).filter(
                Q(name__icontains='cash') | 
                Q(name__icontains='bank') |
                Q(code__startswith='111') |  # Assuming cash accounts start with 111
                Q(code__startswith='112')    # Assuming bank accounts start with 112
            ).select_related('account_type')
            
            cash_flow = {
                'title': _('Cash Flow Statement'),
                'school': self.school.name,
                'period': {
                    'start_date': start_date,
                    'end_date': end_date
                },
                'operating_activities': {
                    'cash_receipts': [],
                    'cash_payments': [],
                    'net_operating_cash': Decimal('0')
                },
                'investing_activities': {
                    'items': [],
                    'net_investing_cash': Decimal('0')
                },
                'financing_activities': {
                    'items': [],
                    'net_financing_cash': Decimal('0')
                },
                'net_change_in_cash': Decimal('0'),
                'beginning_cash': Decimal('0'),
                'ending_cash': Decimal('0')
            }
            
            # Calculate beginning cash balance
            beginning_cash = Decimal('0')
            for account in cash_accounts:
                beginning_cash += account.get_balance(start_date - timedelta(days=1))
            cash_flow['beginning_cash'] = beginning_cash
            
            # Get all cash transactions for the period
            cash_entries = TransactionEntry.objects.filter(
                account__in=cash_accounts,
                entry_date__gte=start_date,
                entry_date__lte=end_date,
                is_posted=True
            ).select_related('account', 'transaction')
            
            # Categorize cash flows
            operating_receipts = Decimal('0')
            operating_payments = Decimal('0')
            
            for entry in cash_entries:
                # Determine if it's a receipt or payment
                if entry.debit_amount > 0:  # Cash increase
                    amount = entry.debit_amount
                    # Categorize based on transaction description or related account
                    if self._is_operating_activity(entry):
                        operating_receipts += amount
                        cash_flow['operating_activities']['cash_receipts'].append({
                            'description': entry.description,
                            'amount': amount,
                            'date': entry.entry_date
                        })
                    elif self._is_investing_activity(entry):
                        cash_flow['investing_activities']['items'].append({
                            'description': entry.description,
                            'amount': amount,
                            'date': entry.entry_date,
                            'type': 'inflow'
                        })
                        cash_flow['investing_activities']['net_investing_cash'] += amount
                    else:  # Financing activity
                        cash_flow['financing_activities']['items'].append({
                            'description': entry.description,
                            'amount': amount,
                            'date': entry.entry_date,
                            'type': 'inflow'
                        })
                        cash_flow['financing_activities']['net_financing_cash'] += amount
                
                elif entry.credit_amount > 0:  # Cash decrease
                    amount = entry.credit_amount
                    if self._is_operating_activity(entry):
                        operating_payments += amount
                        cash_flow['operating_activities']['cash_payments'].append({
                            'description': entry.description,
                            'amount': amount,
                            'date': entry.entry_date
                        })
                    elif self._is_investing_activity(entry):
                        cash_flow['investing_activities']['items'].append({
                            'description': entry.description,
                            'amount': amount,
                            'date': entry.entry_date,
                            'type': 'outflow'
                        })
                        cash_flow['investing_activities']['net_investing_cash'] -= amount
                    else:  # Financing activity
                        cash_flow['financing_activities']['items'].append({
                            'description': entry.description,
                            'amount': amount,
                            'date': entry.entry_date,
                            'type': 'outflow'
                        })
                        cash_flow['financing_activities']['net_financing_cash'] -= amount
            
            cash_flow['operating_activities']['net_operating_cash'] = operating_receipts - operating_payments
            
            # Calculate totals
            cash_flow['net_change_in_cash'] = (
                cash_flow['operating_activities']['net_operating_cash'] +
                cash_flow['investing_activities']['net_investing_cash'] +
                cash_flow['financing_activities']['net_financing_cash']
            )
            
            cash_flow['ending_cash'] = beginning_cash + cash_flow['net_change_in_cash']
            
            logger.info(f"Cash flow statement generated successfully for {self.school.name}")
            return cash_flow
            
        except Exception as e:
            logger.error(f"Error generating cash flow statement: {str(e)}")
            raise
    
    def generate_budget_vs_actual_report(self, budget_id: int, as_of_date: date = None) -> Dict:
        """
        Generate Budget vs Actual report
        
        Args:
            budget_id: ID of the budget to compare against
            as_of_date: Date for the comparison (defaults to today)
            
        Returns:
            Dict containing budget vs actual comparison data
        """
        try:
            if as_of_date is None:
                as_of_date = date.today()
            
            budget = Budget.objects.get(id=budget_id, school=self.school)
            
            logger.info(f"Generating budget vs actual report for budget {budget.name}")
            
            # Update spent amounts for all budget items
            for item in budget.items.all():
                item.update_spent_amount()
            
            budget_items = budget.items.select_related('account', 'cost_center').order_by('account__code')
            
            report = {
                'title': _('Budget vs Actual Report'),
                'school': self.school.name,
                'budget': {
                    'name': budget.name,
                    'name_ar': budget.name_ar or '',
                    'period': {
                        'start_date': budget.start_date,
                        'end_date': budget.end_date
                    },
                    'status': budget.status
                },
                'as_of_date': as_of_date,
                'summary': {
                    'total_budget': budget.get_total_allocated(),
                    'total_actual': budget.get_total_spent(),
                    'total_variance': budget.get_variance(),
                    'utilization_percentage': budget.get_utilization_percentage()
                },
                'items': [],
                'variance_analysis': {
                    'favorable_variances': [],
                    'unfavorable_variances': [],
                    'over_budget_items': []
                }
            }
            
            for item in budget_items:
                item_data = {
                    'account': {
                        'code': item.account.code,
                        'name': item.account.name,
                        'name_ar': item.account.name_ar or '',
                        'type': item.account.account_type.type
                    },
                    'cost_center': {
                        'name': item.cost_center.name if item.cost_center else None,
                        'name_ar': item.cost_center.name_ar if item.cost_center else None
                    },
                    'budget_amount': item.allocated_amount,
                    'actual_amount': item.spent_amount,
                    'variance': item.variance,
                    'utilization_percentage': item.utilization_percentage,
                    'notes': item.notes
                }
                
                report['items'].append(item_data)
                
                # Variance analysis
                if item.variance > 0:
                    if item.account.account_type.type == 'expense':
                        # For expenses, spending less is favorable
                        report['variance_analysis']['favorable_variances'].append(item_data)
                    else:
                        # For revenue, earning less is unfavorable
                        report['variance_analysis']['unfavorable_variances'].append(item_data)
                elif item.variance < 0:
                    if item.account.account_type.type == 'expense':
                        # For expenses, spending more is unfavorable
                        report['variance_analysis']['unfavorable_variances'].append(item_data)
                        if item.spent_amount > item.allocated_amount:
                            report['variance_analysis']['over_budget_items'].append(item_data)
                    else:
                        # For revenue, earning more is favorable
                        report['variance_analysis']['favorable_variances'].append(item_data)
            
            logger.info(f"Budget vs actual report generated successfully for budget {budget.name}")
            return report
            
        except Budget.DoesNotExist:
            logger.error(f"Budget with ID {budget_id} not found for school {self.school.name}")
            raise
        except Exception as e:
            logger.error(f"Error generating budget vs actual report: {str(e)}")
            raise
    
    def generate_financial_dashboard_data(self, period_days: int = 30) -> Dict:
        """
        Generate financial dashboard data
        
        Args:
            period_days: Number of days to include in the analysis
            
        Returns:
            Dict containing dashboard data
        """
        try:
            end_date = date.today()
            start_date = end_date - timedelta(days=period_days)
            
            logger.info(f"Generating financial dashboard data for {self.school.name}")
            
            dashboard = {
                'title': _('Financial Dashboard'),
                'school': self.school.name,
                'period': {
                    'start_date': start_date,
                    'end_date': end_date,
                    'days': period_days
                },
                'key_metrics': {},
                'revenue_analysis': {},
                'expense_analysis': {},
                'cash_position': {},
                'budget_performance': {},
                'trends': {}
            }
            
            # Key Metrics
            dashboard['key_metrics'] = self._calculate_key_metrics(start_date, end_date)
            
            # Revenue Analysis
            dashboard['revenue_analysis'] = self._analyze_revenue(start_date, end_date)
            
            # Expense Analysis
            dashboard['expense_analysis'] = self._analyze_expenses(start_date, end_date)
            
            # Cash Position
            dashboard['cash_position'] = self._analyze_cash_position()
            
            # Budget Performance
            dashboard['budget_performance'] = self._analyze_budget_performance()
            
            # Trends
            dashboard['trends'] = self._calculate_trends(start_date, end_date)
            
            logger.info(f"Financial dashboard data generated successfully for {self.school.name}")
            return dashboard
            
        except Exception as e:
            logger.error(f"Error generating financial dashboard data: {str(e)}")
            raise
    
    def _get_period_balance(self, account: Account, start_date: date, end_date: date) -> Decimal:
        """Get account balance for a specific period (for revenue/expense accounts)"""
        entries = TransactionEntry.objects.filter(
            account=account,
            entry_date__gte=start_date,
            entry_date__lte=end_date,
            is_posted=True
        )
        
        debits = entries.aggregate(total=Sum('debit_amount'))['total'] or Decimal('0')
        credits = entries.aggregate(total=Sum('credit_amount'))['total'] or Decimal('0')
        
        # For revenue accounts, credits are positive
        if account.account_type.type == 'revenue':
            return credits - debits
        # For expense accounts, debits are positive
        elif account.account_type.type == 'expense':
            return debits - credits
        else:
            # For balance sheet accounts, use normal balance type
            if account.balance_type == 'debit':
                return debits - credits
            else:
                return credits - debits
    
    def _is_operating_activity(self, entry: TransactionEntry) -> bool:
        """Determine if a transaction entry is an operating activity"""
        # Check if related to revenue or operating expenses
        description_lower = entry.description.lower()
        operating_keywords = [
            'tuition', 'fee', 'salary', 'wage', 'utility', 'rent', 'supply',
            'payment', 'receipt', 'student', 'teaching'
        ]
        return any(keyword in description_lower for keyword in operating_keywords)
    
    def _is_investing_activity(self, entry: TransactionEntry) -> bool:
        """Determine if a transaction entry is an investing activity"""
        description_lower = entry.description.lower()
        investing_keywords = [
            'equipment', 'building', 'asset', 'investment', 'purchase',
            'sale', 'disposal', 'capital'
        ]
        return any(keyword in description_lower for keyword in investing_keywords)
    
    def _calculate_key_metrics(self, start_date: date, end_date: date) -> Dict:
        """Calculate key financial metrics"""
        # Get revenue accounts
        revenue_accounts = Account.objects.filter(
            school=self.school,
            account_type__type='revenue',
            is_active=True
        )
        
        # Get expense accounts
        expense_accounts = Account.objects.filter(
            school=self.school,
            account_type__type='expense',
            is_active=True
        )
        
        total_revenue = sum(
            self._get_period_balance(account, start_date, end_date)
            for account in revenue_accounts
        )
        
        total_expenses = sum(
            self._get_period_balance(account, start_date, end_date)
            for account in expense_accounts
        )
        
        # Outstanding fees
        outstanding_fees = StudentFee.objects.filter(
            student__school=self.school,
            is_paid=False
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
        
        # Collection rate
        total_fees = StudentFee.objects.filter(
            student__school=self.school,
            due_date__gte=start_date,
            due_date__lte=end_date
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
        
        paid_fees = StudentFee.objects.filter(
            student__school=self.school,
            due_date__gte=start_date,
            due_date__lte=end_date,
            is_paid=True
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
        
        collection_rate = (paid_fees / total_fees * 100) if total_fees > 0 else Decimal('0')
        
        return {
            'total_revenue': total_revenue,
            'total_expenses': total_expenses,
            'net_income': total_revenue - total_expenses,
            'outstanding_fees': outstanding_fees,
            'collection_rate': collection_rate.quantize(Decimal('0.01')),
            'profit_margin': ((total_revenue - total_expenses) / total_revenue * 100).quantize(Decimal('0.01')) if total_revenue > 0 else Decimal('0')
        }
    
    def _analyze_revenue(self, start_date: date, end_date: date) -> Dict:
        """Analyze revenue breakdown"""
        revenue_accounts = Account.objects.filter(
            school=self.school,
            account_type__type='revenue',
            is_active=True,
            is_header=False
        )
        
        revenue_breakdown = []
        total_revenue = Decimal('0')
        
        for account in revenue_accounts:
            balance = self._get_period_balance(account, start_date, end_date)
            if balance > 0:
                revenue_breakdown.append({
                    'account': account.name,
                    'amount': balance
                })
                total_revenue += balance
        
        # Calculate percentages
        for item in revenue_breakdown:
            item['percentage'] = (item['amount'] / total_revenue * 100).quantize(Decimal('0.01')) if total_revenue > 0 else Decimal('0')
        
        return {
            'breakdown': revenue_breakdown,
            'total': total_revenue
        }
    
    def _analyze_expenses(self, start_date: date, end_date: date) -> Dict:
        """Analyze expense breakdown"""
        expense_accounts = Account.objects.filter(
            school=self.school,
            account_type__type='expense',
            is_active=True,
            is_header=False
        )
        
        expense_breakdown = []
        total_expenses = Decimal('0')
        
        for account in expense_accounts:
            balance = self._get_period_balance(account, start_date, end_date)
            if balance > 0:
                expense_breakdown.append({
                    'account': account.name,
                    'amount': balance
                })
                total_expenses += balance
        
        # Calculate percentages
        for item in expense_breakdown:
            item['percentage'] = (item['amount'] / total_expenses * 100).quantize(Decimal('0.01')) if total_expenses > 0 else Decimal('0')
        
        return {
            'breakdown': expense_breakdown,
            'total': total_expenses
        }
    
    def _analyze_cash_position(self) -> Dict:
        """Analyze current cash position"""
        cash_accounts = Account.objects.filter(
            school=self.school,
            is_active=True
        ).filter(
            Q(name__icontains='cash') | Q(name__icontains='bank')
        )
        
        cash_balances = []
        total_cash = Decimal('0')
        
        for account in cash_accounts:
            balance = account.get_balance()
            cash_balances.append({
                'account': account.name,
                'balance': balance
            })
            total_cash += balance
        
        return {
            'accounts': cash_balances,
            'total_cash': total_cash
        }
    
    def _analyze_budget_performance(self) -> Dict:
        """Analyze budget performance"""
        active_budgets = Budget.objects.filter(
            school=self.school,
            status='active'
        )
        
        budget_performance = []
        
        for budget in active_budgets:
            budget_performance.append({
                'name': budget.name,
                'allocated': budget.get_total_allocated(),
                'spent': budget.get_total_spent(),
                'utilization': budget.get_utilization_percentage()
            })
        
        return {
            'budgets': budget_performance
        }
    
    def _calculate_trends(self, start_date: date, end_date: date) -> Dict:
        """Calculate financial trends"""
        # Calculate monthly trends for the period
        current_month = start_date.replace(day=1)
        monthly_data = []
        
        while current_month <= end_date:
            next_month = (current_month + timedelta(days=32)).replace(day=1)
            month_end = min(next_month - timedelta(days=1), end_date)
            
            revenue = sum(
                self._get_period_balance(account, current_month, month_end)
                for account in Account.objects.filter(
                    school=self.school,
                    account_type__type='revenue',
                    is_active=True
                )
            )
            
            expenses = sum(
                self._get_period_balance(account, current_month, month_end)
                for account in Account.objects.filter(
                    school=self.school,
                    account_type__type='expense',
                    is_active=True
                )
            )
            
            monthly_data.append({
                'month': current_month.strftime('%Y-%m'),
                'revenue': revenue,
                'expenses': expenses,
                'net_income': revenue - expenses
            })
            
            current_month = next_month
        
        return {
            'monthly_data': monthly_data
        }