"""
Custom template filters for finance module
"""

from django import template
from decimal import Decimal, InvalidOperation

register = template.Library()


@register.filter
def div(value, divisor):
    """Divide two numbers"""
    try:
        if divisor == 0:
            return 0
        return Decimal(str(value)) / Decimal(str(divisor))
    except (ValueError, InvalidOperation, TypeError):
        return 0


@register.filter
def mul(value, multiplier):
    """Multiply two numbers"""
    try:
        return Decimal(str(value)) * Decimal(str(multiplier))
    except (ValueError, InvalidOperation, TypeError):
        return 0


@register.filter
def abs_value(value):
    """Get absolute value of a number"""
    try:
        return abs(Decimal(str(value)))
    except (ValueError, InvalidOperation, TypeError):
        return 0


@register.filter
def add_decimal(value, addend):
    """Add two decimal numbers"""
    try:
        return Decimal(str(value)) + Decimal(str(addend))
    except (ValueError, InvalidOperation, TypeError):
        return 0


@register.filter
def subtract(value, subtrahend):
    """Subtract two numbers"""
    try:
        return Decimal(str(value)) - Decimal(str(subtrahend))
    except (ValueError, InvalidOperation, TypeError):
        return 0


@register.filter
def percentage(value, total):
    """Calculate percentage"""
    try:
        if total == 0:
            return 0
        return (Decimal(str(value)) / Decimal(str(total))) * 100
    except (ValueError, InvalidOperation, TypeError):
        return 0


@register.filter
def currency(value):
    """Format value as currency"""
    try:
        return f"{Decimal(str(value)):,.2f}"
    except (ValueError, InvalidOperation, TypeError):
        return "0.00"


@register.filter
def variance_class(variance):
    """Return CSS class based on variance value"""
    try:
        if Decimal(str(variance)) > 0:
            return "text-success"
        elif Decimal(str(variance)) < 0:
            return "text-danger"
        else:
            return "text-muted"
    except (ValueError, InvalidOperation, TypeError):
        return "text-muted"


@register.filter
def utilization_class(utilization):
    """Return CSS class based on utilization percentage"""
    try:
        util = Decimal(str(utilization))
        if util <= 75:
            return "bg-success"
        elif util <= 90:
            return "bg-warning"
        else:
            return "bg-danger"
    except (ValueError, InvalidOperation, TypeError):
        return "bg-secondary"