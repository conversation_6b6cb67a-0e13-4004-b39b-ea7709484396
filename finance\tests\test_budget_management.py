"""
Test cases for Budget Management System

This module tests all budget management functionality including:
- Budget creation and planning
- Budget approval workflows
- Budget monitoring and variance analysis
- Budget alerts and notifications
- Budget reporting and analytics
"""

import pytest
from datetime import date, datetime, timedelta
from decimal import Decimal
from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, Permission
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.db import transaction

from core.models import School, AcademicYear
from students.models import Grade

User = get_user_model()
from finance.models import (
    Account, AccountType, Budget, BudgetItem, BudgetAlert, BudgetRevision,
    BudgetApprovalWorkflow, FinancialYear, CostCenter, Transaction, TransactionEntry
)
from finance.budget_services import BudgetService, BudgetMonitoringService, BudgetReportingService


class BudgetManagementTestCase(TestCase):
    """Base test case for budget management tests"""
    
    def setUp(self):
        """Set up test data"""
        # Create school
        self.school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2020, 1, 1)
        )
        
        # Create users
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.budget_manager = User.objects.create_user(
            username='budget_manager',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.approver = User.objects.create_user(
            username='approver',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create permissions and groups
        budget_group = Group.objects.create(name='Budget Managers')
        approver_group = Group.objects.create(name='Budget Approvers')
        
        # Add permissions
        approve_permission = Permission.objects.get_or_create(
            codename='approve_budget',
            name='Can approve budgets',
            content_type_id=1
        )[0]
        
        approver_group.permissions.add(approve_permission)
        self.approver.groups.add(approver_group)
        
        # Create financial year
        self.financial_year = FinancialYear.objects.create(
            school=self.school,
            name="2025",
            start_date=date(2025, 1, 1),
            end_date=date(2025, 12, 31),
            is_current=True,
            created_by=self.admin_user
        )
        
        # Create account types
        self.expense_type = AccountType.objects.create(
            school=self.school,
            name="Expense",
            type="expense",
            created_by=self.admin_user
        )
        
        self.revenue_type = AccountType.objects.create(
            school=self.school,
            name="Revenue",
            type="revenue",
            created_by=self.admin_user
        )
        
        # Create accounts
        self.salary_account = Account.objects.create(
            school=self.school,
            code="5001",
            name="Salary Expense",
            account_type=self.expense_type,
            created_by=self.admin_user
        )
        
        self.utilities_account = Account.objects.create(
            school=self.school,
            code="5002",
            name="Utilities Expense",
            account_type=self.expense_type,
            created_by=self.admin_user
        )
        
        self.tuition_account = Account.objects.create(
            school=self.school,
            code="4001",
            name="Tuition Revenue",
            account_type=self.revenue_type,
            created_by=self.admin_user
        )
        
        # Create cost center
        self.cost_center = CostCenter.objects.create(
            school=self.school,
            code="CC001",
            name="Administration",
            budget_amount=Decimal('50000.00'),
            created_by=self.admin_user
        )


class BudgetCreationTest(BudgetManagementTestCase):
    """Test budget creation functionality"""
    
    def test_create_budget_success(self):
        """Test successful budget creation"""
        budget = BudgetService.create_budget(
            school=self.school,
            name="Annual Budget 2025",
            budget_type="annual",
            financial_year=self.financial_year,
            start_date=date(2025, 1, 1),
            end_date=date(2025, 12, 31),
            description="Annual budget for 2025",
            created_by=self.budget_manager
        )
        
        self.assertIsNotNone(budget)
        self.assertEqual(budget.name, "Annual Budget 2025")
        self.assertEqual(budget.budget_type, "annual")
        self.assertEqual(budget.status, "draft")
        self.assertEqual(budget.created_by, self.budget_manager)
    
    def test_create_budget_invalid_dates(self):
        """Test budget creation with invalid dates"""
        with self.assertRaises(ValidationError):
            BudgetService.create_budget(
                school=self.school,
                name="Invalid Budget",
                budget_type="annual",
                financial_year=self.financial_year,
                start_date=date(2025, 12, 31),
                end_date=date(2025, 1, 1),  # End before start
                created_by=self.budget_manager
            )
    
    def test_create_overlapping_budget(self):
        """Test creation of overlapping budgets"""
        # Create first budget
        first_budget = BudgetService.create_budget(
            school=self.school,
            name="First Budget",
            budget_type="annual",
            financial_year=self.financial_year,
            start_date=date(2025, 1, 1),
            end_date=date(2025, 12, 31),
            created_by=self.budget_manager
        )
        
        # Add items and approve the first budget
        BudgetService.add_budget_items(first_budget, [
            {
                'account_id': self.salary_account.id,
                'allocated_amount': '10000.00'
            }
        ])
        BudgetService.submit_for_approval(first_budget, self.budget_manager)
        BudgetService.approve_budget(first_budget, self.approver)
        
        # Try to create overlapping budget
        with self.assertRaises(ValidationError):
            BudgetService.create_budget(
                school=self.school,
                name="Overlapping Budget",
                budget_type="annual",
                financial_year=self.financial_year,
                start_date=date(2025, 6, 1),
                end_date=date(2025, 12, 31),
                created_by=self.budget_manager
            )


class BudgetItemManagementTest(BudgetManagementTestCase):
    """Test budget item management"""
    
    def setUp(self):
        super().setUp()
        self.budget = BudgetService.create_budget(
            school=self.school,
            name="Test Budget",
            budget_type="annual",
            financial_year=self.financial_year,
            start_date=date(2025, 1, 1),
            end_date=date(2025, 12, 31),
            created_by=self.budget_manager
        )
    
    def test_add_budget_items_success(self):
        """Test successful addition of budget items"""
        items_data = [
            {
                'account_id': self.salary_account.id,
                'cost_center_id': self.cost_center.id,
                'allocated_amount': '30000.00',
                'notes': 'Staff salaries'
            },
            {
                'account_id': self.utilities_account.id,
                'allocated_amount': '5000.00',
                'notes': 'Electricity and water'
            }
        ]
        
        budget_items = BudgetService.add_budget_items(self.budget, items_data)
        
        self.assertEqual(len(budget_items), 2)
        self.assertEqual(budget_items[0].allocated_amount, Decimal('30000.00'))
        self.assertEqual(budget_items[1].allocated_amount, Decimal('5000.00'))
        
        # Check budget total is updated
        self.budget.refresh_from_db()
        self.assertEqual(self.budget.total_budget, Decimal('35000.00'))
    
    def test_add_budget_item_to_header_account(self):
        """Test adding budget item to header account (should fail)"""
        # Create header account
        header_account = Account.objects.create(
            school=self.school,
            code="5000",
            name="Expenses Header",
            account_type=self.expense_type,
            is_header=True,
            created_by=self.admin_user
        )
        
        items_data = [
            {
                'account_id': header_account.id,
                'allocated_amount': '10000.00'
            }
        ]
        
        with self.assertRaises(ValidationError):
            BudgetService.add_budget_items(self.budget, items_data)


class BudgetApprovalWorkflowTest(BudgetManagementTestCase):
    """Test budget approval workflow"""
    
    def setUp(self):
        super().setUp()
        self.budget = BudgetService.create_budget(
            school=self.school,
            name="Test Budget",
            budget_type="annual",
            financial_year=self.financial_year,
            start_date=date(2025, 1, 1),
            end_date=date(2025, 12, 31),
            created_by=self.budget_manager
        )
        
        # Add budget items
        BudgetService.add_budget_items(self.budget, [
            {
                'account_id': self.salary_account.id,
                'allocated_amount': '25000.00'
            }
        ])
    
    def test_submit_for_approval_success(self):
        """Test successful budget submission for approval"""
        result = BudgetService.submit_for_approval(self.budget, self.budget_manager)
        
        self.assertTrue(result)
        self.budget.refresh_from_db()
        self.assertEqual(self.budget.status, 'pending_approval')
        
        # Check workflow record created
        workflow = BudgetApprovalWorkflow.objects.get(budget=self.budget)
        self.assertEqual(workflow.created_by, self.budget_manager)
        self.assertEqual(workflow.status, 'pending')
    
    def test_submit_empty_budget_for_approval(self):
        """Test submitting budget without items for approval"""
        empty_budget = BudgetService.create_budget(
            school=self.school,
            name="Empty Budget",
            budget_type="monthly",
            financial_year=self.financial_year,
            start_date=date(2025, 1, 1),
            end_date=date(2025, 1, 31),
            created_by=self.budget_manager
        )
        
        with self.assertRaises(ValidationError):
            BudgetService.submit_for_approval(empty_budget, self.budget_manager)
    
    def test_approve_budget_success(self):
        """Test successful budget approval"""
        # Submit for approval first
        BudgetService.submit_for_approval(self.budget, self.budget_manager)
        
        # Approve budget
        result = BudgetService.approve_budget(
            self.budget, 
            self.approver, 
            "Budget approved for implementation"
        )
        
        self.assertTrue(result)
        self.budget.refresh_from_db()
        # Budget should be active since it's within the date range
        self.assertEqual(self.budget.status, 'active')
        self.assertEqual(self.budget.approved_by, self.approver)
        self.assertIsNotNone(self.budget.approved_at)
    
    def test_reject_budget_success(self):
        """Test successful budget rejection"""
        # Submit for approval first
        BudgetService.submit_for_approval(self.budget, self.budget_manager)
        
        # Reject budget
        result = BudgetService.reject_budget(
            self.budget,
            self.approver,
            "Budget needs revision - amounts too high"
        )
        
        self.assertTrue(result)
        self.budget.refresh_from_db()
        self.assertEqual(self.budget.status, 'draft')
        
        # Check workflow record
        workflow = BudgetApprovalWorkflow.objects.get(budget=self.budget)
        self.assertEqual(workflow.status, 'rejected')
        self.assertEqual(workflow.comments, "Budget needs revision - amounts too high")


class BudgetMonitoringTest(BudgetManagementTestCase):
    """Test budget monitoring functionality"""
    
    def setUp(self):
        super().setUp()
        self.budget = BudgetService.create_budget(
            school=self.school,
            name="Test Budget",
            budget_type="annual",
            financial_year=self.financial_year,
            start_date=date(2025, 1, 1),
            end_date=date(2025, 12, 31),
            created_by=self.budget_manager
        )
        
        # Add budget items
        self.budget_items = BudgetService.add_budget_items(self.budget, [
            {
                'account_id': self.salary_account.id,
                'cost_center_id': self.cost_center.id,
                'allocated_amount': '30000.00'
            },
            {
                'account_id': self.utilities_account.id,
                'allocated_amount': '5000.00'
            }
        ])
        
        # Approve budget
        BudgetService.submit_for_approval(self.budget, self.budget_manager)
        BudgetService.approve_budget(self.budget, self.approver)
        
        # Create some transactions to test monitoring
        self._create_test_transactions()
    
    def _create_test_transactions(self):
        """Create test transactions for monitoring"""
        # Create transaction for salary expense
        transaction = Transaction.objects.create(
            school=self.school,
            transaction_id="TXN001",
            transaction_date=date.today(),
            description="Salary payment",
            total_amount=Decimal('15000.00'),
            created_by=self.admin_user
        )
        
        TransactionEntry.objects.create(
            school=self.school,
            transaction=transaction,
            account=self.salary_account,
            cost_center=self.cost_center,
            debit_amount=Decimal('15000.00'),
            credit_amount=Decimal('0.00'),
            entry_date=date.today(),
            is_posted=True,
            created_by=self.admin_user
        )
        
        # Create transaction for utilities
        transaction2 = Transaction.objects.create(
            school=self.school,
            transaction_id="TXN002",
            transaction_date=date.today(),
            description="Utility payment",
            total_amount=Decimal('2000.00'),
            created_by=self.admin_user
        )
        
        TransactionEntry.objects.create(
            school=self.school,
            transaction=transaction2,
            account=self.utilities_account,
            debit_amount=Decimal('2000.00'),
            credit_amount=Decimal('0.00'),
            entry_date=date.today(),
            is_posted=True,
            created_by=self.admin_user
        )
    
    def test_update_budget_actuals(self):
        """Test updating budget actuals from transactions"""
        result = BudgetMonitoringService.update_budget_actuals(self.budget)
        
        self.assertIsNotNone(result)
        self.assertEqual(result['budget_id'], self.budget.id)
        self.assertEqual(result['total_allocated'], Decimal('35000.00'))
        self.assertEqual(result['total_spent'], Decimal('17000.00'))
        self.assertEqual(result['variance'], Decimal('18000.00'))
        
        # Check individual budget items
        salary_item = BudgetItem.objects.get(
            budget=self.budget,
            account=self.salary_account
        )
        salary_item.refresh_from_db()
        self.assertEqual(salary_item.spent_amount, Decimal('15000.00'))
        
        utilities_item = BudgetItem.objects.get(
            budget=self.budget,
            account=self.utilities_account
        )
        utilities_item.refresh_from_db()
        self.assertEqual(utilities_item.spent_amount, Decimal('2000.00'))
    
    def test_generate_variance_report(self):
        """Test variance report generation"""
        # Update actuals first
        BudgetMonitoringService.update_budget_actuals(self.budget)
        
        report = BudgetMonitoringService.generate_variance_report(self.budget)
        
        self.assertIsNotNone(report)
        self.assertIn('budget', report)
        self.assertIn('summary', report)
        self.assertIn('items', report)
        
        # Check summary
        summary = report['summary']
        self.assertEqual(summary['total_allocated'], Decimal('35000.00'))
        self.assertEqual(summary['total_spent'], Decimal('17000.00'))
        self.assertEqual(summary['variance_amount'], Decimal('18000.00'))
        
        # Check items analysis
        items = report['items']
        self.assertEqual(len(items), 2)
        
        # Find salary item
        salary_item = next(item for item in items if item['account_code'] == '5001')
        self.assertEqual(salary_item['allocated_amount'], Decimal('30000.00'))
        self.assertEqual(salary_item['spent_amount'], Decimal('15000.00'))
        self.assertEqual(salary_item['variance_amount'], Decimal('15000.00'))
        self.assertEqual(salary_item['status'], 'on_track')


class BudgetAlertTest(BudgetManagementTestCase):
    """Test budget alert functionality"""
    
    def setUp(self):
        super().setUp()
        self.budget = BudgetService.create_budget(
            school=self.school,
            name="Test Budget",
            budget_type="annual",
            financial_year=self.financial_year,
            start_date=date(2025, 1, 1),
            end_date=date(2025, 12, 31),
            created_by=self.budget_manager
        )
        
        # Add budget items
        BudgetService.add_budget_items(self.budget, [
            {
                'account_id': self.salary_account.id,
                'allocated_amount': '10000.00'
            }
        ])
        
        # Create budget alert
        self.alert = BudgetAlert.objects.create(
            school=self.school,
            budget=self.budget,
            alert_type='utilization_threshold',
            threshold_percentage=Decimal('80.00'),
            notification_method='email',
            recipients='<EMAIL>',
            is_active=True,
            created_by=self.admin_user
        )
    
    def test_budget_alert_creation(self):
        """Test budget alert creation"""
        self.assertIsNotNone(self.alert)
        self.assertEqual(self.alert.budget, self.budget)
        self.assertEqual(self.alert.alert_type, 'utilization_threshold')
        self.assertEqual(self.alert.threshold_percentage, Decimal('80.00'))
        self.assertTrue(self.alert.is_active)


class BudgetReportingTest(BudgetManagementTestCase):
    """Test budget reporting functionality"""
    
    def setUp(self):
        super().setUp()
        # Create multiple budgets for testing
        self.budget1 = BudgetService.create_budget(
            school=self.school,
            name="Annual Budget 2025",
            budget_type="annual",
            financial_year=self.financial_year,
            start_date=date(2025, 1, 1),
            end_date=date(2025, 12, 31),
            created_by=self.budget_manager
        )
        
        self.budget2 = BudgetService.create_budget(
            school=self.school,
            name="Q1 Budget 2025",
            budget_type="quarterly",
            financial_year=self.financial_year,
            start_date=date(2025, 1, 1),
            end_date=date(2025, 3, 31),
            created_by=self.budget_manager
        )
        
        # Add items to budgets
        BudgetService.add_budget_items(self.budget1, [
            {
                'account_id': self.salary_account.id,
                'allocated_amount': '50000.00'
            }
        ])
        
        BudgetService.add_budget_items(self.budget2, [
            {
                'account_id': self.utilities_account.id,
                'allocated_amount': '10000.00'
            }
        ])
        
        # Approve budgets
        for budget in [self.budget1, self.budget2]:
            BudgetService.submit_for_approval(budget, self.budget_manager)
            BudgetService.approve_budget(budget, self.approver)
    
    def test_generate_budget_summary_report(self):
        """Test budget summary report generation"""
        report = BudgetReportingService.generate_budget_summary_report(
            self.school,
            self.financial_year
        )
        
        self.assertIsNotNone(report)
        self.assertEqual(report['total_budgets'], 2)
        self.assertEqual(report['active_budgets'], 1)  # One budget is active
        self.assertEqual(report['total_allocated'], Decimal('60000.00'))
        self.assertEqual(len(report['budgets']), 2)
        
        # Check individual budget data
        budget_data = report['budgets']
        annual_budget = next(b for b in budget_data if b['name'] == 'Annual Budget 2025')
        self.assertEqual(annual_budget['allocated'], Decimal('50000.00'))
        self.assertEqual(annual_budget['type'], 'Annual Budget')
    
    def test_generate_budget_performance_dashboard(self):
        """Test budget performance dashboard generation"""
        dashboard = BudgetReportingService.generate_budget_performance_dashboard(self.school)
        
        self.assertIsNotNone(dashboard)
        self.assertIn('overview', dashboard)
        self.assertIn('budget_performance', dashboard)
        self.assertIn('top_variances', dashboard)
        self.assertIn('alerts_summary', dashboard)
        
        # Check overview
        overview = dashboard['overview']
        self.assertEqual(overview['total_active_budgets'], 1)  # One budget is active
        self.assertEqual(overview['total_allocated'], Decimal('50000.00'))  # Active budget allocation


class BudgetIntegrationTest(TransactionTestCase):
    """Integration tests for budget management system"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Integration Test School",
            code="INT001",
            address="123 Integration St",
            phone="************",
            email="<EMAIL>",
            principal_name="Integration Principal",
            established_date=date(2020, 1, 1)
        )
        
        self.user = User.objects.create_user(
            username='integration_user',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.financial_year = FinancialYear.objects.create(
            school=self.school,
            name="2025",
            start_date=date(2025, 1, 1),
            end_date=date(2025, 12, 31),
            is_current=True,
            created_by=self.user
        )
        
        # Create account type and account
        self.expense_type = AccountType.objects.create(
            school=self.school,
            name="Expense",
            type="expense",
            created_by=self.user
        )
        
        self.test_account = Account.objects.create(
            school=self.school,
            code="5001",
            name="Test Expense",
            account_type=self.expense_type,
            created_by=self.user
        )
    
    def test_complete_budget_lifecycle(self):
        """Test complete budget lifecycle from creation to monitoring"""
        with transaction.atomic():
            # 1. Create budget
            budget = BudgetService.create_budget(
                school=self.school,
                name="Lifecycle Test Budget",
                budget_type="annual",
                financial_year=self.financial_year,
                start_date=date(2025, 1, 1),
                end_date=date(2025, 12, 31),
                created_by=self.user
            )
            
            # 2. Add budget items
            BudgetService.add_budget_items(budget, [
                {
                    'account_id': self.test_account.id,
                    'allocated_amount': '20000.00',
                    'notes': 'Test allocation'
                }
            ])
            
            # 3. Submit for approval
            BudgetService.submit_for_approval(budget, self.user)
            
            # 4. Approve budget
            BudgetService.approve_budget(budget, self.user, "Approved for testing")
            
            # 5. Create transaction to test monitoring
            test_transaction = Transaction.objects.create(
                school=self.school,
                transaction_id="LIFECYCLE001",
                transaction_date=date.today(),
                description="Test transaction",
                total_amount=Decimal('5000.00'),
                created_by=self.user
            )
            
            TransactionEntry.objects.create(
                school=self.school,
                transaction=test_transaction,
                account=self.test_account,
                debit_amount=Decimal('5000.00'),
                credit_amount=Decimal('0.00'),
                entry_date=date.today(),
                is_posted=True,
                created_by=self.user
            )
            
            # 6. Update budget actuals
            result = BudgetMonitoringService.update_budget_actuals(budget)
            
            # 7. Generate variance report
            variance_report = BudgetMonitoringService.generate_variance_report(budget)
            
            # Verify complete lifecycle
            budget.refresh_from_db()
            self.assertEqual(budget.status, 'approved')
            self.assertEqual(result['total_spent'], Decimal('5000.00'))
            self.assertEqual(variance_report['summary']['variance_amount'], Decimal('15000.00'))
            
            # 8. Generate summary report
            summary_report = BudgetReportingService.generate_budget_summary_report(
                self.school,
                self.financial_year
            )
            
            self.assertEqual(summary_report['total_budgets'], 1)
            self.assertEqual(summary_report['total_allocated'], Decimal('20000.00'))


if __name__ == '__main__':
    pytest.main([__file__])