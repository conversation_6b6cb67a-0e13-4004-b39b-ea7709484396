"""
Integration tests for double-entry bookkeeping system
"""
import pytest
from decimal import Decimal
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.core.exceptions import ValidationError

from core.models import School, AcademicYear
from hr.models import Employee, Department, Position

User = get_user_model()
from finance.models import (
    Account, AccountType, Transaction, TransactionEntry, 
    TransactionAuditLog, CostCenter
)
from finance.services import DoubleEntryBookkeepingService, ChartOfAccountsService


class DoubleEntryIntegrationTest(TestCase):
    """Integration tests for complete double-entry bookkeeping workflow"""
    
    def setUp(self):
        """Set up test data"""
        # Create school
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            established_date="2020-01-01"
        )
        
        # Create academic year
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date="2024-09-01",
            end_date="2025-06-30",
            is_current=True
        )
        
        # Create users
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='testpass123',
            first_name='Admin',
            last_name='User'
        )
        
        self.accountant_user = User.objects.create_user(
            username='accountant',
            email='<EMAIL>',
            password='testpass123',
            first_name='Account',
            last_name='User'
        )
        
        # Create departments and positions
        self.admin_dept = Department.objects.create(
            school=self.school,
            name='Administration',
            code='ADMIN',
            created_by=self.admin_user
        )
        
        self.finance_dept = Department.objects.create(
            school=self.school,
            name='Finance',
            code='FIN',
            created_by=self.admin_user
        )
        
        self.admin_position = Position.objects.create(
            school=self.school,
            title='Administrator',
            department=self.admin_dept,
            min_salary=45000,
            max_salary=55000,
            created_by=self.admin_user
        )
        
        self.accountant_position = Position.objects.create(
            school=self.school,
            title='Accountant',
            department=self.finance_dept,
            min_salary=35000,
            max_salary=45000,
            created_by=self.admin_user
        )
        
        # Create employees
        self.admin_employee = Employee.objects.create(
            school=self.school,
            user=self.admin_user,
            employee_id='EMP001',
            position=self.admin_position,
            hire_date='2024-01-01',
            salary=50000,
            emergency_contact_name='Emergency Contact',
            emergency_contact_phone='************',
            emergency_contact_relationship='Spouse',
            created_by=self.admin_user
        )
        
        self.accountant_employee = Employee.objects.create(
            school=self.school,
            user=self.accountant_user,
            employee_id='EMP002',
            position=self.accountant_position,
            hire_date='2024-01-01',
            salary=40000,
            emergency_contact_name='Emergency Contact',
            emergency_contact_phone='************',
            emergency_contact_relationship='Spouse',
            created_by=self.admin_user
        )
        
        # Create default chart of accounts
        ChartOfAccountsService.create_default_accounts(self.school)
        
        # Get some accounts for testing
        self.cash_account = Account.objects.get(school=self.school, code='1110')
        self.bank_account = Account.objects.get(school=self.school, code='1120')
        self.revenue_account = Account.objects.get(school=self.school, code='4100')
        self.expense_account = Account.objects.get(school=self.school, code='5100')
        
        # Create cost center
        self.cost_center = CostCenter.objects.create(
            school=self.school,
            code='CC001',
            name='Administration',
            budget_amount=100000,
            created_by=self.admin_user
        )
        
        # Create client
        self.client = Client()
    
    def test_complete_transaction_workflow(self):
        """Test complete transaction workflow from creation to posting"""
        
        # 1. Create a transaction
        transaction_data = {
            'transaction_date': '2024-01-15',
            'transaction_type': 'manual',
            'description': 'Test transaction for tuition payment',
            'reference': 'REF001',
            'total_amount': Decimal('1000.00'),
            'requires_approval': True
        }
        
        entries_data = [
            {
                'account_id': self.cash_account.id,
                'description': 'Cash received from student',
                'debit_amount': Decimal('1000.00'),
                'credit_amount': Decimal('0.00'),
                'cost_center_id': self.cost_center.id,
                'reference': 'CASH001'
            },
            {
                'account_id': self.revenue_account.id,
                'description': 'Tuition revenue',
                'debit_amount': Decimal('0.00'),
                'credit_amount': Decimal('1000.00'),
                'cost_center_id': None,
                'reference': 'REV001'
            }
        ]
        
        # Create transaction
        transaction = DoubleEntryBookkeepingService.create_transaction(
            self.school,
            self.accountant_user,
            transaction_data,
            entries_data
        )
        
        # Verify transaction creation
        self.assertIsNotNone(transaction)
        self.assertEqual(transaction.status, 'pending_approval')
        self.assertEqual(transaction.total_amount, Decimal('1000.00'))
        self.assertTrue(transaction.is_balanced)
        self.assertEqual(transaction.entries.count(), 2)
        
        # Verify audit log
        audit_logs = TransactionAuditLog.objects.filter(transaction=transaction)
        self.assertEqual(audit_logs.count(), 1)
        self.assertEqual(audit_logs.first().action, 'created')
        
        # 2. Approve the transaction
        DoubleEntryBookkeepingService.approve_transaction(
            transaction,
            self.admin_user,
            'Approved for posting'
        )
        
        # Verify approval
        transaction.refresh_from_db()
        self.assertEqual(transaction.status, 'approved')
        self.assertEqual(transaction.approved_by, self.admin_user)
        self.assertIsNotNone(transaction.approved_at)
        
        # Verify audit log for approval
        approval_log = audit_logs.filter(action='approved').first()
        self.assertIsNotNone(approval_log)
        self.assertEqual(approval_log.user, self.admin_user)
        
        # 3. Post the transaction
        DoubleEntryBookkeepingService.post_transaction(transaction, self.admin_user)
        
        # Verify posting
        transaction.refresh_from_db()
        self.assertEqual(transaction.status, 'posted')
        self.assertEqual(transaction.posted_by, self.admin_user)
        self.assertIsNotNone(transaction.posted_at)
        
        # Verify entries are posted
        for entry in transaction.entries.all():
            self.assertTrue(entry.is_posted)
            self.assertIsNotNone(entry.posted_at)
        
        # Verify account balances are updated
        self.cash_account.refresh_from_db()
        self.revenue_account.refresh_from_db()
        
        # Cash account should have increased by 1000 (debit balance account)
        self.assertEqual(self.cash_account.current_balance, Decimal('1000.00'))
        
        # Revenue account should have increased by 1000 (credit balance account)
        self.assertEqual(self.revenue_account.current_balance, Decimal('1000.00'))
        
        # Verify audit log for posting
        posting_log = audit_logs.filter(action='posted').first()
        self.assertIsNotNone(posting_log)
        self.assertEqual(posting_log.user, self.admin_user)
    
    def test_transaction_validation_errors(self):
        """Test various validation errors in transaction creation"""
        
        # Test unbalanced transaction
        transaction_data = {
            'transaction_date': '2024-01-15',
            'transaction_type': 'manual',
            'description': 'Unbalanced transaction',
            'reference': 'REF002',
            'total_amount': Decimal('1000.00'),
            'requires_approval': False
        }
        
        entries_data = [
            {
                'account_id': self.cash_account.id,
                'description': 'Cash received',
                'debit_amount': Decimal('1000.00'),
                'credit_amount': Decimal('0.00'),
                'cost_center_id': None,
                'reference': ''
            },
            {
                'account_id': self.revenue_account.id,
                'description': 'Revenue',
                'debit_amount': Decimal('0.00'),
                'credit_amount': Decimal('500.00'),  # Unbalanced!
                'cost_center_id': None,
                'reference': ''
            }
        ]
        
        with self.assertRaises(ValidationError) as context:
            DoubleEntryBookkeepingService.create_transaction(
                self.school,
                self.accountant_user,
                transaction_data,
                entries_data
            )
        
        self.assertIn('not balanced', str(context.exception))
    
    def test_header_account_validation(self):
        """Test that header accounts cannot be used in transactions"""
        
        # Get a header account
        header_account = Account.objects.get(school=self.school, code='1000')  # Assets header
        
        transaction_data = {
            'transaction_date': '2024-01-15',
            'transaction_type': 'manual',
            'description': 'Invalid transaction with header account',
            'reference': 'REF003',
            'total_amount': Decimal('1000.00'),
            'requires_approval': False
        }
        
        entries_data = [
            {
                'account_id': header_account.id,  # Header account - should fail
                'description': 'Invalid entry',
                'debit_amount': Decimal('1000.00'),
                'credit_amount': Decimal('0.00'),
                'cost_center_id': None,
                'reference': ''
            },
            {
                'account_id': self.revenue_account.id,
                'description': 'Revenue',
                'debit_amount': Decimal('0.00'),
                'credit_amount': Decimal('1000.00'),
                'cost_center_id': None,
                'reference': ''
            }
        ]
        
        with self.assertRaises(ValidationError) as context:
            DoubleEntryBookkeepingService.create_transaction(
                self.school,
                self.accountant_user,
                transaction_data,
                entries_data
            )
        
        self.assertIn('header accounts', str(context.exception))
    
    def test_account_ledger_generation(self):
        """Test account ledger generation"""
        
        # Create multiple transactions affecting the same account
        for i in range(3):
            transaction_data = {
                'transaction_date': f'2024-01-{15+i}',
                'transaction_type': 'manual',
                'description': f'Test transaction {i+1}',
                'reference': f'REF{i+1:03d}',
                'total_amount': Decimal('500.00'),
                'requires_approval': False
            }
            
            entries_data = [
                {
                    'account_id': self.cash_account.id,
                    'description': f'Cash transaction {i+1}',
                    'debit_amount': Decimal('500.00'),
                    'credit_amount': Decimal('0.00'),
                    'cost_center_id': None,
                    'reference': f'CASH{i+1:03d}'
                },
                {
                    'account_id': self.revenue_account.id,
                    'description': f'Revenue transaction {i+1}',
                    'debit_amount': Decimal('0.00'),
                    'credit_amount': Decimal('500.00'),
                    'cost_center_id': None,
                    'reference': f'REV{i+1:03d}'
                }
            ]
            
            transaction = DoubleEntryBookkeepingService.create_transaction(
                self.school,
                self.accountant_user,
                transaction_data,
                entries_data
            )
            
            # Post the transaction
            DoubleEntryBookkeepingService.post_transaction(transaction, self.admin_user)
        
        # Generate account ledger
        from datetime import date
        ledger_data = DoubleEntryBookkeepingService.get_account_ledger(
            self.cash_account,
            date(2024, 1, 1),
            date(2024, 1, 31)
        )
        
        # Verify ledger data
        self.assertEqual(len(ledger_data['entries']), 3)
        self.assertEqual(ledger_data['closing_balance'], Decimal('1500.00'))
        
        # Calculate totals from entries
        total_debits = sum(entry['debit_amount'] for entry in ledger_data['entries'])
        total_credits = sum(entry['credit_amount'] for entry in ledger_data['entries'])
        self.assertEqual(total_debits, Decimal('1500.00'))
        self.assertEqual(total_credits, Decimal('0.00'))
    
    def test_transaction_cancellation(self):
        """Test transaction cancellation"""
        
        # Create a transaction
        transaction_data = {
            'transaction_date': '2024-01-15',
            'transaction_type': 'manual',
            'description': 'Transaction to be cancelled',
            'reference': 'REF999',
            'total_amount': Decimal('1000.00'),
            'requires_approval': True
        }
        
        entries_data = [
            {
                'account_id': self.cash_account.id,
                'description': 'Cash entry',
                'debit_amount': Decimal('1000.00'),
                'credit_amount': Decimal('0.00'),
                'cost_center_id': None,
                'reference': ''
            },
            {
                'account_id': self.revenue_account.id,
                'description': 'Revenue entry',
                'debit_amount': Decimal('0.00'),
                'credit_amount': Decimal('1000.00'),
                'cost_center_id': None,
                'reference': ''
            }
        ]
        
        transaction = DoubleEntryBookkeepingService.create_transaction(
            self.school,
            self.accountant_user,
            transaction_data,
            entries_data
        )
        
        # Cancel the transaction
        DoubleEntryBookkeepingService.cancel_transaction(
            transaction,
            self.admin_user,
            'Cancelled due to error'
        )
        
        # Verify cancellation
        transaction.refresh_from_db()
        self.assertEqual(transaction.status, 'cancelled')
        
        # Verify audit log
        audit_logs = TransactionAuditLog.objects.filter(
            transaction=transaction,
            action='cancelled'
        )
        self.assertEqual(audit_logs.count(), 1)
        self.assertEqual(audit_logs.first().user, self.admin_user)
    
    def test_cost_center_tracking(self):
        """Test cost center integration with transactions"""
        
        transaction_data = {
            'transaction_date': '2024-01-15',
            'transaction_type': 'manual',
            'description': 'Transaction with cost center',
            'reference': 'CC001',
            'total_amount': Decimal('2000.00'),
            'requires_approval': False
        }
        
        entries_data = [
            {
                'account_id': self.expense_account.id,
                'description': 'Office supplies expense',
                'debit_amount': Decimal('2000.00'),
                'credit_amount': Decimal('0.00'),
                'cost_center_id': self.cost_center.id,
                'reference': 'EXP001'
            },
            {
                'account_id': self.cash_account.id,
                'description': 'Cash payment',
                'debit_amount': Decimal('0.00'),
                'credit_amount': Decimal('2000.00'),
                'cost_center_id': None,
                'reference': 'CASH001'
            }
        ]
        
        transaction = DoubleEntryBookkeepingService.create_transaction(
            self.school,
            self.accountant_user,
            transaction_data,
            entries_data
        )
        
        # Post the transaction
        DoubleEntryBookkeepingService.post_transaction(transaction, self.admin_user)
        
        # Verify cost center tracking
        expense_entry = transaction.entries.filter(account=self.expense_account).first()
        self.assertEqual(expense_entry.cost_center, self.cost_center)
        
        # Verify cost center total expenses
        self.cost_center.refresh_from_db()
        self.assertEqual(self.cost_center.total_expenses, Decimal('2000.00'))
    
    def test_audit_trail_completeness(self):
        """Test that audit trail captures all transaction changes"""
        
        # Create transaction
        transaction_data = {
            'transaction_date': '2024-01-15',
            'transaction_type': 'manual',
            'description': 'Audit trail test',
            'reference': 'AUDIT001',
            'total_amount': Decimal('1000.00'),
            'requires_approval': True
        }
        
        entries_data = [
            {
                'account_id': self.cash_account.id,
                'description': 'Cash entry',
                'debit_amount': Decimal('1000.00'),
                'credit_amount': Decimal('0.00'),
                'cost_center_id': None,
                'reference': ''
            },
            {
                'account_id': self.revenue_account.id,
                'description': 'Revenue entry',
                'debit_amount': Decimal('0.00'),
                'credit_amount': Decimal('1000.00'),
                'cost_center_id': None,
                'reference': ''
            }
        ]
        
        transaction = DoubleEntryBookkeepingService.create_transaction(
            self.school,
            self.accountant_user,
            transaction_data,
            entries_data
        )
        
        # Approve transaction
        DoubleEntryBookkeepingService.approve_transaction(
            transaction,
            self.admin_user,
            'Approved for testing'
        )
        
        # Post transaction
        DoubleEntryBookkeepingService.post_transaction(transaction, self.admin_user)
        
        # Verify complete audit trail
        audit_logs = TransactionAuditLog.objects.filter(transaction=transaction).order_by('timestamp')
        
        expected_actions = ['created', 'approved', 'posted']
        actual_actions = [log.action for log in audit_logs]
        
        self.assertEqual(actual_actions, expected_actions)
        
        # Verify each log has proper user and timestamp
        for log in audit_logs:
            self.assertIsNotNone(log.user)
            self.assertIsNotNone(log.timestamp)
            self.assertEqual(log.school, self.school)
    
    def test_transaction_id_uniqueness(self):
        """Test that transaction IDs are unique and properly generated"""
        
        transaction_ids = set()
        
        # Create multiple transactions
        for i in range(10):
            transaction_data = {
                'transaction_date': '2024-01-15',
                'transaction_type': 'manual',
                'description': f'Uniqueness test {i+1}',
                'reference': f'UNIQUE{i+1:03d}',
                'total_amount': Decimal('100.00'),
                'requires_approval': False
            }
            
            entries_data = [
                {
                    'account_id': self.cash_account.id,
                    'description': 'Cash entry',
                    'debit_amount': Decimal('100.00'),
                    'credit_amount': Decimal('0.00'),
                    'cost_center_id': None,
                    'reference': ''
                },
                {
                    'account_id': self.revenue_account.id,
                    'description': 'Revenue entry',
                    'debit_amount': Decimal('0.00'),
                    'credit_amount': Decimal('100.00'),
                    'cost_center_id': None,
                    'reference': ''
                }
            ]
            
            transaction = DoubleEntryBookkeepingService.create_transaction(
                self.school,
                self.accountant_user,
                transaction_data,
                entries_data
            )
            
            # Verify transaction ID is unique
            self.assertNotIn(transaction.transaction_id, transaction_ids)
            transaction_ids.add(transaction.transaction_id)
            
            # Verify transaction ID format (use current year)
            from datetime import datetime
            current_year = datetime.now().year
            self.assertTrue(transaction.transaction_id.startswith(f'TXN-{current_year}-'))