"""
Tests for Fee Management System
"""
import pytest
from decimal import Decimal
from datetime import date, timedelta
from django.test import TestCase
from django.contrib.auth import get_user_model

User = get_user_model()
from django.core.exceptions import ValidationError

from core.models import School, AcademicYear
from students.models import Student, Grade, Parent, Class
from finance.models import (
    FeeType, GradeFee, StudentFee, Payment, PaymentItem, Invoice, InvoiceItem,
    Account, AccountType
)
from finance.fee_services import (
    FeeCalculationService, InvoiceGenerationService, PaymentTrackingService,
    FeeCollectionReportingService
)


@pytest.mark.django_db
class TestFeeCalculationService:
    """Test fee calculation algorithms"""
    
    @pytest.fixture
    def setup_data(self):
        """Set up test data"""
        # Create school
        school = School.objects.create(
            name="Test School",
            code="TEST",
            address="Test Address",
            phone="**********",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2020, 1, 1)
        )
        
        # Create academic year
        academic_year = AcademicYear.objects.create(
            school=school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        # Create grade
        grade = Grade.objects.create(
            school=school,
            name="Grade 1",
            level=1,
            max_capacity=30
        )
        
        # Create user for student
        user = User.objects.create_user(
            username="student001",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        # Create user for parent
        parent_user = User.objects.create_user(
            username="parent001",
            email="<EMAIL>",
            password="testpass123",
            user_type="parent"
        )
        
        # Create parent first
        parent = Parent.objects.create(
            school=school,
            user=parent_user,
            father_name="John Doe Sr.",
            father_phone="**********",
            home_address="123 Test Street"
        )
        
        # Create class
        class_obj = Class.objects.create(
            school=school,
            name="Section A",
            grade=grade,
            academic_year=academic_year,
            max_students=30
        )
        
        # Create student
        student = Student.objects.create(
            school=school,
            user=user,
            student_id="STU001",
            admission_number="ADM001",
            first_name="John",
            last_name="Doe",
            date_of_birth=date(2015, 1, 1),
            gender="M",
            nationality="US",
            admission_date=date(2024, 9, 1),
            parent=parent,
            current_class=class_obj
        )
        
        # Create account type and account
        account_type = AccountType.objects.create(
            school=school,
            name="Revenue",
            type="revenue"
        )
        
        account = Account.objects.create(
            school=school,
            code="4100",
            name="Tuition Revenue",
            account_type=account_type,
            is_header=False,
            allow_manual_entries=True
        )
        
        # Create fee types
        tuition_fee = FeeType.objects.create(
            school=school,
            name="Tuition Fee",
            account=account,
            is_mandatory=True
        )
        
        activity_fee = FeeType.objects.create(
            school=school,
            name="Activity Fee",
            account=account,
            is_mandatory=False
        )
        
        # Create grade fees
        grade_tuition = GradeFee.objects.create(
            school=school,
            grade=grade,
            fee_type=tuition_fee,
            academic_year=academic_year,
            amount=Decimal('1000.00'),
            due_date=date(2024, 10, 1)
        )
        
        grade_activity = GradeFee.objects.create(
            school=school,
            grade=grade,
            fee_type=activity_fee,
            academic_year=academic_year,
            amount=Decimal('200.00'),
            due_date=date(2024, 10, 15)
        )
        
        return {
            'school': school,
            'academic_year': academic_year,
            'grade': grade,
            'student': student,
            'tuition_fee': tuition_fee,
            'activity_fee': activity_fee,
            'grade_tuition': grade_tuition,
            'grade_activity': grade_activity
        }
    
    def test_calculate_student_fees(self, setup_data):
        """Test student fee calculation"""
        student = setup_data['student']
        academic_year = setup_data['academic_year']
        
        fee_breakdown = FeeCalculationService.calculate_student_fees(student, academic_year)
        
        assert fee_breakdown['student'] == student
        assert fee_breakdown['academic_year'] == academic_year
        assert fee_breakdown['total_amount'] == Decimal('1200.00')
        assert fee_breakdown['mandatory_fees'] > 0
        assert fee_breakdown['optional_fees'] > 0
        assert len(fee_breakdown['fees']) == 2
    
    def test_calculate_student_fees_with_discount(self, setup_data):
        """Test fee calculation with sibling discount"""
        student = setup_data['student']
        academic_year = setup_data['academic_year']
        
        # Create a sibling
        sibling = Student.objects.create(
            school=setup_data['school'],
            student_id="STU002",
            first_name="Jane",
            last_name="Doe",
            date_of_birth=date(2016, 1, 1),
            gender="F",
            nationality="US",
            admission_date=date(2024, 9, 1),
            current_grade=setup_data['grade'],
            status="active"
        )
        
        # Create parent for both students
        parent = Parent.objects.create(
            school=setup_data['school'],
            student=student,
            relationship="father",
            first_name="John Sr.",
            last_name="Doe",
            phone="**********",
            email="<EMAIL>"
        )
        
        Parent.objects.create(
            school=setup_data['school'],
            student=sibling,
            relationship="father",
            first_name="John Sr.",
            last_name="Doe",
            phone="**********",
            email="<EMAIL>"
        )
        
        fee_breakdown = FeeCalculationService.calculate_student_fees(student, academic_year)
        
        # Should have discount applied
        assert fee_breakdown['total_discount'] > 0
        assert fee_breakdown['net_amount'] < fee_breakdown['total_amount']
    
    def test_calculate_grade_fee_summary(self, setup_data):
        """Test grade fee summary calculation"""
        grade = setup_data['grade']
        academic_year = setup_data['academic_year']
        
        # Calculate fees for the student first
        FeeCalculationService.calculate_student_fees(setup_data['student'], academic_year)
        
        summary = FeeCalculationService.calculate_grade_fee_summary(grade, academic_year)
        
        assert summary['grade'] == grade
        assert summary['academic_year'] == academic_year
        assert summary['total_students'] == 1
        assert summary['total_fees_amount'] > 0
        assert len(summary['fee_types']) == 2


@pytest.mark.django_db
class TestInvoiceGenerationService:
    """Test invoice generation system"""
    
    @pytest.fixture
    def setup_data(self):
        """Set up test data"""
        # Reuse setup from fee calculation tests
        from .test_fee_management import TestFeeCalculationService
        test_instance = TestFeeCalculationService()
        return test_instance.setup_data()
    
    def test_generate_student_invoice(self, setup_data):
        """Test single student invoice generation"""
        student = setup_data['student']
        academic_year = setup_data['academic_year']
        
        # First calculate fees
        FeeCalculationService.calculate_student_fees(student, academic_year)
        
        invoice = InvoiceGenerationService.generate_student_invoice(student, None, academic_year)
        
        assert invoice.student == student
        assert invoice.invoice_type == 'fees'
        assert invoice.total_amount > 0
        assert invoice.items.count() > 0
        assert invoice.status == 'draft'
    
    def test_generate_student_invoice_specific_fees(self, setup_data):
        """Test invoice generation for specific fee types"""
        student = setup_data['student']
        academic_year = setup_data['academic_year']
        tuition_fee = setup_data['tuition_fee']
        
        # First calculate fees
        FeeCalculationService.calculate_student_fees(student, academic_year)
        
        invoice = InvoiceGenerationService.generate_student_invoice(
            student, [tuition_fee.id], academic_year
        )
        
        assert invoice.items.count() == 1
        assert invoice.items.first().fee_type == tuition_fee
    
    def test_generate_bulk_invoices(self, setup_data):
        """Test bulk invoice generation"""
        grade = setup_data['grade']
        academic_year = setup_data['academic_year']
        
        # Create additional students
        for i in range(2, 4):
            Student.objects.create(
                school=setup_data['school'],
                student_id=f"STU00{i}",
                first_name=f"Student{i}",
                last_name="Test",
                date_of_birth=date(2015, 1, 1),
                gender="M",
                nationality="US",
                admission_date=date(2024, 9, 1),
                current_grade=grade,
                status="active"
            )
        
        invoices = InvoiceGenerationService.generate_bulk_invoices(grade, academic_year)
        
        assert len(invoices) == 3  # Original student + 2 new ones
        for invoice in invoices:
            assert invoice.invoice_type == 'fees'
            assert invoice.total_amount > 0


@pytest.mark.django_db
class TestPaymentTrackingService:
    """Test payment tracking and receipt management"""
    
    @pytest.fixture
    def setup_data(self):
        """Set up test data with fees"""
        from .test_fee_management import TestFeeCalculationService
        test_instance = TestFeeCalculationService()
        data = test_instance.setup_data()
        
        # Calculate fees to create StudentFee records
        FeeCalculationService.calculate_student_fees(data['student'], data['academic_year'])
        
        return data
    
    def test_process_payment(self, setup_data):
        """Test payment processing"""
        student = setup_data['student']
        
        # Get student fees
        student_fees = StudentFee.objects.filter(student=student)
        assert student_fees.count() > 0
        
        # Create payment allocation
        fee_allocations = [
            {
                'student_fee_id': student_fees.first().id,
                'amount': Decimal('500.00')
            }
        ]
        
        payment = PaymentTrackingService.process_payment(
            student=student,
            amount=Decimal('500.00'),
            payment_method='cash',
            fee_allocations=fee_allocations,
            reference_number='REF001',
            notes='Test payment'
        )
        
        assert payment.student == student
        assert payment.amount == Decimal('500.00')
        assert payment.payment_method == 'cash'
        assert payment.items.count() == 1
        
        # Check if student fee is updated
        student_fee = student_fees.first()
        student_fee.refresh_from_db()
        # Fee should be partially paid if amount < net_amount
    
    def test_get_student_payment_history(self, setup_data):
        """Test student payment history retrieval"""
        student = setup_data['student']
        academic_year = setup_data['academic_year']
        
        # Create a payment first
        student_fees = StudentFee.objects.filter(student=student)
        fee_allocations = [
            {
                'student_fee_id': student_fees.first().id,
                'amount': Decimal('300.00')
            }
        ]
        
        PaymentTrackingService.process_payment(
            student=student,
            amount=Decimal('300.00'),
            payment_method='cash',
            fee_allocations=fee_allocations
        )
        
        history = PaymentTrackingService.get_student_payment_history(student, academic_year)
        
        assert history['student'] == student
        assert history['academic_year'] == academic_year
        assert history['payment_count'] == 1
        assert history['total_paid'] == Decimal('300.00')


@pytest.mark.django_db
class TestFeeCollectionReportingService:
    """Test fee collection reporting"""
    
    @pytest.fixture
    def setup_data(self):
        """Set up test data with payments"""
        from .test_fee_management import TestFeeCalculationService
        test_instance = TestFeeCalculationService()
        data = test_instance.setup_data()
        
        # Calculate fees and make some payments
        FeeCalculationService.calculate_student_fees(data['student'], data['academic_year'])
        
        student_fees = StudentFee.objects.filter(student=data['student'])
        fee_allocations = [
            {
                'student_fee_id': student_fees.first().id,
                'amount': Decimal('600.00')
            }
        ]
        
        PaymentTrackingService.process_payment(
            student=data['student'],
            amount=Decimal('600.00'),
            payment_method='cash',
            fee_allocations=fee_allocations
        )
        
        return data
    
    def test_generate_collection_summary_report(self, setup_data):
        """Test collection summary report generation"""
        school = setup_data['school']
        academic_year = setup_data['academic_year']
        
        report = FeeCollectionReportingService.generate_collection_summary_report(
            school, academic_year
        )
        
        assert report['school'] == school
        assert report['academic_year'] == academic_year
        assert report['summary']['total_fees_amount'] > 0
        assert report['summary']['total_collected'] > 0
        assert report['summary']['collection_rate'] > 0
        assert len(report['by_grade']) > 0
        assert len(report['by_fee_type']) > 0
    
    def test_generate_outstanding_fees_report(self, setup_data):
        """Test outstanding fees report generation"""
        school = setup_data['school']
        grade = setup_data['grade']
        
        report = FeeCollectionReportingService.generate_outstanding_fees_report(school, grade)
        
        assert report['school'] == school
        assert report['grade'] == grade
        assert 'outstanding_fees' in report
        assert 'summary' in report
    
    def test_generate_payment_analytics(self, setup_data):
        """Test payment analytics generation"""
        school = setup_data['school']
        academic_year = setup_data['academic_year']
        
        analytics = FeeCollectionReportingService.generate_payment_analytics(school, academic_year)
        
        assert analytics['academic_year'] == academic_year
        assert analytics['total_payments'] > 0
        assert analytics['total_amount'] > 0
        assert 'monthly_trends' in analytics
        assert 'payment_method_distribution' in analytics


class TestFeeManagementIntegration(TestCase):
    """Integration tests for the complete fee management system"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Integration Test School",
            code="INTTEST",
            address="Test Address",
            phone="**********",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2020, 1, 1)
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            password="testpass123",
            email="<EMAIL>"
        )
        
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        self.grade = Grade.objects.create(
            school=self.school,
            name="Grade 1",
            level=1,
            max_capacity=30
        )
        
        # Create user for student
        student_user = User.objects.create_user(
            username="student_int",
            email="<EMAIL>",
            password="testpass123",
            user_type="student"
        )
        
        # Create user for parent
        parent_user = User.objects.create_user(
            username="parent_int",
            email="<EMAIL>",
            password="testpass123",
            user_type="parent"
        )
        
        # Create parent first
        parent = Parent.objects.create(
            school=self.school,
            user=parent_user,
            father_name="John Integration Sr.",
            father_phone="**********",
            home_address="123 Integration Street"
        )
        
        # Create class
        class_obj = Class.objects.create(
            school=self.school,
            name="Section A",
            grade=self.grade,
            academic_year=self.academic_year,
            max_students=30
        )
        
        self.student = Student.objects.create(
            school=self.school,
            user=student_user,
            student_id="INT001",
            admission_number="INTADM001",
            first_name="Integration",
            last_name="Test",
            date_of_birth=date(2015, 1, 1),
            gender="M",
            nationality="US",
            admission_date=date(2024, 9, 1),
            parent=parent,
            current_class=class_obj
        )
    
    def test_complete_fee_management_workflow(self):
        """Test the complete fee management workflow"""
        # 1. Set up fee structure
        account_type = AccountType.objects.create(
            school=self.school,
            name="Revenue",
            type="revenue"
        )
        
        account = Account.objects.create(
            school=self.school,
            code="4100",
            name="Tuition Revenue",
            account_type=account_type,
            is_header=False,
            allow_manual_entries=True
        )
        
        fee_type = FeeType.objects.create(
            school=self.school,
            name="Tuition Fee",
            account=account,
            is_mandatory=True
        )
        
        grade_fee = GradeFee.objects.create(
            school=self.school,
            grade=self.grade,
            fee_type=fee_type,
            academic_year=self.academic_year,
            amount=Decimal('1000.00'),
            due_date=date(2024, 10, 1)
        )
        
        # 2. Calculate student fees
        fee_breakdown = FeeCalculationService.calculate_student_fees(
            self.student, self.academic_year
        )
        
        self.assertEqual(fee_breakdown['total_amount'], Decimal('1000.00'))
        self.assertEqual(len(fee_breakdown['fees']), 1)
        
        # Note: There might be a discount applied, so net amount could be less
        expected_net_amount = fee_breakdown['net_amount']
        
        # 3. Generate invoice
        invoice = InvoiceGenerationService.generate_student_invoice(
            self.student, None, self.academic_year
        )
        
        self.assertEqual(invoice.total_amount, expected_net_amount)
        self.assertEqual(invoice.items.count(), 1)
        
        # 4. Process payment
        student_fees = StudentFee.objects.filter(student=self.student)
        student_fee = student_fees.first()
        net_amount = student_fee.net_amount
        
        fee_allocations = [
            {
                'student_fee_id': student_fee.id,
                'amount': net_amount
            }
        ]
        
        payment = PaymentTrackingService.process_payment(
            student=self.student,
            amount=net_amount,
            payment_method='cash',
            fee_allocations=fee_allocations,
            received_by_id=self.user.id
        )
        
        self.assertEqual(payment.amount, net_amount)
        
        # 5. Verify fee is marked as paid
        student_fee = student_fees.first()
        student_fee.refresh_from_db()
        self.assertTrue(student_fee.is_paid)
        
        # 6. Generate collection report
        report = FeeCollectionReportingService.generate_collection_summary_report(
            self.school, self.academic_year
        )
        
        self.assertEqual(report['summary']['total_collected'], net_amount)
        self.assertEqual(report['summary']['collection_rate'], Decimal('100.00'))
    
    def test_fee_management_with_discounts(self):
        """Test fee management with discount scenarios"""
        # Set up fee structure
        account_type = AccountType.objects.create(
            school=self.school,
            name="Revenue",
            type="revenue"
        )
        
        account = Account.objects.create(
            school=self.school,
            code="4100",
            name="Tuition Revenue",
            account_type=account_type,
            is_header=False,
            allow_manual_entries=True
        )
        
        fee_type = FeeType.objects.create(
            school=self.school,
            name="Tuition Fee",
            account=account,
            is_mandatory=True
        )
        
        grade_fee = GradeFee.objects.create(
            school=self.school,
            grade=self.grade,
            fee_type=fee_type,
            academic_year=self.academic_year,
            amount=Decimal('1000.00'),
            due_date=date(2024, 10, 1)
        )
        
        # Create sibling for discount
        sibling = Student.objects.create(
            school=self.school,
            student_id="INT002",
            first_name="Sibling",
            last_name="Test",
            date_of_birth=date(2016, 1, 1),
            gender="F",
            nationality="US",
            admission_date=date(2024, 9, 1),
            current_grade=self.grade,
            status="active"
        )
        
        # Create parent for both students
        parent = Parent.objects.create(
            school=self.school,
            student=self.student,
            relationship="father",
            first_name="Parent",
            last_name="Test",
            phone="**********",
            email="<EMAIL>"
        )
        
        Parent.objects.create(
            school=self.school,
            student=sibling,
            relationship="father",
            first_name="Parent",
            last_name="Test",
            phone="**********",
            email="<EMAIL>"
        )
        
        # Calculate fees with discount
        fee_breakdown = FeeCalculationService.calculate_student_fees(
            self.student, self.academic_year
        )
        
        # Should have discount applied
        self.assertGreater(fee_breakdown['total_discount'], Decimal('0'))
        self.assertLess(fee_breakdown['net_amount'], fee_breakdown['total_amount'])
        
        # Process payment for net amount
        student_fees = StudentFee.objects.filter(student=self.student)
        student_fee = student_fees.first()
        
        fee_allocations = [
            {
                'student_fee_id': student_fee.id,
                'amount': student_fee.net_amount
            }
        ]
        
        payment = PaymentTrackingService.process_payment(
            student=self.student,
            amount=student_fee.net_amount,
            payment_method='cash',
            fee_allocations=fee_allocations,
            received_by_id=self.user.id
        )
        
        # Verify payment processed correctly
        self.assertEqual(payment.amount, student_fee.net_amount)
        
        student_fee.refresh_from_db()
        self.assertTrue(student_fee.is_paid)