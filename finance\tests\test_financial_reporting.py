"""
Tests for Financial Reporting Services
"""

import pytest
from decimal import Decimal
from datetime import date, timedelta
from django.test import TestCase
from django.contrib.auth.models import User
from django.urls import reverse

from core.models import School, AcademicYear
from students.models import Student, Grade
from finance.models import (
    Account, AccountType, Transaction, TransactionEntry, Budget, BudgetItem,
    FinancialYear, CostCenter, Payment, StudentFee, FeeType, GradeFee
)
from finance.reporting_services import FinancialReportingService


class FinancialReportingServiceTest(TestCase):
    """Test cases for FinancialReportingService"""
    
    def setUp(self):
        """Set up test data"""
        # Create school
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="123 Test St",
            phone="************",
            email="<EMAIL>"
        )
        
        # Create user
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
        
        # Create academic year
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 8, 31),
            is_current=True
        )
        
        # Create financial year
        self.financial_year = FinancialYear.objects.create(
            school=self.school,
            name="FY 2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 8, 31),
            is_current=True,
            created_by=self.user
        )
        
        # Create account types
        self.asset_type = AccountType.objects.create(
            school=self.school,
            name="Assets",
            name_ar="الأصول",
            type="asset",
            created_by=self.user
        )
        
        self.liability_type = AccountType.objects.create(
            school=self.school,
            name="Liabilities",
            name_ar="الخصوم",
            type="liability",
            created_by=self.user
        )
        
        self.equity_type = AccountType.objects.create(
            school=self.school,
            name="Equity",
            name_ar="حقوق الملكية",
            type="equity",
            created_by=self.user
        )
        
        self.revenue_type = AccountType.objects.create(
            school=self.school,
            name="Revenue",
            name_ar="الإيرادات",
            type="revenue",
            created_by=self.user
        )
        
        self.expense_type = AccountType.objects.create(
            school=self.school,
            name="Expenses",
            name_ar="المصروفات",
            type="expense",
            created_by=self.user
        )
        
        # Create accounts
        self.cash_account = Account.objects.create(
            school=self.school,
            code="1110",
            name="Cash",
            name_ar="النقدية",
            account_type=self.asset_type,
            opening_balance=Decimal('10000.00'),
            created_by=self.user
        )
        
        self.bank_account = Account.objects.create(
            school=self.school,
            code="1120",
            name="Bank Account",
            name_ar="الحساب البنكي",
            account_type=self.asset_type,
            opening_balance=Decimal('50000.00'),
            created_by=self.user
        )
        
        self.accounts_payable = Account.objects.create(
            school=self.school,
            code="2110",
            name="Accounts Payable",
            name_ar="الذمم الدائنة",
            account_type=self.liability_type,
            opening_balance=Decimal('5000.00'),
            created_by=self.user
        )
        
        self.capital_account = Account.objects.create(
            school=self.school,
            code="3100",
            name="Capital",
            name_ar="رأس المال",
            account_type=self.equity_type,
            opening_balance=Decimal('55000.00'),
            created_by=self.user
        )
        
        self.tuition_revenue = Account.objects.create(
            school=self.school,
            code="4100",
            name="Tuition Revenue",
            name_ar="إيرادات الرسوم الدراسية",
            account_type=self.revenue_type,
            created_by=self.user
        )
        
        self.salary_expense = Account.objects.create(
            school=self.school,
            code="5100",
            name="Salaries Expense",
            name_ar="مصروف الرواتب",
            account_type=self.expense_type,
            created_by=self.user
        )
        
        # Create cost center
        self.cost_center = CostCenter.objects.create(
            school=self.school,
            code="CC001",
            name="Administration",
            name_ar="الإدارة",
            created_by=self.user
        )
        
        # Create reporting service
        self.reporting_service = FinancialReportingService(self.school)
        
        # Create some transactions for testing
        self.create_test_transactions()
    
    def create_test_transactions(self):
        """Create test transactions"""
        # Revenue transaction
        revenue_transaction = Transaction.objects.create(
            school=self.school,
            transaction_date=date.today(),
            transaction_type='manual',
            description='Student fee payment',
            total_amount=Decimal('5000.00'),
            status='posted',
            created_by=self.user,
            posted_by=self.user,
            posted_at=date.today()
        )
        
        # Debit cash, credit revenue
        TransactionEntry.objects.create(
            transaction=revenue_transaction,
            account=self.cash_account,
            entry_date=date.today(),
            description='Student fee payment',
            debit_amount=Decimal('5000.00'),
            credit_amount=Decimal('0.00'),
            is_posted=True,
            school=self.school,
            created_by=self.user
        )
        
        TransactionEntry.objects.create(
            transaction=revenue_transaction,
            account=self.tuition_revenue,
            entry_date=date.today(),
            description='Student fee payment',
            debit_amount=Decimal('0.00'),
            credit_amount=Decimal('5000.00'),
            is_posted=True,
            school=self.school,
            created_by=self.user
        )
        
        # Expense transaction
        expense_transaction = Transaction.objects.create(
            school=self.school,
            transaction_date=date.today(),
            transaction_type='manual',
            description='Salary payment',
            total_amount=Decimal('3000.00'),
            status='posted',
            created_by=self.user,
            posted_by=self.user,
            posted_at=date.today()
        )
        
        # Debit expense, credit cash
        TransactionEntry.objects.create(
            transaction=expense_transaction,
            account=self.salary_expense,
            entry_date=date.today(),
            description='Salary payment',
            debit_amount=Decimal('3000.00'),
            credit_amount=Decimal('0.00'),
            is_posted=True,
            school=self.school,
            created_by=self.user
        )
        
        TransactionEntry.objects.create(
            transaction=expense_transaction,
            account=self.cash_account,
            entry_date=date.today(),
            description='Salary payment',
            debit_amount=Decimal('0.00'),
            credit_amount=Decimal('3000.00'),
            is_posted=True,
            school=self.school,
            created_by=self.user
        )
    
    def test_generate_balance_sheet(self):
        """Test balance sheet generation"""
        balance_sheet = self.reporting_service.generate_balance_sheet()
        
        # Check structure
        self.assertIn('title', balance_sheet)
        self.assertIn('school', balance_sheet)
        self.assertIn('as_of_date', balance_sheet)
        self.assertIn('assets', balance_sheet)
        self.assertIn('liabilities', balance_sheet)
        self.assertIn('equity', balance_sheet)
        
        # Check that we have assets
        self.assertGreater(balance_sheet['assets']['total_assets'], 0)
        
        # Check balance
        self.assertTrue(balance_sheet['is_balanced'])
    
    def test_generate_profit_loss_statement(self):
        """Test P&L statement generation"""
        start_date = date.today() - timedelta(days=30)
        end_date = date.today()
        
        pl_statement = self.reporting_service.generate_profit_loss_statement(
            start_date=start_date,
            end_date=end_date
        )
        
        # Check structure
        self.assertIn('title', pl_statement)
        self.assertIn('school', pl_statement)
        self.assertIn('period', pl_statement)
        self.assertIn('revenue', pl_statement)
        self.assertIn('expenses', pl_statement)
        self.assertIn('net_income', pl_statement)
        
        # Check that we have revenue and expenses
        self.assertGreater(pl_statement['revenue']['total_revenue'], 0)
        self.assertGreater(pl_statement['expenses']['total_expenses'], 0)
        
        # Check net income calculation
        expected_net_income = (
            pl_statement['revenue']['total_revenue'] - 
            pl_statement['expenses']['total_expenses']
        )
        self.assertEqual(pl_statement['net_income'], expected_net_income)
    
    def test_generate_cash_flow_statement(self):
        """Test cash flow statement generation"""
        start_date = date.today() - timedelta(days=30)
        end_date = date.today()
        
        cash_flow = self.reporting_service.generate_cash_flow_statement(
            start_date=start_date,
            end_date=end_date
        )
        
        # Check structure
        self.assertIn('title', cash_flow)
        self.assertIn('school', cash_flow)
        self.assertIn('period', cash_flow)
        self.assertIn('operating_activities', cash_flow)
        self.assertIn('investing_activities', cash_flow)
        self.assertIn('financing_activities', cash_flow)
        self.assertIn('beginning_cash', cash_flow)
        self.assertIn('ending_cash', cash_flow)
        self.assertIn('net_change_in_cash', cash_flow)
    
    def test_generate_budget_vs_actual_report(self):
        """Test budget vs actual report generation"""
        # Create a budget
        budget = Budget.objects.create(
            school=self.school,
            name="Test Budget",
            budget_type="annual",
            financial_year=self.financial_year,
            start_date=date.today() - timedelta(days=30),
            end_date=date.today() + timedelta(days=30),
            status="active",
            created_by=self.user
        )
        
        # Create budget items
        BudgetItem.objects.create(
            budget=budget,
            account=self.tuition_revenue,
            allocated_amount=Decimal('10000.00'),
            school=self.school,
            created_by=self.user
        )
        
        BudgetItem.objects.create(
            budget=budget,
            account=self.salary_expense,
            allocated_amount=Decimal('8000.00'),
            school=self.school,
            created_by=self.user
        )
        
        # Generate report
        budget_report = self.reporting_service.generate_budget_vs_actual_report(
            budget_id=budget.id
        )
        
        # Check structure
        self.assertIn('title', budget_report)
        self.assertIn('school', budget_report)
        self.assertIn('budget', budget_report)
        self.assertIn('summary', budget_report)
        self.assertIn('items', budget_report)
        self.assertIn('variance_analysis', budget_report)
        
        # Check summary
        self.assertGreater(budget_report['summary']['total_budget'], 0)
        self.assertGreaterEqual(budget_report['summary']['total_actual'], 0)
    
    def test_generate_financial_dashboard_data(self):
        """Test financial dashboard data generation"""
        dashboard_data = self.reporting_service.generate_financial_dashboard_data()
        
        # Check structure
        self.assertIn('title', dashboard_data)
        self.assertIn('school', dashboard_data)
        self.assertIn('period', dashboard_data)
        self.assertIn('key_metrics', dashboard_data)
        self.assertIn('revenue_analysis', dashboard_data)
        self.assertIn('expense_analysis', dashboard_data)
        self.assertIn('cash_position', dashboard_data)
        self.assertIn('budget_performance', dashboard_data)
        self.assertIn('trends', dashboard_data)
        
        # Check key metrics
        key_metrics = dashboard_data['key_metrics']
        self.assertIn('total_revenue', key_metrics)
        self.assertIn('total_expenses', key_metrics)
        self.assertIn('net_income', key_metrics)
        self.assertIn('outstanding_fees', key_metrics)
        self.assertIn('collection_rate', key_metrics)
        self.assertIn('profit_margin', key_metrics)


class FinancialReportingViewsTest(TestCase):
    """Test cases for Financial Reporting Views"""
    
    def setUp(self):
        """Set up test data"""
        # Create school
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="123 Test St",
            phone="************",
            email="<EMAIL>"
        )
        
        # Create user with employee profile
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
        
        # Create employee profile (assuming HR app structure)
        from hr.models import Employee
        self.employee = Employee.objects.create(
            school=self.school,
            employee_id="EMP001",
            first_name="Test",
            last_name="User",
            email="<EMAIL>",
            phone="************",
            hire_date=date.today(),
            department="Finance",
            position="Accountant",
            salary=Decimal('5000.00'),
            user=self.user,
            created_by=self.user
        )
        
        # Create basic account structure
        self.create_basic_accounts()
    
    def create_basic_accounts(self):
        """Create basic account structure for testing"""
        # Create account types
        asset_type = AccountType.objects.create(
            school=self.school,
            name="Assets",
            type="asset",
            created_by=self.user
        )
        
        # Create a cash account
        Account.objects.create(
            school=self.school,
            code="1110",
            name="Cash",
            account_type=asset_type,
            opening_balance=Decimal('10000.00'),
            created_by=self.user
        )
    
    def test_financial_reports_dashboard_view(self):
        """Test financial reports dashboard view"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('finance:reports')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Financial Dashboard')
    
    def test_balance_sheet_view(self):
        """Test balance sheet view"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('finance:balance_sheet_report')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Balance Sheet')
    
    def test_balance_sheet_view_with_date(self):
        """Test balance sheet view with specific date"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('finance:balance_sheet_report')
        response = self.client.get(url, {'as_of_date': '2024-12-31'})
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Balance Sheet')
    
    def test_profit_loss_view(self):
        """Test profit & loss view"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('finance:profit_loss_report')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Profit & Loss')
    
    def test_profit_loss_view_with_dates(self):
        """Test profit & loss view with date range"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('finance:profit_loss_report')
        response = self.client.get(url, {
            'start_date': '2024-01-01',
            'end_date': '2024-12-31'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Profit & Loss')
    
    def test_cash_flow_view(self):
        """Test cash flow view"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('finance:cash_flow_report')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Cash Flow')
    
    def test_budget_vs_actual_view_no_budget(self):
        """Test budget vs actual view without budget selection"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('finance:budget_vs_actual_report')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Budget vs Actual')
        self.assertContains(response, 'Please select a budget')
    
    def test_budget_vs_actual_view_with_budget(self):
        """Test budget vs actual view with budget"""
        # Create financial year
        financial_year = FinancialYear.objects.create(
            school=self.school,
            name="FY 2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 8, 31),
            is_current=True,
            created_by=self.user
        )
        
        # Create budget
        budget = Budget.objects.create(
            school=self.school,
            name="Test Budget",
            budget_type="annual",
            financial_year=financial_year,
            start_date=date.today() - timedelta(days=30),
            end_date=date.today() + timedelta(days=30),
            status="active",
            created_by=self.user
        )
        
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('finance:budget_vs_actual_report')
        response = self.client.get(url, {'budget_id': budget.id})
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Budget vs Actual')
        self.assertContains(response, budget.name)
    
    def test_unauthorized_access(self):
        """Test unauthorized access to financial reports"""
        url = reverse('finance:reports')
        response = self.client.get(url)
        
        # Should redirect to login
        self.assertEqual(response.status_code, 302)
        self.assertIn('/accounts/login/', response.url)


class BudgetModelTest(TestCase):
    """Test cases for Budget model"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="123 Test St",
            phone="************",
            email="<EMAIL>"
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
        
        self.financial_year = FinancialYear.objects.create(
            school=self.school,
            name="FY 2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 8, 31),
            is_current=True,
            created_by=self.user
        )
    
    def test_budget_creation(self):
        """Test budget creation"""
        budget = Budget.objects.create(
            school=self.school,
            name="Test Budget",
            budget_type="annual",
            financial_year=self.financial_year,
            start_date=date.today(),
            end_date=date.today() + timedelta(days=365),
            status="draft",
            created_by=self.user
        )
        
        self.assertEqual(budget.name, "Test Budget")
        self.assertEqual(budget.budget_type, "annual")
        self.assertEqual(budget.status, "draft")
        self.assertFalse(budget.is_active)
    
    def test_budget_is_active_property(self):
        """Test budget is_active property"""
        # Create active budget
        active_budget = Budget.objects.create(
            school=self.school,
            name="Active Budget",
            budget_type="annual",
            financial_year=self.financial_year,
            start_date=date.today() - timedelta(days=10),
            end_date=date.today() + timedelta(days=10),
            status="active",
            created_by=self.user
        )
        
        # Create inactive budget (future)
        future_budget = Budget.objects.create(
            school=self.school,
            name="Future Budget",
            budget_type="annual",
            financial_year=self.financial_year,
            start_date=date.today() + timedelta(days=10),
            end_date=date.today() + timedelta(days=20),
            status="active",
            created_by=self.user
        )
        
        self.assertTrue(active_budget.is_active)
        self.assertFalse(future_budget.is_active)
    
    def test_budget_calculations(self):
        """Test budget calculation methods"""
        budget = Budget.objects.create(
            school=self.school,
            name="Test Budget",
            budget_type="annual",
            financial_year=self.financial_year,
            start_date=date.today(),
            end_date=date.today() + timedelta(days=365),
            status="draft",
            created_by=self.user
        )
        
        # Create account type and account
        account_type = AccountType.objects.create(
            school=self.school,
            name="Expenses",
            type="expense",
            created_by=self.user
        )
        
        account = Account.objects.create(
            school=self.school,
            code="5100",
            name="Test Expense",
            account_type=account_type,
            created_by=self.user
        )
        
        # Create budget item
        budget_item = BudgetItem.objects.create(
            budget=budget,
            account=account,
            allocated_amount=Decimal('1000.00'),
            spent_amount=Decimal('750.00'),
            school=self.school,
            created_by=self.user
        )
        
        # Test calculations
        self.assertEqual(budget.get_total_allocated(), Decimal('1000.00'))
        self.assertEqual(budget.get_total_spent(), Decimal('750.00'))
        self.assertEqual(budget.get_variance(), Decimal('250.00'))
        self.assertEqual(budget.get_utilization_percentage(), Decimal('75.00'))


if __name__ == '__main__':
    pytest.main([__file__])