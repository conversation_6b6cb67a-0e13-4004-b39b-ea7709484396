import pytest
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from decimal import Decimal
from datetime import datetime, timedelta

from core.models import School, AcademicYear
from students.models import Student, Grade, Parent
from finance.models import (
    PaymentGateway, PaymentTransaction, PaymentRefund, 
    PaymentReminder, PaymentAnalytics, Payment, StudentFee,
    FeeType, GradeFee, Account, AccountType
)
from finance.payment_services import (
    PaymentGatewayService, PaymentRefundService, 
    PaymentReminderService, PaymentAnalyticsService
)

User = get_user_model()


class PaymentProcessingTestCase(TestCase):
    """Base test case for payment processing tests"""
    
    def setUp(self):
        # Create test school
        self.school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="Test Address",
            established_date=datetime(2020, 1, 1).date()
        )
        
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create academic year
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date=datetime(2024, 9, 1).date(),
            end_date=datetime(2025, 6, 30).date(),
            is_current=True
        )
        
        # Create grade
        self.grade = Grade.objects.create(
            school=self.school,
            name="Grade 1",
            level=1
        )
        
        # Create user for parent
        self.parent_user = User.objects.create_user(
            username='parent001',
            email='<EMAIL>',
            password='testpass123',
            user_type='parent'
        )
        
        # Create parent
        self.parent = Parent.objects.create(
            school=self.school,
            user=self.parent_user,
            father_name="John Doe Sr",
            father_phone="1234567890"
        )
        
        # Create user for student
        self.student_user = User.objects.create_user(
            username='student001',
            email='<EMAIL>',
            password='testpass123',
            user_type='student'
        )
        
        # Create student
        self.student = Student.objects.create(
            school=self.school,
            user=self.student_user,
            parent=self.parent,
            student_id="STU001",
            admission_number="ADM001",
            first_name="John",
            last_name="Doe",
            date_of_birth=datetime(2010, 1, 1).date(),
            admission_date=datetime(2024, 9, 1).date(),
            gender='M',
            nationality="Saudi"
        )
        
        # Create account type
        self.account_type = AccountType.objects.create(
            school=self.school,
            name="Revenue",
            type="revenue"
        )
        
        # Create account
        self.account = Account.objects.create(
            school=self.school,
            code="4000",
            name="Tuition Revenue",
            account_type=self.account_type
        )
        
        # Create fee type
        self.fee_type = FeeType.objects.create(
            school=self.school,
            name="Tuition Fee",
            account=self.account
        )
        
        # Create grade fee
        self.grade_fee = GradeFee.objects.create(
            school=self.school,
            grade=self.grade,
            fee_type=self.fee_type,
            academic_year=self.academic_year,
            amount=Decimal('1000.00'),
            due_date=timezone.now().date() + timedelta(days=30)
        )
        
        # Create student fee
        self.student_fee = StudentFee.objects.create(
            school=self.school,
            student=self.student,
            grade_fee=self.grade_fee,
            amount=Decimal('1000.00'),
            due_date=timezone.now().date() + timedelta(days=30)
        )
        
        # Create payment gateway
        self.gateway = PaymentGateway.objects.create(
            school=self.school,
            name="Test Gateway",
            gateway_type="stripe",
            api_key="test_api_key",
            secret_key="test_secret_key",
            is_sandbox=True,
            is_enabled=True,
            transaction_fee_percentage=Decimal('2.5'),
            transaction_fee_fixed=Decimal('0.30')
        )
        
        # Create payment
        self.payment = Payment.objects.create(
            school=self.school,
            student=self.student,
            receipt_number="REC001",
            amount=Decimal('1000.00'),
            payment_date=timezone.now().date(),
            payment_method='credit_card',
            received_by=self.user
        )
        
        self.client = Client()


class PaymentGatewayServiceTest(PaymentProcessingTestCase):
    """Test payment gateway service functionality"""
    
    def test_get_available_gateways(self):
        """Test getting available payment gateways"""
        gateways = PaymentGatewayService.get_available_gateways(self.school)
        self.assertEqual(gateways.count(), 1)
        self.assertEqual(gateways.first(), self.gateway)
    
    def test_process_stripe_payment(self):
        """Test processing Stripe payment"""
        result = PaymentGatewayService.process_payment(
            self.payment, self.gateway
        )
        
        self.assertTrue(result['success'])
        self.assertIn('transaction', result)
        
        transaction = result['transaction']
        self.assertEqual(transaction.status, 'completed')
        self.assertEqual(transaction.amount, self.payment.amount)
        self.assertIsNotNone(transaction.gateway_transaction_id)
    
    def test_process_bank_transfer_payment(self):
        """Test processing bank transfer payment"""
        # Create bank transfer gateway
        bank_gateway = PaymentGateway.objects.create(
            school=self.school,
            name="Bank Transfer",
            gateway_type="bank_transfer",
            is_enabled=True
        )
        
        result = PaymentGatewayService.process_payment(
            self.payment, bank_gateway,
            reference_number="BT123456"
        )
        
        self.assertTrue(result['success'])
        self.assertTrue(result.get('requires_verification', False))
        
        transaction = result['transaction']
        self.assertEqual(transaction.status, 'pending')
    
    def test_calculate_gateway_fees(self):
        """Test gateway fee calculation"""
        amount = Decimal('100.00')
        expected_fee = (amount * Decimal('2.5') / 100) + Decimal('0.30')
        
        calculated_fee = self.gateway.calculate_fees(amount)
        self.assertEqual(calculated_fee, expected_fee)


class PaymentRefundServiceTest(PaymentProcessingTestCase):
    """Test payment refund service functionality"""
    
    def setUp(self):
        super().setUp()
        # Create payment transaction
        self.payment_transaction = PaymentTransaction.objects.create(
            school=self.school,
            payment=self.payment,
            gateway=self.gateway,
            amount=self.payment.amount,
            status='completed',
            processed_at=timezone.now()
        )
    
    def test_create_refund(self):
        """Test creating a refund request"""
        refund_amount = Decimal('500.00')
        reason = "Customer requested refund"
        
        refund = PaymentRefundService.create_refund(
            payment_transaction=self.payment_transaction,
            amount=refund_amount,
            reason=reason,
            requested_by=self.user,
            refund_type='partial'
        )
        
        self.assertEqual(refund.amount, refund_amount)
        self.assertEqual(refund.reason, reason)
        self.assertEqual(refund.requested_by, self.user)
        self.assertEqual(refund.refund_type, 'partial')
        self.assertEqual(refund.status, 'pending')
    
    def test_create_refund_exceeds_amount(self):
        """Test creating refund that exceeds available balance"""
        refund_amount = Decimal('1500.00')  # More than payment amount
        
        with self.assertRaises(Exception):
            PaymentRefundService.create_refund(
                payment_transaction=self.payment_transaction,
                amount=refund_amount,
                reason="Test refund",
                requested_by=self.user
            )
    
    def test_process_refund(self):
        """Test processing a refund"""
        # Create refund
        refund = PaymentRefund.objects.create(
            school=self.school,
            payment_transaction=self.payment_transaction,
            refund_type='partial',
            amount=Decimal('500.00'),
            reason="Test refund",
            requested_by=self.user
        )
        
        result = PaymentRefundService.process_refund(refund, self.user)
        
        self.assertTrue(result['success'])
        
        refund.refresh_from_db()
        self.assertEqual(refund.status, 'completed')
        self.assertEqual(refund.approved_by, self.user)
        self.assertIsNotNone(refund.processed_at)


class PaymentReminderServiceTest(PaymentProcessingTestCase):
    """Test payment reminder service functionality"""
    
    def test_create_email_reminder(self):
        """Test creating email reminder"""
        scheduled_date = timezone.now() + timedelta(days=1)
        message_template = "Dear {student_name}, your payment of {amount} is due on {due_date}"
        
        reminder = PaymentReminderService.create_reminder(
            student_fee=self.student_fee,
            reminder_type='email',
            scheduled_date=scheduled_date,
            message_template=message_template
        )
        
        self.assertEqual(reminder.student_fee, self.student_fee)
        self.assertEqual(reminder.reminder_type, 'email')
        self.assertEqual(reminder.scheduled_date, scheduled_date)
        self.assertEqual(reminder.message_template, message_template)
        self.assertEqual(reminder.recipient, self.student.email)
        self.assertEqual(reminder.status, 'scheduled')
    
    def test_create_sms_reminder(self):
        """Test creating SMS reminder"""
        scheduled_date = timezone.now() + timedelta(hours=2)
        message_template = "Payment reminder: {amount} due {due_date}"
        
        reminder = PaymentReminderService.create_reminder(
            student_fee=self.student_fee,
            reminder_type='sms',
            scheduled_date=scheduled_date,
            message_template=message_template
        )
        
        self.assertEqual(reminder.reminder_type, 'sms')
        self.assertEqual(reminder.recipient, self.student.phone)
    
    def test_send_reminder(self):
        """Test sending a reminder"""
        reminder = PaymentReminder.objects.create(
            school=self.school,
            student_fee=self.student_fee,
            reminder_type='email',
            scheduled_date=timezone.now() + timedelta(hours=1),
            message_template="Test reminder",
            recipient=self.student.email
        )
        
        success = PaymentReminderService.send_reminder(reminder)
        
        self.assertTrue(success)
        
        reminder.refresh_from_db()
        self.assertEqual(reminder.status, 'sent')
        self.assertEqual(reminder.attempts, 1)
        self.assertIsNotNone(reminder.sent_date)


class PaymentAnalyticsServiceTest(PaymentProcessingTestCase):
    """Test payment analytics service functionality"""
    
    def setUp(self):
        super().setUp()
        # Create additional test data
        self.payment_transaction = PaymentTransaction.objects.create(
            school=self.school,
            payment=self.payment,
            gateway=self.gateway,
            amount=self.payment.amount,
            status='completed',
            processed_at=timezone.now()
        )
    
    def test_generate_daily_analytics(self):
        """Test generating daily analytics"""
        date = timezone.now().date()
        
        analytics = PaymentAnalyticsService.generate_daily_analytics(
            self.school, date
        )
        
        self.assertEqual(analytics.school, self.school)
        self.assertEqual(analytics.date, date)
        self.assertEqual(analytics.total_payments, 1)
        self.assertEqual(analytics.total_amount, self.payment.amount)
        self.assertEqual(analytics.successful_payments, 1)
        self.assertEqual(analytics.failed_payments, 0)
    
    def test_get_payment_trends(self):
        """Test getting payment trends"""
        start_date = timezone.now().date() - timedelta(days=7)
        end_date = timezone.now().date()
        
        # Generate analytics for today
        PaymentAnalyticsService.generate_daily_analytics(
            self.school, timezone.now().date()
        )
        
        trends = PaymentAnalyticsService.get_payment_trends(
            self.school, start_date, end_date
        )
        
        self.assertIn('dates', trends)
        self.assertIn('total_amounts', trends)
        self.assertIn('successful_payments', trends)
        self.assertIn('failed_payments', trends)
        self.assertIn('net_revenue', trends)
    
    def test_get_gateway_performance(self):
        """Test getting gateway performance metrics"""
        start_date = timezone.now().date() - timedelta(days=7)
        end_date = timezone.now().date()
        
        performance = PaymentAnalyticsService.get_gateway_performance(
            self.school, start_date, end_date
        )
        
        self.assertIn(self.gateway.name, performance)
        
        gateway_data = performance[self.gateway.name]
        self.assertEqual(gateway_data['total_transactions'], 1)
        self.assertEqual(gateway_data['successful_transactions'], 1)
        self.assertEqual(gateway_data['failed_transactions'], 0)
        self.assertEqual(gateway_data['success_rate'], 100.0)


class PaymentProcessingViewsTest(PaymentProcessingTestCase):
    """Test payment processing views"""
    
    def setUp(self):
        super().setUp()
        self.client.force_login(self.user)
    
    def test_payment_dashboard_view(self):
        """Test payment dashboard view"""
        url = reverse('finance:payment_dashboard')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Payment Processing Dashboard')
    
    def test_payment_gateway_list_view(self):
        """Test payment gateway list view"""
        url = reverse('finance:payment_gateways')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.gateway.name)
    
    def test_payment_gateway_create_view(self):
        """Test payment gateway create view"""
        url = reverse('finance:add_payment_gateway')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        
        # Test POST
        data = {
            'name': 'New Gateway',
            'gateway_type': 'paypal',
            'api_key': 'test_key',
            'secret_key': 'test_secret',
            'is_sandbox': True,
            'is_enabled': True,
            'transaction_fee_percentage': '3.0',
            'transaction_fee_fixed': '0.50'
        }
        
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 302)  # Redirect after success
        
        # Verify gateway was created
        self.assertTrue(
            PaymentGateway.objects.filter(name='New Gateway').exists()
        )


@pytest.mark.django_db
class PaymentProcessingIntegrationTest:
    """Integration tests for payment processing system"""
    
    def test_complete_payment_flow(self):
        """Test complete payment processing flow"""
        # This would test the entire flow from payment creation
        # through gateway processing to completion
        pass
    
    def test_refund_flow(self):
        """Test complete refund processing flow"""
        # This would test the entire refund flow
        pass
    
    def test_reminder_flow(self):
        """Test complete reminder processing flow"""
        # This would test the entire reminder flow
        pass