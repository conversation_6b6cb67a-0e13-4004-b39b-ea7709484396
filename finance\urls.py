from django.urls import path
from . import views

app_name = 'finance'

urlpatterns = [
    # Finance Dashboard
    path('', views.FinanceDashboardView.as_view(), name='dashboard'),

    # Accounts Management
    path('accounts-tree/', views.AccountsTreeView.as_view(), name='accounts_tree'),
    path('accounts/add/', views.AccountCreateView.as_view(), name='add_account'),
    path('accounts/<uuid:pk>/', views.AccountDetailView.as_view(), name='account_detail'),
    path('accounts/<uuid:pk>/edit/', views.AccountUpdateView.as_view(), name='edit_account'),
    path('accounts/<uuid:pk>/archive/', views.AccountArchiveView.as_view(), name='archive_account'),
    path('accounts/<uuid:pk>/unarchive/', views.AccountUnarchiveView.as_view(), name='unarchive_account'),
    path('accounts/reports/', views.AccountReportsView.as_view(), name='account_reports'),
    path('journal-entries/', views.JournalEntriesView.as_view(), name='journal_entries'),
    path('journal-entries/add/', views.JournalEntryCreateView.as_view(), name='add_journal_entry'),

    # Payments and Invoices
    path('payments/', views.FeesPaymentView.as_view(), name='payments'),
    path('payments/add/', views.PaymentCreateView.as_view(), name='add_payment'),
    path('invoices/', views.InvoiceListView.as_view(), name='invoices'),
    path('invoices/add/', views.InvoiceCreateView.as_view(), name='add_invoice'),
    path('cost-center/', views.CostCenterView.as_view(), name='cost_center'),
    path('cost-center/report/', views.CostCenterReportView.as_view(), name='cost_center_report'),
    path('daily-entries/', views.DailyEntriesView.as_view(), name='daily_entries'),
    path('daily-entries/create/', views.CreateDailyEntryView.as_view(), name='create_daily_entry'),
    path('daily-entries/history/', views.DailyEntriesHistoryView.as_view(), name='daily_entries_history'),
    path('financial-years/', views.FinancialYearsView.as_view(), name='financial_years'),
    path('banks/', views.BanksView.as_view(), name='banks'),
    path('safes/', views.SafesView.as_view(), name='safes'),
    path('receipt-voucher/', views.ReceiptVoucherView.as_view(), name='receipt_voucher'),
    path('exchange-permission/', views.ExchangePermissionView.as_view(), name='exchange_permission'),
    path('trial-balance/', views.TrialBalanceView.as_view(), name='trial_balance'),
    path('account-statement/', views.AccountStatementView.as_view(), name='account_statement'),
    path('search-receipts/', views.SearchReceiptsView.as_view(), name='search_receipts'),
    path('monthly-statement/', views.MonthlyAccountStatementView.as_view(), name='monthly_statement'),
    path('opening-balances/', views.OpeningBalancesView.as_view(), name='opening_balances'),
    path('cash-bank-statement/', views.CashBankStatementView.as_view(), name='cash_bank_statement'),
    path('balance-sheet/', views.BalanceSheetView.as_view(), name='balance_sheet'),
    path('income-statement/', views.IncomeStatementView.as_view(), name='income_statement'),

    # Student Accounts
    path('fees-items/', views.FeesItemsView.as_view(), name='fees_items'),
    path('grouped-fees/', views.GroupedOptionalFeesView.as_view(), name='grouped_fees'),
    path('grade-fees/', views.GradeFeesView.as_view(), name='grade_fees'),
    path('fees-payment/', views.FeesPaymentView.as_view(), name='fees_payment'),
    path('payment-history/', views.PaymentHistoryView.as_view(), name='payment_history'),

    # Fee Management System
    path('fee-calculation/', views.FeeCalculationView.as_view(), name='fee_calculation'),
    path('invoice-generation/', views.InvoiceGenerationView.as_view(), name='invoice_generation'),
    path('fee-collection-report/', views.FeeCollectionReportView.as_view(), name='fee_collection_report'),
    path('outstanding-fees-report/', views.OutstandingFeesReportView.as_view(), name='outstanding_fees_report'),
    path('payment-analytics/', views.PaymentAnalyticsView.as_view(), name='payment_analytics'),
    path('student-fee-management/<uuid:pk>/', views.StudentFeeManagementView.as_view(), name='student_fee_management'),

    # Additional aliases for common views
    path('student-fees/', views.FeesPaymentView.as_view(), name='student_fees'),
    path('outstanding-fees/', views.PaymentHistoryView.as_view(), name='outstanding_fees'),
    path('financial-reports/', views.FinancialReportsView.as_view(), name='financial_reports'),
    path('tax-code/', views.TaxCodeView.as_view(), name='tax_code'),
    path('daily-payments-report/', views.DailyPaymentsReportView.as_view(), name='daily_payments_report'),
    path('student-debits/', views.StudentDebitsView.as_view(), name='student_debits'),
    path('accounts-aggregate-report/', views.AccountsAggregateReportView.as_view(), name='accounts_aggregate_report'),
    path('payments-by-stages/', views.PaymentsByStagesView.as_view(), name='payments_by_stages'),
    path('payment-account-report/', views.PaymentAccountReportView.as_view(), name='payment_account_report'),
    path('items-saves-report/', views.ItemsSavesReportView.as_view(), name='items_saves_report'),
    path('tax-payment-report/', views.TaxPaymentReportView.as_view(), name='tax_payment_report'),

    # Payment Permissions
    path('payment-permission/', views.PaymentPermissionView.as_view(), name='payment_permission'),
    path('pay-permission/', views.PayPaymentPermissionView.as_view(), name='pay_permission'),
    path('permission-reports/', views.PermissionReportsView.as_view(), name='permission_reports'),

    # Registration and Discounts
    path('registration-fees/', views.RegistrationFeesView.as_view(), name='registration_fees'),
    path('discount-settings/', views.DiscountSettingsView.as_view(), name='discount_settings'),
    path('discount-details/', views.DiscountSettingsDetailsView.as_view(), name='discount_details'),
    path('add-discount/', views.AddDiscountView.as_view(), name='add_discount'),
    path('remove-debits/', views.RemoveStudentDebitsView.as_view(), name='remove_debits'),
    path('discount-report/', views.DiscountReportView.as_view(), name='discount_report'),
    path('bus-payments-report/', views.BusPaymentsReportView.as_view(), name='bus_payments_report'),
    path('fees-requests/', views.FeesRequestsView.as_view(), name='fees_requests'),
    path('recovery-report/', views.RecoveryReportView.as_view(), name='recovery_report'),
    path('payment-statement/', views.PaymentStatementView.as_view(), name='payment_statement'),
    path('total-payment-statement/', views.TotalPaymentStatementView.as_view(), name='total_payment_statement'),
    path('unpaid-fees-report/', views.UnpaidFeesReportView.as_view(), name='unpaid_fees_report'),
    path('opening-balance-student/', views.AddStudentOpeningBalanceView.as_view(), name='opening_balance_student'),
    path('payment-request/', views.PaymentRequestView.as_view(), name='payment_request'),
    path('grouped-discount/', views.GroupedDiscountView.as_view(), name='grouped_discount'),
    path('grouped-payments/', views.GroupedPaymentTransactionsView.as_view(), name='grouped_payments'),

    # Financial Reports
    path('reports/', views.FinancialReportsView.as_view(), name='reports'),
    path('reports/balance-sheet/', views.BalanceSheetView.as_view(), name='balance_sheet_report'),
    path('reports/profit-loss/', views.ProfitLossView.as_view(), name='profit_loss_report'),
    path('reports/cash-flow/', views.CashFlowView.as_view(), name='cash_flow_report'),
    path('reports/budget-vs-actual/', views.BudgetVsActualView.as_view(), name='budget_vs_actual_report'),
    path('reports/dashboard/', views.FinancialReportsView.as_view(), name='financial_dashboard'),
    path('einvoice-report/', views.EInvoiceReportView.as_view(), name='einvoice_report'),
    path('zakat-income-report/', views.ZakatIncomeReportView.as_view(), name='zakat_income_report'),

    # Double-Entry Bookkeeping
    path('transactions/', views.TransactionListView.as_view(), name='transactions_list'),
    path('transactions/add/', views.TransactionCreateView.as_view(), name='add_transaction'),
    path('transactions/<uuid:pk>/', views.TransactionDetailView.as_view(), name='transaction_detail'),
    path('transactions/<uuid:pk>/edit/', views.TransactionUpdateView.as_view(), name='edit_transaction'),
    path('transactions/<uuid:pk>/approve/', views.TransactionApprovalView.as_view(), name='approve_transaction'),
    path('transactions/<uuid:pk>/post/', views.TransactionPostView.as_view(), name='post_transaction'),
    path('journal-interface/', views.JournalEntryInterfaceView.as_view(), name='journal_entry_interface'),
    path('accounts/<uuid:pk>/ledger/', views.AccountLedgerView.as_view(), name='account_ledger'),
    path('audit-logs/', views.TransactionAuditLogView.as_view(), name='audit_logs'),

    # Payment Processing System
    path('payment/', views.PaymentDashboardView.as_view(), name='payment_dashboard'),
    path('payment/gateways/', views.PaymentGatewayListView.as_view(), name='payment_gateways'),
    path('payment/gateways/add/', views.PaymentGatewayCreateView.as_view(), name='add_payment_gateway'),
    path('payment/gateways/<uuid:pk>/edit/', views.PaymentGatewayUpdateView.as_view(), name='edit_payment_gateway'),
    path('payment/process/', views.PaymentProcessingView.as_view(), name='process_payment'),
    path('payment/transactions/', views.PaymentTransactionListView.as_view(), name='payment_transactions'),
    path('payment/transactions/<uuid:pk>/', views.PaymentTransactionDetailView.as_view(), name='payment_transaction_detail'),
    path('payment/refunds/', views.PaymentRefundListView.as_view(), name='payment_refunds'),
    path('payment/refunds/create/<uuid:transaction_id>/', views.PaymentRefundCreateView.as_view(), name='create_payment_refund'),
    path('payment/refunds/<uuid:pk>/process/', views.PaymentRefundProcessView.as_view(), name='process_payment_refund'),
    path('payment/reminders/', views.PaymentReminderListView.as_view(), name='payment_reminders'),
    path('payment/reminders/create/<uuid:student_fee_id>/', views.PaymentReminderCreateView.as_view(), name='create_payment_reminder'),
    path('payment/reminders/bulk/', views.BulkPaymentReminderView.as_view(), name='bulk_payment_reminders'),
    path('payment/analytics/', views.PaymentAnalyticsView.as_view(), name='payment_analytics'),
    
    # Budget Management
    path('budgets/', views.BudgetListView.as_view(), name='budget_list'),
    path('budgets/create/', views.BudgetCreateView.as_view(), name='budget_create'),
    path('budgets/<uuid:pk>/', views.BudgetDetailView.as_view(), name='budget_detail'),
    path('budgets/<uuid:pk>/edit/', views.BudgetUpdateView.as_view(), name='budget_update'),
    path('budgets/<uuid:budget_pk>/add-item/', views.BudgetItemCreateView.as_view(), name='budget_add_item'),
    path('budgets/<uuid:pk>/submit/', views.BudgetSubmitApprovalView.as_view(), name='budget_submit_approval'),
    path('budgets/<uuid:pk>/approve/', views.BudgetApprovalView.as_view(), name='budget_approval'),
    path('budgets/monitoring/', views.BudgetMonitoringView.as_view(), name='budget_monitoring'),
    path('budgets/<uuid:pk>/variance-report/', views.BudgetVarianceReportView.as_view(), name='budget_variance_report'),
]