from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.views.generic import TemplateView, ListView, DetailView, CreateView, UpdateView, DeleteView, View, FormView
from django.contrib import messages
from django.urls import reverse_lazy
from django.db.models import Q, Sum, Count
from django.http import HttpResponse, JsonResponse
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.core.exceptions import ValidationError
from datetime import datetime, timedelta, date
from decimal import Decimal

# Import payment processing views
from .payment_views import (
    PaymentDashboardView, PaymentGatewayListView, PaymentGatewayCreateView,
    PaymentGatewayUpdateView, PaymentProcessingView, PaymentTransactionListView,
    PaymentTransactionDetailView, PaymentRefundListView, PaymentRefundCreateView,
    PaymentRefundProcessView, PaymentReminderListView, PaymentReminderCreateView,
    BulkPaymentReminderView, PaymentAnalyticsView
)
from .models import (
    Account, AccountType, Payment, PaymentItem, StudentFee, FeeType, GradeFee,
    CostCenter, JournalEntry, FinancialYear, Bank, Invoice, InvoiceItem,
    Transaction, TransactionEntry, TransactionAuditLog, Budget, BudgetItem
)
from .forms import (
    AccountForm, JournalEntryForm, PaymentForm, InvoiceForm, CostCenterForm,
    BankForm, FeeTypeForm, GradeFeeForm, StudentFeeForm, FinancialYearForm, QuickPaymentForm,
    BudgetForm, BudgetItemForm, BudgetApprovalForm, BudgetMonitoringFilterForm,
    TransactionForm, TransactionEntryInlineFormSet, TransactionApprovalForm, 
    TransactionSearchForm, DoubleEntryTransactionForm
)
from students.models import Student

# Import payment processing views
from .payment_views import (
    PaymentDashboardView, PaymentGatewayListView, PaymentGatewayCreateView,
    PaymentGatewayUpdateView, PaymentProcessingView, PaymentTransactionListView,
    PaymentTransactionDetailView, PaymentRefundListView, PaymentRefundCreateView,
    PaymentRefundProcessView, PaymentReminderListView, PaymentReminderCreateView,
    BulkPaymentReminderView, PaymentAnalyticsView
)


class SchoolMixin:
    """Mixin to handle school access for views"""
    
    def get_user_school(self):
        """Get the current user's school"""
        # Try to get school from employee profile first
        if hasattr(self.request.user, 'employee_profile'):
            return self.request.user.employee_profile.school
        
        # Try to get school from student profile
        if hasattr(self.request.user, 'student_profile'):
            return self.request.user.student_profile.school
            
        # Try to get school from parent profile
        if hasattr(self.request.user, 'parent_profile'):
            return self.request.user.parent_profile.school
            
        # For now, return the first school if available (fallback)
        from core.models import School
        return School.objects.first()
    
    def dispatch(self, request, *args, **kwargs):
        """Check if user has access to a school"""
        if not self.get_user_school():
            messages.error(request, 'No school associated with your account. Please contact administrator.')
            return redirect('dashboard')
        return super().dispatch(request, *args, **kwargs)

# Finance Dashboard
class FinanceDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get current month data
        current_month = timezone.now().replace(day=1)
        next_month = (current_month + timedelta(days=32)).replace(day=1)

        # Calculate financial metrics
        context['total_revenue'] = Payment.objects.filter(
            payment_date__gte=current_month,
            payment_date__lt=next_month
        ).aggregate(total=Sum('amount'))['total'] or 0

        context['total_expenses'] = JournalEntry.objects.filter(
            entry_date__gte=current_month,
            entry_date__lt=next_month,
            debit_amount__gt=0,
            account__account_type__type='expense'
        ).aggregate(total=Sum('debit_amount'))['total'] or 0

        # Recent transactions for the dashboard
        context['recent_transactions'] = JournalEntry.objects.select_related(
            'account', 'account__account_type'
        ).order_by('-entry_date', '-created_at')[:5]

        # Outstanding payments
        context['outstanding_payments'] = StudentFee.objects.filter(
            is_paid=False
        ).aggregate(total=Sum('amount'))['total'] or 0

        # Monthly statistics
        context['monthly_payments'] = Payment.objects.filter(
            payment_date__gte=current_month,
            payment_date__lt=next_month
        ).count()

        # Net income
        context['net_income'] = context['total_revenue'] - context['total_expenses']

        # Pending and overdue payments
        pending_invoices = Invoice.objects.filter(status='sent')
        context['pending_payments'] = pending_invoices.aggregate(total=Sum('total_amount'))['total'] or 0
        context['pending_count'] = pending_invoices.count()

        overdue_invoices = Invoice.objects.filter(
            status='sent',
            due_date__lt=timezone.now().date()
        )
        context['overdue_payments'] = overdue_invoices.aggregate(total=Sum('total_amount'))['total'] or 0
        context['overdue_count'] = overdue_invoices.count()

        # Recent payments
        context['recent_payments'] = Payment.objects.select_related('student').order_by('-payment_date')[:10]

        return context


# Financial Reporting Views
class FinancialReportsView(LoginRequiredMixin, TemplateView):
    """Main financial reports dashboard"""
    template_name = 'finance/reports/financial_dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        from .reporting_services import FinancialReportingService
        
        # Initialize reporting service
        reporting_service = FinancialReportingService(self.request.user.employee.school)
        
        # Get dashboard data
        try:
            dashboard_data = reporting_service.generate_financial_dashboard_data()
            context['dashboard_data'] = dashboard_data
        except Exception as e:
            messages.error(self.request, f"Error loading dashboard data: {str(e)}")
            context['dashboard_data'] = None
        
        # Get available budgets for budget vs actual reports
        context['budgets'] = Budget.objects.filter(
            school=self.request.user.employee.school,
            status__in=['active', 'approved']
        ).order_by('-start_date')
        
        return context


class BalanceSheetView(LoginRequiredMixin, TemplateView):
    """Balance Sheet report view"""
    template_name = 'finance/reports/balance_sheet.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        from .reporting_services import FinancialReportingService
        
        # Get date parameter
        as_of_date_str = self.request.GET.get('as_of_date')
        if as_of_date_str:
            try:
                as_of_date = datetime.strptime(as_of_date_str, '%Y-%m-%d').date()
            except ValueError:
                as_of_date = date.today()
                messages.warning(self.request, "Invalid date format. Using today's date.")
        else:
            as_of_date = date.today()
        
        # Initialize reporting service
        reporting_service = FinancialReportingService(self.request.user.employee.school)
        
        try:
            balance_sheet = reporting_service.generate_balance_sheet(
                as_of_date=as_of_date,
                include_zero_balances=self.request.GET.get('include_zero') == 'true'
            )
            context['balance_sheet'] = balance_sheet
        except Exception as e:
            messages.error(self.request, f"Error generating balance sheet: {str(e)}")
            context['balance_sheet'] = None
        
        context['as_of_date'] = as_of_date
        return context


class ProfitLossView(LoginRequiredMixin, TemplateView):
    """Profit & Loss Statement view"""
    template_name = 'finance/reports/profit_loss.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        from .reporting_services import FinancialReportingService
        
        # Get date parameters
        start_date_str = self.request.GET.get('start_date')
        end_date_str = self.request.GET.get('end_date')
        
        if start_date_str:
            try:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            except ValueError:
                start_date = date.today().replace(day=1)  # First day of current month
                messages.warning(self.request, "Invalid start date format. Using first day of current month.")
        else:
            start_date = date.today().replace(day=1)
        
        if end_date_str:
            try:
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
            except ValueError:
                end_date = date.today()
                messages.warning(self.request, "Invalid end date format. Using today's date.")
        else:
            end_date = date.today()
        
        # Initialize reporting service
        reporting_service = FinancialReportingService(self.request.user.employee.school)
        
        try:
            pl_statement = reporting_service.generate_profit_loss_statement(
                start_date=start_date,
                end_date=end_date,
                include_zero_balances=self.request.GET.get('include_zero') == 'true'
            )
            context['pl_statement'] = pl_statement
        except Exception as e:
            messages.error(self.request, f"Error generating P&L statement: {str(e)}")
            context['pl_statement'] = None
        
        context['start_date'] = start_date
        context['end_date'] = end_date
        return context


class CashFlowView(LoginRequiredMixin, TemplateView):
    """Cash Flow Statement view"""
    template_name = 'finance/reports/cash_flow.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        from .reporting_services import FinancialReportingService
        
        # Get date parameters
        start_date_str = self.request.GET.get('start_date')
        end_date_str = self.request.GET.get('end_date')
        
        if start_date_str:
            try:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            except ValueError:
                start_date = date.today().replace(day=1)  # First day of current month
                messages.warning(self.request, "Invalid start date format. Using first day of current month.")
        else:
            start_date = date.today().replace(day=1)
        
        if end_date_str:
            try:
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
            except ValueError:
                end_date = date.today()
                messages.warning(self.request, "Invalid end date format. Using today's date.")
        else:
            end_date = date.today()
        
        # Initialize reporting service
        reporting_service = FinancialReportingService(self.request.user.employee.school)
        
        try:
            cash_flow = reporting_service.generate_cash_flow_statement(
                start_date=start_date,
                end_date=end_date
            )
            context['cash_flow'] = cash_flow
        except Exception as e:
            messages.error(self.request, f"Error generating cash flow statement: {str(e)}")
            context['cash_flow'] = None
        
        context['start_date'] = start_date
        context['end_date'] = end_date
        return context


class BudgetVsActualView(LoginRequiredMixin, TemplateView):
    """Budget vs Actual report view"""
    template_name = 'finance/reports/budget_vs_actual.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        from .reporting_services import FinancialReportingService
        
        # Get budget parameter
        budget_id = self.request.GET.get('budget_id')
        if not budget_id:
            context['budgets'] = Budget.objects.filter(
                school=self.request.user.employee.school,
                status__in=['active', 'approved']
            ).order_by('-start_date')
            return context
        
        # Get as_of_date parameter
        as_of_date_str = self.request.GET.get('as_of_date')
        if as_of_date_str:
            try:
                as_of_date = datetime.strptime(as_of_date_str, '%Y-%m-%d').date()
            except ValueError:
                as_of_date = date.today()
                messages.warning(self.request, "Invalid date format. Using today's date.")
        else:
            as_of_date = date.today()
        
        # Initialize reporting service
        reporting_service = FinancialReportingService(self.request.user.employee.school)
        
        try:
            budget_report = reporting_service.generate_budget_vs_actual_report(
                budget_id=int(budget_id),
                as_of_date=as_of_date
            )
            context['budget_report'] = budget_report
        except Budget.DoesNotExist:
            messages.error(self.request, "Budget not found.")
            context['budget_report'] = None
        except Exception as e:
            messages.error(self.request, f"Error generating budget vs actual report: {str(e)}")
            context['budget_report'] = None
        
        # Get available budgets for dropdown
        context['budgets'] = Budget.objects.filter(
            school=self.request.user.employee.school,
            status__in=['active', 'approved']
        ).order_by('-start_date')
        
        context['selected_budget_id'] = budget_id
        context['as_of_date'] = as_of_date
        return context

        # Outstanding fees
        outstanding_fees = StudentFee.objects.filter(is_paid=False)
        context['outstanding_fees_count'] = outstanding_fees.values('student').distinct().count()
        context['outstanding_fees_amount'] = outstanding_fees.aggregate(
            total=Sum('amount') - Sum('discount_amount')
        )['total'] or 0

        # Chart data (last 6 months)
        months = []
        revenue_data = []
        expense_data = []

        for i in range(6):
            month_start = (current_month - timedelta(days=i*30)).replace(day=1)
            month_end = (month_start + timedelta(days=32)).replace(day=1)

            months.append(month_start.strftime('%b %Y'))

            month_revenue = Payment.objects.filter(
                payment_date__gte=month_start,
                payment_date__lt=month_end
            ).aggregate(total=Sum('amount'))['total'] or 0

            month_expenses = JournalEntry.objects.filter(
                entry_date__gte=month_start,
                entry_date__lt=month_end,
                debit_amount__gt=0,
                account__account_type__type='expense'
            ).aggregate(total=Sum('debit_amount'))['total'] or 0

            revenue_data.append(float(month_revenue))
            expense_data.append(float(month_expenses))

        context['chart_labels'] = list(reversed(months))
        context['revenue_data'] = list(reversed(revenue_data))
        context['expense_data'] = list(reversed(expense_data))

        # Payment methods data
        payment_methods = Payment.objects.values('payment_method').annotate(
            count=Count('id'),
            total=Sum('amount')
        ).order_by('-total')

        context['payment_method_labels'] = [pm['payment_method'] for pm in payment_methods]
        context['payment_method_data'] = [float(pm['total']) for pm in payment_methods]

        return context

# Accounts Management Views
class AccountsTreeView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/accounts_tree.html'

class CostCenterView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/cost_center.html'

class CostCenterReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/cost_center_report.html'

class DailyEntriesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/daily_entries.html'

class CreateDailyEntryView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/create_daily_entry.html'

class DailyEntriesHistoryView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/daily_entries_history.html'

class FinancialYearsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/financial_years.html'

class BanksView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/banks.html'

class SafesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/safes.html'

class ReceiptVoucherView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/receipt_voucher.html'

class ExchangePermissionView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/exchange_permission.html'

class TrialBalanceView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/trial_balance.html'

class AccountStatementView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/account_statement.html'

class SearchReceiptsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/search_receipts.html'

class MonthlyAccountStatementView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/monthly_statement.html'

class OpeningBalancesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/opening_balances.html'

class CashBankStatementView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/cash_bank_statement.html'

class BalanceSheetView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/balance_sheet.html'

class IncomeStatementView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/income_statement.html'

# Student Accounts Views
class FeesItemsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/fees_items.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            context['fee_types'] = FeeType.objects.filter(school=school, is_active=True)
            context['total_fee_types'] = context['fee_types'].count()
            
            # Get fee type statistics
            from django.db.models import Count, Sum
            context['fee_statistics'] = context['fee_types'].annotate(
                grade_count=Count('grade_fees'),
                total_amount=Sum('grade_fees__amount')
            )
        
        return context


class GroupedOptionalFeesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/grouped_fees.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            # Get optional fees grouped by grade
            context['optional_fees'] = GradeFee.objects.filter(
                school=school,
                fee_type__is_mandatory=False,
                is_active=True
            ).select_related('grade', 'fee_type', 'academic_year').order_by('grade__level', 'fee_type__name')
            
            # Group by grade
            from itertools import groupby
            context['fees_by_grade'] = {
                grade: list(fees) for grade, fees in 
                groupby(context['optional_fees'], key=lambda x: x.grade)
            }
        
        return context


class GradeFeesView(LoginRequiredMixin, ListView):
    model = GradeFee
    template_name = 'finance/grade_fees.html'
    context_object_name = 'grade_fees'
    paginate_by = 20

    def get_queryset(self):
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        if not school:
            return GradeFee.objects.none()
        
        queryset = GradeFee.objects.filter(school=school).select_related(
            'grade', 'fee_type', 'academic_year'
        )
        
        # Apply filters
        grade_id = self.request.GET.get('grade')
        fee_type_id = self.request.GET.get('fee_type')
        academic_year_id = self.request.GET.get('academic_year')
        
        if grade_id:
            queryset = queryset.filter(grade_id=grade_id)
        if fee_type_id:
            queryset = queryset.filter(fee_type_id=fee_type_id)
        if academic_year_id:
            queryset = queryset.filter(academic_year_id=academic_year_id)
        
        return queryset.order_by('grade__level', 'fee_type__name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            from students.models import Grade
            context['grades'] = Grade.objects.filter(school=school, is_active=True)
            context['fee_types'] = FeeType.objects.filter(school=school, is_active=True)
            context['academic_years'] = AcademicYear.objects.filter(school=school)
        
        return context

class FeesPaymentView(LoginRequiredMixin, ListView):
    model = Payment
    template_name = 'finance/fees_payment.html'
    context_object_name = 'payments'
    paginate_by = 20

    def get_queryset(self):
        queryset = Payment.objects.select_related('student', 'received_by').all()
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(student__first_name__icontains=search) |
                Q(student__last_name__icontains=search) |
                Q(receipt_number__icontains=search)
            )
        return queryset.order_by('-payment_date')


class PaymentCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Payment
    form_class = PaymentForm
    template_name = 'finance/payment_form.html'
    permission_required = 'finance.add_payment'
    success_url = reverse_lazy('finance:payments')

    def form_valid(self, form):
        form.instance.received_by = self.request.user
        messages.success(self.request, _('Payment recorded successfully!'))
        return super().form_valid(form)


class PaymentHistoryView(LoginRequiredMixin, ListView):
    model = Payment
    template_name = 'finance/payment_history.html'
    context_object_name = 'payments'
    paginate_by = 20

    def get_queryset(self):
        queryset = Payment.objects.select_related('student', 'received_by').all()
        student_id = self.request.GET.get('student')
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')

        if student_id:
            queryset = queryset.filter(student_id=student_id)
        if date_from:
            queryset = queryset.filter(payment_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(payment_date__lte=date_to)

        return queryset.order_by('-payment_date')


class InvoiceListView(LoginRequiredMixin, ListView):
    model = Invoice
    template_name = 'finance/invoices.html'
    context_object_name = 'invoices'
    paginate_by = 20

    def get_queryset(self):
        queryset = Invoice.objects.select_related('student', 'created_by').all()
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
        return queryset.order_by('-invoice_date')


class InvoiceCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Invoice
    form_class = InvoiceForm
    template_name = 'finance/invoice_form.html'
    permission_required = 'finance.add_invoice'
    success_url = reverse_lazy('finance:invoices')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        # Auto-generate invoice number
        last_invoice = Invoice.objects.order_by('-id').first()
        if last_invoice:
            last_number = int(last_invoice.invoice_number.split('-')[-1])
            form.instance.invoice_number = f"INV-{last_number + 1:06d}"
        else:
            form.instance.invoice_number = "INV-000001"

        messages.success(self.request, _('Invoice created successfully!'))
        return super().form_valid(form)

class AccountsTreeView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/accounts_tree.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            from .services import ChartOfAccountsService
            
            context['account_tree'] = ChartOfAccountsService.get_account_tree(school)
            context['account_types'] = AccountType.objects.filter(school=school).prefetch_related('accounts')
            context['total_accounts'] = Account.objects.filter(school=school).active().count()
            context['archived_accounts'] = Account.objects.filter(school=school).archived().count()
            
            # Get account balances
            balances = ChartOfAccountsService.get_account_balances(school)
            context['account_balances'] = balances
        
        return context


class AccountCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Account
    form_class = AccountForm
    template_name = 'finance/account_form.html'
    permission_required = 'finance.add_account'
    success_url = reverse_lazy('finance:accounts_tree')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        if hasattr(self.request.user, 'employee'):
            kwargs['school'] = self.request.user.employee.school
        return kwargs

    def form_valid(self, form):
        form.instance.school = self.get_user_school()
        form.instance.created_by = self.request.user
        messages.success(self.request, _('Account created successfully!'))
        return super().form_valid(form)


class AccountUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = Account
    form_class = AccountForm
    template_name = 'finance/account_form.html'
    permission_required = 'finance.change_account'
    success_url = reverse_lazy('finance:accounts_tree')

    def get_queryset(self):
        if hasattr(self.request.user, 'employee'):
            return Account.objects.filter(school=self.request.user.employee.school)
        return Account.objects.none()

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        if hasattr(self.request.user, 'employee'):
            kwargs['school'] = self.request.user.employee.school
        return kwargs

    def form_valid(self, form):
        form.instance.updated_by = self.request.user
        messages.success(self.request, _('Account updated successfully!'))
        return super().form_valid(form)


class AccountDetailView(LoginRequiredMixin, DetailView):
    model = Account
    template_name = 'finance/account_detail.html'
    context_object_name = 'account'

    def get_queryset(self):
        if hasattr(self.request.user, 'employee'):
            return Account.objects.filter(school=self.request.user.employee.school)
        return Account.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        account = self.get_object()
        
        # Get account balance
        context['current_balance'] = account.get_balance()
        
        # Get recent journal entries
        context['recent_entries'] = account.journal_entries.select_related(
            'created_by'
        ).order_by('-entry_date', '-created_at')[:10]
        
        # Get child accounts
        context['child_accounts'] = account.children.active().select_related('account_type')
        
        # Check if account can be deleted/archived
        context['can_delete'], context['delete_error'] = account.can_be_deleted()
        context['can_archive'], context['archive_error'] = account.can_be_archived()
        
        return context


class AccountArchiveView(LoginRequiredMixin, PermissionRequiredMixin, View):
    permission_required = 'finance.change_account'

    def post(self, request, pk):
        account = get_object_or_404(Account, pk=pk, school=request.user.employee.school)
        
        from .forms import AccountArchiveForm
        form = AccountArchiveForm(request.POST)
        
        if form.is_valid():
            try:
                from .services import ChartOfAccountsService
                ChartOfAccountsService.archive_account(
                    account, 
                    request.user, 
                    form.cleaned_data['reason']
                )
                messages.success(request, _('Account archived successfully!'))
            except ValidationError as e:
                messages.error(request, str(e))
        else:
            messages.error(request, _('Please provide a valid reason for archiving.'))
        
        return redirect('finance:account_detail', pk=pk)


class AccountUnarchiveView(LoginRequiredMixin, PermissionRequiredMixin, View):
    permission_required = 'finance.change_account'

    def post(self, request, pk):
        account = get_object_or_404(Account, pk=pk, school=request.user.employee.school)
        
        try:
            account.unarchive()
            messages.success(request, _('Account unarchived successfully!'))
        except Exception as e:
            messages.error(request, str(e))
        
        return redirect('finance:account_detail', pk=pk)


class AccountReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/account_reports.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            from .services import ChartOfAccountsService
            
            report_type = self.request.GET.get('type', 'trial_balance')
            as_of_date = self.request.GET.get('as_of_date')
            
            if as_of_date:
                try:
                    as_of_date = datetime.strptime(as_of_date, '%Y-%m-%d').date()
                except ValueError:
                    as_of_date = None
            
            context['report_data'] = ChartOfAccountsService.generate_account_report(
                school, report_type, as_of_date
            )
            context['report_type'] = report_type
        
        return context


class JournalEntriesView(LoginRequiredMixin, ListView):
    model = JournalEntry
    template_name = 'finance/journal_entries.html'
    context_object_name = 'entries'
    paginate_by = 20

    def get_queryset(self):
        queryset = JournalEntry.objects.select_related(
            'account', 'cost_center', 'created_by', 'posted_by'
        ).all()

        account_id = self.request.GET.get('account')
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        is_posted = self.request.GET.get('is_posted')

        if account_id:
            queryset = queryset.filter(account_id=account_id)
        if date_from:
            queryset = queryset.filter(entry_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(entry_date__lte=date_to)
        if is_posted:
            queryset = queryset.filter(is_posted=is_posted == 'true')

        return queryset.order_by('-entry_date', '-created_at')


class JournalEntryCreateView(LoginRequiredMixin, PermissionRequiredMixin, SchoolMixin, CreateView):
    model = JournalEntry
    form_class = JournalEntryForm
    template_name = 'finance/journal_entry_form.html'
    permission_required = 'finance.add_journalentry'
    success_url = reverse_lazy('finance:journal_entries')

    def form_valid(self, form):
        school = self.get_user_school()
        form.instance.school = school
        form.instance.created_by = self.request.user
        
        # Auto-generate reference number
        last_entry = JournalEntry.objects.filter(school=school).order_by('-id').first()
        if last_entry:
            last_number = int(last_entry.reference_number.split('-')[-1])
            form.instance.reference_number = f"JE-{last_number + 1:06d}"
        else:
            form.instance.reference_number = "JE-000001"

        messages.success(self.request, _('Journal entry created successfully!'))
        return super().form_valid(form)

class TaxCodeView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/tax_code.html'

class DailyPaymentsReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/daily_payments_report.html'

class StudentDebitsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/student_debits.html'

class AccountsAggregateReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/accounts_aggregate_report.html'

class PaymentsByStagesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/payments_by_stages.html'

class PaymentAccountReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/payment_account_report.html'

class ItemsSavesReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/items_saves_report.html'

class TaxPaymentReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/tax_payment_report.html'

# Payment Permissions Views
class PaymentPermissionView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/payment_permission.html'

class PayPaymentPermissionView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/pay_permission.html'

class PermissionReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/permission_reports.html'

# Registration and Discounts Views
class RegistrationFeesView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/registration_fees.html'

class DiscountSettingsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/discount_settings.html'

class DiscountSettingsDetailsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/discount_details.html'

class AddDiscountView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/add_discount.html'

class RemoveStudentDebitsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/remove_debits.html'

class DiscountReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/discount_report.html'

class BusPaymentsReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/bus_payments_report.html'

class FeesRequestsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/fees_requests.html'

class RecoveryReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/recovery_report.html'

class PaymentStatementView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/payment_statement.html'

class TotalPaymentStatementView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/total_payment_statement.html'

class UnpaidFeesReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/unpaid_fees_report.html'

class AddStudentOpeningBalanceView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/opening_balance_student.html'

class PaymentRequestView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/payment_request.html'

class GroupedDiscountView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/grouped_discount.html'

class GroupedPaymentTransactionsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/grouped_payments.html'


# Financial Reporting Views
class FinancialReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/financial_reports.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            from .services import FinancialReportingService
            
            # Get dashboard data for quick overview
            context['dashboard_data'] = FinancialReportingService.generate_financial_dashboard_data(school)
            
            # Available report types
            context['report_types'] = [
                {'key': 'balance_sheet', 'name': _('Balance Sheet'), 'description': _('Assets, Liabilities, and Equity')},
                {'key': 'profit_loss', 'name': _('Profit & Loss Statement'), 'description': _('Revenue and Expenses')},
                {'key': 'cash_flow', 'name': _('Cash Flow Statement'), 'description': _('Cash Inflows and Outflows')},
                {'key': 'budget_vs_actual', 'name': _('Budget vs Actual'), 'description': _('Budget Performance Analysis')},
                {'key': 'general_ledger', 'name': _('General Ledger'), 'description': _('Detailed Account Transactions')},
                {'key': 'journal_register', 'name': _('Journal Register'), 'description': _('All Journal Entries')}
            ]
        
        return context


class BalanceSheetReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/reports/balance_sheet.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            from .services import FinancialReportingService
            
            as_of_date = self.request.GET.get('as_of_date')
            if as_of_date:
                try:
                    as_of_date = datetime.strptime(as_of_date, '%Y-%m-%d').date()
                except ValueError:
                    as_of_date = None
            
            context['report_data'] = FinancialReportingService.generate_balance_sheet(school, as_of_date)
        
        return context


class ProfitLossReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/reports/profit_loss.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            from .services import FinancialReportingService
            
            start_date = self.request.GET.get('start_date')
            end_date = self.request.GET.get('end_date')
            
            if start_date:
                try:
                    start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                except ValueError:
                    start_date = None
            
            if end_date:
                try:
                    end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                except ValueError:
                    end_date = None
            
            context['report_data'] = FinancialReportingService.generate_profit_loss_statement(
                school, start_date, end_date
            )
        
        return context


class CashFlowReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/reports/cash_flow.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            from .services import FinancialReportingService
            
            start_date = self.request.GET.get('start_date')
            end_date = self.request.GET.get('end_date')
            
            if start_date:
                try:
                    start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                except ValueError:
                    start_date = None
            
            if end_date:
                try:
                    end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                except ValueError:
                    end_date = None
            
            context['report_data'] = FinancialReportingService.generate_cash_flow_report(
                school, start_date, end_date
            )
        
        return context


class BudgetVsActualReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/reports/budget_vs_actual.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            from .services import FinancialReportingService
            
            budget_year = self.request.GET.get('budget_year')
            if budget_year:
                try:
                    budget_year = int(budget_year)
                except ValueError:
                    budget_year = None
            
            context['report_data'] = FinancialReportingService.generate_budget_vs_actual_report(
                school, budget_year
            )
        
        return context


class GeneralLedgerReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/reports/general_ledger.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            from .services import FinancialReportingService
            
            start_date = self.request.GET.get('start_date')
            end_date = self.request.GET.get('end_date')
            
            if start_date:
                try:
                    start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                except ValueError:
                    start_date = None
            
            if end_date:
                try:
                    end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                except ValueError:
                    end_date = None
            
            context['report_data'] = FinancialReportingService.generate_general_ledger(
                school, start_date, end_date
            )
        
        return context


class JournalRegisterReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/reports/journal_register.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            from .services import FinancialReportingService
            
            start_date = self.request.GET.get('start_date')
            end_date = self.request.GET.get('end_date')
            
            if start_date:
                try:
                    start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                except ValueError:
                    start_date = None
            
            if end_date:
                try:
                    end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                except ValueError:
                    end_date = None
            
            context['report_data'] = FinancialReportingService.generate_journal_register(
                school, start_date, end_date
            )
        
        return context


class FinancialDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/reports/financial_dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            from .services import FinancialReportingService
            
            context['dashboard_data'] = FinancialReportingService.generate_financial_dashboard_data(school)
        
        return context


# Fee Management Views
class FeeCalculationView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/fee_calculation.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            from students.models import Grade
            context['grades'] = Grade.objects.filter(school=school, is_active=True)
            context['academic_years'] = AcademicYear.objects.filter(school=school)
        
        return context

    def post(self, request, *args, **kwargs):
        from .fee_services import FeeCalculationService
        
        student_id = request.POST.get('student_id')
        academic_year_id = request.POST.get('academic_year_id')
        
        try:
            student = Student.objects.get(id=student_id)
            academic_year = AcademicYear.objects.get(id=academic_year_id) if academic_year_id else None
            
            fee_breakdown = FeeCalculationService.calculate_student_fees(student, academic_year)
            
            return JsonResponse({
                'success': True,
                'fee_breakdown': {
                    'total_amount': str(fee_breakdown['total_amount']),
                    'total_discount': str(fee_breakdown['total_discount']),
                    'net_amount': str(fee_breakdown['net_amount']),
                    'mandatory_fees': str(fee_breakdown['mandatory_fees']),
                    'optional_fees': str(fee_breakdown['optional_fees']),
                    'fees': [
                        {
                            'fee_type': fee['fee_type'].name,
                            'amount': str(fee['amount']),
                            'discount_amount': str(fee['discount_amount']),
                            'net_amount': str(fee['net_amount']),
                            'due_date': fee['due_date'].isoformat(),
                            'is_mandatory': fee['is_mandatory'],
                            'is_paid': fee['is_paid']
                        }
                        for fee in fee_breakdown['fees']
                    ]
                }
            })
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})


class InvoiceGenerationView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'finance/invoice_generation.html'
    permission_required = 'finance.add_invoice'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            from students.models import Grade
            context['grades'] = Grade.objects.filter(school=school, is_active=True)
            context['fee_types'] = FeeType.objects.filter(school=school, is_active=True)
            context['academic_years'] = AcademicYear.objects.filter(school=school)
        
        return context

    def post(self, request, *args, **kwargs):
        from .fee_services import InvoiceGenerationService
        
        action = request.POST.get('action')
        
        try:
            if action == 'generate_single':
                student_id = request.POST.get('student_id')
                fee_type_ids = request.POST.getlist('fee_types')
                academic_year_id = request.POST.get('academic_year_id')
                due_date = request.POST.get('due_date')
                
                student = Student.objects.get(id=student_id)
                academic_year = AcademicYear.objects.get(id=academic_year_id) if academic_year_id else None
                due_date = datetime.strptime(due_date, '%Y-%m-%d').date() if due_date else None
                
                invoice = InvoiceGenerationService.generate_student_invoice(
                    student, [int(id) for id in fee_type_ids], academic_year, due_date
                )
                
                messages.success(request, _('Invoice {} generated successfully!').format(invoice.invoice_number))
                return redirect('finance:invoice_detail', pk=invoice.pk)
                
            elif action == 'generate_bulk':
                grade_id = request.POST.get('grade_id')
                fee_type_ids = request.POST.getlist('fee_types')
                academic_year_id = request.POST.get('academic_year_id')
                
                from students.models import Grade
                grade = Grade.objects.get(id=grade_id)
                academic_year = AcademicYear.objects.get(id=academic_year_id) if academic_year_id else None
                
                invoices = InvoiceGenerationService.generate_bulk_invoices(
                    grade, academic_year, [int(id) for id in fee_type_ids]
                )
                
                messages.success(request, _('Generated {} invoices successfully!').format(len(invoices)))
                return redirect('finance:invoices')
                
        except Exception as e:
            messages.error(request, str(e))
        
        return self.get(request, *args, **kwargs)


class FeeCollectionReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/fee_collection_report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            from .fee_services import FeeCollectionReportingService
            from students.models import Grade
            
            # Get report parameters
            academic_year_id = self.request.GET.get('academic_year')
            start_date = self.request.GET.get('start_date')
            end_date = self.request.GET.get('end_date')
            
            academic_year = None
            if academic_year_id:
                academic_year = AcademicYear.objects.get(id=academic_year_id)
            
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date() if start_date else None
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date() if end_date else None
            
            # Generate collection summary report
            context['collection_report'] = FeeCollectionReportingService.generate_collection_summary_report(
                school, academic_year, start_date, end_date
            )
            
            # Additional context
            context['grades'] = Grade.objects.filter(school=school, is_active=True)
            context['academic_years'] = AcademicYear.objects.filter(school=school)
        
        return context


class OutstandingFeesReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/outstanding_fees_report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            from .fee_services import FeeCollectionReportingService
            from students.models import Grade
            
            grade_id = self.request.GET.get('grade')
            grade = Grade.objects.get(id=grade_id) if grade_id else None
            
            context['outstanding_report'] = FeeCollectionReportingService.generate_outstanding_fees_report(
                school, grade
            )
            
            context['grades'] = Grade.objects.filter(school=school, is_active=True)
        
        return context


class PaymentAnalyticsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/payment_analytics.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            from .fee_services import FeeCollectionReportingService
            
            academic_year_id = self.request.GET.get('academic_year')
            academic_year = AcademicYear.objects.get(id=academic_year_id) if academic_year_id else None
            
            context['analytics'] = FeeCollectionReportingService.generate_payment_analytics(
                school, academic_year
            )
            
            context['academic_years'] = AcademicYear.objects.filter(school=school)
        
        return context


class StudentFeeManagementView(LoginRequiredMixin, DetailView):
    model = Student
    template_name = 'finance/student_fee_management.html'
    context_object_name = 'student'

    def get_queryset(self):
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        if not school:
            return Student.objects.none()
        return Student.objects.filter(school=school)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        student = self.get_object()
        
        from .fee_services import FeeCalculationService, PaymentTrackingService
        
        # Get current academic year
        academic_year = AcademicYear.objects.filter(
            school=student.school,
            is_current=True
        ).first()
        
        if academic_year:
            # Calculate student fees
            context['fee_breakdown'] = FeeCalculationService.calculate_student_fees(student, academic_year)
            
            # Get payment history
            context['payment_history'] = PaymentTrackingService.get_student_payment_history(student, academic_year)
        
        context['academic_year'] = academic_year
        return context


# Reports Views
class FinancialReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/reports.html'

class EInvoiceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/einvoice_report.html'

class ZakatIncomeReportView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/zakat_income_report.html'


# Double-Entry Bookkeeping Views
class TransactionListView(LoginRequiredMixin, ListView):
    model = Transaction
    template_name = 'finance/transactions_list.html'
    context_object_name = 'transactions'
    paginate_by = 20

    def get_queryset(self):
        from .models import Transaction
        queryset = Transaction.objects.select_related(
            'created_by', 'approved_by', 'posted_by'
        ).prefetch_related('entries__account').all()

        # Apply filters
        transaction_id = self.request.GET.get('transaction_id')
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        transaction_type = self.request.GET.get('transaction_type')
        status = self.request.GET.get('status')
        account_id = self.request.GET.get('account')

        if transaction_id:
            queryset = queryset.filter(transaction_id__icontains=transaction_id)
        if date_from:
            queryset = queryset.filter(transaction_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(transaction_date__lte=date_to)
        if transaction_type:
            queryset = queryset.filter(transaction_type=transaction_type)
        if status:
            queryset = queryset.filter(status=status)
        if account_id:
            queryset = queryset.filter(entries__account_id=account_id).distinct()

        return queryset.order_by('-transaction_date', '-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        from .forms import TransactionSearchForm
        
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        context['search_form'] = TransactionSearchForm(self.request.GET, school=school)
        
        return context


class TransactionDetailView(LoginRequiredMixin, DetailView):
    model = Transaction
    template_name = 'finance/transaction_detail.html'
    context_object_name = 'transaction'

    def get_queryset(self):
        from .models import Transaction
        if hasattr(self.request.user, 'employee'):
            return Transaction.objects.filter(school=self.request.user.employee.school)
        return Transaction.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        transaction_obj = self.get_object()
        
        # Get transaction entries
        context['entries'] = transaction_obj.entries.select_related('account', 'cost_center').all()
        
        # Get audit trail
        from .services import DoubleEntryBookkeepingService
        context['audit_trail'] = DoubleEntryBookkeepingService.get_transaction_audit_trail(transaction_obj)
        
        # Check permissions
        context['can_approve'] = (
            transaction_obj.status == 'pending_approval' and 
            self.request.user.has_perm('finance.approve_transaction')
        )
        context['can_post'] = (
            transaction_obj.status in ['approved', 'draft'] and 
            self.request.user.has_perm('finance.post_transaction')
        )
        context['can_cancel'] = (
            transaction_obj.status != 'posted' and 
            self.request.user.has_perm('finance.cancel_transaction')
        )
        
        return context


class TransactionCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Transaction
    template_name = 'finance/transaction_form.html'
    permission_required = 'finance.add_transaction'
    
    def get_form_class(self):
        from .forms import DoubleEntryTransactionForm
        return DoubleEntryTransactionForm

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        if hasattr(self.request.user, 'employee'):
            kwargs['school'] = self.request.user.employee.school
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            # Get accounts for entries
            context['accounts'] = Account.objects.filter(
                school=school,
                is_header=False,
                is_active=True,
                allow_manual_entries=True
            ).select_related('account_type').order_by('code')
            
            # Get cost centers
            context['cost_centers'] = CostCenter.objects.filter(
                school=school,
                is_active=True
            ).order_by('code')
        
        return context

    def form_valid(self, form):
        if hasattr(self.request.user, 'employee'):
            form.instance.school = self.request.user.employee.school
        form.instance.created_by = self.request.user
        
        # Handle transaction entries from POST data
        entries_data = self._extract_entries_data()
        
        try:
            from .services import DoubleEntryBookkeepingService
            transaction_obj = DoubleEntryBookkeepingService.create_transaction(
                form.instance.school,
                self.request.user,
                {
                    'transaction_date': form.cleaned_data['transaction_date'],
                    'transaction_type': form.cleaned_data['transaction_type'],
                    'description': form.cleaned_data['description'],
                    'reference': form.cleaned_data.get('reference', ''),
                    'total_amount': 0,  # Will be calculated
                    'requires_approval': form.cleaned_data.get('requires_approval', False)
                },
                entries_data
            )
            
            messages.success(self.request, _('Transaction created successfully!'))
            return redirect('finance:transaction_detail', pk=transaction_obj.pk)
            
        except ValidationError as e:
            messages.error(self.request, str(e))
            return self.form_invalid(form)

    def _extract_entries_data(self):
        """Extract transaction entries data from POST"""
        entries_data = []
        entry_count = 0
        
        while f'entry_{entry_count}_account' in self.request.POST:
            account_id = self.request.POST.get(f'entry_{entry_count}_account')
            description = self.request.POST.get(f'entry_{entry_count}_description', '')
            debit_amount = self.request.POST.get(f'entry_{entry_count}_debit_amount', '0')
            credit_amount = self.request.POST.get(f'entry_{entry_count}_credit_amount', '0')
            cost_center_id = self.request.POST.get(f'entry_{entry_count}_cost_center')
            reference = self.request.POST.get(f'entry_{entry_count}_reference', '')
            
            if account_id:
                try:
                    entries_data.append({
                        'account_id': int(account_id),
                        'description': description,
                        'debit_amount': Decimal(debit_amount or '0'),
                        'credit_amount': Decimal(credit_amount or '0'),
                        'cost_center_id': int(cost_center_id) if cost_center_id else None,
                        'reference': reference
                    })
                except (ValueError, TypeError):
                    pass  # Skip invalid entries
            
            entry_count += 1
        
        return entries_data


class TransactionUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = Transaction
    template_name = 'finance/transaction_form.html'
    permission_required = 'finance.change_transaction'
    
    def get_form_class(self):
        from .forms import TransactionForm
        return TransactionForm

    def get_queryset(self):
        from .models import Transaction
        if hasattr(self.request.user, 'employee'):
            return Transaction.objects.filter(
                school=self.request.user.employee.school,
                status__in=['draft', 'pending_approval']  # Only allow editing draft/pending transactions
            )
        return Transaction.objects.none()

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        if hasattr(self.request.user, 'employee'):
            kwargs['school'] = self.request.user.employee.school
        return kwargs

    def form_valid(self, form):
        form.instance.modified_by = self.request.user
        messages.success(self.request, _('Transaction updated successfully!'))
        return super().form_valid(form)

    def get_success_url(self):
        return reverse_lazy('finance:transaction_detail', kwargs={'pk': self.object.pk})


class TransactionApprovalView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    permission_required = 'finance.approve_transaction'
    template_name = 'finance/transaction_approval.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        from .models import Transaction
        
        transaction_obj = get_object_or_404(
            Transaction, 
            pk=kwargs['pk'], 
            school=self.request.user.employee.school,
            status='pending_approval'
        )
        
        context['transaction'] = transaction_obj
        context['entries'] = transaction_obj.entries.select_related('account', 'cost_center').all()
        
        return context

    def post(self, request, pk):
        from .models import Transaction
        from .forms import TransactionApprovalForm
        from .services import DoubleEntryBookkeepingService
        
        transaction_obj = get_object_or_404(
            Transaction, 
            pk=pk, 
            school=request.user.employee.school,
            status='pending_approval'
        )
        
        action = request.POST.get('action')
        notes = request.POST.get('approval_notes', '')
        
        try:
            if action == 'approve':
                DoubleEntryBookkeepingService.approve_transaction(transaction_obj, request.user, notes)
                messages.success(request, _('Transaction approved successfully!'))
            elif action == 'reject':
                DoubleEntryBookkeepingService.cancel_transaction(transaction_obj, request.user, notes)
                messages.success(request, _('Transaction rejected successfully!'))
            else:
                messages.error(request, _('Invalid action'))
                
        except ValidationError as e:
            messages.error(request, str(e))
        
        return redirect('finance:transaction_detail', pk=pk)


class TransactionPostView(LoginRequiredMixin, PermissionRequiredMixin, View):
    permission_required = 'finance.post_transaction'

    def post(self, request, pk):
        from .models import Transaction
        from .services import DoubleEntryBookkeepingService
        
        transaction_obj = get_object_or_404(
            Transaction, 
            pk=pk, 
            school=request.user.employee.school
        )
        
        try:
            DoubleEntryBookkeepingService.post_transaction(transaction_obj, request.user)
            messages.success(request, _('Transaction posted successfully!'))
        except ValidationError as e:
            messages.error(request, str(e))
        
        return redirect('finance:transaction_detail', pk=pk)


class TransactionCancelView(LoginRequiredMixin, PermissionRequiredMixin, View):
    permission_required = 'finance.cancel_transaction'

    def post(self, request, pk):
        from .models import Transaction
        from .services import DoubleEntryBookkeepingService
        
        transaction_obj = get_object_or_404(
            Transaction, 
            pk=pk, 
            school=request.user.employee.school,
            status__in=['draft', 'pending_approval', 'approved']
        )
        
        reason = request.POST.get('reason', '')
        
        try:
            DoubleEntryBookkeepingService.cancel_transaction(transaction_obj, request.user, reason)
            messages.success(request, _('Transaction cancelled successfully!'))
        except ValidationError as e:
            messages.error(request, str(e))
        
        return redirect('finance:transaction_detail', pk=pk)


class PendingApprovalsView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    template_name = 'finance/pending_approvals.html'
    context_object_name = 'transactions'
    permission_required = 'finance.approve_transaction'
    paginate_by = 20

    def get_queryset(self):
        from .models import Transaction
        if hasattr(self.request.user, 'employee'):
            return Transaction.objects.filter(
                school=self.request.user.employee.school,
                status='pending_approval'
            ).select_related('created_by').order_by('-created_at')
        return Transaction.objects.none()


class BulkTransactionApprovalView(LoginRequiredMixin, PermissionRequiredMixin, View):
    permission_required = 'finance.approve_transaction'

    def post(self, request):
        from .forms import BulkTransactionApprovalForm
        from .services import TransactionApprovalService
        
        form = BulkTransactionApprovalForm(request.POST)
        
        if form.is_valid():
            transaction_ids = [int(id) for id in form.cleaned_data['transaction_ids'].split(',')]
            action = form.cleaned_data['action']
            notes = form.cleaned_data.get('notes', '')
            
            try:
                results = TransactionApprovalService.bulk_approve_transactions(
                    transaction_ids, request.user, notes
                )
                
                if results['approved']:
                    messages.success(
                        request, 
                        _('Successfully processed {} transactions').format(len(results['approved']))
                    )
                
                if results['failed']:
                    messages.warning(
                        request,
                        _('Failed to process {} transactions').format(len(results['failed']))
                    )
                    
            except Exception as e:
                messages.error(request, str(e))
        else:
            messages.error(request, _('Invalid form data'))
        
        return redirect('finance:pending_approvals')


class JournalEntryInterfaceView(LoginRequiredMixin, TemplateView):
    template_name = 'finance/journal_entry_interface.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            from datetime import date
            today = date.today()
            
            context['accounts'] = Account.objects.filter(
                school=school,
                is_header=False,
                is_active=True,
                allow_manual_entries=True
            ).select_related('account_type').order_by('code')
            
            context['cost_centers'] = CostCenter.objects.filter(
                school=school,
                is_active=True
            ).order_by('code')
            
            # Statistics for dashboard
            context['today'] = today
            context['today_transactions'] = Transaction.objects.filter(
                school=school,
                transaction_date=today
            ).count()
            
            context['pending_approval'] = Transaction.objects.filter(
                school=school,
                status='pending_approval'
            ).count()
            
            context['draft_transactions'] = Transaction.objects.filter(
                school=school,
                status='draft'
            ).count()
            
            context['posted_today'] = Transaction.objects.filter(
                school=school,
                status='posted',
                posted_at__date=today
            ).count()
            
            # Recent transactions
            context['recent_transactions'] = Transaction.objects.filter(
                school=school
            ).select_related('created_by').order_by('-created_at')[:10]
        
        return context

    def post(self, request):
        """Handle journal entry creation"""
        from .services import DoubleEntryBookkeepingService
        
        try:
            # Extract journal entries data
            entries_data = self._extract_journal_entries_data(request)
            
            if not entries_data:
                messages.error(request, _('No valid entries provided'))
                return redirect('finance:journal_entry_interface')
            
            # Validate entries are balanced
            total_debits = sum(entry['debit_amount'] for entry in entries_data)
            total_credits = sum(entry['credit_amount'] for entry in entries_data)
            
            if abs(total_debits - total_credits) > 0.01:
                messages.error(request, _('Journal entries must be balanced'))
                return redirect('finance:journal_entry_interface')
            
            # Create transaction with entries
            school = request.user.employee.school
            transaction_data = {
                'transaction_date': request.POST.get('transaction_date'),
                'transaction_type': 'journal',
                'description': request.POST.get('description', 'Journal Entry'),
                'reference': request.POST.get('reference', ''),
                'total_amount': total_debits,
                'requires_approval': request.POST.get('requires_approval') == 'on'
            }
            
            transaction_obj = DoubleEntryBookkeepingService.create_transaction(
                school, request.user, transaction_data, entries_data
            )
            
            messages.success(request, _('Journal entries created successfully!'))
            return redirect('finance:transaction_detail', pk=transaction_obj.pk)
            
        except ValidationError as e:
            messages.error(request, str(e))
        except Exception as e:
            messages.error(request, _('Error creating journal entries: {}').format(str(e)))
        
        return redirect('finance:journal_entry_interface')

    def _extract_journal_entries_data(self, request):
        """Extract journal entries data from POST"""
        entries_data = []
        entry_count = 0
        
        while f'account_{entry_count}' in request.POST:
            account_id = request.POST.get(f'account_{entry_count}')
            description = request.POST.get(f'description_{entry_count}', '')
            debit_amount = request.POST.get(f'debit_{entry_count}', '0')
            credit_amount = request.POST.get(f'credit_{entry_count}', '0')
            cost_center_id = request.POST.get(f'cost_center_{entry_count}')
            reference = request.POST.get(f'reference_{entry_count}', '')
            
            if account_id:
                try:
                    debit_val = Decimal(debit_amount or '0')
                    credit_val = Decimal(credit_amount or '0')
                    
                    if debit_val > 0 or credit_val > 0:
                        entries_data.append({
                            'account_id': int(account_id),
                            'description': description,
                            'debit_amount': debit_val,
                            'credit_amount': credit_val,
                            'cost_center_id': int(cost_center_id) if cost_center_id else None,
                            'reference': reference
                        })
                except (ValueError, TypeError):
                    pass  # Skip invalid entries
            
            entry_count += 1
        
        return entries_data

# Double-Entry Bookkeeping Views

class TransactionListView(LoginRequiredMixin, ListView):
    """List all transactions with filtering and search"""
    model = Transaction
    template_name = 'finance/transactions/list.html'
    context_object_name = 'transactions'
    paginate_by = 25

    def get_queryset(self):
        if hasattr(self.request.user, 'employee'):
            queryset = Transaction.objects.filter(
                school=self.request.user.employee.school
            ).select_related('created_by', 'approved_by', 'posted_by').order_by('-transaction_date', '-created_at')
        else:
            queryset = Transaction.objects.none()
        
        # Apply search filters
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        if school:
            form = TransactionSearchForm(self.request.GET, school=school)
        if form.is_valid():
            if form.cleaned_data.get('transaction_id'):
                queryset = queryset.filter(
                    transaction_id__icontains=form.cleaned_data['transaction_id']
                )
            
            if form.cleaned_data.get('date_from'):
                queryset = queryset.filter(
                    transaction_date__gte=form.cleaned_data['date_from']
                )
            
            if form.cleaned_data.get('date_to'):
                queryset = queryset.filter(
                    transaction_date__lte=form.cleaned_data['date_to']
                )
            
            if form.cleaned_data.get('transaction_type'):
                queryset = queryset.filter(
                    transaction_type=form.cleaned_data['transaction_type']
                )
            
            if form.cleaned_data.get('status'):
                queryset = queryset.filter(
                    status=form.cleaned_data['status']
                )
            
            if form.cleaned_data.get('account'):
                queryset = queryset.filter(
                    entries__account=form.cleaned_data['account']
                ).distinct()
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        context['search_form'] = TransactionSearchForm(
            self.request.GET, 
            school=school
        )
        
        # Summary statistics
        queryset = self.get_queryset()
        context['total_transactions'] = queryset.count()
        context['pending_approval'] = queryset.filter(status='pending_approval').count()
        context['draft_transactions'] = queryset.filter(status='draft').count()
        context['posted_transactions'] = queryset.filter(status='posted').count()
        
        return context


class TransactionDetailView(LoginRequiredMixin, DetailView):
    """View transaction details with entries and audit trail"""
    model = Transaction
    template_name = 'finance/transactions/detail.html'
    context_object_name = 'transaction'

    def get_queryset(self):
        if hasattr(self.request.user, 'employee'):
            return Transaction.objects.filter(
                school=self.request.user.employee.school
            ).select_related('created_by', 'approved_by', 'posted_by')
        return Transaction.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        transaction = self.get_object()
        
        # Get transaction entries
        context['entries'] = transaction.entries.select_related(
            'account', 'account__account_type', 'cost_center'
        ).order_by('id')
        
        # Get audit trail
        from .services import DoubleEntryBookkeepingService
        context['audit_trail'] = DoubleEntryBookkeepingService.get_transaction_audit_trail(transaction)
        
        # Check if user can approve/post
        context['can_approve'] = (
            transaction.status == 'pending_approval' and 
            self.request.user.has_perm('finance.approve_transaction')
        )
        context['can_post'] = (
            transaction.status in ['approved', 'draft'] and 
            self.request.user.has_perm('finance.post_transaction')
        )
        
        return context


class TransactionCreateView(LoginRequiredMixin, CreateView):
    """Create new transaction with entries"""
    model = Transaction
    form_class = TransactionForm
    template_name = 'finance/transactions/create.html'

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        if hasattr(self.request.user, 'employee'):
            kwargs['school'] = self.request.user.employee.school
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if self.request.POST:
            context['entry_formset'] = TransactionEntryInlineFormSet(
                self.request.POST, 
                instance=self.object,
                form_kwargs={'school': school}
            )
        else:
            context['entry_formset'] = TransactionEntryInlineFormSet(
                instance=self.object,
                form_kwargs={'school': school}
            )
        
        return context

    def form_valid(self, form):
        context = self.get_context_data()
        entry_formset = context['entry_formset']
        
        if entry_formset.is_valid():
            # Set school and user
            if hasattr(self.request.user, 'employee'):
                form.instance.school = self.request.user.employee.school
            form.instance.created_by = self.request.user
            
            # Save transaction
            self.object = form.save()
            
            # Save entries
            entry_formset.instance = self.object
            entries = entry_formset.save(commit=False)
            
            for entry in entries:
                if hasattr(self.request.user, 'employee'):
                    entry.school = self.request.user.employee.school
                entry.created_by = self.request.user
                entry.entry_date = self.object.transaction_date
                entry.save()
            
            # Delete removed entries
            for entry in entry_formset.deleted_objects:
                entry.delete()
            
            # Create audit log
            self.object.create_audit_log('created', self.request.user)
            
            messages.success(
                self.request, 
                _('Transaction {} created successfully.').format(self.object.transaction_id)
            )
            return redirect('finance:transaction_detail', pk=self.object.pk)
        else:
            return self.form_invalid(form)

    def form_invalid(self, form):
        messages.error(self.request, _('Please correct the errors below.'))
        return super().form_invalid(form)


class TransactionUpdateView(LoginRequiredMixin, UpdateView):
    """Update transaction (only if not posted)"""
    model = Transaction
    form_class = TransactionForm
    template_name = 'finance/transactions/update.html'

    def get_queryset(self):
        if hasattr(self.request.user, 'employee'):
            return Transaction.objects.filter(
                school=self.request.user.employee.school,
                status__in=['draft', 'pending_approval']  # Only allow editing of non-posted transactions
            )
        return Transaction.objects.none()

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        if hasattr(self.request.user, 'employee'):
            kwargs['school'] = self.request.user.employee.school
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if self.request.POST:
            context['entry_formset'] = TransactionEntryInlineFormSet(
                self.request.POST, 
                instance=self.object,
                form_kwargs={'school': school}
            )
        else:
            context['entry_formset'] = TransactionEntryInlineFormSet(
                instance=self.object,
                form_kwargs={'school': school}
            )
        
        return context

    def form_valid(self, form):
        context = self.get_context_data()
        entry_formset = context['entry_formset']
        
        if entry_formset.is_valid():
            # Set modified by
            form.instance.modified_by = self.request.user
            
            # Save transaction
            self.object = form.save()
            
            # Save entries
            entry_formset.instance = self.object
            entries = entry_formset.save(commit=False)
            
            for entry in entries:
                if not entry.pk:  # New entry
                    if hasattr(self.request.user, 'employee'):
                        entry.school = self.request.user.employee.school
                    entry.created_by = self.request.user
                entry.entry_date = self.object.transaction_date
                entry.save()
            
            # Delete removed entries
            for entry in entry_formset.deleted_objects:
                entry.delete()
            
            # Create audit log
            self.object.create_audit_log('modified', self.request.user)
            
            messages.success(
                self.request, 
                _('Transaction {} updated successfully.').format(self.object.transaction_id)
            )
            return redirect('finance:transaction_detail', pk=self.object.pk)
        else:
            return self.form_invalid(form)


class TransactionApprovalView(LoginRequiredMixin, PermissionRequiredMixin, View):
    """Approve or reject transactions"""
    permission_required = 'finance.approve_transaction'

    def get(self, request, pk):
        school = request.user.employee.school if hasattr(request.user, 'employee') else None
        transaction = get_object_or_404(
            Transaction,
            pk=pk,
            school=school,
            status='pending_approval'
        )
        
        form = TransactionApprovalForm()
        
        return render(request, 'finance/transactions/approve.html', {
            'transaction': transaction,
            'form': form,
            'entries': transaction.entries.select_related('account', 'cost_center')
        })

    def post(self, request, pk):
        school = request.user.employee.school if hasattr(request.user, 'employee') else None
        transaction = get_object_or_404(
            Transaction,
            pk=pk,
            school=school,
            status='pending_approval'
        )
        
        form = TransactionApprovalForm(request.POST)
        
        if form.is_valid():
            action = form.cleaned_data['action']
            notes = form.cleaned_data['approval_notes']
            
            try:
                if action == 'approve':
                    from .services import DoubleEntryBookkeepingService
                    DoubleEntryBookkeepingService.approve_transaction(transaction, request.user, notes)
                    messages.success(
                        request, 
                        _('Transaction {} approved successfully.').format(transaction.transaction_id)
                    )
                else:  # reject
                    transaction.cancel(request.user, notes)
                    messages.success(
                        request, 
                        _('Transaction {} rejected.').format(transaction.transaction_id)
                    )
                
                return redirect('finance:transaction_detail', pk=transaction.pk)
                
            except ValidationError as e:
                messages.error(request, str(e))
        
        return render(request, 'finance/transactions/approve.html', {
            'transaction': transaction,
            'form': form,
            'entries': transaction.entries.select_related('account', 'cost_center')
        })


class TransactionPostView(LoginRequiredMixin, PermissionRequiredMixin, View):
    """Post approved transactions"""
    permission_required = 'finance.post_transaction'

    def post(self, request, pk):
        school = request.user.employee.school if hasattr(request.user, 'employee') else None
        transaction = get_object_or_404(
            Transaction,
            pk=pk,
            school=school,
            status__in=['approved', 'draft']
        )
        
        try:
            from .services import DoubleEntryBookkeepingService
            DoubleEntryBookkeepingService.post_transaction(transaction, request.user)
            
            messages.success(
                request, 
                _('Transaction {} posted successfully.').format(transaction.transaction_id)
            )
        except ValidationError as e:
            messages.error(request, str(e))
        
        return redirect('finance:transaction_detail', pk=transaction.pk)


class JournalEntryInterfaceView(LoginRequiredMixin, TemplateView):
    """Modern journal entry interface for double-entry bookkeeping"""
    template_name = 'finance/journal/entry_interface.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        
        if school:
            # Get accounts for dropdown
            context['accounts'] = Account.objects.filter(
                school=school,
                is_header=False,
                is_active=True,
                allow_manual_entries=True
            ).select_related('account_type').order_by('code')
            
            # Get cost centers
            context['cost_centers'] = CostCenter.objects.filter(
                school=school,
                is_active=True
            ).order_by('code')
            
            # Transaction form
            context['transaction_form'] = DoubleEntryTransactionForm(
                school=school
            )
        
        return context

    def post(self, request):
        """Handle journal entry creation via AJAX"""
        import json
        
        try:
            data = json.loads(request.body)
            
            # Extract transaction data
            transaction_data = {
                'transaction_date': datetime.strptime(data['transaction_date'], '%Y-%m-%d').date(),
                'transaction_type': data.get('transaction_type', 'manual'),
                'description': data['description'],
                'reference': data.get('reference', ''),
                'total_amount': Decimal(str(data['total_amount'])),
                'requires_approval': data.get('requires_approval', False)
            }
            
            # Extract entries data
            entries_data = []
            for entry in data['entries']:
                entries_data.append({
                    'account_id': entry['account_id'],
                    'description': entry.get('description', transaction_data['description']),
                    'debit_amount': Decimal(str(entry.get('debit_amount', 0))),
                    'credit_amount': Decimal(str(entry.get('credit_amount', 0))),
                    'cost_center_id': entry.get('cost_center_id'),
                    'reference': entry.get('reference', '')
                })
            
            # Create transaction using service
            from .services import DoubleEntryBookkeepingService
            school = request.user.employee.school if hasattr(request.user, 'employee') else None
            if school:
                transaction = DoubleEntryBookkeepingService.create_transaction(
                    school,
                    request.user,
                    transaction_data,
                    entries_data
            )
            
            return JsonResponse({
                'success': True,
                'transaction_id': transaction.transaction_id,
                'message': _('Transaction created successfully'),
                'redirect_url': f'/finance/transactions/{transaction.pk}/'
            })
            
        except ValidationError as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=400)
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': _('An error occurred while creating the transaction')
            }, status=500)


class AccountLedgerView(LoginRequiredMixin, DetailView):
    """View account ledger with all transactions"""
    model = Account
    template_name = 'finance/account_ledger.html'
    context_object_name = 'account'

    def get_queryset(self):
        if hasattr(self.request.user, 'employee'):
            return Account.objects.filter(
                school=self.request.user.employee.school,
                is_active=True
            )
        return Account.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        account = self.get_object()
        
        # Get date range from request
        from datetime import date, timedelta
        end_date = date.today()
        start_date = end_date - timedelta(days=365)  # Default to last year
        
        if self.request.GET.get('start_date'):
            start_date = datetime.strptime(self.request.GET['start_date'], '%Y-%m-%d').date()
        if self.request.GET.get('end_date'):
            end_date = datetime.strptime(self.request.GET['end_date'], '%Y-%m-%d').date()
        
        # Get ledger data
        from .services import DoubleEntryBookkeepingService
        context['ledger_data'] = DoubleEntryBookkeepingService.get_account_ledger(
            account, start_date, end_date
        )
        
        context['start_date'] = start_date
        context['end_date'] = end_date
        
        return context


class TrialBalanceView(LoginRequiredMixin, TemplateView):
    """Generate trial balance report"""
    template_name = 'finance/reports/trial_balance.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get date from request or use today
        from datetime import date
        as_of_date = date.today()
        
        if self.request.GET.get('as_of_date'):
            as_of_date = datetime.strptime(self.request.GET['as_of_date'], '%Y-%m-%d').date()
        
        # Generate trial balance
        from .services import ChartOfAccountsService
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        if school:
            context['trial_balance'] = ChartOfAccountsService.generate_account_report(
                school,
                'trial_balance',
                as_of_date
            )
        
        context['as_of_date'] = as_of_date
        
        return context


class TransactionAuditLogView(LoginRequiredMixin, ListView):
    """View transaction audit logs"""
    model = TransactionAuditLog
    template_name = 'finance/audit_logs.html'
    context_object_name = 'audit_logs'
    paginate_by = 50

    def get_queryset(self):
        if hasattr(self.request.user, 'employee'):
            queryset = TransactionAuditLog.objects.filter(
                school=self.request.user.employee.school
            ).select_related('transaction', 'user').order_by('-timestamp')
            
            # Apply filters
            transaction_id = self.request.GET.get('transaction_id')
            action = self.request.GET.get('action')
            user = self.request.GET.get('user')
            date_from = self.request.GET.get('date_from')
            date_to = self.request.GET.get('date_to')
            
            if transaction_id:
                queryset = queryset.filter(transaction__transaction_id__icontains=transaction_id)
            if action:
                queryset = queryset.filter(action=action)
            if user:
                queryset = queryset.filter(user__username__icontains=user)
            if date_from:
                queryset = queryset.filter(timestamp__date__gte=date_from)
            if date_to:
                queryset = queryset.filter(timestamp__date__lte=date_to)
            
            return queryset
        return TransactionAuditLog.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Summary statistics
        all_logs = TransactionAuditLog.objects.filter(
            school=self.request.user.employee.school
        ) if hasattr(self.request.user, 'employee') else TransactionAuditLog.objects.none()
        
        context['total_logs'] = all_logs.count()
        context['created_count'] = all_logs.filter(action='created').count()
        context['approved_count'] = all_logs.filter(action='approved').count()
        context['posted_count'] = all_logs.filter(action='posted').count()
        context['modified_count'] = all_logs.filter(action='modified').count()
        context['cancelled_count'] = all_logs.filter(action='cancelled').count()
        
        return context

# Budget Management Views

class BudgetListView(LoginRequiredMixin, ListView):
    """List all budgets"""
    model = Budget
    template_name = 'finance/budget/list.html'
    context_object_name = 'budgets'
    paginate_by = 20

    def get_queryset(self):
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        if not school:
            return Budget.objects.none()
        
        queryset = Budget.objects.filter(school=school).select_related(
            'financial_year', 'approved_by'
        ).prefetch_related('items')
        
        # Apply filters
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
        
        budget_type = self.request.GET.get('budget_type')
        if budget_type:
            queryset = queryset.filter(budget_type=budget_type)
        
        return queryset.order_by('-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_choices'] = Budget.BUDGET_STATUS
        context['type_choices'] = Budget.BUDGET_TYPES
        context['current_status'] = self.request.GET.get('status', '')
        context['current_type'] = self.request.GET.get('budget_type', '')
        return context


class BudgetCreateView(LoginRequiredMixin, CreateView):
    """Create a new budget"""
    model = Budget
    form_class = BudgetForm
    template_name = 'finance/budget/create.html'
    success_url = reverse_lazy('finance:budget_list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['school'] = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        return kwargs

    def form_valid(self, form):
        from .budget_services import BudgetService
        
        try:
            school = self.request.user.employee.school
            budget = BudgetService.create_budget(
                school=school,
                name=form.cleaned_data['name'],
                budget_type=form.cleaned_data['budget_type'],
                financial_year=form.cleaned_data['financial_year'],
                start_date=form.cleaned_data['start_date'],
                end_date=form.cleaned_data['end_date'],
                description=form.cleaned_data.get('description'),
                name_ar=form.cleaned_data.get('name_ar'),
                created_by=self.request.user
            )
            
            messages.success(self.request, f'Budget "{budget.name}" created successfully.')
            return redirect('finance:budget_detail', pk=budget.pk)
            
        except ValidationError as e:
            messages.error(self.request, str(e))
            return self.form_invalid(form)
        except Exception as e:
            messages.error(self.request, f'Error creating budget: {str(e)}')
            return self.form_invalid(form)


class BudgetDetailView(LoginRequiredMixin, DetailView):
    """Budget detail view with items"""
    model = Budget
    template_name = 'finance/budget/detail.html'
    context_object_name = 'budget'

    def get_queryset(self):
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        if not school:
            return Budget.objects.none()
        
        return Budget.objects.filter(school=school).select_related(
            'financial_year', 'approved_by'
        ).prefetch_related(
            'items__account',
            'items__cost_center'
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        budget = self.get_object()
        
        # Calculate budget metrics
        context['total_allocated'] = budget.get_total_allocated()
        context['total_spent'] = budget.get_total_spent()
        context['variance'] = budget.get_variance()
        context['utilization'] = budget.get_utilization_percentage()
        
        # Budget items with analysis
        items_data = []
        for item in budget.items.all():
            items_data.append({
                'item': item,
                'variance': item.variance,
                'utilization': item.utilization_percentage,
                'status': 'over_budget' if item.variance < 0 else 'on_track'
            })
        
        context['items_data'] = items_data
        context['can_edit'] = budget.status in ['draft']
        context['can_approve'] = (
            budget.status == 'pending_approval' and 
            self.request.user.has_perm('finance.approve_budget')
        )
        
        return context


class BudgetUpdateView(LoginRequiredMixin, UpdateView):
    """Update budget"""
    model = Budget
    form_class = BudgetForm
    template_name = 'finance/budget/update.html'

    def get_queryset(self):
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        if not school:
            return Budget.objects.none()
        
        return Budget.objects.filter(school=school, status='draft')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['school'] = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        return kwargs

    def get_success_url(self):
        return reverse('finance:budget_detail', kwargs={'pk': self.object.pk})


class BudgetItemCreateView(LoginRequiredMixin, CreateView):
    """Add items to budget"""
    model = BudgetItem
    form_class = BudgetItemForm
    template_name = 'finance/budget/add_item.html'

    def dispatch(self, request, *args, **kwargs):
        self.budget = get_object_or_404(
            Budget,
            pk=kwargs['budget_pk'],
            school=request.user.employee.school if hasattr(request.user, 'employee') else None,
            status='draft'
        )
        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['school'] = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        return kwargs

    def form_valid(self, form):
        form.instance.budget = self.budget
        form.instance.created_by = self.request.user
        
        try:
            response = super().form_valid(form)
            
            # Update budget total
            self.budget.total_budget = self.budget.get_total_allocated()
            self.budget.save(update_fields=['total_budget'])
            
            messages.success(self.request, 'Budget item added successfully.')
            return response
            
        except Exception as e:
            messages.error(self.request, f'Error adding budget item: {str(e)}')
            return self.form_invalid(form)

    def get_success_url(self):
        return reverse('finance:budget_detail', kwargs={'pk': self.budget.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['budget'] = self.budget
        return context


class BudgetApprovalView(LoginRequiredMixin, FormView):
    """Budget approval view"""
    form_class = BudgetApprovalForm
    template_name = 'finance/budget/approval.html'

    def dispatch(self, request, *args, **kwargs):
        if not request.user.has_perm('finance.approve_budget'):
            messages.error(request, 'You do not have permission to approve budgets.')
            return redirect('finance:budget_list')
        
        self.budget = get_object_or_404(
            Budget,
            pk=kwargs['pk'],
            school=request.user.employee.school if hasattr(request.user, 'employee') else None,
            status='pending_approval'
        )
        return super().dispatch(request, *args, **kwargs)

    def form_valid(self, form):
        from .budget_services import BudgetService
        
        action = form.cleaned_data['action']
        comments = form.cleaned_data.get('comments', '')
        
        try:
            if action == 'approve':
                BudgetService.approve_budget(self.budget, self.request.user, comments)
                messages.success(self.request, f'Budget "{self.budget.name}" approved successfully.')
            
            elif action == 'reject':
                BudgetService.reject_budget(self.budget, self.request.user, comments)
                messages.success(self.request, f'Budget "{self.budget.name}" rejected.')
            
            return redirect('finance:budget_detail', pk=self.budget.pk)
            
        except Exception as e:
            messages.error(self.request, f'Error processing approval: {str(e)}')
            return self.form_invalid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['budget'] = self.budget
        context['total_allocated'] = self.budget.get_total_allocated()
        context['items_count'] = self.budget.items.count()
        return context


class BudgetSubmitApprovalView(LoginRequiredMixin, View):
    """Submit budget for approval"""
    
    def post(self, request, pk):
        from .budget_services import BudgetService
        
        budget = get_object_or_404(
            Budget,
            pk=pk,
            school=request.user.employee.school if hasattr(request.user, 'employee') else None,
            status='draft'
        )
        
        try:
            BudgetService.submit_for_approval(budget, request.user)
            messages.success(request, f'Budget "{budget.name}" submitted for approval.')
        except ValidationError as e:
            messages.error(request, str(e))
        except Exception as e:
            messages.error(request, f'Error submitting budget: {str(e)}')
        
        return redirect('finance:budget_detail', pk=budget.pk)


class BudgetMonitoringView(LoginRequiredMixin, TemplateView):
    """Budget monitoring dashboard"""
    template_name = 'finance/budget/monitoring.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        if not school:
            return context
        
        from .budget_services import BudgetReportingService, BudgetMonitoringService
        
        try:
            # Get dashboard data
            context['dashboard_data'] = BudgetReportingService.generate_budget_performance_dashboard(school)
            
            # Get filter form
            filter_form = BudgetMonitoringFilterForm(self.request.GET, school=school)
            context['filter_form'] = filter_form
            
            # Apply filters and get detailed budget data
            budgets_query = Budget.objects.filter(
                school=school,
                status__in=['active', 'approved']
            ).select_related('financial_year').prefetch_related('items')
            
            if filter_form.is_valid():
                if filter_form.cleaned_data.get('budget'):
                    budgets_query = budgets_query.filter(id=filter_form.cleaned_data['budget'].id)
                
                if filter_form.cleaned_data.get('budget_type'):
                    budgets_query = budgets_query.filter(budget_type=filter_form.cleaned_data['budget_type'])
                
                if filter_form.cleaned_data.get('status'):
                    budgets_query = budgets_query.filter(status=filter_form.cleaned_data['status'])
            
            # Generate variance reports for filtered budgets
            budget_reports = []
            for budget in budgets_query:
                try:
                    # Update actuals first
                    BudgetMonitoringService.update_budget_actuals(budget)
                    
                    # Generate variance report
                    variance_report = BudgetMonitoringService.generate_variance_report(budget)
                    budget_reports.append(variance_report)
                except Exception as e:
                    logger.error(f"Error generating report for budget {budget.id}: {str(e)}")
            
            context['budget_reports'] = budget_reports
            
        except Exception as e:
            messages.error(self.request, f'Error loading budget monitoring data: {str(e)}')
            context['dashboard_data'] = {}
            context['budget_reports'] = []
        
        return context


class BudgetVarianceReportView(LoginRequiredMixin, DetailView):
    """Detailed budget variance report"""
    model = Budget
    template_name = 'finance/budget/variance_report.html'
    context_object_name = 'budget'

    def get_queryset(self):
        school = self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        if not school:
            return Budget.objects.none()
        
        return Budget.objects.filter(school=school)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        budget = self.get_object()
        
        from .budget_services import BudgetMonitoringService
        
        try:
            # Update actuals
            BudgetMonitoringService.update_budget_actuals(budget)
            
            # Generate detailed variance report
            context['variance_report'] = BudgetMonitoringService.generate_variance_report(budget)
            
        except Exception as e:
            messages.error(self.request, f'Error generating variance report: {str(e)}')
            context['variance_report'] = {}
        
        return context