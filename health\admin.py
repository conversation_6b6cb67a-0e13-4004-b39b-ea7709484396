from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from django.db.models import Count

from .models import (
    HealthProfile, Allergy, Medication, MedicalHistory, 
    Vaccination, HealthScreening, HealthAlert, MedicalIncident,
    IncidentTreatment, IncidentFollowUp, IncidentNotification
)


class AllergyInline(admin.TabularInline):
    model = Allergy
    extra = 0
    fields = ['allergy_type', 'allergen', 'severity', 'symptoms', 'is_active']
    readonly_fields = ['created_at']


class MedicationInline(admin.TabularInline):
    model = Medication
    extra = 0
    fields = ['medication_name', 'dosage', 'frequency', 'start_date', 'end_date', 'is_active']
    readonly_fields = ['created_at']


class HealthAlertInline(admin.TabularInline):
    model = HealthAlert
    extra = 0
    fields = ['alert_type', 'priority', 'title', 'is_active']
    readonly_fields = ['created_at']


@admin.register(HealthProfile)
class HealthProfileAdmin(admin.ModelAdmin):
    list_display = [
        'student_name', 'student_class', 'blood_type', 'bmi_display', 
        'allergies_count', 'medications_count', 'alerts_count', 'last_updated'
    ]
    list_filter = ['blood_type', 'created_at']
    search_fields = [
        'student__first_name', 'student__last_name', 'student__student_id',
        'chronic_conditions', 'emergency_contact_name'
    ]
    ordering = ['student__first_name', 'student__last_name']
    
    fieldsets = (
        ('Student Information', {
            'fields': ('student',)
        }),
        ('Basic Health Information', {
            'fields': ('blood_type', 'height', 'weight', 'last_physical_exam')
        }),
        ('Medical Conditions', {
            'fields': ('chronic_conditions', 'disabilities', 'special_needs', 'medical_notes')
        }),
        ('Emergency Contact', {
            'fields': (
                'emergency_contact_name', 'emergency_contact_relationship',
                'emergency_contact_phone', 'emergency_contact_phone2',
                'emergency_contact_address'
            )
        }),
        ('Insurance Information', {
            'fields': ('insurance_provider', 'insurance_policy_number', 'insurance_expiry_date')
        }),
        ('Family Doctor', {
            'fields': ('family_doctor_name', 'family_doctor_phone', 'family_doctor_address')
        })
    )
    
    inlines = [AllergyInline, MedicationInline, HealthAlertInline]
    
    def student_name(self, obj):
        return obj.student.get_full_name()
    student_name.short_description = 'Student'
    student_name.admin_order_field = 'student__first_name'
    
    def student_class(self, obj):
        return obj.student.class_enrolled.name if obj.student.class_enrolled else 'No Class'
    student_class.short_description = 'Class'
    
    def bmi_display(self, obj):
        bmi = obj.bmi
        if bmi:
            color = 'green'
            if bmi < 18.5 or bmi >= 25:
                color = 'orange'
            if bmi >= 30:
                color = 'red'
            return format_html(
                '<span style="color: {};">{} ({})</span>',
                color, bmi, obj.bmi_category
            )
        return 'N/A'
    bmi_display.short_description = 'BMI'
    
    def allergies_count(self, obj):
        count = obj.allergies.filter(is_active=True).count()
        if count > 0:
            url = reverse('admin:health_allergy_changelist') + f'?health_profile__id__exact={obj.id}'
            return format_html('<a href="{}">{} allergies</a>', url, count)
        return '0'
    allergies_count.short_description = 'Allergies'
    
    def medications_count(self, obj):
        count = obj.medications.filter(is_active=True).count()
        if count > 0:
            url = reverse('admin:health_medication_changelist') + f'?health_profile__id__exact={obj.id}'
            return format_html('<a href="{}">{} medications</a>', url, count)
        return '0'
    medications_count.short_description = 'Medications'
    
    def alerts_count(self, obj):
        count = obj.alerts.filter(is_active=True).count()
        if count > 0:
            color = 'red' if any(alert.priority == 'critical' for alert in obj.alerts.filter(is_active=True)) else 'orange'
            url = reverse('admin:health_healthalert_changelist') + f'?health_profile__id__exact={obj.id}'
            return format_html('<a href="{}" style="color: {};">{} alerts</a>', url, color, count)
        return '0'
    alerts_count.short_description = 'Alerts'
    
    def last_updated(self, obj):
        return obj.updated_at.strftime('%Y-%m-%d')
    last_updated.short_description = 'Last Updated'


@admin.register(Allergy)
class AllergyAdmin(admin.ModelAdmin):
    list_display = [
        'student_name', 'allergen', 'allergy_type', 'severity_badge', 
        'diagnosed_date', 'is_active'
    ]
    list_filter = ['allergy_type', 'severity', 'is_active', 'diagnosed_date']
    search_fields = [
        'health_profile__student__first_name', 'health_profile__student__last_name',
        'allergen', 'symptoms'
    ]
    ordering = ['-severity', 'allergen']
    list_editable = ['is_active']
    
    fieldsets = (
        ('Allergy Information', {
            'fields': ('health_profile', 'allergy_type', 'allergen', 'severity')
        }),
        ('Medical Details', {
            'fields': ('symptoms', 'treatment', 'diagnosed_date')
        }),
        ('Status', {
            'fields': ('is_active', 'notes')
        })
    )
    
    def student_name(self, obj):
        return obj.health_profile.student.get_full_name()
    student_name.short_description = 'Student'
    student_name.admin_order_field = 'health_profile__student__first_name'
    
    def severity_badge(self, obj):
        colors = {
            'mild': 'green',
            'moderate': 'orange',
            'severe': 'red',
            'life_threatening': 'darkred'
        }
        color = colors.get(obj.severity, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_severity_display()
        )
    severity_badge.short_description = 'Severity'


@admin.register(Medication)
class MedicationAdmin(admin.ModelAdmin):
    list_display = [
        'student_name', 'medication_name', 'dosage', 'frequency',
        'current_status', 'start_date', 'end_date', 'requires_supervision'
    ]
    list_filter = [
        'frequency', 'administration_route', 'requires_supervision',
        'can_self_administer', 'stored_at_school', 'is_active'
    ]
    search_fields = [
        'health_profile__student__first_name', 'health_profile__student__last_name',
        'medication_name', 'prescribed_by', 'reason'
    ]
    ordering = ['medication_name', '-start_date']
    
    fieldsets = (
        ('Medication Information', {
            'fields': ('health_profile', 'medication_name', 'dosage', 'frequency', 'administration_route')
        }),
        ('Schedule', {
            'fields': ('start_date', 'end_date', 'administration_times')
        }),
        ('Medical Information', {
            'fields': ('prescribed_by', 'reason', 'side_effects', 'special_instructions')
        }),
        ('School Administration', {
            'fields': (
                'can_self_administer', 'requires_supervision', 
                'stored_at_school', 'storage_location'
            )
        }),
        ('Status', {
            'fields': ('is_active',)
        })
    )
    
    def student_name(self, obj):
        return obj.health_profile.student.get_full_name()
    student_name.short_description = 'Student'
    student_name.admin_order_field = 'health_profile__student__first_name'
    
    def current_status(self, obj):
        if obj.is_current:
            return format_html('<span style="color: green;">Current</span>')
        else:
            return format_html('<span style="color: red;">Inactive</span>')
    current_status.short_description = 'Status'


@admin.register(MedicalHistory)
class MedicalHistoryAdmin(admin.ModelAdmin):
    list_display = [
        'student_name', 'title', 'record_type', 'date', 
        'healthcare_provider', 'follow_up_required', 'affects_school_activities'
    ]
    list_filter = [
        'record_type', 'date', 'follow_up_required', 
        'affects_school_activities', 'return_to_school_date'
    ]
    search_fields = [
        'health_profile__student__first_name', 'health_profile__student__last_name',
        'title', 'description', 'diagnosis', 'healthcare_provider'
    ]
    ordering = ['-date']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('health_profile', 'record_type', 'date', 'title')
        }),
        ('Medical Details', {
            'fields': ('description', 'diagnosis', 'treatment', 'medications_prescribed')
        }),
        ('Healthcare Provider', {
            'fields': ('healthcare_provider', 'facility')
        }),
        ('Follow-up', {
            'fields': ('follow_up_required', 'follow_up_date')
        }),
        ('School Impact', {
            'fields': ('affects_school_activities', 'activity_restrictions', 'return_to_school_date')
        }),
        ('Documentation', {
            'fields': ('documents', 'notes')
        })
    )
    
    def student_name(self, obj):
        return obj.health_profile.student.get_full_name()
    student_name.short_description = 'Student'
    student_name.admin_order_field = 'health_profile__student__first_name'


@admin.register(Vaccination)
class VaccinationAdmin(admin.ModelAdmin):
    list_display = [
        'student_name', 'vaccine_display', 'dose_info', 'date_administered',
        'administered_by', 'series_status', 'next_due'
    ]
    list_filter = [
        'vaccine_name', 'date_administered', 'dose_number', 
        'next_dose_due', 'administered_by'
    ]
    search_fields = [
        'health_profile__student__first_name', 'health_profile__student__last_name',
        'vaccine_brand', 'administered_by', 'facility'
    ]
    ordering = ['-date_administered']
    
    fieldsets = (
        ('Vaccination Information', {
            'fields': ('health_profile', 'vaccine_name', 'vaccine_brand', 'date_administered')
        }),
        ('Dose Information', {
            'fields': ('dose_number', 'total_doses_required', 'next_dose_due')
        }),
        ('Administration Details', {
            'fields': ('administered_by', 'facility', 'batch_number', 'expiry_date')
        }),
        ('Reactions and Notes', {
            'fields': ('adverse_reactions', 'notes')
        }),
        ('Documentation', {
            'fields': ('certificate',)
        })
    )
    
    def student_name(self, obj):
        return obj.health_profile.student.get_full_name()
    student_name.short_description = 'Student'
    student_name.admin_order_field = 'health_profile__student__first_name'
    
    def vaccine_display(self, obj):
        return obj.get_vaccine_name_display()
    vaccine_display.short_description = 'Vaccine'
    
    def dose_info(self, obj):
        return f"Dose {obj.dose_number} of {obj.total_doses_required}"
    dose_info.short_description = 'Dose'
    
    def series_status(self, obj):
        if obj.is_series_complete:
            return format_html('<span style="color: green;">Complete</span>')
        else:
            return format_html('<span style="color: orange;">Incomplete</span>')
    series_status.short_description = 'Series Status'
    
    def next_due(self, obj):
        if obj.next_dose_due:
            if obj.next_dose_overdue:
                return format_html('<span style="color: red;">{} (Overdue)</span>', obj.next_dose_due)
            else:
                return obj.next_dose_due
        return 'N/A'
    next_due.short_description = 'Next Due'


@admin.register(HealthScreening)
class HealthScreeningAdmin(admin.ModelAdmin):
    list_display = [
        'student_name', 'screening_type', 'date', 'result_badge',
        'screened_by', 'referral_needed', 'parent_notified'
    ]
    list_filter = [
        'screening_type', 'result', 'date', 'referral_needed',
        'parent_notified', 'follow_up_completed'
    ]
    search_fields = [
        'health_profile__student__first_name', 'health_profile__student__last_name',
        'screened_by', 'findings', 'recommendations'
    ]
    ordering = ['-date']
    
    fieldsets = (
        ('Screening Information', {
            'fields': ('health_profile', 'screening_type', 'date', 'screened_by')
        }),
        ('Results', {
            'fields': ('result', 'measurements', 'findings', 'recommendations')
        }),
        ('Referral', {
            'fields': ('referral_needed', 'referral_to')
        }),
        ('Follow-up', {
            'fields': ('follow_up_completed', 'follow_up_date', 'follow_up_results')
        }),
        ('Parent Notification', {
            'fields': ('parent_notified', 'parent_notification_date', 'parent_notification_method')
        }),
        ('Notes', {
            'fields': ('notes',)
        })
    )
    
    def student_name(self, obj):
        return obj.health_profile.student.get_full_name()
    student_name.short_description = 'Student'
    student_name.admin_order_field = 'health_profile__student__first_name'
    
    def result_badge(self, obj):
        colors = {
            'pass': 'green',
            'refer': 'orange',
            'fail': 'red',
            'incomplete': 'gray'
        }
        color = colors.get(obj.result, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_result_display()
        )
    result_badge.short_description = 'Result'


@admin.register(HealthAlert)
class HealthAlertAdmin(admin.ModelAdmin):
    list_display = [
        'student_name', 'title', 'alert_type', 'priority_badge',
        'is_current', 'show_on_dashboard', 'notify_teachers', 'created_at'
    ]
    list_filter = [
        'alert_type', 'priority', 'is_active', 'show_on_dashboard',
        'notify_teachers', 'notify_staff', 'created_at'
    ]
    search_fields = [
        'health_profile__student__first_name', 'health_profile__student__last_name',
        'title', 'description', 'action_required'
    ]
    ordering = ['-priority', '-created_at']
    list_editable = ['show_on_dashboard']
    
    fieldsets = (
        ('Alert Information', {
            'fields': ('health_profile', 'alert_type', 'priority', 'title', 'description')
        }),
        ('Notification Settings', {
            'fields': ('show_on_dashboard', 'notify_teachers', 'notify_staff')
        }),
        ('Validity Period', {
            'fields': ('is_active', 'start_date', 'end_date')
        }),
        ('Action Instructions', {
            'fields': ('action_required', 'emergency_instructions')
        }),
        ('Notes', {
            'fields': ('notes',)
        })
    )
    
    def student_name(self, obj):
        return obj.health_profile.student.get_full_name()
    student_name.short_description = 'Student'
    student_name.admin_order_field = 'health_profile__student__first_name'
    
    def priority_badge(self, obj):
        colors = {
            'low': 'green',
            'medium': 'orange',
            'high': 'red',
            'critical': 'darkred'
        }
        color = colors.get(obj.priority, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_priority_display()
        )
    priority_badge.short_description = 'Priority'
    
    def is_current(self, obj):
        if obj.is_current:
            return format_html('<span style="color: green;">✓ Active</span>')
        else:
            return format_html('<span style="color: red;">✗ Inactive</span>')
    is_current.short_description = 'Current Status'


class IncidentTreatmentInline(admin.TabularInline):
    model = IncidentTreatment
    extra = 0
    fields = ['treatment_type', 'treatment_time', 'administered_by', 'treatment_description', 'effectiveness']
    readonly_fields = ['created_at']


class IncidentFollowUpInline(admin.TabularInline):
    model = IncidentFollowUp
    extra = 0
    fields = ['follow_up_type', 'status', 'scheduled_date', 'assigned_to', 'priority']
    readonly_fields = ['created_at']


@admin.register(MedicalIncident)
class MedicalIncidentAdmin(admin.ModelAdmin):
    list_display = [
        'incident_id', 'student_name', 'incident_type', 'severity_badge',
        'status_badge', 'incident_date', 'location', 'reported_by'
    ]
    list_filter = [
        'incident_type', 'severity', 'status', 'incident_date',
        'emergency_services_called', 'transported_to_hospital', 'parent_notified'
    ]
    search_fields = [
        'incident_id', 'health_profile__student__first_name', 
        'health_profile__student__last_name', 'description', 'location'
    ]
    ordering = ['-incident_date', '-incident_time']
    readonly_fields = ['incident_id']
    
    fieldsets = (
        ('Incident Information', {
            'fields': ('incident_id', 'health_profile', 'incident_type', 'severity', 'status')
        }),
        ('When and Where', {
            'fields': ('incident_date', 'incident_time', 'location')
        }),
        ('Description', {
            'fields': ('description', 'symptoms_observed')
        }),
        ('People Involved', {
            'fields': ('reported_by', 'witnessed_by')
        }),
        ('Initial Response', {
            'fields': ('immediate_action_taken', 'first_aid_given', 'first_aid_details')
        }),
        ('External Care', {
            'fields': (
                'emergency_services_called', 'emergency_service_details',
                'transported_to_hospital', 'hospital_details'
            )
        }),
        ('Parent Notification', {
            'fields': (
                'parent_notified', 'parent_notification_time',
                'parent_notification_method', 'parent_response'
            )
        }),
        ('Follow-up', {
            'fields': (
                'follow_up_required', 'follow_up_instructions',
                'return_to_class_time', 'activity_restrictions'
            )
        }),
        ('Documentation', {
            'fields': ('photos_taken', 'incident_report_file')
        }),
        ('Resolution', {
            'fields': ('resolved_date', 'resolution_notes', 'notes')
        })
    )
    
    inlines = [IncidentTreatmentInline, IncidentFollowUpInline]
    
    def student_name(self, obj):
        return obj.health_profile.student.get_full_name()
    student_name.short_description = 'Student'
    student_name.admin_order_field = 'health_profile__student__first_name'
    
    def severity_badge(self, obj):
        colors = {
            'minor': 'green',
            'moderate': 'orange',
            'serious': 'red',
            'critical': 'darkred'
        }
        color = colors.get(obj.severity, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_severity_display()
        )
    severity_badge.short_description = 'Severity'
    
    def status_badge(self, obj):
        colors = {
            'reported': 'orange',
            'in_treatment': 'blue',
            'resolved': 'green',
            'referred': 'purple',
            'follow_up_required': 'red'
        }
        color = colors.get(obj.status, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_status_display()
        )
    status_badge.short_description = 'Status'


@admin.register(IncidentTreatment)
class IncidentTreatmentAdmin(admin.ModelAdmin):
    list_display = [
        'incident_id', 'student_name', 'treatment_type', 'treatment_time',
        'administered_by', 'effectiveness'
    ]
    list_filter = ['treatment_type', 'treatment_time', 'effectiveness', 'administered_by']
    search_fields = [
        'incident__incident_id', 'incident__health_profile__student__first_name',
        'incident__health_profile__student__last_name', 'treatment_description'
    ]
    ordering = ['-treatment_time']
    
    fieldsets = (
        ('Treatment Information', {
            'fields': ('incident', 'treatment_type', 'treatment_time', 'administered_by')
        }),
        ('Treatment Details', {
            'fields': ('treatment_description', 'medication_given', 'supplies_used')
        }),
        ('Response', {
            'fields': ('student_response', 'effectiveness')
        }),
        ('Follow-up', {
            'fields': ('additional_treatment_needed', 'next_treatment_time', 'notes')
        })
    )
    
    def incident_id(self, obj):
        return obj.incident.incident_id
    incident_id.short_description = 'Incident ID'
    
    def student_name(self, obj):
        return obj.incident.health_profile.student.get_full_name()
    student_name.short_description = 'Student'


@admin.register(IncidentFollowUp)
class IncidentFollowUpAdmin(admin.ModelAdmin):
    list_display = [
        'incident_id', 'student_name', 'follow_up_type', 'status_badge',
        'scheduled_date', 'assigned_to', 'priority_badge'
    ]
    list_filter = ['follow_up_type', 'status', 'priority', 'scheduled_date', 'assigned_to']
    search_fields = [
        'incident__incident_id', 'incident__health_profile__student__first_name',
        'incident__health_profile__student__last_name', 'description'
    ]
    ordering = ['scheduled_date', 'scheduled_time']
    
    fieldsets = (
        ('Follow-up Information', {
            'fields': ('incident', 'follow_up_type', 'status', 'priority')
        }),
        ('Scheduling', {
            'fields': ('scheduled_date', 'scheduled_time', 'assigned_to')
        }),
        ('Description', {
            'fields': ('description',)
        }),
        ('Completion', {
            'fields': ('completed_date', 'completed_by', 'outcome')
        }),
        ('Next Steps', {
            'fields': ('additional_follow_up_needed', 'next_follow_up_date', 'notes')
        })
    )
    
    def incident_id(self, obj):
        return obj.incident.incident_id
    incident_id.short_description = 'Incident ID'
    
    def student_name(self, obj):
        return obj.incident.health_profile.student.get_full_name()
    student_name.short_description = 'Student'
    
    def status_badge(self, obj):
        colors = {
            'pending': 'orange',
            'in_progress': 'blue',
            'completed': 'green',
            'cancelled': 'red'
        }
        color = colors.get(obj.status, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_status_display()
        )
    status_badge.short_description = 'Status'
    
    def priority_badge(self, obj):
        colors = {
            'low': 'green',
            'medium': 'orange',
            'high': 'red',
            'urgent': 'darkred'
        }
        color = colors.get(obj.priority, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_priority_display()
        )
    priority_badge.short_description = 'Priority'


@admin.register(IncidentNotification)
class IncidentNotificationAdmin(admin.ModelAdmin):
    list_display = [
        'incident_id', 'student_name', 'notification_type', 'method',
        'recipient_name', 'status_badge', 'scheduled_time', 'sent_by'
    ]
    list_filter = ['notification_type', 'method', 'status', 'scheduled_time', 'sent_by']
    search_fields = [
        'incident__incident_id', 'incident__health_profile__student__first_name',
        'incident__health_profile__student__last_name', 'recipient_name', 'subject'
    ]
    ordering = ['-scheduled_time']
    
    fieldsets = (
        ('Notification Information', {
            'fields': ('incident', 'notification_type', 'method', 'status')
        }),
        ('Recipient', {
            'fields': ('recipient_name', 'recipient_relationship', 'recipient_contact')
        }),
        ('Message', {
            'fields': ('subject', 'message')
        }),
        ('Timing', {
            'fields': ('scheduled_time', 'sent_time', 'delivered_time', 'read_time')
        }),
        ('Response', {
            'fields': ('response_received', 'response_content', 'response_time')
        }),
        ('Sending Details', {
            'fields': ('sent_by', 'notes')
        })
    )
    
    def incident_id(self, obj):
        return obj.incident.incident_id
    incident_id.short_description = 'Incident ID'
    
    def student_name(self, obj):
        return obj.incident.health_profile.student.get_full_name()
    student_name.short_description = 'Student'
    
    def status_badge(self, obj):
        colors = {
            'pending': 'orange',
            'sent': 'blue',
            'delivered': 'green',
            'read': 'darkgreen',
            'failed': 'red'
        }
        color = colors.get(obj.status, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_status_display()
        )
    status_badge.short_description = 'Status'


# Custom admin site configuration
admin.site.site_header = "School ERP - Health Management"
admin.site.site_title = "Health Admin"
admin.site.index_title = "Student Health Management"
# Additional imports for health monitoring
from .models import HealthScreeningSchedule, MedicalAppointment, HealthTrendAnalysis, ComplianceMonitoring, HealthReport


@admin.register(HealthScreeningSchedule)
class HealthScreeningScheduleAdmin(admin.ModelAdmin):
    list_display = ['student_name', 'screening_type', 'title', 'scheduled_date', 'status', 'assigned_to', 'is_overdue']
    list_filter = ['screening_type', 'status', 'frequency', 'scheduled_date', 'assigned_to']
    search_fields = ['health_profile__student__first_name', 'health_profile__student__last_name', 'title', 'description']
    date_hierarchy = 'scheduled_date'
    ordering = ['scheduled_date', 'scheduled_time']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('health_profile', 'screening_type', 'title', 'description')
        }),
        ('Scheduling', {
            'fields': ('scheduled_date', 'scheduled_time', 'frequency', 'assigned_to', 'location')
        }),
        ('Requirements', {
            'fields': ('requirements', 'notes')
        }),
        ('Status', {
            'fields': ('status', 'completed_date', 'completed_by', 'results_available', 'follow_up_required', 'follow_up_notes')
        }),
        ('Next Screening', {
            'fields': ('next_screening_date',)
        }),
    )
    
    def student_name(self, obj):
        return obj.health_profile.student.get_full_name()
    student_name.short_description = 'Student'
    student_name.admin_order_field = 'health_profile__student__last_name'
    
    def is_overdue(self, obj):
        if obj.is_overdue:
            return format_html('<span style="color: red;">Yes</span>')
        return 'No'
    is_overdue.short_description = 'Overdue'
    is_overdue.boolean = True


@admin.register(MedicalAppointment)
class MedicalAppointmentAdmin(admin.ModelAdmin):
    list_display = ['student_name', 'appointment_type', 'title', 'appointment_date', 'appointment_time', 'status', 'priority', 'provider_name']
    list_filter = ['appointment_type', 'status', 'priority', 'appointment_date', 'excused_from_class', 'parent_attending']
    search_fields = ['health_profile__student__first_name', 'health_profile__student__last_name', 'title', 'provider_name', 'clinic_hospital']
    date_hierarchy = 'appointment_date'
    ordering = ['appointment_date', 'appointment_time']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('health_profile', 'appointment_type', 'title', 'description')
        }),
        ('Scheduling', {
            'fields': ('appointment_date', 'appointment_time', 'duration_minutes', 'priority')
        }),
        ('Provider Information', {
            'fields': ('provider_name', 'provider_contact', 'clinic_hospital', 'address')
        }),
        ('School Coordination', {
            'fields': ('school_coordinator', 'excused_from_class', 'transportation_arranged')
        }),
        ('Parent/Guardian', {
            'fields': ('parent_notified', 'parent_attending', 'parent_notification_sent')
        }),
        ('Status & Results', {
            'fields': ('status', 'completed_date', 'appointment_summary', 'recommendations', 'follow_up_required', 'follow_up_date')
        }),
        ('Documentation', {
            'fields': ('medical_report_file', 'prescription_file', 'notes')
        }),
    )
    
    def student_name(self, obj):
        return obj.health_profile.student.get_full_name()
    student_name.short_description = 'Student'
    student_name.admin_order_field = 'health_profile__student__last_name'


@admin.register(HealthTrendAnalysis)
class HealthTrendAnalysisAdmin(admin.ModelAdmin):
    list_display = ['student_name', 'trend_type', 'title', 'analysis_end_date', 'trend_direction', 'action_required', 'analyzed_by']
    list_filter = ['trend_type', 'trend_direction', 'action_required', 'analysis_end_date', 'analyzed_by']
    search_fields = ['health_profile__student__first_name', 'health_profile__student__last_name', 'title', 'key_findings']
    date_hierarchy = 'analysis_end_date'
    ordering = ['-analysis_end_date']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('health_profile', 'trend_type', 'title')
        }),
        ('Analysis Period', {
            'fields': ('analysis_start_date', 'analysis_end_date')
        }),
        ('Trend Data', {
            'fields': ('trend_direction', 'trend_description', 'baseline_value', 'current_value', 'change_percentage')
        }),
        ('Analysis Results', {
            'fields': ('key_findings', 'recommendations', 'action_required')
        }),
        ('Follow-up', {
            'fields': ('next_analysis_date', 'analyzed_by', 'notes')
        }),
    )
    
    def student_name(self, obj):
        return obj.health_profile.student.get_full_name()
    student_name.short_description = 'Student'
    student_name.admin_order_field = 'health_profile__student__last_name'


@admin.register(ComplianceMonitoring)
class ComplianceMonitoringAdmin(admin.ModelAdmin):
    list_display = ['student_name', 'compliance_type', 'requirement_name', 'status', 'required_by_date', 'is_overdue', 'is_expiring_soon']
    list_filter = ['compliance_type', 'status', 'is_mandatory', 'required_by_date', 'checked_by']
    search_fields = ['health_profile__student__first_name', 'health_profile__student__last_name', 'requirement_name', 'description']
    date_hierarchy = 'required_by_date'
    ordering = ['required_by_date']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('health_profile', 'compliance_type', 'requirement_name', 'description')
        }),
        ('Requirements', {
            'fields': ('required_by_date', 'is_mandatory')
        }),
        ('Compliance Status', {
            'fields': ('status', 'compliance_date', 'expiry_date')
        }),
        ('Monitoring', {
            'fields': ('last_checked_date', 'checked_by')
        }),
        ('Non-compliance', {
            'fields': ('non_compliance_reason', 'action_taken')
        }),
        ('Documentation', {
            'fields': ('supporting_documents', 'notes')
        }),
    )
    
    def student_name(self, obj):
        return obj.health_profile.student.get_full_name()
    student_name.short_description = 'Student'
    student_name.admin_order_field = 'health_profile__student__last_name'
    
    def is_overdue(self, obj):
        if obj.is_overdue:
            return format_html('<span style="color: red;">Yes</span>')
        return 'No'
    is_overdue.short_description = 'Overdue'
    is_overdue.boolean = True
    
    def is_expiring_soon(self, obj):
        if obj.is_expiring_soon:
            return format_html('<span style="color: orange;">Yes</span>')
        return 'No'
    is_expiring_soon.short_description = 'Expiring Soon'
    is_expiring_soon.boolean = True


@admin.register(HealthReport)
class HealthReportAdmin(admin.ModelAdmin):
    list_display = ['title', 'report_type', 'status', 'report_date_from', 'report_date_to', 'generated_by', 'created_at']
    list_filter = ['report_type', 'status', 'report_date_from', 'report_date_to', 'generated_by']
    search_fields = ['title', 'description', 'executive_summary', 'key_findings']
    date_hierarchy = 'created_at'
    ordering = ['-created_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('report_type', 'title', 'description')
        }),
        ('Report Scope', {
            'fields': ('health_profiles', 'report_date_from', 'report_date_to')
        }),
        ('Status & Approval', {
            'fields': ('status', 'generated_by', 'reviewed_by', 'approved_by')
        }),
        ('Report Content', {
            'fields': ('executive_summary', 'key_findings', 'recommendations')
        }),
        ('Metrics', {
            'fields': ('total_students', 'total_incidents', 'compliance_rate')
        }),
        ('Files', {
            'fields': ('report_file', 'notes')
        }),
    )
    
    filter_horizontal = ['health_profiles']