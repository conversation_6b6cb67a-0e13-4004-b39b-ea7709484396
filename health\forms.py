from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from .models import (
    HealthProfile, Allergy, Medication, MedicalHistory, 
    Vaccination, HealthScreening, HealthAlert, MedicalIncident,
    IncidentTreatment, IncidentFollowUp, IncidentNotification,
    HealthScreeningSchedule, MedicalAppointment, HealthTrendAnalysis,
    ComplianceMonitoring, HealthReport
)


class HealthProfileForm(forms.ModelForm):
    """Form for creating and editing health profiles"""
    
    class Meta:
        model = HealthProfile
        fields = [
            'blood_type', 'height', 'weight', 'chronic_conditions', 'disabilities',
            'special_needs', 'emergency_contact_name', 'emergency_contact_relationship',
            'emergency_contact_phone', 'emergency_contact_phone2', 'emergency_contact_address',
            'insurance_provider', 'insurance_policy_number', 'insurance_expiry_date',
            'family_doctor_name', 'family_doctor_phone', 'family_doctor_address',
            'medical_notes', 'last_physical_exam'
        ]
        widgets = {
            'blood_type': forms.Select(attrs={'class': 'form-control'}),
            'height': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.1', 'placeholder': 'Height in cm'}),
            'weight': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.1', 'placeholder': 'Weight in kg'}),
            'chronic_conditions': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'disabilities': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'special_needs': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'emergency_contact_name': forms.TextInput(attrs={'class': 'form-control'}),
            'emergency_contact_relationship': forms.TextInput(attrs={'class': 'form-control'}),
            'emergency_contact_phone': forms.TextInput(attrs={'class': 'form-control'}),
            'emergency_contact_phone2': forms.TextInput(attrs={'class': 'form-control'}),
            'emergency_contact_address': forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
            'insurance_provider': forms.TextInput(attrs={'class': 'form-control'}),
            'insurance_policy_number': forms.TextInput(attrs={'class': 'form-control'}),
            'insurance_expiry_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'family_doctor_name': forms.TextInput(attrs={'class': 'form-control'}),
            'family_doctor_phone': forms.TextInput(attrs={'class': 'form-control'}),
            'family_doctor_address': forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
            'medical_notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'last_physical_exam': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        }
    
    def __init__(self, *args, **kwargs):
        school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if school:
            # Filter student choices by school if needed
            from students.models import Student
            if 'student' in self.fields:
                self.fields['student'].queryset = Student.objects.filter(school=school)


class MedicalIncidentForm(forms.ModelForm):
    """Form for creating and editing medical incidents"""
    
    class Meta:
        model = MedicalIncident
        fields = [
            'health_profile', 'incident_type', 'severity', 'description',
            'location', 'symptoms_observed', 'immediate_action_taken', 'first_aid_given',
            'first_aid_details', 'emergency_services_called', 'reported_by', 'witnessed_by'
        ]
        widgets = {
            'health_profile': forms.Select(attrs={'class': 'form-control'}),
            'incident_type': forms.Select(attrs={'class': 'form-control'}),
            'severity': forms.Select(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'location': forms.TextInput(attrs={'class': 'form-control'}),
            'symptoms_observed': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'immediate_action_taken': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'first_aid_given': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'first_aid_details': forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
            'emergency_services_called': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'reported_by': forms.Select(attrs={'class': 'form-control'}),
            'witnessed_by': forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
        }
    
    def __init__(self, *args, **kwargs):
        school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if school:
            # Filter health profiles by school
            self.fields['health_profile'].queryset = HealthProfile.objects.filter(
                student__school=school
            )
        
        # Filter health profiles by school if provided


class HealthScreeningScheduleForm(forms.ModelForm):
    """Form for scheduling health screenings"""
    
    class Meta:
        model = HealthScreeningSchedule
        fields = [
            'health_profile', 'screening_type', 'title', 'description', 'scheduled_date', 'scheduled_time',
            'frequency', 'assigned_to', 'location', 'requirements', 'notes'
        ]
        widgets = {
            'health_profile': forms.Select(attrs={'class': 'form-control'}),
            'screening_type': forms.Select(attrs={'class': 'form-control'}),
            'title': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'scheduled_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'scheduled_time': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),
            'frequency': forms.Select(attrs={'class': 'form-control'}),
            'assigned_to': forms.Select(attrs={'class': 'form-control'}),
            'location': forms.TextInput(attrs={'class': 'form-control'}),
            'requirements': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        self.school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        # Filter assigned_to to school employees
        if self.school:
            from hr.models import Employee
            self.fields['assigned_to'].queryset = Employee.objects.filter(school=self.school)
            # Filter health profiles to school students
            self.fields['health_profile'].queryset = HealthProfile.objects.filter(student__school=self.school)
        
        # Set default values
        if not self.instance.pk:
            self.fields['scheduled_date'].initial = timezone.now().date()


class MedicalAppointmentForm(forms.ModelForm):
    """Form for managing medical appointments"""
    
    class Meta:
        model = MedicalAppointment
        fields = [
            'health_profile', 'appointment_type', 'title', 'description', 'appointment_date', 'appointment_time',
            'duration_minutes', 'priority', 'provider_name', 'provider_contact',
            'clinic_hospital', 'address', 'school_coordinator', 'excused_from_class',
            'transportation_arranged', 'parent_notified', 'parent_attending', 'notes'
        ]
        widgets = {
            'health_profile': forms.Select(attrs={'class': 'form-control'}),
            'appointment_type': forms.Select(attrs={'class': 'form-control'}),
            'title': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'appointment_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'appointment_time': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),
            'duration_minutes': forms.NumberInput(attrs={'class': 'form-control', 'min': '15', 'step': '15'}),
            'priority': forms.Select(attrs={'class': 'form-control'}),
            'provider_name': forms.TextInput(attrs={'class': 'form-control'}),
            'provider_contact': forms.TextInput(attrs={'class': 'form-control'}),
            'clinic_hospital': forms.TextInput(attrs={'class': 'form-control'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
            'school_coordinator': forms.Select(attrs={'class': 'form-control'}),
            'excused_from_class': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'transportation_arranged': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'parent_notified': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'parent_attending': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        self.school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        # Filter school_coordinator to school employees
        if self.school:
            from hr.models import Employee
            self.fields['school_coordinator'].queryset = Employee.objects.filter(school=self.school)
            # Filter health profiles to school students
            self.fields['health_profile'].queryset = HealthProfile.objects.filter(student__school=self.school)
        
        # Make required fields
        self.fields['appointment_type'].required = True
        self.fields['title'].required = True
        self.fields['appointment_date'].required = True
        self.fields['appointment_time'].required = True
        self.fields['provider_name'].required = True
        
        # Set default values
        if not self.instance.pk:
            self.fields['appointment_date'].initial = timezone.now().date()
            self.fields['duration_minutes'].initial = 30


class ComplianceMonitoringForm(forms.ModelForm):
    """Form for managing compliance monitoring"""
    
    class Meta:
        model = ComplianceMonitoring
        fields = [
            'health_profile', 'compliance_type', 'requirement_name', 'description', 'required_by_date',
            'is_mandatory', 'status', 'compliance_date', 'expiry_date',
            'non_compliance_reason', 'action_taken', 'supporting_documents', 'notes'
        ]
        widgets = {
            'health_profile': forms.Select(attrs={'class': 'form-control'}),
            'compliance_type': forms.Select(attrs={'class': 'form-control'}),
            'requirement_name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'required_by_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'is_mandatory': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'status': forms.Select(attrs={'class': 'form-control'}),
            'compliance_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'expiry_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'non_compliance_reason': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'action_taken': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'supporting_documents': forms.FileInput(attrs={'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        self.school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if self.school:
            # Filter health profiles to school students
            self.fields['health_profile'].queryset = HealthProfile.objects.filter(student__school=self.school)
        
        # Make required fields
        self.fields['compliance_type'].required = True
        self.fields['requirement_name'].required = True
        self.fields['description'].required = True
        self.fields['required_by_date'].required = True

class AllergyForm(forms.ModelForm):
    """Form for creating and editing allergies"""
    
    class Meta:
        model = Allergy
        fields = [
            'allergy_type', 'allergen', 'severity', 'symptoms', 'treatment', 
            'notes', 'diagnosed_date', 'is_active'
        ]
        widgets = {
            'allergy_type': forms.Select(attrs={'class': 'form-control'}),
            'allergen': forms.TextInput(attrs={'class': 'form-control'}),
            'severity': forms.Select(attrs={'class': 'form-control'}),
            'symptoms': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'treatment': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
            'diagnosed_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
    
    def __init__(self, *args, **kwargs):
        school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)


class MedicationForm(forms.ModelForm):
    """Form for creating and editing medications"""
    
    class Meta:
        model = Medication
        fields = [
            'medication_name', 'dosage', 'frequency', 'administration_route',
            'start_date', 'end_date', 'administration_times', 'prescribed_by', 'reason',
            'side_effects', 'special_instructions', 'can_self_administer', 'requires_supervision', 
            'stored_at_school', 'storage_location', 'is_active'
        ]
        widgets = {
            'medication_name': forms.TextInput(attrs={'class': 'form-control'}),
            'dosage': forms.TextInput(attrs={'class': 'form-control'}),
            'frequency': forms.Select(attrs={'class': 'form-control'}),
            'administration_route': forms.Select(attrs={'class': 'form-control'}),
            'start_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'administration_times': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., 8:00 AM, 2:00 PM'}),
            'prescribed_by': forms.TextInput(attrs={'class': 'form-control'}),
            'reason': forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
            'side_effects': forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
            'special_instructions': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'can_self_administer': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'requires_supervision': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'stored_at_school': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'storage_location': forms.TextInput(attrs={'class': 'form-control'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
    
    def __init__(self, *args, **kwargs):
        school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)


class MedicalHistoryForm(forms.ModelForm):
    """Form for creating and editing medical history"""
    
    class Meta:
        model = MedicalHistory
        fields = [
            'record_type', 'date', 'title', 'description', 'diagnosis', 'treatment',
            'medications_prescribed', 'follow_up_required', 'follow_up_date',
            'healthcare_provider', 'facility', 'documents', 'affects_school_activities',
            'activity_restrictions', 'return_to_school_date', 'notes'
        ]
        widgets = {
            'record_type': forms.Select(attrs={'class': 'form-control'}),
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'title': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'diagnosis': forms.TextInput(attrs={'class': 'form-control'}),
            'treatment': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'medications_prescribed': forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
            'follow_up_required': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'follow_up_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'healthcare_provider': forms.TextInput(attrs={'class': 'form-control'}),
            'facility': forms.TextInput(attrs={'class': 'form-control'}),
            'documents': forms.FileInput(attrs={'class': 'form-control'}),
            'affects_school_activities': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'activity_restrictions': forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
            'return_to_school_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)