# Generated by Django 5.2.4 on 2025-08-05 20:14

import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        ("hr", "0005_performancegoal_performanceimprovementplan_and_more"),
        ("students", "0008_grade_max_age_grade_max_capacity_grade_min_age"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="HealthProfile",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.<PERSON><PERSON>an<PERSON>ield(default=True, verbose_name="Is Active"),
                ),
                (
                    "blood_type",
                    models.Char<PERSON>ield(
                        choices=[
                            ("A+", "A+"),
                            ("A-", "A-"),
                            ("B+", "B+"),
                            ("B-", "B-"),
                            ("AB+", "AB+"),
                            ("AB-", "AB-"),
                            ("O+", "O+"),
                            ("O-", "O-"),
                            ("unknown", "Unknown"),
                        ],
                        default="unknown",
                        max_length=10,
                    ),
                ),
                (
                    "height",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Height in cm",
                        max_digits=5,
                        null=True,
                    ),
                ),
                (
                    "weight",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Weight in kg",
                        max_digits=5,
                        null=True,
                    ),
                ),
                (
                    "chronic_conditions",
                    models.TextField(
                        blank=True, help_text="List of chronic medical conditions"
                    ),
                ),
                (
                    "disabilities",
                    models.TextField(
                        blank=True, help_text="Physical or learning disabilities"
                    ),
                ),
                (
                    "special_needs",
                    models.TextField(
                        blank=True, help_text="Special medical or educational needs"
                    ),
                ),
                (
                    "emergency_contact_name",
                    models.CharField(blank=True, max_length=200),
                ),
                (
                    "emergency_contact_relationship",
                    models.CharField(blank=True, max_length=100),
                ),
                (
                    "emergency_contact_phone",
                    models.CharField(blank=True, max_length=20),
                ),
                (
                    "emergency_contact_phone2",
                    models.CharField(blank=True, max_length=20),
                ),
                ("emergency_contact_address", models.TextField(blank=True)),
                ("insurance_provider", models.CharField(blank=True, max_length=200)),
                (
                    "insurance_policy_number",
                    models.CharField(blank=True, max_length=100),
                ),
                ("insurance_expiry_date", models.DateField(blank=True, null=True)),
                ("family_doctor_name", models.CharField(blank=True, max_length=200)),
                ("family_doctor_phone", models.CharField(blank=True, max_length=20)),
                ("family_doctor_address", models.TextField(blank=True)),
                (
                    "medical_notes",
                    models.TextField(
                        blank=True, help_text="Additional medical information"
                    ),
                ),
                ("last_physical_exam", models.DateField(blank=True, null=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "student",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="health_profile",
                        to="students.student",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "ordering": ["student__first_name", "student__last_name"],
            },
        ),
        migrations.CreateModel(
            name="HealthAlert",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "alert_type",
                    models.CharField(
                        choices=[
                            ("allergy", "Severe Allergy"),
                            ("medication", "Critical Medication"),
                            ("condition", "Medical Condition"),
                            ("emergency", "Emergency Contact"),
                            ("restriction", "Activity Restriction"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("critical", "Critical"),
                        ],
                        default="medium",
                        max_length=10,
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField()),
                (
                    "show_on_dashboard",
                    models.BooleanField(
                        default=True, help_text="Show alert on student dashboard"
                    ),
                ),
                (
                    "notify_teachers",
                    models.BooleanField(
                        default=False, help_text="Notify all student's teachers"
                    ),
                ),
                (
                    "notify_staff",
                    models.BooleanField(
                        default=False, help_text="Notify relevant staff members"
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("start_date", models.DateField(default=django.utils.timezone.now)),
                ("end_date", models.DateField(blank=True, null=True)),
                (
                    "action_required",
                    models.TextField(
                        blank=True, help_text="What action should be taken"
                    ),
                ),
                (
                    "emergency_instructions",
                    models.TextField(
                        blank=True, help_text="Emergency response instructions"
                    ),
                ),
                ("notes", models.TextField(blank=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "health_profile",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="alerts",
                        to="health.healthprofile",
                    ),
                ),
            ],
            options={
                "ordering": ["-priority", "-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Allergy",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "allergy_type",
                    models.CharField(
                        choices=[
                            ("food", "Food Allergy"),
                            ("environmental", "Environmental"),
                            ("medication", "Medication"),
                            ("insect", "Insect Sting"),
                            ("contact", "Contact Allergy"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "allergen",
                    models.CharField(
                        help_text="Specific allergen (e.g., peanuts, pollen)",
                        max_length=200,
                    ),
                ),
                (
                    "severity",
                    models.CharField(
                        choices=[
                            ("mild", "Mild"),
                            ("moderate", "Moderate"),
                            ("severe", "Severe"),
                            ("life_threatening", "Life Threatening"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "symptoms",
                    models.TextField(help_text="Typical symptoms experienced"),
                ),
                (
                    "treatment",
                    models.TextField(
                        blank=True, help_text="Treatment or medication required"
                    ),
                ),
                ("notes", models.TextField(blank=True)),
                ("diagnosed_date", models.DateField(blank=True, null=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "health_profile",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="allergies",
                        to="health.healthprofile",
                    ),
                ),
            ],
            options={
                "ordering": ["-severity", "allergen"],
            },
        ),
        migrations.CreateModel(
            name="HealthScreening",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "screening_type",
                    models.CharField(
                        choices=[
                            ("vision", "Vision Screening"),
                            ("hearing", "Hearing Screening"),
                            ("dental", "Dental Screening"),
                            ("scoliosis", "Scoliosis Screening"),
                            ("bmi", "BMI/Growth Screening"),
                            ("blood_pressure", "Blood Pressure"),
                            ("general", "General Health Screening"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                ("date", models.DateField()),
                (
                    "result",
                    models.CharField(
                        choices=[
                            ("pass", "Pass"),
                            ("refer", "Refer for Further Evaluation"),
                            ("fail", "Fail"),
                            ("incomplete", "Incomplete"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "screened_by",
                    models.CharField(
                        blank=True,
                        help_text="Person who conducted screening",
                        max_length=200,
                    ),
                ),
                (
                    "measurements",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Specific measurements or results",
                    ),
                ),
                (
                    "findings",
                    models.TextField(blank=True, help_text="Detailed findings"),
                ),
                (
                    "recommendations",
                    models.TextField(
                        blank=True, help_text="Recommendations for follow-up"
                    ),
                ),
                ("referral_needed", models.BooleanField(default=False)),
                (
                    "referral_to",
                    models.CharField(
                        blank=True,
                        help_text="Specialist or facility to refer to",
                        max_length=200,
                    ),
                ),
                ("follow_up_completed", models.BooleanField(default=False)),
                ("follow_up_date", models.DateField(blank=True, null=True)),
                ("follow_up_results", models.TextField(blank=True)),
                ("parent_notified", models.BooleanField(default=False)),
                ("parent_notification_date", models.DateField(blank=True, null=True)),
                (
                    "parent_notification_method",
                    models.CharField(
                        blank=True,
                        help_text="Email, phone, letter, etc.",
                        max_length=100,
                    ),
                ),
                ("notes", models.TextField(blank=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "health_profile",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="screenings",
                        to="health.healthprofile",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "ordering": ["-date"],
            },
        ),
        migrations.CreateModel(
            name="MedicalHistory",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "record_type",
                    models.CharField(
                        choices=[
                            ("illness", "Illness"),
                            ("injury", "Injury"),
                            ("surgery", "Surgery"),
                            ("hospitalization", "Hospitalization"),
                            ("vaccination", "Vaccination"),
                            ("checkup", "Regular Checkup"),
                            ("dental", "Dental"),
                            ("vision", "Vision/Hearing"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                ("date", models.DateField()),
                (
                    "title",
                    models.CharField(
                        help_text="Brief title of the medical event", max_length=200
                    ),
                ),
                ("description", models.TextField(help_text="Detailed description")),
                ("diagnosis", models.CharField(blank=True, max_length=200)),
                (
                    "treatment",
                    models.TextField(blank=True, help_text="Treatment provided"),
                ),
                ("medications_prescribed", models.TextField(blank=True)),
                ("follow_up_required", models.BooleanField(default=False)),
                ("follow_up_date", models.DateField(blank=True, null=True)),
                ("healthcare_provider", models.CharField(blank=True, max_length=200)),
                (
                    "facility",
                    models.CharField(
                        blank=True, help_text="Hospital, clinic, etc.", max_length=200
                    ),
                ),
                (
                    "documents",
                    models.FileField(
                        blank=True,
                        help_text="Medical reports, prescriptions, etc.",
                        upload_to="health/medical_records/",
                    ),
                ),
                ("affects_school_activities", models.BooleanField(default=False)),
                (
                    "activity_restrictions",
                    models.TextField(
                        blank=True, help_text="Any restrictions on school activities"
                    ),
                ),
                ("return_to_school_date", models.DateField(blank=True, null=True)),
                ("notes", models.TextField(blank=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "health_profile",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="medical_history",
                        to="health.healthprofile",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Medical histories",
                "ordering": ["-date"],
            },
        ),
        migrations.CreateModel(
            name="MedicalIncident",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "incident_id",
                    models.CharField(
                        help_text="Auto-generated incident ID",
                        max_length=50,
                        unique=True,
                    ),
                ),
                (
                    "incident_type",
                    models.CharField(
                        choices=[
                            ("injury", "Injury"),
                            ("illness", "Sudden Illness"),
                            ("allergic_reaction", "Allergic Reaction"),
                            ("medication_issue", "Medication Issue"),
                            ("behavioral", "Behavioral/Mental Health"),
                            ("emergency", "Medical Emergency"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "severity",
                    models.CharField(
                        choices=[
                            ("minor", "Minor"),
                            ("moderate", "Moderate"),
                            ("serious", "Serious"),
                            ("critical", "Critical"),
                        ],
                        max_length=15,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("reported", "Reported"),
                            ("in_treatment", "In Treatment"),
                            ("resolved", "Resolved"),
                            ("referred", "Referred to External Care"),
                            ("follow_up_required", "Follow-up Required"),
                        ],
                        default="reported",
                        max_length=20,
                    ),
                ),
                ("incident_date", models.DateField()),
                ("incident_time", models.TimeField()),
                (
                    "location",
                    models.CharField(
                        help_text="Where the incident occurred", max_length=200
                    ),
                ),
                (
                    "description",
                    models.TextField(help_text="Detailed description of what happened"),
                ),
                (
                    "symptoms_observed",
                    models.TextField(
                        blank=True, help_text="Symptoms observed by staff"
                    ),
                ),
                (
                    "witnessed_by",
                    models.TextField(blank=True, help_text="Names of witnesses"),
                ),
                (
                    "immediate_action_taken",
                    models.TextField(blank=True, help_text="What was done immediately"),
                ),
                ("first_aid_given", models.BooleanField(default=False)),
                (
                    "first_aid_details",
                    models.TextField(
                        blank=True, help_text="Details of first aid provided"
                    ),
                ),
                ("emergency_services_called", models.BooleanField(default=False)),
                (
                    "emergency_service_details",
                    models.TextField(
                        blank=True, help_text="Which services called and response"
                    ),
                ),
                ("transported_to_hospital", models.BooleanField(default=False)),
                (
                    "hospital_details",
                    models.TextField(blank=True, help_text="Hospital name and details"),
                ),
                ("parent_notified", models.BooleanField(default=False)),
                (
                    "parent_notification_time",
                    models.DateTimeField(blank=True, null=True),
                ),
                (
                    "parent_notification_method",
                    models.CharField(
                        blank=True,
                        help_text="Phone, email, in-person, etc.",
                        max_length=100,
                    ),
                ),
                (
                    "parent_response",
                    models.TextField(
                        blank=True, help_text="Parent's response or actions"
                    ),
                ),
                ("follow_up_required", models.BooleanField(default=False)),
                ("follow_up_instructions", models.TextField(blank=True)),
                ("return_to_class_time", models.DateTimeField(blank=True, null=True)),
                (
                    "activity_restrictions",
                    models.TextField(
                        blank=True, help_text="Any restrictions on student activities"
                    ),
                ),
                (
                    "photos_taken",
                    models.BooleanField(
                        default=False, help_text="Were photos taken of injuries?"
                    ),
                ),
                (
                    "incident_report_file",
                    models.FileField(blank=True, upload_to="health/incident_reports/"),
                ),
                ("resolved_date", models.DateField(blank=True, null=True)),
                (
                    "resolution_notes",
                    models.TextField(
                        blank=True, help_text="How the incident was resolved"
                    ),
                ),
                ("notes", models.TextField(blank=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "health_profile",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="incidents",
                        to="health.healthprofile",
                    ),
                ),
                (
                    "reported_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="reported_incidents",
                        to="hr.employee",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "ordering": ["-incident_date", "-incident_time"],
            },
        ),
        migrations.CreateModel(
            name="IncidentTreatment",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "treatment_type",
                    models.CharField(
                        choices=[
                            ("first_aid", "First Aid"),
                            ("medication", "Medication Administration"),
                            ("rest", "Rest/Observation"),
                            ("ice_pack", "Ice Pack Application"),
                            ("bandaging", "Bandaging/Dressing"),
                            ("cleaning", "Wound Cleaning"),
                            ("comfort", "Comfort Measures"),
                            ("referral", "Referral to Healthcare Provider"),
                            ("other", "Other Treatment"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "treatment_time",
                    models.DateTimeField(default=django.utils.timezone.now),
                ),
                (
                    "treatment_description",
                    models.TextField(
                        help_text="Detailed description of treatment provided"
                    ),
                ),
                (
                    "medication_given",
                    models.CharField(
                        blank=True,
                        help_text="Name and dosage of medication",
                        max_length=200,
                    ),
                ),
                (
                    "supplies_used",
                    models.TextField(blank=True, help_text="Medical supplies used"),
                ),
                (
                    "student_response",
                    models.TextField(
                        blank=True, help_text="How the student responded to treatment"
                    ),
                ),
                (
                    "effectiveness",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("effective", "Effective"),
                            ("partially_effective", "Partially Effective"),
                            ("not_effective", "Not Effective"),
                            ("too_early", "Too Early to Tell"),
                        ],
                        max_length=20,
                    ),
                ),
                ("additional_treatment_needed", models.BooleanField(default=False)),
                ("next_treatment_time", models.DateTimeField(blank=True, null=True)),
                ("notes", models.TextField(blank=True)),
                (
                    "administered_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="hr.employee",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "incident",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="treatments",
                        to="health.medicalincident",
                    ),
                ),
            ],
            options={
                "ordering": ["treatment_time"],
            },
        ),
        migrations.CreateModel(
            name="IncidentNotification",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("parent_immediate", "Immediate Parent Notification"),
                            ("parent_update", "Parent Update"),
                            ("staff_alert", "Staff Alert"),
                            ("administration", "Administration Notification"),
                            ("teacher_info", "Teacher Information"),
                            ("follow_up_reminder", "Follow-up Reminder"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "method",
                    models.CharField(
                        choices=[
                            ("phone", "Phone Call"),
                            ("sms", "SMS/Text Message"),
                            ("email", "Email"),
                            ("in_person", "In Person"),
                            ("letter", "Written Letter"),
                            ("app_notification", "App Notification"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("sent", "Sent"),
                            ("delivered", "Delivered"),
                            ("read", "Read/Acknowledged"),
                            ("failed", "Failed"),
                        ],
                        default="pending",
                        max_length=15,
                    ),
                ),
                ("recipient_name", models.CharField(max_length=200)),
                (
                    "recipient_relationship",
                    models.CharField(
                        help_text="Parent, teacher, administrator, etc.", max_length=100
                    ),
                ),
                (
                    "recipient_contact",
                    models.CharField(
                        help_text="Phone, email, or other contact info", max_length=200
                    ),
                ),
                ("subject", models.CharField(blank=True, max_length=200)),
                ("message", models.TextField()),
                (
                    "scheduled_time",
                    models.DateTimeField(default=django.utils.timezone.now),
                ),
                ("sent_time", models.DateTimeField(blank=True, null=True)),
                ("delivered_time", models.DateTimeField(blank=True, null=True)),
                ("read_time", models.DateTimeField(blank=True, null=True)),
                ("response_received", models.BooleanField(default=False)),
                ("response_content", models.TextField(blank=True)),
                ("response_time", models.DateTimeField(blank=True, null=True)),
                ("notes", models.TextField(blank=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "sent_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="hr.employee",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "incident",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notifications",
                        to="health.medicalincident",
                    ),
                ),
            ],
            options={
                "ordering": ["-scheduled_time"],
            },
        ),
        migrations.CreateModel(
            name="IncidentFollowUp",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "follow_up_type",
                    models.CharField(
                        choices=[
                            ("check_in", "Student Check-in"),
                            ("parent_contact", "Parent Contact"),
                            ("medical_referral", "Medical Referral"),
                            ("activity_modification", "Activity Modification"),
                            ("documentation", "Documentation Update"),
                            ("investigation", "Incident Investigation"),
                            ("prevention", "Prevention Measures"),
                            ("other", "Other"),
                        ],
                        max_length=25,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=15,
                    ),
                ),
                ("scheduled_date", models.DateField()),
                ("scheduled_time", models.TimeField(blank=True, null=True)),
                ("description", models.TextField(help_text="What needs to be done")),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("urgent", "Urgent"),
                        ],
                        default="medium",
                        max_length=10,
                    ),
                ),
                ("completed_date", models.DateField(blank=True, null=True)),
                (
                    "outcome",
                    models.TextField(
                        blank=True, help_text="Results of the follow-up action"
                    ),
                ),
                ("additional_follow_up_needed", models.BooleanField(default=False)),
                ("next_follow_up_date", models.DateField(blank=True, null=True)),
                ("notes", models.TextField(blank=True)),
                (
                    "assigned_to",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="hr.employee",
                    ),
                ),
                (
                    "completed_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="completed_follow_ups",
                        to="hr.employee",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "incident",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="follow_ups",
                        to="health.medicalincident",
                    ),
                ),
            ],
            options={
                "ordering": ["scheduled_date", "scheduled_time"],
            },
        ),
        migrations.CreateModel(
            name="Medication",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                ("medication_name", models.CharField(max_length=200)),
                (
                    "dosage",
                    models.CharField(help_text="e.g., 10mg, 1 tablet", max_length=100),
                ),
                (
                    "frequency",
                    models.CharField(
                        choices=[
                            ("once_daily", "Once Daily"),
                            ("twice_daily", "Twice Daily"),
                            ("three_times_daily", "Three Times Daily"),
                            ("four_times_daily", "Four Times Daily"),
                            ("as_needed", "As Needed"),
                            ("weekly", "Weekly"),
                            ("monthly", "Monthly"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "administration_route",
                    models.CharField(
                        choices=[
                            ("oral", "Oral"),
                            ("injection", "Injection"),
                            ("inhaler", "Inhaler"),
                            ("topical", "Topical"),
                            ("nasal", "Nasal"),
                            ("eye_drops", "Eye Drops"),
                            ("other", "Other"),
                        ],
                        default="oral",
                        max_length=20,
                    ),
                ),
                ("start_date", models.DateField()),
                ("end_date", models.DateField(blank=True, null=True)),
                (
                    "administration_times",
                    models.CharField(
                        blank=True,
                        help_text="Specific times (e.g., 8:00 AM, 2:00 PM)",
                        max_length=200,
                    ),
                ),
                (
                    "prescribed_by",
                    models.CharField(
                        blank=True, help_text="Prescribing doctor", max_length=200
                    ),
                ),
                ("reason", models.TextField(help_text="Reason for medication")),
                (
                    "side_effects",
                    models.TextField(
                        blank=True, help_text="Known side effects to watch for"
                    ),
                ),
                ("special_instructions", models.TextField(blank=True)),
                (
                    "can_self_administer",
                    models.BooleanField(
                        default=False,
                        help_text="Student can take medication themselves",
                    ),
                ),
                (
                    "requires_supervision",
                    models.BooleanField(
                        default=True, help_text="Requires adult supervision"
                    ),
                ),
                (
                    "stored_at_school",
                    models.BooleanField(
                        default=False, help_text="Medication stored at school"
                    ),
                ),
                (
                    "storage_location",
                    models.CharField(
                        blank=True,
                        help_text="Where medication is stored",
                        max_length=200,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "health_profile",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="medications",
                        to="health.healthprofile",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "ordering": ["medication_name", "-start_date"],
            },
        ),
        migrations.CreateModel(
            name="Vaccination",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "vaccine_name",
                    models.CharField(
                        choices=[
                            ("bcg", "BCG (Tuberculosis)"),
                            ("hepatitis_b", "Hepatitis B"),
                            ("polio", "Polio (IPV/OPV)"),
                            ("dtp", "DTP (Diphtheria, Tetanus, Pertussis)"),
                            ("hib", "Hib (Haemophilus influenzae type b)"),
                            ("pneumococcal", "Pneumococcal"),
                            ("rotavirus", "Rotavirus"),
                            ("mmr", "MMR (Measles, Mumps, Rubella)"),
                            ("varicella", "Varicella (Chickenpox)"),
                            ("hepatitis_a", "Hepatitis A"),
                            ("meningococcal", "Meningococcal"),
                            ("hpv", "HPV (Human Papillomavirus)"),
                            ("influenza", "Influenza (Flu)"),
                            ("covid19", "COVID-19"),
                            ("other", "Other"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "vaccine_brand",
                    models.CharField(
                        blank=True, help_text="Brand name if applicable", max_length=100
                    ),
                ),
                ("date_administered", models.DateField()),
                (
                    "dose_number",
                    models.PositiveIntegerField(
                        default=1, help_text="Which dose in the series"
                    ),
                ),
                (
                    "total_doses_required",
                    models.PositiveIntegerField(
                        default=1, help_text="Total doses in the series"
                    ),
                ),
                (
                    "administered_by",
                    models.CharField(
                        blank=True, help_text="Healthcare provider", max_length=200
                    ),
                ),
                (
                    "facility",
                    models.CharField(
                        blank=True, help_text="Where vaccine was given", max_length=200
                    ),
                ),
                ("batch_number", models.CharField(blank=True, max_length=100)),
                ("expiry_date", models.DateField(blank=True, null=True)),
                ("next_dose_due", models.DateField(blank=True, null=True)),
                (
                    "adverse_reactions",
                    models.TextField(
                        blank=True, help_text="Any adverse reactions experienced"
                    ),
                ),
                (
                    "certificate",
                    models.FileField(
                        blank=True, upload_to="health/vaccination_certificates/"
                    ),
                ),
                ("notes", models.TextField(blank=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "health_profile",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="vaccinations",
                        to="health.healthprofile",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "ordering": ["-date_administered"],
                "unique_together": {("health_profile", "vaccine_name", "dose_number")},
            },
        ),
    ]
