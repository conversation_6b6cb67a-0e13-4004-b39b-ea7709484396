# Generated by Django 5.2.4 on 2025-08-06 07:45

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        ("health", "0001_initial"),
        ("hr", "0005_performancegoal_performanceimprovementplan_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ComplianceMonitoring",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "compliance_type",
                    models.CharField(
                        choices=[
                            ("vaccination", "Vaccination Requirements"),
                            ("screening", "Health Screening Requirements"),
                            ("medication", "Medication Compliance"),
                            ("dietary", "Dietary Restrictions"),
                            ("activity", "Activity Restrictions"),
                            ("documentation", "Medical Documentation"),
                            ("emergency_info", "Emergency Information"),
                            ("other", "Other Compliance"),
                        ],
                        max_length=20,
                    ),
                ),
                ("requirement_name", models.CharField(max_length=200)),
                (
                    "description",
                    models.TextField(
                        help_text="Description of the compliance requirement"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("compliant", "Compliant"),
                            ("non_compliant", "Non-Compliant"),
                            ("partially_compliant", "Partially Compliant"),
                            ("pending", "Pending Review"),
                            ("exempt", "Exempt"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                (
                    "compliance_date",
                    models.DateField(
                        blank=True,
                        help_text="Date when compliance was achieved",
                        null=True,
                    ),
                ),
                (
                    "expiry_date",
                    models.DateField(
                        blank=True, help_text="Date when compliance expires", null=True
                    ),
                ),
                (
                    "required_by_date",
                    models.DateField(help_text="Date by which compliance is required"),
                ),
                ("is_mandatory", models.BooleanField(default=True)),
                ("last_checked_date", models.DateField(blank=True, null=True)),
                (
                    "non_compliance_reason",
                    models.TextField(blank=True, help_text="Reason for non-compliance"),
                ),
                (
                    "action_taken",
                    models.TextField(
                        blank=True, help_text="Actions taken to address non-compliance"
                    ),
                ),
                (
                    "supporting_documents",
                    models.FileField(blank=True, upload_to="health/compliance/"),
                ),
                ("notes", models.TextField(blank=True)),
                (
                    "checked_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="hr.employee",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "health_profile",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="compliance_records",
                        to="health.healthprofile",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "ordering": ["required_by_date", "compliance_type"],
            },
        ),
        migrations.CreateModel(
            name="HealthReport",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "report_type",
                    models.CharField(
                        choices=[
                            ("individual", "Individual Student Report"),
                            ("class", "Class Health Report"),
                            ("grade", "Grade Level Report"),
                            ("school", "School-wide Report"),
                            ("incident", "Incident Summary Report"),
                            ("compliance", "Compliance Report"),
                            ("screening", "Screening Results Report"),
                            ("trend", "Health Trends Report"),
                            ("custom", "Custom Report"),
                        ],
                        max_length=15,
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField(blank=True)),
                ("report_date_from", models.DateField()),
                ("report_date_to", models.DateField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("pending_review", "Pending Review"),
                            ("approved", "Approved"),
                            ("published", "Published"),
                            ("archived", "Archived"),
                        ],
                        default="draft",
                        max_length=15,
                    ),
                ),
                ("executive_summary", models.TextField(blank=True)),
                ("key_findings", models.TextField(blank=True)),
                ("recommendations", models.TextField(blank=True)),
                ("total_students", models.PositiveIntegerField(default=0)),
                ("total_incidents", models.PositiveIntegerField(default=0)),
                (
                    "compliance_rate",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "report_file",
                    models.FileField(blank=True, upload_to="health/reports/"),
                ),
                ("notes", models.TextField(blank=True)),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_health_reports",
                        to="hr.employee",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "generated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="hr.employee",
                    ),
                ),
                (
                    "health_profiles",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Students included in report",
                        to="health.healthprofile",
                    ),
                ),
                (
                    "reviewed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="reviewed_health_reports",
                        to="hr.employee",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="HealthScreeningSchedule",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "screening_type",
                    models.CharField(
                        choices=[
                            ("vision", "Vision Screening"),
                            ("hearing", "Hearing Screening"),
                            ("dental", "Dental Screening"),
                            ("physical", "Physical Examination"),
                            ("bmi", "BMI Assessment"),
                            ("scoliosis", "Scoliosis Screening"),
                            ("immunization", "Immunization Check"),
                            ("mental_health", "Mental Health Screening"),
                            ("other", "Other Screening"),
                        ],
                        max_length=20,
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField(blank=True)),
                ("scheduled_date", models.DateField()),
                ("scheduled_time", models.TimeField(blank=True, null=True)),
                (
                    "frequency",
                    models.CharField(
                        choices=[
                            ("annual", "Annual"),
                            ("biannual", "Bi-annual"),
                            ("quarterly", "Quarterly"),
                            ("monthly", "Monthly"),
                            ("as_needed", "As Needed"),
                        ],
                        default="annual",
                        max_length=15,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("scheduled", "Scheduled"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                            ("rescheduled", "Rescheduled"),
                        ],
                        default="scheduled",
                        max_length=15,
                    ),
                ),
                (
                    "location",
                    models.CharField(
                        blank=True,
                        help_text="Where the screening will take place",
                        max_length=200,
                    ),
                ),
                (
                    "requirements",
                    models.TextField(
                        blank=True, help_text="Special requirements or preparations"
                    ),
                ),
                ("completed_date", models.DateField(blank=True, null=True)),
                ("results_available", models.BooleanField(default=False)),
                ("follow_up_required", models.BooleanField(default=False)),
                ("follow_up_notes", models.TextField(blank=True)),
                ("next_screening_date", models.DateField(blank=True, null=True)),
                ("notes", models.TextField(blank=True)),
                (
                    "assigned_to",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="hr.employee",
                    ),
                ),
                (
                    "completed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="completed_screenings",
                        to="hr.employee",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "health_profile",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="screening_schedules",
                        to="health.healthprofile",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "ordering": ["scheduled_date", "scheduled_time"],
            },
        ),
        migrations.CreateModel(
            name="HealthTrendAnalysis",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "trend_type",
                    models.CharField(
                        choices=[
                            ("bmi", "BMI Trend"),
                            ("height_weight", "Height/Weight Growth"),
                            ("vision", "Vision Changes"),
                            ("hearing", "Hearing Changes"),
                            ("blood_pressure", "Blood Pressure"),
                            ("attendance", "Health-related Attendance"),
                            ("medication_compliance", "Medication Compliance"),
                            ("incident_frequency", "Incident Frequency"),
                            ("screening_results", "Screening Results"),
                            ("other", "Other Trend"),
                        ],
                        max_length=25,
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("analysis_start_date", models.DateField()),
                ("analysis_end_date", models.DateField()),
                (
                    "trend_direction",
                    models.CharField(
                        choices=[
                            ("improving", "Improving"),
                            ("stable", "Stable"),
                            ("declining", "Declining"),
                            ("concerning", "Concerning"),
                        ],
                        max_length=15,
                    ),
                ),
                (
                    "trend_description",
                    models.TextField(help_text="Detailed description of the trend"),
                ),
                (
                    "baseline_value",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                (
                    "current_value",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                (
                    "change_percentage",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "key_findings",
                    models.TextField(help_text="Key findings from the analysis"),
                ),
                (
                    "recommendations",
                    models.TextField(
                        blank=True, help_text="Recommendations based on trends"
                    ),
                ),
                ("action_required", models.BooleanField(default=False)),
                ("next_analysis_date", models.DateField(blank=True, null=True)),
                ("notes", models.TextField(blank=True)),
                (
                    "analyzed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="hr.employee",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "health_profile",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="trend_analyses",
                        to="health.healthprofile",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "ordering": ["-analysis_end_date"],
            },
        ),
        migrations.CreateModel(
            name="MedicalAppointment",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "appointment_type",
                    models.CharField(
                        choices=[
                            ("routine_checkup", "Routine Checkup"),
                            ("follow_up", "Follow-up Visit"),
                            ("specialist", "Specialist Consultation"),
                            ("emergency", "Emergency Visit"),
                            ("vaccination", "Vaccination"),
                            ("dental", "Dental Appointment"),
                            ("vision", "Vision Care"),
                            ("mental_health", "Mental Health"),
                            ("therapy", "Therapy Session"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField(blank=True)),
                ("appointment_date", models.DateField()),
                ("appointment_time", models.TimeField()),
                (
                    "duration_minutes",
                    models.PositiveIntegerField(
                        default=30, help_text="Appointment duration in minutes"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("scheduled", "Scheduled"),
                            ("confirmed", "Confirmed"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                            ("no_show", "No Show"),
                            ("rescheduled", "Rescheduled"),
                        ],
                        default="scheduled",
                        max_length=15,
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("urgent", "Urgent"),
                        ],
                        default="medium",
                        max_length=10,
                    ),
                ),
                (
                    "provider_name",
                    models.CharField(
                        help_text="Doctor or healthcare provider name", max_length=200
                    ),
                ),
                (
                    "provider_contact",
                    models.CharField(
                        blank=True,
                        help_text="Provider contact information",
                        max_length=100,
                    ),
                ),
                (
                    "clinic_hospital",
                    models.CharField(
                        blank=True, help_text="Clinic or hospital name", max_length=200
                    ),
                ),
                (
                    "address",
                    models.TextField(
                        blank=True, help_text="Appointment location address"
                    ),
                ),
                (
                    "excused_from_class",
                    models.BooleanField(
                        default=False, help_text="Student excused from classes"
                    ),
                ),
                ("transportation_arranged", models.BooleanField(default=False)),
                ("parent_notified", models.BooleanField(default=False)),
                ("parent_attending", models.BooleanField(default=False)),
                (
                    "parent_notification_sent",
                    models.DateTimeField(blank=True, null=True),
                ),
                ("completed_date", models.DateField(blank=True, null=True)),
                (
                    "appointment_summary",
                    models.TextField(blank=True, help_text="Summary of appointment"),
                ),
                (
                    "recommendations",
                    models.TextField(blank=True, help_text="Doctor's recommendations"),
                ),
                ("follow_up_required", models.BooleanField(default=False)),
                ("follow_up_date", models.DateField(blank=True, null=True)),
                (
                    "medical_report_file",
                    models.FileField(blank=True, upload_to="health/medical_reports/"),
                ),
                (
                    "prescription_file",
                    models.FileField(blank=True, upload_to="health/prescriptions/"),
                ),
                ("notes", models.TextField(blank=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "health_profile",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="appointments",
                        to="health.healthprofile",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "school_coordinator",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="hr.employee",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "ordering": ["appointment_date", "appointment_time"],
            },
        ),
    ]
