# Generated by Django 5.2.4 on 2025-08-06 08:19

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        ("health", "0002_compliancemonitoring_healthreport_and_more"),
        ("hr", "0005_performancegoal_performanceimprovementplan_and_more"),
        ("students", "0008_grade_max_age_grade_max_capacity_grade_min_age"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="HealthAlertRule",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                ("name", models.CharField(max_length=200)),
                ("description", models.TextField()),
                (
                    "condition_type",
                    models.CharField(
                        choices=[
                            ("threshold", "Threshold"),
                            ("trend", "Trend"),
                            ("overdue", "Overdue"),
                            ("compliance", "Compliance"),
                            ("pattern", "Pattern"),
                        ],
                        max_length=15,
                    ),
                ),
                (
                    "data_source",
                    models.CharField(
                        help_text="Data source to monitor", max_length=100
                    ),
                ),
                (
                    "condition_config",
                    models.JSONField(
                        default=dict, help_text="Condition-specific configuration"
                    ),
                ),
                (
                    "severity",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("critical", "Critical"),
                        ],
                        default="medium",
                        max_length=10,
                    ),
                ),
                (
                    "alert_message_template",
                    models.TextField(help_text="Template for alert message"),
                ),
                (
                    "notify_roles",
                    models.JSONField(
                        default=list, help_text="Roles to notify when alert triggers"
                    ),
                ),
                (
                    "notification_methods",
                    models.JSONField(
                        default=list,
                        help_text="Notification methods (email, sms, etc.)",
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "check_frequency",
                    models.PositiveIntegerField(
                        default=3600, help_text="Check frequency in seconds"
                    ),
                ),
                ("last_checked", models.DateTimeField(blank=True, null=True)),
                ("last_triggered", models.DateTimeField(blank=True, null=True)),
                (
                    "applies_to_classes",
                    models.ManyToManyField(blank=True, to="students.class"),
                ),
                (
                    "applies_to_grades",
                    models.ManyToManyField(blank=True, to="students.grade"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="core.school"
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="HealthAlertInstance",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("title", models.CharField(max_length=200)),
                ("message", models.TextField()),
                (
                    "triggered_by_data",
                    models.JSONField(
                        default=dict, help_text="Data that triggered the alert"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("acknowledged", "Acknowledged"),
                            ("resolved", "Resolved"),
                            ("dismissed", "Dismissed"),
                        ],
                        default="active",
                        max_length=15,
                    ),
                ),
                ("triggered_at", models.DateTimeField(auto_now_add=True)),
                ("acknowledged_at", models.DateTimeField(blank=True, null=True)),
                ("resolved_at", models.DateTimeField(blank=True, null=True)),
                (
                    "actions_taken",
                    models.TextField(
                        blank=True, help_text="Actions taken to address the alert"
                    ),
                ),
                ("resolution_notes", models.TextField(blank=True)),
                (
                    "acknowledged_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="hr.employee",
                    ),
                ),
                (
                    "affected_students",
                    models.ManyToManyField(blank=True, to="students.student"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "resolved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="resolved_alerts",
                        to="hr.employee",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "alert_rule",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="instances",
                        to="health.healthalertrule",
                    ),
                ),
            ],
            options={
                "ordering": ["-triggered_at"],
            },
        ),
        migrations.CreateModel(
            name="HealthDashboardWidget",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                ("title", models.CharField(max_length=200)),
                (
                    "widget_type",
                    models.CharField(
                        choices=[
                            ("kpi_card", "KPI Card"),
                            ("chart", "Chart"),
                            ("table", "Data Table"),
                            ("alert_list", "Alert List"),
                            ("trend_indicator", "Trend Indicator"),
                            ("progress_bar", "Progress Bar"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField(blank=True)),
                (
                    "data_source",
                    models.CharField(
                        help_text="Data source identifier", max_length=100
                    ),
                ),
                (
                    "chart_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("line", "Line Chart"),
                            ("bar", "Bar Chart"),
                            ("pie", "Pie Chart"),
                            ("doughnut", "Doughnut Chart"),
                            ("area", "Area Chart"),
                        ],
                        max_length=15,
                    ),
                ),
                (
                    "configuration",
                    models.JSONField(
                        default=dict, help_text="Widget-specific configuration"
                    ),
                ),
                ("position_row", models.PositiveIntegerField(default=1)),
                ("position_col", models.PositiveIntegerField(default=1)),
                (
                    "width",
                    models.PositiveIntegerField(
                        default=6, help_text="Bootstrap column width (1-12)"
                    ),
                ),
                (
                    "height",
                    models.PositiveIntegerField(
                        default=300, help_text="Height in pixels"
                    ),
                ),
                (
                    "visible_to_roles",
                    models.JSONField(
                        default=list, help_text="List of roles that can see this widget"
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "refresh_interval",
                    models.PositiveIntegerField(
                        default=300, help_text="Refresh interval in seconds"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="core.school"
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "ordering": ["position_row", "position_col"],
            },
        ),
        migrations.CreateModel(
            name="HealthStatistics",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "statistic_type",
                    models.CharField(
                        choices=[
                            ("vaccination_rate", "Vaccination Rate"),
                            ("incident_rate", "Incident Rate"),
                            ("screening_completion", "Screening Completion"),
                            ("compliance_rate", "Compliance Rate"),
                            ("health_alert_count", "Health Alert Count"),
                            ("bmi_distribution", "BMI Distribution"),
                            (
                                "chronic_condition_prevalence",
                                "Chronic Condition Prevalence",
                            ),
                            ("medication_usage", "Medication Usage"),
                        ],
                        max_length=30,
                    ),
                ),
                (
                    "period_type",
                    models.CharField(
                        choices=[
                            ("daily", "Daily"),
                            ("weekly", "Weekly"),
                            ("monthly", "Monthly"),
                            ("quarterly", "Quarterly"),
                            ("yearly", "Yearly"),
                        ],
                        max_length=15,
                    ),
                ),
                ("period_start", models.DateField()),
                ("period_end", models.DateField()),
                ("total_count", models.PositiveIntegerField(default=0)),
                ("positive_count", models.PositiveIntegerField(default=0)),
                (
                    "percentage",
                    models.DecimalField(decimal_places=2, default=0, max_digits=5),
                ),
                (
                    "metadata",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Additional statistical data",
                    ),
                ),
                ("calculated_at", models.DateTimeField(auto_now_add=True)),
                (
                    "calculated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="hr.employee",
                    ),
                ),
                (
                    "class_group",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="students.class",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "grade",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="students.grade",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="core.school"
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "ordering": ["-period_end", "statistic_type"],
                "unique_together": {
                    (
                        "statistic_type",
                        "period_type",
                        "period_start",
                        "period_end",
                        "school",
                        "grade",
                        "class_group",
                    )
                },
            },
        ),
        migrations.CreateModel(
            name="VaccinationSchedule",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "vaccine_name",
                    models.CharField(
                        choices=[
                            ("bcg", "BCG (Tuberculosis)"),
                            ("hepatitis_b", "Hepatitis B"),
                            ("polio", "Polio (IPV/OPV)"),
                            ("dtp", "DTP (Diphtheria, Tetanus, Pertussis)"),
                            ("hib", "Hib (Haemophilus influenzae type b)"),
                            ("pneumococcal", "Pneumococcal"),
                            ("rotavirus", "Rotavirus"),
                            ("mmr", "MMR (Measles, Mumps, Rubella)"),
                            ("varicella", "Varicella (Chickenpox)"),
                            ("hepatitis_a", "Hepatitis A"),
                            ("meningococcal", "Meningococcal"),
                            ("hpv", "HPV (Human Papillomavirus)"),
                            ("influenza", "Influenza (Flu)"),
                            ("covid19", "COVID-19"),
                            ("other", "Other"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "schedule_type",
                    models.CharField(
                        choices=[
                            ("routine", "Routine Vaccination"),
                            ("catch_up", "Catch-up Vaccination"),
                            ("travel", "Travel Vaccination"),
                            ("outbreak", "Outbreak Response"),
                            ("booster", "Booster Shot"),
                        ],
                        default="routine",
                        max_length=15,
                    ),
                ),
                ("due_date", models.DateField()),
                ("scheduled_date", models.DateField(blank=True, null=True)),
                ("dose_number", models.PositiveIntegerField(default=1)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("scheduled", "Scheduled"),
                            ("due", "Due"),
                            ("overdue", "Overdue"),
                            ("completed", "Completed"),
                            ("exempted", "Exempted"),
                            ("deferred", "Deferred"),
                        ],
                        default="scheduled",
                        max_length=15,
                    ),
                ),
                ("completed_date", models.DateField(blank=True, null=True)),
                (
                    "exemption_reason",
                    models.TextField(blank=True, help_text="Reason for exemption"),
                ),
                (
                    "deferral_reason",
                    models.TextField(blank=True, help_text="Reason for deferral"),
                ),
                ("deferral_until", models.DateField(blank=True, null=True)),
                ("reminder_sent", models.BooleanField(default=False)),
                ("reminder_date", models.DateField(blank=True, null=True)),
                ("notes", models.TextField(blank=True)),
                (
                    "administered_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="hr.employee",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "health_profile",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="vaccination_schedules",
                        to="health.healthprofile",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "ordering": ["due_date", "vaccine_name"],
                "unique_together": {("health_profile", "vaccine_name", "dose_number")},
            },
        ),
    ]
