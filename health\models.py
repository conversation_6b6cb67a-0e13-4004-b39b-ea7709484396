from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from decimal import Decimal
from core.models import BaseModel


class HealthProfile(BaseModel):
    """Student health profile with basic health information"""
    
    BLOOD_TYPE_CHOICES = [
        ('A+', 'A+'),
        ('A-', 'A-'),
        ('B+', 'B+'),
        ('B-', 'B-'),
        ('AB+', 'AB+'),
        ('AB-', 'AB-'),
        ('O+', 'O+'),
        ('O-', 'O-'),
        ('unknown', 'Unknown'),
    ]
    
    student = models.OneToOneField('students.Student', on_delete=models.CASCADE, related_name='health_profile')
    blood_type = models.CharField(max_length=10, choices=BLOOD_TYPE_CHOICES, default='unknown')
    height = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True, help_text="Height in cm")
    weight = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True, help_text="Weight in kg")
    
    # Medical information
    chronic_conditions = models.TextField(blank=True, help_text="List of chronic medical conditions")
    disabilities = models.TextField(blank=True, help_text="Physical or learning disabilities")
    special_needs = models.TextField(blank=True, help_text="Special medical or educational needs")
    
    # Emergency information
    emergency_contact_name = models.CharField(max_length=200, blank=True)
    emergency_contact_relationship = models.CharField(max_length=100, blank=True)
    emergency_contact_phone = models.CharField(max_length=20, blank=True)
    emergency_contact_phone2 = models.CharField(max_length=20, blank=True)
    emergency_contact_address = models.TextField(blank=True)
    
    # Insurance information
    insurance_provider = models.CharField(max_length=200, blank=True)
    insurance_policy_number = models.CharField(max_length=100, blank=True)
    insurance_expiry_date = models.DateField(null=True, blank=True)
    
    # Doctor information
    family_doctor_name = models.CharField(max_length=200, blank=True)
    family_doctor_phone = models.CharField(max_length=20, blank=True)
    family_doctor_address = models.TextField(blank=True)
    
    # Additional notes
    medical_notes = models.TextField(blank=True, help_text="Additional medical information")
    last_physical_exam = models.DateField(null=True, blank=True)
    
    class Meta:
        ordering = ['student__first_name', 'student__last_name']
    
    def __str__(self):
        return f"Health Profile - {self.student.get_full_name()}"
    
    @property
    def bmi(self):
        """Calculate BMI if height and weight are available"""
        if self.height and self.weight and self.height > 0:
            height_m = self.height / 100  # Convert cm to meters
            return round(float(self.weight) / (float(height_m) ** 2), 2)
        return None
    
    @property
    def bmi_category(self):
        """Get BMI category"""
        bmi = self.bmi
        if not bmi:
            return "Unknown"
        
        if bmi < 18.5:
            return "Underweight"
        elif bmi < 25:
            return "Normal weight"
        elif bmi < 30:
            return "Overweight"
        else:
            return "Obese"


class Allergy(BaseModel):
    """Student allergies"""
    
    SEVERITY_CHOICES = [
        ('mild', 'Mild'),
        ('moderate', 'Moderate'),
        ('severe', 'Severe'),
        ('life_threatening', 'Life Threatening'),
    ]
    
    ALLERGY_TYPE_CHOICES = [
        ('food', 'Food Allergy'),
        ('environmental', 'Environmental'),
        ('medication', 'Medication'),
        ('insect', 'Insect Sting'),
        ('contact', 'Contact Allergy'),
        ('other', 'Other'),
    ]
    
    health_profile = models.ForeignKey(HealthProfile, on_delete=models.CASCADE, related_name='allergies')
    allergy_type = models.CharField(max_length=20, choices=ALLERGY_TYPE_CHOICES)
    allergen = models.CharField(max_length=200, help_text="Specific allergen (e.g., peanuts, pollen)")
    severity = models.CharField(max_length=20, choices=SEVERITY_CHOICES)
    symptoms = models.TextField(help_text="Typical symptoms experienced")
    treatment = models.TextField(blank=True, help_text="Treatment or medication required")
    notes = models.TextField(blank=True)
    diagnosed_date = models.DateField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        ordering = ['-severity', 'allergen']
    
    def __str__(self):
        return f"{self.health_profile.student.get_full_name()} - {self.allergen} ({self.get_severity_display()})"


class Medication(BaseModel):
    """Student medications"""
    
    FREQUENCY_CHOICES = [
        ('once_daily', 'Once Daily'),
        ('twice_daily', 'Twice Daily'),
        ('three_times_daily', 'Three Times Daily'),
        ('four_times_daily', 'Four Times Daily'),
        ('as_needed', 'As Needed'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('other', 'Other'),
    ]
    
    ADMINISTRATION_ROUTE_CHOICES = [
        ('oral', 'Oral'),
        ('injection', 'Injection'),
        ('inhaler', 'Inhaler'),
        ('topical', 'Topical'),
        ('nasal', 'Nasal'),
        ('eye_drops', 'Eye Drops'),
        ('other', 'Other'),
    ]
    
    health_profile = models.ForeignKey(HealthProfile, on_delete=models.CASCADE, related_name='medications')
    medication_name = models.CharField(max_length=200)
    dosage = models.CharField(max_length=100, help_text="e.g., 10mg, 1 tablet")
    frequency = models.CharField(max_length=20, choices=FREQUENCY_CHOICES)
    administration_route = models.CharField(max_length=20, choices=ADMINISTRATION_ROUTE_CHOICES, default='oral')
    
    # Timing
    start_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)
    administration_times = models.CharField(max_length=200, blank=True, help_text="Specific times (e.g., 8:00 AM, 2:00 PM)")
    
    # Medical information
    prescribed_by = models.CharField(max_length=200, blank=True, help_text="Prescribing doctor")
    reason = models.TextField(help_text="Reason for medication")
    side_effects = models.TextField(blank=True, help_text="Known side effects to watch for")
    special_instructions = models.TextField(blank=True)
    
    # School administration
    can_self_administer = models.BooleanField(default=False, help_text="Student can take medication themselves")
    requires_supervision = models.BooleanField(default=True, help_text="Requires adult supervision")
    stored_at_school = models.BooleanField(default=False, help_text="Medication stored at school")
    storage_location = models.CharField(max_length=200, blank=True, help_text="Where medication is stored")
    
    is_active = models.BooleanField(default=True)
    
    class Meta:
        ordering = ['medication_name', '-start_date']
    
    def __str__(self):
        return f"{self.health_profile.student.get_full_name()} - {self.medication_name}"
    
    @property
    def is_current(self):
        """Check if medication is currently active"""
        if not self.is_active:
            return False
        
        today = timezone.now().date()
        if self.start_date > today:
            return False
        
        if self.end_date and self.end_date < today:
            return False
        
        return True


class MedicalHistory(BaseModel):
    """Medical history records for students"""
    
    RECORD_TYPE_CHOICES = [
        ('illness', 'Illness'),
        ('injury', 'Injury'),
        ('surgery', 'Surgery'),
        ('hospitalization', 'Hospitalization'),
        ('vaccination', 'Vaccination'),
        ('checkup', 'Regular Checkup'),
        ('dental', 'Dental'),
        ('vision', 'Vision/Hearing'),
        ('other', 'Other'),
    ]
    
    health_profile = models.ForeignKey(HealthProfile, on_delete=models.CASCADE, related_name='medical_history')
    record_type = models.CharField(max_length=20, choices=RECORD_TYPE_CHOICES)
    date = models.DateField()
    title = models.CharField(max_length=200, help_text="Brief title of the medical event")
    description = models.TextField(help_text="Detailed description")
    
    # Medical details
    diagnosis = models.CharField(max_length=200, blank=True)
    treatment = models.TextField(blank=True, help_text="Treatment provided")
    medications_prescribed = models.TextField(blank=True)
    follow_up_required = models.BooleanField(default=False)
    follow_up_date = models.DateField(null=True, blank=True)
    
    # Provider information
    healthcare_provider = models.CharField(max_length=200, blank=True)
    facility = models.CharField(max_length=200, blank=True, help_text="Hospital, clinic, etc.")
    
    # Documentation
    documents = models.FileField(upload_to='health/medical_records/', blank=True, help_text="Medical reports, prescriptions, etc.")
    
    # Impact on school
    affects_school_activities = models.BooleanField(default=False)
    activity_restrictions = models.TextField(blank=True, help_text="Any restrictions on school activities")
    return_to_school_date = models.DateField(null=True, blank=True)
    
    notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-date']
        verbose_name_plural = "Medical histories"
    
    def __str__(self):
        return f"{self.health_profile.student.get_full_name()} - {self.title} ({self.date})"


class Vaccination(BaseModel):
    """Vaccination records"""
    
    VACCINE_CHOICES = [
        # Common childhood vaccines
        ('bcg', 'BCG (Tuberculosis)'),
        ('hepatitis_b', 'Hepatitis B'),
        ('polio', 'Polio (IPV/OPV)'),
        ('dtp', 'DTP (Diphtheria, Tetanus, Pertussis)'),
        ('hib', 'Hib (Haemophilus influenzae type b)'),
        ('pneumococcal', 'Pneumococcal'),
        ('rotavirus', 'Rotavirus'),
        ('mmr', 'MMR (Measles, Mumps, Rubella)'),
        ('varicella', 'Varicella (Chickenpox)'),
        ('hepatitis_a', 'Hepatitis A'),
        ('meningococcal', 'Meningococcal'),
        ('hpv', 'HPV (Human Papillomavirus)'),
        ('influenza', 'Influenza (Flu)'),
        ('covid19', 'COVID-19'),
        ('other', 'Other'),
    ]
    
    health_profile = models.ForeignKey(HealthProfile, on_delete=models.CASCADE, related_name='vaccinations')
    vaccine_name = models.CharField(max_length=50, choices=VACCINE_CHOICES)
    vaccine_brand = models.CharField(max_length=100, blank=True, help_text="Brand name if applicable")
    date_administered = models.DateField()
    dose_number = models.PositiveIntegerField(default=1, help_text="Which dose in the series")
    total_doses_required = models.PositiveIntegerField(default=1, help_text="Total doses in the series")
    
    # Administration details
    administered_by = models.CharField(max_length=200, blank=True, help_text="Healthcare provider")
    facility = models.CharField(max_length=200, blank=True, help_text="Where vaccine was given")
    batch_number = models.CharField(max_length=100, blank=True)
    expiry_date = models.DateField(null=True, blank=True)
    
    # Next dose information
    next_dose_due = models.DateField(null=True, blank=True)
    
    # Reactions
    adverse_reactions = models.TextField(blank=True, help_text="Any adverse reactions experienced")
    
    # Documentation
    certificate = models.FileField(upload_to='health/vaccination_certificates/', blank=True)
    
    notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-date_administered']
        unique_together = ['health_profile', 'vaccine_name', 'dose_number']
    
    def __str__(self):
        return f"{self.health_profile.student.get_full_name()} - {self.get_vaccine_name_display()} (Dose {self.dose_number})"
    
    @property
    def is_series_complete(self):
        """Check if vaccination series is complete"""
        return self.dose_number >= self.total_doses_required
    
    @property
    def next_dose_overdue(self):
        """Check if next dose is overdue"""
        if not self.next_dose_due or self.is_series_complete:
            return False
        return self.next_dose_due < timezone.now().date()


class HealthScreening(BaseModel):
    """Health screening records (vision, hearing, dental, etc.)"""
    
    SCREENING_TYPE_CHOICES = [
        ('vision', 'Vision Screening'),
        ('hearing', 'Hearing Screening'),
        ('dental', 'Dental Screening'),
        ('scoliosis', 'Scoliosis Screening'),
        ('bmi', 'BMI/Growth Screening'),
        ('blood_pressure', 'Blood Pressure'),
        ('general', 'General Health Screening'),
        ('other', 'Other'),
    ]
    
    RESULT_CHOICES = [
        ('pass', 'Pass'),
        ('refer', 'Refer for Further Evaluation'),
        ('fail', 'Fail'),
        ('incomplete', 'Incomplete'),
    ]
    
    health_profile = models.ForeignKey(HealthProfile, on_delete=models.CASCADE, related_name='screenings')
    screening_type = models.CharField(max_length=20, choices=SCREENING_TYPE_CHOICES)
    date = models.DateField()
    result = models.CharField(max_length=20, choices=RESULT_CHOICES)
    
    # Screening details
    screened_by = models.CharField(max_length=200, blank=True, help_text="Person who conducted screening")
    measurements = models.JSONField(default=dict, blank=True, help_text="Specific measurements or results")
    
    # Results and recommendations
    findings = models.TextField(blank=True, help_text="Detailed findings")
    recommendations = models.TextField(blank=True, help_text="Recommendations for follow-up")
    referral_needed = models.BooleanField(default=False)
    referral_to = models.CharField(max_length=200, blank=True, help_text="Specialist or facility to refer to")
    
    # Follow-up
    follow_up_completed = models.BooleanField(default=False)
    follow_up_date = models.DateField(null=True, blank=True)
    follow_up_results = models.TextField(blank=True)
    
    # Parent notification
    parent_notified = models.BooleanField(default=False)
    parent_notification_date = models.DateField(null=True, blank=True)
    parent_notification_method = models.CharField(max_length=100, blank=True, help_text="Email, phone, letter, etc.")
    
    notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-date']
    
    def __str__(self):
        return f"{self.health_profile.student.get_full_name()} - {self.get_screening_type_display()} ({self.date})"


class HealthAlert(BaseModel):
    """Health alerts for students requiring special attention"""
    
    ALERT_TYPE_CHOICES = [
        ('allergy', 'Severe Allergy'),
        ('medication', 'Critical Medication'),
        ('condition', 'Medical Condition'),
        ('emergency', 'Emergency Contact'),
        ('restriction', 'Activity Restriction'),
        ('other', 'Other'),
    ]
    
    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]
    
    health_profile = models.ForeignKey(HealthProfile, on_delete=models.CASCADE, related_name='alerts')
    alert_type = models.CharField(max_length=20, choices=ALERT_TYPE_CHOICES)
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium')
    title = models.CharField(max_length=200)
    description = models.TextField()
    
    # Alert settings
    show_on_dashboard = models.BooleanField(default=True, help_text="Show alert on student dashboard")
    notify_teachers = models.BooleanField(default=False, help_text="Notify all student's teachers")
    notify_staff = models.BooleanField(default=False, help_text="Notify relevant staff members")
    
    # Validity
    is_active = models.BooleanField(default=True)
    start_date = models.DateField(default=timezone.now)
    end_date = models.DateField(null=True, blank=True)
    
    # Actions
    action_required = models.TextField(blank=True, help_text="What action should be taken")
    emergency_instructions = models.TextField(blank=True, help_text="Emergency response instructions")
    
    notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-priority', '-created_at']
    
    def __str__(self):
        return f"{self.health_profile.student.get_full_name()} - {self.title} ({self.get_priority_display()})"
    
    @property
    def is_current(self):
        """Check if alert is currently active"""
        if not self.is_active:
            return False
        
        today = timezone.now().date()
        if self.start_date > today:
            return False
        
        if self.end_date and self.end_date < today:
            return False
        
        return True


class MedicalIncident(BaseModel):
    """Medical incidents that occur at school"""
    
    INCIDENT_TYPE_CHOICES = [
        ('injury', 'Injury'),
        ('illness', 'Sudden Illness'),
        ('allergic_reaction', 'Allergic Reaction'),
        ('medication_issue', 'Medication Issue'),
        ('behavioral', 'Behavioral/Mental Health'),
        ('emergency', 'Medical Emergency'),
        ('other', 'Other'),
    ]
    
    SEVERITY_CHOICES = [
        ('minor', 'Minor'),
        ('moderate', 'Moderate'),
        ('serious', 'Serious'),
        ('critical', 'Critical'),
    ]
    
    STATUS_CHOICES = [
        ('reported', 'Reported'),
        ('in_treatment', 'In Treatment'),
        ('resolved', 'Resolved'),
        ('referred', 'Referred to External Care'),
        ('follow_up_required', 'Follow-up Required'),
    ]
    
    # Basic incident information
    incident_id = models.CharField(max_length=50, unique=True, help_text="Auto-generated incident ID")
    health_profile = models.ForeignKey(HealthProfile, on_delete=models.CASCADE, related_name='incidents')
    incident_type = models.CharField(max_length=20, choices=INCIDENT_TYPE_CHOICES)
    severity = models.CharField(max_length=15, choices=SEVERITY_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='reported')
    
    # When and where
    incident_date = models.DateField()
    incident_time = models.TimeField()
    location = models.CharField(max_length=200, help_text="Where the incident occurred")
    
    # Description
    description = models.TextField(help_text="Detailed description of what happened")
    symptoms_observed = models.TextField(blank=True, help_text="Symptoms observed by staff")
    
    # People involved
    reported_by = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True, related_name='reported_incidents')
    witnessed_by = models.TextField(blank=True, help_text="Names of witnesses")
    
    # Initial response
    immediate_action_taken = models.TextField(blank=True, help_text="What was done immediately")
    first_aid_given = models.BooleanField(default=False)
    first_aid_details = models.TextField(blank=True, help_text="Details of first aid provided")
    
    # External care
    emergency_services_called = models.BooleanField(default=False)
    emergency_service_details = models.TextField(blank=True, help_text="Which services called and response")
    transported_to_hospital = models.BooleanField(default=False)
    hospital_details = models.TextField(blank=True, help_text="Hospital name and details")
    
    # Parent/guardian notification
    parent_notified = models.BooleanField(default=False)
    parent_notification_time = models.DateTimeField(null=True, blank=True)
    parent_notification_method = models.CharField(max_length=100, blank=True, help_text="Phone, email, in-person, etc.")
    parent_response = models.TextField(blank=True, help_text="Parent's response or actions")
    
    # Follow-up
    follow_up_required = models.BooleanField(default=False)
    follow_up_instructions = models.TextField(blank=True)
    return_to_class_time = models.DateTimeField(null=True, blank=True)
    activity_restrictions = models.TextField(blank=True, help_text="Any restrictions on student activities")
    
    # Documentation
    photos_taken = models.BooleanField(default=False, help_text="Were photos taken of injuries?")
    incident_report_file = models.FileField(upload_to='health/incident_reports/', blank=True)
    
    # Resolution
    resolved_date = models.DateField(null=True, blank=True)
    resolution_notes = models.TextField(blank=True, help_text="How the incident was resolved")
    
    # Additional notes
    notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-incident_date', '-incident_time']
    
    def __str__(self):
        return f"{self.incident_id} - {self.health_profile.student.get_full_name()} ({self.get_incident_type_display()})"
    
    def save(self, *args, **kwargs):
        if not self.incident_id:
            # Generate incident ID: INC-YYYY-NNNN
            from django.db.models import Max
            today = timezone.now().date()
            year = today.year
            
            # Get the highest incident number for this year
            last_incident = MedicalIncident.objects.filter(
                incident_id__startswith=f'INC-{year}-'
            ).aggregate(Max('incident_id'))
            
            if last_incident['incident_id__max']:
                last_number = int(last_incident['incident_id__max'].split('-')[-1])
                new_number = last_number + 1
            else:
                new_number = 1
            
            self.incident_id = f'INC-{year}-{new_number:04d}'
        
        super().save(*args, **kwargs)
    
    @property
    def is_recent(self):
        """Check if incident occurred in the last 24 hours"""
        incident_datetime = timezone.datetime.combine(self.incident_date, self.incident_time)
        incident_datetime = timezone.make_aware(incident_datetime)
        return timezone.now() - incident_datetime < timezone.timedelta(hours=24)
    
    @property
    def requires_attention(self):
        """Check if incident requires immediate attention"""
        return self.status in ['reported', 'in_treatment'] or self.severity in ['serious', 'critical']


class IncidentTreatment(BaseModel):
    """Treatment provided for medical incidents"""
    
    TREATMENT_TYPE_CHOICES = [
        ('first_aid', 'First Aid'),
        ('medication', 'Medication Administration'),
        ('rest', 'Rest/Observation'),
        ('ice_pack', 'Ice Pack Application'),
        ('bandaging', 'Bandaging/Dressing'),
        ('cleaning', 'Wound Cleaning'),
        ('comfort', 'Comfort Measures'),
        ('referral', 'Referral to Healthcare Provider'),
        ('other', 'Other Treatment'),
    ]
    
    incident = models.ForeignKey(MedicalIncident, on_delete=models.CASCADE, related_name='treatments')
    treatment_type = models.CharField(max_length=20, choices=TREATMENT_TYPE_CHOICES)
    treatment_time = models.DateTimeField(default=timezone.now)
    administered_by = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True)
    
    # Treatment details
    treatment_description = models.TextField(help_text="Detailed description of treatment provided")
    medication_given = models.CharField(max_length=200, blank=True, help_text="Name and dosage of medication")
    supplies_used = models.TextField(blank=True, help_text="Medical supplies used")
    
    # Response
    student_response = models.TextField(blank=True, help_text="How the student responded to treatment")
    effectiveness = models.CharField(
        max_length=20,
        choices=[
            ('effective', 'Effective'),
            ('partially_effective', 'Partially Effective'),
            ('not_effective', 'Not Effective'),
            ('too_early', 'Too Early to Tell'),
        ],
        blank=True
    )
    
    # Follow-up
    additional_treatment_needed = models.BooleanField(default=False)
    next_treatment_time = models.DateTimeField(null=True, blank=True)
    
    notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['treatment_time']
    
    def __str__(self):
        return f"{self.incident.incident_id} - {self.get_treatment_type_display()} at {self.treatment_time.strftime('%H:%M')}"


class IncidentFollowUp(BaseModel):
    """Follow-up actions and monitoring for incidents"""
    
    FOLLOW_UP_TYPE_CHOICES = [
        ('check_in', 'Student Check-in'),
        ('parent_contact', 'Parent Contact'),
        ('medical_referral', 'Medical Referral'),
        ('activity_modification', 'Activity Modification'),
        ('documentation', 'Documentation Update'),
        ('investigation', 'Incident Investigation'),
        ('prevention', 'Prevention Measures'),
        ('other', 'Other'),
    ]
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    incident = models.ForeignKey(MedicalIncident, on_delete=models.CASCADE, related_name='follow_ups')
    follow_up_type = models.CharField(max_length=25, choices=FOLLOW_UP_TYPE_CHOICES)
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='pending')
    
    # Scheduling
    scheduled_date = models.DateField()
    scheduled_time = models.TimeField(null=True, blank=True)
    assigned_to = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True)
    
    # Description
    description = models.TextField(help_text="What needs to be done")
    priority = models.CharField(
        max_length=10,
        choices=[
            ('low', 'Low'),
            ('medium', 'Medium'),
            ('high', 'High'),
            ('urgent', 'Urgent'),
        ],
        default='medium'
    )
    
    # Completion
    completed_date = models.DateField(null=True, blank=True)
    completed_by = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True, related_name='completed_follow_ups')
    outcome = models.TextField(blank=True, help_text="Results of the follow-up action")
    
    # Next steps
    additional_follow_up_needed = models.BooleanField(default=False)
    next_follow_up_date = models.DateField(null=True, blank=True)
    
    notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['scheduled_date', 'scheduled_time']
    
    def __str__(self):
        return f"{self.incident.incident_id} - {self.get_follow_up_type_display()} ({self.get_status_display()})"
    
    @property
    def is_overdue(self):
        """Check if follow-up is overdue"""
        if self.status == 'completed':
            return False
        
        today = timezone.now().date()
        return self.scheduled_date < today


class IncidentNotification(BaseModel):
    """Notifications sent regarding incidents"""
    
    NOTIFICATION_TYPE_CHOICES = [
        ('parent_immediate', 'Immediate Parent Notification'),
        ('parent_update', 'Parent Update'),
        ('staff_alert', 'Staff Alert'),
        ('administration', 'Administration Notification'),
        ('teacher_info', 'Teacher Information'),
        ('follow_up_reminder', 'Follow-up Reminder'),
        ('other', 'Other'),
    ]
    
    METHOD_CHOICES = [
        ('phone', 'Phone Call'),
        ('sms', 'SMS/Text Message'),
        ('email', 'Email'),
        ('in_person', 'In Person'),
        ('letter', 'Written Letter'),
        ('app_notification', 'App Notification'),
        ('other', 'Other'),
    ]
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('delivered', 'Delivered'),
        ('read', 'Read/Acknowledged'),
        ('failed', 'Failed'),
    ]
    
    incident = models.ForeignKey(MedicalIncident, on_delete=models.CASCADE, related_name='notifications')
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPE_CHOICES)
    method = models.CharField(max_length=20, choices=METHOD_CHOICES)
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='pending')
    
    # Recipient information
    recipient_name = models.CharField(max_length=200)
    recipient_relationship = models.CharField(max_length=100, help_text="Parent, teacher, administrator, etc.")
    recipient_contact = models.CharField(max_length=200, help_text="Phone, email, or other contact info")
    
    # Message content
    subject = models.CharField(max_length=200, blank=True)
    message = models.TextField()
    
    # Timing
    scheduled_time = models.DateTimeField(default=timezone.now)
    sent_time = models.DateTimeField(null=True, blank=True)
    delivered_time = models.DateTimeField(null=True, blank=True)
    read_time = models.DateTimeField(null=True, blank=True)
    
    # Response
    response_received = models.BooleanField(default=False)
    response_content = models.TextField(blank=True)
    response_time = models.DateTimeField(null=True, blank=True)
    
    # Sending details
    sent_by = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True)
    
    notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-scheduled_time']
    
    def __str__(self):
        return f"{self.incident.incident_id} - {self.get_notification_type_display()} to {self.recipient_name}"
    
    def mark_as_sent(self):
        """Mark notification as sent"""
        self.status = 'sent'
        self.sent_time = timezone.now()
        self.save()
    
    def mark_as_delivered(self):
        """Mark notification as delivered"""
        self.status = 'delivered'
        self.delivered_time = timezone.now()
        self.save()
    
    def mark_as_read(self):
        """Mark notification as read/acknowledged"""
        self.status = 'read'
        self.read_time = timezone.now()
        self.save()


class HealthScreeningSchedule(BaseModel):
    """Health screening schedules for students"""
    
    SCREENING_TYPE_CHOICES = [
        ('vision', 'Vision Screening'),
        ('hearing', 'Hearing Screening'),
        ('dental', 'Dental Screening'),
        ('physical', 'Physical Examination'),
        ('bmi', 'BMI Assessment'),
        ('scoliosis', 'Scoliosis Screening'),
        ('immunization', 'Immunization Check'),
        ('mental_health', 'Mental Health Screening'),
        ('other', 'Other Screening'),
    ]
    
    FREQUENCY_CHOICES = [
        ('annual', 'Annual'),
        ('biannual', 'Bi-annual'),
        ('quarterly', 'Quarterly'),
        ('monthly', 'Monthly'),
        ('as_needed', 'As Needed'),
    ]
    
    STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('rescheduled', 'Rescheduled'),
    ]
    
    # Basic information
    health_profile = models.ForeignKey(HealthProfile, on_delete=models.CASCADE, related_name='screening_schedules')
    screening_type = models.CharField(max_length=20, choices=SCREENING_TYPE_CHOICES)
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    
    # Scheduling
    scheduled_date = models.DateField()
    scheduled_time = models.TimeField(null=True, blank=True)
    frequency = models.CharField(max_length=15, choices=FREQUENCY_CHOICES, default='annual')
    
    # Status and assignment
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='scheduled')
    assigned_to = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True, blank=True)
    
    # Location and requirements
    location = models.CharField(max_length=200, blank=True, help_text="Where the screening will take place")
    requirements = models.TextField(blank=True, help_text="Special requirements or preparations")
    
    # Completion tracking
    completed_date = models.DateField(null=True, blank=True)
    completed_by = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True, blank=True, related_name='completed_screenings')
    
    # Results and follow-up
    results_available = models.BooleanField(default=False)
    follow_up_required = models.BooleanField(default=False)
    follow_up_notes = models.TextField(blank=True)
    
    # Next screening
    next_screening_date = models.DateField(null=True, blank=True)
    
    notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['scheduled_date', 'scheduled_time']
    
    def __str__(self):
        return f"{self.health_profile.student.get_full_name()} - {self.get_screening_type_display()} ({self.scheduled_date})"
    
    def save(self, *args, **kwargs):
        # Calculate next screening date based on frequency
        if self.status == 'completed' and self.completed_date and not self.next_screening_date:
            from dateutil.relativedelta import relativedelta
            
            if self.frequency == 'annual':
                self.next_screening_date = self.completed_date + relativedelta(years=1)
            elif self.frequency == 'biannual':
                self.next_screening_date = self.completed_date + relativedelta(months=6)
            elif self.frequency == 'quarterly':
                self.next_screening_date = self.completed_date + relativedelta(months=3)
            elif self.frequency == 'monthly':
                self.next_screening_date = self.completed_date + relativedelta(months=1)
        
        super().save(*args, **kwargs)
    
    @property
    def is_overdue(self):
        """Check if screening is overdue"""
        if self.status == 'completed':
            return False
        return self.scheduled_date < timezone.now().date()
    
    @property
    def days_until_due(self):
        """Calculate days until screening is due"""
        if self.status == 'completed':
            return None
        delta = self.scheduled_date - timezone.now().date()
        return delta.days


class MedicalAppointment(BaseModel):
    """Medical appointments for students"""
    
    APPOINTMENT_TYPE_CHOICES = [
        ('routine_checkup', 'Routine Checkup'),
        ('follow_up', 'Follow-up Visit'),
        ('specialist', 'Specialist Consultation'),
        ('emergency', 'Emergency Visit'),
        ('vaccination', 'Vaccination'),
        ('dental', 'Dental Appointment'),
        ('vision', 'Vision Care'),
        ('mental_health', 'Mental Health'),
        ('therapy', 'Therapy Session'),
        ('other', 'Other'),
    ]
    
    STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('confirmed', 'Confirmed'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('no_show', 'No Show'),
        ('rescheduled', 'Rescheduled'),
    ]
    
    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]
    
    # Basic information
    health_profile = models.ForeignKey(HealthProfile, on_delete=models.CASCADE, related_name='appointments')
    appointment_type = models.CharField(max_length=20, choices=APPOINTMENT_TYPE_CHOICES)
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    
    # Scheduling
    appointment_date = models.DateField()
    appointment_time = models.TimeField()
    duration_minutes = models.PositiveIntegerField(default=30, help_text="Appointment duration in minutes")
    
    # Status and priority
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='scheduled')
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium')
    
    # Provider information
    provider_name = models.CharField(max_length=200, help_text="Doctor or healthcare provider name")
    provider_contact = models.CharField(max_length=100, blank=True, help_text="Provider contact information")
    clinic_hospital = models.CharField(max_length=200, blank=True, help_text="Clinic or hospital name")
    address = models.TextField(blank=True, help_text="Appointment location address")
    
    # School coordination
    school_coordinator = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True, blank=True)
    excused_from_class = models.BooleanField(default=False, help_text="Student excused from classes")
    transportation_arranged = models.BooleanField(default=False)
    
    # Parent/guardian information
    parent_notified = models.BooleanField(default=False)
    parent_attending = models.BooleanField(default=False)
    parent_notification_sent = models.DateTimeField(null=True, blank=True)
    
    # Completion and results
    completed_date = models.DateField(null=True, blank=True)
    appointment_summary = models.TextField(blank=True, help_text="Summary of appointment")
    recommendations = models.TextField(blank=True, help_text="Doctor's recommendations")
    follow_up_required = models.BooleanField(default=False)
    follow_up_date = models.DateField(null=True, blank=True)
    
    # Documentation
    medical_report_file = models.FileField(upload_to='health/medical_reports/', blank=True)
    prescription_file = models.FileField(upload_to='health/prescriptions/', blank=True)
    
    notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['appointment_date', 'appointment_time']
    
    def __str__(self):
        return f"{self.health_profile.student.get_full_name()} - {self.title} ({self.appointment_date})"
    
    @property
    def is_upcoming(self):
        """Check if appointment is upcoming (within next 7 days)"""
        if self.status in ['completed', 'cancelled', 'no_show']:
            return False
        
        today = timezone.now().date()
        return self.appointment_date >= today and self.appointment_date <= today + timezone.timedelta(days=7)
    
    @property
    def is_overdue(self):
        """Check if appointment is overdue"""
        if self.status in ['completed', 'cancelled', 'no_show']:
            return False
        return self.appointment_date < timezone.now().date()


class HealthTrendAnalysis(BaseModel):
    """Health trend analysis for students"""
    
    TREND_TYPE_CHOICES = [
        ('bmi', 'BMI Trend'),
        ('height_weight', 'Height/Weight Growth'),
        ('vision', 'Vision Changes'),
        ('hearing', 'Hearing Changes'),
        ('blood_pressure', 'Blood Pressure'),
        ('attendance', 'Health-related Attendance'),
        ('medication_compliance', 'Medication Compliance'),
        ('incident_frequency', 'Incident Frequency'),
        ('screening_results', 'Screening Results'),
        ('other', 'Other Trend'),
    ]
    
    TREND_DIRECTION_CHOICES = [
        ('improving', 'Improving'),
        ('stable', 'Stable'),
        ('declining', 'Declining'),
        ('concerning', 'Concerning'),
    ]
    
    # Basic information
    health_profile = models.ForeignKey(HealthProfile, on_delete=models.CASCADE, related_name='trend_analyses')
    trend_type = models.CharField(max_length=25, choices=TREND_TYPE_CHOICES)
    title = models.CharField(max_length=200)
    
    # Analysis period
    analysis_start_date = models.DateField()
    analysis_end_date = models.DateField()
    
    # Trend data
    trend_direction = models.CharField(max_length=15, choices=TREND_DIRECTION_CHOICES)
    trend_description = models.TextField(help_text="Detailed description of the trend")
    
    # Metrics
    baseline_value = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    current_value = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    change_percentage = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    
    # Analysis results
    key_findings = models.TextField(help_text="Key findings from the analysis")
    recommendations = models.TextField(blank=True, help_text="Recommendations based on trends")
    action_required = models.BooleanField(default=False)
    
    # Follow-up
    next_analysis_date = models.DateField(null=True, blank=True)
    analyzed_by = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True, blank=True)
    
    notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-analysis_end_date']
    
    def __str__(self):
        return f"{self.health_profile.student.get_full_name()} - {self.get_trend_type_display()} ({self.analysis_end_date})"
    
    @property
    def trend_summary(self):
        """Generate a summary of the trend"""
        if self.baseline_value and self.current_value:
            change = self.current_value - self.baseline_value
            if change > 0:
                return f"Increased by {abs(change)} ({self.change_percentage}%)"
            elif change < 0:
                return f"Decreased by {abs(change)} ({self.change_percentage}%)"
            else:
                return "No change"
        return "Trend analysis available"


class ComplianceMonitoring(BaseModel):
    """Compliance monitoring for health requirements"""
    
    COMPLIANCE_TYPE_CHOICES = [
        ('vaccination', 'Vaccination Requirements'),
        ('screening', 'Health Screening Requirements'),
        ('medication', 'Medication Compliance'),
        ('dietary', 'Dietary Restrictions'),
        ('activity', 'Activity Restrictions'),
        ('documentation', 'Medical Documentation'),
        ('emergency_info', 'Emergency Information'),
        ('other', 'Other Compliance'),
    ]
    
    STATUS_CHOICES = [
        ('compliant', 'Compliant'),
        ('non_compliant', 'Non-Compliant'),
        ('partially_compliant', 'Partially Compliant'),
        ('pending', 'Pending Review'),
        ('exempt', 'Exempt'),
    ]
    
    # Basic information
    health_profile = models.ForeignKey(HealthProfile, on_delete=models.CASCADE, related_name='compliance_records')
    compliance_type = models.CharField(max_length=20, choices=COMPLIANCE_TYPE_CHOICES)
    requirement_name = models.CharField(max_length=200)
    description = models.TextField(help_text="Description of the compliance requirement")
    
    # Compliance status
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    compliance_date = models.DateField(null=True, blank=True, help_text="Date when compliance was achieved")
    expiry_date = models.DateField(null=True, blank=True, help_text="Date when compliance expires")
    
    # Requirements
    required_by_date = models.DateField(help_text="Date by which compliance is required")
    is_mandatory = models.BooleanField(default=True)
    
    # Monitoring
    last_checked_date = models.DateField(null=True, blank=True)
    checked_by = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True, blank=True)
    
    # Non-compliance handling
    non_compliance_reason = models.TextField(blank=True, help_text="Reason for non-compliance")
    action_taken = models.TextField(blank=True, help_text="Actions taken to address non-compliance")
    
    # Documentation
    supporting_documents = models.FileField(upload_to='health/compliance/', blank=True)
    
    notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['required_by_date', 'compliance_type']
    
    def __str__(self):
        return f"{self.health_profile.student.get_full_name()} - {self.requirement_name} ({self.get_status_display()})"
    
    @property
    def is_overdue(self):
        """Check if compliance requirement is overdue"""
        if self.status == 'compliant':
            return False
        return self.required_by_date < timezone.now().date()
    
    @property
    def days_until_due(self):
        """Calculate days until compliance is due"""
        if self.status == 'compliant':
            return None
        delta = self.required_by_date - timezone.now().date()
        return delta.days
    
    @property
    def is_expiring_soon(self):
        """Check if compliance is expiring within 30 days"""
        if not self.expiry_date or self.status != 'compliant':
            return False
        
        days_until_expiry = (self.expiry_date - timezone.now().date()).days
        return 0 <= days_until_expiry <= 30


class HealthReport(BaseModel):
    """Health reports for various purposes"""
    
    REPORT_TYPE_CHOICES = [
        ('individual', 'Individual Student Report'),
        ('class', 'Class Health Report'),
        ('grade', 'Grade Level Report'),
        ('school', 'School-wide Report'),
        ('incident', 'Incident Summary Report'),
        ('compliance', 'Compliance Report'),
        ('screening', 'Screening Results Report'),
        ('trend', 'Health Trends Report'),
        ('custom', 'Custom Report'),
    ]
    
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('pending_review', 'Pending Review'),
        ('approved', 'Approved'),
        ('published', 'Published'),
        ('archived', 'Archived'),
    ]
    
    # Basic information
    report_type = models.CharField(max_length=15, choices=REPORT_TYPE_CHOICES)
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    
    # Report scope
    health_profiles = models.ManyToManyField(HealthProfile, blank=True, help_text="Students included in report")
    report_date_from = models.DateField()
    report_date_to = models.DateField()
    
    # Status and approval
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='draft')
    generated_by = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True, blank=True)
    reviewed_by = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True, blank=True, related_name='reviewed_health_reports')
    approved_by = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_health_reports')
    
    # Report content
    executive_summary = models.TextField(blank=True)
    key_findings = models.TextField(blank=True)
    recommendations = models.TextField(blank=True)
    
    # Data and metrics
    total_students = models.PositiveIntegerField(default=0)
    total_incidents = models.PositiveIntegerField(default=0)
    compliance_rate = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    
    # Files
    report_file = models.FileField(upload_to='health/reports/', blank=True)
    
    notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.title} ({self.report_date_from} to {self.report_date_to})"
    
    def generate_report_data(self):
        """Generate report data based on scope and date range"""
        # This method would contain logic to compile report data
        # Implementation would depend on specific report requirements
        pass
class VaccinationSchedule(BaseModel):
    """Vaccination schedules and tracking"""
    
    SCHEDULE_TYPE_CHOICES = [
        ('routine', 'Routine Vaccination'),
        ('catch_up', 'Catch-up Vaccination'),
        ('travel', 'Travel Vaccination'),
        ('outbreak', 'Outbreak Response'),
        ('booster', 'Booster Shot'),
    ]
    
    STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('due', 'Due'),
        ('overdue', 'Overdue'),
        ('completed', 'Completed'),
        ('exempted', 'Exempted'),
        ('deferred', 'Deferred'),
    ]
    
    # Basic information
    health_profile = models.ForeignKey(HealthProfile, on_delete=models.CASCADE, related_name='vaccination_schedules')
    vaccine_name = models.CharField(max_length=50, choices=Vaccination.VACCINE_CHOICES)
    schedule_type = models.CharField(max_length=15, choices=SCHEDULE_TYPE_CHOICES, default='routine')
    
    # Scheduling
    due_date = models.DateField()
    scheduled_date = models.DateField(null=True, blank=True)
    dose_number = models.PositiveIntegerField(default=1)
    
    # Status tracking
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='scheduled')
    completed_date = models.DateField(null=True, blank=True)
    administered_by = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True, blank=True)
    
    # Exemption/deferral
    exemption_reason = models.TextField(blank=True, help_text="Reason for exemption")
    deferral_reason = models.TextField(blank=True, help_text="Reason for deferral")
    deferral_until = models.DateField(null=True, blank=True)
    
    # Reminders
    reminder_sent = models.BooleanField(default=False)
    reminder_date = models.DateField(null=True, blank=True)
    
    notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['due_date', 'vaccine_name']
        unique_together = ['health_profile', 'vaccine_name', 'dose_number']
    
    def __str__(self):
        return f"{self.health_profile.student.get_full_name()} - {self.get_vaccine_name_display()} (Dose {self.dose_number})"
    
    @property
    def is_overdue(self):
        """Check if vaccination is overdue"""
        if self.status in ['completed', 'exempted']:
            return False
        return self.due_date < timezone.now().date()
    
    @property
    def days_overdue(self):
        """Calculate days overdue"""
        if not self.is_overdue:
            return 0
        return (timezone.now().date() - self.due_date).days


class HealthStatistics(BaseModel):
    """Health statistics and metrics"""
    
    STATISTIC_TYPE_CHOICES = [
        ('vaccination_rate', 'Vaccination Rate'),
        ('incident_rate', 'Incident Rate'),
        ('screening_completion', 'Screening Completion'),
        ('compliance_rate', 'Compliance Rate'),
        ('health_alert_count', 'Health Alert Count'),
        ('bmi_distribution', 'BMI Distribution'),
        ('chronic_condition_prevalence', 'Chronic Condition Prevalence'),
        ('medication_usage', 'Medication Usage'),
    ]
    
    PERIOD_TYPE_CHOICES = [
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('quarterly', 'Quarterly'),
        ('yearly', 'Yearly'),
    ]
    
    # Basic information
    statistic_type = models.CharField(max_length=30, choices=STATISTIC_TYPE_CHOICES)
    period_type = models.CharField(max_length=15, choices=PERIOD_TYPE_CHOICES)
    period_start = models.DateField()
    period_end = models.DateField()
    
    # Scope
    school = models.ForeignKey('core.School', on_delete=models.CASCADE)
    grade = models.ForeignKey('students.Grade', on_delete=models.CASCADE, null=True, blank=True)
    class_group = models.ForeignKey('students.Class', on_delete=models.CASCADE, null=True, blank=True)
    
    # Statistics data
    total_count = models.PositiveIntegerField(default=0)
    positive_count = models.PositiveIntegerField(default=0)
    percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Additional metrics
    metadata = models.JSONField(default=dict, blank=True, help_text="Additional statistical data")
    
    # Calculation info
    calculated_at = models.DateTimeField(auto_now_add=True)
    calculated_by = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True, blank=True)
    
    class Meta:
        ordering = ['-period_end', 'statistic_type']
        unique_together = ['statistic_type', 'period_type', 'period_start', 'period_end', 'school', 'grade', 'class_group']
    
    def __str__(self):
        scope = "School-wide"
        if self.class_group:
            scope = f"Class {self.class_group.name}"
        elif self.grade:
            scope = f"Grade {self.grade.name}"
        
        return f"{self.get_statistic_type_display()} - {scope} ({self.period_start} to {self.period_end})"


class HealthDashboardWidget(BaseModel):
    """Configurable dashboard widgets for health analytics"""
    
    WIDGET_TYPE_CHOICES = [
        ('kpi_card', 'KPI Card'),
        ('chart', 'Chart'),
        ('table', 'Data Table'),
        ('alert_list', 'Alert List'),
        ('trend_indicator', 'Trend Indicator'),
        ('progress_bar', 'Progress Bar'),
    ]
    
    CHART_TYPE_CHOICES = [
        ('line', 'Line Chart'),
        ('bar', 'Bar Chart'),
        ('pie', 'Pie Chart'),
        ('doughnut', 'Doughnut Chart'),
        ('area', 'Area Chart'),
    ]
    
    # Basic information
    title = models.CharField(max_length=200)
    widget_type = models.CharField(max_length=20, choices=WIDGET_TYPE_CHOICES)
    description = models.TextField(blank=True)
    
    # Configuration
    data_source = models.CharField(max_length=100, help_text="Data source identifier")
    chart_type = models.CharField(max_length=15, choices=CHART_TYPE_CHOICES, blank=True)
    configuration = models.JSONField(default=dict, help_text="Widget-specific configuration")
    
    # Display settings
    position_row = models.PositiveIntegerField(default=1)
    position_col = models.PositiveIntegerField(default=1)
    width = models.PositiveIntegerField(default=6, help_text="Bootstrap column width (1-12)")
    height = models.PositiveIntegerField(default=300, help_text="Height in pixels")
    
    # Permissions
    school = models.ForeignKey('core.School', on_delete=models.CASCADE)
    visible_to_roles = models.JSONField(default=list, help_text="List of roles that can see this widget")
    
    # Status
    is_active = models.BooleanField(default=True)
    refresh_interval = models.PositiveIntegerField(default=300, help_text="Refresh interval in seconds")
    
    class Meta:
        ordering = ['position_row', 'position_col']
    
    def __str__(self):
        return f"{self.title} ({self.get_widget_type_display()})"


class HealthAlertRule(BaseModel):
    """Rules for generating automated health alerts"""
    
    CONDITION_TYPE_CHOICES = [
        ('threshold', 'Threshold'),
        ('trend', 'Trend'),
        ('overdue', 'Overdue'),
        ('compliance', 'Compliance'),
        ('pattern', 'Pattern'),
    ]
    
    SEVERITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]
    
    # Basic information
    name = models.CharField(max_length=200)
    description = models.TextField()
    condition_type = models.CharField(max_length=15, choices=CONDITION_TYPE_CHOICES)
    
    # Rule configuration
    data_source = models.CharField(max_length=100, help_text="Data source to monitor")
    condition_config = models.JSONField(default=dict, help_text="Condition-specific configuration")
    
    # Alert settings
    severity = models.CharField(max_length=10, choices=SEVERITY_CHOICES, default='medium')
    alert_message_template = models.TextField(help_text="Template for alert message")
    
    # Scope
    school = models.ForeignKey('core.School', on_delete=models.CASCADE)
    applies_to_grades = models.ManyToManyField('students.Grade', blank=True)
    applies_to_classes = models.ManyToManyField('students.Class', blank=True)
    
    # Notification settings
    notify_roles = models.JSONField(default=list, help_text="Roles to notify when alert triggers")
    notification_methods = models.JSONField(default=list, help_text="Notification methods (email, sms, etc.)")
    
    # Status and timing
    is_active = models.BooleanField(default=True)
    check_frequency = models.PositiveIntegerField(default=3600, help_text="Check frequency in seconds")
    last_checked = models.DateTimeField(null=True, blank=True)
    last_triggered = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.get_severity_display()})"


class HealthAlertInstance(BaseModel):
    """Individual instances of triggered health alerts"""
    
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('acknowledged', 'Acknowledged'),
        ('resolved', 'Resolved'),
        ('dismissed', 'Dismissed'),
    ]
    
    # Basic information
    alert_rule = models.ForeignKey(HealthAlertRule, on_delete=models.CASCADE, related_name='instances')
    title = models.CharField(max_length=200)
    message = models.TextField()
    
    # Context
    triggered_by_data = models.JSONField(default=dict, help_text="Data that triggered the alert")
    affected_students = models.ManyToManyField('students.Student', blank=True)
    
    # Status tracking
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='active')
    triggered_at = models.DateTimeField(auto_now_add=True)
    acknowledged_at = models.DateTimeField(null=True, blank=True)
    acknowledged_by = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True, blank=True)
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolved_by = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True, blank=True, related_name='resolved_alerts')
    
    # Actions taken
    actions_taken = models.TextField(blank=True, help_text="Actions taken to address the alert")
    resolution_notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-triggered_at']
    
    def __str__(self):
        return f"{self.title} - {self.get_status_display()}"
    
    @property
    def is_overdue(self):
        """Check if alert has been active too long"""
        if self.status != 'active':
            return False
        
        # Consider alert overdue if active for more than 24 hours for critical,
        # 48 hours for high, 72 hours for medium, 1 week for low
        hours_active = (timezone.now() - self.triggered_at).total_seconds() / 3600
        
        thresholds = {
            'critical': 24,
            'high': 48,
            'medium': 72,
            'low': 168  # 1 week
        }
        
        return hours_active > thresholds.get(self.alert_rule.severity, 72)