from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import date, time

from core.models import School
from students.models import Student, Parent
from hr.models import Employee, Department, Position
from .models import (
    HealthProfile, MedicalIncident, IncidentTreatment, 
    IncidentFollowUp, IncidentNotification
)

User = get_user_model()


class IncidentManagementTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create school
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2020, 1, 1)
        )
        
        # Create user
        self.user = User.objects.create_user(
            username="testuser",
            password="testpass123",
            email="<EMAIL>"
        )
        
        # Create department
        self.department = Department.objects.create(
            school=self.school,
            name="Health Department",
            code="HEALTH",
            created_by=self.user
        )
        
        # Create position
        self.position = Position.objects.create(
            school=self.school,
            title="School Nurse",
            department=self.department,
            min_salary=40000,
            max_salary=60000,
            created_by=self.user
        )
        
        # Create employee
        self.employee = Employee.objects.create(
            school=self.school,
            user=self.user,
            employee_id="EMP001",
            position=self.position,
            hire_date=date.today(),
            salary=50000,
            emergency_contact_name="Emergency Contact",
            emergency_contact_phone="************",
            emergency_contact_relationship="Spouse",
            created_by=self.user
        )
        
        # Create student
        self.student = Student.objects.create(
            school=self.school,
            student_id="STU001",
            first_name="Test",
            last_name="Student",
            date_of_birth=date(2010, 1, 1),
            gender="M",
            nationality="US",
            admission_date=date.today(),
            created_by=self.user
        )
        
        # Create health profile
        self.health_profile = HealthProfile.objects.create(
            school=self.school,
            student=self.student,
            blood_type="A+",
            emergency_contact_name="Test Parent",
            emergency_contact_phone="************",
            created_by=self.user
        )
        
        # Set up client
        self.client = Client()
        self.client.login(username="testuser", password="testpass123")
        
        # Mock school_required decorator
        from unittest.mock import patch
        self.school_patcher = patch('health.views.school_required')
        self.mock_school_required = self.school_patcher.start()
        self.mock_school_required.side_effect = lambda func: func
        
        # Mock request.school
        self.request_patcher = patch('health.views.request')
        self.mock_request = self.request_patcher.start()
        self.mock_request.school = self.school
        self.mock_request.user = self.user
    
    def tearDown(self):
        """Clean up patches"""
        self.school_patcher.stop()
        self.request_patcher.stop()
    
    def test_medical_incident_creation(self):
        """Test creating a medical incident"""
        incident = MedicalIncident.objects.create(
            school=self.school,
            health_profile=self.health_profile,
            incident_type='injury',
            severity='moderate',
            incident_date=date.today(),
            incident_time=time(10, 30),
            location='Playground',
            description='Student fell from swing',
            reported_by=self.employee,
            created_by=self.user
        )
        
        self.assertEqual(incident.health_profile, self.health_profile)
        self.assertEqual(incident.incident_type, 'injury')
        self.assertEqual(incident.severity, 'moderate')
        self.assertEqual(incident.status, 'reported')  # Default status
        self.assertTrue(incident.incident_id.startswith('INC-'))
        self.assertIn(str(date.today().year), incident.incident_id)
    
    def test_incident_id_generation(self):
        """Test automatic incident ID generation"""
        incident1 = MedicalIncident.objects.create(
            school=self.school,
            health_profile=self.health_profile,
            incident_type='illness',
            severity='minor',
            incident_date=date.today(),
            incident_time=time(9, 0),
            location='Classroom',
            description='Student feeling unwell',
            created_by=self.user
        )
        
        incident2 = MedicalIncident.objects.create(
            school=self.school,
            health_profile=self.health_profile,
            incident_type='injury',
            severity='minor',
            incident_date=date.today(),
            incident_time=time(11, 0),
            location='Cafeteria',
            description='Student slipped',
            created_by=self.user
        )
        
        # Both should have unique IDs
        self.assertNotEqual(incident1.incident_id, incident2.incident_id)
        
        # Second incident should have a higher number
        id1_num = int(incident1.incident_id.split('-')[-1])
        id2_num = int(incident2.incident_id.split('-')[-1])
        self.assertEqual(id2_num, id1_num + 1)
    
    def test_incident_treatment_creation(self):
        """Test creating incident treatment"""
        incident = MedicalIncident.objects.create(
            school=self.school,
            health_profile=self.health_profile,
            incident_type='injury',
            severity='moderate',
            incident_date=date.today(),
            incident_time=time(10, 30),
            location='Playground',
            description='Student fell from swing',
            created_by=self.user
        )
        
        treatment = IncidentTreatment.objects.create(
            school=self.school,
            incident=incident,
            treatment_type='first_aid',
            treatment_description='Applied ice pack to bruised knee',
            administered_by=self.employee,
            student_response='Student felt better after treatment',
            effectiveness='effective',
            created_by=self.user
        )
        
        self.assertEqual(treatment.incident, incident)
        self.assertEqual(treatment.treatment_type, 'first_aid')
        self.assertEqual(treatment.effectiveness, 'effective')
        self.assertEqual(treatment.administered_by, self.employee)
    
    def test_incident_follow_up_creation(self):
        """Test creating incident follow-up"""
        incident = MedicalIncident.objects.create(
            school=self.school,
            health_profile=self.health_profile,
            incident_type='injury',
            severity='serious',
            incident_date=date.today(),
            incident_time=time(10, 30),
            location='Playground',
            description='Student fell from swing',
            created_by=self.user
        )
        
        follow_up = IncidentFollowUp.objects.create(
            school=self.school,
            incident=incident,
            follow_up_type='check_in',
            scheduled_date=date.today(),
            assigned_to=self.employee,
            description='Check on student recovery',
            priority='medium',
            created_by=self.user
        )
        
        self.assertEqual(follow_up.incident, incident)
        self.assertEqual(follow_up.follow_up_type, 'check_in')
        self.assertEqual(follow_up.status, 'pending')  # Default status
        self.assertEqual(follow_up.priority, 'medium')
        self.assertEqual(follow_up.assigned_to, self.employee)
    
    def test_incident_notification_creation(self):
        """Test creating incident notification"""
        incident = MedicalIncident.objects.create(
            school=self.school,
            health_profile=self.health_profile,
            incident_type='injury',
            severity='serious',
            incident_date=date.today(),
            incident_time=time(10, 30),
            location='Playground',
            description='Student fell from swing',
            created_by=self.user
        )
        
        notification = IncidentNotification.objects.create(
            school=self.school,
            incident=incident,
            notification_type='parent_immediate',
            method='phone',
            recipient_name='Test Parent',
            recipient_relationship='Mother',
            recipient_contact='************',
            subject='Incident Report - Test Student',
            message='Your child was involved in a minor incident at school.',
            sent_by=self.employee,
            created_by=self.user
        )
        
        self.assertEqual(notification.incident, incident)
        self.assertEqual(notification.notification_type, 'parent_immediate')
        self.assertEqual(notification.method, 'phone')
        self.assertEqual(notification.status, 'pending')  # Default status
        self.assertEqual(notification.sent_by, self.employee)
    
    def test_incident_properties(self):
        """Test incident model properties"""
        # Create recent incident
        recent_incident = MedicalIncident.objects.create(
            school=self.school,
            health_profile=self.health_profile,
            incident_type='injury',
            severity='critical',
            incident_date=date.today(),
            incident_time=timezone.now().time(),
            location='Playground',
            description='Student fell from swing',
            status='in_treatment',
            created_by=self.user
        )
        
        # Test is_recent property
        self.assertTrue(recent_incident.is_recent)
        
        # Test requires_attention property
        self.assertTrue(recent_incident.requires_attention)
        
        # Create resolved incident
        resolved_incident = MedicalIncident.objects.create(
            school=self.school,
            health_profile=self.health_profile,
            incident_type='injury',
            severity='minor',
            incident_date=date.today(),
            incident_time=time(10, 0),
            location='Classroom',
            description='Minor cut',
            status='resolved',
            created_by=self.user
        )
        
        # Resolved incident should not require attention
        self.assertFalse(resolved_incident.requires_attention)
    
    def test_follow_up_overdue_property(self):
        """Test follow-up overdue property"""
        incident = MedicalIncident.objects.create(
            school=self.school,
            health_profile=self.health_profile,
            incident_type='injury',
            severity='moderate',
            incident_date=date.today(),
            incident_time=time(10, 30),
            location='Playground',
            description='Student fell from swing',
            created_by=self.user
        )
        
        # Create overdue follow-up
        from datetime import timedelta
        yesterday = date.today() - timedelta(days=1)
        
        overdue_follow_up = IncidentFollowUp.objects.create(
            school=self.school,
            incident=incident,
            follow_up_type='check_in',
            scheduled_date=yesterday,
            description='Check on student recovery',
            created_by=self.user
        )
        
        self.assertTrue(overdue_follow_up.is_overdue)
        
        # Create future follow-up
        tomorrow = date.today() + timedelta(days=1)
        
        future_follow_up = IncidentFollowUp.objects.create(
            school=self.school,
            incident=incident,
            follow_up_type='parent_contact',
            scheduled_date=tomorrow,
            description='Contact parent for update',
            created_by=self.user
        )
        
        self.assertFalse(future_follow_up.is_overdue)
        
        # Completed follow-up should not be overdue
        overdue_follow_up.status = 'completed'
        overdue_follow_up.save()
        self.assertFalse(overdue_follow_up.is_overdue)
    
    def test_notification_status_methods(self):
        """Test notification status update methods"""
        incident = MedicalIncident.objects.create(
            school=self.school,
            health_profile=self.health_profile,
            incident_type='injury',
            severity='moderate',
            incident_date=date.today(),
            incident_time=time(10, 30),
            location='Playground',
            description='Student fell from swing',
            created_by=self.user
        )
        
        notification = IncidentNotification.objects.create(
            school=self.school,
            incident=incident,
            notification_type='parent_immediate',
            method='phone',
            recipient_name='Test Parent',
            recipient_relationship='Mother',
            recipient_contact='************',
            message='Test message',
            created_by=self.user
        )
        
        # Test mark_as_sent
        notification.mark_as_sent()
        self.assertEqual(notification.status, 'sent')
        self.assertIsNotNone(notification.sent_time)
        
        # Test mark_as_delivered
        notification.mark_as_delivered()
        self.assertEqual(notification.status, 'delivered')
        self.assertIsNotNone(notification.delivered_time)
        
        # Test mark_as_read
        notification.mark_as_read()
        self.assertEqual(notification.status, 'read')
        self.assertIsNotNone(notification.read_time)


class IncidentViewsTestCase(TestCase):
    def setUp(self):
        """Set up test data for views"""
        # Create school
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2020, 1, 1)
        )
        
        # Create user
        self.user = User.objects.create_user(
            username="testuser",
            password="testpass123",
            email="<EMAIL>"
        )
        
        # Create department
        self.department = Department.objects.create(
            school=self.school,
            name="Health Department",
            code="HEALTH",
            created_by=self.user
        )
        
        # Create position
        self.position = Position.objects.create(
            school=self.school,
            title="School Nurse",
            department=self.department,
            min_salary=40000,
            max_salary=60000,
            created_by=self.user
        )
        
        # Create employee
        self.employee = Employee.objects.create(
            school=self.school,
            user=self.user,
            employee_id="EMP001",
            position=self.position,
            hire_date=date.today(),
            salary=50000,
            emergency_contact_name="Emergency Contact",
            emergency_contact_phone="************",
            emergency_contact_relationship="Spouse",
            created_by=self.user
        )
        
        # Create student
        self.student = Student.objects.create(
            school=self.school,
            student_id="STU001",
            first_name="Test",
            last_name="Student",
            date_of_birth=date(2010, 1, 1),
            gender="M",
            nationality="US",
            admission_date=date.today(),
            created_by=self.user
        )
        
        # Create health profile
        self.health_profile = HealthProfile.objects.create(
            school=self.school,
            student=self.student,
            blood_type="A+",
            created_by=self.user
        )
        
        # Set up client
        self.client = Client()
        self.client.login(username="testuser", password="testpass123")
    
    def test_incident_dashboard_view(self):
        """Test incident dashboard view"""
        # Mock the school_required decorator
        with self.settings(DEBUG=True):
            # Create some test incidents
            MedicalIncident.objects.create(
                school=self.school,
                health_profile=self.health_profile,
                incident_type='injury',
                severity='moderate',
                incident_date=date.today(),
                incident_time=time(10, 30),
                location='Playground',
                description='Test incident',
                created_by=self.user
            )
            
            # The view requires proper middleware setup, so we'll test the URL exists
            url = reverse('health:incident_dashboard')
            self.assertTrue(url.startswith('/health/'))
    
    def test_incident_list_view_url(self):
        """Test incident list view URL"""
        url = reverse('health:incident_list')
        self.assertTrue(url.startswith('/health/'))
    
    def test_incident_create_view_url(self):
        """Test incident create view URL"""
        url = reverse('health:incident_create')
        self.assertTrue(url.startswith('/health/'))
    
    def test_incident_detail_view_url(self):
        """Test incident detail view URL"""
        incident = MedicalIncident.objects.create(
            school=self.school,
            health_profile=self.health_profile,
            incident_type='injury',
            severity='moderate',
            incident_date=date.today(),
            incident_time=time(10, 30),
            location='Playground',
            description='Test incident',
            created_by=self.user
        )
        
        url = reverse('health:incident_detail', kwargs={'incident_id': incident.id})
        self.assertTrue(url.startswith('/health/'))


if __name__ == '__main__':
    import django
    django.setup()
    
    from django.test.utils import get_runner
    from django.conf import settings
    
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    failures = test_runner.run_tests(["health.test_incidents"])