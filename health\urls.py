from django.urls import path
from . import views

app_name = 'health'

urlpatterns = [
    # Dashboard
    path('', views.health_dashboard, name='dashboard'),
    
    # Health Profiles
    path('profiles/', views.health_profile_list, name='profile_list'),
    path('profiles/create/', views.health_profile_create, name='profile_create'),
    path('profiles/<int:student_id>/', views.health_profile_detail, name='profile_detail'),
    path('profiles/<int:student_id>/edit/', views.health_profile_edit, name='profile_edit'),
    
    # Allergies
    path('allergies/<int:student_id>/add/', views.allergy_create, name='allergy_create'),
    path('allergies/<int:allergy_id>/edit/', views.allergy_edit, name='allergy_edit'),
    
    # Medications
    path('medications/<int:student_id>/add/', views.medication_create, name='medication_create'),
    path('medications/<int:medication_id>/edit/', views.medication_edit, name='medication_edit'),
    
    # Medical History
    path('history/<int:student_id>/add/', views.medical_history_create, name='medical_history_create'),
    
    # Vaccinations
    path('vaccinations/<int:student_id>/add/', views.vaccination_create, name='vaccination_create'),
    
    # Health Screenings
    path('screenings/<int:student_id>/add/', views.health_screening_create, name='screening_create'),
    
    # Health Alerts
    path('alerts/<int:student_id>/add/', views.health_alert_create, name='alert_create'),
    
    # Analytics and Reports
    path('analytics/', views.health_analytics, name='analytics'),
    path('reports/', views.health_reports, name='reports'),
    
    # Incident Management
    path('incidents/', views.incident_list, name='incident_list'),
    path('incidents/dashboard/', views.incident_dashboard, name='incident_dashboard'),
    path('incidents/<int:incident_id>/', views.incident_detail, name='incident_detail'),
    path('incidents/create/', views.incident_create, name='incident_create'),
    path('incidents/create/<int:student_id>/', views.incident_create, name='incident_create_for_student'),
    path('incidents/<int:incident_id>/edit/', views.incident_edit, name='incident_edit'),
    path('incidents/<int:incident_id>/treatment/add/', views.incident_treatment_add, name='incident_treatment_add'),
    path('incidents/<int:incident_id>/follow-up/add/', views.incident_follow_up_add, name='incident_follow_up_add'),
    path('incidents/<int:incident_id>/notification/add/', views.incident_notification_add, name='incident_notification_add'),
    path('incidents/<int:incident_id>/status-update/', views.incident_status_update, name='incident_status_update'),
    path('incidents/analytics/', views.incident_analytics, name='incident_analytics'),
    
    # Health Monitoring
    path('monitoring/', views.health_monitoring_dashboard, name='health_monitoring_dashboard'),
    
    # Health Screening Schedules
    path('screening-schedules/', views.screening_schedule_list, name='screening_schedule_list'),
    path('screening-schedules/create/', views.screening_schedule_create, name='screening_schedule_create'),
    
    # Medical Appointments
    path('appointments/', views.appointment_list, name='appointment_list'),
    path('appointments/create/', views.appointment_create, name='appointment_create'),
    
    # Compliance Monitoring
    path('compliance/', views.compliance_monitoring_list, name='compliance_monitoring_list'),
    
    # Health Trends
    path('trends/', views.health_trends_dashboard, name='health_trends_dashboard'),
    
    # Health Reports
    path('health-reports/', views.health_reports_list, name='health_reports_list'),
    
    # Health Analytics (Task 11.4)
    path('vaccination-tracking/', views.vaccination_tracking, name='vaccination_tracking'),
    path('alerts/', views.health_alerts_dashboard, name='health_alerts_dashboard'),
    path('trends-analysis/', views.health_trends_analysis, name='health_trends_analysis'),
    
    # API Endpoints
    path('api/student-search/', views.api_student_search, name='api_student_search'),
    path('api/health-summary/<int:student_id>/', views.api_health_summary, name='api_health_summary'),
]