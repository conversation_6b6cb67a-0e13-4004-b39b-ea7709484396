from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.db.models import Q, Count, Avg
from django.db import models
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from decimal import Decimal
import json
import csv

from core.permissions import school_required
from students.models import Student
from .models import (
    HealthProfile, Allergy, Medication, MedicalHistory, 
    Vaccination, HealthScreening, HealthAlert, MedicalIncident,
    IncidentTreatment, IncidentFollowUp, IncidentNotification,
    VaccinationSchedule, HealthAlertInstance, HealthAlertRule,
    ComplianceMonitoring, HealthTrendAnalysis, HealthReport,
    MedicalAppointment, HealthScreeningSchedule
)
from .forms import (
    HealthProfileForm, HealthScreeningScheduleForm, MedicalAppointmentForm,
    ComplianceMonitoringForm, MedicalIncidentForm
)


@login_required
@school_required
def health_dashboard(request):
    """Health management dashboard"""
    school = request.school
    
    # Get health statistics
    total_students = Student.objects.filter(school=school, is_active=True).count()
    students_with_profiles = HealthProfile.objects.filter(student__school=school).count()
    
    # Get alerts
    active_alerts = HealthAlert.objects.filter(
        health_profile__student__school=school,
        is_active=True
    ).select_related('health_profile__student').order_by('-priority', '-created_at')[:10]
    
    # Get students with critical allergies
    critical_allergies = Allergy.objects.filter(
        health_profile__student__school=school,
        severity='life_threatening',
        is_active=True
    ).select_related('health_profile__student').count()
    
    # Get medication alerts (students on critical medications)
    critical_medications = Medication.objects.filter(
        health_profile__student__school=school,
        is_active=True,
        requires_supervision=True
    ).select_related('health_profile__student').count()
    
    # Get overdue vaccinations
    from django.db.models import F
    overdue_vaccinations = Vaccination.objects.filter(
        health_profile__student__school=school,
        next_dose_due__lt=timezone.now().date(),
        dose_number__lt=F('total_doses_required')
    ).count()
    
    # Get recent health screenings
    recent_screenings = HealthScreening.objects.filter(
        health_profile__student__school=school,
        date__gte=timezone.now().date() - timezone.timedelta(days=30)
    ).select_related('health_profile__student').order_by('-date')[:10]
    
    # Get students needing follow-up
    follow_up_needed = HealthScreening.objects.filter(
        health_profile__student__school=school,
        referral_needed=True,
        follow_up_completed=False
    ).count()
    
    # Get incident statistics
    today = timezone.now().date()
    week_ago = today - timezone.timedelta(days=7)
    
    recent_incidents = MedicalIncident.objects.filter(
        health_profile__student__school=school,
        incident_date__gte=week_ago
    ).count()
    
    active_incidents = MedicalIncident.objects.filter(
        health_profile__student__school=school,
        status__in=['reported', 'in_treatment']
    ).count()
    
    context = {
        'total_students': total_students,
        'students_with_profiles': students_with_profiles,
        'profile_completion_rate': round((students_with_profiles / total_students * 100) if total_students > 0 else 0, 1),
        'active_alerts': active_alerts,
        'critical_allergies': critical_allergies,
        'critical_medications': critical_medications,
        'overdue_vaccinations': overdue_vaccinations,
        'recent_screenings': recent_screenings,
        'follow_up_needed': follow_up_needed,
        'recent_incidents': recent_incidents,
        'active_incidents': active_incidents,
    }
    
    return render(request, 'health/dashboard.html', context)


@login_required
@school_required
def health_profile_list(request):
    """List all health profiles"""
    school = request.school
    
    # Get all students with their health profiles
    students = Student.objects.filter(school=school, is_active=True).select_related(
        'health_profile', 'class_enrolled', 'grade'
    ).prefetch_related(
        'health_profile__allergies',
        'health_profile__medications',
        'health_profile__alerts'
    )
    
    # Search functionality
    query = request.GET.get('q')
    if query:
        students = students.filter(
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query) |
            Q(student_id__icontains=query)
        )
    
    # Filter by grade
    grade = request.GET.get('grade')
    if grade:
        students = students.filter(grade_id=grade)
    
    # Filter by class
    class_id = request.GET.get('class')
    if class_id:
        students = students.filter(class_enrolled_id=class_id)
    
    # Filter by health status
    health_filter = request.GET.get('health_filter')
    if health_filter == 'no_profile':
        students = students.filter(health_profile__isnull=True)
    elif health_filter == 'has_allergies':
        students = students.filter(health_profile__allergies__is_active=True).distinct()
    elif health_filter == 'on_medication':
        students = students.filter(health_profile__medications__is_active=True).distinct()
    elif health_filter == 'has_alerts':
        students = students.filter(health_profile__alerts__is_active=True).distinct()
    
    # Pagination
    paginator = Paginator(students, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get filter options
    from students.models import Grade, Class
    grades = Grade.objects.filter(school=school).order_by('name')
    classes = Class.objects.filter(school=school).order_by('name')
    
    context = {
        'page_obj': page_obj,
        'grades': grades,
        'classes': classes,
        'query': query,
        'current_grade': grade,
        'current_class': class_id,
        'current_health_filter': health_filter,
    }
    
    return render(request, 'health/profile_list.html', context)


@login_required
@school_required
def health_profile_detail(request, student_id):
    """View detailed health profile for a student"""
    school = request.school
    student = get_object_or_404(Student, id=student_id, school=school)
    
    # Get or create health profile
    health_profile, created = HealthProfile.objects.get_or_create(
        student=student,
        defaults={'school': school, 'created_by': request.user}
    )
    
    # Get related health data
    allergies = health_profile.allergies.filter(is_active=True).order_by('-severity')
    medications = health_profile.medications.filter(is_active=True).order_by('medication_name')
    medical_history = health_profile.medical_history.all().order_by('-date')[:10]
    vaccinations = health_profile.vaccinations.all().order_by('-date_administered')
    screenings = health_profile.screenings.all().order_by('-date')[:10]
    alerts = health_profile.alerts.filter(is_active=True).order_by('-priority')
    
    # Get vaccination status summary
    vaccination_summary = {}
    for vaccination in vaccinations:
        vaccine_name = vaccination.get_vaccine_name_display()
        if vaccine_name not in vaccination_summary:
            vaccination_summary[vaccine_name] = {
                'doses_received': 0,
                'total_required': vaccination.total_doses_required,
                'last_dose_date': None,
                'next_due': vaccination.next_dose_due,
                'complete': False
            }
        
        vaccination_summary[vaccine_name]['doses_received'] = max(
            vaccination_summary[vaccine_name]['doses_received'],
            vaccination.dose_number
        )
        
        if not vaccination_summary[vaccine_name]['last_dose_date'] or vaccination.date_administered > vaccination_summary[vaccine_name]['last_dose_date']:
            vaccination_summary[vaccine_name]['last_dose_date'] = vaccination.date_administered
            vaccination_summary[vaccine_name]['next_due'] = vaccination.next_dose_due
        
        vaccination_summary[vaccine_name]['complete'] = vaccination.is_series_complete
    
    context = {
        'student': student,
        'health_profile': health_profile,
        'allergies': allergies,
        'medications': medications,
        'medical_history': medical_history,
        'vaccinations': vaccinations,
        'vaccination_summary': vaccination_summary,
        'screenings': screenings,
        'alerts': alerts,
        'created': created,
    }
    
    return render(request, 'health/profile_detail.html', context)


@login_required
@school_required
def health_profile_create(request):
    """Create health profile for a student"""
    school = request.school
    
    if request.method == 'POST':
        form = HealthProfileForm(request.POST, school=school)
        if form.is_valid():
            profile = form.save(commit=False)
            profile.school = school
            profile.save()
            messages.success(request, 'Health profile created successfully.')
            return redirect('health:profile_detail', student_id=profile.student.id)
    else:
        form = HealthProfileForm(school=school)
    
    context = {
        'form': form,
        'title': 'Create Health Profile',
    }
    return render(request, 'health/profile_form.html', context)


@login_required
@school_required
def health_profile_edit(request, student_id):
    """Edit health profile for a student"""
    school = request.school
    student = get_object_or_404(Student, id=student_id, school=school)
    
    # Get or create health profile
    health_profile, created = HealthProfile.objects.get_or_create(
        student=student,
        defaults={'school': school, 'created_by': request.user}
    )
    
    if request.method == 'POST':
        form = HealthProfileForm(request.POST, instance=health_profile, school=school)
        if form.is_valid():
            health_profile = form.save(commit=False)
            if created:
                health_profile.created_by = request.user
            health_profile.updated_by = request.user
            health_profile.save()
            
            messages.success(request, f'Health profile for {student.get_full_name()} updated successfully.')
            return redirect('health:profile_detail', student_id=student.id)
    else:
        form = HealthProfileForm(instance=health_profile, school=school)
    
    context = {
        'form': form,
        'student': student,
        'health_profile': health_profile,
        'created': created,
    }
    
    return render(request, 'health/profile_form.html', context)


@login_required
@school_required
def allergy_create(request, student_id):
    """Add allergy to student's health profile"""
    school = request.school
    student = get_object_or_404(Student, id=student_id, school=school)
    health_profile = get_object_or_404(HealthProfile, student=student)
    
    if request.method == 'POST':
        form = AllergyForm(request.POST, school=school)
        if form.is_valid():
            allergy = form.save(commit=False)
            allergy.health_profile = health_profile
            allergy.school = school
            allergy.created_by = request.user
            allergy.save()
            
            messages.success(request, f'Allergy added to {student.get_full_name()}\'s profile.')
            return redirect('health:profile_detail', student_id=student.id)
    else:
        form = AllergyForm(school=school)
    
    context = {
        'form': form,
        'student': student,
        'health_profile': health_profile,
        'title': 'Add Allergy',
    }
    
    return render(request, 'health/allergy_form.html', context)


@login_required
@school_required
def allergy_edit(request, allergy_id):
    """Edit allergy record"""
    school = request.school
    allergy = get_object_or_404(Allergy, id=allergy_id, school=school)
    
    if request.method == 'POST':
        form = AllergyForm(request.POST, instance=allergy, school=school)
        if form.is_valid():
            allergy = form.save(commit=False)
            allergy.updated_by = request.user
            allergy.save()
            
            messages.success(request, 'Allergy record updated successfully.')
            return redirect('health:profile_detail', student_id=allergy.health_profile.student.id)
    else:
        form = AllergyForm(instance=allergy, school=school)
    
    context = {
        'form': form,
        'allergy': allergy,
        'student': allergy.health_profile.student,
        'title': 'Edit Allergy',
    }
    
    return render(request, 'health/allergy_form.html', context)


@login_required
@school_required
def medication_create(request, student_id):
    """Add medication to student's health profile"""
    school = request.school
    student = get_object_or_404(Student, id=student_id, school=school)
    health_profile = get_object_or_404(HealthProfile, student=student)
    
    if request.method == 'POST':
        form = MedicationForm(request.POST, school=school)
        if form.is_valid():
            medication = form.save(commit=False)
            medication.health_profile = health_profile
            medication.school = school
            medication.created_by = request.user
            medication.save()
            
            messages.success(request, f'Medication added to {student.get_full_name()}\'s profile.')
            return redirect('health:profile_detail', student_id=student.id)
    else:
        form = MedicationForm(school=school)
    
    context = {
        'form': form,
        'student': student,
        'health_profile': health_profile,
        'title': 'Add Medication',
    }
    
    return render(request, 'health/medication_form.html', context)


@login_required
@school_required
def medication_edit(request, medication_id):
    """Edit medication record"""
    school = request.school
    medication = get_object_or_404(Medication, id=medication_id, school=school)
    
    if request.method == 'POST':
        form = MedicationForm(request.POST, instance=medication, school=school)
        if form.is_valid():
            medication = form.save(commit=False)
            medication.updated_by = request.user
            medication.save()
            
            messages.success(request, 'Medication record updated successfully.')
            return redirect('health:profile_detail', student_id=medication.health_profile.student.id)
    else:
        form = MedicationForm(instance=medication, school=school)
    
    context = {
        'form': form,
        'medication': medication,
        'student': medication.health_profile.student,
        'title': 'Edit Medication',
    }
    
    return render(request, 'health/medication_form.html', context)


@login_required
@school_required
def medical_history_create(request, student_id):
    """Add medical history record"""
    school = request.school
    student = get_object_or_404(Student, id=student_id, school=school)
    health_profile = get_object_or_404(HealthProfile, student=student)
    
    if request.method == 'POST':
        form = MedicalHistoryForm(request.POST, request.FILES, school=school)
        if form.is_valid():
            history = form.save(commit=False)
            history.health_profile = health_profile
            history.school = school
            history.created_by = request.user
            history.save()
            
            messages.success(request, f'Medical history added to {student.get_full_name()}\'s profile.')
            return redirect('health:profile_detail', student_id=student.id)
    else:
        form = MedicalHistoryForm(school=school)
    
    context = {
        'form': form,
        'student': student,
        'health_profile': health_profile,
        'title': 'Add Medical History',
    }
    
    return render(request, 'health/medical_history_form.html', context)


@login_required
@school_required
def vaccination_create(request, student_id):
    """Add vaccination record"""
    school = request.school
    student = get_object_or_404(Student, id=student_id, school=school)
    health_profile = get_object_or_404(HealthProfile, student=student)
    
    if request.method == 'POST':
        form = VaccinationForm(request.POST, request.FILES, school=school)
        if form.is_valid():
            vaccination = form.save(commit=False)
            vaccination.health_profile = health_profile
            vaccination.school = school
            vaccination.created_by = request.user
            vaccination.save()
            
            messages.success(request, f'Vaccination record added to {student.get_full_name()}\'s profile.')
            return redirect('health:profile_detail', student_id=student.id)
    else:
        form = VaccinationForm(school=school)
    
    context = {
        'form': form,
        'student': student,
        'health_profile': health_profile,
        'title': 'Add Vaccination',
    }
    
    return render(request, 'health/vaccination_form.html', context)


@login_required
@school_required
def health_screening_create(request, student_id):
    """Add health screening record"""
    school = request.school
    student = get_object_or_404(Student, id=student_id, school=school)
    health_profile = get_object_or_404(HealthProfile, student=student)
    
    if request.method == 'POST':
        form = HealthScreeningForm(request.POST, school=school)
        if form.is_valid():
            screening = form.save(commit=False)
            screening.health_profile = health_profile
            screening.school = school
            screening.created_by = request.user
            screening.save()
            
            messages.success(request, f'Health screening added to {student.get_full_name()}\'s profile.')
            return redirect('health:profile_detail', student_id=student.id)
    else:
        form = HealthScreeningForm(school=school)
    
    context = {
        'form': form,
        'student': student,
        'health_profile': health_profile,
        'title': 'Add Health Screening',
    }
    
    return render(request, 'health/screening_form.html', context)


@login_required
@school_required
def health_alert_create(request, student_id):
    """Create health alert for student"""
    school = request.school
    student = get_object_or_404(Student, id=student_id, school=school)
    health_profile = get_object_or_404(HealthProfile, student=student)
    
    if request.method == 'POST':
        form = HealthAlertForm(request.POST, school=school)
        if form.is_valid():
            alert = form.save(commit=False)
            alert.health_profile = health_profile
            alert.school = school
            alert.created_by = request.user
            alert.save()
            
            messages.success(request, f'Health alert created for {student.get_full_name()}.')
            return redirect('health:profile_detail', student_id=student.id)
    else:
        form = HealthAlertForm(school=school)
    
    context = {
        'form': form,
        'student': student,
        'health_profile': health_profile,
        'title': 'Create Health Alert',
    }
    
    return render(request, 'health/alert_form.html', context)


@login_required
@school_required
def health_analytics(request):
    """Health analytics and reporting dashboard"""
    school = request.school
    
    # Date range filter
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    if not date_from:
        date_from = (timezone.now() - timezone.timedelta(days=365)).date()
    else:
        date_from = timezone.datetime.strptime(date_from, '%Y-%m-%d').date()
    
    if not date_to:
        date_to = timezone.now().date()
    else:
        date_to = timezone.datetime.strptime(date_to, '%Y-%m-%d').date()
    
    # Basic health statistics
    total_students = HealthProfile.objects.filter(student__school=school).count()
    
    # Vaccination statistics
    total_vaccinations = Vaccination.objects.filter(
        health_profile__student__school=school,
        date_administered__range=[date_from, date_to]
    ).count()
    
    vaccination_schedules = VaccinationSchedule.objects.filter(
        health_profile__student__school=school
    )
    overdue_vaccinations = vaccination_schedules.filter(status='overdue').count()
    completed_vaccinations = vaccination_schedules.filter(status='completed').count()
    
    vaccination_completion_rate = 0
    if vaccination_schedules.count() > 0:
        vaccination_completion_rate = (completed_vaccinations / vaccination_schedules.count()) * 100
    
    # Health screening statistics
    total_screenings = HealthScreening.objects.filter(
        health_profile__student__school=school,
        date__range=[date_from, date_to]
    ).count()
    
    screening_schedules = HealthScreeningSchedule.objects.filter(
        health_profile__student__school=school
    )
    overdue_screenings = screening_schedules.filter(
        scheduled_date__lt=timezone.now().date(),
        status__in=['scheduled', 'in_progress']
    ).count()
    
    # Medical incidents statistics
    total_incidents = MedicalIncident.objects.filter(
        health_profile__student__school=school,
        incident_date__range=[date_from, date_to]
    ).count()
    
    # Health alerts statistics
    active_alerts = HealthAlertInstance.objects.filter(
        alert_rule__school=school,
        status='active'
    ).count()
    
    # Compliance statistics
    compliance_records = ComplianceMonitoring.objects.filter(
        health_profile__student__school=school
    )
    compliant_records = compliance_records.filter(status='compliant').count()
    compliance_rate = 0
    if compliance_records.count() > 0:
        compliance_rate = (compliant_records / compliance_records.count()) * 100
    
    # BMI distribution
    health_profiles = HealthProfile.objects.filter(
        student__school=school,
        height__isnull=False,
        weight__isnull=False
    )
    
    bmi_distribution = {
        'underweight': 0,
        'normal': 0,
        'overweight': 0,
        'obese': 0
    }
    
    for profile in health_profiles:
        bmi_category = profile.bmi_category.lower().replace(' ', '_')
        if 'underweight' in bmi_category:
            bmi_distribution['underweight'] += 1
        elif 'normal' in bmi_category:
            bmi_distribution['normal'] += 1
        elif 'overweight' in bmi_category:
            bmi_distribution['overweight'] += 1
        elif 'obese' in bmi_category:
            bmi_distribution['obese'] += 1
    
    # Chronic conditions prevalence
    chronic_conditions = {}
    for profile in HealthProfile.objects.filter(student__school=school, chronic_conditions__isnull=False).exclude(chronic_conditions=''):
        conditions = [c.strip() for c in profile.chronic_conditions.split(',') if c.strip()]
        for condition in conditions:
            chronic_conditions[condition] = chronic_conditions.get(condition, 0) + 1
    
    # Top 5 chronic conditions
    top_chronic_conditions = sorted(chronic_conditions.items(), key=lambda x: x[1], reverse=True)[:5]
    
    # Monthly incident trends (last 12 months)
    incident_trends = []
    trend_labels = []
    
    for i in range(12):
        month_start = (timezone.now() - timezone.timedelta(days=30*i)).replace(day=1).date()
        month_end = (month_start.replace(month=month_start.month % 12 + 1) - timezone.timedelta(days=1)) if month_start.month < 12 else month_start.replace(year=month_start.year + 1, month=1) - timezone.timedelta(days=1)
        
        incident_count = MedicalIncident.objects.filter(
            health_profile__student__school=school,
            incident_date__range=[month_start, month_end]
        ).count()
        
        incident_trends.insert(0, incident_count)
        trend_labels.insert(0, month_start.strftime('%b %Y'))
    
    # Recent health alerts
    recent_alerts = HealthAlertInstance.objects.filter(
        alert_rule__school=school
    ).order_by('-triggered_at')[:10]
    
    # Health statistics by grade
    grade_stats = []
    from students.models import Grade
    for grade in Grade.objects.filter(school=school):
        grade_students = HealthProfile.objects.filter(student__school=school, student__grade=grade).count()
        grade_incidents = MedicalIncident.objects.filter(
            health_profile__student__school=school,
            health_profile__student__grade=grade,
            incident_date__range=[date_from, date_to]
        ).count()
        
        grade_stats.append({
            'grade': grade.name,
            'students': grade_students,
            'incidents': grade_incidents,
            'incident_rate': (grade_incidents / grade_students * 100) if grade_students > 0 else 0
        })
    
    context = {
        'date_from': date_from,
        'date_to': date_to,
        'total_students': total_students,
        'total_vaccinations': total_vaccinations,
        'overdue_vaccinations': overdue_vaccinations,
        'vaccination_completion_rate': round(vaccination_completion_rate, 1),
        'total_screenings': total_screenings,
        'overdue_screenings': overdue_screenings,
        'total_incidents': total_incidents,
        'active_alerts': active_alerts,
        'compliance_rate': round(compliance_rate, 1),
        'bmi_distribution': bmi_distribution,
        'top_chronic_conditions': top_chronic_conditions,
        'incident_trends': incident_trends,
        'trend_labels': trend_labels,
        'recent_alerts': recent_alerts,
        'grade_stats': grade_stats,
    }
    
    # Get basic statistics
    total_students = Student.objects.filter(school=school, is_active=True).count()
    students_with_profiles = HealthProfile.objects.filter(student__school=school).count()
    
    # Allergy statistics
    allergy_stats = Allergy.objects.filter(
        health_profile__student__school=school,
        is_active=True
    ).values('allergy_type').annotate(count=Count('id')).order_by('-count')
    
    # Medication statistics
    medication_stats = Medication.objects.filter(
        health_profile__student__school=school,
        is_active=True
    ).values('administration_route').annotate(count=Count('id')).order_by('-count')
    
    # Vaccination coverage
    vaccination_coverage = {}
    for vaccine_choice in Vaccination.VACCINE_CHOICES:
        vaccine_code, vaccine_name = vaccine_choice
        total_vaccinated = Vaccination.objects.filter(
            health_profile__student__school=school,
            vaccine_name=vaccine_code
        ).values('health_profile').distinct().count()
        
        coverage_rate = (total_vaccinated / total_students * 100) if total_students > 0 else 0
        vaccination_coverage[vaccine_name] = {
            'count': total_vaccinated,
            'rate': round(coverage_rate, 1)
        }
    
    # Health screening statistics
    screening_stats = HealthScreening.objects.filter(
        health_profile__student__school=school,
        date__gte=timezone.now().date() - timezone.timedelta(days=365)
    ).values('screening_type', 'result').annotate(count=Count('id'))
    
    # BMI distribution
    bmi_distribution = {'underweight': 0, 'normal': 0, 'overweight': 0, 'obese': 0, 'unknown': 0}
    health_profiles = HealthProfile.objects.filter(student__school=school)
    
    for profile in health_profiles:
        bmi_category = profile.bmi_category.lower().replace(' ', '_')
        if bmi_category in bmi_distribution:
            bmi_distribution[bmi_category] += 1
        else:
            bmi_distribution['unknown'] += 1
    
    # Recent trends (last 6 months)
    six_months_ago = timezone.now().date() - timezone.timedelta(days=180)
    
    monthly_screenings = HealthScreening.objects.filter(
        health_profile__student__school=school,
        date__gte=six_months_ago
    ).extra(
        select={'month': "DATE_FORMAT(date, '%%Y-%%m')"}
    ).values('month').annotate(count=Count('id')).order_by('month')
    
    monthly_vaccinations = Vaccination.objects.filter(
        health_profile__student__school=school,
        date_administered__gte=six_months_ago
    ).extra(
        select={'month': "DATE_FORMAT(date_administered, '%%Y-%%m')"}
    ).values('month').annotate(count=Count('id')).order_by('month')
    
    context = {
        'total_students': total_students,
        'students_with_profiles': students_with_profiles,
        'profile_completion_rate': round((students_with_profiles / total_students * 100) if total_students > 0 else 0, 1),
        'allergy_stats': allergy_stats,
        'medication_stats': medication_stats,
        'vaccination_coverage': vaccination_coverage,
        'screening_stats': screening_stats,
        'bmi_distribution': bmi_distribution,
        'monthly_screenings': monthly_screenings,
        'monthly_vaccinations': monthly_vaccinations,
    }
    
    return render(request, 'health/analytics.html', context)


@login_required
@school_required
def health_reports(request):
    """Generate health reports"""
    school = request.school
    
    # Get report parameters
    report_type = request.GET.get('type', 'summary')
    grade_id = request.GET.get('grade')
    class_id = request.GET.get('class')
    
    # Base queryset
    students = Student.objects.filter(school=school, is_active=True)
    
    if grade_id:
        students = students.filter(grade_id=grade_id)
    if class_id:
        students = students.filter(class_enrolled_id=class_id)
    
    context = {
        'report_type': report_type,
        'students': students,
    }
    
    if report_type == 'allergies':
        # Allergy report
        allergies = Allergy.objects.filter(
            health_profile__student__in=students,
            is_active=True
        ).select_related('health_profile__student').order_by('-severity', 'allergen')
        context['allergies'] = allergies
        
    elif report_type == 'medications':
        # Medication report
        medications = Medication.objects.filter(
            health_profile__student__in=students,
            is_active=True
        ).select_related('health_profile__student').order_by('medication_name')
        context['medications'] = medications
        
    elif report_type == 'vaccinations':
        # Vaccination report
        vaccinations = Vaccination.objects.filter(
            health_profile__student__in=students
        ).select_related('health_profile__student').order_by('vaccine_name', '-date_administered')
        context['vaccinations'] = vaccinations
        
    elif report_type == 'alerts':
        # Health alerts report
        alerts = HealthAlert.objects.filter(
            health_profile__student__in=students,
            is_active=True
        ).select_related('health_profile__student').order_by('-priority', 'title')
        context['alerts'] = alerts
    
    # Get filter options
    from students.models import Grade, Class
    grades = Grade.objects.filter(school=school).order_by('name')
    classes = Class.objects.filter(school=school).order_by('name')
    
    context.update({
        'grades': grades,
        'classes': classes,
        'current_grade': grade_id,
        'current_class': class_id,
    })
    
    return render(request, 'health/reports.html', context)


# API Views for AJAX requests
@login_required
@school_required
def api_student_search(request):
    """API endpoint for student search (AJAX)"""
    school = request.school
    query = request.GET.get('q', '')
    
    if len(query) < 2:
        return JsonResponse({'students': []})
    
    students = Student.objects.filter(
        school=school,
        is_active=True
    ).filter(
        Q(first_name__icontains=query) |
        Q(last_name__icontains=query) |
        Q(student_id__icontains=query)
    )[:10]
    
    results = []
    for student in students:
        results.append({
            'id': student.id,
            'name': student.get_full_name(),
            'student_id': student.student_id,
            'grade': student.grade.name if student.grade else '',
            'class': student.class_enrolled.name if student.class_enrolled else ''
        })
    
    return JsonResponse({'students': results})


@login_required
@school_required
def api_health_summary(request, student_id):
    """API endpoint to get student health summary"""
    school = request.school
    student = get_object_or_404(Student, id=student_id, school=school)
    
    try:
        health_profile = student.health_profile
        
        # Get critical information
        critical_allergies = health_profile.allergies.filter(
            severity__in=['severe', 'life_threatening'],
            is_active=True
        ).count()
        
        active_medications = health_profile.medications.filter(is_active=True).count()
        active_alerts = health_profile.alerts.filter(is_active=True).count()
        
        return JsonResponse({
            'has_profile': True,
            'critical_allergies': critical_allergies,
            'active_medications': active_medications,
            'active_alerts': active_alerts,
            'blood_type': health_profile.blood_type,
            'emergency_contact': health_profile.emergency_contact_name,
            'emergency_phone': health_profile.emergency_contact_phone,
        })
        
    except HealthProfile.DoesNotExist:
        return JsonResponse({
            'has_profile': False,
            'critical_allergies': 0,
            'active_medications': 0,
            'active_alerts': 0,
        })


# Incident Management Views

@login_required
@school_required
def incident_list(request):
    """List all medical incidents"""
    school = request.school
    
    # Get all incidents for the school
    incidents = MedicalIncident.objects.filter(
        health_profile__student__school=school
    ).select_related(
        'health_profile__student', 'reported_by'
    ).prefetch_related(
        'treatments', 'follow_ups', 'notifications'
    ).order_by('-incident_date', '-incident_time')
    
    # Filter by status
    status_filter = request.GET.get('status')
    if status_filter:
        incidents = incidents.filter(status=status_filter)
    
    # Filter by severity
    severity_filter = request.GET.get('severity')
    if severity_filter:
        incidents = incidents.filter(severity=severity_filter)
    
    # Filter by incident type
    type_filter = request.GET.get('type')
    if type_filter:
        incidents = incidents.filter(incident_type=type_filter)
    
    # Filter by date range
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    if date_from:
        incidents = incidents.filter(incident_date__gte=date_from)
    if date_to:
        incidents = incidents.filter(incident_date__lte=date_to)
    
    # Search functionality
    query = request.GET.get('q')
    if query:
        incidents = incidents.filter(
            Q(incident_id__icontains=query) |
            Q(health_profile__student__first_name__icontains=query) |
            Q(health_profile__student__last_name__icontains=query) |
            Q(description__icontains=query) |
            Q(location__icontains=query)
        )
    
    # Pagination
    paginator = Paginator(incidents, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get filter options
    status_choices = MedicalIncident.STATUS_CHOICES
    severity_choices = MedicalIncident.SEVERITY_CHOICES
    type_choices = MedicalIncident.INCIDENT_TYPE_CHOICES
    
    context = {
        'page_obj': page_obj,
        'status_choices': status_choices,
        'severity_choices': severity_choices,
        'type_choices': type_choices,
        'current_status': status_filter,
        'current_severity': severity_filter,
        'current_type': type_filter,
        'date_from': date_from,
        'date_to': date_to,
        'query': query,
    }
    
    return render(request, 'health/incident_list.html', context)


@login_required
@school_required
def incident_detail(request, incident_id):
    """View detailed incident information"""
    school = request.school
    incident = get_object_or_404(
        MedicalIncident, 
        id=incident_id, 
        health_profile__student__school=school
    )
    
    # Get related data
    treatments = incident.treatments.all().order_by('treatment_time')
    follow_ups = incident.follow_ups.all().order_by('scheduled_date', 'scheduled_time')
    notifications = incident.notifications.all().order_by('-scheduled_time')
    
    context = {
        'incident': incident,
        'treatments': treatments,
        'follow_ups': follow_ups,
        'notifications': notifications,
    }
    
    return render(request, 'health/incident_detail.html', context)


@login_required
@school_required
def incident_create(request, student_id=None):
    """Create new medical incident"""
    school = request.school
    
    # Get student if provided
    student = None
    health_profile = None
    if student_id:
        student = get_object_or_404(Student, id=student_id, school=school)
        health_profile, created = HealthProfile.objects.get_or_create(
            student=student,
            defaults={'school': school, 'created_by': request.user}
        )
    
    if request.method == 'POST':
        form = MedicalIncidentForm(request.POST, request.FILES, school=school)
        if form.is_valid():
            incident = form.save(commit=False)
            if health_profile:
                incident.health_profile = health_profile
            incident.school = school
            incident.created_by = request.user
            incident.save()
            
            messages.success(request, f'Medical incident {incident.incident_id} created successfully.')
            return redirect('health:incident_detail', incident_id=incident.id)
    else:
        form = MedicalIncidentForm(school=school)
        if health_profile:
            form.instance.health_profile = health_profile
    
    context = {
        'form': form,
        'student': student,
        'health_profile': health_profile,
        'title': 'Report Medical Incident',
    }
    
    return render(request, 'health/incident_form.html', context)


@login_required
@school_required
def incident_edit(request, incident_id):
    """Edit medical incident"""
    school = request.school
    incident = get_object_or_404(
        MedicalIncident, 
        id=incident_id, 
        health_profile__student__school=school
    )
    
    if request.method == 'POST':
        form = MedicalIncidentForm(request.POST, request.FILES, instance=incident, school=school)
        if form.is_valid():
            incident = form.save(commit=False)
            incident.updated_by = request.user
            incident.save()
            
            messages.success(request, f'Incident {incident.incident_id} updated successfully.')
            return redirect('health:incident_detail', incident_id=incident.id)
    else:
        form = MedicalIncidentForm(instance=incident, school=school)
    
    context = {
        'form': form,
        'incident': incident,
        'student': incident.health_profile.student,
        'title': f'Edit Incident {incident.incident_id}',
    }
    
    return render(request, 'health/incident_form.html', context)


@login_required
@school_required
def incident_treatment_add(request, incident_id):
    """Add treatment to incident"""
    school = request.school
    incident = get_object_or_404(
        MedicalIncident, 
        id=incident_id, 
        health_profile__student__school=school
    )
    
    if request.method == 'POST':
        form = IncidentTreatmentForm(request.POST, school=school)
        if form.is_valid():
            treatment = form.save(commit=False)
            treatment.incident = incident
            treatment.school = school
            treatment.created_by = request.user
            treatment.save()
            
            messages.success(request, 'Treatment record added successfully.')
            return redirect('health:incident_detail', incident_id=incident.id)
    else:
        form = IncidentTreatmentForm(school=school)
    
    context = {
        'form': form,
        'incident': incident,
        'title': f'Add Treatment - {incident.incident_id}',
    }
    
    return render(request, 'health/incident_treatment_form.html', context)


@login_required
@school_required
def incident_follow_up_add(request, incident_id):
    """Add follow-up to incident"""
    school = request.school
    incident = get_object_or_404(
        MedicalIncident, 
        id=incident_id, 
        health_profile__student__school=school
    )
    
    if request.method == 'POST':
        form = IncidentFollowUpForm(request.POST, school=school)
        if form.is_valid():
            follow_up = form.save(commit=False)
            follow_up.incident = incident
            follow_up.school = school
            follow_up.created_by = request.user
            follow_up.save()
            
            messages.success(request, 'Follow-up task created successfully.')
            return redirect('health:incident_detail', incident_id=incident.id)
    else:
        form = IncidentFollowUpForm(school=school)
    
    context = {
        'form': form,
        'incident': incident,
        'title': f'Add Follow-up - {incident.incident_id}',
    }
    
    return render(request, 'health/incident_follow_up_form.html', context)


@login_required
@school_required
def incident_notification_add(request, incident_id):
    """Add notification to incident"""
    school = request.school
    incident = get_object_or_404(
        MedicalIncident, 
        id=incident_id, 
        health_profile__student__school=school
    )
    
    if request.method == 'POST':
        form = IncidentNotificationForm(request.POST, school=school)
        if form.is_valid():
            notification = form.save(commit=False)
            notification.incident = incident
            notification.school = school
            notification.created_by = request.user
            notification.sent_by = request.user.employee if hasattr(request.user, 'employee') else None
            notification.save()
            
            messages.success(request, 'Notification scheduled successfully.')
            return redirect('health:incident_detail', incident_id=incident.id)
    else:
        form = IncidentNotificationForm(school=school)
        # Pre-populate with parent information if available
        student = incident.health_profile.student
        if student.parents.exists():
            parent = student.parents.first()
            form.fields['recipient_name'].initial = f"{parent.first_name} {parent.last_name}"
            form.fields['recipient_relationship'].initial = parent.relationship
            form.fields['recipient_contact'].initial = parent.phone or parent.email
    
    context = {
        'form': form,
        'incident': incident,
        'title': f'Send Notification - {incident.incident_id}',
    }
    
    return render(request, 'health/incident_notification_form.html', context)


@login_required
@school_required
def incident_analytics(request):
    """Incident analytics and reporting"""
    school = request.school
    
    # Date range for analysis (default to current academic year)
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    if not date_from or not date_to:
        # Default to current academic year
        today = timezone.now().date()
        if today.month >= 9:  # September onwards is new academic year
            date_from = f"{today.year}-09-01"
            date_to = f"{today.year + 1}-08-31"
        else:
            date_from = f"{today.year - 1}-09-01"
            date_to = f"{today.year}-08-31"
    
    # Base queryset
    incidents = MedicalIncident.objects.filter(
        health_profile__student__school=school,
        incident_date__gte=date_from,
        incident_date__lte=date_to
    )
    
    # Basic statistics
    total_incidents = incidents.count()
    
    # Incidents by type
    incidents_by_type = incidents.values('incident_type').annotate(
        count=Count('id')
    ).order_by('-count')
    
    # Incidents by severity
    incidents_by_severity = incidents.values('severity').annotate(
        count=Count('id')
    ).order_by('-count')
    
    # Incidents by status
    incidents_by_status = incidents.values('status').annotate(
        count=Count('id')
    ).order_by('-count')
    
    # Incidents by location
    incidents_by_location = incidents.values('location').annotate(
        count=Count('id')
    ).order_by('-count')[:10]
    
    # Monthly trend
    monthly_incidents = incidents.extra(
        select={'month': "DATE_FORMAT(incident_date, '%%Y-%%m')"}
    ).values('month').annotate(count=Count('id')).order_by('month')
    
    # Response time analysis (time from incident to first treatment)
    incidents_with_treatment = incidents.filter(treatments__isnull=False).annotate(
        first_treatment_time=models.Min('treatments__treatment_time')
    )
    
    # Most common injuries/incidents
    common_incidents = incidents.values('description').annotate(
        count=Count('id')
    ).order_by('-count')[:10]
    
    # Students with multiple incidents
    frequent_students = incidents.values(
        'health_profile__student__first_name',
        'health_profile__student__last_name',
        'health_profile__student_id'
    ).annotate(
        incident_count=Count('id')
    ).filter(incident_count__gt=1).order_by('-incident_count')[:10]
    
    # Time of day analysis
    hourly_distribution = incidents.extra(
        select={'hour': "HOUR(incident_time)"}
    ).values('hour').annotate(count=Count('id')).order_by('hour')
    
    # Day of week analysis
    daily_distribution = incidents.extra(
        select={'weekday': "DAYOFWEEK(incident_date)"}
    ).values('weekday').annotate(count=Count('id')).order_by('weekday')
    
    context = {
        'date_from': date_from,
        'date_to': date_to,
        'total_incidents': total_incidents,
        'incidents_by_type': incidents_by_type,
        'incidents_by_severity': incidents_by_severity,
        'incidents_by_status': incidents_by_status,
        'incidents_by_location': incidents_by_location,
        'monthly_incidents': monthly_incidents,
        'common_incidents': common_incidents,
        'frequent_students': frequent_students,
        'hourly_distribution': hourly_distribution,
        'daily_distribution': daily_distribution,
    }
    
    return render(request, 'health/incident_analytics.html', context)


@login_required
@school_required
@require_http_methods(["POST"])
def incident_status_update(request, incident_id):
    """Update incident status via AJAX"""
    school = request.school
    incident = get_object_or_404(
        MedicalIncident, 
        id=incident_id, 
        health_profile__student__school=school
    )
    
    new_status = request.POST.get('status')
    if new_status in dict(MedicalIncident.STATUS_CHOICES):
        incident.status = new_status
        if new_status == 'resolved':
            incident.resolved_date = timezone.now().date()
        incident.save()
        
        return JsonResponse({
            'success': True,
            'message': f'Incident status updated to {incident.get_status_display()}'
        })
    
    return JsonResponse({
        'success': False,
        'message': 'Invalid status'
    })


@login_required
@school_required
def incident_dashboard(request):
    """Incident management dashboard"""
    school = request.school
    
    # Recent incidents (last 7 days)
    week_ago = timezone.now().date() - timezone.timedelta(days=7)
    recent_incidents = MedicalIncident.objects.filter(
        health_profile__student__school=school,
        incident_date__gte=week_ago
    ).select_related('health_profile__student').order_by('-incident_date', '-incident_time')[:10]
    
    # Incidents requiring attention
    attention_incidents = MedicalIncident.objects.filter(
        health_profile__student__school=school,
        status__in=['reported', 'in_treatment']
    ).select_related('health_profile__student').order_by('-severity', '-incident_date')[:10]
    
    # Overdue follow-ups
    overdue_follow_ups = IncidentFollowUp.objects.filter(
        incident__health_profile__student__school=school,
        status__in=['pending', 'in_progress'],
        scheduled_date__lt=timezone.now().date()
    ).select_related('incident__health_profile__student').order_by('scheduled_date')[:10]
    
    # Statistics
    today = timezone.now().date()
    this_month = today.replace(day=1)
    
    stats = {
        'today_incidents': MedicalIncident.objects.filter(
            health_profile__student__school=school,
            incident_date=today
        ).count(),
        'this_month_incidents': MedicalIncident.objects.filter(
            health_profile__student__school=school,
            incident_date__gte=this_month
        ).count(),
        'active_incidents': MedicalIncident.objects.filter(
            health_profile__student__school=school,
            status__in=['reported', 'in_treatment']
        ).count(),
        'pending_follow_ups': IncidentFollowUp.objects.filter(
            incident__health_profile__student__school=school,
            status='pending'
        ).count(),
    }
    
    context = {
        'recent_incidents': recent_incidents,
        'attention_incidents': attention_incidents,
        'overdue_follow_ups': overdue_follow_ups,
        'stats': stats,
    }
    
    return render(request, 'health/incident_dashboard.html', context)
# Additional forms import
from .forms import HealthScreeningScheduleForm, MedicalAppointmentForm, ComplianceMonitoringForm


# Health Monitoring Views

@login_required
@school_required
def health_monitoring_dashboard(request):
    """Health monitoring dashboard with screenings, appointments, and compliance"""
    school = request.school
    
    # Get upcoming screenings
    upcoming_screenings = HealthScreeningSchedule.objects.filter(
        health_profile__student__school=school,
        status__in=['scheduled', 'in_progress'],
        scheduled_date__gte=timezone.now().date()
    ).order_by('scheduled_date')[:10]
    
    # Get overdue screenings
    overdue_screenings = HealthScreeningSchedule.objects.filter(
        health_profile__student__school=school,
        status__in=['scheduled', 'in_progress'],
        scheduled_date__lt=timezone.now().date()
    ).count()
    
    # Get upcoming appointments
    upcoming_appointments = MedicalAppointment.objects.filter(
        health_profile__student__school=school,
        status__in=['scheduled', 'confirmed'],
        appointment_date__gte=timezone.now().date()
    ).order_by('appointment_date')[:10]
    
    # Get compliance issues
    compliance_issues = ComplianceMonitoring.objects.filter(
        health_profile__student__school=school,
        status='non_compliant'
    ).count()
    
    # Get expiring compliance
    expiring_compliance = ComplianceMonitoring.objects.filter(
        health_profile__student__school=school,
        status='compliant',
        expiry_date__lte=timezone.now().date() + timezone.timedelta(days=30),
        expiry_date__gte=timezone.now().date()
    ).count()
    
    # Statistics
    total_students = HealthProfile.objects.filter(student__school=school).count()
    completed_screenings_this_month = HealthScreeningSchedule.objects.filter(
        health_profile__student__school=school,
        status='completed',
        completed_date__month=timezone.now().month,
        completed_date__year=timezone.now().year
    ).count()
    
    context = {
        'upcoming_screenings': upcoming_screenings,
        'overdue_screenings': overdue_screenings,
        'upcoming_appointments': upcoming_appointments,
        'compliance_issues': compliance_issues,
        'expiring_compliance': expiring_compliance,
        'total_students': total_students,
        'completed_screenings_this_month': completed_screenings_this_month,
    }
    
    return render(request, 'health/monitoring_dashboard.html', context)


@login_required
@school_required
def screening_schedule_list(request):
    """List of health screening schedules"""
    school = request.user.school
    
    # Filter parameters
    status_filter = request.GET.get('status', '')
    screening_type_filter = request.GET.get('screening_type', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    search_query = request.GET.get('search', '')
    
    # Base queryset
    screenings = HealthScreeningSchedule.objects.filter(
        health_profile__student__school=school
    ).select_related('health_profile__student', 'assigned_to')
    
    # Apply filters
    if status_filter:
        screenings = screenings.filter(status=status_filter)
    
    if screening_type_filter:
        screenings = screenings.filter(screening_type=screening_type_filter)
    
    if date_from:
        screenings = screenings.filter(scheduled_date__gte=date_from)
    
    if date_to:
        screenings = screenings.filter(scheduled_date__lte=date_to)
    
    if search_query:
        screenings = screenings.filter(
            Q(health_profile__student__first_name__icontains=search_query) |
            Q(health_profile__student__last_name__icontains=search_query) |
            Q(title__icontains=search_query) |
            Q(description__icontains=search_query)
        )
    
    # Pagination
    paginator = Paginator(screenings.order_by('scheduled_date'), 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'status_filter': status_filter,
        'screening_type_filter': screening_type_filter,
        'date_from': date_from,
        'date_to': date_to,
        'search_query': search_query,
        'screening_types': HealthScreeningSchedule.SCREENING_TYPE_CHOICES,
        'status_choices': HealthScreeningSchedule.STATUS_CHOICES,
    }
    
    return render(request, 'health/screening_schedule_list.html', context)


@login_required
@school_required
def screening_schedule_create(request):
    """Create a new health screening schedule"""
    school = request.school
    
    if request.method == 'POST':
        form = HealthScreeningScheduleForm(request.POST, school=school)
        if form.is_valid():
            screening = form.save()
            messages.success(request, f'Health screening scheduled for {screening.health_profile.student.get_full_name()}.')
            return redirect('health:screening_schedule_list')
    else:
        form = HealthScreeningScheduleForm(school=school)
        
        # Pre-populate with student if provided
        student_id = request.GET.get('student_id')
        if student_id:
            try:
                student = Student.objects.get(id=student_id, school=school)
                health_profile, created = HealthProfile.objects.get_or_create(student=student)
                form.initial['health_profile'] = health_profile
            except Student.DoesNotExist:
                pass
    
    context = {
        'form': form,
        'title': 'Schedule Health Screening',
    }
    
    return render(request, 'health/screening_schedule_form.html', context)


@login_required
@school_required
def appointment_list(request):
    """List of medical appointments"""
    school = request.user.school
    
    # Filter parameters
    status_filter = request.GET.get('status', '')
    appointment_type_filter = request.GET.get('appointment_type', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    search_query = request.GET.get('search', '')
    
    # Base queryset
    appointments = MedicalAppointment.objects.filter(
        health_profile__student__school=school
    ).select_related('health_profile__student', 'school_coordinator')
    
    # Apply filters
    if status_filter:
        appointments = appointments.filter(status=status_filter)
    
    if appointment_type_filter:
        appointments = appointments.filter(appointment_type=appointment_type_filter)
    
    if date_from:
        appointments = appointments.filter(appointment_date__gte=date_from)
    
    if date_to:
        appointments = appointments.filter(appointment_date__lte=date_to)
    
    if search_query:
        appointments = appointments.filter(
            Q(health_profile__student__first_name__icontains=search_query) |
            Q(health_profile__student__last_name__icontains=search_query) |
            Q(title__icontains=search_query) |
            Q(provider_name__icontains=search_query)
        )
    
    # Pagination
    paginator = Paginator(appointments.order_by('appointment_date', 'appointment_time'), 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'status_filter': status_filter,
        'appointment_type_filter': appointment_type_filter,
        'date_from': date_from,
        'date_to': date_to,
        'search_query': search_query,
        'appointment_types': MedicalAppointment.APPOINTMENT_TYPE_CHOICES,
        'status_choices': MedicalAppointment.STATUS_CHOICES,
    }
    
    return render(request, 'health/appointment_list.html', context)


@login_required
@school_required
def appointment_create(request):
    """Create a new medical appointment"""
    school = request.school
    
    if request.method == 'POST':
        form = MedicalAppointmentForm(request.POST, school=school)
        if form.is_valid():
            appointment = form.save()
            messages.success(request, f'Medical appointment scheduled for {appointment.health_profile.student.get_full_name()}.')
            return redirect('health:appointment_list')
    else:
        form = MedicalAppointmentForm(school=school)
        
        # Pre-populate with student if provided
        student_id = request.GET.get('student_id')
        if student_id:
            try:
                student = Student.objects.get(id=student_id, school=school)
                health_profile, created = HealthProfile.objects.get_or_create(student=student)
                form.initial['health_profile'] = health_profile
            except Student.DoesNotExist:
                pass
    
    context = {
        'form': form,
        'title': 'Schedule Medical Appointment',
    }
    
    return render(request, 'health/appointment_form.html', context)


@login_required
@school_required
def compliance_monitoring_list(request):
    """List of compliance monitoring records"""
    school = request.school
    
    # Filter parameters
    status_filter = request.GET.get('status', '')
    compliance_type_filter = request.GET.get('compliance_type', '')
    overdue_only = request.GET.get('overdue_only', '')
    search_query = request.GET.get('search', '')
    
    # Base queryset
    compliance_records = ComplianceMonitoring.objects.filter(
        health_profile__student__school=school
    ).select_related('health_profile__student', 'checked_by')
    
    # Apply filters
    if status_filter:
        compliance_records = compliance_records.filter(status=status_filter)
    
    if compliance_type_filter:
        compliance_records = compliance_records.filter(compliance_type=compliance_type_filter)
    
    if overdue_only:
        compliance_records = compliance_records.filter(
            required_by_date__lt=timezone.now().date(),
            status__in=['pending', 'non_compliant', 'partially_compliant']
        )
    
    if search_query:
        compliance_records = compliance_records.filter(
            Q(health_profile__student__first_name__icontains=search_query) |
            Q(health_profile__student__last_name__icontains=search_query) |
            Q(requirement_name__icontains=search_query) |
            Q(description__icontains=search_query)
        )
    
    # Pagination
    paginator = Paginator(compliance_records.order_by('required_by_date'), 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'status_filter': status_filter,
        'compliance_type_filter': compliance_type_filter,
        'overdue_only': overdue_only,
        'search_query': search_query,
        'compliance_types': ComplianceMonitoring.COMPLIANCE_TYPE_CHOICES,
        'status_choices': ComplianceMonitoring.STATUS_CHOICES,
    }
    
    return render(request, 'health/compliance_monitoring_list.html', context)


@login_required
@school_required
def health_trends_dashboard(request):
    """Health trends analysis dashboard"""
    school = request.school
    
    # Get recent trend analyses
    recent_trends = HealthTrendAnalysis.objects.filter(
        health_profile__student__school=school
    ).select_related('health_profile__student', 'analyzed_by').order_by('-analysis_end_date')[:10]
    
    # Get trends requiring action
    action_required_trends = HealthTrendAnalysis.objects.filter(
        health_profile__student__school=school,
        action_required=True
    ).count()
    
    # Get trend statistics by type
    trend_stats = HealthTrendAnalysis.objects.filter(
        health_profile__student__school=school
    ).values('trend_type').annotate(count=Count('id')).order_by('-count')
    
    context = {
        'recent_trends': recent_trends,
        'action_required_trends': action_required_trends,
        'trend_stats': trend_stats,
    }
    
    return render(request, 'health/trends_dashboard.html', context)


@login_required
@school_required
def health_reports_list(request):
    """List of health reports"""
    school = request.user.school
    
    # Filter parameters
    report_type_filter = request.GET.get('report_type', '')
    status_filter = request.GET.get('status', '')
    search_query = request.GET.get('search', '')
    
    # Base queryset
    reports = HealthReport.objects.filter(
        generated_by__school=school
    ).select_related('generated_by', 'reviewed_by', 'approved_by')
    
    # Apply filters
    if report_type_filter:
        reports = reports.filter(report_type=report_type_filter)
    
    if status_filter:
        reports = reports.filter(status=status_filter)
    
    if search_query:
        reports = reports.filter(
            Q(title__icontains=search_query) |
            Q(description__icontains=search_query)
        )
    
    # Pagination
    paginator = Paginator(reports.order_by('-created_at'), 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'report_type_filter': report_type_filter,
        'status_filter': status_filter,
        'search_query': search_query,
        'report_types': HealthReport.REPORT_TYPE_CHOICES,
        'status_choices': HealthReport.STATUS_CHOICES,
    }
    
    return render(request, 'health/health_reports_list.html', context)

@login_required
@school_required
def vaccination_tracking(request):
    """Vaccination tracking dashboard"""
    school = request.school
    
    # Filter parameters
    status_filter = request.GET.get('status', '')
    vaccine_filter = request.GET.get('vaccine', '')
    grade_filter = request.GET.get('grade', '')
    overdue_only = request.GET.get('overdue_only', '')
    
    # Base queryset
    vaccination_schedules = VaccinationSchedule.objects.filter(
        health_profile__student__school=school
    ).select_related('health_profile__student', 'administered_by')
    
    # Apply filters
    if status_filter:
        vaccination_schedules = vaccination_schedules.filter(status=status_filter)
    
    if vaccine_filter:
        vaccination_schedules = vaccination_schedules.filter(vaccine_name=vaccine_filter)
    
    if grade_filter:
        vaccination_schedules = vaccination_schedules.filter(health_profile__student__grade_id=grade_filter)
    
    if overdue_only:
        vaccination_schedules = vaccination_schedules.filter(
            due_date__lt=timezone.now().date(),
            status__in=['scheduled', 'due']
        )
    
    # Statistics
    total_schedules = vaccination_schedules.count()
    overdue_count = vaccination_schedules.filter(
        due_date__lt=timezone.now().date(),
        status__in=['scheduled', 'due']
    ).count()
    completed_count = vaccination_schedules.filter(status='completed').count()
    
    completion_rate = 0
    if total_schedules > 0:
        completion_rate = (completed_count / total_schedules) * 100
    
    # Vaccination coverage by vaccine type
    vaccine_coverage = {}
    for choice in Vaccination.VACCINE_CHOICES:
        vaccine_name = choice[0]
        total_for_vaccine = vaccination_schedules.filter(vaccine_name=vaccine_name).count()
        completed_for_vaccine = vaccination_schedules.filter(
            vaccine_name=vaccine_name, 
            status='completed'
        ).count()
        
        if total_for_vaccine > 0:
            coverage_rate = (completed_for_vaccine / total_for_vaccine) * 100
            vaccine_coverage[choice[1]] = {
                'total': total_for_vaccine,
                'completed': completed_for_vaccine,
                'rate': coverage_rate
            }
    
    # Pagination
    from django.core.paginator import Paginator
    paginator = Paginator(vaccination_schedules.order_by('due_date'), 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get filter options
    from students.models import Grade
    grades = Grade.objects.filter(school=school)
    
    context = {
        'page_obj': page_obj,
        'total_schedules': total_schedules,
        'overdue_count': overdue_count,
        'completed_count': completed_count,
        'completion_rate': round(completion_rate, 1),
        'vaccine_coverage': vaccine_coverage,
        'status_filter': status_filter,
        'vaccine_filter': vaccine_filter,
        'grade_filter': grade_filter,
        'overdue_only': overdue_only,
        'grades': grades,
        'vaccine_choices': Vaccination.VACCINE_CHOICES,
        'status_choices': VaccinationSchedule.STATUS_CHOICES,
    }
    
    return render(request, 'health/vaccination_tracking.html', context)


@login_required
@school_required
def health_alerts_dashboard(request):
    """Health alerts management dashboard"""
    school = request.school
    
    # Filter parameters
    status_filter = request.GET.get('status', '')
    severity_filter = request.GET.get('severity', '')
    
    # Base queryset
    alerts = HealthAlertInstance.objects.filter(
        alert_rule__school=school
    ).select_related('alert_rule', 'acknowledged_by', 'resolved_by')
    
    # Apply filters
    if status_filter:
        alerts = alerts.filter(status=status_filter)
    
    if severity_filter:
        alerts = alerts.filter(alert_rule__severity=severity_filter)
    
    # Statistics
    total_alerts = alerts.count()
    active_alerts = alerts.filter(status='active').count()
    overdue_alerts = sum(1 for alert in alerts.filter(status='active') if alert.is_overdue)
    
    # Alert statistics by severity
    severity_stats = {}
    for severity_choice in HealthAlertRule.SEVERITY_CHOICES:
        severity = severity_choice[0]
        count = alerts.filter(alert_rule__severity=severity).count()
        severity_stats[severity_choice[1]] = count
    
    # Recent alert activity
    recent_alerts = alerts.order_by('-triggered_at')[:10]
    
    # Alert rules
    alert_rules = HealthAlertRule.objects.filter(school=school, is_active=True)
    
    # Pagination
    from django.core.paginator import Paginator
    paginator = Paginator(alerts.order_by('-triggered_at'), 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'total_alerts': total_alerts,
        'active_alerts': active_alerts,
        'overdue_alerts': overdue_alerts,
        'severity_stats': severity_stats,
        'recent_alerts': recent_alerts,
        'alert_rules': alert_rules,
        'status_filter': status_filter,
        'severity_filter': severity_filter,
        'status_choices': HealthAlertInstance.STATUS_CHOICES,
        'severity_choices': HealthAlertRule.SEVERITY_CHOICES,
    }
    
    return render(request, 'health/alerts_dashboard.html', context)


@login_required
@school_required
def health_trends_analysis(request):
    """Health trends analysis dashboard"""
    school = request.user.school
    
    # Date range
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    if not date_from:
        date_from = (timezone.now() - timezone.timedelta(days=365)).date()
    else:
        date_from = timezone.datetime.strptime(date_from, '%Y-%m-%d').date()
    
    if not date_to:
        date_to = timezone.now().date()
    else:
        date_to = timezone.datetime.strptime(date_to, '%Y-%m-%d').date()
    
    # Health trend analyses
    trend_analyses = HealthTrendAnalysis.objects.filter(
        health_profile__student__school=school,
        analysis_end_date__range=[date_from, date_to]
    ).select_related('health_profile__student', 'analyzed_by')
    
    # Trends requiring action
    action_required_trends = trend_analyses.filter(action_required=True)
    
    # Trend statistics by type
    trend_type_stats = {}
    for trend_type_choice in HealthTrendAnalysis.TREND_TYPE_CHOICES:
        trend_type = trend_type_choice[0]
        count = trend_analyses.filter(trend_type=trend_type).count()
        if count > 0:
            trend_type_stats[trend_type_choice[1]] = count
    
    # Trend direction distribution
    trend_direction_stats = {}
    for direction_choice in HealthTrendAnalysis.TREND_DIRECTION_CHOICES:
        direction = direction_choice[0]
        count = trend_analyses.filter(trend_direction=direction).count()
        if count > 0:
            trend_direction_stats[direction_choice[1]] = count
    
    # BMI trends over time
    bmi_trends = []
    bmi_labels = []
    
    # Get monthly BMI averages for the last 12 months
    for i in range(12):
        month_start = (timezone.now() - timezone.timedelta(days=30*i)).replace(day=1).date()
        month_end = (month_start.replace(month=month_start.month % 12 + 1) - timezone.timedelta(days=1)) if month_start.month < 12 else month_start.replace(year=month_start.year + 1, month=1) - timezone.timedelta(days=1)
        
        # Calculate average BMI for students with recorded height/weight
        health_profiles = HealthProfile.objects.filter(
            student__school=school,
            height__isnull=False,
            weight__isnull=False,
            updated_at__range=[month_start, month_end]
        )
        
        if health_profiles.exists():
            avg_bmi = sum(profile.bmi for profile in health_profiles if profile.bmi) / health_profiles.count()
            bmi_trends.insert(0, round(avg_bmi, 1))
        else:
            bmi_trends.insert(0, 0)
        
        bmi_labels.insert(0, month_start.strftime('%b %Y'))
    
    context = {
        'date_from': date_from,
        'date_to': date_to,
        'trend_analyses': trend_analyses[:20],  # Latest 20 trends
        'action_required_count': action_required_trends.count(),
        'trend_type_stats': trend_type_stats,
        'trend_direction_stats': trend_direction_stats,
        'bmi_trends': bmi_trends,
        'bmi_labels': bmi_labels,
    }
    
    return render(request, 'health/trends_analysis.html', context)