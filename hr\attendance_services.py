"""
HR Attendance Services
Provides comprehensive attendance tracking functionality including
multiple attendance methods, biometric integration, and analytics.
"""

from django.db import models
from django.utils import timezone
from django.db.models import Q, Count, Avg, Sum
from datetime import datetime, timedelta, time
from typing import Dict, List, Optional, Tuple
import json
import logging

from .models import Employee, AttendanceRecord, Department
from core.models import School

logger = logging.getLogger(__name__)


class AttendanceService:
    """Service class for attendance management operations"""
    
    @staticmethod
    def mark_attendance(employee_id: str, attendance_type: str = 'check_in', 
                       timestamp: datetime = None, method: str = 'manual', created_by=None) -> Dict:
        """
        Mark attendance for an employee
        
        Args:
            employee_id: Employee ID or user ID
            attendance_type: 'check_in', 'check_out', 'break_start', 'break_end'
            timestamp: When the attendance was marked (defaults to now)
            method: How attendance was marked ('manual', 'biometric', 'qr_code', 'web')
        
        Returns:
            Dict with success status and message
        """
        try:
            # Get employee
            try:
                employee = Employee.objects.get(employee_id=employee_id, employment_status='active')
            except Employee.DoesNotExist:
                return {'success': False, 'message': 'Employee not found or inactive'}
            
            if timestamp is None:
                timestamp = timezone.now()
            
            date = timestamp.date()
            time_value = timestamp.time()
            
            # Get or create attendance record for the date
            defaults = {
                'school': employee.school,
                'status': 'present',
                'is_manual': method == 'manual',
            }
            
            # Set created_by if provided, otherwise use the employee's user
            if created_by:
                defaults['created_by'] = created_by
            else:
                defaults['created_by'] = employee.user
            
            attendance, created = AttendanceRecord.objects.get_or_create(
                employee=employee,
                date=date,
                defaults=defaults
            )
            
            # Update the appropriate time field
            if attendance_type == 'check_in':
                if attendance.check_in_time is None:
                    attendance.check_in_time = time_value
                    # Check if late
                    work_start_time = time(9, 0)  # 9:00 AM default
                    if time_value > work_start_time:
                        attendance.status = 'late'
                else:
                    return {'success': False, 'message': 'Already checked in today'}
                    
            elif attendance_type == 'check_out':
                if attendance.check_in_time is None:
                    return {'success': False, 'message': 'Must check in first'}
                if attendance.check_out_time is None:
                    attendance.check_out_time = time_value
                else:
                    return {'success': False, 'message': 'Already checked out today'}
                    
            elif attendance_type == 'break_start':
                if attendance.check_in_time is None:
                    return {'success': False, 'message': 'Must check in first'}
                attendance.break_start_time = time_value
                
            elif attendance_type == 'break_end':
                if attendance.break_start_time is None:
                    return {'success': False, 'message': 'Must start break first'}
                attendance.break_end_time = time_value
            
            attendance.save()
            
            return {
                'success': True, 
                'message': f'{attendance_type.replace("_", " ").title()} recorded successfully',
                'attendance_id': attendance.id,
                'total_hours': attendance.total_hours
            }
            
        except Exception as e:
            logger.error(f"Error marking attendance: {str(e)}")
            return {'success': False, 'message': 'Error recording attendance'}
    
    @staticmethod
    def bulk_mark_attendance(employee_ids: List[str], date: datetime.date, 
                           status: str = 'present', created_by=None) -> Dict:
        """
        Mark attendance for multiple employees at once
        
        Args:
            employee_ids: List of employee IDs
            date: Date for attendance
            status: Attendance status
            created_by: User who created the records
        
        Returns:
            Dict with success count and errors
        """
        success_count = 0
        errors = []
        
        for emp_id in employee_ids:
            try:
                employee = Employee.objects.get(employee_id=emp_id, employment_status='active')
                
                attendance, created = AttendanceRecord.objects.get_or_create(
                    employee=employee,
                    date=date,
                    defaults={
                        'school': employee.school,
                        'status': status,
                        'is_manual': True,
                        'created_by': created_by or employee.user
                    }
                )
                
                if created:
                    success_count += 1
                else:
                    # Update existing record
                    attendance.status = status
                    attendance.save()
                    success_count += 1
                    
            except Employee.DoesNotExist:
                errors.append(f"Employee {emp_id} not found")
            except Exception as e:
                errors.append(f"Error for employee {emp_id}: {str(e)}")
        
        return {
            'success_count': success_count,
            'errors': errors,
            'total_processed': len(employee_ids)
        }
    
    @staticmethod
    def calculate_overtime(employee: Employee, date: datetime.date) -> float:
        """
        Calculate overtime hours for an employee on a specific date
        
        Args:
            employee: Employee instance
            date: Date to calculate overtime for
        
        Returns:
            Overtime hours as float
        """
        try:
            attendance = AttendanceRecord.objects.get(employee=employee, date=date)
            
            if not attendance.check_in_time or not attendance.check_out_time:
                return 0.0
            
            total_hours = attendance.total_hours
            standard_hours = 8.0  # Standard work day
            
            overtime = max(0, total_hours - standard_hours)
            return round(overtime, 2)
            
        except AttendanceRecord.DoesNotExist:
            return 0.0
    
    @staticmethod
    def get_attendance_summary(employee: Employee, start_date: datetime.date, 
                             end_date: datetime.date) -> Dict:
        """
        Get attendance summary for an employee over a date range
        
        Args:
            employee: Employee instance
            start_date: Start date for summary
            end_date: End date for summary
        
        Returns:
            Dict with attendance statistics
        """
        attendance_records = AttendanceRecord.objects.filter(
            employee=employee,
            date__gte=start_date,
            date__lte=end_date
        )
        
        total_days = (end_date - start_date).days + 1
        working_days = total_days  # Simplified - should exclude weekends/holidays
        
        present_days = attendance_records.filter(
            status__in=['present', 'late']
        ).count()
        
        absent_days = working_days - present_days
        late_days = attendance_records.filter(status='late').count()
        
        # Calculate total working hours
        total_hours = sum([
            record.total_hours for record in attendance_records 
            if record.total_hours > 0
        ])
        
        # Calculate overtime
        total_overtime = sum([
            AttendanceService.calculate_overtime(employee, record.date)
            for record in attendance_records
        ])
        
        return {
            'total_days': total_days,
            'working_days': working_days,
            'present_days': present_days,
            'absent_days': absent_days,
            'late_days': late_days,
            'attendance_rate': round((present_days / working_days) * 100, 2) if working_days > 0 else 0,
            'total_hours': round(total_hours, 2),
            'total_overtime': round(total_overtime, 2),
            'average_hours_per_day': round(total_hours / present_days, 2) if present_days > 0 else 0
        }


class BiometricIntegrationService:
    """Service for biometric device integration"""
    
    @staticmethod
    def register_biometric_device(device_id: str, device_name: str, 
                                device_type: str, ip_address: str = None) -> Dict:
        """
        Register a new biometric device
        
        Args:
            device_id: Unique device identifier
            device_name: Human readable device name
            device_type: Type of device (fingerprint, face, palm)
            ip_address: Device IP address if network-based
        
        Returns:
            Dict with registration status
        """
        # This would integrate with actual biometric devices
        # For now, we'll store device info in a simple way
        
        device_info = {
            'device_id': device_id,
            'device_name': device_name,
            'device_type': device_type,
            'ip_address': ip_address,
            'registered_at': timezone.now().isoformat(),
            'status': 'active'
        }
        
        # In a real implementation, this would be stored in a proper model
        # For now, we'll return success
        return {
            'success': True,
            'message': 'Biometric device registered successfully',
            'device_info': device_info
        }
    
    @staticmethod
    def process_biometric_data(device_id: str, employee_biometric_id: str, 
                             timestamp: datetime = None) -> Dict:
        """
        Process biometric attendance data from devices
        
        Args:
            device_id: ID of the device that captured the data
            employee_biometric_id: Employee's biometric ID
            timestamp: When the biometric was captured
        
        Returns:
            Dict with processing result
        """
        try:
            # Map biometric ID to employee
            # In real implementation, this would be stored in employee profile
            employee = Employee.objects.filter(
                employee_id=employee_biometric_id
            ).first()
            
            if not employee:
                return {'success': False, 'message': 'Employee not found'}
            
            # Determine if this is check-in or check-out
            today = timezone.now().date()
            existing_attendance = AttendanceRecord.objects.filter(
                employee=employee,
                date=today
            ).first()
            
            if existing_attendance and existing_attendance.check_in_time and not existing_attendance.check_out_time:
                attendance_type = 'check_out'
            else:
                attendance_type = 'check_in'
            
            # Mark attendance
            result = AttendanceService.mark_attendance(
                employee_id=employee.employee_id,
                attendance_type=attendance_type,
                timestamp=timestamp,
                method='biometric'
            )
            
            result['device_id'] = device_id
            result['employee_name'] = f"{employee.user.first_name} {employee.user.last_name}"
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing biometric data: {str(e)}")
            return {'success': False, 'message': 'Error processing biometric data'}


class AttendanceAnalyticsService:
    """Service for attendance analytics and reporting"""
    
    @staticmethod
    def get_department_attendance_stats(department: Department, 
                                      start_date: datetime.date, 
                                      end_date: datetime.date) -> Dict:
        """
        Get attendance statistics for a department
        
        Args:
            department: Department instance
            start_date: Start date for analysis
            end_date: End date for analysis
        
        Returns:
            Dict with department attendance statistics
        """
        employees = Employee.objects.filter(
            position__department=department,
            employment_status='active'
        )
        
        attendance_records = AttendanceRecord.objects.filter(
            employee__in=employees,
            date__gte=start_date,
            date__lte=end_date
        )
        
        total_employees = employees.count()
        total_days = (end_date - start_date).days + 1
        expected_attendance = total_employees * total_days
        
        present_records = attendance_records.filter(
            status__in=['present', 'late']
        ).count()
        
        late_records = attendance_records.filter(status='late').count()
        absent_records = expected_attendance - present_records
        
        return {
            'department_name': department.name,
            'total_employees': total_employees,
            'total_days': total_days,
            'expected_attendance': expected_attendance,
            'present_records': present_records,
            'absent_records': absent_records,
            'late_records': late_records,
            'attendance_rate': round((present_records / expected_attendance) * 100, 2) if expected_attendance > 0 else 0,
            'punctuality_rate': round(((present_records - late_records) / present_records) * 100, 2) if present_records > 0 else 0
        }
    
    @staticmethod
    def get_school_attendance_trends(school: School, months: int = 6) -> Dict:
        """
        Get attendance trends for the school over specified months
        
        Args:
            school: School instance
            months: Number of months to analyze
        
        Returns:
            Dict with trend data
        """
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=months * 30)
        
        # Get monthly data
        monthly_data = []
        current_date = start_date.replace(day=1)
        
        while current_date <= end_date:
            next_month = (current_date + timedelta(days=32)).replace(day=1)
            
            month_attendance = AttendanceRecord.objects.filter(
                employee__school=school,
                date__gte=current_date,
                date__lt=next_month
            )
            
            total_records = month_attendance.count()
            present_records = month_attendance.filter(
                status__in=['present', 'late']
            ).count()
            
            attendance_rate = (present_records / total_records * 100) if total_records > 0 else 0
            
            monthly_data.append({
                'month': current_date.strftime('%Y-%m'),
                'month_name': current_date.strftime('%B %Y'),
                'total_records': total_records,
                'present_records': present_records,
                'attendance_rate': round(attendance_rate, 2)
            })
            
            current_date = next_month
        
        return {
            'monthly_trends': monthly_data,
            'period': f"{start_date.strftime('%B %Y')} - {end_date.strftime('%B %Y')}"
        }
    
    @staticmethod
    def identify_attendance_patterns(employee: Employee, days: int = 30) -> Dict:
        """
        Identify attendance patterns for an employee
        
        Args:
            employee: Employee instance
            days: Number of days to analyze
        
        Returns:
            Dict with pattern analysis
        """
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)
        
        attendance_records = AttendanceRecord.objects.filter(
            employee=employee,
            date__gte=start_date,
            date__lte=end_date
        ).order_by('date')
        
        # Analyze patterns
        patterns = {
            'frequent_late_days': [],
            'absent_streaks': [],
            'perfect_weeks': [],
            'average_arrival_time': None,
            'average_departure_time': None
        }
        
        # Find frequent late days (by day of week)
        late_by_day = {}
        arrival_times = []
        departure_times = []
        
        for record in attendance_records:
            day_of_week = record.date.strftime('%A')
            
            if record.status == 'late':
                late_by_day[day_of_week] = late_by_day.get(day_of_week, 0) + 1
            
            if record.check_in_time:
                arrival_times.append(record.check_in_time)
            
            if record.check_out_time:
                departure_times.append(record.check_out_time)
        
        # Most frequent late days
        if late_by_day:
            patterns['frequent_late_days'] = sorted(
                late_by_day.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:3]
        
        # Calculate average times
        if arrival_times:
            avg_seconds = sum(
                t.hour * 3600 + t.minute * 60 + t.second 
                for t in arrival_times
            ) / len(arrival_times)
            
            hours = int(avg_seconds // 3600)
            minutes = int((avg_seconds % 3600) // 60)
            patterns['average_arrival_time'] = f"{hours:02d}:{minutes:02d}"
        
        if departure_times:
            avg_seconds = sum(
                t.hour * 3600 + t.minute * 60 + t.second 
                for t in departure_times
            ) / len(departure_times)
            
            hours = int(avg_seconds // 3600)
            minutes = int((avg_seconds % 3600) // 60)
            patterns['average_departure_time'] = f"{hours:02d}:{minutes:02d}"
        
        return patterns


class QRCodeAttendanceService:
    """Service for QR code-based attendance tracking"""
    
    @staticmethod
    def generate_attendance_qr_code(location: str, valid_duration: int = 30) -> Dict:
        """
        Generate QR code for attendance marking
        
        Args:
            location: Location identifier (e.g., "main_office", "branch_1")
            valid_duration: How long the QR code is valid (in minutes)
        
        Returns:
            Dict with QR code data
        """
        import uuid
        
        # Generate unique token
        token = str(uuid.uuid4())
        expires_at = timezone.now() + timedelta(minutes=valid_duration)
        
        qr_data = {
            'token': token,
            'location': location,
            'expires_at': expires_at.isoformat(),
            'type': 'attendance'
        }
        
        # In a real implementation, this would be stored temporarily
        # and the QR code image would be generated
        
        return {
            'success': True,
            'qr_token': token,
            'qr_data': json.dumps(qr_data),
            'expires_at': expires_at,
            'location': location
        }
    
    @staticmethod
    def process_qr_attendance(employee_id: str, qr_token: str) -> Dict:
        """
        Process attendance from QR code scan
        
        Args:
            employee_id: Employee ID
            qr_token: QR code token
        
        Returns:
            Dict with processing result
        """
        # In a real implementation, we would validate the token
        # against stored QR codes and check expiration
        
        # For now, we'll simulate successful processing
        result = AttendanceService.mark_attendance(
            employee_id=employee_id,
            attendance_type='check_in',  # or determine based on existing records
            method='qr_code'
        )
        
        result['qr_token'] = qr_token
        return result