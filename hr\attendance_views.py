"""
Enhanced Attendance Views
Provides comprehensive attendance tracking views including
biometric integration, QR code attendance, and analytics.
"""

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.views.generic import TemplateView, ListView, DetailView, CreateView, UpdateView, View
from django.contrib import messages
from django.urls import reverse_lazy
from django.db.models import Q, Count, Avg, Sum
from django.http import HttpResponse, JsonResponse
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.core.paginator import Paginator
from datetime import datetime, timedelta, date
import json

from .models import Employee, AttendanceRecord, Department
from .forms import AttendanceRecordForm, QuickAttendanceForm
from .attendance_services import (
    AttendanceService, BiometricIntegrationService, 
    AttendanceAnalyticsService, QRCodeAttendanceService
)


class BiometricAttendanceView(LoginRequiredMixin, TemplateView):
    """View for biometric attendance management"""
    template_name = 'hr/biometric_attendance.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get registered biometric devices (mock data for now)
        context['biometric_devices'] = [
            {
                'device_id': 'BIO001',
                'device_name': 'Main Entrance Scanner',
                'device_type': 'fingerprint',
                'status': 'active',
                'last_sync': timezone.now() - timedelta(minutes=5)
            },
            {
                'device_id': 'BIO002', 
                'device_name': 'Office Floor Scanner',
                'device_type': 'face',
                'status': 'active',
                'last_sync': timezone.now() - timedelta(minutes=2)
            }
        ]
        
        # Recent biometric attendance records
        context['recent_biometric_records'] = AttendanceRecord.objects.filter(
            is_manual=False,
            created_at__gte=timezone.now() - timedelta(hours=24)
        ).select_related('employee', 'employee__user').order_by('-created_at')[:10]
        
        return context


class QRCodeAttendanceView(LoginRequiredMixin, TemplateView):
    """View for QR code attendance management"""
    template_name = 'hr/qr_attendance.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Generate QR code for current location
        qr_result = QRCodeAttendanceService.generate_attendance_qr_code(
            location='main_office',
            valid_duration=30
        )
        
        context['qr_code_data'] = qr_result
        context['qr_code_json'] = qr_result['qr_data']
        
        return context
    
    def post(self, request, *args, **kwargs):
        """Process QR code attendance submission"""
        employee_id = request.POST.get('employee_id')
        qr_token = request.POST.get('qr_token')
        
        if not employee_id or not qr_token:
            messages.error(request, _('Missing required information'))
            return redirect('hr:qr_attendance')
        
        result = QRCodeAttendanceService.process_qr_attendance(
            employee_id=employee_id,
            qr_token=qr_token
        )
        
        if result['success']:
            messages.success(request, result['message'])
        else:
            messages.error(request, result['message'])
        
        return redirect('hr:qr_attendance')


class AttendanceAnalyticsView(LoginRequiredMixin, TemplateView):
    """View for attendance analytics and reporting"""
    template_name = 'hr/attendance_analytics.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Date range for analysis
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
        
        # Overall attendance statistics
        total_employees = Employee.objects.filter(employment_status='active').count()
        total_records = AttendanceRecord.objects.filter(
            date__gte=start_date,
            date__lte=end_date
        ).count()
        
        present_records = AttendanceRecord.objects.filter(
            date__gte=start_date,
            date__lte=end_date,
            status__in=['present', 'late']
        ).count()
        
        context['overall_stats'] = {
            'total_employees': total_employees,
            'total_records': total_records,
            'present_records': present_records,
            'absent_records': total_records - present_records if total_records > 0 else 0,
            'attendance_rate': round((present_records / total_records) * 100, 2) if total_records > 0 else 0
        }
        
        # Department-wise statistics
        departments = Department.objects.filter(is_active=True)
        dept_stats = []
        
        for dept in departments:
            stats = AttendanceAnalyticsService.get_department_attendance_stats(
                department=dept,
                start_date=start_date,
                end_date=end_date
            )
            dept_stats.append(stats)
        
        context['department_stats'] = dept_stats
        
        # Attendance trends (last 6 months)
        if hasattr(self.request.user, 'employee_profile'):
            school = self.request.user.employee_profile.school
            trends = AttendanceAnalyticsService.get_school_attendance_trends(
                school=school,
                months=6
            )
            context['attendance_trends'] = trends
        
        # Top performers (highest attendance rate)
        employee_stats = []
        for employee in Employee.objects.filter(employment_status='active')[:10]:
            summary = AttendanceService.get_attendance_summary(
                employee=employee,
                start_date=start_date,
                end_date=end_date
            )
            employee_stats.append({
                'employee': employee,
                'stats': summary
            })
        
        # Sort by attendance rate
        employee_stats.sort(key=lambda x: x['stats']['attendance_rate'], reverse=True)
        context['top_performers'] = employee_stats[:5]
        
        # Employees needing attention (low attendance)
        context['attention_needed'] = [
            stat for stat in employee_stats 
            if stat['stats']['attendance_rate'] < 80
        ][:5]
        
        return context


class OvertimeReportView(LoginRequiredMixin, TemplateView):
    """View for overtime reporting"""
    template_name = 'hr/overtime_report.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get date range from request
        start_date = self.request.GET.get('start_date')
        end_date = self.request.GET.get('end_date')
        
        if not start_date or not end_date:
            end_date = timezone.now().date()
            start_date = end_date - timedelta(days=30)
        else:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        
        context['start_date'] = start_date
        context['end_date'] = end_date
        
        # Calculate overtime for all employees
        overtime_data = []
        employees = Employee.objects.filter(employment_status='active')
        
        for employee in employees:
            attendance_records = AttendanceRecord.objects.filter(
                employee=employee,
                date__gte=start_date,
                date__lte=end_date,
                check_in_time__isnull=False,
                check_out_time__isnull=False
            )
            
            total_overtime = 0
            overtime_days = 0
            
            for record in attendance_records:
                daily_overtime = AttendanceService.calculate_overtime(employee, record.date)
                if daily_overtime > 0:
                    total_overtime += daily_overtime
                    overtime_days += 1
            
            if total_overtime > 0:
                overtime_data.append({
                    'employee': employee,
                    'total_overtime': round(total_overtime, 2),
                    'overtime_days': overtime_days,
                    'average_daily_overtime': round(total_overtime / overtime_days, 2) if overtime_days > 0 else 0
                })
        
        # Sort by total overtime
        overtime_data.sort(key=lambda x: x['total_overtime'], reverse=True)
        context['overtime_data'] = overtime_data
        
        # Summary statistics
        context['total_overtime_hours'] = sum(data['total_overtime'] for data in overtime_data)
        context['employees_with_overtime'] = len(overtime_data)
        context['average_overtime_per_employee'] = (
            context['total_overtime_hours'] / len(overtime_data) 
            if overtime_data else 0
        )
        
        return context


class AttendancePatternAnalysisView(LoginRequiredMixin, DetailView):
    """View for analyzing individual employee attendance patterns"""
    model = Employee
    template_name = 'hr/attendance_patterns.html'
    context_object_name = 'employee'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        employee = self.get_object()
        
        # Get analysis period from request
        days = int(self.request.GET.get('days', 30))
        
        # Get attendance patterns
        patterns = AttendanceAnalyticsService.identify_attendance_patterns(
            employee=employee,
            days=days
        )
        context['patterns'] = patterns
        context['analysis_days'] = days
        
        # Get detailed attendance summary
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)
        
        summary = AttendanceService.get_attendance_summary(
            employee=employee,
            start_date=start_date,
            end_date=end_date
        )
        context['attendance_summary'] = summary
        
        # Get recent attendance records for visualization
        recent_records = AttendanceRecord.objects.filter(
            employee=employee,
            date__gte=start_date,
            date__lte=end_date
        ).order_by('date')
        
        context['recent_records'] = recent_records
        
        # Prepare chart data
        chart_data = []
        for record in recent_records:
            chart_data.append({
                'date': record.date.strftime('%Y-%m-%d'),
                'status': record.status,
                'hours': record.total_hours,
                'check_in': record.check_in_time.strftime('%H:%M') if record.check_in_time else None,
                'check_out': record.check_out_time.strftime('%H:%M') if record.check_out_time else None
            })
        
        context['chart_data'] = json.dumps(chart_data)
        
        return context


class BulkAttendanceView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    """View for bulk attendance marking"""
    template_name = 'hr/bulk_attendance.html'
    permission_required = 'hr.add_attendancerecord'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get all active employees
        context['employees'] = Employee.objects.filter(
            employment_status='active'
        ).select_related('user', 'position__department').order_by(
            'user__first_name', 'user__last_name'
        )
        
        # Get departments for filtering
        context['departments'] = Department.objects.filter(is_active=True).order_by('name')
        
        context['form'] = QuickAttendanceForm()
        
        return context
    
    def post(self, request, *args, **kwargs):
        """Process bulk attendance submission"""
        selected_employees = request.POST.getlist('employees')
        attendance_date = request.POST.get('date')
        status = request.POST.get('status', 'present')
        
        if not selected_employees or not attendance_date:
            messages.error(request, _('Please select employees and date'))
            return redirect('hr:bulk_attendance')
        
        try:
            attendance_date = datetime.strptime(attendance_date, '%Y-%m-%d').date()
        except ValueError:
            messages.error(request, _('Invalid date format'))
            return redirect('hr:bulk_attendance')
        
        # Process bulk attendance
        result = AttendanceService.bulk_mark_attendance(
            employee_ids=selected_employees,
            date=attendance_date,
            status=status,
            created_by=request.user
        )
        
        if result['success_count'] > 0:
            messages.success(
                request, 
                _('Successfully marked attendance for {} employees').format(result['success_count'])
            )
        
        if result['errors']:
            for error in result['errors']:
                messages.warning(request, error)
        
        return redirect('hr:bulk_attendance')


class AttendanceAPIView(LoginRequiredMixin, View):
    """API view for attendance operations"""
    
    def post(self, request, *args, **kwargs):
        """Handle attendance marking via API"""
        try:
            data = json.loads(request.body)
            
            employee_id = data.get('employee_id')
            attendance_type = data.get('type', 'check_in')
            method = data.get('method', 'web')
            
            if not employee_id:
                return JsonResponse({
                    'success': False, 
                    'message': 'Employee ID is required'
                }, status=400)
            
            result = AttendanceService.mark_attendance(
                employee_id=employee_id,
                attendance_type=attendance_type,
                method=method
            )
            
            return JsonResponse(result)
            
        except json.JSONDecodeError:
            return JsonResponse({
                'success': False, 
                'message': 'Invalid JSON data'
            }, status=400)
        except Exception as e:
            return JsonResponse({
                'success': False, 
                'message': str(e)
            }, status=500)
    
    def get(self, request, *args, **kwargs):
        """Get attendance data via API"""
        employee_id = request.GET.get('employee_id')
        date_str = request.GET.get('date')
        
        if not employee_id:
            return JsonResponse({
                'success': False, 
                'message': 'Employee ID is required'
            }, status=400)
        
        try:
            employee = Employee.objects.get(employee_id=employee_id)
            
            if date_str:
                date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
            else:
                date_obj = timezone.now().date()
            
            try:
                attendance = AttendanceRecord.objects.get(
                    employee=employee,
                    date=date_obj
                )
                
                return JsonResponse({
                    'success': True,
                    'data': {
                        'employee_id': employee.employee_id,
                        'employee_name': f"{employee.user.first_name} {employee.user.last_name}",
                        'date': attendance.date.strftime('%Y-%m-%d'),
                        'check_in_time': attendance.check_in_time.strftime('%H:%M:%S') if attendance.check_in_time else None,
                        'check_out_time': attendance.check_out_time.strftime('%H:%M:%S') if attendance.check_out_time else None,
                        'status': attendance.status,
                        'total_hours': attendance.total_hours,
                        'is_manual': attendance.is_manual
                    }
                })
                
            except AttendanceRecord.DoesNotExist:
                return JsonResponse({
                    'success': True,
                    'data': {
                        'employee_id': employee.employee_id,
                        'employee_name': f"{employee.user.first_name} {employee.user.last_name}",
                        'date': date_obj.strftime('%Y-%m-%d'),
                        'check_in_time': None,
                        'check_out_time': None,
                        'status': 'absent',
                        'total_hours': 0,
                        'is_manual': False
                    }
                })
                
        except Employee.DoesNotExist:
            return JsonResponse({
                'success': False, 
                'message': 'Employee not found'
            }, status=404)
        except ValueError:
            return JsonResponse({
                'success': False, 
                'message': 'Invalid date format'
            }, status=400)


class BiometricDataProcessingView(LoginRequiredMixin, View):
    """View for processing biometric device data"""
    
    def post(self, request, *args, **kwargs):
        """Process biometric attendance data from devices"""
        try:
            data = json.loads(request.body)
            
            device_id = data.get('device_id')
            employee_biometric_id = data.get('employee_biometric_id')
            timestamp_str = data.get('timestamp')
            
            if not all([device_id, employee_biometric_id]):
                return JsonResponse({
                    'success': False,
                    'message': 'Missing required fields'
                }, status=400)
            
            # Parse timestamp if provided
            timestamp = None
            if timestamp_str:
                try:
                    timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                except ValueError:
                    return JsonResponse({
                        'success': False,
                        'message': 'Invalid timestamp format'
                    }, status=400)
            
            # Process biometric data
            result = BiometricIntegrationService.process_biometric_data(
                device_id=device_id,
                employee_biometric_id=employee_biometric_id,
                timestamp=timestamp
            )
            
            return JsonResponse(result)
            
        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'message': 'Invalid JSON data'
            }, status=400)
        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': str(e)
            }, status=500)