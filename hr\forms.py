from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Fieldset, Row, Column, Submit, HTML
from crispy_forms.bootstrap import FormActions
from .models import (
    Department, Position, Employee, AttendanceRecord, LeaveType, LeaveRequest,
    PayrollPeriod, Payroll, PerformanceEvaluation, EmployeeDocument,
    SalaryStructure, AllowanceType, DeductionType, EmployeeAllowance, EmployeeDeduction,
    PerformanceReviewCycle, PerformanceGoal, PerformanceImprovementPlan,
    PerformanceImprovementAction, PerformanceMetric, EmployeePerformanceMetric
)
from accounts.models import User


class DepartmentForm(forms.ModelForm):
    """
    Department creation and update form
    """
    class Meta:
        model = Department
        fields = ['name', 'name_ar', 'code', 'description', 'head']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class PositionForm(forms.ModelForm):
    """
    Position creation and update form
    """
    class Meta:
        model = Position
        fields = ['title', 'title_ar', 'department', 'description']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class EmployeeForm(forms.ModelForm):
    """
    Employee creation and update form
    """
    class Meta:
        model = Employee
        fields = [
            'user', 'employee_id', 'position', 'hire_date', 'termination_date',
            'employment_status', 'salary', 'emergency_contact_name',
            'emergency_contact_phone', 'emergency_contact_relationship'
        ]
        widgets = {
            'hire_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'termination_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'salary': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            Fieldset(
                _('Basic Information'),
                Row(
                    Column('user', css_class='form-group col-md-6 mb-3'),
                    Column('employee_id', css_class='form-group col-md-6 mb-3'),
                ),
                Row(
                    Column('position', css_class='form-group col-md-6 mb-3'),
                    Column('employment_status', css_class='form-group col-md-6 mb-3'),
                ),
            ),
            Fieldset(
                _('Employment Details'),
                Row(
                    Column('hire_date', css_class='form-group col-md-6 mb-3'),
                    Column('termination_date', css_class='form-group col-md-6 mb-3'),
                ),
                'salary',
            ),
            Fieldset(
                _('Emergency Contact'),
                'emergency_contact_name',
                Row(
                    Column('emergency_contact_phone', css_class='form-group col-md-6 mb-3'),
                    Column('emergency_contact_relationship', css_class='form-group col-md-6 mb-3'),
                ),
            ),
            FormActions(
                Submit('submit', _('Save Employee'), css_class='btn btn-primary'),
            )
        )


class AttendanceRecordForm(forms.ModelForm):
    """
    Attendance record form
    """
    class Meta:
        model = AttendanceRecord
        fields = [
            'employee', 'date', 'check_in_time', 'check_out_time',
            'break_start_time', 'break_end_time', 'status', 'notes'
        ]
        widgets = {
            'date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'check_in_time': forms.TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
            'check_out_time': forms.TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
            'break_start_time': forms.TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
            'break_end_time': forms.TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class LeaveRequestForm(forms.ModelForm):
    """
    Leave request form
    """
    class Meta:
        model = LeaveRequest
        fields = ['employee', 'leave_type', 'start_date', 'end_date', 'reason', 'attachment']
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'reason': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'attachment': forms.FileInput(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_enctype = 'multipart/form-data'

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date:
            if start_date > end_date:
                raise ValidationError(_('Start date cannot be after end date.'))

        return cleaned_data


class PayrollForm(forms.ModelForm):
    """
    Payroll form
    """
    class Meta:
        model = Payroll
        fields = [
            'employee', 'period', 'basic_salary', 'total_allowances', 'overtime_hours',
            'overtime_rate', 'total_deductions', 'tax_deduction', 'insurance_deduction', 'notes'
        ]
        widgets = {
            'basic_salary': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'total_allowances': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'overtime_hours': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'overtime_rate': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'total_deductions': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'tax_deduction': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'insurance_deduction': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class SalaryStructureForm(forms.ModelForm):
    """
    Salary structure form
    """
    class Meta:
        model = SalaryStructure
        fields = ['name', 'name_ar', 'position', 'basic_salary', 'effective_date', 'description']
        widgets = {
            'basic_salary': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'effective_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class AllowanceTypeForm(forms.ModelForm):
    """
    Allowance type form
    """
    class Meta:
        model = AllowanceType
        fields = ['name', 'name_ar', 'code', 'is_taxable', 'is_fixed', 'default_amount', 'description']
        widgets = {
            'default_amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class DeductionTypeForm(forms.ModelForm):
    """
    Deduction type form
    """
    class Meta:
        model = DeductionType
        fields = ['name', 'name_ar', 'code', 'is_mandatory', 'is_percentage', 'default_amount', 'max_amount', 'description']
        widgets = {
            'default_amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'max_amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class EmployeeAllowanceForm(forms.ModelForm):
    """
    Employee allowance form
    """
    class Meta:
        model = EmployeeAllowance
        fields = ['employee', 'allowance_type', 'amount', 'effective_date', 'end_date']
        widgets = {
            'amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'effective_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'

    def clean(self):
        cleaned_data = super().clean()
        effective_date = cleaned_data.get('effective_date')
        end_date = cleaned_data.get('end_date')

        if effective_date and end_date:
            if effective_date >= end_date:
                raise ValidationError(_('Effective date must be before end date.'))

        return cleaned_data


class EmployeeDeductionForm(forms.ModelForm):
    """
    Employee deduction form
    """
    class Meta:
        model = EmployeeDeduction
        fields = ['employee', 'deduction_type', 'amount', 'effective_date', 'end_date']
        widgets = {
            'amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'effective_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'

    def clean(self):
        cleaned_data = super().clean()
        effective_date = cleaned_data.get('effective_date')
        end_date = cleaned_data.get('end_date')

        if effective_date and end_date:
            if effective_date >= end_date:
                raise ValidationError(_('Effective date must be before end date.'))

        return cleaned_data


class PerformanceEvaluationForm(forms.ModelForm):
    """
    Performance evaluation form
    """
    class Meta:
        model = PerformanceEvaluation
        fields = [
            'employee', 'evaluation_period_start', 'evaluation_period_end',
            'quality_of_work', 'productivity', 'communication', 'teamwork',
            'punctuality', 'initiative', 'overall_rating', 'strengths',
            'areas_for_improvement', 'goals_for_next_period', 'employee_comments'
        ]
        widgets = {
            'evaluation_period_start': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'evaluation_period_end': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'strengths': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'areas_for_improvement': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'goals_for_next_period': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'employee_comments': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class EmployeeDocumentForm(forms.ModelForm):
    """
    Employee document form
    """
    class Meta:
        model = EmployeeDocument
        fields = ['employee', 'document_type', 'title', 'file', 'description', 'is_confidential', 'expiry_date']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'file': forms.FileInput(attrs={'class': 'form-control'}),
            'expiry_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_enctype = 'multipart/form-data'


class LeaveTypeForm(forms.ModelForm):
    """
    Leave type form
    """
    class Meta:
        model = LeaveType
        fields = ['name', 'name_ar', 'code', 'max_days_per_year', 'is_paid', 'requires_approval', 'description']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class PayrollPeriodForm(forms.ModelForm):
    """
    Payroll period form
    """
    class Meta:
        model = PayrollPeriod
        fields = ['name', 'start_date', 'end_date']
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date:
            if start_date >= end_date:
                raise ValidationError(_('Start date must be before end date.'))

        return cleaned_data


# Quick attendance form for bulk entry
class QuickAttendanceForm(forms.Form):
    """
    Quick attendance form for marking attendance
    """
    date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        label=_('Date')
    )
    
    employees = forms.ModelMultipleChoiceField(
        queryset=Employee.objects.filter(employment_status='active'),
        widget=forms.CheckboxSelectMultiple,
        label=_('Employees')
    )
    
    status = forms.ChoiceField(
        choices=AttendanceRecord._meta.get_field('status').choices,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Status')
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'


class PerformanceEvaluationForm(forms.ModelForm):
    """
    Performance evaluation form
    """
    class Meta:
        model = PerformanceEvaluation
        fields = [
            'employee', 'evaluation_period_start', 'evaluation_period_end',
            'quality_of_work', 'productivity', 'communication', 'teamwork',
            'punctuality', 'initiative', 'strengths', 'areas_for_improvement',
            'goals_for_next_period', 'employee_comments'
        ]
        widgets = {
            'evaluation_period_start': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'evaluation_period_end': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'quality_of_work': forms.Select(attrs={'class': 'form-control'}),
            'productivity': forms.Select(attrs={'class': 'form-control'}),
            'communication': forms.Select(attrs={'class': 'form-control'}),
            'teamwork': forms.Select(attrs={'class': 'form-control'}),
            'punctuality': forms.Select(attrs={'class': 'form-control'}),
            'initiative': forms.Select(attrs={'class': 'form-control'}),
            'strengths': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'areas_for_improvement': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'goals_for_next_period': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'employee_comments': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            Fieldset(
                _('Evaluation Details'),
                Row(
                    Column('employee', css_class='form-group col-md-12 mb-0'),
                ),
                Row(
                    Column('evaluation_period_start', css_class='form-group col-md-6 mb-0'),
                    Column('evaluation_period_end', css_class='form-group col-md-6 mb-0'),
                ),
            ),
            Fieldset(
                _('Performance Ratings'),
                Row(
                    Column('quality_of_work', css_class='form-group col-md-6 mb-0'),
                    Column('productivity', css_class='form-group col-md-6 mb-0'),
                ),
                Row(
                    Column('communication', css_class='form-group col-md-6 mb-0'),
                    Column('teamwork', css_class='form-group col-md-6 mb-0'),
                ),
                Row(
                    Column('punctuality', css_class='form-group col-md-6 mb-0'),
                    Column('initiative', css_class='form-group col-md-6 mb-0'),
                ),
            ),
            Fieldset(
                _('Evaluation Comments'),
                'strengths',
                'areas_for_improvement',
                'goals_for_next_period',
                'employee_comments',
            ),
            FormActions(
                Submit('submit', _('Save Evaluation'), css_class='btn btn-primary'),
            )
        )

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('evaluation_period_start')
        end_date = cleaned_data.get('evaluation_period_end')

        if start_date and end_date:
            if start_date >= end_date:
                raise ValidationError(_('Start date must be before end date.'))

        return cleaned_data


class PerformanceReviewCycleForm(forms.ModelForm):
    """
    Performance review cycle form
    """
    class Meta:
        model = PerformanceReviewCycle
        fields = [
            'name', 'name_ar', 'start_date', 'end_date', 'description',
            'auto_create_evaluations', 'evaluation_deadline'
        ]
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'evaluation_deadline': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            Row(
                Column('name', css_class='form-group col-md-6 mb-0'),
                Column('name_ar', css_class='form-group col-md-6 mb-0'),
            ),
            Row(
                Column('start_date', css_class='form-group col-md-4 mb-0'),
                Column('end_date', css_class='form-group col-md-4 mb-0'),
                Column('evaluation_deadline', css_class='form-group col-md-4 mb-0'),
            ),
            'description',
            'auto_create_evaluations',
            FormActions(
                Submit('submit', _('Save Cycle'), css_class='btn btn-primary'),
            )
        )


class PerformanceGoalForm(forms.ModelForm):
    """
    Performance goal form
    """
    class Meta:
        model = PerformanceGoal
        fields = [
            'employee', 'title', 'description', 'target_date', 'priority',
            'progress_percentage', 'notes'
        ]
        widgets = {
            'target_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 2, 'class': 'form-control'}),
            'progress_percentage': forms.NumberInput(attrs={'min': 0, 'max': 100, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            'employee',
            'title',
            'description',
            Row(
                Column('target_date', css_class='form-group col-md-4 mb-0'),
                Column('priority', css_class='form-group col-md-4 mb-0'),
                Column('progress_percentage', css_class='form-group col-md-4 mb-0'),
            ),
            'notes',
            FormActions(
                Submit('submit', _('Save Goal'), css_class='btn btn-primary'),
            )
        )


class PerformanceImprovementPlanForm(forms.ModelForm):
    """
    Performance improvement plan form
    """
    class Meta:
        model = PerformanceImprovementPlan
        fields = [
            'employee', 'evaluation', 'title', 'description', 'start_date',
            'target_completion_date', 'success_criteria', 'resources_needed',
            'supervisor'
        ]
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'target_completion_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'description': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'success_criteria': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'resources_needed': forms.Textarea(attrs={'rows': 2, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            Row(
                Column('employee', css_class='form-group col-md-6 mb-0'),
                Column('evaluation', css_class='form-group col-md-6 mb-0'),
            ),
            'title',
            'description',
            Row(
                Column('start_date', css_class='form-group col-md-4 mb-0'),
                Column('target_completion_date', css_class='form-group col-md-4 mb-0'),
                Column('supervisor', css_class='form-group col-md-4 mb-0'),
            ),
            'success_criteria',
            'resources_needed',
            FormActions(
                Submit('submit', _('Save Plan'), css_class='btn btn-primary'),
            )
        )

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        target_date = cleaned_data.get('target_completion_date')

        if start_date and target_date:
            if start_date >= target_date:
                raise ValidationError(_('Start date must be before target completion date.'))

        return cleaned_data


class PerformanceImprovementActionForm(forms.ModelForm):
    """
    Performance improvement action form
    """
    class Meta:
        model = PerformanceImprovementAction
        fields = [
            'improvement_plan', 'title', 'description', 'due_date',
            'assigned_to', 'notes'
        ]
        widgets = {
            'due_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 2, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            'improvement_plan',
            'title',
            'description',
            Row(
                Column('due_date', css_class='form-group col-md-6 mb-0'),
                Column('assigned_to', css_class='form-group col-md-6 mb-0'),
            ),
            'notes',
            FormActions(
                Submit('submit', _('Save Action'), css_class='btn btn-primary'),
            )
        )


class PerformanceMetricForm(forms.ModelForm):
    """
    Performance metric form
    """
    class Meta:
        model = PerformanceMetric
        fields = [
            'name', 'name_ar', 'description', 'metric_type', 'department',
            'position', 'target_value', 'unit_of_measure'
        ]
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'target_value': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            Row(
                Column('name', css_class='form-group col-md-6 mb-0'),
                Column('name_ar', css_class='form-group col-md-6 mb-0'),
            ),
            'description',
            Row(
                Column('metric_type', css_class='form-group col-md-4 mb-0'),
                Column('department', css_class='form-group col-md-4 mb-0'),
                Column('position', css_class='form-group col-md-4 mb-0'),
            ),
            Row(
                Column('target_value', css_class='form-group col-md-6 mb-0'),
                Column('unit_of_measure', css_class='form-group col-md-6 mb-0'),
            ),
            FormActions(
                Submit('submit', _('Save Metric'), css_class='btn btn-primary'),
            )
        )


class EmployeePerformanceMetricForm(forms.ModelForm):
    """
    Employee performance metric tracking form
    """
    class Meta:
        model = EmployeePerformanceMetric
        fields = [
            'employee', 'metric', 'evaluation', 'actual_value',
            'measurement_date', 'notes'
        ]
        widgets = {
            'measurement_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'actual_value': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 2, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            Row(
                Column('employee', css_class='form-group col-md-6 mb-0'),
                Column('metric', css_class='form-group col-md-6 mb-0'),
            ),
            Row(
                Column('evaluation', css_class='form-group col-md-4 mb-0'),
                Column('actual_value', css_class='form-group col-md-4 mb-0'),
                Column('measurement_date', css_class='form-group col-md-4 mb-0'),
            ),
            'notes',
            FormActions(
                Submit('submit', _('Save Metric'), css_class='btn btn-primary'),
            )
        )


