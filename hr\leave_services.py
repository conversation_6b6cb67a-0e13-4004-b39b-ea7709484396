"""
Leave management services
"""
from decimal import Decimal
from datetime import datetime, date, timedelta
from django.db import transaction, models
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.db.models import Q, Sum, Count
from .models import (
    Employee, LeaveType, LeaveRequest, AttendanceRecord
)


class LeaveBalanceService:
    """Service for managing employee leave balances"""
    
    @staticmethod
    def get_employee_leave_balance(employee: Employee, leave_type: LeaveType, year=None):
        """Get employee's leave balance for a specific leave type"""
        if year is None:
            year = timezone.now().year
        
        # Get total allocated days for the year
        allocated_days = leave_type.max_days_per_year
        
        # Get used days in the year
        used_requests = LeaveRequest.objects.filter(
            employee=employee,
            leave_type=leave_type,
            status='approved',
            start_date__year=year
        )
        used_days = sum(request.duration_days for request in used_requests)
        
        # Get pending requests
        pending_requests = LeaveRequest.objects.filter(
            employee=employee,
            leave_type=leave_type,
            status='pending',
            start_date__year=year
        )
        pending_days = sum(request.duration_days for request in pending_requests)
        
        remaining_days = allocated_days - used_days
        available_days = remaining_days - pending_days
        
        return {
            'leave_type': leave_type,
            'allocated_days': allocated_days,
            'used_days': used_days,
            'pending_days': pending_days,
            'remaining_days': remaining_days,
            'available_days': available_days,
            'year': year
        }
    
    @staticmethod
    def get_employee_all_leave_balances(employee: Employee, year=None):
        """Get all leave balances for an employee"""
        if year is None:
            year = timezone.now().year
        
        leave_types = LeaveType.objects.filter(school=employee.school)
        balances = []
        
        for leave_type in leave_types:
            balance = LeaveBalanceService.get_employee_leave_balance(
                employee, leave_type, year
            )
            balances.append(balance)
        
        return balances
    
    @staticmethod
    def check_leave_eligibility(employee: Employee, leave_type: LeaveType, 
                              start_date: date, end_date: date):
        """Check if employee is eligible for leave"""
        duration_days = (end_date - start_date).days + 1
        
        # Check leave balance
        balance = LeaveBalanceService.get_employee_leave_balance(
            employee, leave_type, start_date.year
        )
        
        if balance['available_days'] < duration_days:
            return {
                'eligible': False,
                'reason': f'Insufficient leave balance. Available: {balance["available_days"]} days, Requested: {duration_days} days'
            }
        
        # Check for overlapping requests
        overlapping_requests = LeaveRequest.objects.filter(
            employee=employee,
            status__in=['pending', 'approved'],
            start_date__lte=end_date,
            end_date__gte=start_date
        )
        
        if overlapping_requests.exists():
            return {
                'eligible': False,
                'reason': 'Overlapping leave request exists'
            }
        
        # Check minimum notice period (if applicable)
        notice_days = getattr(leave_type, 'minimum_notice_days', 0)
        if notice_days > 0:
            notice_date = timezone.now().date() + timedelta(days=notice_days)
            if start_date < notice_date:
                return {
                    'eligible': False,
                    'reason': f'Minimum {notice_days} days notice required'
                }
        
        return {
            'eligible': True,
            'reason': 'Eligible for leave'
        }


class LeaveRequestService:
    """Service for managing leave requests"""
    
    @staticmethod
    def create_leave_request(employee: Employee, leave_type: LeaveType,
                           start_date: date, end_date: date, reason: str,
                           attachment=None, user=None):
        """Create a new leave request"""
        try:
            with transaction.atomic():
                # Check eligibility
                eligibility = LeaveBalanceService.check_leave_eligibility(
                    employee, leave_type, start_date, end_date
                )
                
                if not eligibility['eligible']:
                    raise ValidationError(eligibility['reason'])
                
                # Create leave request
                leave_request = LeaveRequest.objects.create(
                    school=employee.school,
                    employee=employee,
                    leave_type=leave_type,
                    start_date=start_date,
                    end_date=end_date,
                    reason=reason,
                    attachment=attachment,
                    status='pending',
                    created_by=user
                )
                
                return leave_request
                
        except Exception as e:
            raise ValidationError(f"Error creating leave request: {str(e)}")
    
    @staticmethod
    def approve_leave_request(leave_request: LeaveRequest, approved_by, 
                            approval_notes=None):
        """Approve a leave request"""
        try:
            with transaction.atomic():
                if leave_request.status != 'pending':
                    raise ValidationError("Only pending requests can be approved")
                
                # Re-check eligibility
                eligibility = LeaveBalanceService.check_leave_eligibility(
                    leave_request.employee,
                    leave_request.leave_type,
                    leave_request.start_date,
                    leave_request.end_date
                )
                
                if not eligibility['eligible']:
                    raise ValidationError(f"Cannot approve: {eligibility['reason']}")
                
                # Update request status
                leave_request.status = 'approved'
                leave_request.approved_by = approved_by
                leave_request.approved_at = timezone.now()
                if approval_notes:
                    leave_request.rejection_reason = approval_notes  # Reuse field for notes
                leave_request.save()
                
                # Create attendance records for leave days
                LeaveRequestService._create_leave_attendance_records(leave_request)
                
                return leave_request
                
        except Exception as e:
            raise ValidationError(f"Error approving leave request: {str(e)}")
    
    @staticmethod
    def reject_leave_request(leave_request: LeaveRequest, rejected_by,
                           rejection_reason: str):
        """Reject a leave request"""
        try:
            with transaction.atomic():
                if leave_request.status != 'pending':
                    raise ValidationError("Only pending requests can be rejected")
                
                leave_request.status = 'rejected'
                leave_request.approved_by = rejected_by
                leave_request.approved_at = timezone.now()
                leave_request.rejection_reason = rejection_reason
                leave_request.save()
                
                return leave_request
                
        except Exception as e:
            raise ValidationError(f"Error rejecting leave request: {str(e)}")
    
    @staticmethod
    def cancel_leave_request(leave_request: LeaveRequest, cancelled_by,
                           cancellation_reason=None):
        """Cancel a leave request"""
        try:
            with transaction.atomic():
                if leave_request.status not in ['pending', 'approved']:
                    raise ValidationError("Only pending or approved requests can be cancelled")
                
                # If approved, remove attendance records
                if leave_request.status == 'approved':
                    AttendanceRecord.objects.filter(
                        employee=leave_request.employee,
                        date__range=[leave_request.start_date, leave_request.end_date],
                        status__in=['sick_leave', 'vacation', 'permission']
                    ).delete()
                
                leave_request.status = 'cancelled'
                leave_request.approved_by = cancelled_by
                leave_request.approved_at = timezone.now()
                if cancellation_reason:
                    leave_request.rejection_reason = cancellation_reason
                leave_request.save()
                
                return leave_request
                
        except Exception as e:
            raise ValidationError(f"Error cancelling leave request: {str(e)}")
    
    @staticmethod
    def _create_leave_attendance_records(leave_request: LeaveRequest):
        """Create attendance records for approved leave"""
        current_date = leave_request.start_date
        
        # Map leave types to attendance status
        leave_status_mapping = {
            'sick': 'sick_leave',
            'vacation': 'vacation',
            'personal': 'permission',
            'emergency': 'permission',
            'maternity': 'vacation',
            'paternity': 'vacation'
        }
        
        # Default to vacation if not mapped
        attendance_status = leave_status_mapping.get(
            leave_request.leave_type.code.lower(), 'vacation'
        )
        
        while current_date <= leave_request.end_date:
            # Only create records for working days (Monday to Friday)
            if current_date.weekday() < 5:
                AttendanceRecord.objects.get_or_create(
                    school=leave_request.school,
                    employee=leave_request.employee,
                    date=current_date,
                    defaults={
                        'status': attendance_status,
                        'notes': f'Leave: {leave_request.leave_type.name}',
                        'is_manual': True,
                        'created_by': leave_request.approved_by
                    }
                )
            current_date += timedelta(days=1)
    
    @staticmethod
    def get_employee_leave_requests(employee: Employee, year=None, status=None):
        """Get leave requests for an employee"""
        requests = LeaveRequest.objects.filter(employee=employee)
        
        if year:
            requests = requests.filter(start_date__year=year)
        
        if status:
            requests = requests.filter(status=status)
        
        return requests.order_by('-created_at')
    
    @staticmethod
    def get_pending_leave_requests(school, department=None):
        """Get pending leave requests for approval"""
        requests = LeaveRequest.objects.filter(
            school=school,
            status='pending'
        ).select_related(
            'employee__user',
            'employee__position__department',
            'leave_type'
        )
        
        if department:
            requests = requests.filter(employee__position__department=department)
        
        return requests.order_by('created_at')


class LeaveCalendarService:
    """Service for leave calendar management"""
    
    @staticmethod
    def get_leave_calendar(school, start_date: date, end_date: date, 
                          department=None, employee=None):
        """Get leave calendar for a date range"""
        requests = LeaveRequest.objects.filter(
            school=school,
            status='approved',
            start_date__lte=end_date,
            end_date__gte=start_date
        ).select_related(
            'employee__user',
            'employee__position__department',
            'leave_type'
        )
        
        if department:
            requests = requests.filter(employee__position__department=department)
        
        if employee:
            requests = requests.filter(employee=employee)
        
        # Group by date
        calendar_data = {}
        for request in requests:
            current_date = max(request.start_date, start_date)
            end_date_limit = min(request.end_date, end_date)
            
            while current_date <= end_date_limit:
                if current_date not in calendar_data:
                    calendar_data[current_date] = []
                
                calendar_data[current_date].append({
                    'employee': request.employee,
                    'leave_type': request.leave_type,
                    'request': request
                })
                
                current_date += timedelta(days=1)
        
        return calendar_data
    
    @staticmethod
    def get_department_leave_summary(department, year=None):
        """Get leave summary for a department"""
        if year is None:
            year = timezone.now().year
        
        employees = Employee.objects.filter(
            position__department=department,
            employment_status='active'
        )
        
        summary = []
        for employee in employees:
            balances = LeaveBalanceService.get_employee_all_leave_balances(
                employee, year
            )
            
            # Get recent requests
            recent_requests = LeaveRequestService.get_employee_leave_requests(
                employee, year
            )[:5]
            
            summary.append({
                'employee': employee,
                'balances': balances,
                'recent_requests': recent_requests
            })
        
        return summary


class LeaveReportService:
    """Service for leave reporting and analytics"""
    
    @staticmethod
    def generate_leave_utilization_report(school, year=None):
        """Generate leave utilization report"""
        if year is None:
            year = timezone.now().year
        
        # Get all leave types
        leave_types = LeaveType.objects.filter(school=school)
        
        report_data = []
        for leave_type in leave_types:
            # Get statistics for this leave type
            requests = LeaveRequest.objects.filter(
                school=school,
                leave_type=leave_type,
                start_date__year=year,
                status='approved'
            )
            
            total_requests = requests.count()
            total_days = sum(request.duration_days for request in requests)
            
            # Get employee count
            employee_count = Employee.objects.filter(
                school=school,
                employment_status='active'
            ).count()
            
            # Calculate utilization
            max_possible_days = employee_count * leave_type.max_days_per_year
            utilization_percentage = (total_days / max_possible_days * 100) if max_possible_days > 0 else 0
            
            report_data.append({
                'leave_type': leave_type,
                'total_requests': total_requests,
                'total_days': total_days,
                'employee_count': employee_count,
                'max_possible_days': max_possible_days,
                'utilization_percentage': round(utilization_percentage, 2)
            })
        
        return report_data
    
    @staticmethod
    def generate_employee_leave_report(employee: Employee, year=None):
        """Generate detailed leave report for an employee"""
        if year is None:
            year = timezone.now().year
        
        # Get all leave requests for the year
        requests = LeaveRequest.objects.filter(
            employee=employee,
            start_date__year=year
        ).order_by('start_date')
        
        # Get leave balances
        balances = LeaveBalanceService.get_employee_all_leave_balances(
            employee, year
        )
        
        # Calculate statistics
        approved_requests = requests.filter(status='approved')
        total_leave_days = sum(request.duration_days for request in approved_requests)
        
        # Group by leave type
        leave_by_type = {}
        for request in approved_requests:
            leave_type_name = request.leave_type.name
            if leave_type_name not in leave_by_type:
                leave_by_type[leave_type_name] = {
                    'requests': 0,
                    'days': 0
                }
            leave_by_type[leave_type_name]['requests'] += 1
            leave_by_type[leave_type_name]['days'] += request.duration_days
        
        return {
            'employee': employee,
            'year': year,
            'requests': requests,
            'balances': balances,
            'total_leave_days': total_leave_days,
            'leave_by_type': leave_by_type,
            'approved_count': approved_requests.count(),
            'pending_count': requests.filter(status='pending').count(),
            'rejected_count': requests.filter(status='rejected').count()
        }
    
    @staticmethod
    def generate_monthly_leave_report(school, year, month):
        """Generate monthly leave report"""
        start_date = date(year, month, 1)
        if month == 12:
            end_date = date(year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = date(year, month + 1, 1) - timedelta(days=1)
        
        # Get all approved leave requests for the month
        requests = LeaveRequest.objects.filter(
            school=school,
            status='approved',
            start_date__lte=end_date,
            end_date__gte=start_date
        ).select_related(
            'employee__user',
            'employee__position__department',
            'leave_type'
        )
        
        # Calculate total leave days in the month
        total_leave_days = 0
        department_summary = {}
        
        for request in requests:
            # Calculate days within the month
            request_start = max(request.start_date, start_date)
            request_end = min(request.end_date, end_date)
            days_in_month = (request_end - request_start).days + 1
            
            total_leave_days += days_in_month
            
            # Department summary
            dept_name = request.employee.position.department.name
            if dept_name not in department_summary:
                department_summary[dept_name] = {
                    'requests': 0,
                    'days': 0,
                    'employees': set()
                }
            
            department_summary[dept_name]['requests'] += 1
            department_summary[dept_name]['days'] += days_in_month
            department_summary[dept_name]['employees'].add(request.employee.id)
        
        # Convert sets to counts
        for dept in department_summary.values():
            dept['employees'] = len(dept['employees'])
        
        return {
            'year': year,
            'month': month,
            'start_date': start_date,
            'end_date': end_date,
            'total_requests': requests.count(),
            'total_leave_days': total_leave_days,
            'department_summary': department_summary,
            'requests': requests
        }