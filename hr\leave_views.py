"""
Leave management views
"""
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required, permission_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
from django.template.loader import render_to_string
from django.views.decorators.http import require_http_methods
import json
from datetime import datetime, date, timedelta
from calendar import monthrange

from .models import (
    Employee, LeaveType, LeaveRequest, Department
)
from .leave_services import (
    LeaveBalanceService, LeaveRequestService, LeaveCalendarService, LeaveReportService
)
from .forms import LeaveRequestForm, LeaveTypeForm


@login_required
@permission_required('hr.view_leaverequest', raise_exception=True)
def leave_dashboard(request):
    """Leave management dashboard"""
    current_year = timezone.now().year
    current_month = timezone.now().month
    
    # Get user's employee profile
    try:
        employee = request.user.employee_profile
        school = employee.school
    except:
        messages.error(request, 'Employee profile not found.')
        return redirect('core:dashboard')
    
    # Get leave balances for current user
    leave_balances = LeaveBalanceService.get_employee_all_leave_balances(
        employee, current_year
    )
    
    # Get recent leave requests
    recent_requests = LeaveRequestService.get_employee_leave_requests(
        employee, current_year
    )[:5]
    
    # Get pending requests for approval (if user has permission)
    pending_requests = []
    if request.user.has_perm('hr.change_leaverequest'):
        pending_requests = LeaveRequestService.get_pending_leave_requests(
            school
        )[:10]
    
    # Get leave calendar for current month
    start_date = date(current_year, current_month, 1)
    end_date = date(current_year, current_month, monthrange(current_year, current_month)[1])
    leave_calendar = LeaveCalendarService.get_leave_calendar(
        school, start_date, end_date
    )
    
    context = {
        'leave_balances': leave_balances,
        'recent_requests': recent_requests,
        'pending_requests': pending_requests,
        'leave_calendar': leave_calendar,
        'current_year': current_year,
        'current_month': current_month
    }
    
    return render(request, 'hr/leave_dashboard.html', context)


@login_required
@permission_required('hr.view_leaverequest', raise_exception=True)
def my_leave_requests(request):
    """View user's own leave requests"""
    try:
        employee = request.user.employee_profile
    except:
        messages.error(request, 'Employee profile not found.')
        return redirect('core:dashboard')
    
    # Get filter parameters
    year = request.GET.get('year', timezone.now().year)
    status = request.GET.get('status', '')
    
    try:
        year = int(year)
    except:
        year = timezone.now().year
    
    # Get leave requests
    requests = LeaveRequestService.get_employee_leave_requests(
        employee, year, status if status else None
    )
    
    # Pagination
    paginator = Paginator(requests, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get leave balances
    leave_balances = LeaveBalanceService.get_employee_all_leave_balances(
        employee, year
    )
    
    context = {
        'page_obj': page_obj,
        'leave_balances': leave_balances,
        'selected_year': year,
        'selected_status': status,
        'years': range(year - 2, year + 2)
    }
    
    return render(request, 'hr/my_leave_requests.html', context)


@login_required
@permission_required('hr.add_leaverequest', raise_exception=True)
def create_leave_request(request):
    """Create new leave request"""
    try:
        employee = request.user.employee_profile
    except:
        messages.error(request, 'Employee profile not found.')
        return redirect('core:dashboard')
    
    if request.method == 'POST':
        form = LeaveRequestForm(request.POST, request.FILES)
        if form.is_valid():
            try:
                leave_request = LeaveRequestService.create_leave_request(
                    employee=employee,
                    leave_type=form.cleaned_data['leave_type'],
                    start_date=form.cleaned_data['start_date'],
                    end_date=form.cleaned_data['end_date'],
                    reason=form.cleaned_data['reason'],
                    attachment=form.cleaned_data.get('attachment'),
                    user=request.user
                )
                messages.success(request, 'Leave request submitted successfully.')
                return redirect('hr:leave_request_detail', request_id=leave_request.id)
            except Exception as e:
                messages.error(request, str(e))
    else:
        form = LeaveRequestForm()
        # Filter leave types by school
        form.fields['leave_type'].queryset = LeaveType.objects.filter(
            school=employee.school
        )
    
    # Get leave balances for reference
    leave_balances = LeaveBalanceService.get_employee_all_leave_balances(employee)
    
    context = {
        'form': form,
        'leave_balances': leave_balances
    }
    
    return render(request, 'hr/create_leave_request.html', context)


@login_required
@permission_required('hr.view_leaverequest', raise_exception=True)
def leave_request_detail(request, request_id):
    """View leave request details"""
    leave_request = get_object_or_404(
        LeaveRequest.objects.select_related(
            'employee__user',
            'employee__position__department',
            'leave_type',
            'approved_by'
        ),
        id=request_id
    )
    
    # Check permissions
    if not (request.user.has_perm('hr.change_leaverequest') or 
            leave_request.employee.user == request.user):
        messages.error(request, 'You do not have permission to view this request.')
        return redirect('hr:my_leave_requests')
    
    # Get leave balance for this leave type
    balance = LeaveBalanceService.get_employee_leave_balance(
        leave_request.employee,
        leave_request.leave_type,
        leave_request.start_date.year
    )
    
    context = {
        'leave_request': leave_request,
        'balance': balance
    }
    
    return render(request, 'hr/leave_request_detail.html', context)


@login_required
@permission_required('hr.change_leaverequest', raise_exception=True)
def pending_leave_requests(request):
    """View pending leave requests for approval"""
    try:
        employee = request.user.employee_profile
        school = employee.school
    except:
        messages.error(request, 'Employee profile not found.')
        return redirect('core:dashboard')
    
    # Get filter parameters
    department_id = request.GET.get('department')
    search_query = request.GET.get('search', '')
    
    # Get pending requests
    requests = LeaveRequestService.get_pending_leave_requests(school)
    
    if department_id:
        requests = requests.filter(employee__position__department_id=department_id)
    
    if search_query:
        requests = requests.filter(
            Q(employee__user__first_name__icontains=search_query) |
            Q(employee__user__last_name__icontains=search_query) |
            Q(employee__employee_id__icontains=search_query) |
            Q(reason__icontains=search_query)
        )
    
    # Pagination
    paginator = Paginator(requests, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get departments for filter
    departments = Department.objects.filter(school=school)
    
    context = {
        'page_obj': page_obj,
        'departments': departments,
        'selected_department': department_id,
        'search_query': search_query
    }
    
    return render(request, 'hr/pending_leave_requests.html', context)


@login_required
@permission_required('hr.change_leaverequest', raise_exception=True)
def approve_leave_request(request, request_id):
    """Approve a leave request"""
    leave_request = get_object_or_404(LeaveRequest, id=request_id)
    
    if request.method == 'POST':
        approval_notes = request.POST.get('approval_notes', '')
        
        try:
            LeaveRequestService.approve_leave_request(
                leave_request, request.user, approval_notes
            )
            messages.success(request, 'Leave request approved successfully.')
        except Exception as e:
            messages.error(request, str(e))
    
    return redirect('hr:leave_request_detail', request_id=request_id)


@login_required
@permission_required('hr.change_leaverequest', raise_exception=True)
def reject_leave_request(request, request_id):
    """Reject a leave request"""
    leave_request = get_object_or_404(LeaveRequest, id=request_id)
    
    if request.method == 'POST':
        rejection_reason = request.POST.get('rejection_reason', '')
        
        if not rejection_reason:
            messages.error(request, 'Rejection reason is required.')
            return redirect('hr:leave_request_detail', request_id=request_id)
        
        try:
            LeaveRequestService.reject_leave_request(
                leave_request, request.user, rejection_reason
            )
            messages.success(request, 'Leave request rejected.')
        except Exception as e:
            messages.error(request, str(e))
    
    return redirect('hr:leave_request_detail', request_id=request_id)


@login_required
@permission_required('hr.change_leaverequest', raise_exception=True)
def cancel_leave_request(request, request_id):
    """Cancel a leave request"""
    leave_request = get_object_or_404(LeaveRequest, id=request_id)
    
    # Check if user can cancel (own request or has permission)
    if not (leave_request.employee.user == request.user or 
            request.user.has_perm('hr.delete_leaverequest')):
        messages.error(request, 'You do not have permission to cancel this request.')
        return redirect('hr:leave_request_detail', request_id=request_id)
    
    if request.method == 'POST':
        cancellation_reason = request.POST.get('cancellation_reason', '')
        
        try:
            LeaveRequestService.cancel_leave_request(
                leave_request, request.user, cancellation_reason
            )
            messages.success(request, 'Leave request cancelled.')
        except Exception as e:
            messages.error(request, str(e))
    
    return redirect('hr:leave_request_detail', request_id=request_id)


@login_required
@permission_required('hr.view_leavetype', raise_exception=True)
def leave_types(request):
    """List leave types"""
    try:
        employee = request.user.employee_profile
        school = employee.school
    except:
        messages.error(request, 'Employee profile not found.')
        return redirect('core:dashboard')
    
    leave_types = LeaveType.objects.filter(school=school).order_by('name')
    
    context = {'leave_types': leave_types}
    return render(request, 'hr/leave_types.html', context)


@login_required
@permission_required('hr.add_leavetype', raise_exception=True)
def create_leave_type(request):
    """Create new leave type"""
    if request.method == 'POST':
        form = LeaveTypeForm(request.POST)
        if form.is_valid():
            leave_type = form.save(commit=False)
            leave_type.school = request.user.employee_profile.school
            leave_type.created_by = request.user
            leave_type.save()
            messages.success(request, 'Leave type created successfully.')
            return redirect('hr:leave_types')
    else:
        form = LeaveTypeForm()
    
    context = {'form': form}
    return render(request, 'hr/create_leave_type.html', context)


@login_required
@permission_required('hr.view_leaverequest', raise_exception=True)
def leave_calendar(request):
    """Leave calendar view"""
    try:
        employee = request.user.employee_profile
        school = employee.school
    except:
        messages.error(request, 'Employee profile not found.')
        return redirect('core:dashboard')
    
    # Get date parameters
    year = int(request.GET.get('year', timezone.now().year))
    month = int(request.GET.get('month', timezone.now().month))
    department_id = request.GET.get('department')
    
    # Calculate date range
    start_date = date(year, month, 1)
    end_date = date(year, month, monthrange(year, month)[1])
    
    # Get leave calendar
    department = None
    if department_id:
        department = get_object_or_404(Department, id=department_id, school=school)
    
    calendar_data = LeaveCalendarService.get_leave_calendar(
        school, start_date, end_date, department
    )
    
    # Get departments for filter
    departments = Department.objects.filter(school=school)
    
    # Generate calendar structure
    calendar_weeks = []
    current_date = start_date
    
    # Add days from previous month to fill first week
    while current_date.weekday() != 0:  # Monday is 0
        current_date -= timedelta(days=1)
    
    week = []
    while current_date <= end_date or len(week) < 7:
        if len(week) == 7:
            calendar_weeks.append(week)
            week = []
        
        day_data = {
            'date': current_date,
            'is_current_month': current_date.month == month,
            'leaves': calendar_data.get(current_date, [])
        }
        week.append(day_data)
        current_date += timedelta(days=1)
        
        # Stop after filling the last week
        if current_date > end_date and len(week) == 7:
            break
    
    if week:
        calendar_weeks.append(week)
    
    context = {
        'calendar_weeks': calendar_weeks,
        'year': year,
        'month': month,
        'departments': departments,
        'selected_department': department_id,
        'prev_month': (month - 1) if month > 1 else 12,
        'prev_year': year if month > 1 else year - 1,
        'next_month': (month + 1) if month < 12 else 1,
        'next_year': year if month < 12 else year + 1,
        'month_name': date(year, month, 1).strftime('%B')
    }
    
    return render(request, 'hr/leave_calendar.html', context)


@login_required
@permission_required('hr.view_leaverequest', raise_exception=True)
def leave_reports(request):
    """Leave reports dashboard"""
    try:
        employee = request.user.employee_profile
        school = employee.school
    except:
        messages.error(request, 'Employee profile not found.')
        return redirect('core:dashboard')
    
    current_year = timezone.now().year
    years = range(current_year - 2, current_year + 2)
    
    context = {
        'years': years,
        'current_year': current_year
    }
    
    return render(request, 'hr/leave_reports.html', context)


@login_required
@permission_required('hr.view_leaverequest', raise_exception=True)
def leave_utilization_report(request):
    """Leave utilization report"""
    try:
        employee = request.user.employee_profile
        school = employee.school
    except:
        messages.error(request, 'Employee profile not found.')
        return redirect('core:dashboard')
    
    year = int(request.GET.get('year', timezone.now().year))
    
    report_data = LeaveReportService.generate_leave_utilization_report(school, year)
    
    context = {
        'report_data': report_data,
        'year': year
    }
    
    return render(request, 'hr/leave_utilization_report.html', context)


@login_required
@permission_required('hr.view_leaverequest', raise_exception=True)
def employee_leave_report(request, employee_id):
    """Employee leave report"""
    employee = get_object_or_404(Employee, id=employee_id)
    
    # Check permission
    if not (request.user.has_perm('hr.view_employee') or 
            employee.user == request.user):
        messages.error(request, 'You do not have permission to view this report.')
        return redirect('hr:leave_reports')
    
    year = int(request.GET.get('year', timezone.now().year))
    
    report_data = LeaveReportService.generate_employee_leave_report(employee, year)
    
    context = {
        'report_data': report_data,
        'year': year
    }
    
    return render(request, 'hr/employee_leave_report.html', context)


@login_required
@permission_required('hr.view_leaverequest', raise_exception=True)
def monthly_leave_report(request):
    """Monthly leave report"""
    try:
        employee = request.user.employee_profile
        school = employee.school
    except:
        messages.error(request, 'Employee profile not found.')
        return redirect('core:dashboard')
    
    year = int(request.GET.get('year', timezone.now().year))
    month = int(request.GET.get('month', timezone.now().month))
    
    report_data = LeaveReportService.generate_monthly_leave_report(school, year, month)
    
    context = {
        'report_data': report_data,
        'year': year,
        'month': month,
        'month_name': date(year, month, 1).strftime('%B')
    }
    
    return render(request, 'hr/monthly_leave_report.html', context)


# AJAX Views
@login_required
@require_http_methods(["GET"])
def check_leave_eligibility(request):
    """Check leave eligibility (AJAX)"""
    try:
        employee = request.user.employee_profile
    except:
        return JsonResponse({'error': 'Employee profile not found'}, status=400)
    
    leave_type_id = request.GET.get('leave_type_id')
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    
    if not all([leave_type_id, start_date, end_date]):
        return JsonResponse({'error': 'Missing required parameters'}, status=400)
    
    try:
        leave_type = LeaveType.objects.get(id=leave_type_id, school=employee.school)
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        
        eligibility = LeaveBalanceService.check_leave_eligibility(
            employee, leave_type, start_date, end_date
        )
        
        # Get balance info
        balance = LeaveBalanceService.get_employee_leave_balance(
            employee, leave_type, start_date.year
        )
        
        return JsonResponse({
            'eligible': eligibility['eligible'],
            'reason': eligibility['reason'],
            'balance': {
                'allocated_days': balance['allocated_days'],
                'used_days': balance['used_days'],
                'available_days': balance['available_days']
            },
            'duration_days': (end_date - start_date).days + 1
        })
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=400)


@login_required
@require_http_methods(["GET"])
def get_leave_balance(request, employee_id, leave_type_id):
    """Get leave balance for employee and leave type (AJAX)"""
    try:
        employee = get_object_or_404(Employee, id=employee_id)
        leave_type = get_object_or_404(LeaveType, id=leave_type_id)
        
        # Check permission
        if not (request.user.has_perm('hr.view_employee') or 
                employee.user == request.user):
            return JsonResponse({'error': 'Permission denied'}, status=403)
        
        year = int(request.GET.get('year', timezone.now().year))
        
        balance = LeaveBalanceService.get_employee_leave_balance(
            employee, leave_type, year
        )
        
        return JsonResponse({
            'allocated_days': balance['allocated_days'],
            'used_days': balance['used_days'],
            'pending_days': balance['pending_days'],
            'remaining_days': balance['remaining_days'],
            'available_days': balance['available_days']
        })
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=400)