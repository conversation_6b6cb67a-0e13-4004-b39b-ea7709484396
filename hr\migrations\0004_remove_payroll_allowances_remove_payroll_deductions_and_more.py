# Generated by Django 5.2.4 on 2025-08-03 09:53

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        ("hr", "0003_attendancerecord_school_attendancerecord_updated_by_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name="payroll",
            name="allowances",
        ),
        migrations.RemoveField(
            model_name="payroll",
            name="deductions",
        ),
        migrations.AddField(
            model_name="payroll",
            name="absent_days",
            field=models.IntegerField(default=0, verbose_name="Absent Days"),
        ),
        migrations.AddField(
            model_name="payroll",
            name="calculated_at",
            field=models.DateTimeField(
                blank=True, null=True, verbose_name="Calculated At"
            ),
        ),
        migrations.AddField(
            model_name="payroll",
            name="calculated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="calculated_payrolls",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Calculated By",
            ),
        ),
        migrations.AddField(
            model_name="payroll",
            name="gross_salary",
            field=models.DecimalField(
                decimal_places=2, default=0, max_digits=10, verbose_name="Gross Salary"
            ),
        ),
        migrations.AddField(
            model_name="payroll",
            name="is_calculated",
            field=models.BooleanField(default=False, verbose_name="Is Calculated"),
        ),
        migrations.AddField(
            model_name="payroll",
            name="leave_days",
            field=models.IntegerField(default=0, verbose_name="Leave Days"),
        ),
        migrations.AddField(
            model_name="payroll",
            name="overtime_amount",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                max_digits=10,
                verbose_name="Overtime Amount",
            ),
        ),
        migrations.AddField(
            model_name="payroll",
            name="paid_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="paid_payrolls",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Paid By",
            ),
        ),
        migrations.AddField(
            model_name="payroll",
            name="present_days",
            field=models.IntegerField(default=0, verbose_name="Present Days"),
        ),
        migrations.AddField(
            model_name="payroll",
            name="total_allowances",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                max_digits=10,
                verbose_name="Total Allowances",
            ),
        ),
        migrations.AddField(
            model_name="payroll",
            name="total_deductions",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                max_digits=10,
                verbose_name="Total Deductions",
            ),
        ),
        migrations.AddField(
            model_name="payroll",
            name="working_days",
            field=models.IntegerField(default=0, verbose_name="Working Days"),
        ),
        migrations.CreateModel(
            name="AllowanceType",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "name",
                    models.CharField(max_length=100, verbose_name="Allowance Name"),
                ),
                (
                    "name_ar",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="Allowance Name (Arabic)",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        max_length=20, unique=True, verbose_name="Allowance Code"
                    ),
                ),
                (
                    "is_taxable",
                    models.BooleanField(default=True, verbose_name="Is Taxable"),
                ),
                (
                    "is_fixed",
                    models.BooleanField(default=True, verbose_name="Is Fixed Amount"),
                ),
                (
                    "default_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        verbose_name="Default Amount",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="Description"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Allowance Type",
                "verbose_name_plural": "Allowance Types",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="DeductionType",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "name",
                    models.CharField(max_length=100, verbose_name="Deduction Name"),
                ),
                (
                    "name_ar",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="Deduction Name (Arabic)",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        max_length=20, unique=True, verbose_name="Deduction Code"
                    ),
                ),
                (
                    "is_mandatory",
                    models.BooleanField(default=False, verbose_name="Is Mandatory"),
                ),
                (
                    "is_percentage",
                    models.BooleanField(
                        default=False, verbose_name="Is Percentage Based"
                    ),
                ),
                (
                    "default_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        verbose_name="Default Amount/Percentage",
                    ),
                ),
                (
                    "max_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="Maximum Amount",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="Description"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Deduction Type",
                "verbose_name_plural": "Deduction Types",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="EmployeeAllowance",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="Amount"
                    ),
                ),
                ("effective_date", models.DateField(verbose_name="Effective Date")),
                (
                    "end_date",
                    models.DateField(blank=True, null=True, verbose_name="End Date"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "allowance_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="employee_allowances",
                        to="hr.allowancetype",
                        verbose_name="Allowance Type",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "employee",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="allowances",
                        to="hr.employee",
                        verbose_name="Employee",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Employee Allowance",
                "verbose_name_plural": "Employee Allowances",
                "ordering": ["-effective_date", "employee"],
            },
        ),
        migrations.CreateModel(
            name="EmployeeDeduction",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="Amount"
                    ),
                ),
                ("effective_date", models.DateField(verbose_name="Effective Date")),
                (
                    "end_date",
                    models.DateField(blank=True, null=True, verbose_name="End Date"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "deduction_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="employee_deductions",
                        to="hr.deductiontype",
                        verbose_name="Deduction Type",
                    ),
                ),
                (
                    "employee",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="deductions",
                        to="hr.employee",
                        verbose_name="Employee",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Employee Deduction",
                "verbose_name_plural": "Employee Deductions",
                "ordering": ["-effective_date", "employee"],
            },
        ),
        migrations.CreateModel(
            name="Payslip",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "payslip_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="Payslip Number"
                    ),
                ),
                (
                    "generated_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="Generated At"
                    ),
                ),
                (
                    "is_sent",
                    models.BooleanField(
                        default=False, verbose_name="Is Sent to Employee"
                    ),
                ),
                (
                    "sent_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="Sent At"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "generated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="generated_payslips",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Generated By",
                    ),
                ),
                (
                    "payroll",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="payslip",
                        to="hr.payroll",
                        verbose_name="Payroll",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Payslip",
                "verbose_name_plural": "Payslips",
                "ordering": ["-generated_at"],
            },
        ),
        migrations.CreateModel(
            name="SalaryStructure",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "name",
                    models.CharField(max_length=100, verbose_name="Structure Name"),
                ),
                (
                    "name_ar",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="Structure Name (Arabic)",
                    ),
                ),
                (
                    "basic_salary",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="Basic Salary"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("effective_date", models.DateField(verbose_name="Effective Date")),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="Description"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "position",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="salary_structures",
                        to="hr.position",
                        verbose_name="Position",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Salary Structure",
                "verbose_name_plural": "Salary Structures",
                "ordering": ["-effective_date", "position"],
            },
        ),
        migrations.CreateModel(
            name="EmployeeSalaryStructure",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "basic_salary",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="Basic Salary"
                    ),
                ),
                ("effective_date", models.DateField(verbose_name="Effective Date")),
                (
                    "end_date",
                    models.DateField(blank=True, null=True, verbose_name="End Date"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "employee",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="salary_structures",
                        to="hr.employee",
                        verbose_name="Employee",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "salary_structure",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="employee_assignments",
                        to="hr.salarystructure",
                        verbose_name="Salary Structure",
                    ),
                ),
            ],
            options={
                "verbose_name": "Employee Salary Structure",
                "verbose_name_plural": "Employee Salary Structures",
                "ordering": ["-effective_date", "employee"],
            },
        ),
        migrations.CreateModel(
            name="PayrollAllowance",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="Amount"
                    ),
                ),
                (
                    "allowance_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="hr.allowancetype",
                        verbose_name="Allowance Type",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "payroll",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="allowance_details",
                        to="hr.payroll",
                        verbose_name="Payroll",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Payroll Allowance",
                "verbose_name_plural": "Payroll Allowances",
                "unique_together": {("payroll", "allowance_type")},
            },
        ),
        migrations.CreateModel(
            name="PayrollDeduction",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="Amount"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "deduction_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="hr.deductiontype",
                        verbose_name="Deduction Type",
                    ),
                ),
                (
                    "payroll",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="deduction_details",
                        to="hr.payroll",
                        verbose_name="Payroll",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Payroll Deduction",
                "verbose_name_plural": "Payroll Deductions",
                "unique_together": {("payroll", "deduction_type")},
            },
        ),
    ]
