# Generated by Django 5.2.4 on 2025-08-03 10:55

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        ("hr", "0004_remove_payroll_allowances_remove_payroll_deductions_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="PerformanceGoal",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("title", models.CharField(max_length=200, verbose_name="Goal Title")),
                ("description", models.TextField(verbose_name="Goal Description")),
                ("target_date", models.DateField(verbose_name="Target Date")),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("not_started", "Not Started"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                            ("overdue", "Overdue"),
                        ],
                        default="not_started",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("critical", "Critical"),
                        ],
                        default="medium",
                        max_length=20,
                        verbose_name="Priority",
                    ),
                ),
                (
                    "progress_percentage",
                    models.PositiveIntegerField(
                        default=0,
                        validators=[django.core.validators.MaxValueValidator(100)],
                        verbose_name="Progress Percentage",
                    ),
                ),
                (
                    "completed_date",
                    models.DateField(
                        blank=True, null=True, verbose_name="Completed Date"
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="Notes"),
                ),
                (
                    "assigned_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="assigned_goals",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Assigned By",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "employee",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="performance_goals",
                        to="hr.employee",
                        verbose_name="Employee",
                    ),
                ),
                (
                    "evaluation",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="goals",
                        to="hr.performanceevaluation",
                        verbose_name="Related Evaluation",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Performance Goal",
                "verbose_name_plural": "Performance Goals",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="PerformanceImprovementPlan",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("title", models.CharField(max_length=200, verbose_name="Plan Title")),
                ("description", models.TextField(verbose_name="Plan Description")),
                ("start_date", models.DateField(verbose_name="Start Date")),
                (
                    "target_completion_date",
                    models.DateField(verbose_name="Target Completion Date"),
                ),
                (
                    "actual_completion_date",
                    models.DateField(
                        blank=True, null=True, verbose_name="Actual Completion Date"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                            ("extended", "Extended"),
                        ],
                        default="active",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                ("success_criteria", models.TextField(verbose_name="Success Criteria")),
                (
                    "resources_needed",
                    models.TextField(
                        blank=True, null=True, verbose_name="Resources Needed"
                    ),
                ),
                (
                    "progress_notes",
                    models.TextField(
                        blank=True, null=True, verbose_name="Progress Notes"
                    ),
                ),
                (
                    "final_outcome",
                    models.TextField(
                        blank=True, null=True, verbose_name="Final Outcome"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "employee",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="improvement_plans",
                        to="hr.employee",
                        verbose_name="Employee",
                    ),
                ),
                (
                    "evaluation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="improvement_plans",
                        to="hr.performanceevaluation",
                        verbose_name="Related Evaluation",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "supervisor",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="supervised_improvement_plans",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Supervisor",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Performance Improvement Plan",
                "verbose_name_plural": "Performance Improvement Plans",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="PerformanceImprovementAction",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "title",
                    models.CharField(max_length=200, verbose_name="Action Title"),
                ),
                ("description", models.TextField(verbose_name="Action Description")),
                ("due_date", models.DateField(verbose_name="Due Date")),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "completed_date",
                    models.DateField(
                        blank=True, null=True, verbose_name="Completed Date"
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="Notes"),
                ),
                (
                    "assigned_to",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="assigned_improvement_actions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Assigned To",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "improvement_plan",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="actions",
                        to="hr.performanceimprovementplan",
                        verbose_name="Improvement Plan",
                    ),
                ),
            ],
            options={
                "verbose_name": "Performance Improvement Action",
                "verbose_name_plural": "Performance Improvement Actions",
                "ordering": ["due_date"],
            },
        ),
        migrations.CreateModel(
            name="PerformanceMetric",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                ("name", models.CharField(max_length=100, verbose_name="Metric Name")),
                (
                    "name_ar",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="Metric Name (Arabic)",
                    ),
                ),
                ("description", models.TextField(verbose_name="Description")),
                (
                    "metric_type",
                    models.CharField(
                        choices=[
                            ("quantitative", "Quantitative"),
                            ("qualitative", "Qualitative"),
                            ("behavioral", "Behavioral"),
                        ],
                        max_length=20,
                        verbose_name="Metric Type",
                    ),
                ),
                (
                    "target_value",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="Target Value",
                    ),
                ),
                (
                    "unit_of_measure",
                    models.CharField(
                        blank=True,
                        max_length=50,
                        null=True,
                        verbose_name="Unit of Measure",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "department",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="performance_metrics",
                        to="hr.department",
                        verbose_name="Department",
                    ),
                ),
                (
                    "position",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="performance_metrics",
                        to="hr.position",
                        verbose_name="Position",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Performance Metric",
                "verbose_name_plural": "Performance Metrics",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="EmployeePerformanceMetric",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "actual_value",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="Actual Value"
                    ),
                ),
                ("measurement_date", models.DateField(verbose_name="Measurement Date")),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="Notes"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "employee",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="performance_metrics",
                        to="hr.employee",
                        verbose_name="Employee",
                    ),
                ),
                (
                    "evaluation",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="metric_scores",
                        to="hr.performanceevaluation",
                        verbose_name="Related Evaluation",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "metric",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="employee_metrics",
                        to="hr.performancemetric",
                        verbose_name="Performance Metric",
                    ),
                ),
            ],
            options={
                "verbose_name": "Employee Performance Metric",
                "verbose_name_plural": "Employee Performance Metrics",
                "ordering": ["-measurement_date"],
            },
        ),
        migrations.CreateModel(
            name="PerformanceReviewCycle",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                ("name", models.CharField(max_length=100, verbose_name="Cycle Name")),
                (
                    "name_ar",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="Cycle Name (Arabic)",
                    ),
                ),
                ("start_date", models.DateField(verbose_name="Start Date")),
                ("end_date", models.DateField(verbose_name="End Date")),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="Description"),
                ),
                (
                    "auto_create_evaluations",
                    models.BooleanField(
                        default=False, verbose_name="Auto Create Evaluations"
                    ),
                ),
                (
                    "evaluation_deadline",
                    models.DateField(
                        blank=True, null=True, verbose_name="Evaluation Deadline"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Performance Review Cycle",
                "verbose_name_plural": "Performance Review Cycles",
                "ordering": ["-start_date"],
            },
        ),
    ]
