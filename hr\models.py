from django.db import models
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from django.core.validators import MinValueValidator, MaxValueValidator
from datetime import date, datetime, timedelta
from core.models import BaseModel


class Department(BaseModel):
    """
    Department model
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('Department Name')
    )

    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Department Name (Arabic)')
    )

    code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Department Code')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    head = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='headed_departments',
        verbose_name=_('Department Head')
    )

    class Meta:
        verbose_name = _('Department')
        verbose_name_plural = _('Departments')
        ordering = ['name']

    def __str__(self):
        return self.name


class Position(BaseModel):
    """
    Job position model
    """
    title = models.CharField(
        max_length=100,
        verbose_name=_('Position Title')
    )

    title_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Position Title (Arabic)')
    )

    department = models.ForeignKey(
        Department,
        on_delete=models.CASCADE,
        related_name='positions',
        verbose_name=_('Department')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Job Description')
    )

    requirements = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Requirements')
    )

    min_salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name=_('Minimum Salary')
    )

    max_salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name=_('Maximum Salary')
    )

    class Meta:
        verbose_name = _('Position')
        verbose_name_plural = _('Positions')
        ordering = ['department', 'title']

    def __str__(self):
        return f"{self.title} - {self.department.name}"


class Employee(BaseModel):
    """
    Employee model
    """
    EMPLOYMENT_STATUS = (
        ('active', _('Active')),
        ('inactive', _('Inactive')),
        ('terminated', _('Terminated')),
        ('resigned', _('Resigned')),
        ('retired', _('Retired')),
    )

    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='employee_profile',
        verbose_name=_('User Account')
    )

    employee_id = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Employee ID')
    )

    position = models.ForeignKey(
        Position,
        on_delete=models.CASCADE,
        related_name='employees',
        verbose_name=_('Position')
    )

    hire_date = models.DateField(
        verbose_name=_('Hire Date')
    )

    termination_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_('Termination Date')
    )

    employment_status = models.CharField(
        max_length=20,
        choices=EMPLOYMENT_STATUS,
        default='active',
        verbose_name=_('Employment Status')
    )

    salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Salary')
    )

    emergency_contact_name = models.CharField(
        max_length=100,
        verbose_name=_('Emergency Contact Name')
    )

    emergency_contact_phone = models.CharField(
        max_length=20,
        verbose_name=_('Emergency Contact Phone')
    )

    emergency_contact_relationship = models.CharField(
        max_length=50,
        verbose_name=_('Emergency Contact Relationship')
    )

    class Meta:
        verbose_name = _('Employee')
        verbose_name_plural = _('Employees')
        ordering = ['user__first_name', 'user__last_name']

    def __str__(self):
        return f"{self.user.first_name} {self.user.last_name} ({self.employee_id})"


class AttendanceRecord(BaseModel):
    """
    Employee attendance record model
    """
    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='attendance_records',
        verbose_name=_('Employee')
    )

    date = models.DateField(
        verbose_name=_('Date')
    )

    check_in_time = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_('Check In Time')
    )

    check_out_time = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_('Check Out Time')
    )

    break_start_time = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_('Break Start Time')
    )

    break_end_time = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_('Break End Time')
    )

    status = models.CharField(
        max_length=20,
        choices=[
            ('present', _('Present')),
            ('absent', _('Absent')),
            ('late', _('Late')),
            ('half_day', _('Half Day')),
            ('sick_leave', _('Sick Leave')),
            ('vacation', _('Vacation')),
            ('permission', _('Permission')),
        ],
        default='present',
        verbose_name=_('Status')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    is_manual = models.BooleanField(
        default=False,
        verbose_name=_('Is Manual Entry')
    )

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_attendance_records',
        verbose_name=_('Created By')
    )

    class Meta:
        verbose_name = _('Attendance Record')
        verbose_name_plural = _('Attendance Records')
        unique_together = ['employee', 'date']
        ordering = ['-date', 'employee']

    def __str__(self):
        return f"{self.employee.user.first_name} {self.employee.user.last_name} - {self.date} ({self.status})"

    @property
    def total_hours(self):
        """Calculate total working hours"""
        if self.check_in_time and self.check_out_time and self.date:
            from datetime import datetime, timedelta
            check_in = datetime.combine(self.date, self.check_in_time)
            check_out = datetime.combine(self.date, self.check_out_time)

            # Handle overnight shifts
            if check_out < check_in:
                check_out += timedelta(days=1)

            total_time = check_out - check_in

            # Subtract break time if available
            if self.break_start_time and self.break_end_time and self.date:
                break_start = datetime.combine(self.date, self.break_start_time)
                break_end = datetime.combine(self.date, self.break_end_time)
                break_duration = break_end - break_start
                total_time -= break_duration

            return total_time.total_seconds() / 3600  # Return hours as float
        return 0


class LeaveType(BaseModel):
    """
    Leave type model
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('Leave Type Name')
    )

    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Leave Type Name (Arabic)')
    )

    code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Leave Type Code')
    )

    max_days_per_year = models.PositiveIntegerField(
        default=30,
        verbose_name=_('Max Days Per Year')
    )

    is_paid = models.BooleanField(
        default=True,
        verbose_name=_('Is Paid Leave')
    )

    requires_approval = models.BooleanField(
        default=True,
        verbose_name=_('Requires Approval')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    class Meta:
        verbose_name = _('Leave Type')
        verbose_name_plural = _('Leave Types')
        ordering = ['name']

    def __str__(self):
        return self.name


class LeaveRequest(BaseModel):
    """
    Employee leave request model
    """
    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('approved', _('Approved')),
        ('rejected', _('Rejected')),
        ('cancelled', _('Cancelled')),
    ]

    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='leave_requests',
        verbose_name=_('Employee')
    )

    leave_type = models.ForeignKey(
        LeaveType,
        on_delete=models.CASCADE,
        related_name='leave_requests',
        verbose_name=_('Leave Type')
    )

    start_date = models.DateField(
        verbose_name=_('Start Date')
    )

    end_date = models.DateField(
        verbose_name=_('End Date')
    )

    reason = models.TextField(
        verbose_name=_('Reason')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('Status')
    )

    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_leave_requests',
        verbose_name=_('Approved By')
    )

    approved_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Approved At')
    )

    rejection_reason = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Rejection Reason')
    )

    attachment = models.FileField(
        upload_to='hr/leave_attachments/',
        blank=True,
        null=True,
        verbose_name=_('Attachment')
    )

    class Meta:
        verbose_name = _('Leave Request')
        verbose_name_plural = _('Leave Requests')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.employee.user.first_name} {self.employee.user.last_name} - {self.leave_type.name} ({self.start_date} to {self.end_date})"

    @property
    def duration_days(self):
        """Calculate leave duration in days"""
        return (self.end_date - self.start_date).days + 1

    def clean(self):
        from django.core.exceptions import ValidationError
        if self.start_date and self.end_date:
            if self.start_date > self.end_date:
                raise ValidationError(_('Start date cannot be after end date.'))


class SalaryStructure(BaseModel):
    """
    Salary structure template model
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('Structure Name')
    )
    
    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Structure Name (Arabic)')
    )
    
    position = models.ForeignKey(
        Position,
        on_delete=models.CASCADE,
        related_name='salary_structures',
        verbose_name=_('Position')
    )
    
    basic_salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Basic Salary')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    
    effective_date = models.DateField(
        verbose_name=_('Effective Date')
    )
    
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    class Meta:
        verbose_name = _('Salary Structure')
        verbose_name_plural = _('Salary Structures')
        ordering = ['-effective_date', 'position']

    def __str__(self):
        return f"{self.name} - {self.position.title}"


class AllowanceType(BaseModel):
    """
    Allowance type model
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('Allowance Name')
    )
    
    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Allowance Name (Arabic)')
    )
    
    code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Allowance Code')
    )
    
    is_taxable = models.BooleanField(
        default=True,
        verbose_name=_('Is Taxable')
    )
    
    is_fixed = models.BooleanField(
        default=True,
        verbose_name=_('Is Fixed Amount')
    )
    
    default_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_('Default Amount')
    )
    
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    class Meta:
        verbose_name = _('Allowance Type')
        verbose_name_plural = _('Allowance Types')
        ordering = ['name']

    def __str__(self):
        return self.name


class DeductionType(BaseModel):
    """
    Deduction type model
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('Deduction Name')
    )
    
    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Deduction Name (Arabic)')
    )
    
    code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Deduction Code')
    )
    
    is_mandatory = models.BooleanField(
        default=False,
        verbose_name=_('Is Mandatory')
    )
    
    is_percentage = models.BooleanField(
        default=False,
        verbose_name=_('Is Percentage Based')
    )
    
    default_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_('Default Amount/Percentage')
    )
    
    max_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_('Maximum Amount')
    )
    
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    class Meta:
        verbose_name = _('Deduction Type')
        verbose_name_plural = _('Deduction Types')
        ordering = ['name']

    def __str__(self):
        return self.name


class EmployeeSalaryStructure(BaseModel):
    """
    Employee specific salary structure
    """
    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='salary_structures',
        verbose_name=_('Employee')
    )
    
    salary_structure = models.ForeignKey(
        SalaryStructure,
        on_delete=models.CASCADE,
        related_name='employee_assignments',
        verbose_name=_('Salary Structure')
    )
    
    basic_salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Basic Salary')
    )
    
    effective_date = models.DateField(
        verbose_name=_('Effective Date')
    )
    
    end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('End Date')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )

    class Meta:
        verbose_name = _('Employee Salary Structure')
        verbose_name_plural = _('Employee Salary Structures')
        ordering = ['-effective_date', 'employee']

    def __str__(self):
        return f"{self.employee} - {self.salary_structure.name}"


class EmployeeAllowance(BaseModel):
    """
    Employee allowance model
    """
    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='allowances',
        verbose_name=_('Employee')
    )
    
    allowance_type = models.ForeignKey(
        AllowanceType,
        on_delete=models.CASCADE,
        related_name='employee_allowances',
        verbose_name=_('Allowance Type')
    )
    
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Amount')
    )
    
    effective_date = models.DateField(
        verbose_name=_('Effective Date')
    )
    
    end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('End Date')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )

    class Meta:
        verbose_name = _('Employee Allowance')
        verbose_name_plural = _('Employee Allowances')
        ordering = ['-effective_date', 'employee']

    def __str__(self):
        return f"{self.employee} - {self.allowance_type.name}"


class EmployeeDeduction(BaseModel):
    """
    Employee deduction model
    """
    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='deductions',
        verbose_name=_('Employee')
    )
    
    deduction_type = models.ForeignKey(
        DeductionType,
        on_delete=models.CASCADE,
        related_name='employee_deductions',
        verbose_name=_('Deduction Type')
    )
    
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Amount')
    )
    
    effective_date = models.DateField(
        verbose_name=_('Effective Date')
    )
    
    end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('End Date')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )

    class Meta:
        verbose_name = _('Employee Deduction')
        verbose_name_plural = _('Employee Deductions')
        ordering = ['-effective_date', 'employee']

    def __str__(self):
        return f"{self.employee} - {self.deduction_type.name}"


class PayrollPeriod(BaseModel):
    """
    Payroll period model
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('Period Name')
    )

    start_date = models.DateField(
        verbose_name=_('Start Date')
    )

    end_date = models.DateField(
        verbose_name=_('End Date')
    )

    is_closed = models.BooleanField(
        default=False,
        verbose_name=_('Is Closed')
    )

    closed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='closed_payroll_periods',
        verbose_name=_('Closed By')
    )

    closed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Closed At')
    )

    class Meta:
        verbose_name = _('Payroll Period')
        verbose_name_plural = _('Payroll Periods')
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.name} ({self.start_date} - {self.end_date})"


class Payroll(BaseModel):
    """
    Employee payroll model
    """
    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='payrolls',
        verbose_name=_('Employee')
    )

    period = models.ForeignKey(
        PayrollPeriod,
        on_delete=models.CASCADE,
        related_name='payrolls',
        verbose_name=_('Payroll Period')
    )

    basic_salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Basic Salary')
    )

    total_allowances = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_('Total Allowances')
    )

    overtime_hours = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        verbose_name=_('Overtime Hours')
    )

    overtime_rate = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_('Overtime Rate')
    )

    overtime_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_('Overtime Amount')
    )

    gross_salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_('Gross Salary')
    )

    total_deductions = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_('Total Deductions')
    )

    tax_deduction = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_('Tax Deduction')
    )

    insurance_deduction = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_('Insurance Deduction')
    )

    net_salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Net Salary')
    )

    working_days = models.IntegerField(
        default=0,
        verbose_name=_('Working Days')
    )

    present_days = models.IntegerField(
        default=0,
        verbose_name=_('Present Days')
    )

    absent_days = models.IntegerField(
        default=0,
        verbose_name=_('Absent Days')
    )

    leave_days = models.IntegerField(
        default=0,
        verbose_name=_('Leave Days')
    )

    is_calculated = models.BooleanField(
        default=False,
        verbose_name=_('Is Calculated')
    )

    calculated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='calculated_payrolls',
        verbose_name=_('Calculated By')
    )

    calculated_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Calculated At')
    )

    is_paid = models.BooleanField(
        default=False,
        verbose_name=_('Is Paid')
    )

    paid_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Paid Date')
    )

    paid_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='paid_payrolls',
        verbose_name=_('Paid By')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    class Meta:
        verbose_name = _('Payroll')
        verbose_name_plural = _('Payrolls')
        unique_together = ['employee', 'period']
        ordering = ['-period__start_date', 'employee']

    def __str__(self):
        return f"{self.employee.user.first_name} {self.employee.user.last_name} - {self.period.name}"

    def calculate_salary(self):
        """Calculate salary components"""
        # Calculate overtime amount
        self.overtime_amount = self.overtime_hours * self.overtime_rate
        
        # Calculate gross salary
        self.gross_salary = self.basic_salary + self.total_allowances + self.overtime_amount
        
        # Calculate net salary
        self.net_salary = self.gross_salary - self.total_deductions - self.tax_deduction - self.insurance_deduction
        
        return self.net_salary

    def save(self, *args, **kwargs):
        # Auto-calculate salary components
        self.calculate_salary()
        super().save(*args, **kwargs)


class PayrollAllowance(BaseModel):
    """
    Payroll allowance detail model
    """
    payroll = models.ForeignKey(
        Payroll,
        on_delete=models.CASCADE,
        related_name='allowance_details',
        verbose_name=_('Payroll')
    )
    
    allowance_type = models.ForeignKey(
        AllowanceType,
        on_delete=models.CASCADE,
        verbose_name=_('Allowance Type')
    )
    
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Amount')
    )

    class Meta:
        verbose_name = _('Payroll Allowance')
        verbose_name_plural = _('Payroll Allowances')
        unique_together = ['payroll', 'allowance_type']

    def __str__(self):
        return f"{self.payroll} - {self.allowance_type.name}"


class PayrollDeduction(BaseModel):
    """
    Payroll deduction detail model
    """
    payroll = models.ForeignKey(
        Payroll,
        on_delete=models.CASCADE,
        related_name='deduction_details',
        verbose_name=_('Payroll')
    )
    
    deduction_type = models.ForeignKey(
        DeductionType,
        on_delete=models.CASCADE,
        verbose_name=_('Deduction Type')
    )
    
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Amount')
    )

    class Meta:
        verbose_name = _('Payroll Deduction')
        verbose_name_plural = _('Payroll Deductions')
        unique_together = ['payroll', 'deduction_type']

    def __str__(self):
        return f"{self.payroll} - {self.deduction_type.name}"


class Payslip(BaseModel):
    """
    Employee payslip model
    """
    payroll = models.OneToOneField(
        Payroll,
        on_delete=models.CASCADE,
        related_name='payslip',
        verbose_name=_('Payroll')
    )
    
    payslip_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('Payslip Number')
    )
    
    generated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='generated_payslips',
        verbose_name=_('Generated By')
    )
    
    generated_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Generated At')
    )
    
    is_sent = models.BooleanField(
        default=False,
        verbose_name=_('Is Sent to Employee')
    )
    
    sent_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Sent At')
    )

    class Meta:
        verbose_name = _('Payslip')
        verbose_name_plural = _('Payslips')
        ordering = ['-generated_at']

    def __str__(self):
        return f"Payslip {self.payslip_number} - {self.payroll.employee}"

    def save(self, *args, **kwargs):
        if not self.payslip_number:
            # Generate payslip number
            from datetime import datetime
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            self.payslip_number = f"PS-{self.payroll.employee.employee_id}-{timestamp}"
        super().save(*args, **kwargs)


class PerformanceEvaluation(BaseModel):
    """
    Employee performance evaluation model
    """
    RATING_CHOICES = [
        (1, _('Poor')),
        (2, _('Below Average')),
        (3, _('Average')),
        (4, _('Good')),
        (5, _('Excellent')),
    ]

    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='performance_evaluations',
        verbose_name=_('Employee')
    )

    evaluator = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='conducted_evaluations',
        verbose_name=_('Evaluator')
    )

    evaluation_period_start = models.DateField(
        verbose_name=_('Evaluation Period Start')
    )

    evaluation_period_end = models.DateField(
        verbose_name=_('Evaluation Period End')
    )

    # Performance criteria ratings
    quality_of_work = models.PositiveIntegerField(
        choices=RATING_CHOICES,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_('Quality of Work')
    )

    productivity = models.PositiveIntegerField(
        choices=RATING_CHOICES,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_('Productivity')
    )

    communication = models.PositiveIntegerField(
        choices=RATING_CHOICES,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_('Communication')
    )

    teamwork = models.PositiveIntegerField(
        choices=RATING_CHOICES,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_('Teamwork')
    )

    punctuality = models.PositiveIntegerField(
        choices=RATING_CHOICES,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_('Punctuality')
    )

    initiative = models.PositiveIntegerField(
        choices=RATING_CHOICES,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_('Initiative')
    )

    # Overall assessment
    overall_rating = models.PositiveIntegerField(
        choices=RATING_CHOICES,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_('Overall Rating')
    )

    strengths = models.TextField(
        verbose_name=_('Strengths')
    )

    areas_for_improvement = models.TextField(
        verbose_name=_('Areas for Improvement')
    )

    goals_for_next_period = models.TextField(
        verbose_name=_('Goals for Next Period')
    )

    employee_comments = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Employee Comments')
    )

    is_finalized = models.BooleanField(
        default=False,
        verbose_name=_('Is Finalized')
    )

    finalized_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Finalized At')
    )

    class Meta:
        verbose_name = _('Performance Evaluation')
        verbose_name_plural = _('Performance Evaluations')
        ordering = ['-evaluation_period_end', 'employee']

    def __str__(self):
        return f"{self.employee.user.first_name} {self.employee.user.last_name} - {self.evaluation_period_start} to {self.evaluation_period_end}"

    @property
    def average_rating(self):
        """Calculate average rating across all criteria"""
        ratings = [
            self.quality_of_work,
            self.productivity,
            self.communication,
            self.teamwork,
            self.punctuality,
            self.initiative
        ]
        return sum(ratings) / len(ratings)


class EmployeeDocument(BaseModel):
    """
    Employee document model
    """
    DOCUMENT_TYPES = [
        ('contract', _('Employment Contract')),
        ('id_copy', _('ID Copy')),
        ('resume', _('Resume/CV')),
        ('certificate', _('Certificate')),
        ('medical', _('Medical Certificate')),
        ('reference', _('Reference Letter')),
        ('other', _('Other')),
    ]

    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='documents',
        verbose_name=_('Employee')
    )

    document_type = models.CharField(
        max_length=20,
        choices=DOCUMENT_TYPES,
        verbose_name=_('Document Type')
    )

    title = models.CharField(
        max_length=200,
        verbose_name=_('Document Title')
    )

    file = models.FileField(
        upload_to='hr/employee_documents/',
        verbose_name=_('Document File')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    uploaded_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='uploaded_employee_documents',
        verbose_name=_('Uploaded By')
    )

    is_confidential = models.BooleanField(
        default=False,
        verbose_name=_('Is Confidential')
    )

    expiry_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Expiry Date')
    )

    class Meta:
        verbose_name = _('Employee Document')
        verbose_name_plural = _('Employee Documents')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.employee.user.first_name} {self.employee.user.last_name} - {self.title}"

    @property
    def is_expired(self):
        """Check if document is expired"""
        if self.expiry_date:
            from datetime import date
            return self.expiry_date < date.today()
        return False


class PerformanceReviewCycle(BaseModel):
    """
    Performance review cycle model for managing review periods
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('Cycle Name')
    )
    
    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Cycle Name (Arabic)')
    )
    
    start_date = models.DateField(
        verbose_name=_('Start Date')
    )
    
    end_date = models.DateField(
        verbose_name=_('End Date')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )
    
    auto_create_evaluations = models.BooleanField(
        default=False,
        verbose_name=_('Auto Create Evaluations')
    )
    
    evaluation_deadline = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Evaluation Deadline')
    )

    class Meta:
        verbose_name = _('Performance Review Cycle')
        verbose_name_plural = _('Performance Review Cycles')
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.name} ({self.start_date} - {self.end_date})"

    def clean(self):
        if self.start_date and self.end_date:
            if self.start_date >= self.end_date:
                raise ValidationError(_('Start date must be before end date.'))


class PerformanceGoal(BaseModel):
    """
    Individual performance goal model
    """
    GOAL_STATUS_CHOICES = [
        ('not_started', _('Not Started')),
        ('in_progress', _('In Progress')),
        ('completed', _('Completed')),
        ('cancelled', _('Cancelled')),
        ('overdue', _('Overdue')),
    ]
    
    PRIORITY_CHOICES = [
        ('low', _('Low')),
        ('medium', _('Medium')),
        ('high', _('High')),
        ('critical', _('Critical')),
    ]
    
    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='performance_goals',
        verbose_name=_('Employee')
    )
    
    title = models.CharField(
        max_length=200,
        verbose_name=_('Goal Title')
    )
    
    description = models.TextField(
        verbose_name=_('Goal Description')
    )
    
    target_date = models.DateField(
        verbose_name=_('Target Date')
    )
    
    status = models.CharField(
        max_length=20,
        choices=GOAL_STATUS_CHOICES,
        default='not_started',
        verbose_name=_('Status')
    )
    
    priority = models.CharField(
        max_length=20,
        choices=PRIORITY_CHOICES,
        default='medium',
        verbose_name=_('Priority')
    )
    
    progress_percentage = models.PositiveIntegerField(
        default=0,
        validators=[MaxValueValidator(100)],
        verbose_name=_('Progress Percentage')
    )
    
    assigned_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='assigned_goals',
        verbose_name=_('Assigned By')
    )
    
    completed_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Completed Date')
    )
    
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )
    
    evaluation = models.ForeignKey(
        PerformanceEvaluation,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='goals',
        verbose_name=_('Related Evaluation')
    )

    class Meta:
        verbose_name = _('Performance Goal')
        verbose_name_plural = _('Performance Goals')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.employee.user.first_name} {self.employee.user.last_name} - {self.title}"

    @property
    def is_overdue(self):
        """Check if goal is overdue"""
        if self.status not in ['completed', 'cancelled'] and self.target_date:
            return self.target_date < date.today()
        return False

    def update_status(self):
        """Auto-update status based on progress and dates"""
        if self.progress_percentage == 100:
            self.status = 'completed'
            if not self.completed_date:
                self.completed_date = date.today()
        elif self.progress_percentage > 0:
            self.status = 'in_progress'
        elif self.is_overdue and self.status != 'completed':
            self.status = 'overdue'

    def save(self, *args, **kwargs):
        self.update_status()
        super().save(*args, **kwargs)


class PerformanceImprovementPlan(BaseModel):
    """
    Performance improvement plan model
    """
    PLAN_STATUS_CHOICES = [
        ('active', _('Active')),
        ('completed', _('Completed')),
        ('cancelled', _('Cancelled')),
        ('extended', _('Extended')),
    ]
    
    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='improvement_plans',
        verbose_name=_('Employee')
    )
    
    evaluation = models.ForeignKey(
        PerformanceEvaluation,
        on_delete=models.CASCADE,
        related_name='improvement_plans',
        verbose_name=_('Related Evaluation')
    )
    
    title = models.CharField(
        max_length=200,
        verbose_name=_('Plan Title')
    )
    
    description = models.TextField(
        verbose_name=_('Plan Description')
    )
    
    start_date = models.DateField(
        verbose_name=_('Start Date')
    )
    
    target_completion_date = models.DateField(
        verbose_name=_('Target Completion Date')
    )
    
    actual_completion_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Actual Completion Date')
    )
    
    status = models.CharField(
        max_length=20,
        choices=PLAN_STATUS_CHOICES,
        default='active',
        verbose_name=_('Status')
    )
    
    success_criteria = models.TextField(
        verbose_name=_('Success Criteria')
    )
    
    resources_needed = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Resources Needed')
    )
    
    supervisor = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='supervised_improvement_plans',
        verbose_name=_('Supervisor')
    )
    
    progress_notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Progress Notes')
    )
    
    final_outcome = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Final Outcome')
    )

    class Meta:
        verbose_name = _('Performance Improvement Plan')
        verbose_name_plural = _('Performance Improvement Plans')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.employee.user.first_name} {self.employee.user.last_name} - {self.title}"

    @property
    def is_overdue(self):
        """Check if plan is overdue"""
        if self.status == 'active' and self.target_completion_date:
            return self.target_completion_date < date.today()
        return False

    @property
    def duration_days(self):
        """Calculate plan duration in days"""
        return (self.target_completion_date - self.start_date).days

    def clean(self):
        if self.start_date and self.target_completion_date:
            if self.start_date >= self.target_completion_date:
                raise ValidationError(_('Start date must be before target completion date.'))


class PerformanceImprovementAction(BaseModel):
    """
    Individual action items within an improvement plan
    """
    ACTION_STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('in_progress', _('In Progress')),
        ('completed', _('Completed')),
        ('cancelled', _('Cancelled')),
    ]
    
    improvement_plan = models.ForeignKey(
        PerformanceImprovementPlan,
        on_delete=models.CASCADE,
        related_name='actions',
        verbose_name=_('Improvement Plan')
    )
    
    title = models.CharField(
        max_length=200,
        verbose_name=_('Action Title')
    )
    
    description = models.TextField(
        verbose_name=_('Action Description')
    )
    
    due_date = models.DateField(
        verbose_name=_('Due Date')
    )
    
    status = models.CharField(
        max_length=20,
        choices=ACTION_STATUS_CHOICES,
        default='pending',
        verbose_name=_('Status')
    )
    
    assigned_to = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='assigned_improvement_actions',
        verbose_name=_('Assigned To')
    )
    
    completed_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Completed Date')
    )
    
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    class Meta:
        verbose_name = _('Performance Improvement Action')
        verbose_name_plural = _('Performance Improvement Actions')
        ordering = ['due_date']

    def __str__(self):
        return f"{self.improvement_plan.employee.user.first_name} {self.improvement_plan.employee.user.last_name} - {self.title}"

    @property
    def is_overdue(self):
        """Check if action is overdue"""
        if self.status not in ['completed', 'cancelled'] and self.due_date:
            return self.due_date < date.today()
        return False


class PerformanceMetric(BaseModel):
    """
    Performance metrics and KPIs
    """
    METRIC_TYPE_CHOICES = [
        ('quantitative', _('Quantitative')),
        ('qualitative', _('Qualitative')),
        ('behavioral', _('Behavioral')),
    ]
    
    name = models.CharField(
        max_length=100,
        verbose_name=_('Metric Name')
    )
    
    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Metric Name (Arabic)')
    )
    
    description = models.TextField(
        verbose_name=_('Description')
    )
    
    metric_type = models.CharField(
        max_length=20,
        choices=METRIC_TYPE_CHOICES,
        verbose_name=_('Metric Type')
    )
    
    department = models.ForeignKey(
        Department,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='performance_metrics',
        verbose_name=_('Department')
    )
    
    position = models.ForeignKey(
        Position,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='performance_metrics',
        verbose_name=_('Position')
    )
    
    target_value = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_('Target Value')
    )
    
    unit_of_measure = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('Unit of Measure')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )

    class Meta:
        verbose_name = _('Performance Metric')
        verbose_name_plural = _('Performance Metrics')
        ordering = ['name']

    def __str__(self):
        return self.name


class EmployeePerformanceMetric(BaseModel):
    """
    Employee performance metric tracking
    """
    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='performance_metrics',
        verbose_name=_('Employee')
    )
    
    metric = models.ForeignKey(
        PerformanceMetric,
        on_delete=models.CASCADE,
        related_name='employee_metrics',
        verbose_name=_('Performance Metric')
    )
    
    evaluation = models.ForeignKey(
        PerformanceEvaluation,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='metric_scores',
        verbose_name=_('Related Evaluation')
    )
    
    actual_value = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Actual Value')
    )
    
    measurement_date = models.DateField(
        verbose_name=_('Measurement Date')
    )
    
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    class Meta:
        verbose_name = _('Employee Performance Metric')
        verbose_name_plural = _('Employee Performance Metrics')
        ordering = ['-measurement_date']

    def __str__(self):
        return f"{self.employee.user.first_name} {self.employee.user.last_name} - {self.metric.name}"

    @property
    def achievement_percentage(self):
        """Calculate achievement percentage against target"""
        if self.metric.target_value and self.metric.target_value > 0:
            return (self.actual_value / self.metric.target_value) * 100
        return 0
