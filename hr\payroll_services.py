"""
Payroll processing services
"""
from decimal import Decimal
from datetime import datetime, date
from django.db import transaction, models
from django.utils import timezone
from django.core.exceptions import ValidationError
from .models import (
    Employee, PayrollPeriod, Payroll, PayrollAllowance, PayrollDeduction,
    Payslip, EmployeeAllowance, EmployeeDeduction, AttendanceRecord,
    SalaryStructure, EmployeeSalaryStructure, AllowanceType, DeductionType
)


class PayrollCalculationService:
    """Service for payroll calculations"""
    
    def __init__(self, period: PayrollPeriod):
        self.period = period
    
    def calculate_employee_payroll(self, employee: Employee, user=None):
        """Calculate payroll for a single employee"""
        try:
            with transaction.atomic():
                # Get or create payroll record
                payroll, created = Payroll.objects.get_or_create(
                    employee=employee,
                    period=self.period,
                    defaults={
                        'school': employee.school,
                        'basic_salary': self._get_employee_basic_salary(employee),
                        'created_by': user
                    }
                )
                
                if not created and payroll.is_calculated:
                    raise ValidationError(f"Payroll for {employee} is already calculated")
                
                # Calculate attendance data
                attendance_data = self._calculate_attendance(employee)
                payroll.working_days = attendance_data['working_days']
                payroll.present_days = attendance_data['present_days']
                payroll.absent_days = attendance_data['absent_days']
                payroll.leave_days = attendance_data['leave_days']
                
                # Calculate basic salary based on attendance
                payroll.basic_salary = self._calculate_prorated_salary(
                    employee, attendance_data
                )
                
                # Calculate allowances
                total_allowances = self._calculate_allowances(payroll)
                payroll.total_allowances = total_allowances
                
                # Calculate overtime
                overtime_data = self._calculate_overtime(employee)
                payroll.overtime_hours = overtime_data['hours']
                payroll.overtime_rate = overtime_data['rate']
                payroll.overtime_amount = overtime_data['amount']
                
                # Calculate deductions
                total_deductions = self._calculate_deductions(payroll)
                payroll.total_deductions = total_deductions
                
                # Calculate tax
                payroll.tax_deduction = self._calculate_tax(payroll)
                
                # Calculate insurance
                payroll.insurance_deduction = self._calculate_insurance(payroll)
                
                # Mark as calculated
                payroll.is_calculated = True
                payroll.calculated_by = user
                payroll.calculated_at = timezone.now()
                
                # Save payroll (this will auto-calculate net salary)
                payroll.save()
                
                return payroll
                
        except Exception as e:
            raise ValidationError(f"Error calculating payroll for {employee}: {str(e)}")
    
    def calculate_bulk_payroll(self, employees=None, user=None):
        """Calculate payroll for multiple employees"""
        if employees is None:
            employees = Employee.objects.filter(
                school=self.period.school,
                employment_status='active'
            )
        
        results = {
            'success': [],
            'errors': []
        }
        
        for employee in employees:
            try:
                payroll = self.calculate_employee_payroll(employee, user)
                results['success'].append({
                    'employee': employee,
                    'payroll': payroll
                })
            except Exception as e:
                results['errors'].append({
                    'employee': employee,
                    'error': str(e)
                })
        
        return results
    
    def _get_employee_basic_salary(self, employee: Employee):
        """Get employee's current basic salary"""
        try:
            salary_structure = EmployeeSalaryStructure.objects.filter(
                employee=employee,
                is_active=True,
                effective_date__lte=self.period.end_date
            ).order_by('-effective_date').first()
            
            if salary_structure:
                return salary_structure.basic_salary
            else:
                return employee.salary
        except:
            return employee.salary
    
    def _calculate_attendance(self, employee: Employee):
        """Calculate attendance data for the payroll period"""
        attendance_records = AttendanceRecord.objects.filter(
            employee=employee,
            date__range=[self.period.start_date, self.period.end_date]
        )
        
        # Calculate working days (excluding weekends)
        working_days = 0
        current_date = self.period.start_date
        while current_date <= self.period.end_date:
            if current_date.weekday() < 5:  # Monday to Friday
                working_days += 1
            current_date += timezone.timedelta(days=1)
        
        present_days = attendance_records.filter(
            status__in=['present', 'late']
        ).count()
        
        absent_days = attendance_records.filter(status='absent').count()
        
        leave_days = attendance_records.filter(
            status__in=['sick_leave', 'vacation', 'permission']
        ).count()
        
        return {
            'working_days': working_days,
            'present_days': present_days,
            'absent_days': absent_days,
            'leave_days': leave_days
        }
    
    def _calculate_prorated_salary(self, employee: Employee, attendance_data):
        """Calculate prorated salary based on attendance"""
        basic_salary = self._get_employee_basic_salary(employee)
        working_days = attendance_data['working_days']
        present_days = attendance_data['present_days']
        leave_days = attendance_data['leave_days']
        
        if working_days == 0:
            return Decimal('0.00')
        
        # Include paid leave days in salary calculation
        effective_days = present_days + leave_days
        
        # Calculate prorated salary
        daily_salary = basic_salary / working_days
        prorated_salary = daily_salary * effective_days
        
        return prorated_salary
    
    def _calculate_allowances(self, payroll: Payroll):
        """Calculate total allowances for the employee"""
        employee = payroll.employee
        total_allowances = Decimal('0.00')
        
        # Get active allowances for the employee
        allowances = EmployeeAllowance.objects.filter(
            employee=employee,
            is_active=True,
            effective_date__lte=self.period.end_date
        ).filter(
            models.Q(end_date__isnull=True) | 
            models.Q(end_date__gte=self.period.start_date)
        )
        
        # Clear existing allowance details
        PayrollAllowance.objects.filter(payroll=payroll).delete()
        
        for allowance in allowances:
            # Create payroll allowance detail
            PayrollAllowance.objects.create(
                payroll=payroll,
                allowance_type=allowance.allowance_type,
                amount=allowance.amount,
                school=payroll.school,
                created_by=payroll.created_by
            )
            total_allowances += allowance.amount
        
        return total_allowances
    
    def _calculate_overtime(self, employee: Employee):
        """Calculate overtime hours and amount"""
        # Get attendance records for the period
        attendance_records = AttendanceRecord.objects.filter(
            employee=employee,
            date__range=[self.period.start_date, self.period.end_date],
            check_in_time__isnull=False,
            check_out_time__isnull=False
        )
        
        total_overtime_hours = Decimal('0.00')
        standard_hours_per_day = Decimal('8.00')  # Standard 8 hours per day
        
        for record in attendance_records:
            daily_hours = Decimal(str(record.total_hours))
            if daily_hours > standard_hours_per_day:
                overtime_hours = daily_hours - standard_hours_per_day
                total_overtime_hours += overtime_hours
        
        # Calculate overtime rate (typically 1.5x hourly rate)
        basic_salary = self._get_employee_basic_salary(employee)
        monthly_working_hours = Decimal('176.00')  # 22 days * 8 hours
        hourly_rate = basic_salary / monthly_working_hours
        overtime_rate = hourly_rate * Decimal('1.5')
        
        overtime_amount = total_overtime_hours * overtime_rate
        
        return {
            'hours': total_overtime_hours,
            'rate': overtime_rate,
            'amount': overtime_amount
        }
    
    def _calculate_deductions(self, payroll: Payroll):
        """Calculate total deductions for the employee"""
        employee = payroll.employee
        total_deductions = Decimal('0.00')
        
        # Get active deductions for the employee
        deductions = EmployeeDeduction.objects.filter(
            employee=employee,
            is_active=True,
            effective_date__lte=self.period.end_date
        ).filter(
            models.Q(end_date__isnull=True) | 
            models.Q(end_date__gte=self.period.start_date)
        )
        
        # Clear existing deduction details
        PayrollDeduction.objects.filter(payroll=payroll).delete()
        
        for deduction in deductions:
            deduction_amount = deduction.amount
            
            # If percentage-based deduction
            if deduction.deduction_type.is_percentage:
                gross_salary = payroll.basic_salary + payroll.total_allowances + payroll.overtime_amount
                deduction_amount = gross_salary * (deduction.amount / 100)
                
                # Apply maximum limit if set
                if deduction.deduction_type.max_amount:
                    deduction_amount = min(deduction_amount, deduction.deduction_type.max_amount)
            
            # Create payroll deduction detail
            PayrollDeduction.objects.create(
                payroll=payroll,
                deduction_type=deduction.deduction_type,
                amount=deduction_amount,
                school=payroll.school,
                created_by=payroll.created_by
            )
            total_deductions += deduction_amount
        
        return total_deductions
    
    def _calculate_tax(self, payroll: Payroll):
        """Calculate income tax (simplified calculation)"""
        gross_salary = payroll.basic_salary + payroll.total_allowances + payroll.overtime_amount
        
        # Simple tax calculation (this should be customized based on local tax laws)
        if gross_salary <= 5000:
            return Decimal('0.00')
        elif gross_salary <= 10000:
            return gross_salary * Decimal('0.05')  # 5%
        elif gross_salary <= 20000:
            return gross_salary * Decimal('0.10')  # 10%
        else:
            return gross_salary * Decimal('0.15')  # 15%
    
    def _calculate_insurance(self, payroll: Payroll):
        """Calculate insurance deduction"""
        gross_salary = payroll.basic_salary + payroll.total_allowances + payroll.overtime_amount
        
        # Simple insurance calculation (typically 2-5% of gross salary)
        insurance_rate = Decimal('0.03')  # 3%
        return gross_salary * insurance_rate


class PayslipService:
    """Service for payslip generation and management"""
    
    @staticmethod
    def generate_payslip(payroll: Payroll, user=None):
        """Generate payslip for a payroll record"""
        if not payroll.is_calculated:
            raise ValidationError("Payroll must be calculated before generating payslip")
        
        # Create or get existing payslip
        payslip, created = Payslip.objects.get_or_create(
            payroll=payroll,
            defaults={
                'school': payroll.school,
                'generated_by': user,
                'created_by': user
            }
        )
        
        return payslip
    
    @staticmethod
    def generate_bulk_payslips(payrolls, user=None):
        """Generate payslips for multiple payroll records"""
        results = {
            'success': [],
            'errors': []
        }
        
        for payroll in payrolls:
            try:
                payslip = PayslipService.generate_payslip(payroll, user)
                results['success'].append({
                    'payroll': payroll,
                    'payslip': payslip
                })
            except Exception as e:
                results['errors'].append({
                    'payroll': payroll,
                    'error': str(e)
                })
        
        return results
    
    @staticmethod
    def send_payslip_to_employee(payslip: Payslip):
        """Send payslip to employee via email"""
        try:
            from django.core.mail import send_mail
            from django.template.loader import render_to_string
            from django.conf import settings
            
            employee = payslip.payroll.employee
            
            # Render payslip email template
            subject = f"Payslip for {payslip.payroll.period.name}"
            message = render_to_string('hr/emails/payslip_email.html', {
                'employee': employee,
                'payslip': payslip,
                'payroll': payslip.payroll
            })
            
            # Send email
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[employee.user.email],
                html_message=message
            )
            
            # Mark as sent
            payslip.is_sent = True
            payslip.sent_at = timezone.now()
            payslip.save()
            
            return True
            
        except Exception as e:
            raise ValidationError(f"Error sending payslip: {str(e)}")


class PayrollReportService:
    """Service for payroll reporting"""
    
    @staticmethod
    def generate_payroll_summary(period: PayrollPeriod):
        """Generate payroll summary report for a period"""
        payrolls = Payroll.objects.filter(period=period, is_calculated=True)
        
        summary = {
            'period': period,
            'total_employees': payrolls.count(),
            'total_basic_salary': sum(p.basic_salary for p in payrolls),
            'total_allowances': sum(p.total_allowances for p in payrolls),
            'total_overtime': sum(p.overtime_amount for p in payrolls),
            'total_gross_salary': sum(p.gross_salary for p in payrolls),
            'total_deductions': sum(p.total_deductions for p in payrolls),
            'total_tax': sum(p.tax_deduction for p in payrolls),
            'total_insurance': sum(p.insurance_deduction for p in payrolls),
            'total_net_salary': sum(p.net_salary for p in payrolls),
            'paid_count': payrolls.filter(is_paid=True).count(),
            'unpaid_count': payrolls.filter(is_paid=False).count()
        }
        
        return summary
    
    @staticmethod
    def generate_department_payroll_report(period: PayrollPeriod, department=None):
        """Generate payroll report by department"""
        payrolls = Payroll.objects.filter(period=period, is_calculated=True)
        
        if department:
            payrolls = payrolls.filter(employee__position__department=department)
        
        # Group by department
        from django.db.models import Sum, Count
        from hr.models import Department
        
        departments = Department.objects.filter(
            school=period.school
        ).annotate(
            employee_count=Count('positions__employees__payrolls', 
                               filter=models.Q(positions__employees__payrolls__period=period)),
            total_basic_salary=Sum('positions__employees__payrolls__basic_salary',
                                 filter=models.Q(positions__employees__payrolls__period=period)),
            total_net_salary=Sum('positions__employees__payrolls__net_salary',
                               filter=models.Q(positions__employees__payrolls__period=period))
        )
        
        return departments
    
    @staticmethod
    def generate_employee_payroll_history(employee: Employee, start_date=None, end_date=None):
        """Generate payroll history for an employee"""
        payrolls = Payroll.objects.filter(employee=employee, is_calculated=True)
        
        if start_date:
            payrolls = payrolls.filter(period__start_date__gte=start_date)
        if end_date:
            payrolls = payrolls.filter(period__end_date__lte=end_date)
        
        return payrolls.order_by('-period__start_date')