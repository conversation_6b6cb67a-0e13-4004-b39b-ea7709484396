"""
Payroll management views
"""
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required, permission_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
from django.template.loader import render_to_string
from django.views.decorators.http import require_http_methods
import json
from datetime import datetime, date

from .models import (
    Employee, PayrollPeriod, Payroll, Payslip, SalaryStructure,
    EmployeeSalaryStructure, AllowanceType, DeductionType,
    EmployeeAllowance, EmployeeDeduction, Department
)
from .payroll_services import PayrollCalculationService, PayslipService, PayrollReportService
from .forms import PayrollPeriodForm, SalaryStructureForm, AllowanceTypeForm, DeductionTypeForm


@login_required
@permission_required('hr.view_payroll', raise_exception=True)
def payroll_dashboard(request):
    """Payroll dashboard view"""
    # Get current period
    current_period = PayrollPeriod.objects.filter(
        school=request.user.employee_profile.school,
        is_closed=False
    ).order_by('-start_date').first()
    
    # Get recent payroll periods
    recent_periods = PayrollPeriod.objects.filter(
        school=request.user.employee_profile.school
    ).order_by('-start_date')[:5]
    
    # Get statistics
    stats = {}
    if current_period:
        payrolls = Payroll.objects.filter(period=current_period)
        stats = {
            'total_employees': Employee.objects.filter(
                school=request.user.employee_profile.school,
                employment_status='active'
            ).count(),
            'calculated_payrolls': payrolls.filter(is_calculated=True).count(),
            'paid_payrolls': payrolls.filter(is_paid=True).count(),
            'total_payroll_amount': sum(p.net_salary for p in payrolls.filter(is_calculated=True))
        }
    
    context = {
        'current_period': current_period,
        'recent_periods': recent_periods,
        'stats': stats
    }
    
    return render(request, 'hr/payroll_dashboard.html', context)


@login_required
@permission_required('hr.view_payrollperiod', raise_exception=True)
def payroll_periods(request):
    """List payroll periods"""
    periods = PayrollPeriod.objects.filter(
        school=request.user.employee_profile.school
    ).order_by('-start_date')
    
    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        periods = periods.filter(
            Q(name__icontains=search_query) |
            Q(start_date__icontains=search_query) |
            Q(end_date__icontains=search_query)
        )
    
    # Pagination
    paginator = Paginator(periods, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'search_query': search_query
    }
    
    return render(request, 'hr/payroll_periods.html', context)


@login_required
@permission_required('hr.add_payrollperiod', raise_exception=True)
def create_payroll_period(request):
    """Create new payroll period"""
    if request.method == 'POST':
        form = PayrollPeriodForm(request.POST)
        if form.is_valid():
            period = form.save(commit=False)
            period.school = request.user.employee_profile.school
            period.created_by = request.user
            period.save()
            messages.success(request, 'Payroll period created successfully.')
            return redirect('hr:payroll_periods')
    else:
        form = PayrollPeriodForm()
    
    context = {'form': form}
    return render(request, 'hr/create_payroll_period.html', context)


@login_required
@permission_required('hr.view_payroll', raise_exception=True)
def payroll_list(request, period_id):
    """List payrolls for a specific period"""
    period = get_object_or_404(PayrollPeriod, id=period_id, school=request.user.employee_profile.school)
    
    payrolls = Payroll.objects.filter(period=period).select_related(
        'employee__user', 'employee__position__department'
    ).order_by('employee__user__first_name')
    
    # Filter by department
    department_id = request.GET.get('department')
    if department_id:
        payrolls = payrolls.filter(employee__position__department_id=department_id)
    
    # Filter by status
    status = request.GET.get('status')
    if status == 'calculated':
        payrolls = payrolls.filter(is_calculated=True)
    elif status == 'paid':
        payrolls = payrolls.filter(is_paid=True)
    elif status == 'unpaid':
        payrolls = payrolls.filter(is_calculated=True, is_paid=False)
    
    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        payrolls = payrolls.filter(
            Q(employee__user__first_name__icontains=search_query) |
            Q(employee__user__last_name__icontains=search_query) |
            Q(employee__employee_id__icontains=search_query)
        )
    
    # Pagination
    paginator = Paginator(payrolls, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get departments for filter
    departments = Department.objects.filter(school=request.user.employee_profile.school)
    
    context = {
        'period': period,
        'page_obj': page_obj,
        'departments': departments,
        'search_query': search_query,
        'selected_department': department_id,
        'selected_status': status
    }
    
    return render(request, 'hr/payroll_list.html', context)


@login_required
@permission_required('hr.change_payroll', raise_exception=True)
def calculate_payroll(request, period_id):
    """Calculate payroll for all employees in a period"""
    period = get_object_or_404(PayrollPeriod, id=period_id, school=request.user.employee_profile.school)
    
    if period.is_closed:
        messages.error(request, 'Cannot calculate payroll for a closed period.')
        return redirect('hr:payroll_list', period_id=period.id)
    
    if request.method == 'POST':
        try:
            calculation_service = PayrollCalculationService(period)
            
            # Get selected employees or all active employees
            employee_ids = request.POST.getlist('employee_ids')
            if employee_ids:
                employees = Employee.objects.filter(
                    id__in=employee_ids,
                    school=request.user.employee_profile.school,
                    employment_status='active'
                )
            else:
                employees = Employee.objects.filter(
                    school=request.user.employee_profile.school,
                    employment_status='active'
                )
            
            results = calculation_service.calculate_bulk_payroll(employees, request.user)
            
            success_count = len(results['success'])
            error_count = len(results['errors'])
            
            if success_count > 0:
                messages.success(request, f'Successfully calculated payroll for {success_count} employees.')
            
            if error_count > 0:
                messages.warning(request, f'Failed to calculate payroll for {error_count} employees.')
                for error in results['errors']:
                    messages.error(request, f"{error['employee']}: {error['error']}")
            
        except Exception as e:
            messages.error(request, f'Error calculating payroll: {str(e)}')
    
    return redirect('hr:payroll_list', period_id=period.id)


@login_required
@permission_required('hr.view_payroll', raise_exception=True)
def payroll_detail(request, payroll_id):
    """View payroll details"""
    payroll = get_object_or_404(
        Payroll.objects.select_related(
            'employee__user', 'employee__position__department', 'period'
        ).prefetch_related(
            'allowance_details__allowance_type',
            'deduction_details__deduction_type'
        ),
        id=payroll_id,
        school=request.user.employee_profile.school
    )
    
    context = {'payroll': payroll}
    return render(request, 'hr/payroll_detail.html', context)


@login_required
@permission_required('hr.add_payslip', raise_exception=True)
def generate_payslip(request, payroll_id):
    """Generate payslip for a payroll"""
    payroll = get_object_or_404(Payroll, id=payroll_id, school=request.user.employee_profile.school)
    
    try:
        payslip = PayslipService.generate_payslip(payroll, request.user)
        messages.success(request, 'Payslip generated successfully.')
        return redirect('hr:payslip_detail', payslip_id=payslip.id)
    except Exception as e:
        messages.error(request, f'Error generating payslip: {str(e)}')
        return redirect('hr:payroll_detail', payroll_id=payroll.id)


@login_required
@permission_required('hr.view_payslip', raise_exception=True)
def payslip_detail(request, payslip_id):
    """View payslip details"""
    payslip = get_object_or_404(
        Payslip.objects.select_related(
            'payroll__employee__user',
            'payroll__employee__position__department',
            'payroll__period'
        ).prefetch_related(
            'payroll__allowance_details__allowance_type',
            'payroll__deduction_details__deduction_type'
        ),
        id=payslip_id,
        school=request.user.employee_profile.school
    )
    
    context = {'payslip': payslip}
    return render(request, 'hr/payslip_detail.html', context)


@login_required
@permission_required('hr.view_payslip', raise_exception=True)
def print_payslip(request, payslip_id):
    """Print payslip"""
    payslip = get_object_or_404(
        Payslip.objects.select_related(
            'payroll__employee__user',
            'payroll__employee__position__department',
            'payroll__period'
        ).prefetch_related(
            'payroll__allowance_details__allowance_type',
            'payroll__deduction_details__deduction_type'
        ),
        id=payslip_id,
        school=request.user.employee_profile.school
    )
    
    context = {'payslip': payslip}
    return render(request, 'hr/print_payslip.html', context)


@login_required
@permission_required('hr.view_salarystructure', raise_exception=True)
def salary_structures(request):
    """List salary structures"""
    structures = SalaryStructure.objects.filter(
        school=request.user.employee_profile.school
    ).select_related('position__department').order_by('-effective_date')
    
    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        structures = structures.filter(
            Q(name__icontains=search_query) |
            Q(position__title__icontains=search_query)
        )
    
    # Pagination
    paginator = Paginator(structures, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'search_query': search_query
    }
    
    return render(request, 'hr/salary_structures.html', context)


@login_required
@permission_required('hr.add_salarystructure', raise_exception=True)
def create_salary_structure(request):
    """Create new salary structure"""
    if request.method == 'POST':
        form = SalaryStructureForm(request.POST)
        if form.is_valid():
            structure = form.save(commit=False)
            structure.school = request.user.employee_profile.school
            structure.created_by = request.user
            structure.save()
            messages.success(request, 'Salary structure created successfully.')
            return redirect('hr:salary_structures')
    else:
        form = SalaryStructureForm()
    
    context = {'form': form}
    return render(request, 'hr/create_salary_structure.html', context)


@login_required
@permission_required('hr.view_allowancetype', raise_exception=True)
def allowance_types(request):
    """List allowance types"""
    allowances = AllowanceType.objects.filter(
        school=request.user.employee_profile.school
    ).order_by('name')
    
    context = {'allowances': allowances}
    return render(request, 'hr/allowance_types.html', context)


@login_required
@permission_required('hr.view_deductiontype', raise_exception=True)
def deduction_types(request):
    """List deduction types"""
    deductions = DeductionType.objects.filter(
        school=request.user.employee_profile.school
    ).order_by('name')
    
    context = {'deductions': deductions}
    return render(request, 'hr/deduction_types.html', context)


@login_required
@permission_required('hr.view_payroll', raise_exception=True)
def payroll_reports(request):
    """Payroll reports dashboard"""
    periods = PayrollPeriod.objects.filter(
        school=request.user.employee_profile.school
    ).order_by('-start_date')[:12]
    
    context = {'periods': periods}
    return render(request, 'hr/payroll_reports.html', context)


@login_required
@permission_required('hr.view_payroll', raise_exception=True)
def payroll_summary_report(request, period_id):
    """Generate payroll summary report"""
    period = get_object_or_404(PayrollPeriod, id=period_id, school=request.user.employee_profile.school)
    
    summary = PayrollReportService.generate_payroll_summary(period)
    
    context = {
        'period': period,
        'summary': summary
    }
    
    return render(request, 'hr/payroll_summary_report.html', context)


@login_required
@permission_required('hr.view_payroll', raise_exception=True)
def department_payroll_report(request, period_id):
    """Generate department-wise payroll report"""
    period = get_object_or_404(PayrollPeriod, id=period_id, school=request.user.employee_profile.school)
    
    departments = PayrollReportService.generate_department_payroll_report(period)
    
    context = {
        'period': period,
        'departments': departments
    }
    
    return render(request, 'hr/department_payroll_report.html', context)


@login_required
@permission_required('hr.view_payroll', raise_exception=True)
def employee_payroll_history(request, employee_id):
    """View employee payroll history"""
    employee = get_object_or_404(Employee, id=employee_id, school=request.user.employee_profile.school)
    
    # Get date range from request
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    
    if start_date:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
    if end_date:
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
    
    payrolls = PayrollReportService.generate_employee_payroll_history(
        employee, start_date, end_date
    )
    
    context = {
        'employee': employee,
        'payrolls': payrolls,
        'start_date': start_date,
        'end_date': end_date
    }
    
    return render(request, 'hr/employee_payroll_history.html', context)


# AJAX Views
@login_required
@require_http_methods(["POST"])
def mark_payroll_paid(request, payroll_id):
    """Mark payroll as paid (AJAX)"""
    payroll = get_object_or_404(Payroll, id=payroll_id, school=request.user.employee_profile.school)
    
    if not payroll.is_calculated:
        return JsonResponse({'success': False, 'error': 'Payroll must be calculated first'})
    
    payroll.is_paid = True
    payroll.paid_date = timezone.now().date()
    payroll.paid_by = request.user
    payroll.save()
    
    return JsonResponse({'success': True, 'message': 'Payroll marked as paid'})


@login_required
@require_http_methods(["POST"])
def send_payslip_email(request, payslip_id):
    """Send payslip via email (AJAX)"""
    payslip = get_object_or_404(Payslip, id=payslip_id, school=request.user.employee_profile.school)
    
    try:
        PayslipService.send_payslip_to_employee(payslip)
        return JsonResponse({'success': True, 'message': 'Payslip sent successfully'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
def get_employees_for_payroll(request, period_id):
    """Get employees list for payroll calculation (AJAX)"""
    period = get_object_or_404(PayrollPeriod, id=period_id, school=request.user.employee_profile.school)
    
    employees = Employee.objects.filter(
        school=request.user.employee_profile.school,
        employment_status='active'
    ).select_related('user', 'position__department')
    
    # Check which employees already have calculated payroll
    calculated_payrolls = Payroll.objects.filter(
        period=period,
        is_calculated=True
    ).values_list('employee_id', flat=True)
    
    employee_data = []
    for employee in employees:
        employee_data.append({
            'id': employee.id,
            'name': f"{employee.user.first_name} {employee.user.last_name}",
            'employee_id': employee.employee_id,
            'department': employee.position.department.name,
            'is_calculated': employee.id in calculated_payrolls
        })
    
    return JsonResponse({'employees': employee_data})