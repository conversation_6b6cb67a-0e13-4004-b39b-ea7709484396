"""
Performance management services
"""
from decimal import Decimal
from datetime import datetime, date, timedelta
from django.db import transaction, models
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.db.models import Q, Avg, Count, Sum
from .models import (
    Employee, PerformanceEvaluation, Department, Position
)


class PerformanceEvaluationService:
    """Service for managing performance evaluations"""
    
    @staticmethod
    def create_evaluation(employee: Employee, evaluator, evaluation_period_start: date,
                         evaluation_period_end: date, ratings: dict, 
                         strengths: str, areas_for_improvement: str,
                         goals_for_next_period: str, user=None):
        """Create a new performance evaluation"""
        try:
            with transaction.atomic():
                # Validate evaluation period
                if evaluation_period_start >= evaluation_period_end:
                    raise ValidationError("Start date must be before end date")
                
                # Check for overlapping evaluations
                overlapping = PerformanceEvaluation.objects.filter(
                    employee=employee,
                    evaluation_period_start__lte=evaluation_period_end,
                    evaluation_period_end__gte=evaluation_period_start
                )
                
                if overlapping.exists():
                    raise ValidationError("Overlapping evaluation period exists")
                
                # Calculate overall rating
                rating_values = [
                    ratings.get('quality_of_work', 3),
                    ratings.get('productivity', 3),
                    ratings.get('communication', 3),
                    ratings.get('teamwork', 3),
                    ratings.get('punctuality', 3),
                    ratings.get('initiative', 3)
                ]
                overall_rating = round(sum(rating_values) / len(rating_values))
                
                # Create evaluation
                evaluation = PerformanceEvaluation.objects.create(
                    school=employee.school,
                    employee=employee,
                    evaluator=evaluator,
                    evaluation_period_start=evaluation_period_start,
                    evaluation_period_end=evaluation_period_end,
                    quality_of_work=ratings.get('quality_of_work', 3),
                    productivity=ratings.get('productivity', 3),
                    communication=ratings.get('communication', 3),
                    teamwork=ratings.get('teamwork', 3),
                    punctuality=ratings.get('punctuality', 3),
                    initiative=ratings.get('initiative', 3),
                    overall_rating=overall_rating,
                    strengths=strengths,
                    areas_for_improvement=areas_for_improvement,
                    goals_for_next_period=goals_for_next_period,
                    created_by=user
                )
                
                return evaluation
                
        except Exception as e:
            raise ValidationError(f"Error creating evaluation: {str(e)}")
    
    @staticmethod
    def update_evaluation(evaluation: PerformanceEvaluation, ratings: dict,
                         strengths: str, areas_for_improvement: str,
                         goals_for_next_period: str, employee_comments=None):
        """Update an existing performance evaluation"""
        try:
            with transaction.atomic():
                if evaluation.is_finalized:
                    raise ValidationError("Cannot update finalized evaluation")
                
                # Update ratings
                evaluation.quality_of_work = ratings.get('quality_of_work', evaluation.quality_of_work)
                evaluation.productivity = ratings.get('productivity', evaluation.productivity)
                evaluation.communication = ratings.get('communication', evaluation.communication)
                evaluation.teamwork = ratings.get('teamwork', evaluation.teamwork)
                evaluation.punctuality = ratings.get('punctuality', evaluation.punctuality)
                evaluation.initiative = ratings.get('initiative', evaluation.initiative)
                
                # Recalculate overall rating
                rating_values = [
                    evaluation.quality_of_work,
                    evaluation.productivity,
                    evaluation.communication,
                    evaluation.teamwork,
                    evaluation.punctuality,
                    evaluation.initiative
                ]
                evaluation.overall_rating = round(sum(rating_values) / len(rating_values))
                
                # Update text fields
                evaluation.strengths = strengths
                evaluation.areas_for_improvement = areas_for_improvement
                evaluation.goals_for_next_period = goals_for_next_period
                
                if employee_comments is not None:
                    evaluation.employee_comments = employee_comments
                
                evaluation.save()
                
                return evaluation
                
        except Exception as e:
            raise ValidationError(f"Error updating evaluation: {str(e)}")
    
    @staticmethod
    def finalize_evaluation(evaluation: PerformanceEvaluation, user=None):
        """Finalize a performance evaluation"""
        try:
            with transaction.atomic():
                if evaluation.is_finalized:
                    raise ValidationError("Evaluation is already finalized")
                
                evaluation.is_finalized = True
                evaluation.finalized_at = timezone.now()
                evaluation.save()
                
                return evaluation
                
        except Exception as e:
            raise ValidationError(f"Error finalizing evaluation: {str(e)}")
    
    @staticmethod
    def get_employee_evaluations(employee: Employee, year=None):
        """Get evaluations for an employee"""
        evaluations = PerformanceEvaluation.objects.filter(employee=employee)
        
        if year:
            evaluations = evaluations.filter(evaluation_period_end__year=year)
        
        return evaluations.order_by('-evaluation_period_end')
    
    @staticmethod
    def get_pending_evaluations(school, department=None, evaluator=None):
        """Get pending evaluations (not finalized)"""
        evaluations = PerformanceEvaluation.objects.filter(
            school=school,
            is_finalized=False
        ).select_related(
            'employee__user',
            'employee__position__department',
            'evaluator'
        )
        
        if department:
            evaluations = evaluations.filter(employee__position__department=department)
        
        if evaluator:
            evaluations = evaluations.filter(evaluator=evaluator)
        
        return evaluations.order_by('evaluation_period_end')
    
    @staticmethod
    def get_due_evaluations(school, department=None):
        """Get employees who are due for evaluation"""
        # Get employees who haven't been evaluated in the last 12 months
        one_year_ago = timezone.now().date() - timedelta(days=365)
        
        employees = Employee.objects.filter(
            school=school,
            employment_status='active'
        ).select_related('user', 'position__department')
        
        if department:
            employees = employees.filter(position__department=department)
        
        due_employees = []
        for employee in employees:
            last_evaluation = PerformanceEvaluation.objects.filter(
                employee=employee,
                is_finalized=True
            ).order_by('-evaluation_period_end').first()
            
            if not last_evaluation or last_evaluation.evaluation_period_end < one_year_ago:
                due_employees.append({
                    'employee': employee,
                    'last_evaluation': last_evaluation,
                    'days_overdue': (timezone.now().date() - (
                        last_evaluation.evaluation_period_end if last_evaluation 
                        else employee.hire_date
                    )).days - 365 if last_evaluation else (
                        timezone.now().date() - employee.hire_date
                    ).days - 365
                })
        
        return due_employees


class PerformanceAnalyticsService:
    """Service for performance analytics and reporting"""
    
    @staticmethod
    def get_employee_performance_summary(employee: Employee, year=None):
        """Get performance summary for an employee"""
        evaluations = PerformanceEvaluationService.get_employee_evaluations(employee, year)
        
        if not evaluations.exists():
            return {
                'employee': employee,
                'year': year,
                'total_evaluations': 0,
                'average_ratings': {},
                'performance_trend': [],
                'latest_evaluation': None
            }
        
        # Calculate average ratings
        avg_ratings = evaluations.aggregate(
            avg_quality=Avg('quality_of_work'),
            avg_productivity=Avg('productivity'),
            avg_communication=Avg('communication'),
            avg_teamwork=Avg('teamwork'),
            avg_punctuality=Avg('punctuality'),
            avg_initiative=Avg('initiative'),
            avg_overall=Avg('overall_rating')
        )
        
        # Get performance trend (last 5 evaluations)
        recent_evaluations = evaluations[:5]
        performance_trend = []
        for eval in recent_evaluations:
            performance_trend.append({
                'date': eval.evaluation_period_end,
                'overall_rating': eval.overall_rating,
                'average_rating': eval.average_rating
            })
        
        return {
            'employee': employee,
            'year': year,
            'total_evaluations': evaluations.count(),
            'average_ratings': {
                'quality_of_work': round(avg_ratings['avg_quality'] or 0, 2),
                'productivity': round(avg_ratings['avg_productivity'] or 0, 2),
                'communication': round(avg_ratings['avg_communication'] or 0, 2),
                'teamwork': round(avg_ratings['avg_teamwork'] or 0, 2),
                'punctuality': round(avg_ratings['avg_punctuality'] or 0, 2),
                'initiative': round(avg_ratings['avg_initiative'] or 0, 2),
                'overall': round(avg_ratings['avg_overall'] or 0, 2)
            },
            'performance_trend': performance_trend,
            'latest_evaluation': evaluations.first()
        }
    
    @staticmethod
    def get_department_performance_summary(department: Department, year=None):
        """Get performance summary for a department"""
        employees = Employee.objects.filter(
            position__department=department,
            employment_status='active'
        )
        
        evaluations = PerformanceEvaluation.objects.filter(
            employee__position__department=department,
            is_finalized=True
        )
        
        if year:
            evaluations = evaluations.filter(evaluation_period_end__year=year)
        
        if not evaluations.exists():
            return {
                'department': department,
                'year': year,
                'total_employees': employees.count(),
                'evaluated_employees': 0,
                'evaluation_completion_rate': 0,
                'average_ratings': {},
                'performance_distribution': {},
                'top_performers': [],
                'improvement_needed': []
            }
        
        # Calculate average ratings
        avg_ratings = evaluations.aggregate(
            avg_quality=Avg('quality_of_work'),
            avg_productivity=Avg('productivity'),
            avg_communication=Avg('communication'),
            avg_teamwork=Avg('teamwork'),
            avg_punctuality=Avg('punctuality'),
            avg_initiative=Avg('initiative'),
            avg_overall=Avg('overall_rating')
        )
        
        # Get performance distribution
        performance_distribution = {
            'excellent': evaluations.filter(overall_rating=5).count(),
            'good': evaluations.filter(overall_rating=4).count(),
            'average': evaluations.filter(overall_rating=3).count(),
            'below_average': evaluations.filter(overall_rating=2).count(),
            'poor': evaluations.filter(overall_rating=1).count()
        }
        
        # Get top performers (rating 4 or 5)
        top_performers = evaluations.filter(
            overall_rating__gte=4
        ).select_related('employee__user').order_by('-overall_rating')[:10]
        
        # Get employees needing improvement (rating 1 or 2)
        improvement_needed = evaluations.filter(
            overall_rating__lte=2
        ).select_related('employee__user').order_by('overall_rating')[:10]
        
        # Calculate evaluation completion rate
        evaluated_employees = evaluations.values('employee').distinct().count()
        completion_rate = (evaluated_employees / employees.count() * 100) if employees.count() > 0 else 0
        
        return {
            'department': department,
            'year': year,
            'total_employees': employees.count(),
            'evaluated_employees': evaluated_employees,
            'evaluation_completion_rate': round(completion_rate, 2),
            'average_ratings': {
                'quality_of_work': round(avg_ratings['avg_quality'] or 0, 2),
                'productivity': round(avg_ratings['avg_productivity'] or 0, 2),
                'communication': round(avg_ratings['avg_communication'] or 0, 2),
                'teamwork': round(avg_ratings['avg_teamwork'] or 0, 2),
                'punctuality': round(avg_ratings['avg_punctuality'] or 0, 2),
                'initiative': round(avg_ratings['avg_initiative'] or 0, 2),
                'overall': round(avg_ratings['avg_overall'] or 0, 2)
            },
            'performance_distribution': performance_distribution,
            'top_performers': top_performers,
            'improvement_needed': improvement_needed
        }
    
    @staticmethod
    def get_school_performance_overview(school, year=None):
        """Get overall performance overview for the school"""
        evaluations = PerformanceEvaluation.objects.filter(
            school=school,
            is_finalized=True
        )
        
        if year:
            evaluations = evaluations.filter(evaluation_period_end__year=year)
        
        total_employees = Employee.objects.filter(
            school=school,
            employment_status='active'
        ).count()
        
        if not evaluations.exists():
            return {
                'school': school,
                'year': year,
                'total_employees': total_employees,
                'total_evaluations': 0,
                'evaluation_completion_rate': 0,
                'average_overall_rating': 0,
                'performance_distribution': {},
                'department_performance': []
            }
        
        # Calculate overall statistics
        evaluated_employees = evaluations.values('employee').distinct().count()
        completion_rate = (evaluated_employees / total_employees * 100) if total_employees > 0 else 0
        
        avg_overall_rating = evaluations.aggregate(
            avg=Avg('overall_rating')
        )['avg'] or 0
        
        # Performance distribution
        performance_distribution = {
            'excellent': evaluations.filter(overall_rating=5).count(),
            'good': evaluations.filter(overall_rating=4).count(),
            'average': evaluations.filter(overall_rating=3).count(),
            'below_average': evaluations.filter(overall_rating=2).count(),
            'poor': evaluations.filter(overall_rating=1).count()
        }
        
        # Department performance
        departments = Department.objects.filter(school=school)
        department_performance = []
        
        for dept in departments:
            dept_summary = PerformanceAnalyticsService.get_department_performance_summary(dept, year)
            department_performance.append(dept_summary)
        
        return {
            'school': school,
            'year': year,
            'total_employees': total_employees,
            'total_evaluations': evaluations.count(),
            'evaluation_completion_rate': round(completion_rate, 2),
            'average_overall_rating': round(avg_overall_rating, 2),
            'performance_distribution': performance_distribution,
            'department_performance': department_performance
        }
    
    @staticmethod
    def get_performance_trends(school, start_year, end_year):
        """Get performance trends over multiple years"""
        trends = {}
        
        for year in range(start_year, end_year + 1):
            evaluations = PerformanceEvaluation.objects.filter(
                school=school,
                evaluation_period_end__year=year,
                is_finalized=True
            )
            
            if evaluations.exists():
                avg_rating = evaluations.aggregate(avg=Avg('overall_rating'))['avg'] or 0
                total_evaluations = evaluations.count()
                
                # Performance distribution
                distribution = {
                    'excellent': evaluations.filter(overall_rating=5).count(),
                    'good': evaluations.filter(overall_rating=4).count(),
                    'average': evaluations.filter(overall_rating=3).count(),
                    'below_average': evaluations.filter(overall_rating=2).count(),
                    'poor': evaluations.filter(overall_rating=1).count()
                }
                
                trends[year] = {
                    'average_rating': round(avg_rating, 2),
                    'total_evaluations': total_evaluations,
                    'distribution': distribution
                }
            else:
                trends[year] = {
                    'average_rating': 0,
                    'total_evaluations': 0,
                    'distribution': {
                        'excellent': 0, 'good': 0, 'average': 0,
                        'below_average': 0, 'poor': 0
                    }
                }
        
        return trends


class GoalManagementService:
    """Service for managing employee goals and objectives"""
    
    @staticmethod
    def extract_goals_from_evaluation(evaluation: PerformanceEvaluation):
        """Extract goals from evaluation for tracking"""
        goals = []
        
        # Parse goals from the goals_for_next_period text
        # This is a simple implementation - could be enhanced with structured goal storage
        goal_lines = evaluation.goals_for_next_period.split('\n')
        
        for i, line in enumerate(goal_lines):
            line = line.strip()
            if line and not line.startswith('-') and not line.startswith('•'):
                goals.append({
                    'id': i + 1,
                    'description': line,
                    'evaluation': evaluation,
                    'status': 'pending',
                    'created_date': evaluation.finalized_at or evaluation.created_at
                })
        
        return goals
    
    @staticmethod
    def get_employee_goals(employee: Employee, year=None):
        """Get goals for an employee from their evaluations"""
        evaluations = PerformanceEvaluationService.get_employee_evaluations(employee, year)
        
        all_goals = []
        for evaluation in evaluations:
            goals = GoalManagementService.extract_goals_from_evaluation(evaluation)
            all_goals.extend(goals)
        
        return all_goals
    
    @staticmethod
    def generate_improvement_plan(evaluation: PerformanceEvaluation):
        """Generate improvement plan based on evaluation"""
        improvement_areas = []
        
        # Identify areas with low ratings (1 or 2)
        criteria = {
            'Quality of Work': evaluation.quality_of_work,
            'Productivity': evaluation.productivity,
            'Communication': evaluation.communication,
            'Teamwork': evaluation.teamwork,
            'Punctuality': evaluation.punctuality,
            'Initiative': evaluation.initiative
        }
        
        for criterion, rating in criteria.items():
            if rating <= 2:
                improvement_areas.append({
                    'area': criterion,
                    'current_rating': rating,
                    'target_rating': min(rating + 2, 5),
                    'priority': 'high' if rating == 1 else 'medium'
                })
        
        # Generate action items based on areas for improvement text
        action_items = []
        improvement_text = evaluation.areas_for_improvement
        
        if improvement_text:
            # Simple parsing - could be enhanced
            items = improvement_text.split('\n')
            for item in items:
                item = item.strip()
                if item and not item.startswith('-') and not item.startswith('•'):
                    action_items.append({
                        'description': item,
                        'timeline': '3 months',  # Default timeline
                        'status': 'pending'
                    })
        
        return {
            'evaluation': evaluation,
            'improvement_areas': improvement_areas,
            'action_items': action_items,
            'review_date': evaluation.evaluation_period_end + timedelta(days=90),
            'created_date': timezone.now().date()
        }