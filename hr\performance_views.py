"""
Performance management views
"""
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required, permission_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
from django.template.loader import render_to_string
from django.views.decorators.http import require_http_methods
import json
from datetime import datetime, date, timedelta

from .models import (
    Employee, PerformanceEvaluation, Department
)
from .performance_services import (
    PerformanceEvaluationService, PerformanceAnalyticsService, GoalManagementService
)
from .forms import PerformanceEvaluationForm


@login_required
@permission_required('hr.view_performanceevaluation', raise_exception=True)
def performance_dashboard(request):
    """Performance management dashboard"""
    try:
        employee = request.user.employee_profile
        school = employee.school
    except:
        messages.error(request, 'Employee profile not found.')
        return redirect('core:dashboard')
    
    current_year = timezone.now().year
    
    # Get school performance overview
    school_overview = PerformanceAnalyticsService.get_school_performance_overview(
        school, current_year
    )
    
    # Get due evaluations
    due_evaluations = PerformanceEvaluationService.get_due_evaluations(school)[:10]
    
    # Get pending evaluations
    pending_evaluations = PerformanceEvaluationService.get_pending_evaluations(school)[:10]
    
    # Get user's own performance summary
    user_performance = None
    if hasattr(request.user, 'employee_profile'):
        user_performance = PerformanceAnalyticsService.get_employee_performance_summary(
            request.user.employee_profile, current_year
        )
    
    context = {
        'school_overview': school_overview,
        'due_evaluations': due_evaluations,
        'pending_evaluations': pending_evaluations,
        'user_performance': user_performance,
        'current_year': current_year
    }
    
    return render(request, 'hr/performance_dashboard.html', context)


@login_required
@permission_required('hr.view_performanceevaluation', raise_exception=True)
def evaluation_list(request):
    """List performance evaluations"""
    try:
        employee = request.user.employee_profile
        school = employee.school
    except:
        messages.error(request, 'Employee profile not found.')
        return redirect('core:dashboard')
    
    # Get filter parameters
    department_id = request.GET.get('department')
    employee_id = request.GET.get('employee')
    year = request.GET.get('year', timezone.now().year)
    status = request.GET.get('status', '')
    search_query = request.GET.get('search', '')
    
    try:
        year = int(year)
    except:
        year = timezone.now().year
    
    # Build queryset
    evaluations = PerformanceEvaluation.objects.filter(
        school=school
    ).select_related(
        'employee__user',
        'employee__position__department',
        'evaluator'
    )
    
    # Apply filters
    if department_id:
        evaluations = evaluations.filter(employee__position__department_id=department_id)
    
    if employee_id:
        evaluations = evaluations.filter(employee_id=employee_id)
    
    if year:
        evaluations = evaluations.filter(evaluation_period_end__year=year)
    
    if status == 'finalized':
        evaluations = evaluations.filter(is_finalized=True)
    elif status == 'pending':
        evaluations = evaluations.filter(is_finalized=False)
    
    if search_query:
        evaluations = evaluations.filter(
            Q(employee__user__first_name__icontains=search_query) |
            Q(employee__user__last_name__icontains=search_query) |
            Q(employee__employee_id__icontains=search_query) |
            Q(evaluator__first_name__icontains=search_query) |
            Q(evaluator__last_name__icontains=search_query)
        )
    
    evaluations = evaluations.order_by('-evaluation_period_end')
    
    # Pagination
    paginator = Paginator(evaluations, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get filter options
    departments = Department.objects.filter(school=school)
    employees = Employee.objects.filter(
        school=school,
        employment_status='active'
    ).select_related('user')
    
    context = {
        'page_obj': page_obj,
        'departments': departments,
        'employees': employees,
        'selected_department': department_id,
        'selected_employee': employee_id,
        'selected_year': year,
        'selected_status': status,
        'search_query': search_query,
        'years': range(year - 2, year + 2)
    }
    
    return render(request, 'hr/evaluation_list.html', context)


@login_required
@permission_required('hr.add_performanceevaluation', raise_exception=True)
def create_evaluation(request, employee_id=None):
    """Create new performance evaluation"""
    try:
        user_employee = request.user.employee_profile
        school = user_employee.school
    except:
        messages.error(request, 'Employee profile not found.')
        return redirect('core:dashboard')
    
    # Get employee to evaluate
    if employee_id:
        employee = get_object_or_404(Employee, id=employee_id, school=school)
    else:
        employee = None
    
    if request.method == 'POST':
        form = PerformanceEvaluationForm(request.POST)
        if form.is_valid():
            try:
                # Extract ratings from form
                ratings = {
                    'quality_of_work': form.cleaned_data['quality_of_work'],
                    'productivity': form.cleaned_data['productivity'],
                    'communication': form.cleaned_data['communication'],
                    'teamwork': form.cleaned_data['teamwork'],
                    'punctuality': form.cleaned_data['punctuality'],
                    'initiative': form.cleaned_data['initiative']
                }
                
                evaluation = PerformanceEvaluationService.create_evaluation(
                    employee=form.cleaned_data['employee'],
                    evaluator=request.user,
                    evaluation_period_start=form.cleaned_data['evaluation_period_start'],
                    evaluation_period_end=form.cleaned_data['evaluation_period_end'],
                    ratings=ratings,
                    strengths=form.cleaned_data['strengths'],
                    areas_for_improvement=form.cleaned_data['areas_for_improvement'],
                    goals_for_next_period=form.cleaned_data['goals_for_next_period'],
                    user=request.user
                )
                
                messages.success(request, 'Performance evaluation created successfully.')
                return redirect('hr:evaluation_detail', evaluation_id=evaluation.id)
                
            except Exception as e:
                messages.error(request, str(e))
    else:
        form = PerformanceEvaluationForm()
        # Filter employees by school
        form.fields['employee'].queryset = Employee.objects.filter(
            school=school,
            employment_status='active'
        )
        
        # Pre-select employee if provided
        if employee:
            form.fields['employee'].initial = employee
    
    context = {
        'form': form,
        'employee': employee
    }
    
    return render(request, 'hr/create_evaluation.html', context)


@login_required
@permission_required('hr.view_performanceevaluation', raise_exception=True)
def evaluation_detail(request, evaluation_id):
    """View evaluation details"""
    evaluation = get_object_or_404(
        PerformanceEvaluation.objects.select_related(
            'employee__user',
            'employee__position__department',
            'evaluator'
        ),
        id=evaluation_id
    )
    
    # Check permissions
    if not (request.user.has_perm('hr.change_performanceevaluation') or 
            evaluation.employee.user == request.user or
            evaluation.evaluator == request.user):
        messages.error(request, 'You do not have permission to view this evaluation.')
        return redirect('hr:evaluation_list')
    
    # Get goals extracted from evaluation
    goals = GoalManagementService.extract_goals_from_evaluation(evaluation)
    
    # Get improvement plan if needed
    improvement_plan = None
    if evaluation.overall_rating <= 3:
        improvement_plan = GoalManagementService.generate_improvement_plan(evaluation)
    
    context = {
        'evaluation': evaluation,
        'goals': goals,
        'improvement_plan': improvement_plan
    }
    
    return render(request, 'hr/evaluation_detail.html', context)


@login_required
@permission_required('hr.change_performanceevaluation', raise_exception=True)
def edit_evaluation(request, evaluation_id):
    """Edit performance evaluation"""
    evaluation = get_object_or_404(PerformanceEvaluation, id=evaluation_id)
    
    # Check permissions
    if not (evaluation.evaluator == request.user or 
            request.user.has_perm('hr.change_performanceevaluation')):
        messages.error(request, 'You do not have permission to edit this evaluation.')
        return redirect('hr:evaluation_detail', evaluation_id=evaluation.id)
    
    if evaluation.is_finalized:
        messages.error(request, 'Cannot edit finalized evaluation.')
        return redirect('hr:evaluation_detail', evaluation_id=evaluation.id)
    
    if request.method == 'POST':
        form = PerformanceEvaluationForm(request.POST, instance=evaluation)
        if form.is_valid():
            try:
                # Extract ratings from form
                ratings = {
                    'quality_of_work': form.cleaned_data['quality_of_work'],
                    'productivity': form.cleaned_data['productivity'],
                    'communication': form.cleaned_data['communication'],
                    'teamwork': form.cleaned_data['teamwork'],
                    'punctuality': form.cleaned_data['punctuality'],
                    'initiative': form.cleaned_data['initiative']
                }
                
                PerformanceEvaluationService.update_evaluation(
                    evaluation=evaluation,
                    ratings=ratings,
                    strengths=form.cleaned_data['strengths'],
                    areas_for_improvement=form.cleaned_data['areas_for_improvement'],
                    goals_for_next_period=form.cleaned_data['goals_for_next_period'],
                    employee_comments=form.cleaned_data.get('employee_comments')
                )
                
                messages.success(request, 'Evaluation updated successfully.')
                return redirect('hr:evaluation_detail', evaluation_id=evaluation.id)
                
            except Exception as e:
                messages.error(request, str(e))
    else:
        form = PerformanceEvaluationForm(instance=evaluation)
    
    context = {
        'form': form,
        'evaluation': evaluation
    }
    
    return render(request, 'hr/edit_evaluation.html', context)


@login_required
@permission_required('hr.change_performanceevaluation', raise_exception=True)
def finalize_evaluation(request, evaluation_id):
    """Finalize performance evaluation"""
    evaluation = get_object_or_404(PerformanceEvaluation, id=evaluation_id)
    
    # Check permissions
    if not (evaluation.evaluator == request.user or 
            request.user.has_perm('hr.change_performanceevaluation')):
        messages.error(request, 'You do not have permission to finalize this evaluation.')
        return redirect('hr:evaluation_detail', evaluation_id=evaluation.id)
    
    if request.method == 'POST':
        try:
            PerformanceEvaluationService.finalize_evaluation(evaluation, request.user)
            messages.success(request, 'Evaluation finalized successfully.')
        except Exception as e:
            messages.error(request, str(e))
    
    return redirect('hr:evaluation_detail', evaluation_id=evaluation.id)


@login_required
@permission_required('hr.view_performanceevaluation', raise_exception=True)
def my_evaluations(request):
    """View user's own evaluations"""
    try:
        employee = request.user.employee_profile
    except:
        messages.error(request, 'Employee profile not found.')
        return redirect('core:dashboard')
    
    # Get filter parameters
    year = request.GET.get('year', timezone.now().year)
    
    try:
        year = int(year)
    except:
        year = timezone.now().year
    
    # Get evaluations
    evaluations = PerformanceEvaluationService.get_employee_evaluations(employee, year)
    
    # Get performance summary
    performance_summary = PerformanceAnalyticsService.get_employee_performance_summary(
        employee, year
    )
    
    # Get goals
    goals = GoalManagementService.get_employee_goals(employee, year)
    
    context = {
        'evaluations': evaluations,
        'performance_summary': performance_summary,
        'goals': goals,
        'selected_year': year,
        'years': range(year - 2, year + 2)
    }
    
    return render(request, 'hr/my_evaluations.html', context)


@login_required
@permission_required('hr.view_performanceevaluation', raise_exception=True)
def due_evaluations(request):
    """View employees due for evaluation"""
    try:
        employee = request.user.employee_profile
        school = employee.school
    except:
        messages.error(request, 'Employee profile not found.')
        return redirect('core:dashboard')
    
    # Get filter parameters
    department_id = request.GET.get('department')
    
    # Get due evaluations
    due_employees = PerformanceEvaluationService.get_due_evaluations(
        school, 
        Department.objects.get(id=department_id) if department_id else None
    )
    
    # Pagination
    paginator = Paginator(due_employees, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get departments for filter
    departments = Department.objects.filter(school=school)
    
    context = {
        'page_obj': page_obj,
        'departments': departments,
        'selected_department': department_id
    }
    
    return render(request, 'hr/due_evaluations.html', context)


@login_required
@permission_required('hr.view_performanceevaluation', raise_exception=True)
def performance_reports(request):
    """Performance reports dashboard"""
    try:
        employee = request.user.employee_profile
        school = employee.school
    except:
        messages.error(request, 'Employee profile not found.')
        return redirect('core:dashboard')
    
    current_year = timezone.now().year
    years = range(current_year - 4, current_year + 1)
    
    context = {
        'years': years,
        'current_year': current_year
    }
    
    return render(request, 'hr/performance_reports.html', context)


@login_required
@permission_required('hr.view_performanceevaluation', raise_exception=True)
def school_performance_report(request):
    """School performance overview report"""
    try:
        employee = request.user.employee_profile
        school = employee.school
    except:
        messages.error(request, 'Employee profile not found.')
        return redirect('core:dashboard')
    
    year = int(request.GET.get('year', timezone.now().year))
    
    overview = PerformanceAnalyticsService.get_school_performance_overview(school, year)
    
    context = {
        'overview': overview,
        'year': year
    }
    
    return render(request, 'hr/school_performance_report.html', context)


@login_required
@permission_required('hr.view_performanceevaluation', raise_exception=True)
def department_performance_report(request, department_id):
    """Department performance report"""
    try:
        employee = request.user.employee_profile
        school = employee.school
    except:
        messages.error(request, 'Employee profile not found.')
        return redirect('core:dashboard')
    
    department = get_object_or_404(Department, id=department_id, school=school)
    year = int(request.GET.get('year', timezone.now().year))
    
    summary = PerformanceAnalyticsService.get_department_performance_summary(
        department, year
    )
    
    context = {
        'summary': summary,
        'year': year
    }
    
    return render(request, 'hr/department_performance_report.html', context)


@login_required
@permission_required('hr.view_performanceevaluation', raise_exception=True)
def employee_performance_report(request, employee_id):
    """Individual employee performance report"""
    employee = get_object_or_404(Employee, id=employee_id)
    
    # Check permission
    if not (request.user.has_perm('hr.view_employee') or 
            employee.user == request.user):
        messages.error(request, 'You do not have permission to view this report.')
        return redirect('hr:performance_reports')
    
    year = int(request.GET.get('year', timezone.now().year))
    
    summary = PerformanceAnalyticsService.get_employee_performance_summary(
        employee, year
    )
    
    # Get goals and improvement plans
    goals = GoalManagementService.get_employee_goals(employee, year)
    
    improvement_plans = []
    for evaluation in summary['performance_trend']:
        if evaluation.get('overall_rating', 0) <= 3:
            plan = GoalManagementService.generate_improvement_plan(evaluation)
            improvement_plans.append(plan)
    
    context = {
        'summary': summary,
        'goals': goals,
        'improvement_plans': improvement_plans,
        'year': year
    }
    
    return render(request, 'hr/employee_performance_report.html', context)


@login_required
@permission_required('hr.view_performanceevaluation', raise_exception=True)
def performance_trends_report(request):
    """Performance trends over multiple years"""
    try:
        employee = request.user.employee_profile
        school = employee.school
    except:
        messages.error(request, 'Employee profile not found.')
        return redirect('core:dashboard')
    
    current_year = timezone.now().year
    start_year = int(request.GET.get('start_year', current_year - 4))
    end_year = int(request.GET.get('end_year', current_year))
    
    trends = PerformanceAnalyticsService.get_performance_trends(
        school, start_year, end_year
    )
    
    context = {
        'trends': trends,
        'start_year': start_year,
        'end_year': end_year,
        'years': range(current_year - 4, current_year + 1)
    }
    
    return render(request, 'hr/performance_trends_report.html', context)


# AJAX Views
@login_required
@require_http_methods(["GET"])
def get_employee_performance_data(request, employee_id):
    """Get employee performance data (AJAX)"""
    try:
        employee = get_object_or_404(Employee, id=employee_id)
        
        # Check permission
        if not (request.user.has_perm('hr.view_employee') or 
                employee.user == request.user):
            return JsonResponse({'error': 'Permission denied'}, status=403)
        
        year = int(request.GET.get('year', timezone.now().year))
        
        summary = PerformanceAnalyticsService.get_employee_performance_summary(
            employee, year
        )
        
        # Convert to JSON-serializable format
        data = {
            'employee_name': f"{employee.user.first_name} {employee.user.last_name}",
            'total_evaluations': summary['total_evaluations'],
            'average_ratings': summary['average_ratings'],
            'performance_trend': [
                {
                    'date': trend['date'].isoformat(),
                    'overall_rating': trend['overall_rating'],
                    'average_rating': trend['average_rating']
                }
                for trend in summary['performance_trend']
            ]
        }
        
        return JsonResponse(data)
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=400)


@login_required
@require_http_methods(["POST"])
def add_employee_comment(request, evaluation_id):
    """Add employee comment to evaluation (AJAX)"""
    evaluation = get_object_or_404(PerformanceEvaluation, id=evaluation_id)
    
    # Check if user is the employee being evaluated
    if evaluation.employee.user != request.user:
        return JsonResponse({'error': 'Permission denied'}, status=403)
    
    if evaluation.is_finalized:
        return JsonResponse({'error': 'Cannot comment on finalized evaluation'}, status=400)
    
    comment = request.POST.get('comment', '').strip()
    if not comment:
        return JsonResponse({'error': 'Comment cannot be empty'}, status=400)
    
    evaluation.employee_comments = comment
    evaluation.save()
    
    return JsonResponse({
        'success': True,
        'message': 'Comment added successfully',
        'comment': comment
    })