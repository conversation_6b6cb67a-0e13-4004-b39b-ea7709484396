"""
Test cases for HR Attendance Tracking functionality
Tests the comprehensive attendance tracking system including
multiple attendance methods, biometric integration, and analytics.
"""

import pytest
from django.test import TestCase, Client
from django.urls import reverse
from accounts.models import User
from django.utils import timezone
from datetime import datetime, timedelta, time, date
import json

from core.models import School
from .models import Employee, Department, Position, AttendanceRecord
from .attendance_services import (
    AttendanceService, BiometricIntegrationService, 
    AttendanceAnalyticsService, QRCodeAttendanceService
)


@pytest.fixture
def school(db):
    """Create a test school"""
    return School.objects.create(
        name="Test School",
        code="TEST001",
        address="123 Test Street",
        phone="************",
        email="<EMAIL>",
        principal_name="Test Principal",
        established_date=date(2020, 1, 1)
    )


@pytest.fixture
def department(db, school):
    """Create a test department"""
    return Department.objects.create(
        school=school,
        name="Test Department",
        code="DEPT001",
        description="Test department for testing"
    )


@pytest.fixture
def position(db, department):
    """Create a test position"""
    return Position.objects.create(
        school=department.school,
        title="Test Position",
        department=department,
        description="Test position for testing",
        min_salary=30000,
        max_salary=50000
    )


@pytest.fixture
def user(db):
    """Create a test user"""
    return User.objects.create_user(
        username="testuser",
        email="<EMAIL>",
        password="testpass123",
        first_name="John",
        last_name="Doe"
    )


@pytest.fixture
def employee(db, user, position):
    """Create a test employee"""
    return Employee.objects.create(
        school=position.school,
        user=user,
        employee_id="EMP001",
        position=position,
        hire_date=date.today(),
        employment_status="active",
        salary=40000,
        emergency_contact_name="Jane Doe",
        emergency_contact_phone="************",
        emergency_contact_relationship="Spouse"
    )


class TestAttendanceService:
    """Test cases for AttendanceService"""
    
    def test_mark_attendance_check_in(self, employee):
        """Test marking check-in attendance"""
        result = AttendanceService.mark_attendance(
            employee_id=employee.employee_id,
            attendance_type='check_in',
            method='manual'
        )
        
        assert result['success'] is True
        assert 'check_in' in result['message'].lower()
        
        # Verify attendance record was created
        attendance = AttendanceRecord.objects.get(
            employee=employee,
            date=timezone.now().date()
        )
        assert attendance.check_in_time is not None
        assert attendance.status == 'present'
    
    def test_mark_attendance_late_check_in(self, employee):
        """Test marking late check-in attendance"""
        # Set a late time (after 9 AM)
        late_time = timezone.now().replace(hour=10, minute=30)
        
        result = AttendanceService.mark_attendance(
            employee_id=employee.employee_id,
            attendance_type='check_in',
            timestamp=late_time,
            method='manual'
        )
        
        assert result['success'] is True
        
        attendance = AttendanceRecord.objects.get(
            employee=employee,
            date=late_time.date()
        )
        assert attendance.status == 'late'
    
    def test_mark_attendance_check_out(self, employee):
        """Test marking check-out attendance"""
        # First check in
        AttendanceService.mark_attendance(
            employee_id=employee.employee_id,
            attendance_type='check_in',
            method='manual'
        )
        
        # Then check out
        result = AttendanceService.mark_attendance(
            employee_id=employee.employee_id,
            attendance_type='check_out',
            method='manual'
        )
        
        assert result['success'] is True
        assert 'check_out' in result['message'].lower()
        
        attendance = AttendanceRecord.objects.get(
            employee=employee,
            date=timezone.now().date()
        )
        assert attendance.check_out_time is not None
    
    def test_mark_attendance_check_out_without_check_in(self, employee):
        """Test that check-out fails without check-in"""
        result = AttendanceService.mark_attendance(
            employee_id=employee.employee_id,
            attendance_type='check_out',
            method='manual'
        )
        
        assert result['success'] is False
        assert 'check in first' in result['message'].lower()
    
    def test_mark_attendance_duplicate_check_in(self, employee):
        """Test that duplicate check-in fails"""
        # First check in
        AttendanceService.mark_attendance(
            employee_id=employee.employee_id,
            attendance_type='check_in',
            method='manual'
        )
        
        # Try to check in again
        result = AttendanceService.mark_attendance(
            employee_id=employee.employee_id,
            attendance_type='check_in',
            method='manual'
        )
        
        assert result['success'] is False
        assert 'already checked in' in result['message'].lower()
    
    def test_mark_attendance_invalid_employee(self):
        """Test marking attendance for non-existent employee"""
        result = AttendanceService.mark_attendance(
            employee_id="INVALID001",
            attendance_type='check_in',
            method='manual'
        )
        
        assert result['success'] is False
        assert 'not found' in result['message'].lower()
    
    def test_bulk_mark_attendance(self, employee, position):
        """Test bulk attendance marking"""
        # Create another employee
        user2 = User.objects.create_user(
            username="testuser2",
            email="<EMAIL>",
            password="testpass123",
            first_name="Jane",
            last_name="Smith"
        )
        employee2 = Employee.objects.create(
            school=employee.school,
            user=user2,
            employee_id="EMP002",
            position=position,
            hire_date=date.today(),
            employment_status="active",
            salary=40000,
            emergency_contact_name="John Smith",
            emergency_contact_phone="************",
            emergency_contact_relationship="Spouse"
        )
        
        employee_ids = [employee.employee_id, employee2.employee_id]
        test_date = date.today()
        
        result = AttendanceService.bulk_mark_attendance(
            employee_ids=employee_ids,
            date=test_date,
            status='present'
        )
        
        assert result['success_count'] == 2
        assert result['total_processed'] == 2
        assert len(result['errors']) == 0
        
        # Verify records were created
        assert AttendanceRecord.objects.filter(
            employee__in=[employee, employee2],
            date=test_date
        ).count() == 2
    
    def test_calculate_overtime(self, employee):
        """Test overtime calculation"""
        test_date = date.today()
        
        # Create attendance record with overtime
        attendance = AttendanceRecord.objects.create(
            school=employee.school,
            employee=employee,
            date=test_date,
            check_in_time=time(9, 0),  # 9:00 AM
            check_out_time=time(19, 0),  # 7:00 PM (10 hours total)
            status='present',
            created_by_id=1
        )
        
        overtime = AttendanceService.calculate_overtime(employee, test_date)
        
        # 10 hours - 8 standard hours = 2 hours overtime
        assert overtime == 2.0
    
    def test_calculate_overtime_no_overtime(self, employee):
        """Test overtime calculation with no overtime"""
        test_date = date.today()
        
        # Create attendance record without overtime
        attendance = AttendanceRecord.objects.create(
            school=employee.school,
            employee=employee,
            date=test_date,
            check_in_time=time(9, 0),  # 9:00 AM
            check_out_time=time(17, 0),  # 5:00 PM (8 hours total)
            status='present',
            created_by_id=1
        )
        
        overtime = AttendanceService.calculate_overtime(employee, test_date)
        
        assert overtime == 0.0
    
    def test_get_attendance_summary(self, employee):
        """Test attendance summary calculation"""
        start_date = date.today() - timedelta(days=7)
        end_date = date.today()
        
        # Create some attendance records
        for i in range(5):  # 5 present days
            test_date = start_date + timedelta(days=i)
            AttendanceRecord.objects.create(
                school=employee.school,
                employee=employee,
                date=test_date,
                check_in_time=time(9, 0),
                check_out_time=time(17, 0),
                status='present',
                created_by_id=1
            )
        
        # Create 1 late day
        AttendanceRecord.objects.create(
            school=employee.school,
            employee=employee,
            date=start_date + timedelta(days=5),
            check_in_time=time(10, 0),
            check_out_time=time(17, 0),
            status='late',
            created_by_id=1
        )
        
        summary = AttendanceService.get_attendance_summary(
            employee=employee,
            start_date=start_date,
            end_date=end_date
        )
        
        assert summary['total_days'] == 8
        assert summary['present_days'] == 6  # 5 present + 1 late
        assert summary['late_days'] == 1
        assert summary['absent_days'] == 2
        assert summary['attendance_rate'] == 75.0  # 6/8 * 100


class TestBiometricIntegrationService:
    """Test cases for BiometricIntegrationService"""
    
    def test_register_biometric_device(self):
        """Test biometric device registration"""
        result = BiometricIntegrationService.register_biometric_device(
            device_id="BIO001",
            device_name="Test Scanner",
            device_type="fingerprint",
            ip_address="*************"
        )
        
        assert result['success'] is True
        assert result['device_info']['device_id'] == "BIO001"
        assert result['device_info']['device_name'] == "Test Scanner"
        assert result['device_info']['device_type'] == "fingerprint"
    
    def test_process_biometric_data_check_in(self, employee):
        """Test processing biometric data for check-in"""
        result = BiometricIntegrationService.process_biometric_data(
            device_id="BIO001",
            employee_biometric_id=employee.employee_id,
            timestamp=timezone.now()
        )
        
        assert result['success'] is True
        assert result['device_id'] == "BIO001"
        assert employee.user.first_name in result['employee_name']
        
        # Verify attendance was recorded
        attendance = AttendanceRecord.objects.get(
            employee=employee,
            date=timezone.now().date()
        )
        assert attendance.check_in_time is not None
    
    def test_process_biometric_data_invalid_employee(self):
        """Test processing biometric data for invalid employee"""
        result = BiometricIntegrationService.process_biometric_data(
            device_id="BIO001",
            employee_biometric_id="INVALID001",
            timestamp=timezone.now()
        )
        
        assert result['success'] is False
        assert 'not found' in result['message'].lower()


class TestAttendanceAnalyticsService:
    """Test cases for AttendanceAnalyticsService"""
    
    def test_get_department_attendance_stats(self, department, employee):
        """Test department attendance statistics"""
        start_date = date.today() - timedelta(days=7)
        end_date = date.today()
        
        # Create attendance records
        for i in range(5):
            test_date = start_date + timedelta(days=i)
            AttendanceRecord.objects.create(
                school=employee.school,
                employee=employee,
                date=test_date,
                status='present',
                created_by_id=1
            )
        
        stats = AttendanceAnalyticsService.get_department_attendance_stats(
            department=department,
            start_date=start_date,
            end_date=end_date
        )
        
        assert stats['department_name'] == department.name
        assert stats['total_employees'] == 1
        assert stats['present_records'] == 5
        assert stats['attendance_rate'] > 0
    
    def test_identify_attendance_patterns(self, employee):
        """Test attendance pattern identification"""
        # Create some attendance records with patterns
        base_date = date.today() - timedelta(days=30)
        
        for i in range(20):
            test_date = base_date + timedelta(days=i)
            status = 'late' if test_date.weekday() == 0 else 'present'  # Late on Mondays
            
            AttendanceRecord.objects.create(
                school=employee.school,
                employee=employee,
                date=test_date,
                check_in_time=time(10, 0) if status == 'late' else time(9, 0),
                check_out_time=time(17, 0),
                status=status,
                created_by_id=1
            )
        
        patterns = AttendanceAnalyticsService.identify_attendance_patterns(
            employee=employee,
            days=30
        )
        
        assert 'frequent_late_days' in patterns
        assert 'average_arrival_time' in patterns
        assert 'average_departure_time' in patterns
        
        # Should identify Monday as frequent late day
        if patterns['frequent_late_days']:
            assert 'Monday' in [day[0] for day in patterns['frequent_late_days']]


class TestQRCodeAttendanceService:
    """Test cases for QRCodeAttendanceService"""
    
    def test_generate_attendance_qr_code(self):
        """Test QR code generation for attendance"""
        result = QRCodeAttendanceService.generate_attendance_qr_code(
            location="main_office",
            valid_duration=30
        )
        
        assert result['success'] is True
        assert result['location'] == "main_office"
        assert result['qr_token'] is not None
        assert result['expires_at'] is not None
        
        # Verify QR data is valid JSON
        qr_data = json.loads(result['qr_data'])
        assert qr_data['location'] == "main_office"
        assert qr_data['type'] == "attendance"
    
    def test_process_qr_attendance(self, employee):
        """Test processing QR code attendance"""
        # Generate QR code first
        qr_result = QRCodeAttendanceService.generate_attendance_qr_code(
            location="main_office",
            valid_duration=30
        )
        
        # Process attendance using QR token
        result = QRCodeAttendanceService.process_qr_attendance(
            employee_id=employee.employee_id,
            qr_token=qr_result['qr_token']
        )
        
        assert result['success'] is True
        assert result['qr_token'] == qr_result['qr_token']
        
        # Verify attendance was recorded
        attendance = AttendanceRecord.objects.get(
            employee=employee,
            date=timezone.now().date()
        )
        assert attendance.check_in_time is not None


class TestAttendanceRecord:
    """Test cases for AttendanceRecord model"""
    
    def test_total_hours_calculation(self, employee):
        """Test total hours calculation property"""
        test_date = date.today()
        
        attendance = AttendanceRecord.objects.create(
            school=employee.school,
            employee=employee,
            date=test_date,
            check_in_time=time(9, 0),  # 9:00 AM
            check_out_time=time(17, 30),  # 5:30 PM
            break_start_time=time(12, 0),  # 12:00 PM
            break_end_time=time(13, 0),  # 1:00 PM
            status='present',
            created_by_id=1
        )
        
        # 8.5 hours total - 1 hour break = 7.5 hours
        assert attendance.total_hours == 7.5
    
    def test_total_hours_no_break(self, employee):
        """Test total hours calculation without break"""
        test_date = date.today()
        
        attendance = AttendanceRecord.objects.create(
            school=employee.school,
            employee=employee,
            date=test_date,
            check_in_time=time(9, 0),  # 9:00 AM
            check_out_time=time(17, 0),  # 5:00 PM
            status='present',
            created_by_id=1
        )
        
        # 8 hours total
        assert attendance.total_hours == 8.0
    
    def test_total_hours_incomplete_record(self, employee):
        """Test total hours calculation with incomplete record"""
        test_date = date.today()
        
        attendance = AttendanceRecord.objects.create(
            school=employee.school,
            employee=employee,
            date=test_date,
            check_in_time=time(9, 0),  # Only check-in
            status='present',
            created_by_id=1
        )
        
        # No check-out time, should return 0
        assert attendance.total_hours == 0
    
    def test_unique_constraint(self, employee):
        """Test unique constraint on employee and date"""
        test_date = date.today()
        
        # Create first record
        AttendanceRecord.objects.create(
            school=employee.school,
            employee=employee,
            date=test_date,
            status='present',
            created_by_id=1
        )
        
        # Try to create duplicate - should raise IntegrityError
        with pytest.raises(Exception):  # IntegrityError
            AttendanceRecord.objects.create(
                school=employee.school,
                employee=employee,
                date=test_date,
                status='late',
                created_by_id=1
            )


class TestAttendanceViews(TestCase):
    """Test cases for attendance views"""
    
    def setUp(self):
        """Set up test data"""
        self.client = Client()
        
        # Create test school
        self.school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test Street",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2020, 1, 1)
        )
        
        # Create test user and employee
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            first_name="John",
            last_name="Doe"
        )
        
        self.department = Department.objects.create(
            school=self.school,
            name="Test Department",
            code="DEPT001"
        )
        
        self.position = Position.objects.create(
            school=self.school,
            title="Test Position",
            department=self.department,
            min_salary=30000,
            max_salary=50000
        )
        
        self.employee = Employee.objects.create(
            school=self.school,
            user=self.user,
            employee_id="EMP001",
            position=self.position,
            hire_date=date.today(),
            employment_status="active",
            salary=40000,
            emergency_contact_name="Jane Doe",
            emergency_contact_phone="************",
            emergency_contact_relationship="Spouse"
        )
        
        # Login user
        self.client.login(username="testuser", password="testpass123")
    
    def test_biometric_attendance_view(self):
        """Test biometric attendance view"""
        response = self.client.get(reverse('hr:biometric_attendance'))
        
        assert response.status_code == 200
        assert 'biometric_devices' in response.context
        assert 'recent_biometric_records' in response.context
    
    def test_qr_attendance_view_get(self):
        """Test QR attendance view GET request"""
        response = self.client.get(reverse('hr:qr_attendance'))
        
        assert response.status_code == 200
        assert 'qr_code_data' in response.context
        assert 'qr_code_json' in response.context
    
    def test_attendance_analytics_view(self):
        """Test attendance analytics view"""
        response = self.client.get(reverse('hr:attendance_analytics'))
        
        assert response.status_code == 200
        assert 'overall_stats' in response.context
        assert 'department_stats' in response.context
    
    def test_attendance_api_mark_attendance(self):
        """Test attendance API for marking attendance"""
        data = {
            'employee_id': self.employee.employee_id,
            'type': 'check_in',
            'method': 'web'
        }
        
        response = self.client.post(
            reverse('hr:attendance_api'),
            data=json.dumps(data),
            content_type='application/json'
        )
        
        assert response.status_code == 200
        response_data = json.loads(response.content)
        assert response_data['success'] is True
        
        # Verify attendance was recorded
        attendance = AttendanceRecord.objects.get(
            employee=self.employee,
            date=timezone.now().date()
        )
        assert attendance.check_in_time is not None
    
    def test_attendance_api_get_attendance(self):
        """Test attendance API for getting attendance data"""
        # Create attendance record first
        AttendanceRecord.objects.create(
            school=self.school,
            employee=self.employee,
            date=date.today(),
            check_in_time=time(9, 0),
            status='present',
            created_by=self.user
        )
        
        response = self.client.get(
            reverse('hr:attendance_api'),
            {'employee_id': self.employee.employee_id}
        )
        
        assert response.status_code == 200
        response_data = json.loads(response.content)
        assert response_data['success'] is True
        assert response_data['data']['employee_id'] == self.employee.employee_id
        assert response_data['data']['status'] == 'present'


if __name__ == '__main__':
    pytest.main([__file__])