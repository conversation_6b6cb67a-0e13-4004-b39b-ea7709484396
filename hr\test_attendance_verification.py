"""
Verification test for attendance tracking functionality
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from datetime import date, time

from core.models import School
from .models import Employee, Department, Position, AttendanceRecord
from .attendance_services import AttendanceService

User = get_user_model()


class AttendanceVerificationTest(TestCase):
    """Simple verification test for attendance functionality"""
    
    def setUp(self):
        """Set up test data"""
        # Create school
        self.school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test Street",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2020, 1, 1)
        )
        
        # Create department
        self.department = Department.objects.create(
            school=self.school,
            name="Test Department",
            code="DEPT001"
        )
        
        # Create position
        self.position = Position.objects.create(
            school=self.school,
            title="Test Position",
            department=self.department,
            min_salary=30000,
            max_salary=50000
        )
        
        # Create user
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            first_name="<PERSON>",
            last_name="Doe"
        )
        
        # Create employee
        self.employee = Employee.objects.create(
            school=self.school,
            user=self.user,
            employee_id="EMP001",
            position=self.position,
            hire_date=date.today(),
            employment_status="active",
            salary=40000,
            emergency_contact_name="Jane Doe",
            emergency_contact_phone="************",
            emergency_contact_relationship="Spouse"
        )
    
    def test_attendance_service_check_in(self):
        """Test basic check-in functionality"""
        result = AttendanceService.mark_attendance(
            employee_id=self.employee.employee_id,
            attendance_type='check_in',
            method='manual'
        )
        
        self.assertTrue(result['success'])
        self.assertIn('check in', result['message'].lower())
        
        # Verify attendance record was created
        attendance = AttendanceRecord.objects.get(
            employee=self.employee,
            date=date.today()
        )
        self.assertIsNotNone(attendance.check_in_time)
        self.assertEqual(attendance.status, 'present')
    
    def test_attendance_service_check_out(self):
        """Test check-out functionality"""
        # First check in
        AttendanceService.mark_attendance(
            employee_id=self.employee.employee_id,
            attendance_type='check_in',
            method='manual'
        )
        
        # Then check out
        result = AttendanceService.mark_attendance(
            employee_id=self.employee.employee_id,
            attendance_type='check_out',
            method='manual'
        )
        
        self.assertTrue(result['success'])
        self.assertIn('check out', result['message'].lower())
        
        attendance = AttendanceRecord.objects.get(
            employee=self.employee,
            date=date.today()
        )
        self.assertIsNotNone(attendance.check_out_time)
    
    def test_attendance_record_total_hours(self):
        """Test total hours calculation"""
        attendance = AttendanceRecord.objects.create(
            school=self.school,
            employee=self.employee,
            date=date.today(),
            check_in_time=time(9, 0),  # 9:00 AM
            check_out_time=time(17, 0),  # 5:00 PM
            status='present',
            created_by=self.user
        )
        
        # 8 hours total
        self.assertEqual(attendance.total_hours, 8.0)
    
    def test_overtime_calculation(self):
        """Test overtime calculation"""
        # Create attendance record with overtime
        AttendanceRecord.objects.create(
            school=self.school,
            employee=self.employee,
            date=date.today(),
            check_in_time=time(9, 0),  # 9:00 AM
            check_out_time=time(19, 0),  # 7:00 PM (10 hours total)
            status='present',
            created_by=self.user
        )
        
        overtime = AttendanceService.calculate_overtime(self.employee, date.today())
        
        # 10 hours - 8 standard hours = 2 hours overtime
        self.assertEqual(overtime, 2.0)
    
    def test_bulk_attendance_marking(self):
        """Test bulk attendance marking"""
        # Create another employee
        user2 = User.objects.create_user(
            username="testuser2",
            email="<EMAIL>",
            password="testpass123",
            first_name="Jane",
            last_name="Smith"
        )
        employee2 = Employee.objects.create(
            school=self.school,
            user=user2,
            employee_id="EMP002",
            position=self.position,
            hire_date=date.today(),
            employment_status="active",
            salary=40000,
            emergency_contact_name="John Smith",
            emergency_contact_phone="************",
            emergency_contact_relationship="Spouse"
        )
        
        employee_ids = [self.employee.employee_id, employee2.employee_id]
        test_date = date.today()
        
        result = AttendanceService.bulk_mark_attendance(
            employee_ids=employee_ids,
            date=test_date,
            status='present'
        )
        
        self.assertEqual(result['success_count'], 2)
        self.assertEqual(result['total_processed'], 2)
        self.assertEqual(len(result['errors']), 0)
        
        # Verify records were created
        self.assertEqual(
            AttendanceRecord.objects.filter(
                employee__in=[self.employee, employee2],
                date=test_date
            ).count(),
            2
        )