"""
Tests for leave management functionality
"""
import pytest
from decimal import Decimal
from datetime import date, datetime, timedelta
from django.test import TestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.utils import timezone

from core.models import School, AcademicYear
from accounts.models import User
from .models import (
    Department, Position, Employee, LeaveType, LeaveRequest, AttendanceRecord
)
from .leave_services import (
    LeaveBalanceService, LeaveRequestService, LeaveCalendarService, LeaveReportService
)


class LeaveManagementTestCase(TestCase):
    """Test case for leave management"""
    
    def setUp(self):
        """Set up test data"""
        # Create school
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="Test Address",
            phone="**********",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2020, 1, 1)
        )
        
        # Create academic year
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        # Create users
        self.admin_user = User.objects.create_user(
            username="admin",
            email="<EMAIL>",
            password="testpass123",
            first_name="Admin",
            last_name="User"
        )
        
        self.employee_user = User.objects.create_user(
            username="employee1",
            email="<EMAIL>",
            password="testpass123",
            first_name="John",
            last_name="Doe"
        )
        
        self.manager_user = User.objects.create_user(
            username="manager1",
            email="<EMAIL>",
            password="testpass123",
            first_name="Jane",
            last_name="Manager"
        )
        
        # Create department and position
        self.department = Department.objects.create(
            school=self.school,
            name="IT Department",
            code="IT",
            created_by=self.admin_user
        )
        
        self.position = Position.objects.create(
            school=self.school,
            title="Software Developer",
            department=self.department,
            min_salary=Decimal('5000.00'),
            max_salary=Decimal('10000.00'),
            created_by=self.admin_user
        )
        
        self.manager_position = Position.objects.create(
            school=self.school,
            title="IT Manager",
            department=self.department,
            min_salary=Decimal('8000.00'),
            max_salary=Decimal('15000.00'),
            created_by=self.admin_user
        )
        
        # Create employees
        self.employee = Employee.objects.create(
            school=self.school,
            user=self.employee_user,
            employee_id="EMP001",
            position=self.position,
            hire_date=date(2024, 1, 1),
            employment_status='active',
            salary=Decimal('7000.00'),
            emergency_contact_name="Jane Doe",
            emergency_contact_phone="9876543210",
            emergency_contact_relationship="Spouse",
            created_by=self.admin_user
        )
        
        self.manager = Employee.objects.create(
            school=self.school,
            user=self.manager_user,
            employee_id="MGR001",
            position=self.manager_position,
            hire_date=date(2024, 1, 1),
            employment_status='active',
            salary=Decimal('12000.00'),
            emergency_contact_name="John Manager",
            emergency_contact_phone="9876543211",
            emergency_contact_relationship="Spouse",
            created_by=self.admin_user
        )
        
        # Create leave types
        self.annual_leave = LeaveType.objects.create(
            school=self.school,
            name="Annual Leave",
            name_ar="إجازة سنوية",
            code="ANNUAL",
            max_days_per_year=30,
            is_paid=True,
            requires_approval=True,
            description="Annual vacation leave",
            created_by=self.admin_user
        )
        
        self.sick_leave = LeaveType.objects.create(
            school=self.school,
            name="Sick Leave",
            name_ar="إجازة مرضية",
            code="SICK",
            max_days_per_year=15,
            is_paid=True,
            requires_approval=True,
            description="Medical leave",
            created_by=self.admin_user
        )
        
        self.personal_leave = LeaveType.objects.create(
            school=self.school,
            name="Personal Leave",
            name_ar="إجازة شخصية",
            code="PERSONAL",
            max_days_per_year=5,
            is_paid=False,
            requires_approval=True,
            description="Personal leave",
            created_by=self.admin_user
        )
    
    def test_leave_type_creation(self):
        """Test leave type creation"""
        self.assertEqual(self.annual_leave.name, "Annual Leave")
        self.assertEqual(self.annual_leave.code, "ANNUAL")
        self.assertEqual(self.annual_leave.max_days_per_year, 30)
        self.assertTrue(self.annual_leave.is_paid)
        self.assertTrue(self.annual_leave.requires_approval)
    
    def test_leave_balance_calculation(self):
        """Test leave balance calculation"""
        current_year = timezone.now().year
        
        # Test initial balance (no leave taken)
        balance = LeaveBalanceService.get_employee_leave_balance(
            self.employee, self.annual_leave, current_year
        )
        
        self.assertEqual(balance['allocated_days'], 30)
        self.assertEqual(balance['used_days'], 0)
        self.assertEqual(balance['pending_days'], 0)
        self.assertEqual(balance['remaining_days'], 30)
        self.assertEqual(balance['available_days'], 30)
    
    def test_leave_balance_with_approved_leave(self):
        """Test leave balance calculation with approved leave"""
        current_year = timezone.now().year
        
        # Create approved leave request
        leave_request = LeaveRequest.objects.create(
            school=self.school,
            employee=self.employee,
            leave_type=self.annual_leave,
            start_date=date(current_year, 3, 1),
            end_date=date(current_year, 3, 5),
            reason="Vacation",
            status='approved',
            approved_by=self.manager_user,
            approved_at=timezone.now(),
            created_by=self.employee_user
        )
        
        balance = LeaveBalanceService.get_employee_leave_balance(
            self.employee, self.annual_leave, current_year
        )
        
        self.assertEqual(balance['allocated_days'], 30)
        self.assertEqual(balance['used_days'], 5)  # 5 days leave
        self.assertEqual(balance['remaining_days'], 25)
        self.assertEqual(balance['available_days'], 25)
    
    def test_leave_balance_with_pending_leave(self):
        """Test leave balance calculation with pending leave"""
        current_year = timezone.now().year
        
        # Create pending leave request
        leave_request = LeaveRequest.objects.create(
            school=self.school,
            employee=self.employee,
            leave_type=self.annual_leave,
            start_date=date(current_year, 4, 1),
            end_date=date(current_year, 4, 3),
            reason="Personal",
            status='pending',
            created_by=self.employee_user
        )
        
        balance = LeaveBalanceService.get_employee_leave_balance(
            self.employee, self.annual_leave, current_year
        )
        
        self.assertEqual(balance['allocated_days'], 30)
        self.assertEqual(balance['used_days'], 0)
        self.assertEqual(balance['pending_days'], 3)  # 3 days pending
        self.assertEqual(balance['remaining_days'], 30)
        self.assertEqual(balance['available_days'], 27)  # 30 - 3 pending
    
    def test_get_all_leave_balances(self):
        """Test getting all leave balances for an employee"""
        current_year = timezone.now().year
        
        balances = LeaveBalanceService.get_employee_all_leave_balances(
            self.employee, current_year
        )
        
        self.assertEqual(len(balances), 3)  # 3 leave types
        
        # Check that all leave types are included
        leave_type_names = [balance['leave_type'].name for balance in balances]
        self.assertIn("Annual Leave", leave_type_names)
        self.assertIn("Sick Leave", leave_type_names)
        self.assertIn("Personal Leave", leave_type_names)
    
    def test_leave_eligibility_check_sufficient_balance(self):
        """Test leave eligibility check with sufficient balance"""
        current_year = timezone.now().year
        start_date = date(current_year, 5, 1)
        end_date = date(current_year, 5, 5)
        
        eligibility = LeaveBalanceService.check_leave_eligibility(
            self.employee, self.annual_leave, start_date, end_date
        )
        
        self.assertTrue(eligibility['eligible'])
        self.assertEqual(eligibility['reason'], 'Eligible for leave')
    
    def test_leave_eligibility_check_insufficient_balance(self):
        """Test leave eligibility check with insufficient balance"""
        current_year = timezone.now().year
        
        # Create a leave request that uses most of the balance
        LeaveRequest.objects.create(
            school=self.school,
            employee=self.employee,
            leave_type=self.annual_leave,
            start_date=date(current_year, 2, 1),
            end_date=date(current_year, 2, 28),
            reason="Long vacation",
            status='approved',
            approved_by=self.manager_user,
            approved_at=timezone.now(),
            created_by=self.employee_user
        )
        
        # Try to request more leave than available
        start_date = date(current_year, 6, 1)
        end_date = date(current_year, 6, 10)  # 10 days, but only 2 remaining
        
        eligibility = LeaveBalanceService.check_leave_eligibility(
            self.employee, self.annual_leave, start_date, end_date
        )
        
        self.assertFalse(eligibility['eligible'])
        self.assertIn('Insufficient leave balance', eligibility['reason'])
    
    def test_leave_eligibility_check_overlapping_request(self):
        """Test leave eligibility check with overlapping request"""
        current_year = timezone.now().year
        
        # Create existing leave request
        LeaveRequest.objects.create(
            school=self.school,
            employee=self.employee,
            leave_type=self.annual_leave,
            start_date=date(current_year, 7, 5),
            end_date=date(current_year, 7, 10),
            reason="Vacation",
            status='approved',
            approved_by=self.manager_user,
            approved_at=timezone.now(),
            created_by=self.employee_user
        )
        
        # Try to request overlapping leave
        start_date = date(current_year, 7, 8)
        end_date = date(current_year, 7, 12)
        
        eligibility = LeaveBalanceService.check_leave_eligibility(
            self.employee, self.annual_leave, start_date, end_date
        )
        
        self.assertFalse(eligibility['eligible'])
        self.assertEqual(eligibility['reason'], 'Overlapping leave request exists')
    
    def test_create_leave_request(self):
        """Test creating a leave request"""
        current_year = timezone.now().year
        start_date = date(current_year, 8, 1)
        end_date = date(current_year, 8, 5)
        
        leave_request = LeaveRequestService.create_leave_request(
            employee=self.employee,
            leave_type=self.annual_leave,
            start_date=start_date,
            end_date=end_date,
            reason="Summer vacation",
            user=self.employee_user
        )
        
        self.assertIsNotNone(leave_request)
        self.assertEqual(leave_request.employee, self.employee)
        self.assertEqual(leave_request.leave_type, self.annual_leave)
        self.assertEqual(leave_request.start_date, start_date)
        self.assertEqual(leave_request.end_date, end_date)
        self.assertEqual(leave_request.reason, "Summer vacation")
        self.assertEqual(leave_request.status, 'pending')
        self.assertEqual(leave_request.duration_days, 5)
    
    def test_create_leave_request_insufficient_balance(self):
        """Test creating leave request with insufficient balance"""
        current_year = timezone.now().year
        
        # Use up most of the annual leave
        LeaveRequest.objects.create(
            school=self.school,
            employee=self.employee,
            leave_type=self.annual_leave,
            start_date=date(current_year, 1, 1),
            end_date=date(current_year, 1, 29),
            reason="Long vacation",
            status='approved',
            approved_by=self.manager_user,
            approved_at=timezone.now(),
            created_by=self.employee_user
        )
        
        # Try to request more leave
        start_date = date(current_year, 9, 1)
        end_date = date(current_year, 9, 5)
        
        with self.assertRaises(ValidationError):
            LeaveRequestService.create_leave_request(
                employee=self.employee,
                leave_type=self.annual_leave,
                start_date=start_date,
                end_date=end_date,
                reason="More vacation",
                user=self.employee_user
            )
    
    def test_approve_leave_request(self):
        """Test approving a leave request"""
        current_year = timezone.now().year
        
        # Create leave request
        leave_request = LeaveRequest.objects.create(
            school=self.school,
            employee=self.employee,
            leave_type=self.annual_leave,
            start_date=date(current_year, 10, 1),
            end_date=date(current_year, 10, 3),
            reason="Personal",
            status='pending',
            created_by=self.employee_user
        )
        
        # Approve the request
        approved_request = LeaveRequestService.approve_leave_request(
            leave_request, self.manager_user, "Approved for personal reasons"
        )
        
        self.assertEqual(approved_request.status, 'approved')
        self.assertEqual(approved_request.approved_by, self.manager_user)
        self.assertIsNotNone(approved_request.approved_at)
        
        # Check that attendance records were created
        attendance_records = AttendanceRecord.objects.filter(
            employee=self.employee,
            date__range=[leave_request.start_date, leave_request.end_date]
        )
        
        # Should have records for working days only (3 days, assuming weekdays)
        self.assertGreater(attendance_records.count(), 0)
    
    def test_reject_leave_request(self):
        """Test rejecting a leave request"""
        current_year = timezone.now().year
        
        # Create leave request
        leave_request = LeaveRequest.objects.create(
            school=self.school,
            employee=self.employee,
            leave_type=self.annual_leave,
            start_date=date(current_year, 11, 1),
            end_date=date(current_year, 11, 3),
            reason="Personal",
            status='pending',
            created_by=self.employee_user
        )
        
        # Reject the request
        rejected_request = LeaveRequestService.reject_leave_request(
            leave_request, self.manager_user, "Insufficient staffing"
        )
        
        self.assertEqual(rejected_request.status, 'rejected')
        self.assertEqual(rejected_request.approved_by, self.manager_user)
        self.assertEqual(rejected_request.rejection_reason, "Insufficient staffing")
        self.assertIsNotNone(rejected_request.approved_at)
    
    def test_cancel_leave_request(self):
        """Test cancelling a leave request"""
        current_year = timezone.now().year
        
        # Create and approve leave request
        leave_request = LeaveRequest.objects.create(
            school=self.school,
            employee=self.employee,
            leave_type=self.annual_leave,
            start_date=date(current_year, 12, 1),
            end_date=date(current_year, 12, 3),
            reason="Personal",
            status='approved',
            approved_by=self.manager_user,
            approved_at=timezone.now(),
            created_by=self.employee_user
        )
        
        # Create attendance records (simulating approval process)
        for i in range(3):
            AttendanceRecord.objects.create(
                school=self.school,
                employee=self.employee,
                date=date(current_year, 12, 1) + timedelta(days=i),
                status='vacation',
                notes=f'Leave: {leave_request.leave_type.name}',
                is_manual=True,
                created_by=self.manager_user
            )
        
        # Cancel the request
        cancelled_request = LeaveRequestService.cancel_leave_request(
            leave_request, self.employee_user, "Plans changed"
        )
        
        self.assertEqual(cancelled_request.status, 'cancelled')
        self.assertEqual(cancelled_request.rejection_reason, "Plans changed")
        
        # Check that attendance records were removed
        attendance_records = AttendanceRecord.objects.filter(
            employee=self.employee,
            date__range=[leave_request.start_date, leave_request.end_date],
            status='vacation'
        )
        self.assertEqual(attendance_records.count(), 0)
    
    def test_get_employee_leave_requests(self):
        """Test getting employee leave requests"""
        current_year = timezone.now().year
        
        # Create multiple leave requests
        for i in range(3):
            LeaveRequest.objects.create(
                school=self.school,
                employee=self.employee,
                leave_type=self.annual_leave,
                start_date=date(current_year, i + 1, 1),
                end_date=date(current_year, i + 1, 3),
                reason=f"Leave {i + 1}",
                status='pending',
                created_by=self.employee_user
            )
        
        # Get all requests
        requests = LeaveRequestService.get_employee_leave_requests(self.employee)
        self.assertEqual(requests.count(), 3)
        
        # Get requests for specific year
        requests_year = LeaveRequestService.get_employee_leave_requests(
            self.employee, current_year
        )
        self.assertEqual(requests_year.count(), 3)
        
        # Get requests with specific status
        requests_pending = LeaveRequestService.get_employee_leave_requests(
            self.employee, current_year, 'pending'
        )
        self.assertEqual(requests_pending.count(), 3)
    
    def test_get_pending_leave_requests(self):
        """Test getting pending leave requests"""
        current_year = timezone.now().year
        
        # Create pending requests for different employees
        LeaveRequest.objects.create(
            school=self.school,
            employee=self.employee,
            leave_type=self.annual_leave,
            start_date=date(current_year, 6, 1),
            end_date=date(current_year, 6, 3),
            reason="Employee leave",
            status='pending',
            created_by=self.employee_user
        )
        
        LeaveRequest.objects.create(
            school=self.school,
            employee=self.manager,
            leave_type=self.sick_leave,
            start_date=date(current_year, 6, 5),
            end_date=date(current_year, 6, 7),
            reason="Manager leave",
            status='pending',
            created_by=self.manager_user
        )
        
        # Create approved request (should not be included)
        LeaveRequest.objects.create(
            school=self.school,
            employee=self.employee,
            leave_type=self.personal_leave,
            start_date=date(current_year, 6, 10),
            end_date=date(current_year, 6, 12),
            reason="Approved leave",
            status='approved',
            approved_by=self.manager_user,
            approved_at=timezone.now(),
            created_by=self.employee_user
        )
        
        pending_requests = LeaveRequestService.get_pending_leave_requests(self.school)
        self.assertEqual(pending_requests.count(), 2)
        
        # Test department filter
        pending_dept = LeaveRequestService.get_pending_leave_requests(
            self.school, self.department
        )
        self.assertEqual(pending_dept.count(), 2)
    
    def test_leave_calendar_service(self):
        """Test leave calendar service"""
        current_year = timezone.now().year
        start_date = date(current_year, 3, 1)
        end_date = date(current_year, 3, 31)
        
        # Create approved leave requests
        leave1 = LeaveRequest.objects.create(
            school=self.school,
            employee=self.employee,
            leave_type=self.annual_leave,
            start_date=date(current_year, 3, 5),
            end_date=date(current_year, 3, 7),
            reason="Vacation",
            status='approved',
            approved_by=self.manager_user,
            approved_at=timezone.now(),
            created_by=self.employee_user
        )
        
        leave2 = LeaveRequest.objects.create(
            school=self.school,
            employee=self.manager,
            leave_type=self.sick_leave,
            start_date=date(current_year, 3, 10),
            end_date=date(current_year, 3, 12),
            reason="Sick",
            status='approved',
            approved_by=self.admin_user,
            approved_at=timezone.now(),
            created_by=self.manager_user
        )
        
        calendar_data = LeaveCalendarService.get_leave_calendar(
            self.school, start_date, end_date
        )
        
        # Check that leave dates are in calendar
        self.assertIn(date(current_year, 3, 5), calendar_data)
        self.assertIn(date(current_year, 3, 10), calendar_data)
        
        # Check leave details
        march_5_leaves = calendar_data[date(current_year, 3, 5)]
        self.assertEqual(len(march_5_leaves), 1)
        self.assertEqual(march_5_leaves[0]['employee'], self.employee)
        self.assertEqual(march_5_leaves[0]['leave_type'], self.annual_leave)
    
    def test_leave_utilization_report(self):
        """Test leave utilization report"""
        current_year = timezone.now().year
        
        # Create approved leave requests
        LeaveRequest.objects.create(
            school=self.school,
            employee=self.employee,
            leave_type=self.annual_leave,
            start_date=date(current_year, 4, 1),
            end_date=date(current_year, 4, 10),
            reason="Vacation",
            status='approved',
            approved_by=self.manager_user,
            approved_at=timezone.now(),
            created_by=self.employee_user
        )
        
        report_data = LeaveReportService.generate_leave_utilization_report(
            self.school, current_year
        )
        
        self.assertEqual(len(report_data), 3)  # 3 leave types
        
        # Find annual leave data
        annual_leave_data = next(
            (item for item in report_data if item['leave_type'] == self.annual_leave),
            None
        )
        
        self.assertIsNotNone(annual_leave_data)
        self.assertEqual(annual_leave_data['total_requests'], 1)
        self.assertEqual(annual_leave_data['total_days'], 10)
        self.assertEqual(annual_leave_data['employee_count'], 2)  # 2 active employees
        self.assertGreater(annual_leave_data['utilization_percentage'], 0)
    
    def test_employee_leave_report(self):
        """Test employee leave report"""
        current_year = timezone.now().year
        
        # Create various leave requests
        LeaveRequest.objects.create(
            school=self.school,
            employee=self.employee,
            leave_type=self.annual_leave,
            start_date=date(current_year, 5, 1),
            end_date=date(current_year, 5, 5),
            reason="Vacation",
            status='approved',
            approved_by=self.manager_user,
            approved_at=timezone.now(),
            created_by=self.employee_user
        )
        
        LeaveRequest.objects.create(
            school=self.school,
            employee=self.employee,
            leave_type=self.sick_leave,
            start_date=date(current_year, 6, 1),
            end_date=date(current_year, 6, 2),
            reason="Illness",
            status='approved',
            approved_by=self.manager_user,
            approved_at=timezone.now(),
            created_by=self.employee_user
        )
        
        LeaveRequest.objects.create(
            school=self.school,
            employee=self.employee,
            leave_type=self.personal_leave,
            start_date=date(current_year, 7, 1),
            end_date=date(current_year, 7, 1),
            reason="Personal",
            status='pending',
            created_by=self.employee_user
        )
        
        report_data = LeaveReportService.generate_employee_leave_report(
            self.employee, current_year
        )
        
        self.assertEqual(report_data['employee'], self.employee)
        self.assertEqual(report_data['year'], current_year)
        self.assertEqual(report_data['total_leave_days'], 7)  # 5 + 2 approved days
        self.assertEqual(report_data['approved_count'], 2)
        self.assertEqual(report_data['pending_count'], 1)
        self.assertEqual(report_data['rejected_count'], 0)
        
        # Check leave by type
        self.assertIn('Annual Leave', report_data['leave_by_type'])
        self.assertIn('Sick Leave', report_data['leave_by_type'])
        self.assertEqual(report_data['leave_by_type']['Annual Leave']['days'], 5)
        self.assertEqual(report_data['leave_by_type']['Sick Leave']['days'], 2)


if __name__ == '__main__':
    pytest.main([__file__])