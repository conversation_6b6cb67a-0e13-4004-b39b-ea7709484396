"""
Tests for payroll processing functionality
"""
import pytest
from decimal import Decimal
from datetime import date, datetime, timedelta
from django.test import TestCase
from accounts.models import User
from django.core.exceptions import ValidationError
from django.utils import timezone

from core.models import School, AcademicYear
from .models import (
    Department, Position, Employee, PayrollPeriod, Payroll, PayrollAllowance,
    PayrollDeduction, Payslip, SalaryStructure, AllowanceType, DeductionType,
    EmployeeAllowance, EmployeeDeduction, AttendanceRecord
)
from .payroll_services import PayrollCalculationService, PayslipService, PayrollReportService


class PayrollProcessingTestCase(TestCase):
    """Test case for payroll processing"""
    
    def setUp(self):
        """Set up test data"""
        # Create school
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="Test Address",
            phone="**********",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2020, 1, 1)
        )
        
        # Create academic year
        self.academic_year = AcademicYear.objects.create(
            school=self.school,
            name="2024-2025",
            start_date=date(2024, 9, 1),
            end_date=date(2025, 6, 30),
            is_current=True
        )
        
        # Create users
        self.admin_user = User.objects.create_user(
            username="admin",
            email="<EMAIL>",
            password="testpass123",
            first_name="Admin",
            last_name="User"
        )
        
        self.employee_user = User.objects.create_user(
            username="employee1",
            email="<EMAIL>",
            password="testpass123",
            first_name="John",
            last_name="Doe"
        )
        
        # Create department and position
        self.department = Department.objects.create(
            school=self.school,
            name="IT Department",
            code="IT",
            created_by=self.admin_user
        )
        
        self.position = Position.objects.create(
            school=self.school,
            title="Software Developer",
            department=self.department,
            min_salary=Decimal('5000.00'),
            max_salary=Decimal('10000.00'),
            created_by=self.admin_user
        )
        
        # Create employee
        self.employee = Employee.objects.create(
            school=self.school,
            user=self.employee_user,
            employee_id="EMP001",
            position=self.position,
            hire_date=date(2024, 1, 1),
            employment_status='active',
            salary=Decimal('7000.00'),
            emergency_contact_name="Jane Doe",
            emergency_contact_phone="9876543210",
            emergency_contact_relationship="Spouse",
            created_by=self.admin_user
        )
        
        # Create payroll period
        self.payroll_period = PayrollPeriod.objects.create(
            school=self.school,
            name="January 2025",
            start_date=date(2025, 1, 1),
            end_date=date(2025, 1, 31),
            created_by=self.admin_user
        )
        
        # Create allowance and deduction types
        self.allowance_type = AllowanceType.objects.create(
            school=self.school,
            name="Transport Allowance",
            code="TRANSPORT",
            is_taxable=True,
            is_fixed=True,
            default_amount=Decimal('500.00'),
            created_by=self.admin_user
        )
        
        self.deduction_type = DeductionType.objects.create(
            school=self.school,
            name="Health Insurance",
            code="HEALTH_INS",
            is_mandatory=True,
            is_percentage=True,
            default_amount=Decimal('2.00'),  # 2%
            max_amount=Decimal('200.00'),
            created_by=self.admin_user
        )
        
        # Create employee allowance and deduction
        self.employee_allowance = EmployeeAllowance.objects.create(
            school=self.school,
            employee=self.employee,
            allowance_type=self.allowance_type,
            amount=Decimal('500.00'),
            effective_date=date(2024, 1, 1),
            created_by=self.admin_user
        )
        
        self.employee_deduction = EmployeeDeduction.objects.create(
            school=self.school,
            employee=self.employee,
            deduction_type=self.deduction_type,
            amount=Decimal('2.00'),  # 2%
            effective_date=date(2024, 1, 1),
            created_by=self.admin_user
        )
    
    def test_salary_structure_creation(self):
        """Test salary structure creation"""
        salary_structure = SalaryStructure.objects.create(
            school=self.school,
            name="Developer Salary Structure",
            position=self.position,
            basic_salary=Decimal('8000.00'),
            effective_date=date(2025, 1, 1),
            created_by=self.admin_user
        )
        
        self.assertEqual(salary_structure.name, "Developer Salary Structure")
        self.assertEqual(salary_structure.basic_salary, Decimal('8000.00'))
        self.assertTrue(salary_structure.is_active)
    
    def test_allowance_type_creation(self):
        """Test allowance type creation"""
        self.assertEqual(self.allowance_type.name, "Transport Allowance")
        self.assertEqual(self.allowance_type.code, "TRANSPORT")
        self.assertTrue(self.allowance_type.is_taxable)
        self.assertTrue(self.allowance_type.is_fixed)
        self.assertEqual(self.allowance_type.default_amount, Decimal('500.00'))
    
    def test_deduction_type_creation(self):
        """Test deduction type creation"""
        self.assertEqual(self.deduction_type.name, "Health Insurance")
        self.assertEqual(self.deduction_type.code, "HEALTH_INS")
        self.assertTrue(self.deduction_type.is_mandatory)
        self.assertTrue(self.deduction_type.is_percentage)
        self.assertEqual(self.deduction_type.default_amount, Decimal('2.00'))
    
    def test_employee_allowance_creation(self):
        """Test employee allowance creation"""
        self.assertEqual(self.employee_allowance.employee, self.employee)
        self.assertEqual(self.employee_allowance.allowance_type, self.allowance_type)
        self.assertEqual(self.employee_allowance.amount, Decimal('500.00'))
        self.assertTrue(self.employee_allowance.is_active)
    
    def test_employee_deduction_creation(self):
        """Test employee deduction creation"""
        self.assertEqual(self.employee_deduction.employee, self.employee)
        self.assertEqual(self.employee_deduction.deduction_type, self.deduction_type)
        self.assertEqual(self.employee_deduction.amount, Decimal('2.00'))
        self.assertTrue(self.employee_deduction.is_active)
    
    def test_payroll_period_creation(self):
        """Test payroll period creation"""
        self.assertEqual(self.payroll_period.name, "January 2025")
        self.assertEqual(self.payroll_period.start_date, date(2025, 1, 1))
        self.assertEqual(self.payroll_period.end_date, date(2025, 1, 31))
        self.assertFalse(self.payroll_period.is_closed)
    
    def test_payroll_calculation_service_initialization(self):
        """Test payroll calculation service initialization"""
        service = PayrollCalculationService(self.payroll_period)
        self.assertEqual(service.period, self.payroll_period)
    
    def test_basic_payroll_calculation(self):
        """Test basic payroll calculation"""
        service = PayrollCalculationService(self.payroll_period)
        
        # Create some attendance records
        for day in range(1, 23):  # 22 working days
            if date(2025, 1, day).weekday() < 5:  # Monday to Friday
                AttendanceRecord.objects.create(
                    school=self.school,
                    employee=self.employee,
                    date=date(2025, 1, day),
                    check_in_time="09:00",
                    check_out_time="17:00",
                    status='present',
                    created_by=self.admin_user
                )
        
        payroll = service.calculate_employee_payroll(self.employee, self.admin_user)
        
        self.assertIsNotNone(payroll)
        self.assertEqual(payroll.employee, self.employee)
        self.assertEqual(payroll.period, self.payroll_period)
        self.assertTrue(payroll.is_calculated)
        self.assertEqual(payroll.calculated_by, self.admin_user)
        self.assertIsNotNone(payroll.calculated_at)
        
        # Check that basic salary is calculated
        self.assertGreater(payroll.basic_salary, 0)
        
        # Check that allowances are calculated
        self.assertEqual(payroll.total_allowances, Decimal('500.00'))
        
        # Check that deductions are calculated
        self.assertGreater(payroll.total_deductions, 0)
        
        # Check that net salary is calculated
        self.assertGreater(payroll.net_salary, 0)
    
    def test_payroll_calculation_with_overtime(self):
        """Test payroll calculation with overtime"""
        service = PayrollCalculationService(self.payroll_period)
        
        # Create attendance records with overtime
        for day in range(1, 23):
            if date(2025, 1, day).weekday() < 5:
                AttendanceRecord.objects.create(
                    school=self.school,
                    employee=self.employee,
                    date=date(2025, 1, day),
                    check_in_time="09:00",
                    check_out_time="19:00",  # 10 hours (2 hours overtime)
                    status='present',
                    created_by=self.admin_user
                )
        
        payroll = service.calculate_employee_payroll(self.employee, self.admin_user)
        
        # Check that overtime is calculated
        self.assertGreater(payroll.overtime_hours, 0)
        self.assertGreater(payroll.overtime_amount, 0)
        self.assertGreater(payroll.overtime_rate, 0)
    
    def test_payroll_calculation_with_absences(self):
        """Test payroll calculation with absences"""
        service = PayrollCalculationService(self.payroll_period)
        
        # Create attendance records with some absences
        working_days = 0
        present_days = 0
        absent_days = 0
        
        for day in range(1, 32):
            current_date = date(2025, 1, day)
            if current_date.weekday() < 5:  # Monday to Friday
                working_days += 1
                if day <= 15:  # Present for first half of month
                    AttendanceRecord.objects.create(
                        school=self.school,
                        employee=self.employee,
                        date=current_date,
                        check_in_time="09:00",
                        check_out_time="17:00",
                        status='present',
                        created_by=self.admin_user
                    )
                    present_days += 1
                else:  # Absent for second half
                    AttendanceRecord.objects.create(
                        school=self.school,
                        employee=self.employee,
                        date=current_date,
                        status='absent',
                        created_by=self.admin_user
                    )
                    absent_days += 1
        
        payroll = service.calculate_employee_payroll(self.employee, self.admin_user)
        
        # Check attendance calculations
        self.assertEqual(payroll.working_days, working_days)
        self.assertEqual(payroll.present_days, present_days)
        self.assertEqual(payroll.absent_days, absent_days)
        
        # Check that salary is prorated based on attendance
        expected_daily_salary = self.employee.salary / working_days
        expected_prorated_salary = expected_daily_salary * present_days
        self.assertAlmostEqual(
            float(payroll.basic_salary),
            float(expected_prorated_salary),
            places=2
        )
    
    def test_bulk_payroll_calculation(self):
        """Test bulk payroll calculation"""
        # Create another employee
        user2 = User.objects.create_user(
            username="employee2",
            email="<EMAIL>",
            password="testpass123",
            first_name="Jane",
            last_name="Smith"
        )
        
        employee2 = Employee.objects.create(
            school=self.school,
            user=user2,
            employee_id="EMP002",
            position=self.position,
            hire_date=date(2024, 1, 1),
            employment_status='active',
            salary=Decimal('6000.00'),
            emergency_contact_name="John Smith",
            emergency_contact_phone="9876543211",
            emergency_contact_relationship="Spouse",
            created_by=self.admin_user
        )
        
        # Create attendance for both employees
        for day in range(1, 23):
            if date(2025, 1, day).weekday() < 5:
                for emp in [self.employee, employee2]:
                    AttendanceRecord.objects.create(
                        school=self.school,
                        employee=emp,
                        date=date(2025, 1, day),
                        check_in_time="09:00",
                        check_out_time="17:00",
                        status='present',
                        created_by=self.admin_user
                    )
        
        service = PayrollCalculationService(self.payroll_period)
        results = service.calculate_bulk_payroll(user=self.admin_user)
        
        self.assertEqual(len(results['success']), 2)
        self.assertEqual(len(results['errors']), 0)
        
        # Check that both payrolls were created
        payrolls = Payroll.objects.filter(period=self.payroll_period)
        self.assertEqual(payrolls.count(), 2)
    
    def test_payslip_generation(self):
        """Test payslip generation"""
        # First calculate payroll
        service = PayrollCalculationService(self.payroll_period)
        
        # Create attendance records
        for day in range(1, 23):
            if date(2025, 1, day).weekday() < 5:
                AttendanceRecord.objects.create(
                    school=self.school,
                    employee=self.employee,
                    date=date(2025, 1, day),
                    check_in_time="09:00",
                    check_out_time="17:00",
                    status='present',
                    created_by=self.admin_user
                )
        
        payroll = service.calculate_employee_payroll(self.employee, self.admin_user)
        
        # Generate payslip
        payslip = PayslipService.generate_payslip(payroll, self.admin_user)
        
        self.assertIsNotNone(payslip)
        self.assertEqual(payslip.payroll, payroll)
        self.assertEqual(payslip.generated_by, self.admin_user)
        self.assertIsNotNone(payslip.payslip_number)
        self.assertFalse(payslip.is_sent)
    
    def test_payslip_generation_without_calculation(self):
        """Test payslip generation fails without payroll calculation"""
        # Create payroll without calculation
        payroll = Payroll.objects.create(
            school=self.school,
            employee=self.employee,
            period=self.payroll_period,
            basic_salary=Decimal('7000.00'),
            is_calculated=False,
            created_by=self.admin_user
        )
        
        with self.assertRaises(ValidationError):
            PayslipService.generate_payslip(payroll, self.admin_user)
    
    def test_bulk_payslip_generation(self):
        """Test bulk payslip generation"""
        # Create and calculate payrolls for multiple employees
        user2 = User.objects.create_user(
            username="employee2",
            email="<EMAIL>",
            password="testpass123",
            first_name="Jane",
            last_name="Smith"
        )
        
        employee2 = Employee.objects.create(
            school=self.school,
            user=user2,
            employee_id="EMP002",
            position=self.position,
            hire_date=date(2024, 1, 1),
            employment_status='active',
            salary=Decimal('6000.00'),
            emergency_contact_name="John Smith",
            emergency_contact_phone="9876543211",
            emergency_contact_relationship="Spouse",
            created_by=self.admin_user
        )
        
        # Create attendance and calculate payrolls
        service = PayrollCalculationService(self.payroll_period)
        
        for day in range(1, 23):
            if date(2025, 1, day).weekday() < 5:
                for emp in [self.employee, employee2]:
                    AttendanceRecord.objects.create(
                        school=self.school,
                        employee=emp,
                        date=date(2025, 1, day),
                        check_in_time="09:00",
                        check_out_time="17:00",
                        status='present',
                        created_by=self.admin_user
                    )
        
        service.calculate_bulk_payroll(user=self.admin_user)
        
        # Generate bulk payslips
        payrolls = Payroll.objects.filter(period=self.payroll_period, is_calculated=True)
        results = PayslipService.generate_bulk_payslips(payrolls, self.admin_user)
        
        self.assertEqual(len(results['success']), 2)
        self.assertEqual(len(results['errors']), 0)
        
        # Check that payslips were created
        payslips = Payslip.objects.filter(payroll__period=self.payroll_period)
        self.assertEqual(payslips.count(), 2)
    
    def test_payroll_summary_report(self):
        """Test payroll summary report generation"""
        # Create and calculate payroll
        service = PayrollCalculationService(self.payroll_period)
        
        for day in range(1, 23):
            if date(2025, 1, day).weekday() < 5:
                AttendanceRecord.objects.create(
                    school=self.school,
                    employee=self.employee,
                    date=date(2025, 1, day),
                    check_in_time="09:00",
                    check_out_time="17:00",
                    status='present',
                    created_by=self.admin_user
                )
        
        service.calculate_employee_payroll(self.employee, self.admin_user)
        
        # Generate summary report
        summary = PayrollReportService.generate_payroll_summary(self.payroll_period)
        
        self.assertEqual(summary['period'], self.payroll_period)
        self.assertEqual(summary['total_employees'], 1)
        self.assertGreater(summary['total_basic_salary'], 0)
        self.assertGreater(summary['total_allowances'], 0)
        self.assertGreater(summary['total_net_salary'], 0)
        self.assertEqual(summary['paid_count'], 0)
        self.assertEqual(summary['unpaid_count'], 1)
    
    def test_department_payroll_report(self):
        """Test department payroll report generation"""
        # Create and calculate payroll
        service = PayrollCalculationService(self.payroll_period)
        
        for day in range(1, 23):
            if date(2025, 1, day).weekday() < 5:
                AttendanceRecord.objects.create(
                    school=self.school,
                    employee=self.employee,
                    date=date(2025, 1, day),
                    check_in_time="09:00",
                    check_out_time="17:00",
                    status='present',
                    created_by=self.admin_user
                )
        
        service.calculate_employee_payroll(self.employee, self.admin_user)
        
        # Generate department report
        departments = PayrollReportService.generate_department_payroll_report(self.payroll_period)
        
        self.assertGreater(departments.count(), 0)
    
    def test_employee_payroll_history(self):
        """Test employee payroll history report"""
        # Create multiple payroll periods and calculate payrolls
        period2 = PayrollPeriod.objects.create(
            school=self.school,
            name="February 2025",
            start_date=date(2025, 2, 1),
            end_date=date(2025, 2, 28),
            created_by=self.admin_user
        )
        
        # Calculate payroll for both periods
        for period in [self.payroll_period, period2]:
            service = PayrollCalculationService(period)
            
            # Create attendance for the period
            start_day = period.start_date.day
            end_day = period.end_date.day
            month = period.start_date.month
            year = period.start_date.year
            
            for day in range(start_day, min(end_day + 1, 23)):
                current_date = date(year, month, day)
                if current_date.weekday() < 5:
                    AttendanceRecord.objects.create(
                        school=self.school,
                        employee=self.employee,
                        date=current_date,
                        check_in_time="09:00",
                        check_out_time="17:00",
                        status='present',
                        created_by=self.admin_user
                    )
            
            service.calculate_employee_payroll(self.employee, self.admin_user)
        
        # Generate history report
        history = PayrollReportService.generate_employee_payroll_history(self.employee)
        
        self.assertEqual(history.count(), 2)
        
        # Test with date range
        history_filtered = PayrollReportService.generate_employee_payroll_history(
            self.employee,
            start_date=date(2025, 1, 1),
            end_date=date(2025, 1, 31)
        )
        
        self.assertEqual(history_filtered.count(), 1)
    
    def test_payroll_calculation_error_handling(self):
        """Test payroll calculation error handling"""
        service = PayrollCalculationService(self.payroll_period)
        
        # Try to calculate payroll twice
        for day in range(1, 23):
            if date(2025, 1, day).weekday() < 5:
                AttendanceRecord.objects.create(
                    school=self.school,
                    employee=self.employee,
                    date=date(2025, 1, day),
                    check_in_time="09:00",
                    check_out_time="17:00",
                    status='present',
                    created_by=self.admin_user
                )
        
        # First calculation should succeed
        payroll = service.calculate_employee_payroll(self.employee, self.admin_user)
        self.assertTrue(payroll.is_calculated)
        
        # Second calculation should fail
        with self.assertRaises(ValidationError):
            service.calculate_employee_payroll(self.employee, self.admin_user)
    
    def test_payroll_net_salary_calculation(self):
        """Test net salary calculation accuracy"""
        service = PayrollCalculationService(self.payroll_period)
        
        # Create full attendance
        for day in range(1, 23):
            if date(2025, 1, day).weekday() < 5:
                AttendanceRecord.objects.create(
                    school=self.school,
                    employee=self.employee,
                    date=date(2025, 1, day),
                    check_in_time="09:00",
                    check_out_time="17:00",
                    status='present',
                    created_by=self.admin_user
                )
        
        payroll = service.calculate_employee_payroll(self.employee, self.admin_user)
        
        # Manual calculation
        expected_gross = payroll.basic_salary + payroll.total_allowances + payroll.overtime_amount
        expected_net = expected_gross - payroll.total_deductions - payroll.tax_deduction - payroll.insurance_deduction
        
        self.assertAlmostEqual(
            float(payroll.gross_salary),
            float(expected_gross),
            places=2
        )
        
        self.assertAlmostEqual(
            float(payroll.net_salary),
            float(expected_net),
            places=2
        )


if __name__ == '__main__':
    pytest.main([__file__])