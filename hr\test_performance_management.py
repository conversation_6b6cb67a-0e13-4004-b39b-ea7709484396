"""
Tests for performance management functionality
"""
import pytest
from decimal import Decimal
from datetime import date, datetime, timedelta
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.utils import timezone

from core.models import School, AcademicYear
from accounts.models import User
from .models import (
    Department, Position, Employee, PerformanceEvaluation,
    PerformanceReviewCycle, PerformanceGoal, PerformanceImprovementPlan,
    PerformanceImprovementAction, PerformanceMetric, EmployeePerformanceMetric
)
from .performance_services import (
    PerformanceEvaluationService, PerformanceAnalyticsService, GoalManagementService
)
  

@pytest.mark.django_db
class TestPerformanceEvaluationService:
    """Test performance evaluation service"""
    
    @pytest.fixture
    def setup_data(self):
        """Setup test data"""
        # Create school
        school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today() - timedelta(days=365)
        )
        
        # Create users
        evaluator_user = User.objects.create_user(
            username="evaluator",
            email="<EMAIL>",
            password="testpass123",
            first_name="John",
            last_name="Evaluator"
        )
        
        employee_user = User.objects.create_user(
            username="employee",
            email="<EMAIL>",
            password="testpass123",
            first_name="Jane",
            last_name="Employee"
        )
        
        # Create department and position
        department = Department.objects.create(
            school=school,
            name="IT Department",
            code="IT",
            created_by=evaluator_user
        )
        
        position = Position.objects.create(
            school=school,
            title="Software Developer",
            department=department,
            min_salary=Decimal('50000.00'),
            max_salary=Decimal('80000.00'),
            created_by=evaluator_user
        )
        
        # Create employees
        evaluator = Employee.objects.create(
            school=school,
            user=evaluator_user,
            employee_id="EMP001",
            position=position,
            hire_date=date.today() - timedelta(days=365),
            salary=Decimal('70000.00'),
            emergency_contact_name="Emergency Contact",
            emergency_contact_phone="************",
            emergency_contact_relationship="Spouse",
            created_by=evaluator_user
        )
        
        employee = Employee.objects.create(
            school=school,
            user=employee_user,
            employee_id="EMP002",
            position=position,
            hire_date=date.today() - timedelta(days=180),
            salary=Decimal('60000.00'),
            emergency_contact_name="Emergency Contact",
            emergency_contact_phone="************",
            emergency_contact_relationship="Parent",
            created_by=evaluator_user
        )
        
        return {
            'school': school,
            'evaluator_user': evaluator_user,
            'employee_user': employee_user,
            'evaluator': evaluator,
            'employee': employee,
            'department': department,
            'position': position
        }
    
    def test_create_evaluation_success(self, setup_data):
        """Test successful evaluation creation"""
        data = setup_data
        
        ratings = {
            'quality_of_work': 4,
            'productivity': 4,
            'communication': 3,
            'teamwork': 4,
            'punctuality': 5,
            'initiative': 3
        }
        
        evaluation = PerformanceEvaluationService.create_evaluation(
            employee=data['employee'],
            evaluator=data['evaluator_user'],
            evaluation_period_start=date.today() - timedelta(days=90),
            evaluation_period_end=date.today(),
            ratings=ratings,
            strengths="Good technical skills",
            areas_for_improvement="Communication needs improvement",
            goals_for_next_period="Improve presentation skills",
            user=data['evaluator_user']
        )
        
        assert evaluation is not None
        assert evaluation.employee == data['employee']
        assert evaluation.evaluator == data['evaluator_user']
        assert evaluation.quality_of_work == 4
        assert evaluation.overall_rating == 4  # Average of ratings rounded
        assert not evaluation.is_finalized
    
    def test_create_evaluation_overlapping_period(self, setup_data):
        """Test evaluation creation with overlapping period fails"""
        data = setup_data
        
        # Create first evaluation
        ratings = {'quality_of_work': 4, 'productivity': 4, 'communication': 3,
                  'teamwork': 4, 'punctuality': 5, 'initiative': 3}
        
        PerformanceEvaluationService.create_evaluation(
            employee=data['employee'],
            evaluator=data['evaluator_user'],
            evaluation_period_start=date.today() - timedelta(days=90),
            evaluation_period_end=date.today(),
            ratings=ratings,
            strengths="Good work",
            areas_for_improvement="None",
            goals_for_next_period="Continue good work",
            user=data['evaluator_user']
        )
        
        # Try to create overlapping evaluation
        with pytest.raises(ValidationError):
            PerformanceEvaluationService.create_evaluation(
                employee=data['employee'],
                evaluator=data['evaluator_user'],
                evaluation_period_start=date.today() - timedelta(days=60),
                evaluation_period_end=date.today() + timedelta(days=30),
                ratings=ratings,
                strengths="Good work",
                areas_for_improvement="None",
                goals_for_next_period="Continue good work",
                user=data['evaluator_user']
            )
    
    def test_update_evaluation_success(self, setup_data):
        """Test successful evaluation update"""
        data = setup_data
        
        # Create evaluation
        ratings = {'quality_of_work': 3, 'productivity': 3, 'communication': 3,
                  'teamwork': 3, 'punctuality': 3, 'initiative': 3}
        
        evaluation = PerformanceEvaluationService.create_evaluation(
            employee=data['employee'],
            evaluator=data['evaluator_user'],
            evaluation_period_start=date.today() - timedelta(days=90),
            evaluation_period_end=date.today(),
            ratings=ratings,
            strengths="Average performance",
            areas_for_improvement="All areas",
            goals_for_next_period="Improve all areas",
            user=data['evaluator_user']
        )
        
        # Update evaluation
        new_ratings = {'quality_of_work': 4, 'productivity': 4, 'communication': 4,
                      'teamwork': 4, 'punctuality': 4, 'initiative': 4}
        
        updated_evaluation = PerformanceEvaluationService.update_evaluation(
            evaluation=evaluation,
            ratings=new_ratings,
            strengths="Improved performance",
            areas_for_improvement="Minor improvements needed",
            goals_for_next_period="Continue improvement",
            employee_comments="I agree with the assessment"
        )
        
        assert updated_evaluation.quality_of_work == 4
        assert updated_evaluation.overall_rating == 4
        assert updated_evaluation.strengths == "Improved performance"
        assert updated_evaluation.employee_comments == "I agree with the assessment"
    
    def test_finalize_evaluation_success(self, setup_data):
        """Test successful evaluation finalization"""
        data = setup_data
        
        # Create evaluation
        ratings = {'quality_of_work': 4, 'productivity': 4, 'communication': 3,
                  'teamwork': 4, 'punctuality': 5, 'initiative': 3}
        
        evaluation = PerformanceEvaluationService.create_evaluation(
            employee=data['employee'],
            evaluator=data['evaluator_user'],
            evaluation_period_start=date.today() - timedelta(days=90),
            evaluation_period_end=date.today(),
            ratings=ratings,
            strengths="Good work",
            areas_for_improvement="Communication",
            goals_for_next_period="Improve communication",
            user=data['evaluator_user']
        )
        
        # Finalize evaluation
        finalized_evaluation = PerformanceEvaluationService.finalize_evaluation(
            evaluation, data['evaluator_user']
        )
        
        assert finalized_evaluation.is_finalized
        assert finalized_evaluation.finalized_at is not None
    
    def test_get_due_evaluations(self, setup_data):
        """Test getting employees due for evaluation"""
        data = setup_data
        
        # Employee hired over a year ago should be due for evaluation
        old_employee_user = User.objects.create_user(
            username="old_employee",
            email="<EMAIL>",
            password="testpass123",
            first_name="Old",
            last_name="Employee"
        )
        
        old_employee = Employee.objects.create(
            school=data['school'],
            user=old_employee_user,
            employee_id="EMP003",
            position=data['position'],
            hire_date=date.today() - timedelta(days=400),
            salary=Decimal('55000.00'),
            emergency_contact_name="Emergency Contact",
            emergency_contact_phone="************",
            emergency_contact_relationship="Spouse",
            created_by=data['evaluator_user']
        )
        
        due_evaluations = PerformanceEvaluationService.get_due_evaluations(data['school'])
        
        # Should include the old employee
        due_employee_ids = [item['employee'].id for item in due_evaluations]
        assert old_employee.id in due_employee_ids


@pytest.mark.django_db
class TestPerformanceAnalyticsService:
    """Test performance analytics service"""
    
    @pytest.fixture
    def setup_analytics_data(self):
        """Setup test data for analytics"""
        # Create school
        school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today() - timedelta(days=365)
        )
        
        # Create users
        evaluator_user = User.objects.create_user(
            username="evaluator",
            email="<EMAIL>",
            password="testpass123",
            first_name="John",
            last_name="Evaluator"
        )
        
        employee_user = User.objects.create_user(
            username="employee",
            email="<EMAIL>",
            password="testpass123",
            first_name="Jane",
            last_name="Employee"
        )
        
        # Create department and position
        department = Department.objects.create(
            school=school,
            name="IT Department",
            code="IT",
            created_by=evaluator_user
        )
        
        position = Position.objects.create(
            school=school,
            title="Software Developer",
            department=department,
            min_salary=Decimal('50000.00'),
            max_salary=Decimal('80000.00'),
            created_by=evaluator_user
        )
        
        # Create employee
        employee = Employee.objects.create(
            school=school,
            user=employee_user,
            employee_id="EMP001",
            position=position,
            hire_date=date.today() - timedelta(days=365),
            salary=Decimal('60000.00'),
            emergency_contact_name="Emergency Contact",
            emergency_contact_phone="************",
            emergency_contact_relationship="Parent",
            created_by=evaluator_user
        )
        
        # Create evaluations
        evaluation1 = PerformanceEvaluation.objects.create(
            school=school,
            employee=employee,
            evaluator=evaluator_user,
            evaluation_period_start=date.today() - timedelta(days=180),
            evaluation_period_end=date.today() - timedelta(days=90),
            quality_of_work=4,
            productivity=4,
            communication=3,
            teamwork=4,
            punctuality=5,
            initiative=3,
            overall_rating=4,
            strengths="Good technical skills",
            areas_for_improvement="Communication",
            goals_for_next_period="Improve communication",
            is_finalized=True,
            finalized_at=timezone.now(),
            created_by=evaluator_user
        )
        
        evaluation2 = PerformanceEvaluation.objects.create(
            school=school,
            employee=employee,
            evaluator=evaluator_user,
            evaluation_period_start=date.today() - timedelta(days=90),
            evaluation_period_end=date.today(),
            quality_of_work=5,
            productivity=4,
            communication=4,
            teamwork=5,
            punctuality=5,
            initiative=4,
            overall_rating=4,
            strengths="Improved communication",
            areas_for_improvement="Continue growth",
            goals_for_next_period="Leadership development",
            is_finalized=True,
            finalized_at=timezone.now(),
            created_by=evaluator_user
        )
        
        return {
            'school': school,
            'employee': employee,
            'department': department,
            'evaluations': [evaluation1, evaluation2]
        }
    
    def test_employee_performance_summary(self, setup_analytics_data):
        """Test employee performance summary generation"""
        data = setup_analytics_data
        
        summary = PerformanceAnalyticsService.get_employee_performance_summary(
            data['employee'], timezone.now().year
        )
        
        assert summary['employee'] == data['employee']
        assert summary['total_evaluations'] == 2
        assert 'average_ratings' in summary
        assert 'performance_trend' in summary
        assert len(summary['performance_trend']) == 2
        assert summary['average_ratings']['overall'] > 0
    
    def test_department_performance_summary(self, setup_analytics_data):
        """Test department performance summary generation"""
        data = setup_analytics_data
        
        summary = PerformanceAnalyticsService.get_department_performance_summary(
            data['department'], timezone.now().year
        )
        
        assert summary['department'] == data['department']
        assert summary['total_employees'] >= 1
        assert summary['evaluated_employees'] >= 1
        assert 'average_ratings' in summary
        assert 'performance_distribution' in summary
    
    def test_school_performance_overview(self, setup_analytics_data):
        """Test school performance overview generation"""
        data = setup_analytics_data
        
        overview = PerformanceAnalyticsService.get_school_performance_overview(
            data['school'], timezone.now().year
        )
        
        assert overview['school'] == data['school']
        assert overview['total_employees'] >= 1
        assert overview['total_evaluations'] >= 2
        assert 'performance_distribution' in overview
        assert 'department_performance' in overview


@pytest.mark.django_db
class TestPerformanceModels:
    """Test performance management models"""
    
    @pytest.fixture
    def setup_model_data(self):
        """Setup test data for models"""
        # Create school
        school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today() - timedelta(days=365)
        )
        
        # Create user
        user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User"
        )
        
        # Create department and position
        department = Department.objects.create(
            school=school,
            name="IT Department",
            code="IT",
            created_by=user
        )
        
        position = Position.objects.create(
            school=school,
            title="Software Developer",
            department=department,
            min_salary=Decimal('50000.00'),
            max_salary=Decimal('80000.00'),
            created_by=user
        )
        
        # Create employee
        employee = Employee.objects.create(
            school=school,
            user=user,
            employee_id="EMP001",
            position=position,
            hire_date=date.today() - timedelta(days=180),
            salary=Decimal('60000.00'),
            emergency_contact_name="Emergency Contact",
            emergency_contact_phone="************",
            emergency_contact_relationship="Parent",
            created_by=user
        )
        
        return {
            'school': school,
            'user': user,
            'employee': employee,
            'department': department,
            'position': position
        }
    
    def test_performance_review_cycle_creation(self, setup_model_data):
        """Test performance review cycle creation"""
        data = setup_model_data
        
        cycle = PerformanceReviewCycle.objects.create(
            school=data['school'],
            name="Annual Review 2025",
            start_date=date.today(),
            end_date=date.today() + timedelta(days=365),
            description="Annual performance review cycle",
            created_by=data['user']
        )
        
        assert cycle.name == "Annual Review 2025"
        assert cycle.is_active
        assert str(cycle) == f"Annual Review 2025 ({cycle.start_date} - {cycle.end_date})"
    
    def test_performance_goal_creation(self, setup_model_data):
        """Test performance goal creation"""
        data = setup_model_data
        
        goal = PerformanceGoal.objects.create(
            school=data['school'],
            employee=data['employee'],
            title="Complete Python Certification",
            description="Complete advanced Python certification course",
            target_date=date.today() + timedelta(days=90),
            priority='high',
            assigned_by=data['user'],
            created_by=data['user']
        )
        
        assert goal.title == "Complete Python Certification"
        assert goal.status == 'not_started'
        assert goal.progress_percentage == 0
        assert not goal.is_overdue
    
    def test_performance_goal_overdue_check(self, setup_model_data):
        """Test performance goal overdue check"""
        data = setup_model_data
        
        # Create overdue goal
        goal = PerformanceGoal.objects.create(
            school=data['school'],
            employee=data['employee'],
            title="Overdue Goal",
            description="This goal is overdue",
            target_date=date.today() - timedelta(days=10),
            assigned_by=data['user'],
            created_by=data['user']
        )
        
        assert goal.is_overdue
    
    def test_performance_improvement_plan_creation(self, setup_model_data):
        """Test performance improvement plan creation"""
        data = setup_model_data
        
        # Create evaluation first
        evaluation = PerformanceEvaluation.objects.create(
            school=data['school'],
            employee=data['employee'],
            evaluator=data['user'],
            evaluation_period_start=date.today() - timedelta(days=90),
            evaluation_period_end=date.today(),
            quality_of_work=2,
            productivity=2,
            communication=2,
            teamwork=2,
            punctuality=3,
            initiative=2,
            overall_rating=2,
            strengths="Shows potential",
            areas_for_improvement="All areas need improvement",
            goals_for_next_period="Focus on basic skills",
            created_by=data['user']
        )
        
        # Create improvement plan
        plan = PerformanceImprovementPlan.objects.create(
            school=data['school'],
            employee=data['employee'],
            evaluation=evaluation,
            title="Performance Improvement Plan",
            description="Plan to improve overall performance",
            start_date=date.today(),
            target_completion_date=date.today() + timedelta(days=90),
            success_criteria="Achieve rating of 3 or higher in all areas",
            supervisor=data['user'],
            created_by=data['user']
        )
        
        assert plan.title == "Performance Improvement Plan"
        assert plan.status == 'active'
        assert plan.duration_days == 90
        assert not plan.is_overdue
    
    def test_performance_metric_creation(self, setup_model_data):
        """Test performance metric creation"""
        data = setup_model_data
        
        metric = PerformanceMetric.objects.create(
            school=data['school'],
            name="Code Quality Score",
            description="Measure of code quality based on reviews",
            metric_type='quantitative',
            department=data['department'],
            target_value=Decimal('85.00'),
            unit_of_measure='percentage',
            created_by=data['user']
        )
        
        assert metric.name == "Code Quality Score"
        assert metric.metric_type == 'quantitative'
        assert metric.target_value == Decimal('85.00')
        assert metric.is_active
    
    def test_employee_performance_metric_tracking(self, setup_model_data):
        """Test employee performance metric tracking"""
        data = setup_model_data
        
        # Create metric
        metric = PerformanceMetric.objects.create(
            school=data['school'],
            name="Code Quality Score",
            description="Measure of code quality",
            metric_type='quantitative',
            target_value=Decimal('85.00'),
            unit_of_measure='percentage',
            created_by=data['user']
        )
        
        # Create employee metric
        emp_metric = EmployeePerformanceMetric.objects.create(
            school=data['school'],
            employee=data['employee'],
            metric=metric,
            actual_value=Decimal('78.50'),
            measurement_date=date.today(),
            notes="Good improvement from last month",
            created_by=data['user']
        )
        
        assert emp_metric.actual_value == Decimal('78.50')
        assert emp_metric.achievement_percentage > 90  # 78.5/85 * 100
        assert emp_metric.achievement_percentage < 95


@pytest.mark.django_db
class TestGoalManagementService:
    """Test goal management service"""
    
    @pytest.fixture
    def setup_goal_data(self):
        """Setup test data for goal management"""
        # Create school
        school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today() - timedelta(days=365)
        )
        
        # Create user
        user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User"
        )
        
        # Create department and position
        department = Department.objects.create(
            school=school,
            name="IT Department",
            code="IT",
            created_by=user
        )
        
        position = Position.objects.create(
            school=school,
            title="Software Developer",
            department=department,
            min_salary=Decimal('50000.00'),
            max_salary=Decimal('80000.00'),
            created_by=user
        )
        
        # Create employee
        employee = Employee.objects.create(
            school=school,
            user=user,
            employee_id="EMP001",
            position=position,
            hire_date=date.today() - timedelta(days=180),
            salary=Decimal('60000.00'),
            emergency_contact_name="Emergency Contact",
            emergency_contact_phone="************",
            emergency_contact_relationship="Parent",
            created_by=user
        )
        
        # Create evaluation with goals
        evaluation = PerformanceEvaluation.objects.create(
            school=school,
            employee=employee,
            evaluator=user,
            evaluation_period_start=date.today() - timedelta(days=90),
            evaluation_period_end=date.today(),
            quality_of_work=4,
            productivity=4,
            communication=3,
            teamwork=4,
            punctuality=5,
            initiative=3,
            overall_rating=4,
            strengths="Good technical skills",
            areas_for_improvement="Communication needs improvement",
            goals_for_next_period="Improve presentation skills\nComplete certification\nLead a project",
            is_finalized=True,
            finalized_at=timezone.now(),
            created_by=user
        )
        
        return {
            'school': school,
            'user': user,
            'employee': employee,
            'evaluation': evaluation
        }
    
    def test_extract_goals_from_evaluation(self, setup_goal_data):
        """Test extracting goals from evaluation"""
        data = setup_goal_data
        
        goals = GoalManagementService.extract_goals_from_evaluation(data['evaluation'])
        
        assert len(goals) == 3
        assert goals[0]['description'] == "Improve presentation skills"
        assert goals[1]['description'] == "Complete certification"
        assert goals[2]['description'] == "Lead a project"
        assert all(goal['status'] == 'pending' for goal in goals)
    
    def test_get_employee_goals(self, setup_goal_data):
        """Test getting employee goals"""
        data = setup_goal_data
        
        goals = GoalManagementService.get_employee_goals(
            data['employee'], timezone.now().year
        )
        
        assert len(goals) >= 3
        assert all(goal['evaluation'] == data['evaluation'] for goal in goals)
    
    def test_generate_improvement_plan(self, setup_goal_data):
        """Test generating improvement plan"""
        data = setup_goal_data
        
        # Create evaluation with low ratings
        low_evaluation = PerformanceEvaluation.objects.create(
            school=data['school'],
            employee=data['employee'],
            evaluator=data['user'],
            evaluation_period_start=date.today() - timedelta(days=180),
            evaluation_period_end=date.today() - timedelta(days=90),
            quality_of_work=2,
            productivity=1,
            communication=2,
            teamwork=3,
            punctuality=4,
            initiative=2,
            overall_rating=2,
            strengths="Shows effort",
            areas_for_improvement="Quality control\nTime management\nTechnical skills",
            goals_for_next_period="Focus on improvement",
            created_by=data['user']
        )
        
        improvement_plan = GoalManagementService.generate_improvement_plan(low_evaluation)
        
        assert improvement_plan['evaluation'] == low_evaluation
        assert len(improvement_plan['improvement_areas']) > 0
        assert len(improvement_plan['action_items']) >= 3
        
        # Check that low-rated areas are identified
        improvement_area_names = [area['area'] for area in improvement_plan['improvement_areas']]
        assert 'Quality of Work' in improvement_area_names
        assert 'Productivity' in improvement_area_names


class TestPerformanceManagementIntegration(TestCase):
    """Integration tests for performance management system"""
    
    def setUp(self):
        """Setup test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today() - timedelta(days=365)
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User"
        )
        
        self.department = Department.objects.create(
            school=self.school,
            name="IT Department",
            code="IT",
            created_by=self.user
        )
        
        self.position = Position.objects.create(
            school=self.school,
            title="Software Developer",
            department=self.department,
            min_salary=Decimal('50000.00'),
            max_salary=Decimal('80000.00'),
            created_by=self.user
        )
        
        self.employee = Employee.objects.create(
            school=self.school,
            user=self.user,
            employee_id="EMP001",
            position=self.position,
            hire_date=date.today() - timedelta(days=180),
            salary=Decimal('60000.00'),
            emergency_contact_name="Emergency Contact",
            emergency_contact_phone="************",
            emergency_contact_relationship="Parent",
            created_by=self.user
        )
    
    def test_complete_performance_cycle(self):
        """Test complete performance management cycle"""
        # 1. Create review cycle
        cycle = PerformanceReviewCycle.objects.create(
            school=self.school,
            name="Q1 Review 2025",
            start_date=date.today() - timedelta(days=90),
            end_date=date.today(),
            evaluation_deadline=date.today() + timedelta(days=30),
            created_by=self.user
        )
        
        # 2. Create performance evaluation
        ratings = {
            'quality_of_work': 3,
            'productivity': 2,
            'communication': 2,
            'teamwork': 3,
            'punctuality': 4,
            'initiative': 2
        }
        
        evaluation = PerformanceEvaluationService.create_evaluation(
            employee=self.employee,
            evaluator=self.user,
            evaluation_period_start=cycle.start_date,
            evaluation_period_end=cycle.end_date,
            ratings=ratings,
            strengths="Good attendance and teamwork",
            areas_for_improvement="Quality control and communication skills",
            goals_for_next_period="Improve technical skills and communication",
            user=self.user
        )
        
        # 3. Create performance goals
        goal1 = PerformanceGoal.objects.create(
            school=self.school,
            employee=self.employee,
            title="Complete Technical Training",
            description="Complete advanced technical training course",
            target_date=date.today() + timedelta(days=60),
            priority='high',
            assigned_by=self.user,
            evaluation=evaluation,
            created_by=self.user
        )
        
        goal2 = PerformanceGoal.objects.create(
            school=self.school,
            employee=self.employee,
            title="Improve Communication Skills",
            description="Attend communication workshop and practice presentations",
            target_date=date.today() + timedelta(days=45),
            priority='medium',
            assigned_by=self.user,
            evaluation=evaluation,
            created_by=self.user
        )
        
        # 4. Create improvement plan (since overall rating is low)
        improvement_plan = PerformanceImprovementPlan.objects.create(
            school=self.school,
            employee=self.employee,
            evaluation=evaluation,
            title="Performance Improvement Plan - Q1 2025",
            description="Plan to address performance gaps identified in Q1 review",
            start_date=date.today(),
            target_completion_date=date.today() + timedelta(days=90),
            success_criteria="Achieve rating of 3 or higher in all performance areas",
            supervisor=self.user,
            created_by=self.user
        )
        
        # 5. Create improvement actions
        action1 = PerformanceImprovementAction.objects.create(
            school=self.school,
            improvement_plan=improvement_plan,
            title="Enroll in Quality Control Training",
            description="Complete quality control certification course",
            due_date=date.today() + timedelta(days=30),
            assigned_to=self.user,
            created_by=self.user
        )
        
        action2 = PerformanceImprovementAction.objects.create(
            school=self.school,
            improvement_plan=improvement_plan,
            title="Weekly One-on-One Meetings",
            description="Schedule weekly meetings with supervisor for feedback",
            due_date=date.today() + timedelta(days=7),
            assigned_to=self.user,
            created_by=self.user
        )
        
        # 6. Create performance metrics
        metric = PerformanceMetric.objects.create(
            school=self.school,
            name="Code Review Score",
            description="Average score from code reviews",
            metric_type='quantitative',
            department=self.department,
            target_value=Decimal('80.00'),
            unit_of_measure='percentage',
            created_by=self.user
        )
        
        emp_metric = EmployeePerformanceMetric.objects.create(
            school=self.school,
            employee=self.employee,
            metric=metric,
            evaluation=evaluation,
            actual_value=Decimal('65.00'),
            measurement_date=date.today(),
            notes="Below target, needs improvement",
            created_by=self.user
        )
        
        # 7. Finalize evaluation
        PerformanceEvaluationService.finalize_evaluation(evaluation, self.user)
        
        # Verify the complete cycle
        assert cycle.name == "Q1 Review 2025"
        assert evaluation.is_finalized
        assert evaluation.overall_rating == 3  # Average of ratings
        assert goal1.employee == self.employee
        assert goal2.employee == self.employee
        assert improvement_plan.status == 'active'
        assert action1.improvement_plan == improvement_plan
        assert action2.improvement_plan == improvement_plan
        assert emp_metric.achievement_percentage == 81.25  # 65/80 * 100
        
        # Test analytics
        summary = PerformanceAnalyticsService.get_employee_performance_summary(
            self.employee, timezone.now().year
        )
        assert summary['total_evaluations'] == 1
        assert summary['latest_evaluation'] == evaluation
        
        # Test goal extraction
        goals = GoalManagementService.extract_goals_from_evaluation(evaluation)
        assert len(goals) > 0
        
        # Test improvement plan generation
        generated_plan = GoalManagementService.generate_improvement_plan(evaluation)
        assert len(generated_plan['improvement_areas']) > 0
        assert len(generated_plan['action_items']) > 0
    
    def test_performance_analytics_comprehensive(self):
        """Test comprehensive performance analytics"""
        # Create multiple evaluations over time
        evaluations = []
        for i in range(3):
            eval_date = date.today() - timedelta(days=90 * i)
            rating = 3 + i  # Improving over time
            
            evaluation = PerformanceEvaluation.objects.create(
                school=self.school,
                employee=self.employee,
                evaluator=self.user,
                evaluation_period_start=eval_date - timedelta(days=90),
                evaluation_period_end=eval_date,
                quality_of_work=rating,
                productivity=rating,
                communication=rating,
                teamwork=rating,
                punctuality=rating,
                initiative=rating,
                overall_rating=rating,
                strengths=f"Performance level {rating}",
                areas_for_improvement="Continue improvement",
                goals_for_next_period="Maintain progress",
                is_finalized=True,
                finalized_at=timezone.now(),
                created_by=self.user
            )
            evaluations.append(evaluation)
        
        # Test employee performance summary
        summary = PerformanceAnalyticsService.get_employee_performance_summary(
            self.employee, timezone.now().year
        )
        
        assert summary['total_evaluations'] == 3
        assert len(summary['performance_trend']) == 3
        assert summary['average_ratings']['overall'] > 3
        
        # Test department performance summary
        dept_summary = PerformanceAnalyticsService.get_department_performance_summary(
            self.department, timezone.now().year
        )
        
        assert dept_summary['total_employees'] == 1
        assert dept_summary['evaluated_employees'] == 1
        assert dept_summary['evaluation_completion_rate'] == 100.0
        
        # Test school performance overview
        school_overview = PerformanceAnalyticsService.get_school_performance_overview(
            self.school, timezone.now().year
        )
        
        assert school_overview['total_employees'] == 1
        assert school_overview['total_evaluations'] == 3
        assert school_overview['evaluation_completion_rate'] == 100.0
        assert 'performance_distribution' in school_overview
        assert 'department_performance' in school_overview