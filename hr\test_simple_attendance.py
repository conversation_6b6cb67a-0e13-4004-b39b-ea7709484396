"""
Simple test to verify attendance functionality works
"""

from django.test import TestCase
from accounts.models import User
from django.utils import timezone
from datetime import date, time

from core.models import School
from .models import Employee, Department, Position, AttendanceRecord
from .attendance_services import AttendanceService


class SimpleAttendanceTest(TestCase):
    """Simple test for attendance functionality"""
    
    def setUp(self):
        """Set up test data"""
        # Create school
        self.school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test Street",
            phone="************",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2020, 1, 1)
        )
        
        # Create department
        self.department = Department.objects.create(
            school=self.school,
            name="Test Department",
            code="DEPT001"
        )
        
        # Create position
        self.position = Position.objects.create(
            school=self.school,
            title="Test Position",
            department=self.department,
            min_salary=30000,
            max_salary=50000
        )
        
        # Create user
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            first_name="<PERSON>",
            last_name="Doe"
        )
        
        # Create employee
        self.employee = Employee.objects.create(
            school=self.school,
            user=self.user,
            employee_id="EMP001",
            position=self.position,
            hire_date=date.today(),
            employment_status="active",
            salary=40000,
            emergency_contact_name="Jane Doe",
            emergency_contact_phone="************",
            emergency_contact_relationship="Spouse"
        )
    
    def test_mark_attendance_check_in(self):
        """Test marking check-in attendance"""
        result = AttendanceService.mark_attendance(
            employee_id=self.employee.employee_id,
            attendance_type='check_in',
            method='manual'
        )
        
        if not result['success']:
            print(f"Error: {result['message']}")
        self.assertTrue(result['success'])
        self.assertIn('check in', result['message'].lower())
        
        # Verify attendance record was created
        attendance = AttendanceRecord.objects.get(
            employee=self.employee,
            date=timezone.now().date()
        )
        self.assertIsNotNone(attendance.check_in_time)
        self.assertEqual(attendance.status, 'present')
    
    def test_attendance_record_total_hours(self):
        """Test total hours calculation"""
        test_date = date.today()
        
        attendance = AttendanceRecord.objects.create(
            school=self.school,
            employee=self.employee,
            date=test_date,
            check_in_time=time(9, 0),  # 9:00 AM
            check_out_time=time(17, 0),  # 5:00 PM
            status='present',
            created_by=self.user
        )
        
        # 8 hours total
        self.assertEqual(attendance.total_hours, 8.0)
    
    def test_overtime_calculation(self):
        """Test overtime calculation"""
        test_date = date.today()
        
        # Create attendance record with overtime
        AttendanceRecord.objects.create(
            school=self.school,
            employee=self.employee,
            date=test_date,
            check_in_time=time(9, 0),  # 9:00 AM
            check_out_time=time(19, 0),  # 7:00 PM (10 hours total)
            status='present',
            created_by=self.user
        )
        
        overtime = AttendanceService.calculate_overtime(self.employee, test_date)
        
        # 10 hours - 8 standard hours = 2 hours overtime
        self.assertEqual(overtime, 2.0)