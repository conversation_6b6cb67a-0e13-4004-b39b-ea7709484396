from django.urls import path
from . import views
from .attendance_views import (
    BiometricAttendanceView, QRCodeAttendanceView, AttendanceAnalyticsView,
    OvertimeReportView, AttendancePatternAnalysisView, BulkAttendanceView,
    AttendanceAPIView, BiometricDataProcessingView
)
from .performance_views import (
    performance_dashboard, evaluation_list, create_evaluation, evaluation_detail,
    edit_evaluation, finalize_evaluation, my_evaluations, due_evaluations,
    performance_reports, school_performance_report, department_performance_report,
    employee_performance_report, performance_trends_report, get_employee_performance_data,
    add_employee_comment
)

app_name = 'hr'

urlpatterns = [
    # HR Dashboard
    path('', views.HRDashboardView.as_view(), name='dashboard'),

    # Employee Management
    path('employees/', views.EmployeeListView.as_view(), name='employees'),
    path('employees/add/', views.EmployeeCreateView.as_view(), name='employee_add'),
    path('employees/create/', views.EmployeeCreateView.as_view(), name='employee_create'),  # Alias for admin dashboard
    path('employees/<uuid:pk>/', views.EmployeeDetailView.as_view(), name='employee_detail'),
    path('employees/<uuid:pk>/edit/', views.EmployeeUpdateView.as_view(), name='employee_edit'),
    path('employees/<uuid:pk>/delete/', views.EmployeeDeleteView.as_view(), name='employee_delete'),

    # Department Management
    path('departments/', views.DepartmentListView.as_view(), name='departments'),
    path('departments/add/', views.DepartmentCreateView.as_view(), name='department_add'),
    path('departments/<uuid:pk>/edit/', views.DepartmentUpdateView.as_view(), name='department_edit'),

    # Position Management
    path('positions/', views.PositionListView.as_view(), name='positions'),
    path('positions/add/', views.PositionCreateView.as_view(), name='position_add'),
    path('positions/<uuid:pk>/edit/', views.PositionUpdateView.as_view(), name='position_edit'),

    # General Settings
    path('registration-files/', views.RegistrationFilesView.as_view(), name='registration_files'),
    path('affairs-settings/', views.EmployeeAffairsSettingsView.as_view(), name='affairs_settings'),
    path('work-system/', views.WorkSystemView.as_view(), name='work_system'),
    path('attendance-devices/', views.AttendanceDevicesView.as_view(), name='attendance_devices'),
    path('attendance-devices-2/', views.AttendanceDevices2View.as_view(), name='attendance_devices_2'),
    path('permission-types/', views.PermissionTypesView.as_view(), name='permission_types'),
    path('holiday-types/', views.HolidayTypesView.as_view(), name='holiday_types'),
    path('allowance-types/', views.AllowanceTypesView.as_view(), name='allowance_types'),
    path('deduction-types/', views.DeductionTypesView.as_view(), name='deduction_types'),
    path('public-holidays/', views.PublicHolidaysView.as_view(), name='public_holidays'),

    # Work System Settings
    path('deduction-rules/', views.DeductionRulesView.as_view(), name='deduction_rules'),
    path('extra-time-rules/', views.ExtraTimeRulesView.as_view(), name='extra_time_rules'),
    path('rewards-rules/', views.RewardsRulesView.as_view(), name='rewards_rules'),
    path('penalties-rules/', views.PenaltiesRulesView.as_view(), name='penalties_rules'),

    # Attendance and Presence
    path('attendance/', views.AttendanceView.as_view(), name='attendance'),
    path('attendance/manual/', views.ManualAttendanceView.as_view(), name='manual_attendance'),
    path('attendance/bulk/', BulkAttendanceView.as_view(), name='bulk_attendance'),
    path('attendance/biometric/', BiometricAttendanceView.as_view(), name='biometric_attendance'),
    path('attendance/qr-code/', QRCodeAttendanceView.as_view(), name='qr_attendance'),
    path('attendance/analytics/', AttendanceAnalyticsView.as_view(), name='attendance_analytics'),
    path('attendance/overtime/', OvertimeReportView.as_view(), name='overtime_report'),
    path('attendance/patterns/<uuid:pk>/', AttendancePatternAnalysisView.as_view(), name='attendance_patterns'),
    path('attendance/reports/', views.AttendanceReportsView.as_view(), name='attendance_reports'),
    path('attendance/summary/', views.AttendanceSummaryView.as_view(), name='attendance_summary'),
    path('attendance/devices-data/', views.AttendanceDevicesDataView.as_view(), name='attendance_devices_data'),
    
    # API Endpoints
    path('api/attendance/', AttendanceAPIView.as_view(), name='attendance_api'),
    path('api/biometric/', BiometricDataProcessingView.as_view(), name='biometric_api'),

    # Permissions and Leaves
    path('permissions/', views.PermissionsView.as_view(), name='permissions'),
    path('permissions/add/', views.AddPermissionView.as_view(), name='add_permission'),
    path('permissions/approve/', views.ApprovePermissionsView.as_view(), name='approve_permissions'),
    path('permissions/reports/', views.PermissionReportsView.as_view(), name='permission_reports'),

    # Holidays and Vacations
    path('holidays/', views.HolidaysView.as_view(), name='holidays'),
    path('leaves/', views.VacationRequestsView.as_view(), name='leaves'),  # Alias for vacation requests
    path('vacation-requests/', views.VacationRequestsView.as_view(), name='vacation_requests'),
    path('vacation-balance/', views.VacationBalanceView.as_view(), name='vacation_balance'),

    # Payroll
    path('payroll/', views.PayrollView.as_view(), name='payroll'),
    path('payroll/generate/', views.GeneratePayrollView.as_view(), name='generate_payroll'),
    path('payroll/reports/', views.PayrollReportsView.as_view(), name='payroll_reports'),
    path('salary-structure/', views.SalaryStructureView.as_view(), name='salary_structure'),
    path('allowances/', views.AllowancesView.as_view(), name='allowances'),
    path('deductions/', views.DeductionsView.as_view(), name='deductions'),
    path('bonuses/', views.BonusesView.as_view(), name='bonuses'),

    # Performance and Evaluation
    path('performance/', views.PerformanceView.as_view(), name='performance'),
    
    # Performance Management URLs
    path('performance/dashboard/', performance_dashboard, name='performance_dashboard'),
    path('performance/evaluations/', evaluation_list, name='evaluation_list'),
    path('performance/evaluations/create/', create_evaluation, name='create_evaluation'),
    path('performance/evaluations/create/<int:employee_id>/', create_evaluation, name='create_evaluation_for_employee'),
    path('performance/evaluations/<int:evaluation_id>/', evaluation_detail, name='evaluation_detail'),
    path('performance/evaluations/<int:evaluation_id>/edit/', edit_evaluation, name='edit_evaluation'),
    path('performance/evaluations/<int:evaluation_id>/finalize/', finalize_evaluation, name='finalize_evaluation'),
    path('performance/my-evaluations/', my_evaluations, name='my_evaluations'),
    path('performance/due-evaluations/', due_evaluations, name='due_evaluations'),
    path('performance/reports/', performance_reports, name='performance_reports'),
    path('performance/reports/school/', school_performance_report, name='school_performance_report'),
    path('performance/reports/department/<int:department_id>/', department_performance_report, name='department_performance_report'),
    path('performance/reports/employee/<int:employee_id>/', employee_performance_report, name='employee_performance_report'),
    path('performance/reports/trends/', performance_trends_report, name='performance_trends_report'),
    
    # AJAX endpoints
    path('api/performance/employee/<int:employee_id>/', get_employee_performance_data, name='get_employee_performance_data'),
    path('api/performance/evaluations/<int:evaluation_id>/comment/', add_employee_comment, name='add_employee_comment'),

    # HR Reports
    path('reports/', views.HRReportsView.as_view(), name='reports'),
    path('reports/employee/', views.EmployeeReportsView.as_view(), name='employee_reports'),
    path('reports/attendance/', views.AttendanceReportsView.as_view(), name='attendance_reports'),
    path('reports/payroll/', views.PayrollReportsView.as_view(), name='payroll_reports'),
    path('reports/payroll_summary/export/', views.PayrollSummaryExportView.as_view(), name='payroll_summary_export'),
    path('reports/performance/', views.PerformanceReportsView.as_view(), name='performance_reports'),
    path('reports/export-all/', views.ExportAllReportsView.as_view(), name='export_all_reports'),
]
