from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from django.db.models import Count, Sum
from django.http import HttpResponse
import csv

from .models import (
    AssetCategory, Location, Supplier, Asset, AssetMovement, 
    AssetMaintenance, AssetDepreciation, AssetAudit, AssetAuditItem, 
    AssetAnalytics
)


@admin.register(AssetCategory)
class AssetCategoryAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'arabic_name', 'parent', 'depreciation_rate', 'useful_life_years', 'asset_count', 'is_active']
    list_filter = ['is_active', 'parent', 'depreciation_rate']
    search_fields = ['name', 'arabic_name', 'code', 'description']
    ordering = ['code']
    list_editable = ['is_active']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'arabic_name', 'code', 'description', 'parent')
        }),
        ('Depreciation Settings', {
            'fields': ('depreciation_rate', 'useful_life_years')
        }),
        ('Status', {
            'fields': ('is_active',)
        })
    )
    
    def asset_count(self, obj):
        count = obj.assets.filter(status='active').count()
        if count > 0:
            url = reverse('admin:inventory_asset_changelist') + f'?category__id__exact={obj.id}'
            return format_html('<a href="{}">{} assets</a>', url, count)
        return '0 assets'
    asset_count.short_description = 'Active Assets'


@admin.register(Location)
class LocationAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'arabic_name', 'building', 'floor', 'room', 'responsible_person', 'asset_count', 'is_active']
    list_filter = ['is_active', 'building', 'floor']
    search_fields = ['name', 'arabic_name', 'code', 'building', 'room']
    ordering = ['building', 'floor', 'room', 'name']
    list_editable = ['is_active']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'arabic_name', 'code', 'description')
        }),
        ('Location Details', {
            'fields': ('building', 'floor', 'room', 'responsible_person')
        }),
        ('Status', {
            'fields': ('is_active',)
        })
    )
    
    def asset_count(self, obj):
        count = obj.asset_set.filter(status='active').count()
        if count > 0:
            url = reverse('admin:inventory_asset_changelist') + f'?location__id__exact={obj.id}'
            return format_html('<a href="{}">{} assets</a>', url, count)
        return '0 assets'
    asset_count.short_description = 'Active Assets'


@admin.register(Supplier)
class SupplierAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'arabic_name', 'contact_person', 'phone', 'email', 'rating', 'asset_count', 'is_active']
    list_filter = ['is_active', 'rating']
    search_fields = ['name', 'arabic_name', 'code', 'contact_person', 'phone', 'email']
    ordering = ['name']
    list_editable = ['rating', 'is_active']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'arabic_name', 'code')
        }),
        ('Contact Information', {
            'fields': ('contact_person', 'phone', 'email', 'address')
        }),
        ('Business Information', {
            'fields': ('tax_number', 'payment_terms', 'rating')
        }),
        ('Status', {
            'fields': ('is_active',)
        })
    )
    
    def asset_count(self, obj):
        count = obj.asset_set.filter(status='active').count()
        if count > 0:
            url = reverse('admin:inventory_asset_changelist') + f'?supplier__id__exact={obj.id}'
            return format_html('<a href="{}">{} assets</a>', url, count)
        return '0 assets'
    asset_count.short_description = 'Active Assets'


class AssetMovementInline(admin.TabularInline):
    model = AssetMovement
    extra = 0
    readonly_fields = ['movement_date', 'created_by']
    fields = ['movement_type', 'from_location', 'to_location', 'from_employee', 'to_employee', 'reason', 'movement_date']
    
    def has_add_permission(self, request, obj=None):
        return False


class AssetMaintenanceInline(admin.TabularInline):
    model = AssetMaintenance
    extra = 0
    readonly_fields = ['created_at']
    fields = ['maintenance_type', 'status', 'scheduled_date', 'completed_date', 'description', 'cost']


@admin.register(Asset)
class AssetAdmin(admin.ModelAdmin):
    list_display = [
        'asset_tag', 'name', 'category', 'status_badge', 'condition_badge', 
        'location', 'assigned_to', 'purchase_price', 'current_value_display', 
        'warranty_status', 'maintenance_status'
    ]
    list_filter = [
        'status', 'condition', 'category', 'location', 'brand', 
        'purchase_date', 'warranty_expiry'
    ]
    search_fields = [
        'asset_tag', 'name', 'arabic_name', 'description', 'brand', 
        'model', 'serial_number', 'barcode', 'qr_code'
    ]
    ordering = ['asset_tag']
    readonly_fields = [
        'asset_tag', 'barcode', 'qr_code', 'age_in_years', 
        'accumulated_depreciation', 'book_value', 'created_at', 'updated_at'
    ]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('asset_tag', 'name', 'arabic_name', 'description', 'category', 'image')
        }),
        ('Physical Properties', {
            'fields': ('brand', 'model', 'serial_number', 'barcode', 'qr_code')
        }),
        ('Financial Information', {
            'fields': ('purchase_price', 'current_value', 'salvage_value', 'purchase_date', 'supplier', 'invoice_number', 'warranty_expiry')
        }),
        ('Location and Assignment', {
            'fields': ('location', 'assigned_to', 'department')
        }),
        ('Status and Condition', {
            'fields': ('status', 'condition', 'last_maintenance_date', 'next_maintenance_date')
        }),
        ('Depreciation', {
            'fields': ('depreciation_method', 'useful_life_years', 'depreciation_rate', 'age_in_years', 'accumulated_depreciation', 'book_value')
        }),
        ('Additional Information', {
            'fields': ('notes',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    inlines = [AssetMovementInline, AssetMaintenanceInline]
    
    actions = ['export_to_csv', 'update_depreciation', 'mark_for_maintenance']
    
    def status_badge(self, obj):
        colors = {
            'active': 'green',
            'maintenance': 'orange',
            'retired': 'gray',
            'disposed': 'red',
            'lost': 'red',
            'damaged': 'red'
        }
        color = colors.get(obj.status, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_status_display()
        )
    status_badge.short_description = 'Status'
    
    def condition_badge(self, obj):
        colors = {
            'excellent': 'green',
            'good': 'blue',
            'fair': 'orange',
            'poor': 'red',
            'damaged': 'red'
        }
        color = colors.get(obj.condition, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_condition_display()
        )
    condition_badge.short_description = 'Condition'
    
    def current_value_display(self, obj):
        return f"${obj.book_value:,.2f}"
    current_value_display.short_description = 'Book Value'
    
    def warranty_status(self, obj):
        if obj.warranty_expiry:
            if obj.is_warranty_valid:
                return format_html('<span style="color: green;">Valid until {}</span>', obj.warranty_expiry)
            else:
                return format_html('<span style="color: red;">Expired on {}</span>', obj.warranty_expiry)
        return 'No warranty'
    warranty_status.short_description = 'Warranty'
    
    def maintenance_status(self, obj):
        if obj.maintenance_due:
            return format_html('<span style="color: red; font-weight: bold;">Due</span>')
        elif obj.next_maintenance_date:
            return format_html('<span style="color: orange;">Due {}</span>', obj.next_maintenance_date)
        return 'No schedule'
    maintenance_status.short_description = 'Maintenance'
    
    def export_to_csv(self, request, queryset):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="assets.csv"'
        
        writer = csv.writer(response)
        writer.writerow([
            'Asset Tag', 'Name', 'Category', 'Status', 'Condition', 'Location',
            'Assigned To', 'Purchase Price', 'Current Value', 'Purchase Date',
            'Brand', 'Model', 'Serial Number'
        ])
        
        for asset in queryset:
            writer.writerow([
                asset.asset_tag, asset.name, asset.category.name, asset.status,
                asset.condition, str(asset.location) if asset.location else '',
                str(asset.assigned_to) if asset.assigned_to else '',
                asset.purchase_price, asset.book_value, asset.purchase_date,
                asset.brand, asset.model, asset.serial_number
            ])
        
        return response
    export_to_csv.short_description = "Export selected assets to CSV"
    
    def update_depreciation(self, request, queryset):
        updated = 0
        for asset in queryset:
            asset.update_current_value()
            updated += 1
        
        self.message_user(request, f"Updated depreciation for {updated} assets.")
    update_depreciation.short_description = "Update depreciation values"
    
    def mark_for_maintenance(self, request, queryset):
        updated = queryset.update(status='maintenance')
        self.message_user(request, f"Marked {updated} assets for maintenance.")
    mark_for_maintenance.short_description = "Mark for maintenance"


@admin.register(AssetMovement)
class AssetMovementAdmin(admin.ModelAdmin):
    list_display = ['asset', 'movement_type', 'from_location', 'to_location', 'from_employee', 'to_employee', 'movement_date', 'created_by']
    list_filter = ['movement_type', 'movement_date', 'from_location', 'to_location']
    search_fields = ['asset__asset_tag', 'asset__name', 'reason']
    ordering = ['-movement_date']
    readonly_fields = ['movement_date', 'created_by']
    
    fieldsets = (
        ('Movement Information', {
            'fields': ('asset', 'movement_type', 'movement_date')
        }),
        ('Location Transfer', {
            'fields': ('from_location', 'to_location')
        }),
        ('Employee Assignment', {
            'fields': ('from_employee', 'to_employee')
        }),
        ('Details', {
            'fields': ('reason', 'notes', 'approved_by')
        })
    )


@admin.register(AssetMaintenance)
class AssetMaintenanceAdmin(admin.ModelAdmin):
    list_display = ['asset', 'maintenance_type', 'status_badge', 'scheduled_date', 'completed_date', 'cost', 'performed_by']
    list_filter = ['maintenance_type', 'status', 'scheduled_date', 'completed_date']
    search_fields = ['asset__asset_tag', 'asset__name', 'description', 'performed_by']
    ordering = ['-scheduled_date']
    
    fieldsets = (
        ('Maintenance Information', {
            'fields': ('asset', 'maintenance_type', 'status', 'description')
        }),
        ('Scheduling', {
            'fields': ('scheduled_date', 'completed_date', 'next_maintenance_date')
        }),
        ('Cost and Supplier', {
            'fields': ('cost', 'performed_by', 'supplier')
        }),
        ('Notes', {
            'fields': ('notes',)
        })
    )
    
    def status_badge(self, obj):
        colors = {
            'scheduled': 'blue',
            'in_progress': 'orange',
            'completed': 'green',
            'cancelled': 'red'
        }
        color = colors.get(obj.status, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_status_display()
        )
    status_badge.short_description = 'Status'


@admin.register(AssetDepreciation)
class AssetDepreciationAdmin(admin.ModelAdmin):
    list_display = ['asset', 'period_start', 'period_end', 'opening_value', 'depreciation_amount', 'closing_value', 'method_used']
    list_filter = ['method_used', 'calculation_date', 'period_start']
    search_fields = ['asset__asset_tag', 'asset__name']
    ordering = ['-calculation_date']
    readonly_fields = ['calculation_date']
    
    fieldsets = (
        ('Asset Information', {
            'fields': ('asset', 'calculation_date')
        }),
        ('Period', {
            'fields': ('period_start', 'period_end')
        }),
        ('Depreciation Calculation', {
            'fields': ('opening_value', 'depreciation_amount', 'closing_value', 'method_used')
        }),
        ('Notes', {
            'fields': ('notes',)
        })
    )


class AssetAuditItemInline(admin.TabularInline):
    model = AssetAuditItem
    extra = 0
    readonly_fields = ['scanned_at', 'has_discrepancy']
    fields = ['asset', 'expected_location', 'actual_location', 'expected_condition', 'actual_condition', 'status', 'scanned_at']


@admin.register(AssetAudit)
class AssetAuditAdmin(admin.ModelAdmin):
    list_display = ['audit_name', 'audit_date', 'status_badge', 'auditor', 'location', 'total_assets_expected', 'total_assets_found', 'discrepancies_found', 'accuracy_rate']
    list_filter = ['status', 'audit_date', 'location', 'category']
    search_fields = ['audit_name', 'auditor__first_name', 'auditor__last_name']
    ordering = ['-audit_date']
    
    fieldsets = (
        ('Audit Information', {
            'fields': ('audit_name', 'audit_date', 'status', 'auditor')
        }),
        ('Scope', {
            'fields': ('location', 'category')
        }),
        ('Results', {
            'fields': ('total_assets_expected', 'total_assets_found', 'discrepancies_found')
        }),
        ('Notes', {
            'fields': ('notes',)
        })
    )
    
    inlines = [AssetAuditItemInline]
    
    def status_badge(self, obj):
        colors = {
            'planned': 'blue',
            'in_progress': 'orange',
            'completed': 'green',
            'cancelled': 'red'
        }
        color = colors.get(obj.status, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_status_display()
        )
    status_badge.short_description = 'Status'
    
    def accuracy_rate(self, obj):
        if obj.total_assets_expected > 0:
            rate = (obj.total_assets_found / obj.total_assets_expected) * 100
            color = 'green' if rate >= 95 else 'orange' if rate >= 90 else 'red'
            return format_html(
                '<span style="color: {}; font-weight: bold;">{:.1f}%</span>',
                color, rate
            )
        return 'N/A'
    accuracy_rate.short_description = 'Accuracy Rate'


@admin.register(AssetAuditItem)
class AssetAuditItemAdmin(admin.ModelAdmin):
    list_display = ['audit', 'asset', 'expected_location', 'actual_location', 'expected_condition', 'actual_condition', 'status_badge', 'discrepancy_indicator', 'scanned_at']
    list_filter = ['status', 'audit', 'expected_condition', 'actual_condition']
    search_fields = ['asset__asset_tag', 'asset__name', 'audit__audit_name']
    ordering = ['audit', 'asset__asset_tag']
    readonly_fields = ['has_discrepancy', 'scanned_at']
    
    fieldsets = (
        ('Audit Information', {
            'fields': ('audit', 'asset')
        }),
        ('Expected vs Actual', {
            'fields': ('expected_location', 'actual_location', 'expected_condition', 'actual_condition')
        }),
        ('Status', {
            'fields': ('status', 'scanned_at', 'has_discrepancy')
        }),
        ('Notes', {
            'fields': ('notes',)
        })
    )
    
    def status_badge(self, obj):
        colors = {
            'found': 'green',
            'missing': 'red',
            'damaged': 'red',
            'misplaced': 'orange'
        }
        color = colors.get(obj.status, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_status_display()
        )
    status_badge.short_description = 'Status'
    
    def discrepancy_indicator(self, obj):
        if obj.has_discrepancy:
            return format_html('<span style="color: red; font-weight: bold;">⚠ Yes</span>')
        return format_html('<span style="color: green;">✓ No</span>')
    discrepancy_indicator.short_description = 'Discrepancy'


@admin.register(AssetAnalytics)
class AssetAnalyticsAdmin(admin.ModelAdmin):
    list_display = ['metric_type', 'calculation_date', 'period_start', 'period_end', 'category', 'location', 'metric_value']
    list_filter = ['metric_type', 'calculation_date', 'category', 'location']
    search_fields = ['metric_type', 'notes']
    ordering = ['-calculation_date']
    readonly_fields = ['calculation_date']
    
    fieldsets = (
        ('Metric Information', {
            'fields': ('metric_type', 'calculation_date')
        }),
        ('Period', {
            'fields': ('period_start', 'period_end')
        }),
        ('Scope', {
            'fields': ('category', 'location')
        }),
        ('Results', {
            'fields': ('metric_value', 'metric_data')
        }),
        ('Notes', {
            'fields': ('notes',)
        })
    )


# Custom admin site configuration
admin.site.site_header = "School ERP - Inventory Management"
admin.site.site_title = "Inventory Admin"
admin.site.index_title = "Asset & Inventory Management"