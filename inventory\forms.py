from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal

from .models import (
    Asset, AssetCategory, Location, Supplier, AssetMovement, 
    AssetMaintenance, AssetAudit, AssetAuditItem
)


class AssetForm(forms.ModelForm):
    """Form for creating and editing assets"""
    
    class Meta:
        model = Asset
        fields = [
            'name', 'arabic_name', 'description', 'category', 'brand', 'model',
            'serial_number', 'purchase_price', 'salvage_value', 'purchase_date',
            'supplier', 'invoice_number', 'warranty_expiry', 'location',
            'assigned_to', 'department', 'status', 'condition',
            'depreciation_method', 'useful_life_years', 'depreciation_rate',
            'notes', 'image'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Asset name'}),
            'arabic_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Arabic name'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'category': forms.Select(attrs={'class': 'form-control'}),
            'brand': forms.TextInput(attrs={'class': 'form-control'}),
            'model': forms.TextInput(attrs={'class': 'form-control'}),
            'serial_number': forms.TextInput(attrs={'class': 'form-control'}),
            'purchase_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'salvage_value': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'purchase_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'supplier': forms.Select(attrs={'class': 'form-control'}),
            'invoice_number': forms.TextInput(attrs={'class': 'form-control'}),
            'warranty_expiry': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'location': forms.Select(attrs={'class': 'form-control'}),
            'assigned_to': forms.Select(attrs={'class': 'form-control'}),
            'department': forms.TextInput(attrs={'class': 'form-control'}),
            'status': forms.Select(attrs={'class': 'form-control'}),
            'condition': forms.Select(attrs={'class': 'form-control'}),
            'depreciation_method': forms.Select(attrs={'class': 'form-control'}),
            'useful_life_years': forms.NumberInput(attrs={'class': 'form-control', 'min': '1'}),
            'depreciation_rate': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'image': forms.FileInput(attrs={'class': 'form-control'})
        }
    
    def __init__(self, *args, **kwargs):
        self.school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if self.school:
            # Filter choices based on school
            self.fields['category'].queryset = AssetCategory.objects.filter(school=self.school, is_active=True)
            self.fields['location'].queryset = Location.objects.filter(school=self.school, is_active=True)
            self.fields['supplier'].queryset = Supplier.objects.filter(school=self.school, is_active=True)
            
            # Filter employees if hr app is available
            try:
                from hr.models import Employee
                self.fields['assigned_to'].queryset = Employee.objects.filter(school=self.school, user__is_active=True)
            except ImportError:
                pass
    
    def clean_purchase_price(self):
        price = self.cleaned_data.get('purchase_price')
        if price and price <= 0:
            raise ValidationError("Purchase price must be greater than zero.")
        return price
    
    def clean_salvage_value(self):
        salvage = self.cleaned_data.get('salvage_value')
        purchase_price = self.cleaned_data.get('purchase_price')
        
        if salvage and purchase_price and salvage >= purchase_price:
            raise ValidationError("Salvage value must be less than purchase price.")
        
        return salvage
    
    def clean_warranty_expiry(self):
        warranty_expiry = self.cleaned_data.get('warranty_expiry')
        purchase_date = self.cleaned_data.get('purchase_date')
        
        if warranty_expiry and purchase_date and warranty_expiry < purchase_date:
            raise ValidationError("Warranty expiry date cannot be before purchase date.")
        
        return warranty_expiry
    
    def clean_useful_life_years(self):
        years = self.cleaned_data.get('useful_life_years')
        if years and years <= 0:
            raise ValidationError("Useful life must be greater than zero.")
        return years
    
    def clean_depreciation_rate(self):
        rate = self.cleaned_data.get('depreciation_rate')
        if rate and (rate < 0 or rate > 100):
            raise ValidationError("Depreciation rate must be between 0 and 100.")
        return rate


class AssetCategoryForm(forms.ModelForm):
    """Form for creating and editing asset categories"""
    
    class Meta:
        model = AssetCategory
        fields = ['name', 'arabic_name', 'code', 'description', 'parent', 'depreciation_rate', 'useful_life_years']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'arabic_name': forms.TextInput(attrs={'class': 'form-control'}),
            'code': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'parent': forms.Select(attrs={'class': 'form-control'}),
            'depreciation_rate': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'useful_life_years': forms.NumberInput(attrs={'class': 'form-control', 'min': '1'})
        }
    
    def __init__(self, *args, **kwargs):
        self.school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if self.school:
            self.fields['parent'].queryset = AssetCategory.objects.filter(school=self.school, is_active=True)
            
            # Exclude self from parent choices when editing
            if self.instance and self.instance.pk:
                self.fields['parent'].queryset = self.fields['parent'].queryset.exclude(pk=self.instance.pk)


class LocationForm(forms.ModelForm):
    """Form for creating and editing locations"""
    
    class Meta:
        model = Location
        fields = ['name', 'arabic_name', 'code', 'description', 'building', 'floor', 'room', 'responsible_person']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'arabic_name': forms.TextInput(attrs={'class': 'form-control'}),
            'code': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'building': forms.TextInput(attrs={'class': 'form-control'}),
            'floor': forms.TextInput(attrs={'class': 'form-control'}),
            'room': forms.TextInput(attrs={'class': 'form-control'}),
            'responsible_person': forms.Select(attrs={'class': 'form-control'})
        }
    
    def __init__(self, *args, **kwargs):
        self.school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if self.school:
            try:
                from hr.models import Employee
                self.fields['responsible_person'].queryset = Employee.objects.filter(school=self.school, user__is_active=True)
            except ImportError:
                pass


class SupplierForm(forms.ModelForm):
    """Form for creating and editing suppliers"""
    
    class Meta:
        model = Supplier
        fields = ['name', 'arabic_name', 'code', 'contact_person', 'phone', 'email', 'address', 'tax_number', 'payment_terms', 'rating']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'arabic_name': forms.TextInput(attrs={'class': 'form-control'}),
            'code': forms.TextInput(attrs={'class': 'form-control'}),
            'contact_person': forms.TextInput(attrs={'class': 'form-control'}),
            'phone': forms.TextInput(attrs={'class': 'form-control'}),
            'email': forms.EmailInput(attrs={'class': 'form-control'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'tax_number': forms.TextInput(attrs={'class': 'form-control'}),
            'payment_terms': forms.TextInput(attrs={'class': 'form-control'}),
            'rating': forms.Select(attrs={'class': 'form-control'}, choices=[(i, f"{i} Star{'s' if i != 1 else ''}") for i in range(1, 6)])
        }


class AssetTransferForm(forms.ModelForm):
    """Form for transferring assets"""
    
    class Meta:
        model = AssetMovement
        fields = ['to_location', 'to_employee', 'reason', 'notes']
        widgets = {
            'to_location': forms.Select(attrs={'class': 'form-control'}),
            'to_employee': forms.Select(attrs={'class': 'form-control'}),
            'reason': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Reason for transfer'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'Additional notes'})
        }
    
    def __init__(self, *args, **kwargs):
        self.school = kwargs.pop('school', None)
        self.asset = kwargs.pop('asset', None)
        super().__init__(*args, **kwargs)
        
        if self.school:
            self.fields['to_location'].queryset = Location.objects.filter(school=self.school, is_active=True)
            
            try:
                from hr.models import Employee
                self.fields['to_employee'].queryset = Employee.objects.filter(school=self.school, user__is_active=True)
            except ImportError:
                pass
        
        # Make fields optional but require at least one
        self.fields['to_location'].required = False
        self.fields['to_employee'].required = False
    
    def clean(self):
        cleaned_data = super().clean()
        to_location = cleaned_data.get('to_location')
        to_employee = cleaned_data.get('to_employee')
        
        if not to_location and not to_employee:
            raise ValidationError("Please specify either a location or an employee for the transfer.")
        
        return cleaned_data


class AssetMaintenanceForm(forms.ModelForm):
    """Form for scheduling and managing asset maintenance"""
    
    class Meta:
        model = AssetMaintenance
        fields = ['maintenance_type', 'scheduled_date', 'description', 'cost', 'performed_by', 'supplier', 'next_maintenance_date', 'notes']
        widgets = {
            'maintenance_type': forms.Select(attrs={'class': 'form-control'}),
            'scheduled_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'cost': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'performed_by': forms.TextInput(attrs={'class': 'form-control'}),
            'supplier': forms.Select(attrs={'class': 'form-control'}),
            'next_maintenance_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3})
        }
    
    def __init__(self, *args, **kwargs):
        self.school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if self.school:
            self.fields['supplier'].queryset = Supplier.objects.filter(school=self.school, is_active=True)
        
        # Make supplier optional
        self.fields['supplier'].required = False
    
    def clean_scheduled_date(self):
        scheduled_date = self.cleaned_data.get('scheduled_date')
        if scheduled_date and scheduled_date < timezone.now().date():
            raise ValidationError("Scheduled date cannot be in the past.")
        return scheduled_date
    
    def clean_next_maintenance_date(self):
        next_date = self.cleaned_data.get('next_maintenance_date')
        scheduled_date = self.cleaned_data.get('scheduled_date')
        
        if next_date and scheduled_date and next_date <= scheduled_date:
            raise ValidationError("Next maintenance date must be after the scheduled date.")
        
        return next_date


class MaintenanceCompletionForm(forms.Form):
    """Form for completing maintenance"""
    
    completed_date = forms.DateField(
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        initial=timezone.now().date()
    )
    cost = forms.DecimalField(
        max_digits=10, 
        decimal_places=2,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
        required=False
    )
    performed_by = forms.CharField(
        max_length=200,
        widget=forms.TextInput(attrs={'class': 'form-control'}),
        required=False
    )
    asset_condition = forms.ChoiceField(
        choices=Asset.CONDITION_CHOICES,
        widget=forms.Select(attrs={'class': 'form-control'}),
        required=False
    )
    next_maintenance_date = forms.DateField(
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        required=False
    )
    notes = forms.CharField(
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        required=False
    )
    completion_notes = forms.CharField(
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
        required=False,
        label="Completion Notes"
    )
    
    def clean_completed_date(self):
        completed_date = self.cleaned_data.get('completed_date')
        if completed_date and completed_date > timezone.now().date():
            raise ValidationError("Completion date cannot be in the future.")
        return completed_date


class AssetAuditForm(forms.ModelForm):
    """Form for creating asset audits"""
    
    class Meta:
        model = AssetAudit
        fields = ['audit_name', 'audit_date', 'auditor', 'location', 'category', 'notes']
        widgets = {
            'audit_name': forms.TextInput(attrs={'class': 'form-control'}),
            'audit_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'auditor': forms.Select(attrs={'class': 'form-control'}),
            'location': forms.Select(attrs={'class': 'form-control'}),
            'category': forms.Select(attrs={'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3})
        }
    
    def __init__(self, *args, **kwargs):
        self.school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if self.school:
            self.fields['location'].queryset = Location.objects.filter(school=self.school, is_active=True)
            self.fields['category'].queryset = AssetCategory.objects.filter(school=self.school, is_active=True)
            
            try:
                from hr.models import Employee
                self.fields['auditor'].queryset = Employee.objects.filter(school=self.school, user__is_active=True)
            except ImportError:
                pass
        
        # Make location and category optional for full audits
        self.fields['location'].required = False
        self.fields['category'].required = False


class AssetScanForm(forms.Form):
    """Form for scanning assets during audit"""
    
    asset_tag = forms.CharField(
        max_length=50,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Scan or enter asset tag',
            'autofocus': True
        })
    )
    location = forms.ModelChoiceField(
        queryset=Location.objects.none(),
        widget=forms.Select(attrs={'class': 'form-control'}),
        required=False
    )
    condition = forms.ChoiceField(
        choices=Asset.CONDITION_CHOICES,
        widget=forms.Select(attrs={'class': 'form-control'}),
        required=False
    )
    notes = forms.CharField(
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
        required=False
    )
    
    def __init__(self, *args, **kwargs):
        self.school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if self.school:
            self.fields['location'].queryset = Location.objects.filter(school=self.school, is_active=True)


class AssetSearchForm(forms.Form):
    """Form for searching assets"""
    
    query = forms.CharField(
        max_length=200,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by asset tag, name, brand, model, or serial number...'
        }),
        required=False
    )
    category = forms.ModelChoiceField(
        queryset=AssetCategory.objects.none(),
        widget=forms.Select(attrs={'class': 'form-control'}),
        required=False,
        empty_label="All Categories"
    )
    location = forms.ModelChoiceField(
        queryset=Location.objects.none(),
        widget=forms.Select(attrs={'class': 'form-control'}),
        required=False,
        empty_label="All Locations"
    )
    status = forms.ChoiceField(
        choices=[('', 'All Statuses')] + Asset.STATUS_CHOICES,
        widget=forms.Select(attrs={'class': 'form-control'}),
        required=False
    )
    condition = forms.ChoiceField(
        choices=[('', 'All Conditions')] + Asset.CONDITION_CHOICES,
        widget=forms.Select(attrs={'class': 'form-control'}),
        required=False
    )
    assigned_to = forms.ModelChoiceField(
        queryset=None,
        widget=forms.Select(attrs={'class': 'form-control'}),
        required=False,
        empty_label="All Assignments"
    )
    
    def __init__(self, *args, **kwargs):
        self.school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if self.school:
            self.fields['category'].queryset = AssetCategory.objects.filter(school=self.school, is_active=True)
            self.fields['location'].queryset = Location.objects.filter(school=self.school, is_active=True)
            
            try:
                from hr.models import Employee
                self.fields['assigned_to'].queryset = Employee.objects.filter(school=self.school, user__is_active=True)
            except ImportError:
                self.fields['assigned_to'].widget = forms.HiddenInput()


class BulkAssetActionForm(forms.Form):
    """Form for bulk actions on assets"""
    
    ACTION_CHOICES = [
        ('update_status', 'Update Status'),
        ('update_condition', 'Update Condition'),
        ('transfer_location', 'Transfer to Location'),
        ('schedule_maintenance', 'Schedule Maintenance'),
        ('export_csv', 'Export to CSV'),
    ]
    
    action = forms.ChoiceField(
        choices=ACTION_CHOICES,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    # Optional fields based on action
    status = forms.ChoiceField(
        choices=Asset.STATUS_CHOICES,
        widget=forms.Select(attrs={'class': 'form-control'}),
        required=False
    )
    condition = forms.ChoiceField(
        choices=Asset.CONDITION_CHOICES,
        widget=forms.Select(attrs={'class': 'form-control'}),
        required=False
    )
    location = forms.ModelChoiceField(
        queryset=Location.objects.none(),
        widget=forms.Select(attrs={'class': 'form-control'}),
        required=False
    )
    maintenance_date = forms.DateField(
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        required=False
    )
    
    def __init__(self, *args, **kwargs):
        self.school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if self.school:
            self.fields['location'].queryset = Location.objects.filter(school=self.school, is_active=True)