# Generated by Django 5.2.5 on 2025-08-08 14:09

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("core", "0005_integrationprovider_integration_integrationschedule_and_more"),
        ("hr", "0005_performancegoal_performanceimprovementplan_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AssetCategory",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                ("name", models.Char<PERSON>ield(max_length=100)),
                ("arabic_name", models.CharField(blank=True, max_length=100)),
                ("code", models.CharField(max_length=20, unique=True)),
                ("description", models.TextField(blank=True)),
                (
                    "depreciation_rate",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Annual depreciation rate as percentage",
                        max_digits=5,
                    ),
                ),
                (
                    "useful_life_years",
                    models.IntegerField(
                        default=5, help_text="Expected useful life in years"
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subcategories",
                        to="inventory.assetcategory",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Asset Categories",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Asset",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "asset_tag",
                    models.CharField(
                        help_text="Unique asset identifier", max_length=50, unique=True
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                ("arabic_name", models.CharField(blank=True, max_length=200)),
                ("description", models.TextField(blank=True)),
                ("brand", models.CharField(blank=True, max_length=100)),
                ("model", models.CharField(blank=True, max_length=100)),
                ("serial_number", models.CharField(blank=True, max_length=100)),
                ("barcode", models.CharField(blank=True, max_length=100, unique=True)),
                ("qr_code", models.CharField(blank=True, max_length=200, unique=True)),
                (
                    "purchase_price",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    "current_value",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    "salvage_value",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                ("purchase_date", models.DateField()),
                ("invoice_number", models.CharField(blank=True, max_length=100)),
                ("warranty_expiry", models.DateField(blank=True, null=True)),
                ("department", models.CharField(blank=True, max_length=100)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("maintenance", "Under Maintenance"),
                            ("retired", "Retired"),
                            ("disposed", "Disposed"),
                            ("lost", "Lost"),
                            ("damaged", "Damaged"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                (
                    "condition",
                    models.CharField(
                        choices=[
                            ("excellent", "Excellent"),
                            ("good", "Good"),
                            ("fair", "Fair"),
                            ("poor", "Poor"),
                            ("damaged", "Damaged"),
                        ],
                        default="excellent",
                        max_length=20,
                    ),
                ),
                ("last_maintenance_date", models.DateField(blank=True, null=True)),
                ("next_maintenance_date", models.DateField(blank=True, null=True)),
                (
                    "depreciation_method",
                    models.CharField(
                        choices=[
                            ("straight_line", "Straight Line"),
                            ("declining_balance", "Declining Balance"),
                            ("sum_of_years", "Sum of Years Digits"),
                        ],
                        default="straight_line",
                        max_length=20,
                    ),
                ),
                ("useful_life_years", models.IntegerField(default=5)),
                (
                    "depreciation_rate",
                    models.DecimalField(decimal_places=2, default=20.0, max_digits=5),
                ),
                ("notes", models.TextField(blank=True)),
                ("image", models.ImageField(blank=True, upload_to="asset_images/")),
                (
                    "assigned_to",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="assigned_assets",
                        to="hr.employee",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="assets",
                        to="inventory.assetcategory",
                    ),
                ),
            ],
            options={
                "ordering": ["asset_tag"],
            },
        ),
        migrations.CreateModel(
            name="Location",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                ("name", models.CharField(max_length=100)),
                ("arabic_name", models.CharField(blank=True, max_length=100)),
                ("code", models.CharField(max_length=20, unique=True)),
                ("description", models.TextField(blank=True)),
                ("building", models.CharField(blank=True, max_length=100)),
                ("floor", models.CharField(blank=True, max_length=50)),
                ("room", models.CharField(blank=True, max_length=50)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "responsible_person",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="hr.employee",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "ordering": ["building", "floor", "room", "name"],
            },
        ),
        migrations.CreateModel(
            name="InventoryCount",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("count_name", models.CharField(max_length=200)),
                ("count_date", models.DateField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("planned", "Planned"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="planned",
                        max_length=20,
                    ),
                ),
                ("total_items_expected", models.IntegerField(default=0)),
                ("total_items_counted", models.IntegerField(default=0)),
                ("discrepancies_found", models.IntegerField(default=0)),
                ("notes", models.TextField(blank=True)),
                (
                    "category",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="inventory.assetcategory",
                    ),
                ),
                (
                    "counter",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="hr.employee",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "location",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="inventory.location",
                    ),
                ),
            ],
            options={
                "ordering": ["-count_date"],
            },
        ),
        migrations.CreateModel(
            name="AssetMovement",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "movement_type",
                    models.CharField(
                        choices=[
                            ("transfer", "Transfer"),
                            ("assignment", "Assignment"),
                            ("return", "Return"),
                            ("maintenance", "Maintenance"),
                            ("disposal", "Disposal"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "movement_date",
                    models.DateTimeField(default=django.utils.timezone.now),
                ),
                ("reason", models.TextField(blank=True)),
                ("notes", models.TextField(blank=True)),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_movements",
                        to="hr.employee",
                    ),
                ),
                (
                    "asset",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="movements",
                        to="inventory.asset",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "from_employee",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="asset_movements_from",
                        to="hr.employee",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "to_employee",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="asset_movements_to",
                        to="hr.employee",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "from_location",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="movements_from",
                        to="inventory.location",
                    ),
                ),
                (
                    "to_location",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="movements_to",
                        to="inventory.location",
                    ),
                ),
            ],
            options={
                "ordering": ["-movement_date"],
            },
        ),
        migrations.CreateModel(
            name="AssetAudit",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("audit_name", models.CharField(max_length=200)),
                ("audit_date", models.DateField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("planned", "Planned"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="planned",
                        max_length=20,
                    ),
                ),
                ("total_assets_expected", models.IntegerField(default=0)),
                ("total_assets_found", models.IntegerField(default=0)),
                ("discrepancies_found", models.IntegerField(default=0)),
                ("notes", models.TextField(blank=True)),
                (
                    "auditor",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="hr.employee",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "category",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="inventory.assetcategory",
                    ),
                ),
                (
                    "location",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="inventory.location",
                    ),
                ),
            ],
            options={
                "ordering": ["-audit_date"],
            },
        ),
        migrations.AddField(
            model_name="asset",
            name="location",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="inventory.location",
            ),
        ),
        migrations.CreateModel(
            name="Supplier",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                ("name", models.CharField(max_length=200)),
                ("arabic_name", models.CharField(blank=True, max_length=200)),
                ("code", models.CharField(max_length=20, unique=True)),
                ("contact_person", models.CharField(blank=True, max_length=100)),
                ("phone", models.CharField(blank=True, max_length=20)),
                ("email", models.EmailField(blank=True, max_length=254)),
                ("address", models.TextField(blank=True)),
                ("tax_number", models.CharField(blank=True, max_length=50)),
                ("payment_terms", models.CharField(blank=True, max_length=100)),
                (
                    "rating",
                    models.IntegerField(
                        default=5,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="PurchaseOrder",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("po_number", models.CharField(max_length=50, unique=True)),
                ("order_date", models.DateField(default=django.utils.timezone.now)),
                ("expected_delivery_date", models.DateField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("pending", "Pending Approval"),
                            ("approved", "Approved"),
                            ("sent", "Sent to Supplier"),
                            ("partial", "Partially Received"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                (
                    "subtotal",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    "tax_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    "total_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                ("approval_date", models.DateField(blank=True, null=True)),
                ("notes", models.TextField(blank=True)),
                ("terms_and_conditions", models.TextField(blank=True)),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_purchase_orders",
                        to="hr.employee",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "requested_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="requested_purchase_orders",
                        to="hr.employee",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "supplier",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="purchase_orders",
                        to="inventory.supplier",
                    ),
                ),
            ],
            options={
                "ordering": ["-order_date"],
            },
        ),
        migrations.CreateModel(
            name="InventoryItem",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "item_code",
                    models.CharField(
                        help_text="Unique item identifier", max_length=50, unique=True
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                ("arabic_name", models.CharField(blank=True, max_length=200)),
                ("description", models.TextField(blank=True)),
                ("brand", models.CharField(blank=True, max_length=100)),
                ("model", models.CharField(blank=True, max_length=100)),
                (
                    "unit_of_measure",
                    models.CharField(
                        choices=[
                            ("pcs", "Pieces"),
                            ("kg", "Kilograms"),
                            ("ltr", "Liters"),
                            ("box", "Boxes"),
                            ("pack", "Packs"),
                            ("roll", "Rolls"),
                            ("sheet", "Sheets"),
                            ("bottle", "Bottles"),
                            ("bag", "Bags"),
                            ("set", "Sets"),
                        ],
                        default="pcs",
                        max_length=20,
                    ),
                ),
                ("barcode", models.CharField(blank=True, max_length=100, unique=True)),
                (
                    "current_stock",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "minimum_stock",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Reorder point",
                        max_digits=10,
                    ),
                ),
                (
                    "maximum_stock",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Maximum stock level",
                        max_digits=10,
                    ),
                ),
                (
                    "reorder_quantity",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Standard reorder quantity",
                        max_digits=10,
                    ),
                ),
                (
                    "unit_cost",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Average unit cost",
                        max_digits=10,
                    ),
                ),
                (
                    "last_purchase_price",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("last_purchase_date", models.DateField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("discontinued", "Discontinued"),
                            ("out_of_stock", "Out of Stock"),
                            ("low_stock", "Low Stock"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                (
                    "last_counted_date",
                    models.DateField(
                        blank=True, help_text="Last physical count date", null=True
                    ),
                ),
                (
                    "last_counted_quantity",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                ("notes", models.TextField(blank=True)),
                ("image", models.ImageField(blank=True, upload_to="inventory_images/")),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="inventory_items",
                        to="inventory.assetcategory",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "primary_location",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="primary_inventory_items",
                        to="inventory.location",
                    ),
                ),
                (
                    "primary_supplier",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="primary_inventory_items",
                        to="inventory.supplier",
                    ),
                ),
            ],
            options={
                "ordering": ["item_code"],
            },
        ),
        migrations.CreateModel(
            name="AssetMaintenance",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "maintenance_type",
                    models.CharField(
                        choices=[
                            ("preventive", "Preventive"),
                            ("corrective", "Corrective"),
                            ("emergency", "Emergency"),
                            ("upgrade", "Upgrade"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("scheduled", "Scheduled"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="scheduled",
                        max_length=20,
                    ),
                ),
                ("scheduled_date", models.DateField()),
                ("completed_date", models.DateField(blank=True, null=True)),
                ("description", models.TextField()),
                (
                    "cost",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("performed_by", models.CharField(blank=True, max_length=200)),
                ("notes", models.TextField(blank=True)),
                ("next_maintenance_date", models.DateField(blank=True, null=True)),
                (
                    "asset",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="maintenance_records",
                        to="inventory.asset",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "supplier",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="inventory.supplier",
                    ),
                ),
            ],
            options={
                "ordering": ["-scheduled_date"],
            },
        ),
        migrations.AddField(
            model_name="asset",
            name="supplier",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="inventory.supplier",
            ),
        ),
        migrations.CreateModel(
            name="AssetDepreciation",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "calculation_date",
                    models.DateField(default=django.utils.timezone.now),
                ),
                ("period_start", models.DateField()),
                ("period_end", models.DateField()),
                ("opening_value", models.DecimalField(decimal_places=2, max_digits=12)),
                (
                    "depreciation_amount",
                    models.DecimalField(decimal_places=2, max_digits=12),
                ),
                ("closing_value", models.DecimalField(decimal_places=2, max_digits=12)),
                ("method_used", models.CharField(max_length=20)),
                ("notes", models.TextField(blank=True)),
                (
                    "asset",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="depreciation_records",
                        to="inventory.asset",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "ordering": ["-calculation_date"],
                "unique_together": {("asset", "period_start", "period_end")},
            },
        ),
        migrations.CreateModel(
            name="InventoryCountItem",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "expected_quantity",
                    models.DecimalField(decimal_places=2, max_digits=10),
                ),
                (
                    "counted_quantity",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "variance",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("counted", "Counted"),
                            ("not_found", "Not Found"),
                            ("damaged", "Damaged"),
                            ("discrepancy", "Discrepancy"),
                        ],
                        default="counted",
                        max_length=20,
                    ),
                ),
                ("notes", models.TextField(blank=True)),
                ("counted_at", models.DateTimeField(blank=True, null=True)),
                (
                    "count",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="count_items",
                        to="inventory.inventorycount",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="inventory.inventoryitem",
                    ),
                ),
            ],
            options={
                "ordering": ["item__name"],
                "unique_together": {("count", "item")},
            },
        ),
        migrations.CreateModel(
            name="InventoryLocation",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "quantity",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "minimum_quantity",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("last_updated", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="location_stocks",
                        to="inventory.inventoryitem",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "location",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="inventory_stocks",
                        to="inventory.location",
                    ),
                ),
            ],
            options={
                "ordering": ["item", "location"],
                "unique_together": {("item", "location")},
            },
        ),
        migrations.CreateModel(
            name="AssetAuditItem",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "expected_condition",
                    models.CharField(
                        choices=[
                            ("excellent", "Excellent"),
                            ("good", "Good"),
                            ("fair", "Fair"),
                            ("poor", "Poor"),
                            ("damaged", "Damaged"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "actual_condition",
                    models.CharField(
                        choices=[
                            ("excellent", "Excellent"),
                            ("good", "Good"),
                            ("fair", "Fair"),
                            ("poor", "Poor"),
                            ("damaged", "Damaged"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("found", "Found"),
                            ("missing", "Missing"),
                            ("damaged", "Damaged"),
                            ("misplaced", "Misplaced"),
                        ],
                        max_length=20,
                    ),
                ),
                ("notes", models.TextField(blank=True)),
                ("scanned_at", models.DateTimeField(blank=True, null=True)),
                (
                    "asset",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="inventory.asset",
                    ),
                ),
                (
                    "audit",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="audit_items",
                        to="inventory.assetaudit",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "actual_location",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="actual_audit_items",
                        to="inventory.location",
                    ),
                ),
                (
                    "expected_location",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="expected_audit_items",
                        to="inventory.location",
                    ),
                ),
            ],
            options={
                "unique_together": {("audit", "asset")},
            },
        ),
        migrations.CreateModel(
            name="AssetAnalytics",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "metric_type",
                    models.CharField(
                        choices=[
                            ("utilization", "Utilization Rate"),
                            ("depreciation", "Depreciation Analysis"),
                            ("maintenance_cost", "Maintenance Cost"),
                            ("category_distribution", "Category Distribution"),
                            ("location_distribution", "Location Distribution"),
                            ("age_analysis", "Age Analysis"),
                            ("condition_analysis", "Condition Analysis"),
                            ("inventory_turnover", "Inventory Turnover"),
                            ("stock_value", "Stock Value Analysis"),
                            ("reorder_analysis", "Reorder Analysis"),
                        ],
                        max_length=30,
                    ),
                ),
                (
                    "calculation_date",
                    models.DateField(default=django.utils.timezone.now),
                ),
                ("period_start", models.DateField()),
                ("period_end", models.DateField()),
                ("metric_value", models.DecimalField(decimal_places=2, max_digits=15)),
                (
                    "metric_data",
                    models.JSONField(
                        default=dict, help_text="Additional metric data in JSON format"
                    ),
                ),
                ("notes", models.TextField(blank=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "category",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="inventory.assetcategory",
                    ),
                ),
                (
                    "location",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="inventory.location",
                    ),
                ),
            ],
            options={
                "ordering": ["-calculation_date"],
                "indexes": [
                    models.Index(
                        fields=["school", "metric_type", "calculation_date"],
                        name="inventory_a_school__c4dd70_idx",
                    ),
                    models.Index(
                        fields=["category", "metric_type"],
                        name="inventory_a_categor_987821_idx",
                    ),
                    models.Index(
                        fields=["location", "metric_type"],
                        name="inventory_a_locatio_7e7a74_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="PurchaseOrderItem",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "quantity_ordered",
                    models.DecimalField(decimal_places=2, max_digits=10),
                ),
                (
                    "quantity_received",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("unit_price", models.DecimalField(decimal_places=2, max_digits=10)),
                ("total_amount", models.DecimalField(decimal_places=2, max_digits=10)),
                ("notes", models.TextField(blank=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="inventory.inventoryitem",
                    ),
                ),
                (
                    "purchase_order",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="line_items",
                        to="inventory.purchaseorder",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "ordering": ["item__name"],
                "unique_together": {("purchase_order", "item")},
            },
        ),
        migrations.CreateModel(
            name="StockAlert",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "alert_type",
                    models.CharField(
                        choices=[
                            ("low_stock", "Low Stock"),
                            ("out_of_stock", "Out of Stock"),
                            ("overstock", "Overstock"),
                            ("reorder_point", "Reorder Point Reached"),
                            ("expiry_warning", "Expiry Warning"),
                            ("no_movement", "No Movement"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("acknowledged", "Acknowledged"),
                            ("resolved", "Resolved"),
                            ("dismissed", "Dismissed"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                ("alert_date", models.DateTimeField(default=django.utils.timezone.now)),
                ("message", models.TextField()),
                ("current_stock", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "threshold_value",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                ("acknowledged_date", models.DateTimeField(blank=True, null=True)),
                ("resolved_date", models.DateTimeField(blank=True, null=True)),
                ("resolution_notes", models.TextField(blank=True)),
                (
                    "acknowledged_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="acknowledged_alerts",
                        to="hr.employee",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="stock_alerts",
                        to="inventory.inventoryitem",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "ordering": ["-alert_date"],
                "indexes": [
                    models.Index(
                        fields=["school", "status", "alert_date"],
                        name="inventory_s_school__f92c49_idx",
                    ),
                    models.Index(
                        fields=["item", "alert_type"],
                        name="inventory_s_item_id_1481c4_idx",
                    ),
                    models.Index(
                        fields=["alert_type", "status"],
                        name="inventory_s_alert_t_938177_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="StockTransaction",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("transaction_id", models.CharField(max_length=50, unique=True)),
                (
                    "transaction_type",
                    models.CharField(
                        choices=[
                            ("receipt", "Receipt"),
                            ("issue", "Issue"),
                            ("transfer", "Transfer"),
                            ("adjustment", "Adjustment"),
                            ("return", "Return"),
                            ("damage", "Damage"),
                            ("loss", "Loss"),
                            ("count", "Physical Count"),
                        ],
                        max_length=20,
                    ),
                ),
                ("quantity", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "unit_cost",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "reference_type",
                    models.CharField(
                        blank=True,
                        help_text="Type of reference document",
                        max_length=50,
                    ),
                ),
                (
                    "reference_id",
                    models.CharField(
                        blank=True, help_text="Reference document ID", max_length=100
                    ),
                ),
                ("reference_date", models.DateField(blank=True, null=True)),
                ("reason", models.CharField(blank=True, max_length=200)),
                ("notes", models.TextField(blank=True)),
                (
                    "transaction_date",
                    models.DateTimeField(default=django.utils.timezone.now),
                ),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_stock_transactions",
                        to="hr.employee",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "from_location",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="stock_transactions_from",
                        to="inventory.location",
                    ),
                ),
                (
                    "item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="stock_transactions",
                        to="inventory.inventoryitem",
                    ),
                ),
                (
                    "requested_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="requested_stock_transactions",
                        to="hr.employee",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "to_location",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="stock_transactions_to",
                        to="inventory.location",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "ordering": ["-transaction_date"],
                "indexes": [
                    models.Index(
                        fields=["school", "transaction_date"],
                        name="inventory_s_school__05908a_idx",
                    ),
                    models.Index(
                        fields=["item", "transaction_date"],
                        name="inventory_s_item_id_f9962b_idx",
                    ),
                    models.Index(
                        fields=["transaction_type", "transaction_date"],
                        name="inventory_s_transac_23e651_idx",
                    ),
                    models.Index(
                        fields=["from_location", "transaction_date"],
                        name="inventory_s_from_lo_c1043b_idx",
                    ),
                    models.Index(
                        fields=["to_location", "transaction_date"],
                        name="inventory_s_to_loca_077c7f_idx",
                    ),
                ],
            },
        ),
        migrations.AddIndex(
            model_name="purchaseorder",
            index=models.Index(
                fields=["school", "po_number"], name="inventory_p_school__60a310_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="purchaseorder",
            index=models.Index(
                fields=["supplier", "order_date"], name="inventory_p_supplie_8bff1c_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="purchaseorder",
            index=models.Index(
                fields=["status", "order_date"], name="inventory_p_status_1b7ac0_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="inventoryitem",
            index=models.Index(
                fields=["school", "item_code"], name="inventory_i_school__a061c7_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="inventoryitem",
            index=models.Index(
                fields=["school", "status"], name="inventory_i_school__125382_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="inventoryitem",
            index=models.Index(
                fields=["category", "status"], name="inventory_i_categor_878bff_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="inventoryitem",
            index=models.Index(
                fields=["primary_location"], name="inventory_i_primary_086ca2_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="inventoryitem",
            index=models.Index(
                fields=["barcode"], name="inventory_i_barcode_c7bf0e_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="inventoryitem",
            index=models.Index(
                fields=["current_stock"], name="inventory_i_current_9d2479_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="asset",
            index=models.Index(
                fields=["school", "asset_tag"], name="inventory_a_school__6f6358_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="asset",
            index=models.Index(
                fields=["school", "status"], name="inventory_a_school__28964e_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="asset",
            index=models.Index(
                fields=["category", "status"], name="inventory_a_categor_3055de_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="asset",
            index=models.Index(
                fields=["location"], name="inventory_a_locatio_ea89ef_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="asset",
            index=models.Index(
                fields=["assigned_to"], name="inventory_a_assigne_84c795_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="asset",
            index=models.Index(
                fields=["barcode"], name="inventory_a_barcode_e90a74_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="asset",
            index=models.Index(
                fields=["qr_code"], name="inventory_a_qr_code_23709c_idx"
            ),
        ),
    ]
