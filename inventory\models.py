from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from decimal import Decimal
import uuid
from core.models import BaseModel


class AssetCategory(BaseModel):
    """Asset categories for organizing inventory items"""
    name = models.CharField(max_length=100)
    arabic_name = models.CharField(max_length=100, blank=True)
    code = models.CharField(max_length=20, unique=True)
    description = models.TextField(blank=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='subcategories')
    depreciation_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0, help_text="Annual depreciation rate as percentage")
    useful_life_years = models.IntegerField(default=5, help_text="Expected useful life in years")
    is_active = models.BooleanField(default=True)
    
    class Meta:
        verbose_name_plural = "Asset Categories"
        ordering = ['name']
    
    def __str__(self):
        return f"{self.code} - {self.name}"
    
    @property
    def full_path(self):
        """Get full category path"""
        if self.parent:
            return f"{self.parent.full_path} > {self.name}"
        return self.name


class Location(BaseModel):
    """Physical locations where assets can be stored"""
    name = models.CharField(max_length=100)
    arabic_name = models.CharField(max_length=100, blank=True)
    code = models.CharField(max_length=20, unique=True)
    description = models.TextField(blank=True)
    building = models.CharField(max_length=100, blank=True)
    floor = models.CharField(max_length=50, blank=True)
    room = models.CharField(max_length=50, blank=True)
    responsible_person = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        ordering = ['building', 'floor', 'room', 'name']
    
    def __str__(self):
        parts = [self.name]
        if self.building:
            parts.append(f"Building: {self.building}")
        if self.floor:
            parts.append(f"Floor: {self.floor}")
        if self.room:
            parts.append(f"Room: {self.room}")
        return " - ".join(parts)


class Supplier(BaseModel):
    """Suppliers for asset procurement"""
    name = models.CharField(max_length=200)
    arabic_name = models.CharField(max_length=200, blank=True)
    code = models.CharField(max_length=20, unique=True)
    contact_person = models.CharField(max_length=100, blank=True)
    phone = models.CharField(max_length=20, blank=True)
    email = models.EmailField(blank=True)
    address = models.TextField(blank=True)
    tax_number = models.CharField(max_length=50, blank=True)
    payment_terms = models.CharField(max_length=100, blank=True)
    rating = models.IntegerField(default=5, validators=[MinValueValidator(1), MaxValueValidator(5)])
    is_active = models.BooleanField(default=True)
    
    class Meta:
        ordering = ['name']
    
    def __str__(self):
        return f"{self.code} - {self.name}"


class Asset(BaseModel):
    """Main asset model with comprehensive tracking"""
    
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('maintenance', 'Under Maintenance'),
        ('retired', 'Retired'),
        ('disposed', 'Disposed'),
        ('lost', 'Lost'),
        ('damaged', 'Damaged'),
    ]
    
    CONDITION_CHOICES = [
        ('excellent', 'Excellent'),
        ('good', 'Good'),
        ('fair', 'Fair'),
        ('poor', 'Poor'),
        ('damaged', 'Damaged'),
    ]
    
    # Basic Information
    asset_tag = models.CharField(max_length=50, unique=True, help_text="Unique asset identifier")
    name = models.CharField(max_length=200)
    arabic_name = models.CharField(max_length=200, blank=True)
    description = models.TextField(blank=True)
    category = models.ForeignKey(AssetCategory, on_delete=models.PROTECT, related_name='assets')
    
    # Physical Properties
    brand = models.CharField(max_length=100, blank=True)
    model = models.CharField(max_length=100, blank=True)
    serial_number = models.CharField(max_length=100, blank=True)
    barcode = models.CharField(max_length=100, blank=True, unique=True)
    qr_code = models.CharField(max_length=200, blank=True, unique=True)
    
    # Financial Information
    purchase_price = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    current_value = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    salvage_value = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    purchase_date = models.DateField()
    supplier = models.ForeignKey(Supplier, on_delete=models.SET_NULL, null=True, blank=True)
    invoice_number = models.CharField(max_length=100, blank=True)
    warranty_expiry = models.DateField(null=True, blank=True)
    
    # Location and Assignment
    location = models.ForeignKey(Location, on_delete=models.SET_NULL, null=True, blank=True)
    assigned_to = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_assets')
    department = models.CharField(max_length=100, blank=True)
    
    # Status and Condition
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    condition = models.CharField(max_length=20, choices=CONDITION_CHOICES, default='excellent')
    last_maintenance_date = models.DateField(null=True, blank=True)
    next_maintenance_date = models.DateField(null=True, blank=True)
    
    # Depreciation
    depreciation_method = models.CharField(max_length=20, choices=[
        ('straight_line', 'Straight Line'),
        ('declining_balance', 'Declining Balance'),
        ('sum_of_years', 'Sum of Years Digits'),
    ], default='straight_line')
    useful_life_years = models.IntegerField(default=5)
    depreciation_rate = models.DecimalField(max_digits=5, decimal_places=2, default=20.00)
    
    # Additional Information
    notes = models.TextField(blank=True)
    image = models.ImageField(upload_to='asset_images/', blank=True)
    
    class Meta:
        ordering = ['asset_tag']
        indexes = [
            models.Index(fields=['school', 'asset_tag']),
            models.Index(fields=['school', 'status']),
            models.Index(fields=['category', 'status']),
            models.Index(fields=['location']),
            models.Index(fields=['assigned_to']),
            models.Index(fields=['barcode']),
            models.Index(fields=['qr_code']),
        ]
    
    def __str__(self):
        return f"{self.asset_tag} - {self.name}"
    
    def save(self, *args, **kwargs):
        # Generate barcode and QR code if not provided
        if not self.barcode:
            self.barcode = f"BC{self.asset_tag}"
        if not self.qr_code:
            self.qr_code = f"QR{uuid.uuid4().hex[:8].upper()}{self.asset_tag}"
        
        # Set initial current value to purchase price
        if not self.current_value and self.purchase_price:
            self.current_value = self.purchase_price
        
        # Set useful life from category if not specified
        if not self.useful_life_years and self.category:
            self.useful_life_years = self.category.useful_life_years
        
        # Set depreciation rate from category if not specified
        if not self.depreciation_rate and self.category:
            self.depreciation_rate = self.category.depreciation_rate
        
        super().save(*args, **kwargs)
    
    @property
    def age_in_years(self):
        """Calculate asset age in years"""
        if self.purchase_date:
            return (timezone.now().date() - self.purchase_date).days / 365.25
        return 0
    
    @property
    def accumulated_depreciation(self):
        """Calculate accumulated depreciation"""
        if self.depreciation_method == 'straight_line':
            return self.calculate_straight_line_depreciation()
        elif self.depreciation_method == 'declining_balance':
            return self.calculate_declining_balance_depreciation()
        elif self.depreciation_method == 'sum_of_years':
            return self.calculate_sum_of_years_depreciation()
        return Decimal('0.00')
    
    def calculate_straight_line_depreciation(self):
        """Calculate straight-line depreciation"""
        if self.useful_life_years <= 0:
            return Decimal('0.00')
        
        annual_depreciation = (self.purchase_price - self.salvage_value) / self.useful_life_years
        years_elapsed = min(self.age_in_years, self.useful_life_years)
        return Decimal(str(annual_depreciation * years_elapsed)).quantize(Decimal('0.01'))
    
    def calculate_declining_balance_depreciation(self):
        """Calculate declining balance depreciation"""
        if self.depreciation_rate <= 0:
            return Decimal('0.00')
        
        rate = self.depreciation_rate / 100
        years_elapsed = int(self.age_in_years)
        accumulated = Decimal('0.00')
        book_value = self.purchase_price
        
        for year in range(years_elapsed):
            depreciation = book_value * Decimal(str(rate))
            accumulated += depreciation
            book_value -= depreciation
            
            if book_value <= self.salvage_value:
                break
        
        return accumulated.quantize(Decimal('0.01'))
    
    def calculate_sum_of_years_depreciation(self):
        """Calculate sum of years digits depreciation"""
        if self.useful_life_years <= 0:
            return Decimal('0.00')
        
        sum_of_years = self.useful_life_years * (self.useful_life_years + 1) / 2
        depreciable_amount = self.purchase_price - self.salvage_value
        years_elapsed = min(int(self.age_in_years), self.useful_life_years)
        accumulated = Decimal('0.00')
        
        for year in range(1, years_elapsed + 1):
            remaining_years = self.useful_life_years - year + 1
            annual_depreciation = depreciable_amount * (remaining_years / sum_of_years)
            accumulated += Decimal(str(annual_depreciation))
        
        return accumulated.quantize(Decimal('0.01'))
    
    @property
    def book_value(self):
        """Calculate current book value"""
        return (self.purchase_price - self.accumulated_depreciation).quantize(Decimal('0.01'))
    
    @property
    def is_warranty_valid(self):
        """Check if warranty is still valid"""
        if self.warranty_expiry:
            return timezone.now().date() <= self.warranty_expiry
        return False
    
    @property
    def maintenance_due(self):
        """Check if maintenance is due"""
        if self.next_maintenance_date:
            return timezone.now().date() >= self.next_maintenance_date
        return False
    
    def update_current_value(self):
        """Update current value based on depreciation"""
        self.current_value = self.book_value
        self.save(update_fields=['current_value'])


class AssetMovement(BaseModel):
    """Track asset movements between locations"""
    
    MOVEMENT_TYPES = [
        ('transfer', 'Transfer'),
        ('assignment', 'Assignment'),
        ('return', 'Return'),
        ('maintenance', 'Maintenance'),
        ('disposal', 'Disposal'),
    ]
    
    asset = models.ForeignKey(Asset, on_delete=models.CASCADE, related_name='movements')
    movement_type = models.CharField(max_length=20, choices=MOVEMENT_TYPES)
    from_location = models.ForeignKey(Location, on_delete=models.SET_NULL, null=True, blank=True, related_name='movements_from')
    to_location = models.ForeignKey(Location, on_delete=models.SET_NULL, null=True, blank=True, related_name='movements_to')
    from_employee = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True, blank=True, related_name='asset_movements_from')
    to_employee = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True, blank=True, related_name='asset_movements_to')
    movement_date = models.DateTimeField(default=timezone.now)
    reason = models.TextField(blank=True)
    notes = models.TextField(blank=True)
    approved_by = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_movements')
    
    class Meta:
        ordering = ['-movement_date']
    
    def __str__(self):
        return f"{self.asset.asset_tag} - {self.get_movement_type_display()} on {self.movement_date.date()}"


class AssetMaintenance(BaseModel):
    """Track asset maintenance activities"""
    
    MAINTENANCE_TYPES = [
        ('preventive', 'Preventive'),
        ('corrective', 'Corrective'),
        ('emergency', 'Emergency'),
        ('upgrade', 'Upgrade'),
    ]
    
    STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    asset = models.ForeignKey(Asset, on_delete=models.CASCADE, related_name='maintenance_records')
    maintenance_type = models.CharField(max_length=20, choices=MAINTENANCE_TYPES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='scheduled')
    scheduled_date = models.DateField()
    completed_date = models.DateField(null=True, blank=True)
    description = models.TextField()
    cost = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    performed_by = models.CharField(max_length=200, blank=True)
    supplier = models.ForeignKey(Supplier, on_delete=models.SET_NULL, null=True, blank=True)
    notes = models.TextField(blank=True)
    next_maintenance_date = models.DateField(null=True, blank=True)
    
    class Meta:
        ordering = ['-scheduled_date']
    
    def __str__(self):
        return f"{self.asset.asset_tag} - {self.get_maintenance_type_display()} on {self.scheduled_date}"
    
    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        
        # Update asset maintenance dates
        if self.status == 'completed' and self.completed_date:
            self.asset.last_maintenance_date = self.completed_date
            if self.next_maintenance_date:
                self.asset.next_maintenance_date = self.next_maintenance_date
            self.asset.save(update_fields=['last_maintenance_date', 'next_maintenance_date'])


class AssetDepreciation(BaseModel):
    """Track asset depreciation calculations"""
    asset = models.ForeignKey(Asset, on_delete=models.CASCADE, related_name='depreciation_records')
    calculation_date = models.DateField(default=timezone.now)
    period_start = models.DateField()
    period_end = models.DateField()
    opening_value = models.DecimalField(max_digits=12, decimal_places=2)
    depreciation_amount = models.DecimalField(max_digits=12, decimal_places=2)
    closing_value = models.DecimalField(max_digits=12, decimal_places=2)
    method_used = models.CharField(max_length=20)
    notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-calculation_date']
        unique_together = ['asset', 'period_start', 'period_end']
    
    def __str__(self):
        return f"{self.asset.asset_tag} - Depreciation for {self.period_start} to {self.period_end}"


class AssetAudit(BaseModel):
    """Track asset audit activities"""
    
    STATUS_CHOICES = [
        ('planned', 'Planned'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    audit_name = models.CharField(max_length=200)
    audit_date = models.DateField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='planned')
    auditor = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True, blank=True)
    location = models.ForeignKey(Location, on_delete=models.SET_NULL, null=True, blank=True)
    category = models.ForeignKey(AssetCategory, on_delete=models.SET_NULL, null=True, blank=True)
    total_assets_expected = models.IntegerField(default=0)
    total_assets_found = models.IntegerField(default=0)
    discrepancies_found = models.IntegerField(default=0)
    notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-audit_date']
    
    def __str__(self):
        return f"{self.audit_name} - {self.audit_date}"


class AssetAuditItem(BaseModel):
    """Individual asset items in an audit"""
    
    STATUS_CHOICES = [
        ('found', 'Found'),
        ('missing', 'Missing'),
        ('damaged', 'Damaged'),
        ('misplaced', 'Misplaced'),
    ]
    
    audit = models.ForeignKey(AssetAudit, on_delete=models.CASCADE, related_name='audit_items')
    asset = models.ForeignKey(Asset, on_delete=models.CASCADE)
    expected_location = models.ForeignKey(Location, on_delete=models.SET_NULL, null=True, blank=True, related_name='expected_audit_items')
    actual_location = models.ForeignKey(Location, on_delete=models.SET_NULL, null=True, blank=True, related_name='actual_audit_items')
    expected_condition = models.CharField(max_length=20, choices=Asset.CONDITION_CHOICES)
    actual_condition = models.CharField(max_length=20, choices=Asset.CONDITION_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    notes = models.TextField(blank=True)
    scanned_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        unique_together = ['audit', 'asset']
    
    def __str__(self):
        return f"{self.audit.audit_name} - {self.asset.asset_tag}"
    
    @property
    def has_discrepancy(self):
        """Check if there's any discrepancy in this audit item"""
        return (
            self.expected_location != self.actual_location or
            self.expected_condition != self.actual_condition or
            self.status in ['missing', 'damaged', 'misplaced']
        )


class InventoryItem(BaseModel):
    """Inventory items for consumable supplies and materials"""
    
    UNIT_CHOICES = [
        ('pcs', 'Pieces'),
        ('kg', 'Kilograms'),
        ('ltr', 'Liters'),
        ('box', 'Boxes'),
        ('pack', 'Packs'),
        ('roll', 'Rolls'),
        ('sheet', 'Sheets'),
        ('bottle', 'Bottles'),
        ('bag', 'Bags'),
        ('set', 'Sets'),
    ]
    
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('discontinued', 'Discontinued'),
        ('out_of_stock', 'Out of Stock'),
        ('low_stock', 'Low Stock'),
    ]
    
    # Basic Information
    item_code = models.CharField(max_length=50, unique=True, help_text="Unique item identifier")
    name = models.CharField(max_length=200)
    arabic_name = models.CharField(max_length=200, blank=True)
    description = models.TextField(blank=True)
    category = models.ForeignKey(AssetCategory, on_delete=models.PROTECT, related_name='inventory_items')
    
    # Physical Properties
    brand = models.CharField(max_length=100, blank=True)
    model = models.CharField(max_length=100, blank=True)
    unit_of_measure = models.CharField(max_length=20, choices=UNIT_CHOICES, default='pcs')
    barcode = models.CharField(max_length=100, blank=True, unique=True)
    
    # Stock Information
    current_stock = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    minimum_stock = models.DecimalField(max_digits=10, decimal_places=2, default=0, help_text="Reorder point")
    maximum_stock = models.DecimalField(max_digits=10, decimal_places=2, default=0, help_text="Maximum stock level")
    reorder_quantity = models.DecimalField(max_digits=10, decimal_places=2, default=0, help_text="Standard reorder quantity")
    
    # Financial Information
    unit_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0, help_text="Average unit cost")
    last_purchase_price = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    last_purchase_date = models.DateField(null=True, blank=True)
    
    # Location and Supplier
    primary_location = models.ForeignKey(Location, on_delete=models.SET_NULL, null=True, blank=True, related_name='primary_inventory_items')
    primary_supplier = models.ForeignKey(Supplier, on_delete=models.SET_NULL, null=True, blank=True, related_name='primary_inventory_items')
    
    # Status and Tracking
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    last_counted_date = models.DateField(null=True, blank=True, help_text="Last physical count date")
    last_counted_quantity = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    
    # Additional Information
    notes = models.TextField(blank=True)
    image = models.ImageField(upload_to='inventory_images/', blank=True)
    
    class Meta:
        ordering = ['item_code']
        indexes = [
            models.Index(fields=['school', 'item_code']),
            models.Index(fields=['school', 'status']),
            models.Index(fields=['category', 'status']),
            models.Index(fields=['primary_location']),
            models.Index(fields=['barcode']),
            models.Index(fields=['current_stock']),
        ]
    
    def __str__(self):
        return f"{self.item_code} - {self.name}"
    
    def save(self, *args, **kwargs):
        # Generate item code if not provided
        if not self.item_code:
            self.item_code = self.generate_item_code()
        
        # Generate barcode if not provided
        if not self.barcode:
            self.barcode = f"INV{uuid.uuid4().hex[:8].upper()}"
        
        # Update status based on stock levels
        self.update_status_from_stock()
        
        super().save(*args, **kwargs)
    
    def generate_item_code(self):
        """Generate unique item code"""
        prefix = "INV"
        if self.category:
            prefix = self.category.code[:3].upper()
        
        year = timezone.now().year
        
        # Get the next sequence number
        last_item = InventoryItem.objects.filter(
            school=self.school,
            item_code__startswith=f"{prefix}{year}"
        ).order_by('-item_code').first()
        
        if last_item:
            try:
                last_number = int(last_item.item_code[-4:])
                next_number = last_number + 1
            except ValueError:
                next_number = 1
        else:
            next_number = 1
        
        return f"{prefix}{year}{next_number:04d}"
    
    def update_status_from_stock(self):
        """Update status based on current stock levels"""
        if self.current_stock <= 0:
            self.status = 'out_of_stock'
        elif self.current_stock <= self.minimum_stock:
            self.status = 'low_stock'
        elif self.status in ['out_of_stock', 'low_stock']:
            self.status = 'active'
    
    @property
    def stock_value(self):
        """Calculate total stock value"""
        return (self.current_stock * self.unit_cost).quantize(Decimal('0.01'))
    
    @property
    def is_low_stock(self):
        """Check if item is low on stock"""
        return self.current_stock <= self.minimum_stock
    
    @property
    def is_out_of_stock(self):
        """Check if item is out of stock"""
        return self.current_stock <= 0
    
    @property
    def reorder_needed(self):
        """Check if reorder is needed"""
        return self.current_stock <= self.minimum_stock and self.status == 'active'
    
    @property
    def days_since_last_count(self):
        """Calculate days since last physical count"""
        if self.last_counted_date:
            return (timezone.now().date() - self.last_counted_date).days
        return None


class InventoryLocation(BaseModel):
    """Track inventory items at specific locations"""
    item = models.ForeignKey(InventoryItem, on_delete=models.CASCADE, related_name='location_stocks')
    location = models.ForeignKey(Location, on_delete=models.CASCADE, related_name='inventory_stocks')
    quantity = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    minimum_quantity = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    last_updated = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['item', 'location']
        ordering = ['item', 'location']
    
    def __str__(self):
        return f"{self.item.name} at {self.location.name}: {self.quantity}"


class StockTransaction(BaseModel):
    """Track all stock movements and transactions"""
    
    TRANSACTION_TYPES = [
        ('receipt', 'Receipt'),
        ('issue', 'Issue'),
        ('transfer', 'Transfer'),
        ('adjustment', 'Adjustment'),
        ('return', 'Return'),
        ('damage', 'Damage'),
        ('loss', 'Loss'),
        ('count', 'Physical Count'),
    ]
    
    transaction_id = models.CharField(max_length=50, unique=True)
    item = models.ForeignKey(InventoryItem, on_delete=models.CASCADE, related_name='stock_transactions')
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES)
    quantity = models.DecimalField(max_digits=10, decimal_places=2)
    unit_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Location information
    from_location = models.ForeignKey(Location, on_delete=models.SET_NULL, null=True, blank=True, related_name='stock_transactions_from')
    to_location = models.ForeignKey(Location, on_delete=models.SET_NULL, null=True, blank=True, related_name='stock_transactions_to')
    
    # Reference information
    reference_type = models.CharField(max_length=50, blank=True, help_text="Type of reference document")
    reference_id = models.CharField(max_length=100, blank=True, help_text="Reference document ID")
    reference_date = models.DateField(null=True, blank=True)
    
    # Additional information
    reason = models.CharField(max_length=200, blank=True)
    notes = models.TextField(blank=True)
    transaction_date = models.DateTimeField(default=timezone.now)
    
    # Tracking
    requested_by = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True, blank=True, related_name='requested_stock_transactions')
    approved_by = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_stock_transactions')
    
    class Meta:
        ordering = ['-transaction_date']
        indexes = [
            models.Index(fields=['school', 'transaction_date']),
            models.Index(fields=['item', 'transaction_date']),
            models.Index(fields=['transaction_type', 'transaction_date']),
            models.Index(fields=['from_location', 'transaction_date']),
            models.Index(fields=['to_location', 'transaction_date']),
        ]
    
    def __str__(self):
        return f"{self.transaction_id} - {self.item.name} ({self.get_transaction_type_display()})"
    
    def save(self, *args, **kwargs):
        # Generate transaction ID if not provided
        if not self.transaction_id:
            self.transaction_id = self.generate_transaction_id()
        
        super().save(*args, **kwargs)
        
        # Update item stock after saving transaction
        self.update_item_stock()
    
    def generate_transaction_id(self):
        """Generate unique transaction ID"""
        prefix = self.transaction_type.upper()[:3]
        date_str = timezone.now().strftime('%Y%m%d')
        
        # Get the next sequence number for today
        last_transaction = StockTransaction.objects.filter(
            school=self.school,
            transaction_id__startswith=f"{prefix}{date_str}"
        ).order_by('-transaction_id').first()
        
        if last_transaction:
            try:
                last_number = int(last_transaction.transaction_id[-3:])
                next_number = last_number + 1
            except ValueError:
                next_number = 1
        else:
            next_number = 1
        
        return f"{prefix}{date_str}{next_number:03d}"
    
    def update_item_stock(self):
        """Update item stock based on transaction"""
        item = self.item
        
        if self.transaction_type in ['receipt', 'return']:
            # Increase stock
            item.current_stock += self.quantity
        elif self.transaction_type in ['issue', 'damage', 'loss']:
            # Decrease stock
            item.current_stock -= self.quantity
        elif self.transaction_type == 'adjustment':
            # Direct adjustment (quantity can be positive or negative)
            item.current_stock += self.quantity
        elif self.transaction_type == 'count':
            # Physical count - set to exact quantity
            item.current_stock = self.quantity
            item.last_counted_date = self.transaction_date.date()
            item.last_counted_quantity = self.quantity
        
        # Ensure stock doesn't go negative
        if item.current_stock < 0:
            item.current_stock = Decimal('0.00')
        
        # Update unit cost if this is a receipt with cost
        if self.transaction_type == 'receipt' and self.unit_cost > 0:
            # Calculate weighted average cost
            total_value = (item.current_stock * item.unit_cost) + (self.quantity * self.unit_cost)
            total_quantity = item.current_stock + self.quantity
            if total_quantity > 0:
                item.unit_cost = (total_value / total_quantity).quantize(Decimal('0.01'))
            
            item.last_purchase_price = self.unit_cost
            item.last_purchase_date = self.transaction_date.date()
        
        item.save()


class PurchaseOrder(BaseModel):
    """Purchase orders for inventory items"""
    
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('pending', 'Pending Approval'),
        ('approved', 'Approved'),
        ('sent', 'Sent to Supplier'),
        ('partial', 'Partially Received'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    po_number = models.CharField(max_length=50, unique=True)
    supplier = models.ForeignKey(Supplier, on_delete=models.PROTECT, related_name='purchase_orders')
    order_date = models.DateField(default=timezone.now)
    expected_delivery_date = models.DateField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    
    # Financial information
    subtotal = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    tax_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    
    # Approval workflow
    requested_by = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True, blank=True, related_name='requested_purchase_orders')
    approved_by = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_purchase_orders')
    approval_date = models.DateField(null=True, blank=True)
    
    # Additional information
    notes = models.TextField(blank=True)
    terms_and_conditions = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-order_date']
        indexes = [
            models.Index(fields=['school', 'po_number']),
            models.Index(fields=['supplier', 'order_date']),
            models.Index(fields=['status', 'order_date']),
        ]
    
    def __str__(self):
        return f"{self.po_number} - {self.supplier.name}"
    
    def save(self, *args, **kwargs):
        # Generate PO number if not provided
        if not self.po_number:
            self.po_number = self.generate_po_number()
        
        super().save(*args, **kwargs)
    
    def generate_po_number(self):
        """Generate unique purchase order number"""
        prefix = "PO"
        year = timezone.now().year
        
        # Get the next sequence number
        last_po = PurchaseOrder.objects.filter(
            school=self.school,
            po_number__startswith=f"{prefix}{year}"
        ).order_by('-po_number').first()
        
        if last_po:
            try:
                last_number = int(last_po.po_number[-4:])
                next_number = last_number + 1
            except ValueError:
                next_number = 1
        else:
            next_number = 1
        
        return f"{prefix}{year}{next_number:04d}"
    
    def calculate_totals(self):
        """Calculate order totals from line items"""
        line_items = self.line_items.all()
        self.subtotal = sum(item.total_amount for item in line_items)
        # Tax calculation can be customized based on requirements
        self.tax_amount = Decimal('0.00')  # Placeholder
        self.total_amount = self.subtotal + self.tax_amount
        self.save(update_fields=['subtotal', 'tax_amount', 'total_amount'])


class PurchaseOrderItem(BaseModel):
    """Line items for purchase orders"""
    purchase_order = models.ForeignKey(PurchaseOrder, on_delete=models.CASCADE, related_name='line_items')
    item = models.ForeignKey(InventoryItem, on_delete=models.CASCADE)
    quantity_ordered = models.DecimalField(max_digits=10, decimal_places=2)
    quantity_received = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    notes = models.TextField(blank=True)
    
    class Meta:
        unique_together = ['purchase_order', 'item']
        ordering = ['item__name']
    
    def __str__(self):
        return f"{self.purchase_order.po_number} - {self.item.name}"
    
    def save(self, *args, **kwargs):
        # Calculate total amount
        self.total_amount = self.quantity_ordered * self.unit_price
        super().save(*args, **kwargs)
        
        # Update purchase order totals
        self.purchase_order.calculate_totals()
    
    @property
    def quantity_pending(self):
        """Calculate quantity still pending delivery"""
        return self.quantity_ordered - self.quantity_received
    
    @property
    def is_fully_received(self):
        """Check if item is fully received"""
        return self.quantity_received >= self.quantity_ordered


class StockAlert(BaseModel):
    """Stock alerts and notifications"""
    
    ALERT_TYPES = [
        ('low_stock', 'Low Stock'),
        ('out_of_stock', 'Out of Stock'),
        ('overstock', 'Overstock'),
        ('reorder_point', 'Reorder Point Reached'),
        ('expiry_warning', 'Expiry Warning'),
        ('no_movement', 'No Movement'),
    ]
    
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('acknowledged', 'Acknowledged'),
        ('resolved', 'Resolved'),
        ('dismissed', 'Dismissed'),
    ]
    
    item = models.ForeignKey(InventoryItem, on_delete=models.CASCADE, related_name='stock_alerts')
    alert_type = models.CharField(max_length=20, choices=ALERT_TYPES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    alert_date = models.DateTimeField(default=timezone.now)
    message = models.TextField()
    current_stock = models.DecimalField(max_digits=10, decimal_places=2)
    threshold_value = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    
    # Resolution tracking
    acknowledged_by = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True, blank=True, related_name='acknowledged_alerts')
    acknowledged_date = models.DateTimeField(null=True, blank=True)
    resolved_date = models.DateTimeField(null=True, blank=True)
    resolution_notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-alert_date']
        indexes = [
            models.Index(fields=['school', 'status', 'alert_date']),
            models.Index(fields=['item', 'alert_type']),
            models.Index(fields=['alert_type', 'status']),
        ]
    
    def __str__(self):
        return f"{self.item.name} - {self.get_alert_type_display()}"
    
    def acknowledge(self, user=None):
        """Acknowledge the alert"""
        self.status = 'acknowledged'
        self.acknowledged_by = user
        self.acknowledged_date = timezone.now()
        self.save()
    
    def resolve(self, notes="", user=None):
        """Resolve the alert"""
        self.status = 'resolved'
        self.resolved_date = timezone.now()
        self.resolution_notes = notes
        if user and not self.acknowledged_by:
            self.acknowledged_by = user
            self.acknowledged_date = timezone.now()
        self.save()


class InventoryCount(BaseModel):
    """Physical inventory count sessions"""
    
    STATUS_CHOICES = [
        ('planned', 'Planned'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    count_name = models.CharField(max_length=200)
    count_date = models.DateField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='planned')
    counter = models.ForeignKey('hr.Employee', on_delete=models.SET_NULL, null=True, blank=True)
    location = models.ForeignKey(Location, on_delete=models.SET_NULL, null=True, blank=True)
    category = models.ForeignKey(AssetCategory, on_delete=models.SET_NULL, null=True, blank=True)
    
    # Count statistics
    total_items_expected = models.IntegerField(default=0)
    total_items_counted = models.IntegerField(default=0)
    discrepancies_found = models.IntegerField(default=0)
    
    notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-count_date']
    
    def __str__(self):
        return f"{self.count_name} - {self.count_date}"


class InventoryCountItem(BaseModel):
    """Individual items in an inventory count"""
    
    STATUS_CHOICES = [
        ('counted', 'Counted'),
        ('not_found', 'Not Found'),
        ('damaged', 'Damaged'),
        ('discrepancy', 'Discrepancy'),
    ]
    
    count = models.ForeignKey(InventoryCount, on_delete=models.CASCADE, related_name='count_items')
    item = models.ForeignKey(InventoryItem, on_delete=models.CASCADE)
    expected_quantity = models.DecimalField(max_digits=10, decimal_places=2)
    counted_quantity = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    variance = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='counted')
    notes = models.TextField(blank=True)
    counted_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        unique_together = ['count', 'item']
        ordering = ['item__name']
    
    def __str__(self):
        return f"{self.count.count_name} - {self.item.name}"
    
    def save(self, *args, **kwargs):
        # Calculate variance
        self.variance = self.counted_quantity - self.expected_quantity
        
        # Determine status based on variance
        if self.variance != 0:
            self.status = 'discrepancy'
        elif self.counted_quantity == 0:
            self.status = 'not_found'
        else:
            self.status = 'counted'
        
        super().save(*args, **kwargs)
    
    @property
    def has_discrepancy(self):
        """Check if there's a discrepancy"""
        return self.variance != 0


class AssetAnalytics(BaseModel):
    """Store asset analytics and metrics"""
    
    METRIC_TYPES = [
        ('utilization', 'Utilization Rate'),
        ('depreciation', 'Depreciation Analysis'),
        ('maintenance_cost', 'Maintenance Cost'),
        ('category_distribution', 'Category Distribution'),
        ('location_distribution', 'Location Distribution'),
        ('age_analysis', 'Age Analysis'),
        ('condition_analysis', 'Condition Analysis'),
        ('inventory_turnover', 'Inventory Turnover'),
        ('stock_value', 'Stock Value Analysis'),
        ('reorder_analysis', 'Reorder Analysis'),
    ]
    
    metric_type = models.CharField(max_length=30, choices=METRIC_TYPES)
    calculation_date = models.DateField(default=timezone.now)
    period_start = models.DateField()
    period_end = models.DateField()
    category = models.ForeignKey(AssetCategory, on_delete=models.SET_NULL, null=True, blank=True)
    location = models.ForeignKey(Location, on_delete=models.SET_NULL, null=True, blank=True)
    metric_value = models.DecimalField(max_digits=15, decimal_places=2)
    metric_data = models.JSONField(default=dict, help_text="Additional metric data in JSON format")
    notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-calculation_date']
        indexes = [
            models.Index(fields=['school', 'metric_type', 'calculation_date']),
            models.Index(fields=['category', 'metric_type']),
            models.Index(fields=['location', 'metric_type']),
        ]
    
    def __str__(self):
        return f"{self.get_metric_type_display()} - {self.calculation_date}"