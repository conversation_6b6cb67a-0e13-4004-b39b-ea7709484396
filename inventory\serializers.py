from rest_framework import serializers
from decimal import Decimal
from .models import (
    AssetCategory, Location, Supplier, Asset, AssetMovement, 
    AssetMaintenance, AssetDepreciation, AssetAudit, AssetAuditItem, 
    AssetAnalytics
)


class AssetCategorySerializer(serializers.ModelSerializer):
    """Serializer for asset categories"""
    
    asset_count = serializers.SerializerMethodField()
    full_path = serializers.ReadOnlyField()
    
    class Meta:
        model = AssetCategory
        fields = [
            'id', 'name', 'arabic_name', 'code', 'description', 'parent',
            'depreciation_rate', 'useful_life_years', 'is_active',
            'asset_count', 'full_path', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_asset_count(self, obj):
        return obj.assets.filter(status='active').count()


class LocationSerializer(serializers.ModelSerializer):
    """Serializer for locations"""
    
    asset_count = serializers.SerializerMethodField()
    responsible_person_name = serializers.SerializerMethodField()
    
    class Meta:
        model = Location
        fields = [
            'id', 'name', 'arabic_name', 'code', 'description', 'building',
            'floor', 'room', 'responsible_person', 'responsible_person_name',
            'is_active', 'asset_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_asset_count(self, obj):
        return obj.asset_set.filter(status='active').count()
    
    def get_responsible_person_name(self, obj):
        if obj.responsible_person:
            return f"{obj.responsible_person.first_name} {obj.responsible_person.last_name}"
        return None


class SupplierSerializer(serializers.ModelSerializer):
    """Serializer for suppliers"""
    
    asset_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Supplier
        fields = [
            'id', 'name', 'arabic_name', 'code', 'contact_person', 'phone',
            'email', 'address', 'tax_number', 'payment_terms', 'rating',
            'is_active', 'asset_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_asset_count(self, obj):
        return obj.asset_set.filter(status='active').count()


class AssetSerializer(serializers.ModelSerializer):
    """Serializer for assets"""
    
    category_name = serializers.CharField(source='category.name', read_only=True)
    location_name = serializers.CharField(source='location.name', read_only=True)
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    assigned_to_name = serializers.SerializerMethodField()
    
    # Calculated fields
    age_in_years = serializers.ReadOnlyField()
    accumulated_depreciation = serializers.ReadOnlyField()
    book_value = serializers.ReadOnlyField()
    is_warranty_valid = serializers.ReadOnlyField()
    maintenance_due = serializers.ReadOnlyField()
    
    # Status indicators
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    condition_display = serializers.CharField(source='get_condition_display', read_only=True)
    
    class Meta:
        model = Asset
        fields = [
            'id', 'asset_tag', 'name', 'arabic_name', 'description', 'category',
            'category_name', 'brand', 'model', 'serial_number', 'barcode', 'qr_code',
            'purchase_price', 'current_value', 'salvage_value', 'purchase_date',
            'supplier', 'supplier_name', 'invoice_number', 'warranty_expiry',
            'location', 'location_name', 'assigned_to', 'assigned_to_name',
            'department', 'status', 'status_display', 'condition', 'condition_display',
            'last_maintenance_date', 'next_maintenance_date', 'depreciation_method',
            'useful_life_years', 'depreciation_rate', 'notes', 'image',
            'age_in_years', 'accumulated_depreciation', 'book_value',
            'is_warranty_valid', 'maintenance_due', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'asset_tag', 'barcode', 'qr_code', 'current_value', 'created_at', 'updated_at'
        ]
    
    def get_assigned_to_name(self, obj):
        if obj.assigned_to:
            return f"{obj.assigned_to.first_name} {obj.assigned_to.last_name}"
        return None


class AssetSummarySerializer(serializers.ModelSerializer):
    """Lightweight serializer for asset summaries"""
    
    category_name = serializers.CharField(source='category.name', read_only=True)
    location_name = serializers.CharField(source='location.name', read_only=True)
    book_value = serializers.ReadOnlyField()
    
    class Meta:
        model = Asset
        fields = [
            'id', 'asset_tag', 'name', 'category_name', 'location_name',
            'status', 'condition', 'purchase_price', 'book_value'
        ]


class AssetMovementSerializer(serializers.ModelSerializer):
    """Serializer for asset movements"""
    
    asset_tag = serializers.CharField(source='asset.asset_tag', read_only=True)
    asset_name = serializers.CharField(source='asset.name', read_only=True)
    from_location_name = serializers.CharField(source='from_location.name', read_only=True)
    to_location_name = serializers.CharField(source='to_location.name', read_only=True)
    from_employee_name = serializers.SerializerMethodField()
    to_employee_name = serializers.SerializerMethodField()
    movement_type_display = serializers.CharField(source='get_movement_type_display', read_only=True)
    created_by_name = serializers.SerializerMethodField()
    
    class Meta:
        model = AssetMovement
        fields = [
            'id', 'asset', 'asset_tag', 'asset_name', 'movement_type',
            'movement_type_display', 'from_location', 'from_location_name',
            'to_location', 'to_location_name', 'from_employee', 'from_employee_name',
            'to_employee', 'to_employee_name', 'movement_date', 'reason', 'notes',
            'approved_by', 'created_by_name', 'created_at'
        ]
        read_only_fields = ['movement_date', 'created_at']
    
    def get_from_employee_name(self, obj):
        if obj.from_employee:
            return f"{obj.from_employee.first_name} {obj.from_employee.last_name}"
        return None
    
    def get_to_employee_name(self, obj):
        if obj.to_employee:
            return f"{obj.to_employee.first_name} {obj.to_employee.last_name}"
        return None
    
    def get_created_by_name(self, obj):
        if obj.created_by:
            return f"{obj.created_by.first_name} {obj.created_by.last_name}"
        return None


class AssetMaintenanceSerializer(serializers.ModelSerializer):
    """Serializer for asset maintenance"""
    
    asset_tag = serializers.CharField(source='asset.asset_tag', read_only=True)
    asset_name = serializers.CharField(source='asset.name', read_only=True)
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    maintenance_type_display = serializers.CharField(source='get_maintenance_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = AssetMaintenance
        fields = [
            'id', 'asset', 'asset_tag', 'asset_name', 'maintenance_type',
            'maintenance_type_display', 'status', 'status_display', 'scheduled_date',
            'completed_date', 'description', 'cost', 'performed_by', 'supplier',
            'supplier_name', 'notes', 'next_maintenance_date', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class AssetDepreciationSerializer(serializers.ModelSerializer):
    """Serializer for asset depreciation"""
    
    asset_tag = serializers.CharField(source='asset.asset_tag', read_only=True)
    asset_name = serializers.CharField(source='asset.name', read_only=True)
    
    class Meta:
        model = AssetDepreciation
        fields = [
            'id', 'asset', 'asset_tag', 'asset_name', 'calculation_date',
            'period_start', 'period_end', 'opening_value', 'depreciation_amount',
            'closing_value', 'method_used', 'notes', 'created_at'
        ]
        read_only_fields = ['calculation_date', 'created_at']


class AssetAuditItemSerializer(serializers.ModelSerializer):
    """Serializer for asset audit items"""
    
    asset_tag = serializers.CharField(source='asset.asset_tag', read_only=True)
    asset_name = serializers.CharField(source='asset.name', read_only=True)
    expected_location_name = serializers.CharField(source='expected_location.name', read_only=True)
    actual_location_name = serializers.CharField(source='actual_location.name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    has_discrepancy = serializers.ReadOnlyField()
    
    class Meta:
        model = AssetAuditItem
        fields = [
            'id', 'asset', 'asset_tag', 'asset_name', 'expected_location',
            'expected_location_name', 'actual_location', 'actual_location_name',
            'expected_condition', 'actual_condition', 'status', 'status_display',
            'notes', 'scanned_at', 'has_discrepancy'
        ]


class AssetAuditSerializer(serializers.ModelSerializer):
    """Serializer for asset audits"""
    
    auditor_name = serializers.SerializerMethodField()
    location_name = serializers.CharField(source='location.name', read_only=True)
    category_name = serializers.CharField(source='category.name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    accuracy_rate = serializers.SerializerMethodField()
    audit_items = AssetAuditItemSerializer(many=True, read_only=True)
    
    class Meta:
        model = AssetAudit
        fields = [
            'id', 'audit_name', 'audit_date', 'status', 'status_display',
            'auditor', 'auditor_name', 'location', 'location_name', 'category',
            'category_name', 'total_assets_expected', 'total_assets_found',
            'discrepancies_found', 'accuracy_rate', 'notes', 'audit_items',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_auditor_name(self, obj):
        if obj.auditor:
            return f"{obj.auditor.first_name} {obj.auditor.last_name}"
        return None
    
    def get_accuracy_rate(self, obj):
        if obj.total_assets_expected > 0:
            return round((obj.total_assets_found / obj.total_assets_expected) * 100, 2)
        return 0


class AssetAnalyticsSerializer(serializers.ModelSerializer):
    """Serializer for asset analytics"""
    
    metric_type_display = serializers.CharField(source='get_metric_type_display', read_only=True)
    category_name = serializers.CharField(source='category.name', read_only=True)
    location_name = serializers.CharField(source='location.name', read_only=True)
    
    class Meta:
        model = AssetAnalytics
        fields = [
            'id', 'metric_type', 'metric_type_display', 'calculation_date',
            'period_start', 'period_end', 'category', 'category_name',
            'location', 'location_name', 'metric_value', 'metric_data',
            'notes', 'created_at'
        ]
        read_only_fields = ['calculation_date', 'created_at']


class AssetDashboardSerializer(serializers.Serializer):
    """Serializer for asset dashboard data"""
    
    total_assets = serializers.IntegerField()
    active_assets = serializers.IntegerField()
    maintenance_assets = serializers.IntegerField()
    retired_assets = serializers.IntegerField()
    total_value = serializers.DecimalField(max_digits=15, decimal_places=2)
    current_value = serializers.DecimalField(max_digits=15, decimal_places=2)
    depreciation_amount = serializers.DecimalField(max_digits=15, decimal_places=2)
    maintenance_due = serializers.IntegerField()
    warranty_expiring = serializers.IntegerField()
    
    # Distribution data
    by_category = serializers.ListField()
    by_location = serializers.ListField()
    by_condition = serializers.ListField()
    by_age = serializers.DictField()
    
    # Recent activities
    recent_movements = AssetMovementSerializer(many=True)
    recent_maintenance = AssetMaintenanceSerializer(many=True)


class AssetReportSerializer(serializers.Serializer):
    """Serializer for asset reports"""
    
    report_type = serializers.CharField()
    generated_at = serializers.DateTimeField()
    filters = serializers.DictField()
    data = serializers.DictField()


class AssetBarcodeSerializer(serializers.ModelSerializer):
    """Lightweight serializer for barcode scanning"""
    
    category_name = serializers.CharField(source='category.name', read_only=True)
    location_name = serializers.CharField(source='location.name', read_only=True)
    
    class Meta:
        model = Asset
        fields = [
            'id', 'asset_tag', 'name', 'barcode', 'qr_code', 'category_name',
            'location_name', 'status', 'condition'
        ]


class AssetTransferSerializer(serializers.Serializer):
    """Serializer for asset transfer requests"""
    
    asset_id = serializers.IntegerField()
    to_location_id = serializers.IntegerField(required=False, allow_null=True)
    to_employee_id = serializers.IntegerField(required=False, allow_null=True)
    reason = serializers.CharField(max_length=500)
    notes = serializers.CharField(required=False, allow_blank=True)
    
    def validate(self, data):
        if not data.get('to_location_id') and not data.get('to_employee_id'):
            raise serializers.ValidationError(
                "Either to_location_id or to_employee_id must be provided"
            )
        return data


class MaintenanceScheduleSerializer(serializers.Serializer):
    """Serializer for maintenance scheduling"""
    
    asset_id = serializers.IntegerField()
    maintenance_type = serializers.ChoiceField(choices=AssetMaintenance.MAINTENANCE_TYPES)
    scheduled_date = serializers.DateField()
    description = serializers.CharField()
    cost = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, default=0)
    performed_by = serializers.CharField(required=False, allow_blank=True)
    supplier_id = serializers.IntegerField(required=False, allow_null=True)
    next_maintenance_date = serializers.DateField(required=False, allow_null=True)
    notes = serializers.CharField(required=False, allow_blank=True)


class AssetUtilizationSerializer(serializers.Serializer):
    """Serializer for asset utilization metrics"""
    
    total_assets = serializers.IntegerField()
    assigned_assets = serializers.IntegerField()
    utilization_rate = serializers.DecimalField(max_digits=5, decimal_places=2)
    by_category = serializers.ListField()


class DepreciationAnalysisSerializer(serializers.Serializer):
    """Serializer for depreciation analysis"""
    
    total_purchase_value = serializers.DecimalField(max_digits=15, decimal_places=2)
    total_current_value = serializers.DecimalField(max_digits=15, decimal_places=2)
    total_depreciation = serializers.DecimalField(max_digits=15, decimal_places=2)
    depreciation_rate = serializers.DecimalField(max_digits=5, decimal_places=2)
    by_category = serializers.DictField()


class MaintenanceCostSerializer(serializers.Serializer):
    """Serializer for maintenance cost analysis"""
    
    total_cost = serializers.DecimalField(max_digits=12, decimal_places=2)
    by_type = serializers.ListField()
    by_category = serializers.ListField()
    record_count = serializers.IntegerField()