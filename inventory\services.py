from django.db import transaction
from django.utils import timezone
from django.db.models import Q, Count, Sum, Avg, F, Case, When, DecimalField
from decimal import Decimal
from datetime import datetime, timedelta
import json
from typing import Dict, List, Optional, Tuple

from .models import (
    Asset, AssetCategory, Location, Supplier, AssetMovement, 
    AssetMaintenance, AssetDepreciation, AssetAudit, AssetAuditItem, 
    AssetAnalytics, InventoryItem, InventoryLocation, StockTransaction,
    PurchaseOrder, PurchaseOrderItem, StockAlert, InventoryCount, 
    InventoryCountItem
)


class AssetTrackingService:
    """Service for comprehensive asset tracking and management"""
    
    @staticmethod
    def create_asset(asset_data: dict, school) -> Asset:
        """Create a new asset with validation"""
        with transaction.atomic():
            asset_data['school'] = school
            
            # Generate asset tag if not provided
            if not asset_data.get('asset_tag'):
                asset_data['asset_tag'] = AssetTrackingService.generate_asset_tag(school)
            
            asset = Asset.objects.create(**asset_data)
            
            # Create initial movement record
            if asset.location or asset.assigned_to:
                AssetMovement.objects.create(
                    school=school,
                    asset=asset,
                    movement_type='assignment',
                    to_location=asset.location,
                    to_employee=asset.assigned_to,
                    reason='Initial assignment',
                    created_by=asset.created_by
                )
            
            return asset
    
    @staticmethod
    def generate_asset_tag(school) -> str:
        """Generate unique asset tag"""
        prefix = school.code[:3].upper()
        year = timezone.now().year
        
        # Get the next sequence number
        last_asset = Asset.objects.filter(
            school=school,
            asset_tag__startswith=f"{prefix}{year}"
        ).order_by('-asset_tag').first()
        
        if last_asset:
            try:
                last_number = int(last_asset.asset_tag[-4:])
                next_number = last_number + 1
            except ValueError:
                next_number = 1
        else:
            next_number = 1
        
        return f"{prefix}{year}{next_number:04d}"
    
    @staticmethod
    def transfer_asset(asset: Asset, to_location: Location = None, 
                      to_employee=None, reason: str = "", user=None) -> AssetMovement:
        """Transfer asset to new location or employee"""
        with transaction.atomic():
            movement = AssetMovement.objects.create(
                school=asset.school,
                asset=asset,
                movement_type='transfer',
                from_location=asset.location,
                from_employee=asset.assigned_to,
                to_location=to_location,
                to_employee=to_employee,
                reason=reason,
                created_by=user
            )
            
            # Update asset location and assignment
            asset.location = to_location
            asset.assigned_to = to_employee
            asset.save(update_fields=['location', 'assigned_to'])
            
            return movement
    
    @staticmethod
    def retire_asset(asset: Asset, reason: str = "", user=None) -> AssetMovement:
        """Retire an asset"""
        with transaction.atomic():
            movement = AssetMovement.objects.create(
                school=asset.school,
                asset=asset,
                movement_type='disposal',
                from_location=asset.location,
                from_employee=asset.assigned_to,
                reason=reason,
                created_by=user
            )
            
            asset.status = 'retired'
            asset.location = None
            asset.assigned_to = None
            asset.save(update_fields=['status', 'location', 'assigned_to'])
            
            return movement
    
    @staticmethod
    def get_asset_history(asset: Asset) -> List[Dict]:
        """Get complete history of an asset"""
        movements = asset.movements.select_related(
            'from_location', 'to_location', 'from_employee', 'to_employee', 'created_by'
        ).order_by('-movement_date')
        
        maintenance = asset.maintenance_records.select_related('supplier').order_by('-scheduled_date')
        
        history = []
        
        # Add movements to history
        for movement in movements:
            history.append({
                'type': 'movement',
                'date': movement.movement_date,
                'action': movement.get_movement_type_display(),
                'details': {
                    'from_location': str(movement.from_location) if movement.from_location else None,
                    'to_location': str(movement.to_location) if movement.to_location else None,
                    'from_employee': str(movement.from_employee) if movement.from_employee else None,
                    'to_employee': str(movement.to_employee) if movement.to_employee else None,
                    'reason': movement.reason,
                    'notes': movement.notes
                },
                'user': str(movement.created_by) if movement.created_by else None
            })
        
        # Add maintenance to history
        for maint in maintenance:
            history.append({
                'type': 'maintenance',
                'date': maint.scheduled_date,
                'action': f"{maint.get_maintenance_type_display()} Maintenance",
                'details': {
                    'status': maint.get_status_display(),
                    'description': maint.description,
                    'cost': float(maint.cost),
                    'performed_by': maint.performed_by,
                    'supplier': str(maint.supplier) if maint.supplier else None,
                    'completed_date': maint.completed_date
                },
                'user': str(maint.created_by) if maint.created_by else None
            })
        
        # Sort by date descending
        history.sort(key=lambda x: x['date'], reverse=True)
        
        return history
    
    @staticmethod
    def search_assets(school, query: str = "", filters: dict = None) -> List[Asset]:
        """Search assets with filters"""
        queryset = Asset.objects.filter(school=school).select_related(
            'category', 'location', 'assigned_to', 'supplier'
        )
        
        if query:
            queryset = queryset.filter(
                Q(asset_tag__icontains=query) |
                Q(name__icontains=query) |
                Q(arabic_name__icontains=query) |
                Q(description__icontains=query) |
                Q(brand__icontains=query) |
                Q(model__icontains=query) |
                Q(serial_number__icontains=query) |
                Q(barcode__icontains=query)
            )
        
        if filters:
            if filters.get('category'):
                queryset = queryset.filter(category_id=filters['category'])
            if filters.get('location'):
                queryset = queryset.filter(location_id=filters['location'])
            if filters.get('status'):
                queryset = queryset.filter(status=filters['status'])
            if filters.get('condition'):
                queryset = queryset.filter(condition=filters['condition'])
            if filters.get('assigned_to'):
                queryset = queryset.filter(assigned_to_id=filters['assigned_to'])
        
        return queryset.order_by('asset_tag')


class AssetDepreciationService:
    """Service for asset depreciation calculations"""
    
    @staticmethod
    def calculate_depreciation_for_period(school, start_date, end_date) -> List[Dict]:
        """Calculate depreciation for all assets in a period"""
        assets = Asset.objects.filter(
            school=school,
            status__in=['active', 'maintenance'],
            purchase_date__lte=end_date
        )
        
        depreciation_records = []
        
        for asset in assets:
            # Skip if asset was purchased after the period
            if asset.purchase_date > end_date:
                continue
            
            # Calculate depreciation for the period
            opening_value = asset.purchase_price
            
            # Get previous depreciation records
            previous_depreciation = AssetDepreciation.objects.filter(
                asset=asset,
                period_end__lt=start_date
            ).aggregate(total=Sum('depreciation_amount'))['total'] or Decimal('0.00')
            
            opening_value -= previous_depreciation
            
            # Calculate depreciation amount for this period
            if asset.depreciation_method == 'straight_line':
                annual_depreciation = (asset.purchase_price - asset.salvage_value) / asset.useful_life_years
                days_in_period = (end_date - start_date).days
                period_depreciation = annual_depreciation * (days_in_period / 365.25)
            else:
                # For other methods, use the asset's built-in calculation
                period_depreciation = asset.accumulated_depreciation - previous_depreciation
            
            closing_value = opening_value - period_depreciation
            
            # Ensure closing value doesn't go below salvage value
            if closing_value < asset.salvage_value:
                period_depreciation = opening_value - asset.salvage_value
                closing_value = asset.salvage_value
            
            depreciation_record = {
                'asset': asset,
                'opening_value': opening_value,
                'depreciation_amount': period_depreciation,
                'closing_value': closing_value,
                'method_used': asset.depreciation_method
            }
            
            depreciation_records.append(depreciation_record)
        
        return depreciation_records
    
    @staticmethod
    def create_depreciation_entries(school, start_date, end_date, user=None):
        """Create depreciation entries for a period"""
        depreciation_records = AssetDepreciationService.calculate_depreciation_for_period(
            school, start_date, end_date
        )
        
        created_entries = []
        
        with transaction.atomic():
            for record in depreciation_records:
                # Check if entry already exists
                existing = AssetDepreciation.objects.filter(
                    asset=record['asset'],
                    period_start=start_date,
                    period_end=end_date
                ).first()
                
                if not existing:
                    entry = AssetDepreciation.objects.create(
                        school=school,
                        asset=record['asset'],
                        period_start=start_date,
                        period_end=end_date,
                        opening_value=record['opening_value'],
                        depreciation_amount=record['depreciation_amount'],
                        closing_value=record['closing_value'],
                        method_used=record['method_used'],
                        created_by=user
                    )
                    created_entries.append(entry)
                    
                    # Update asset current value
                    record['asset'].current_value = record['closing_value']
                    record['asset'].save(update_fields=['current_value'])
        
        return created_entries


class AssetMaintenanceService:
    """Service for asset maintenance management"""
    
    @staticmethod
    def schedule_maintenance(asset: Asset, maintenance_data: dict, user=None) -> AssetMaintenance:
        """Schedule maintenance for an asset"""
        maintenance_data['school'] = asset.school
        maintenance_data['asset'] = asset
        maintenance_data['created_by'] = user
        
        maintenance = AssetMaintenance.objects.create(**maintenance_data)
        
        # Update asset next maintenance date if this is preventive maintenance
        if maintenance.maintenance_type == 'preventive' and maintenance.next_maintenance_date:
            asset.next_maintenance_date = maintenance.next_maintenance_date
            asset.save(update_fields=['next_maintenance_date'])
        
        return maintenance
    
    @staticmethod
    def complete_maintenance(maintenance: AssetMaintenance, completion_data: dict) -> AssetMaintenance:
        """Mark maintenance as completed"""
        with transaction.atomic():
            maintenance.status = 'completed'
            maintenance.completed_date = completion_data.get('completed_date', timezone.now().date())
            maintenance.cost = completion_data.get('cost', maintenance.cost)
            maintenance.notes = completion_data.get('notes', maintenance.notes)
            maintenance.performed_by = completion_data.get('performed_by', maintenance.performed_by)
            
            if completion_data.get('next_maintenance_date'):
                maintenance.next_maintenance_date = completion_data['next_maintenance_date']
            
            maintenance.save()
            
            # Update asset status and maintenance dates
            asset = maintenance.asset
            asset.last_maintenance_date = maintenance.completed_date
            
            if maintenance.next_maintenance_date:
                asset.next_maintenance_date = maintenance.next_maintenance_date
            
            # Update asset condition if provided
            if completion_data.get('asset_condition'):
                asset.condition = completion_data['asset_condition']
            
            asset.save(update_fields=['last_maintenance_date', 'next_maintenance_date', 'condition'])
        
        return maintenance
    
    @staticmethod
    def get_maintenance_due(school, days_ahead: int = 30) -> List[Asset]:
        """Get assets with maintenance due within specified days"""
        cutoff_date = timezone.now().date() + timedelta(days=days_ahead)
        
        return Asset.objects.filter(
            school=school,
            status__in=['active', 'maintenance'],
            next_maintenance_date__lte=cutoff_date
        ).select_related('category', 'location', 'assigned_to')
    
    @staticmethod
    def get_maintenance_costs(school, start_date, end_date) -> Dict:
        """Get maintenance costs for a period"""
        maintenance_records = AssetMaintenance.objects.filter(
            school=school,
            completed_date__range=[start_date, end_date],
            status='completed'
        ).select_related('asset', 'asset__category')
        
        total_cost = maintenance_records.aggregate(total=Sum('cost'))['total'] or Decimal('0.00')
        
        # Group by maintenance type
        by_type = maintenance_records.values('maintenance_type').annotate(
            total_cost=Sum('cost'),
            count=Count('id')
        ).order_by('-total_cost')
        
        # Group by asset category
        by_category = maintenance_records.values(
            'asset__category__name'
        ).annotate(
            total_cost=Sum('cost'),
            count=Count('id')
        ).order_by('-total_cost')
        
        return {
            'total_cost': total_cost,
            'by_type': list(by_type),
            'by_category': list(by_category),
            'record_count': maintenance_records.count()
        }


class AssetAuditService:
    """Service for asset audit management"""
    
    @staticmethod
    def create_audit(audit_data: dict, school, user=None) -> AssetAudit:
        """Create a new asset audit"""
        audit_data['school'] = school
        audit_data['created_by'] = user
        
        audit = AssetAudit.objects.create(**audit_data)
        
        # Create audit items for assets in scope
        assets_query = Asset.objects.filter(school=school, status__in=['active', 'maintenance'])
        
        if audit.location:
            assets_query = assets_query.filter(location=audit.location)
        if audit.category:
            assets_query = assets_query.filter(category=audit.category)
        
        assets = assets_query.select_related('location')
        audit_items = []
        
        for asset in assets:
            audit_items.append(AssetAuditItem(
                school=school,
                audit=audit,
                asset=asset,
                expected_location=asset.location,
                expected_condition=asset.condition,
                actual_location=asset.location,  # Initially same as expected
                actual_condition=asset.condition,  # Initially same as expected
                status='found',  # Default status
                created_by=user
            ))
        
        AssetAuditItem.objects.bulk_create(audit_items)
        
        # Update audit totals
        audit.total_assets_expected = len(audit_items)
        audit.save(update_fields=['total_assets_expected'])
        
        return audit
    
    @staticmethod
    def scan_asset(audit: AssetAudit, asset_tag: str, location: Location = None, 
                  condition: str = None, notes: str = "") -> Tuple[bool, str, AssetAuditItem]:
        """Scan an asset during audit"""
        try:
            asset = Asset.objects.get(school=audit.school, asset_tag=asset_tag)
        except Asset.DoesNotExist:
            return False, f"Asset {asset_tag} not found", None
        
        try:
            audit_item = AssetAuditItem.objects.get(audit=audit, asset=asset)
        except AssetAuditItem.DoesNotExist:
            return False, f"Asset {asset_tag} not in this audit scope", None
        
        # Update audit item with scan results
        audit_item.actual_location = location or audit_item.expected_location
        audit_item.actual_condition = condition or audit_item.expected_condition
        audit_item.scanned_at = timezone.now()
        audit_item.notes = notes
        
        # Determine status based on findings
        if audit_item.actual_location != audit_item.expected_location:
            audit_item.status = 'misplaced'
        elif audit_item.actual_condition != audit_item.expected_condition:
            if audit_item.actual_condition in ['damaged', 'poor']:
                audit_item.status = 'damaged'
            else:
                audit_item.status = 'found'
        else:
            audit_item.status = 'found'
        
        audit_item.save()
        
        return True, "Asset scanned successfully", audit_item
    
    @staticmethod
    def complete_audit(audit: AssetAudit) -> Dict:
        """Complete an audit and generate summary"""
        with transaction.atomic():
            audit_items = audit.audit_items.all()
            
            # Count findings
            total_found = audit_items.filter(status='found').count()
            total_missing = audit_items.filter(status='missing').count()
            total_damaged = audit_items.filter(status='damaged').count()
            total_misplaced = audit_items.filter(status='misplaced').count()
            
            discrepancies = total_missing + total_damaged + total_misplaced
            
            # Update audit summary
            audit.total_assets_found = total_found
            audit.discrepancies_found = discrepancies
            audit.status = 'completed'
            audit.save()
            
            # Update asset information based on audit findings
            for item in audit_items:
                if item.has_discrepancy:
                    asset = item.asset
                    
                    if item.status == 'missing':
                        asset.status = 'lost'
                    elif item.status == 'damaged':
                        asset.condition = item.actual_condition
                        asset.status = 'damaged'
                    elif item.status == 'misplaced':
                        asset.location = item.actual_location
                    
                    asset.save()
            
            summary = {
                'total_expected': audit.total_assets_expected,
                'total_found': total_found,
                'total_missing': total_missing,
                'total_damaged': total_damaged,
                'total_misplaced': total_misplaced,
                'discrepancies': discrepancies,
                'accuracy_rate': (total_found / audit.total_assets_expected * 100) if audit.total_assets_expected > 0 else 0
            }
            
            return summary


class AssetAnalyticsService:
    """Service for asset analytics and reporting"""
    
    @staticmethod
    def calculate_asset_utilization(school, start_date, end_date) -> Dict:
        """Calculate asset utilization metrics"""
        total_assets = Asset.objects.filter(school=school, status='active').count()
        assigned_assets = Asset.objects.filter(
            school=school, 
            status='active', 
            assigned_to__isnull=False
        ).count()
        
        utilization_rate = (assigned_assets / total_assets * 100) if total_assets > 0 else 0
        
        # Utilization by category
        by_category = Asset.objects.filter(school=school, status='active').values(
            'category__name'
        ).annotate(
            total=Count('id'),
            assigned=Count(Case(When(assigned_to__isnull=False, then=1))),
            utilization=Case(
                When(total=0, then=0),
                default=F('assigned') * 100.0 / F('total'),
                output_field=DecimalField(max_digits=5, decimal_places=2)
            )
        ).order_by('-utilization')
        
        return {
            'total_assets': total_assets,
            'assigned_assets': assigned_assets,
            'utilization_rate': round(utilization_rate, 2),
            'by_category': list(by_category)
        }
    
    @staticmethod
    def calculate_depreciation_analysis(school) -> Dict:
        """Calculate depreciation analysis"""
        assets = Asset.objects.filter(school=school, status__in=['active', 'maintenance'])
        
        total_purchase_value = assets.aggregate(total=Sum('purchase_price'))['total'] or Decimal('0.00')
        total_current_value = sum(asset.book_value for asset in assets)
        total_depreciation = total_purchase_value - total_current_value
        
        # Depreciation by category
        by_category = {}
        for asset in assets:
            category = asset.category.name
            if category not in by_category:
                by_category[category] = {
                    'purchase_value': Decimal('0.00'),
                    'current_value': Decimal('0.00'),
                    'depreciation': Decimal('0.00'),
                    'count': 0
                }
            
            by_category[category]['purchase_value'] += asset.purchase_price
            by_category[category]['current_value'] += asset.book_value
            by_category[category]['depreciation'] += asset.accumulated_depreciation
            by_category[category]['count'] += 1
        
        return {
            'total_purchase_value': float(total_purchase_value),
            'total_current_value': float(total_current_value),
            'total_depreciation': float(total_depreciation),
            'depreciation_rate': float((total_depreciation / total_purchase_value * 100)) if total_purchase_value > 0 else 0,
            'by_category': {k: {
                'purchase_value': float(v['purchase_value']),
                'current_value': float(v['current_value']),
                'depreciation': float(v['depreciation']),
                'count': v['count']
            } for k, v in by_category.items()}
        }
    
    @staticmethod
    def get_asset_distribution(school) -> Dict:
        """Get asset distribution by various dimensions"""
        # By category
        by_category = Asset.objects.filter(school=school, status='active').values(
            'category__name'
        ).annotate(count=Count('id')).order_by('-count')
        
        # By location
        by_location = Asset.objects.filter(school=school, status='active').values(
            'location__name'
        ).annotate(count=Count('id')).order_by('-count')
        
        # By condition
        by_condition = Asset.objects.filter(school=school, status='active').values(
            'condition'
        ).annotate(count=Count('id')).order_by('-count')
        
        # By age groups
        current_date = timezone.now().date()
        age_groups = {
            'new': 0,      # 0-1 years
            'recent': 0,   # 1-3 years
            'mature': 0,   # 3-5 years
            'old': 0       # 5+ years
        }
        
        for asset in Asset.objects.filter(school=school, status='active'):
            age = asset.age_in_years
            if age <= 1:
                age_groups['new'] += 1
            elif age <= 3:
                age_groups['recent'] += 1
            elif age <= 5:
                age_groups['mature'] += 1
            else:
                age_groups['old'] += 1
        
        return {
            'by_category': list(by_category),
            'by_location': list(by_location),
            'by_condition': list(by_condition),
            'by_age': age_groups
        }
    
    @staticmethod
    def generate_asset_report(school, report_type: str, filters: dict = None) -> Dict:
        """Generate comprehensive asset reports"""
        if report_type == 'summary':
            return AssetAnalyticsService._generate_summary_report(school, filters)
        elif report_type == 'depreciation':
            return AssetAnalyticsService._generate_depreciation_report(school, filters)
        elif report_type == 'maintenance':
            return AssetAnalyticsService._generate_maintenance_report(school, filters)
        elif report_type == 'utilization':
            return AssetAnalyticsService._generate_utilization_report(school, filters)
        else:
            raise ValueError(f"Unknown report type: {report_type}")
    
    @staticmethod
    def _generate_summary_report(school, filters: dict = None) -> Dict:
        """Generate asset summary report"""
        queryset = Asset.objects.filter(school=school)
        
        if filters:
            if filters.get('category'):
                queryset = queryset.filter(category_id=filters['category'])
            if filters.get('location'):
                queryset = queryset.filter(location_id=filters['location'])
            if filters.get('status'):
                queryset = queryset.filter(status=filters['status'])
        
        total_assets = queryset.count()
        total_value = queryset.aggregate(total=Sum('purchase_price'))['total'] or Decimal('0.00')
        current_value = sum(asset.book_value for asset in queryset)
        
        status_breakdown = queryset.values('status').annotate(count=Count('id'))
        condition_breakdown = queryset.values('condition').annotate(count=Count('id'))
        
        return {
            'total_assets': total_assets,
            'total_purchase_value': float(total_value),
            'total_current_value': float(current_value),
            'status_breakdown': list(status_breakdown),
            'condition_breakdown': list(condition_breakdown),
            'generated_at': timezone.now().isoformat()
        }
    
    @staticmethod
    def _generate_depreciation_report(school, filters: dict = None) -> Dict:
        """Generate depreciation report"""
        return AssetAnalyticsService.calculate_depreciation_analysis(school)
    
    @staticmethod
    def _generate_maintenance_report(school, filters: dict = None) -> Dict:
        """Generate maintenance report"""
        start_date = filters.get('start_date', timezone.now().date().replace(day=1))
        end_date = filters.get('end_date', timezone.now().date())
        
        return AssetMaintenanceService.get_maintenance_costs(school, start_date, end_date)
    
    @staticmethod
    def _generate_utilization_report(school, filters: dict = None) -> Dict:
        """Generate utilization report"""
        start_date = filters.get('start_date', timezone.now().date().replace(day=1))
        end_date = filters.get('end_date', timezone.now().date())
        
        return AssetAnalyticsService.calculate_asset_utilization(school, start_date, end_date)


class InventoryManagementService:
    """Service for comprehensive inventory management"""
    
    @staticmethod
    def create_inventory_item(item_data: dict, school) -> InventoryItem:
        """Create a new inventory item with validation"""
        with transaction.atomic():
            item_data['school'] = school
            
            # Generate item code if not provided
            if not item_data.get('item_code'):
                item_data['item_code'] = InventoryManagementService.generate_item_code(school, item_data.get('category'))
            
            item = InventoryItem.objects.create(**item_data)
            
            # Create initial stock transaction if initial stock is provided
            if item.current_stock > 0:
                StockTransaction.objects.create(
                    school=school,
                    item=item,
                    transaction_type='receipt',
                    quantity=item.current_stock,
                    unit_cost=item.unit_cost,
                    to_location=item.primary_location,
                    reason='Initial stock',
                    created_by=item.created_by
                )
            
            return item
    
    @staticmethod
    def generate_item_code(school, category=None) -> str:
        """Generate unique item code"""
        prefix = "INV"
        if category:
            prefix = category.code[:3].upper()
        
        year = timezone.now().year
        
        # Get the next sequence number
        last_item = InventoryItem.objects.filter(
            school=school,
            item_code__startswith=f"{prefix}{year}"
        ).order_by('-item_code').first()
        
        if last_item:
            try:
                last_number = int(last_item.item_code[-4:])
                next_number = last_number + 1
            except ValueError:
                next_number = 1
        else:
            next_number = 1
        
        return f"{prefix}{year}{next_number:04d}"
    
    @staticmethod
    def update_stock_levels(item: InventoryItem, new_quantity: Decimal, 
                           location: Location = None, reason: str = "", 
                           user=None) -> StockTransaction:
        """Update stock levels with transaction tracking"""
        with transaction.atomic():
            current_stock = item.current_stock
            quantity_change = new_quantity - current_stock
            
            transaction_type = 'adjustment'
            if quantity_change > 0:
                transaction_type = 'receipt'
            elif quantity_change < 0:
                transaction_type = 'issue'
                quantity_change = abs(quantity_change)
            
            if quantity_change != 0:
                transaction = StockTransaction.objects.create(
                    school=item.school,
                    item=item,
                    transaction_type=transaction_type,
                    quantity=quantity_change,
                    to_location=location or item.primary_location,
                    reason=reason or f"Stock level adjustment",
                    created_by=user
                )
                
                return transaction
            
            return None
    
    @staticmethod
    def transfer_stock(item: InventoryItem, from_location: Location, 
                      to_location: Location, quantity: Decimal, 
                      reason: str = "", user=None) -> StockTransaction:
        """Transfer stock between locations"""
        with transaction.atomic():
            # Check if sufficient stock exists at from_location
            from_stock = InventoryLocation.objects.filter(
                item=item, location=from_location
            ).first()
            
            if not from_stock or from_stock.quantity < quantity:
                raise ValueError(f"Insufficient stock at {from_location.name}")
            
            # Create transfer transaction
            transaction = StockTransaction.objects.create(
                school=item.school,
                item=item,
                transaction_type='transfer',
                quantity=quantity,
                from_location=from_location,
                to_location=to_location,
                reason=reason or f"Transfer from {from_location.name} to {to_location.name}",
                created_by=user
            )
            
            # Update location stocks
            from_stock.quantity -= quantity
            from_stock.save()
            
            to_stock, created = InventoryLocation.objects.get_or_create(
                item=item, location=to_location,
                defaults={'quantity': Decimal('0.00')}
            )
            to_stock.quantity += quantity
            to_stock.save()
            
            return transaction
    
    @staticmethod
    def issue_stock(item: InventoryItem, quantity: Decimal, 
                   location: Location = None, issued_to: str = "", 
                   reason: str = "", user=None) -> StockTransaction:
        """Issue stock from inventory"""
        with transaction.atomic():
            if item.current_stock < quantity:
                raise ValueError(f"Insufficient stock. Available: {item.current_stock}, Requested: {quantity}")
            
            transaction = StockTransaction.objects.create(
                school=item.school,
                item=item,
                transaction_type='issue',
                quantity=quantity,
                from_location=location or item.primary_location,
                reason=reason or f"Stock issued to {issued_to}",
                notes=f"Issued to: {issued_to}" if issued_to else "",
                created_by=user
            )
            
            return transaction
    
    @staticmethod
    def receive_stock(item: InventoryItem, quantity: Decimal, 
                     unit_cost: Decimal = None, location: Location = None,
                     supplier=None, reference_id: str = "", 
                     reason: str = "", user=None) -> StockTransaction:
        """Receive stock into inventory"""
        with transaction.atomic():
            transaction = StockTransaction.objects.create(
                school=item.school,
                item=item,
                transaction_type='receipt',
                quantity=quantity,
                unit_cost=unit_cost or item.unit_cost,
                to_location=location or item.primary_location,
                reference_id=reference_id,
                reason=reason or "Stock receipt",
                created_by=user
            )
            
            return transaction
    
    @staticmethod
    def search_inventory_items(school, query: str = "", filters: dict = None) -> List[InventoryItem]:
        """Search inventory items with filters"""
        queryset = InventoryItem.objects.filter(school=school).select_related(
            'category', 'primary_location', 'primary_supplier'
        )
        
        if query:
            queryset = queryset.filter(
                Q(item_code__icontains=query) |
                Q(name__icontains=query) |
                Q(arabic_name__icontains=query) |
                Q(description__icontains=query) |
                Q(brand__icontains=query) |
                Q(model__icontains=query) |
                Q(barcode__icontains=query)
            )
        
        if filters:
            if filters.get('category'):
                queryset = queryset.filter(category_id=filters['category'])
            if filters.get('location'):
                queryset = queryset.filter(primary_location_id=filters['location'])
            if filters.get('status'):
                queryset = queryset.filter(status=filters['status'])
            if filters.get('low_stock'):
                queryset = queryset.filter(current_stock__lte=F('minimum_stock'))
            if filters.get('out_of_stock'):
                queryset = queryset.filter(current_stock__lte=0)
        
        return queryset.order_by('item_code')
    
    @staticmethod
    def get_stock_history(item: InventoryItem, days: int = 30) -> List[Dict]:
        """Get stock transaction history for an item"""
        start_date = timezone.now().date() - timedelta(days=days)
        
        transactions = item.stock_transactions.filter(
            transaction_date__date__gte=start_date
        ).select_related(
            'from_location', 'to_location', 'requested_by', 'approved_by', 'created_by'
        ).order_by('-transaction_date')
        
        history = []
        running_stock = item.current_stock
        
        # Calculate running stock backwards
        for transaction in reversed(transactions):
            if transaction.transaction_type in ['receipt', 'return']:
                running_stock -= transaction.quantity
            elif transaction.transaction_type in ['issue', 'damage', 'loss']:
                running_stock += transaction.quantity
            elif transaction.transaction_type == 'adjustment':
                running_stock -= transaction.quantity
            
            history.append({
                'date': transaction.transaction_date,
                'type': transaction.get_transaction_type_display(),
                'quantity': float(transaction.quantity),
                'running_stock': float(running_stock),
                'from_location': str(transaction.from_location) if transaction.from_location else None,
                'to_location': str(transaction.to_location) if transaction.to_location else None,
                'reason': transaction.reason,
                'reference_id': transaction.reference_id,
                'user': str(transaction.created_by) if transaction.created_by else None
            })
        
        return list(reversed(history))


class StockAlertService:
    """Service for managing stock alerts and notifications"""
    
    @staticmethod
    def check_stock_levels(school) -> List[StockAlert]:
        """Check all items for stock level alerts"""
        alerts_created = []
        
        # Get all active inventory items
        items = InventoryItem.objects.filter(school=school, status='active')
        
        for item in items:
            # Check for low stock
            if item.is_low_stock and not StockAlert.objects.filter(
                item=item, alert_type='low_stock', status='active'
            ).exists():
                alert = StockAlert.objects.create(
                    school=school,
                    item=item,
                    alert_type='low_stock',
                    message=f"Stock level for {item.name} is below minimum threshold",
                    current_stock=item.current_stock,
                    threshold_value=item.minimum_stock
                )
                alerts_created.append(alert)
            
            # Check for out of stock
            if item.is_out_of_stock and not StockAlert.objects.filter(
                item=item, alert_type='out_of_stock', status='active'
            ).exists():
                alert = StockAlert.objects.create(
                    school=school,
                    item=item,
                    alert_type='out_of_stock',
                    message=f"{item.name} is out of stock",
                    current_stock=item.current_stock,
                    threshold_value=Decimal('0.00')
                )
                alerts_created.append(alert)
            
            # Check for overstock
            if item.maximum_stock > 0 and item.current_stock > item.maximum_stock:
                if not StockAlert.objects.filter(
                    item=item, alert_type='overstock', status='active'
                ).exists():
                    alert = StockAlert.objects.create(
                        school=school,
                        item=item,
                        alert_type='overstock',
                        message=f"Stock level for {item.name} exceeds maximum threshold",
                        current_stock=item.current_stock,
                        threshold_value=item.maximum_stock
                    )
                    alerts_created.append(alert)
        
        return alerts_created
    
    @staticmethod
    def get_reorder_recommendations(school) -> List[Dict]:
        """Get items that need reordering"""
        items_to_reorder = InventoryItem.objects.filter(
            school=school,
            status='active',
            current_stock__lte=F('minimum_stock')
        ).select_related('category', 'primary_supplier')
        
        recommendations = []
        
        for item in items_to_reorder:
            recommended_quantity = item.reorder_quantity
            if recommended_quantity <= 0:
                # Calculate recommended quantity based on usage patterns
                recommended_quantity = max(
                    item.minimum_stock * 2,  # At least double the minimum
                    item.maximum_stock - item.current_stock  # Or fill to maximum
                )
            
            recommendations.append({
                'item': item,
                'current_stock': float(item.current_stock),
                'minimum_stock': float(item.minimum_stock),
                'recommended_quantity': float(recommended_quantity),
                'estimated_cost': float(recommended_quantity * item.unit_cost),
                'supplier': item.primary_supplier,
                'days_since_last_order': StockAlertService._days_since_last_order(item)
            })
        
        return recommendations
    
    @staticmethod
    def _days_since_last_order(item: InventoryItem) -> Optional[int]:
        """Calculate days since last purchase order for item"""
        last_receipt = item.stock_transactions.filter(
            transaction_type='receipt'
        ).order_by('-transaction_date').first()
        
        if last_receipt:
            return (timezone.now().date() - last_receipt.transaction_date.date()).days
        
        return None
    
    @staticmethod
    def resolve_alerts_for_item(item: InventoryItem, user=None):
        """Resolve active alerts for an item after stock update"""
        active_alerts = StockAlert.objects.filter(
            item=item, status='active'
        )
        
        for alert in active_alerts:
            if alert.alert_type == 'low_stock' and not item.is_low_stock:
                alert.resolve(f"Stock level restored above minimum threshold", user)
            elif alert.alert_type == 'out_of_stock' and not item.is_out_of_stock:
                alert.resolve(f"Stock replenished", user)
            elif alert.alert_type == 'overstock' and item.current_stock <= item.maximum_stock:
                alert.resolve(f"Stock level reduced below maximum threshold", user)


class PurchaseOrderService:
    """Service for managing purchase orders"""
    
    @staticmethod
    def create_purchase_order(po_data: dict, line_items: List[Dict], school, user=None) -> PurchaseOrder:
        """Create a new purchase order with line items"""
        with transaction.atomic():
            po_data['school'] = school
            po_data['requested_by'] = user
            
            po = PurchaseOrder.objects.create(**po_data)
            
            # Create line items
            for item_data in line_items:
                PurchaseOrderItem.objects.create(
                    school=school,
                    purchase_order=po,
                    item_id=item_data['item_id'],
                    quantity_ordered=item_data['quantity'],
                    unit_price=item_data['unit_price'],
                    notes=item_data.get('notes', ''),
                    created_by=user
                )
            
            # Calculate totals
            po.calculate_totals()
            
            return po
    
    @staticmethod
    def create_po_from_reorder_recommendations(recommendations: List[Dict], 
                                             supplier: Supplier, school, user=None) -> PurchaseOrder:
        """Create purchase order from reorder recommendations"""
        # Filter recommendations for the specified supplier
        supplier_items = [
            rec for rec in recommendations 
            if rec['supplier'] and rec['supplier'].id == supplier.id
        ]
        
        if not supplier_items:
            raise ValueError(f"No items found for supplier {supplier.name}")
        
        po_data = {
            'supplier': supplier,
            'order_date': timezone.now().date(),
            'notes': f"Auto-generated from reorder recommendations"
        }
        
        line_items = []
        for rec in supplier_items:
            line_items.append({
                'item_id': rec['item'].id,
                'quantity': rec['recommended_quantity'],
                'unit_price': rec['item'].unit_cost,
                'notes': f"Reorder - Current stock: {rec['current_stock']}"
            })
        
        return PurchaseOrderService.create_purchase_order(po_data, line_items, school, user)
    
    @staticmethod
    def receive_purchase_order(po: PurchaseOrder, received_items: List[Dict], 
                             user=None) -> List[StockTransaction]:
        """Receive items from a purchase order"""
        transactions = []
        
        with transaction.atomic():
            for item_data in received_items:
                po_item = PurchaseOrderItem.objects.get(
                    purchase_order=po,
                    item_id=item_data['item_id']
                )
                
                quantity_received = Decimal(str(item_data['quantity_received']))
                
                # Update PO item
                po_item.quantity_received += quantity_received
                po_item.save()
                
                # Create stock transaction
                transaction = StockTransaction.objects.create(
                    school=po.school,
                    item=po_item.item,
                    transaction_type='receipt',
                    quantity=quantity_received,
                    unit_cost=po_item.unit_price,
                    to_location=po_item.item.primary_location,
                    reference_type='purchase_order',
                    reference_id=po.po_number,
                    reference_date=po.order_date,
                    reason=f"Received from PO {po.po_number}",
                    created_by=user
                )
                transactions.append(transaction)
            
            # Update PO status
            all_items_received = all(
                item.is_fully_received for item in po.line_items.all()
            )
            
            if all_items_received:
                po.status = 'completed'
            else:
                po.status = 'partial'
            
            po.save()
        
        return transactions
    
    @staticmethod
    def get_pending_orders(school) -> List[PurchaseOrder]:
        """Get purchase orders pending delivery"""
        return PurchaseOrder.objects.filter(
            school=school,
            status__in=['approved', 'sent', 'partial']
        ).select_related('supplier').prefetch_related('line_items__item')


class InventoryCountService:
    """Service for managing physical inventory counts"""
    
    @staticmethod
    def create_inventory_count(count_data: dict, school, user=None) -> InventoryCount:
        """Create a new inventory count session"""
        with transaction.atomic():
            count_data['school'] = school
            count_data['created_by'] = user
            
            count = InventoryCount.objects.create(**count_data)
            
            # Create count items for items in scope
            items_query = InventoryItem.objects.filter(school=school, status='active')
            
            if count.location:
                items_query = items_query.filter(primary_location=count.location)
            if count.category:
                items_query = items_query.filter(category=count.category)
            
            count_items = []
            for item in items_query:
                count_items.append(InventoryCountItem(
                    school=school,
                    count=count,
                    item=item,
                    expected_quantity=item.current_stock,
                    created_by=user
                ))
            
            InventoryCountItem.objects.bulk_create(count_items)
            
            # Update count totals
            count.total_items_expected = len(count_items)
            count.save(update_fields=['total_items_expected'])
            
            return count
    
    @staticmethod
    def record_count(count: InventoryCount, item_code: str, 
                    counted_quantity: Decimal, notes: str = "", 
                    user=None) -> Tuple[bool, str, InventoryCountItem]:
        """Record count for a specific item"""
        try:
            item = InventoryItem.objects.get(school=count.school, item_code=item_code)
        except InventoryItem.DoesNotExist:
            return False, f"Item {item_code} not found", None
        
        try:
            count_item = InventoryCountItem.objects.get(count=count, item=item)
        except InventoryCountItem.DoesNotExist:
            return False, f"Item {item_code} not in this count scope", None
        
        # Update count item
        count_item.counted_quantity = counted_quantity
        count_item.notes = notes
        count_item.counted_at = timezone.now()
        count_item.save()
        
        return True, "Count recorded successfully", count_item
    
    @staticmethod
    def complete_inventory_count(count: InventoryCount, 
                               apply_adjustments: bool = True, 
                               user=None) -> Dict:
        """Complete inventory count and optionally apply adjustments"""
        with transaction.atomic():
            count_items = count.count_items.all()
            
            # Calculate statistics
            total_counted = count_items.filter(counted_at__isnull=False).count()
            discrepancies = count_items.filter(variance__ne=0).count()
            
            # Update count summary
            count.total_items_counted = total_counted
            count.discrepancies_found = discrepancies
            count.status = 'completed'
            count.save()
            
            adjustments_made = []
            
            if apply_adjustments:
                # Apply stock adjustments for discrepancies
                for count_item in count_items.filter(variance__ne=0):
                    if count_item.counted_at:  # Only adjust items that were actually counted
                        transaction = StockTransaction.objects.create(
                            school=count.school,
                            item=count_item.item,
                            transaction_type='count',
                            quantity=count_item.counted_quantity,
                            reason=f"Physical count adjustment - {count.count_name}",
                            notes=f"Expected: {count_item.expected_quantity}, Counted: {count_item.counted_quantity}, Variance: {count_item.variance}",
                            created_by=user
                        )
                        adjustments_made.append(transaction)
            
            summary = {
                'total_expected': count.total_items_expected,
                'total_counted': total_counted,
                'discrepancies': discrepancies,
                'accuracy_rate': (total_counted / count.total_items_expected * 100) if count.total_items_expected > 0 else 0,
                'adjustments_applied': len(adjustments_made),
                'total_variance_value': float(sum(
                    item.variance * item.item.unit_cost 
                    for item in count_items.filter(variance__ne=0)
                ))
            }
            
            return summary


class InventoryAnalyticsService:
    """Service for inventory analytics and reporting"""
    
    @staticmethod
    def calculate_inventory_turnover(school, start_date, end_date) -> Dict:
        """Calculate inventory turnover metrics"""
        # Get all active inventory items
        items = InventoryItem.objects.filter(school=school, status='active')
        
        total_value = sum(item.stock_value for item in items)
        
        # Calculate cost of goods issued during period
        issued_transactions = StockTransaction.objects.filter(
            school=school,
            transaction_type='issue',
            transaction_date__date__range=[start_date, end_date]
        ).select_related('item')
        
        cogs = sum(
            transaction.quantity * transaction.item.unit_cost 
            for transaction in issued_transactions
        )
        
        # Calculate turnover ratio
        days_in_period = (end_date - start_date).days
        turnover_ratio = (cogs / total_value) if total_value > 0 else 0
        annual_turnover = turnover_ratio * (365 / days_in_period) if days_in_period > 0 else 0
        
        # Turnover by category
        by_category = {}
        for item in items:
            category = item.category.name
            if category not in by_category:
                by_category[category] = {
                    'total_value': Decimal('0.00'),
                    'cogs': Decimal('0.00'),
                    'turnover_ratio': 0
                }
            
            by_category[category]['total_value'] += item.stock_value
            
            # Calculate COGS for this category
            category_cogs = sum(
                transaction.quantity * transaction.item.unit_cost
                for transaction in issued_transactions
                if transaction.item.category == item.category
            )
            by_category[category]['cogs'] += category_cogs
        
        # Calculate turnover ratios by category
        for category_data in by_category.values():
            if category_data['total_value'] > 0:
                category_data['turnover_ratio'] = float(
                    category_data['cogs'] / category_data['total_value']
                )
        
        return {
            'total_inventory_value': float(total_value),
            'cost_of_goods_issued': float(cogs),
            'turnover_ratio': round(turnover_ratio, 2),
            'annual_turnover_ratio': round(annual_turnover, 2),
            'by_category': {k: {
                'total_value': float(v['total_value']),
                'cogs': float(v['cogs']),
                'turnover_ratio': round(v['turnover_ratio'], 2)
            } for k, v in by_category.items()}
        }
    
    @staticmethod
    def get_stock_valuation(school) -> Dict:
        """Get current stock valuation"""
        items = InventoryItem.objects.filter(school=school, status='active').select_related('category')
        
        total_value = Decimal('0.00')
        by_category = {}
        by_location = {}
        
        for item in items:
            item_value = item.stock_value
            total_value += item_value
            
            # By category
            category = item.category.name
            if category not in by_category:
                by_category[category] = {
                    'value': Decimal('0.00'),
                    'items': 0,
                    'quantity': Decimal('0.00')
                }
            by_category[category]['value'] += item_value
            by_category[category]['items'] += 1
            by_category[category]['quantity'] += item.current_stock
            
            # By location
            if item.primary_location:
                location = item.primary_location.name
                if location not in by_location:
                    by_location[location] = {
                        'value': Decimal('0.00'),
                        'items': 0,
                        'quantity': Decimal('0.00')
                    }
                by_location[location]['value'] += item_value
                by_location[location]['items'] += 1
                by_location[location]['quantity'] += item.current_stock
        
        return {
            'total_value': float(total_value),
            'total_items': items.count(),
            'by_category': {k: {
                'value': float(v['value']),
                'items': v['items'],
                'quantity': float(v['quantity'])
            } for k, v in by_category.items()},
            'by_location': {k: {
                'value': float(v['value']),
                'items': v['items'],
                'quantity': float(v['quantity'])
            } for k, v in by_location.items()}
        }
    
    @staticmethod
    def get_slow_moving_items(school, days_threshold: int = 90) -> List[Dict]:
        """Get items with no movement in specified days"""
        cutoff_date = timezone.now().date() - timedelta(days=days_threshold)
        
        # Get items with no recent transactions
        items_with_recent_activity = StockTransaction.objects.filter(
            school=school,
            transaction_date__date__gte=cutoff_date
        ).values_list('item_id', flat=True).distinct()
        
        slow_moving_items = InventoryItem.objects.filter(
            school=school,
            status='active',
            current_stock__gt=0
        ).exclude(id__in=items_with_recent_activity).select_related('category')
        
        result = []
        for item in slow_moving_items:
            last_transaction = item.stock_transactions.order_by('-transaction_date').first()
            
            result.append({
                'item': item,
                'current_stock': float(item.current_stock),
                'stock_value': float(item.stock_value),
                'days_since_last_movement': (
                    timezone.now().date() - last_transaction.transaction_date.date()
                ).days if last_transaction else None,
                'last_transaction_type': last_transaction.get_transaction_type_display() if last_transaction else None
            })
        
        return sorted(result, key=lambda x: x['days_since_last_movement'] or 0, reverse=True)
    
    @staticmethod
    def generate_inventory_report(school, report_type: str, filters: dict = None) -> Dict:
        """Generate comprehensive inventory reports"""
        if report_type == 'stock_summary':
            return InventoryAnalyticsService._generate_stock_summary_report(school, filters)
        elif report_type == 'valuation':
            return InventoryAnalyticsService.get_stock_valuation(school)
        elif report_type == 'turnover':
            start_date = filters.get('start_date', timezone.now().date().replace(day=1))
            end_date = filters.get('end_date', timezone.now().date())
            return InventoryAnalyticsService.calculate_inventory_turnover(school, start_date, end_date)
        elif report_type == 'slow_moving':
            days_threshold = filters.get('days_threshold', 90)
            return {'slow_moving_items': InventoryAnalyticsService.get_slow_moving_items(school, days_threshold)}
        else:
            raise ValueError(f"Unknown report type: {report_type}")
    
    @staticmethod
    def _generate_stock_summary_report(school, filters: dict = None) -> Dict:
        """Generate stock summary report"""
        queryset = InventoryItem.objects.filter(school=school)
        
        if filters:
            if filters.get('category'):
                queryset = queryset.filter(category_id=filters['category'])
            if filters.get('location'):
                queryset = queryset.filter(primary_location_id=filters['location'])
            if filters.get('status'):
                queryset = queryset.filter(status=filters['status'])
        
        total_items = queryset.count()
        active_items = queryset.filter(status='active').count()
        low_stock_items = queryset.filter(current_stock__lte=F('minimum_stock')).count()
        out_of_stock_items = queryset.filter(current_stock__lte=0).count()
        
        total_value = sum(item.stock_value for item in queryset.filter(status='active'))
        
        status_breakdown = queryset.values('status').annotate(count=Count('id'))
        
        return {
            'total_items': total_items,
            'active_items': active_items,
            'low_stock_items': low_stock_items,
            'out_of_stock_items': out_of_stock_items,
            'total_stock_value': float(total_value),
            'status_breakdown': list(status_breakdown),
            'generated_at': timezone.now().isoformat()
        }