import pytest
from django.test import TestCase
from django.utils import timezone
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import date, timedelta

from core.models import School
from .models import (
    AssetCategory, Location, Supplier, Asset, AssetMovement, 
    AssetMaintenance, AssetDepreciation, AssetAudit, AssetAuditItem, 
    AssetAnalytics
)
from .services import (
    AssetTrackingService, AssetDepreciationService, AssetMaintenanceService,
    AssetAuditService, AssetAnalyticsService
)


class AssetModelTests(TestCase):
    """Test cases for Asset models"""
    
    def setUp(self):
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="Test Address",
            phone="1234567890",
            email="<EMAIL>"
        )
        
        self.category = AssetCategory.objects.create(
            school=self.school,
            name="Computers",
            code="COMP",
            depreciation_rate=20.00,
            useful_life_years=5
        )
        
        self.location = Location.objects.create(
            school=self.school,
            name="IT Lab",
            code="ITL",
            building="Main Building",
            floor="2nd Floor",
            room="Room 201"
        )
        
        self.supplier = Supplier.objects.create(
            school=self.school,
            name="Tech Supplier",
            code="TECH",
            contact_person="John Doe",
            phone="1234567890",
            email="<EMAIL>",
            rating=5
        )
    
    def test_asset_creation(self):
        """Test asset creation with all required fields"""
        asset = Asset.objects.create(
            school=self.school,
            name="Dell Laptop",
            category=self.category,
            purchase_price=Decimal('1000.00'),
            purchase_date=date.today(),
            location=self.location,
            supplier=self.supplier
        )
        
        self.assertIsNotNone(asset.asset_tag)
        self.assertIsNotNone(asset.barcode)
        self.assertIsNotNone(asset.qr_code)
        self.assertEqual(asset.current_value, asset.purchase_price)
        self.assertEqual(asset.useful_life_years, self.category.useful_life_years)
        self.assertEqual(asset.depreciation_rate, self.category.depreciation_rate)
    
    def test_asset_tag_generation(self):
        """Test automatic asset tag generation"""
        asset1 = Asset.objects.create(
            school=self.school,
            name="Asset 1",
            category=self.category,
            purchase_price=Decimal('500.00'),
            purchase_date=date.today()
        )
        
        asset2 = Asset.objects.create(
            school=self.school,
            name="Asset 2",
            category=self.category,
            purchase_price=Decimal('600.00'),
            purchase_date=date.today()
        )
        
        self.assertNotEqual(asset1.asset_tag, asset2.asset_tag)
        self.assertTrue(asset1.asset_tag.startswith('TES'))
        self.assertTrue(asset2.asset_tag.startswith('TES'))
    
    def test_depreciation_calculation_straight_line(self):
        """Test straight-line depreciation calculation"""
        asset = Asset.objects.create(
            school=self.school,
            name="Test Asset",
            category=self.category,
            purchase_price=Decimal('1000.00'),
            salvage_value=Decimal('100.00'),
            purchase_date=date.today() - timedelta(days=365),  # 1 year ago
            useful_life_years=5,
            depreciation_method='straight_line'
        )
        
        # Expected annual depreciation: (1000 - 100) / 5 = 180
        expected_depreciation = Decimal('180.00')
        actual_depreciation = asset.accumulated_depreciation
        
        # Allow for small rounding differences
        self.assertAlmostEqual(float(actual_depreciation), float(expected_depreciation), places=0)
    
    def test_book_value_calculation(self):
        """Test book value calculation"""
        asset = Asset.objects.create(
            school=self.school,
            name="Test Asset",
            category=self.category,
            purchase_price=Decimal('1000.00'),
            salvage_value=Decimal('100.00'),
            purchase_date=date.today() - timedelta(days=365),  # 1 year ago
            useful_life_years=5,
            depreciation_method='straight_line'
        )
        
        book_value = asset.book_value
        expected_book_value = asset.purchase_price - asset.accumulated_depreciation
        
        self.assertEqual(book_value, expected_book_value)
        self.assertLess(book_value, asset.purchase_price)
    
    def test_warranty_validation(self):
        """Test warranty status validation"""
        # Valid warranty
        asset = Asset.objects.create(
            school=self.school,
            name="Test Asset",
            category=self.category,
            purchase_price=Decimal('1000.00'),
            purchase_date=date.today(),
            warranty_expiry=date.today() + timedelta(days=365)
        )
        
        self.assertTrue(asset.is_warranty_valid)
        
        # Expired warranty
        asset.warranty_expiry = date.today() - timedelta(days=1)
        asset.save()
        
        self.assertFalse(asset.is_warranty_valid)
    
    def test_maintenance_due_check(self):
        """Test maintenance due check"""
        asset = Asset.objects.create(
            school=self.school,
            name="Test Asset",
            category=self.category,
            purchase_price=Decimal('1000.00'),
            purchase_date=date.today(),
            next_maintenance_date=date.today() - timedelta(days=1)
        )
        
        self.assertTrue(asset.maintenance_due)
        
        asset.next_maintenance_date = date.today() + timedelta(days=30)
        asset.save()
        
        self.assertFalse(asset.maintenance_due)


class AssetTrackingServiceTests(TestCase):
    """Test cases for AssetTrackingService"""
    
    def setUp(self):
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="Test Address",
            phone="1234567890",
            email="<EMAIL>"
        )
        
        self.category = AssetCategory.objects.create(
            school=self.school,
            name="Computers",
            code="COMP",
            depreciation_rate=20.00,
            useful_life_years=5
        )
        
        self.location1 = Location.objects.create(
            school=self.school,
            name="IT Lab",
            code="ITL",
            building="Main Building"
        )
        
        self.location2 = Location.objects.create(
            school=self.school,
            name="Library",
            code="LIB",
            building="Main Building"
        )
    
    def test_create_asset(self):
        """Test asset creation through service"""
        asset_data = {
            'name': 'Test Laptop',
            'category': self.category,
            'purchase_price': Decimal('1200.00'),
            'purchase_date': date.today(),
            'location': self.location1
        }
        
        asset = AssetTrackingService.create_asset(asset_data, self.school)
        
        self.assertIsNotNone(asset.asset_tag)
        self.assertEqual(asset.school, self.school)
        self.assertEqual(asset.location, self.location1)
        
        # Check if initial movement was created
        movements = AssetMovement.objects.filter(asset=asset)
        self.assertEqual(movements.count(), 1)
        self.assertEqual(movements.first().movement_type, 'assignment')
    
    def test_transfer_asset(self):
        """Test asset transfer"""
        asset = Asset.objects.create(
            school=self.school,
            name="Test Asset",
            category=self.category,
            purchase_price=Decimal('1000.00'),
            purchase_date=date.today(),
            location=self.location1
        )
        
        movement = AssetTrackingService.transfer_asset(
            asset=asset,
            to_location=self.location2,
            reason="Relocation"
        )
        
        # Refresh asset from database
        asset.refresh_from_db()
        
        self.assertEqual(asset.location, self.location2)
        self.assertEqual(movement.from_location, self.location1)
        self.assertEqual(movement.to_location, self.location2)
        self.assertEqual(movement.movement_type, 'transfer')
    
    def test_retire_asset(self):
        """Test asset retirement"""
        asset = Asset.objects.create(
            school=self.school,
            name="Test Asset",
            category=self.category,
            purchase_price=Decimal('1000.00'),
            purchase_date=date.today(),
            location=self.location1,
            status='active'
        )
        
        movement = AssetTrackingService.retire_asset(
            asset=asset,
            reason="End of useful life"
        )
        
        # Refresh asset from database
        asset.refresh_from_db()
        
        self.assertEqual(asset.status, 'retired')
        self.assertIsNone(asset.location)
        self.assertEqual(movement.movement_type, 'disposal')
    
    def test_search_assets(self):
        """Test asset search functionality"""
        asset1 = Asset.objects.create(
            school=self.school,
            name="Dell Laptop",
            category=self.category,
            purchase_price=Decimal('1000.00'),
            purchase_date=date.today(),
            brand="Dell",
            model="Inspiron"
        )
        
        asset2 = Asset.objects.create(
            school=self.school,
            name="HP Printer",
            category=self.category,
            purchase_price=Decimal('500.00'),
            purchase_date=date.today(),
            brand="HP",
            model="LaserJet"
        )
        
        # Search by name
        results = AssetTrackingService.search_assets(self.school, query="Dell")
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0], asset1)
        
        # Search by brand
        results = AssetTrackingService.search_assets(self.school, query="HP")
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0], asset2)
        
        # Search with filters
        filters = {'category': self.category.id}
        results = AssetTrackingService.search_assets(self.school, filters=filters)
        self.assertEqual(len(results), 2)


class AssetDepreciationServiceTests(TestCase):
    """Test cases for AssetDepreciationService"""
    
    def setUp(self):
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="Test Address",
            phone="1234567890",
            email="<EMAIL>"
        )
        
        self.category = AssetCategory.objects.create(
            school=self.school,
            name="Computers",
            code="COMP",
            depreciation_rate=20.00,
            useful_life_years=5
        )
        
        self.asset = Asset.objects.create(
            school=self.school,
            name="Test Asset",
            category=self.category,
            purchase_price=Decimal('1000.00'),
            salvage_value=Decimal('100.00'),
            purchase_date=date.today() - timedelta(days=365),
            useful_life_years=5,
            depreciation_method='straight_line'
        )
    
    def test_calculate_depreciation_for_period(self):
        """Test depreciation calculation for a period"""
        start_date = date.today() - timedelta(days=365)
        end_date = date.today()
        
        records = AssetDepreciationService.calculate_depreciation_for_period(
            self.school, start_date, end_date
        )
        
        self.assertEqual(len(records), 1)
        record = records[0]
        
        self.assertEqual(record['asset'], self.asset)
        self.assertGreater(record['depreciation_amount'], 0)
        self.assertEqual(record['method_used'], 'straight_line')
    
    def test_create_depreciation_entries(self):
        """Test creation of depreciation entries"""
        start_date = date.today() - timedelta(days=365)
        end_date = date.today()
        
        entries = AssetDepreciationService.create_depreciation_entries(
            self.school, start_date, end_date
        )
        
        self.assertEqual(len(entries), 1)
        entry = entries[0]
        
        self.assertEqual(entry.asset, self.asset)
        self.assertEqual(entry.period_start, start_date)
        self.assertEqual(entry.period_end, end_date)
        self.assertGreater(entry.depreciation_amount, 0)


class AssetMaintenanceServiceTests(TestCase):
    """Test cases for AssetMaintenanceService"""
    
    def setUp(self):
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="Test Address",
            phone="1234567890",
            email="<EMAIL>"
        )
        
        self.category = AssetCategory.objects.create(
            school=self.school,
            name="Computers",
            code="COMP"
        )
        
        self.asset = Asset.objects.create(
            school=self.school,
            name="Test Asset",
            category=self.category,
            purchase_price=Decimal('1000.00'),
            purchase_date=date.today()
        )
    
    def test_schedule_maintenance(self):
        """Test maintenance scheduling"""
        maintenance_data = {
            'maintenance_type': 'preventive',
            'scheduled_date': date.today() + timedelta(days=30),
            'description': 'Regular maintenance',
            'next_maintenance_date': date.today() + timedelta(days=90)
        }
        
        maintenance = AssetMaintenanceService.schedule_maintenance(
            self.asset, maintenance_data
        )
        
        self.assertEqual(maintenance.asset, self.asset)
        self.assertEqual(maintenance.maintenance_type, 'preventive')
        self.assertEqual(maintenance.status, 'scheduled')
        
        # Check if asset next maintenance date was updated
        self.asset.refresh_from_db()
        self.assertEqual(self.asset.next_maintenance_date, maintenance_data['next_maintenance_date'])
    
    def test_complete_maintenance(self):
        """Test maintenance completion"""
        maintenance = AssetMaintenance.objects.create(
            school=self.school,
            asset=self.asset,
            maintenance_type='corrective',
            scheduled_date=date.today(),
            description='Fix issue',
            status='in_progress'
        )
        
        completion_data = {
            'completed_date': date.today(),
            'cost': Decimal('150.00'),
            'performed_by': 'Tech Team',
            'asset_condition': 'good',
            'next_maintenance_date': date.today() + timedelta(days=90)
        }
        
        completed_maintenance = AssetMaintenanceService.complete_maintenance(
            maintenance, completion_data
        )
        
        self.assertEqual(completed_maintenance.status, 'completed')
        self.assertEqual(completed_maintenance.cost, completion_data['cost'])
        
        # Check if asset was updated
        self.asset.refresh_from_db()
        self.assertEqual(self.asset.condition, 'good')
        self.assertEqual(self.asset.last_maintenance_date, completion_data['completed_date'])
    
    def test_get_maintenance_due(self):
        """Test getting assets with maintenance due"""
        # Create asset with maintenance due
        self.asset.next_maintenance_date = date.today() - timedelta(days=1)
        self.asset.save()
        
        due_assets = AssetMaintenanceService.get_maintenance_due(self.school, days_ahead=30)
        
        self.assertEqual(len(due_assets), 1)
        self.assertEqual(due_assets[0], self.asset)


class AssetAuditServiceTests(TestCase):
    """Test cases for AssetAuditService"""
    
    def setUp(self):
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="Test Address",
            phone="1234567890",
            email="<EMAIL>"
        )
        
        self.category = AssetCategory.objects.create(
            school=self.school,
            name="Computers",
            code="COMP"
        )
        
        self.location = Location.objects.create(
            school=self.school,
            name="IT Lab",
            code="ITL"
        )
        
        self.asset = Asset.objects.create(
            school=self.school,
            name="Test Asset",
            category=self.category,
            purchase_price=Decimal('1000.00'),
            purchase_date=date.today(),
            location=self.location,
            status='active'
        )
    
    def test_create_audit(self):
        """Test audit creation"""
        audit_data = {
            'audit_name': 'Monthly Audit',
            'audit_date': date.today(),
            'location': self.location
        }
        
        audit = AssetAuditService.create_audit(audit_data, self.school)
        
        self.assertEqual(audit.audit_name, 'Monthly Audit')
        self.assertEqual(audit.location, self.location)
        self.assertEqual(audit.total_assets_expected, 1)
        
        # Check if audit items were created
        audit_items = audit.audit_items.all()
        self.assertEqual(len(audit_items), 1)
        self.assertEqual(audit_items[0].asset, self.asset)
    
    def test_scan_asset(self):
        """Test asset scanning during audit"""
        audit = AssetAudit.objects.create(
            school=self.school,
            audit_name='Test Audit',
            audit_date=date.today(),
            status='in_progress'
        )
        
        AssetAuditItem.objects.create(
            school=self.school,
            audit=audit,
            asset=self.asset,
            expected_location=self.location,
            expected_condition='excellent',
            actual_location=self.location,
            actual_condition='excellent',
            status='found'
        )
        
        success, message, audit_item = AssetAuditService.scan_asset(
            audit, self.asset.asset_tag, self.location, 'good'
        )
        
        self.assertTrue(success)
        self.assertIsNotNone(audit_item)
        self.assertEqual(audit_item.actual_condition, 'good')
        self.assertIsNotNone(audit_item.scanned_at)
    
    def test_complete_audit(self):
        """Test audit completion"""
        audit = AssetAudit.objects.create(
            school=self.school,
            audit_name='Test Audit',
            audit_date=date.today(),
            status='in_progress',
            total_assets_expected=1
        )
        
        AssetAuditItem.objects.create(
            school=self.school,
            audit=audit,
            asset=self.asset,
            expected_location=self.location,
            expected_condition='excellent',
            actual_location=self.location,
            actual_condition='excellent',
            status='found'
        )
        
        summary = AssetAuditService.complete_audit(audit)
        
        self.assertEqual(summary['total_expected'], 1)
        self.assertEqual(summary['total_found'], 1)
        self.assertEqual(summary['discrepancies'], 0)
        self.assertEqual(summary['accuracy_rate'], 100.0)
        
        # Check if audit status was updated
        audit.refresh_from_db()
        self.assertEqual(audit.status, 'completed')


class AssetAnalyticsServiceTests(TestCase):
    """Test cases for AssetAnalyticsService"""
    
    def setUp(self):
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="Test Address",
            phone="1234567890",
            email="<EMAIL>"
        )
        
        self.category = AssetCategory.objects.create(
            school=self.school,
            name="Computers",
            code="COMP"
        )
        
        self.location = Location.objects.create(
            school=self.school,
            name="IT Lab",
            code="ITL"
        )
        
        # Create test assets
        for i in range(5):
            Asset.objects.create(
                school=self.school,
                name=f"Asset {i+1}",
                category=self.category,
                purchase_price=Decimal('1000.00'),
                purchase_date=date.today(),
                location=self.location if i < 3 else None,  # 3 assigned, 2 unassigned
                status='active'
            )
    
    def test_calculate_asset_utilization(self):
        """Test asset utilization calculation"""
        start_date = date.today() - timedelta(days=30)
        end_date = date.today()
        
        utilization = AssetAnalyticsService.calculate_asset_utilization(
            self.school, start_date, end_date
        )
        
        self.assertEqual(utilization['total_assets'], 5)
        self.assertEqual(utilization['assigned_assets'], 3)
        self.assertEqual(utilization['utilization_rate'], 60.0)
    
    def test_calculate_depreciation_analysis(self):
        """Test depreciation analysis"""
        analysis = AssetAnalyticsService.calculate_depreciation_analysis(self.school)
        
        self.assertEqual(analysis['total_purchase_value'], 5000.0)  # 5 assets * 1000 each
        self.assertGreaterEqual(analysis['total_current_value'], 0)
        self.assertGreaterEqual(analysis['total_depreciation'], 0)
    
    def test_get_asset_distribution(self):
        """Test asset distribution analysis"""
        distribution = AssetAnalyticsService.get_asset_distribution(self.school)
        
        self.assertIn('by_category', distribution)
        self.assertIn('by_location', distribution)
        self.assertIn('by_condition', distribution)
        self.assertIn('by_age', distribution)
        
        # Check category distribution
        category_dist = distribution['by_category']
        self.assertEqual(len(category_dist), 1)
        self.assertEqual(category_dist[0]['category__name'], 'Computers')
        self.assertEqual(category_dist[0]['count'], 5)


@pytest.mark.django_db
class AssetAPITests:
    """Test cases for Asset API endpoints"""
    
    def test_asset_creation_api(self):
        """Test asset creation through API"""
        # This would test the API endpoints when views are implemented
        pass
    
    def test_asset_search_api(self):
        """Test asset search through API"""
        # This would test the search API endpoints
        pass
    
    def test_asset_transfer_api(self):
        """Test asset transfer through API"""
        # This would test the transfer API endpoints
        pass


class AssetFormTests(TestCase):
    """Test cases for Asset forms"""
    
    def setUp(self):
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="Test Address",
            phone="1234567890",
            email="<EMAIL>"
        )
        
        self.category = AssetCategory.objects.create(
            school=self.school,
            name="Computers",
            code="COMP"
        )
    
    def test_asset_form_validation(self):
        """Test asset form validation"""
        from .forms import AssetForm
        
        # Valid form data
        form_data = {
            'name': 'Test Asset',
            'category': self.category.id,
            'purchase_price': '1000.00',
            'purchase_date': date.today(),
            'status': 'active',
            'condition': 'excellent',
            'depreciation_method': 'straight_line',
            'useful_life_years': 5,
            'depreciation_rate': 20.00
        }
        
        form = AssetForm(data=form_data, school=self.school)
        self.assertTrue(form.is_valid())
        
        # Invalid form data - negative price
        form_data['purchase_price'] = '-100.00'
        form = AssetForm(data=form_data, school=self.school)
        self.assertFalse(form.is_valid())
        self.assertIn('purchase_price', form.errors)


if __name__ == '__main__':
    pytest.main([__file__])