from django.urls import path
from . import views

app_name = 'inventory'

urlpatterns = [
    # Dashboard
    path('', views.inventory_dashboard, name='dashboard'),
    
    # Inventory Items
    path('items/', views.inventory_item_list, name='item_list'),
    path('items/create/', views.inventory_item_create, name='item_create'),
    path('items/<int:item_id>/', views.inventory_item_detail, name='item_detail'),
    path('items/<int:item_id>/edit/', views.inventory_item_edit, name='item_edit'),
    path('items/<int:item_id>/delete/', views.inventory_item_delete, name='item_delete'),
    
    # Stock Management
    path('items/<int:item_id>/adjust/', views.stock_adjustment, name='stock_adjustment'),
    path('transfer/', views.stock_transfer, name='stock_transfer'),
    path('transactions/', views.stock_transaction_list, name='transaction_list'),
    
    # Purchase Orders
    path('purchase-orders/', views.purchase_order_list, name='purchase_order_list'),
    path('purchase-orders/create/', views.purchase_order_create, name='purchase_order_create'),
    path('purchase-orders/<int:order_id>/', views.purchase_order_detail, name='purchase_order_detail'),
    
    # Stock Alerts
    path('alerts/', views.stock_alert_list, name='alert_list'),
    path('alerts/<int:alert_id>/resolve/', views.stock_alert_resolve, name='alert_resolve'),
    
    # Inventory Counts
    path('counts/', views.inventory_count_list, name='count_list'),
    path('counts/create/', views.inventory_count_create, name='count_create'),
    path('counts/<int:count_id>/', views.inventory_count_detail, name='count_detail'),
    
    # Suppliers
    path('suppliers/', views.supplier_list, name='supplier_list'),
    path('suppliers/create/', views.supplier_create, name='supplier_create'),
    path('suppliers/<int:supplier_id>/edit/', views.supplier_edit, name='supplier_edit'),
    
    # Locations
    path('locations/', views.location_list, name='location_list'),
    path('locations/create/', views.location_create, name='location_create'),
    path('locations/<int:location_id>/edit/', views.location_edit, name='location_edit'),
    
    # Categories
    path('categories/', views.category_list, name='category_list'),
    path('categories/create/', views.category_create, name='category_create'),
    path('categories/<int:category_id>/edit/', views.category_edit, name='category_edit'),
    
    # Reports and Analytics
    path('reports/', views.inventory_reports, name='reports'),
    path('export/csv/', views.export_inventory_csv, name='export_csv'),
    
    # Bulk Actions
    path('bulk-action/', views.bulk_action, name='bulk_action'),
    
    # API Endpoints
    path('api/search/', views.api_item_search, name='api_item_search'),
    path('api/stock-level/<int:item_id>/', views.api_stock_level, name='api_stock_level'),
    
    # Maintenance Management
    path('maintenance/', views.maintenance_dashboard, name='maintenance_dashboard'),
    path('maintenance/list/', views.maintenance_list, name='maintenance_list'),
    path('maintenance/<int:maintenance_id>/', views.maintenance_detail, name='maintenance_detail'),
    path('maintenance/schedule/', views.maintenance_schedule, name='maintenance_schedule'),
    path('maintenance/schedule/<int:asset_id>/', views.maintenance_schedule, name='maintenance_schedule_asset'),
    path('maintenance/<int:maintenance_id>/complete/', views.maintenance_complete, name='maintenance_complete'),
    path('maintenance/<int:maintenance_id>/cancel/', views.maintenance_cancel, name='maintenance_cancel'),
    path('maintenance/history/<int:asset_id>/', views.maintenance_history, name='maintenance_history'),
    path('maintenance/due/', views.maintenance_due_list, name='maintenance_due'),
    path('maintenance/analytics/', views.maintenance_analytics, name='maintenance_analytics'),
    
    # Work Orders
    path('work-orders/', views.work_order_list, name='work_order_list'),
    path('work-orders/<int:maintenance_id>/', views.work_order_detail, name='work_order_detail'),
    path('work-orders/<int:maintenance_id>/start/', views.work_order_start, name='work_order_start'),
    
    # Asset Analytics
    path('analytics/', views.asset_analytics_dashboard, name='asset_analytics_dashboard'),
    path('analytics/utilization/', views.asset_utilization_report, name='asset_utilization_report'),
    path('analytics/depreciation/', views.asset_depreciation_report, name='asset_depreciation_report'),
    path('analytics/replacement/', views.asset_replacement_planning, name='asset_replacement_planning'),
    path('analytics/performance/', views.asset_performance_metrics, name='asset_performance_metrics'),
    path('analytics/replacement-tools/', views.replacement_planning_tools, name='replacement_planning_tools'),
    path('analytics/dashboards/', views.asset_dashboards, name='asset_dashboards'),
]