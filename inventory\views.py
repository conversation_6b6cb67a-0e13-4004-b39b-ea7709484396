from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.db.models import Q, F, Sum, Count, Avg
from django.db.models.functions import Extract
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from decimal import Decimal
import json
import csv

from core.permissions import school_required
from .models import (
    InventoryItem, AssetCategory, Location, Supplier, StockTransaction,
    PurchaseOrder, PurchaseOrderItem, StockAlert, InventoryCount, 
    InventoryCountItem, Asset, AssetMaintenance
)
from .forms import (
    AssetForm, AssetCategoryForm, LocationForm, SupplierForm,
    AssetSearchForm, BulkAssetActionForm, AssetMaintenanceForm,
    MaintenanceCompletionForm
)
from .services import (
    InventoryManagementService, StockAlertService, PurchaseOrderService,
    InventoryCountService, InventoryAnalyticsService, AssetMaintenanceService,
    AssetAnalyticsService
)


@login_required
@school_required
def inventory_dashboard(request):
    """Main inventory dashboard"""
    school = request.school
    
    # Get summary statistics
    total_items = InventoryItem.objects.filter(school=school, status='active').count()
    low_stock_items = InventoryItem.objects.filter(
        school=school, status='active', current_stock__lte=F('minimum_stock')
    ).count()
    out_of_stock_items = InventoryItem.objects.filter(
        school=school, status='active', current_stock__lte=0
    ).count()
    
    # Get recent transactions
    recent_transactions = StockTransaction.objects.filter(
        school=school
    ).select_related('item', 'from_location', 'to_location').order_by('-transaction_date')[:10]
    
    # Get active alerts
    active_alerts = StockAlert.objects.filter(
        school=school, status='active'
    ).select_related('item').order_by('-alert_date')[:10]
    
    # Get stock valuation
    stock_valuation = InventoryAnalyticsService.get_stock_valuation(school)
    
    context = {
        'total_items': total_items,
        'low_stock_items': low_stock_items,
        'out_of_stock_items': out_of_stock_items,
        'recent_transactions': recent_transactions,
        'active_alerts': active_alerts,
        'stock_valuation': stock_valuation,
    }
    
    return render(request, 'inventory/dashboard.html', context)


@login_required
@school_required
def inventory_item_list(request):
    """List all inventory items"""
    school = request.school
    
    # Handle search and filters
    search_form = AssetSearchForm(request.GET, school=school)
    
    items = InventoryItem.objects.filter(school=school).select_related(
        'category', 'primary_location', 'primary_supplier'
    )
    
    if search_form.is_valid():
        query = search_form.cleaned_data.get('query')
        if query:
            items = items.filter(
                Q(item_code__icontains=query) |
                Q(name__icontains=query) |
                Q(description__icontains=query)
            )
        
        category = search_form.cleaned_data.get('category')
        if category:
            items = items.filter(category=category)
        
        location = search_form.cleaned_data.get('location')
        if location:
            items = items.filter(primary_location=location)
        
        status = search_form.cleaned_data.get('status')
        if status:
            items = items.filter(status=status)
    
    # Pagination
    paginator = Paginator(items, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'search_form': search_form,
    }
    
    return render(request, 'inventory/item_list.html', context)


@login_required
@school_required
def inventory_item_detail(request, item_id):
    """View inventory item details"""
    school = request.school
    item = get_object_or_404(InventoryItem, id=item_id, school=school)
    
    # Get recent transactions for this item
    transactions = StockTransaction.objects.filter(
        item=item
    ).select_related('from_location', 'to_location').order_by('-transaction_date')[:20]
    
    # Get stock alerts for this item
    alerts = StockAlert.objects.filter(
        item=item
    ).order_by('-alert_date')[:10]
    
    context = {
        'item': item,
        'transactions': transactions,
        'alerts': alerts,
    }
    
    return render(request, 'inventory/item_detail.html', context)


@login_required
@school_required
def inventory_item_create(request):
    """Create new inventory item"""
    school = request.school
    
    if request.method == 'POST':
        form = AssetForm(request.POST, request.FILES, school=school)
        if form.is_valid():
            item = form.save(commit=False)
            item.school = school
            item.created_by = request.user
            item.save()
            
            messages.success(request, f'Inventory item "{item.name}" created successfully.')
            return redirect('inventory:item_detail', item_id=item.id)
    else:
        form = AssetForm(school=school)
    
    context = {
        'form': form,
        'title': 'Create Inventory Item',
    }
    
    return render(request, 'inventory/item_form.html', context)


@login_required
@school_required
def inventory_item_edit(request, item_id):
    """Edit inventory item"""
    school = request.school
    item = get_object_or_404(InventoryItem, id=item_id, school=school)
    
    if request.method == 'POST':
        form = AssetForm(request.POST, request.FILES, instance=item, school=school)
        if form.is_valid():
            item = form.save()
            messages.success(request, f'Inventory item "{item.name}" updated successfully.')
            return redirect('inventory:item_detail', item_id=item.id)
    else:
        form = AssetForm(instance=item, school=school)
    
    context = {
        'form': form,
        'item': item,
        'title': 'Edit Inventory Item',
    }
    
    return render(request, 'inventory/item_form.html', context)


@login_required
@school_required
def inventory_item_delete(request, item_id):
    """Delete inventory item"""
    school = request.school
    item = get_object_or_404(InventoryItem, id=item_id, school=school)
    
    if request.method == 'POST':
        item_name = item.name
        item.delete()
        messages.success(request, f'Inventory item "{item_name}" deleted successfully.')
        return redirect('inventory:item_list')
    
    context = {
        'item': item,
    }
    
    return render(request, 'inventory/item_confirm_delete.html', context)


@login_required
@school_required
def stock_transaction_list(request):
    """List stock transactions"""
    school = request.school
    
    transactions = StockTransaction.objects.filter(
        school=school
    ).select_related('item', 'from_location', 'to_location').order_by('-transaction_date')
    
    # Filter by item if specified
    item_id = request.GET.get('item')
    if item_id:
        transactions = transactions.filter(item_id=item_id)
    
    # Filter by date range
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    if start_date:
        transactions = transactions.filter(transaction_date__gte=start_date)
    if end_date:
        transactions = transactions.filter(transaction_date__lte=end_date)
    
    # Pagination
    paginator = Paginator(transactions, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
    }
    
    return render(request, 'inventory/transaction_list.html', context)


@login_required
@school_required
def stock_adjustment(request, item_id):
    """Adjust stock levels"""
    school = request.school
    item = get_object_or_404(InventoryItem, id=item_id, school=school)
    
    if request.method == 'POST':
        adjustment_type = request.POST.get('adjustment_type')
        quantity = int(request.POST.get('quantity', 0))
        reason = request.POST.get('reason', '')
        
        if adjustment_type and quantity > 0:
            service = InventoryManagementService()
            
            if adjustment_type == 'increase':
                service.adjust_stock(item, quantity, reason, request.user)
                messages.success(request, f'Stock increased by {quantity} units.')
            elif adjustment_type == 'decrease':
                service.adjust_stock(item, -quantity, reason, request.user)
                messages.success(request, f'Stock decreased by {quantity} units.')
            
            return redirect('inventory:item_detail', item_id=item.id)
    
    context = {
        'item': item,
    }
    
    return render(request, 'inventory/stock_adjustment.html', context)


@login_required
@school_required
def stock_transfer(request):
    """Transfer stock between locations"""
    school = request.school
    
    if request.method == 'POST':
        item_id = request.POST.get('item')
        from_location_id = request.POST.get('from_location')
        to_location_id = request.POST.get('to_location')
        quantity = int(request.POST.get('quantity', 0))
        notes = request.POST.get('notes', '')
        
        if item_id and from_location_id and to_location_id and quantity > 0:
            item = get_object_or_404(InventoryItem, id=item_id, school=school)
            from_location = get_object_or_404(Location, id=from_location_id, school=school)
            to_location = get_object_or_404(Location, id=to_location_id, school=school)
            
            service = InventoryManagementService()
            success = service.transfer_stock(
                item, from_location, to_location, quantity, notes, request.user
            )
            
            if success:
                messages.success(request, f'Stock transferred successfully.')
            else:
                messages.error(request, 'Insufficient stock for transfer.')
            
            return redirect('inventory:dashboard')
    
    # Get items and locations for form
    items = InventoryItem.objects.filter(school=school, status='active')
    locations = Location.objects.filter(school=school, is_active=True)
    
    context = {
        'items': items,
        'locations': locations,
    }
    
    return render(request, 'inventory/stock_transfer.html', context)


@login_required
@school_required
def purchase_order_list(request):
    """List purchase orders"""
    school = request.school
    
    orders = PurchaseOrder.objects.filter(
        school=school
    ).select_related('supplier').order_by('-order_date')
    
    # Filter by status
    status = request.GET.get('status')
    if status:
        orders = orders.filter(status=status)
    
    # Pagination
    paginator = Paginator(orders, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
    }
    
    return render(request, 'inventory/purchase_order_list.html', context)


@login_required
@school_required
def purchase_order_detail(request, order_id):
    """View purchase order details"""
    school = request.school
    order = get_object_or_404(PurchaseOrder, id=order_id, school=school)
    
    context = {
        'order': order,
    }
    
    return render(request, 'inventory/purchase_order_detail.html', context)


@login_required
@school_required
def purchase_order_create(request):
    """Create new purchase order"""
    school = request.school
    
    if request.method == 'POST':
        supplier_id = request.POST.get('supplier')
        expected_delivery = request.POST.get('expected_delivery')
        notes = request.POST.get('notes', '')
        
        if supplier_id:
            supplier = get_object_or_404(Supplier, id=supplier_id, school=school)
            
            service = PurchaseOrderService()
            order = service.create_purchase_order(
                school, supplier, expected_delivery, notes, request.user
            )
            
            messages.success(request, f'Purchase order {order.order_number} created successfully.')
            return redirect('inventory:purchase_order_detail', order_id=order.id)
    
    suppliers = Supplier.objects.filter(school=school, is_active=True)
    
    context = {
        'suppliers': suppliers,
    }
    
    return render(request, 'inventory/purchase_order_form.html', context)


@login_required
@school_required
def stock_alert_list(request):
    """List stock alerts"""
    school = request.school
    
    alerts = StockAlert.objects.filter(
        school=school
    ).select_related('item').order_by('-alert_date')
    
    # Filter by status
    status = request.GET.get('status', 'active')
    if status:
        alerts = alerts.filter(status=status)
    
    # Pagination
    paginator = Paginator(alerts, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'current_status': status,
    }
    
    return render(request, 'inventory/alert_list.html', context)


@login_required
@school_required
def stock_alert_resolve(request, alert_id):
    """Resolve stock alert"""
    school = request.school
    alert = get_object_or_404(StockAlert, id=alert_id, school=school)
    
    if request.method == 'POST':
        resolution_notes = request.POST.get('resolution_notes', '')
        
        service = StockAlertService()
        service.resolve_alert(alert, request.user, resolution_notes)
        
        messages.success(request, 'Stock alert resolved successfully.')
        return redirect('inventory:alert_list')
    
    context = {
        'alert': alert,
    }
    
    return render(request, 'inventory/alert_resolve.html', context)


@login_required
@school_required
def inventory_count_list(request):
    """List inventory counts"""
    school = request.school
    
    counts = InventoryCount.objects.filter(
        school=school
    ).order_by('-count_date')
    
    # Pagination
    paginator = Paginator(counts, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
    }
    
    return render(request, 'inventory/count_list.html', context)


@login_required
@school_required
def inventory_count_create(request):
    """Create new inventory count"""
    school = request.school
    
    if request.method == 'POST':
        count_name = request.POST.get('count_name')
        location_id = request.POST.get('location')
        category_id = request.POST.get('category')
        
        if count_name:
            location = None
            if location_id:
                location = get_object_or_404(Location, id=location_id, school=school)
            
            category = None
            if category_id:
                category = get_object_or_404(AssetCategory, id=category_id, school=school)
            
            service = InventoryCountService()
            count = service.create_inventory_count(
                school, count_name, location, category, request.user
            )
            
            messages.success(request, f'Inventory count "{count.count_name}" created successfully.')
            return redirect('inventory:count_detail', count_id=count.id)
    
    locations = Location.objects.filter(school=school, is_active=True)
    categories = AssetCategory.objects.filter(school=school, is_active=True)
    
    context = {
        'locations': locations,
        'categories': categories,
    }
    
    return render(request, 'inventory/count_form.html', context)


@login_required
@school_required
def inventory_count_detail(request, count_id):
    """View inventory count details"""
    school = request.school
    count = get_object_or_404(InventoryCount, id=count_id, school=school)
    
    context = {
        'count': count,
    }
    
    return render(request, 'inventory/count_detail.html', context)


@login_required
@school_required
def supplier_list(request):
    """List suppliers"""
    school = request.school
    
    suppliers = Supplier.objects.filter(school=school)
    
    # Search functionality
    query = request.GET.get('q')
    if query:
        suppliers = suppliers.filter(
            Q(name__icontains=query) |
            Q(contact_person__icontains=query) |
            Q(email__icontains=query)
        )
    
    # Pagination
    paginator = Paginator(suppliers, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'query': query,
    }
    
    return render(request, 'inventory/supplier_list.html', context)


@login_required
@school_required
def supplier_create(request):
    """Create new supplier"""
    school = request.school
    
    if request.method == 'POST':
        form = SupplierForm(request.POST, school=school)
        if form.is_valid():
            supplier = form.save(commit=False)
            supplier.school = school
            supplier.save()
            
            messages.success(request, f'Supplier "{supplier.name}" created successfully.')
            return redirect('inventory:supplier_list')
    else:
        form = SupplierForm(school=school)
    
    context = {
        'form': form,
        'title': 'Create Supplier',
    }
    
    return render(request, 'inventory/supplier_form.html', context)


@login_required
@school_required
def supplier_edit(request, supplier_id):
    """Edit supplier"""
    school = request.school
    supplier = get_object_or_404(Supplier, id=supplier_id, school=school)
    
    if request.method == 'POST':
        form = SupplierForm(request.POST, instance=supplier, school=school)
        if form.is_valid():
            supplier = form.save()
            messages.success(request, f'Supplier "{supplier.name}" updated successfully.')
            return redirect('inventory:supplier_list')
    else:
        form = SupplierForm(instance=supplier, school=school)
    
    context = {
        'form': form,
        'supplier': supplier,
        'title': 'Edit Supplier',
    }
    
    return render(request, 'inventory/supplier_form.html', context)


@login_required
@school_required
def location_list(request):
    """List locations"""
    school = request.school
    
    locations = Location.objects.filter(school=school)
    
    # Search functionality
    query = request.GET.get('q')
    if query:
        locations = locations.filter(
            Q(name__icontains=query) |
            Q(description__icontains=query)
        )
    
    # Pagination
    paginator = Paginator(locations, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'query': query,
    }
    
    return render(request, 'inventory/location_list.html', context)


@login_required
@school_required
def location_create(request):
    """Create new location"""
    school = request.school
    
    if request.method == 'POST':
        form = LocationForm(request.POST, school=school)
        if form.is_valid():
            location = form.save(commit=False)
            location.school = school
            location.save()
            
            messages.success(request, f'Location "{location.name}" created successfully.')
            return redirect('inventory:location_list')
    else:
        form = LocationForm(school=school)
    
    context = {
        'form': form,
        'title': 'Create Location',
    }
    
    return render(request, 'inventory/location_form.html', context)


@login_required
@school_required
def location_edit(request, location_id):
    """Edit location"""
    school = request.school
    location = get_object_or_404(Location, id=location_id, school=school)
    
    if request.method == 'POST':
        form = LocationForm(request.POST, instance=location, school=school)
        if form.is_valid():
            location = form.save()
            messages.success(request, f'Location "{location.name}" updated successfully.')
            return redirect('inventory:location_list')
    else:
        form = LocationForm(instance=location, school=school)
    
    context = {
        'form': form,
        'location': location,
        'title': 'Edit Location',
    }
    
    return render(request, 'inventory/location_form.html', context)


@login_required
@school_required
def category_list(request):
    """List asset categories"""
    school = request.school
    
    categories = AssetCategory.objects.filter(school=school)
    
    # Search functionality
    query = request.GET.get('q')
    if query:
        categories = categories.filter(
            Q(name__icontains=query) |
            Q(description__icontains=query)
        )
    
    # Pagination
    paginator = Paginator(categories, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'query': query,
    }
    
    return render(request, 'inventory/category_list.html', context)


@login_required
@school_required
def category_create(request):
    """Create new asset category"""
    school = request.school
    
    if request.method == 'POST':
        form = AssetCategoryForm(request.POST, school=school)
        if form.is_valid():
            category = form.save(commit=False)
            category.school = school
            category.save()
            
            messages.success(request, f'Category "{category.name}" created successfully.')
            return redirect('inventory:category_list')
    else:
        form = AssetCategoryForm(school=school)
    
    context = {
        'form': form,
        'title': 'Create Category',
    }
    
    return render(request, 'inventory/category_form.html', context)


@login_required
@school_required
def category_edit(request, category_id):
    """Edit asset category"""
    school = request.school
    category = get_object_or_404(AssetCategory, id=category_id, school=school)
    
    if request.method == 'POST':
        form = AssetCategoryForm(request.POST, instance=category, school=school)
        if form.is_valid():
            category = form.save()
            messages.success(request, f'Category "{category.name}" updated successfully.')
            return redirect('inventory:category_list')
    else:
        form = AssetCategoryForm(instance=category, school=school)
    
    context = {
        'form': form,
        'category': category,
        'title': 'Edit Category',
    }
    
    return render(request, 'inventory/category_form.html', context)


@login_required
@school_required
def inventory_reports(request):
    """Inventory reports and analytics"""
    school = request.school
    
    # Get analytics data
    analytics_service = InventoryAnalyticsService()
    
    # Stock valuation
    stock_valuation = analytics_service.get_stock_valuation(school)
    
    # Low stock items
    low_stock_items = InventoryItem.objects.filter(
        school=school, 
        status='active',
        current_stock__lte=F('minimum_stock')
    ).select_related('category', 'primary_location')[:20]
    
    # Top items by value
    top_items_by_value = InventoryItem.objects.filter(
        school=school,
        status='active'
    ).annotate(
        total_value=F('current_stock') * F('unit_cost')
    ).order_by('-total_value')[:10]
    
    # Category distribution
    category_stats = AssetCategory.objects.filter(
        school=school,
        is_active=True
    ).annotate(
        item_count=Count('inventory_items'),
        total_value=Sum(F('inventory_items__current_stock') * F('inventory_items__unit_cost'))
    ).order_by('-total_value')
    
    # Recent transactions summary
    recent_transactions = StockTransaction.objects.filter(
        school=school,
        transaction_date__gte=timezone.now() - timezone.timedelta(days=30)
    ).values('transaction_type').annotate(
        count=Count('id'),
        total_quantity=Sum('quantity')
    )
    
    context = {
        'stock_valuation': stock_valuation,
        'low_stock_items': low_stock_items,
        'top_items_by_value': top_items_by_value,
        'category_stats': category_stats,
        'recent_transactions': recent_transactions,
    }
    
    return render(request, 'inventory/reports.html', context)


@login_required
@school_required
def export_inventory_csv(request):
    """Export inventory data to CSV"""
    school = request.school
    
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="inventory_export.csv"'
    
    writer = csv.writer(response)
    writer.writerow([
        'Item Code', 'Name', 'Category', 'Location', 'Current Stock',
        'Minimum Stock', 'Unit Cost', 'Total Value', 'Status'
    ])
    
    items = InventoryItem.objects.filter(school=school).select_related(
        'category', 'primary_location'
    )
    
    for item in items:
        total_value = item.current_stock * item.unit_cost if item.unit_cost else 0
        writer.writerow([
            item.item_code,
            item.name,
            item.category.name if item.category else '',
            item.primary_location.name if item.primary_location else '',
            item.current_stock,
            item.minimum_stock,
            item.unit_cost,
            total_value,
            item.get_status_display()
        ])
    
    return response


@login_required
@school_required
@require_http_methods(["POST"])
def bulk_action(request):
    """Handle bulk actions on inventory items"""
    school = request.school
    
    form = BulkAssetActionForm(request.POST, school=school)
    if form.is_valid():
        action = form.cleaned_data['action']
        items = form.cleaned_data['items']
        
        if action == 'delete':
            count = items.count()
            items.delete()
            messages.success(request, f'{count} items deleted successfully.')
        
        elif action == 'activate':
            count = items.update(status='active')
            messages.success(request, f'{count} items activated successfully.')
        
        elif action == 'deactivate':
            count = items.update(status='inactive')
            messages.success(request, f'{count} items deactivated successfully.')
        
        elif action == 'update_location':
            location = form.cleaned_data.get('new_location')
            if location:
                count = items.update(primary_location=location)
                messages.success(request, f'{count} items location updated successfully.')
        
        elif action == 'update_category':
            category = form.cleaned_data.get('new_category')
            if category:
                count = items.update(category=category)
                messages.success(request, f'{count} items category updated successfully.')
    
    return redirect('inventory:item_list')


# API Views for AJAX requests
@login_required
@school_required
def api_item_search(request):
    """API endpoint for item search (AJAX)"""
    school = request.school
    query = request.GET.get('q', '')
    
    if len(query) < 2:
        return JsonResponse({'items': []})
    
    items = InventoryItem.objects.filter(
        school=school,
        status='active'
    ).filter(
        Q(item_code__icontains=query) |
        Q(name__icontains=query)
    )[:10]
    
    results = []
    for item in items:
        results.append({
            'id': item.id,
            'item_code': item.item_code,
            'name': item.name,
            'current_stock': item.current_stock,
            'unit_cost': str(item.unit_cost) if item.unit_cost else '0.00'
        })
    
    return JsonResponse({'items': results})


@login_required
@school_required
def api_stock_level(request, item_id):
    """API endpoint to get current stock level"""
    school = request.school
    item = get_object_or_404(InventoryItem, id=item_id, school=school)
    
    return JsonResponse({
        'current_stock': item.current_stock,
        'minimum_stock': item.minimum_stock,
        'status': 'low' if item.current_stock <= item.minimum_stock else 'normal'
    })


# Maintenance Management Views

@login_required
@school_required
def maintenance_dashboard(request):
    """Maintenance management dashboard"""
    school = request.school
    
    # Get maintenance statistics
    total_assets = Asset.objects.filter(school=school, status='active').count()
    maintenance_due = Asset.objects.filter(
        school=school, 
        status='active',
        next_maintenance_date__lte=timezone.now().date()
    ).count()
    
    in_maintenance = Asset.objects.filter(school=school, status='maintenance').count()
    
    # Get recent maintenance activities
    recent_maintenance = AssetMaintenance.objects.filter(
        school=school
    ).select_related('asset', 'supplier').order_by('-created_at')[:10]
    
    # Get upcoming maintenance
    upcoming_maintenance = AssetMaintenance.objects.filter(
        school=school,
        status='scheduled',
        scheduled_date__gte=timezone.now().date(),
        scheduled_date__lte=timezone.now().date() + timezone.timedelta(days=30)
    ).select_related('asset').order_by('scheduled_date')[:10]
    
    # Get maintenance costs for current month
    current_month_start = timezone.now().date().replace(day=1)
    maintenance_costs = AssetMaintenanceService.get_maintenance_costs(
        school, current_month_start, timezone.now().date()
    )
    
    # Get assets needing maintenance
    assets_due = AssetMaintenanceService.get_maintenance_due(school, days_ahead=7)
    
    context = {
        'total_assets': total_assets,
        'maintenance_due': maintenance_due,
        'in_maintenance': in_maintenance,
        'recent_maintenance': recent_maintenance,
        'upcoming_maintenance': upcoming_maintenance,
        'maintenance_costs': maintenance_costs,
        'assets_due': assets_due,
    }
    
    return render(request, 'inventory/maintenance_dashboard.html', context)


@login_required
@school_required
def maintenance_list(request):
    """List all maintenance records"""
    school = request.school
    
    maintenance_records = AssetMaintenance.objects.filter(
        school=school
    ).select_related('asset', 'supplier').order_by('-scheduled_date')
    
    # Filter by status
    status = request.GET.get('status')
    if status:
        maintenance_records = maintenance_records.filter(status=status)
    
    # Filter by maintenance type
    maintenance_type = request.GET.get('type')
    if maintenance_type:
        maintenance_records = maintenance_records.filter(maintenance_type=maintenance_type)
    
    # Filter by asset
    asset_id = request.GET.get('asset')
    if asset_id:
        maintenance_records = maintenance_records.filter(asset_id=asset_id)
    
    # Search functionality
    query = request.GET.get('q')
    if query:
        maintenance_records = maintenance_records.filter(
            Q(asset__asset_tag__icontains=query) |
            Q(asset__name__icontains=query) |
            Q(description__icontains=query) |
            Q(performed_by__icontains=query)
        )
    
    # Pagination
    paginator = Paginator(maintenance_records, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get filter options
    assets = Asset.objects.filter(school=school, status='active').order_by('asset_tag')
    
    context = {
        'page_obj': page_obj,
        'assets': assets,
        'current_status': status,
        'current_type': maintenance_type,
        'current_asset': asset_id,
        'query': query,
    }
    
    return render(request, 'inventory/maintenance_list.html', context)


@login_required
@school_required
def maintenance_detail(request, maintenance_id):
    """View maintenance record details"""
    school = request.school
    maintenance = get_object_or_404(AssetMaintenance, id=maintenance_id, school=school)
    
    context = {
        'maintenance': maintenance,
    }
    
    return render(request, 'inventory/maintenance_detail.html', context)


@login_required
@school_required
def maintenance_schedule(request, asset_id=None):
    """Schedule maintenance for an asset"""
    school = request.school
    asset = None
    
    if asset_id:
        asset = get_object_or_404(Asset, id=asset_id, school=school)
    
    if request.method == 'POST':
        form = AssetMaintenanceForm(request.POST, school=school)
        if form.is_valid():
            maintenance_data = form.cleaned_data.copy()
            if asset:
                maintenance_data['asset'] = asset
            else:
                asset = maintenance_data['asset']
            
            maintenance = AssetMaintenanceService.schedule_maintenance(
                asset, maintenance_data, request.user
            )
            
            messages.success(request, f'Maintenance scheduled for {asset.asset_tag} on {maintenance.scheduled_date}.')
            return redirect('inventory:maintenance_detail', maintenance_id=maintenance.id)
    else:
        initial_data = {}
        if asset:
            initial_data['asset'] = asset
        form = AssetMaintenanceForm(initial=initial_data, school=school)
    
    # Get assets for selection if no specific asset
    assets = Asset.objects.filter(school=school, status__in=['active', 'maintenance']).order_by('asset_tag')
    
    context = {
        'form': form,
        'asset': asset,
        'assets': assets,
        'title': f'Schedule Maintenance - {asset.asset_tag}' if asset else 'Schedule Maintenance',
    }
    
    return render(request, 'inventory/maintenance_schedule.html', context)


@login_required
@school_required
def maintenance_complete(request, maintenance_id):
    """Complete a maintenance record"""
    school = request.school
    maintenance = get_object_or_404(AssetMaintenance, id=maintenance_id, school=school)
    
    if maintenance.status == 'completed':
        messages.warning(request, 'This maintenance is already completed.')
        return redirect('inventory:maintenance_detail', maintenance_id=maintenance.id)
    
    if request.method == 'POST':
        form = MaintenanceCompletionForm(request.POST)
        if form.is_valid():
            completion_data = form.cleaned_data
            
            AssetMaintenanceService.complete_maintenance(maintenance, completion_data)
            
            messages.success(request, f'Maintenance for {maintenance.asset.asset_tag} marked as completed.')
            return redirect('inventory:maintenance_detail', maintenance_id=maintenance.id)
    else:
        initial_data = {
            'cost': maintenance.cost,
            'performed_by': maintenance.performed_by,
        }
        form = MaintenanceCompletionForm(initial=initial_data)
    
    context = {
        'form': form,
        'maintenance': maintenance,
    }
    
    return render(request, 'inventory/maintenance_complete.html', context)


@login_required
@school_required
def maintenance_cancel(request, maintenance_id):
    """Cancel a scheduled maintenance"""
    school = request.school
    maintenance = get_object_or_404(AssetMaintenance, id=maintenance_id, school=school)
    
    if maintenance.status in ['completed', 'cancelled']:
        messages.warning(request, 'Cannot cancel this maintenance record.')
        return redirect('inventory:maintenance_detail', maintenance_id=maintenance.id)
    
    if request.method == 'POST':
        reason = request.POST.get('reason', '')
        maintenance.status = 'cancelled'
        maintenance.notes = f"Cancelled: {reason}\n{maintenance.notes}".strip()
        maintenance.save()
        
        messages.success(request, f'Maintenance for {maintenance.asset.asset_tag} has been cancelled.')
        return redirect('inventory:maintenance_list')
    
    context = {
        'maintenance': maintenance,
    }
    
    return render(request, 'inventory/maintenance_cancel.html', context)


@login_required
@school_required
def maintenance_history(request, asset_id):
    """View maintenance history for an asset"""
    school = request.school
    asset = get_object_or_404(Asset, id=asset_id, school=school)
    
    maintenance_records = AssetMaintenance.objects.filter(
        asset=asset
    ).select_related('supplier').order_by('-scheduled_date')
    
    # Calculate maintenance statistics
    total_maintenance = maintenance_records.count()
    completed_maintenance = maintenance_records.filter(status='completed').count()
    total_cost = maintenance_records.filter(status='completed').aggregate(
        total=Sum('cost')
    )['total'] or Decimal('0.00')
    
    # Get maintenance by type
    maintenance_by_type = maintenance_records.values('maintenance_type').annotate(
        count=Count('id'),
        total_cost=Sum('cost')
    ).order_by('-count')
    
    context = {
        'asset': asset,
        'maintenance_records': maintenance_records,
        'total_maintenance': total_maintenance,
        'completed_maintenance': completed_maintenance,
        'total_cost': total_cost,
        'maintenance_by_type': maintenance_by_type,
    }
    
    return render(request, 'inventory/maintenance_history.html', context)


@login_required
@school_required
def maintenance_due_list(request):
    """List assets with maintenance due"""
    school = request.school
    
    days_ahead = int(request.GET.get('days', 30))
    assets_due = AssetMaintenanceService.get_maintenance_due(school, days_ahead)
    
    # Group by urgency
    overdue = []
    due_soon = []
    upcoming = []
    
    today = timezone.now().date()
    
    for asset in assets_due:
        if asset.next_maintenance_date:
            days_until = (asset.next_maintenance_date - today).days
            if days_until < 0:
                overdue.append(asset)
            elif days_until <= 7:
                due_soon.append(asset)
            else:
                upcoming.append(asset)
    
    context = {
        'overdue': overdue,
        'due_soon': due_soon,
        'upcoming': upcoming,
        'days_ahead': days_ahead,
    }
    
    return render(request, 'inventory/maintenance_due.html', context)


@login_required
@school_required
def maintenance_analytics(request):
    """Maintenance analytics and reports"""
    school = request.school
    
    # Get date range from request
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    
    if not start_date:
        start_date = timezone.now().date().replace(day=1)  # First day of current month
    else:
        start_date = timezone.datetime.strptime(start_date, '%Y-%m-%d').date()
    
    if not end_date:
        end_date = timezone.now().date()
    else:
        end_date = timezone.datetime.strptime(end_date, '%Y-%m-%d').date()
    
    # Get maintenance costs
    maintenance_costs = AssetMaintenanceService.get_maintenance_costs(school, start_date, end_date)
    
    # Get maintenance by status
    maintenance_by_status = AssetMaintenance.objects.filter(
        school=school,
        scheduled_date__range=[start_date, end_date]
    ).values('status').annotate(
        count=Count('id')
    ).order_by('-count')
    
    # Get top assets by maintenance cost
    top_assets_by_cost = AssetMaintenance.objects.filter(
        school=school,
        completed_date__range=[start_date, end_date],
        status='completed'
    ).values(
        'asset__asset_tag', 'asset__name'
    ).annotate(
        total_cost=Sum('cost'),
        maintenance_count=Count('id')
    ).order_by('-total_cost')[:10]
    
    # Get maintenance trends (monthly)
    maintenance_trends = AssetMaintenance.objects.filter(
        school=school,
        scheduled_date__range=[start_date, end_date]
    ).extra(
        select={'month': "DATE_FORMAT(scheduled_date, '%%Y-%%m')"}
    ).values('month').annotate(
        count=Count('id'),
        total_cost=Sum('cost')
    ).order_by('month')
    
    # Get preventive vs corrective maintenance ratio
    maintenance_type_stats = AssetMaintenance.objects.filter(
        school=school,
        scheduled_date__range=[start_date, end_date]
    ).values('maintenance_type').annotate(
        count=Count('id'),
        total_cost=Sum('cost')
    ).order_by('-count')
    
    context = {
        'start_date': start_date,
        'end_date': end_date,
        'maintenance_costs': maintenance_costs,
        'maintenance_by_status': maintenance_by_status,
        'top_assets_by_cost': top_assets_by_cost,
        'maintenance_trends': maintenance_trends,
        'maintenance_type_stats': maintenance_type_stats,
    }
    
    return render(request, 'inventory/maintenance_analytics.html', context)


@login_required
@school_required
def work_order_list(request):
    """List work orders (scheduled and in-progress maintenance)"""
    school = request.school
    
    work_orders = AssetMaintenance.objects.filter(
        school=school,
        status__in=['scheduled', 'in_progress']
    ).select_related('asset', 'supplier').order_by('scheduled_date')
    
    # Filter by status
    status = request.GET.get('status')
    if status:
        work_orders = work_orders.filter(status=status)
    
    # Filter by maintenance type
    maintenance_type = request.GET.get('type')
    if maintenance_type:
        work_orders = work_orders.filter(maintenance_type=maintenance_type)
    
    # Pagination
    paginator = Paginator(work_orders, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'current_status': status,
        'current_type': maintenance_type,
    }
    
    return render(request, 'inventory/work_order_list.html', context)


@login_required
@school_required
def work_order_detail(request, maintenance_id):
    """View work order details"""
    school = request.school
    work_order = get_object_or_404(
        AssetMaintenance, 
        id=maintenance_id, 
        school=school,
        status__in=['scheduled', 'in_progress']
    )
    
    context = {
        'work_order': work_order,
    }
    
    return render(request, 'inventory/work_order_detail.html', context)


@login_required
@school_required
def work_order_start(request, maintenance_id):
    """Start a work order (change status to in_progress)"""
    school = request.school
    work_order = get_object_or_404(
        AssetMaintenance, 
        id=maintenance_id, 
        school=school,
        status='scheduled'
    )
    
    if request.method == 'POST':
        work_order.status = 'in_progress'
        work_order.save()
        
        # Update asset status to maintenance
        work_order.asset.status = 'maintenance'
        work_order.asset.save(update_fields=['status'])
        
        messages.success(request, f'Work order for {work_order.asset.asset_tag} has been started.')
        return redirect('inventory:work_order_detail', maintenance_id=work_order.id)
    
    context = {
        'work_order': work_order,
    }
    
    return render(request, 'inventory/work_order_start.html', context)


# Asset Analytics Views

@login_required
@school_required
def asset_analytics_dashboard(request):
    """Asset analytics dashboard with comprehensive metrics"""
    school = request.school
    
    # Get basic metrics
    total_assets = Asset.objects.filter(school=school, status='active').count()
    total_asset_value = Asset.objects.filter(school=school, status='active').aggregate(
        total=Sum('purchase_price')
    )['total'] or Decimal('0.00')
    
    # Calculate total depreciation
    assets = Asset.objects.filter(school=school, status__in=['active', 'maintenance'])
    total_depreciation = sum(asset.accumulated_depreciation for asset in assets)
    
    # Get assets needing maintenance
    assets_needing_maintenance = Asset.objects.filter(
        school=school,
        status='active',
        next_maintenance_date__lte=timezone.now().date()
    ).count()
    
    # Get asset distribution
    distribution_data = AssetAnalyticsService.get_asset_distribution(school)
    
    # Get category distribution for chart
    category_distribution = distribution_data['by_category']
    
    # Get status distribution for chart
    status_distribution = Asset.objects.filter(school=school).values('status').annotate(
        count=Count('id')
    ).order_by('-count')
    
    # Get age distribution
    age_distribution = []
    age_ranges = [
        ('0-1 years', 0, 1),
        ('1-3 years', 1, 3),
        ('3-5 years', 3, 5),
        ('5-10 years', 5, 10),
        ('10+ years', 10, 100)
    ]
    
    for range_name, min_age, max_age in age_ranges:
        count = 0
        for asset in assets:
            age = asset.age_in_years
            if min_age <= age < max_age:
                count += 1
        age_distribution.append({
            'age_range': range_name,
            'count': count
        })
    
    # Get top assets by value
    top_assets_by_value = Asset.objects.filter(
        school=school, status='active'
    ).order_by('-purchase_price')[:10]
    
    # Get assets needing attention
    assets_needing_attention = []
    
    # Add assets with maintenance due
    maintenance_due_assets = Asset.objects.filter(
        school=school,
        status='active',
        next_maintenance_date__lte=timezone.now().date()
    )[:5]
    
    # Add assets in poor condition
    poor_condition_assets = Asset.objects.filter(
        school=school,
        status='active',
        condition='poor'
    )[:5]
    
    assets_needing_attention = list(maintenance_due_assets) + list(poor_condition_assets)
    
    # Get category performance
    category_performance = []
    for category in AssetCategory.objects.filter(school=school, is_active=True):
        category_assets = Asset.objects.filter(school=school, category=category, status='active')
        if category_assets.exists():
            total_value = category_assets.aggregate(total=Sum('purchase_price'))['total'] or Decimal('0.00')
            avg_age = sum(asset.age_in_years for asset in category_assets) / category_assets.count()
            
            category_performance.append({
                'name': category.name,
                'asset_count': category_assets.count(),
                'total_value': total_value,
                'avg_age': avg_age
            })
    
    # Get location analysis
    location_analysis = []
    for location in Location.objects.filter(school=school, is_active=True):
        location_assets = Asset.objects.filter(school=school, location=location, status='active')
        if location_assets.exists():
            total_value = location_assets.aggregate(total=Sum('purchase_price'))['total'] or Decimal('0.00')
            # Simple utilization calculation based on assigned assets
            assigned_count = location_assets.filter(assigned_to__isnull=False).count()
            utilization_rate = (assigned_count / location_assets.count() * 100) if location_assets.count() > 0 else 0
            
            location_analysis.append({
                'name': location.name,
                'asset_count': location_assets.count(),
                'total_value': total_value,
                'utilization_rate': utilization_rate
            })
    
    # Add unassigned assets
    unassigned_assets = Asset.objects.filter(school=school, location__isnull=True, status='active')
    if unassigned_assets.exists():
        total_value = unassigned_assets.aggregate(total=Sum('purchase_price'))['total'] or Decimal('0.00')
        location_analysis.append({
            'name': None,
            'asset_count': unassigned_assets.count(),
            'total_value': total_value,
            'utilization_rate': 0
        })
    
    # Get lifecycle analysis
    lifecycle_analysis = {
        'new_assets': 0,      # < 1 year
        'mature_assets': 0,   # 1-5 years
        'aging_assets': 0,    # 5-10 years
        'legacy_assets': 0    # > 10 years
    }
    
    for asset in assets:
        age = asset.age_in_years
        if age < 1:
            lifecycle_analysis['new_assets'] += 1
        elif age < 5:
            lifecycle_analysis['mature_assets'] += 1
        elif age < 10:
            lifecycle_analysis['aging_assets'] += 1
        else:
            lifecycle_analysis['legacy_assets'] += 1
    
    # Get depreciation trends (by year)
    depreciation_trends = []
    current_year = timezone.now().year
    for year in range(current_year - 4, current_year + 1):
        year_assets = Asset.objects.filter(
            school=school,
            purchase_date__year__lte=year,
            status__in=['active', 'maintenance', 'retired']
        )
        
        purchase_value = year_assets.filter(purchase_date__year=year).aggregate(
            total=Sum('purchase_price')
        )['total'] or Decimal('0.00')
        
        # Calculate current value for assets purchased in that year
        current_value = sum(
            asset.book_value for asset in year_assets.filter(purchase_date__year=year)
        )
        
        depreciation_trends.append({
            'year': year,
            'purchase_value': float(purchase_value),
            'current_value': float(current_value)
        })
    
    context = {
        'total_assets': total_assets,
        'total_asset_value': total_asset_value,
        'total_depreciation': total_depreciation,
        'assets_needing_maintenance': assets_needing_maintenance,
        'category_distribution': category_distribution,
        'status_distribution': status_distribution,
        'age_distribution': age_distribution,
        'top_assets_by_value': top_assets_by_value,
        'assets_needing_attention': assets_needing_attention,
        'category_performance': category_performance,
        'location_analysis': location_analysis,
        'lifecycle_analysis': lifecycle_analysis,
        'depreciation_trends': depreciation_trends,
    }
    
    return render(request, 'inventory/asset_analytics_dashboard.html', context)


@login_required
@school_required
def asset_utilization_report(request):
    """Asset utilization analysis and reporting"""
    school = request.school
    
    # Get date range from request
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    
    if not start_date:
        start_date = timezone.now().date().replace(day=1)  # First day of current month
    else:
        start_date = timezone.datetime.strptime(start_date, '%Y-%m-%d').date()
    
    if not end_date:
        end_date = timezone.now().date()
    else:
        end_date = timezone.datetime.strptime(end_date, '%Y-%m-%d').date()
    
    # Get utilization data
    utilization_data = AssetAnalyticsService.calculate_asset_utilization(school, start_date, end_date)
    
    # Get underutilized assets (not assigned for more than 30 days)
    underutilized_assets = Asset.objects.filter(
        school=school,
        status='active',
        assigned_to__isnull=True
    ).select_related('category', 'location')[:20]
    
    # Get highly utilized locations
    location_utilization = []
    for location in Location.objects.filter(school=school, is_active=True):
        location_assets = Asset.objects.filter(school=school, location=location, status='active')
        if location_assets.exists():
            assigned_count = location_assets.filter(assigned_to__isnull=False).count()
            utilization_rate = (assigned_count / location_assets.count() * 100) if location_assets.count() > 0 else 0
            
            location_utilization.append({
                'location': location,
                'total_assets': location_assets.count(),
                'assigned_assets': assigned_count,
                'utilization_rate': utilization_rate
            })
    
    # Sort by utilization rate
    location_utilization.sort(key=lambda x: x['utilization_rate'], reverse=True)
    
    context = {
        'start_date': start_date,
        'end_date': end_date,
        'utilization_data': utilization_data,
        'underutilized_assets': underutilized_assets,
        'location_utilization': location_utilization,
    }
    
    return render(request, 'inventory/asset_utilization_report.html', context)


@login_required
@school_required
def asset_depreciation_report(request):
    """Asset depreciation analysis and reporting"""
    school = request.school
    
    # Get depreciation analysis
    depreciation_data = AssetAnalyticsService.calculate_depreciation_analysis(school)
    
    # Get assets by depreciation method
    depreciation_methods = Asset.objects.filter(
        school=school, status__in=['active', 'maintenance']
    ).values('depreciation_method').annotate(
        count=Count('id'),
        total_value=Sum('purchase_price'),
        avg_depreciation=Avg('purchase_price') - Avg('current_value')
    ).order_by('-count')
    
    # Get assets with high depreciation
    high_depreciation_assets = []
    for asset in Asset.objects.filter(school=school, status__in=['active', 'maintenance']):
        depreciation_rate = (asset.accumulated_depreciation / asset.purchase_price * 100) if asset.purchase_price > 0 else 0
        if depreciation_rate > 50:  # More than 50% depreciated
            high_depreciation_assets.append({
                'asset': asset,
                'depreciation_rate': depreciation_rate
            })
    
    # Sort by depreciation rate
    high_depreciation_assets.sort(key=lambda x: x['depreciation_rate'], reverse=True)
    high_depreciation_assets = high_depreciation_assets[:20]  # Top 20
    
    # Get replacement candidates (assets with high depreciation and poor condition)
    replacement_candidates = Asset.objects.filter(
        school=school,
        status='active',
        condition__in=['poor', 'damaged']
    ).select_related('category', 'location')
    
    # Filter by age and depreciation
    replacement_list = []
    for asset in replacement_candidates:
        if asset.age_in_years > 5:  # Older than 5 years
            depreciation_rate = (asset.accumulated_depreciation / asset.purchase_price * 100) if asset.purchase_price > 0 else 0
            if depreciation_rate > 70:  # More than 70% depreciated
                replacement_list.append({
                    'asset': asset,
                    'depreciation_rate': depreciation_rate,
                    'age': asset.age_in_years
                })
    
    replacement_list.sort(key=lambda x: (x['depreciation_rate'], x['age']), reverse=True)
    
    context = {
        'depreciation_data': depreciation_data,
        'depreciation_methods': depreciation_methods,
        'high_depreciation_assets': high_depreciation_assets,
        'replacement_candidates': replacement_list[:15],  # Top 15 candidates
    }
    
    return render(request, 'inventory/asset_depreciation_report.html', context)


@login_required
@school_required
def asset_replacement_planning(request):
    """Asset replacement planning dashboard"""
    school = request.school
    
    # Get assets approaching end of useful life
    approaching_eol = []
    for asset in Asset.objects.filter(school=school, status='active'):
        remaining_life = asset.useful_life_years - asset.age_in_years
        if remaining_life <= 2 and remaining_life > 0:  # Within 2 years of end of life
            approaching_eol.append({
                'asset': asset,
                'remaining_life': remaining_life,
                'replacement_cost_estimate': asset.purchase_price * Decimal('1.1')  # 10% inflation estimate
            })
    
    approaching_eol.sort(key=lambda x: x['remaining_life'])
    
    # Get assets by replacement priority
    replacement_priority = []
    for asset in Asset.objects.filter(school=school, status='active'):
        priority_score = 0
        
        # Age factor (older = higher priority)
        if asset.age_in_years > asset.useful_life_years:
            priority_score += 50
        elif asset.age_in_years > asset.useful_life_years * 0.8:
            priority_score += 30
        
        # Condition factor
        condition_scores = {'poor': 40, 'damaged': 50, 'fair': 20, 'good': 10, 'excellent': 0}
        priority_score += condition_scores.get(asset.condition, 0)
        
        # Maintenance frequency factor
        maintenance_count = asset.maintenance_records.filter(
            scheduled_date__gte=timezone.now().date() - timezone.timedelta(days=365)
        ).count()
        if maintenance_count > 4:  # More than 4 maintenances per year
            priority_score += 20
        
        # Depreciation factor
        depreciation_rate = (asset.accumulated_depreciation / asset.purchase_price * 100) if asset.purchase_price > 0 else 0
        if depreciation_rate > 80:
            priority_score += 30
        
        if priority_score > 50:  # Only include high priority assets
            replacement_priority.append({
                'asset': asset,
                'priority_score': priority_score,
                'estimated_cost': asset.purchase_price * Decimal('1.1')
            })
    
    replacement_priority.sort(key=lambda x: x['priority_score'], reverse=True)
    
    # Calculate total replacement budget needed
    total_replacement_cost = sum(item['estimated_cost'] for item in replacement_priority[:10])  # Top 10
    
    context = {
        'approaching_eol': approaching_eol[:15],
        'replacement_priority': replacement_priority[:15],
        'total_replacement_cost': total_replacement_cost,
    }
    
    return render(request, 'inventory/asset_replacement_planning.html', context)


# Asset Analytics Views

@login_required
@school_required
def asset_analytics_dashboard(request):
    """Asset analytics dashboard"""
    school = request.school
    
    # Get utilization metrics
    utilization_data = AssetAnalyticsService.calculate_asset_utilization(
        school, timezone.now().date().replace(day=1), timezone.now().date()
    )
    
    # Get depreciation analysis
    depreciation_data = AssetAnalyticsService.calculate_depreciation_analysis(school)
    
    # Get asset distribution
    distribution_data = AssetAnalyticsService.get_asset_distribution(school)
    
    # Get replacement planning data
    assets_needing_replacement = Asset.objects.filter(
        school=school,
        status='active'
    ).annotate(
        age=timezone.now().date().year - Extract('purchase_date', 'year')
    ).filter(age__gte=F('useful_life_years')).count()
    
    # Get top assets by value
    top_assets_by_value = Asset.objects.filter(
        school=school,
        status='active'
    ).order_by('-purchase_price')[:10]
    
    # Get maintenance due assets
    maintenance_due_count = Asset.objects.filter(
        school=school,
        status='active',
        next_maintenance_date__lte=timezone.now().date()
    ).count()
    
    context = {
        'utilization_data': utilization_data,
        'depreciation_data': depreciation_data,
        'distribution_data': distribution_data,
        'assets_needing_replacement': assets_needing_replacement,
        'top_assets_by_value': top_assets_by_value,
        'maintenance_due_count': maintenance_due_count,
    }
    
    return render(request, 'inventory/asset_analytics_dashboard.html', context)


@login_required
@school_required
def asset_utilization_report(request):
    """Asset utilization report"""
    school = request.school
    
    # Get date range from request
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    
    if not start_date:
        start_date = timezone.now().date().replace(day=1)  # First day of current month
    else:
        start_date = timezone.datetime.strptime(start_date, '%Y-%m-%d').date()
    
    if not end_date:
        end_date = timezone.now().date()
    else:
        end_date = timezone.datetime.strptime(end_date, '%Y-%m-%d').date()
    
    # Get utilization data
    utilization_data = AssetAnalyticsService.calculate_asset_utilization(school, start_date, end_date)
    
    # Get underutilized assets (not assigned for more than 30 days)
    underutilized_assets = Asset.objects.filter(
        school=school,
        status='active',
        assigned_to__isnull=True
    ).select_related('category', 'location')
    
    # Get overutilized assets (assets with high maintenance frequency)
    overutilized_assets = Asset.objects.filter(
        school=school,
        status='active'
    ).annotate(
        maintenance_count=Count('maintenance_records', filter=Q(
            maintenance_records__completed_date__gte=start_date,
            maintenance_records__completed_date__lte=end_date
        ))
    ).filter(maintenance_count__gte=3).select_related('category', 'location')
    
    context = {
        'start_date': start_date,
        'end_date': end_date,
        'utilization_data': utilization_data,
        'underutilized_assets': underutilized_assets,
        'overutilized_assets': overutilized_assets,
    }
    
    return render(request, 'inventory/asset_utilization_report.html', context)


@login_required
@school_required
def asset_depreciation_report(request):
    """Asset depreciation tracking report"""
    school = request.school
    
    # Get depreciation analysis
    depreciation_data = AssetAnalyticsService.calculate_depreciation_analysis(school)
    
    # Get assets by depreciation method
    depreciation_methods = Asset.objects.filter(
        school=school,
        status__in=['active', 'maintenance']
    ).values('depreciation_method').annotate(
        count=Count('id'),
        total_value=Sum('purchase_price'),
        total_depreciation=Sum('purchase_price') - Sum('current_value')
    ).order_by('-count')
    
    # Get assets nearing full depreciation
    fully_depreciated_assets = Asset.objects.filter(
        school=school,
        status__in=['active', 'maintenance']
    ).annotate(
        depreciation_percentage=Case(
            When(purchase_price=0, then=0),
            default=(F('purchase_price') - F('current_value')) * 100.0 / F('purchase_price'),
            output_field=DecimalField(max_digits=5, decimal_places=2)
        )
    ).filter(depreciation_percentage__gte=80).select_related('category', 'location')
    
    # Get monthly depreciation trends
    monthly_depreciation = AssetDepreciation.objects.filter(
        school=school,
        calculation_date__gte=timezone.now().date() - timezone.timedelta(days=365)
    ).extra(
        select={'month': "DATE_FORMAT(calculation_date, '%%Y-%%m')"}
    ).values('month').annotate(
        total_depreciation=Sum('depreciation_amount'),
        asset_count=Count('asset', distinct=True)
    ).order_by('month')
    
    context = {
        'depreciation_data': depreciation_data,
        'depreciation_methods': depreciation_methods,
        'fully_depreciated_assets': fully_depreciated_assets,
        'monthly_depreciation': monthly_depreciation,
    }
    
    return render(request, 'inventory/asset_depreciation_report.html', context)


@login_required
@school_required
def asset_performance_metrics(request):
    """Asset performance metrics and KPIs"""
    school = request.school
    
    # Calculate key performance indicators
    total_assets = Asset.objects.filter(school=school, status='active').count()
    
    # Asset availability (not under maintenance)
    available_assets = Asset.objects.filter(school=school, status='active').count()
    availability_rate = (available_assets / total_assets * 100) if total_assets > 0 else 0
    
    # Mean Time Between Failures (MTBF) - simplified calculation
    corrective_maintenance = AssetMaintenance.objects.filter(
        school=school,
        maintenance_type='corrective',
        status='completed',
        completed_date__gte=timezone.now().date() - timezone.timedelta(days=365)
    ).count()
    
    mtbf_days = 365 / corrective_maintenance if corrective_maintenance > 0 else 365
    
    # Mean Time To Repair (MTTR) - average days between scheduled and completed
    maintenance_records = AssetMaintenance.objects.filter(
        school=school,
        status='completed',
        completed_date__gte=timezone.now().date() - timezone.timedelta(days=365)
    ).exclude(completed_date__isnull=True)
    
    total_repair_time = 0
    repair_count = 0
    for record in maintenance_records:
        if record.completed_date and record.scheduled_date:
            repair_time = (record.completed_date - record.scheduled_date).days
            if repair_time >= 0:  # Only count positive repair times
                total_repair_time += repair_time
                repair_count += 1
    
    mttr_days = total_repair_time / repair_count if repair_count > 0 else 0
    
    # Overall Equipment Effectiveness (OEE) - simplified
    # OEE = Availability × Performance × Quality
    # For simplicity, we'll use availability as the main factor
    oee_percentage = availability_rate * 0.85  # Assuming 85% performance and quality
    
    # Asset reliability by category
    reliability_by_category = Asset.objects.filter(
        school=school,
        status='active'
    ).values('category__name').annotate(
        total_assets=Count('id'),
        maintenance_count=Count('maintenance_records', filter=Q(
            maintenance_records__maintenance_type='corrective',
            maintenance_records__completed_date__gte=timezone.now().date() - timezone.timedelta(days=365)
        )),
        reliability_score=Case(
            When(maintenance_count=0, then=100),
            default=100 - (F('maintenance_count') * 10),
            output_field=DecimalField(max_digits=5, decimal_places=2)
        )
    ).order_by('-reliability_score')
    
    # Cost efficiency metrics
    maintenance_costs = AssetMaintenance.objects.filter(
        school=school,
        status='completed',
        completed_date__gte=timezone.now().date() - timezone.timedelta(days=365)
    ).aggregate(total_cost=Sum('cost'))['total_cost'] or Decimal('0.00')
    
    asset_values = Asset.objects.filter(
        school=school,
        status='active'
    ).aggregate(total_value=Sum('purchase_price'))['total_value'] or Decimal('0.00')
    
    maintenance_cost_ratio = (maintenance_costs / asset_values * 100) if asset_values > 0 else 0
    
    context = {
        'total_assets': total_assets,
        'availability_rate': round(availability_rate, 2),
        'mtbf_days': round(mtbf_days, 1),
        'mttr_days': round(mttr_days, 1),
        'oee_percentage': round(oee_percentage, 2),
        'reliability_by_category': reliability_by_category,
        'maintenance_costs': maintenance_costs,
        'maintenance_cost_ratio': round(float(maintenance_cost_ratio), 2),
    }
    
    return render(request, 'inventory/asset_performance_metrics.html', context)


@login_required
@school_required
def replacement_planning_tools(request):
    """Asset replacement planning tools"""
    school = request.school
    
    # Assets approaching end of useful life
    approaching_eol = Asset.objects.filter(
        school=school,
        status='active'
    ).annotate(
        age=timezone.now().date().year - Extract('purchase_date', 'year'),
        remaining_life=F('useful_life_years') - F('age')
    ).filter(remaining_life__lte=2, remaining_life__gt=0).select_related('category', 'location')
    
    # Assets past useful life
    past_eol = Asset.objects.filter(
        school=school,
        status='active'
    ).annotate(
        age=timezone.now().date().year - Extract('purchase_date', 'year')
    ).filter(age__gte=F('useful_life_years')).select_related('category', 'location')
    
    # High maintenance cost assets
    high_maintenance_assets = Asset.objects.filter(
        school=school,
        status='active'
    ).annotate(
        annual_maintenance_cost=Sum(
            'maintenance_records__cost',
            filter=Q(
                maintenance_records__completed_date__gte=timezone.now().date() - timezone.timedelta(days=365)
            )
        )
    ).filter(annual_maintenance_cost__gt=0).order_by('-annual_maintenance_cost')[:20]
    
    # Replacement cost estimates by category
    replacement_estimates = Asset.objects.filter(
        school=school,
        status='active'
    ).values('category__name').annotate(
        asset_count=Count('id'),
        avg_age=Avg(timezone.now().date().year - Extract('purchase_date', 'year')),
        total_replacement_cost=Sum('purchase_price'),  # Simplified - using original cost
        assets_due_replacement=Count(
            'id',
            filter=Q(purchase_date__year__lte=timezone.now().date().year - F('useful_life_years'))
        )
    ).order_by('-total_replacement_cost')
    
    # Budget planning - next 5 years
    budget_planning = []
    for year in range(1, 6):
        target_year = timezone.now().date().year + year
        assets_due = Asset.objects.filter(
            school=school,
            status='active',
            purchase_date__year__lte=target_year - F('useful_life_years')
        ).aggregate(
            count=Count('id'),
            estimated_cost=Sum('purchase_price')
        )
        
        budget_planning.append({
            'year': target_year,
            'assets_due': assets_due['count'] or 0,
            'estimated_cost': assets_due['estimated_cost'] or Decimal('0.00')
        })
    
    context = {
        'approaching_eol': approaching_eol,
        'past_eol': past_eol,
        'high_maintenance_assets': high_maintenance_assets,
        'replacement_estimates': replacement_estimates,
        'budget_planning': budget_planning,
    }
    
    return render(request, 'inventory/replacement_planning_tools.html', context)


@login_required
@school_required
def asset_dashboards(request):
    """Comprehensive asset dashboards"""
    school = request.school
    
    # Executive summary metrics
    total_assets = Asset.objects.filter(school=school).count()
    active_assets = Asset.objects.filter(school=school, status='active').count()
    maintenance_assets = Asset.objects.filter(school=school, status='maintenance').count()
    retired_assets = Asset.objects.filter(school=school, status='retired').count()
    
    # Financial metrics
    total_asset_value = Asset.objects.filter(
        school=school,
        status__in=['active', 'maintenance']
    ).aggregate(total=Sum('purchase_price'))['total'] or Decimal('0.00')
    
    current_asset_value = sum(
        asset.book_value for asset in Asset.objects.filter(
            school=school,
            status__in=['active', 'maintenance']
        )
    )
    
    # Maintenance metrics
    maintenance_due = Asset.objects.filter(
        school=school,
        status='active',
        next_maintenance_date__lte=timezone.now().date()
    ).count()
    
    monthly_maintenance_cost = AssetMaintenance.objects.filter(
        school=school,
        status='completed',
        completed_date__gte=timezone.now().date().replace(day=1)
    ).aggregate(total=Sum('cost'))['total'] or Decimal('0.00')
    
    # Utilization metrics
    assigned_assets = Asset.objects.filter(
        school=school,
        status='active',
        assigned_to__isnull=False
    ).count()
    
    utilization_rate = (assigned_assets / active_assets * 100) if active_assets > 0 else 0
    
    # Recent activities
    recent_acquisitions = Asset.objects.filter(
        school=school,
        created_at__gte=timezone.now() - timezone.timedelta(days=30)
    ).count()
    
    recent_maintenance = AssetMaintenance.objects.filter(
        school=school,
        completed_date__gte=timezone.now().date() - timezone.timedelta(days=30)
    ).count()
    
    # Trends data for charts
    monthly_trends = Asset.objects.filter(
        school=school,
        created_at__gte=timezone.now() - timezone.timedelta(days=365)
    ).extra(
        select={'month': "DATE_FORMAT(created_at, '%%Y-%%m')"}
    ).values('month').annotate(
        acquisitions=Count('id'),
        total_value=Sum('purchase_price')
    ).order_by('month')
    
    context = {
        'total_assets': total_assets,
        'active_assets': active_assets,
        'maintenance_assets': maintenance_assets,
        'retired_assets': retired_assets,
        'total_asset_value': total_asset_value,
        'current_asset_value': current_asset_value,
        'maintenance_due': maintenance_due,
        'monthly_maintenance_cost': monthly_maintenance_cost,
        'utilization_rate': round(utilization_rate, 2),
        'recent_acquisitions': recent_acquisitions,
        'recent_maintenance': recent_maintenance,
        'monthly_trends': monthly_trends,
    }
    
    return render(request, 'inventory/asset_dashboards.html', context)
