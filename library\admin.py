from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import (
    Category, Author, Publisher, Book, BookCopy, 
    DigitalResource, LibrarySettings, BookBorrowing,
    BorrowingHistory, BorrowingReminder, BorrowingAnalytics,
    DigitalResourceUsage, DigitalResourceIssue, DigitalResourceLoan,
    DigitalResourceCollection, DigitalResourceCollectionItem
)


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'name_ar', 'parent', 'color_display', 'is_active']
    list_filter = ['parent', 'is_active', 'created_at']
    search_fields = ['name', 'name_ar', 'code', 'description']
    ordering = ['code', 'name']
    list_editable = ['is_active']
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('name', 'name_ar', 'code', 'description', 'parent')
        }),
        (_('Display'), {
            'fields': ('color',)
        }),
        (_('Status'), {
            'fields': ('is_active',)
        }),
    )
    
    def color_display(self, obj):
        return format_html(
            '<div style="width: 20px; height: 20px; background-color: {}; border: 1px solid #ccc;"></div>',
            obj.color
        )
    color_display.short_description = _('Color')
    
    def save_model(self, request, obj, form, change):
        if not change:  # Only set school for new objects
            school = None
            
            # Try different ways to get the school
            if hasattr(request.user, 'employee') and hasattr(request.user.employee, 'school'):
                school = request.user.employee.school
            elif hasattr(request.user, 'teacher_profile') and hasattr(request.user.teacher_profile, 'school'):
                school = request.user.teacher_profile.school
            elif hasattr(request, 'school'):
                school = request.school
            
            # If no school found, get the first available school as fallback
            if not school:
                from core.models import School
                school = School.objects.first()
            
            if school:
                obj.school = school
        super().save_model(request, obj, form, change)


@admin.register(Author)
class AuthorAdmin(admin.ModelAdmin):
    list_display = ['full_name', 'full_name_ar', 'nationality', 'birth_date', 'book_count', 'is_active']
    list_filter = ['nationality', 'birth_date', 'is_active', 'created_at']
    search_fields = ['first_name', 'last_name', 'first_name_ar', 'last_name_ar', 'biography']
    ordering = ['last_name', 'first_name']
    list_editable = ['is_active']
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('first_name', 'last_name', 'first_name_ar', 'last_name_ar')
        }),
        (_('Details'), {
            'fields': ('biography', 'nationality', 'birth_date', 'death_date')
        }),
        (_('Media'), {
            'fields': ('photo',)
        }),
        (_('Status'), {
            'fields': ('is_active',)
        }),
    )
    
    def book_count(self, obj):
        return obj.books.count()
    book_count.short_description = _('Books')
    
    def save_model(self, request, obj, form, change):
        if not change:  # Only set school for new objects
            school = None
            
            # Try different ways to get the school
            if hasattr(request.user, 'employee') and hasattr(request.user.employee, 'school'):
                school = request.user.employee.school
            elif hasattr(request.user, 'teacher_profile') and hasattr(request.user.teacher_profile, 'school'):
                school = request.user.teacher_profile.school
            elif hasattr(request, 'school'):
                school = request.school
            
            # If no school found, get the first available school as fallback
            if not school:
                from core.models import School
                school = School.objects.first()
            
            if school:
                obj.school = school
        super().save_model(request, obj, form, change)


@admin.register(Publisher)
class PublisherAdmin(admin.ModelAdmin):
    list_display = ['name', 'name_ar', 'established_year', 'phone', 'email', 'book_count', 'is_active']
    list_filter = ['established_year', 'is_active', 'created_at']
    search_fields = ['name', 'name_ar', 'address', 'email']
    ordering = ['name']
    list_editable = ['is_active']
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('name', 'name_ar', 'established_year')
        }),
        (_('Contact Information'), {
            'fields': ('address', 'phone', 'email', 'website')
        }),
        (_('Status'), {
            'fields': ('is_active',)
        }),
    )
    
    def book_count(self, obj):
        return obj.books.count()
    book_count.short_description = _('Books')
    
    def save_model(self, request, obj, form, change):
        if not change:  # Only set school for new objects
            school = None
            
            # Try different ways to get the school
            if hasattr(request.user, 'employee') and hasattr(request.user.employee, 'school'):
                school = request.user.employee.school
            elif hasattr(request.user, 'teacher_profile') and hasattr(request.user.teacher_profile, 'school'):
                school = request.user.teacher_profile.school
            elif hasattr(request, 'school'):
                school = request.school
            
            # If no school found, get the first available school as fallback
            if not school:
                from core.models import School
                school = School.objects.first()
            
            if school:
                obj.school = school
        super().save_model(request, obj, form, change)


class BookCopyInline(admin.TabularInline):
    model = BookCopy
    extra = 0
    fields = ['copy_number', 'barcode', 'condition', 'status', 'location']
    readonly_fields = ['barcode']


@admin.register(Book)
class BookAdmin(admin.ModelAdmin):
    list_display = [
        'title', 'get_authors_display', 'category', 'isbn', 'barcode', 
        'total_copies', 'available_copies', 'status', 'is_active'
    ]
    list_filter = [
        'category', 'publisher', 'format_type', 'language', 'status', 
        'acquisition_method', 'is_active', 'acquisition_date'
    ]
    search_fields = [
        'title', 'title_ar', 'isbn', 'barcode', 'call_number', 
        'description', 'keywords', 'authors__first_name', 'authors__last_name'
    ]
    ordering = ['title']
    list_editable = ['status', 'is_active']
    filter_horizontal = ['authors']
    inlines = [BookCopyInline]
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('title', 'title_ar', 'subtitle', 'authors', 'publisher', 'category')
        }),
        (_('Identification'), {
            'fields': ('isbn', 'barcode', 'rfid_tag', 'call_number')
        }),
        (_('Publication Details'), {
            'fields': ('publication_date', 'edition', 'pages', 'language', 'format_type')
        }),
        (_('Physical Details'), {
            'fields': ('dimensions', 'weight', 'location')
        }),
        (_('Content'), {
            'fields': ('description', 'table_of_contents', 'keywords')
        }),
        (_('Digital Content'), {
            'fields': ('cover_image', 'digital_file', 'preview_url')
        }),
        (_('Inventory'), {
            'fields': ('total_copies', 'available_copies', 'status')
        }),
        (_('Acquisition'), {
            'fields': ('acquisition_date', 'acquisition_method', 'cost', 'vendor')
        }),
        (_('Ratings'), {
            'fields': ('rating', 'review_count')
        }),
        (_('Metadata'), {
            'fields': ('last_inventory_date', 'notes', 'is_active')
        }),
    )
    
    readonly_fields = ['barcode', 'acquisition_date']
    
    def get_authors_display(self, obj):
        return obj.get_authors_display()
    get_authors_display.short_description = _('Authors')
    
    def save_model(self, request, obj, form, change):
        if not change:  # Only set school for new objects
            # Get school from user's profile or set a default
            school = None
            
            # Try different ways to get the school
            if hasattr(request.user, 'employee') and hasattr(request.user.employee, 'school'):
                school = request.user.employee.school
            elif hasattr(request.user, 'teacher_profile') and hasattr(request.user.teacher_profile, 'school'):
                school = request.user.teacher_profile.school
            elif hasattr(request, 'school'):
                school = request.school
            
            # If no school found, get the first available school as fallback
            if not school:
                from core.models import School
                school = School.objects.first()
            
            if school:
                obj.school = school
            else:
                # Create a default school if none exists
                from core.models import School
                school = School.objects.create(
                    name='Default School',
                    code='DEFAULT',
                    address='Default Address',
                    phone='************',
                    email='<EMAIL>'
                )
                obj.school = school
        super().save_model(request, obj, form, change)
    
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        # Filter by user's school if available
        if hasattr(request.user, 'employee') and request.user.employee.school:
            qs = qs.filter(school=request.user.employee.school)
        elif hasattr(request.user, 'teacher_profile') and request.user.teacher_profile.school:
            qs = qs.filter(school=request.user.teacher_profile.school)
        return qs


@admin.register(BookCopy)
class BookCopyAdmin(admin.ModelAdmin):
    list_display = ['book', 'copy_number', 'barcode', 'condition', 'status', 'location', 'is_active']
    list_filter = ['condition', 'status', 'acquisition_date', 'is_active']
    search_fields = ['book__title', 'copy_number', 'barcode', 'rfid_tag', 'location']
    ordering = ['book', 'copy_number']
    list_editable = ['condition', 'status', 'is_active']
    
    fieldsets = (
        (_('Book Information'), {
            'fields': ('book', 'copy_number')
        }),
        (_('Identification'), {
            'fields': ('barcode', 'rfid_tag')
        }),
        (_('Status'), {
            'fields': ('condition', 'status', 'location')
        }),
        (_('Metadata'), {
            'fields': ('acquisition_date', 'last_checked', 'notes', 'is_active')
        }),
    )
    
    readonly_fields = ['barcode']


@admin.register(DigitalResource)
class DigitalResourceAdmin(admin.ModelAdmin):
    list_display = [
        'title', 'get_authors_display', 'resource_type', 'file_format', 
        'file_size_mb', 'access_type', 'download_count', 'is_active'
    ]
    list_filter = [
        'resource_type', 'file_format', 'access_type', 'language', 
        'category', 'is_active', 'created_at'
    ]
    search_fields = [
        'title', 'title_ar', 'description', 'keywords', 
        'authors__first_name', 'authors__last_name'
    ]
    ordering = ['title']
    list_editable = ['access_type', 'is_active']
    filter_horizontal = ['authors']
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('title', 'title_ar', 'authors', 'category')
        }),
        (_('Resource Details'), {
            'fields': ('resource_type', 'file_format', 'file_size', 'duration')
        }),
        (_('Content'), {
            'fields': ('description', 'keywords', 'language', 'publication_date')
        }),
        (_('Digital Rights'), {
            'fields': ('access_type', 'license_type', 'copyright_info')
        }),
        (_('Files'), {
            'fields': ('file_path', 'external_url', 'thumbnail')
        }),
        (_('Usage Statistics'), {
            'fields': ('download_count', 'view_count', 'last_accessed', 'rating')
        }),
        (_('Status'), {
            'fields': ('is_active',)
        }),
    )
    
    readonly_fields = ['download_count', 'view_count', 'last_accessed']
    
    def get_authors_display(self, obj):
        return obj.get_authors_display()
    get_authors_display.short_description = _('Authors')
    
    def file_size_mb(self, obj):
        return obj.file_size_mb
    file_size_mb.short_description = _('Size (MB)')


@admin.register(LibrarySettings)
class LibrarySettingsAdmin(admin.ModelAdmin):
    list_display = ['library_name', 'library_name_ar', 'max_books_per_student', 'default_loan_period_days']
    
    fieldsets = (
        (_('General Settings'), {
            'fields': ('library_name', 'library_name_ar')
        }),
        (_('Borrowing Rules'), {
            'fields': (
                'max_books_per_student', 'max_books_per_teacher', 
                'default_loan_period_days', 'max_renewals'
            )
        }),
        (_('Fines and Fees'), {
            'fields': (
                'fine_per_day', 'max_fine_amount', 'replacement_cost_multiplier'
            )
        }),
        (_('Notifications'), {
            'fields': (
                'reminder_days_before_due', 'overdue_notice_frequency'
            )
        }),
        (_('Digital Library'), {
            'fields': (
                'enable_digital_library', 'max_concurrent_digital_loans', 
                'digital_loan_period_days'
            )
        }),
        (_('Operating Hours'), {
            'fields': ('opening_time', 'closing_time')
        }),
        (_('Contact Information'), {
            'fields': ('librarian_name', 'contact_email', 'contact_phone')
        }),
    )
    
    def has_add_permission(self, request):
        # Only allow one settings record per school
        if self.model.objects.filter(school=request.user.employee.school if hasattr(request.user, 'employee') else None).exists():
            return False
        return super().has_add_permission(request)


class BorrowingHistoryInline(admin.TabularInline):
    model = BorrowingHistory
    extra = 0
    fields = ['action', 'action_date', 'performed_by', 'notes']
    readonly_fields = ['action_date']


@admin.register(BookBorrowing)
class BookBorrowingAdmin(admin.ModelAdmin):
    list_display = [
        'borrower_name', 'book', 'borrower_type', 'borrow_date', 
        'due_date', 'status', 'days_overdue_display', 'fine_amount', 'is_active'
    ]
    list_filter = [
        'status', 'borrower_type', 'borrow_date', 'due_date', 
        'fine_paid', 'fine_waived', 'is_active'
    ]
    search_fields = [
        'borrower_name', 'borrower_id', 'borrower_email', 
        'book__title', 'book__isbn', 'book__barcode'
    ]
    ordering = ['-borrow_date']
    list_editable = ['status', 'fine_amount', 'is_active']
    inlines = [BorrowingHistoryInline]
    
    fieldsets = (
        (_('Book Information'), {
            'fields': ('book', 'book_copy')
        }),
        (_('Borrower Information'), {
            'fields': (
                'borrower_type', 'borrower_id', 'borrower_name', 
                'borrower_email', 'borrower_phone'
            )
        }),
        (_('Borrowing Details'), {
            'fields': (
                'borrow_date', 'due_date', 'return_date', 'status'
            )
        }),
        (_('Renewal Information'), {
            'fields': ('renewal_count', 'max_renewals')
        }),
        (_('Fine Information'), {
            'fields': ('fine_amount', 'fine_paid', 'fine_waived')
        }),
        (_('Staff Information'), {
            'fields': ('issued_by', 'returned_to')
        }),
        (_('Reminders'), {
            'fields': ('reminder_sent_count', 'last_reminder_sent')
        }),
        (_('Additional Information'), {
            'fields': ('notes', 'is_active')
        }),
    )
    
    readonly_fields = ['borrow_date', 'reminder_sent_count', 'last_reminder_sent']
    
    def days_overdue_display(self, obj):
        if obj.is_overdue:
            return f"{obj.days_overdue} days"
        return "-"
    days_overdue_display.short_description = _('Days Overdue')
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('book', 'book_copy', 'issued_by', 'returned_to')


@admin.register(BorrowingHistory)
class BorrowingHistoryAdmin(admin.ModelAdmin):
    list_display = [
        'borrowing', 'action', 'action_date', 'performed_by'
    ]
    list_filter = ['action', 'action_date']
    search_fields = [
        'borrowing__borrower_name', 'borrowing__book__title', 
        'notes', 'performed_by__username'
    ]
    ordering = ['-action_date']
    
    fieldsets = (
        (_('Borrowing Information'), {
            'fields': ('borrowing', 'action', 'action_date')
        }),
        (_('Details'), {
            'fields': ('performed_by', 'notes', 'old_value', 'new_value')
        }),
    )
    
    readonly_fields = ['action_date']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('borrowing', 'performed_by')


@admin.register(BorrowingReminder)
class BorrowingReminderAdmin(admin.ModelAdmin):
    list_display = [
        'borrowing', 'reminder_type', 'scheduled_date', 'sent_date', 
        'status', 'delivery_method', 'retry_count'
    ]
    list_filter = [
        'reminder_type', 'status', 'delivery_method', 
        'scheduled_date', 'sent_date'
    ]
    search_fields = [
        'borrowing__borrower_name', 'recipient_email', 
        'message_subject', 'error_message'
    ]
    ordering = ['-scheduled_date']
    list_editable = ['status']
    
    fieldsets = (
        (_('Borrowing Information'), {
            'fields': ('borrowing', 'reminder_type')
        }),
        (_('Scheduling'), {
            'fields': ('scheduled_date', 'sent_date', 'status')
        }),
        (_('Recipient Information'), {
            'fields': ('recipient_email', 'recipient_phone', 'delivery_method')
        }),
        (_('Message Content'), {
            'fields': ('message_subject', 'message_body')
        }),
        (_('Delivery Status'), {
            'fields': ('error_message', 'retry_count')
        }),
    )
    
    readonly_fields = ['sent_date']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('borrowing')


@admin.register(BorrowingAnalytics)
class BorrowingAnalyticsAdmin(admin.ModelAdmin):
    list_display = [
        'school', 'date_from', 'date_to', 'total_borrowings', 
        'total_returns', 'total_overdue', 'total_fines', 'generated_at'
    ]
    list_filter = ['date_from', 'date_to', 'generated_at']
    search_fields = ['school__name']
    ordering = ['-generated_at']
    
    fieldsets = (
        (_('Date Range'), {
            'fields': ('date_from', 'date_to', 'generated_at')
        }),
        (_('Borrowing Statistics'), {
            'fields': (
                'total_borrowings', 'total_returns', 'total_renewals', 
                'total_overdue', 'total_lost'
            )
        }),
        (_('Fine Statistics'), {
            'fields': ('total_fines', 'fines_collected', 'fines_waived')
        }),
        (_('Detailed Data'), {
            'fields': ('popular_books', 'borrower_stats', 'category_stats'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ['generated_at']


@admin.register(DigitalResourceUsage)
class DigitalResourceUsageAdmin(admin.ModelAdmin):
    list_display = [
        'resource', 'user', 'action', 'session_duration', 
        'device_type', 'created_at'
    ]
    list_filter = ['action', 'device_type', 'created_at']
    search_fields = [
        'resource__title', 'user__username', 'user__first_name', 
        'user__last_name', 'ip_address'
    ]
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'ip_address', 'user_agent']
    
    fieldsets = (
        (_('Usage Information'), {
            'fields': ('resource', 'user', 'action', 'session_duration')
        }),
        (_('Technical Details'), {
            'fields': ('device_type', 'ip_address', 'user_agent', 'referrer')
        }),
        (_('Metadata'), {
            'fields': ('created_at',)
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('resource', 'user')


@admin.register(DigitalResourceIssue)
class DigitalResourceIssueAdmin(admin.ModelAdmin):
    list_display = [
        'resource', 'issue_type', 'reported_by', 'status', 
        'priority', 'assigned_to', 'created_at'
    ]
    list_filter = ['issue_type', 'status', 'priority', 'created_at']
    search_fields = [
        'resource__title', 'reported_by__username', 
        'description', 'resolution_notes'
    ]
    ordering = ['-created_at']
    list_editable = ['status', 'priority', 'assigned_to']
    
    fieldsets = (
        (_('Issue Information'), {
            'fields': ('resource', 'reported_by', 'issue_type', 'description')
        }),
        (_('Status & Assignment'), {
            'fields': ('status', 'priority', 'assigned_to')
        }),
        (_('Resolution'), {
            'fields': ('resolution_notes', 'resolved_at')
        }),
    )
    
    readonly_fields = ['created_at', 'resolved_at']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'resource', 'reported_by', 'assigned_to'
        )


@admin.register(DigitalResourceLoan)
class DigitalResourceLoanAdmin(admin.ModelAdmin):
    list_display = [
        'resource', 'borrower', 'loan_date', 'due_date', 
        'status', 'access_count', 'days_remaining_display'
    ]
    list_filter = ['status', 'auto_return', 'loan_date', 'due_date']
    search_fields = [
        'resource__title', 'borrower__username', 
        'borrower__first_name', 'borrower__last_name'
    ]
    ordering = ['-loan_date']
    list_editable = ['status']
    
    fieldsets = (
        (_('Loan Information'), {
            'fields': ('resource', 'borrower', 'loan_date', 'due_date')
        }),
        (_('Status & Settings'), {
            'fields': ('status', 'auto_return')
        }),
        (_('Usage Tracking'), {
            'fields': ('access_count', 'last_accessed', 'return_date')
        }),
    )
    
    readonly_fields = ['loan_date', 'access_count', 'last_accessed', 'return_date']
    
    def days_remaining_display(self, obj):
        if obj.status == 'active':
            return f"{obj.days_remaining} days"
        return "-"
    days_remaining_display.short_description = _('Days Remaining')
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('resource', 'borrower')


class DigitalResourceCollectionItemInline(admin.TabularInline):
    model = DigitalResourceCollectionItem
    extra = 0
    fields = ['resource', 'order', 'notes', 'added_by']
    readonly_fields = ['added_by']


@admin.register(DigitalResourceCollection)
class DigitalResourceCollectionAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'collection_type', 'curator', 'resource_count_display', 
        'is_public', 'is_featured', 'view_count', 'sort_order'
    ]
    list_filter = ['collection_type', 'is_public', 'is_featured', 'created_at']
    search_fields = ['name', 'name_ar', 'description']
    ordering = ['sort_order', 'name']
    list_editable = ['is_public', 'is_featured', 'sort_order']
    inlines = [DigitalResourceCollectionItemInline]
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('name', 'name_ar', 'description', 'collection_type')
        }),
        (_('Settings'), {
            'fields': ('curator', 'is_public', 'is_featured', 'sort_order')
        }),
        (_('Media'), {
            'fields': ('cover_image',)
        }),
        (_('Statistics'), {
            'fields': ('view_count',)
        }),
    )
    
    readonly_fields = ['view_count']
    
    def resource_count_display(self, obj):
        return obj.resource_count
    resource_count_display.short_description = _('Resources')
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('curator')


# Customize admin site
admin.site.site_header = _('School ERP - Library Management')
admin.site.site_title = _('Library Admin')
admin.site.index_title = _('Library Administration')
