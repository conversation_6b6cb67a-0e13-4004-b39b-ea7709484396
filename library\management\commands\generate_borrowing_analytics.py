from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db.models import Count, Sum, Q
from datetime import timedelta
from library.models import BookBorrowing, BorrowingAnalytics


class Command(BaseCommand):
    help = 'Generate borrowing analytics for reporting'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='Number of days to analyze (default: 30)',
        )
        parser.add_argument(
            '--school-id',
            type=str,
            help='Generate analytics for specific school only',
        )

    def handle(self, *args, **options):
        days = options['days']
        school_id = options.get('school_id')
        
        date_to = timezone.now().date()
        date_from = date_to - timedelta(days=days)
        
        from core.models import School
        schools = School.objects.all()
        if school_id:
            schools = schools.filter(id=school_id)
        
        for school in schools:
            self.stdout.write(f'Generating analytics for {school.name}...')
            
            # Get borrowings in date range
            borrowings = BookBorrowing.objects.filter(
                school=school,
                borrow_date__date__range=[date_from, date_to]
            )
            
            # Calculate statistics
            stats = {
                'total_borrowings': borrowings.count(),
                'total_returns': borrowings.filter(status='returned').count(),
                'total_renewals': borrowings.aggregate(
                    total=Sum('renewal_count')
                )['total'] or 0,
                'total_overdue': borrowings.filter(status='overdue').count(),
                'total_lost': borrowings.filter(status='lost').count(),
                'total_fines': borrowings.aggregate(
                    total=Sum('fine_amount')
                )['total'] or 0,
                'fines_collected': borrowings.filter(
                    fine_paid=True
                ).aggregate(
                    total=Sum('fine_amount')
                )['total'] or 0,
                'fines_waived': borrowings.filter(
                    fine_waived=True
                ).aggregate(
                    total=Sum('fine_amount')
                )['total'] or 0,
            }
            
            # Popular books
            popular_books = list(borrowings.values(
                'book__title', 'book__id'
            ).annotate(
                borrow_count=Count('id')
            ).order_by('-borrow_count')[:10])
            
            # Borrower statistics
            borrower_stats = dict(borrowings.values('borrower_type').annotate(
                count=Count('id')
            ).values_list('borrower_type', 'count'))
            
            # Category statistics
            category_stats = dict(borrowings.filter(
                book__category__isnull=False
            ).values(
                'book__category__name'
            ).annotate(
                count=Count('id')
            ).values_list('book__category__name', 'count')[:10])
            
            # Create or update analytics record
            analytics, created = BorrowingAnalytics.objects.update_or_create(
                school=school,
                date_from=date_from,
                date_to=date_to,
                defaults={
                    'total_borrowings': stats['total_borrowings'],
                    'total_returns': stats['total_returns'],
                    'total_renewals': stats['total_renewals'],
                    'total_overdue': stats['total_overdue'],
                    'total_lost': stats['total_lost'],
                    'total_fines': stats['total_fines'],
                    'fines_collected': stats['fines_collected'],
                    'fines_waived': stats['fines_waived'],
                    'popular_books': popular_books,
                    'borrower_stats': borrower_stats,
                    'category_stats': category_stats,
                    'created_by_id': 1,  # System user
                }
            )
            
            action = 'Created' if created else 'Updated'
            self.stdout.write(
                self.style.SUCCESS(
                    f'{action} analytics for {school.name}: '
                    f'{stats["total_borrowings"]} borrowings, '
                    f'{stats["total_returns"]} returns'
                )
            )