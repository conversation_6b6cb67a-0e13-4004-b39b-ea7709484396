from django.core.management.base import BaseCommand
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
from datetime import timedelta
from library.models import BookBorrowing, BorrowingReminder, LibrarySettings


class Command(BaseCommand):
    help = 'Send automated library reminders for due and overdue books'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be sent without actually sending',
        )
        parser.add_argument(
            '--school-id',
            type=str,
            help='Send reminders for specific school only',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        school_id = options.get('school_id')
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No emails will be sent')
            )
        
        # Get pending reminders
        reminders = BorrowingReminder.objects.filter(
            status='pending',
            scheduled_date__lte=timezone.now()
        )
        
        if school_id:
            reminders = reminders.filter(school_id=school_id)
        
        sent_count = 0
        failed_count = 0
        
        for reminder in reminders:
            try:
                if not dry_run:
                    success = self.send_reminder(reminder)
                    if success:
                        reminder.mark_sent()
                        sent_count += 1
                    else:
                        reminder.mark_failed('Failed to send email')
                        failed_count += 1
                else:
                    self.stdout.write(
                        f"Would send: {reminder.reminder_type} to {reminder.recipient_email}"
                    )
                    sent_count += 1
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error processing reminder {reminder.id}: {str(e)}')
                )
                if not dry_run:
                    reminder.mark_failed(str(e))
                failed_count += 1
        
        # Create new reminders for books due soon
        self.create_due_soon_reminders(dry_run, school_id)
        
        # Create overdue reminders
        self.create_overdue_reminders(dry_run, school_id)
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Processed reminders: {sent_count} sent, {failed_count} failed'
            )
        )

    def send_reminder(self, reminder):
        """Send email reminder"""
        try:
            send_mail(
                subject=reminder.message_subject,
                message=reminder.message_body,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[reminder.recipient_email],
                fail_silently=False,
            )
            return True
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to send email: {str(e)}')
            )
            return False

    def create_due_soon_reminders(self, dry_run, school_id=None):
        """Create reminders for books due soon"""
        from core.models import School
        
        schools = School.objects.all()
        if school_id:
            schools = schools.filter(id=school_id)
        
        for school in schools:
            try:
                settings_obj = LibrarySettings.objects.get(school=school)
                reminder_days = settings_obj.reminder_days_before_due
            except LibrarySettings.DoesNotExist:
                reminder_days = 3  # Default
            
            if reminder_days <= 0:
                continue
            
            # Find books due in reminder_days
            due_date = timezone.now().date() + timedelta(days=reminder_days)
            
            borrowings = BookBorrowing.objects.filter(
                school=school,
                status__in=['active', 'renewed'],
                due_date=due_date,
                borrower_email__isnull=False
            ).exclude(
                reminders__reminder_type='due_soon',
                reminders__status__in=['sent', 'pending']
            )
            
            for borrowing in borrowings:
                if not dry_run:
                    BorrowingReminder.objects.create(
                        school=school,
                        borrowing=borrowing,
                        reminder_type='due_soon',
                        scheduled_date=timezone.now(),
                        recipient_email=borrowing.borrower_email,
                        recipient_phone=borrowing.borrower_phone,
                        message_subject=f"Book Due Soon: {borrowing.book.title}",
                        message_body=self.get_due_soon_message(borrowing),
                        created_by_id=1  # System user
                    )
                else:
                    self.stdout.write(
                        f"Would create due soon reminder for: {borrowing.borrower_name} - {borrowing.book.title}"
                    )

    def create_overdue_reminders(self, dry_run, school_id=None):
        """Create reminders for overdue books"""
        from core.models import School
        
        schools = School.objects.all()
        if school_id:
            schools = schools.filter(id=school_id)
        
        for school in schools:
            try:
                settings_obj = LibrarySettings.objects.get(school=school)
                notice_frequency = settings_obj.overdue_notice_frequency
            except LibrarySettings.DoesNotExist:
                notice_frequency = 7  # Default weekly
            
            # Find overdue books
            borrowings = BookBorrowing.objects.filter(
                school=school,
                status__in=['active', 'overdue', 'renewed'],
                due_date__lt=timezone.now().date(),
                borrower_email__isnull=False
            )
            
            for borrowing in borrowings:
                # Check if we should send a reminder based on frequency
                days_overdue = borrowing.days_overdue
                
                # Send reminder every notice_frequency days
                if days_overdue % notice_frequency == 0:
                    # Check if we already sent a reminder today
                    existing_reminder = BorrowingReminder.objects.filter(
                        borrowing=borrowing,
                        reminder_type='overdue',
                        scheduled_date__date=timezone.now().date()
                    ).exists()
                    
                    if not existing_reminder:
                        reminder_type = 'final_notice' if days_overdue >= 30 else 'overdue'
                        
                        if not dry_run:
                            BorrowingReminder.objects.create(
                                school=school,
                                borrowing=borrowing,
                                reminder_type=reminder_type,
                                scheduled_date=timezone.now(),
                                recipient_email=borrowing.borrower_email,
                                recipient_phone=borrowing.borrower_phone,
                                message_subject=f"Overdue Book: {borrowing.book.title}",
                                message_body=self.get_overdue_message(borrowing),
                                created_by_id=1  # System user
                            )
                        else:
                            self.stdout.write(
                                f"Would create overdue reminder for: {borrowing.borrower_name} - {borrowing.book.title} ({days_overdue} days)"
                            )

    def get_due_soon_message(self, borrowing):
        """Generate due soon message"""
        return f"""
Dear {borrowing.borrower_name},

This is a friendly reminder that your borrowed book is due soon:

Book: {borrowing.book.title}
Due Date: {borrowing.due_date.strftime('%B %d, %Y')}
Days Remaining: {(borrowing.due_date - timezone.now().date()).days}

Please return the book on time to avoid late fees.

You can renew the book if you need more time (subject to renewal limits).

Thank you,
{borrowing.school.name} Library
        """.strip()

    def get_overdue_message(self, borrowing):
        """Generate overdue message"""
        fine_amount = borrowing.calculate_fine()
        
        return f"""
Dear {borrowing.borrower_name},

Your borrowed book is now overdue:

Book: {borrowing.book.title}
Due Date: {borrowing.due_date.strftime('%B %d, %Y')}
Days Overdue: {borrowing.days_overdue}
Current Fine: ${fine_amount:.2f}

Please return the book as soon as possible to avoid additional charges.

If you have any questions, please contact the library.

Thank you,
{borrowing.school.name} Library
        """.strip()