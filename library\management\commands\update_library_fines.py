from django.core.management.base import BaseCommand
from django.utils import timezone
from library.models import BookBorrowing


class Command(BaseCommand):
    help = 'Update fines for overdue library books'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without actually updating',
        )
        parser.add_argument(
            '--school-id',
            type=str,
            help='Update fines for specific school only',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        school_id = options.get('school_id')
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No fines will be updated')
            )
        
        # Get overdue borrowings
        overdue_borrowings = BookBorrowing.objects.filter(
            status__in=['active', 'overdue', 'renewed'],
            due_date__lt=timezone.now().date()
        )
        
        if school_id:
            overdue_borrowings = overdue_borrowings.filter(school_id=school_id)
        
        updated_count = 0
        total_fines = 0
        
        for borrowing in overdue_borrowings:
            old_fine = borrowing.fine_amount
            new_fine = borrowing.calculate_fine()
            
            if old_fine != new_fine:
                if not dry_run:
                    borrowing.fine_amount = new_fine
                    if borrowing.status != 'overdue':
                        borrowing.status = 'overdue'
                    borrowing.save(update_fields=['fine_amount', 'status'])
                    updated_count += 1
                else:
                    self.stdout.write(
                        f"Would update: {borrowing.borrower_name} - {borrowing.book.title} "
                        f"Fine: ${old_fine:.2f} -> ${new_fine:.2f}"
                    )
                    updated_count += 1
                
                total_fines += new_fine
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Updated {updated_count} borrowings. Total fines: ${total_fines:.2f}'
            )
        )