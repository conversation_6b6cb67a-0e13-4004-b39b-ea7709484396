# Generated by Django 5.2.4 on 2025-08-03 14:01

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Author",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "first_name",
                    models.CharField(max_length=100, verbose_name="First Name"),
                ),
                (
                    "last_name",
                    models.Char<PERSON><PERSON>(max_length=100, verbose_name="Last Name"),
                ),
                (
                    "first_name_ar",
                    models.Char<PERSON>ield(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="First Name (Arabic)",
                    ),
                ),
                (
                    "last_name_ar",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="Last Name (Arabic)",
                    ),
                ),
                ("biography", models.TextField(blank=True, verbose_name="Biography")),
                (
                    "birth_date",
                    models.DateField(blank=True, null=True, verbose_name="Birth Date"),
                ),
                (
                    "death_date",
                    models.DateField(blank=True, null=True, verbose_name="Death Date"),
                ),
                (
                    "nationality",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Nationality"
                    ),
                ),
                (
                    "photo",
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to="library/authors/",
                        verbose_name="Photo",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Author",
                "verbose_name_plural": "Authors",
                "ordering": ["last_name", "first_name"],
            },
        ),
        migrations.CreateModel(
            name="Book",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("title", models.CharField(max_length=300, verbose_name="Title")),
                (
                    "title_ar",
                    models.CharField(
                        blank=True,
                        max_length=300,
                        null=True,
                        verbose_name="Title (Arabic)",
                    ),
                ),
                (
                    "subtitle",
                    models.CharField(
                        blank=True, max_length=300, verbose_name="Subtitle"
                    ),
                ),
                (
                    "isbn",
                    models.CharField(
                        blank=True,
                        help_text="10 or 13 digit ISBN",
                        max_length=17,
                        validators=[
                            django.core.validators.RegexValidator(
                                "^(?:\\d{9}[\\dX]|\\d{13})$"
                            )
                        ],
                        verbose_name="ISBN",
                    ),
                ),
                (
                    "barcode",
                    models.CharField(
                        help_text="Unique barcode for physical identification",
                        max_length=50,
                        unique=True,
                        verbose_name="Barcode",
                    ),
                ),
                (
                    "rfid_tag",
                    models.CharField(
                        blank=True,
                        help_text="RFID tag identifier",
                        max_length=50,
                        unique=True,
                        verbose_name="RFID Tag",
                    ),
                ),
                (
                    "call_number",
                    models.CharField(
                        help_text="Library classification number",
                        max_length=50,
                        verbose_name="Call Number",
                    ),
                ),
                (
                    "publication_date",
                    models.DateField(
                        blank=True, null=True, verbose_name="Publication Date"
                    ),
                ),
                (
                    "edition",
                    models.CharField(blank=True, max_length=50, verbose_name="Edition"),
                ),
                (
                    "pages",
                    models.IntegerField(
                        blank=True,
                        null=True,
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="Number of Pages",
                    ),
                ),
                (
                    "language",
                    models.CharField(
                        choices=[
                            ("en", "English"),
                            ("ar", "Arabic"),
                            ("fr", "French"),
                            ("es", "Spanish"),
                            ("other", "Other"),
                        ],
                        default="en",
                        max_length=10,
                        verbose_name="Language",
                    ),
                ),
                (
                    "format_type",
                    models.CharField(
                        choices=[
                            ("hardcover", "Hardcover"),
                            ("paperback", "Paperback"),
                            ("ebook", "E-book"),
                            ("audiobook", "Audiobook"),
                            ("magazine", "Magazine"),
                            ("journal", "Journal"),
                            ("dvd", "DVD"),
                            ("cd", "CD"),
                            ("other", "Other"),
                        ],
                        default="hardcover",
                        max_length=20,
                        verbose_name="Format",
                    ),
                ),
                (
                    "dimensions",
                    models.CharField(
                        blank=True,
                        help_text="Length x Width x Height in cm",
                        max_length=50,
                        verbose_name="Dimensions",
                    ),
                ),
                (
                    "weight",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=6,
                        null=True,
                        verbose_name="Weight (kg)",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                (
                    "table_of_contents",
                    models.TextField(blank=True, verbose_name="Table of Contents"),
                ),
                (
                    "keywords",
                    models.CharField(
                        blank=True,
                        help_text="Comma-separated keywords for search",
                        max_length=500,
                        verbose_name="Keywords",
                    ),
                ),
                (
                    "cover_image",
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to="library/covers/",
                        verbose_name="Cover Image",
                    ),
                ),
                (
                    "digital_file",
                    models.FileField(
                        blank=True,
                        help_text="PDF, EPUB, or other digital format",
                        null=True,
                        upload_to="library/digital/",
                        verbose_name="Digital File",
                    ),
                ),
                (
                    "preview_url",
                    models.URLField(
                        blank=True,
                        help_text="Link to online preview or sample",
                        verbose_name="Preview URL",
                    ),
                ),
                (
                    "total_copies",
                    models.IntegerField(
                        default=1,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="Total Copies",
                    ),
                ),
                (
                    "available_copies",
                    models.IntegerField(
                        default=1,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="Available Copies",
                    ),
                ),
                (
                    "location",
                    models.CharField(
                        blank=True,
                        help_text="Physical location in library (shelf, section, etc.)",
                        max_length=100,
                        verbose_name="Location",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("available", "Available"),
                            ("checked_out", "Checked Out"),
                            ("reserved", "Reserved"),
                            ("damaged", "Damaged"),
                            ("lost", "Lost"),
                            ("repair", "Under Repair"),
                            ("withdrawn", "Withdrawn"),
                        ],
                        default="available",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "acquisition_date",
                    models.DateField(
                        auto_now_add=True, verbose_name="Acquisition Date"
                    ),
                ),
                (
                    "acquisition_method",
                    models.CharField(
                        choices=[
                            ("purchase", "Purchase"),
                            ("donation", "Donation"),
                            ("exchange", "Exchange"),
                            ("gift", "Gift"),
                        ],
                        default="purchase",
                        max_length=20,
                        verbose_name="Acquisition Method",
                    ),
                ),
                (
                    "cost",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="Cost",
                    ),
                ),
                (
                    "vendor",
                    models.CharField(blank=True, max_length=200, verbose_name="Vendor"),
                ),
                (
                    "rating",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=3,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(5),
                        ],
                        verbose_name="Average Rating",
                    ),
                ),
                (
                    "review_count",
                    models.IntegerField(default=0, verbose_name="Review Count"),
                ),
                (
                    "last_inventory_date",
                    models.DateField(
                        blank=True, null=True, verbose_name="Last Inventory Date"
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "authors",
                    models.ManyToManyField(
                        related_name="books",
                        to="library.author",
                        verbose_name="Authors",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Book",
                "verbose_name_plural": "Books",
                "ordering": ["title"],
            },
        ),
        migrations.CreateModel(
            name="BookCopy",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "copy_number",
                    models.CharField(max_length=20, verbose_name="Copy Number"),
                ),
                (
                    "barcode",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="Copy Barcode"
                    ),
                ),
                (
                    "rfid_tag",
                    models.CharField(
                        blank=True, max_length=50, unique=True, verbose_name="RFID Tag"
                    ),
                ),
                (
                    "condition",
                    models.CharField(
                        choices=[
                            ("excellent", "Excellent"),
                            ("good", "Good"),
                            ("fair", "Fair"),
                            ("poor", "Poor"),
                            ("damaged", "Damaged"),
                        ],
                        default="excellent",
                        max_length=20,
                        verbose_name="Condition",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("available", "Available"),
                            ("checked_out", "Checked Out"),
                            ("reserved", "Reserved"),
                            ("damaged", "Damaged"),
                            ("lost", "Lost"),
                            ("repair", "Under Repair"),
                            ("withdrawn", "Withdrawn"),
                        ],
                        default="available",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "location",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Specific Location"
                    ),
                ),
                ("acquisition_date", models.DateField(verbose_name="Acquisition Date")),
                (
                    "last_checked",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Last Checked"
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "book",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="copies",
                        to="library.book",
                        verbose_name="Book",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Book Copy",
                "verbose_name_plural": "Book Copies",
                "ordering": ["book", "copy_number"],
            },
        ),
        migrations.CreateModel(
            name="Category",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "name",
                    models.CharField(max_length=100, verbose_name="Category Name"),
                ),
                (
                    "name_ar",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="Category Name (Arabic)",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                (
                    "code",
                    models.CharField(
                        help_text="Dewey Decimal or custom classification code",
                        max_length=20,
                        verbose_name="Category Code",
                    ),
                ),
                (
                    "color",
                    models.CharField(
                        default="#007bff",
                        help_text="Hex color code for visual identification",
                        max_length=7,
                        validators=[
                            django.core.validators.RegexValidator("^#[0-9A-Fa-f]{6}$")
                        ],
                        verbose_name="Color",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subcategories",
                        to="library.category",
                        verbose_name="Parent Category",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Category",
                "verbose_name_plural": "Categories",
                "ordering": ["code", "name"],
            },
        ),
        migrations.AddField(
            model_name="book",
            name="category",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="books",
                to="library.category",
                verbose_name="Category",
            ),
        ),
        migrations.CreateModel(
            name="DigitalResource",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("title", models.CharField(max_length=300, verbose_name="Title")),
                (
                    "title_ar",
                    models.CharField(
                        blank=True,
                        max_length=300,
                        null=True,
                        verbose_name="Title (Arabic)",
                    ),
                ),
                (
                    "resource_type",
                    models.CharField(
                        choices=[
                            ("ebook", "E-book"),
                            ("audiobook", "Audiobook"),
                            ("video", "Video"),
                            ("audio", "Audio"),
                            ("document", "Document"),
                            ("presentation", "Presentation"),
                            ("image", "Image"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                        verbose_name="Resource Type",
                    ),
                ),
                (
                    "file_format",
                    models.CharField(
                        choices=[
                            ("pdf", "PDF"),
                            ("epub", "EPUB"),
                            ("mobi", "MOBI"),
                            ("mp3", "MP3"),
                            ("mp4", "MP4"),
                            ("avi", "AVI"),
                            ("doc", "DOC"),
                            ("docx", "DOCX"),
                            ("ppt", "PPT"),
                            ("pptx", "PPTX"),
                            ("jpg", "JPG"),
                            ("png", "PNG"),
                            ("other", "Other"),
                        ],
                        max_length=10,
                        verbose_name="File Format",
                    ),
                ),
                (
                    "file_size",
                    models.BigIntegerField(
                        blank=True, null=True, verbose_name="File Size (bytes)"
                    ),
                ),
                (
                    "duration",
                    models.DurationField(
                        blank=True,
                        help_text="For audio/video resources",
                        null=True,
                        verbose_name="Duration",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                (
                    "keywords",
                    models.CharField(
                        blank=True, max_length=500, verbose_name="Keywords"
                    ),
                ),
                (
                    "access_type",
                    models.CharField(
                        choices=[
                            ("open", "Open Access"),
                            ("restricted", "Restricted"),
                            ("licensed", "Licensed"),
                            ("subscription", "Subscription"),
                        ],
                        default="open",
                        max_length=20,
                        verbose_name="Access Type",
                    ),
                ),
                (
                    "license_type",
                    models.CharField(
                        blank=True, max_length=50, verbose_name="License Type"
                    ),
                ),
                (
                    "copyright_info",
                    models.TextField(blank=True, verbose_name="Copyright Information"),
                ),
                (
                    "file_path",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to="library/digital/",
                        verbose_name="File",
                    ),
                ),
                (
                    "external_url",
                    models.URLField(
                        blank=True,
                        help_text="URL if resource is hosted externally",
                        verbose_name="External URL",
                    ),
                ),
                (
                    "thumbnail",
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to="library/thumbnails/",
                        verbose_name="Thumbnail",
                    ),
                ),
                (
                    "download_count",
                    models.IntegerField(default=0, verbose_name="Download Count"),
                ),
                (
                    "view_count",
                    models.IntegerField(default=0, verbose_name="View Count"),
                ),
                (
                    "last_accessed",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Last Accessed"
                    ),
                ),
                (
                    "publication_date",
                    models.DateField(
                        blank=True, null=True, verbose_name="Publication Date"
                    ),
                ),
                (
                    "language",
                    models.CharField(
                        choices=[
                            ("en", "English"),
                            ("ar", "Arabic"),
                            ("fr", "French"),
                            ("es", "Spanish"),
                            ("other", "Other"),
                        ],
                        default="en",
                        max_length=10,
                        verbose_name="Language",
                    ),
                ),
                (
                    "rating",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=3,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(5),
                        ],
                        verbose_name="Average Rating",
                    ),
                ),
                (
                    "authors",
                    models.ManyToManyField(
                        blank=True,
                        related_name="digital_resources",
                        to="library.author",
                        verbose_name="Authors",
                    ),
                ),
                (
                    "category",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="digital_resources",
                        to="library.category",
                        verbose_name="Category",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Digital Resource",
                "verbose_name_plural": "Digital Resources",
                "ordering": ["title"],
            },
        ),
        migrations.CreateModel(
            name="LibrarySettings",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "library_name",
                    models.CharField(max_length=200, verbose_name="Library Name"),
                ),
                (
                    "library_name_ar",
                    models.CharField(
                        blank=True,
                        max_length=200,
                        null=True,
                        verbose_name="Library Name (Arabic)",
                    ),
                ),
                (
                    "max_books_per_student",
                    models.IntegerField(
                        default=3,
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="Max Books per Student",
                    ),
                ),
                (
                    "max_books_per_teacher",
                    models.IntegerField(
                        default=5,
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="Max Books per Teacher",
                    ),
                ),
                (
                    "default_loan_period_days",
                    models.IntegerField(
                        default=14,
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="Default Loan Period (days)",
                    ),
                ),
                (
                    "max_renewals",
                    models.IntegerField(
                        default=2,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="Maximum Renewals",
                    ),
                ),
                (
                    "fine_per_day",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.5,
                        max_digits=5,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="Fine per Day",
                    ),
                ),
                (
                    "max_fine_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=50.0,
                        max_digits=8,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="Maximum Fine Amount",
                    ),
                ),
                (
                    "replacement_cost_multiplier",
                    models.DecimalField(
                        decimal_places=2,
                        default=1.5,
                        max_digits=3,
                        validators=[django.core.validators.MinValueValidator(0.1)],
                        verbose_name="Replacement Cost Multiplier",
                    ),
                ),
                (
                    "reminder_days_before_due",
                    models.IntegerField(
                        default=3,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="Reminder Days Before Due",
                    ),
                ),
                (
                    "overdue_notice_frequency",
                    models.IntegerField(
                        default=7,
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="Overdue Notice Frequency (days)",
                    ),
                ),
                (
                    "enable_digital_library",
                    models.BooleanField(
                        default=True, verbose_name="Enable Digital Library"
                    ),
                ),
                (
                    "max_concurrent_digital_loans",
                    models.IntegerField(
                        default=5,
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="Max Concurrent Digital Loans",
                    ),
                ),
                (
                    "digital_loan_period_days",
                    models.IntegerField(
                        default=7,
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="Digital Loan Period (days)",
                    ),
                ),
                (
                    "opening_time",
                    models.TimeField(default="08:00", verbose_name="Opening Time"),
                ),
                (
                    "closing_time",
                    models.TimeField(default="16:00", verbose_name="Closing Time"),
                ),
                (
                    "librarian_name",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Librarian Name"
                    ),
                ),
                (
                    "contact_email",
                    models.EmailField(
                        blank=True, max_length=254, verbose_name="Contact Email"
                    ),
                ),
                (
                    "contact_phone",
                    models.CharField(
                        blank=True,
                        max_length=20,
                        validators=[
                            django.core.validators.RegexValidator("^\\+?1?\\d{9,15}$")
                        ],
                        verbose_name="Contact Phone",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Library Settings",
                "verbose_name_plural": "Library Settings",
            },
        ),
        migrations.CreateModel(
            name="Publisher",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "name",
                    models.CharField(max_length=200, verbose_name="Publisher Name"),
                ),
                (
                    "name_ar",
                    models.CharField(
                        blank=True,
                        max_length=200,
                        null=True,
                        verbose_name="Publisher Name (Arabic)",
                    ),
                ),
                ("address", models.TextField(blank=True, verbose_name="Address")),
                (
                    "phone",
                    models.CharField(
                        blank=True,
                        max_length=20,
                        validators=[
                            django.core.validators.RegexValidator("^\\+?1?\\d{9,15}$")
                        ],
                        verbose_name="Phone Number",
                    ),
                ),
                (
                    "email",
                    models.EmailField(blank=True, max_length=254, verbose_name="Email"),
                ),
                ("website", models.URLField(blank=True, verbose_name="Website")),
                (
                    "established_year",
                    models.IntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1000),
                            django.core.validators.MaxValueValidator(2100),
                        ],
                        verbose_name="Established Year",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Publisher",
                "verbose_name_plural": "Publishers",
                "ordering": ["name"],
            },
        ),
        migrations.AddField(
            model_name="book",
            name="publisher",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="books",
                to="library.publisher",
                verbose_name="Publisher",
            ),
        ),
        migrations.AddIndex(
            model_name="author",
            index=models.Index(
                fields=["school", "last_name", "first_name"],
                name="library_aut_school__3fc656_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="bookcopy",
            index=models.Index(
                fields=["school", "barcode"], name="library_boo_school__c64a7a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="bookcopy",
            index=models.Index(
                fields=["book", "status"], name="library_boo_book_id_a4d6f3_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="bookcopy",
            unique_together={("book", "copy_number")},
        ),
        migrations.AddIndex(
            model_name="category",
            index=models.Index(
                fields=["school", "code"], name="library_cat_school__ad15db_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="category",
            index=models.Index(
                fields=["school", "parent"], name="library_cat_school__353377_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="category",
            unique_together={("school", "code")},
        ),
        migrations.AddIndex(
            model_name="digitalresource",
            index=models.Index(
                fields=["school", "title"], name="library_dig_school__c7693d_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="digitalresource",
            index=models.Index(
                fields=["school", "resource_type"],
                name="library_dig_school__349219_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="digitalresource",
            index=models.Index(
                fields=["school", "category"], name="library_dig_school__b3c623_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="digitalresource",
            index=models.Index(
                fields=["school", "access_type"], name="library_dig_school__9c653d_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="librarysettings",
            unique_together={("school",)},
        ),
        migrations.AddIndex(
            model_name="publisher",
            index=models.Index(
                fields=["school", "name"], name="library_pub_school__bad406_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="book",
            index=models.Index(
                fields=["school", "title"], name="library_boo_school__0b7edc_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="book",
            index=models.Index(
                fields=["school", "barcode"], name="library_boo_school__79dac1_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="book",
            index=models.Index(
                fields=["school", "isbn"], name="library_boo_school__7923a0_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="book",
            index=models.Index(
                fields=["school", "call_number"], name="library_boo_school__357cd4_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="book",
            index=models.Index(
                fields=["school", "category"], name="library_boo_school__66e220_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="book",
            index=models.Index(
                fields=["school", "status"], name="library_boo_school__ed5702_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="book",
            index=models.Index(
                fields=["school", "acquisition_date"],
                name="library_boo_school__3d06ee_idx",
            ),
        ),
    ]
