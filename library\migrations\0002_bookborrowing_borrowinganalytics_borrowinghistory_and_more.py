# Generated by Django 5.2.4 on 2025-08-03 14:17

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        ("library", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="BookBorrowing",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.Boolean<PERSON>ield(default=True, verbose_name="Is Active"),
                ),
                (
                    "borrower_type",
                    models.Char<PERSON>ield(
                        choices=[
                            ("student", "Student"),
                            ("teacher", "Teacher"),
                            ("staff", "Staff"),
                            ("parent", "Parent"),
                        ],
                        max_length=20,
                        verbose_name="Borrower Type",
                    ),
                ),
                (
                    "borrower_id",
                    models.CharField(
                        help_text="Student ID, Employee ID, etc.",
                        max_length=50,
                        verbose_name="Borrower ID",
                    ),
                ),
                (
                    "borrower_name",
                    models.CharField(max_length=200, verbose_name="Borrower Name"),
                ),
                (
                    "borrower_email",
                    models.EmailField(
                        blank=True, max_length=254, verbose_name="Borrower Email"
                    ),
                ),
                (
                    "borrower_phone",
                    models.CharField(
                        blank=True,
                        max_length=20,
                        validators=[
                            django.core.validators.RegexValidator("^\\+?1?\\d{9,15}$")
                        ],
                        verbose_name="Borrower Phone",
                    ),
                ),
                (
                    "borrow_date",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="Borrow Date"
                    ),
                ),
                ("due_date", models.DateField(verbose_name="Due Date")),
                (
                    "return_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Return Date"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("returned", "Returned"),
                            ("overdue", "Overdue"),
                            ("lost", "Lost"),
                            ("damaged", "Damaged"),
                            ("renewed", "Renewed"),
                        ],
                        default="active",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "renewal_count",
                    models.IntegerField(default=0, verbose_name="Renewal Count"),
                ),
                (
                    "max_renewals",
                    models.IntegerField(default=2, verbose_name="Max Renewals Allowed"),
                ),
                (
                    "fine_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=8,
                        verbose_name="Fine Amount",
                    ),
                ),
                (
                    "fine_paid",
                    models.BooleanField(default=False, verbose_name="Fine Paid"),
                ),
                (
                    "fine_waived",
                    models.BooleanField(default=False, verbose_name="Fine Waived"),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "reminder_sent_count",
                    models.IntegerField(default=0, verbose_name="Reminder Sent Count"),
                ),
                (
                    "last_reminder_sent",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Last Reminder Sent"
                    ),
                ),
                (
                    "book",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="borrowings",
                        to="library.book",
                        verbose_name="Book",
                    ),
                ),
                (
                    "book_copy",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="borrowings",
                        to="library.bookcopy",
                        verbose_name="Book Copy",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "issued_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="issued_borrowings",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Issued By",
                    ),
                ),
                (
                    "returned_to",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="received_returns",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Returned To",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Book Borrowing",
                "verbose_name_plural": "Book Borrowings",
                "ordering": ["-borrow_date"],
            },
        ),
        migrations.CreateModel(
            name="BorrowingAnalytics",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("date_from", models.DateField(verbose_name="Date From")),
                ("date_to", models.DateField(verbose_name="Date To")),
                (
                    "total_borrowings",
                    models.IntegerField(default=0, verbose_name="Total Borrowings"),
                ),
                (
                    "total_returns",
                    models.IntegerField(default=0, verbose_name="Total Returns"),
                ),
                (
                    "total_renewals",
                    models.IntegerField(default=0, verbose_name="Total Renewals"),
                ),
                (
                    "total_overdue",
                    models.IntegerField(default=0, verbose_name="Total Overdue"),
                ),
                (
                    "total_lost",
                    models.IntegerField(default=0, verbose_name="Total Lost"),
                ),
                (
                    "total_fines",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="Total Fines",
                    ),
                ),
                (
                    "fines_collected",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="Fines Collected",
                    ),
                ),
                (
                    "fines_waived",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="Fines Waived",
                    ),
                ),
                (
                    "popular_books",
                    models.JSONField(default=list, verbose_name="Popular Books Data"),
                ),
                (
                    "borrower_stats",
                    models.JSONField(default=dict, verbose_name="Borrower Statistics"),
                ),
                (
                    "category_stats",
                    models.JSONField(default=dict, verbose_name="Category Statistics"),
                ),
                (
                    "generated_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="Generated At"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Borrowing Analytics",
                "verbose_name_plural": "Borrowing Analytics",
                "ordering": ["-generated_at"],
            },
        ),
        migrations.CreateModel(
            name="BorrowingHistory",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "action",
                    models.CharField(
                        choices=[
                            ("borrowed", "Borrowed"),
                            ("returned", "Returned"),
                            ("renewed", "Renewed"),
                            ("overdue", "Marked Overdue"),
                            ("lost", "Marked Lost"),
                            ("damaged", "Marked Damaged"),
                            ("fine_applied", "Fine Applied"),
                            ("fine_paid", "Fine Paid"),
                            ("fine_waived", "Fine Waived"),
                        ],
                        max_length=20,
                        verbose_name="Action",
                    ),
                ),
                (
                    "action_date",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="Action Date"
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                ("old_value", models.TextField(blank=True, verbose_name="Old Value")),
                ("new_value", models.TextField(blank=True, verbose_name="New Value")),
                (
                    "borrowing",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="history",
                        to="library.bookborrowing",
                        verbose_name="Borrowing",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "performed_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Performed By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Borrowing History",
                "verbose_name_plural": "Borrowing Histories",
                "ordering": ["-action_date"],
            },
        ),
        migrations.CreateModel(
            name="BorrowingReminder",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "reminder_type",
                    models.CharField(
                        choices=[
                            ("due_soon", "Due Soon"),
                            ("overdue", "Overdue"),
                            ("final_notice", "Final Notice"),
                        ],
                        max_length=20,
                        verbose_name="Reminder Type",
                    ),
                ),
                ("scheduled_date", models.DateTimeField(verbose_name="Scheduled Date")),
                (
                    "sent_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Sent Date"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("sent", "Sent"),
                            ("failed", "Failed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "recipient_email",
                    models.EmailField(max_length=254, verbose_name="Recipient Email"),
                ),
                (
                    "recipient_phone",
                    models.CharField(
                        blank=True, max_length=20, verbose_name="Recipient Phone"
                    ),
                ),
                (
                    "message_subject",
                    models.CharField(max_length=200, verbose_name="Message Subject"),
                ),
                ("message_body", models.TextField(verbose_name="Message Body")),
                (
                    "delivery_method",
                    models.CharField(
                        choices=[("email", "Email"), ("sms", "SMS"), ("both", "Both")],
                        default="email",
                        max_length=20,
                        verbose_name="Delivery Method",
                    ),
                ),
                (
                    "error_message",
                    models.TextField(blank=True, verbose_name="Error Message"),
                ),
                (
                    "retry_count",
                    models.IntegerField(default=0, verbose_name="Retry Count"),
                ),
                (
                    "borrowing",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reminders",
                        to="library.bookborrowing",
                        verbose_name="Borrowing",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Borrowing Reminder",
                "verbose_name_plural": "Borrowing Reminders",
                "ordering": ["-scheduled_date"],
            },
        ),
        migrations.AddIndex(
            model_name="bookborrowing",
            index=models.Index(
                fields=["school", "borrower_type", "borrower_id"],
                name="library_boo_school__f608ac_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="bookborrowing",
            index=models.Index(
                fields=["school", "status"], name="library_boo_school__5d5442_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="bookborrowing",
            index=models.Index(
                fields=["school", "due_date"], name="library_boo_school__55faf3_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="bookborrowing",
            index=models.Index(
                fields=["book", "status"], name="library_boo_book_id_dc43c3_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="bookborrowing",
            index=models.Index(
                fields=["borrow_date"], name="library_boo_borrow__5073fb_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="borrowinganalytics",
            unique_together={("school", "date_from", "date_to")},
        ),
        migrations.AddIndex(
            model_name="borrowinghistory",
            index=models.Index(
                fields=["borrowing", "action_date"],
                name="library_bor_borrowi_934da2_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="borrowinghistory",
            index=models.Index(
                fields=["school", "action"], name="library_bor_school__ae08f1_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="borrowingreminder",
            index=models.Index(
                fields=["school", "status", "scheduled_date"],
                name="library_bor_school__2e4b44_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="borrowingreminder",
            index=models.Index(
                fields=["borrowing", "reminder_type"],
                name="library_bor_borrowi_bc6c7a_idx",
            ),
        ),
    ]
