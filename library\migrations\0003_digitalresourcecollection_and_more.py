# Generated by Django 5.2.4 on 2025-08-04 08:42

import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        ("library", "0002_bookborrowing_borrowinganalytics_borrowinghistory_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="DigitalResourceCollection",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.Bo<PERSON>an<PERSON>ield(default=True, verbose_name="Is Active"),
                ),
                (
                    "name",
                    models.CharField(max_length=200, verbose_name="Collection Name"),
                ),
                (
                    "name_ar",
                    models.CharField(
                        blank=True,
                        max_length=200,
                        null=True,
                        verbose_name="Collection Name (Arabic)",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                (
                    "collection_type",
                    models.CharField(
                        choices=[
                            ("reading_list", "Reading List"),
                            ("course_materials", "Course Materials"),
                            ("featured", "Featured Collection"),
                            ("new_arrivals", "New Arrivals"),
                            ("popular", "Popular Resources"),
                            ("custom", "Custom Collection"),
                        ],
                        max_length=20,
                        verbose_name="Collection Type",
                    ),
                ),
                (
                    "is_public",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this collection is visible to all users",
                        verbose_name="Is Public",
                    ),
                ),
                (
                    "is_featured",
                    models.BooleanField(
                        default=False,
                        help_text="Show this collection prominently",
                        verbose_name="Is Featured",
                    ),
                ),
                (
                    "cover_image",
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to="library/collections/",
                        verbose_name="Cover Image",
                    ),
                ),
                (
                    "view_count",
                    models.IntegerField(default=0, verbose_name="View Count"),
                ),
                (
                    "sort_order",
                    models.IntegerField(default=0, verbose_name="Sort Order"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "curator",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="curated_collections",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Curator",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Digital Resource Collection",
                "verbose_name_plural": "Digital Resource Collections",
                "ordering": ["sort_order", "name"],
            },
        ),
        migrations.CreateModel(
            name="DigitalResourceCollectionItem",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("order", models.IntegerField(default=0, verbose_name="Order")),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Curator notes about this resource in the collection",
                        verbose_name="Notes",
                    ),
                ),
                (
                    "added_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Added By",
                    ),
                ),
                (
                    "collection",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="library.digitalresourcecollection",
                        verbose_name="Collection",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "resource",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="library.digitalresource",
                        verbose_name="Resource",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Collection Item",
                "verbose_name_plural": "Collection Items",
                "ordering": ["order", "created_at"],
                "unique_together": {("collection", "resource")},
            },
        ),
        migrations.AddField(
            model_name="digitalresourcecollection",
            name="resources",
            field=models.ManyToManyField(
                related_name="collections",
                through="library.DigitalResourceCollectionItem",
                to="library.digitalresource",
                verbose_name="Resources",
            ),
        ),
        migrations.CreateModel(
            name="DigitalResourceIssue",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "issue_type",
                    models.CharField(
                        choices=[
                            ("access", "Access Problem"),
                            ("quality", "Quality Issue"),
                            ("content", "Content Error"),
                            ("technical", "Technical Problem"),
                            ("copyright", "Copyright Concern"),
                            ("general", "General Issue"),
                        ],
                        max_length=20,
                        verbose_name="Issue Type",
                    ),
                ),
                ("description", models.TextField(verbose_name="Description")),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("open", "Open"),
                            ("investigating", "Investigating"),
                            ("resolved", "Resolved"),
                            ("closed", "Closed"),
                        ],
                        default="open",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "resolution_notes",
                    models.TextField(blank=True, verbose_name="Resolution Notes"),
                ),
                (
                    "resolved_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Resolved At"
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("urgent", "Urgent"),
                        ],
                        default="medium",
                        max_length=10,
                        verbose_name="Priority",
                    ),
                ),
                (
                    "assigned_to",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="assigned_issues",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Assigned To",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "reported_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reported_issues",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Reported By",
                    ),
                ),
                (
                    "resource",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="issues",
                        to="library.digitalresource",
                        verbose_name="Digital Resource",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Digital Resource Issue",
                "verbose_name_plural": "Digital Resource Issues",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="DigitalResourceLoan",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "loan_date",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="Loan Date"
                    ),
                ),
                ("due_date", models.DateTimeField(verbose_name="Due Date")),
                (
                    "return_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Return Date"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("expired", "Expired"),
                            ("returned", "Returned"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="active",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "auto_return",
                    models.BooleanField(
                        default=True,
                        help_text="Automatically return when due date is reached",
                        verbose_name="Auto Return",
                    ),
                ),
                (
                    "access_count",
                    models.IntegerField(
                        default=0,
                        help_text="Number of times the resource was accessed during loan",
                        verbose_name="Access Count",
                    ),
                ),
                (
                    "last_accessed",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Last Accessed"
                    ),
                ),
                (
                    "borrower",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="digital_loans",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Borrower",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "resource",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="loans",
                        to="library.digitalresource",
                        verbose_name="Digital Resource",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Digital Resource Loan",
                "verbose_name_plural": "Digital Resource Loans",
                "ordering": ["-loan_date"],
            },
        ),
        migrations.CreateModel(
            name="DigitalResourceUsage",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "action",
                    models.CharField(
                        choices=[
                            ("view", "View"),
                            ("download", "Download"),
                            ("watch", "Watch"),
                            ("listen", "Listen"),
                            ("share", "Share"),
                        ],
                        max_length=20,
                        verbose_name="Action",
                    ),
                ),
                (
                    "session_duration",
                    models.DurationField(
                        blank=True,
                        help_text="How long the user engaged with the resource",
                        null=True,
                        verbose_name="Session Duration",
                    ),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        blank=True, null=True, verbose_name="IP Address"
                    ),
                ),
                ("user_agent", models.TextField(blank=True, verbose_name="User Agent")),
                (
                    "device_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("desktop", "Desktop"),
                            ("tablet", "Tablet"),
                            ("mobile", "Mobile"),
                            ("unknown", "Unknown"),
                        ],
                        max_length=20,
                        verbose_name="Device Type",
                    ),
                ),
                ("referrer", models.URLField(blank=True, verbose_name="Referrer URL")),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "resource",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="usage_logs",
                        to="library.digitalresource",
                        verbose_name="Digital Resource",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "Digital Resource Usage",
                "verbose_name_plural": "Digital Resource Usage",
                "ordering": ["-created_at"],
            },
        ),
        migrations.AddIndex(
            model_name="digitalresourcecollection",
            index=models.Index(
                fields=["school", "collection_type", "is_public"],
                name="library_dig_school__826730_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="digitalresourcecollection",
            index=models.Index(
                fields=["school", "is_featured", "sort_order"],
                name="library_dig_school__77aa45_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="digitalresourceissue",
            index=models.Index(
                fields=["school", "status", "created_at"],
                name="library_dig_school__1d01db_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="digitalresourceissue",
            index=models.Index(
                fields=["school", "resource", "status"],
                name="library_dig_school__1351fa_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="digitalresourceissue",
            index=models.Index(
                fields=["school", "assigned_to", "status"],
                name="library_dig_school__21791e_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="digitalresourceloan",
            index=models.Index(
                fields=["school", "borrower", "status"],
                name="library_dig_school__42d980_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="digitalresourceloan",
            index=models.Index(
                fields=["school", "resource", "status"],
                name="library_dig_school__6b55a8_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="digitalresourceloan",
            index=models.Index(
                fields=["school", "due_date", "status"],
                name="library_dig_school__5310ec_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="digitalresourceusage",
            index=models.Index(
                fields=["school", "resource", "created_at"],
                name="library_dig_school__90bea1_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="digitalresourceusage",
            index=models.Index(
                fields=["school", "user", "created_at"],
                name="library_dig_school__52e196_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="digitalresourceusage",
            index=models.Index(
                fields=["school", "action", "created_at"],
                name="library_dig_school__61d9b0_idx",
            ),
        ),
    ]
