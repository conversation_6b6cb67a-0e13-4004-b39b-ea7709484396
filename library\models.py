from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import RegexValidator, MinValueValidator, MaxValueValidator
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import timedelta
from django.conf import settings
from core.models import BaseModel
import uuid


class Category(BaseModel):
    """
    Book category model for organizing library resources
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('Category Name')
    )
    
    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Category Name (Arabic)')
    )
    
    description = models.TextField(
        blank=True,
        verbose_name=_('Description')
    )
    
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='subcategories',
        verbose_name=_('Parent Category')
    )
    
    code = models.CharField(
        max_length=20,
        verbose_name=_('Category Code'),
        help_text=_('Dewey Decimal or custom classification code')
    )
    
    color = models.CharField(
        max_length=7,
        default='#007bff',
        validators=[RegexValidator(r'^#[0-9A-Fa-f]{6}$')],
        verbose_name=_('Color'),
        help_text=_('Hex color code for visual identification')
    )

    class Meta:
        verbose_name = _('Category')
        verbose_name_plural = _('Categories')
        unique_together = ['school', 'code']
        ordering = ['code', 'name']
        indexes = [
            models.Index(fields=['school', 'code']),
            models.Index(fields=['school', 'parent']),
        ]

    def __str__(self):
        return f"{self.code} - {self.name}"
    
    def get_full_path(self):
        """Get the full category path"""
        if self.parent:
            return f"{self.parent.get_full_path()} > {self.name}"
        return self.name


class Author(BaseModel):
    """
    Author model for book authors
    """
    first_name = models.CharField(
        max_length=100,
        verbose_name=_('First Name')
    )
    
    last_name = models.CharField(
        max_length=100,
        verbose_name=_('Last Name')
    )
    
    first_name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('First Name (Arabic)')
    )
    
    last_name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Last Name (Arabic)')
    )
    
    biography = models.TextField(
        blank=True,
        verbose_name=_('Biography')
    )
    
    birth_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Birth Date')
    )
    
    death_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Death Date')
    )
    
    nationality = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_('Nationality')
    )
    
    photo = models.ImageField(
        upload_to='library/authors/',
        blank=True,
        null=True,
        verbose_name=_('Photo')
    )

    class Meta:
        verbose_name = _('Author')
        verbose_name_plural = _('Authors')
        ordering = ['last_name', 'first_name']
        indexes = [
            models.Index(fields=['school', 'last_name', 'first_name']),
        ]

    def __str__(self):
        return f"{self.first_name} {self.last_name}"
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
    
    @property
    def full_name_ar(self):
        if self.first_name_ar and self.last_name_ar:
            return f"{self.first_name_ar} {self.last_name_ar}"
        return self.full_name
    
    def clean(self):
        if self.birth_date and self.death_date:
            if self.birth_date >= self.death_date:
                raise ValidationError(_('Birth date must be before death date'))


class Publisher(BaseModel):
    """
    Publisher model for book publishers
    """
    name = models.CharField(
        max_length=200,
        verbose_name=_('Publisher Name')
    )
    
    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Publisher Name (Arabic)')
    )
    
    address = models.TextField(
        blank=True,
        verbose_name=_('Address')
    )
    
    phone = models.CharField(
        max_length=20,
        blank=True,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$')],
        verbose_name=_('Phone Number')
    )
    
    email = models.EmailField(
        blank=True,
        verbose_name=_('Email')
    )
    
    website = models.URLField(
        blank=True,
        verbose_name=_('Website')
    )
    
    established_year = models.IntegerField(
        null=True,
        blank=True,
        validators=[MinValueValidator(1000), MaxValueValidator(2100)],
        verbose_name=_('Established Year')
    )

    class Meta:
        verbose_name = _('Publisher')
        verbose_name_plural = _('Publishers')
        ordering = ['name']
        indexes = [
            models.Index(fields=['school', 'name']),
        ]

    def __str__(self):
        return self.name


class Book(BaseModel):
    """
    Main book model for library catalog
    """
    # Basic Information
    title = models.CharField(
        max_length=300,
        verbose_name=_('Title')
    )
    
    title_ar = models.CharField(
        max_length=300,
        blank=True,
        null=True,
        verbose_name=_('Title (Arabic)')
    )
    
    subtitle = models.CharField(
        max_length=300,
        blank=True,
        verbose_name=_('Subtitle')
    )
    
    authors = models.ManyToManyField(
        Author,
        related_name='books',
        verbose_name=_('Authors')
    )
    
    publisher = models.ForeignKey(
        Publisher,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='books',
        verbose_name=_('Publisher')
    )
    
    category = models.ForeignKey(
        Category,
        on_delete=models.SET_NULL,
        null=True,
        related_name='books',
        verbose_name=_('Category')
    )
    
    # Identification
    isbn = models.CharField(
        max_length=17,
        blank=True,
        validators=[RegexValidator(r'^(?:\d{9}[\dX]|\d{13})$')],
        verbose_name=_('ISBN'),
        help_text=_('10 or 13 digit ISBN')
    )
    
    barcode = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('Barcode'),
        help_text=_('Unique barcode for physical identification')
    )
    
    rfid_tag = models.CharField(
        max_length=50,
        blank=True,
        unique=True,
        verbose_name=_('RFID Tag'),
        help_text=_('RFID tag identifier')
    )
    
    call_number = models.CharField(
        max_length=50,
        verbose_name=_('Call Number'),
        help_text=_('Library classification number')
    )
    
    # Publication Details
    publication_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Publication Date')
    )
    
    edition = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_('Edition')
    )
    
    pages = models.IntegerField(
        null=True,
        blank=True,
        validators=[MinValueValidator(1)],
        verbose_name=_('Number of Pages')
    )
    
    language = models.CharField(
        max_length=10,
        default='en',
        verbose_name=_('Language'),
        choices=[
            ('en', _('English')),
            ('ar', _('Arabic')),
            ('fr', _('French')),
            ('es', _('Spanish')),
            ('other', _('Other')),
        ]
    )
    
    # Physical Details
    format_type = models.CharField(
        max_length=20,
        default='hardcover',
        verbose_name=_('Format'),
        choices=[
            ('hardcover', _('Hardcover')),
            ('paperback', _('Paperback')),
            ('ebook', _('E-book')),
            ('audiobook', _('Audiobook')),
            ('magazine', _('Magazine')),
            ('journal', _('Journal')),
            ('dvd', _('DVD')),
            ('cd', _('CD')),
            ('other', _('Other')),
        ]
    )
    
    dimensions = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_('Dimensions'),
        help_text=_('Length x Width x Height in cm')
    )
    
    weight = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_('Weight (kg)')
    )
    
    # Content
    description = models.TextField(
        blank=True,
        verbose_name=_('Description')
    )
    
    table_of_contents = models.TextField(
        blank=True,
        verbose_name=_('Table of Contents')
    )
    
    keywords = models.CharField(
        max_length=500,
        blank=True,
        verbose_name=_('Keywords'),
        help_text=_('Comma-separated keywords for search')
    )
    
    # Digital Content
    cover_image = models.ImageField(
        upload_to='library/covers/',
        blank=True,
        null=True,
        verbose_name=_('Cover Image')
    )
    
    digital_file = models.FileField(
        upload_to='library/digital/',
        blank=True,
        null=True,
        verbose_name=_('Digital File'),
        help_text=_('PDF, EPUB, or other digital format')
    )
    
    preview_url = models.URLField(
        blank=True,
        verbose_name=_('Preview URL'),
        help_text=_('Link to online preview or sample')
    )
    
    # Inventory
    total_copies = models.IntegerField(
        default=1,
        validators=[MinValueValidator(0)],
        verbose_name=_('Total Copies')
    )
    
    available_copies = models.IntegerField(
        default=1,
        validators=[MinValueValidator(0)],
        verbose_name=_('Available Copies')
    )
    
    location = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_('Location'),
        help_text=_('Physical location in library (shelf, section, etc.)')
    )
    
    # Status
    status = models.CharField(
        max_length=20,
        default='available',
        verbose_name=_('Status'),
        choices=[
            ('available', _('Available')),
            ('checked_out', _('Checked Out')),
            ('reserved', _('Reserved')),
            ('damaged', _('Damaged')),
            ('lost', _('Lost')),
            ('repair', _('Under Repair')),
            ('withdrawn', _('Withdrawn')),
        ]
    )
    
    # Acquisition
    acquisition_date = models.DateField(
        auto_now_add=True,
        verbose_name=_('Acquisition Date')
    )
    
    acquisition_method = models.CharField(
        max_length=20,
        default='purchase',
        verbose_name=_('Acquisition Method'),
        choices=[
            ('purchase', _('Purchase')),
            ('donation', _('Donation')),
            ('exchange', _('Exchange')),
            ('gift', _('Gift')),
        ]
    )
    
    cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_('Cost')
    )
    
    vendor = models.CharField(
        max_length=200,
        blank=True,
        verbose_name=_('Vendor')
    )
    
    # Ratings and Reviews
    rating = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(0), MaxValueValidator(5)],
        verbose_name=_('Average Rating')
    )
    
    review_count = models.IntegerField(
        default=0,
        verbose_name=_('Review Count')
    )
    
    # Metadata
    last_inventory_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Last Inventory Date')
    )
    
    notes = models.TextField(
        blank=True,
        verbose_name=_('Notes')
    )

    class Meta:
        verbose_name = _('Book')
        verbose_name_plural = _('Books')
        ordering = ['title']
        indexes = [
            models.Index(fields=['school', 'title']),
            models.Index(fields=['school', 'barcode']),
            models.Index(fields=['school', 'isbn']),
            models.Index(fields=['school', 'call_number']),
            models.Index(fields=['school', 'category']),
            models.Index(fields=['school', 'status']),
            models.Index(fields=['school', 'acquisition_date']),
        ]

    def __str__(self):
        return self.title
    
    def save(self, *args, **kwargs):
        # Generate barcode if not provided
        if not self.barcode:
            self.barcode = self.generate_barcode()
        
        # Ensure available copies doesn't exceed total copies
        if self.available_copies > self.total_copies:
            self.available_copies = self.total_copies
            
        super().save(*args, **kwargs)
    
    def generate_barcode(self):
        """Generate a unique barcode for the book"""
        import random
        import string
        
        # Generate a unique barcode with school prefix
        school_code = self.school.code.upper()[:3]
        random_part = ''.join(random.choices(string.digits, k=8))
        return f"{school_code}{random_part}"
    
    @property
    def is_available(self):
        """Check if book is available for borrowing"""
        return self.status == 'available' and self.available_copies > 0
    
    @property
    def borrowed_copies(self):
        """Get number of currently borrowed copies"""
        return self.total_copies - self.available_copies
    
    def get_authors_display(self):
        """Get comma-separated list of authors"""
        return ', '.join([str(author) for author in self.authors.all()])
    
    def clean(self):
        if self.available_copies > self.total_copies:
            raise ValidationError(_('Available copies cannot exceed total copies'))
        
        if self.isbn:
            # Basic ISBN validation
            isbn_digits = ''.join(filter(str.isdigit, self.isbn.replace('X', '0')))
            if len(isbn_digits) not in [10, 13]:
                raise ValidationError(_('ISBN must be 10 or 13 digits'))


class BookCopy(BaseModel):
    """
    Individual copy of a book for detailed tracking
    """
    book = models.ForeignKey(
        Book,
        on_delete=models.CASCADE,
        related_name='copies',
        verbose_name=_('Book')
    )
    
    copy_number = models.CharField(
        max_length=20,
        verbose_name=_('Copy Number')
    )
    
    barcode = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('Copy Barcode')
    )
    
    rfid_tag = models.CharField(
        max_length=50,
        blank=True,
        unique=True,
        verbose_name=_('RFID Tag')
    )
    
    condition = models.CharField(
        max_length=20,
        default='excellent',
        verbose_name=_('Condition'),
        choices=[
            ('excellent', _('Excellent')),
            ('good', _('Good')),
            ('fair', _('Fair')),
            ('poor', _('Poor')),
            ('damaged', _('Damaged')),
        ]
    )
    
    status = models.CharField(
        max_length=20,
        default='available',
        verbose_name=_('Status'),
        choices=[
            ('available', _('Available')),
            ('checked_out', _('Checked Out')),
            ('reserved', _('Reserved')),
            ('damaged', _('Damaged')),
            ('lost', _('Lost')),
            ('repair', _('Under Repair')),
            ('withdrawn', _('Withdrawn')),
        ]
    )
    
    location = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_('Specific Location')
    )
    
    acquisition_date = models.DateField(
        verbose_name=_('Acquisition Date')
    )
    
    last_checked = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Last Checked')
    )
    
    notes = models.TextField(
        blank=True,
        verbose_name=_('Notes')
    )

    class Meta:
        verbose_name = _('Book Copy')
        verbose_name_plural = _('Book Copies')
        unique_together = ['book', 'copy_number']
        ordering = ['book', 'copy_number']
        indexes = [
            models.Index(fields=['school', 'barcode']),
            models.Index(fields=['book', 'status']),
        ]

    def __str__(self):
        return f"{self.book.title} - Copy {self.copy_number}"
    
    def save(self, *args, **kwargs):
        # Generate barcode if not provided
        if not self.barcode:
            self.barcode = self.generate_copy_barcode()
        super().save(*args, **kwargs)
    
    def generate_copy_barcode(self):
        """Generate a unique barcode for the book copy"""
        return f"{self.book.barcode}-{self.copy_number}"
    
    @property
    def is_available(self):
        """Check if this copy is available for borrowing"""
        return self.status == 'available'


class DigitalResource(BaseModel):
    """
    Digital library resources (e-books, audiobooks, videos, etc.)
    """
    title = models.CharField(
        max_length=300,
        verbose_name=_('Title')
    )
    
    title_ar = models.CharField(
        max_length=300,
        blank=True,
        null=True,
        verbose_name=_('Title (Arabic)')
    )
    
    authors = models.ManyToManyField(
        Author,
        related_name='digital_resources',
        blank=True,
        verbose_name=_('Authors')
    )
    
    category = models.ForeignKey(
        Category,
        on_delete=models.SET_NULL,
        null=True,
        related_name='digital_resources',
        verbose_name=_('Category')
    )
    
    resource_type = models.CharField(
        max_length=20,
        verbose_name=_('Resource Type'),
        choices=[
            ('ebook', _('E-book')),
            ('audiobook', _('Audiobook')),
            ('video', _('Video')),
            ('audio', _('Audio')),
            ('document', _('Document')),
            ('presentation', _('Presentation')),
            ('image', _('Image')),
            ('other', _('Other')),
        ]
    )
    
    file_format = models.CharField(
        max_length=10,
        verbose_name=_('File Format'),
        choices=[
            ('pdf', 'PDF'),
            ('epub', 'EPUB'),
            ('mobi', 'MOBI'),
            ('mp3', 'MP3'),
            ('mp4', 'MP4'),
            ('avi', 'AVI'),
            ('doc', 'DOC'),
            ('docx', 'DOCX'),
            ('ppt', 'PPT'),
            ('pptx', 'PPTX'),
            ('jpg', 'JPG'),
            ('png', 'PNG'),
            ('other', _('Other')),
        ]
    )
    
    file_size = models.BigIntegerField(
        null=True,
        blank=True,
        verbose_name=_('File Size (bytes)')
    )
    
    duration = models.DurationField(
        null=True,
        blank=True,
        verbose_name=_('Duration'),
        help_text=_('For audio/video resources')
    )
    
    description = models.TextField(
        blank=True,
        verbose_name=_('Description')
    )
    
    keywords = models.CharField(
        max_length=500,
        blank=True,
        verbose_name=_('Keywords')
    )
    
    # Digital Rights Management
    access_type = models.CharField(
        max_length=20,
        default='open',
        verbose_name=_('Access Type'),
        choices=[
            ('open', _('Open Access')),
            ('restricted', _('Restricted')),
            ('licensed', _('Licensed')),
            ('subscription', _('Subscription')),
        ]
    )
    
    license_type = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_('License Type')
    )
    
    copyright_info = models.TextField(
        blank=True,
        verbose_name=_('Copyright Information')
    )
    
    # File Storage
    file_path = models.FileField(
        upload_to='library/digital/',
        blank=True,
        null=True,
        verbose_name=_('File')
    )
    
    external_url = models.URLField(
        blank=True,
        verbose_name=_('External URL'),
        help_text=_('URL if resource is hosted externally')
    )
    
    thumbnail = models.ImageField(
        upload_to='library/thumbnails/',
        blank=True,
        null=True,
        verbose_name=_('Thumbnail')
    )
    
    # Usage Tracking
    download_count = models.IntegerField(
        default=0,
        verbose_name=_('Download Count')
    )
    
    view_count = models.IntegerField(
        default=0,
        verbose_name=_('View Count')
    )
    
    last_accessed = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Last Accessed')
    )
    
    # Metadata
    publication_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Publication Date')
    )
    
    language = models.CharField(
        max_length=10,
        default='en',
        verbose_name=_('Language'),
        choices=[
            ('en', _('English')),
            ('ar', _('Arabic')),
            ('fr', _('French')),
            ('es', _('Spanish')),
            ('other', _('Other')),
        ]
    )
    
    rating = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(0), MaxValueValidator(5)],
        verbose_name=_('Average Rating')
    )

    class Meta:
        verbose_name = _('Digital Resource')
        verbose_name_plural = _('Digital Resources')
        ordering = ['title']
        indexes = [
            models.Index(fields=['school', 'title']),
            models.Index(fields=['school', 'resource_type']),
            models.Index(fields=['school', 'category']),
            models.Index(fields=['school', 'access_type']),
        ]

    def __str__(self):
        return self.title
    
    @property
    def file_size_mb(self):
        """Get file size in MB"""
        if self.file_size:
            return round(self.file_size / (1024 * 1024), 2)
        return None
    
    def get_authors_display(self):
        """Get comma-separated list of authors"""
        return ', '.join([str(author) for author in self.authors.all()])


class LibrarySettings(BaseModel):
    """
    Library-specific settings and configuration
    """
    # General Settings
    library_name = models.CharField(
        max_length=200,
        verbose_name=_('Library Name')
    )
    
    library_name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Library Name (Arabic)')
    )
    
    # Borrowing Rules
    max_books_per_student = models.IntegerField(
        default=3,
        validators=[MinValueValidator(1)],
        verbose_name=_('Max Books per Student')
    )
    
    max_books_per_teacher = models.IntegerField(
        default=5,
        validators=[MinValueValidator(1)],
        verbose_name=_('Max Books per Teacher')
    )
    
    default_loan_period_days = models.IntegerField(
        default=14,
        validators=[MinValueValidator(1)],
        verbose_name=_('Default Loan Period (days)')
    )
    
    max_renewals = models.IntegerField(
        default=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('Maximum Renewals')
    )
    
    # Fines and Fees
    fine_per_day = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0.50,
        validators=[MinValueValidator(0)],
        verbose_name=_('Fine per Day')
    )
    
    max_fine_amount = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        default=50.00,
        validators=[MinValueValidator(0)],
        verbose_name=_('Maximum Fine Amount')
    )
    
    replacement_cost_multiplier = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        default=1.50,
        validators=[MinValueValidator(0.1)],
        verbose_name=_('Replacement Cost Multiplier')
    )
    
    # Notification Settings
    reminder_days_before_due = models.IntegerField(
        default=3,
        validators=[MinValueValidator(0)],
        verbose_name=_('Reminder Days Before Due')
    )
    
    overdue_notice_frequency = models.IntegerField(
        default=7,
        validators=[MinValueValidator(1)],
        verbose_name=_('Overdue Notice Frequency (days)')
    )
    
    # Digital Library Settings
    enable_digital_library = models.BooleanField(
        default=True,
        verbose_name=_('Enable Digital Library')
    )
    
    max_concurrent_digital_loans = models.IntegerField(
        default=5,
        validators=[MinValueValidator(1)],
        verbose_name=_('Max Concurrent Digital Loans')
    )
    
    digital_loan_period_days = models.IntegerField(
        default=7,
        validators=[MinValueValidator(1)],
        verbose_name=_('Digital Loan Period (days)')
    )
    
    # Operating Hours
    opening_time = models.TimeField(
        default='08:00',
        verbose_name=_('Opening Time')
    )
    
    closing_time = models.TimeField(
        default='16:00',
        verbose_name=_('Closing Time')
    )
    
    # Contact Information
    librarian_name = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_('Librarian Name')
    )
    
    contact_email = models.EmailField(
        blank=True,
        verbose_name=_('Contact Email')
    )
    
    contact_phone = models.CharField(
        max_length=20,
        blank=True,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$')],
        verbose_name=_('Contact Phone')
    )

    class Meta:
        verbose_name = _('Library Settings')
        verbose_name_plural = _('Library Settings')
        # Ensure only one settings record per school
        unique_together = ['school']

    def __str__(self):
        return f"{self.school.name} - Library Settings"


class DigitalResourceUsage(BaseModel):
    """
    Track usage of digital resources for analytics
    """
    ACTION_CHOICES = [
        ('view', _('View')),
        ('download', _('Download')),
        ('watch', _('Watch')),
        ('listen', _('Listen')),
        ('share', _('Share')),
    ]
    
    resource = models.ForeignKey(
        DigitalResource,
        on_delete=models.CASCADE,
        related_name='usage_logs',
        verbose_name=_('Digital Resource')
    )
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        verbose_name=_('User')
    )
    
    action = models.CharField(
        max_length=20,
        choices=ACTION_CHOICES,
        verbose_name=_('Action')
    )
    
    session_duration = models.DurationField(
        null=True,
        blank=True,
        verbose_name=_('Session Duration'),
        help_text=_('How long the user engaged with the resource')
    )
    
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name=_('IP Address')
    )
    
    user_agent = models.TextField(
        blank=True,
        verbose_name=_('User Agent')
    )
    
    device_type = models.CharField(
        max_length=20,
        blank=True,
        verbose_name=_('Device Type'),
        choices=[
            ('desktop', _('Desktop')),
            ('tablet', _('Tablet')),
            ('mobile', _('Mobile')),
            ('unknown', _('Unknown')),
        ]
    )
    
    referrer = models.URLField(
        blank=True,
        verbose_name=_('Referrer URL')
    )

    class Meta:
        verbose_name = _('Digital Resource Usage')
        verbose_name_plural = _('Digital Resource Usage')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['school', 'resource', 'created_at']),
            models.Index(fields=['school', 'user', 'created_at']),
            models.Index(fields=['school', 'action', 'created_at']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.action} - {self.resource.title}"


class DigitalResourceIssue(BaseModel):
    """
    Track reported issues with digital resources
    """
    ISSUE_TYPE_CHOICES = [
        ('access', _('Access Problem')),
        ('quality', _('Quality Issue')),
        ('content', _('Content Error')),
        ('technical', _('Technical Problem')),
        ('copyright', _('Copyright Concern')),
        ('general', _('General Issue')),
    ]
    
    STATUS_CHOICES = [
        ('open', _('Open')),
        ('investigating', _('Investigating')),
        ('resolved', _('Resolved')),
        ('closed', _('Closed')),
    ]
    
    resource = models.ForeignKey(
        DigitalResource,
        on_delete=models.CASCADE,
        related_name='issues',
        verbose_name=_('Digital Resource')
    )
    
    reported_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='reported_issues',
        verbose_name=_('Reported By')
    )
    
    issue_type = models.CharField(
        max_length=20,
        choices=ISSUE_TYPE_CHOICES,
        verbose_name=_('Issue Type')
    )
    
    description = models.TextField(
        verbose_name=_('Description')
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='open',
        verbose_name=_('Status')
    )
    
    assigned_to = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_issues',
        verbose_name=_('Assigned To')
    )
    
    resolution_notes = models.TextField(
        blank=True,
        verbose_name=_('Resolution Notes')
    )
    
    resolved_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Resolved At')
    )
    
    priority = models.CharField(
        max_length=10,
        choices=[
            ('low', _('Low')),
            ('medium', _('Medium')),
            ('high', _('High')),
            ('urgent', _('Urgent')),
        ],
        default='medium',
        verbose_name=_('Priority')
    )

    class Meta:
        verbose_name = _('Digital Resource Issue')
        verbose_name_plural = _('Digital Resource Issues')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['school', 'status', 'created_at']),
            models.Index(fields=['school', 'resource', 'status']),
            models.Index(fields=['school', 'assigned_to', 'status']),
        ]

    def __str__(self):
        return f"{self.resource.title} - {self.get_issue_type_display()} - {self.status}"


class DigitalResourceLoan(BaseModel):
    """
    Track digital resource loans (for licensed content with loan limits)
    """
    STATUS_CHOICES = [
        ('active', _('Active')),
        ('expired', _('Expired')),
        ('returned', _('Returned')),
        ('cancelled', _('Cancelled')),
    ]
    
    resource = models.ForeignKey(
        DigitalResource,
        on_delete=models.CASCADE,
        related_name='loans',
        verbose_name=_('Digital Resource')
    )
    
    borrower = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='digital_loans',
        verbose_name=_('Borrower')
    )
    
    loan_date = models.DateTimeField(
        default=timezone.now,
        verbose_name=_('Loan Date')
    )
    
    due_date = models.DateTimeField(
        verbose_name=_('Due Date')
    )
    
    return_date = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Return Date')
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='active',
        verbose_name=_('Status')
    )
    
    auto_return = models.BooleanField(
        default=True,
        verbose_name=_('Auto Return'),
        help_text=_('Automatically return when due date is reached')
    )
    
    access_count = models.IntegerField(
        default=0,
        verbose_name=_('Access Count'),
        help_text=_('Number of times the resource was accessed during loan')
    )
    
    last_accessed = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Last Accessed')
    )

    class Meta:
        verbose_name = _('Digital Resource Loan')
        verbose_name_plural = _('Digital Resource Loans')
        ordering = ['-loan_date']
        indexes = [
            models.Index(fields=['school', 'borrower', 'status']),
            models.Index(fields=['school', 'resource', 'status']),
            models.Index(fields=['school', 'due_date', 'status']),
        ]

    def __str__(self):
        return f"{self.borrower.username} - {self.resource.title} - {self.status}"
    
    @property
    def is_overdue(self):
        """Check if the loan is overdue"""
        return self.status == 'active' and timezone.now() > self.due_date
    
    @property
    def days_remaining(self):
        """Get days remaining for the loan"""
        if self.status != 'active':
            return 0
        delta = self.due_date - timezone.now()
        return max(0, delta.days)
    
    def extend_loan(self, days=7):
        """Extend the loan period"""
        if self.status == 'active':
            self.due_date += timedelta(days=days)
            self.save(update_fields=['due_date'])
    
    def return_loan(self):
        """Return the digital resource"""
        self.status = 'returned'
        self.return_date = timezone.now()
        self.save(update_fields=['status', 'return_date'])


class DigitalResourceCollection(BaseModel):
    """
    Collections of digital resources (curated lists, reading lists, etc.)
    """
    COLLECTION_TYPE_CHOICES = [
        ('reading_list', _('Reading List')),
        ('course_materials', _('Course Materials')),
        ('featured', _('Featured Collection')),
        ('new_arrivals', _('New Arrivals')),
        ('popular', _('Popular Resources')),
        ('custom', _('Custom Collection')),
    ]
    
    name = models.CharField(
        max_length=200,
        verbose_name=_('Collection Name')
    )
    
    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Collection Name (Arabic)')
    )
    
    description = models.TextField(
        blank=True,
        verbose_name=_('Description')
    )
    
    collection_type = models.CharField(
        max_length=20,
        choices=COLLECTION_TYPE_CHOICES,
        verbose_name=_('Collection Type')
    )
    
    resources = models.ManyToManyField(
        DigitalResource,
        through='DigitalResourceCollectionItem',
        related_name='collections',
        verbose_name=_('Resources')
    )
    
    curator = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='curated_collections',
        verbose_name=_('Curator')
    )
    
    is_public = models.BooleanField(
        default=True,
        verbose_name=_('Is Public'),
        help_text=_('Whether this collection is visible to all users')
    )
    
    is_featured = models.BooleanField(
        default=False,
        verbose_name=_('Is Featured'),
        help_text=_('Show this collection prominently')
    )
    
    cover_image = models.ImageField(
        upload_to='library/collections/',
        blank=True,
        null=True,
        verbose_name=_('Cover Image')
    )
    
    view_count = models.IntegerField(
        default=0,
        verbose_name=_('View Count')
    )
    
    sort_order = models.IntegerField(
        default=0,
        verbose_name=_('Sort Order')
    )

    class Meta:
        verbose_name = _('Digital Resource Collection')
        verbose_name_plural = _('Digital Resource Collections')
        ordering = ['sort_order', 'name']
        indexes = [
            models.Index(fields=['school', 'collection_type', 'is_public']),
            models.Index(fields=['school', 'is_featured', 'sort_order']),
        ]

    def __str__(self):
        return self.name
    
    @property
    def resource_count(self):
        """Get the number of resources in this collection"""
        return self.resources.filter(is_active=True).count()


class DigitalResourceCollectionItem(BaseModel):
    """
    Through model for digital resource collections
    """
    collection = models.ForeignKey(
        DigitalResourceCollection,
        on_delete=models.CASCADE,
        verbose_name=_('Collection')
    )
    
    resource = models.ForeignKey(
        DigitalResource,
        on_delete=models.CASCADE,
        verbose_name=_('Resource')
    )
    
    order = models.IntegerField(
        default=0,
        verbose_name=_('Order')
    )
    
    notes = models.TextField(
        blank=True,
        verbose_name=_('Notes'),
        help_text=_('Curator notes about this resource in the collection')
    )
    
    added_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Added By')
    )

    class Meta:
        verbose_name = _('Collection Item')
        verbose_name_plural = _('Collection Items')
        ordering = ['order', 'created_at']
        unique_together = ['collection', 'resource']

    def __str__(self):
        return f"{self.collection.name} - {self.resource.title}"


class BookBorrowing(BaseModel):
    """
    Book borrowing/loan tracking model
    """
    BORROWER_TYPE_CHOICES = [
        ('student', _('Student')),
        ('teacher', _('Teacher')),
        ('staff', _('Staff')),
        ('parent', _('Parent')),
    ]
    
    STATUS_CHOICES = [
        ('active', _('Active')),
        ('returned', _('Returned')),
        ('overdue', _('Overdue')),
        ('lost', _('Lost')),
        ('damaged', _('Damaged')),
        ('renewed', _('Renewed')),
    ]
    
    # Book Information
    book = models.ForeignKey(
        Book,
        on_delete=models.CASCADE,
        related_name='borrowings',
        verbose_name=_('Book')
    )
    
    book_copy = models.ForeignKey(
        BookCopy,
        on_delete=models.CASCADE,
        related_name='borrowings',
        null=True,
        blank=True,
        verbose_name=_('Book Copy')
    )
    
    # Borrower Information
    borrower_type = models.CharField(
        max_length=20,
        choices=BORROWER_TYPE_CHOICES,
        verbose_name=_('Borrower Type')
    )
    
    borrower_id = models.CharField(
        max_length=50,
        verbose_name=_('Borrower ID'),
        help_text=_('Student ID, Employee ID, etc.')
    )
    
    borrower_name = models.CharField(
        max_length=200,
        verbose_name=_('Borrower Name')
    )
    
    borrower_email = models.EmailField(
        blank=True,
        verbose_name=_('Borrower Email')
    )
    
    borrower_phone = models.CharField(
        max_length=20,
        blank=True,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$')],
        verbose_name=_('Borrower Phone')
    )
    
    # Borrowing Details
    borrow_date = models.DateTimeField(
        default=timezone.now,
        verbose_name=_('Borrow Date')
    )
    
    due_date = models.DateField(
        verbose_name=_('Due Date')
    )
    
    return_date = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Return Date')
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='active',
        verbose_name=_('Status')
    )
    
    # Renewal Information
    renewal_count = models.IntegerField(
        default=0,
        verbose_name=_('Renewal Count')
    )
    
    max_renewals = models.IntegerField(
        default=2,
        verbose_name=_('Max Renewals Allowed')
    )
    
    # Fine Information
    fine_amount = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        default=0.00,
        verbose_name=_('Fine Amount')
    )
    
    fine_paid = models.BooleanField(
        default=False,
        verbose_name=_('Fine Paid')
    )
    
    fine_waived = models.BooleanField(
        default=False,
        verbose_name=_('Fine Waived')
    )
    
    # Additional Information
    notes = models.TextField(
        blank=True,
        verbose_name=_('Notes')
    )
    
    issued_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='issued_borrowings',
        verbose_name=_('Issued By')
    )
    
    returned_to = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='received_returns',
        verbose_name=_('Returned To')
    )
    
    # Reminder tracking
    reminder_sent_count = models.IntegerField(
        default=0,
        verbose_name=_('Reminder Sent Count')
    )
    
    last_reminder_sent = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Last Reminder Sent')
    )

    class Meta:
        verbose_name = _('Book Borrowing')
        verbose_name_plural = _('Book Borrowings')
        ordering = ['-borrow_date']
        indexes = [
            models.Index(fields=['school', 'borrower_type', 'borrower_id']),
            models.Index(fields=['school', 'status']),
            models.Index(fields=['school', 'due_date']),
            models.Index(fields=['book', 'status']),
            models.Index(fields=['borrow_date']),
        ]

    def __str__(self):
        return f"{self.borrower_name} - {self.book.title} ({self.status})"
    
    def save(self, *args, **kwargs):
        # Set due date if not provided
        if not self.due_date:
            try:
                settings = LibrarySettings.objects.get(school=self.school)
                loan_period = settings.default_loan_period_days
            except LibrarySettings.DoesNotExist:
                loan_period = 14  # Default 14 days
            
            self.due_date = (self.borrow_date + timedelta(days=loan_period)).date()
        
        # Update book copy status
        if self.book_copy and self.status == 'active':
            self.book_copy.status = 'checked_out'
            self.book_copy.save()
        elif self.book_copy and self.status in ['returned', 'lost', 'damaged']:
            if self.status == 'returned':
                self.book_copy.status = 'available'
            else:
                self.book_copy.status = self.status
            self.book_copy.save()
        
        # Update book available copies
        if self.pk is None:  # New borrowing
            if self.book.available_copies > 0:
                self.book.available_copies -= 1
                self.book.save()
        
        super().save(*args, **kwargs)
    
    @property
    def is_overdue(self):
        """Check if the borrowing is overdue"""
        if self.status in ['returned', 'lost', 'damaged']:
            return False
        return timezone.now().date() > self.due_date
    
    @property
    def days_overdue(self):
        """Get number of days overdue"""
        if not self.is_overdue:
            return 0
        return (timezone.now().date() - self.due_date).days
    
    @property
    def can_renew(self):
        """Check if the borrowing can be renewed"""
        return (
            self.status == 'active' and
            self.renewal_count < self.max_renewals and
            not self.is_overdue and
            self.fine_amount == 0
        )
    
    def calculate_fine(self):
        """Calculate fine amount based on overdue days"""
        if not self.is_overdue or self.status in ['returned', 'lost', 'damaged']:
            return 0
        
        try:
            settings = LibrarySettings.objects.get(school=self.school)
            fine_per_day = settings.fine_per_day
            max_fine = settings.max_fine_amount
        except LibrarySettings.DoesNotExist:
            fine_per_day = 0.50
            max_fine = 50.00
        
        total_fine = self.days_overdue * fine_per_day
        return min(total_fine, max_fine)
    
    def update_fine(self):
        """Update the fine amount"""
        self.fine_amount = self.calculate_fine()
        self.save(update_fields=['fine_amount'])
    
    def renew(self, renewed_by=None):
        """Renew the borrowing"""
        if not self.can_renew:
            raise ValidationError(_('This borrowing cannot be renewed'))
        
        try:
            settings = LibrarySettings.objects.get(school=self.school)
            loan_period = settings.default_loan_period_days
        except LibrarySettings.DoesNotExist:
            loan_period = 14
        
        self.due_date = timezone.now().date() + timedelta(days=loan_period)
        self.renewal_count += 1
        self.status = 'renewed'
        
        if renewed_by:
            self.notes += f"\nRenewed by {renewed_by} on {timezone.now().strftime('%Y-%m-%d %H:%M')}"
        
        self.save()
    
    def return_book(self, returned_to=None, condition_notes=''):
        """Mark the book as returned"""
        if self.status in ['returned', 'lost', 'damaged']:
            raise ValidationError(_('This book has already been processed'))
        
        self.return_date = timezone.now()
        self.status = 'returned'
        self.returned_to = returned_to
        
        if condition_notes:
            self.notes += f"\nReturn condition: {condition_notes}"
        
        # Update book available copies
        self.book.available_copies += 1
        self.book.save()
        
        # Update book copy status
        if self.book_copy:
            self.book_copy.status = 'available'
            self.book_copy.save()
        
        self.save()
    
    def mark_lost(self, marked_by=None, notes=''):
        """Mark the book as lost"""
        self.status = 'lost'
        self.return_date = timezone.now()
        
        if marked_by:
            self.notes += f"\nMarked as lost by {marked_by} on {timezone.now().strftime('%Y-%m-%d %H:%M')}"
        
        if notes:
            self.notes += f"\nLost book notes: {notes}"
        
        # Update book copy status
        if self.book_copy:
            self.book_copy.status = 'lost'
            self.book_copy.save()
        
        self.save()


class BorrowingHistory(BaseModel):
    """
    Historical record of all borrowing activities
    """
    ACTION_CHOICES = [
        ('borrowed', _('Borrowed')),
        ('returned', _('Returned')),
        ('renewed', _('Renewed')),
        ('overdue', _('Marked Overdue')),
        ('lost', _('Marked Lost')),
        ('damaged', _('Marked Damaged')),
        ('fine_applied', _('Fine Applied')),
        ('fine_paid', _('Fine Paid')),
        ('fine_waived', _('Fine Waived')),
    ]
    
    borrowing = models.ForeignKey(
        BookBorrowing,
        on_delete=models.CASCADE,
        related_name='history',
        verbose_name=_('Borrowing')
    )
    
    action = models.CharField(
        max_length=20,
        choices=ACTION_CHOICES,
        verbose_name=_('Action')
    )
    
    action_date = models.DateTimeField(
        default=timezone.now,
        verbose_name=_('Action Date')
    )
    
    performed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('Performed By')
    )
    
    notes = models.TextField(
        blank=True,
        verbose_name=_('Notes')
    )
    
    old_value = models.TextField(
        blank=True,
        verbose_name=_('Old Value')
    )
    
    new_value = models.TextField(
        blank=True,
        verbose_name=_('New Value')
    )

    class Meta:
        verbose_name = _('Borrowing History')
        verbose_name_plural = _('Borrowing Histories')
        ordering = ['-action_date']
        indexes = [
            models.Index(fields=['borrowing', 'action_date']),
            models.Index(fields=['school', 'action']),
        ]

    def __str__(self):
        return f"{self.borrowing} - {self.get_action_display()} ({self.action_date})"


class BorrowingReminder(BaseModel):
    """
    Automated reminder system for overdue books
    """
    REMINDER_TYPE_CHOICES = [
        ('due_soon', _('Due Soon')),
        ('overdue', _('Overdue')),
        ('final_notice', _('Final Notice')),
    ]
    
    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('sent', _('Sent')),
        ('failed', _('Failed')),
        ('cancelled', _('Cancelled')),
    ]
    
    borrowing = models.ForeignKey(
        BookBorrowing,
        on_delete=models.CASCADE,
        related_name='reminders',
        verbose_name=_('Borrowing')
    )
    
    reminder_type = models.CharField(
        max_length=20,
        choices=REMINDER_TYPE_CHOICES,
        verbose_name=_('Reminder Type')
    )
    
    scheduled_date = models.DateTimeField(
        verbose_name=_('Scheduled Date')
    )
    
    sent_date = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Sent Date')
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('Status')
    )
    
    recipient_email = models.EmailField(
        verbose_name=_('Recipient Email')
    )
    
    recipient_phone = models.CharField(
        max_length=20,
        blank=True,
        verbose_name=_('Recipient Phone')
    )
    
    message_subject = models.CharField(
        max_length=200,
        verbose_name=_('Message Subject')
    )
    
    message_body = models.TextField(
        verbose_name=_('Message Body')
    )
    
    delivery_method = models.CharField(
        max_length=20,
        choices=[
            ('email', _('Email')),
            ('sms', _('SMS')),
            ('both', _('Both')),
        ],
        default='email',
        verbose_name=_('Delivery Method')
    )
    
    error_message = models.TextField(
        blank=True,
        verbose_name=_('Error Message')
    )
    
    retry_count = models.IntegerField(
        default=0,
        verbose_name=_('Retry Count')
    )

    class Meta:
        verbose_name = _('Borrowing Reminder')
        verbose_name_plural = _('Borrowing Reminders')
        ordering = ['-scheduled_date']
        indexes = [
            models.Index(fields=['school', 'status', 'scheduled_date']),
            models.Index(fields=['borrowing', 'reminder_type']),
        ]

    def __str__(self):
        return f"{self.borrowing.borrower_name} - {self.get_reminder_type_display()}"
    
    def mark_sent(self):
        """Mark reminder as sent"""
        self.status = 'sent'
        self.sent_date = timezone.now()
        self.save()
    
    def mark_failed(self, error_message=''):
        """Mark reminder as failed"""
        self.status = 'failed'
        self.error_message = error_message
        self.retry_count += 1
        self.save()


class BorrowingAnalytics(BaseModel):
    """
    Analytics and reporting data for borrowing patterns
    """
    # Date range for analytics
    date_from = models.DateField(
        verbose_name=_('Date From')
    )
    
    date_to = models.DateField(
        verbose_name=_('Date To')
    )
    
    # Borrowing statistics
    total_borrowings = models.IntegerField(
        default=0,
        verbose_name=_('Total Borrowings')
    )
    
    total_returns = models.IntegerField(
        default=0,
        verbose_name=_('Total Returns')
    )
    
    total_renewals = models.IntegerField(
        default=0,
        verbose_name=_('Total Renewals')
    )
    
    total_overdue = models.IntegerField(
        default=0,
        verbose_name=_('Total Overdue')
    )
    
    total_lost = models.IntegerField(
        default=0,
        verbose_name=_('Total Lost')
    )
    
    # Fine statistics
    total_fines = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.00,
        verbose_name=_('Total Fines')
    )
    
    fines_collected = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.00,
        verbose_name=_('Fines Collected')
    )
    
    fines_waived = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.00,
        verbose_name=_('Fines Waived')
    )
    
    # Popular books data (JSON)
    popular_books = models.JSONField(
        default=list,
        verbose_name=_('Popular Books Data')
    )
    
    # Borrower statistics (JSON)
    borrower_stats = models.JSONField(
        default=dict,
        verbose_name=_('Borrower Statistics')
    )
    
    # Category statistics (JSON)
    category_stats = models.JSONField(
        default=dict,
        verbose_name=_('Category Statistics')
    )
    
    # Generated timestamp
    generated_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Generated At')
    )

    class Meta:
        verbose_name = _('Borrowing Analytics')
        verbose_name_plural = _('Borrowing Analytics')
        ordering = ['-generated_at']
        unique_together = ['school', 'date_from', 'date_to']

    def __str__(self):
        return f"{self.school.name} - Analytics ({self.date_from} to {self.date_to})"


class DigitalResourceBookmark(BaseModel):
    """
    User bookmarks for digital resources
    """
    resource = models.ForeignKey(
        DigitalResource,
        on_delete=models.CASCADE,
        related_name='bookmarks',
        verbose_name=_('Digital Resource')
    )
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        verbose_name=_('User')
    )
    
    notes = models.TextField(
        blank=True,
        verbose_name=_('Notes'),
        help_text=_('Personal notes about this resource')
    )

    class Meta:
        verbose_name = _('Digital Resource Bookmark')
        verbose_name_plural = _('Digital Resource Bookmarks')
        unique_together = ['school', 'resource', 'user']
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['school', 'user', 'created_at']),
            models.Index(fields=['school', 'resource']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.resource.title}"