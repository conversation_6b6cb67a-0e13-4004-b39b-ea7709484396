from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import date, timedelta
from core.models import School
from hr.models import Employee, Department, Position
from .models import (
    Category, Author, Publisher, Book, BookCopy, 
    DigitalResource, LibrarySettings, DigitalResourceUsage,
    DigitalResourceIssue, DigitalResourceLoan, DigitalResourceCollection
)

User = get_user_model()


class LibraryModelTests(TestCase):
    """Test cases for library models"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="123 Test St",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
        
        # Create department and position
        self.department = Department.objects.create(
            school=self.school,
            name="Library",
            code="LIB"
        )
        
        self.position = Position.objects.create(
            school=self.school,
            title="Librarian",
            department=self.department
        )
        
        self.employee = Employee.objects.create(
            school=self.school,
            user=self.user,
            employee_id="EMP001",
            position=self.position,
            hire_date=date.today(),
            salary=Decimal('50000.00'),
            emergency_contact_name="Emergency Contact",
            emergency_contact_phone="+1234567890",
            emergency_contact_relationship="Spouse"
        )
    
    def test_category_creation(self):
        """Test category model creation and methods"""
        category = Category.objects.create(
            school=self.school,
            name="Fiction",
            name_ar="خيال",
            code="FIC",
            description="Fiction books",
            color="#FF5733"
        )
        
        self.assertEqual(str(category), "FIC - Fiction")
        self.assertEqual(category.get_full_path(), "Fiction")
        
        # Test subcategory
        subcategory = Category.objects.create(
            school=self.school,
            name="Science Fiction",
            code="SCI-FI",
            parent=category
        )
        
        self.assertEqual(subcategory.get_full_path(), "Fiction > Science Fiction")
    
    def test_author_creation(self):
        """Test author model creation and properties"""
        author = Author.objects.create(
            school=self.school,
            first_name="John",
            last_name="Doe",
            first_name_ar="جون",
            last_name_ar="دو",
            biography="Test author biography",
            birth_date=date(1980, 1, 1),
            nationality="American"
        )
        
        self.assertEqual(str(author), "John Doe")
        self.assertEqual(author.full_name, "John Doe")
        self.assertEqual(author.full_name_ar, "جون دو")
    
    def test_author_validation(self):
        """Test author model validation"""
        with self.assertRaises(ValidationError):
            author = Author(
                school=self.school,
                first_name="John",
                last_name="Doe",
                birth_date=date(2000, 1, 1),
                death_date=date(1999, 1, 1)  # Death before birth
            )
            author.full_clean()
    
    def test_publisher_creation(self):
        """Test publisher model creation"""
        publisher = Publisher.objects.create(
            school=self.school,
            name="Test Publisher",
            name_ar="ناشر تجريبي",
            address="123 Publisher St",
            phone="+1234567890",
            email="<EMAIL>",
            website="https://publisher.com",
            established_year=1990
        )
        
        self.assertEqual(str(publisher), "Test Publisher")
    
    def test_book_creation(self):
        """Test book model creation and methods"""
        category = Category.objects.create(
            school=self.school,
            name="Fiction",
            code="FIC"
        )
        
        author = Author.objects.create(
            school=self.school,
            first_name="John",
            last_name="Doe"
        )
        
        publisher = Publisher.objects.create(
            school=self.school,
            name="Test Publisher"
        )
        
        book = Book.objects.create(
            school=self.school,
            title="Test Book",
            title_ar="كتاب تجريبي",
            category=category,
            publisher=publisher,
            isbn="9781234567890",
            call_number="FIC-001",
            publication_date=date(2023, 1, 1),
            pages=300,
            total_copies=5,
            available_copies=3,
            cost=Decimal('25.99')
        )
        
        book.authors.add(author)
        
        self.assertEqual(str(book), "Test Book")
        self.assertTrue(book.is_available)
        self.assertEqual(book.borrowed_copies, 2)
        self.assertEqual(book.get_authors_display(), "John Doe")
        self.assertTrue(book.barcode.startswith("TES"))  # School code prefix
    
    def test_book_validation(self):
        """Test book model validation"""
        book = Book(
            school=self.school,
            title="Test Book",
            total_copies=5,
            available_copies=10  # More available than total
        )
        
        with self.assertRaises(ValidationError):
            book.full_clean()
    
    def test_book_copy_creation(self):
        """Test book copy model creation"""
        book = Book.objects.create(
            school=self.school,
            title="Test Book",
            call_number="TEST-001",
            total_copies=1,
            available_copies=1
        )
        
        copy = BookCopy.objects.create(
            school=self.school,
            book=book,
            copy_number="001",
            condition="excellent",
            acquisition_date=date.today()
        )
        
        self.assertEqual(str(copy), "Test Book - Copy 001")
        self.assertTrue(copy.is_available)
        self.assertTrue(copy.barcode.endswith("-001"))
    
    def test_digital_resource_creation(self):
        """Test digital resource model creation"""
        category = Category.objects.create(
            school=self.school,
            name="E-books",
            code="EBOOK"
        )
        
        author = Author.objects.create(
            school=self.school,
            first_name="Jane",
            last_name="Smith"
        )
        
        resource = DigitalResource.objects.create(
            school=self.school,
            title="Digital Test Book",
            title_ar="كتاب رقمي تجريبي",
            category=category,
            resource_type="ebook",
            file_format="pdf",
            file_size=1048576,  # 1MB
            description="Test digital resource",
            access_type="open",
            language="en"
        )
        
        resource.authors.add(author)
        
        self.assertEqual(str(resource), "Digital Test Book")
        self.assertEqual(resource.file_size_mb, 1.0)
        self.assertEqual(resource.get_authors_display(), "Jane Smith")
    
    def test_library_settings_creation(self):
        """Test library settings model"""
        settings = LibrarySettings.objects.create(
            school=self.school,
            library_name="Test Library",
            library_name_ar="مكتبة تجريبية",
            max_books_per_student=3,
            max_books_per_teacher=5,
            default_loan_period_days=14,
            fine_per_day=Decimal('0.50'),
            max_fine_amount=Decimal('50.00'),
            librarian_name="Test Librarian",
            contact_email="<EMAIL>"
        )
        
        self.assertEqual(str(settings), "Test School - Library Settings")


class LibraryViewTests(TestCase):
    """Test cases for library views"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="123 Test St",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
        
        # Create department and position
        self.department = Department.objects.create(
            school=self.school,
            name="Library",
            code="LIB"
        )
        
        self.position = Position.objects.create(
            school=self.school,
            title="Librarian",
            department=self.department
        )
        
        self.employee = Employee.objects.create(
            school=self.school,
            user=self.user,
            employee_id="EMP001",
            position=self.position,
            hire_date=date.today(),
            salary=Decimal('50000.00'),
            emergency_contact_name="Emergency Contact",
            emergency_contact_phone="+1234567890",
            emergency_contact_relationship="Spouse"
        )
        
        self.client = Client()
        self.client.login(username="testuser", password="testpass123")
        
        # Create test data
        self.category = Category.objects.create(
            school=self.school,
            name="Fiction",
            code="FIC"
        )
        
        self.author = Author.objects.create(
            school=self.school,
            first_name="John",
            last_name="Doe"
        )
        
        self.book = Book.objects.create(
            school=self.school,
            title="Test Book",
            category=self.category,
            call_number="FIC-001",
            isbn="9781234567890",
            total_copies=3,
            available_copies=2
        )
        
        self.book.authors.add(self.author)
    
    def test_catalog_view(self):
        """Test catalog view"""
        response = self.client.get(reverse('library:catalog'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Test Book")
        self.assertContains(response, "John Doe")
    
    def test_catalog_search(self):
        """Test catalog search functionality"""
        response = self.client.get(reverse('library:catalog'), {'q': 'Test'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Test Book")
        
        response = self.client.get(reverse('library:catalog'), {'q': 'Nonexistent'})
        self.assertEqual(response.status_code, 200)
        self.assertNotContains(response, "Test Book")
    
    def test_catalog_filters(self):
        """Test catalog filtering"""
        response = self.client.get(reverse('library:catalog'), {
            'category': str(self.category.id)
        })
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Test Book")
        
        response = self.client.get(reverse('library:catalog'), {
            'author': str(self.author.id)
        })
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Test Book")
    
    def test_book_detail_view(self):
        """Test book detail view"""
        response = self.client.get(reverse('library:book_detail', args=[self.book.id]))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Test Book")
        self.assertContains(response, "John Doe")
        self.assertContains(response, "9781234567890")
    
    def test_search_api(self):
        """Test search API"""
        response = self.client.get(reverse('library:search_api'), {
            'q': 'Test',
            'type': 'books'
        })
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(len(data['results']), 1)
        self.assertEqual(data['results'][0]['title'], 'Test Book')
    
    def test_barcode_lookup(self):
        """Test barcode lookup API"""
        response = self.client.get(reverse('library:barcode_lookup'), {
            'barcode': self.book.barcode
        })
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['type'], 'book')
        self.assertEqual(data['title'], 'Test Book')
    
    def test_library_statistics(self):
        """Test library statistics view"""
        response = self.client.get(reverse('library:statistics'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "1")  # Total books
        self.assertContains(response, "Fiction")  # Category stats


class LibraryIntegrationTests(TestCase):
    """Integration tests for library functionality"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="123 Test St",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
        
        # Create department and position
        self.department = Department.objects.create(
            school=self.school,
            name="Library",
            code="LIB"
        )
        
        self.position = Position.objects.create(
            school=self.school,
            title="Librarian",
            department=self.department
        )
        
        self.employee = Employee.objects.create(
            school=self.school,
            user=self.user,
            employee_id="EMP001",
            position=self.position,
            hire_date=date.today(),
            salary=Decimal('50000.00'),
            emergency_contact_name="Emergency Contact",
            emergency_contact_phone="+1234567890",
            emergency_contact_relationship="Spouse"
        )
    
    def test_complete_book_workflow(self):
        """Test complete book management workflow"""
        # Create category
        category = Category.objects.create(
            school=self.school,
            name="Science",
            code="SCI"
        )
        
        # Create author
        author = Author.objects.create(
            school=self.school,
            first_name="Albert",
            last_name="Einstein"
        )
        
        # Create publisher
        publisher = Publisher.objects.create(
            school=self.school,
            name="Science Press"
        )
        
        # Create book
        book = Book.objects.create(
            school=self.school,
            title="Theory of Relativity",
            category=category,
            publisher=publisher,
            isbn="9781234567890",
            call_number="SCI-001",
            total_copies=2,
            available_copies=2,
            cost=Decimal('45.99')
        )
        
        book.authors.add(author)
        
        # Create book copies
        copy1 = BookCopy.objects.create(
            school=self.school,
            book=book,
            copy_number="001",
            acquisition_date=date.today()
        )
        
        copy2 = BookCopy.objects.create(
            school=self.school,
            book=book,
            copy_number="002",
            acquisition_date=date.today()
        )
        
        # Verify relationships
        self.assertEqual(book.copies.count(), 2)
        self.assertEqual(category.books.count(), 1)
        self.assertEqual(author.books.count(), 1)
        self.assertEqual(publisher.books.count(), 1)
        
        # Test availability
        self.assertTrue(book.is_available)
        self.assertTrue(copy1.is_available)
        self.assertTrue(copy2.is_available)
        
        # Simulate borrowing
        copy1.status = 'checked_out'
        copy1.save()
        
        book.available_copies = 1
        book.save()
        
        self.assertEqual(book.borrowed_copies, 1)
        self.assertFalse(copy1.is_available)
        self.assertTrue(copy2.is_available)
    
    def test_digital_library_workflow(self):
        """Test digital library workflow"""
        # Create category
        category = Category.objects.create(
            school=self.school,
            name="E-books",
            code="EBOOK"
        )
        
        # Create author
        author = Author.objects.create(
            school=self.school,
            first_name="Digital",
            last_name="Author"
        )
        
        # Create digital resource
        resource = DigitalResource.objects.create(
            school=self.school,
            title="Digital Learning Guide",
            category=category,
            resource_type="ebook",
            file_format="pdf",
            file_size=2097152,  # 2MB
            access_type="open",
            description="Comprehensive digital learning guide"
        )
        
        resource.authors.add(author)
        
        # Verify creation
        self.assertEqual(str(resource), "Digital Learning Guide")
        self.assertEqual(resource.file_size_mb, 2.0)
        self.assertEqual(resource.get_authors_display(), "Digital Author")
        
        # Test view tracking
        initial_views = resource.view_count
        resource.view_count += 1
        resource.save()
        
        self.assertEqual(resource.view_count, initial_views + 1)
    
    def test_library_settings_workflow(self):
        """Test library settings management"""
        settings = LibrarySettings.objects.create(
            school=self.school,
            library_name="Central Library",
            max_books_per_student=5,
            default_loan_period_days=21,
            fine_per_day=Decimal('1.00'),
            max_fine_amount=Decimal('100.00'),
            enable_digital_library=True,
            max_concurrent_digital_loans=3
        )
        
        # Verify settings
        self.assertEqual(settings.library_name, "Central Library")
        self.assertEqual(settings.max_books_per_student, 5)
        self.assertEqual(settings.default_loan_period_days, 21)
        self.assertTrue(settings.enable_digital_library)
        
        # Test uniqueness constraint
        with self.assertRaises(Exception):
            LibrarySettings.objects.create(
                school=self.school,
                library_name="Another Library"
            )


class BorrowingSystemTestCase(TestCase):
    """Test cases for the borrowing system"""
    
    def setUp(self):
        """Set up test data for borrowing tests"""
        # Create school
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="Test Address",
            phone="1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
        
        # Create users
        self.librarian_user = User.objects.create_user(
            username='librarian',
            email='<EMAIL>',
            password='testpass123',
            user_type='employee'
        )
        
        # Create department and position
        self.department = Department.objects.create(
            school=self.school,
            name="Library",
            code="LIB"
        )
        
        self.position = Position.objects.create(
            school=self.school,
            title="Librarian",
            department=self.department
        )
        
        self.employee = Employee.objects.create(
            school=self.school,
            user=self.librarian_user,
            employee_id="EMP001",
            position=self.position,
            hire_date=date.today(),
            salary=Decimal('50000.00'),
            emergency_contact_name="Emergency Contact",
            emergency_contact_phone="+1234567890",
            emergency_contact_relationship="Spouse"
        )
        
        # Import borrowing models
        from .models import BookBorrowing, BorrowingHistory, BorrowingReminder
        
        # Create library settings
        self.library_settings = LibrarySettings.objects.create(
            school=self.school,
            library_name="Test Library",
            max_books_per_student=3,
            max_books_per_teacher=5,
            default_loan_period_days=14,
            max_renewals=2,
            fine_per_day=Decimal('0.50'),
            max_fine_amount=Decimal('50.00'),
            reminder_days_before_due=3,
            overdue_notice_frequency=7,
            created_by=self.librarian_user
        )
        
        # Create category and author
        self.category = Category.objects.create(
            school=self.school,
            name="Fiction",
            code="FIC",
            created_by=self.librarian_user
        )
        
        self.author = Author.objects.create(
            school=self.school,
            first_name="Test",
            last_name="Author",
            created_by=self.librarian_user
        )
        
        self.publisher = Publisher.objects.create(
            school=self.school,
            name="Test Publisher",
            created_by=self.librarian_user
        )
        
        # Create book
        self.book = Book.objects.create(
            school=self.school,
            title="Test Book",
            isbn="1234567890123",
            barcode="TEST001",
            call_number="FIC-001",
            category=self.category,
            publisher=self.publisher,
            total_copies=2,
            available_copies=2,
            created_by=self.librarian_user
        )
        self.book.authors.add(self.author)
        
        # Create book copies
        self.book_copy1 = BookCopy.objects.create(
            school=self.school,
            book=self.book,
            copy_number="001",
            barcode="TEST001-001",
            rfid_tag="RFID001",
            acquisition_date=date.today(),
            created_by=self.librarian_user
        )
        
        self.book_copy2 = BookCopy.objects.create(
            school=self.school,
            book=self.book,
            copy_number="002",
            barcode="TEST001-002",
            rfid_tag="RFID002",
            acquisition_date=date.today(),
            created_by=self.librarian_user
        )

    def test_book_borrowing_creation(self):
        """Test creating a book borrowing"""
        from .models import BookBorrowing
        
        borrowing = BookBorrowing.objects.create(
            school=self.school,
            book=self.book,
            book_copy=self.book_copy1,
            borrower_type='student',
            borrower_id='STU001',
            borrower_name='Test Student',
            borrower_email='<EMAIL>',
            issued_by=self.librarian_user,
            created_by=self.librarian_user
        )
        
        self.assertEqual(borrowing.status, 'active')
        self.assertEqual(borrowing.renewal_count, 0)
        self.assertEqual(borrowing.fine_amount, Decimal('0.00'))
        self.assertIsNotNone(borrowing.due_date)
        
        # Check that due date is set correctly (14 days from now)
        from django.utils import timezone
        expected_due_date = (timezone.now() + timedelta(days=14)).date()
        self.assertEqual(borrowing.due_date, expected_due_date)
        
        # Check that book copy status is updated
        self.book_copy1.refresh_from_db()
        self.assertEqual(self.book_copy1.status, 'checked_out')
        
        # Check that book available copies is updated
        self.book.refresh_from_db()
        self.assertEqual(self.book.available_copies, 1)

    def test_book_return(self):
        """Test returning a book"""
        from .models import BookBorrowing
        
        # Create borrowing
        borrowing = BookBorrowing.objects.create(
            school=self.school,
            book=self.book,
            book_copy=self.book_copy1,
            borrower_type='student',
            borrower_id='STU001',
            borrower_name='Test Student',
            borrower_email='<EMAIL>',
            issued_by=self.librarian_user,
            created_by=self.librarian_user
        )
        
        # Return the book
        borrowing.return_book(returned_to=self.librarian_user)
        
        self.assertEqual(borrowing.status, 'returned')
        self.assertIsNotNone(borrowing.return_date)
        
        # Check that book copy status is updated
        self.book_copy1.refresh_from_db()
        self.assertEqual(self.book_copy1.status, 'available')
        
        # Check that book available copies is updated
        self.book.refresh_from_db()
        self.assertEqual(self.book.available_copies, 2)

    def test_book_renewal(self):
        """Test renewing a book"""
        from .models import BookBorrowing
        
        borrowing = BookBorrowing.objects.create(
            school=self.school,
            book=self.book,
            book_copy=self.book_copy1,
            borrower_type='student',
            borrower_id='STU001',
            borrower_name='Test Student',
            borrower_email='<EMAIL>',
            issued_by=self.librarian_user,
            created_by=self.librarian_user
        )
        
        original_due_date = borrowing.due_date
        
        # Renew the book
        borrowing.renew(renewed_by=self.librarian_user)
        
        self.assertEqual(borrowing.status, 'renewed')
        self.assertEqual(borrowing.renewal_count, 1)
        self.assertGreater(borrowing.due_date, original_due_date)

    def test_overdue_detection(self):
        """Test overdue book detection"""
        from .models import BookBorrowing
        from django.utils import timezone
        
        # Create borrowing with past due date
        past_date = timezone.now().date() - timedelta(days=5)
        borrowing = BookBorrowing.objects.create(
            school=self.school,
            book=self.book,
            book_copy=self.book_copy1,
            borrower_type='student',
            borrower_id='STU001',
            borrower_name='Test Student',
            borrower_email='<EMAIL>',
            due_date=past_date,
            issued_by=self.librarian_user,
            created_by=self.librarian_user
        )
        
        self.assertTrue(borrowing.is_overdue)
        self.assertEqual(borrowing.days_overdue, 5)

    def test_fine_calculation(self):
        """Test fine calculation for overdue books"""
        from .models import BookBorrowing
        from django.utils import timezone
        
        # Create overdue borrowing
        past_date = timezone.now().date() - timedelta(days=10)
        borrowing = BookBorrowing.objects.create(
            school=self.school,
            book=self.book,
            book_copy=self.book_copy1,
            borrower_type='student',
            borrower_id='STU001',
            borrower_name='Test Student',
            borrower_email='<EMAIL>',
            due_date=past_date,
            issued_by=self.librarian_user,
            created_by=self.librarian_user
        )
        
        expected_fine = Decimal('5.00')  # 10 days * $0.50
        calculated_fine = borrowing.calculate_fine()
        
        self.assertEqual(calculated_fine, expected_fine)

    def test_renewal_limits(self):
        """Test renewal limits"""
        from .models import BookBorrowing
        
        borrowing = BookBorrowing.objects.create(
            school=self.school,
            book=self.book,
            book_copy=self.book_copy1,
            borrower_type='student',
            borrower_id='STU001',
            borrower_name='Test Student',
            borrower_email='<EMAIL>',
            renewal_count=2,  # At max renewals
            issued_by=self.librarian_user,
            created_by=self.librarian_user
        )
        
        self.assertFalse(borrowing.can_renew)

    def test_borrowing_history_creation(self):
        """Test that borrowing history is created"""
        from .models import BookBorrowing, BorrowingHistory
        
        borrowing = BookBorrowing.objects.create(
            school=self.school,
            book=self.book,
            book_copy=self.book_copy1,
            borrower_type='student',
            borrower_id='STU001',
            borrower_name='Test Student',
            borrower_email='<EMAIL>',
            issued_by=self.librarian_user,
            created_by=self.librarian_user
        )
        
        # Create history record
        history = BorrowingHistory.objects.create(
            school=self.school,
            borrowing=borrowing,
            action='borrowed',
            performed_by=self.librarian_user,
            notes='Book borrowed successfully',
            created_by=self.librarian_user
        )
        
        self.assertEqual(history.borrowing, borrowing)
        self.assertEqual(history.action, 'borrowed')
        self.assertEqual(history.performed_by, self.librarian_user)

    def test_reminder_creation(self):
        """Test reminder creation"""
        from .models import BookBorrowing, BorrowingReminder
        from django.utils import timezone
        
        borrowing = BookBorrowing.objects.create(
            school=self.school,
            book=self.book,
            book_copy=self.book_copy1,
            borrower_type='student',
            borrower_id='STU001',
            borrower_name='Test Student',
            borrower_email='<EMAIL>',
            issued_by=self.librarian_user,
            created_by=self.librarian_user
        )
        
        reminder = BorrowingReminder.objects.create(
            school=self.school,
            borrowing=borrowing,
            reminder_type='due_soon',
            scheduled_date=timezone.now(),
            recipient_email='<EMAIL>',
            message_subject='Book Due Soon',
            message_body='Your book is due soon',
            created_by=self.librarian_user
        )
        
        self.assertEqual(reminder.status, 'pending')
        self.assertEqual(reminder.reminder_type, 'due_soon')
        self.assertEqual(reminder.borrowing, borrowing)

    def test_borrowing_analytics(self):
        """Test borrowing analytics creation"""
        from .models import BookBorrowing, BorrowingAnalytics
        from django.utils import timezone
        
        # Create some test borrowings
        borrowing1 = BookBorrowing.objects.create(
            school=self.school,
            book=self.book,
            book_copy=self.book_copy1,
            borrower_type='student',
            borrower_id='STU001',
            borrower_name='Test Student 1',
            status='returned',
            issued_by=self.librarian_user,
            created_by=self.librarian_user
        )
        
        # Test analytics creation
        date_from = timezone.now().date() - timedelta(days=30)
        date_to = timezone.now().date()
        
        analytics = BorrowingAnalytics.objects.create(
            school=self.school,
            date_from=date_from,
            date_to=date_to,
            total_borrowings=1,
            total_returns=1,
            total_renewals=0,
            total_overdue=0,
            total_lost=0,
            total_fines=Decimal('0.00'),
            fines_collected=Decimal('0.00'),
            fines_waived=Decimal('0.00'),
            popular_books=[],
            borrower_stats={'student': 1},
            category_stats={'Fiction': 1},
            created_by=self.librarian_user
        )
        
        self.assertEqual(analytics.total_borrowings, 1)
        self.assertEqual(analytics.total_returns, 1)
        self.assertEqual(analytics.borrower_stats['student'], 1)

    def test_max_fine_limit(self):
        """Test that fines don't exceed maximum limit"""
        from .models import BookBorrowing
        from django.utils import timezone
        
        # Create borrowing overdue by 200 days (would be $100 at $0.50/day)
        past_date = timezone.now().date() - timedelta(days=200)
        borrowing = BookBorrowing.objects.create(
            school=self.school,
            book=self.book,
            book_copy=self.book_copy1,
            borrower_type='student',
            borrower_id='STU001',
            borrower_name='Test Student',
            borrower_email='<EMAIL>',
            due_date=past_date,
            issued_by=self.librarian_user,
            created_by=self.librarian_user
        )
        
        calculated_fine = borrowing.calculate_fine()
        
        # Should be capped at max_fine_amount ($50.00)
        self.assertEqual(calculated_fine, Decimal('50.00'))

    def test_borrowing_workflow_integration(self):
        """Test complete borrowing workflow"""
        from .models import BookBorrowing, BorrowingHistory
        from django.utils import timezone
        
        # Step 1: Borrow book
        borrowing = BookBorrowing.objects.create(
            school=self.school,
            book=self.book,
            book_copy=self.book_copy1,
            borrower_type='student',
            borrower_id='STU001',
            borrower_name='Test Student',
            borrower_email='<EMAIL>',
            issued_by=self.librarian_user,
            created_by=self.librarian_user
        )
        
        # Verify initial state
        self.assertEqual(borrowing.status, 'active')
        self.book.refresh_from_db()
        self.assertEqual(self.book.available_copies, 1)
        
        # Step 2: Renew book
        borrowing.renew(renewed_by=self.librarian_user)
        self.assertEqual(borrowing.status, 'renewed')
        self.assertEqual(borrowing.renewal_count, 1)
        
        # Step 3: Return book
        borrowing.return_book(returned_to=self.librarian_user)
        self.assertEqual(borrowing.status, 'returned')
        self.assertIsNotNone(borrowing.return_date)
        
        # Verify final state
        self.book.refresh_from_db()
        self.assertEqual(self.book.available_copies, 2)
        self.book_copy1.refresh_from_db()
        self.assertEqual(self.book_copy1.status, 'available')


class LibraryAnalyticsTests(TestCase):
    """Test cases for library analytics functionality"""
    
    def setUp(self):
        """Set up test data for analytics"""
        self.school = School.objects.create(
            name="Test School Analytics",
            code="TESTA",
            address="123 Test St",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
        
        self.user = User.objects.create_user(
            username="analyticsuser",
            email="<EMAIL>",
            password="testpass123"
        )
        
        # Create department and position
        self.department = Department.objects.create(
            school=self.school,
            name="Library",
            code="LIB"
        )
        
        self.position = Position.objects.create(
            school=self.school,
            title="Librarian",
            department=self.department
        )
        
        self.employee = Employee.objects.create(
            school=self.school,
            user=self.user,
 
           employee_id="ANALY001",
            position=self.position,
            hire_date=date.today(),
            salary=Decimal('50000.00'),
            emergency_contact_name="Emergency Contact",
            emergency_contact_phone="+1234567890",
            emergency_contact_relationship="Spouse"
        )
        
        # Create test data for analytics
        self.category = Category.objects.create(
            school=self.school,
            name="Analytics Fiction",
            code="AFIC"
        )
        
        self.author = Author.objects.create(
            school=self.school,
            first_name="Analytics",
            last_name="Author"
        )
        
        self.book = Book.objects.create(
            school=self.school,
            title="Analytics Test Book",
            category=self.category,
            isbn="9781234567890",
            call_number="AFIC-001",
            total_copies=5,
            available_copies=3
        )
        
        self.book.authors.add(self.author)
        
        # Create digital resource
        self.digital_resource = DigitalResource.objects.create(
            school=self.school,
            title="Analytics Digital Resource",
            category=self.category,
            resource_type="ebook",
            file_format="pdf"
        )
        
        self.digital_resource.authors.add(self.author)
        
        # Create client and login
        self.client = Client()
        self.client.login(username="analyticsuser", password="testpass123")
    
    def test_library_statistics_view(self):
        """Test library statistics view"""
        response = self.client.get(reverse('library:statistics'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Analytics Test Book")
        self.assertContains(response, "Analytics Digital Resource")
        
        # Check context data
        self.assertIn('total_books', response.context)
        self.assertIn('total_digital', response.context)
        self.assertIn('popular_books', response.context)
        self.assertIn('category_stats', response.context)
    
    def test_comprehensive_analytics_view(self):
        """Test comprehensive analytics dashboard"""
        response = self.client.get(reverse('library:comprehensive_analytics'))
        self.assertEqual(response.status_code, 200)
        
        # Check key metrics are present
        self.assertIn('total_books', response.context)
        self.assertIn('total_digital', response.context)
        self.assertIn('active_borrowings', response.context)
        self.assertIn('daily_activity', response.context)
        self.assertIn('top_categories', response.context)
        self.assertIn('popular_books', response.context)
    
    def test_analytics_date_filtering(self):
        """Test analytics with date filtering"""
        from datetime import datetime
        
        # Test with specific date range
        response = self.client.get(reverse('library:comprehensive_analytics'), {
            'date_from': '2025-01-01',
            'date_to': '2025-12-31'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['date_from'], datetime.strptime('2025-01-01', '%Y-%m-%d').date())
        self.assertEqual(response.context['date_to'], datetime.strptime('2025-12-31', '%Y-%m-%d').date())
    
    def test_analytics_export_pdf(self):
        """Test PDF export functionality"""
        response = self.client.get(reverse('library:comprehensive_analytics'), {
            'export': 'pdf'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertIn('attachment', response['Content-Disposition'])
    
    def test_analytics_export_excel(self):
        """Test Excel export functionality"""
        response = self.client.get(reverse('library:comprehensive_analytics'), {
            'export': 'excel'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')
        self.assertIn('attachment', response['Content-Disposition'])
    
    def test_digital_usage_analytics(self):
        """Test digital usage analytics"""
        # Create some usage data
        DigitalResourceUsage.objects.create(
            school=self.school,
            resource=self.digital_resource,
            user=self.user,
            action='view',
            created_by=self.user
        )
        
        DigitalResourceUsage.objects.create(
            school=self.school,
            resource=self.digital_resource,
            user=self.user,
            action='download',
            created_by=self.user
        )
        
        response = self.client.get(reverse('library:digital_usage_analytics'))
        self.assertEqual(response.status_code, 200)
        
        # Check that usage data is displayed
        self.assertIn('usage_by_action', response.context)
        self.assertIn('popular_resources', response.context)
    
    def test_library_performance_metrics(self):
        """Test library performance metrics view"""
        response = self.client.get(reverse('library:performance_metrics'))
        self.assertEqual(response.status_code, 200)
        
        # Check performance metrics are calculated
        self.assertIn('utilization_rate', response.context)
        self.assertIn('collection_growth', response.context)
        self.assertIn('user_satisfaction', response.context)
    
    def test_acquisition_recommendations(self):
        """Test acquisition recommendations functionality"""
        # Create some borrowing data to generate recommendations
        from .models import BookBorrowing
        
        # Create multiple borrowings for the same category to trigger recommendations
        for i in range(5):
            BookBorrowing.objects.create(
                school=self.school,
                book=self.book,
                borrower_type='student',
                borrower_id=f'STU{i:03d}',
                borrower_name=f'Student {i}',
                issued_by=self.user,
                created_by=self.user
            )
        
        response = self.client.get(reverse('library:statistics'))
        self.assertEqual(response.status_code, 200)
        
        # Check that acquisition recommendations are generated
        self.assertIn('acquisition_recommendations', response.context)
    
    def test_popular_books_reporting(self):
        """Test popular books reporting"""
        from .models import BookBorrowing
        
        # Create borrowing data
        borrowing = BookBorrowing.objects.create(
            school=self.school,
            book=self.book,
            borrower_type='student',
            borrower_id='STU001',
            borrower_name='Test Student',
            issued_by=self.user,
            created_by=self.user
        )
        
        response = self.client.get(reverse('library:statistics'))
        self.assertEqual(response.status_code, 200)
        
        # Check that popular books are identified
        popular_books = response.context['popular_books']
        self.assertTrue(any(book.title == 'Analytics Test Book' for book in popular_books))
    
    def test_library_dashboard_integration(self):
        """Test that analytics are accessible from dashboard and navbar"""
        # Test that analytics links are present in the main library views
        response = self.client.get(reverse('library:catalog'))
        self.assertEqual(response.status_code, 200)
        
        # Check for analytics navigation links
        self.assertContains(response, reverse('library:statistics'))
        
        # Test comprehensive analytics access
        response = self.client.get(reverse('library:comprehensive_analytics'))
        self.assertEqual(response.status_code, 200)
        
        # Check navigation to other library sections
        self.assertContains(response, reverse('library:catalog'))
        self.assertContains(response, reverse('library:digital_library'))


class LibraryReportsTests(TestCase):
    """Test cases for library reports functionality"""
    
    def setUp(self):
        """Set up test data for reports"""
        self.school = School.objects.create(
            name="Test School Reports",
            code="TESTR",
            address="123 Test St",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
        
        self.user = User.objects.create_user(
            username="reportsuser",
            email="<EMAIL>",
            password="testpass123"
        )
        
        # Create department and position
        self.department = Department.objects.create(
            school=self.school,
            name="Library",
            code="LIB"
        )
        
        self.position = Position.objects.create(
            school=self.school,
            title="Librarian",
            department=self.department
        )
        
        self.employee = Employee.objects.create(
            school=self.school,
            user=self.user,
            employee_id="REP001",
            position=self.position,
            hire_date=date.today(),
            salary=Decimal('50000.00'),
            emergency_contact_name="Emergency Contact",
            emergency_contact_phone="+1234567890",
            emergency_contact_relationship="Spouse"
        )
        
        self.client = Client()
        self.client.login(username="reportsuser", password="testpass123")
    
    def test_library_reports_view(self):
        """Test library reports view"""
        response = self.client.get(reverse('library:reports'))
        self.assertEqual(response.status_code, 200)
        
        # Check that different report types are available
        self.assertContains(response, "Collection Report")
        self.assertContains(response, "Usage Report")
        self.assertContains(response, "Financial Report")
    
    def test_collection_report_generation(self):
        """Test collection report generation"""
        response = self.client.get(reverse('library:reports'), {
            'report_type': 'collection'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('collection_summary', response.context)
    
    def test_usage_report_generation(self):
        """Test usage report generation"""
        response = self.client.get(reverse('library:reports'), {
            'report_type': 'usage'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('usage_summary', response.context)
    
    def test_financial_report_generation(self):
        """Test financial report generation"""
        response = self.client.get(reverse('library:reports'), {
            'report_type': 'financial'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('financial_summary', response.context)


class LibraryPerformanceTests(TestCase):
    """Test cases for library performance metrics"""
    
    def setUp(self):
        """Set up test data for performance metrics"""
        self.school = School.objects.create(
            name="Test School Performance",
            code="TESTP",
            address="123 Test St",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date.today()
        )
        
        self.user = User.objects.create_user(
            username="perfuser",
            email="<EMAIL>",
            password="testpass123"
        )
        
        # Create department and position
        self.department = Department.objects.create(
            school=self.school,
            name="Library",
            code="LIB"
        )
        
        self.position = Position.objects.create(
            school=self.school,
            title="Librarian",
            department=self.department
        )
        
        self.employee = Employee.objects.create(
            school=self.school,
            user=self.user,
            employee_id="PERF001",
            position=self.position,
            hire_date=date.today(),
            salary=Decimal('50000.00'),
            emergency_contact_name="Emergency Contact",
            emergency_contact_phone="+1234567890",
            emergency_contact_relationship="Spouse"
        )
        
        self.client = Client()
        self.client.login(username="perfuser", password="testpass123")
    
    def test_performance_metrics_view(self):
        """Test performance metrics view"""
        response = self.client.get(reverse('library:performance_metrics'))
        self.assertEqual(response.status_code, 200)
        
        # Check key performance indicators
        self.assertIn('utilization_rate', response.context)
        self.assertIn('collection_growth', response.context)
        self.assertIn('user_engagement', response.context)
        self.assertIn('operational_efficiency', response.context)
    
    def test_utilization_rate_calculation(self):
        """Test utilization rate calculation"""
        # Create test books
        book = Book.objects.create(
            school=self.school,
            title="Performance Test Book",
            total_copies=10,
            available_copies=6  # 4 borrowed, 60% utilization
        )
        
        response = self.client.get(reverse('library:performance_metrics'))
        self.assertEqual(response.status_code, 200)
        
        # Check that utilization rate is calculated correctly
        utilization_rate = response.context['utilization_rate']
        self.assertIsInstance(utilization_rate, (int, float))
        self.assertGreaterEqual(utilization_rate, 0)
        self.assertLessEqual(utilization_rate, 100)
    
    def test_collection_growth_tracking(self):
        """Test collection growth tracking"""
        response = self.client.get(reverse('library:performance_metrics'))
        self.assertEqual(response.status_code, 200)
        
        # Check collection growth metrics
        self.assertIn('monthly_growth', response.context)
        self.assertIn('yearly_growth', response.context)
    
    def test_user_engagement_metrics(self):
        """Test user engagement metrics"""
        response = self.client.get(reverse('library:performance_metrics'))
        self.assertEqual(response.status_code, 200)
        
        # Check user engagement metrics
        self.assertIn('active_users', response.context)
        self.assertIn('avg_books_per_user', response.context)
        self.assertIn('return_rate', response.context)