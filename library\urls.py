from django.urls import path
from . import views

app_name = 'library'

urlpatterns = [
    # Main catalog views
    path('', views.catalog_view, name='catalog'),
    path('book/<uuid:book_id>/', views.book_detail_view, name='book_detail'),
    
    # Digital library
    path('digital/', views.digital_library_view, name='digital_library'),
    path('digital/<uuid:resource_id>/', views.digital_resource_detail_view, name='digital_resource_detail'),
    path('digital/<uuid:resource_id>/read/', views.read_online_view, name='read_online'),
    path('digital/<uuid:resource_id>/watch/', views.watch_online_view, name='watch_online'),
    path('digital/<uuid:resource_id>/listen/', views.listen_online_view, name='listen_online'),
    path('digital/<uuid:resource_id>/download/', views.download_resource_view, name='download_resource'),
    path('digital/<uuid:resource_id>/related/', views.related_resources_api, name='related_resources'),
    path('digital/usage-analytics/', views.digital_usage_analytics_view, name='digital_usage_analytics'),
    
    # Borrowing system
    path('borrowing/', views.borrowing_system_view, name='borrowing_system'),
    path('borrowing/history/', views.borrowing_history_view, name='borrowing_history'),
    path('borrowing/history/<str:borrower_type>/<str:borrower_id>/', views.borrowing_history_view, name='borrower_history'),
    path('borrowing/overdue/', views.overdue_books_view, name='overdue_books'),
    path('borrowing/analytics/', views.borrowing_analytics_view, name='borrowing_analytics'),
    
    # API endpoints
    path('api/search/', views.search_api, name='search_api'),
    path('api/barcode/', views.barcode_lookup, name='barcode_lookup'),
    path('api/borrow/', views.borrow_book, name='borrow_book'),
    path('api/return/', views.return_book, name='return_book'),
    path('api/renew/', views.renew_book, name='renew_book'),
    path('api/borrower-search/', views.borrower_search, name='borrower_search'),
    path('api/report-issue/', views.report_issue_api, name='report_issue'),
    path('api/bookmark/', views.api_bookmark, name='api_bookmark'),
    path('api/reading-progress/', views.api_reading_progress, name='api_reading_progress'),
    path('api/text-content/<uuid:resource_id>/', views.api_text_content, name='api_text_content'),
    path('api/watch-progress/', views.api_watch_progress, name='api_watch_progress'),
    path('api/listen-progress/', views.api_listen_progress, name='api_listen_progress'),
    
    # Management views
    path('dashboard/', views.library_dashboard, name='dashboard'),
    path('statistics/', views.library_statistics, name='statistics'),
    path('reports/', views.library_reports, name='reports'),
    path('analytics/', views.comprehensive_analytics_view, name='comprehensive_analytics'),
    path('performance/', views.library_performance_metrics, name='performance_metrics'),
    path('inventory/', views.inventory_management, name='inventory'),
]