from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.db.models import Q, Count, Avg, Sum, <PERSON>, <PERSON>, F, Case, When, FloatField
from django.db import models
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods
from django.utils.translation import gettext_lazy as _
from django.contrib import messages
from django.core.exceptions import PermissionDenied
from django.utils import timezone
from django.urls import reverse
from django.template.loader import render_to_string
from datetime import datetime, timedelta
import csv
from .models import (
    Book, BookCopy, Category, Author, Publisher, DigitalResource, 
    LibrarySettings, BookBorrowing, BorrowingHistory, BorrowingReminder,
    DigitalResourceUsage
)
from core.permissions import has_school_permission
import json


def school_required(view_func):
    """
    Enhanced decorator to ensure user has access to school data with security validation
    """
    def wrapper(request, *args, **kwargs):
        from core.school_utils import (
            get_current_school, validate_school_access_permission,
            validate_session_security, log_security_event, get_client_ip
        )
        
        if not request.user.is_authenticated:
            log_security_event(None, 'LIBRARY_UNAUTHORIZED_ACCESS', f"Unauthenticated access to {request.path}", ip_address=get_client_ip(request))
            raise PermissionDenied("Authentication required")
        
        # Validate session security
        is_secure, warning_msg = validate_session_security(request)
        if not is_secure:
            log_security_event(request.user, 'LIBRARY_SESSION_SECURITY_VIOLATION', warning_msg, ip_address=get_client_ip(request))
            raise PermissionDenied("Session security violation")
        
        # Get school from middleware context (preferred) or fallback to user profile
        school = getattr(request, 'school', None)
        
        if not school:
            school = get_current_school(request)
        
        if not school:
            # Fallback to user profile school
            if hasattr(request.user, 'employee_profile') and request.user.employee_profile:
                school = request.user.employee_profile.school
            elif hasattr(request.user, 'student') and request.user.student:
                school = request.user.student.school
            elif hasattr(request.user, 'parent') and request.user.parent:
                school = request.user.parent.school
        
        if not school and not request.user.is_superuser:
            log_security_event(request.user, 'LIBRARY_NO_SCHOOL_ACCESS', f"No school access for {request.path}", ip_address=get_client_ip(request))
            raise PermissionDenied("No school access")
        
        # Validate school access permission
        if school:
            has_permission, error_msg = validate_school_access_permission(request.user, school, 'read')
            if not has_permission:
                log_security_event(request.user, 'LIBRARY_SCHOOL_ACCESS_DENIED', f"School access denied: {error_msg}", school=school, ip_address=get_client_ip(request))
                raise PermissionDenied(error_msg)
        
        request.school = school
        return view_func(request, *args, **kwargs)
    
    return wrapper


def library_api_required(endpoint_type='library'):
    """
    Decorator for library API endpoints with enhanced security
    """
    def decorator(view_func):
        def wrapper(request, *args, **kwargs):
            from core.school_utils import (
                check_api_access_permission, validate_session_security,
                log_security_event, get_client_ip
            )
            
            if not request.user.is_authenticated:
                log_security_event(None, 'LIBRARY_API_UNAUTHORIZED', f"Unauthenticated API access to {request.path}", ip_address=get_client_ip(request))
                return JsonResponse({'error': 'Authentication required'}, status=401)
            
            # Validate session security
            is_secure, warning_msg = validate_session_security(request)
            if not is_secure:
                log_security_event(request.user, 'LIBRARY_API_SECURITY_VIOLATION', warning_msg, ip_address=get_client_ip(request))
                return JsonResponse({'error': 'Session security violation'}, status=403)
            
            school = getattr(request, 'school', None)
            if not school:
                log_security_event(request.user, 'LIBRARY_API_NO_SCHOOL', f"No school context for API {request.path}", ip_address=get_client_ip(request))
                return JsonResponse({'error': 'School context required'}, status=400)
            
            # Check API access permission
            has_permission, error_msg = check_api_access_permission(request.user, school, endpoint_type)
            if not has_permission:
                log_security_event(request.user, 'LIBRARY_API_ACCESS_DENIED', f"API access denied: {error_msg}", school=school, ip_address=get_client_ip(request))
                return JsonResponse({'error': error_msg}, status=403)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


@login_required
@school_required
def catalog_view(request):
    """
    Main catalog view with search and filtering
    """
    # Get search parameters
    query = request.GET.get('q', '')
    category_id = request.GET.get('category', '')
    author_id = request.GET.get('author', '')
    publisher_id = request.GET.get('publisher', '')
    format_type = request.GET.get('format', '')
    language = request.GET.get('language', '')
    status = request.GET.get('status', '')
    
    # Base queryset
    books = Book.objects.filter(school=request.school, is_active=True)
    
    # Apply search
    if query:
        books = books.filter(
            Q(title__icontains=query) |
            Q(title_ar__icontains=query) |
            Q(isbn__icontains=query) |
            Q(barcode__icontains=query) |
            Q(call_number__icontains=query) |
            Q(description__icontains=query) |
            Q(keywords__icontains=query) |
            Q(authors__first_name__icontains=query) |
            Q(authors__last_name__icontains=query)
        ).distinct()
    
    # Apply filters
    if category_id:
        books = books.filter(category_id=category_id)
    if author_id:
        books = books.filter(authors__id=author_id)
    if publisher_id:
        books = books.filter(publisher_id=publisher_id)
    if format_type:
        books = books.filter(format_type=format_type)
    if language:
        books = books.filter(language=language)
    if status:
        books = books.filter(status=status)
    
    # Prefetch related data
    books = books.select_related('category', 'publisher').prefetch_related('authors')
    
    # Pagination
    paginator = Paginator(books, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get filter options
    categories = Category.objects.filter(school=request.school, is_active=True).order_by('name')
    authors = Author.objects.filter(school=request.school, is_active=True).order_by('last_name', 'first_name')
    publishers = Publisher.objects.filter(school=request.school, is_active=True).order_by('name')
    
    context = {
        'page_obj': page_obj,
        'query': query,
        'categories': categories,
        'authors': authors,
        'publishers': publishers,
        'selected_category': category_id,
        'selected_author': author_id,
        'selected_publisher': publisher_id,
        'selected_format': format_type,
        'selected_language': language,
        'selected_status': status,
        'format_choices': Book._meta.get_field('format_type').choices,
        'language_choices': Book._meta.get_field('language').choices,
        'status_choices': Book._meta.get_field('status').choices,
        'total_books': books.count(),
    }
    
    return render(request, 'library/catalog.html', context)


@login_required
@school_required
def book_detail_view(request, book_id):
    """
    Detailed view of a specific book
    """
    book = get_object_or_404(
        Book.objects.select_related('category', 'publisher').prefetch_related('authors', 'copies'),
        id=book_id,
        school=request.school,
        is_active=True
    )
    
    # Get book copies
    copies = book.copies.filter(is_active=True).order_by('copy_number')
    
    context = {
        'book': book,
        'copies': copies,
        'available_copies': copies.filter(status='available').count(),
        'total_copies': copies.count(),
    }
    
    return render(request, 'library/book_detail.html', context)


@login_required
@school_required
def digital_library_view(request):
    """
    Digital library resources view
    """
    # Get search parameters
    query = request.GET.get('q', '')
    category_id = request.GET.get('category', '')
    resource_type = request.GET.get('type', '')
    file_format = request.GET.get('format', '')
    access_type = request.GET.get('access', '')
    
    # Base queryset
    resources = DigitalResource.objects.filter(school=request.school, is_active=True)
    
    # Apply search
    if query:
        resources = resources.filter(
            Q(title__icontains=query) |
            Q(title_ar__icontains=query) |
            Q(description__icontains=query) |
            Q(keywords__icontains=query) |
            Q(authors__first_name__icontains=query) |
            Q(authors__last_name__icontains=query)
        ).distinct()
    
    # Apply filters
    if category_id:
        resources = resources.filter(category_id=category_id)
    if resource_type:
        resources = resources.filter(resource_type=resource_type)
    if file_format:
        resources = resources.filter(file_format=file_format)
    if access_type:
        resources = resources.filter(access_type=access_type)
    
    # Prefetch related data
    resources = resources.select_related('category').prefetch_related('authors')
    
    # Pagination
    paginator = Paginator(resources, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get filter options
    categories = Category.objects.filter(school=request.school, is_active=True).order_by('name')
    
    context = {
        'page_obj': page_obj,
        'query': query,
        'categories': categories,
        'selected_category': category_id,
        'selected_type': resource_type,
        'selected_format': file_format,
        'selected_access': access_type,
        'type_choices': DigitalResource._meta.get_field('resource_type').choices,
        'format_choices': DigitalResource._meta.get_field('file_format').choices,
        'access_choices': DigitalResource._meta.get_field('access_type').choices,
        'total_resources': resources.count(),
    }
    
    return render(request, 'library/digital_library.html', context)


@login_required
@school_required
def digital_resource_detail_view(request, resource_id):
    """
    Detailed view of a digital resource
    """
    resource = get_object_or_404(
        DigitalResource.objects.select_related('category').prefetch_related('authors'),
        id=resource_id,
        school=request.school,
        is_active=True
    )
    
    # Update view count
    resource.view_count += 1
    resource.save(update_fields=['view_count'])
    
    context = {
        'resource': resource,
    }
    
    return render(request, 'library/digital_resource_detail.html', context)


@login_required
@library_api_required('library')
def search_api(request):
    """
    AJAX search API for autocomplete with enhanced security
    """
    query = request.GET.get('q', '').strip()
    search_type = request.GET.get('type', 'books')  # books, authors, categories
    
    if len(query) < 2:
        return JsonResponse({'results': []})
    
    results = []
    
    if search_type == 'books':
        books = Book.objects.filter(
            school=request.school,
            is_active=True
        ).filter(
            Q(title__icontains=query) |
            Q(title_ar__icontains=query) |
            Q(isbn__icontains=query) |
            Q(barcode__icontains=query)
        )[:10]
        
        results = [
            {
                'id': book.id,
                'title': book.title,
                'subtitle': book.subtitle or '',
                'authors': book.get_authors_display(),
                'isbn': book.isbn,
                'barcode': book.barcode,
                'available': book.is_available,
            }
            for book in books
        ]
    
    elif search_type == 'authors':
        authors = Author.objects.filter(
            school=request.school,
            is_active=True
        ).filter(
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query) |
            Q(first_name_ar__icontains=query) |
            Q(last_name_ar__icontains=query)
        )[:10]
        
        results = [
            {
                'id': author.id,
                'name': author.full_name,
                'name_ar': author.full_name_ar,
                'book_count': author.books.filter(is_active=True).count(),
            }
            for author in authors
        ]
    
    elif search_type == 'categories':
        categories = Category.objects.filter(
            school=request.school,
            is_active=True
        ).filter(
            Q(name__icontains=query) |
            Q(name_ar__icontains=query) |
            Q(code__icontains=query)
        )[:10]
        
        results = [
            {
                'id': category.id,
                'name': category.name,
                'name_ar': category.name_ar or '',
                'code': category.code,
                'book_count': category.books.filter(is_active=True).count(),
            }
            for category in categories
        ]
    
    return JsonResponse({'results': results})


@login_required
@library_api_required('library')
def barcode_lookup(request):
    """
    Look up book by barcode (for scanning) with enhanced security
    """
    barcode = request.GET.get('barcode', '').strip()
    
    if not barcode:
        return JsonResponse({'error': _('Barcode is required')}, status=400)
    
    try:
        # Try book barcode first
        book = Book.objects.get(
            school=request.school,
            barcode=barcode,
            is_active=True
        )
        
        return JsonResponse({
            'type': 'book',
            'id': book.id,
            'title': book.title,
            'authors': book.get_authors_display(),
            'isbn': book.isbn,
            'available_copies': book.available_copies,
            'total_copies': book.total_copies,
            'status': book.status,
        })
    
    except Book.DoesNotExist:
        # Try book copy barcode
        try:
            copy = BookCopy.objects.select_related('book').get(
                school=request.school,
                barcode=barcode,
                is_active=True
            )
            
            return JsonResponse({
                'type': 'copy',
                'id': copy.id,
                'book_id': copy.book.id,
                'title': copy.book.title,
                'copy_number': copy.copy_number,
                'condition': copy.condition,
                'status': copy.status,
                'location': copy.location,
            })
        
        except BookCopy.DoesNotExist:
            return JsonResponse({'error': _('Book not found')}, status=404)


@login_required
@school_required
def library_statistics(request):
    """
    Comprehensive library statistics and analytics dashboard
    """
    from datetime import datetime, timedelta
    from django.db.models import Sum, Avg, Max, Min
    from django.utils import timezone
    
    # Date filtering
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    if not date_from:
        date_from = timezone.now() - timedelta(days=30)
    else:
        date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
    
    if not date_to:
        date_to = timezone.now().date()
    else:
        date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
    
    # Basic collection statistics
    total_books = Book.objects.filter(school=request.school, is_active=True).count()
    total_copies = BookCopy.objects.filter(school=request.school, is_active=True).count()
    available_copies = BookCopy.objects.filter(
        school=request.school, 
        is_active=True, 
        status='available'
    ).count()
    borrowed_copies = total_copies - available_copies
    total_digital = DigitalResource.objects.filter(school=request.school, is_active=True).count()
    
    # Borrowing statistics
    from .models import BookBorrowing
    active_borrowings = BookBorrowing.objects.filter(
        school=request.school,
        status__in=['active', 'overdue', 'renewed']
    ).count()
    
    overdue_borrowings = BookBorrowing.objects.filter(
        school=request.school,
        status='overdue'
    ).count()
    
    total_borrowings = BookBorrowing.objects.filter(
        school=request.school,
        borrow_date__range=[date_from, date_to]
    ).count()
    
    # Popular books (most borrowed)
    popular_books = Book.objects.filter(
        school=request.school,
        is_active=True
    ).annotate(
        borrow_count=Count('borrowings', filter=Q(
            borrowings__borrow_date__range=[date_from, date_to]
        ))
    ).order_by('-borrow_count')[:10]
    
    # Category statistics with borrowing data
    category_stats = Category.objects.filter(
        school=request.school, 
        is_active=True
    ).annotate(
        book_count=Count('books', filter=Q(books__is_active=True)),
        borrow_count=Count('books__borrowings', filter=Q(
            books__borrowings__borrow_date__range=[date_from, date_to]
        ))
    ).order_by('-borrow_count')[:10]
    
    # Author statistics with borrowing data
    author_stats = Author.objects.filter(
        school=request.school, 
        is_active=True
    ).annotate(
        book_count=Count('books', filter=Q(books__is_active=True)),
        borrow_count=Count('books__borrowings', filter=Q(
            books__borrowings__borrow_date__range=[date_from, date_to]
        ))
    ).order_by('-borrow_count')[:10]
    
    # Format statistics
    format_stats = Book.objects.filter(
        school=request.school, 
        is_active=True
    ).values('format_type').annotate(
        count=Count('id'),
        borrow_count=Count('borrowings', filter=Q(
            borrowings__borrow_date__range=[date_from, date_to]
        ))
    ).order_by('-borrow_count')
    
    # Daily borrowing trends
    daily_borrowings = BookBorrowing.objects.filter(
        school=request.school,
        borrow_date__range=[date_from, date_to]
    ).extra(
        select={'day': 'date(borrow_date)'}
    ).values('day').annotate(
        count=Count('id')
    ).order_by('day')
    
    # User type statistics
    user_type_stats = BookBorrowing.objects.filter(
        school=request.school,
        borrow_date__range=[date_from, date_to]
    ).values('borrower_type').annotate(
        count=Count('id')
    ).order_by('-count')
    
    # Fine statistics
    total_fines = BookBorrowing.objects.filter(
        school=request.school,
        fine_amount__gt=0
    ).aggregate(
        total=Sum('fine_amount'),
        avg=Avg('fine_amount'),
        max=Max('fine_amount')
    )
    
    # Digital library statistics
    digital_stats = {
        'total_views': 0,
        'total_downloads': 0,
        'popular_resources': []
    }
    
    if hasattr(request, 'digital_usage'):
        digital_usage = DigitalResourceUsage.objects.filter(
            school=request.school,
            created_at__date__range=[date_from, date_to]
        )
        
        digital_stats['total_views'] = digital_usage.filter(action='view').count()
        digital_stats['total_downloads'] = digital_usage.filter(action='download').count()
        
        digital_stats['popular_resources'] = DigitalResource.objects.filter(
            school=request.school,
            is_active=True
        ).annotate(
            usage_count=Count('usage_logs', filter=Q(
                usage_logs__created_at__date__range=[date_from, date_to]
            ))
        ).order_by('-usage_count')[:5]
    
    # Acquisition recommendations based on popular categories with low stock
    acquisition_recommendations = Category.objects.filter(
        school=request.school,
        is_active=True
    ).annotate(
        book_count=Count('books', filter=Q(books__is_active=True)),
        available_count=Count('books__copies', filter=Q(
            books__copies__status='available',
            books__copies__is_active=True
        )),
        borrow_count=Count('books__borrowings', filter=Q(
            books__borrowings__borrow_date__range=[date_from, date_to]
        ))
    ).filter(
        borrow_count__gt=0
    ).annotate(
        availability_ratio=models.Case(
            models.When(book_count=0, then=0),
            default=models.F('available_count') * 100.0 / models.F('book_count'),
            output_field=models.FloatField()
        )
    ).filter(
        availability_ratio__lt=30  # Less than 30% availability
    ).order_by('-borrow_count', 'availability_ratio')[:5]
    
    # Library performance metrics
    utilization_rate = round((borrowed_copies / total_copies * 100), 1) if total_copies > 0 else 0
    
    avg_borrowing_duration = BookBorrowing.objects.filter(
        school=request.school,
        status='returned',
        return_date__isnull=False
    ).aggregate(
        avg_duration=Avg(
            models.F('return_date') - models.F('borrow_date')
        )
    )['avg_duration']
    
    if avg_borrowing_duration:
        avg_borrowing_duration = avg_borrowing_duration.days
    else:
        avg_borrowing_duration = 0
    
    context = {
        'date_from': date_from,
        'date_to': date_to,
        
        # Collection statistics
        'total_books': total_books,
        'total_copies': total_copies,
        'available_copies': available_copies,
        'borrowed_copies': borrowed_copies,
        'total_digital': total_digital,
        
        # Borrowing statistics
        'active_borrowings': active_borrowings,
        'overdue_borrowings': overdue_borrowings,
        'total_borrowings': total_borrowings,
        
        # Popular content
        'popular_books': popular_books,
        'category_stats': category_stats,
        'author_stats': author_stats,
        'format_stats': format_stats,
        
        # Trends and patterns
        'daily_borrowings': daily_borrowings,
        'user_type_stats': user_type_stats,
        
        # Financial data
        'total_fines': total_fines,
        
        # Digital library
        'digital_stats': digital_stats,
        
        # Recommendations and metrics
        'acquisition_recommendations': acquisition_recommendations,
        'utilization_rate': utilization_rate,
        'avg_borrowing_duration': avg_borrowing_duration,
        
        # Performance indicators
        'overdue_rate': round((overdue_borrowings / active_borrowings * 100), 1) if active_borrowings > 0 else 0,
        'return_rate': round(((total_borrowings - active_borrowings) / total_borrowings * 100), 1) if total_borrowings > 0 else 0,
    }
    
    return render(request, 'library/statistics.html', context)


@login_required
@school_required
def inventory_management(request):
    """
    Inventory management view for librarians
    """
    # Get books with low stock or issues
    low_stock_books = Book.objects.filter(
        school=request.school,
        is_active=True,
        available_copies__lte=1,
        total_copies__gt=0
    ).select_related('category').prefetch_related('authors')
    
    damaged_copies = BookCopy.objects.filter(
        school=request.school,
        is_active=True,
        condition__in=['poor', 'damaged']
    ).select_related('book')
    
    lost_copies = BookCopy.objects.filter(
        school=request.school,
        is_active=True,
        status='lost'
    ).select_related('book')
    
    context = {
        'low_stock_books': low_stock_books,
        'damaged_copies': damaged_copies,
        'lost_copies': lost_copies,
    }
    
    return render(request, 'library/inventory.html', context)


@login_required
@school_required
def borrowing_system_view(request):
    """
    Main borrowing system interface
    """
    # Get active borrowings
    active_borrowings = BookBorrowing.objects.filter(
        school=request.school,
        status__in=['active', 'overdue', 'renewed']
    ).select_related('book', 'book_copy').order_by('due_date')
    
    # Get overdue borrowings
    overdue_borrowings = active_borrowings.filter(
        due_date__lt=timezone.now().date()
    )
    
    # Update overdue status
    for borrowing in overdue_borrowings:
        if borrowing.status != 'overdue':
            borrowing.status = 'overdue'
            borrowing.update_fine()
            borrowing.save()
    
    # Get recent returns
    recent_returns = BookBorrowing.objects.filter(
        school=request.school,
        status='returned',
        return_date__gte=timezone.now() - timedelta(days=7)
    ).select_related('book').order_by('-return_date')[:10]
    
    # Get borrowing statistics
    today = timezone.now().date()
    stats = {
        'total_active': active_borrowings.count(),
        'total_overdue': overdue_borrowings.count(),
        'due_today': active_borrowings.filter(due_date=today).count(),
        'due_tomorrow': active_borrowings.filter(due_date=today + timedelta(days=1)).count(),
        'total_fines': sum(b.fine_amount for b in overdue_borrowings),
    }
    
    context = {
        'active_borrowings': active_borrowings[:20],  # Limit for performance
        'overdue_borrowings': overdue_borrowings[:10],
        'recent_returns': recent_returns,
        'stats': stats,
    }
    
    return render(request, 'library/borrowing_system.html', context)


@login_required
@school_required
@require_http_methods(["POST"])
def borrow_book(request):
    """
    Process book borrowing with enhanced security validation
    """
    from core.school_utils import (
        validate_school_access_permission, check_api_access_permission,
        validate_session_security, log_security_event, get_client_ip
    )
    
    try:
        # Enhanced security validation
        is_secure, warning_msg = validate_session_security(request)
        if not is_secure:
            log_security_event(request.user, 'LIBRARY_BORROW_SECURITY_VIOLATION', warning_msg, school=request.school, ip_address=get_client_ip(request))
            return JsonResponse({
                'error': _('Session security violation detected')
            }, status=403)
        
        # Check API access permission for library operations
        has_api_access, api_error = check_api_access_permission(request.user, request.school, 'library')
        if not has_api_access:
            log_security_event(request.user, 'LIBRARY_BORROW_ACCESS_DENIED', api_error, school=request.school, ip_address=get_client_ip(request))
            return JsonResponse({
                'error': _(api_error)
            }, status=403)
        
        # Check write permission for library operations
        has_write_permission, write_error = validate_school_access_permission(request.user, request.school, 'write')
        if not has_write_permission:
            log_security_event(request.user, 'LIBRARY_BORROW_WRITE_DENIED', write_error, school=request.school, ip_address=get_client_ip(request))
            return JsonResponse({
                'error': _(write_error)
            }, status=403)
        
        book_id = request.POST.get('book_id')
        book_copy_id = request.POST.get('book_copy_id')
        borrower_type = request.POST.get('borrower_type')
        borrower_id = request.POST.get('borrower_id')
        borrower_name = request.POST.get('borrower_name')
        borrower_email = request.POST.get('borrower_email', '')
        borrower_phone = request.POST.get('borrower_phone', '')
        
        # Validate required fields
        if not all([book_id, borrower_type, borrower_id, borrower_name]):
            log_security_event(request.user, 'LIBRARY_BORROW_INVALID_DATA', 'Missing required fields', school=request.school, ip_address=get_client_ip(request))
            return JsonResponse({
                'error': _('Missing required fields')
            }, status=400)
        
        # Get book and copy
        book = get_object_or_404(Book, id=book_id, school=request.school)
        book_copy = None
        
        if book_copy_id:
            book_copy = get_object_or_404(
                BookCopy, 
                id=book_copy_id, 
                book=book, 
                school=request.school
            )
            if not book_copy.is_available:
                return JsonResponse({
                    'error': _('This book copy is not available')
                }, status=400)
        else:
            # Find an available copy
            book_copy = book.copies.filter(
                status='available',
                is_active=True
            ).first()
            
            if not book_copy:
                return JsonResponse({
                    'error': _('No available copies of this book')
                }, status=400)
        
        # Check borrower limits
        try:
            settings = LibrarySettings.objects.get(school=request.school)
            if borrower_type == 'student':
                max_books = settings.max_books_per_student
            else:
                max_books = settings.max_books_per_teacher
        except LibrarySettings.DoesNotExist:
            max_books = 3  # Default limit
        
        current_borrowings = BookBorrowing.objects.filter(
            school=request.school,
            borrower_type=borrower_type,
            borrower_id=borrower_id,
            status__in=['active', 'overdue', 'renewed']
        ).count()
        
        if current_borrowings >= max_books:
            return JsonResponse({
                'error': _('Borrower has reached the maximum book limit')
            }, status=400)
        
        # Check for outstanding fines
        outstanding_fines = BookBorrowing.objects.filter(
            school=request.school,
            borrower_type=borrower_type,
            borrower_id=borrower_id,
            fine_amount__gt=0,
            fine_paid=False,
            fine_waived=False
        ).exists()
        
        if outstanding_fines:
            return JsonResponse({
                'error': _('Borrower has outstanding fines that must be paid first')
            }, status=400)
        
        # Create borrowing record
        borrowing = BookBorrowing.objects.create(
            school=request.school,
            book=book,
            book_copy=book_copy,
            borrower_type=borrower_type,
            borrower_id=borrower_id,
            borrower_name=borrower_name,
            borrower_email=borrower_email,
            borrower_phone=borrower_phone,
            issued_by=request.user,
            created_by=request.user
        )
        
        # Create history record
        BorrowingHistory.objects.create(
            school=request.school,
            borrowing=borrowing,
            action='borrowed',
            performed_by=request.user,
            notes=f"Book borrowed: {book.title}",
            created_by=request.user
        )
        
        # Schedule reminder
        try:
            settings = LibrarySettings.objects.get(school=request.school)
            reminder_days = settings.reminder_days_before_due
        except LibrarySettings.DoesNotExist:
            reminder_days = 3
        
        if borrower_email and reminder_days > 0:
            reminder_date = borrowing.due_date - timedelta(days=reminder_days)
            if reminder_date >= timezone.now().date():
                BorrowingReminder.objects.create(
                    school=request.school,
                    borrowing=borrowing,
                    reminder_type='due_soon',
                    scheduled_date=timezone.make_aware(
                        timezone.datetime.combine(reminder_date, timezone.datetime.min.time())
                    ),
                    recipient_email=borrower_email,
                    recipient_phone=borrower_phone,
                    message_subject=f"Book Due Soon: {book.title}",
                    message_body=f"Your borrowed book '{book.title}' is due on {borrowing.due_date}. Please return it on time to avoid fines.",
                    created_by=request.user
                )
        
        return JsonResponse({
            'success': True,
            'message': _('Book borrowed successfully'),
            'borrowing_id': str(borrowing.id),
            'due_date': borrowing.due_date.strftime('%Y-%m-%d')
        })
        
    except Book.DoesNotExist:
        log_security_event(request.user, 'LIBRARY_BORROW_BOOK_NOT_FOUND', f'Book ID {book_id} not found', school=request.school, ip_address=get_client_ip(request))
        return JsonResponse({
            'success': False,
            'error': _('Book not found')
        }, status=404)
    except BookCopy.DoesNotExist:
        log_security_event(request.user, 'LIBRARY_BORROW_COPY_NOT_FOUND', f'Book copy ID {book_copy_id} not found', school=request.school, ip_address=get_client_ip(request))
        return JsonResponse({
            'success': False,
            'error': _('Book copy not found')
        }, status=404)
    except ValueError as e:
        log_security_event(request.user, 'LIBRARY_BORROW_VALIDATION_ERROR', str(e), school=request.school, ip_address=get_client_ip(request))
        return JsonResponse({
            'success': False,
            'error': _('Invalid data provided: ') + str(e)
        }, status=400)
    except PermissionError as e:
        log_security_event(request.user, 'LIBRARY_BORROW_PERMISSION_ERROR', str(e), school=request.school, ip_address=get_client_ip(request))
        return JsonResponse({
            'success': False,
            'error': _('Permission denied: ') + str(e)
        }, status=403)
    except Exception as e:
        log_security_event(request.user, 'LIBRARY_BORROW_SYSTEM_ERROR', str(e), school=request.school, ip_address=get_client_ip(request))
        return JsonResponse({
            'success': False,
            'error': _('A system error occurred. Please try again or contact support.')
        }, status=500)


@login_required
@school_required
@require_http_methods(["POST"])
def return_book(request):
    """
    Process book return with enhanced security validation
    """
    from core.school_utils import (
        validate_school_access_permission, check_api_access_permission,
        validate_session_security, log_security_event, get_client_ip
    )
    
    try:
        # Enhanced security validation
        is_secure, warning_msg = validate_session_security(request)
        if not is_secure:
            log_security_event(request.user, 'LIBRARY_RETURN_SECURITY_VIOLATION', warning_msg, school=request.school, ip_address=get_client_ip(request))
            return JsonResponse({
                'error': _('Session security violation detected')
            }, status=403)
        
        # Check API access permission for library operations
        has_api_access, api_error = check_api_access_permission(request.user, request.school, 'library')
        if not has_api_access:
            log_security_event(request.user, 'LIBRARY_RETURN_ACCESS_DENIED', api_error, school=request.school, ip_address=get_client_ip(request))
            return JsonResponse({
                'error': _(api_error)
            }, status=403)
        
        # Check write permission for library operations
        has_write_permission, write_error = validate_school_access_permission(request.user, request.school, 'write')
        if not has_write_permission:
            log_security_event(request.user, 'LIBRARY_RETURN_WRITE_DENIED', write_error, school=request.school, ip_address=get_client_ip(request))
            return JsonResponse({
                'error': _(write_error)
            }, status=403)
        
        borrowing_id = request.POST.get('borrowing_id')
        condition_notes = request.POST.get('condition_notes', '')
        
        if not borrowing_id:
            log_security_event(request.user, 'LIBRARY_RETURN_INVALID_DATA', 'Missing borrowing ID', school=request.school, ip_address=get_client_ip(request))
            return JsonResponse({
                'error': _('Borrowing ID is required')
            }, status=400)
        
        borrowing = get_object_or_404(
            BookBorrowing,
            id=borrowing_id,
            school=request.school,
            status__in=['active', 'overdue', 'renewed']
        )
        
        # Calculate final fine
        if borrowing.is_overdue:
            borrowing.update_fine()
        
        # Return the book
        borrowing.return_book(
            returned_to=request.user,
            condition_notes=condition_notes
        )
        
        # Create history record
        BorrowingHistory.objects.create(
            school=request.school,
            borrowing=borrowing,
            action='returned',
            performed_by=request.user,
            notes=f"Book returned: {borrowing.book.title}" + 
                  (f" - Condition: {condition_notes}" if condition_notes else ""),
            created_by=request.user
        )
        
        return JsonResponse({
            'success': True,
            'message': _('Book returned successfully'),
            'fine_amount': float(borrowing.fine_amount)
        })
        
    except BookBorrowing.DoesNotExist:
        log_security_event(request.user, 'LIBRARY_RETURN_NOT_FOUND', f'Borrowing ID {borrowing_id} not found', school=request.school, ip_address=get_client_ip(request))
        return JsonResponse({
            'success': False,
            'error': _('Borrowing record not found or already returned')
        }, status=404)
    except ValueError as e:
        log_security_event(request.user, 'LIBRARY_RETURN_VALIDATION_ERROR', str(e), school=request.school, ip_address=get_client_ip(request))
        return JsonResponse({
            'success': False,
            'error': _('Invalid data provided: ') + str(e)
        }, status=400)
    except PermissionError as e:
        log_security_event(request.user, 'LIBRARY_RETURN_PERMISSION_ERROR', str(e), school=request.school, ip_address=get_client_ip(request))
        return JsonResponse({
            'success': False,
            'error': _('Permission denied: ') + str(e)
        }, status=403)
    except Exception as e:
        log_security_event(request.user, 'LIBRARY_RETURN_SYSTEM_ERROR', str(e), school=request.school, ip_address=get_client_ip(request))
        return JsonResponse({
            'success': False,
            'error': _('A system error occurred. Please try again or contact support.')
        }, status=500)


@login_required
@school_required
@require_http_methods(["POST"])
def renew_book(request):
    """
    Renew book borrowing
    """
    try:
        borrowing_id = request.POST.get('borrowing_id')
        
        if not borrowing_id:
            return JsonResponse({
                'error': _('Borrowing ID is required')
            }, status=400)
        
        borrowing = get_object_or_404(
            BookBorrowing,
            id=borrowing_id,
            school=request.school
        )
        
        if not borrowing.can_renew:
            reasons = []
            if borrowing.status not in ['active', 'renewed']:
                reasons.append(_('Book is not currently active'))
            if borrowing.renewal_count >= borrowing.max_renewals:
                reasons.append(_('Maximum renewals reached'))
            if borrowing.is_overdue:
                reasons.append(_('Book is overdue'))
            if borrowing.fine_amount > 0:
                reasons.append(_('Outstanding fines must be paid'))
            
            return JsonResponse({
                'error': _('Cannot renew: ') + ', '.join(reasons)
            }, status=400)
        
        # Renew the book
        borrowing.renew(renewed_by=request.user)
        
        # Create history record
        BorrowingHistory.objects.create(
            school=request.school,
            borrowing=borrowing,
            action='renewed',
            performed_by=request.user,
            notes=f"Book renewed: {borrowing.book.title} (Renewal #{borrowing.renewal_count})",
            created_by=request.user
        )
        
        return JsonResponse({
            'success': True,
            'message': _('Book renewed successfully'),
            'new_due_date': borrowing.due_date.strftime('%Y-%m-%d')
        })
        
    except BookBorrowing.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': _('Borrowing record not found')
        }, status=404)
    except ValueError as e:
        return JsonResponse({
            'success': False,
            'error': _('Invalid data provided: ') + str(e)
        }, status=400)
    except PermissionError as e:
        return JsonResponse({
            'success': False,
            'error': _('Permission denied: ') + str(e)
        }, status=403)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': _('A system error occurred. Please try again or contact support.')
        }, status=500)


@login_required
@school_required
def borrower_search(request):
    """
    Search for borrowers (students, teachers, etc.)
    """
    query = request.GET.get('q', '').strip()
    borrower_type = request.GET.get('type', 'student')
    
    if len(query) < 2:
        return JsonResponse({'results': []})
    
    results = []
    
    if borrower_type == 'student':
        # Import here to avoid circular imports
        from students.models import Student
        
        students = Student.objects.filter(
            school=request.school,
            is_active=True
        ).filter(
            Q(student_id__icontains=query) |
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query) |
            Q(first_name_ar__icontains=query) |
            Q(last_name_ar__icontains=query)
        )[:10]
        
        results = [
            {
                'id': student.student_id,
                'name': f"{student.first_name} {student.last_name}",
                'name_ar': f"{student.first_name_ar or ''} {student.last_name_ar or ''}".strip(),
                'email': getattr(student.user, 'email', '') if hasattr(student, 'user') else '',
                'phone': getattr(student, 'phone', ''),
                'grade': str(student.current_grade) if hasattr(student, 'current_grade') and student.current_grade else '',
            }
            for student in students
        ]
    
    elif borrower_type == 'teacher':
        # Import here to avoid circular imports
        from hr.models import Employee
        
        teachers = Employee.objects.filter(
            school=request.school,
            is_active=True,
            position__icontains='teacher'
        ).filter(
            Q(employee_id__icontains=query) |
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query)
        )[:10]
        
        results = [
            {
                'id': teacher.employee_id,
                'name': f"{teacher.first_name} {teacher.last_name}",
                'email': teacher.email,
                'phone': getattr(teacher, 'phone', ''),
                'department': getattr(teacher, 'department', ''),
            }
            for teacher in teachers
        ]
    
    return JsonResponse({'results': results})


@login_required
@school_required
def read_online_view(request, resource_id):
    """
    Online reading interface for digital resources
    """
    resource = get_object_or_404(
        DigitalResource,
        id=resource_id,
        school=request.school,
        is_active=True
    )
    
    # Check access permissions
    if resource.access_type == 'restricted' and not request.user.is_staff:
        raise PermissionDenied("Access to this resource is restricted")
    
    # Update view count
    resource.view_count += 1
    resource.last_accessed = timezone.now()
    resource.save(update_fields=['view_count', 'last_accessed'])
    
    # Log usage for analytics
    from .models import DigitalResourceUsage
    DigitalResourceUsage.objects.create(
        school=request.school,
        resource=resource,
        user=request.user,
        action='view',
        ip_address=request.META.get('REMOTE_ADDR'),
        user_agent=request.META.get('HTTP_USER_AGENT', ''),
        created_by=request.user
    )
    
    context = {
        'resource': resource,
    }
    
    return render(request, 'library/read_online.html', context)


@login_required
@school_required
def watch_online_view(request, resource_id):
    """
    Online video watching interface
    """
    resource = get_object_or_404(
        DigitalResource,
        id=resource_id,
        school=request.school,
        is_active=True,
        resource_type='video'
    )
    
    # Check access permissions
    if resource.access_type == 'restricted' and not request.user.is_staff:
        raise PermissionDenied("Access to this resource is restricted")
    
    # Update view count
    resource.view_count += 1
    resource.last_accessed = timezone.now()
    resource.save(update_fields=['view_count', 'last_accessed'])
    
    # Log usage for analytics
    from .models import DigitalResourceUsage
    DigitalResourceUsage.objects.create(
        school=request.school,
        resource=resource,
        user=request.user,
        action='watch',
        ip_address=request.META.get('REMOTE_ADDR'),
        user_agent=request.META.get('HTTP_USER_AGENT', ''),
        created_by=request.user
    )
    
    context = {
        'resource': resource,
    }
    
    return render(request, 'library/watch_online.html', context)


@login_required
@school_required
def listen_online_view(request, resource_id):
    """
    Online audio listening interface
    """
    resource = get_object_or_404(
        DigitalResource,
        id=resource_id,
        school=request.school,
        is_active=True,
        resource_type='audiobook'
    )
    
    # Check access permissions
    if resource.access_type == 'restricted' and not request.user.is_staff:
        raise PermissionDenied("Access to this resource is restricted")
    
    # Update view count
    resource.view_count += 1
    resource.last_accessed = timezone.now()
    resource.save(update_fields=['view_count', 'last_accessed'])
    
    # Log usage for analytics
    from .models import DigitalResourceUsage
    DigitalResourceUsage.objects.create(
        school=request.school,
        resource=resource,
        user=request.user,
        action='listen',
        ip_address=request.META.get('REMOTE_ADDR'),
        user_agent=request.META.get('HTTP_USER_AGENT', ''),
        created_by=request.user
    )
    
    context = {
        'resource': resource,
    }
    
    return render(request, 'library/listen_online.html', context)


@login_required
@school_required
def download_resource_view(request, resource_id):
    """
    Download digital resource with access control
    """
    resource = get_object_or_404(
        DigitalResource,
        id=resource_id,
        school=request.school,
        is_active=True
    )
    
    # Check access permissions
    if resource.access_type == 'restricted' and not request.user.is_staff:
        raise PermissionDenied("Download access to this resource is restricted")
    
    if not resource.file_path:
        return JsonResponse({'error': _('File not available for download')}, status=404)
    
    # Update download count
    resource.download_count += 1
    resource.last_accessed = timezone.now()
    resource.save(update_fields=['download_count', 'last_accessed'])
    
    # Log usage for analytics
    from .models import DigitalResourceUsage
    DigitalResourceUsage.objects.create(
        school=request.school,
        resource=resource,
        user=request.user,
        action='download',
        ip_address=request.META.get('REMOTE_ADDR'),
        user_agent=request.META.get('HTTP_USER_AGENT', ''),
        created_by=request.user
    )
    
    # Serve file
    from django.http import FileResponse
    import os
    
    file_path = resource.file_path.path
    if os.path.exists(file_path):
        response = FileResponse(
            open(file_path, 'rb'),
            as_attachment=True,
            filename=f"{resource.title}.{resource.file_format}"
        )
        return response
    else:
        return JsonResponse({'error': _('File not found')}, status=404)


@login_required
@school_required
def related_resources_api(request, resource_id):
    """
    API to get related digital resources
    """
    resource = get_object_or_404(
        DigitalResource,
        id=resource_id,
        school=request.school,
        is_active=True
    )
    
    # Find related resources based on:
    # 1. Same category
    # 2. Same authors
    # 3. Similar keywords
    related = DigitalResource.objects.filter(
        school=request.school,
        is_active=True
    ).exclude(id=resource.id)
    
    # Same category
    if resource.category:
        category_related = related.filter(category=resource.category)[:3]
    else:
        category_related = DigitalResource.objects.none()
    
    # Same authors
    if resource.authors.exists():
        author_related = related.filter(
            authors__in=resource.authors.all()
        ).distinct()[:3]
    else:
        author_related = DigitalResource.objects.none()
    
    # Combine and limit results
    related_resources = list(category_related) + list(author_related)
    # Remove duplicates while preserving order
    seen = set()
    unique_related = []
    for r in related_resources:
        if r.id not in seen:
            unique_related.append(r)
            seen.add(r.id)
    
    # Limit to 5 resources
    unique_related = unique_related[:5]
    
    results = [
        {
            'id': str(r.id),
            'title': r.title,
            'resource_type': r.get_resource_type_display(),
            'authors': r.get_authors_display(),
            'thumbnail': r.thumbnail.url if r.thumbnail else None,
        }
        for r in unique_related
    ]
    
    return JsonResponse({'resources': results})


@login_required
@school_required
@require_http_methods(["POST"])
def report_issue_api(request):
    """
    API to report issues with digital resources
    """
    try:
        import json
        data = json.loads(request.body)
        resource_id = data.get('resource_id')
        issue_description = data.get('issue_description')
        
        if not resource_id or not issue_description:
            return JsonResponse({'error': _('Missing required fields')}, status=400)
        
        resource = get_object_or_404(
            DigitalResource,
            id=resource_id,
            school=request.school,
            is_active=True
        )
        
        # Create issue report
        from .models import DigitalResourceIssue
        DigitalResourceIssue.objects.create(
            school=request.school,
            resource=resource,
            reported_by=request.user,
            issue_type='general',
            description=issue_description,
            status='open',
            created_by=request.user
        )
        
        return JsonResponse({'success': True, 'message': _('Issue reported successfully')})
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@school_required
def digital_usage_analytics_view(request):
    """
    Digital library usage analytics
    """
    from django.db.models import Count, Sum, Avg
    from datetime import timedelta
    
    # Date range filter
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    if not date_from:
        date_from = (timezone.now() - timedelta(days=30)).date()
    else:
        date_from = timezone.datetime.strptime(date_from, '%Y-%m-%d').date()
    
    if not date_to:
        date_to = timezone.now().date()
    else:
        date_to = timezone.datetime.strptime(date_to, '%Y-%m-%d').date()
    
    # Base queryset for usage
    from .models import DigitalResourceUsage
    usage_qs = DigitalResourceUsage.objects.filter(
        school=request.school,
        created_at__date__range=[date_from, date_to]
    )
    
    # Overall statistics
    total_resources = DigitalResource.objects.filter(
        school=request.school,
        is_active=True
    ).count()
    
    total_usage = usage_qs.count()
    unique_users = usage_qs.values('user').distinct().count()
    
    # Usage by action
    usage_by_action = usage_qs.values('action').annotate(
        count=Count('id')
    ).order_by('-count')
    
    # Most popular resources
    popular_resources = DigitalResource.objects.filter(
        school=request.school,
        is_active=True
    ).annotate(
        total_usage=Count('usage_logs', filter=Q(
            usage_logs__created_at__date__range=[date_from, date_to]
        ))
    ).order_by('-total_usage')[:10]
    
    # Usage by resource type
    usage_by_type = usage_qs.values(
        'resource__resource_type'
    ).annotate(
        count=Count('id')
    ).order_by('-count')
    
    # Daily usage trend
    daily_usage = usage_qs.extra(
        select={'day': 'date(created_at)'}
    ).values('day').annotate(
        count=Count('id')
    ).order_by('day')
    
    context = {
        'date_from': date_from,
        'date_to': date_to,
        'total_resources': total_resources,
        'total_usage': total_usage,
        'unique_users': unique_users,
        'usage_by_action': usage_by_action,
        'popular_resources': popular_resources,
        'usage_by_type': usage_by_type,
        'daily_usage': daily_usage,
    }
    
    return render(request, 'library/digital_usage_analytics.html', context)


@login_required
@school_required
def borrowing_history_view(request, borrower_type=None, borrower_id=None):
    """
    View borrowing history for a specific borrower or all borrowers
    """
    borrowings = BookBorrowing.objects.filter(
        school=request.school
    ).select_related('book', 'book_copy')
    
    if borrower_type and borrower_id:
        borrowings = borrowings.filter(
            borrower_type=borrower_type,
            borrower_id=borrower_id
        )
    
    # Apply filters
    status_filter = request.GET.get('status')
    if status_filter:
        borrowings = borrowings.filter(status=status_filter)
    
    date_from = request.GET.get('date_from')
    if date_from:
        borrowings = borrowings.filter(borrow_date__gte=date_from)
    
    date_to = request.GET.get('date_to')
    if date_to:
        borrowings = borrowings.filter(borrow_date__lte=date_to)
    
    # Pagination
    paginator = Paginator(borrowings.order_by('-borrow_date'), 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'borrower_type': borrower_type,
        'borrower_id': borrower_id,
        'status_filter': status_filter,
        'date_from': date_from,
        'date_to': date_to,
        'status_choices': BookBorrowing.STATUS_CHOICES,
    }
    
    return render(request, 'library/borrowing_history.html', context)


@login_required
@school_required
def overdue_books_view(request):
    """
    View and manage overdue books
    """
    overdue_borrowings = BookBorrowing.objects.filter(
        school=request.school,
        status__in=['active', 'overdue', 'renewed'],
        due_date__lt=timezone.now().date()
    ).select_related('book', 'book_copy').order_by('due_date')
    
    # Update fines for all overdue books
    for borrowing in overdue_borrowings:
        if borrowing.status != 'overdue':
            borrowing.status = 'overdue'
            borrowing.save()
        borrowing.update_fine()
    
    # Group by days overdue
    overdue_groups = {
        '1-7': [],
        '8-14': [],
        '15-30': [],
        '30+': []
    }
    
    for borrowing in overdue_borrowings:
        days = borrowing.days_overdue
        if days <= 7:
            overdue_groups['1-7'].append(borrowing)
        elif days <= 14:
            overdue_groups['8-14'].append(borrowing)
        elif days <= 30:
            overdue_groups['15-30'].append(borrowing)
        else:
            overdue_groups['30+'].append(borrowing)
    
    context = {
        'overdue_borrowings': overdue_borrowings,
        'overdue_groups': overdue_groups,
        'total_overdue': overdue_borrowings.count(),
        'total_fines': sum(b.fine_amount for b in overdue_borrowings),
    }
    
    return render(request, 'library/overdue_books.html', context)


@login_required
@school_required
def borrowing_analytics_view(request):
    """
    Borrowing analytics and reports
    """
    # Date range
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    if not date_from:
        date_from = (timezone.now() - timedelta(days=30)).date()
    else:
        date_from = timezone.datetime.strptime(date_from, '%Y-%m-%d').date()
    
    if not date_to:
        date_to = timezone.now().date()
    else:
        date_to = timezone.datetime.strptime(date_to, '%Y-%m-%d').date()
    
    # Basic statistics
    borrowings = BookBorrowing.objects.filter(
        school=request.school,
        borrow_date__date__range=[date_from, date_to]
    )
    
    stats = {
        'total_borrowings': borrowings.count(),
        'total_returns': borrowings.filter(status='returned').count(),
        'total_renewals': borrowings.aggregate(
            total=models.Sum('renewal_count')
        )['total'] or 0,
        'total_overdue': borrowings.filter(status='overdue').count(),
        'total_lost': borrowings.filter(status='lost').count(),
        'total_fines': borrowings.aggregate(
            total=models.Sum('fine_amount')
        )['total'] or 0,
    }
    
    # Popular books
    popular_books = borrowings.values(
        'book__title', 'book__id'
    ).annotate(
        borrow_count=Count('id')
    ).order_by('-borrow_count')[:10]
    
    # Borrower type statistics
    borrower_stats = borrowings.values('borrower_type').annotate(
        count=Count('id')
    ).order_by('-count')
    
    # Category statistics
    category_stats = borrowings.values(
        'book__category__name'
    ).annotate(
        count=Count('id')
    ).order_by('-count')[:10]
    
    # Daily borrowing trend
    daily_stats = borrowings.extra(
        select={'day': 'date(borrow_date)'}
    ).values('day').annotate(
        count=Count('id')
    ).order_by('day')
    
    context = {
        'date_from': date_from,
        'date_to': date_to,
        'stats': stats,
        'popular_books': popular_books,
        'borrower_stats': borrower_stats,
        'category_stats': category_stats,
        'daily_stats': daily_stats,
    }
    
    return render(request, 'library/borrowing_analytics.html', context)


@login_required
@school_required
@require_http_methods(["POST"])
def api_bookmark(request):
    """
    API to bookmark/unbookmark digital resources
    """
    try:
        data = json.loads(request.body)
        resource_id = data.get('resource_id')
        bookmarked = data.get('bookmarked', False)
        
        if not resource_id:
            return JsonResponse({
                'error': _('Resource ID is required')
            }, status=400)
        
        resource = get_object_or_404(
            DigitalResource,
            id=resource_id,
            school=request.school,
            is_active=True
        )
        
        # Create or update bookmark
        from .models import DigitalResourceBookmark
        bookmark, created = DigitalResourceBookmark.objects.get_or_create(
            school=request.school,
            resource=resource,
            user=request.user,
            defaults={'created_by': request.user}
        )
        
        if not bookmarked and not created:
            bookmark.delete()
            return JsonResponse({
                'success': True,
                'bookmarked': False,
                'message': _('Bookmark removed')
            })
        
        return JsonResponse({
            'success': True,
            'bookmarked': True,
            'message': _('Resource bookmarked')
        })
        
    except Exception as e:
        return JsonResponse({
            'error': str(e)
        }, status=500)


@login_required
@school_required
@require_http_methods(["POST"])
def api_reading_progress(request):
    """
    API to save reading progress
    """
    try:
        data = json.loads(request.body)
        resource_id = data.get('resource_id')
        position = data.get('position', 0)
        duration = data.get('duration', 0)
        
        if not resource_id:
            return JsonResponse({
                'error': _('Resource ID is required')
            }, status=400)
        
        resource = get_object_or_404(
            DigitalResource,
            id=resource_id,
            school=request.school,
            is_active=True
        )
        
        # Log usage for analytics
        DigitalResourceUsage.objects.create(
            school=request.school,
            resource=resource,
            user=request.user,
            action='view',
            session_duration=timezone.timedelta(milliseconds=duration) if duration else None,
            ip_address=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            created_by=request.user
        )
        
        return JsonResponse({
            'success': True,
            'message': _('Progress saved')
        })
        
    except Exception as e:
        return JsonResponse({
            'error': str(e)
        }, status=500)


@login_required
@school_required
def api_text_content(request, resource_id):
    """
    API to get text content of a resource
    """
    try:
        resource = get_object_or_404(
            DigitalResource,
            id=resource_id,
            school=request.school,
            is_active=True
        )
        
        # Check access permissions
        if resource.access_type == 'restricted' and not request.user.is_staff:
            return JsonResponse({
                'error': _('Access denied')
            }, status=403)
        
        # For now, return placeholder content
        # In a real implementation, you would extract text from the file
        content = f"""
        <div class="text-content">
            <h1>{resource.title}</h1>
            {f'<p><em>By {resource.get_authors_display()}</em></p>' if resource.get_authors_display() else ''}
            <div class="content-body">
                {resource.description if resource.description else '<p>This is a placeholder for the actual text content of the resource.</p><p>In a real implementation, you would extract and format the text content from the uploaded file.</p>'}
            </div>
        </div>
        """
        
        return JsonResponse({
            'success': True,
            'content': content
        })
        
    except Exception as e:
        return JsonResponse({
            'error': str(e)
        }, status=500)


@login_required
@school_required
@require_http_methods(["POST"])
def api_watch_progress(request):
    """
    API to save video watch progress
    """
    try:
        data = json.loads(request.body)
        resource_id = data.get('resource_id')
        position = data.get('position', 0)
        duration = data.get('duration', 0)
        watch_time = data.get('watch_time', 0)
        
        if not resource_id:
            return JsonResponse({
                'error': _('Resource ID is required')
            }, status=400)
        
        resource = get_object_or_404(
            DigitalResource,
            id=resource_id,
            school=request.school,
            is_active=True,
            resource_type='video'
        )
        
        # Log usage for analytics
        DigitalResourceUsage.objects.create(
            school=request.school,
            resource=resource,
            user=request.user,
            action='watch',
            session_duration=timezone.timedelta(milliseconds=watch_time) if watch_time else None,
            ip_address=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            created_by=request.user
        )
        
        return JsonResponse({
            'success': True,
            'message': _('Watch progress saved')
        })
        
    except Exception as e:
        return JsonResponse({
            'error': str(e)
        }, status=500)


@login_required
@school_required
@require_http_methods(["POST"])
def api_listen_progress(request):
    """
    API to save audio listen progress
    """
    try:
        data = json.loads(request.body)
        resource_id = data.get('resource_id')
        position = data.get('position', 0)
        duration = data.get('duration', 0)
        listen_time = data.get('listen_time', 0)
        
        if not resource_id:
            return JsonResponse({
                'error': _('Resource ID is required')
            }, status=400)
        
        resource = get_object_or_404(
            DigitalResource,
            id=resource_id,
            school=request.school,
            is_active=True,
            resource_type='audiobook'
        )
        
        # Log usage for analytics
        DigitalResourceUsage.objects.create(
            school=request.school,
            resource=resource,
            user=request.user,
            action='listen',
            session_duration=timezone.timedelta(milliseconds=listen_time) if listen_time else None,
            ip_address=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            created_by=request.user
        )
        
        return JsonResponse({
            'success': True,
            'message': _('Listen progress saved')
        })
        
    except Exception as e:
        return JsonResponse({
            'error': str(e)
        }, status=500)
@login_required
@school_required
def library_dashboard(request):
    """
    Comprehensive library dashboard with key metrics and quick actions
    """
    from datetime import datetime, timedelta
    from django.db.models import Sum, Avg, Count, Q
    from django.utils import timezone
    
    # Quick stats for dashboard cards
    today = timezone.now().date()
    week_ago = today - timedelta(days=7)
    month_ago = today - timedelta(days=30)
    
    # Collection overview
    total_books = Book.objects.filter(school=request.school, is_active=True).count()
    total_digital = DigitalResource.objects.filter(school=request.school, is_active=True).count()
    
    # Current activity
    active_borrowings = BookBorrowing.objects.filter(
        school=request.school,
        status__in=['active', 'overdue', 'renewed']
    ).count()
    
    overdue_count = BookBorrowing.objects.filter(
        school=request.school,
        status='overdue'
    ).count()
    
    # Recent activity (last 7 days)
    recent_borrowings = BookBorrowing.objects.filter(
        school=request.school,
        borrow_date__gte=week_ago
    ).count()
    
    recent_returns = BookBorrowing.objects.filter(
        school=request.school,
        return_date__gte=week_ago,
        status='returned'
    ).count()
    
    # Digital library activity
    digital_views_week = DigitalResourceUsage.objects.filter(
        school=request.school,
        created_at__date__gte=week_ago,
        action='view'
    ).count()
    
    # Top borrowed books this month
    popular_books_month = Book.objects.filter(
        school=request.school,
        is_active=True
    ).annotate(
        borrow_count=Count('borrowings', filter=Q(
            borrowings__borrow_date__gte=month_ago
        ))
    ).filter(borrow_count__gt=0).order_by('-borrow_count')[:5]
    
    # Recent new acquisitions
    recent_acquisitions = Book.objects.filter(
        school=request.school,
        is_active=True,
        acquisition_date__gte=month_ago
    ).order_by('-acquisition_date')[:5]
    
    # Alerts and notifications
    alerts = []
    
    # Overdue books alert
    if overdue_count > 0:
        alerts.append({
            'type': 'warning',
            'icon': 'fas fa-exclamation-triangle',
            'title': f'{overdue_count} Overdue Books',
            'message': 'Books need to be returned',
            'action_url': reverse('library:overdue_books'),
            'action_text': 'View Overdue'
        })
    
    # Low stock alert
    low_stock_books = Book.objects.filter(
        school=request.school,
        is_active=True,
        available_copies__lte=1,
        total_copies__gt=0
    ).count()
    
    if low_stock_books > 0:
        alerts.append({
            'type': 'info',
            'icon': 'fas fa-info-circle',
            'title': f'{low_stock_books} Books Low Stock',
            'message': 'Consider acquiring more copies',
            'action_url': reverse('library:inventory'),
            'action_text': 'View Inventory'
        })
    
    # High demand categories (for acquisition recommendations)
    high_demand_categories = Category.objects.filter(
        school=request.school,
        is_active=True
    ).annotate(
        borrow_count=Count('books__borrowings', filter=Q(
            books__borrowings__borrow_date__gte=month_ago
        ))
    ).filter(borrow_count__gt=5).order_by('-borrow_count')[:3]
    
    context = {
        # Quick stats
        'total_books': total_books,
        'total_digital': total_digital,
        'active_borrowings': active_borrowings,
        'overdue_count': overdue_count,
        'recent_borrowings': recent_borrowings,
        'recent_returns': recent_returns,
        'digital_views_week': digital_views_week,
        
        # Content lists
        'popular_books_month': popular_books_month,
        'recent_acquisitions': recent_acquisitions,
        'high_demand_categories': high_demand_categories,
        
        # Alerts
        'alerts': alerts,
        
        # Quick actions
        'quick_actions': [
            {
                'title': 'Add New Book',
                'icon': 'fas fa-plus',
                'url': '/admin/library/book/add/',
                'color': 'primary'
            },
            {
                'title': 'Borrow Book',
                'icon': 'fas fa-hand-holding',
                'url': reverse('library:borrowing_system'),
                'color': 'success'
            },
            {
                'title': 'View Statistics',
                'icon': 'fas fa-chart-bar',
                'url': reverse('library:statistics'),
                'color': 'info'
            },
            {
                'title': 'Digital Library',
                'icon': 'fas fa-tablet-alt',
                'url': reverse('library:digital_library'),
                'color': 'secondary'
            }
        ]
    }
    
    return render(request, 'library/dashboard.html', context)


@login_required
@school_required
def library_reports(request):
    """
    Generate various library reports
    """
    from datetime import datetime, timedelta
    from django.http import HttpResponse
    from django.template.loader import render_to_string
    import csv
    import json
    
    report_type = request.GET.get('type', 'summary')
    format_type = request.GET.get('format', 'html')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    # Default date range (last 30 days)
    if not date_from:
        date_from = timezone.now() - timedelta(days=30)
    else:
        date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
    
    if not date_to:
        date_to = timezone.now().date()
    else:
        date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
    
    context = {
        'date_from': date_from,
        'date_to': date_to,
        'report_type': report_type,
        'school': request.school
    }
    
    if report_type == 'borrowing':
        # Borrowing report
        borrowings = BookBorrowing.objects.filter(
            school=request.school,
            borrow_date__range=[date_from, date_to]
        ).select_related('book', 'book_copy').order_by('-borrow_date')
        
        context['borrowings'] = borrowings
        context['total_borrowings'] = borrowings.count()
        context['returned_count'] = borrowings.filter(status='returned').count()
        context['overdue_count'] = borrowings.filter(status='overdue').count()
        
    elif report_type == 'popular':
        # Popular books report
        popular_books = Book.objects.filter(
            school=request.school,
            is_active=True
        ).annotate(
            borrow_count=Count('borrowings', filter=Q(
                borrowings__borrow_date__range=[date_from, date_to]
            ))
        ).filter(borrow_count__gt=0).order_by('-borrow_count')
        
        context['popular_books'] = popular_books
        
    elif report_type == 'acquisition':
        # Acquisition recommendations
        recommendations = Category.objects.filter(
            school=request.school,
            is_active=True
        ).annotate(
            book_count=Count('books', filter=Q(books__is_active=True)),
            borrow_count=Count('books__borrowings', filter=Q(
                books__borrowings__borrow_date__range=[date_from, date_to]
            )),
            available_count=Count('books__copies', filter=Q(
                books__copies__status='available',
                books__copies__is_active=True
            ))
        ).filter(borrow_count__gt=0).order_by('-borrow_count')
        
        context['recommendations'] = recommendations
        
    elif report_type == 'digital':
        # Digital library usage report
        digital_usage = DigitalResourceUsage.objects.filter(
            school=request.school,
            created_at__date__range=[date_from, date_to]
        ).select_related('resource', 'user')
        
        context['digital_usage'] = digital_usage
        context['total_views'] = digital_usage.filter(action='view').count()
        context['total_downloads'] = digital_usage.filter(action='download').count()
        
    # Handle different output formats
    if format_type == 'csv':
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="library_{report_type}_report.csv"'
        
        writer = csv.writer(response)
        
        if report_type == 'borrowing':
            writer.writerow(['Book Title', 'Borrower', 'Borrow Date', 'Due Date', 'Status'])
            for borrowing in context['borrowings']:
                writer.writerow([
                    borrowing.book.title,
                    borrowing.borrower_name,
                    borrowing.borrow_date,
                    borrowing.due_date,
                    borrowing.status
                ])
        elif report_type == 'popular':
            writer.writerow(['Book Title', 'Author', 'Category', 'Borrow Count'])
            for book in context['popular_books']:
                writer.writerow([
                    book.title,
                    book.get_authors_display(),
                    book.category.name if book.category else '',
                    book.borrow_count
                ])
        
        return response
    
    elif format_type == 'json':
        # Return JSON data for API consumption
        if report_type == 'borrowing':
            data = [{
                'book_title': b.book.title,
                'borrower': b.borrower_name,
                'borrow_date': b.borrow_date.isoformat(),
                'due_date': b.due_date.isoformat(),
                'status': b.status
            } for b in context['borrowings']]
        elif report_type == 'popular':
            data = [{
                'book_title': b.title,
                'author': b.get_authors_display(),
                'category': b.category.name if b.category else '',
                'borrow_count': b.borrow_count
            } for b in context['popular_books']]
        else:
            data = {'message': 'Report data not available in JSON format'}
        
        return JsonResponse({'data': data, 'report_type': report_type})
    
    # Default HTML format
    return render(request, 'library/reports.html', context)
@login_required
@school_required
def comprehensive_library_analytics(request):
    """
    Comprehensive library analytics dashboard with all metrics
    """
    from django.db.models import Count, Sum, Avg, Q
    from datetime import datetime, timedelta
    
    # Date range filters
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    if not date_from:
        date_from = timezone.now() - timedelta(days=30)
    else:
        date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
    
    if not date_to:
        date_to = timezone.now().date()
    else:
        date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
    
    # Collection Statistics
    total_books = Book.objects.filter(school=request.school, is_active=True).count()
    total_digital = DigitalResource.objects.filter(school=request.school, is_active=True).count()
    total_categories = Category.objects.filter(school=request.school, is_active=True).count()
    total_authors = Author.objects.filter(school=request.school, is_active=True).count()
    
    # Borrowing Statistics
    total_borrowings = BookBorrowing.objects.filter(
        school=request.school,
        borrow_date__range=[date_from, date_to]
    ).count()
    
    active_borrowings = BookBorrowing.objects.filter(
        school=request.school,
        status__in=['active', 'overdue', 'renewed']
    ).count()
    
    overdue_books = BookBorrowing.objects.filter(
        school=request.school,
        status='overdue'
    ).count()
    
    # Digital Library Statistics
    digital_views = DigitalResourceUsage.objects.filter(
        school=request.school,
        created_at__date__range=[date_from, date_to],
        action='view'
    ).count()
    
    digital_downloads = DigitalResourceUsage.objects.filter(
        school=request.school,
        created_at__date__range=[date_from, date_to],
        action='download'
    ).count()
    
    # Popular Books
    popular_books = Book.objects.filter(
        school=request.school,
        is_active=True
    ).annotate(
        borrow_count=Count('borrowings', filter=Q(
            borrowings__borrow_date__range=[date_from, date_to]
        ))
    ).filter(borrow_count__gt=0).order_by('-borrow_count')[:10]
    
    # Popular Digital Resources
    popular_digital = DigitalResource.objects.filter(
        school=request.school,
        is_active=True
    ).annotate(
        usage_count=Count('usage_logs', filter=Q(
            usage_logs__created_at__date__range=[date_from, date_to]
        ))
    ).filter(usage_count__gt=0).order_by('-usage_count')[:10]
    
    # Category Performance
    category_stats = Category.objects.filter(
        school=request.school,
        is_active=True
    ).annotate(
        book_count=Count('books', filter=Q(books__is_active=True)),
        borrow_count=Count('books__borrowings', filter=Q(
            books__borrowings__borrow_date__range=[date_from, date_to]
        )),
        digital_count=Count('digital_resources', filter=Q(digital_resources__is_active=True)),
        digital_usage=Count('digital_resources__usage_logs', filter=Q(
            digital_resources__usage_logs__created_at__date__range=[date_from, date_to]
        ))
    ).order_by('-borrow_count', '-digital_usage')
    
    # Daily Activity Trends
    daily_borrowings = BookBorrowing.objects.filter(
        school=request.school,
        borrow_date__range=[date_from, date_to]
    ).extra(
        select={'day': 'date(borrow_date)'}
    ).values('day').annotate(
        count=Count('id')
    ).order_by('day')
    
    daily_digital_usage = DigitalResourceUsage.objects.filter(
        school=request.school,
        created_at__date__range=[date_from, date_to]
    ).extra(
        select={'day': 'date(created_at)'}
    ).values('day').annotate(
        count=Count('id')
    ).order_by('day')
    
    # User Engagement
    unique_borrowers = BookBorrowing.objects.filter(
        school=request.school,
        borrow_date__range=[date_from, date_to]
    ).values('borrower_id').distinct().count()
    
    unique_digital_users = DigitalResourceUsage.objects.filter(
        school=request.school,
        created_at__date__range=[date_from, date_to]
    ).values('user').distinct().count()
    
    # Performance Metrics
    avg_borrowing_duration = BookBorrowing.objects.filter(
        school=request.school,
        status='returned',
        return_date__range=[date_from, date_to]
    ).aggregate(
        avg_duration=Avg(F('return_date') - F('borrow_date'))
    )['avg_duration']
    
    # Collection Utilization
    utilization_rate = 0
    if total_books > 0:
        borrowed_books = BookBorrowing.objects.filter(
            school=request.school,
            borrow_date__range=[date_from, date_to]
        ).values('book').distinct().count()
        utilization_rate = round((borrowed_books / total_books) * 100, 1)
    
    context = {
        'date_from': date_from,
        'date_to': date_to,
        
        # Collection stats
        'total_books': total_books,
        'total_digital': total_digital,
        'total_categories': total_categories,
        'total_authors': total_authors,
        
        # Activity stats
        'total_borrowings': total_borrowings,
        'active_borrowings': active_borrowings,
        'overdue_books': overdue_books,
        'digital_views': digital_views,
        'digital_downloads': digital_downloads,
        
        # Popular content
        'popular_books': popular_books,
        'popular_digital': popular_digital,
        
        # Analytics
        'category_stats': category_stats,
        'daily_borrowings': daily_borrowings,
        'daily_digital_usage': daily_digital_usage,
        
        # Engagement
        'unique_borrowers': unique_borrowers,
        'unique_digital_users': unique_digital_users,
        'avg_borrowing_duration': avg_borrowing_duration,
        'utilization_rate': utilization_rate,
    }
    
    return render(request, 'library/comprehensive_analytics.html', context)


@login_required
@school_required
def library_performance_metrics(request):
    """
    Library performance metrics and KPIs
    """
    from django.db.models import Count, Avg, Sum, Q, F
    from datetime import datetime, timedelta
    
    # Time periods for comparison
    today = timezone.now().date()
    this_month = today.replace(day=1)
    last_month = (this_month - timedelta(days=1)).replace(day=1)
    this_year = today.replace(month=1, day=1)
    
    # Collection Growth
    books_this_month = Book.objects.filter(
        school=request.school,
        acquisition_date__gte=this_month,
        is_active=True
    ).count()
    
    books_last_month = Book.objects.filter(
        school=request.school,
        acquisition_date__range=[last_month, this_month],
        is_active=True
    ).count()
    
    # Circulation Metrics
    circulation_this_month = BookBorrowing.objects.filter(
        school=request.school,
        borrow_date__gte=this_month
    ).count()
    
    circulation_last_month = BookBorrowing.objects.filter(
        school=request.school,
        borrow_date__range=[last_month, this_month]
    ).count()
    
    # Digital Engagement
    digital_usage_this_month = DigitalResourceUsage.objects.filter(
        school=request.school,
        created_at__date__gte=this_month
    ).count()
    
    # Calculate growth rates
    collection_growth = 0
    if books_last_month > 0:
        collection_growth = round(((books_this_month - books_last_month) / books_last_month) * 100, 1)
    
    circulation_growth = 0
    if circulation_last_month > 0:
        circulation_growth = round(((circulation_this_month - circulation_last_month) / circulation_last_month) * 100, 1)
    
    # Top Performers
    top_categories = Category.objects.filter(
        school=request.school,
        is_active=True
    ).annotate(
        monthly_borrows=Count('books__borrowings', filter=Q(
            books__borrowings__borrow_date__gte=this_month
        ))
    ).filter(monthly_borrows__gt=0).order_by('-monthly_borrows')[:5]
    
    # User Engagement Metrics
    active_users_this_month = BookBorrowing.objects.filter(
        school=request.school,
        borrow_date__gte=this_month
    ).values('borrower_id').distinct().count()
    
    digital_active_users = DigitalResourceUsage.objects.filter(
        school=request.school,
        created_at__date__gte=this_month
    ).values('user').distinct().count()
    
    context = {
        'books_this_month': books_this_month,
        'books_last_month': books_last_month,
        'collection_growth': collection_growth,
        
        'circulation_this_month': circulation_this_month,
        'circulation_last_month': circulation_last_month,
        'circulation_growth': circulation_growth,
        
        'digital_usage_this_month': digital_usage_this_month,
        'active_users_this_month': active_users_this_month,
        'digital_active_users': digital_active_users,
        
        'top_categories': top_categories,
        
        'this_month': this_month,
        'last_month': last_month,
    }
    
    return render(request, 'library/performance_metrics.html', context)


@login_required
@school_required
def comprehensive_analytics_view(request):
    """
    Comprehensive library analytics dashboard
    """
    from datetime import datetime, timedelta
    from django.db.models import Count, Q, Avg
    from django.utils import timezone
    
    # Get date range
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=30)
    
    if request.GET.get('date_from'):
        start_date = datetime.strptime(request.GET.get('date_from'), '%Y-%m-%d').date()
    if request.GET.get('date_to'):
        end_date = datetime.strptime(request.GET.get('date_to'), '%Y-%m-%d').date()
    
    report_type = request.GET.get('report_type', 'overview')
    
    # Basic metrics
    total_books = Book.objects.filter(school=request.school, is_active=True).count()
    total_digital = DigitalResource.objects.filter(school=request.school, is_active=True).count()
    total_resources = total_books + total_digital
    
    # Active borrowings
    active_borrowings = BookBorrowing.objects.filter(
        school=request.school,
        status__in=['active', 'overdue', 'renewed']
    ).count()
    
    # Overdue count
    overdue_count = BookBorrowing.objects.filter(
        school=request.school,
        status='overdue'
    ).count()
    
    # Active users (users who borrowed in last 30 days)
    active_users = BookBorrowing.objects.filter(
        school=request.school,
        borrow_date__gte=start_date
    ).values('borrower_id', 'borrower_type').distinct().count()
    
    # Collection health percentages
    available_books = Book.objects.filter(
        school=request.school,
        is_active=True,
        status='available'
    ).count()
    
    available_percentage = round((available_books / total_books * 100), 1) if total_books > 0 else 0
    borrowed_percentage = round((active_borrowings / total_books * 100), 1) if total_books > 0 else 0
    overdue_percentage = round((overdue_count / active_borrowings * 100), 1) if active_borrowings > 0 else 0
    
    # Damaged/lost books
    damaged_count = Book.objects.filter(
        school=request.school,
        is_active=True,
        status__in=['damaged', 'lost']
    ).count()
    
    # Daily activity data
    daily_activity = []
    current_date = start_date
    while current_date <= end_date:
        borrowings = BookBorrowing.objects.filter(
            school=request.school,
            borrow_date=current_date
        ).count()
        
        returns = BookBorrowing.objects.filter(
            school=request.school,
            return_date=current_date
        ).count()
        
        digital_usage = DigitalResourceUsage.objects.filter(
            school=request.school,
            created_at__date=current_date
        ).count()
        
        daily_activity.append({
            'date': current_date,
            'borrowings': borrowings,
            'returns': returns,
            'digital_usage': digital_usage
        })
        
        current_date += timedelta(days=1)
    
    # Top categories
    top_categories = Category.objects.filter(
        school=request.school,
        is_active=True
    ).annotate(
        book_count=Count('books', filter=Q(books__is_active=True)),
        usage_count=Count('books__borrowings', filter=Q(
            books__borrowings__borrow_date__gte=start_date,
            books__borrowings__borrow_date__lte=end_date
        ))
    ).order_by('-usage_count')[:10]
    
    # Most popular books
    popular_books = Book.objects.filter(
        school=request.school,
        is_active=True
    ).annotate(
        borrow_count=Count('borrowings', filter=Q(
            borrowings__borrow_date__gte=start_date,
            borrowings__borrow_date__lte=end_date
        ))
    ).order_by('-borrow_count')[:10]
    
    # Most active users
    active_users_list = BookBorrowing.objects.filter(
        school=request.school,
        borrow_date__gte=start_date,
        borrow_date__lte=end_date
    ).values(
        'borrower_id', 'borrower_type', 'borrower_name'
    ).annotate(
        borrow_count=Count('id'),
        last_activity=models.Max('borrow_date')
    ).order_by('-borrow_count')[:10]
    
    # Alerts
    alerts = []
    
    # Overdue books alert
    if overdue_count > 0:
        alerts.append({
            'icon': 'fa-clock',
            'message': _('Books are overdue'),
            'count': overdue_count
        })
    
    # Low stock alert
    low_stock = Book.objects.filter(
        school=request.school,
        is_active=True,
        available_copies__lte=1,
        total_copies__gt=0
    ).count()
    
    if low_stock > 0:
        alerts.append({
            'icon': 'fa-exclamation-triangle',
            'message': _('Books with low stock'),
            'count': low_stock
        })
    
    # Damaged books alert
    if damaged_count > 0:
        alerts.append({
            'icon': 'fa-tools',
            'message': _('Books need attention (damaged/lost)'),
            'count': damaged_count
        })
    
    context = {
        'date_from': start_date,
        'date_to': end_date,
        'report_type': report_type,
        'total_books': total_books,
        'total_digital': total_digital,
        'total_resources': total_resources,
        'active_borrowings': active_borrowings,
        'overdue_count': overdue_count,
        'active_users': active_users,
        'available_percentage': available_percentage,
        'borrowed_percentage': borrowed_percentage,
        'overdue_percentage': overdue_percentage,
        'damaged_count': damaged_count,
        'daily_activity': daily_activity,
        'top_categories': top_categories,
        'popular_books': popular_books,
        'active_users_list': active_users_list,
        'alerts': alerts,
    }
    
    # Handle export requests
    if request.GET.get('export'):
        export_format = request.GET.get('export')
        if export_format == 'pdf':
            return export_analytics_pdf(request, context)
        elif export_format == 'excel':
            return export_analytics_excel(request, context)
    
    return render(request, 'library/comprehensive_analytics.html', context)


def export_analytics_pdf(request, context):
    """
    Export analytics data as PDF
    """
    from django.http import HttpResponse
    from django.template.loader import render_to_string
    
    # For now, return a simple response
    # In a real implementation, you would use a PDF library like ReportLab or WeasyPrint
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = 'attachment; filename="library_analytics.pdf"'
    
    # Placeholder PDF content
    pdf_content = f"""
    Library Analytics Report
    Generated: {timezone.now().strftime('%Y-%m-%d %H:%M')}
    
    Summary:
    - Total Books: {context['total_books']}
    - Digital Resources: {context['total_digital']}
    - Active Borrowings: {context['active_borrowings']}
    - Active Users: {context['active_users']}
    
    This is a placeholder PDF export.
    In a real implementation, you would generate a proper PDF report.
    """
    
    response.write(pdf_content.encode('utf-8'))
    return response


def export_analytics_excel(request, context):
    """
    Export analytics data as Excel
    """
    from django.http import HttpResponse
    import csv
    
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="library_analytics.csv"'
    
    writer = csv.writer(response)
    writer.writerow(['Library Analytics Report'])
    writer.writerow(['Generated', timezone.now().strftime('%Y-%m-%d %H:%M')])
    writer.writerow([])
    
    writer.writerow(['Metric', 'Value'])
    writer.writerow(['Total Books', context['total_books']])
    writer.writerow(['Digital Resources', context['total_digital']])
    writer.writerow(['Active Borrowings', context['active_borrowings']])
    writer.writerow(['Active Users', context['active_users']])
    writer.writerow(['Overdue Count', context['overdue_count']])
    writer.writerow([])
    
    writer.writerow(['Popular Books'])
    writer.writerow(['Title', 'Author', 'Borrow Count'])
    for book in context['popular_books']:
        writer.writerow([book.title, book.get_authors_display(), book.borrow_count])
    
    return response