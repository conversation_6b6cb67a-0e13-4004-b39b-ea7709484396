#!/usr/bin/env python
"""
Comprehensive data population script for School ERP system
Creates realistic data for all modules and tests URL accessibility
"""
import os
import sys
import django
from datetime import date, datetime, timedelta
from decimal import Decimal
import random
from faker import Faker

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

# Import all models
from accounts.models import User, UserProfile
from core.models import School, AcademicYear, Semester
from students.models import Grade, Class, Parent, Student, StudentDocument
from academics.models import Subject, Teacher, ClassSubject, Schedule
from hr.models import Department, Position, Employee
from finance.models import (
    AccountType, Account, FeeType, GradeFee, StudentFee, 
    Payment, PaymentItem, PaymentGateway
)
from health.models import HealthProfile, Vaccination
from library.models import Category as BookCategory
from transportation.models import Vehicle
from inventory.models import AssetCategory

fake = Faker()

def create_comprehensive_data():
    print("🚀 Starting comprehensive data population for School ERP system...")
    
    # Create School
    school, created = School.objects.get_or_create(
        code='GIS001',
        defaults={
            'name': 'Greenwood International School',
            'name_ar': 'مدرسة جرينوود الدولية',
            'address': '123 Education Boulevard, Knowledge District, Riyadh 12345',
            'phone': '+966-11-555-0123',
            'email': '<EMAIL>',
            'website': 'https://greenwood.edu.sa',
            'principal_name': 'Dr. Sarah Al-Mahmoud',
            'established_date': date(2010, 9, 1),
            'license_number': 'EDU-RYD-2010-001',
            'tax_number': '300123456789003'
        }
    )
    print(f"✅ School: {school.name}")
    
    # Create Academic Years
    current_year = date.today().year
    academic_years = []
    for year_offset in range(-1, 2):  # Previous, current, next year
        year = current_year + year_offset
        is_current = year_offset == 0
        
        academic_year, created = AcademicYear.objects.get_or_create(
            name=f'{year}-{year + 1}',
            school=school,
            defaults={
                'start_date': date(year, 9, 1),
                'end_date': date(year + 1, 6, 30),
                'is_current': is_current
            }
        )
        academic_years.append(academic_year)
    
    current_academic_year = academic_years[1]  # Current year
    print(f"✅ Academic Years: {len(academic_years)} created")
    
    # Create Semesters
    semesters = []
    for academic_year in academic_years:
        year = int(academic_year.name.split('-')[0])
        
        semester1, created = Semester.objects.get_or_create(
            academic_year=academic_year,
            name='First Semester',
            school=school,
            defaults={
                'start_date': date(year, 9, 1),
                'end_date': date(year, 12, 20),
                'is_current': academic_year.is_current
            }
        )
        
        semester2, created = Semester.objects.get_or_create(
            academic_year=academic_year,
            name='Second Semester',
            school=school,
            defaults={
                'start_date': date(year + 1, 1, 10),
                'end_date': date(year + 1, 6, 30),
                'is_current': False
            }
        )
        
        semesters.extend([semester1, semester2])
    
    print(f"✅ Semesters: {len(semesters)} created")
    
    # Create Grades (K-12)
    grades_data = [
        (0, 'Kindergarten', 'روضة الأطفال'),
        (1, 'Grade 1', 'الصف الأول'),
        (2, 'Grade 2', 'الصف الثاني'),
        (3, 'Grade 3', 'الصف الثالث'),
        (4, 'Grade 4', 'الصف الرابع'),
        (5, 'Grade 5', 'الصف الخامس'),
        (6, 'Grade 6', 'الصف السادس'),
        (7, 'Grade 7', 'الصف السابع'),
        (8, 'Grade 8', 'الصف الثامن'),
        (9, 'Grade 9', 'الصف التاسع'),
        (10, 'Grade 10', 'الصف العاشر'),
        (11, 'Grade 11', 'الصف الحادي عشر'),
        (12, 'Grade 12', 'الصف الثاني عشر'),
    ]
    
    grades = []
    for level, name, name_ar in grades_data:
        grade, created = Grade.objects.get_or_create(
            level=level,
            school=school,
            defaults={
                'name': name, 
                'name_ar': name_ar,
                'max_capacity': 120,
                'min_age': 3 + level if level > 0 else 3,
                'max_age': 6 + level if level > 0 else 5
            }
        )
        grades.append(grade)
    print(f"✅ Grades: {len(grades)} created")
    
    # Create Subjects
    subjects_data = [
        ('MATH', 'Mathematics', 'الرياضيات', 'mathematics'),
        ('ENG', 'English Language', 'اللغة الإنجليزية', 'language'),
        ('ARA', 'Arabic Language', 'اللغة العربية', 'language'),
        ('SCI', 'Science', 'العلوم', 'science'),
        ('PHYS', 'Physics', 'الفيزياء', 'science'),
        ('CHEM', 'Chemistry', 'الكيمياء', 'science'),
        ('BIO', 'Biology', 'الأحياء', 'science'),
        ('HIST', 'History', 'التاريخ', 'core'),
        ('GEO', 'Geography', 'الجغرافيا', 'core'),
        ('ISLAM', 'Islamic Studies', 'التربية الإسلامية', 'religious'),
        ('ART', 'Art & Design', 'الفنون والتصميم', 'arts'),
        ('MUSIC', 'Music', 'الموسيقى', 'arts'),
        ('PE', 'Physical Education', 'التربية البدنية', 'physical_education'),
        ('CS', 'Computer Science', 'علوم الحاسوب', 'science'),
        ('ECON', 'Economics', 'الاقتصاد', 'elective'),
        ('PSYCH', 'Psychology', 'علم النفس', 'elective'),
    ]
    
    subjects = []
    for code, name, name_ar, subject_type in subjects_data:
        subject, created = Subject.objects.get_or_create(
            code=code,
            school=school,
            defaults={
                'name': name, 
                'name_ar': name_ar,
                'subject_type': subject_type,
                'credit_hours': random.randint(2, 4),
                'weekly_hours': random.randint(3, 6),
                'is_mandatory': subject_type in ['core', 'mathematics', 'language', 'religious'],
                'minimum_grade_required': Decimal('60.00'),
                'max_students_per_class': 30
            }
        )
        
        # Assign subjects to appropriate grades
        if code in ['MATH', 'ENG', 'ARA', 'SCI', 'HIST', 'GEO', 'ISLAM', 'ART', 'PE']:
            subject.grades.set(grades)  # All grades
        elif code in ['PHYS', 'CHEM', 'BIO', 'CS', 'ECON', 'PSYCH']:
            subject.grades.set(grades[9:])  # Grades 9-12 only
        else:
            subject.grades.set(grades[6:])  # Grades 6-12
            
        subjects.append(subject)
    print(f"✅ Subjects: {len(subjects)} created")
    
    # Create Users for different roles
    print("👥 Creating users...")
    
    # Create admin user
    admin_user, created = User.objects.get_or_create(
        username='admin',
        defaults={
            'user_type': 'admin',
            'first_name': 'System',
            'last_name': 'Administrator',
            'email': '<EMAIL>',
            'is_active': True,
            'is_staff': True,
            'is_superuser': True,
            'school': school
        }
    )
    if created:
        admin_user.set_password('admin123')
        admin_user.save()
    
    # Create teachers (20 teachers)
    teachers = []
    teacher_names = [
        ('Ahmed', 'Al-Rashid', '<EMAIL>'),
        ('Fatima', 'Al-Zahra', '<EMAIL>'),
        ('Mohammed', 'Al-Mansouri', '<EMAIL>'),
        ('Aisha', 'Al-Qasimi', '<EMAIL>'),
        ('Omar', 'Al-Farisi', '<EMAIL>'),
        ('Maryam', 'Al-Hashimi', '<EMAIL>'),
        ('Khalid', 'Al-Otaibi', '<EMAIL>'),
        ('Nora', 'Al-Saud', '<EMAIL>'),
        ('Hassan', 'Al-Maliki', '<EMAIL>'),
        ('Layla', 'Al-Dosari', '<EMAIL>'),
        ('John', 'Smith', '<EMAIL>'),
        ('Emily', 'Johnson', '<EMAIL>'),
        ('Michael', 'Brown', '<EMAIL>'),
        ('Sarah', 'Davis', '<EMAIL>'),
        ('David', 'Wilson', '<EMAIL>'),
        ('Lisa', 'Anderson', '<EMAIL>'),
        ('Robert', 'Taylor', '<EMAIL>'),
        ('Jennifer', 'Martinez', '<EMAIL>'),
        ('William', 'Garcia', '<EMAIL>'),
        ('Amanda', 'Rodriguez', '<EMAIL>'),
    ]
    
    for i, (first_name, last_name, email) in enumerate(teacher_names, 1):
        username = f'teacher{i:02d}'
        user, created = User.objects.get_or_create(
            username=username,
            defaults={
                'user_type': 'teacher',
                'first_name': first_name,
                'last_name': last_name,
                'email': email,
                'is_active': True
            }
        )
        if created:
            user.set_password('password123')
            user.save()
        
        teacher, created = Teacher.objects.get_or_create(
            user=user,
            defaults={
                'employee_id': f'T{i:04d}',
                'hire_date': fake.date_between(start_date='-5y', end_date='-1y'),
                'qualification': random.choice([
                    'Bachelor of Education', 'Master of Education', 'PhD in Education',
                    'Bachelor of Science', 'Master of Science', 'Bachelor of Arts'
                ]),
                'experience_years': random.randint(2, 15),
                'salary': Decimal(str(random.randint(8000, 15000))),
                'department': random.choice(['Mathematics', 'Science', 'Languages', 'Arts', 'Sports'])
            }
        )
        
        # Assign 2-4 subjects to each teacher
        teacher_subjects = random.sample(subjects, random.randint(2, 4))
        teacher.subjects.set(teacher_subjects)
        teachers.append(teacher)
    
    print(f"✅ Teachers: {len(teachers)} created")
    
    # Create Classes (2-3 sections per grade)
    classes = []
    for grade in grades:
        sections = ['A', 'B'] if grade.level <= 6 else ['A', 'B', 'C']
        for section in sections:
            class_teacher = random.choice(teachers)
            class_obj, created = Class.objects.get_or_create(
                name=section,
                grade=grade,
                academic_year=current_academic_year,
                defaults={
                    'class_teacher': class_teacher.user,
                    'max_students': random.randint(25, 30),
                    'room_number': f'{grade.level}{section}01'
                }
            )
            classes.append(class_obj)
    
    print(f"✅ Classes: {len(classes)} created")
    
    # Create Parents (100 parents)
    parents = []
    for i in range(1, 101):
        username = f'parent{i:03d}'
        first_name = fake.first_name()
        last_name = fake.last_name()
        
        user, created = User.objects.get_or_create(
            username=username,
            defaults={
                'user_type': 'parent',
                'first_name': first_name,
                'last_name': last_name,
                'email': f'{first_name.lower()}.{last_name.lower()}@email.com',
                'is_active': True
            }
        )
        if created:
            user.set_password('password123')
            user.save()
        
        parent, created = Parent.objects.get_or_create(
            user=user,
            defaults={
                'father_name': f'{first_name} {last_name}',
                'mother_name': f'{fake.first_name_female()} {last_name}',
                'father_phone': f'+966-5{random.randint(10000000, 99999999)}',
                'mother_phone': f'+966-5{random.randint(10000000, 99999999)}',
                'father_occupation': fake.job(),
                'mother_occupation': fake.job(),
                'father_workplace': fake.company(),
                'mother_workplace': fake.company(),
                'home_address': fake.address(),
                'emergency_contact': fake.name(),
                'emergency_phone': f'+966-5{random.randint(10000000, 99999999)}'
            }
        )
        parents.append(parent)
    
    print(f"✅ Parents: {len(parents)} created")
    
    # Create Students (300 students)
    students = []
    for i in range(1, 301):
        username = f'student{i:04d}'
        first_name = fake.first_name()
        last_name = fake.last_name()
        
        user, created = User.objects.get_or_create(
            username=username,
            defaults={
                'user_type': 'student',
                'first_name': first_name,
                'last_name': last_name,
                'email': f'{username}@student.greenwood.edu.sa',
                'is_active': True
            }
        )
        if created:
            user.set_password('password123')
            user.save()
        
        # Assign to random class
        current_class = random.choice(classes)
        parent = random.choice(parents)
        
        # Calculate age based on grade
        grade_level = current_class.grade.level
        base_age = 5 + grade_level if grade_level > 0 else 4
        age_variation = random.randint(-1, 1)
        student_age = max(3, base_age + age_variation)
        
        birth_year = date.today().year - student_age
        date_of_birth = fake.date_between(
            start_date=date(birth_year, 1, 1),
            end_date=date(birth_year, 12, 31)
        )
        
        student, created = Student.objects.get_or_create(
            user=user,
            defaults={
                'student_id': f'S{current_year}{i:04d}',
                'admission_number': f'ADM{current_year}{i:04d}',
                'first_name': first_name,
                'last_name': last_name,
                'first_name_ar': fake.first_name() if random.choice([True, False]) else None,
                'last_name_ar': fake.last_name() if random.choice([True, False]) else None,
                'date_of_birth': date_of_birth,
                'gender': random.choice(['M', 'F']),
                'nationality': random.choice(['Saudi', 'American', 'British', 'Canadian', 'Egyptian', 'Jordanian']),
                'national_id': f'{random.randint(**********, **********)}' if random.choice([True, False]) else None,
                'blood_type': random.choice(['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-']),
                'parent': parent,
                'current_class': current_class,
                'admission_date': fake.date_between(start_date='-3y', end_date='today'),
                'previous_school': fake.company() if random.choice([True, False]) else None,
                'medical_conditions': fake.text(max_nb_chars=100) if random.choice([True, False]) else None,
                'allergies': fake.text(max_nb_chars=50) if random.choice([True, False]) else None
            }
        )
        students.append(student)
    
    print(f"✅ Students: {len(students)} created")
    
    # Create Staff Users
    staff_roles = [
        ('accountant', 'Finance Manager', '<EMAIL>'),
        ('librarian', 'Head Librarian', '<EMAIL>'),
        ('nurse', 'School Nurse', '<EMAIL>'),
        ('counselor', 'School Counselor', '<EMAIL>'),
        ('secretary', 'School Secretary', '<EMAIL>'),
    ]
    
    staff_users = []
    for i, (role, title, email) in enumerate(staff_roles, 1):
        username = f'{role}01'
        user, created = User.objects.get_or_create(
            username=username,
            defaults={
                'user_type': role,
                'first_name': fake.first_name(),
                'last_name': fake.last_name(),
                'email': email,
                'is_active': True,
                'is_staff': True
            }
        )
        if created:
            user.set_password('password123')
            user.save()
        staff_users.append(user)
    
    print(f"✅ Staff Users: {len(staff_users)} created")
    
    print("\n💰 Creating Financial Data...")
    
    # Create Account Types
    account_types_data = [
        ('asset', 'Assets', 'الأصول'),
        ('liability', 'Liabilities', 'الخصوم'),
        ('equity', 'Equity', 'حقوق الملكية'),
        ('revenue', 'Revenue', 'الإيرادات'),
        ('expense', 'Expenses', 'المصروفات'),
    ]
    
    account_types = {}
    for type_code, name, name_ar in account_types_data:
        account_type, created = AccountType.objects.get_or_create(
            type=type_code,
            defaults={'name': name, 'name_ar': name_ar}
        )
        account_types[type_code] = account_type
    
    # Create Chart of Accounts
    accounts_data = [
        ('1000', 'Cash in Hand', 'النقد في الصندوق', 'asset', Decimal('50000')),
        ('1100', 'Bank Account - Main', 'الحساب البنكي الرئيسي', 'asset', Decimal('500000')),
        ('1200', 'Accounts Receivable', 'الذمم المدينة', 'asset', Decimal('0')),
        ('1300', 'Inventory', 'المخزون', 'asset', Decimal('25000')),
        ('1400', 'Equipment', 'المعدات', 'asset', Decimal('200000')),
        ('2000', 'Accounts Payable', 'الذمم الدائنة', 'liability', Decimal('0')),
        ('2100', 'Salaries Payable', 'الرواتب المستحقة', 'liability', Decimal('0')),
        ('3000', 'Capital', 'رأس المال', 'equity', Decimal('500000')),
        ('4000', 'Tuition Revenue', 'إيرادات الرسوم الدراسية', 'revenue', Decimal('0')),
        ('4100', 'Transportation Revenue', 'إيرادات النقل', 'revenue', Decimal('0')),
        ('4200', 'Other Revenue', 'إيرادات أخرى', 'revenue', Decimal('0')),
        ('5000', 'Salary Expenses', 'مصروفات الرواتب', 'expense', Decimal('0')),
        ('5100', 'Utilities Expenses', 'مصروفات المرافق', 'expense', Decimal('0')),
        ('5200', 'Maintenance Expenses', 'مصروفات الصيانة', 'expense', Decimal('0')),
    ]
    
    accounts = {}
    for code, name, name_ar, type_code, opening_balance in accounts_data:
        account, created = Account.objects.get_or_create(
            code=code,
            defaults={
                'name': name,
                'name_ar': name_ar,
                'account_type': account_types[type_code],
                'opening_balance': opening_balance,
                'current_balance': opening_balance
            }
        )
        accounts[code] = account
    
    print(f"✅ Accounts: {len(accounts)} created")
    
    # Create Fee Types
    fee_types_data = [
        ('Tuition Fee', 'الرسوم الدراسية', '4000'),
        ('Transportation Fee', 'رسوم النقل', '4100'),
        ('Books & Materials', 'رسوم الكتب والمواد', '4200'),
        ('Activity Fee', 'رسوم الأنشطة', '4200'),
        ('Lab Fee', 'رسوم المختبر', '4200'),
        ('Registration Fee', 'رسوم التسجيل', '4200'),
    ]
    
    fee_types = []
    for name, name_ar, account_code in fee_types_data:
        fee_type, created = FeeType.objects.get_or_create(
            name=name,
            defaults={
                'name_ar': name_ar,
                'account': accounts[account_code],
                'is_mandatory': name in ['Tuition Fee', 'Registration Fee']
            }
        )
        fee_types.append(fee_type)
    
    print(f"✅ Fee Types: {len(fee_types)} created")
    
    # Create Grade Fees
    grade_fees = []
    for grade in grades:
        for fee_type in fee_types:
            # Calculate fee based on grade level
            base_amount = 2000 + (grade.level * 200)  # Higher grades cost more
            
            if fee_type.name == 'Transportation Fee':
                amount = 800
            elif fee_type.name == 'Books & Materials':
                amount = 500 + (grade.level * 50)
            elif fee_type.name == 'Activity Fee':
                amount = 300
            elif fee_type.name == 'Lab Fee':
                amount = 400 if grade.level >= 6 else 0
            elif fee_type.name == 'Registration Fee':
                amount = 500
            else:  # Tuition Fee
                amount = base_amount
            
            if amount > 0:
                grade_fee, created = GradeFee.objects.get_or_create(
                    grade=grade,
                    fee_type=fee_type,
                    academic_year=current_academic_year,
                    defaults={
                        'amount': Decimal(str(amount)),
                        'due_date': date(current_year, 10, 1)
                    }
                )
                grade_fees.append(grade_fee)
    
    print(f"✅ Grade Fees: {len(grade_fees)} created")
    
    # Create Student Fees and Payments
    payments_created = 0
    for student in students[:100]:  # Create fees for first 100 students
        student_grade_fees = GradeFee.objects.filter(
            grade=student.current_class.grade,
            academic_year=current_academic_year
        )
        
        for grade_fee in student_grade_fees:
            # Apply random discount (0-20%)
            discount_percentage = random.randint(0, 20)
            discount_amount = (grade_fee.amount * discount_percentage) / 100
            
            student_fee, created = StudentFee.objects.get_or_create(
                student=student,
                grade_fee=grade_fee,
                defaults={
                    'amount': grade_fee.amount,
                    'discount_amount': discount_amount,
                    'due_date': grade_fee.due_date,
                    'is_paid': random.choice([True, False, False])  # 33% paid
                }
            )
            
            # Create payment if fee is marked as paid
            if student_fee.is_paid:
                payment, created = Payment.objects.get_or_create(
                    student=student,
                    receipt_number=f'RCP{current_year}{payments_created + 1:06d}',
                    defaults={
                        'amount': student_fee.net_amount,
                        'payment_date': fake.date_between(
                            start_date=current_academic_year.start_date,
                            end_date='today'
                        ),
                        'payment_method': random.choice(['cash', 'bank_transfer', 'credit_card']),
                        'reference_number': f'REF{random.randint(100000, 999999)}',
                        'received_by': random.choice(staff_users),
                        'notes': 'Payment received in full'
                    }
                )
                
                if created:
                    # Create payment item
                    PaymentItem.objects.create(
                        payment=payment,
                        student_fee=student_fee,
                        amount=student_fee.net_amount
                    )
                    payments_created += 1
    
    print(f"✅ Student Fees and Payments: {payments_created} payments created")
    
    print("\n📚 Creating Academic Data...")
    
    # Create Class Subjects
    class_subjects = []
    for class_obj in classes:
        # Get subjects appropriate for this grade
        grade_subjects = Subject.objects.filter(grades=class_obj.grade)
        
        for subject in grade_subjects:
            # Find a qualified teacher
            qualified_teachers = Teacher.objects.filter(subjects=subject)
            if qualified_teachers.exists():
                teacher = random.choice(qualified_teachers)
                
                class_subject, created = ClassSubject.objects.get_or_create(
                    class_obj=class_obj,
                    subject=subject,
                    teacher=teacher,
                    academic_year=current_academic_year,
                    semester=semesters[1],  # Current semester
                    defaults={
                        'weekly_hours': subject.weekly_hours,
                        'max_students': min(class_obj.max_students, subject.max_students_per_class),
                        'is_active': True
                    }
                )
                class_subjects.append(class_subject)
    
    print(f"✅ Class Subjects: {len(class_subjects)} created")
    
    print("\n🏥 Creating Health Data...")
    
    # Create Health Profiles for students
    health_profiles = []
    for student in students[:50]:  # Create health profiles for first 50 students
        health_profile, created = HealthProfile.objects.get_or_create(
            student=student,
            defaults={
                'blood_type': random.choice(['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-']),
                'height': Decimal(str(random.randint(100, 180))),
                'weight': Decimal(str(random.randint(20, 80))),
                'chronic_conditions': student.medical_conditions or '',
                'special_needs': student.special_needs or '',
                'emergency_contact_name': student.parent.emergency_contact or student.parent.father_name,
                'emergency_contact_phone': student.parent.emergency_phone or student.parent.father_phone,
                'emergency_contact_relationship': 'Parent',
                'medical_notes': fake.text(max_nb_chars=100) if random.choice([True, False]) else '',
                'last_physical_exam': fake.date_between(start_date='-1y', end_date='today')
            }
        )
        health_profiles.append(health_profile)
    
    print(f"✅ Health Profiles: {len(health_profiles)} created")
    
    print("\n📖 Creating Library Data...")
    
    # Create Book Categories
    categories_data = [
        ('Fiction', 'الخيال'),
        ('Science', 'العلوم'),
        ('Mathematics', 'الرياضيات'),
        ('History', 'التاريخ'),
        ('Geography', 'الجغرافيا'),
        ('Literature', 'الأدب'),
        ('Reference', 'المراجع'),
        ('Children', 'الأطفال'),
    ]
    
    book_categories = []
    for name, name_ar in categories_data:
        category, created = BookCategory.objects.get_or_create(
            name=name,
            defaults={'name_ar': name_ar}
        )
        book_categories.append(category)
    
    print(f"✅ Book Categories: {len(book_categories)} created")
    
    print("\n🚌 Creating Transportation Data...")
    
    # Create Vehicles
    vehicles = []
    for i in range(1, 11):  # 10 vehicles
        vehicle, created = Vehicle.objects.get_or_create(
            vehicle_number=f'BUS{i:03d}',
            defaults={
                'license_plate': f'ABC-{random.randint(1000, 9999)}',
                'vehicle_type': 'bus',
                'capacity': random.choice([30, 35, 40, 45]),
                'model': random.choice(['Mercedes', 'Volvo', 'Scania', 'MAN']),
                'year': random.randint(2015, 2023),
                'fuel_type': 'diesel',
                'status': 'active'
            }
        )
        vehicles.append(vehicle)
    
    print(f"✅ Transportation: {len(vehicles)} vehicles created")
    
    print("\n📦 Creating Inventory Data...")
    
    # Create Asset Categories
    inventory_categories_data = [
        ('OFFICE', 'Office Supplies', 'اللوازم المكتبية'),
        ('CLEAN', 'Cleaning Supplies', 'مواد التنظيف'),
        ('EDU', 'Educational Materials', 'المواد التعليمية'),
        ('SPORT', 'Sports Equipment', 'المعدات الرياضية'),
        ('TECH', 'Technology', 'التكنولوجيا'),
        ('FURN', 'Furniture', 'الأثاث'),
    ]
    
    inventory_categories = []
    for code, name, name_ar in inventory_categories_data:
        category, created = AssetCategory.objects.get_or_create(
            code=code,
            defaults={
                'name': name,
                'arabic_name': name_ar,
                'description': f'Category for {name.lower()}',
                'depreciation_rate': Decimal(str(random.randint(5, 20))),
                'useful_life_years': random.randint(3, 10)
            }
        )
        inventory_categories.append(category)
    
    print(f"✅ Inventory: {len(inventory_categories)} categories created")
    
    print("\n🎯 Creating Sample Schedules...")
    
    # Create sample schedules for some class subjects
    time_slots = [
        ('08:00', '08:45'),
        ('08:45', '09:30'),
        ('09:45', '10:30'),
        ('10:30', '11:15'),
        ('11:30', '12:15'),
        ('12:15', '13:00'),
    ]
    
    days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
    
    schedules_created = 0
    for class_subject in class_subjects[:50]:  # Create schedules for first 50 class subjects
        # Create 2-3 schedule entries per subject
        num_sessions = min(class_subject.weekly_hours, 3)
        selected_days = random.sample(days, num_sessions)
        
        for day in selected_days:
            start_time, end_time = random.choice(time_slots)
            
            schedule, created = Schedule.objects.get_or_create(
                class_subject=class_subject,
                day_of_week=day,
                start_time=datetime.strptime(start_time, '%H:%M').time(),
                end_time=datetime.strptime(end_time, '%H:%M').time(),
                defaults={
                    'room_number': f'Room {random.randint(101, 350)}',
                    'is_active': True
                }
            )
            if created:
                schedules_created += 1
    
    print(f"✅ Schedules: {schedules_created} created")
    
    print("\n✅ Comprehensive data population completed successfully!")
    print("\n" + "="*60)
    print("📊 DATA SUMMARY")
    print("="*60)
    print(f"🏫 Schools: 1")
    print(f"📅 Academic Years: {len(academic_years)}")
    print(f"📚 Grades: {len(grades)}")
    print(f"🏛️ Classes: {len(classes)}")
    print(f"📖 Subjects: {len(subjects)}")
    print(f"👨‍🏫 Teachers: {len(teachers)}")
    print(f"👨‍👩‍👧‍👦 Parents: {len(parents)}")
    print(f"🎓 Students: {len(students)}")
    print(f"👥 Staff Users: {len(staff_users)}")
    print(f"💰 Accounts: {len(accounts)}")
    print(f"💳 Fee Types: {len(fee_types)}")
    print(f"💵 Payments: {payments_created}")
    print(f"📚 Book Categories: {len(book_categories)}")
    print(f"🚌 Vehicles: {len(vehicles)}")
    print(f"📦 Inventory Categories: {len(inventory_categories)}")
    print(f"🏥 Health Profiles: {len(health_profiles)}")
    print(f"⏰ Schedules: {schedules_created}")
    print("="*60)
    
    print("\n🔐 TEST ACCOUNTS:")
    print("="*30)
    print("👑 Admin: admin / admin123")
    print("👨‍🏫 Teacher: teacher01 / password123")
    print("👨‍👩‍👧‍👦 Parent: parent001 / password123")
    print("🎓 Student: student0001 / password123")
    print("💰 Accountant: accountant01 / password123")
    print("📚 Librarian: librarian01 / password123")
    print("🏥 Nurse: nurse01 / password123")
    print("="*30)
    
    print(f"\n🌐 Access the system at: http://127.0.0.1:8000")
    print(f"📋 Admin panel at: http://127.0.0.1:8000/admin")


if __name__ == '__main__':
    create_comprehensive_data()