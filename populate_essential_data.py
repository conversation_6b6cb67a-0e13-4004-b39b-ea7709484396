#!/usr/bin/env python
"""
Essential data population script for School ERP system
Creates basic realistic data and tests core functionality
"""
import os
import sys
import django
from datetime import date, datetime, timedelta
from decimal import Decimal
import random

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

# Import all models
from accounts.models import User, UserProfile
from core.models import School, AcademicYear, Semester
from students.models import Grade, Class, Parent, Student
from academics.models import Subject, Teacher
from finance.models import AccountType, Account, FeeType, GradeFee
from hr.models import Department, Position

def create_essential_data():
    print("🚀 Creating essential data for School ERP system...")
    
    # Create School
    school, created = School.objects.get_or_create(
        code='GIS001',
        defaults={
            'name': 'Greenwood International School',
            'name_ar': 'مدرسة جرينوود الدولية',
            'address': '123 Education Boulevard, Knowledge District, Riyadh 12345',
            'phone': '+966-11-555-0123',
            'email': '<EMAIL>',
            'website': 'https://greenwood.edu.sa',
            'principal_name': 'Dr. <PERSON> Al-Mahmoud',
            'established_date': date(2010, 9, 1),
            'license_number': 'EDU-RYD-2010-001',
            'tax_number': '***************'
        }
    )
    print(f"✅ School: {school.name}")
    
    # Create Academic Year
    current_year = date.today().year
    academic_year, created = AcademicYear.objects.get_or_create(
        name=f'{current_year}-{current_year + 1}',
        school=school,
        defaults={
            'start_date': date(current_year, 9, 1),
            'end_date': date(current_year + 1, 6, 30),
            'is_current': True
        }
    )
    print(f"✅ Academic Year: {academic_year.name}")
    
    # Create Semester
    semester, created = Semester.objects.get_or_create(
        academic_year=academic_year,
        name='First Semester',
        school=school,
        defaults={
            'start_date': date(current_year, 9, 1),
            'end_date': date(current_year, 12, 20),
            'is_current': True
        }
    )
    print(f"✅ Semester: {semester.name}")
    
    # Create Grades
    grades_data = [
        (1, 'Grade 1', 'الصف الأول'),
        (2, 'Grade 2', 'الصف الثاني'),
        (3, 'Grade 3', 'الصف الثالث'),
        (4, 'Grade 4', 'الصف الرابع'),
        (5, 'Grade 5', 'الصف الخامس'),
        (6, 'Grade 6', 'الصف السادس'),
    ]
    
    grades = []
    for level, name, name_ar in grades_data:
        grade, created = Grade.objects.get_or_create(
            level=level,
            school=school,
            defaults={
                'name': name, 
                'name_ar': name_ar,
                'max_capacity': 120,
                'min_age': 5 + level,
                'max_age': 7 + level
            }
        )
        grades.append(grade)
    print(f"✅ Grades: {len(grades)} created")
    
    # Create Subjects
    subjects_data = [
        ('MATH', 'Mathematics', 'الرياضيات', 'mathematics'),
        ('ENG', 'English Language', 'اللغة الإنجليزية', 'language'),
        ('ARA', 'Arabic Language', 'اللغة العربية', 'language'),
        ('SCI', 'Science', 'العلوم', 'science'),
        ('HIST', 'History', 'التاريخ', 'core'),
        ('ART', 'Art & Design', 'الفنون والتصميم', 'arts'),
        ('PE', 'Physical Education', 'التربية البدنية', 'physical_education'),
    ]
    
    subjects = []
    for code, name, name_ar, subject_type in subjects_data:
        subject, created = Subject.objects.get_or_create(
            code=code,
            school=school,
            defaults={
                'name': name, 
                'name_ar': name_ar,
                'subject_type': subject_type,
                'credit_hours': random.randint(2, 4),
                'weekly_hours': random.randint(3, 6),
                'is_mandatory': subject_type in ['core', 'mathematics', 'language'],
                'minimum_grade_required': Decimal('60.00'),
                'max_students_per_class': 30
            }
        )
        subject.grades.set(grades)
        subjects.append(subject)
    print(f"✅ Subjects: {len(subjects)} created")
    
    # Create Admin User
    admin_user, created = User.objects.get_or_create(
        username='admin',
        defaults={
            'user_type': 'admin',
            'first_name': 'System',
            'last_name': 'Administrator',
            'email': '<EMAIL>',
            'is_active': True,
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        admin_user.set_password('admin123')
        admin_user.save()
    print(f"✅ Admin user created: {admin_user.username}")
    
    # Create Teachers
    teacher_names = [
        ('Ahmed', 'Al-Rashid', '<EMAIL>'),
        ('Fatima', 'Al-Zahra', '<EMAIL>'),
        ('Mohammed', 'Al-Mansouri', '<EMAIL>'),
        ('Aisha', 'Al-Qasimi', '<EMAIL>'),
        ('John', 'Smith', '<EMAIL>'),
    ]
    
    teachers = []
    for i, (first_name, last_name, email) in enumerate(teacher_names, 1):
        username = f'teacher{i:02d}'
        user, created = User.objects.get_or_create(
            username=username,
            defaults={
                'user_type': 'teacher',
                'first_name': first_name,
                'last_name': last_name,
                'email': email,
                'is_active': True
            }
        )
        if created:
            user.set_password('password123')
            user.save()
        
        teacher, created = Teacher.objects.get_or_create(
            user=user,
            defaults={
                'employee_id': f'T{i:04d}',
                'hire_date': date(2020, 9, 1),
                'qualification': 'Bachelor of Education',
                'experience_years': random.randint(2, 10),
                'salary': Decimal(str(random.randint(8000, 12000))),
                'department': 'Academic',
                'school': school
            }
        )
        teacher.subjects.set(random.sample(subjects, 2))
        teachers.append(teacher)
    
    print(f"✅ Teachers: {len(teachers)} created")
    
    # Create Classes
    classes = []
    for grade in grades:
        for section in ['A', 'B']:
            class_teacher = random.choice(teachers)
            class_obj, created = Class.objects.get_or_create(
                name=section,
                grade=grade,
                academic_year=academic_year,
                defaults={
                    'class_teacher': class_teacher.user,
                    'max_students': 25,
                    'room_number': f'{grade.level}{section}01',
                    'school': school
                }
            )
            classes.append(class_obj)
    
    print(f"✅ Classes: {len(classes)} created")
    
    # Create Parents
    parents = []
    for i in range(1, 21):  # 20 parents
        username = f'parent{i:03d}'
        user, created = User.objects.get_or_create(
            username=username,
            defaults={
                'user_type': 'parent',
                'first_name': f'Parent{i}',
                'last_name': 'Family',
                'email': f'parent{i}@email.com',
                'is_active': True
            }
        )
        if created:
            user.set_password('password123')
            user.save()
        
        parent, created = Parent.objects.get_or_create(
            user=user,
            defaults={
                'father_name': f'Father {i}',
                'mother_name': f'Mother {i}',
                'father_phone': f'+966-5{random.randint(10000000, 99999999)}',
                'mother_phone': f'+966-5{random.randint(10000000, 99999999)}',
                'father_occupation': 'Engineer',
                'mother_occupation': 'Teacher',
                'home_address': f'Address {i}, Riyadh',
                'emergency_contact': f'Emergency {i}',
                'emergency_phone': f'+966-5{random.randint(10000000, 99999999)}',
                'school': school
            }
        )
        parents.append(parent)
    
    print(f"✅ Parents: {len(parents)} created")
    
    # Create Students
    students = []
    for i in range(1, 51):  # 50 students
        username = f'student{i:04d}'
        user, created = User.objects.get_or_create(
            username=username,
            defaults={
                'user_type': 'student',
                'first_name': f'Student{i}',
                'last_name': 'Name',
                'email': f'student{i}@student.greenwood.edu.sa',
                'is_active': True
            }
        )
        if created:
            user.set_password('password123')
            user.save()
        
        current_class = random.choice(classes)
        parent = random.choice(parents)
        
        # Calculate age based on grade
        grade_level = current_class.grade.level
        student_age = 5 + grade_level + random.randint(-1, 1)
        birth_year = date.today().year - student_age
        date_of_birth = date(birth_year, random.randint(1, 12), random.randint(1, 28))
        
        student, created = Student.objects.get_or_create(
            user=user,
            defaults={
                'student_id': f'S{current_year}{i:04d}',
                'admission_number': f'ADM{current_year}{i:04d}',
                'first_name': f'Student{i}',
                'last_name': 'Name',
                'date_of_birth': date_of_birth,
                'gender': random.choice(['M', 'F']),
                'nationality': random.choice(['Saudi', 'American', 'British']),
                'blood_type': random.choice(['A+', 'B+', 'O+', 'AB+']),
                'parent': parent,
                'current_class': current_class,
                'admission_date': date(current_year, 9, 1),
                'school': school
            }
        )
        students.append(student)
    
    print(f"✅ Students: {len(students)} created")
    
    # Create Account Types
    account_types_data = [
        ('asset', 'Assets', 'الأصول'),
        ('liability', 'Liabilities', 'الخصوم'),
        ('equity', 'Equity', 'حقوق الملكية'),
        ('revenue', 'Revenue', 'الإيرادات'),
        ('expense', 'Expenses', 'المصروفات'),
    ]
    
    account_types = {}
    for type_code, name, name_ar in account_types_data:
        account_type, created = AccountType.objects.get_or_create(
            type=type_code,
            school=school,
            defaults={'name': name, 'name_ar': name_ar}
        )
        account_types[type_code] = account_type
    
    # Create Basic Accounts
    accounts_data = [
        ('1000', 'Cash in Hand', 'النقد في الصندوق', 'asset', Decimal('50000')),
        ('4000', 'Tuition Revenue', 'إيرادات الرسوم الدراسية', 'revenue', Decimal('0')),
        ('5000', 'Salary Expenses', 'مصروفات الرواتب', 'expense', Decimal('0')),
    ]
    
    accounts = {}
    for code, name, name_ar, type_code, opening_balance in accounts_data:
        account, created = Account.objects.get_or_create(
            code=code,
            school=school,
            defaults={
                'name': name,
                'name_ar': name_ar,
                'account_type': account_types[type_code],
                'opening_balance': opening_balance,
                'current_balance': opening_balance
            }
        )
        accounts[code] = account
    
    print(f"✅ Accounts: {len(accounts)} created")
    
    # Create Fee Types
    fee_type, created = FeeType.objects.get_or_create(
        name='Tuition Fee',
        school=school,
        defaults={
            'name_ar': 'الرسوم الدراسية',
            'account': accounts['4000'],
            'is_mandatory': True
        }
    )
    
    # Create Grade Fees
    for grade in grades:
        amount = 2000 + (grade.level * 200)  # Higher grades cost more
        grade_fee, created = GradeFee.objects.get_or_create(
            grade=grade,
            fee_type=fee_type,
            academic_year=academic_year,
            defaults={
                'amount': Decimal(str(amount)),
                'due_date': date(current_year, 10, 1),
                'school': school
            }
        )
    
    print(f"✅ Fee structure created")
    
    # Create HR Departments
    departments_data = [
        ('Academic', 'الأكاديمي', 'ACAD'),
        ('Administration', 'الإدارة', 'ADMIN'),
        ('Finance', 'المالية', 'FIN'),
        ('IT', 'تقنية المعلومات', 'IT'),
    ]
    
    for name, name_ar, code in departments_data:
        department, created = Department.objects.get_or_create(
            code=code,
            school=school,
            defaults={
                'name': name,
                'name_ar': name_ar,
                'description': f'{name} Department',
                'head': None
            }
        )
    
    print(f"✅ HR Departments created")
    
    print("\n🎉 Essential data creation completed successfully!")
    print(f"📊 Summary:")
    print(f"   • 1 School")
    print(f"   • 1 Academic Year")
    print(f"   • 1 Semester")
    print(f"   • {len(grades)} Grades")
    print(f"   • {len(subjects)} Subjects")
    print(f"   • {len(teachers)} Teachers")
    print(f"   • {len(classes)} Classes")
    print(f"   • {len(parents)} Parents")
    print(f"   • {len(students)} Students")
    print(f"   • Basic financial structure")
    print(f"   • HR departments")
    
    return {
        'school': school,
        'academic_year': academic_year,
        'grades': grades,
        'subjects': subjects,
        'teachers': teachers,
        'classes': classes,
        'parents': parents,
        'students': students
    }

if __name__ == '__main__':
    try:
        data = create_essential_data()
        print("\n✅ Data population completed successfully!")
        print("\n💡 You can now:")
        print("1. Start the development server: python manage.py runserver")
        print("2. Login with admin credentials: admin / admin123")
        print("3. Test the system with different user roles")
        
    except Exception as e:
        print(f"\n❌ Error during data population: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)