#!/usr/bin/env python
"""
Quality Assurance Automation Script for School ERP System
"""
import os
import sys
import subprocess
import json
import time
from datetime import datetime
from pathlib import Path


class QAAutomation:
    """Quality Assurance Automation System"""
    
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'unit_tests': {},
            'integration_tests': {},
            'e2e_tests': {},
            'quality_checks': {},
            'security_scan': {},
            'performance_tests': {},
            'coverage_report': {}
        }
        
        self.config = {
            'coverage_threshold': 80,
            'performance_threshold': 2.0,  # seconds
            'max_complexity': 10,
            'max_line_length': 120
        }
    
    def run_full_qa_suite(self):
        """Run complete QA suite"""
        print("🚀 Starting Full QA Suite...")
        print("=" * 60)
        
        start_time = time.time()
        
        # 1. Unit Tests
        self.run_unit_tests()
        
        # 2. Integration Tests
        self.run_integration_tests()
        
        # 3. End-to-End Tests
        self.run_e2e_tests()
        
        # 4. Quality Checks
        self.run_quality_checks()
        
        # 5. Security Scan
        self.run_security_scan()
        
        # 6. Performance Tests
        self.run_performance_tests()
        
        # 7. Coverage Report
        self.generate_coverage_report()
        
        # 8. Generate Final Report
        total_time = time.time() - start_time
        self.generate_final_report(total_time)
        
        return self.results
    
    def run_unit_tests(self):
        """Run unit tests with coverage"""
        print("\n🧪 Running Unit Tests...")
        print("-" * 40)
        
        try:
            cmd = [
                'python', '-m', 'pytest',
                'tests/',
                '-m', 'unit',
                '--tb=short',
                '--cov=.',
                '--cov-report=json:coverage.json',
                '--cov-report=html:htmlcov',
                '--json-report',
                '--json-report-file=unit_test_report.json',
                '-v'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            self.results['unit_tests'] = {
                'status': 'passed' if result.returncode == 0 else 'failed',
                'return_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'duration': self.extract_duration(result.stdout)
            }
            
            # Parse JSON report if available
            if Path('unit_test_report.json').exists():
                with open('unit_test_report.json', 'r') as f:
                    test_data = json.load(f)
                    self.results['unit_tests']['summary'] = test_data.get('summary', {})
            
            status_icon = "✅" if result.returncode == 0 else "❌"
            print(f"{status_icon} Unit Tests: {'PASSED' if result.returncode == 0 else 'FAILED'}")
            
        except subprocess.TimeoutExpired:
            self.results['unit_tests'] = {
                'status': 'timeout',
                'error': 'Unit tests timed out after 5 minutes'
            }
            print("⏰ Unit Tests: TIMEOUT")
        except Exception as e:
            self.results['unit_tests'] = {
                'status': 'error',
                'error': str(e)
            }
            print(f"❌ Unit Tests: ERROR - {e}")
    
    def run_integration_tests(self):
        """Run integration tests"""
        print("\n🔗 Running Integration Tests...")
        print("-" * 40)
        
        try:
            cmd = [
                'python', '-m', 'pytest',
                'tests/',
                '-m', 'integration',
                '--tb=short',
                '--json-report',
                '--json-report-file=integration_test_report.json',
                '-v'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            
            self.results['integration_tests'] = {
                'status': 'passed' if result.returncode == 0 else 'failed',
                'return_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'duration': self.extract_duration(result.stdout)
            }
            
            status_icon = "✅" if result.returncode == 0 else "❌"
            print(f"{status_icon} Integration Tests: {'PASSED' if result.returncode == 0 else 'FAILED'}")
            
        except subprocess.TimeoutExpired:
            self.results['integration_tests'] = {
                'status': 'timeout',
                'error': 'Integration tests timed out after 10 minutes'
            }
            print("⏰ Integration Tests: TIMEOUT")
        except Exception as e:
            self.results['integration_tests'] = {
                'status': 'error',
                'error': str(e)
            }
            print(f"❌ Integration Tests: ERROR - {e}")
    
    def run_e2e_tests(self):
        """Run end-to-end tests"""
        print("\n🎯 Running End-to-End Tests...")
        print("-" * 40)
        
        try:
            cmd = [
                'python', '-m', 'pytest',
                'tests/test_e2e_workflows.py',
                '--tb=short',
                '--json-report',
                '--json-report-file=e2e_test_report.json',
                '-v'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=900)
            
            self.results['e2e_tests'] = {
                'status': 'passed' if result.returncode == 0 else 'failed',
                'return_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'duration': self.extract_duration(result.stdout)
            }
            
            status_icon = "✅" if result.returncode == 0 else "❌"
            print(f"{status_icon} E2E Tests: {'PASSED' if result.returncode == 0 else 'FAILED'}")
            
        except subprocess.TimeoutExpired:
            self.results['e2e_tests'] = {
                'status': 'timeout',
                'error': 'E2E tests timed out after 15 minutes'
            }
            print("⏰ E2E Tests: TIMEOUT")
        except Exception as e:
            self.results['e2e_tests'] = {
                'status': 'error',
                'error': str(e)
            }
            print(f"❌ E2E Tests: ERROR - {e}")
    
    def run_quality_checks(self):
        """Run code quality checks"""
        print("\n📝 Running Quality Checks...")
        print("-" * 40)
        
        try:
            cmd = [
                'python', '-m', 'pytest',
                'tests/test_quality_assurance.py',
                '--tb=short',
                '-v'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            self.results['quality_checks'] = {
                'status': 'passed' if result.returncode == 0 else 'failed',
                'return_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
            status_icon = "✅" if result.returncode == 0 else "❌"
            print(f"{status_icon} Quality Checks: {'PASSED' if result.returncode == 0 else 'FAILED'}")
            
        except Exception as e:
            self.results['quality_checks'] = {
                'status': 'error',
                'error': str(e)
            }
            print(f"❌ Quality Checks: ERROR - {e}")
    
    def run_security_scan(self):
        """Run security scan"""
        print("\n🔒 Running Security Scan...")
        print("-" * 40)
        
        try:
            # Django security check
            cmd = ['python', 'manage.py', 'check', '--deploy']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            self.results['security_scan'] = {
                'django_check': {
                    'status': 'passed' if result.returncode == 0 else 'failed',
                    'return_code': result.returncode,
                    'stdout': result.stdout,
                    'stderr': result.stderr
                }
            }
            
            # Additional security checks could be added here
            # e.g., bandit, safety, etc.
            
            status_icon = "✅" if result.returncode == 0 else "❌"
            print(f"{status_icon} Security Scan: {'PASSED' if result.returncode == 0 else 'FAILED'}")
            
        except Exception as e:
            self.results['security_scan'] = {
                'status': 'error',
                'error': str(e)
            }
            print(f"❌ Security Scan: ERROR - {e}")
    
    def run_performance_tests(self):
        """Run performance tests"""
        print("\n⚡ Running Performance Tests...")
        print("-" * 40)
        
        try:
            cmd = [
                'python', '-m', 'pytest',
                'tests/test_performance.py',
                '--tb=short',
                '-v'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            
            self.results['performance_tests'] = {
                'status': 'passed' if result.returncode == 0 else 'failed',
                'return_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
            status_icon = "✅" if result.returncode == 0 else "❌"
            print(f"{status_icon} Performance Tests: {'PASSED' if result.returncode == 0 else 'FAILED'}")
            
        except Exception as e:
            self.results['performance_tests'] = {
                'status': 'error',
                'error': str(e)
            }
            print(f"❌ Performance Tests: ERROR - {e}")
    
    def generate_coverage_report(self):
        """Generate coverage report"""
        print("\n📊 Generating Coverage Report...")
        print("-" * 40)
        
        try:
            if Path('coverage.json').exists():
                with open('coverage.json', 'r') as f:
                    coverage_data = json.load(f)
                
                total_coverage = coverage_data.get('totals', {}).get('percent_covered', 0)
                
                self.results['coverage_report'] = {
                    'total_coverage': total_coverage,
                    'threshold_met': total_coverage >= self.config['coverage_threshold'],
                    'details': coverage_data
                }
                
                status_icon = "✅" if total_coverage >= self.config['coverage_threshold'] else "❌"
                print(f"{status_icon} Coverage: {total_coverage:.1f}% (threshold: {self.config['coverage_threshold']}%)")
            else:
                self.results['coverage_report'] = {
                    'status': 'error',
                    'error': 'Coverage report not found'
                }
                print("❌ Coverage: Report not found")
                
        except Exception as e:
            self.results['coverage_report'] = {
                'status': 'error',
                'error': str(e)
            }
            print(f"❌ Coverage: ERROR - {e}")
    
    def generate_final_report(self, total_time):
        """Generate final QA report"""
        print("\n📋 Final QA Report")
        print("=" * 60)
        
        # Calculate overall score
        scores = []
        
        # Unit tests (30%)
        if self.results['unit_tests'].get('status') == 'passed':
            scores.append(30)
        elif self.results['unit_tests'].get('status') == 'failed':
            scores.append(15)
        else:
            scores.append(0)
        
        # Integration tests (25%)
        if self.results['integration_tests'].get('status') == 'passed':
            scores.append(25)
        elif self.results['integration_tests'].get('status') == 'failed':
            scores.append(12)
        else:
            scores.append(0)
        
        # E2E tests (20%)
        if self.results['e2e_tests'].get('status') == 'passed':
            scores.append(20)
        elif self.results['e2e_tests'].get('status') == 'failed':
            scores.append(10)
        else:
            scores.append(0)
        
        # Quality checks (15%)
        if self.results['quality_checks'].get('status') == 'passed':
            scores.append(15)
        elif self.results['quality_checks'].get('status') == 'failed':
            scores.append(7)
        else:
            scores.append(0)
        
        # Security scan (10%)
        if self.results['security_scan'].get('django_check', {}).get('status') == 'passed':
            scores.append(10)
        else:
            scores.append(0)
        
        total_score = sum(scores)
        
        print(f"🎯 Overall QA Score: {total_score}/100")
        print(f"⏱️  Total Execution Time: {total_time:.2f} seconds")
        
        # Coverage information
        coverage = self.results.get('coverage_report', {}).get('total_coverage', 0)
        print(f"📊 Test Coverage: {coverage:.1f}%")
        
        # Grade assignment
        if total_score >= 90:
            grade = "A"
            status = "🌟 Excellent"
        elif total_score >= 80:
            grade = "B"
            status = "👍 Good"
        elif total_score >= 70:
            grade = "C"
            status = "⚠️  Acceptable"
        elif total_score >= 60:
            grade = "D"
            status = "❌ Needs Improvement"
        else:
            grade = "F"
            status = "💥 Critical Issues"
        
        print(f"📝 Grade: {grade} - {status}")
        
        # Save detailed report
        self.results['summary'] = {
            'total_score': total_score,
            'grade': grade,
            'status': status,
            'total_time': total_time,
            'coverage': coverage
        }
        
        # Save to file
        with open('qa_report.json', 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: qa_report.json")
        
        if Path('htmlcov/index.html').exists():
            print(f"📊 Coverage report available at: htmlcov/index.html")
        
        return total_score >= 70  # Return True if QA passes
    
    def extract_duration(self, output):
        """Extract test duration from pytest output"""
        import re
        
        # Look for pattern like "===== 5 passed in 2.34s ====="
        pattern = r'=+ .+ in ([\d.]+)s =+'
        match = re.search(pattern, output)
        
        if match:
            return float(match.group(1))
        
        return None
    
    def run_quick_check(self):
        """Run quick quality check"""
        print("⚡ Running Quick QA Check...")
        print("-" * 40)
        
        # Run only unit tests and basic quality checks
        self.run_unit_tests()
        self.run_quality_checks()
        self.generate_coverage_report()
        
        # Quick score calculation
        unit_score = 50 if self.results['unit_tests'].get('status') == 'passed' else 0
        quality_score = 30 if self.results['quality_checks'].get('status') == 'passed' else 0
        coverage_score = 20 if self.results.get('coverage_report', {}).get('threshold_met') else 0
        
        total_score = unit_score + quality_score + coverage_score
        
        print(f"\n🎯 Quick QA Score: {total_score}/100")
        
        return total_score >= 70


def main():
    """Main entry point"""
    qa = QAAutomation()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == 'quick':
            success = qa.run_quick_check()
        elif sys.argv[1] == 'full':
            success = qa.run_full_qa_suite()
        else:
            print("Usage: python qa_automation.py [quick|full]")
            return
    else:
        # Default to quick check
        success = qa.run_quick_check()
    
    if not success:
        print("\n❌ QA checks failed!")
        sys.exit(1)
    else:
        print("\n✅ QA checks passed!")


if __name__ == "__main__":
    main()