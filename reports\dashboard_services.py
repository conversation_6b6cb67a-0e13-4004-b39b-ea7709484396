"""
Dashboard Services
Handles interactive dashboard functionality, real-time data updates, and chart generation
"""

import json
import logging
from datetime import datetime, timedelta
from django.db import connection
from django.utils import timezone
from django.core.cache import cache
from django.db.models import Count, Sum, Avg, Max, Min, Q
from django.apps import apps
from .models import Dashboard, DashboardWidget, DashboardTheme
from .services import QueryBuilder

logger = logging.getLogger(__name__)


class DashboardService:
    """
    Service for managing interactive dashboards
    """
    
    def __init__(self):
        self.query_builder = QueryBuilder()
    
    def create_dashboard(self, user, name, dashboard_type='custom', **kwargs):
        """
        Create a new dashboard
        
        Args:
            user: User creating the dashboard
            name: Dashboard name
            dashboard_type: Type of dashboard
            **kwargs: Additional dashboard properties
            
        Returns:
            Dashboard instance
        """
        try:
            dashboard = Dashboard.objects.create(
                user=user,
                name=name,
                dashboard_type=dashboard_type,
                description=kwargs.get('description', ''),
                layout=kwargs.get('layout', self._get_default_layout()),
                settings=kwargs.get('settings', self._get_default_settings()),
                real_time_config=kwargs.get('real_time_config', {}),
                filters=kwargs.get('filters', {}),
                is_public=kwargs.get('is_public', False),
                school=getattr(user, 'employee', {}).get('school') if hasattr(user, 'employee') else None
            )
            
            # Create default widgets if specified
            if kwargs.get('create_default_widgets', False):
                self._create_default_widgets(dashboard, dashboard_type)
            
            logger.info(f"Dashboard '{name}' created successfully for user {user.username}")
            return dashboard
            
        except Exception as e:
            logger.error(f"Failed to create dashboard: {e}")
            raise
    
    def _get_default_layout(self):
        """Get default dashboard layout configuration"""
        return {
            'grid': {
                'columns': 12,
                'row_height': 60,
                'margin': [10, 10],
                'container_padding': [10, 10],
                'max_rows': 20,
                'is_resizable': True,
                'is_draggable': True,
                'prevent_collision': True,
                'use_css_transforms': True
            },
            'breakpoints': {
                'lg': 1200,
                'md': 996,
                'sm': 768,
                'xs': 480,
                'xxs': 0
            },
            'cols': {
                'lg': 12,
                'md': 10,
                'sm': 6,
                'xs': 4,
                'xxs': 2
            }
        }
    
    def _get_default_settings(self):
        """Get default dashboard settings"""
        return {
            'theme': 'default',
            'auto_refresh': True,
            'refresh_interval': 300,  # 5 minutes
            'show_grid': False,
            'show_toolbar': True,
            'allow_editing': True,
            'full_screen_mode': False,
            'export_enabled': True,
            'print_enabled': True,
            'share_enabled': True
        }
    
    def _create_default_widgets(self, dashboard, dashboard_type):
        """Create default widgets based on dashboard type"""
        default_widgets = {
            'executive': [
                {
                    'name': 'Key Metrics',
                    'widget_type': 'metric',
                    'position': {'x': 0, 'y': 0, 'w': 3, 'h': 2},
                    'data_source': {'type': 'kpi', 'metrics': ['total_students', 'total_revenue', 'attendance_rate']}
                },
                {
                    'name': 'Revenue Trend',
                    'widget_type': 'chart',
                    'chart_type': 'line',
                    'position': {'x': 3, 'y': 0, 'w': 6, 'h': 4},
                    'data_source': {'type': 'revenue_trend', 'period': 'monthly'}
                },
                {
                    'name': 'Student Distribution',
                    'widget_type': 'chart',
                    'chart_type': 'pie',
                    'position': {'x': 9, 'y': 0, 'w': 3, 'h': 4},
                    'data_source': {'type': 'student_distribution', 'group_by': 'grade'}
                }
            ],
            'operational': [
                {
                    'name': 'Daily Attendance',
                    'widget_type': 'chart',
                    'chart_type': 'bar',
                    'position': {'x': 0, 'y': 0, 'w': 6, 'h': 3},
                    'data_source': {'type': 'attendance', 'period': 'daily'}
                },
                {
                    'name': 'Class Schedule',
                    'widget_type': 'calendar',
                    'position': {'x': 6, 'y': 0, 'w': 6, 'h': 3},
                    'data_source': {'type': 'schedule', 'view': 'week'}
                },
                {
                    'name': 'Recent Activities',
                    'widget_type': 'table',
                    'position': {'x': 0, 'y': 3, 'w': 12, 'h': 3},
                    'data_source': {'type': 'activities', 'limit': 10}
                }
            ],
            'analytical': [
                {
                    'name': 'Performance Trends',
                    'widget_type': 'chart',
                    'chart_type': 'line',
                    'position': {'x': 0, 'y': 0, 'w': 8, 'h': 4},
                    'data_source': {'type': 'performance_trends', 'period': 'monthly'}
                },
                {
                    'name': 'Grade Distribution',
                    'widget_type': 'chart',
                    'chart_type': 'heatmap',
                    'position': {'x': 8, 'y': 0, 'w': 4, 'h': 4},
                    'data_source': {'type': 'grade_distribution'}
                }
            ]
        }
        
        widgets_config = default_widgets.get(dashboard_type, [])
        
        for i, widget_config in enumerate(widgets_config):
            DashboardWidget.objects.create(
                dashboard=dashboard,
                name=widget_config['name'],
                widget_type=widget_config['widget_type'],
                chart_type=widget_config.get('chart_type'),
                position=widget_config['position'],
                data_source=widget_config['data_source'],
                order=i,
                school=dashboard.school
            )
    
    def add_widget(self, dashboard, widget_config):
        """
        Add a widget to a dashboard
        
        Args:
            dashboard: Dashboard instance
            widget_config: Widget configuration dictionary
            
        Returns:
            DashboardWidget instance
        """
        try:
            widget = DashboardWidget.objects.create(
                dashboard=dashboard,
                name=widget_config['name'],
                widget_type=widget_config['widget_type'],
                chart_type=widget_config.get('chart_type'),
                position=widget_config.get('position', {}),
                data_source=widget_config.get('data_source', {}),
                config=widget_config.get('config', {}),
                refresh_interval=widget_config.get('refresh_interval', 300),
                auto_refresh=widget_config.get('auto_refresh', True),
                order=widget_config.get('order', 0),
                school=dashboard.school
            )
            
            logger.info(f"Widget '{widget.name}' added to dashboard '{dashboard.name}'")
            return widget
            
        except Exception as e:
            logger.error(f"Failed to add widget to dashboard: {e}")
            raise
    
    def update_widget_position(self, widget_id, position):
        """
        Update widget position in dashboard grid
        
        Args:
            widget_id: Widget ID
            position: New position dictionary {x, y, w, h}
        """
        try:
            widget = DashboardWidget.objects.get(id=widget_id)
            widget.position = position
            widget.save(update_fields=['position'])
            
            logger.info(f"Widget position updated for widget {widget_id}")
            
        except DashboardWidget.DoesNotExist:
            logger.error(f"Widget {widget_id} not found")
            raise
        except Exception as e:
            logger.error(f"Failed to update widget position: {e}")
            raise
    
    def get_dashboard_data(self, dashboard_id, user=None):
        """
        Get complete dashboard data including all widgets
        
        Args:
            dashboard_id: Dashboard ID
            user: User requesting the data (for access control)
            
        Returns:
            Dictionary with dashboard and widget data
        """
        try:
            dashboard = Dashboard.objects.get(id=dashboard_id)
            
            # Check access permissions
            if not self._check_dashboard_access(dashboard, user):
                raise PermissionError("Access denied to dashboard")
            
            # Increment view count
            dashboard.increment_view_count()
            
            # Get widgets data
            widgets_data = []
            for widget in dashboard.dashboard_widgets.filter(is_active=True).order_by('order'):
                widget_data = self.get_widget_data(widget)
                widgets_data.append(widget_data)
            
            return {
                'dashboard': {
                    'id': dashboard.id,
                    'name': dashboard.name,
                    'description': dashboard.description,
                    'dashboard_type': dashboard.dashboard_type,
                    'layout': dashboard.layout,
                    'settings': dashboard.settings,
                    'filters': dashboard.filters,
                    'last_updated': dashboard.updated_at.isoformat() if dashboard.updated_at else None
                },
                'widgets': widgets_data,
                'theme': self._get_dashboard_theme(dashboard)
            }
            
        except Dashboard.DoesNotExist:
            logger.error(f"Dashboard {dashboard_id} not found")
            raise
        except Exception as e:
            logger.error(f"Failed to get dashboard data: {e}")
            raise
    
    def _check_dashboard_access(self, dashboard, user):
        """Check if user has access to dashboard"""
        if not user:
            return False
        
        # Owner has full access
        if dashboard.user == user:
            return True
        
        # Public dashboards are accessible to all authenticated users
        if dashboard.is_public:
            return True
        
        # Check if user is in allowed users list
        if dashboard.allowed_users.filter(id=user.id).exists():
            return True
        
        return False
    
    def get_widget_data(self, widget):
        """
        Get data for a specific widget
        
        Args:
            widget: DashboardWidget instance
            
        Returns:
            Dictionary with widget configuration and data
        """
        try:
            # Check cache first
            cache_key = f"widget_data_{widget.id}_{widget.updated_at.timestamp()}"
            cached_data = cache.get(cache_key)
            
            if cached_data and widget.auto_refresh:
                return cached_data
            
            # Generate widget data based on type
            widget_data = {
                'id': widget.id,
                'name': widget.name,
                'widget_type': widget.widget_type,
                'chart_type': widget.chart_type,
                'position': widget.position,
                'config': widget.config,
                'refresh_interval': widget.refresh_interval,
                'auto_refresh': widget.auto_refresh,
                'data': self._generate_widget_data(widget),
                'last_updated': timezone.now().isoformat()
            }
            
            # Cache the data
            if widget.auto_refresh:
                cache.set(cache_key, widget_data, timeout=widget.refresh_interval)
            
            return widget_data
            
        except Exception as e:
            logger.error(f"Failed to get widget data for widget {widget.id}: {e}")
            return {
                'id': widget.id,
                'name': widget.name,
                'widget_type': widget.widget_type,
                'error': str(e),
                'data': None
            }
    
    def _generate_widget_data(self, widget):
        """
        Generate data for a widget based on its data source configuration
        
        Args:
            widget: DashboardWidget instance
            
        Returns:
            Widget data dictionary
        """
        data_source = widget.data_source
        data_type = data_source.get('type', 'custom')
        
        try:
            if data_type == 'kpi':
                return self._generate_kpi_data(data_source)
            elif data_type == 'chart':
                return self._generate_chart_data(data_source)
            elif data_type == 'table':
                return self._generate_table_data(data_source)
            elif data_type == 'revenue_trend':
                return self._generate_revenue_trend_data(data_source)
            elif data_type == 'student_distribution':
                return self._generate_student_distribution_data(data_source)
            elif data_type == 'attendance':
                return self._generate_attendance_data(data_source)
            elif data_type == 'performance_trends':
                return self._generate_performance_trends_data(data_source)
            elif data_type == 'grade_distribution':
                return self._generate_grade_distribution_data(data_source)
            elif data_type == 'activities':
                return self._generate_activities_data(data_source)
            elif data_type == 'schedule':
                return self._generate_schedule_data(data_source)
            elif data_type == 'custom_query':
                return self._generate_custom_query_data(data_source)
            else:
                return {'error': f'Unknown data type: {data_type}'}
                
        except Exception as e:
            logger.error(f"Failed to generate widget data: {e}")
            return {'error': str(e)}
    
    def _generate_kpi_data(self, data_source):
        """Generate KPI metrics data"""
        metrics = data_source.get('metrics', [])
        kpi_data = []
        
        for metric in metrics:
            if metric == 'total_students':
                from students.models import Student
                value = Student.objects.filter(is_active=True).count()
                kpi_data.append({
                    'name': 'Total Students',
                    'value': value,
                    'format': 'number',
                    'icon': 'users',
                    'color': 'primary'
                })
            elif metric == 'total_revenue':
                from finance.models import Payment
                value = Payment.objects.aggregate(total=Sum('amount'))['total'] or 0
                kpi_data.append({
                    'name': 'Total Revenue',
                    'value': float(value),
                    'format': 'currency',
                    'icon': 'dollar-sign',
                    'color': 'success'
                })
            elif metric == 'attendance_rate':
                from academics.models import StudentAttendance
                today = timezone.now().date()
                total_records = StudentAttendance.objects.filter(date=today).count()
                present_records = StudentAttendance.objects.filter(date=today, status='present').count()
                rate = (present_records / total_records * 100) if total_records > 0 else 0
                kpi_data.append({
                    'name': 'Attendance Rate',
                    'value': round(rate, 1),
                    'format': 'percentage',
                    'icon': 'check-circle',
                    'color': 'info'
                })
        
        return {'metrics': kpi_data}
    
    def _generate_chart_data(self, data_source):
        """Generate chart data"""
        # This would be implemented based on specific chart requirements
        return {
            'labels': ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            'datasets': [{
                'label': 'Sample Data',
                'data': [12, 19, 3, 5, 2, 3],
                'backgroundColor': 'rgba(54, 162, 235, 0.2)',
                'borderColor': 'rgba(54, 162, 235, 1)',
                'borderWidth': 1
            }]
        }
    
    def _generate_table_data(self, data_source):
        """Generate table data"""
        return {
            'columns': ['Name', 'Value', 'Status'],
            'rows': [
                ['Sample Row 1', '100', 'Active'],
                ['Sample Row 2', '200', 'Inactive'],
                ['Sample Row 3', '150', 'Active']
            ]
        }
    
    def _generate_revenue_trend_data(self, data_source):
        """Generate revenue trend data"""
        from finance.models import Payment
        
        period = data_source.get('period', 'monthly')
        
        if period == 'monthly':
            # Get last 12 months of revenue data
            end_date = timezone.now().date()
            start_date = end_date - timedelta(days=365)
            
            # Group payments by month
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT 
                        DATE_FORMAT(payment_date, '%%Y-%%m') as month,
                        SUM(amount) as total_amount
                    FROM finance_payment 
                    WHERE payment_date BETWEEN %s AND %s
                    GROUP BY DATE_FORMAT(payment_date, '%%Y-%%m')
                    ORDER BY month
                """, [start_date, end_date])
                
                results = cursor.fetchall()
                
                labels = [row[0] for row in results]
                data = [float(row[1]) for row in results]
                
                return {
                    'labels': labels,
                    'datasets': [{
                        'label': 'Revenue',
                        'data': data,
                        'backgroundColor': 'rgba(75, 192, 192, 0.2)',
                        'borderColor': 'rgba(75, 192, 192, 1)',
                        'borderWidth': 2,
                        'fill': True
                    }]
                }
        
        return {'labels': [], 'datasets': []}
    
    def _generate_student_distribution_data(self, data_source):
        """Generate student distribution data"""
        from students.models import Student
        
        group_by = data_source.get('group_by', 'grade')
        
        if group_by == 'grade':
            distribution = Student.objects.filter(is_active=True).values(
                'current_class__grade__name'
            ).annotate(count=Count('id')).order_by('current_class__grade__level')
            
            labels = [item['current_class__grade__name'] or 'Unknown' for item in distribution]
            data = [item['count'] for item in distribution]
            
            return {
                'labels': labels,
                'datasets': [{
                    'label': 'Students by Grade',
                    'data': data,
                    'backgroundColor': [
                        'rgba(255, 99, 132, 0.8)',
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(255, 205, 86, 0.8)',
                        'rgba(75, 192, 192, 0.8)',
                        'rgba(153, 102, 255, 0.8)',
                        'rgba(255, 159, 64, 0.8)'
                    ]
                }]
            }
        
        return {'labels': [], 'datasets': []}
    
    def _generate_attendance_data(self, data_source):
        """Generate attendance data"""
        from academics.models import StudentAttendance
        
        period = data_source.get('period', 'daily')
        
        if period == 'daily':
            # Get last 7 days attendance data
            end_date = timezone.now().date()
            start_date = end_date - timedelta(days=6)
            
            attendance_data = []
            labels = []
            
            current_date = start_date
            while current_date <= end_date:
                total = StudentAttendance.objects.filter(date=current_date).count()
                present = StudentAttendance.objects.filter(date=current_date, status='present').count()
                
                attendance_data.append(present)
                labels.append(current_date.strftime('%m/%d'))
                
                current_date += timedelta(days=1)
            
            return {
                'labels': labels,
                'datasets': [{
                    'label': 'Present Students',
                    'data': attendance_data,
                    'backgroundColor': 'rgba(40, 167, 69, 0.8)',
                    'borderColor': 'rgba(40, 167, 69, 1)',
                    'borderWidth': 1
                }]
            }
        
        return {'labels': [], 'datasets': []}
    
    def _generate_performance_trends_data(self, data_source):
        """Generate performance trends data"""
        from academics.models import StudentGrade
        
        # Get average grades over time
        period = data_source.get('period', 'monthly')
        
        if period == 'monthly':
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT 
                        DATE_FORMAT(graded_at, '%%Y-%%m') as month,
                        AVG(percentage) as avg_grade
                    FROM academics_studentgrade 
                    WHERE graded_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
                    GROUP BY DATE_FORMAT(graded_at, '%%Y-%%m')
                    ORDER BY month
                """)
                
                results = cursor.fetchall()
                
                labels = [row[0] for row in results]
                data = [float(row[1]) if row[1] else 0 for row in results]
                
                return {
                    'labels': labels,
                    'datasets': [{
                        'label': 'Average Grade',
                        'data': data,
                        'backgroundColor': 'rgba(255, 206, 84, 0.2)',
                        'borderColor': 'rgba(255, 206, 84, 1)',
                        'borderWidth': 2,
                        'fill': True
                    }]
                }
        
        return {'labels': [], 'datasets': []}
    
    def _generate_grade_distribution_data(self, data_source):
        """Generate grade distribution heatmap data"""
        from academics.models import StudentGrade
        
        # This would generate heatmap data for grade distribution
        # For now, return sample data
        return {
            'data': [
                [0, 0, 85], [0, 1, 90], [0, 2, 78], [0, 3, 92],
                [1, 0, 88], [1, 1, 85], [1, 2, 90], [1, 3, 87],
                [2, 0, 92], [2, 1, 89], [2, 2, 85], [2, 3, 91]
            ],
            'xLabels': ['Math', 'Science', 'English', 'History'],
            'yLabels': ['Grade 9', 'Grade 10', 'Grade 11']
        }
    
    def _generate_activities_data(self, data_source):
        """Generate recent activities data"""
        limit = data_source.get('limit', 10)
        
        # This would fetch recent activities from various models
        # For now, return sample data
        return {
            'activities': [
                {
                    'id': 1,
                    'type': 'enrollment',
                    'description': 'New student enrolled: John Doe',
                    'timestamp': timezone.now().isoformat(),
                    'user': 'Admin'
                },
                {
                    'id': 2,
                    'type': 'payment',
                    'description': 'Payment received: $500',
                    'timestamp': (timezone.now() - timedelta(hours=1)).isoformat(),
                    'user': 'Finance'
                }
            ]
        }
    
    def _generate_schedule_data(self, data_source):
        """Generate schedule/calendar data"""
        view = data_source.get('view', 'week')
        
        # This would fetch schedule data
        # For now, return sample data
        return {
            'events': [
                {
                    'id': 1,
                    'title': 'Math Class',
                    'start': timezone.now().isoformat(),
                    'end': (timezone.now() + timedelta(hours=1)).isoformat(),
                    'color': '#007bff'
                }
            ]
        }
    
    def _generate_custom_query_data(self, data_source):
        """Generate data from custom SQL query"""
        query = data_source.get('query', '')
        
        if not query:
            return {'error': 'No query specified'}
        
        try:
            result = self.query_builder.execute_query(query)
            return {
                'columns': result['columns'],
                'rows': [[row[col] for col in result['columns']] for row in result['results']]
            }
        except Exception as e:
            return {'error': str(e)}
    
    def _get_dashboard_theme(self, dashboard):
        """Get theme configuration for dashboard"""
        theme_name = dashboard.settings.get('theme', 'default')
        
        try:
            theme = DashboardTheme.objects.get(name=theme_name, is_active=True)
            return {
                'name': theme.name,
                'colors': theme.colors,
                'fonts': theme.fonts,
                'layout': theme.layout,
                'chart_styles': theme.chart_styles
            }
        except DashboardTheme.DoesNotExist:
            # Return default theme
            return self._get_default_theme()
    
    def _get_default_theme(self):
        """Get default theme configuration"""
        return {
            'name': 'default',
            'colors': {
                'primary': '#007bff',
                'secondary': '#6c757d',
                'success': '#28a745',
                'danger': '#dc3545',
                'warning': '#ffc107',
                'info': '#17a2b8',
                'light': '#f8f9fa',
                'dark': '#343a40'
            },
            'fonts': {
                'family': 'Arial, sans-serif',
                'size': {
                    'small': '12px',
                    'medium': '14px',
                    'large': '16px',
                    'xlarge': '18px'
                }
            },
            'layout': {
                'border_radius': '4px',
                'box_shadow': '0 2px 4px rgba(0,0,0,0.1)',
                'spacing': {
                    'small': '8px',
                    'medium': '16px',
                    'large': '24px'
                }
            },
            'chart_styles': {
                'colors': [
                    '#007bff', '#28a745', '#ffc107', '#dc3545',
                    '#17a2b8', '#6f42c1', '#e83e8c', '#fd7e14'
                ]
            }
        }
    
    def export_dashboard(self, dashboard_id, format_type='pdf'):
        """
        Export dashboard to specified format
        
        Args:
            dashboard_id: Dashboard ID
            format_type: Export format (pdf, png, svg)
            
        Returns:
            Export result dictionary
        """
        try:
            dashboard = Dashboard.objects.get(id=dashboard_id)
            
            # This would implement actual export functionality
            # For now, return success response
            return {
                'success': True,
                'format': format_type,
                'file_path': f'/tmp/dashboard_{dashboard_id}.{format_type}',
                'message': f'Dashboard exported successfully as {format_type.upper()}'
            }
            
        except Dashboard.DoesNotExist:
            return {
                'success': False,
                'error': f'Dashboard {dashboard_id} not found'
            }
        except Exception as e:
            logger.error(f"Failed to export dashboard: {e}")
            return {
                'success': False,
                'error': str(e)
            }


class RealTimeDataService:
    """
    Service for handling real-time data updates in dashboards
    """
    
    def __init__(self):
        self.active_connections = {}
    
    def register_connection(self, dashboard_id, connection_id):
        """Register a WebSocket connection for real-time updates"""
        if dashboard_id not in self.active_connections:
            self.active_connections[dashboard_id] = set()
        
        self.active_connections[dashboard_id].add(connection_id)
        logger.info(f"Connection {connection_id} registered for dashboard {dashboard_id}")
    
    def unregister_connection(self, dashboard_id, connection_id):
        """Unregister a WebSocket connection"""
        if dashboard_id in self.active_connections:
            self.active_connections[dashboard_id].discard(connection_id)
            
            if not self.active_connections[dashboard_id]:
                del self.active_connections[dashboard_id]
        
        logger.info(f"Connection {connection_id} unregistered from dashboard {dashboard_id}")
    
    def broadcast_update(self, dashboard_id, widget_id, data):
        """Broadcast data update to all connected clients"""
        if dashboard_id in self.active_connections:
            update_message = {
                'type': 'widget_update',
                'dashboard_id': dashboard_id,
                'widget_id': widget_id,
                'data': data,
                'timestamp': timezone.now().isoformat()
            }
            
            # This would send the update to all connected WebSocket clients
            # Implementation would depend on the WebSocket framework used
            logger.info(f"Broadcasting update for widget {widget_id} in dashboard {dashboard_id}")
    
    def get_real_time_data(self, widget):
        """Get real-time data for a widget"""
        # This would implement real-time data fetching
        # For now, return the regular widget data
        dashboard_service = DashboardService()
        return dashboard_service._generate_widget_data(widget)