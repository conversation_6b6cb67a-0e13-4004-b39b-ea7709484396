"""
Enhanced Export Services for Reports
Handles multiple export formats, automated generation, and distribution
"""

import os
import io
import csv
import json
import uuid
import logging
from datetime import datetime, timedelta
from django.conf import settings
from django.core.mail import EmailMessage
from django.template.loader import render_to_string
from django.utils import timezone
from django.http import HttpResponse
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from .models import ReportExecution, ReportShare

logger = logging.getLogger(__name__)


class ReportExporter:
    """
    Enhanced report export service supporting multiple formats
    """
    
    def __init__(self):
        self.supported_formats = ['pdf', 'excel', 'csv', 'json', 'html', 'xml']
        self.export_dir = os.path.join(settings.MEDIA_ROOT, 'reports', 'exports')
        os.makedirs(self.export_dir, exist_ok=True)
    
    def export_report(self, report_data, format_type, template_config=None, filename=None):
        """
        Export report data to specified format
        
        Args:
            report_data (dict): Report data to export
            format_type (str): Export format
            template_config (dict): Template configuration
            filename (str): Custom filename
            
        Returns:
            dict: Export result with file path or content
        """
        if format_type not in self.supported_formats:
            raise ValueError(f"Unsupported format: {format_type}")
        
        try:
            # Generate filename if not provided
            if not filename:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"report_{timestamp}.{format_type}"
            
            # Export based on format
            if format_type == 'pdf':
                return self._export_to_pdf(report_data, template_config, filename)
            elif format_type == 'excel':
                return self._export_to_excel(report_data, template_config, filename)
            elif format_type == 'csv':
                return self._export_to_csv(report_data, template_config, filename)
            elif format_type == 'json':
                return self._export_to_json(report_data, template_config, filename)
            elif format_type == 'html':
                return self._export_to_html(report_data, template_config, filename)
            elif format_type == 'xml':
                return self._export_to_xml(report_data, template_config, filename)
                
        except Exception as e:
            logger.error(f"Report export failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _export_to_pdf(self, report_data, template_config, filename):
        """Export to PDF format using ReportLab"""
        try:
            from reportlab.lib import colors
            from reportlab.lib.pagesizes import letter, A4
            from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch
            
            file_path = os.path.join(self.export_dir, filename)
            
            # Create PDF document
            doc = SimpleDocTemplate(file_path, pagesize=A4)
            styles = getSampleStyleSheet()
            story = []
            
            # Add title
            title = template_config.get('title', 'Report') if template_config else 'Report'
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1  # Center alignment
            )
            story.append(Paragraph(title, title_style))
            
            # Add generation info
            info_text = f"Generated on: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}"
            story.append(Paragraph(info_text, styles['Normal']))
            story.append(Spacer(1, 20))
            
            # Add data table
            if report_data.get('data') and report_data.get('columns'):
                # Prepare table data
                table_data = [report_data['columns']]  # Headers
                
                for row in report_data['data']:
                    if isinstance(row, dict):
                        table_row = [str(row.get(col, '')) for col in report_data['columns']]
                    else:
                        table_row = [str(cell) for cell in row]
                    table_data.append(table_row)
                
                # Create table
                table = Table(table_data)
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                
                story.append(table)
            
            # Build PDF
            doc.build(story)
            
            return {
                'success': True,
                'format': 'pdf',
                'file_path': file_path,
                'filename': filename,
                'size': os.path.getsize(file_path),
                'message': 'PDF export completed successfully'
            }
            
        except ImportError:
            logger.error("ReportLab not available for PDF export")
            return {
                'success': False,
                'error': "PDF export requires ReportLab library"
            }
        except Exception as e:
            logger.error(f"PDF export failed: {e}")
            return {
                'success': False,
                'error': f"PDF export failed: {str(e)}"
            }
    
    def _export_to_excel(self, report_data, template_config, filename):
        """Export to Excel format using xlsxwriter"""
        try:
            import xlsxwriter
            
            file_path = os.path.join(self.export_dir, filename)
            
            # Create workbook and worksheet
            workbook = xlsxwriter.Workbook(file_path)
            worksheet = workbook.add_worksheet('Report Data')
            
            # Define formats
            title_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center'
            })
            
            header_format = workbook.add_format({
                'bold': True,
                'bg_color': '#D7E4BC',
                'border': 1
            })
            
            data_format = workbook.add_format({
                'border': 1
            })
            
            # Add title
            title = template_config.get('title', 'Report') if template_config else 'Report'
            worksheet.merge_range('A1:E1', title, title_format)
            
            # Add generation info
            worksheet.write('A2', f"Generated on: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # Add headers and data
            if report_data.get('columns'):
                start_row = 3
                
                # Write headers
                for col_idx, column in enumerate(report_data['columns']):
                    worksheet.write(start_row, col_idx, column, header_format)
                
                # Write data
                for row_idx, row_data in enumerate(report_data.get('data', []), start_row + 1):
                    if isinstance(row_data, dict):
                        for col_idx, column in enumerate(report_data['columns']):
                            worksheet.write(row_idx, col_idx, row_data.get(column, ''), data_format)
                    else:
                        for col_idx, value in enumerate(row_data):
                            worksheet.write(row_idx, col_idx, value, data_format)
                
                # Auto-adjust column widths
                for col_idx, column in enumerate(report_data['columns']):
                    worksheet.set_column(col_idx, col_idx, len(column) + 5)
            
            workbook.close()
            
            return {
                'success': True,
                'format': 'excel',
                'file_path': file_path,
                'filename': filename,
                'size': os.path.getsize(file_path),
                'message': 'Excel export completed successfully'
            }
            
        except ImportError:
            logger.error("xlsxwriter not available for Excel export")
            return {
                'success': False,
                'error': "Excel export requires xlsxwriter library"
            }
        except Exception as e:
            logger.error(f"Excel export failed: {e}")
            return {
                'success': False,
                'error': f"Excel export failed: {str(e)}"
            }
    
    def _export_to_csv(self, report_data, template_config, filename):
        """Export to CSV format"""
        try:
            file_path = os.path.join(self.export_dir, filename)
            
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                
                # Write headers
                if report_data.get('columns'):
                    writer.writerow(report_data['columns'])
                
                # Write data
                for row_data in report_data.get('data', []):
                    if isinstance(row_data, dict):
                        row = [row_data.get(col, '') for col in report_data.get('columns', [])]
                    else:
                        row = row_data
                    writer.writerow(row)
            
            return {
                'success': True,
                'format': 'csv',
                'file_path': file_path,
                'filename': filename,
                'size': os.path.getsize(file_path),
                'message': 'CSV export completed successfully'
            }
            
        except Exception as e:
            logger.error(f"CSV export failed: {e}")
            return {
                'success': False,
                'error': f"CSV export failed: {str(e)}"
            }
    
    def _export_to_json(self, report_data, template_config, filename):
        """Export to JSON format"""
        try:
            export_data = {
                'metadata': {
                    'generated_at': timezone.now().isoformat(),
                    'format': 'json',
                    'row_count': len(report_data.get('data', [])),
                    'columns': report_data.get('columns', [])
                },
                'config': template_config or {},
                'data': report_data.get('data', [])
            }
            
            file_path = os.path.join(self.export_dir, filename)
            
            with open(file_path, 'w', encoding='utf-8') as jsonfile:
                json.dump(export_data, jsonfile, indent=2, default=str, ensure_ascii=False)
            
            return {
                'success': True,
                'format': 'json',
                'file_path': file_path,
                'filename': filename,
                'size': os.path.getsize(file_path),
                'message': 'JSON export completed successfully'
            }
            
        except Exception as e:
            logger.error(f"JSON export failed: {e}")
            return {
                'success': False,
                'error': f"JSON export failed: {str(e)}"
            }
    
    def _export_to_html(self, report_data, template_config, filename):
        """Export to HTML format"""
        try:
            html_content = render_to_string('reports/export/html_template.html', {
                'report_data': report_data,
                'config': template_config or {},
                'generated_at': timezone.now()
            })
            
            file_path = os.path.join(self.export_dir, filename)
            
            with open(file_path, 'w', encoding='utf-8') as htmlfile:
                htmlfile.write(html_content)
            
            return {
                'success': True,
                'format': 'html',
                'file_path': file_path,
                'filename': filename,
                'size': os.path.getsize(file_path),
                'message': 'HTML export completed successfully'
            }
            
        except Exception as e:
            logger.error(f"HTML export failed: {e}")
            return {
                'success': False,
                'error': f"HTML export failed: {str(e)}"
            }
    
    def _export_to_xml(self, report_data, template_config, filename):
        """Export to XML format"""
        try:
            import xml.etree.ElementTree as ET
            
            root = ET.Element('report')
            
            # Add metadata
            metadata = ET.SubElement(root, 'metadata')
            ET.SubElement(metadata, 'generated_at').text = timezone.now().isoformat()
            ET.SubElement(metadata, 'format').text = 'xml'
            ET.SubElement(metadata, 'row_count').text = str(len(report_data.get('data', [])))
            
            # Add columns
            columns = ET.SubElement(root, 'columns')
            for column in report_data.get('columns', []):
                ET.SubElement(columns, 'column').text = column
            
            # Add data
            data_element = ET.SubElement(root, 'data')
            for row_data in report_data.get('data', []):
                row_element = ET.SubElement(data_element, 'row')
                
                if isinstance(row_data, dict):
                    for key, value in row_data.items():
                        field = ET.SubElement(row_element, 'field')
                        field.set('name', key)
                        field.text = str(value) if value is not None else ''
                else:
                    for i, value in enumerate(row_data):
                        field = ET.SubElement(row_element, 'field')
                        field.set('index', str(i))
                        field.text = str(value) if value is not None else ''
            
            # Write to file
            file_path = os.path.join(self.export_dir, filename)
            tree = ET.ElementTree(root)
            tree.write(file_path, encoding='utf-8', xml_declaration=True)
            
            return {
                'success': True,
                'format': 'xml',
                'file_path': file_path,
                'filename': filename,
                'size': os.path.getsize(file_path),
                'message': 'XML export completed successfully'
            }
            
        except Exception as e:
            logger.error(f"XML export failed: {e}")
            return {
                'success': False,
                'error': f"XML export failed: {str(e)}"
            }


class ReportDistributor:
    """
    Service for distributing reports via various channels
    """
    
    def __init__(self):
        self.distribution_methods = ['email', 'download', 'api']
    
    def distribute_report(self, report_execution, distribution_config):
        """
        Distribute report based on configuration
        
        Args:
            report_execution: ReportExecution instance
            distribution_config (dict): Distribution configuration
            
        Returns:
            dict: Distribution result
        """
        method = distribution_config.get('method', 'email')
        
        if method not in self.distribution_methods:
            return {
                'success': False,
                'error': f"Unsupported distribution method: {method}"
            }
        
        try:
            if method == 'email':
                return self._distribute_via_email(report_execution, distribution_config)
            elif method == 'download':
                return self._create_download_link(report_execution, distribution_config)
            elif method == 'api':
                return self._distribute_via_api(report_execution, distribution_config)
                
        except Exception as e:
            logger.error(f"Report distribution failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _distribute_via_email(self, report_execution, config):
        """Distribute report via email"""
        try:
            recipients = config.get('recipients', [])
            if not recipients:
                return {
                    'success': False,
                    'error': 'No recipients specified'
                }
            
            subject = config.get('subject', f'Report: {report_execution.template.name}')
            
            # Render email content
            context = {
                'report_execution': report_execution,
                'config': config
            }
            
            html_content = render_to_string('reports/emails/report_distribution.html', context)
            text_content = render_to_string('reports/emails/report_distribution.txt', context)
            
            # Create email
            email = EmailMessage(
                subject=subject,
                body=text_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=recipients,
                cc=config.get('cc', []),
                bcc=config.get('bcc', [])
            )
            
            # Add HTML alternative
            email.attach_alternative(html_content, "text/html")
            
            # Attach report file if available
            if report_execution.file_path and os.path.exists(report_execution.file_path):
                email.attach_file(report_execution.file_path)
            
            # Send email
            email.send()
            
            return {
                'success': True,
                'method': 'email',
                'recipients_count': len(recipients),
                'message': f'Report sent to {len(recipients)} recipients'
            }
            
        except Exception as e:
            logger.error(f"Email distribution failed: {e}")
            return {
                'success': False,
                'error': f"Email distribution failed: {str(e)}"
            }
    
    def _create_download_link(self, report_execution, config):
        """Create secure download link"""
        try:
            # Create report share
            share = ReportShare.objects.create(
                report_template=report_execution.template,
                share_type='private_link',
                share_token=str(uuid.uuid4()),
                expires_at=timezone.now() + timedelta(days=config.get('expires_days', 7)),
                max_access_count=config.get('max_downloads', 10),
                created_by=report_execution.executed_by,
                school=report_execution.school
            )
            
            # Generate download URL
            from django.urls import reverse
            download_url = reverse('reports:shared_report_download', kwargs={'token': share.share_token})
            
            return {
                'success': True,
                'method': 'download',
                'download_url': download_url,
                'share_token': share.share_token,
                'expires_at': share.expires_at,
                'message': 'Download link created successfully'
            }
            
        except Exception as e:
            logger.error(f"Download link creation failed: {e}")
            return {
                'success': False,
                'error': f"Download link creation failed: {str(e)}"
            }
    
    def _distribute_via_api(self, report_execution, config):
        """Distribute report via API webhook"""
        try:
            import requests
            
            webhook_url = config.get('webhook_url')
            if not webhook_url:
                return {
                    'success': False,
                    'error': 'No webhook URL specified'
                }
            
            # Prepare payload
            payload = {
                'report_id': report_execution.id,
                'template_name': report_execution.template.name,
                'executed_at': report_execution.created_at.isoformat(),
                'row_count': report_execution.row_count,
                'execution_time': str(report_execution.execution_time),
                'file_url': config.get('file_url', ''),
                'metadata': config.get('metadata', {})
            }
            
            # Send webhook
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'SchoolERP-Reports/1.0'
            }
            
            # Add authentication if provided
            if config.get('auth_token'):
                headers['Authorization'] = f"Bearer {config['auth_token']}"
            
            response = requests.post(
                webhook_url,
                json=payload,
                headers=headers,
                timeout=30
            )
            
            response.raise_for_status()
            
            return {
                'success': True,
                'method': 'api',
                'webhook_url': webhook_url,
                'response_status': response.status_code,
                'message': 'API webhook sent successfully'
            }
            
        except Exception as e:
            logger.error(f"API distribution failed: {e}")
            return {
                'success': False,
                'error': f"API distribution failed: {str(e)}"
            }


class ReportArchiver:
    """
    Service for archiving old reports and managing storage
    """
    
    def __init__(self):
        self.archive_dir = os.path.join(settings.MEDIA_ROOT, 'reports', 'archive')
        os.makedirs(self.archive_dir, exist_ok=True)
    
    def archive_old_reports(self, days_old=90):
        """Archive reports older than specified days"""
        try:
            cutoff_date = timezone.now() - timedelta(days=days_old)
            old_executions = ReportExecution.objects.filter(
                created_at__lt=cutoff_date,
                file_path__isnull=False
            )
            
            archived_count = 0
            for execution in old_executions:
                if execution.file_path and os.path.exists(execution.file_path):
                    # Move to archive
                    archive_path = os.path.join(
                        self.archive_dir,
                        f"{execution.id}_{os.path.basename(execution.file_path)}"
                    )
                    
                    os.rename(execution.file_path, archive_path)
                    execution.file_path = archive_path
                    execution.save()
                    
                    archived_count += 1
            
            return {
                'success': True,
                'archived_count': archived_count,
                'message': f'Archived {archived_count} old reports'
            }
            
        except Exception as e:
            logger.error(f"Report archiving failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def cleanup_expired_shares(self):
        """Clean up expired report shares"""
        try:
            expired_shares = ReportShare.objects.filter(
                expires_at__lt=timezone.now(),
                is_active=True
            )
            
            expired_count = expired_shares.count()
            expired_shares.update(is_active=False)
            
            return {
                'success': True,
                'expired_count': expired_count,
                'message': f'Cleaned up {expired_count} expired shares'
            }
            
        except Exception as e:
            logger.error(f"Share cleanup failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }