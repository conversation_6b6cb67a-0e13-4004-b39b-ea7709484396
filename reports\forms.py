"""
Forms for report builder and management
"""

from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from .models import ReportTemplate, ReportBuilder, ReportSchedule, ReportShare
from .services import QueryBuilder
import json


class ReportBuilderForm(forms.ModelForm):
    """
    Form for creating and editing report builders
    """
    
    class Meta:
        model = ReportBuilder
        fields = [
            'name', 'name_ar', 'description', 'is_public', 'is_template'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('Enter report builder name')
            }),
            'name_ar': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('Enter Arabic name (optional)')
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': _('Enter description (optional)')
            }),
            'is_public': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'is_template': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Set required fields
        self.fields['name'].required = True
        
        # Add help text
        self.fields['is_public'].help_text = _('Allow other users to view and use this report builder')
        self.fields['is_template'].help_text = _('Make this available as a template for new reports')
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        
        if self.user:
            instance.created_by = self.user
        
        if commit:
            instance.save()
        
        return instance


class ReportTemplateForm(forms.ModelForm):
    """
    Form for creating and editing report templates
    """
    
    report_builder = forms.ModelChoiceField(
        queryset=ReportBuilder.objects.none(),
        required=False,
        empty_label=_('Select a report builder (optional)'),
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    class Meta:
        model = ReportTemplate
        fields = [
            'name', 'name_ar', 'report_type', 'description', 
            'query', 'parameters', 'is_public', 'report_builder'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('Enter report name')
            }),
            'name_ar': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('Enter Arabic name (optional)')
            }),
            'report_type': forms.Select(attrs={
                'class': 'form-control'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': _('Enter description (optional)')
            }),
            'query': forms.Textarea(attrs={
                'class': 'form-control font-monospace',
                'rows': 10,
                'placeholder': _('Enter SQL query or leave empty if using report builder')
            }),
            'parameters': forms.Textarea(attrs={
                'class': 'form-control font-monospace',
                'rows': 5,
                'placeholder': _('Enter parameters in JSON format (optional)')
            }),
            'is_public': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Set queryset for report_builder field
        if self.user:
            self.fields['report_builder'].queryset = ReportBuilder.objects.filter(
                models.Q(created_by=self.user) | models.Q(is_public=True),
                school=self.user.employee.school if hasattr(self.user, 'employee') else None
            )
        
        # Set required fields
        self.fields['name'].required = True
        self.fields['report_type'].required = True
        
        # Add help text
        self.fields['query'].help_text = _('SQL query for the report. Leave empty if using visual report builder.')
        self.fields['parameters'].help_text = _('JSON object with parameter definitions')
        self.fields['is_public'].help_text = _('Allow other users to view and use this report')
    
    def clean_parameters(self):
        """Validate parameters JSON"""
        parameters = self.cleaned_data.get('parameters', '')
        
        if parameters:
            try:
                json.loads(parameters)
            except json.JSONDecodeError:
                raise ValidationError(_('Parameters must be valid JSON'))
        
        return parameters
    
    def clean(self):
        """Validate form data"""
        cleaned_data = super().clean()
        query = cleaned_data.get('query')
        report_builder = cleaned_data.get('report_builder')
        
        # Either query or report_builder must be provided
        if not query and not report_builder:
            raise ValidationError(_('Either SQL query or report builder must be provided'))
        
        return cleaned_data
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        
        if self.user:
            instance.created_by = self.user
            if hasattr(self.user, 'employee'):
                instance.school = self.user.employee.school
        
        if commit:
            instance.save()
        
        return instance


class ReportScheduleForm(forms.ModelForm):
    """
    Form for creating and editing report schedules
    """
    
    # Additional fields for schedule configuration
    time = forms.TimeField(
        required=False,
        widget=forms.TimeInput(attrs={
            'class': 'form-control',
            'type': 'time'
        }),
        help_text=_('Time to run the report')
    )
    
    day_of_week = forms.ChoiceField(
        choices=[
            (1, _('Monday')),
            (2, _('Tuesday')),
            (3, _('Wednesday')),
            (4, _('Thursday')),
            (5, _('Friday')),
            (6, _('Saturday')),
            (7, _('Sunday')),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'}),
        help_text=_('Day of week for weekly reports')
    )
    
    day_of_month = forms.IntegerField(
        min_value=1,
        max_value=31,
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'min': 1,
            'max': 31
        }),
        help_text=_('Day of month for monthly reports')
    )
    
    recipients_text = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': _('Enter email addresses, one per line')
        }),
        help_text=_('Email addresses to send the report to'),
        required=False
    )
    
    class Meta:
        model = ReportSchedule
        fields = [
            'name', 'frequency', 'delivery_method', 'is_active'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('Enter schedule name')
            }),
            'frequency': forms.Select(attrs={
                'class': 'form-control'
            }),
            'delivery_method': forms.Select(attrs={
                'class': 'form-control'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.report_template = kwargs.pop('report_template', None)
        super().__init__(*args, **kwargs)
        
        # Set required fields
        self.fields['name'].required = True
        self.fields['frequency'].required = True
        
        # Populate recipients_text from instance
        if self.instance and self.instance.pk:
            recipients = self.instance.recipients
            if isinstance(recipients, list):
                self.fields['recipients_text'].initial = '\n'.join(recipients)
            elif isinstance(recipients, str):
                self.fields['recipients_text'].initial = recipients
    
    def clean_recipients_text(self):
        """Validate and convert recipients text to list"""
        recipients_text = self.cleaned_data.get('recipients_text', '')
        
        if not recipients_text:
            return []
        
        # Split by lines and validate email addresses
        recipients = []
        for line in recipients_text.split('\n'):
            email = line.strip()
            if email:
                # Basic email validation
                if '@' not in email or '.' not in email:
                    raise ValidationError(f'Invalid email address: {email}')
                recipients.append(email)
        
        return recipients
    
    def clean(self):
        """Validate schedule configuration"""
        cleaned_data = super().clean()
        frequency = cleaned_data.get('frequency')
        time = cleaned_data.get('time')
        day_of_week = cleaned_data.get('day_of_week')
        day_of_month = cleaned_data.get('day_of_month')
        
        # Validate required fields based on frequency
        if frequency == 'daily' and not time:
            raise ValidationError(_('Time is required for daily reports'))
        elif frequency == 'weekly' and (not time or not day_of_week):
            raise ValidationError(_('Time and day of week are required for weekly reports'))
        elif frequency == 'monthly' and (not time or not day_of_month):
            raise ValidationError(_('Time and day of month are required for monthly reports'))
        
        return cleaned_data
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        
        if self.user:
            instance.created_by = self.user
        
        if self.report_template:
            instance.report_template = self.report_template
        
        # Build schedule configuration
        schedule_config = {}
        if self.cleaned_data.get('time'):
            schedule_config['time'] = self.cleaned_data['time'].strftime('%H:%M')
        if self.cleaned_data.get('day_of_week'):
            schedule_config['day_of_week'] = int(self.cleaned_data['day_of_week'])
        if self.cleaned_data.get('day_of_month'):
            schedule_config['day_of_month'] = self.cleaned_data['day_of_month']
        
        instance.schedule_config = schedule_config
        
        # Set recipients
        recipients = self.cleaned_data.get('recipients_text', [])
        instance.recipients = recipients
        
        if commit:
            instance.save()
        
        return instance


class ReportShareForm(forms.ModelForm):
    """
    Form for creating and editing report shares
    """
    
    expires_in_days = forms.IntegerField(
        min_value=1,
        max_value=365,
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'min': 1,
            'max': 365,
            'placeholder': _('Number of days until expiration')
        }),
        help_text=_('Number of days until the share link expires (optional)')
    )
    
    class Meta:
        model = ReportShare
        fields = [
            'share_type', 'max_access_count', 'is_active'
        ]
        widgets = {
            'share_type': forms.Select(attrs={
                'class': 'form-control'
            }),
            'max_access_count': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 1,
                'placeholder': _('Maximum number of accesses (optional)')
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.report_template = kwargs.pop('report_template', None)
        super().__init__(*args, **kwargs)
        
        # Set required fields
        self.fields['share_type'].required = True
        
        # Add help text
        self.fields['max_access_count'].help_text = _('Limit the number of times this report can be accessed')
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        
        if self.user:
            instance.created_by = self.user
        
        if self.report_template:
            instance.report_template = self.report_template
        
        # Set expiration date
        expires_in_days = self.cleaned_data.get('expires_in_days')
        if expires_in_days:
            from django.utils import timezone
            from datetime import timedelta
            instance.expires_at = timezone.now() + timedelta(days=expires_in_days)
        
        # Generate share token
        if not instance.share_token:
            import uuid
            instance.share_token = str(uuid.uuid4())
        
        if commit:
            instance.save()
        
        return instance


class QueryBuilderForm(forms.Form):
    """
    Form for visual query builder interface
    """
    
    query_config = forms.CharField(
        widget=forms.HiddenInput(),
        required=True
    )
    
    def clean_query_config(self):
        """Validate query configuration"""
        query_config_str = self.cleaned_data.get('query_config', '')
        
        try:
            query_config = json.loads(query_config_str)
        except json.JSONDecodeError:
            raise ValidationError(_('Invalid query configuration'))
        
        # Validate using QueryBuilder service
        query_builder = QueryBuilder()
        validation_result = query_builder.validate_query_config(query_config)
        
        if not validation_result['valid']:
            errors = validation_result['errors']
            raise ValidationError(_('Query validation failed: ') + '; '.join(errors))
        
        return query_config


class ReportParametersForm(forms.Form):
    """
    Dynamic form for report parameters
    """
    
    def __init__(self, *args, **kwargs):
        parameters_config = kwargs.pop('parameters_config', {})
        super().__init__(*args, **kwargs)
        
        # Dynamically create fields based on parameters configuration
        if isinstance(parameters_config, str):
            try:
                parameters_config = json.loads(parameters_config)
            except json.JSONDecodeError:
                parameters_config = {}
        
        for param_name, param_config in parameters_config.items():
            field_type = param_config.get('type', 'text')
            field_label = param_config.get('label', param_name)
            field_required = param_config.get('required', False)
            field_default = param_config.get('default')
            field_choices = param_config.get('choices')
            
            # Create appropriate field based on type
            if field_type == 'text':
                field = forms.CharField(
                    label=field_label,
                    required=field_required,
                    initial=field_default,
                    widget=forms.TextInput(attrs={'class': 'form-control'})
                )
            elif field_type == 'number':
                field = forms.IntegerField(
                    label=field_label,
                    required=field_required,
                    initial=field_default,
                    widget=forms.NumberInput(attrs={'class': 'form-control'})
                )
            elif field_type == 'date':
                field = forms.DateField(
                    label=field_label,
                    required=field_required,
                    initial=field_default,
                    widget=forms.DateInput(attrs={
                        'class': 'form-control',
                        'type': 'date'
                    })
                )
            elif field_type == 'select' and field_choices:
                choices = [(choice, choice) for choice in field_choices]
                field = forms.ChoiceField(
                    label=field_label,
                    required=field_required,
                    initial=field_default,
                    choices=choices,
                    widget=forms.Select(attrs={'class': 'form-control'})
                )
            elif field_type == 'boolean':
                field = forms.BooleanField(
                    label=field_label,
                    required=field_required,
                    initial=field_default,
                    widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
                )
            else:
                # Default to text field
                field = forms.CharField(
                    label=field_label,
                    required=field_required,
                    initial=field_default,
                    widget=forms.TextInput(attrs={'class': 'form-control'})
                )
            
            self.fields[param_name] = field