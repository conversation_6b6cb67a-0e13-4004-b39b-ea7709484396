# Generated by Django 5.2.4 on 2025-08-06 14:15

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0003_calendarevent_calendareventattendee_and_more"),
        ("reports", "0002_analytics_created_by_analytics_school_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ReportBuilder",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.Boolean<PERSON>ield(default=True, verbose_name="Is Active"),
                ),
                (
                    "name",
                    models.Char<PERSON>ield(
                        max_length=200, verbose_name="Report Builder Name"
                    ),
                ),
                (
                    "name_ar",
                    models.CharField(
                        blank=True,
                        max_length=200,
                        null=True,
                        verbose_name="Report Builder Name (Arabic)",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="Description"),
                ),
                (
                    "report_config",
                    models.JSONField(
                        default=dict,
                        help_text="Drag-and-drop report configuration in JSON format",
                        verbose_name="Report Configuration",
                    ),
                ),
                (
                    "data_sources",
                    models.JSONField(
                        default=list,
                        help_text="List of available data sources and their fields",
                        verbose_name="Available Data Sources",
                    ),
                ),
                (
                    "query_config",
                    models.JSONField(
                        default=dict,
                        help_text="Visual query builder configuration",
                        verbose_name="Query Configuration",
                    ),
                ),
                (
                    "layout_config",
                    models.JSONField(
                        default=dict,
                        help_text="Report layout, styling, and formatting options",
                        verbose_name="Layout Configuration",
                    ),
                ),
                (
                    "is_public",
                    models.BooleanField(default=False, verbose_name="Is Public"),
                ),
                (
                    "is_template",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this can be used as a template for new reports",
                        verbose_name="Is Template",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_report_builders",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Report Builder",
                "verbose_name_plural": "Report Builders",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="ReportSchedule",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "name",
                    models.CharField(max_length=200, verbose_name="Schedule Name"),
                ),
                (
                    "frequency",
                    models.CharField(
                        choices=[
                            ("once", "Once"),
                            ("daily", "Daily"),
                            ("weekly", "Weekly"),
                            ("monthly", "Monthly"),
                            ("quarterly", "Quarterly"),
                            ("yearly", "Yearly"),
                        ],
                        max_length=20,
                        verbose_name="Frequency",
                    ),
                ),
                (
                    "schedule_config",
                    models.JSONField(
                        default=dict,
                        help_text="Detailed scheduling configuration (time, day, etc.)",
                        verbose_name="Schedule Configuration",
                    ),
                ),
                (
                    "recipients",
                    models.JSONField(
                        default=list,
                        help_text="List of email addresses or user IDs",
                        verbose_name="Recipients",
                    ),
                ),
                (
                    "delivery_method",
                    models.CharField(
                        choices=[
                            ("email", "Email"),
                            ("download", "Download Link"),
                            ("dashboard", "Dashboard"),
                            ("api", "API Endpoint"),
                        ],
                        default="email",
                        max_length=20,
                        verbose_name="Delivery Method",
                    ),
                ),
                (
                    "parameters",
                    models.JSONField(default=dict, verbose_name="Report Parameters"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "next_run",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Next Run Time"
                    ),
                ),
                (
                    "last_run",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Last Run Time"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_schedules",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "report_template",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="schedules",
                        to="reports.reporttemplate",
                        verbose_name="Report Template",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Report Schedule",
                "verbose_name_plural": "Report Schedules",
                "ordering": ["next_run"],
            },
        ),
        migrations.CreateModel(
            name="ReportShare",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "share_type",
                    models.CharField(
                        choices=[
                            ("public_link", "Public Link"),
                            ("private_link", "Private Link"),
                            ("user_access", "User Access"),
                            ("group_access", "Group Access"),
                            ("email_share", "Email Share"),
                        ],
                        max_length=20,
                        verbose_name="Share Type",
                    ),
                ),
                (
                    "share_config",
                    models.JSONField(
                        default=dict,
                        help_text="Sharing permissions and access configuration",
                        verbose_name="Share Configuration",
                    ),
                ),
                (
                    "share_token",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        unique=True,
                        verbose_name="Share Token",
                    ),
                ),
                (
                    "expires_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Expires At"
                    ),
                ),
                (
                    "access_count",
                    models.PositiveIntegerField(default=0, verbose_name="Access Count"),
                ),
                (
                    "max_access_count",
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name="Max Access Count"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "allowed_users",
                    models.ManyToManyField(
                        blank=True,
                        related_name="shared_reports",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Allowed Users",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_shares",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "report_template",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="shares",
                        to="reports.reporttemplate",
                        verbose_name="Report Template",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Report Share",
                "verbose_name_plural": "Report Shares",
                "ordering": ["-created_at"],
            },
        ),
    ]
