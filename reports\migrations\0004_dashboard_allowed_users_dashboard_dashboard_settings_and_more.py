# Generated by Django 5.2.5 on 2025-08-08 14:09

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0005_integrationprovider_integration_integrationschedule_and_more"),
        ("reports", "0003_reportbuilder_reportschedule_reportshare"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="dashboard",
            name="allowed_users",
            field=models.ManyToManyField(
                blank=True,
                related_name="accessible_dashboards",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Allowed Users",
            ),
        ),
        migrations.AddField(
            model_name="dashboard",
            name="dashboard_settings",
            field=models.JSONField(
                default=dict,
                help_text="Dashboard-wide settings like refresh rate, theme, etc.",
                verbose_name="Dashboard Settings",
            ),
        ),
        migrations.AddField(
            model_name="dashboard",
            name="dashboard_type",
            field=models.CharField(
                choices=[
                    ("executive", "Executive Dashboard"),
                    ("operational", "Operational Dashboard"),
                    ("analytical", "Analytical Dashboard"),
                    ("strategic", "Strategic Dashboard"),
                    ("custom", "Custom Dashboard"),
                ],
                default="custom",
                max_length=20,
                verbose_name="Dashboard Type",
            ),
        ),
        migrations.AddField(
            model_name="dashboard",
            name="description",
            field=models.TextField(blank=True, null=True, verbose_name="Description"),
        ),
        migrations.AddField(
            model_name="dashboard",
            name="filters",
            field=models.JSONField(
                default=dict,
                help_text="Global filters applied to all widgets",
                verbose_name="Dashboard Filters",
            ),
        ),
        migrations.AddField(
            model_name="dashboard",
            name="is_public",
            field=models.BooleanField(
                default=False, verbose_name="Is Public Dashboard"
            ),
        ),
        migrations.AddField(
            model_name="dashboard",
            name="last_accessed",
            field=models.DateTimeField(
                blank=True, null=True, verbose_name="Last Accessed"
            ),
        ),
        migrations.AddField(
            model_name="dashboard",
            name="real_time_config",
            field=models.JSONField(
                default=dict,
                help_text="Configuration for real-time data updates",
                verbose_name="Real-time Configuration",
            ),
        ),
        migrations.AddField(
            model_name="dashboard",
            name="view_count",
            field=models.PositiveIntegerField(default=0, verbose_name="View Count"),
        ),
        migrations.AlterField(
            model_name="dashboard",
            name="layout",
            field=models.JSONField(
                default=dict,
                help_text="Grid layout configuration for widgets",
                verbose_name="Dashboard Layout",
            ),
        ),
        migrations.AlterField(
            model_name="dashboard",
            name="widgets",
            field=models.JSONField(
                default=list,
                help_text="List of widget configurations",
                verbose_name="Dashboard Widgets",
            ),
        ),
        migrations.CreateModel(
            name="DashboardTheme",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="Theme Name"
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="Description"),
                ),
                (
                    "colors",
                    models.JSONField(
                        default=dict,
                        help_text="Primary, secondary, accent colors, etc.",
                        verbose_name="Color Scheme",
                    ),
                ),
                (
                    "fonts",
                    models.JSONField(
                        default=dict,
                        help_text="Font families, sizes, weights",
                        verbose_name="Font Configuration",
                    ),
                ),
                (
                    "layout",
                    models.JSONField(
                        default=dict,
                        help_text="Spacing, borders, shadows, etc.",
                        verbose_name="Layout Settings",
                    ),
                ),
                (
                    "chart_styles",
                    models.JSONField(
                        default=dict,
                        help_text="Default chart colors and styling",
                        verbose_name="Chart Styles",
                    ),
                ),
                (
                    "is_default",
                    models.BooleanField(default=False, verbose_name="Is Default Theme"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_themes",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Dashboard Theme",
                "verbose_name_plural": "Dashboard Themes",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="DashboardWidget",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                ("name", models.CharField(max_length=200, verbose_name="Widget Name")),
                (
                    "widget_type",
                    models.CharField(
                        choices=[
                            ("chart", "Chart Widget"),
                            ("table", "Table Widget"),
                            ("metric", "Metric Widget"),
                            ("gauge", "Gauge Widget"),
                            ("map", "Map Widget"),
                            ("calendar", "Calendar Widget"),
                            ("text", "Text Widget"),
                            ("image", "Image Widget"),
                            ("iframe", "IFrame Widget"),
                            ("custom", "Custom Widget"),
                        ],
                        max_length=20,
                        verbose_name="Widget Type",
                    ),
                ),
                (
                    "chart_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("line", "Line Chart"),
                            ("bar", "Bar Chart"),
                            ("pie", "Pie Chart"),
                            ("doughnut", "Doughnut Chart"),
                            ("area", "Area Chart"),
                            ("scatter", "Scatter Plot"),
                            ("bubble", "Bubble Chart"),
                            ("radar", "Radar Chart"),
                            ("polar", "Polar Chart"),
                            ("heatmap", "Heatmap"),
                        ],
                        max_length=20,
                        null=True,
                        verbose_name="Chart Type",
                    ),
                ),
                (
                    "position",
                    models.JSONField(
                        default=dict,
                        help_text="Grid position: {x, y, width, height}",
                        verbose_name="Widget Position",
                    ),
                ),
                (
                    "data_source",
                    models.JSONField(
                        default=dict,
                        help_text="Configuration for data retrieval",
                        verbose_name="Data Source Configuration",
                    ),
                ),
                (
                    "config",
                    models.JSONField(
                        default=dict,
                        help_text="Widget-specific configuration options",
                        verbose_name="Widget Configuration",
                    ),
                ),
                (
                    "refresh_interval",
                    models.PositiveIntegerField(
                        default=300, verbose_name="Refresh Interval (seconds)"
                    ),
                ),
                (
                    "auto_refresh",
                    models.BooleanField(default=True, verbose_name="Auto Refresh"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "order",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Display Order"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "dashboard",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="dashboard_widgets",
                        to="reports.dashboard",
                        verbose_name="Dashboard",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Dashboard Widget",
                "verbose_name_plural": "Dashboard Widgets",
                "ordering": ["dashboard", "order", "name"],
            },
        ),
    ]
