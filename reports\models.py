from django.db import models
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from core.models import BaseModel
import json


class ReportBuilder(BaseModel):
    """
    Report builder configuration for drag-and-drop report creation
    """
    name = models.CharField(
        max_length=200,
        verbose_name=_('Report Builder Name')
    )
    
    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Report Builder Name (Arabic)')
    )
    
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )
    
    # JSON schema for report configuration
    report_config = models.JSONField(
        default=dict,
        verbose_name=_('Report Configuration'),
        help_text=_('Drag-and-drop report configuration in JSON format')
    )
    
    # Available data sources for this report builder
    data_sources = models.JSONField(
        default=list,
        verbose_name=_('Available Data Sources'),
        help_text=_('List of available data sources and their fields')
    )
    
    # Query builder configuration
    query_config = models.J<PERSON><PERSON>ield(
        default=dict,
        verbose_name=_('Query Configuration'),
        help_text=_('Visual query builder configuration')
    )
    
    # Report layout and styling
    layout_config = models.JSONField(
        default=dict,
        verbose_name=_('Layout Configuration'),
        help_text=_('Report layout, styling, and formatting options')
    )
    
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_report_builders',
        verbose_name=_('Created By')
    )
    
    is_public = models.BooleanField(
        default=False,
        verbose_name=_('Is Public')
    )
    
    is_template = models.BooleanField(
        default=False,
        verbose_name=_('Is Template'),
        help_text=_('Whether this can be used as a template for new reports')
    )
    
    class Meta:
        verbose_name = _('Report Builder')
        verbose_name_plural = _('Report Builders')
        ordering = ['name']
    
    def __str__(self):
        return self.name
    
    def get_generated_sql(self):
        """Generate SQL query from visual query builder configuration"""
        # This will be implemented to convert visual query to SQL
        return self.query_config.get('generated_sql', '')


class ReportSchedule(BaseModel):
    """
    Report scheduling configuration
    """
    FREQUENCY_CHOICES = [
        ('once', _('Once')),
        ('daily', _('Daily')),
        ('weekly', _('Weekly')),
        ('monthly', _('Monthly')),
        ('quarterly', _('Quarterly')),
        ('yearly', _('Yearly')),
    ]
    
    DELIVERY_METHODS = [
        ('email', _('Email')),
        ('download', _('Download Link')),
        ('dashboard', _('Dashboard')),
        ('api', _('API Endpoint')),
    ]
    
    report_template = models.ForeignKey(
        'ReportTemplate',
        on_delete=models.CASCADE,
        related_name='schedules',
        verbose_name=_('Report Template')
    )
    
    name = models.CharField(
        max_length=200,
        verbose_name=_('Schedule Name')
    )
    
    frequency = models.CharField(
        max_length=20,
        choices=FREQUENCY_CHOICES,
        verbose_name=_('Frequency')
    )
    
    # Scheduling configuration
    schedule_config = models.JSONField(
        default=dict,
        verbose_name=_('Schedule Configuration'),
        help_text=_('Detailed scheduling configuration (time, day, etc.)')
    )
    
    # Recipients and delivery
    recipients = models.JSONField(
        default=list,
        verbose_name=_('Recipients'),
        help_text=_('List of email addresses or user IDs')
    )
    
    delivery_method = models.CharField(
        max_length=20,
        choices=DELIVERY_METHODS,
        default='email',
        verbose_name=_('Delivery Method')
    )
    
    # Parameters for report generation
    parameters = models.JSONField(
        default=dict,
        verbose_name=_('Report Parameters')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    
    next_run = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Next Run Time')
    )
    
    last_run = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Last Run Time')
    )
    
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_schedules',
        verbose_name=_('Created By')
    )
    
    class Meta:
        verbose_name = _('Report Schedule')
        verbose_name_plural = _('Report Schedules')
        ordering = ['next_run']
    
    def __str__(self):
        return f"{self.name} - {self.get_frequency_display()}"


class ReportShare(BaseModel):
    """
    Report sharing configuration
    """
    SHARE_TYPES = [
        ('public_link', _('Public Link')),
        ('private_link', _('Private Link')),
        ('user_access', _('User Access')),
        ('group_access', _('Group Access')),
        ('email_share', _('Email Share')),
    ]
    
    report_template = models.ForeignKey(
        'ReportTemplate',
        on_delete=models.CASCADE,
        related_name='shares',
        verbose_name=_('Report Template')
    )
    
    share_type = models.CharField(
        max_length=20,
        choices=SHARE_TYPES,
        verbose_name=_('Share Type')
    )
    
    # Share configuration
    share_config = models.JSONField(
        default=dict,
        verbose_name=_('Share Configuration'),
        help_text=_('Sharing permissions and access configuration')
    )
    
    # Access control
    allowed_users = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        blank=True,
        related_name='shared_reports',
        verbose_name=_('Allowed Users')
    )
    
    # Share link and access
    share_token = models.CharField(
        max_length=100,
        unique=True,
        null=True,
        blank=True,
        verbose_name=_('Share Token')
    )
    
    expires_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Expires At')
    )
    
    access_count = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Access Count')
    )
    
    max_access_count = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name=_('Max Access Count')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_shares',
        verbose_name=_('Created By')
    )
    
    class Meta:
        verbose_name = _('Report Share')
        verbose_name_plural = _('Report Shares')
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.report_template.name} - {self.get_share_type_display()}"


class ReportTemplate(BaseModel):
    """
    Report template model for custom reports
    """
    REPORT_TYPES = (
        ('student', _('Student Report')),
        ('financial', _('Financial Report')),
        ('academic', _('Academic Report')),
        ('attendance', _('Attendance Report')),
        ('hr', _('HR Report')),
        ('custom', _('Custom Report')),
    )

    name = models.CharField(
        max_length=200,
        verbose_name=_('Report Name')
    )

    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Report Name (Arabic)')
    )

    report_type = models.CharField(
        max_length=20,
        choices=REPORT_TYPES,
        verbose_name=_('Report Type')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    query = models.TextField(
        verbose_name=_('SQL Query')
    )

    parameters = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Parameters')
    )

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_reports',
        verbose_name=_('Created By')
    )

    is_public = models.BooleanField(
        default=False,
        verbose_name=_('Is Public')
    )

    class Meta:
        verbose_name = _('Report Template')
        verbose_name_plural = _('Report Templates')
        ordering = ['name']

    def __str__(self):
        return self.name


class ReportExecution(BaseModel):
    """
    Report execution history model
    """
    template = models.ForeignKey(
        ReportTemplate,
        on_delete=models.CASCADE,
        related_name='executions',
        verbose_name=_('Report Template')
    )

    executed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='executed_reports',
        verbose_name=_('Executed By')
    )

    parameters_used = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Parameters Used')
    )

    execution_time = models.DurationField(
        verbose_name=_('Execution Time')
    )

    row_count = models.PositiveIntegerField(
        verbose_name=_('Row Count')
    )

    file_path = models.CharField(
        max_length=500,
        blank=True,
        null=True,
        verbose_name=_('Generated File Path')
    )

    class Meta:
        verbose_name = _('Report Execution')
        verbose_name_plural = _('Report Executions')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.template.name} - {self.executed_by} ({self.created_at})"


class Dashboard(BaseModel):
    """
    Interactive dashboard configuration model
    """
    DASHBOARD_TYPES = [
        ('executive', _('Executive Dashboard')),
        ('operational', _('Operational Dashboard')),
        ('analytical', _('Analytical Dashboard')),
        ('strategic', _('Strategic Dashboard')),
        ('custom', _('Custom Dashboard')),
    ]
    
    name = models.CharField(
        max_length=200,
        verbose_name=_('Dashboard Name')
    )

    name_ar = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Dashboard Name (Arabic)')
    )
    
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )
    
    dashboard_type = models.CharField(
        max_length=20,
        choices=DASHBOARD_TYPES,
        default='custom',
        verbose_name=_('Dashboard Type')
    )

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='dashboards',
        verbose_name=_('User')
    )

    # Dashboard layout configuration
    layout = models.JSONField(
        default=dict,
        verbose_name=_('Dashboard Layout'),
        help_text=_('Grid layout configuration for widgets')
    )

    # Widget configurations
    widgets = models.JSONField(
        default=list,
        verbose_name=_('Dashboard Widgets'),
        help_text=_('List of widget configurations')
    )
    
    # Dashboard settings
    dashboard_settings = models.JSONField(
        default=dict,
        verbose_name=_('Dashboard Settings'),
        help_text=_('Dashboard-wide settings like refresh rate, theme, etc.')
    )
    
    # Real-time data configuration
    real_time_config = models.JSONField(
        default=dict,
        verbose_name=_('Real-time Configuration'),
        help_text=_('Configuration for real-time data updates')
    )
    
    # Filters and parameters
    filters = models.JSONField(
        default=dict,
        verbose_name=_('Dashboard Filters'),
        help_text=_('Global filters applied to all widgets')
    )

    is_default = models.BooleanField(
        default=False,
        verbose_name=_('Is Default Dashboard')
    )
    
    is_public = models.BooleanField(
        default=False,
        verbose_name=_('Is Public Dashboard')
    )
    
    # Access control
    allowed_users = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        blank=True,
        related_name='accessible_dashboards',
        verbose_name=_('Allowed Users')
    )
    
    # Dashboard analytics
    view_count = models.PositiveIntegerField(
        default=0,
        verbose_name=_('View Count')
    )
    
    last_accessed = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Last Accessed')
    )

    class Meta:
        verbose_name = _('Dashboard')
        verbose_name_plural = _('Dashboards')
        ordering = ['user', 'name']

    def __str__(self):
        return f"{self.user.username} - {self.name}"
    
    def increment_view_count(self):
        """Increment view count and update last accessed time"""
        from django.utils import timezone
        self.view_count += 1
        self.last_accessed = timezone.now()
        self.save(update_fields=['view_count', 'last_accessed'])


class DashboardWidget(BaseModel):
    """
    Individual dashboard widget model
    """
    WIDGET_TYPES = [
        ('chart', _('Chart Widget')),
        ('table', _('Table Widget')),
        ('metric', _('Metric Widget')),
        ('gauge', _('Gauge Widget')),
        ('map', _('Map Widget')),
        ('calendar', _('Calendar Widget')),
        ('text', _('Text Widget')),
        ('image', _('Image Widget')),
        ('iframe', _('IFrame Widget')),
        ('custom', _('Custom Widget')),
    ]
    
    CHART_TYPES = [
        ('line', _('Line Chart')),
        ('bar', _('Bar Chart')),
        ('pie', _('Pie Chart')),
        ('doughnut', _('Doughnut Chart')),
        ('area', _('Area Chart')),
        ('scatter', _('Scatter Plot')),
        ('bubble', _('Bubble Chart')),
        ('radar', _('Radar Chart')),
        ('polar', _('Polar Chart')),
        ('heatmap', _('Heatmap')),
    ]
    
    dashboard = models.ForeignKey(
        Dashboard,
        on_delete=models.CASCADE,
        related_name='dashboard_widgets',
        verbose_name=_('Dashboard')
    )
    
    name = models.CharField(
        max_length=200,
        verbose_name=_('Widget Name')
    )
    
    widget_type = models.CharField(
        max_length=20,
        choices=WIDGET_TYPES,
        verbose_name=_('Widget Type')
    )
    
    chart_type = models.CharField(
        max_length=20,
        choices=CHART_TYPES,
        blank=True,
        null=True,
        verbose_name=_('Chart Type')
    )
    
    # Widget position and size
    position = models.JSONField(
        default=dict,
        verbose_name=_('Widget Position'),
        help_text=_('Grid position: {x, y, width, height}')
    )
    
    # Data source configuration
    data_source = models.JSONField(
        default=dict,
        verbose_name=_('Data Source Configuration'),
        help_text=_('Configuration for data retrieval')
    )
    
    # Widget styling and configuration
    config = models.JSONField(
        default=dict,
        verbose_name=_('Widget Configuration'),
        help_text=_('Widget-specific configuration options')
    )
    
    # Refresh settings
    refresh_interval = models.PositiveIntegerField(
        default=300,  # 5 minutes
        verbose_name=_('Refresh Interval (seconds)')
    )
    
    auto_refresh = models.BooleanField(
        default=True,
        verbose_name=_('Auto Refresh')
    )
    
    # Widget state
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    
    order = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Display Order')
    )
    
    class Meta:
        verbose_name = _('Dashboard Widget')
        verbose_name_plural = _('Dashboard Widgets')
        ordering = ['dashboard', 'order', 'name']
    
    def __str__(self):
        return f"{self.dashboard.name} - {self.name}"


class DashboardTheme(BaseModel):
    """
    Dashboard theme configuration
    """
    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_('Theme Name')
    )
    
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )
    
    # Theme configuration
    colors = models.JSONField(
        default=dict,
        verbose_name=_('Color Scheme'),
        help_text=_('Primary, secondary, accent colors, etc.')
    )
    
    fonts = models.JSONField(
        default=dict,
        verbose_name=_('Font Configuration'),
        help_text=_('Font families, sizes, weights')
    )
    
    layout = models.JSONField(
        default=dict,
        verbose_name=_('Layout Settings'),
        help_text=_('Spacing, borders, shadows, etc.')
    )
    
    chart_styles = models.JSONField(
        default=dict,
        verbose_name=_('Chart Styles'),
        help_text=_('Default chart colors and styling')
    )
    
    is_default = models.BooleanField(
        default=False,
        verbose_name=_('Is Default Theme')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_themes',
        verbose_name=_('Created By')
    )
    
    class Meta:
        verbose_name = _('Dashboard Theme')
        verbose_name_plural = _('Dashboard Themes')
        ordering = ['name']
    
    def __str__(self):
        return self.name


class Analytics(BaseModel):
    """
    Analytics data model for storing computed metrics
    """
    METRIC_TYPES = (
        ('enrollment', _('Enrollment')),
        ('attendance', _('Attendance')),
        ('financial', _('Financial')),
        ('academic', _('Academic Performance')),
        ('hr', _('Human Resources')),
    )

    metric_type = models.CharField(
        max_length=20,
        choices=METRIC_TYPES,
        verbose_name=_('Metric Type')
    )

    metric_name = models.CharField(
        max_length=100,
        verbose_name=_('Metric Name')
    )

    value = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Value')
    )

    date = models.DateField(
        verbose_name=_('Date')
    )

    metadata = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Metadata')
    )

    class Meta:
        verbose_name = _('Analytics')
        verbose_name_plural = _('Analytics')
        unique_together = ['metric_type', 'metric_name', 'date']
        ordering = ['-date', 'metric_type']

    def __str__(self):
        return f"{self.metric_name} - {self.date} ({self.value})"
