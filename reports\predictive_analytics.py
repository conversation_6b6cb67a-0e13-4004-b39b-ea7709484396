"""
Predictive Analytics Services
Handles machine learning models, trend analysis, and forecasting
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from django.db import connection
from django.utils import timezone
from django.core.cache import cache
from django.db.models import Count, Sum, Avg, Max, Min, Q
from django.apps import apps
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.svm import SVR
from sklearn.neural_network import MLPRegressor
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
from scipy import stats
from scipy.stats import pearsonr
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class PredictiveAnalyticsService:
    """
    Advanced service for predictive analytics and machine learning models
    """
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.cache_timeout = 3600  # 1 hour
        self.model_types = {
            'linear': LinearRegression(),
            'ridge': Ridge(alpha=1.0),
            'lasso': Lasso(alpha=1.0),
            'random_forest': RandomForestRegressor(n_estimators=100, random_state=42),
            'gradient_boost': GradientBoostingRegressor(n_estimators=100, random_state=42),
            'svr': SVR(kernel='rbf', C=1.0, gamma='scale'),
            'neural_network': MLPRegressor(hidden_layer_sizes=(100, 50), max_iter=500, random_state=42)
        }
        self.trend_analysis_cache = {}
    
    def predict_student_enrollment(self, months_ahead=12):
        """
        Predict student enrollment for the next N months
        
        Args:
            months_ahead (int): Number of months to predict
            
        Returns:
            dict: Prediction results with confidence intervals
        """
        try:
            # Get historical enrollment data
            historical_data = self._get_enrollment_history()
            
            if len(historical_data) < 12:  # Need at least 12 months of data
                return {
                    'success': False,
                    'error': 'Insufficient historical data (need at least 12 months)'
                }
            
            # Prepare data for modeling
            X, y = self._prepare_enrollment_data(historical_data)
            
            # Train model
            model = self._train_enrollment_model(X, y)
            
            # Generate predictions
            predictions = self._generate_enrollment_predictions(model, months_ahead)
            
            # Calculate confidence intervals
            confidence_intervals = self._calculate_confidence_intervals(
                model, X, y, predictions
            )
            
            return {
                'success': True,
                'predictions': predictions,
                'confidence_intervals': confidence_intervals,
                'model_metrics': self._get_model_metrics(model, X, y),
                'generated_at': timezone.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error predicting student enrollment: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def predict_revenue_trends(self, months_ahead=12):
        """
        Predict revenue trends for the next N months
        
        Args:
            months_ahead (int): Number of months to predict
            
        Returns:
            dict: Revenue prediction results
        """
        try:
            # Get historical revenue data
            historical_data = self._get_revenue_history()
            
            if len(historical_data) < 12:
                return {
                    'success': False,
                    'error': 'Insufficient historical revenue data'
                }
            
            # Prepare data
            X, y = self._prepare_revenue_data(historical_data)
            
            # Train model
            model = self._train_revenue_model(X, y)
            
            # Generate predictions
            predictions = self._generate_revenue_predictions(model, months_ahead)
            
            return {
                'success': True,
                'predictions': predictions,
                'model_metrics': self._get_model_metrics(model, X, y),
                'trends': self._analyze_revenue_trends(predictions),
                'generated_at': timezone.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error predicting revenue trends: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def predict_student_performance(self, student_id=None, subject_id=None):
        """
        Predict student academic performance
        
        Args:
            student_id (int): Specific student ID (optional)
            subject_id (int): Specific subject ID (optional)
            
        Returns:
            dict: Performance prediction results
        """
        try:
            # Get historical performance data
            historical_data = self._get_performance_history(student_id, subject_id)
            
            if len(historical_data) < 10:
                return {
                    'success': False,
                    'error': 'Insufficient performance data'
                }
            
            # Prepare data
            X, y = self._prepare_performance_data(historical_data)
            
            # Train model
            model = self._train_performance_model(X, y)
            
            # Generate predictions
            predictions = self._generate_performance_predictions(
                model, student_id, subject_id
            )
            
            # Identify at-risk students
            at_risk_students = self._identify_at_risk_students(predictions)
            
            return {
                'success': True,
                'predictions': predictions,
                'at_risk_students': at_risk_students,
                'model_metrics': self._get_model_metrics(model, X, y),
                'recommendations': self._generate_performance_recommendations(predictions),
                'generated_at': timezone.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error predicting student performance: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def predict_attendance_patterns(self, months_ahead=3):
        """
        Predict attendance patterns and identify trends
        
        Args:
            months_ahead (int): Number of months to predict
            
        Returns:
            dict: Attendance prediction results
        """
        try:
            # Get historical attendance data
            historical_data = self._get_attendance_history()
            
            if len(historical_data) < 30:  # Need at least 30 days of data
                return {
                    'success': False,
                    'error': 'Insufficient attendance data'
                }
            
            # Prepare data
            X, y = self._prepare_attendance_data(historical_data)
            
            # Train model
            model = self._train_attendance_model(X, y)
            
            # Generate predictions
            predictions = self._generate_attendance_predictions(model, months_ahead)
            
            # Analyze patterns
            patterns = self._analyze_attendance_patterns(historical_data, predictions)
            
            return {
                'success': True,
                'predictions': predictions,
                'patterns': patterns,
                'alerts': self._generate_attendance_alerts(predictions),
                'model_metrics': self._get_model_metrics(model, X, y),
                'generated_at': timezone.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error predicting attendance patterns: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def analyze_trends(self, data_type='all', time_period=12):
        """
        Advanced trend analysis using multiple statistical methods
        
        Args:
            data_type (str): Type of data to analyze ('enrollment', 'revenue', 'performance', 'attendance', 'all')
            time_period (int): Number of months to analyze
            
        Returns:
            dict: Comprehensive trend analysis results
        """
        try:
            cache_key = f"trend_analysis_{data_type}_{time_period}"
            cached_result = cache.get(cache_key)
            if cached_result:
                return cached_result
            
            trends = {}
            
            if data_type in ['enrollment', 'all']:
                trends['enrollment'] = self._analyze_enrollment_trends(time_period)
            
            if data_type in ['revenue', 'all']:
                trends['revenue'] = self._analyze_revenue_trends_advanced(time_period)
            
            if data_type in ['performance', 'all']:
                trends['performance'] = self._analyze_performance_trends(time_period)
            
            if data_type in ['attendance', 'all']:
                trends['attendance'] = self._analyze_attendance_trends_advanced(time_period)
            
            # Cross-correlation analysis
            if data_type == 'all':
                trends['correlations'] = self._analyze_cross_correlations(time_period)
                trends['anomalies'] = self._detect_anomalies(time_period)
                trends['seasonality'] = self._analyze_seasonality(time_period)
            
            result = {
                'success': True,
                'trends': trends,
                'analysis_period': time_period,
                'generated_at': timezone.now().isoformat()
            }
            
            cache.set(cache_key, result, self.cache_timeout)
            return result
            
        except Exception as e:
            logger.error(f"Error analyzing trends: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def forecast_with_confidence_intervals(self, data_type, periods_ahead=12, confidence_level=0.95):
        """
        Advanced forecasting with confidence intervals using ensemble methods
        
        Args:
            data_type (str): Type of data to forecast
            periods_ahead (int): Number of periods to forecast
            confidence_level (float): Confidence level for intervals
            
        Returns:
            dict: Forecast results with confidence intervals
        """
        try:
            if data_type == 'enrollment':
                return self._forecast_enrollment_advanced(periods_ahead, confidence_level)
            elif data_type == 'revenue':
                return self._forecast_revenue_advanced(periods_ahead, confidence_level)
            elif data_type == 'performance':
                return self._forecast_performance_advanced(periods_ahead, confidence_level)
            elif data_type == 'attendance':
                return self._forecast_attendance_advanced(periods_ahead, confidence_level)
            else:
                return {
                    'success': False,
                    'error': f'Unsupported data type: {data_type}'
                }
                
        except Exception as e:
            logger.error(f"Error forecasting {data_type}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def generate_predictive_alerts(self):
        """
        Generate advanced predictive alerts based on various models and thresholds
        
        Returns:
            dict: List of predictive alerts with severity levels
        """
        try:
            alerts = []
            
            # Enrollment alerts with trend analysis
            enrollment_prediction = self.predict_student_enrollment(3)
            if enrollment_prediction['success']:
                alerts.extend(self._generate_enrollment_alerts(enrollment_prediction))
                alerts.extend(self._generate_trend_alerts('enrollment'))
            
            # Revenue alerts with forecasting
            revenue_prediction = self.predict_revenue_trends(3)
            if revenue_prediction['success']:
                alerts.extend(self._generate_revenue_alerts(revenue_prediction))
                alerts.extend(self._generate_trend_alerts('revenue'))
            
            # Performance alerts with risk assessment
            performance_prediction = self.predict_student_performance()
            if performance_prediction['success']:
                alerts.extend(self._generate_performance_alerts(performance_prediction))
                alerts.extend(self._generate_risk_alerts())
            
            # Attendance alerts with pattern analysis
            attendance_prediction = self.predict_attendance_patterns(1)
            if attendance_prediction['success']:
                alerts.extend(attendance_prediction.get('alerts', []))
                alerts.extend(self._generate_pattern_alerts())
            
            # Anomaly detection alerts
            alerts.extend(self._generate_anomaly_alerts())
            
            # Correlation-based alerts
            alerts.extend(self._generate_correlation_alerts())
            
            # Sort alerts by priority and severity
            alerts.sort(key=lambda x: (x.get('priority', 0), x.get('severity_score', 0)), reverse=True)
            
            return {
                'success': True,
                'alerts': alerts,
                'total_alerts': len(alerts),
                'high_priority_count': len([a for a in alerts if a.get('priority', 0) >= 8]),
                'generated_at': timezone.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating predictive alerts: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    # Private helper methods
    
    def _get_enrollment_history(self):
        """Get historical enrollment data"""
        from students.models import Student
        
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT 
                    DATE_FORMAT(admission_date, '%Y-%m') as month,
                    COUNT(*) as enrollment_count,
                    AVG(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as retention_rate
                FROM students_student 
                WHERE admission_date >= DATE_SUB(NOW(), INTERVAL 24 MONTH)
                GROUP BY DATE_FORMAT(admission_date, '%Y-%m')
                ORDER BY month
            """)
            
            results = cursor.fetchall()
            
            return [
                {
                    'month': row[0],
                    'enrollment_count': row[1],
                    'retention_rate': float(row[2]) if row[2] else 0
                }
                for row in results
            ]
    
    def _get_revenue_history(self):
        """Get historical revenue data"""
        from finance.models import Payment
        
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT 
                    DATE_FORMAT(payment_date, '%Y-%m') as month,
                    SUM(amount) as total_revenue,
                    COUNT(*) as payment_count,
                    AVG(amount) as avg_payment
                FROM finance_payment 
                WHERE payment_date >= DATE_SUB(NOW(), INTERVAL 24 MONTH)
                GROUP BY DATE_FORMAT(payment_date, '%Y-%m')
                ORDER BY month
            """)
            
            results = cursor.fetchall()
            
            return [
                {
                    'month': row[0],
                    'total_revenue': float(row[1]) if row[1] else 0,
                    'payment_count': row[2],
                    'avg_payment': float(row[3]) if row[3] else 0
                }
                for row in results
            ]
    
    def _get_performance_history(self, student_id=None, subject_id=None):
        """Get historical performance data"""
        from academics.models import StudentGrade
        
        query = """
            SELECT 
                sg.student_id,
                sg.exam_id,
                sg.marks_obtained,
                sg.percentage,
                sg.graded_at,
                s.current_class_id,
                e.class_subject_id
            FROM academics_studentgrade sg
            JOIN students_student s ON sg.student_id = s.id
            JOIN academics_exam e ON sg.exam_id = e.id
            WHERE sg.graded_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        """
        
        params = []
        if student_id:
            query += " AND sg.student_id = %s"
            params.append(student_id)
        if subject_id:
            query += " AND e.class_subject_id = %s"
            params.append(subject_id)
        
        query += " ORDER BY sg.graded_at"
        
        with connection.cursor() as cursor:
            cursor.execute(query, params)
            results = cursor.fetchall()
            
            return [
                {
                    'student_id': row[0],
                    'exam_id': row[1],
                    'marks_obtained': row[2],
                    'percentage': float(row[3]) if row[3] else 0,
                    'graded_at': row[4],
                    'class_id': row[5],
                    'subject_id': row[6]
                }
                for row in results
            ]
    
    def _get_attendance_history(self):
        """Get historical attendance data"""
        from academics.models import StudentAttendance
        
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT 
                    date,
                    COUNT(*) as total_students,
                    SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,
                    SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,
                    SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count,
                    DAYOFWEEK(date) as day_of_week,
                    MONTH(date) as month,
                    YEAR(date) as year
                FROM academics_studentattendance 
                WHERE date >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
                GROUP BY date
                ORDER BY date
            """)
            
            results = cursor.fetchall()
            
            return [
                {
                    'date': row[0],
                    'total_students': row[1],
                    'present_count': row[2],
                    'absent_count': row[3],
                    'late_count': row[4],
                    'attendance_rate': (row[2] / row[1] * 100) if row[1] > 0 else 0,
                    'day_of_week': row[5],
                    'month': row[6],
                    'year': row[7]
                }
                for row in results
            ]
    
    def _prepare_enrollment_data(self, historical_data):
        """Prepare enrollment data for modeling"""
        df = pd.DataFrame(historical_data)
        df['month_num'] = pd.to_datetime(df['month']).dt.to_period('M').astype(int)
        
        # Create features
        X = df[['month_num', 'retention_rate']].values
        y = df['enrollment_count'].values
        
        return X, y
    
    def _prepare_revenue_data(self, historical_data):
        """Prepare revenue data for modeling"""
        df = pd.DataFrame(historical_data)
        df['month_num'] = pd.to_datetime(df['month']).dt.to_period('M').astype(int)
        
        # Create features
        X = df[['month_num', 'payment_count', 'avg_payment']].values
        y = df['total_revenue'].values
        
        return X, y
    
    def _prepare_performance_data(self, historical_data):
        """Prepare performance data for modeling"""
        df = pd.DataFrame(historical_data)
        
        # Create features
        features = ['student_id', 'class_id', 'subject_id', 'marks_obtained']
        X = df[features].values
        y = df['percentage'].values
        
        return X, y
    
    def _prepare_attendance_data(self, historical_data):
        """Prepare attendance data for modeling"""
        df = pd.DataFrame(historical_data)
        
        # Create features
        features = ['total_students', 'day_of_week', 'month', 'year']
        X = df[features].values
        y = df['attendance_rate'].values
        
        return X, y
    
    def _train_enrollment_model(self, X, y):
        """Train enrollment prediction model"""
        # Use Random Forest for better handling of non-linear relationships
        model = RandomForestRegressor(n_estimators=100, random_state=42)
        model.fit(X, y)
        
        # Cache the model
        self.models['enrollment'] = model
        
        return model
    
    def _train_revenue_model(self, X, y):
        """Train revenue prediction model"""
        # Scale features for linear regression
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        model = LinearRegression()
        model.fit(X_scaled, y)
        
        # Cache model and scaler
        self.models['revenue'] = model
        self.scalers['revenue'] = scaler
        
        return model
    
    def _train_performance_model(self, X, y):
        """Train performance prediction model"""
        model = RandomForestRegressor(n_estimators=100, random_state=42)
        model.fit(X, y)
        
        self.models['performance'] = model
        
        return model
    
    def _train_attendance_model(self, X, y):
        """Train attendance prediction model"""
        model = RandomForestRegressor(n_estimators=100, random_state=42)
        model.fit(X, y)
        
        self.models['attendance'] = model
        
        return model
    
    def _generate_enrollment_predictions(self, model, months_ahead):
        """Generate enrollment predictions"""
        predictions = []
        current_date = timezone.now().date()
        
        for i in range(months_ahead):
            future_date = current_date + timedelta(days=30 * (i + 1))
            month_num = future_date.year * 12 + future_date.month
            
            # Use average retention rate from historical data
            avg_retention = 0.85  # This would be calculated from historical data
            
            X_future = np.array([[month_num, avg_retention]])
            prediction = model.predict(X_future)[0]
            
            predictions.append({
                'month': future_date.strftime('%Y-%m'),
                'predicted_enrollment': max(0, int(prediction)),
                'confidence': 0.8  # This would be calculated properly
            })
        
        return predictions
    
    def _generate_revenue_predictions(self, model, months_ahead):
        """Generate revenue predictions"""
        predictions = []
        current_date = timezone.now().date()
        scaler = self.scalers.get('revenue')
        
        for i in range(months_ahead):
            future_date = current_date + timedelta(days=30 * (i + 1))
            month_num = future_date.year * 12 + future_date.month
            
            # Use historical averages for features
            avg_payment_count = 100  # This would be calculated from historical data
            avg_payment_amount = 500  # This would be calculated from historical data
            
            X_future = np.array([[month_num, avg_payment_count, avg_payment_amount]])
            if scaler:
                X_future = scaler.transform(X_future)
            
            prediction = model.predict(X_future)[0]
            
            predictions.append({
                'month': future_date.strftime('%Y-%m'),
                'predicted_revenue': max(0, prediction),
                'confidence': 0.75
            })
        
        return predictions
    
    def _generate_performance_predictions(self, model, student_id=None, subject_id=None):
        """Generate performance predictions"""
        # This would generate predictions for specific students/subjects
        # For now, return sample predictions
        return [
            {
                'student_id': 1,
                'subject_id': 1,
                'predicted_percentage': 85.5,
                'confidence': 0.8,
                'risk_level': 'low'
            }
        ]
    
    def _generate_attendance_predictions(self, model, months_ahead):
        """Generate attendance predictions"""
        predictions = []
        current_date = timezone.now().date()
        
        for i in range(30 * months_ahead):  # Daily predictions
            future_date = current_date + timedelta(days=i + 1)
            
            # Skip weekends
            if future_date.weekday() >= 5:
                continue
            
            total_students = 500  # This would be calculated from current enrollment
            day_of_week = future_date.weekday() + 1
            month = future_date.month
            year = future_date.year
            
            X_future = np.array([[total_students, day_of_week, month, year]])
            prediction = model.predict(X_future)[0]
            
            predictions.append({
                'date': future_date.isoformat(),
                'predicted_attendance_rate': max(0, min(100, prediction)),
                'predicted_present': int(total_students * prediction / 100),
                'predicted_absent': int(total_students * (100 - prediction) / 100)
            })
        
        return predictions
    
    def _calculate_confidence_intervals(self, model, X, y, predictions):
        """Calculate confidence intervals for predictions"""
        # This is a simplified implementation
        # In practice, you'd use more sophisticated methods
        y_pred = model.predict(X)
        mse = mean_squared_error(y, y_pred)
        std_error = np.sqrt(mse)
        
        confidence_intervals = []
        for pred in predictions:
            value = pred.get('predicted_enrollment', pred.get('predicted_revenue', 0))
            confidence_intervals.append({
                'lower_bound': max(0, value - 1.96 * std_error),
                'upper_bound': value + 1.96 * std_error
            })
        
        return confidence_intervals
    
    def _get_model_metrics(self, model, X, y):
        """Get model performance metrics"""
        y_pred = model.predict(X)
        
        return {
            'mae': mean_absolute_error(y, y_pred),
            'mse': mean_squared_error(y, y_pred),
            'r2_score': r2_score(y, y_pred),
            'accuracy': max(0, min(100, r2_score(y, y_pred) * 100))
        }
    
    def _analyze_revenue_trends(self, predictions):
        """Analyze revenue trends from predictions"""
        if len(predictions) < 2:
            return {'trend': 'insufficient_data'}
        
        values = [p['predicted_revenue'] for p in predictions]
        
        # Calculate trend
        if values[-1] > values[0]:
            trend = 'increasing'
        elif values[-1] < values[0]:
            trend = 'decreasing'
        else:
            trend = 'stable'
        
        # Calculate growth rate
        growth_rate = ((values[-1] - values[0]) / values[0] * 100) if values[0] > 0 else 0
        
        return {
            'trend': trend,
            'growth_rate': growth_rate,
            'volatility': np.std(values) if len(values) > 1 else 0
        }
    
    def _identify_at_risk_students(self, predictions):
        """Identify students at risk of poor performance"""
        at_risk = []
        
        for pred in predictions:
            if pred.get('predicted_percentage', 100) < 60:  # Below passing grade
                at_risk.append({
                    'student_id': pred['student_id'],
                    'subject_id': pred.get('subject_id'),
                    'predicted_percentage': pred['predicted_percentage'],
                    'risk_level': 'high' if pred['predicted_percentage'] < 50 else 'medium',
                    'recommended_actions': self._get_intervention_recommendations(pred)
                })
        
        return at_risk
    
    def _get_intervention_recommendations(self, prediction):
        """Get intervention recommendations for at-risk students"""
        recommendations = []
        
        percentage = prediction.get('predicted_percentage', 0)
        
        if percentage < 40:
            recommendations.extend([
                'Immediate one-on-one tutoring sessions',
                'Parent-teacher conference required',
                'Consider alternative assessment methods',
                'Implement individualized learning plan'
            ])
        elif percentage < 60:
            recommendations.extend([
                'Additional practice sessions',
                'Peer tutoring program',
                'Regular progress monitoring',
                'Study skills workshop'
            ])
        else:
            recommendations.extend([
                'Monitor progress closely',
                'Provide additional resources',
                'Encourage participation in study groups'
            ])
        
        return recommendations
    
    def _analyze_enrollment_trends(self, time_period):
        """Analyze enrollment trends over specified time period"""
        try:
            historical_data = self._get_enrollment_history()
            if len(historical_data) < 3:
                return {'error': 'Insufficient data for trend analysis'}
            
            df = pd.DataFrame(historical_data)
            df['month_date'] = pd.to_datetime(df['month'])
            df = df.sort_values('month_date')
            
            # Calculate trend metrics
            enrollment_values = df['enrollment_count'].values
            trend_slope, trend_intercept, r_value, p_value, std_err = stats.linregress(
                range(len(enrollment_values)), enrollment_values
            )
            
            # Seasonal analysis
            df['month_num'] = df['month_date'].dt.month
            seasonal_pattern = df.groupby('month_num')['enrollment_count'].mean().to_dict()
            
            # Growth rate calculation
            if len(enrollment_values) >= 2:
                recent_avg = np.mean(enrollment_values[-3:])
                older_avg = np.mean(enrollment_values[:3])
                growth_rate = ((recent_avg - older_avg) / older_avg * 100) if older_avg > 0 else 0
            else:
                growth_rate = 0
            
            return {
                'trend_direction': 'increasing' if trend_slope > 0 else 'decreasing' if trend_slope < 0 else 'stable',
                'trend_strength': abs(r_value),
                'growth_rate': growth_rate,
                'seasonal_pattern': seasonal_pattern,
                'volatility': np.std(enrollment_values),
                'prediction_accuracy': r_value ** 2,
                'statistical_significance': p_value < 0.05
            }
            
        except Exception as e:
            logger.error(f"Error analyzing enrollment trends: {e}")
            return {'error': str(e)}
    
    def _analyze_revenue_trends_advanced(self, time_period):
        """Advanced revenue trend analysis"""
        try:
            historical_data = self._get_revenue_history()
            if len(historical_data) < 3:
                return {'error': 'Insufficient data for trend analysis'}
            
            df = pd.DataFrame(historical_data)
            df['month_date'] = pd.to_datetime(df['month'])
            df = df.sort_values('month_date')
            
            # Revenue trend analysis
            revenue_values = df['total_revenue'].values
            trend_slope, trend_intercept, r_value, p_value, std_err = stats.linregress(
                range(len(revenue_values)), revenue_values
            )
            
            # Moving averages
            if len(revenue_values) >= 3:
                df['ma_3'] = df['total_revenue'].rolling(window=3).mean()
                df['ma_6'] = df['total_revenue'].rolling(window=min(6, len(revenue_values))).mean()
            
            # Revenue per payment analysis
            df['revenue_per_payment'] = df['total_revenue'] / df['payment_count']
            
            # Seasonal analysis
            df['month_num'] = df['month_date'].dt.month
            seasonal_revenue = df.groupby('month_num')['total_revenue'].mean().to_dict()
            
            # Volatility analysis
            volatility = np.std(revenue_values) / np.mean(revenue_values) if np.mean(revenue_values) > 0 else 0
            
            return {
                'trend_direction': 'increasing' if trend_slope > 0 else 'decreasing' if trend_slope < 0 else 'stable',
                'trend_strength': abs(r_value),
                'monthly_growth_rate': trend_slope,
                'seasonal_pattern': seasonal_revenue,
                'volatility_coefficient': volatility,
                'revenue_per_payment_trend': df['revenue_per_payment'].iloc[-1] - df['revenue_per_payment'].iloc[0] if len(df) > 1 else 0,
                'prediction_accuracy': r_value ** 2,
                'statistical_significance': p_value < 0.05
            }
            
        except Exception as e:
            logger.error(f"Error analyzing revenue trends: {e}")
            return {'error': str(e)}
    
    def _analyze_performance_trends(self, time_period):
        """Analyze academic performance trends"""
        try:
            historical_data = self._get_performance_history()
            if len(historical_data) < 10:
                return {'error': 'Insufficient data for trend analysis'}
            
            df = pd.DataFrame(historical_data)
            df['graded_date'] = pd.to_datetime(df['graded_at'])
            df = df.sort_values('graded_date')
            
            # Overall performance trend
            monthly_performance = df.groupby(df['graded_date'].dt.to_period('M')).agg({
                'percentage': ['mean', 'std', 'count'],
                'marks_obtained': 'mean'
            }).reset_index()
            
            # Subject-wise trends
            subject_trends = df.groupby('subject_id')['percentage'].agg(['mean', 'std', 'count']).to_dict('index')
            
            # Class-wise trends
            class_trends = df.groupby('class_id')['percentage'].agg(['mean', 'std', 'count']).to_dict('index')
            
            # Performance distribution
            performance_distribution = {
                'excellent': len(df[df['percentage'] >= 90]) / len(df) * 100,
                'good': len(df[(df['percentage'] >= 80) & (df['percentage'] < 90)]) / len(df) * 100,
                'average': len(df[(df['percentage'] >= 70) & (df['percentage'] < 80)]) / len(df) * 100,
                'below_average': len(df[(df['percentage'] >= 60) & (df['percentage'] < 70)]) / len(df) * 100,
                'poor': len(df[df['percentage'] < 60]) / len(df) * 100
            }
            
            # Trend analysis
            if len(monthly_performance) >= 3:
                performance_values = monthly_performance[('percentage', 'mean')].values
                trend_slope, _, r_value, p_value, _ = stats.linregress(
                    range(len(performance_values)), performance_values
                )
            else:
                trend_slope, r_value, p_value = 0, 0, 1
            
            return {
                'overall_trend': 'improving' if trend_slope > 0 else 'declining' if trend_slope < 0 else 'stable',
                'trend_strength': abs(r_value),
                'average_performance': df['percentage'].mean(),
                'performance_std': df['percentage'].std(),
                'subject_trends': subject_trends,
                'class_trends': class_trends,
                'performance_distribution': performance_distribution,
                'statistical_significance': p_value < 0.05
            }
            
        except Exception as e:
            logger.error(f"Error analyzing performance trends: {e}")
            return {'error': str(e)}
    
    def _analyze_attendance_trends_advanced(self, time_period):
        """Advanced attendance trend analysis"""
        try:
            historical_data = self._get_attendance_history()
            if len(historical_data) < 10:
                return {'error': 'Insufficient data for trend analysis'}
            
            df = pd.DataFrame(historical_data)
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date')
            
            # Overall attendance trend
            attendance_values = df['attendance_rate'].values
            trend_slope, _, r_value, p_value, _ = stats.linregress(
                range(len(attendance_values)), attendance_values
            )
            
            # Day of week analysis
            dow_analysis = df.groupby('day_of_week')['attendance_rate'].agg(['mean', 'std']).to_dict('index')
            
            # Monthly analysis
            monthly_analysis = df.groupby('month')['attendance_rate'].agg(['mean', 'std']).to_dict('index')
            
            # Seasonal patterns
            df['season'] = df['month'].map({
                12: 'winter', 1: 'winter', 2: 'winter',
                3: 'spring', 4: 'spring', 5: 'spring',
                6: 'summer', 7: 'summer', 8: 'summer',
                9: 'fall', 10: 'fall', 11: 'fall'
            })
            seasonal_analysis = df.groupby('season')['attendance_rate'].mean().to_dict()
            
            # Attendance categories
            attendance_distribution = {
                'excellent': len(df[df['attendance_rate'] >= 95]) / len(df) * 100,
                'good': len(df[(df['attendance_rate'] >= 90) & (df['attendance_rate'] < 95)]) / len(df) * 100,
                'average': len(df[(df['attendance_rate'] >= 85) & (df['attendance_rate'] < 90)]) / len(df) * 100,
                'poor': len(df[df['attendance_rate'] < 85]) / len(df) * 100
            }
            
            return {
                'overall_trend': 'improving' if trend_slope > 0 else 'declining' if trend_slope < 0 else 'stable',
                'trend_strength': abs(r_value),
                'average_attendance': df['attendance_rate'].mean(),
                'attendance_volatility': df['attendance_rate'].std(),
                'day_of_week_patterns': dow_analysis,
                'monthly_patterns': monthly_analysis,
                'seasonal_patterns': seasonal_analysis,
                'attendance_distribution': attendance_distribution,
                'statistical_significance': p_value < 0.05
            }
            
        except Exception as e:
            logger.error(f"Error analyzing attendance trends: {e}")
            return {'error': str(e)}
    
    def _analyze_cross_correlations(self, time_period):
        """Analyze correlations between different metrics"""
        try:
            # Get data for correlation analysis
            enrollment_data = self._get_enrollment_history()
            revenue_data = self._get_revenue_history()
            attendance_data = self._get_attendance_history()
            
            correlations = {}
            
            # Enrollment-Revenue correlation
            if len(enrollment_data) >= 3 and len(revenue_data) >= 3:
                enrollment_df = pd.DataFrame(enrollment_data)
                revenue_df = pd.DataFrame(revenue_data)
                
                # Merge on month
                merged = pd.merge(enrollment_df, revenue_df, on='month', how='inner')
                if len(merged) >= 3:
                    corr_coef, p_value = pearsonr(merged['enrollment_count'], merged['total_revenue'])
                    correlations['enrollment_revenue'] = {
                        'correlation': corr_coef,
                        'significance': p_value < 0.05,
                        'strength': 'strong' if abs(corr_coef) > 0.7 else 'moderate' if abs(corr_coef) > 0.3 else 'weak'
                    }
            
            # Add more correlations as needed
            return correlations
            
        except Exception as e:
            logger.error(f"Error analyzing cross-correlations: {e}")
            return {'error': str(e)}
    
    def _detect_anomalies(self, time_period):
        """Detect anomalies in various metrics"""
        try:
            anomalies = []
            
            # Enrollment anomalies
            enrollment_data = self._get_enrollment_history()
            if len(enrollment_data) >= 5:
                df = pd.DataFrame(enrollment_data)
                enrollment_values = df['enrollment_count'].values
                
                # Use IQR method for anomaly detection
                Q1 = np.percentile(enrollment_values, 25)
                Q3 = np.percentile(enrollment_values, 75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                for i, value in enumerate(enrollment_values):
                    if value < lower_bound or value > upper_bound:
                        anomalies.append({
                            'type': 'enrollment_anomaly',
                            'month': df.iloc[i]['month'],
                            'value': value,
                            'expected_range': f"{lower_bound:.0f} - {upper_bound:.0f}",
                            'severity': 'high' if abs(value - np.mean(enrollment_values)) > 2 * np.std(enrollment_values) else 'medium'
                        })
            
            # Revenue anomalies
            revenue_data = self._get_revenue_history()
            if len(revenue_data) >= 5:
                df = pd.DataFrame(revenue_data)
                revenue_values = df['total_revenue'].values
                
                Q1 = np.percentile(revenue_values, 25)
                Q3 = np.percentile(revenue_values, 75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                for i, value in enumerate(revenue_values):
                    if value < lower_bound or value > upper_bound:
                        anomalies.append({
                            'type': 'revenue_anomaly',
                            'month': df.iloc[i]['month'],
                            'value': value,
                            'expected_range': f"{lower_bound:.0f} - {upper_bound:.0f}",
                            'severity': 'high' if abs(value - np.mean(revenue_values)) > 2 * np.std(revenue_values) else 'medium'
                        })
            
            return anomalies
            
        except Exception as e:
            logger.error(f"Error detecting anomalies: {e}")
            return []
    
    def _analyze_seasonality(self, time_period):
        """Analyze seasonal patterns in data"""
        try:
            seasonality = {}
            
            # Enrollment seasonality
            enrollment_data = self._get_enrollment_history()
            if len(enrollment_data) >= 12:
                df = pd.DataFrame(enrollment_data)
                df['month_date'] = pd.to_datetime(df['month'])
                df['month_num'] = df['month_date'].dt.month
                
                monthly_avg = df.groupby('month_num')['enrollment_count'].mean()
                overall_avg = df['enrollment_count'].mean()
                
                seasonality['enrollment'] = {
                    'peak_month': monthly_avg.idxmax(),
                    'low_month': monthly_avg.idxmin(),
                    'seasonal_variation': (monthly_avg.max() - monthly_avg.min()) / overall_avg * 100,
                    'monthly_patterns': monthly_avg.to_dict()
                }
            
            # Revenue seasonality
            revenue_data = self._get_revenue_history()
            if len(revenue_data) >= 12:
                df = pd.DataFrame(revenue_data)
                df['month_date'] = pd.to_datetime(df['month'])
                df['month_num'] = df['month_date'].dt.month
                
                monthly_avg = df.groupby('month_num')['total_revenue'].mean()
                overall_avg = df['total_revenue'].mean()
                
                seasonality['revenue'] = {
                    'peak_month': monthly_avg.idxmax(),
                    'low_month': monthly_avg.idxmin(),
                    'seasonal_variation': (monthly_avg.max() - monthly_avg.min()) / overall_avg * 100,
                    'monthly_patterns': monthly_avg.to_dict()
                }
            
            return seasonality
            
        except Exception as e:
            logger.error(f"Error analyzing seasonality: {e}")
            return {'error': str(e)}
    
    def _forecast_enrollment_advanced(self, periods_ahead, confidence_level):
        """Advanced enrollment forecasting with ensemble methods"""
        try:
            historical_data = self._get_enrollment_history()
            if len(historical_data) < 12:
                return {'success': False, 'error': 'Insufficient data for advanced forecasting'}
            
            X, y = self._prepare_enrollment_data(historical_data)
            
            # Ensemble of models
            models = {
                'linear': LinearRegression(),
                'ridge': Ridge(alpha=1.0),
                'random_forest': RandomForestRegressor(n_estimators=100, random_state=42),
                'gradient_boost': GradientBoostingRegressor(n_estimators=100, random_state=42)
            }
            
            # Train models and get predictions
            ensemble_predictions = []
            model_weights = {}
            
            for name, model in models.items():
                # Cross-validation to get model weight
                cv_scores = cross_val_score(model, X, y, cv=min(5, len(X)//2), scoring='r2')
                model_weights[name] = max(0, np.mean(cv_scores))
                
                # Train on full data
                model.fit(X, y)
                
                # Generate predictions
                predictions = self._generate_enrollment_predictions(model, periods_ahead)
                ensemble_predictions.append(predictions)
            
            # Weighted ensemble
            total_weight = sum(model_weights.values())
            if total_weight > 0:
                model_weights = {k: v/total_weight for k, v in model_weights.items()}
            else:
                model_weights = {k: 1/len(models) for k in models.keys()}
            
            # Combine predictions
            final_predictions = []
            for i in range(periods_ahead):
                weighted_prediction = sum(
                    model_weights[name] * ensemble_predictions[j][i]['predicted_enrollment']
                    for j, name in enumerate(models.keys())
                )
                
                # Calculate confidence intervals
                predictions_for_period = [pred[i]['predicted_enrollment'] for pred in ensemble_predictions]
                std_dev = np.std(predictions_for_period)
                z_score = stats.norm.ppf((1 + confidence_level) / 2)
                margin_error = z_score * std_dev
                
                final_predictions.append({
                    'month': ensemble_predictions[0][i]['month'],
                    'predicted_enrollment': int(weighted_prediction),
                    'lower_bound': max(0, int(weighted_prediction - margin_error)),
                    'upper_bound': int(weighted_prediction + margin_error),
                    'confidence_level': confidence_level,
                    'model_agreement': 1 - (std_dev / weighted_prediction) if weighted_prediction > 0 else 0
                })
            
            return {
                'success': True,
                'predictions': final_predictions,
                'model_weights': model_weights,
                'ensemble_method': 'weighted_average',
                'confidence_level': confidence_level
            }
            
        except Exception as e:
            logger.error(f"Error in advanced enrollment forecasting: {e}")
            return {'success': False, 'error': str(e)}
    
    def _forecast_revenue_advanced(self, periods_ahead, confidence_level):
        """Advanced revenue forecasting with ensemble methods"""
        try:
            historical_data = self._get_revenue_history()
            if len(historical_data) < 12:
                return {'success': False, 'error': 'Insufficient data for advanced forecasting'}
            
            X, y = self._prepare_revenue_data(historical_data)
            
            # Scale features
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            
            # Ensemble of models
            models = {
                'linear': LinearRegression(),
                'ridge': Ridge(alpha=1.0),
                'lasso': Lasso(alpha=1.0),
                'random_forest': RandomForestRegressor(n_estimators=100, random_state=42),
                'svr': SVR(kernel='rbf', C=1.0, gamma='scale')
            }
            
            # Train models and get predictions
            ensemble_predictions = []
            model_weights = {}
            
            for name, model in models.items():
                # Use scaled data for linear models
                X_train = X_scaled if name in ['linear', 'ridge', 'lasso', 'svr'] else X
                
                # Cross-validation
                cv_scores = cross_val_score(model, X_train, y, cv=min(5, len(X)//2), scoring='r2')
                model_weights[name] = max(0, np.mean(cv_scores))
                
                # Train on full data
                model.fit(X_train, y)
                
                # Store model and scaler
                self.models[f'revenue_{name}'] = model
                if name in ['linear', 'ridge', 'lasso', 'svr']:
                    self.scalers[f'revenue_{name}'] = scaler
                
                # Generate predictions
                predictions = self._generate_revenue_predictions_advanced(model, periods_ahead, name, scaler if name in ['linear', 'ridge', 'lasso', 'svr'] else None)
                ensemble_predictions.append(predictions)
            
            # Weighted ensemble
            total_weight = sum(model_weights.values())
            if total_weight > 0:
                model_weights = {k: v/total_weight for k, v in model_weights.items()}
            else:
                model_weights = {k: 1/len(models) for k in models.keys()}
            
            # Combine predictions
            final_predictions = []
            for i in range(periods_ahead):
                weighted_prediction = sum(
                    model_weights[name] * ensemble_predictions[j][i]['predicted_revenue']
                    for j, name in enumerate(models.keys())
                )
                
                # Calculate confidence intervals
                predictions_for_period = [pred[i]['predicted_revenue'] for pred in ensemble_predictions]
                std_dev = np.std(predictions_for_period)
                z_score = stats.norm.ppf((1 + confidence_level) / 2)
                margin_error = z_score * std_dev
                
                final_predictions.append({
                    'month': ensemble_predictions[0][i]['month'],
                    'predicted_revenue': weighted_prediction,
                    'lower_bound': max(0, weighted_prediction - margin_error),
                    'upper_bound': weighted_prediction + margin_error,
                    'confidence_level': confidence_level,
                    'model_agreement': 1 - (std_dev / weighted_prediction) if weighted_prediction > 0 else 0
                })
            
            return {
                'success': True,
                'predictions': final_predictions,
                'model_weights': model_weights,
                'ensemble_method': 'weighted_average',
                'confidence_level': confidence_level
            }
            
        except Exception as e:
            logger.error(f"Error in advanced revenue forecasting: {e}")
            return {'success': False, 'error': str(e)}
    
    def _generate_revenue_predictions_advanced(self, model, months_ahead, model_name, scaler=None):
        """Generate advanced revenue predictions"""
        predictions = []
        current_date = timezone.now().date()
        
        for i in range(months_ahead):
            future_date = current_date + timedelta(days=30 * (i + 1))
            month_num = future_date.year * 12 + future_date.month
            
            # Use historical averages for features
            avg_payment_count = 100  # This would be calculated from historical data
            avg_payment_amount = 500  # This would be calculated from historical data
            
            X_future = np.array([[month_num, avg_payment_count, avg_payment_amount]])
            if scaler:
                X_future = scaler.transform(X_future)
            
            prediction = model.predict(X_future)[0]
            
            predictions.append({
                'month': future_date.strftime('%Y-%m'),
                'predicted_revenue': max(0, prediction),
                'model': model_name
            })
        
        return predictions
    
    def _forecast_performance_advanced(self, periods_ahead, confidence_level):
        """Advanced performance forecasting"""
        try:
            historical_data = self._get_performance_history()
            if len(historical_data) < 20:
                return {'success': False, 'error': 'Insufficient data for performance forecasting'}
            
            # Group by month and calculate average performance
            df = pd.DataFrame(historical_data)
            df['graded_date'] = pd.to_datetime(df['graded_at'])
            monthly_performance = df.groupby(df['graded_date'].dt.to_period('M')).agg({
                'percentage': 'mean',
                'marks_obtained': 'mean'
            }).reset_index()
            
            if len(monthly_performance) < 6:
                return {'success': False, 'error': 'Insufficient monthly data for forecasting'}
            
            # Prepare time series data
            monthly_performance['month_num'] = range(len(monthly_performance))
            X = monthly_performance[['month_num']].values
            y = monthly_performance['percentage'].values
            
            # Train model
            model = RandomForestRegressor(n_estimators=100, random_state=42)
            model.fit(X, y)
            
            # Generate predictions
            predictions = []
            last_month_num = monthly_performance['month_num'].iloc[-1]
            
            for i in range(periods_ahead):
                future_month_num = last_month_num + i + 1
                X_future = np.array([[future_month_num]])
                prediction = model.predict(X_future)[0]
                
                # Calculate confidence interval (simplified)
                std_dev = np.std(y)
                z_score = stats.norm.ppf((1 + confidence_level) / 2)
                margin_error = z_score * std_dev
                
                future_date = timezone.now().date() + timedelta(days=30 * (i + 1))
                
                predictions.append({
                    'month': future_date.strftime('%Y-%m'),
                    'predicted_performance': max(0, min(100, prediction)),
                    'lower_bound': max(0, prediction - margin_error),
                    'upper_bound': min(100, prediction + margin_error),
                    'confidence_level': confidence_level
                })
            
            return {
                'success': True,
                'predictions': predictions,
                'confidence_level': confidence_level
            }
            
        except Exception as e:
            logger.error(f"Error in advanced performance forecasting: {e}")
            return {'success': False, 'error': str(e)}
    
    def _forecast_attendance_advanced(self, periods_ahead, confidence_level):
        """Advanced attendance forecasting"""
        try:
            historical_data = self._get_attendance_history()
            if len(historical_data) < 30:
                return {'success': False, 'error': 'Insufficient data for attendance forecasting'}
            
            df = pd.DataFrame(historical_data)
            df['date'] = pd.to_datetime(df['date'])
            
            # Group by week for forecasting
            weekly_attendance = df.groupby(df['date'].dt.to_period('W')).agg({
                'attendance_rate': 'mean',
                'total_students': 'mean'
            }).reset_index()
            
            if len(weekly_attendance) < 8:
                return {'success': False, 'error': 'Insufficient weekly data for forecasting'}
            
            # Prepare time series data
            weekly_attendance['week_num'] = range(len(weekly_attendance))
            X = weekly_attendance[['week_num']].values
            y = weekly_attendance['attendance_rate'].values
            
            # Train model
            model = RandomForestRegressor(n_estimators=100, random_state=42)
            model.fit(X, y)
            
            # Generate predictions
            predictions = []
            last_week_num = weekly_attendance['week_num'].iloc[-1]
            
            for i in range(periods_ahead * 4):  # Convert months to weeks
                future_week_num = last_week_num + i + 1
                X_future = np.array([[future_week_num]])
                prediction = model.predict(X_future)[0]
                
                # Calculate confidence interval
                std_dev = np.std(y)
                z_score = stats.norm.ppf((1 + confidence_level) / 2)
                margin_error = z_score * std_dev
                
                future_date = timezone.now().date() + timedelta(weeks=i + 1)
                
                predictions.append({
                    'week': future_date.strftime('%Y-W%U'),
                    'predicted_attendance_rate': max(0, min(100, prediction)),
                    'lower_bound': max(0, prediction - margin_error),
                    'upper_bound': min(100, prediction + margin_error),
                    'confidence_level': confidence_level
                })
            
            return {
                'success': True,
                'predictions': predictions,
                'confidence_level': confidence_level
            }
            
        except Exception as e:
            logger.error(f"Error in advanced attendance forecasting: {e}")
            return {'success': False, 'error': str(e)}
    
    def _generate_trend_alerts(self, data_type):
        """Generate alerts based on trend analysis"""
        alerts = []
        
        try:
            if data_type == 'enrollment':
                trends = self._analyze_enrollment_trends(12)
                if trends.get('trend_direction') == 'decreasing' and trends.get('statistical_significance'):
                    alerts.append({
                        'type': 'enrollment_trend_alert',
                        'severity': 'high',
                        'message': f"Significant declining enrollment trend detected: {trends.get('growth_rate', 0):.1f}% change",
                        'priority': 9,
                        'trend_strength': trends.get('trend_strength', 0)
                    })
            
            elif data_type == 'revenue':
                trends = self._analyze_revenue_trends_advanced(12)
                if trends.get('trend_direction') == 'decreasing' and trends.get('statistical_significance'):
                    alerts.append({
                        'type': 'revenue_trend_alert',
                        'severity': 'high',
                        'message': f"Significant declining revenue trend detected",
                        'priority': 8,
                        'volatility': trends.get('volatility_coefficient', 0)
                    })
            
        except Exception as e:
            logger.error(f"Error generating trend alerts for {data_type}: {e}")
        
        return alerts
    
    def _generate_risk_alerts(self):
        """Generate risk-based alerts"""
        alerts = []
        
        try:
            # Performance risk alerts
            performance_prediction = self.predict_student_performance()
            if performance_prediction['success']:
                at_risk_count = len(performance_prediction.get('at_risk_students', []))
                if at_risk_count > 10:  # Threshold for concern
                    alerts.append({
                        'type': 'performance_risk_alert',
                        'severity': 'high' if at_risk_count > 20 else 'medium',
                        'message': f"High number of at-risk students detected: {at_risk_count}",
                        'priority': 8,
                        'student_count': at_risk_count
                    })
            
        except Exception as e:
            logger.error(f"Error generating risk alerts: {e}")
        
        return alerts
    
    def _generate_pattern_alerts(self):
        """Generate pattern-based alerts"""
        alerts = []
        
        try:
            # Attendance pattern alerts
            attendance_data = self._get_attendance_history()
            if len(attendance_data) >= 30:
                df = pd.DataFrame(attendance_data)
                recent_avg = df.tail(7)['attendance_rate'].mean()
                overall_avg = df['attendance_rate'].mean()
                
                if recent_avg < overall_avg - 5:  # 5% drop threshold
                    alerts.append({
                        'type': 'attendance_pattern_alert',
                        'severity': 'medium',
                        'message': f"Recent attendance drop detected: {recent_avg:.1f}% vs {overall_avg:.1f}% average",
                        'priority': 6,
                        'recent_rate': recent_avg,
                        'overall_rate': overall_avg
                    })
            
        except Exception as e:
            logger.error(f"Error generating pattern alerts: {e}")
        
        return alerts
    
    def _generate_anomaly_alerts(self):
        """Generate anomaly-based alerts"""
        alerts = []
        
        try:
            anomalies = self._detect_anomalies(12)
            for anomaly in anomalies:
                if anomaly.get('severity') == 'high':
                    alerts.append({
                        'type': 'anomaly_alert',
                        'severity': 'high',
                        'message': f"Anomaly detected in {anomaly['type']}: {anomaly['value']} (expected: {anomaly['expected_range']})",
                        'priority': 7,
                        'anomaly_details': anomaly
                    })
        
        except Exception as e:
            logger.error(f"Error generating anomaly alerts: {e}")
        
        return alerts
    
    def _generate_correlation_alerts(self):
        """Generate correlation-based alerts"""
        alerts = []
        
        try:
            correlations = self._analyze_cross_correlations(12)
            
            # Check for broken correlations
            for correlation_type, data in correlations.items():
                if data.get('strength') == 'strong' and not data.get('significance'):
                    alerts.append({
                        'type': 'correlation_alert',
                        'severity': 'medium',
                        'message': f"Previously strong correlation in {correlation_type} has weakened",
                        'priority': 5,
                        'correlation_data': data
                    })
        
        except Exception as e:
            logger.error(f"Error generating correlation alerts: {e}")
        
        return alerts
    
    def _get_intervention_recommendations(self, prediction):
        """Get intervention recommendations for at-risk students"""
        recommendations = []
        
        if prediction['predicted_percentage'] < 50:
            recommendations.extend([
                'Schedule immediate parent-teacher conference',
                'Provide additional tutoring sessions',
                'Review learning materials and teaching methods'
            ])
        elif prediction['predicted_percentage'] < 60:
            recommendations.extend([
                'Monitor progress closely',
                'Provide extra practice materials',
                'Consider peer tutoring'
            ])
        
        return recommendations
    
    def _generate_performance_recommendations(self, predictions):
        """Generate general performance recommendations"""
        return [
            'Implement early intervention programs for at-risk students',
            'Provide additional support for struggling subjects',
            'Monitor attendance patterns and their impact on performance',
            'Consider personalized learning approaches'
        ]
    
    def _analyze_attendance_patterns(self, historical_data, predictions):
        """Analyze attendance patterns"""
        df = pd.DataFrame(historical_data)
        
        patterns = {
            'seasonal_trends': self._identify_seasonal_trends(df),
            'day_of_week_patterns': self._identify_day_patterns(df),
            'monthly_patterns': self._identify_monthly_patterns(df),
            'declining_trends': self._identify_declining_trends(df)
        }
        
        return patterns
    
    def _identify_seasonal_trends(self, df):
        """Identify seasonal attendance trends"""
        monthly_avg = df.groupby('month')['attendance_rate'].mean()
        
        return {
            'highest_month': monthly_avg.idxmax(),
            'lowest_month': monthly_avg.idxmin(),
            'seasonal_variation': monthly_avg.max() - monthly_avg.min()
        }
    
    def _identify_day_patterns(self, df):
        """Identify day-of-week attendance patterns"""
        daily_avg = df.groupby('day_of_week')['attendance_rate'].mean()
        
        return {
            'best_day': daily_avg.idxmax(),
            'worst_day': daily_avg.idxmin(),
            'weekly_variation': daily_avg.max() - daily_avg.min()
        }
    
    def _identify_monthly_patterns(self, df):
        """Identify monthly attendance patterns"""
        return df.groupby('month')['attendance_rate'].mean().to_dict()
    
    def _identify_declining_trends(self, df):
        """Identify declining attendance trends"""
        # Simple trend analysis
        df_sorted = df.sort_values('date')
        recent_avg = df_sorted.tail(30)['attendance_rate'].mean()
        older_avg = df_sorted.head(30)['attendance_rate'].mean()
        
        return {
            'is_declining': recent_avg < older_avg,
            'decline_rate': ((recent_avg - older_avg) / older_avg * 100) if older_avg > 0 else 0
        }
    
    def _generate_attendance_alerts(self, predictions):
        """Generate attendance-based alerts"""
        alerts = []
        
        for pred in predictions:
            if pred['predicted_attendance_rate'] < 80:  # Below acceptable threshold
                alerts.append({
                    'type': 'low_attendance_prediction',
                    'date': pred['date'],
                    'predicted_rate': pred['predicted_attendance_rate'],
                    'severity': 'high' if pred['predicted_attendance_rate'] < 70 else 'medium',
                    'message': f"Low attendance predicted for {pred['date']}: {pred['predicted_attendance_rate']:.1f}%"
                })
        
        return alerts
    
    def _generate_enrollment_alerts(self, prediction_result):
        """Generate enrollment-based alerts"""
        alerts = []
        
        if not prediction_result.get('success'):
            return alerts
        
        predictions = prediction_result.get('predictions', [])
        
        for pred in predictions:
            # Check for significant enrollment drops
            if 'predicted_enrollment' in pred:
                if pred['predicted_enrollment'] < 100:  # Threshold for concern
                    alerts.append({
                        'type': 'low_enrollment_prediction',
                        'month': pred['month'],
                        'predicted_enrollment': pred['predicted_enrollment'],
                        'severity': 'high' if pred['predicted_enrollment'] < 50 else 'medium',
                        'priority': 8,
                        'message': f"Low enrollment predicted for {pred['month']}: {pred['predicted_enrollment']} students"
                    })
        
        return alerts
    
    def _generate_revenue_alerts(self, prediction_result):
        """Generate revenue-based alerts"""
        alerts = []
        
        if not prediction_result.get('success'):
            return alerts
        
        predictions = prediction_result.get('predictions', [])
        trends = prediction_result.get('trends', {})
        
        # Check for declining revenue trends
        if trends.get('trend') == 'decreasing':
            growth_rate = trends.get('growth_rate', 0)
            if growth_rate < -10:  # More than 10% decline
                alerts.append({
                    'type': 'revenue_decline_alert',
                    'severity': 'high',
                    'priority': 9,
                    'growth_rate': growth_rate,
                    'message': f"Significant revenue decline predicted: {growth_rate:.1f}%"
                })
        
        # Check for low revenue predictions
        for pred in predictions:
            if 'predicted_revenue' in pred:
                if pred['predicted_revenue'] < 10000:  # Threshold for concern
                    alerts.append({
                        'type': 'low_revenue_prediction',
                        'month': pred['month'],
                        'predicted_revenue': pred['predicted_revenue'],
                        'severity': 'medium',
                        'priority': 6,
                        'message': f"Low revenue predicted for {pred['month']}: ${pred['predicted_revenue']:,.2f}"
                    })
        
        return alerts
    
    def _generate_performance_alerts(self, prediction_result):
        """Generate performance-based alerts"""
        alerts = []
        
        if not prediction_result.get('success'):
            return alerts
        
        at_risk_students = prediction_result.get('at_risk_students', [])
        
        if len(at_risk_students) > 0:
            high_risk_count = len([s for s in at_risk_students if s.get('risk_level') == 'high'])
            
            alerts.append({
                'type': 'at_risk_students_alert',
                'severity': 'high' if high_risk_count > 5 else 'medium',
                'priority': 8,
                'at_risk_count': len(at_risk_students),
                'high_risk_count': high_risk_count,
                'message': f"{len(at_risk_students)} students at risk of poor performance ({high_risk_count} high risk)"
            })
        
        return alerts
    
    def create_analytics_dashboard_data(self, dashboard_type='comprehensive'):
        """Create comprehensive dashboard data for analytics"""
        try:
            dashboard_data = {
                'generated_at': timezone.now().isoformat(),
                'dashboard_type': dashboard_type
            }
            
            if dashboard_type in ['comprehensive', 'enrollment']:
                enrollment_prediction = self.predict_student_enrollment(6)
                dashboard_data['enrollment'] = {
                    'predictions': enrollment_prediction.get('predictions', []),
                    'trends': self._analyze_enrollment_trends(12),
                    'success': enrollment_prediction.get('success', False)
                }
            
            if dashboard_type in ['comprehensive', 'revenue']:
                revenue_prediction = self.predict_revenue_trends(6)
                dashboard_data['revenue'] = {
                    'predictions': revenue_prediction.get('predictions', []),
                    'trends': revenue_prediction.get('trends', {}),
                    'success': revenue_prediction.get('success', False)
                }
            
            if dashboard_type in ['comprehensive', 'performance']:
                performance_prediction = self.predict_student_performance()
                dashboard_data['performance'] = {
                    'at_risk_students': performance_prediction.get('at_risk_students', []),
                    'recommendations': performance_prediction.get('recommendations', []),
                    'success': performance_prediction.get('success', False)
                }
            
            if dashboard_type in ['comprehensive', 'attendance']:
                attendance_prediction = self.predict_attendance_patterns(3)
                dashboard_data['attendance'] = {
                    'predictions': attendance_prediction.get('predictions', []),
                    'patterns': attendance_prediction.get('patterns', {}),
                    'success': attendance_prediction.get('success', False)
                }
            
            if dashboard_type == 'comprehensive':
                # Add cross-correlations and anomalies
                dashboard_data['correlations'] = self._analyze_cross_correlations(12)
                dashboard_data['anomalies'] = self._detect_anomalies(12)
                dashboard_data['seasonality'] = self._analyze_seasonality(12)
                
                # Add summary statistics
                dashboard_data['summary'] = self._create_dashboard_summary(dashboard_data)
            
            return {
                'success': True,
                'data': dashboard_data
            }
            
        except Exception as e:
            logger.error(f"Error creating dashboard data: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _create_dashboard_summary(self, dashboard_data):
        """Create summary statistics for dashboard"""
        summary = {
            'total_predictions': 0,
            'high_priority_alerts': 0,
            'data_quality_score': 0,
            'model_accuracy_avg': 0
        }
        
        # Count predictions
        for section in ['enrollment', 'revenue', 'attendance']:
            if section in dashboard_data and dashboard_data[section].get('success'):
                predictions = dashboard_data[section].get('predictions', [])
                summary['total_predictions'] += len(predictions)
        
        # Calculate data quality score (simplified)
        successful_sections = sum(1 for section in ['enrollment', 'revenue', 'performance', 'attendance'] 
                                if dashboard_data.get(section, {}).get('success', False))
        summary['data_quality_score'] = (successful_sections / 4) * 100
        
        return summary


# Utility functions for external access
def get_predictive_analytics_service():
    """Get singleton instance of PredictiveAnalyticsService"""
    if not hasattr(get_predictive_analytics_service, '_instance'):
        get_predictive_analytics_service._instance = PredictiveAnalyticsService()
    return get_predictive_analytics_service._instance


def predict_enrollment(months_ahead=12):
    """Predict student enrollment"""
    service = get_predictive_analytics_service()
    return service.predict_student_enrollment(months_ahead)


def predict_revenue(months_ahead=12):
    """Predict revenue trends"""
    service = get_predictive_analytics_service()
    return service.predict_revenue_trends(months_ahead)


def predict_performance(student_id=None, subject_id=None):
    """Predict student performance"""
    service = get_predictive_analytics_service()
    return service.predict_student_performance(student_id, subject_id)


def predict_attendance(months_ahead=3):
    """Predict attendance patterns"""
    service = get_predictive_analytics_service()
    return service.predict_attendance_patterns(months_ahead)


def analyze_trends(data_type='all', time_period=12):
    """Analyze trends in data"""
    service = get_predictive_analytics_service()
    return service.analyze_trends(data_type, time_period)


def forecast_with_confidence(data_type, periods_ahead=12, confidence_level=0.95):
    """Advanced forecasting with confidence intervals"""
    service = get_predictive_analytics_service()
    return service.forecast_with_confidence_intervals(data_type, periods_ahead, confidence_level)


def get_predictive_alerts():
    """Get current predictive alerts"""
    service = get_predictive_analytics_service()
    return service.generate_predictive_alerts()


def create_dashboard_data(dashboard_type='comprehensive'):
    """Create dashboard data for frontend"""
    service = get_predictive_analytics_service()
    return service.create_analytics_dashboard_data(dashboard_type)
    
    def _generate_performance_alerts(self, prediction_result):
        """Generate performance-based alerts"""
        alerts = []
        at_risk_students = prediction_result.get('at_risk_students', [])
        
        if len(at_risk_students) > 0:
            alerts.append({
                'type': 'performance_alert',
                'severity': 'medium',
                'message': f"{len(at_risk_students)} students identified as at-risk for poor performance",
                'priority': 7,
                'student_count': len(at_risk_students)
            })
        
        return alerts    

    def create_analytics_dashboard_data(self, dashboard_type='comprehensive'):
        """
        Create comprehensive analytics dashboard data
        
        Args:
            dashboard_type (str): Type of dashboard ('comprehensive', 'enrollment', 'revenue', 'performance', 'attendance')
            
        Returns:
            dict: Dashboard data with charts, metrics, and insights
        """
        try:
            dashboard_data = {
                'dashboard_type': dashboard_type,
                'generated_at': timezone.now().isoformat(),
                'success': True
            }
            
            if dashboard_type in ['comprehensive', 'enrollment']:
                dashboard_data['enrollment'] = self._create_enrollment_dashboard_data()
            
            if dashboard_type in ['comprehensive', 'revenue']:
                dashboard_data['revenue'] = self._create_revenue_dashboard_data()
            
            if dashboard_type in ['comprehensive', 'performance']:
                dashboard_data['performance'] = self._create_performance_dashboard_data()
            
            if dashboard_type in ['comprehensive', 'attendance']:
                dashboard_data['attendance'] = self._create_attendance_dashboard_data()
            
            if dashboard_type == 'comprehensive':
                dashboard_data['correlations'] = self._analyze_cross_correlations(12)
                dashboard_data['anomalies'] = self._detect_anomalies(12)
                dashboard_data['seasonality'] = self._analyze_seasonality(12)
                dashboard_data['alerts'] = self.generate_predictive_alerts()
            
            return dashboard_data
            
        except Exception as e:
            logger.error(f"Error creating analytics dashboard data: {e}")
            return {
                'success': False,
                'error': str(e),
                'dashboard_type': dashboard_type
            }
    
    def _create_enrollment_dashboard_data(self):
        """Create enrollment-specific dashboard data"""
        try:
            historical_data = self._get_enrollment_history()
            predictions = self.predict_student_enrollment(6)
            trends = self._analyze_enrollment_trends(12)
            
            # Chart data
            chart_data = {
                'historical': [
                    {
                        'month': item['month'],
                        'enrollment': item['enrollment_count'],
                        'retention_rate': item['retention_rate']
                    }
                    for item in historical_data
                ],
                'predictions': predictions.get('predictions', []) if predictions['success'] else []
            }
            
            # Key metrics
            current_enrollment = historical_data[-1]['enrollment_count'] if historical_data else 0
            previous_enrollment = historical_data[-2]['enrollment_count'] if len(historical_data) > 1 else current_enrollment
            enrollment_change = ((current_enrollment - previous_enrollment) / previous_enrollment * 100) if previous_enrollment > 0 else 0
            
            metrics = {
                'current_enrollment': current_enrollment,
                'enrollment_change': enrollment_change,
                'trend_direction': trends.get('trend_direction', 'stable'),
                'growth_rate': trends.get('growth_rate', 0),
                'prediction_accuracy': trends.get('prediction_accuracy', 0)
            }
            
            return {
                'chart_data': chart_data,
                'metrics': metrics,
                'trends': trends,
                'insights': self._generate_enrollment_insights(historical_data, predictions, trends)
            }
            
        except Exception as e:
            logger.error(f"Error creating enrollment dashboard data: {e}")
            return {'error': str(e)}
    
    def _create_revenue_dashboard_data(self):
        """Create revenue-specific dashboard data"""
        try:
            historical_data = self._get_revenue_history()
            predictions = self.predict_revenue_trends(6)
            trends = self._analyze_revenue_trends_advanced(12)
            
            # Chart data
            chart_data = {
                'historical': [
                    {
                        'month': item['month'],
                        'revenue': item['total_revenue'],
                        'payment_count': item['payment_count'],
                        'avg_payment': item['avg_payment']
                    }
                    for item in historical_data
                ],
                'predictions': predictions.get('predictions', []) if predictions['success'] else []
            }
            
            # Key metrics
            current_revenue = historical_data[-1]['total_revenue'] if historical_data else 0
            previous_revenue = historical_data[-2]['total_revenue'] if len(historical_data) > 1 else current_revenue
            revenue_change = ((current_revenue - previous_revenue) / previous_revenue * 100) if previous_revenue > 0 else 0
            
            metrics = {
                'current_revenue': current_revenue,
                'revenue_change': revenue_change,
                'trend_direction': trends.get('trend_direction', 'stable'),
                'volatility': trends.get('volatility_coefficient', 0),
                'avg_monthly_revenue': np.mean([item['total_revenue'] for item in historical_data]) if historical_data else 0
            }
            
            return {
                'chart_data': chart_data,
                'metrics': metrics,
                'trends': trends,
                'insights': self._generate_revenue_insights(historical_data, predictions, trends)
            }
            
        except Exception as e:
            logger.error(f"Error creating revenue dashboard data: {e}")
            return {'error': str(e)}
    
    def _create_performance_dashboard_data(self):
        """Create performance-specific dashboard data"""
        try:
            historical_data = self._get_performance_history()
            predictions = self.predict_student_performance()
            trends = self._analyze_performance_trends(12)
            
            # Chart data - group by month
            df = pd.DataFrame(historical_data)
            if not df.empty:
                df['graded_date'] = pd.to_datetime(df['graded_at'])
                monthly_performance = df.groupby(df['graded_date'].dt.to_period('M')).agg({
                    'percentage': ['mean', 'std', 'count']
                }).reset_index()
                
                chart_data = {
                    'monthly_performance': [
                        {
                            'month': str(row[0]),
                            'avg_percentage': row[1],
                            'std_percentage': row[2],
                            'student_count': row[3]
                        }
                        for row in monthly_performance.values
                    ],
                    'subject_performance': trends.get('subject_trends', {}),
                    'class_performance': trends.get('class_trends', {})
                }
            else:
                chart_data = {'monthly_performance': [], 'subject_performance': {}, 'class_performance': {}}
            
            # Key metrics
            metrics = {
                'average_performance': trends.get('average_performance', 0),
                'performance_std': trends.get('performance_std', 0),
                'trend_direction': trends.get('overall_trend', 'stable'),
                'at_risk_count': len(predictions.get('at_risk_students', [])) if predictions['success'] else 0,
                'performance_distribution': trends.get('performance_distribution', {})
            }
            
            return {
                'chart_data': chart_data,
                'metrics': metrics,
                'trends': trends,
                'at_risk_students': predictions.get('at_risk_students', []) if predictions['success'] else [],
                'insights': self._generate_performance_insights(historical_data, predictions, trends)
            }
            
        except Exception as e:
            logger.error(f"Error creating performance dashboard data: {e}")
            return {'error': str(e)}
    
    def _create_attendance_dashboard_data(self):
        """Create attendance-specific dashboard data"""
        try:
            historical_data = self._get_attendance_history()
            predictions = self.predict_attendance_patterns(1)
            trends = self._analyze_attendance_trends_advanced(12)
            
            # Chart data
            chart_data = {
                'daily_attendance': [
                    {
                        'date': item['date'].isoformat() if hasattr(item['date'], 'isoformat') else str(item['date']),
                        'attendance_rate': item['attendance_rate'],
                        'present_count': item['present_count'],
                        'absent_count': item['absent_count'],
                        'day_of_week': item['day_of_week']
                    }
                    for item in historical_data[-30:]  # Last 30 days
                ],
                'predictions': predictions.get('predictions', [])[:7] if predictions['success'] else [],  # Next 7 days
                'day_patterns': trends.get('day_of_week_patterns', {}),
                'monthly_patterns': trends.get('monthly_patterns', {})
            }
            
            # Key metrics
            current_avg = np.mean([item['attendance_rate'] for item in historical_data[-7:]]) if len(historical_data) >= 7 else 0
            overall_avg = trends.get('average_attendance', 0)
            
            metrics = {
                'current_avg_attendance': current_avg,
                'overall_avg_attendance': overall_avg,
                'trend_direction': trends.get('overall_trend', 'stable'),
                'attendance_volatility': trends.get('attendance_volatility', 0),
                'attendance_distribution': trends.get('attendance_distribution', {})
            }
            
            return {
                'chart_data': chart_data,
                'metrics': metrics,
                'trends': trends,
                'insights': self._generate_attendance_insights(historical_data, predictions, trends)
            }
            
        except Exception as e:
            logger.error(f"Error creating attendance dashboard data: {e}")
            return {'error': str(e)}
    
    def _generate_enrollment_insights(self, historical_data, predictions, trends):
        """Generate insights for enrollment data"""
        insights = []
        
        try:
            # Trend insights
            if trends.get('trend_direction') == 'increasing':
                insights.append({
                    'type': 'positive',
                    'title': 'Growing Enrollment',
                    'message': f"Enrollment is trending upward with a {trends.get('growth_rate', 0):.1f}% growth rate.",
                    'priority': 'high'
                })
            elif trends.get('trend_direction') == 'decreasing':
                insights.append({
                    'type': 'warning',
                    'title': 'Declining Enrollment',
                    'message': f"Enrollment is declining at {abs(trends.get('growth_rate', 0)):.1f}% rate. Consider marketing initiatives.",
                    'priority': 'high'
                })
            
            # Seasonal insights
            seasonal_pattern = trends.get('seasonal_pattern', {})
            if seasonal_pattern:
                peak_month = max(seasonal_pattern.items(), key=lambda x: x[1])
                low_month = min(seasonal_pattern.items(), key=lambda x: x[1])
                
                insights.append({
                    'type': 'info',
                    'title': 'Seasonal Patterns',
                    'message': f"Peak enrollment typically occurs in month {peak_month[0]}, lowest in month {low_month[0]}.",
                    'priority': 'medium'
                })
            
            # Prediction insights
            if predictions['success'] and predictions.get('predictions'):
                next_month_prediction = predictions['predictions'][0]
                current_enrollment = historical_data[-1]['enrollment_count'] if historical_data else 0
                predicted_change = ((next_month_prediction['predicted_enrollment'] - current_enrollment) / current_enrollment * 100) if current_enrollment > 0 else 0
                
                if abs(predicted_change) > 5:
                    insights.append({
                        'type': 'warning' if predicted_change < 0 else 'positive',
                        'title': 'Enrollment Forecast',
                        'message': f"Next month enrollment predicted to {'decrease' if predicted_change < 0 else 'increase'} by {abs(predicted_change):.1f}%.",
                        'priority': 'medium'
                    })
            
        except Exception as e:
            logger.error(f"Error generating enrollment insights: {e}")
        
        return insights
    
    def _generate_revenue_insights(self, historical_data, predictions, trends):
        """Generate insights for revenue data"""
        insights = []
        
        try:
            # Trend insights
            if trends.get('trend_direction') == 'increasing':
                insights.append({
                    'type': 'positive',
                    'title': 'Revenue Growth',
                    'message': f"Revenue is trending upward with positive monthly growth.",
                    'priority': 'high'
                })
            elif trends.get('trend_direction') == 'decreasing':
                insights.append({
                    'type': 'warning',
                    'title': 'Revenue Decline',
                    'message': f"Revenue is declining. Review fee collection and payment processes.",
                    'priority': 'high'
                })
            
            # Volatility insights
            volatility = trends.get('volatility_coefficient', 0)
            if volatility > 0.2:
                insights.append({
                    'type': 'warning',
                    'title': 'High Revenue Volatility',
                    'message': f"Revenue shows high volatility ({volatility:.2f}). Consider stabilizing payment schedules.",
                    'priority': 'medium'
                })
            
            # Payment pattern insights
            if historical_data:
                avg_payment_per_transaction = np.mean([item['avg_payment'] for item in historical_data])
                recent_avg = np.mean([item['avg_payment'] for item in historical_data[-3:]])
                
                if recent_avg < avg_payment_per_transaction * 0.9:
                    insights.append({
                        'type': 'warning',
                        'title': 'Declining Payment Size',
                        'message': f"Average payment size has decreased recently. Review fee structures.",
                        'priority': 'medium'
                    })
            
        except Exception as e:
            logger.error(f"Error generating revenue insights: {e}")
        
        return insights
    
    def _generate_performance_insights(self, historical_data, predictions, trends):
        """Generate insights for performance data"""
        insights = []
        
        try:
            # Overall performance insights
            avg_performance = trends.get('average_performance', 0)
            if avg_performance < 70:
                insights.append({
                    'type': 'warning',
                    'title': 'Low Academic Performance',
                    'message': f"Overall performance average is {avg_performance:.1f}%. Consider academic support programs.",
                    'priority': 'high'
                })
            elif avg_performance > 85:
                insights.append({
                    'type': 'positive',
                    'title': 'Excellent Academic Performance',
                    'message': f"Overall performance average is {avg_performance:.1f}%. Students are performing well.",
                    'priority': 'high'
                })
            
            # At-risk student insights
            if predictions['success']:
                at_risk_count = len(predictions.get('at_risk_students', []))
                total_predictions = len(predictions.get('predictions', []))
                
                if at_risk_count > 0 and total_predictions > 0:
                    risk_percentage = (at_risk_count / total_predictions) * 100
                    insights.append({
                        'type': 'warning',
                        'title': 'At-Risk Students',
                        'message': f"{at_risk_count} students ({risk_percentage:.1f}%) are at risk of poor performance.",
                        'priority': 'high'
                    })
            
            # Performance distribution insights
            distribution = trends.get('performance_distribution', {})
            if distribution.get('poor', 0) > 20:
                insights.append({
                    'type': 'warning',
                    'title': 'High Failure Rate',
                    'message': f"{distribution['poor']:.1f}% of students are performing poorly. Intervention needed.",
                    'priority': 'high'
                })
            
        except Exception as e:
            logger.error(f"Error generating performance insights: {e}")
        
        return insights
    
    def _generate_attendance_insights(self, historical_data, predictions, trends):
        """Generate insights for attendance data"""
        insights = []
        
        try:
            # Overall attendance insights
            avg_attendance = trends.get('average_attendance', 0)
            if avg_attendance < 85:
                insights.append({
                    'type': 'warning',
                    'title': 'Low Attendance Rate',
                    'message': f"Average attendance is {avg_attendance:.1f}%. Consider attendance improvement strategies.",
                    'priority': 'high'
                })
            elif avg_attendance > 95:
                insights.append({
                    'type': 'positive',
                    'title': 'Excellent Attendance',
                    'message': f"Average attendance is {avg_attendance:.1f}%. Students are highly engaged.",
                    'priority': 'high'
                })
            
            # Day pattern insights
            day_patterns = trends.get('day_of_week_patterns', {})
            if day_patterns:
                worst_day = min(day_patterns.items(), key=lambda x: x[1]['mean'])
                best_day = max(day_patterns.items(), key=lambda x: x[1]['mean'])
                
                if worst_day[1]['mean'] < best_day[1]['mean'] - 5:
                    day_names = {1: 'Monday', 2: 'Tuesday', 3: 'Wednesday', 4: 'Thursday', 5: 'Friday', 6: 'Saturday', 7: 'Sunday'}
                    insights.append({
                        'type': 'info',
                        'title': 'Day-of-Week Patterns',
                        'message': f"Attendance is lowest on {day_names.get(worst_day[0], 'Unknown')} ({worst_day[1]['mean']:.1f}%).",
                        'priority': 'medium'
                    })
            
            # Trend insights
            if trends.get('overall_trend') == 'declining':
                insights.append({
                    'type': 'warning',
                    'title': 'Declining Attendance Trend',
                    'message': "Attendance is trending downward. Investigate causes and implement interventions.",
                    'priority': 'high'
                })
            
        except Exception as e:
            logger.error(f"Error generating attendance insights: {e}")
        
        return insights
    
    def export_analytics_report(self, report_type='comprehensive', format='json'):
        """
        Export comprehensive analytics report
        
        Args:
            report_type (str): Type of report to export
            format (str): Export format ('json', 'csv', 'excel')
            
        Returns:
            dict: Export result with data or file path
        """
        try:
            # Generate comprehensive analytics data
            dashboard_data = self.create_analytics_dashboard_data(report_type)
            
            if not dashboard_data['success']:
                return dashboard_data
            
            # Add additional analysis
            report_data = {
                'report_metadata': {
                    'type': report_type,
                    'generated_at': timezone.now().isoformat(),
                    'format': format,
                    'version': '1.0'
                },
                'dashboard_data': dashboard_data,
                'summary_statistics': self._generate_summary_statistics(),
                'recommendations': self._generate_strategic_recommendations(dashboard_data)
            }
            
            if format == 'json':
                return {
                    'success': True,
                    'data': report_data,
                    'format': format
                }
            elif format == 'csv':
                # Convert to CSV format (simplified)
                csv_data = self._convert_to_csv(report_data)
                return {
                    'success': True,
                    'csv_data': csv_data,
                    'format': format
                }
            elif format == 'excel':
                # This would require additional Excel export logic
                return {
                    'success': True,
                    'message': 'Excel export functionality would be implemented here',
                    'format': format
                }
            else:
                return {
                    'success': False,
                    'error': f'Unsupported format: {format}'
                }
                
        except Exception as e:
            logger.error(f"Error exporting analytics report: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _generate_summary_statistics(self):
        """Generate summary statistics across all metrics"""
        try:
            summary = {}
            
            # Enrollment statistics
            enrollment_data = self._get_enrollment_history()
            if enrollment_data:
                summary['enrollment'] = {
                    'total_months': len(enrollment_data),
                    'avg_monthly_enrollment': np.mean([item['enrollment_count'] for item in enrollment_data]),
                    'max_enrollment': max([item['enrollment_count'] for item in enrollment_data]),
                    'min_enrollment': min([item['enrollment_count'] for item in enrollment_data])
                }
            
            # Revenue statistics
            revenue_data = self._get_revenue_history()
            if revenue_data:
                summary['revenue'] = {
                    'total_months': len(revenue_data),
                    'total_revenue': sum([item['total_revenue'] for item in revenue_data]),
                    'avg_monthly_revenue': np.mean([item['total_revenue'] for item in revenue_data]),
                    'max_monthly_revenue': max([item['total_revenue'] for item in revenue_data])
                }
            
            # Performance statistics
            performance_data = self._get_performance_history()
            if performance_data:
                summary['performance'] = {
                    'total_grades': len(performance_data),
                    'avg_performance': np.mean([item['percentage'] for item in performance_data]),
                    'performance_std': np.std([item['percentage'] for item in performance_data]),
                    'pass_rate': len([item for item in performance_data if item['percentage'] >= 60]) / len(performance_data) * 100
                }
            
            # Attendance statistics
            attendance_data = self._get_attendance_history()
            if attendance_data:
                summary['attendance'] = {
                    'total_days': len(attendance_data),
                    'avg_attendance_rate': np.mean([item['attendance_rate'] for item in attendance_data]),
                    'best_attendance_day': max([item['attendance_rate'] for item in attendance_data]),
                    'worst_attendance_day': min([item['attendance_rate'] for item in attendance_data])
                }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating summary statistics: {e}")
            return {'error': str(e)}
    
    def _generate_strategic_recommendations(self, dashboard_data):
        """Generate strategic recommendations based on analytics"""
        recommendations = []
        
        try:
            # Enrollment recommendations
            if 'enrollment' in dashboard_data:
                enrollment_metrics = dashboard_data['enrollment'].get('metrics', {})
                if enrollment_metrics.get('trend_direction') == 'decreasing':
                    recommendations.append({
                        'category': 'enrollment',
                        'priority': 'high',
                        'title': 'Address Enrollment Decline',
                        'description': 'Implement marketing campaigns and improve admission processes',
                        'actions': [
                            'Review and improve marketing strategies',
                            'Analyze competitor offerings',
                            'Enhance admission process efficiency',
                            'Implement referral programs'
                        ]
                    })
            
            # Revenue recommendations
            if 'revenue' in dashboard_data:
                revenue_metrics = dashboard_data['revenue'].get('metrics', {})
                if revenue_metrics.get('trend_direction') == 'decreasing':
                    recommendations.append({
                        'category': 'revenue',
                        'priority': 'high',
                        'title': 'Improve Revenue Collection',
                        'description': 'Optimize fee collection and payment processes',
                        'actions': [
                            'Review fee structure and payment terms',
                            'Implement automated payment reminders',
                            'Offer flexible payment options',
                            'Improve collection processes'
                        ]
                    })
            
            # Performance recommendations
            if 'performance' in dashboard_data:
                performance_metrics = dashboard_data['performance'].get('metrics', {})
                if performance_metrics.get('at_risk_count', 0) > 10:
                    recommendations.append({
                        'category': 'performance',
                        'priority': 'high',
                        'title': 'Support At-Risk Students',
                        'description': 'Implement intervention programs for struggling students',
                        'actions': [
                            'Develop early intervention programs',
                            'Provide additional tutoring support',
                            'Implement personalized learning plans',
                            'Increase parent-teacher communication'
                        ]
                    })
            
            # Attendance recommendations
            if 'attendance' in dashboard_data:
                attendance_metrics = dashboard_data['attendance'].get('metrics', {})
                if attendance_metrics.get('current_avg_attendance', 0) < 85:
                    recommendations.append({
                        'category': 'attendance',
                        'priority': 'medium',
                        'title': 'Improve Attendance Rates',
                        'description': 'Implement strategies to increase student attendance',
                        'actions': [
                            'Analyze attendance patterns and causes',
                            'Implement attendance incentive programs',
                            'Improve parent communication about absences',
                            'Review and adjust school policies'
                        ]
                    })
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating strategic recommendations: {e}")
            return []
    
    def _convert_to_csv(self, report_data):
        """Convert report data to CSV format"""
        try:
            # This is a simplified CSV conversion
            # In practice, you'd want more sophisticated CSV generation
            csv_lines = []
            csv_lines.append("Metric,Value,Category,Date")
            
            # Add enrollment data
            if 'enrollment' in report_data['dashboard_data']:
                enrollment_data = report_data['dashboard_data']['enrollment']
                for item in enrollment_data.get('chart_data', {}).get('historical', []):
                    csv_lines.append(f"Enrollment,{item['enrollment']},Historical,{item['month']}")
            
            # Add revenue data
            if 'revenue' in report_data['dashboard_data']:
                revenue_data = report_data['dashboard_data']['revenue']
                for item in revenue_data.get('chart_data', {}).get('historical', []):
                    csv_lines.append(f"Revenue,{item['revenue']},Historical,{item['month']}")
            
            return '\n'.join(csv_lines)
            
        except Exception as e:
            logger.error(f"Error converting to CSV: {e}")
            return "Error,Error,Error,Error"


# Utility functions for external use
def get_predictive_analytics_service():
    """Get singleton instance of PredictiveAnalyticsService"""
    if not hasattr(get_predictive_analytics_service, '_instance'):
        get_predictive_analytics_service._instance = PredictiveAnalyticsService()
    return get_predictive_analytics_service._instance


def generate_quick_insights(data_type='all'):
    """Generate quick insights for dashboard widgets"""
    service = get_predictive_analytics_service()
    return service.analyze_trends(data_type, time_period=6)


def get_predictive_alerts():
    """Get current predictive alerts"""
    service = get_predictive_analytics_service()
    return service.generate_predictive_alerts()


def create_dashboard_data(dashboard_type='comprehensive'):
    """Create dashboard data for frontend"""
    service = get_predictive_analytics_service()
    return service.create_analytics_dashboard_data(dashboard_type)