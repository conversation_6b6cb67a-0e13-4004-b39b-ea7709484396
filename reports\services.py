"""
Report Builder Services
Handles report generation, query building, and data processing
"""

from django.db import connection
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.apps import apps
from django.db.models import Q, Count, Sum, Avg, Max, Min
from django.utils import timezone
from datetime import datetime, timedelta
import json
import uuid
import logging

logger = logging.getLogger(__name__)


class QueryBuilder:
    """
    Visual query builder service for creating SQL queries from drag-and-drop interface
    """
    
    def __init__(self):
        self.available_models = self._get_available_models()
        self.operators = {
            'equals': '=',
            'not_equals': '!=',
            'greater_than': '>',
            'less_than': '<',
            'greater_equal': '>=',
            'less_equal': '<=',
            'contains': 'LIKE',
            'starts_with': 'LIKE',
            'ends_with': 'LIKE',
            'in': 'IN',
            'not_in': 'NOT IN',
            'is_null': 'IS NULL',
            'is_not_null': 'IS NOT NULL',
            'between': 'BETWEEN',
        }
        
    def _get_available_models(self):
        """Get all available models for report building"""
        models_info = {}
        
        # Define the models we want to make available for reporting
        available_apps = ['students', 'academics', 'finance', 'hr', 'core']
        
        for app_name in available_apps:
            try:
                app_config = apps.get_app_config(app_name)
                for model in app_config.get_models():
                    model_name = f"{app_name}.{model.__name__}"
                    models_info[model_name] = {
                        'name': model.__name__,
                        'app': app_name,
                        'verbose_name': getattr(model._meta, 'verbose_name', model.__name__),
                        'fields': self._get_model_fields(model),
                        'relationships': self._get_model_relationships(model)
                    }
            except Exception as e:
                logger.warning(f"Could not load models from app {app_name}: {e}")
                
        return models_info
    
    def _get_model_fields(self, model):
        """Get all fields for a model"""
        fields = {}
        for field in model._meta.get_fields():
            if not field.many_to_many and not field.one_to_many:
                field_info = {
                    'name': field.name,
                    'type': field.__class__.__name__,
                    'verbose_name': getattr(field, 'verbose_name', field.name),
                    'choices': getattr(field, 'choices', None),
                    'null': getattr(field, 'null', False),
                    'blank': getattr(field, 'blank', False),
                }
                
                # Add specific field type information
                if hasattr(field, 'max_length'):
                    field_info['max_length'] = field.max_length
                if hasattr(field, 'decimal_places'):
                    field_info['decimal_places'] = field.decimal_places
                    field_info['max_digits'] = field.max_digits
                    
                fields[field.name] = field_info
                
        return fields
    
    def _get_model_relationships(self, model):
        """Get relationships for a model"""
        relationships = {}
        for field in model._meta.get_fields():
            if field.many_to_one or field.one_to_one:
                relationships[field.name] = {
                    'type': 'foreign_key',
                    'related_model': f"{field.related_model._meta.app_label}.{field.related_model.__name__}",
                    'verbose_name': getattr(field, 'verbose_name', field.name)
                }
            elif field.many_to_many:
                relationships[field.name] = {
                    'type': 'many_to_many',
                    'related_model': f"{field.related_model._meta.app_label}.{field.related_model.__name__}",
                    'verbose_name': getattr(field, 'verbose_name', field.name)
                }
                
        return relationships
    
    def build_query(self, query_config):
        """
        Build SQL query from visual query configuration
        
        Args:
            query_config (dict): Visual query configuration
            
        Returns:
            dict: Generated SQL query and metadata
        """
        try:
            # Extract configuration
            selected_fields = query_config.get('fields', [])
            data_sources = query_config.get('data_sources', [])
            filters = query_config.get('filters', [])
            joins = query_config.get('joins', [])
            grouping = query_config.get('grouping', [])
            sorting = query_config.get('sorting', [])
            aggregations = query_config.get('aggregations', [])
            
            # Build SELECT clause
            select_clause = self._build_select_clause(selected_fields, aggregations)
            
            # Build FROM clause
            from_clause = self._build_from_clause(data_sources)
            
            # Build JOIN clauses
            join_clauses = self._build_join_clauses(joins)
            
            # Build WHERE clause
            where_clause = self._build_where_clause(filters)
            
            # Build GROUP BY clause
            group_by_clause = self._build_group_by_clause(grouping)
            
            # Build ORDER BY clause
            order_by_clause = self._build_order_by_clause(sorting)
            
            # Combine all clauses
            sql_parts = [select_clause, from_clause] + join_clauses
            
            if where_clause:
                sql_parts.append(where_clause)
            if group_by_clause:
                sql_parts.append(group_by_clause)
            if order_by_clause:
                sql_parts.append(order_by_clause)
                
            sql_query = ' '.join(sql_parts)
            
            return {
                'sql': sql_query,
                'parameters': [],
                'fields': selected_fields,
                'metadata': {
                    'data_sources': data_sources,
                    'joins': joins,
                    'filters': filters,
                    'aggregations': aggregations
                }
            }
            
        except Exception as e:
            logger.error(f"Error building query: {e}")
            raise ValidationError(f"Query building failed: {str(e)}")
    
    def _build_select_clause(self, fields, aggregations):
        """Build SELECT clause"""
        if not fields and not aggregations:
            return "SELECT *"
            
        select_items = []
        
        # Add regular fields
        for field in fields:
            table_alias = field.get('table_alias', 't1')
            field_name = field.get('field_name')
            alias = field.get('alias', field_name)
            
            if field_name:
                select_items.append(f"{table_alias}.{field_name} AS {alias}")
        
        # Add aggregations
        for agg in aggregations:
            agg_func = agg.get('function', 'COUNT').upper()
            field_name = agg.get('field_name', '*')
            table_alias = agg.get('table_alias', 't1')
            alias = agg.get('alias', f"{agg_func}_{field_name}")
            
            if field_name == '*':
                select_items.append(f"{agg_func}(*) AS {alias}")
            else:
                select_items.append(f"{agg_func}({table_alias}.{field_name}) AS {alias}")
        
        return f"SELECT {', '.join(select_items)}"
    
    def _build_from_clause(self, data_sources):
        """Build FROM clause"""
        if not data_sources:
            raise ValidationError("At least one data source is required")
            
        primary_source = data_sources[0]
        model_name = primary_source.get('model')
        table_alias = primary_source.get('alias', 't1')
        
        # Get actual table name from model
        app_name, model_class_name = model_name.split('.')
        model_class = apps.get_model(app_name, model_class_name)
        table_name = model_class._meta.db_table
        
        return f"FROM {table_name} AS {table_alias}"
    
    def _build_join_clauses(self, joins):
        """Build JOIN clauses"""
        join_clauses = []
        
        for join in joins:
            join_type = join.get('type', 'INNER').upper()
            left_table = join.get('left_table')
            right_table = join.get('right_table')
            left_field = join.get('left_field')
            right_field = join.get('right_field')
            
            # Get table name for right table
            model_name = join.get('right_model')
            if model_name:
                app_name, model_class_name = model_name.split('.')
                model_class = apps.get_model(app_name, model_class_name)
                right_table_name = model_class._meta.db_table
                
                join_clause = f"{join_type} JOIN {right_table_name} AS {right_table} ON {left_table}.{left_field} = {right_table}.{right_field}"
                join_clauses.append(join_clause)
        
        return join_clauses
    
    def _build_where_clause(self, filters):
        """Build WHERE clause"""
        if not filters:
            return ""
            
        conditions = []
        
        for filter_item in filters:
            table_alias = filter_item.get('table_alias', 't1')
            field_name = filter_item.get('field_name')
            operator = filter_item.get('operator', 'equals')
            value = filter_item.get('value')
            logical_operator = filter_item.get('logical_operator', 'AND')
            
            if field_name and operator in self.operators:
                sql_operator = self.operators[operator]
                
                if operator in ['is_null', 'is_not_null']:
                    condition = f"{table_alias}.{field_name} {sql_operator}"
                elif operator in ['contains', 'starts_with', 'ends_with']:
                    if operator == 'contains':
                        condition = f"{table_alias}.{field_name} LIKE '%{value}%'"
                    elif operator == 'starts_with':
                        condition = f"{table_alias}.{field_name} LIKE '{value}%'"
                    else:  # ends_with
                        condition = f"{table_alias}.{field_name} LIKE '%{value}'"
                elif operator in ['in', 'not_in']:
                    if isinstance(value, list):
                        value_list = "', '".join(str(v) for v in value)
                        condition = f"{table_alias}.{field_name} {sql_operator} ('{value_list}')"
                    else:
                        condition = f"{table_alias}.{field_name} {sql_operator} ('{value}')"
                elif operator == 'between':
                    if isinstance(value, list) and len(value) == 2:
                        condition = f"{table_alias}.{field_name} BETWEEN '{value[0]}' AND '{value[1]}'"
                    else:
                        continue
                else:
                    condition = f"{table_alias}.{field_name} {sql_operator} '{value}'"
                
                conditions.append(condition)
        
        if conditions:
            return f"WHERE {' AND '.join(conditions)}"
        
        return ""
    
    def _build_group_by_clause(self, grouping):
        """Build GROUP BY clause"""
        if not grouping:
            return ""
            
        group_fields = []
        for group in grouping:
            table_alias = group.get('table_alias', 't1')
            field_name = group.get('field_name')
            if field_name:
                group_fields.append(f"{table_alias}.{field_name}")
        
        if group_fields:
            return f"GROUP BY {', '.join(group_fields)}"
        
        return ""
    
    def _build_order_by_clause(self, sorting):
        """Build ORDER BY clause"""
        if not sorting:
            return ""
            
        sort_fields = []
        for sort in sorting:
            table_alias = sort.get('table_alias', 't1')
            field_name = sort.get('field_name')
            direction = sort.get('direction', 'ASC').upper()
            
            if field_name:
                sort_fields.append(f"{table_alias}.{field_name} {direction}")
        
        if sort_fields:
            return f"ORDER BY {', '.join(sort_fields)}"
        
        return ""
    
    def execute_query(self, sql_query, parameters=None):
        """
        Execute the generated SQL query
        
        Args:
            sql_query (str): SQL query to execute
            parameters (list): Query parameters
            
        Returns:
            dict: Query results and metadata
        """
        try:
            with connection.cursor() as cursor:
                start_time = timezone.now()
                
                if parameters:
                    cursor.execute(sql_query, parameters)
                else:
                    cursor.execute(sql_query)
                
                end_time = timezone.now()
                execution_time = end_time - start_time
                
                # Fetch results
                columns = [col[0] for col in cursor.description]
                rows = cursor.fetchall()
                
                # Convert to list of dictionaries
                results = []
                for row in rows:
                    results.append(dict(zip(columns, row)))
                
                return {
                    'results': results,
                    'columns': columns,
                    'row_count': len(results),
                    'execution_time': execution_time.total_seconds(),
                    'success': True
                }
                
        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            return {
                'results': [],
                'columns': [],
                'row_count': 0,
                'execution_time': 0,
                'success': False,
                'error': str(e)
            }
    
    def validate_query_config(self, query_config):
        """
        Validate query configuration
        
        Args:
            query_config (dict): Query configuration to validate
            
        Returns:
            dict: Validation results
        """
        errors = []
        warnings = []
        
        # Check required fields
        if not query_config.get('data_sources'):
            errors.append("At least one data source is required")
        
        if not query_config.get('fields') and not query_config.get('aggregations'):
            errors.append("At least one field or aggregation is required")
        
        # Validate data sources
        for source in query_config.get('data_sources', []):
            model_name = source.get('model')
            if model_name and model_name not in self.available_models:
                errors.append(f"Invalid model: {model_name}")
        
        # Validate fields
        for field in query_config.get('fields', []):
            field_name = field.get('field_name')
            model_name = field.get('model')
            
            if model_name and model_name in self.available_models:
                model_fields = self.available_models[model_name]['fields']
                if field_name and field_name not in model_fields:
                    errors.append(f"Invalid field {field_name} for model {model_name}")
        
        # Validate filters
        for filter_item in query_config.get('filters', []):
            operator = filter_item.get('operator')
            if operator and operator not in self.operators:
                errors.append(f"Invalid operator: {operator}")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }


class ReportGenerator:
    """
    Report generation service for creating reports from templates
    """
    
    def __init__(self):
        self.query_builder = QueryBuilder()
    
    def generate_report(self, report_template, parameters=None):
        """
        Generate report from template
        
        Args:
            report_template: ReportTemplate instance
            parameters (dict): Report parameters
            
        Returns:
            dict: Generated report data
        """
        try:
            start_time = timezone.now()
            
            # Get query from template
            if hasattr(report_template, 'report_builder') and report_template.report_builder:
                # Use visual query builder
                query_config = report_template.report_builder.query_config
                query_result = self.query_builder.build_query(query_config)
                sql_query = query_result['sql']
            else:
                # Use direct SQL query
                sql_query = report_template.query
            
            # Apply parameters if provided
            if parameters:
                sql_query = self._apply_parameters(sql_query, parameters)
            
            # Execute query
            execution_result = self.query_builder.execute_query(sql_query)
            
            end_time = timezone.now()
            total_time = end_time - start_time
            
            return {
                'success': execution_result['success'],
                'data': execution_result['results'],
                'columns': execution_result['columns'],
                'row_count': execution_result['row_count'],
                'execution_time': total_time.total_seconds(),
                'generated_at': end_time,
                'parameters_used': parameters or {},
                'error': execution_result.get('error')
            }
            
        except Exception as e:
            logger.error(f"Report generation failed: {e}")
            return {
                'success': False,
                'data': [],
                'columns': [],
                'row_count': 0,
                'execution_time': 0,
                'generated_at': timezone.now(),
                'parameters_used': parameters or {},
                'error': str(e)
            }
    
    def _apply_parameters(self, sql_query, parameters):
        """Apply parameters to SQL query"""
        # Simple parameter substitution
        # In production, use proper parameterized queries
        for key, value in parameters.items():
            placeholder = f"{{{key}}}"
            if placeholder in sql_query:
                sql_query = sql_query.replace(placeholder, str(value))
        
        return sql_query


class ReportScheduler:
    """
    Report scheduling service
    """
    
    def __init__(self):
        self.report_generator = ReportGenerator()
    
    def schedule_report(self, report_schedule):
        """
        Schedule a report for automatic generation
        
        Args:
            report_schedule: ReportSchedule instance
        """
        try:
            # Calculate next run time based on frequency
            next_run = self._calculate_next_run(report_schedule)
            report_schedule.next_run = next_run
            report_schedule.save()
            
            # Add to task queue (using Celery)
            from .tasks import generate_scheduled_report
            generate_scheduled_report.apply_async(
                args=[report_schedule.id],
                eta=next_run
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Report scheduling failed: {e}")
            return False
    
    def _calculate_next_run(self, report_schedule):
        """Calculate next run time based on frequency"""
        now = timezone.now()
        frequency = report_schedule.frequency
        schedule_config = report_schedule.schedule_config
        
        if frequency == 'once':
            return schedule_config.get('run_at', now)
        elif frequency == 'daily':
            # Run daily at specified time
            run_time = schedule_config.get('time', '09:00')
            hour, minute = map(int, run_time.split(':'))
            next_run = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
            if next_run <= now:
                next_run += timedelta(days=1)
            return next_run
        elif frequency == 'weekly':
            # Run weekly on specified day
            day_of_week = schedule_config.get('day_of_week', 1)  # Monday = 1
            run_time = schedule_config.get('time', '09:00')
            hour, minute = map(int, run_time.split(':'))
            
            days_ahead = day_of_week - now.weekday()
            if days_ahead <= 0:  # Target day already happened this week
                days_ahead += 7
            
            next_run = now + timedelta(days=days_ahead)
            next_run = next_run.replace(hour=hour, minute=minute, second=0, microsecond=0)
            return next_run
        elif frequency == 'monthly':
            # Run monthly on specified day
            day_of_month = schedule_config.get('day_of_month', 1)
            run_time = schedule_config.get('time', '09:00')
            hour, minute = map(int, run_time.split(':'))
            
            # Calculate next month
            if now.day >= day_of_month:
                if now.month == 12:
                    next_run = now.replace(year=now.year + 1, month=1, day=day_of_month)
                else:
                    next_run = now.replace(month=now.month + 1, day=day_of_month)
            else:
                next_run = now.replace(day=day_of_month)
            
            next_run = next_run.replace(hour=hour, minute=minute, second=0, microsecond=0)
            return next_run
        
        return now + timedelta(hours=1)  # Default fallback


class ReportExporter:
    """
    Report export service for multiple formats
    """
    
    def __init__(self):
        self.supported_formats = ['pdf', 'excel', 'csv', 'json']
    
    def export_report(self, report_data, format_type, template_config=None):
        """
        Export report data to specified format
        
        Args:
            report_data (dict): Report data to export
            format_type (str): Export format (pdf, excel, csv, json)
            template_config (dict): Template configuration for formatting
            
        Returns:
            dict: Export result with file path or content
        """
        if format_type not in self.supported_formats:
            raise ValidationError(f"Unsupported format: {format_type}")
        
        try:
            if format_type == 'pdf':
                return self._export_to_pdf(report_data, template_config)
            elif format_type == 'excel':
                return self._export_to_excel(report_data, template_config)
            elif format_type == 'csv':
                return self._export_to_csv(report_data, template_config)
            elif format_type == 'json':
                return self._export_to_json(report_data, template_config)
                
        except Exception as e:
            logger.error(f"Report export failed: {e}")
            raise ValidationError(f"Export failed: {str(e)}")
    
    def _export_to_pdf(self, report_data, template_config):
        """Export to PDF format"""
        # Implementation for PDF export
        # This would use libraries like ReportLab or WeasyPrint
        return {
            'success': True,
            'format': 'pdf',
            'file_path': '/tmp/report.pdf',
            'message': 'PDF export completed'
        }
    
    def _export_to_excel(self, report_data, template_config):
        """Export to Excel format"""
        # Implementation for Excel export
        # This would use libraries like openpyxl or xlsxwriter
        return {
            'success': True,
            'format': 'excel',
            'file_path': '/tmp/report.xlsx',
            'message': 'Excel export completed'
        }
    
    def _export_to_csv(self, report_data, template_config):
        """Export to CSV format"""
        import csv
        import io
        
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write headers
        if report_data.get('columns'):
            writer.writerow(report_data['columns'])
        
        # Write data
        for row in report_data.get('data', []):
            if isinstance(row, dict):
                writer.writerow(row.values())
            else:
                writer.writerow(row)
        
        return {
            'success': True,
            'format': 'csv',
            'content': output.getvalue(),
            'message': 'CSV export completed'
        }
    
    def _export_to_json(self, report_data, template_config):
        """Export to JSON format"""
        return {
            'success': True,
            'format': 'json',
            'content': json.dumps(report_data, indent=2, default=str),
            'message': 'JSON export completed'
        }