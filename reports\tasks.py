"""
Celery tasks for report generation and scheduling
"""

from celery import shared_task
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from .models import ReportSchedule, ReportExecution, ReportTemplate
from .services import ReportGenerator, ReportExporter
import logging

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def generate_scheduled_report(self, schedule_id):
    """
    Generate a scheduled report
    
    Args:
        schedule_id (int): ID of the ReportSchedule instance
    """
    try:
        schedule = ReportSchedule.objects.get(id=schedule_id)
        
        # Generate the report
        generator = ReportGenerator()
        report_result = generator.generate_report(
            schedule.report_template,
            schedule.parameters
        )
        
        if report_result['success']:
            # Create execution record
            execution = ReportExecution.objects.create(
                template=schedule.report_template,
                executed_by=schedule.created_by,
                parameters_used=schedule.parameters,
                execution_time=timezone.timedelta(seconds=report_result['execution_time']),
                row_count=report_result['row_count'],
                school=schedule.school
            )
            
            # Export report if needed
            if schedule.delivery_method in ['email', 'download']:
                exporter = ReportExporter()
                export_result = exporter.export_report(
                    report_result,
                    'pdf',  # Default format
                    schedule.report_template.layout_config
                )
                
                if export_result['success']:
                    execution.file_path = export_result.get('file_path')
                    execution.save()
            
            # Deliver report
            if schedule.delivery_method == 'email':
                send_report_email.delay(schedule.id, execution.id)
            
            # Update schedule for next run
            schedule.last_run = timezone.now()
            if schedule.frequency != 'once':
                from .services import ReportScheduler
                scheduler = ReportScheduler()
                scheduler.schedule_report(schedule)
            else:
                schedule.is_active = False
                schedule.save()
            
            logger.info(f"Scheduled report {schedule.name} generated successfully")
            return f"Report generated successfully: {execution.id}"
            
        else:
            logger.error(f"Scheduled report generation failed: {report_result.get('error')}")
            raise Exception(report_result.get('error', 'Unknown error'))
            
    except ReportSchedule.DoesNotExist:
        logger.error(f"Report schedule {schedule_id} not found")
        raise Exception(f"Report schedule {schedule_id} not found")
        
    except Exception as e:
        logger.error(f"Scheduled report generation failed: {e}")
        
        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2 ** self.request.retries))
        
        raise e


@shared_task
def send_report_email(schedule_id, execution_id):
    """
    Send report via email
    
    Args:
        schedule_id (int): ID of the ReportSchedule instance
        execution_id (int): ID of the ReportExecution instance
    """
    try:
        schedule = ReportSchedule.objects.get(id=schedule_id)
        execution = ReportExecution.objects.get(id=execution_id)
        
        # Prepare email content
        subject = f"Scheduled Report: {schedule.report_template.name}"
        
        context = {
            'schedule': schedule,
            'execution': execution,
            'report_template': schedule.report_template,
        }
        
        # Render email template
        html_message = render_to_string('reports/emails/scheduled_report.html', context)
        plain_message = render_to_string('reports/emails/scheduled_report.txt', context)
        
        # Send email to recipients
        recipients = schedule.recipients
        if isinstance(recipients, str):
            recipients = [recipients]
        
        attachments = []
        if execution.file_path:
            attachments.append(execution.file_path)
        
        for recipient in recipients:
            send_mail(
                subject=subject,
                message=plain_message,
                html_message=html_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[recipient],
                fail_silently=False,
                # attachments=attachments  # Uncomment when file handling is implemented
            )
        
        logger.info(f"Report email sent successfully to {len(recipients)} recipients")
        return f"Email sent to {len(recipients)} recipients"
        
    except Exception as e:
        logger.error(f"Failed to send report email: {e}")
        raise e


@shared_task
def generate_report_async(template_id, parameters=None, user_id=None):
    """
    Generate report asynchronously
    
    Args:
        template_id (int): ID of the ReportTemplate instance
        parameters (dict): Report parameters
        user_id (int): ID of the user requesting the report
    """
    try:
        template = ReportTemplate.objects.get(id=template_id)
        
        # Generate the report
        generator = ReportGenerator()
        report_result = generator.generate_report(template, parameters)
        
        if report_result['success']:
            # Create execution record
            from django.contrib.auth import get_user_model
            User = get_user_model()
            
            executed_by = None
            if user_id:
                try:
                    executed_by = User.objects.get(id=user_id)
                except User.DoesNotExist:
                    pass
            
            execution = ReportExecution.objects.create(
                template=template,
                executed_by=executed_by,
                parameters_used=parameters or {},
                execution_time=timezone.timedelta(seconds=report_result['execution_time']),
                row_count=report_result['row_count'],
                school=template.school
            )
            
            logger.info(f"Async report {template.name} generated successfully")
            return {
                'success': True,
                'execution_id': execution.id,
                'row_count': report_result['row_count'],
                'execution_time': report_result['execution_time']
            }
            
        else:
            logger.error(f"Async report generation failed: {report_result.get('error')}")
            return {
                'success': False,
                'error': report_result.get('error', 'Unknown error')
            }
            
    except ReportTemplate.DoesNotExist:
        logger.error(f"Report template {template_id} not found")
        return {
            'success': False,
            'error': f"Report template {template_id} not found"
        }
        
    except Exception as e:
        logger.error(f"Async report generation failed: {e}")
        return {
            'success': False,
            'error': str(e)
        }


@shared_task
def export_report_async(execution_id, format_type, template_config=None):
    """
    Export report asynchronously
    
    Args:
        execution_id (int): ID of the ReportExecution instance
        format_type (str): Export format
        template_config (dict): Template configuration
    """
    try:
        execution = ReportExecution.objects.get(id=execution_id)
        
        # Get report data (this would need to be stored or regenerated)
        generator = ReportGenerator()
        report_result = generator.generate_report(
            execution.template,
            execution.parameters_used
        )
        
        if report_result['success']:
            # Export the report
            exporter = ReportExporter()
            export_result = exporter.export_report(
                report_result,
                format_type,
                template_config
            )
            
            if export_result['success']:
                # Update execution with file path
                execution.file_path = export_result.get('file_path')
                execution.save()
                
                logger.info(f"Report export completed: {format_type}")
                return {
                    'success': True,
                    'format': format_type,
                    'file_path': export_result.get('file_path'),
                    'content': export_result.get('content')
                }
            else:
                logger.error(f"Report export failed: {export_result.get('error')}")
                return {
                    'success': False,
                    'error': export_result.get('error', 'Export failed')
                }
        else:
            logger.error(f"Report regeneration failed: {report_result.get('error')}")
            return {
                'success': False,
                'error': report_result.get('error', 'Report regeneration failed')
            }
            
    except ReportExecution.DoesNotExist:
        logger.error(f"Report execution {execution_id} not found")
        return {
            'success': False,
            'error': f"Report execution {execution_id} not found"
        }
        
    except Exception as e:
        logger.error(f"Async report export failed: {e}")
        return {
            'success': False,
            'error': str(e)
        }


@shared_task
def cleanup_old_reports():
    """
    Clean up old report executions and files
    """
    try:
        # Delete executions older than 90 days
        cutoff_date = timezone.now() - timezone.timedelta(days=90)
        old_executions = ReportExecution.objects.filter(created_at__lt=cutoff_date)
        
        deleted_count = 0
        for execution in old_executions:
            # Delete associated files
            if execution.file_path:
                try:
                    import os
                    if os.path.exists(execution.file_path):
                        os.remove(execution.file_path)
                except Exception as e:
                    logger.warning(f"Could not delete file {execution.file_path}: {e}")
            
            execution.delete()
            deleted_count += 1
        
        logger.info(f"Cleaned up {deleted_count} old report executions")
        return f"Cleaned up {deleted_count} old report executions"
        
    except Exception as e:
        logger.error(f"Report cleanup failed: {e}")
        raise e


@shared_task
def validate_scheduled_reports():
    """
    Validate and fix scheduled reports
    """
    try:
        # Find schedules that should have run but didn't
        now = timezone.now()
        overdue_schedules = ReportSchedule.objects.filter(
            is_active=True,
            next_run__lt=now - timezone.timedelta(hours=1)
        )
        
        fixed_count = 0
        for schedule in overdue_schedules:
            try:
                # Reschedule the report
                from .services import ReportScheduler
                scheduler = ReportScheduler()
                if scheduler.schedule_report(schedule):
                    fixed_count += 1
                    logger.info(f"Rescheduled overdue report: {schedule.name}")
            except Exception as e:
                logger.error(f"Could not reschedule report {schedule.name}: {e}")
        
        logger.info(f"Fixed {fixed_count} overdue scheduled reports")
        return f"Fixed {fixed_count} overdue scheduled reports"
        
    except Exception as e:
        logger.error(f"Scheduled report validation failed: {e}")
        raise e