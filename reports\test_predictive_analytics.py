"""
Test cases for Predictive Analytics functionality
"""

import unittest
from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from unittest.mock import patch, MagicMock
from .predictive_analytics import PredictiveAnalyticsService


class PredictiveAnalyticsServiceTest(TestCase):
    """Test cases for PredictiveAnalyticsService"""
    
    def setUp(self):
        self.service = PredictiveAnalyticsService()
    
    def test_service_initialization(self):
        """Test that the service initializes correctly"""
        self.assertIsInstance(self.service, PredictiveAnalyticsService)
        self.assertEqual(self.service.cache_timeout, 3600)
        self.assertIn('linear', self.service.model_types)
        self.assertIn('random_forest', self.service.model_types)
    
    @patch('reports.predictive_analytics.connection')
    def test_predict_student_enrollment_insufficient_data(self, mock_connection):
        """Test enrollment prediction with insufficient data"""
        # Mock database response with insufficient data
        mock_cursor = MagicMock()
        mock_cursor.fetchall.return_value = [
            ('2024-01', 50, 0.85),
            ('2024-02', 55, 0.87)
        ]
        mock_connection.cursor.return_value.__enter__.return_value = mock_cursor
        
        result = self.service.predict_student_enrollment(6)
        
        self.assertFalse(result['success'])
        self.assertIn('Insufficient historical data', result['error'])
    
    @patch('reports.predictive_analytics.connection')
    def test_predict_student_enrollment_success(self, mock_connection):
        """Test successful enrollment prediction"""
        # Mock database response with sufficient data
        mock_data = []
        for i in range(15):  # 15 months of data
            mock_data.append((f'2023-{i+1:02d}', 100 + i*5, 0.85 + i*0.01))
        
        mock_cursor = MagicMock()
        mock_cursor.fetchall.return_value = mock_data
        mock_connection.cursor.return_value.__enter__.return_value = mock_cursor
        
        result = self.service.predict_student_enrollment(3)
        
        self.assertTrue(result['success'])
        self.assertIn('predictions', result)
        self.assertIn('model_metrics', result)
        self.assertEqual(len(result['predictions']), 3)
    
    @patch('reports.predictive_analytics.connection')
    def test_predict_revenue_trends_success(self, mock_connection):
        """Test successful revenue prediction"""
        # Mock database response
        mock_data = []
        for i in range(15):
            mock_data.append((f'2023-{i+1:02d}', 10000 + i*1000, 100 + i*5, 100))
        
        mock_cursor = MagicMock()
        mock_cursor.fetchall.return_value = mock_data
        mock_connection.cursor.return_value.__enter__.return_value = mock_cursor
        
        result = self.service.predict_revenue_trends(3)
        
        self.assertTrue(result['success'])
        self.assertIn('predictions', result)
        self.assertIn('trends', result)
        self.assertEqual(len(result['predictions']), 3)
    
    @patch('reports.predictive_analytics.connection')
    def test_generate_predictive_alerts(self, mock_connection):
        """Test predictive alerts generation"""
        # Mock database responses for all data types
        mock_cursor = MagicMock()
        mock_cursor.fetchall.side_effect = [
            # Enrollment data
            [(f'2023-{i+1:02d}', 100 + i*5, 0.85) for i in range(15)],
            # Revenue data  
            [(f'2023-{i+1:02d}', 10000 + i*1000, 100, 100) for i in range(15)],
            # Performance data
            [(1, 1, 85, 85.0, '2024-01-01', 1, 1) for _ in range(20)],
            # Attendance data
            [('2024-01-01', 100, 85, 10, 5, 1, 1, 2024) for _ in range(50)]
        ]
        mock_connection.cursor.return_value.__enter__.return_value = mock_cursor
        
        result = self.service.generate_predictive_alerts()
        
        self.assertTrue(result['success'])
        self.assertIn('alerts', result)
        self.assertIn('total_alerts', result)
    
    def test_analyze_trends_invalid_type(self):
        """Test trend analysis with invalid data type"""
        result = self.service.analyze_trends('invalid_type')
        
        # Should still succeed but with empty trends
        self.assertTrue(result['success'])
        self.assertIn('trends', result)


class PredictiveAnalyticsViewTest(TestCase):
    """Test cases for Predictive Analytics views"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.client.login(username='testuser', password='testpass123')
    
    @patch('reports.views.PredictiveAnalyticsService')
    def test_predictions_view_success(self, mock_service_class):
        """Test predictions view with successful data"""
        # Mock service responses
        mock_service = MagicMock()
        mock_service.predict_student_enrollment.return_value = {
            'success': True,
            'predictions': [{'month': '2024-01', 'predicted_enrollment': 100}]
        }
        mock_service.predict_revenue_trends.return_value = {
            'success': True,
            'predictions': [{'month': '2024-01', 'predicted_revenue': 10000}],
            'trends': {'trend': 'increasing', 'growth_rate': 5.0}
        }
        mock_service.predict_student_performance.return_value = {
            'success': True,
            'at_risk_students': [],
            'recommendations': ['Monitor progress closely']
        }
        mock_service.predict_attendance_patterns.return_value = {
            'success': True,
            'predictions': [{'date': '2024-01-01', 'predicted_attendance_rate': 85}],
            'patterns': {'seasonal_trends': {'highest_month': 1}}
        }
        mock_service.generate_predictive_alerts.return_value = {
            'success': True,
            'alerts': []
        }
        mock_service_class.return_value = mock_service
        
        url = reverse('reports:predictions')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Predictive Analytics')
        self.assertIn('enrollment_prediction', response.context)
        self.assertIn('revenue_prediction', response.context)
    
    @patch('reports.views.PredictiveAnalyticsService')
    def test_predictions_view_error_handling(self, mock_service_class):
        """Test predictions view error handling"""
        # Mock service to raise exception
        mock_service = MagicMock()
        mock_service.predict_student_enrollment.side_effect = Exception("Test error")
        mock_service_class.return_value = mock_service
        
        url = reverse('reports:predictions')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('error', response.context)
    
    def test_predictions_view_requires_login(self):
        """Test that predictions view requires authentication"""
        self.client.logout()
        url = reverse('reports:predictions')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 302)  # Redirect to login


class PredictiveAnalyticsAPITest(TestCase):
    """Test cases for Predictive Analytics API"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.client.login(username='testuser', password='testpass123')
    
    @patch('reports.views.PredictiveAnalyticsService')
    def test_api_predict_enrollment(self, mock_service_class):
        """Test API enrollment prediction endpoint"""
        mock_service = MagicMock()
        mock_service.predict_student_enrollment.return_value = {
            'success': True,
            'predictions': [{'month': '2024-01', 'predicted_enrollment': 100}]
        }
        mock_service_class.return_value = mock_service
        
        url = reverse('reports:predictive_api')
        data = {
            'action': 'predict_enrollment',
            'months_ahead': 6
        }
        
        response = self.client.post(
            url,
            data=data,
            content_type='application/json',
            HTTP_X_REQUESTED_WITH='XMLHttpRequest'
        )
        
        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertTrue(response_data['success'])
        self.assertIn('data', response_data)
    
    @patch('reports.views.PredictiveAnalyticsService')
    def test_api_get_alerts(self, mock_service_class):
        """Test API alerts endpoint"""
        mock_service = MagicMock()
        mock_service.generate_predictive_alerts.return_value = {
            'success': True,
            'alerts': [{'type': 'test_alert', 'message': 'Test alert'}]
        }
        mock_service_class.return_value = mock_service
        
        url = reverse('reports:predictive_api')
        data = {'action': 'get_alerts'}
        
        response = self.client.post(
            url,
            data=data,
            content_type='application/json',
            HTTP_X_REQUESTED_WITH='XMLHttpRequest'
        )
        
        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertTrue(response_data['success'])
    
    def test_api_invalid_action(self):
        """Test API with invalid action"""
        url = reverse('reports:predictive_api')
        data = {'action': 'invalid_action'}
        
        response = self.client.post(
            url,
            data=data,
            content_type='application/json',
            HTTP_X_REQUESTED_WITH='XMLHttpRequest'
        )
        
        self.assertEqual(response.status_code, 400)
        response_data = response.json()
        self.assertFalse(response_data['success'])
        self.assertEqual(response_data['error'], 'Invalid action')
    
    def test_api_requires_ajax(self):
        """Test that API requires AJAX request"""
        url = reverse('reports:predictive_api')
        data = {'action': 'predict_enrollment'}
        
        response = self.client.post(url, data=data)
        
        self.assertEqual(response.status_code, 400)
        response_data = response.json()
        self.assertFalse(response_data['success'])
        self.assertEqual(response_data['error'], 'Invalid request')


if __name__ == '__main__':
    unittest.main()