"""
Tests for Report Builder functionality
"""

import json
from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils import timezone
from datetime import date
from core.models import School, AcademicYear
from hr.models import Employee
from .models import ReportBuilder, ReportTemplate, ReportSchedule, ReportShare
from .services import QueryBuilder, ReportGenerator, ReportScheduler
from .forms import ReportBuilderForm, ReportTemplateForm


class ReportBuilderModelTest(TestCase):
    """Test ReportBuilder model"""
    
    def setUp(self):
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="Test Address",
            phone="1234567890",
            email="<EMAIL>"
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
        
        self.employee = Employee.objects.create(
            school=self.school,
            employee_id="EMP001",
            first_name="Test",
            last_name="User",
            email="<EMAIL>",
            phone="1234567890",
            hire_date=timezone.now().date(),
            department="IT",
            position="Developer",
            salary=50000,
            user=self.user
        )
    
    def test_report_builder_creation(self):
        """Test creating a report builder"""
        report_builder = ReportBuilder.objects.create(
            school=self.school,
            name="Test Report Builder",
            description="Test description",
            created_by=self.user,
            report_config={"test": "config"},
            query_config={"fields": []},
            layout_config={"theme": "default"}
        )
        
        self.assertEqual(report_builder.name, "Test Report Builder")
        self.assertEqual(report_builder.school, self.school)
        self.assertEqual(report_builder.created_by, self.user)
        self.assertFalse(report_builder.is_public)
        self.assertFalse(report_builder.is_template)
    
    def test_report_builder_str(self):
        """Test string representation"""
        report_builder = ReportBuilder.objects.create(
            school=self.school,
            name="Test Builder",
            created_by=self.user
        )
        
        self.assertEqual(str(report_builder), "Test Builder")


class ReportScheduleModelTest(TestCase):
    """Test ReportSchedule model"""
    
    def setUp(self):
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="Test Address",
            phone="1234567890",
            email="<EMAIL>"
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
        
        self.report_template = ReportTemplate.objects.create(
            school=self.school,
            name="Test Template",
            report_type="student",
            query="SELECT * FROM students_student",
            created_by=self.user
        )
    
    def test_report_schedule_creation(self):
        """Test creating a report schedule"""
        schedule = ReportSchedule.objects.create(
            school=self.school,
            report_template=self.report_template,
            name="Daily Student Report",
            frequency="daily",
            delivery_method="email",
            recipients=["<EMAIL>"],
            created_by=self.user
        )
        
        self.assertEqual(schedule.name, "Daily Student Report")
        self.assertEqual(schedule.frequency, "daily")
        self.assertEqual(schedule.delivery_method, "email")
        self.assertTrue(schedule.is_active)


class QueryBuilderServiceTest(TestCase):
    """Test QueryBuilder service"""
    
    def setUp(self):
        self.query_builder = QueryBuilder()
    
    def test_get_available_models(self):
        """Test getting available models"""
        models = self.query_builder.available_models
        
        self.assertIsInstance(models, dict)
        self.assertGreater(len(models), 0)
        
        # Check if core models are included
        self.assertIn('core.School', models)
        
        # Check model structure
        if 'core.School' in models:
            school_model = models['core.School']
            self.assertIn('name', school_model)
            self.assertIn('fields', school_model)
            self.assertIn('relationships', school_model)
    
    def test_build_simple_query(self):
        """Test building a simple query"""
        query_config = {
            'fields': [
                {
                    'table_alias': 't1',
                    'field_name': 'name',
                    'alias': 'school_name'
                }
            ],
            'data_sources': [
                {
                    'model': 'core.School',
                    'alias': 't1'
                }
            ],
            'filters': [],
            'joins': [],
            'grouping': [],
            'sorting': [],
            'aggregations': []
        }
        
        result = self.query_builder.build_query(query_config)
        
        self.assertIn('sql', result)
        self.assertIn('SELECT', result['sql'])
        self.assertIn('FROM', result['sql'])
    
    def test_validate_query_config(self):
        """Test query configuration validation"""
        # Valid configuration
        valid_config = {
            'data_sources': [{'model': 'core.School'}],
            'fields': [{'field_name': 'name'}]
        }
        
        result = self.query_builder.validate_query_config(valid_config)
        self.assertTrue(result['valid'])
        
        # Invalid configuration (no data sources)
        invalid_config = {
            'fields': [{'field_name': 'name'}]
        }
        
        result = self.query_builder.validate_query_config(invalid_config)
        self.assertFalse(result['valid'])
        self.assertGreater(len(result['errors']), 0)


class ReportBuilderViewTest(TestCase):
    """Test ReportBuilder views"""
    
    def setUp(self):
        self.client = Client()
        
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="Test Address",
            phone="1234567890",
            email="<EMAIL>"
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
        
        self.employee = Employee.objects.create(
            school=self.school,
            employee_id="EMP001",
            first_name="Test",
            last_name="User",
            email="<EMAIL>",
            phone="1234567890",
            hire_date=timezone.now().date(),
            department="IT",
            position="Developer",
            salary=50000,
            user=self.user
        )
        
        self.client.login(username="testuser", password="testpass123")
    
    def test_report_builder_list_view(self):
        """Test report builder list view"""
        url = reverse('reports:report_builder')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Report Builder")
    
    def test_report_builder_create_view_get(self):
        """Test report builder create view GET"""
        url = reverse('reports:report_builder_create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Create Report Builder")
    
    def test_report_builder_create_view_post(self):
        """Test report builder create view POST"""
        url = reverse('reports:report_builder_create')
        data = {
            'name': 'Test Report Builder',
            'description': 'Test description',
            'is_public': False,
            'is_template': False
        }
        
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, 302)  # Redirect after successful creation
        
        # Check if report builder was created
        self.assertTrue(
            ReportBuilder.objects.filter(name='Test Report Builder').exists()
        )
    
    def test_query_builder_api_view(self):
        """Test query builder API view"""
        url = reverse('reports:query_builder_api')
        
        # Test get models action
        data = {
            'action': 'get_models'
        }
        
        response = self.client.post(
            url,
            json.dumps(data),
            content_type='application/json',
            HTTP_X_REQUESTED_WITH='XMLHttpRequest'
        )
        
        self.assertEqual(response.status_code, 200)
        
        response_data = json.loads(response.content)
        self.assertTrue(response_data['success'])
        self.assertIn('models', response_data)


class ReportBuilderFormTest(TestCase):
    """Test ReportBuilder forms"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
    
    def test_report_builder_form_valid(self):
        """Test valid report builder form"""
        form_data = {
            'name': 'Test Report Builder',
            'description': 'Test description',
            'is_public': False,
            'is_template': False
        }
        
        form = ReportBuilderForm(data=form_data, user=self.user)
        self.assertTrue(form.is_valid())
    
    def test_report_builder_form_invalid(self):
        """Test invalid report builder form"""
        form_data = {
            'description': 'Test description',
            'is_public': False,
            'is_template': False
        }  # Missing required 'name' field
        
        form = ReportBuilderForm(data=form_data, user=self.user)
        self.assertFalse(form.is_valid())
        self.assertIn('name', form.errors)


class ReportGeneratorServiceTest(TestCase):
    """Test ReportGenerator service"""
    
    def setUp(self):
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="Test Address",
            phone="1234567890",
            email="<EMAIL>"
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
        
        self.report_template = ReportTemplate.objects.create(
            school=self.school,
            name="Test Template",
            report_type="student",
            query="SELECT 'Test' as name, 1 as id",  # Simple test query
            created_by=self.user
        )
        
        self.generator = ReportGenerator()
    
    def test_generate_report(self):
        """Test report generation"""
        result = self.generator.generate_report(self.report_template)
        
        self.assertIn('success', result)
        self.assertIn('data', result)
        self.assertIn('columns', result)
        self.assertIn('row_count', result)
        self.assertIn('execution_time', result)


class ReportSchedulerServiceTest(TestCase):
    """Test ReportScheduler service"""
    
    def setUp(self):
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="Test Address",
            phone="1234567890",
            email="<EMAIL>"
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
        
        self.report_template = ReportTemplate.objects.create(
            school=self.school,
            name="Test Template",
            report_type="student",
            query="SELECT 'Test' as name",
            created_by=self.user
        )
        
        self.scheduler = ReportScheduler()
    
    def test_calculate_next_run_daily(self):
        """Test calculating next run time for daily frequency"""
        schedule = ReportSchedule.objects.create(
            school=self.school,
            report_template=self.report_template,
            name="Daily Report",
            frequency="daily",
            schedule_config={"time": "09:00"},
            created_by=self.user
        )
        
        next_run = self.scheduler._calculate_next_run(schedule)
        
        self.assertIsNotNone(next_run)
        self.assertGreater(next_run, timezone.now())
    
    def test_calculate_next_run_weekly(self):
        """Test calculating next run time for weekly frequency"""
        schedule = ReportSchedule.objects.create(
            school=self.school,
            report_template=self.report_template,
            name="Weekly Report",
            frequency="weekly",
            schedule_config={"time": "09:00", "day_of_week": 1},  # Monday
            created_by=self.user
        )
        
        next_run = self.scheduler._calculate_next_run(schedule)
        
        self.assertIsNotNone(next_run)
        self.assertGreater(next_run, timezone.now())


class ReportIntegrationTest(TestCase):
    """Integration tests for the complete report building workflow"""
    
    def setUp(self):
        self.client = Client()
        
        self.school = School.objects.create(
            name="Test School",
            code="TEST",
            address="Test Address",
            phone="1234567890",
            email="<EMAIL>"
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
        
        self.employee = Employee.objects.create(
            school=self.school,
            employee_id="EMP001",
            first_name="Test",
            last_name="User",
            email="<EMAIL>",
            phone="1234567890",
            hire_date=timezone.now().date(),
            department="IT",
            position="Developer",
            salary=50000,
            user=self.user
        )
        
        self.client.login(username="testuser", password="testpass123")
    
    def test_complete_report_building_workflow(self):
        """Test the complete workflow from creating builder to generating report"""
        
        # Step 1: Create report builder
        builder_data = {
            'name': 'Integration Test Builder',
            'description': 'Test builder for integration test',
            'is_public': False,
            'is_template': False
        }
        
        response = self.client.post(
            reverse('reports:report_builder_create'),
            builder_data
        )
        self.assertEqual(response.status_code, 302)
        
        # Get the created builder
        builder = ReportBuilder.objects.get(name='Integration Test Builder')
        
        # Step 2: Configure the builder (simulate designer interaction)
        config_data = {
            'report_config': {'name': 'Test Report'},
            'query_config': {
                'fields': [
                    {
                        'table_alias': 't1',
                        'field_name': 'name',
                        'alias': 'school_name'
                    }
                ],
                'data_sources': [
                    {
                        'model': 'core.School',
                        'alias': 't1'
                    }
                ],
                'filters': [],
                'joins': [],
                'grouping': [],
                'sorting': [],
                'aggregations': []
            },
            'layout_config': {'theme': 'default'}
        }
        
        response = self.client.post(
            reverse('reports:report_builder_designer', kwargs={'pk': builder.pk}),
            json.dumps(config_data),
            content_type='application/json',
            HTTP_X_REQUESTED_WITH='XMLHttpRequest'
        )
        
        self.assertEqual(response.status_code, 200)
        
        # Step 3: Create report template from builder
        template_data = {
            'name': 'Integration Test Template',
            'report_type': 'custom',
            'description': 'Test template',
            'report_builder': builder.pk,
            'is_public': False
        }
        
        response = self.client.post(
            reverse('reports:report_template_create'),
            template_data
        )
        self.assertEqual(response.status_code, 302)
        
        # Verify template was created
        self.assertTrue(
            ReportTemplate.objects.filter(name='Integration Test Template').exists()
        )
        
        template = ReportTemplate.objects.get(name='Integration Test Template')
        
        # Step 4: Generate report from template
        response = self.client.get(
            reverse('reports:report_generate', kwargs={'pk': template.pk})
        )
        self.assertEqual(response.status_code, 200)
        
        # Test report generation (POST)
        response = self.client.post(
            reverse('reports:report_generate', kwargs={'pk': template.pk})
        )
        
        # Should redirect to report view or show success
        self.assertIn(response.status_code, [200, 302])