from django.urls import path
from . import views

app_name = 'reports'

urlpatterns = [
    # Reports Dashboard
    path('', views.ReportsDashboardView.as_view(), name='dashboard'),

    # Student Reports
    path('students/', views.StudentReportsView.as_view(), name='students'),
    path('students/classes/', views.ClassesReportView.as_view(), name='classes_report'),
    path('students/login-data/', views.UsersLoginDataView.as_view(), name='login_data'),
    path('students/employee-sons/', views.StudentEmployeeSonsReportView.as_view(), name='employee_sons'),
    path('students/brothers/', views.BrotherReportView.as_view(), name='brothers'),
    path('students/attendance/', views.StudentAttendanceReportView.as_view(), name='student_attendance'),
    path('students/performance/', views.StudentPerformanceReportView.as_view(), name='student_performance'),
    path('students/enrollment/', views.EnrollmentReportView.as_view(), name='enrollment'),
    path('students/demographics/', views.DemographicsReportView.as_view(), name='demographics'),

    # Financial Reports
    path('financial/', views.FinancialReportsView.as_view(), name='financial'),
    path('financial/revenue/', views.RevenueReportView.as_view(), name='revenue'),
    path('financial/expenses/', views.ExpensesReportView.as_view(), name='expenses'),
    path('financial/profit-loss/', views.ProfitLossReportView.as_view(), name='profit_loss'),
    path('financial/cash-flow/', views.CashFlowReportView.as_view(), name='cash_flow'),
    path('financial/budget/', views.BudgetReportView.as_view(), name='budget'),
    path('financial/fees-collection/', views.FeesCollectionReportView.as_view(), name='fees_collection'),
    path('financial/outstanding-fees/', views.OutstandingFeesReportView.as_view(), name='outstanding_fees'),
    path('financial/payment-history/', views.PaymentHistoryReportView.as_view(), name='payment_history'),

    # Academic Reports
    path('academic/', views.AcademicReportsView.as_view(), name='academic'),
    path('academic/grades/', views.GradesReportView.as_view(), name='grades'),
    path('academic/exam-results/', views.ExamResultsReportView.as_view(), name='exam_results'),
    path('academic/subject-performance/', views.SubjectPerformanceReportView.as_view(), name='subject_performance'),
    path('academic/teacher-performance/', views.TeacherPerformanceReportView.as_view(), name='teacher_performance'),
    path('academic/class-performance/', views.ClassPerformanceReportView.as_view(), name='class_performance'),
    path('academic/class-performance/export/', views.ClassPerformanceExportView.as_view(), name='class_performance_export'),
    path('academic/student-performance/', views.StudentPerformanceReportView.as_view(), name='student_performance'),
    path('academic/student-performance/<int:student_id>/', views.StudentPerformanceReportView.as_view(), name='student_performance'),
    path('academic/student-performance/export/', views.StudentPerformanceExportView.as_view(), name='student_performance_export'),
    path('academic/curriculum-progress/', views.CurriculumProgressReportView.as_view(), name='curriculum_progress'),

    # Attendance Reports
    path('attendance/', views.AttendanceReportsView.as_view(), name='attendance'),
    path('attendance/daily/', views.DailyAttendanceReportView.as_view(), name='daily_attendance'),
    path('attendance/monthly/', views.MonthlyAttendanceReportView.as_view(), name='monthly_attendance'),
    path('attendance/class-wise/', views.ClassWiseAttendanceReportView.as_view(), name='class_wise_attendance'),
    path('attendance/student-wise/', views.StudentWiseAttendanceReportView.as_view(), name='student_wise_attendance'),
    path('attendance/absentees/', views.AbsenteesReportView.as_view(), name='absentees'),
    path('attendance/late-arrivals/', views.LateArrivalsReportView.as_view(), name='late_arrivals'),

    # HR Reports
    path('hr/', views.HRReportsView.as_view(), name='hr'),
    path('hr/employee/', views.EmployeeReportsView.as_view(), name='employee'),
    path('hr/payroll/', views.PayrollReportsView.as_view(), name='payroll'),
    path('hr/attendance/', views.EmployeeAttendanceReportView.as_view(), name='employee_attendance'),
    path('hr/leave/', views.LeaveReportView.as_view(), name='leave'),
    path('hr/performance/', views.EmployeePerformanceReportView.as_view(), name='employee_performance'),

    # Custom Reports
    path('custom/', views.CustomReportsView.as_view(), name='custom'),
    path('custom/builder/', views.ReportBuilderView.as_view(), name='report_builder'),
    path('custom/builder/create/', views.ReportBuilderCreateView.as_view(), name='report_builder_create'),
    path('custom/builder/<uuid:pk>/edit/', views.ReportBuilderEditView.as_view(), name='report_builder_edit'),
    path('custom/builder/<uuid:pk>/designer/', views.ReportBuilderDesignerView.as_view(), name='report_builder_designer'),
    path('custom/designer/', views.ReportDesignerView.as_view(), name='report_designer'),
    path('custom/designer/<uuid:template_id>/', views.ReportDesignerView.as_view(), name='report_designer_edit'),
    path('custom/templates/', views.ReportTemplatesView.as_view(), name='report_templates'),
    path('custom/templates/create/', views.ReportTemplateCreateView.as_view(), name='report_template_create'),
    path('custom/templates/<uuid:pk>/edit/', views.ReportTemplateEditView.as_view(), name='report_template_edit'),
    path('custom/templates/<uuid:pk>/delete/', views.ReportTemplateDeleteView.as_view(), name='report_template_delete'),
    path('custom/templates/<uuid:pk>/generate/', views.ReportGenerateView.as_view(), name='report_generate'),
    path('custom/execution/<uuid:execution_id>/', views.ReportViewView.as_view(), name='report_view'),
    path('custom/execution/<uuid:execution_id>/export/', views.ReportExportView.as_view(), name='report_export'),
    path('custom/execution/<uuid:execution_id>/share/', views.ReportShareView.as_view(), name='report_share'),
    
    # Shared reports (public access)
    path('shared/<str:token>/', views.SharedReportDownloadView.as_view(), name='shared_report_download'),
    
    # API endpoints
    path('api/query-builder/', views.QueryBuilderAPIView.as_view(), name='query_builder_api'),

    # Export and Print
    path('export/<str:report_type>/', views.ExportReportView.as_view(), name='export'),
    path('print/<str:report_type>/', views.PrintReportView.as_view(), name='print'),

    # Report Scheduling
    path('schedule/', views.ScheduleReportView.as_view(), name='schedule'),
    path('scheduled/', views.ScheduledReportsView.as_view(), name='scheduled'),

    # Analytics Dashboard
    path('analytics/', views.AnalyticsDashboardView.as_view(), name='analytics'),
    path('analytics/trends/', views.TrendsAnalysisView.as_view(), name='trends'),
    path('analytics/predictions/', views.PredictiveAnalysisView.as_view(), name='predictions'),
    
    # Interactive Dashboards
    path('dashboards/', views.DashboardListView.as_view(), name='dashboard_list'),
    path('dashboards/create/', views.DashboardCreateView.as_view(), name='dashboard_create'),
    path('dashboards/<uuid:pk>/', views.DashboardDetailView.as_view(), name='dashboard_detail'),
    path('dashboards/<uuid:pk>/edit/', views.DashboardEditView.as_view(), name='dashboard_edit'),
    path('dashboards/<uuid:pk>/delete/', views.DashboardDeleteView.as_view(), name='dashboard_delete'),
    
    # Dashboard API endpoints
    path('api/dashboard/', views.DashboardAPIView.as_view(), name='dashboard_api'),
    path('api/widget/<uuid:widget_id>/', views.DashboardWidgetView.as_view(), name='widget_api'),
    
    # Predictive Analytics API
    path('api/predictive/', views.PredictiveAnalyticsAPIView.as_view(), name='predictive_api'),
    
    # Interactive Dashboards
    path('dashboards/', views.InteractiveDashboardListView.as_view(), name='dashboard_list'),
    path('dashboards/create/', views.InteractiveDashboardCreateView.as_view(), name='dashboard_create'),
    path('dashboards/<uuid:pk>/', views.InteractiveDashboardView.as_view(), name='interactive_dashboard_view'),
    
    # Dashboard API endpoints
    path('api/dashboards/', views.DashboardAPIView.as_view(), name='dashboard_api'),
    path('api/widgets/<uuid:widget_id>/', views.WidgetAPIView.as_view(), name='widget_api'),
]
