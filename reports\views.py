from django.shortcuts import render, redirect
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.views.generic import TemplateView, View, CreateView, UpdateView, DeleteView, DetailView, ListView
from django.http import HttpResponse, JsonResponse
from django.db.models import Count, Sum, Avg, Q
from django.utils import timezone
from django.urls import reverse_lazy
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from datetime import datetime, timedelta
import os
import logging
from students.models import Student, Class, Grade
from academics.models import Teacher, Subject, StudentGrade, StudentAttendance
from hr.models import Employee, Department
from finance.models import Payment, Account
from core.models import AcademicYear
from .models import ReportTemplate, ReportBuilder, ReportSchedule, ReportShare, ReportExecution, Dashboard, DashboardWidget, DashboardTheme
from .forms import ReportBuilderForm, ReportTemplateForm, ReportScheduleForm, ReportShareForm
from django.db import models

logger = logging.getLogger(__name__)

def placeholder_view(request):
    return HttpResponse("Reports module - Coming soon!")

# Reports Dashboard
class ReportsDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Basic statistics
        context['total_students'] = Student.objects.filter(is_active=True).count()
        context['total_teachers'] = Teacher.objects.filter(is_active=True).count()
        context['total_employees'] = Employee.objects.filter(is_active=True).count()
        context['total_classes'] = Class.objects.filter(is_active=True).count()

        # Recent report activities
        context['recent_reports'] = []  # Will be implemented with actual report generation tracking

        # Available report categories
        context['report_categories'] = [
            {
                'name': 'Student Reports',
                'icon': 'user-graduate',
                'count': 8,
                'url': 'reports:students'
            },
            {
                'name': 'Academic Reports',
                'icon': 'graduation-cap',
                'count': 6,
                'url': 'reports:academic'
            },
            {
                'name': 'Financial Reports',
                'icon': 'chart-line',
                'count': 5,
                'url': 'reports:financial'
            },
            {
                'name': 'HR Reports',
                'icon': 'users',
                'count': 4,
                'url': 'reports:hr'
            }
        ]

        return context

# Student Reports
class StudentReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/students.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Student statistics by grade
        context['students_by_grade'] = Student.objects.filter(is_active=True).values(
            'current_class__grade__name'
        ).annotate(count=Count('id')).order_by('current_class__grade__level')

        # Student statistics by gender
        context['students_by_gender'] = Student.objects.filter(is_active=True).values(
            'gender'
        ).annotate(count=Count('id'))

        # Recent enrollments
        last_month = timezone.now() - timedelta(days=30)
        context['recent_enrollments'] = Student.objects.filter(
            admission_date__gte=last_month,
            is_active=True
        ).count()

        # Available student reports
        context['available_reports'] = [
            {'name': 'Classes Report', 'url': 'reports:classes_report', 'description': 'Student distribution by classes'},
            {'name': 'Attendance Report', 'url': 'reports:student_attendance', 'description': 'Student attendance analysis'},
            {'name': 'Performance Report', 'url': 'reports:student_performance', 'description': 'Academic performance metrics'},
            {'name': 'Enrollment Report', 'url': 'reports:enrollment', 'description': 'Enrollment trends and statistics'},
            {'name': 'Demographics Report', 'url': 'reports:demographics', 'description': 'Student demographic analysis'},
        ]

        return context

class ClassesReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/classes_report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Classes with student counts
        context['classes_data'] = Class.objects.filter(is_active=True).annotate(
            student_count=Count('students', filter=Q(students__is_active=True))
        ).select_related('grade').order_by('grade__level', 'name')

        # Class capacity utilization
        context['capacity_stats'] = {
            'total_capacity': Class.objects.filter(is_active=True).aggregate(
                total=Sum('max_students')
            )['total'] or 0,
            'current_enrollment': Student.objects.filter(is_active=True).count(),
        }

        return context

class UsersLoginDataView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/login_data.html'

class StudentEmployeeSonsReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/employee_sons.html'

class BrotherReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/brothers.html'

class StudentAttendanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/student_attendance.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get current date for filtering
        today = timezone.now().date()

        # Get date from request or use today
        selected_date = self.request.GET.get('date')
        if selected_date:
            try:
                selected_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
            except ValueError:
                selected_date = today
        else:
            selected_date = today

        context['selected_date'] = selected_date

        # Get class filter from request
        selected_class = self.request.GET.get('class')
        if selected_class:
            try:
                selected_class = Class.objects.get(id=selected_class)
                context['selected_class'] = selected_class
            except (Class.DoesNotExist, ValueError):
                selected_class = None
        else:
            selected_class = None

        # Get all classes for dropdown
        context['classes'] = Class.objects.filter(is_active=True).select_related('grade').order_by('grade__level', 'name')

        # Get attendance records for the selected date
        attendance_query = StudentAttendance.objects.filter(
            date=selected_date,
            is_active=True
        ).select_related(
            'student',
            'student__current_class',
            'student__current_class__grade'
        )

        # Apply class filter if selected
        if selected_class:
            attendance_query = attendance_query.filter(student__current_class=selected_class)

        # Get attendance records
        attendance_records = attendance_query.order_by('student__current_class__grade__level', 'student__current_class__name', 'student__last_name')
        context['attendance_records'] = attendance_records

        # Calculate attendance statistics
        total_records = attendance_records.count()
        context['present_count'] = attendance_records.filter(status='present').count()
        context['absent_count'] = attendance_records.filter(status='absent').count()
        context['late_count'] = attendance_records.filter(status='late').count()
        context['excused_count'] = attendance_records.filter(status='excused').count()

        # Calculate percentages
        if total_records > 0:
            context['present_percentage'] = round((context['present_count'] / total_records) * 100, 1)
            context['absent_percentage'] = round((context['absent_count'] / total_records) * 100, 1)
            context['late_percentage'] = round((context['late_count'] / total_records) * 100, 1)
            context['excused_percentage'] = round((context['excused_count'] / total_records) * 100, 1)
        else:
            context['present_percentage'] = 0
            context['absent_percentage'] = 0
            context['late_percentage'] = 0
            context['excused_percentage'] = 0

        # Get attendance trends for the last 5 school days
        last_5_days = []
        current_day = selected_date
        days_checked = 0
        day_names = {
            0: 'الإثنين',
            1: 'الثلاثاء',
            2: 'الأربعاء',
            3: 'الخميس',
            4: 'الجمعة',
            5: 'السبت',
            6: 'الأحد'
        }

        # Get 5 school days (excluding weekends)
        while len(last_5_days) < 5 and days_checked < 10:
            # Skip weekends (assuming Friday and Saturday are weekends)
            if current_day.weekday() not in [4, 5]:  # 4=Friday, 5=Saturday
                last_5_days.append(current_day)
            current_day = current_day - timedelta(days=1)
            days_checked += 1

        # Get attendance data for each day
        attendance_trends = []
        for day in last_5_days:
            day_records = StudentAttendance.objects.filter(date=day)
            total_day_records = day_records.count()

            if total_day_records > 0:
                present_rate = round((day_records.filter(status='present').count() / total_day_records) * 100, 1)
                absent_rate = round((day_records.filter(status='absent').count() / total_day_records) * 100, 1)
            else:
                present_rate = 0
                absent_rate = 0

            attendance_trends.append({
                'date': day,
                'day_name': day_names[day.weekday()],
                'present_rate': present_rate,
                'absent_rate': absent_rate
            })

        # Reverse to show in chronological order
        attendance_trends.reverse()
        context['attendance_trends'] = attendance_trends

        # Get best attendance class
        if context['classes'].exists():
            best_class = None
            best_attendance_rate = 0

            for class_obj in context['classes']:
                class_records = StudentAttendance.objects.filter(
                    date=selected_date,
                    student__current_class=class_obj
                )
                total_class_records = class_records.count()

                if total_class_records > 0:
                    class_present_rate = (class_records.filter(status='present').count() / total_class_records) * 100
                    if class_present_rate > best_attendance_rate:
                        best_attendance_rate = class_present_rate
                        best_class = class_obj

            if best_class:
                context['best_class'] = best_class
                context['best_class_rate'] = round(best_attendance_rate, 1)

        # Calculate average late minutes
        late_records = attendance_records.filter(status='late')
        if late_records.exists() and hasattr(late_records.first(), 'late_minutes'):
            total_late_minutes = sum(record.late_minutes or 0 for record in late_records)
            context['avg_late_minutes'] = round(total_late_minutes / late_records.count(), 1)
        else:
            context['avg_late_minutes'] = 0

        return context

class StudentPerformanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/student_performance.html'

class EnrollmentReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/enrollment.html'

class DemographicsReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/demographics.html'

# Financial Reports
class FinancialReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/financial.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get current month data
        current_month = timezone.now().replace(day=1)
        next_month = (current_month + timedelta(days=32)).replace(day=1)

        # Calculate financial metrics
        from finance.models import Payment, JournalEntry, StudentFee, Invoice

        # Total revenue this month
        context['total_revenue'] = Payment.objects.filter(
            payment_date__gte=current_month,
            payment_date__lt=next_month
        ).aggregate(total=Sum('amount'))['total'] or 0

        # Total expenses this month
        context['total_expenses'] = JournalEntry.objects.filter(
            entry_date__gte=current_month,
            entry_date__lt=next_month,
            debit_amount__gt=0,
            account__account_type__type='expense'
        ).aggregate(total=Sum('debit_amount'))['total'] or 0

        # Net profit
        context['net_profit'] = context['total_revenue'] - context['total_expenses']

        # Outstanding fees
        context['outstanding_fees'] = StudentFee.objects.filter(
            is_paid=False
        ).aggregate(
            total=Sum('amount') - Sum('discount_amount')
        )['total'] or 0

        # Recent transactions (last 10)
        recent_payments = Payment.objects.select_related('student').order_by('-payment_date')[:5]
        recent_expenses = JournalEntry.objects.filter(
            debit_amount__gt=0
        ).select_related('account').order_by('-entry_date')[:5]

        # Combine and sort transactions
        transactions = []

        for payment in recent_payments:
            transactions.append({
                'date': payment.payment_date,
                'description': f"رسوم دراسية - {payment.student.first_name} {payment.student.last_name}" if payment.student else "دفعة مالية",
                'type': 'revenue',
                'amount': payment.amount,
                'status': 'completed',
                'id': payment.id,
                'model': 'payment'
            })

        for expense in recent_expenses:
            transactions.append({
                'date': expense.entry_date,
                'description': expense.description or f"مصروف - {expense.account.name}",
                'type': 'expense',
                'amount': expense.debit_amount,
                'status': 'completed' if expense.is_posted else 'pending',
                'id': expense.id,
                'model': 'expense'
            })

        # Sort by date (most recent first)
        transactions.sort(key=lambda x: x['date'], reverse=True)
        context['recent_transactions'] = transactions[:10]

        return context

class RevenueReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/revenue.html'

class ExpensesReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/expenses.html'

class ProfitLossReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/profit_loss.html'

class CashFlowReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/cash_flow.html'

class BudgetReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/budget.html'

class FeesCollectionReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/fees_collection.html'

class OutstandingFeesReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/outstanding_fees.html'

class PaymentHistoryReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/payment_history.html'

# Academic Reports
class AcademicReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/academic.html'

class GradesReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/grades.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get current academic year
        current_year = AcademicYear.objects.filter(is_current=True).first()

        # Get all student grades for current academic year
        # Filter by exam date within current academic year
        if current_year:
            student_grades = StudentGrade.objects.filter(
                exam__exam_date__gte=current_year.start_date,
                exam__exam_date__lte=current_year.end_date,
                is_active=True
            ).select_related(
                'student',
                'student__current_class',
                'student__current_class__grade',
                'exam__class_subject__subject'
            )
        else:
            # If no current academic year, get recent grades
            student_grades = StudentGrade.objects.filter(
                is_active=True,
                graded_at__gte=timezone.now() - timedelta(days=365)
            ).select_related(
                'student',
                'student__current_class',
                'student__current_class__grade',
                'exam__class_subject__subject'
            )

        # Calculate statistics
        if student_grades.exists():
            # Average grade (using marks_obtained)
            context['average_grade'] = student_grades.aggregate(
                avg=Avg('marks_obtained')
            )['avg'] or 0

            # Students by performance level (using percentage field or calculated percentage)
            context['excellent_students'] = student_grades.filter(percentage__gte=90).values('student').distinct().count()
            context['good_students'] = student_grades.filter(percentage__gte=80, percentage__lt=90).values('student').distinct().count()
            context['average_students'] = student_grades.filter(percentage__gte=70, percentage__lt=80).values('student').distinct().count()
            context['below_average_students'] = student_grades.filter(percentage__lt=70).values('student').distinct().count()

            # Pass rate (assuming 60 is passing grade)
            total_students = student_grades.values('student').distinct().count()
            passing_students = student_grades.filter(percentage__gte=60).values('student').distinct().count()
            context['pass_rate'] = (passing_students / total_students * 100) if total_students > 0 else 0

            # Group grades by student for display
            student_grade_data = {}
            for grade in student_grades:
                student_id = grade.student.id
                if student_id not in student_grade_data:
                    student_grade_data[student_id] = {
                        'student': grade.student,
                        'grades': {},
                        'total_score': 0,
                        'subject_count': 0
                    }

                subject_name = grade.exam.class_subject.subject.name
                student_grade_data[student_id]['grades'][subject_name] = grade.marks_obtained
                student_grade_data[student_id]['total_score'] += grade.marks_obtained
                student_grade_data[student_id]['subject_count'] += 1

            # Calculate averages and grades
            for student_id, data in student_grade_data.items():
                if data['subject_count'] > 0:
                    data['average'] = data['total_score'] / data['subject_count']
                    # Determine grade level
                    if data['average'] >= 90:
                        data['grade_level'] = 'ممتاز'
                        data['grade_color'] = 'success'
                    elif data['average'] >= 80:
                        data['grade_level'] = 'جيد جداً'
                        data['grade_color'] = 'primary'
                    elif data['average'] >= 70:
                        data['grade_level'] = 'جيد'
                        data['grade_color'] = 'info'
                    elif data['average'] >= 60:
                        data['grade_level'] = 'مقبول'
                        data['grade_color'] = 'warning'
                    else:
                        data['grade_level'] = 'راسب'
                        data['grade_color'] = 'danger'
                else:
                    data['average'] = 0
                    data['grade_level'] = 'غير محدد'
                    data['grade_color'] = 'secondary'

            context['student_grades'] = list(student_grade_data.values())

        else:
            # Default values when no grades exist
            context['average_grade'] = 0
            context['excellent_students'] = 0
            context['good_students'] = 0
            context['average_students'] = 0
            context['below_average_students'] = 0
            context['pass_rate'] = 0
            context['student_grades'] = []

        # Get all subjects for table headers
        context['subjects'] = Subject.objects.filter(is_active=True).order_by('name')

        return context

class ExamResultsReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/exam_results.html'

class SubjectPerformanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/subject_performance.html'

class TeacherPerformanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/teacher_performance.html'

class ClassPerformanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/class_performance.html'

class StudentPerformanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/student_performance.html'


class ClassPerformanceExportView(LoginRequiredMixin, View):
    """Export class performance report to PDF/Excel"""

    def get(self, request, *args, **kwargs):
        from django.http import HttpResponse
        import json

        # Get class ID from query parameters
        class_id = request.GET.get('class')
        export_format = request.GET.get('format', 'pdf')

        if export_format == 'pdf':
            # For now, return a simple response - you can implement actual PDF generation
            response = HttpResponse(content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="class_performance_{class_id}.pdf"'
            response.write(b'PDF export functionality - to be implemented')
            return response
        elif export_format == 'excel':
            response = HttpResponse(content_type='application/vnd.ms-excel')
            response['Content-Disposition'] = f'attachment; filename="class_performance_{class_id}.xlsx"'
            response.write(b'Excel export functionality - to be implemented')
            return response
        else:
            return HttpResponse('Invalid export format', status=400)


class StudentPerformanceExportView(LoginRequiredMixin, View):
    """Export student performance report to PDF/Excel"""

    def get(self, request, *args, **kwargs):
        from django.http import HttpResponse

        # Get student ID from query parameters
        student_id = request.GET.get('student')
        export_format = request.GET.get('format', 'pdf')

        if export_format == 'pdf':
            response = HttpResponse(content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="student_performance_{student_id}.pdf"'
            response.write(b'PDF export functionality - to be implemented')
            return response
        elif export_format == 'excel':
            response = HttpResponse(content_type='application/vnd.ms-excel')
            response['Content-Disposition'] = f'attachment; filename="student_performance_{student_id}.xlsx"'
            response.write(b'Excel export functionality - to be implemented')
            return response
        else:
            return HttpResponse('Invalid export format', status=400)

class CurriculumProgressReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/curriculum_progress.html'

# Attendance Reports
class AttendanceReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/attendance.html'

class DailyAttendanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/daily_attendance.html'

class MonthlyAttendanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/monthly_attendance.html'

class ClassWiseAttendanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/class_wise_attendance.html'

class StudentWiseAttendanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/student_wise_attendance.html'

class AbsenteesReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/absentees.html'

class LateArrivalsReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/late_arrivals.html'

# HR Reports
class HRReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/hr.html'

class EmployeeReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/employee.html'

class PayrollReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/payroll.html'

class EmployeeAttendanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/employee_attendance.html'

class LeaveReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/leave.html'

class EmployeePerformanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/employee_performance.html'

# Custom Reports
class CustomReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/custom.html'

class ReportBuilderView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/report_builder.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get available models and fields for the query builder
        from .services import QueryBuilder
        query_builder = QueryBuilder()
        
        context['available_models'] = query_builder.available_models
        context['operators'] = query_builder.operators
        
        # Get existing report builders
        context['report_builders'] = ReportBuilder.objects.filter(
            models.Q(created_by=self.request.user) | models.Q(is_public=True),
            school=self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        ).order_by('-created_at')
        
        return context


class ReportBuilderCreateView(LoginRequiredMixin, CreateView):
    model = ReportBuilder
    form_class = ReportBuilderForm
    template_name = 'reports/report_builder_create.html'
    success_url = reverse_lazy('reports:report_builder')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs
    
    def form_valid(self, form):
        # Set school from user
        if hasattr(self.request.user, 'employee'):
            form.instance.school = self.request.user.employee.school
        
        messages.success(self.request, _('Report builder created successfully'))
        return super().form_valid(form)


class ReportBuilderEditView(LoginRequiredMixin, UpdateView):
    model = ReportBuilder
    form_class = ReportBuilderForm
    template_name = 'reports/report_builder_edit.html'
    success_url = reverse_lazy('reports:report_builder')
    
    def get_queryset(self):
        # Users can only edit their own report builders or public ones they have permission for
        return ReportBuilder.objects.filter(
            models.Q(created_by=self.request.user) | models.Q(is_public=True),
            school=self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        )
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs
    
    def form_valid(self, form):
        messages.success(self.request, _('Report builder updated successfully'))
        return super().form_valid(form)


class ReportBuilderDesignerView(LoginRequiredMixin, DetailView):
    model = ReportBuilder
    template_name = 'reports/report_builder_designer.html'
    context_object_name = 'report_builder'
    
    def get_queryset(self):
        return ReportBuilder.objects.filter(
            models.Q(created_by=self.request.user) | models.Q(is_public=True),
            school=self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        )
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get available models and fields for the drag-and-drop interface
        from .services import QueryBuilder
        query_builder = QueryBuilder()
        
        context['available_models'] = query_builder.available_models
        context['operators'] = query_builder.operators
        context['aggregation_functions'] = ['COUNT', 'SUM', 'AVG', 'MAX', 'MIN']
        
        return context
    
    def post(self, request, *args, **kwargs):
        """Handle AJAX requests for saving report builder configuration"""
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            report_builder = self.get_object()
            
            try:
                import json
                data = json.loads(request.body)
                
                # Update report builder configuration
                if 'report_config' in data:
                    report_builder.report_config = data['report_config']
                if 'query_config' in data:
                    report_builder.query_config = data['query_config']
                if 'layout_config' in data:
                    report_builder.layout_config = data['layout_config']
                if 'data_sources' in data:
                    report_builder.data_sources = data['data_sources']
                
                report_builder.save()
                
                return JsonResponse({
                    'success': True,
                    'message': _('Report builder configuration saved successfully')
                })
                
            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'error': str(e)
                }, status=400)
        
        return super().get(request, *args, **kwargs)


class QueryBuilderAPIView(LoginRequiredMixin, View):
    """API endpoint for query builder operations"""
    
    def post(self, request, *args, **kwargs):
        """Handle query building and validation"""
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            try:
                import json
                data = json.loads(request.body)
                action = data.get('action')
                
                from .services import QueryBuilder
                query_builder = QueryBuilder()
                
                if action == 'validate':
                    # Validate query configuration
                    query_config = data.get('query_config', {})
                    validation_result = query_builder.validate_query_config(query_config)
                    
                    return JsonResponse({
                        'success': True,
                        'validation': validation_result
                    })
                
                elif action == 'build':
                    # Build SQL query from configuration
                    query_config = data.get('query_config', {})
                    query_result = query_builder.build_query(query_config)
                    
                    return JsonResponse({
                        'success': True,
                        'query': query_result
                    })
                
                elif action == 'execute':
                    # Execute query and return results
                    query_config = data.get('query_config', {})
                    query_result = query_builder.build_query(query_config)
                    execution_result = query_builder.execute_query(query_result['sql'])
                    
                    return JsonResponse({
                        'success': True,
                        'query': query_result,
                        'results': execution_result
                    })
                
                elif action == 'get_models':
                    # Get available models and their fields
                    return JsonResponse({
                        'success': True,
                        'models': query_builder.available_models
                    })
                
                else:
                    return JsonResponse({
                        'success': False,
                        'error': 'Invalid action'
                    }, status=400)
                
            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'error': str(e)
                }, status=400)
        
        return JsonResponse({
            'success': False,
            'error': 'Invalid request'
        }, status=400)


class ReportDesignerView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/report_designer.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get report template if editing
        template_id = self.kwargs.get('template_id')
        if template_id:
            try:
                context['report_template'] = ReportTemplate.objects.get(
                    id=template_id,
                    school=self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
                )
            except ReportTemplate.DoesNotExist:
                pass
        
        # Get available report builders
        context['report_builders'] = ReportBuilder.objects.filter(
            models.Q(created_by=self.request.user) | models.Q(is_public=True),
            school=self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        ).order_by('name')
        
        return context


class ReportTemplatesView(LoginRequiredMixin, ListView):
    model = ReportTemplate
    template_name = 'reports/report_templates.html'
    context_object_name = 'report_templates'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = ReportTemplate.objects.filter(
            models.Q(created_by=self.request.user) | models.Q(is_public=True),
            school=self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        ).select_related('created_by').order_by('-created_at')
        
        # Apply filters
        report_type = self.request.GET.get('type')
        if report_type:
            queryset = queryset.filter(report_type=report_type)
        
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                models.Q(name__icontains=search) |
                models.Q(name_ar__icontains=search) |
                models.Q(description__icontains=search)
            )
        
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get filter options
        context['report_types'] = ReportTemplate.REPORT_TYPES
        context['current_type'] = self.request.GET.get('type', '')
        context['current_search'] = self.request.GET.get('search', '')
        
        return context


class ReportTemplateCreateView(LoginRequiredMixin, CreateView):
    model = ReportTemplate
    form_class = ReportTemplateForm
    template_name = 'reports/report_template_create.html'
    success_url = reverse_lazy('reports:report_templates')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs
    
    def form_valid(self, form):
        messages.success(self.request, _('Report template created successfully'))
        return super().form_valid(form)


class ReportTemplateEditView(LoginRequiredMixin, UpdateView):
    model = ReportTemplate
    form_class = ReportTemplateForm
    template_name = 'reports/report_template_edit.html'
    success_url = reverse_lazy('reports:report_templates')
    
    def get_queryset(self):
        return ReportTemplate.objects.filter(
            created_by=self.request.user,
            school=self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        )
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs
    
    def form_valid(self, form):
        messages.success(self.request, _('Report template updated successfully'))
        return super().form_valid(form)


class ReportTemplateDeleteView(LoginRequiredMixin, DeleteView):
    model = ReportTemplate
    template_name = 'reports/report_template_delete.html'
    success_url = reverse_lazy('reports:report_templates')
    
    def get_queryset(self):
        return ReportTemplate.objects.filter(
            created_by=self.request.user,
            school=self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        )
    
    def delete(self, request, *args, **kwargs):
        messages.success(request, _('Report template deleted successfully'))
        return super().delete(request, *args, **kwargs)


class ReportGenerateView(LoginRequiredMixin, DetailView):
    model = ReportTemplate
    template_name = 'reports/report_generate.html'
    context_object_name = 'report_template'
    
    def get_queryset(self):
        return ReportTemplate.objects.filter(
            models.Q(created_by=self.request.user) | models.Q(is_public=True),
            school=self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        )
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Create parameters form if template has parameters
        report_template = self.get_object()
        if report_template.parameters:
            from .forms import ReportParametersForm
            context['parameters_form'] = ReportParametersForm(
                parameters_config=report_template.parameters
            )
        
        return context
    
    def post(self, request, *args, **kwargs):
        """Generate report with parameters"""
        report_template = self.get_object()
        
        # Get parameters from form
        parameters = {}
        if report_template.parameters:
            from .forms import ReportParametersForm
            form = ReportParametersForm(
                request.POST,
                parameters_config=report_template.parameters
            )
            if form.is_valid():
                parameters = form.cleaned_data
            else:
                messages.error(request, _('Please correct the parameter errors'))
                return self.get(request, *args, **kwargs)
        
        # Generate report
        from .services import ReportGenerator
        generator = ReportGenerator()
        report_result = generator.generate_report(report_template, parameters)
        
        if report_result['success']:
            # Create execution record
            execution = ReportExecution.objects.create(
                template=report_template,
                executed_by=request.user,
                parameters_used=parameters,
                execution_time=timezone.timedelta(seconds=report_result['execution_time']),
                row_count=report_result['row_count'],
                school=report_template.school
            )
            
            # Store report data in session for display
            request.session[f'report_data_{execution.id}'] = {
                'data': report_result['data'],
                'columns': report_result['columns'],
                'row_count': report_result['row_count'],
                'execution_time': report_result['execution_time'],
                'generated_at': report_result['generated_at'].isoformat()
            }
            
            messages.success(request, _('Report generated successfully'))
            return redirect('reports:report_view', execution_id=execution.id)
        else:
            messages.error(request, f"Report generation failed: {report_result.get('error', 'Unknown error')}")
            return self.get(request, *args, **kwargs)


class ReportViewView(LoginRequiredMixin, DetailView):
    model = ReportExecution
    template_name = 'reports/report_view.html'
    context_object_name = 'execution'
    pk_url_kwarg = 'execution_id'
    
    def get_queryset(self):
        return ReportExecution.objects.filter(
            executed_by=self.request.user,
            school=self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        ).select_related('template')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get report data from session
        execution = self.get_object()
        report_data = self.request.session.get(f'report_data_{execution.id}')
        
        if report_data:
            context['report_data'] = report_data
        else:
            # Regenerate report if data not in session
            from .services import ReportGenerator
            generator = ReportGenerator()
            report_result = generator.generate_report(
                execution.template,
                execution.parameters_used
            )
            
            if report_result['success']:
                context['report_data'] = {
                    'data': report_result['data'],
                    'columns': report_result['columns'],
                    'row_count': report_result['row_count'],
                    'execution_time': report_result['execution_time'],
                    'generated_at': report_result['generated_at'].isoformat()
                }
        
        return context


class ReportExportView(LoginRequiredMixin, DetailView):
    model = ReportExecution
    template_name = 'reports/report_export.html'
    context_object_name = 'execution'
    pk_url_kwarg = 'execution_id'
    
    def get_queryset(self):
        return ReportExecution.objects.filter(
            executed_by=self.request.user,
            school=self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        ).select_related('template')
    
    def post(self, request, *args, **kwargs):
        """Handle export request"""
        execution = self.get_object()
        format_type = request.POST.get('format', 'pdf')
        
        # Regenerate report data
        from .services import ReportGenerator
        generator = ReportGenerator()
        report_result = generator.generate_report(
            execution.template,
            execution.parameters_used
        )
        
        if not report_result['success']:
            messages.error(request, f"Failed to regenerate report: {report_result.get('error')}")
            return self.get(request, *args, **kwargs)
        
        # Export report
        from .export_services import ReportExporter
        exporter = ReportExporter()
        
        template_config = {
            'title': execution.template.name,
            'description': execution.template.description
        }
        
        export_result = exporter.export_report(
            report_result,
            format_type,
            template_config
        )
        
        if export_result['success']:
            # Update execution with file path
            execution.file_path = export_result['file_path']
            execution.save()
            
            # Return file download
            from django.http import FileResponse
            response = FileResponse(
                open(export_result['file_path'], 'rb'),
                as_attachment=True,
                filename=export_result['filename']
            )
            return response
        else:
            messages.error(request, f"Export failed: {export_result.get('error')}")
            return self.get(request, *args, **kwargs)


class ReportShareView(LoginRequiredMixin, DetailView):
    model = ReportExecution
    template_name = 'reports/report_share.html'
    context_object_name = 'execution'
    pk_url_kwarg = 'execution_id'
    
    def get_queryset(self):
        return ReportExecution.objects.filter(
            executed_by=self.request.user,
            school=self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        ).select_related('template')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get existing shares for this report
        execution = self.get_object()
        context['existing_shares'] = ReportShare.objects.filter(
            report_template=execution.template,
            is_active=True
        ).order_by('-created_at')
        
        return context
    
    def post(self, request, *args, **kwargs):
        """Handle share creation"""
        execution = self.get_object()
        
        # Get share configuration
        share_type = request.POST.get('share_type', 'private_link')
        expires_days = int(request.POST.get('expires_days', 7))
        max_access = request.POST.get('max_access_count')
        
        # Create distribution config
        distribution_config = {
            'method': 'download',
            'expires_days': expires_days,
            'max_downloads': int(max_access) if max_access else None
        }
        
        # Create download link
        from .export_services import ReportDistributor
        distributor = ReportDistributor()
        result = distributor._create_download_link(execution, distribution_config)
        
        if result['success']:
            messages.success(request, 'Share link created successfully!')
            
            # If email sharing is requested
            if request.POST.get('send_email'):
                email_recipients = request.POST.get('email_recipients', '').split('\n')
                email_recipients = [email.strip() for email in email_recipients if email.strip()]
                
                if email_recipients:
                    email_config = {
                        'method': 'email',
                        'recipients': email_recipients,
                        'download_url': request.build_absolute_uri(result['download_url']),
                        'expires_days': expires_days
                    }
                    
                    email_result = distributor._distribute_via_email(execution, email_config)
                    if email_result['success']:
                        messages.success(request, f"Report shared with {len(email_recipients)} recipients via email")
                    else:
                        messages.warning(request, f"Share link created but email sending failed: {email_result.get('error')}")
        else:
            messages.error(request, f"Failed to create share link: {result.get('error')}")
        
        return redirect('reports:report_share', execution_id=execution.id)


class SharedReportDownloadView(View):
    """Public view for downloading shared reports"""
    
    def get(self, request, token):
        """Handle shared report download"""
        try:
            # Find the share
            share = ReportShare.objects.get(
                share_token=token,
                is_active=True
            )
            
            # Check if expired
            if share.expires_at and share.expires_at < timezone.now():
                return HttpResponse('This share link has expired.', status=410)
            
            # Check access count
            if share.max_access_count and share.access_count >= share.max_access_count:
                return HttpResponse('This share link has reached its maximum access limit.', status=410)
            
            # Get the latest execution for this template
            execution = ReportExecution.objects.filter(
                template=share.report_template
            ).order_by('-created_at').first()
            
            if not execution:
                return HttpResponse('Report not found.', status=404)
            
            # Increment access count
            share.access_count += 1
            share.save()
            
            # If file exists, serve it
            if execution.file_path and os.path.exists(execution.file_path):
                from django.http import FileResponse
                response = FileResponse(
                    open(execution.file_path, 'rb'),
                    as_attachment=True,
                    filename=os.path.basename(execution.file_path)
                )
                return response
            else:
                # Regenerate report
                from .services import ReportGenerator
                from .export_services import ReportExporter
                
                generator = ReportGenerator()
                report_result = generator.generate_report(
                    execution.template,
                    execution.parameters_used
                )
                
                if report_result['success']:
                    # Export to PDF by default
                    exporter = ReportExporter()
                    export_result = exporter.export_report(
                        report_result,
                        'pdf',
                        {'title': execution.template.name}
                    )
                    
                    if export_result['success']:
                        from django.http import FileResponse
                        response = FileResponse(
                            open(export_result['file_path'], 'rb'),
                            as_attachment=True,
                            filename=export_result['filename']
                        )
                        return response
                
                return HttpResponse('Failed to generate report.', status=500)
                
        except ReportShare.DoesNotExist:
            return HttpResponse('Invalid share link.', status=404)
        except Exception as e:
            logger.error(f"Shared report download failed: {e}")
            return HttpResponse('An error occurred while downloading the report.', status=500)

# Export and Print
class ExportReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/export.html'

class PrintReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/print.html'

# Report Scheduling
class ScheduleReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/schedule.html'

class ScheduledReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/scheduled.html'

# Analytics Dashboard
class AnalyticsDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/analytics.html'

class TrendsAnalysisView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/trends.html'

class PredictiveAnalysisView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/predictions.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get predictive analytics data
        from .predictive_analytics import PredictiveAnalyticsService
        analytics_service = PredictiveAnalyticsService()
        
        try:
            # Get enrollment predictions
            enrollment_prediction = analytics_service.predict_student_enrollment(6)
            context['enrollment_prediction'] = enrollment_prediction
            
            # Get revenue predictions
            revenue_prediction = analytics_service.predict_revenue_trends(6)
            context['revenue_prediction'] = revenue_prediction
            
            # Get performance predictions
            performance_prediction = analytics_service.predict_student_performance()
            context['performance_prediction'] = performance_prediction
            
            # Get attendance predictions
            attendance_prediction = analytics_service.predict_attendance_patterns(3)
            context['attendance_prediction'] = attendance_prediction
            
            # Get predictive alerts
            alerts = analytics_service.generate_predictive_alerts()
            context['predictive_alerts'] = alerts
            
        except Exception as e:
            messages.error(self.request, f"Error loading predictive analytics: {str(e)}")
            context['error'] = str(e)
        
        return context


class PredictiveAnalyticsAPIView(LoginRequiredMixin, View):
    """API endpoint for predictive analytics operations"""
    
    def post(self, request, *args, **kwargs):
        """Handle predictive analytics API requests"""
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            try:
                import json
                data = json.loads(request.body)
                action = data.get('action')
                
                from .predictive_analytics import PredictiveAnalyticsService
                analytics_service = PredictiveAnalyticsService()
                
                if action == 'predict_enrollment':
                    months_ahead = data.get('months_ahead', 12)
                    result = analytics_service.predict_student_enrollment(months_ahead)
                    
                    return JsonResponse({
                        'success': True,
                        'data': result
                    })
                
                elif action == 'predict_revenue':
                    months_ahead = data.get('months_ahead', 12)
                    result = analytics_service.predict_revenue_trends(months_ahead)
                    
                    return JsonResponse({
                        'success': True,
                        'data': result
                    })
                
                elif action == 'predict_performance':
                    student_id = data.get('student_id')
                    subject_id = data.get('subject_id')
                    result = analytics_service.predict_student_performance(student_id, subject_id)
                    
                    return JsonResponse({
                        'success': True,
                        'data': result
                    })
                
                elif action == 'predict_attendance':
                    months_ahead = data.get('months_ahead', 3)
                    result = analytics_service.predict_attendance_patterns(months_ahead)
                    
                    return JsonResponse({
                        'success': True,
                        'data': result
                    })
                
                elif action == 'get_alerts':
                    result = analytics_service.generate_predictive_alerts()
                    
                    return JsonResponse({
                        'success': True,
                        'data': result
                    })
                
                else:
                    return JsonResponse({
                        'success': False,
                        'error': 'Invalid action'
                    }, status=400)
                
            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'error': str(e)
                }, status=400)
        
        return JsonResponse({
            'success': False,
            'error': 'Invalid request'
        }, status=400)


# Interactive Dashboard Views
class DashboardListView(LoginRequiredMixin, ListView):
    model = Dashboard
    template_name = 'reports/dashboard_list.html'
    context_object_name = 'dashboards'
    paginate_by = 12
    
    def get_queryset(self):
        queryset = Dashboard.objects.filter(
            models.Q(user=self.request.user) | models.Q(is_public=True) | models.Q(allowed_users=self.request.user),
            school=self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        ).distinct().select_related('user').order_by('-updated_at')
        
        # Apply filters
        dashboard_type = self.request.GET.get('type')
        if dashboard_type:
            queryset = queryset.filter(dashboard_type=dashboard_type)
        
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                models.Q(name__icontains=search) |
                models.Q(name_ar__icontains=search) |
                models.Q(description__icontains=search)
            )
        
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get filter options
        context['dashboard_types'] = Dashboard.DASHBOARD_TYPES
        context['current_type'] = self.request.GET.get('type', '')
        context['current_search'] = self.request.GET.get('search', '')
        
        # Get dashboard statistics
        user_dashboards = Dashboard.objects.filter(user=self.request.user)
        context['user_dashboard_count'] = user_dashboards.count()
        context['public_dashboard_count'] = Dashboard.objects.filter(is_public=True).count()
        context['shared_dashboard_count'] = Dashboard.objects.filter(allowed_users=self.request.user).count()
        
        return context


class DashboardCreateView(LoginRequiredMixin, CreateView):
    model = Dashboard
    template_name = 'reports/dashboard_create.html'
    fields = ['name', 'name_ar', 'description', 'dashboard_type', 'is_public']
    success_url = reverse_lazy('reports:dashboard_list')
    
    def form_valid(self, form):
        form.instance.user = self.request.user
        if hasattr(self.request.user, 'employee'):
            form.instance.school = self.request.user.employee.school
        
        # Set default layout and settings
        from .dashboard_services import DashboardService
        dashboard_service = DashboardService()
        form.instance.layout = dashboard_service._get_default_layout()
        form.instance.settings = dashboard_service._get_default_settings()
        
        messages.success(self.request, _('Dashboard created successfully'))
        return super().form_valid(form)


class DashboardDetailView(LoginRequiredMixin, DetailView):
    model = Dashboard
    template_name = 'reports/interactive_dashboard.html'
    context_object_name = 'dashboard'
    
    def get_queryset(self):
        return Dashboard.objects.filter(
            models.Q(user=self.request.user) | models.Q(is_public=True) | models.Q(allowed_users=self.request.user),
            school=self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        ).distinct()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get dashboard data using service
        from .dashboard_services import DashboardService
        dashboard_service = DashboardService()
        
        try:
            dashboard_data = dashboard_service.get_dashboard_data(
                self.object.id, 
                self.request.user
            )
            context['dashboard_data'] = json.dumps(dashboard_data, default=str)
        except Exception as e:
            messages.error(self.request, f"Error loading dashboard data: {str(e)}")
            context['dashboard_data'] = json.dumps({
                'dashboard': {
                    'id': self.object.id,
                    'name': self.object.name,
                    'widgets': []
                },
                'widgets': [],
                'theme': {}
            })
        
        return context


class DashboardEditView(LoginRequiredMixin, UpdateView):
    model = Dashboard
    template_name = 'reports/dashboard_edit.html'
    fields = ['name', 'name_ar', 'description', 'dashboard_type', 'is_public', 'auto_refresh', 'refresh_interval']
    
    def get_queryset(self):
        return Dashboard.objects.filter(
            user=self.request.user,
            school=self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        )
    
    def get_success_url(self):
        return reverse('reports:dashboard_detail', kwargs={'pk': self.object.pk})
    
    def form_valid(self, form):
        messages.success(self.request, _('Dashboard updated successfully'))
        return super().form_valid(form)


class DashboardDeleteView(LoginRequiredMixin, DeleteView):
    model = Dashboard
    template_name = 'reports/dashboard_delete.html'
    success_url = reverse_lazy('reports:dashboard_list')
    
    def get_queryset(self):
        return Dashboard.objects.filter(
            user=self.request.user,
            school=self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        )
    
    def delete(self, request, *args, **kwargs):
        messages.success(request, _('Dashboard deleted successfully'))
        return super().delete(request, *args, **kwargs)


class DashboardAPIView(LoginRequiredMixin, View):
    """API endpoint for dashboard operations"""
    
    def post(self, request, *args, **kwargs):
        """Handle dashboard API requests"""
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            try:
                import json
                data = json.loads(request.body)
                action = data.get('action')
                
                from .dashboard_services import DashboardService
                dashboard_service = DashboardService()
                
                if action == 'refresh_widget':
                    widget_id = data.get('widget_id')
                    widget = DashboardWidget.objects.get(
                        id=widget_id,
                        dashboard__user=request.user
                    )
                    widget_data = dashboard_service.get_widget_data(widget)
                    
                    return JsonResponse({
                        'success': True,
                        'widget_data': widget_data
                    })
                
                elif action == 'update_widget_position':
                    widget_id = data.get('widget_id')
                    position = data.get('position')
                    
                    dashboard_service.update_widget_position(widget_id, position)
                    
                    return JsonResponse({
                        'success': True,
                        'message': _('Widget position updated')
                    })
                
                elif action == 'add_widget':
                    dashboard_id = data.get('dashboard_id')
                    widget_config = data.get('widget_config')
                    
                    dashboard = Dashboard.objects.get(
                        id=dashboard_id,
                        user=request.user
                    )
                    
                    widget = dashboard_service.add_widget(dashboard, widget_config)
                    widget_data = dashboard_service.get_widget_data(widget)
                    
                    return JsonResponse({
                        'success': True,
                        'widget_data': widget_data,
                        'message': _('Widget added successfully')
                    })
                
                elif action == 'remove_widget':
                    widget_id = data.get('widget_id')
                    
                    widget = DashboardWidget.objects.get(
                        id=widget_id,
                        dashboard__user=request.user
                    )
                    widget.delete()
                    
                    return JsonResponse({
                        'success': True,
                        'message': _('Widget removed successfully')
                    })
                
                elif action == 'export_dashboard':
                    dashboard_id = data.get('dashboard_id')
                    format_type = data.get('format', 'pdf')
                    
                    result = dashboard_service.export_dashboard(dashboard_id, format_type)
                    
                    return JsonResponse(result)
                
                elif action == 'get_dashboard_data':
                    dashboard_id = data.get('dashboard_id')
                    
                    dashboard_data = dashboard_service.get_dashboard_data(
                        dashboard_id, 
                        request.user
                    )
                    
                    return JsonResponse({
                        'success': True,
                        'data': dashboard_data
                    })
                
                else:
                    return JsonResponse({
                        'success': False,
                        'error': 'Invalid action'
                    }, status=400)
                
            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'error': str(e)
                }, status=400)
        
        return JsonResponse({
            'success': False,
            'error': 'Invalid request'
        }, status=400)


class DashboardWidgetView(LoginRequiredMixin, View):
    """API endpoint for individual widget operations"""
    
    def get(self, request, widget_id, *args, **kwargs):
        """Get widget data"""
        try:
            widget = DashboardWidget.objects.get(
                id=widget_id,
                dashboard__user=request.user
            )
            
            from .dashboard_services import DashboardService
            dashboard_service = DashboardService()
            widget_data = dashboard_service.get_widget_data(widget)
            
            return JsonResponse({
                'success': True,
                'widget_data': widget_data
            })
            
        except DashboardWidget.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': 'Widget not found'
            }, status=404)
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=400)
    
    def put(self, request, widget_id, *args, **kwargs):
        """Update widget configuration"""
        try:
            widget = DashboardWidget.objects.get(
                id=widget_id,
                dashboard__user=request.user
            )
            
            import json
            data = json.loads(request.body)
            
            # Update widget fields
            if 'name' in data:
                widget.name = data['name']
            if 'config' in data:
                widget.config = data['config']
            if 'data_source' in data:
                widget.data_source = data['data_source']
            if 'refresh_interval' in data:
                widget.refresh_interval = data['refresh_interval']
            if 'auto_refresh' in data:
                widget.auto_refresh = data['auto_refresh']
            
            widget.save()
            
            return JsonResponse({
                'success': True,
                'message': _('Widget updated successfully')
            })
            
        except DashboardWidget.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': 'Widget not found'
            }, status=404)
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=400)
    
    def delete(self, request, widget_id, *args, **kwargs):
        """Delete widget"""
        try:
            widget = DashboardWidget.objects.get(
                id=widget_id,
                dashboard__user=request.user
            )
            
            widget.delete()
            
            return JsonResponse({
                'success': True,
                'message': _('Widget deleted successfully')
            })
            
        except DashboardWidget.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': 'Widget not found'
            }, status=404)
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=400)


# Interactive Dashboard Views

class InteractiveDashboardListView(LoginRequiredMixin, ListView):
    model = Dashboard
    template_name = 'reports/dashboard_list.html'
    context_object_name = 'dashboards'
    paginate_by = 20
    
    def get_queryset(self):
        return Dashboard.objects.filter(
            models.Q(user=self.request.user) | 
            models.Q(is_public=True) |
            models.Q(allowed_users=self.request.user),
            school=self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        ).distinct().order_by('-last_accessed', '-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get dashboard statistics
        user_dashboards = Dashboard.objects.filter(user=self.request.user)
        context['user_dashboard_count'] = user_dashboards.count()
        context['public_dashboard_count'] = Dashboard.objects.filter(is_public=True).count()
        context['total_views'] = user_dashboards.aggregate(total=Sum('view_count'))['total'] or 0
        
        return context


class InteractiveDashboardCreateView(LoginRequiredMixin, CreateView):
    model = Dashboard
    template_name = 'reports/dashboard_create.html'
    fields = ['name', 'name_ar', 'description', 'dashboard_type', 'is_public']
    
    def form_valid(self, form):
        form.instance.user = self.request.user
        if hasattr(self.request.user, 'employee'):
            form.instance.school = self.request.user.employee.school
        
        # Set default layout and settings
        from .dashboard_services import DashboardService
        dashboard_service = DashboardService()
        form.instance.layout = dashboard_service._get_default_layout()
        form.instance.settings = dashboard_service._get_default_settings()
        
        response = super().form_valid(form)
        
        # Create default widgets if requested
        if self.request.POST.get('create_default_widgets'):
            dashboard_service._create_default_widgets(self.object, form.instance.dashboard_type)
        
        messages.success(self.request, _('Dashboard created successfully'))
        return response
    
    def get_success_url(self):
        return reverse_lazy('reports:interactive_dashboard_view', kwargs={'pk': self.object.pk})


class InteractiveDashboardView(LoginRequiredMixin, DetailView):
    model = Dashboard
    template_name = 'reports/interactive_dashboard.html'
    context_object_name = 'dashboard'
    
    def get_queryset(self):
        return Dashboard.objects.filter(
            models.Q(user=self.request.user) | 
            models.Q(is_public=True) |
            models.Q(allowed_users=self.request.user),
            school=self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        ).distinct()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get dashboard data using service
        from .dashboard_services import DashboardService
        dashboard_service = DashboardService()
        
        try:
            dashboard_data = dashboard_service.get_dashboard_data(
                self.object.id, 
                self.request.user
            )
            context['dashboard_data'] = json.dumps(dashboard_data)
        except Exception as e:
            logger.error(f"Failed to load dashboard data: {e}")
            context['dashboard_data'] = json.dumps({
                'dashboard': {
                    'id': self.object.id,
                    'name': self.object.name,
                    'error': str(e)
                },
                'widgets': [],
                'theme': {}
            })
        
        return context


class DashboardAPIView(LoginRequiredMixin, View):
    """API endpoint for dashboard operations"""
    
    def post(self, request, *args, **kwargs):
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            try:
                data = json.loads(request.body)
                action = data.get('action')
                
                from .dashboard_services import DashboardService
                dashboard_service = DashboardService()
                
                if action == 'create_widget':
                    dashboard_id = data.get('dashboard_id')
                    widget_config = data.get('widget_config')
                    
                    dashboard = Dashboard.objects.get(
                        id=dashboard_id,
                        user=request.user
                    )
                    
                    widget = dashboard_service.add_widget(dashboard, widget_config)
                    
                    return JsonResponse({
                        'success': True,
                        'widget_id': widget.id,
                        'message': _('Widget added successfully')
                    })
                
                elif action == 'update_layout':
                    dashboard_id = data.get('dashboard_id')
                    layout = data.get('layout')
                    
                    dashboard = Dashboard.objects.get(
                        id=dashboard_id,
                        user=request.user
                    )
                    
                    # Update widget positions
                    for item in layout:
                        widget_id = item['id']
                        position = {
                            'x': item['x'],
                            'y': item['y'],
                            'w': item['w'],
                            'h': item['h']
                        }
                        dashboard_service.update_widget_position(widget_id, position)
                    
                    return JsonResponse({
                        'success': True,
                        'message': _('Layout updated successfully')
                    })
                
                elif action == 'refresh_widget':
                    widget_id = data.get('widget_id')
                    
                    widget = DashboardWidget.objects.get(
                        id=widget_id,
                        dashboard__user=request.user
                    )
                    
                    widget_data = dashboard_service.get_widget_data(widget)
                    
                    return JsonResponse({
                        'success': True,
                        'widget_data': widget_data
                    })
                
                elif action == 'export_dashboard':
                    dashboard_id = data.get('dashboard_id')
                    format_type = data.get('format', 'pdf')
                    
                    result = dashboard_service.export_dashboard(dashboard_id, format_type)
                    
                    return JsonResponse(result)
                
                else:
                    return JsonResponse({
                        'success': False,
                        'error': 'Invalid action'
                    }, status=400)
                
            except Dashboard.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'error': 'Dashboard not found'
                }, status=404)
            except DashboardWidget.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'error': 'Widget not found'
                }, status=404)
            except Exception as e:
                logger.error(f"Dashboard API error: {e}")
                return JsonResponse({
                    'success': False,
                    'error': str(e)
                }, status=400)
        
        return JsonResponse({
            'success': False,
            'error': 'Invalid request'
        }, status=400)


class WidgetAPIView(LoginRequiredMixin, View):
    """API endpoint for widget operations"""
    
    def post(self, request, widget_id, *args, **kwargs):
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            try:
                data = json.loads(request.body)
                
                widget = DashboardWidget.objects.get(
                    id=widget_id,
                    dashboard__user=request.user
                )
                
                if 'position' in data:
                    # Update widget position
                    from .dashboard_services import DashboardService
                    dashboard_service = DashboardService()
                    dashboard_service.update_widget_position(widget_id, data['position'])
                    
                    return JsonResponse({
                        'success': True,
                        'message': _('Widget position updated')
                    })
                
                elif 'config' in data:
                    # Update widget configuration
                    widget.config = data['config']
                    widget.save(update_fields=['config'])
                    
                    return JsonResponse({
                        'success': True,
                        'message': _('Widget configuration updated')
                    })
                
                else:
                    return JsonResponse({
                        'success': False,
                        'error': 'No valid data provided'
                    }, status=400)
                
            except DashboardWidget.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'error': 'Widget not found'
                }, status=404)
            except Exception as e:
                logger.error(f"Widget API error: {e}")
                return JsonResponse({
                    'success': False,
                    'error': str(e)
                }, status=400)
        
        return JsonResponse({
            'success': False,
            'error': 'Invalid request'
        }, status=400)
    
    def delete(self, request, widget_id, *args, **kwargs):
        try:
            widget = DashboardWidget.objects.get(
                id=widget_id,
                dashboard__user=request.user
            )
            
            widget.delete()
            
            return JsonResponse({
                'success': True,
                'message': _('Widget deleted successfully')
            })
            
        except DashboardWidget.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': 'Widget not found'
            }, status=404)
        except Exception as e:
            logger.error(f"Widget delete error: {e}")
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=400)


# Interactive Dashboard Views
class InteractiveDashboardView(LoginRequiredMixin, ListView):
    model = Dashboard
    template_name = 'reports/interactive_dashboard.html'
    context_object_name = 'dashboards'
    
    def get_queryset(self):
        return Dashboard.objects.filter(
            user=self.request.user,
            school=self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        ).order_by('-updated_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get default dashboard or create one
        default_dashboard = self.get_queryset().filter(is_default=True).first()
        if not default_dashboard and self.get_queryset().exists():
            default_dashboard = self.get_queryset().first()
            default_dashboard.is_default = True
            default_dashboard.save()
        
        context['default_dashboard'] = default_dashboard
        
        return context


class DashboardDetailView(LoginRequiredMixin, DetailView):
    model = Dashboard
    template_name = 'reports/dashboard_detail.html'
    context_object_name = 'dashboard'
    
    def get_queryset(self):
        return Dashboard.objects.filter(
            user=self.request.user,
            school=self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        )
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get dashboard data
        from .dashboard_services import DashboardService
        dashboard_service = DashboardService()
        
        dashboard = self.get_object()
        context['dashboard_data'] = dashboard_service.get_dashboard_data(dashboard)
        
        # Available widget types
        context['widget_types'] = [
            {'type': 'chart', 'name': 'Chart', 'icon': 'fas fa-chart-line'},
            {'type': 'metric', 'name': 'Metric', 'icon': 'fas fa-tachometer-alt'},
            {'type': 'table', 'name': 'Table', 'icon': 'fas fa-table'},
            {'type': 'gauge', 'name': 'Gauge', 'icon': 'fas fa-gauge'},
            {'type': 'map', 'name': 'Map', 'icon': 'fas fa-map'},
            {'type': 'calendar', 'name': 'Calendar', 'icon': 'fas fa-calendar'}
        ]
        
        return context


class DashboardCreateView(LoginRequiredMixin, CreateView):
    model = Dashboard
    fields = ['name', 'name_ar']
    template_name = 'reports/dashboard_create.html'
    
    def form_valid(self, form):
        form.instance.user = self.request.user
        if hasattr(self.request.user, 'employee'):
            form.instance.school = self.request.user.employee.school
        
        # Set default layout
        form.instance.layout = {
            'grid': {
                'columns': 12,
                'row_height': 60,
                'margin': [10, 10]
            },
            'responsive': True,
            'draggable': True,
            'resizable': True
        }
        form.instance.widgets = []
        
        messages.success(self.request, _('Dashboard created successfully'))
        return super().form_valid(form)
    
    def get_success_url(self):
        return reverse('reports:dashboard_detail', kwargs={'pk': self.object.pk})


class DashboardUpdateView(LoginRequiredMixin, UpdateView):
    model = Dashboard
    fields = ['name', 'name_ar']
    template_name = 'reports/dashboard_update.html'
    
    def get_queryset(self):
        return Dashboard.objects.filter(
            user=self.request.user,
            school=self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        )
    
    def form_valid(self, form):
        messages.success(self.request, _('Dashboard updated successfully'))
        return super().form_valid(form)
    
    def get_success_url(self):
        return reverse('reports:dashboard_detail', kwargs={'pk': self.object.pk})


class DashboardDeleteView(LoginRequiredMixin, DeleteView):
    model = Dashboard
    template_name = 'reports/dashboard_delete.html'
    success_url = reverse_lazy('reports:interactive_dashboard')
    
    def get_queryset(self):
        return Dashboard.objects.filter(
            user=self.request.user,
            school=self.request.user.employee.school if hasattr(self.request.user, 'employee') else None
        )
    
    def delete(self, request, *args, **kwargs):
        messages.success(request, _('Dashboard deleted successfully'))
        return super().delete(request, *args, **kwargs)


class DashboardAPIView(LoginRequiredMixin, View):
    """API endpoint for dashboard operations"""
    
    def post(self, request, *args, **kwargs):
        """Handle dashboard API requests"""
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            try:
                import json
                data = json.loads(request.body)
                action = data.get('action')
                
                from .dashboard_services import DashboardService
                dashboard_service = DashboardService()
                
                if action == 'add_widget':
                    dashboard_id = data.get('dashboard_id')
                    widget_config = data.get('widget_config')
                    
                    dashboard = Dashboard.objects.get(
                        id=dashboard_id,
                        user=request.user
                    )
                    
                    updated_dashboard = dashboard_service.add_widget(dashboard, widget_config)
                    
                    return JsonResponse({
                        'success': True,
                        'widget_id': widget_config.get('id'),
                        'message': _('Widget added successfully')
                    })
                
                elif action == 'update_widget':
                    dashboard_id = data.get('dashboard_id')
                    widget_id = data.get('widget_id')
                    widget_config = data.get('widget_config')
                    
                    dashboard = Dashboard.objects.get(
                        id=dashboard_id,
                        user=request.user
                    )
                    
                    updated_dashboard = dashboard_service.update_widget(
                        dashboard, widget_id, widget_config
                    )
                    
                    return JsonResponse({
                        'success': True,
                        'message': _('Widget updated successfully')
                    })
                
                elif action == 'remove_widget':
                    dashboard_id = data.get('dashboard_id')
                    widget_id = data.get('widget_id')
                    
                    dashboard = Dashboard.objects.get(
                        id=dashboard_id,
                        user=request.user
                    )
                    
                    updated_dashboard = dashboard_service.remove_widget(dashboard, widget_id)
                    
                    return JsonResponse({
                        'success': True,
                        'message': _('Widget removed successfully')
                    })
                
                elif action == 'get_widget_data':
                    widget_config = data.get('widget_config')
                    refresh = data.get('refresh', False)
                    
                    widget_data = dashboard_service.get_widget_data(widget_config, refresh)
                    
                    return JsonResponse({
                        'success': True,
                        'data': widget_data
                    })
                
                elif action == 'update_layout':
                    dashboard_id = data.get('dashboard_id')
                    layout = data.get('layout')
                    
                    dashboard = Dashboard.objects.get(
                        id=dashboard_id,
                        user=request.user
                    )
                    
                    dashboard.layout = layout
                    dashboard.save()
                    
                    return JsonResponse({
                        'success': True,
                        'message': _('Layout updated successfully')
                    })
                
                else:
                    return JsonResponse({
                        'success': False,
                        'error': 'Invalid action'
                    }, status=400)
                
            except Dashboard.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'error': 'Dashboard not found'
                }, status=404)
            except Exception as e:
                logger.error(f"Dashboard API error: {e}")
                return JsonResponse({
                    'success': False,
                    'error': str(e)
                }, status=400)
        
        return JsonResponse({
            'success': False,
            'error': 'Invalid request'
        }, status=400)


class WidgetDataAPIView(LoginRequiredMixin, View):
    """API endpoint for real-time widget data updates"""
    
    def get(self, request, dashboard_id, widget_id):
        """Get widget data"""
        try:
            dashboard = Dashboard.objects.get(
                id=dashboard_id,
                user=request.user
            )
            
            # Find widget configuration
            widget_config = None
            for widget in dashboard.widgets:
                if widget.get('id') == widget_id:
                    widget_config = widget
                    break
            
            if not widget_config:
                return JsonResponse({
                    'success': False,
                    'error': 'Widget not found'
                }, status=404)
            
            from .dashboard_services import DashboardService
            dashboard_service = DashboardService()
            
            refresh = request.GET.get('refresh', 'false').lower() == 'true'
            widget_data = dashboard_service.get_widget_data(widget_config, refresh)
            
            return JsonResponse({
                'success': True,
                'data': widget_data,
                'timestamp': timezone.now().isoformat()
            })
            
        except Dashboard.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': 'Dashboard not found'
            }, status=404)
        except Exception as e:
            logger.error(f"Widget data API error: {e}")
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=400)
