#!/usr/bin/env python
"""
Test runner script for School ERP System
"""
import os
import sys
import subprocess
from pathlib import Path

def run_unit_tests():
    """Run unit tests with coverage"""
    print("🧪 Running Unit Tests...")
    print("=" * 50)
    
    # Run pytest with coverage
    cmd = [
        "python", "-m", "pytest",
        "tests/",
        "-v",
        "--tb=short",
        "--cov=.",
        "--cov-report=html:htmlcov",
        "--cov-report=term-missing",
        "--cov-fail-under=70",
        "-m", "unit"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        if result.returncode == 0:
            print("✅ Unit tests passed!")
        else:
            print("❌ Unit tests failed!")
            
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False

def run_integration_tests():
    """Run integration tests"""
    print("\n🔗 Running Integration Tests...")
    print("=" * 50)
    
    cmd = [
        "python", "-m", "pytest",
        "tests/",
        "-v",
        "--tb=short",
        "-m", "integration"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
            
        if result.returncode == 0:
            print("✅ Integration tests passed!")
        else:
            print("❌ Integration tests failed!")
            
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Error running integration tests: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    print("\n🚀 Running All Tests...")
    print("=" * 50)
    
    cmd = [
        "python", "-m", "pytest",
        "tests/",
        "-v",
        "--tb=short",
        "--cov=.",
        "--cov-report=html:htmlcov",
        "--cov-report=term-missing"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
            
        if result.returncode == 0:
            print("✅ All tests passed!")
        else:
            print("❌ Some tests failed!")
            
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Error running all tests: {e}")
        return False

def generate_test_report():
    """Generate test coverage report"""
    print("\n📊 Generating Test Coverage Report...")
    print("=" * 50)
    
    # Check if coverage report exists
    if Path("htmlcov/index.html").exists():
        print("✅ Coverage report generated at: htmlcov/index.html")
        print("📈 Open htmlcov/index.html in your browser to view detailed coverage")
    else:
        print("❌ Coverage report not found. Run tests first.")

def main():
    """Main test runner"""
    if len(sys.argv) > 1:
        test_type = sys.argv[1].lower()
        
        if test_type == "unit":
            success = run_unit_tests()
        elif test_type == "integration":
            success = run_integration_tests()
        elif test_type == "all":
            success = run_all_tests()
        elif test_type == "report":
            generate_test_report()
            return
        else:
            print("Usage: python run_tests.py [unit|integration|all|report]")
            return
    else:
        # Default: run unit tests
        success = run_unit_tests()
    
    generate_test_report()
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()