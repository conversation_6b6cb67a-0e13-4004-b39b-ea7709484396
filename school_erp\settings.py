"""
Django settings for school_erp project.

Generated by 'django-admin startproject' using Django 5.2.4.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

import os
from pathlib import Path
from decouple import config
from datetime import timedelta
import sys

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = config('SECRET_KEY', default='django-insecure-z1v1%ckqrw%5getu)3cvt5l=@z-#luw!zca33^2)#um%lhb!j@')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = config('DEBUG', default=True, cast=bool)

ALLOWED_HOSTS = config('ALLOWED_HOSTS', default='localhost,127.0.0.1', cast=lambda v: [s.strip() for s in v.split(',')])


# Application definition

INSTALLED_APPS = [
    'modeltranslation',  # Must be before admin
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    # Third party apps
    'rest_framework',
    'rest_framework_simplejwt',
    'corsheaders',
    'django_extensions',
    'crispy_forms',
    'crispy_bootstrap4',
    'widget_tweaks',
    'import_export',
    'django_filters',
    'debug_toolbar',
    'django_celery_beat',
    'channels',

    # Local apps
    'core',
    'accounts',
    'students',
    'academics',
    'hr',
    'finance',
    'reports',
    'communications',
    'library',
    'transportation',
    'inventory',
    'health',
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'core.middleware.LanguageMiddleware',
    'django.middleware.locale.LocaleMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'core.middleware.SchoolSelectionMiddleware',
    'core.middleware.SchoolContextMiddleware',
    'core.middleware.AuditMiddleware',
    'core.middleware.SecurityMiddleware',
    'core.middleware.PerformanceMiddleware',
    'core.middleware.ErrorHandlingMiddleware',
    'core.api.versioning.APIVersionMiddleware',
    'core.api.views.APIAnalyticsMiddleware',
    'debug_toolbar.middleware.DebugToolbarMiddleware',
]

ROOT_URLCONF = 'school_erp.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'django.template.context_processors.i18n',
                'core.context_processors.school_context',
                'core.context_processors.dashboard_context',
                'core.context_processors.navigation_context',
                'core.context_processors.localization_context',
                'core.calendar.calendar_utils.calendar_context',
            ],
        },
    },
]

WSGI_APPLICATION = 'school_erp.wsgi.application'
ASGI_APPLICATION = 'school_erp.asgi.application'

# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': config('DB_ENGINE', default='django.db.backends.postgresql'),
        'NAME': config('DB_NAME', default='school_erp'),
        'USER': config('DB_USER', default='postgres'),
        'PASSWORD': config('DB_PASSWORD', default='password'),
        'HOST': config('DB_HOST', default='localhost'),
        'PORT': config('DB_PORT', default='5432'),
        'OPTIONS': {
            'charset': 'utf8',
        },
    }
}

# Fallback to SQLite for development if PostgreSQL is not available
if config('USE_SQLITE', default=False, cast=bool):
    DATABASES['default'] = {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'en'

LANGUAGES = [
    ('en', 'English'),
    ('ar', 'العربية'),
]

TIME_ZONE = 'UTC'

USE_I18N = True
USE_L10N = True
USE_TZ = True

LOCALE_PATHS = [
    BASE_DIR / 'locale',
]

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Custom User Model
AUTH_USER_MODEL = 'accounts.User'

# REST Framework Configuration
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.TokenAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
        'core.permissions.SchoolPermission',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20,
    'DEFAULT_FILTER_BACKENDS': [
        'django_filters.rest_framework.DjangoFilterBackend',
        'rest_framework.filters.SearchFilter',
        'rest_framework.filters.OrderingFilter',
    ],
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
        'rest_framework.renderers.BrowsableAPIRenderer',
    ],
    'DEFAULT_THROTTLE_CLASSES': [
        'core.api.throttling.SchoolERPAnonRateThrottle',
        'core.api.throttling.SchoolERPUserRateThrottle',
        'core.api.throttling.APIEndpointThrottle',
        'core.api.throttling.BurstRateThrottle',
    ],
    'DEFAULT_THROTTLE_RATES': {
        'anon': '100/hour',
        'user': '1000/hour',
        'burst': '20/minute',
        'login': '10/minute',
        'password_reset': '5/hour',
        'bulk_operations': '50/hour',
        'reports': '100/hour',
        'file_upload': '20/hour',
        'export': '10/hour',
        'import': '5/hour'
    },
    'DEFAULT_VERSIONING_CLASS': 'core.api.versioning.SchoolERPVersioning',
    'DEFAULT_VERSION': 'v2',
    'ALLOWED_VERSIONS': ['v1', 'v2'],
    'VERSION_PARAM': 'version',
    # 'EXCEPTION_HANDLER': 'core.api.exceptions.custom_exception_handler',
    'DEFAULT_METADATA_CLASS': 'rest_framework.metadata.SimpleMetadata',
}

# JWT Configuration
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(hours=1),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
    'UPDATE_LAST_LOGIN': True,
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'VERIFYING_KEY': None,
    'AUDIENCE': None,
    'ISSUER': None,
    'AUTH_HEADER_TYPES': ('Bearer',),
    'AUTH_HEADER_NAME': 'HTTP_AUTHORIZATION',
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id',
}

# CORS Configuration
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://localhost:8000",
    "http://127.0.0.1:8000",
]

CORS_ALLOW_CREDENTIALS = True

# Crispy Forms Configuration
CRISPY_ALLOWED_TEMPLATE_PACKS = "bootstrap4"
CRISPY_TEMPLATE_PACK = "bootstrap4"

# Debug Toolbar Configuration
INTERNAL_IPS = [
    "127.0.0.1",
]

# Redis Configuration
REDIS_HOST = config('REDIS_HOST', default='localhost')
REDIS_PORT = config('REDIS_PORT', default=6379, cast=int)
REDIS_DB = config('REDIS_DB', default=0, cast=int)

# Celery Configuration
CELERY_BROKER_URL = config('CELERY_BROKER_URL', default=f'redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}')
CELERY_RESULT_BACKEND = config('CELERY_RESULT_BACKEND', default=f'redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}')
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE
CELERY_BEAT_SCHEDULER = 'django_celery_beat.schedulers:DatabaseScheduler'
CELERY_TASK_TRACK_STARTED = True
CELERY_TASK_TIME_LIMIT = 30 * 60
CELERY_TASK_SOFT_TIME_LIMIT = 60

# Cache Configuration
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': f'redis://{REDIS_HOST}:{REDIS_PORT}/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    },
    'sessions': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': f'redis://{REDIS_HOST}:{REDIS_PORT}/2',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# Fallback to database cache if Redis is not available
if config('USE_DB_CACHE', default=False, cast=bool):
    CACHES = {
        'default': {
            'BACKEND': 'django.core.cache.backends.db.DatabaseCache',
            'LOCATION': 'cache_table',
        },
        'sessions': {
            'BACKEND': 'django.core.cache.backends.db.DatabaseCache',
            'LOCATION': 'cache_table',
        }
    }

# For testing, use locmem cache to avoid Redis dependency but still have working cache
import sys
if 'test' in sys.argv:
    CACHES = {
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'test-cache',
        },
        'sessions': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'test-sessions',
        }
    }
    # Use database sessions for testing
    SESSION_ENGINE = 'django.contrib.sessions.backends.db'

# Channels Configuration
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels_redis.core.RedisChannelLayer',
        'CONFIG': {
            "hosts": [(REDIS_HOST, REDIS_PORT)],
        },
    },
}

# For testing, use in-memory channel layer
if 'test' in sys.argv:
    CHANNEL_LAYERS = {
        'default': {
            'BACKEND': 'channels.layers.InMemoryChannelLayer',
        },
    }

# Session Configuration
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'sessions'
SESSION_COOKIE_AGE = 86400  # 24 hours
SESSION_COOKIE_SECURE = not DEBUG
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Lax'

# Email Configuration
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.environ.get('EMAIL_HOST', 'smtp.gmail.com')
EMAIL_PORT = int(os.environ.get('EMAIL_PORT', '587'))
EMAIL_USE_TLS = True
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER', '')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD', '')

# Logging Configuration
from core.logging_config import get_logging_config
LOGGING = get_logging_config(BASE_DIR)

# Security Settings
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'
SECURE_HSTS_SECONDS = 31536000 if not DEBUG else 0
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

# Data Encryption
ENCRYPTION_KEY = config('ENCRYPTION_KEY', default=None)
if not ENCRYPTION_KEY and not DEBUG:
    raise ValueError("ENCRYPTION_KEY must be set in production")

# File Upload Settings
FILE_UPLOAD_MAX_MEMORY_SIZE = 5242880  # 5MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 5242880  # 5MB
FILE_UPLOAD_PERMISSIONS = 0o644

# School Configuration
SCHOOL_NAME = 'School ERP System'
SCHOOL_NAME_AR = 'نظام إدارة المدرسة'

# Communications Configuration
COMMUNICATIONS = {
    'SMS': {
        'PROVIDER': config('SMS_PROVIDER', default='twilio'),
        'API_KEY': config('SMS_API_KEY', default=''),
        'API_SECRET': config('SMS_API_SECRET', default=''),
        'FROM_NUMBER': config('SMS_FROM_NUMBER', default=''),
        'ENABLED': config('SMS_ENABLED', default=False, cast=bool),
    },
    'EMAIL': {
        'PROVIDER': config('EMAIL_PROVIDER', default='smtp'),
        'TEMPLATES_ENABLED': True,
        'BULK_ENABLED': True,
        'TRACKING_ENABLED': config('EMAIL_TRACKING_ENABLED', default=False, cast=bool),
    },
    'PUSH_NOTIFICATIONS': {
        'FIREBASE_KEY': config('FIREBASE_KEY', default=''),
        'ENABLED': config('PUSH_NOTIFICATIONS_ENABLED', default=False, cast=bool),
    },
    'IN_APP_NOTIFICATIONS': {
        'ENABLED': True,
        'REAL_TIME': True,
        'RETENTION_DAYS': 30,
    }
}

# Webhooks Configuration
WEBHOOKS = {
    'ENABLED': config('WEBHOOKS_ENABLED', default=True, cast=bool),
    'SECRET_KEY': config('WEBHOOK_SECRET_KEY', default=SECRET_KEY),
    'TIMEOUT': config('WEBHOOK_TIMEOUT', default=30, cast=int),
    'RETRY_ATTEMPTS': config('WEBHOOK_RETRY_ATTEMPTS', default=3, cast=int),
    'RETRY_DELAY': config('WEBHOOK_RETRY_DELAY', default=60, cast=int),
    'ALLOWED_EVENTS': [
        'student.created',
        'student.updated',
        'student.deleted',
        'attendance.marked',
        'grade.created',
        'fee.payment',
        'user.login',
        'user.logout',
        'health.incident',
        'library.book_borrowed',
        'library.book_returned',
        'inventory.item_created',
        'transportation.route_updated',
    ],
    'RATE_LIMIT': {
        'REQUESTS_PER_MINUTE': 60,
        'BURST_LIMIT': 10,
    }
}

# WebSockets Configuration
WEBSOCKETS = {
    'ENABLED': config('WEBSOCKETS_ENABLED', default=True, cast=bool),
    'HEARTBEAT_INTERVAL': config('WS_HEARTBEAT_INTERVAL', default=30, cast=int),
    'CONNECTION_TIMEOUT': config('WS_CONNECTION_TIMEOUT', default=300, cast=int),
    'MAX_CONNECTIONS_PER_USER': config('WS_MAX_CONNECTIONS_PER_USER', default=5, cast=int),
    'CHANNELS': {
        'NOTIFICATIONS': 'notifications',
        'CHAT': 'chat',
        'ATTENDANCE': 'attendance',
        'ALERTS': 'alerts',
        'SYSTEM': 'system',
    },
    'AUTHENTICATION': {
        'REQUIRED': True,
        'TOKEN_EXPIRY': 3600,  # 1 hour
    }
}

# Integrations Configuration
INTEGRATIONS = {
    'GOOGLE_WORKSPACE': {
        'ENABLED': config('GOOGLE_WORKSPACE_ENABLED', default=False, cast=bool),
        'CLIENT_ID': config('GOOGLE_CLIENT_ID', default=''),
        'CLIENT_SECRET': config('GOOGLE_CLIENT_SECRET', default=''),
        'DOMAIN': config('GOOGLE_DOMAIN', default=''),
        'SCOPES': [
            'https://www.googleapis.com/auth/admin.directory.user',
            'https://www.googleapis.com/auth/admin.directory.group',
            'https://www.googleapis.com/auth/calendar',
            'https://www.googleapis.com/auth/drive',
        ]
    },
    'MICROSOFT_365': {
        'ENABLED': config('MICROSOFT_365_ENABLED', default=False, cast=bool),
        'CLIENT_ID': config('MICROSOFT_CLIENT_ID', default=''),
        'CLIENT_SECRET': config('MICROSOFT_CLIENT_SECRET', default=''),
        'TENANT_ID': config('MICROSOFT_TENANT_ID', default=''),
    },
    'ZOOM': {
        'ENABLED': config('ZOOM_ENABLED', default=False, cast=bool),
        'API_KEY': config('ZOOM_API_KEY', default=''),
        'API_SECRET': config('ZOOM_API_SECRET', default=''),
        'WEBHOOK_SECRET': config('ZOOM_WEBHOOK_SECRET', default=''),
    },
    'PAYMENT_GATEWAYS': {
        'STRIPE': {
            'ENABLED': config('STRIPE_ENABLED', default=False, cast=bool),
            'PUBLIC_KEY': config('STRIPE_PUBLIC_KEY', default=''),
            'SECRET_KEY': config('STRIPE_SECRET_KEY', default=''),
            'WEBHOOK_SECRET': config('STRIPE_WEBHOOK_SECRET', default=''),
        },
        'PAYPAL': {
            'ENABLED': config('PAYPAL_ENABLED', default=False, cast=bool),
            'CLIENT_ID': config('PAYPAL_CLIENT_ID', default=''),
            'CLIENT_SECRET': config('PAYPAL_CLIENT_SECRET', default=''),
            'SANDBOX': config('PAYPAL_SANDBOX', default=True, cast=bool),
        }
    },
    'LMS': {
        'MOODLE': {
            'ENABLED': config('MOODLE_ENABLED', default=False, cast=bool),
            'URL': config('MOODLE_URL', default=''),
            'TOKEN': config('MOODLE_TOKEN', default=''),
        },
        'CANVAS': {
            'ENABLED': config('CANVAS_ENABLED', default=False, cast=bool),
            'URL': config('CANVAS_URL', default=''),
            'TOKEN': config('CANVAS_TOKEN', default=''),
        }
    },
    'BIOMETRIC': {
        'ENABLED': config('BIOMETRIC_ENABLED', default=False, cast=bool),
        'PROVIDER': config('BIOMETRIC_PROVIDER', default='zkteco'),
        'API_URL': config('BIOMETRIC_API_URL', default=''),
        'API_KEY': config('BIOMETRIC_API_KEY', default=''),
    }
}
