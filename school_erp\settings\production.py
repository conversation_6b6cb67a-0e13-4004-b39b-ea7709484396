"""
Production settings for School ERP System
"""

import os
import dj_database_url
from .base import *

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = False

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.environ.get('SECRET_KEY')

# Allowed hosts
ALLOWED_HOSTS = os.environ.get('ALLOWED_HOSTS', '').split(',')

# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases
DATABASES = {
    'default': dj_database_url.config(
        default=os.environ.get('DATABASE_URL'),
        conn_max_age=600,
        conn_health_checks=True,
    )
}

# Cache configuration
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.environ.get('REDIS_URL', 'redis://localhost:6379/1'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'CONNECTION_POOL_KWARGS': {
                'max_connections': 50,
                'retry_on_timeout': True,
            },
            'COMPRESSOR': 'django_redis.compressors.zlib.ZlibCompressor',
            'SERIALIZER': 'django_redis.serializers.json.JSONSerializer',
        },
        'KEY_PREFIX': 'school_erp_prod',
        'TIMEOUT': 300,
    }
}

# Session configuration
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'
SESSION_COOKIE_AGE = 3600  # 1 hour
SESSION_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Lax'

# CSRF configuration
CSRF_COOKIE_SECURE = True
CSRF_COOKIE_HTTPONLY = True
CSRF_COOKIE_SAMESITE = 'Lax'
CSRF_TRUSTED_ORIGINS = [
    f'https://{host}' for host in ALLOWED_HOSTS if host and not host.startswith('.')
]

# Security settings
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_HSTS_SECONDS = 31536000  # 1 year
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'
SECURE_SSL_REDIRECT = True

# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# File storage configuration
if os.environ.get('USE_S3') == 'True':
    # AWS S3 Configuration
    AWS_ACCESS_KEY_ID = os.environ.get('AWS_ACCESS_KEY_ID')
    AWS_SECRET_ACCESS_KEY = os.environ.get('AWS_SECRET_ACCESS_KEY')
    AWS_STORAGE_BUCKET_NAME = os.environ.get('AWS_STORAGE_BUCKET_NAME')
    AWS_S3_REGION_NAME = os.environ.get('AWS_S3_REGION_NAME', 'us-east-1')
    AWS_S3_CUSTOM_DOMAIN = f'{AWS_STORAGE_BUCKET_NAME}.s3.amazonaws.com'
    AWS_DEFAULT_ACL = 'private'
    AWS_S3_OBJECT_PARAMETERS = {
        'CacheControl': 'max-age=86400',
    }
    AWS_S3_FILE_OVERWRITE = False
    AWS_QUERYSTRING_AUTH = True
    AWS_QUERYSTRING_EXPIRE = 3600
    
    # Static files on S3
    STATICFILES_STORAGE = 'storages.backends.s3boto3.S3StaticStorage'
    AWS_STATIC_LOCATION = 'static'
    STATIC_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/{AWS_STATIC_LOCATION}/'
    
    # Media files on S3
    DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'
    AWS_MEDIA_LOCATION = 'media'
    MEDIA_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/{AWS_MEDIA_LOCATION}/'

# Email configuration
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.environ.get('EMAIL_HOST', 'smtp.gmail.com')
EMAIL_PORT = int(os.environ.get('EMAIL_PORT', 587))
EMAIL_USE_TLS = os.environ.get('EMAIL_USE_TLS', 'True').lower() == 'true'
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD')
DEFAULT_FROM_EMAIL = os.environ.get('DEFAULT_FROM_EMAIL', EMAIL_HOST_USER)
SERVER_EMAIL = DEFAULT_FROM_EMAIL

# Celery configuration
CELERY_BROKER_URL = os.environ.get('CELERY_BROKER_URL', os.environ.get('REDIS_URL', 'redis://localhost:6379/0'))
CELERY_RESULT_BACKEND = os.environ.get('CELERY_RESULT_BACKEND', CELERY_BROKER_URL)
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE
CELERY_ENABLE_UTC = True
CELERY_TASK_ALWAYS_EAGER = False
CELERY_TASK_EAGER_PROPAGATES = False
CELERY_WORKER_PREFETCH_MULTIPLIER = 1
CELERY_TASK_ACKS_LATE = True
CELERY_WORKER_MAX_TASKS_PER_CHILD = 1000

# Celery Beat configuration
CELERY_BEAT_SCHEDULER = 'django_celery_beat.schedulers:DatabaseScheduler'

# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'json': {
            '()': 'pythonjsonlogger.jsonlogger.JsonFormatter',
            'format': '%(levelname)s %(asctime)s %(module)s %(process)d %(thread)d %(message)s %(pathname)s %(lineno)d'
        }
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/school-erp/django.log',
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
            'formatter': 'json',
        },
        'error_file': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/school-erp/django-error.log',
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
            'formatter': 'json',
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
        'syslog': {
            'level': 'INFO',
            'class': 'logging.handlers.SysLogHandler',
            'address': '/dev/log',
            'formatter': 'json',
        }
    },
    'root': {
        'handlers': ['console', 'file'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['file', 'error_file'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.request': {
            'handlers': ['error_file'],
            'level': 'ERROR',
            'propagate': False,
        },
        'django.security': {
            'handlers': ['error_file', 'syslog'],
            'level': 'WARNING',
            'propagate': False,
        },
        'school_erp': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'celery': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# Performance optimizations
CONN_MAX_AGE = 600  # 10 minutes

# Database connection pooling
if os.environ.get('USE_PGBOUNCER') == 'True':
    DATABASES['default']['OPTIONS'] = {
        'MAX_CONNS': 20,
        'OPTIONS': {
            'application_name': 'school_erp_prod',
        }
    }

# Monitoring and observability
if os.environ.get('SENTRY_DSN'):
    import sentry_sdk
    from sentry_sdk.integrations.django import DjangoIntegration
    from sentry_sdk.integrations.celery import CeleryIntegration
    from sentry_sdk.integrations.redis import RedisIntegration
    
    sentry_sdk.init(
        dsn=os.environ.get('SENTRY_DSN'),
        integrations=[
            DjangoIntegration(
                transaction_style='url',
                middleware_spans=True,
                signals_spans=True,
            ),
            CeleryIntegration(
                monitor_beat_tasks=True,
                propagate_traces=True,
            ),
            RedisIntegration(),
        ],
        traces_sample_rate=0.1,
        send_default_pii=False,
        environment='production',
        release=os.environ.get('APP_VERSION', 'unknown'),
    )

# API Rate limiting
REST_FRAMEWORK.update({
    'DEFAULT_THROTTLE_CLASSES': [
        'rest_framework.throttling.AnonRateThrottle',
        'rest_framework.throttling.UserRateThrottle'
    ],
    'DEFAULT_THROTTLE_RATES': {
        'anon': '100/hour',
        'user': '1000/hour',
        'login': '5/min',
        'password_reset': '3/min',
    }
})

# Compression
MIDDLEWARE.insert(1, 'django.middleware.gzip.GZipMiddleware')

# WhiteNoise for static files
MIDDLEWARE.insert(1, 'whitenoise.middleware.WhiteNoiseMiddleware')

# Security middleware
MIDDLEWARE.insert(0, 'django.middleware.security.SecurityMiddleware')

# Custom middleware for production
MIDDLEWARE.extend([
    'core.middleware.HealthCheckMiddleware',
    'core.middleware.RequestLoggingMiddleware',
    'core.middleware.PerformanceMiddleware',
])

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 12,
        }
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
    {
        'NAME': 'core.validators.CustomPasswordValidator',
    },
]

# Internationalization
USE_I18N = True
USE_L10N = True
USE_TZ = True

# Default auto field
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# File upload settings
FILE_UPLOAD_MAX_MEMORY_SIZE = 5242880  # 5MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 5242880  # 5MB
DATA_UPLOAD_MAX_NUMBER_FIELDS = 1000

# Admin settings
ADMIN_URL = os.environ.get('ADMIN_URL', 'admin/')

# API settings
API_VERSION = 'v1'
API_TITLE = 'School ERP API'
API_DESCRIPTION = 'Comprehensive School Management System API'

# Backup settings
BACKUP_ENABLED = os.environ.get('BACKUP_ENABLED', 'True').lower() == 'true'
BACKUP_STORAGE = os.environ.get('BACKUP_STORAGE', 'local')  # local, s3, gcs
BACKUP_RETENTION_DAYS = int(os.environ.get('BACKUP_RETENTION_DAYS', 30))

# Feature flags
FEATURE_FLAGS = {
    'ENABLE_SMS_NOTIFICATIONS': os.environ.get('ENABLE_SMS_NOTIFICATIONS', 'True').lower() == 'true',
    'ENABLE_EMAIL_NOTIFICATIONS': os.environ.get('ENABLE_EMAIL_NOTIFICATIONS', 'True').lower() == 'true',
    'ENABLE_PUSH_NOTIFICATIONS': os.environ.get('ENABLE_PUSH_NOTIFICATIONS', 'True').lower() == 'true',
    'ENABLE_ANALYTICS': os.environ.get('ENABLE_ANALYTICS', 'True').lower() == 'true',
    'ENABLE_AUDIT_LOGGING': os.environ.get('ENABLE_AUDIT_LOGGING', 'True').lower() == 'true',
    'ENABLE_API_VERSIONING': os.environ.get('ENABLE_API_VERSIONING', 'True').lower() == 'true',
    'ENABLE_RATE_LIMITING': os.environ.get('ENABLE_RATE_LIMITING', 'True').lower() == 'true',
    'ENABLE_MAINTENANCE_MODE': os.environ.get('ENABLE_MAINTENANCE_MODE', 'False').lower() == 'true',
}

# Third-party integrations
INTEGRATIONS = {
    'GOOGLE_WORKSPACE': {
        'ENABLED': os.environ.get('GOOGLE_WORKSPACE_ENABLED', 'False').lower() == 'true',
        'CLIENT_ID': os.environ.get('GOOGLE_CLIENT_ID'),
        'CLIENT_SECRET': os.environ.get('GOOGLE_CLIENT_SECRET'),
        'DOMAIN': os.environ.get('GOOGLE_DOMAIN'),
    },
    'MICROSOFT_365': {
        'ENABLED': os.environ.get('MICROSOFT_365_ENABLED', 'False').lower() == 'true',
        'CLIENT_ID': os.environ.get('MICROSOFT_CLIENT_ID'),
        'CLIENT_SECRET': os.environ.get('MICROSOFT_CLIENT_SECRET'),
        'TENANT_ID': os.environ.get('MICROSOFT_TENANT_ID'),
    },
    'PAYMENT_GATEWAYS': {
        'STRIPE': {
            'ENABLED': os.environ.get('STRIPE_ENABLED', 'False').lower() == 'true',
            'PUBLIC_KEY': os.environ.get('STRIPE_PUBLIC_KEY'),
            'SECRET_KEY': os.environ.get('STRIPE_SECRET_KEY'),
            'WEBHOOK_SECRET': os.environ.get('STRIPE_WEBHOOK_SECRET'),
        },
        'PAYPAL': {
            'ENABLED': os.environ.get('PAYPAL_ENABLED', 'False').lower() == 'true',
            'CLIENT_ID': os.environ.get('PAYPAL_CLIENT_ID'),
            'CLIENT_SECRET': os.environ.get('PAYPAL_CLIENT_SECRET'),
            'MODE': os.environ.get('PAYPAL_MODE', 'live'),  # sandbox or live
        },
    },
    'SMS_PROVIDERS': {
        'TWILIO': {
            'ENABLED': os.environ.get('TWILIO_ENABLED', 'False').lower() == 'true',
            'ACCOUNT_SID': os.environ.get('TWILIO_ACCOUNT_SID'),
            'AUTH_TOKEN': os.environ.get('TWILIO_AUTH_TOKEN'),
            'FROM_NUMBER': os.environ.get('TWILIO_FROM_NUMBER'),
        },
        'AWS_SNS': {
            'ENABLED': os.environ.get('AWS_SNS_ENABLED', 'False').lower() == 'true',
            'REGION': os.environ.get('AWS_SNS_REGION', 'us-east-1'),
        },
    },
}

# Health check configuration
HEALTH_CHECK = {
    'ENABLED': True,
    'ENDPOINT': '/health/',
    'CHECKS': [
        'database',
        'cache',
        'celery',
        'storage',
    ],
    'TIMEOUT': 30,
}

# Maintenance mode
if FEATURE_FLAGS.get('ENABLE_MAINTENANCE_MODE'):
    MIDDLEWARE.insert(0, 'core.middleware.MaintenanceModeMiddleware')
    MAINTENANCE_MODE_TEMPLATE = 'maintenance.html'
    MAINTENANCE_MODE_GET_TEMPLATE = 'core.utils.get_maintenance_template'

# Performance monitoring
PERFORMANCE_MONITORING = {
    'ENABLED': os.environ.get('PERFORMANCE_MONITORING_ENABLED', 'True').lower() == 'true',
    'SLOW_QUERY_THRESHOLD': float(os.environ.get('SLOW_QUERY_THRESHOLD', 1.0)),
    'SLOW_REQUEST_THRESHOLD': float(os.environ.get('SLOW_REQUEST_THRESHOLD', 2.0)),
    'MEMORY_THRESHOLD': int(os.environ.get('MEMORY_THRESHOLD', 80)),  # Percentage
}

# Content Security Policy
CSP_DEFAULT_SRC = ("'self'",)
CSP_SCRIPT_SRC = ("'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net", "https://cdnjs.cloudflare.com")
CSP_STYLE_SRC = ("'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net", "https://cdnjs.cloudflare.com")
CSP_IMG_SRC = ("'self'", "data:", "https:")
CSP_FONT_SRC = ("'self'", "https://cdn.jsdelivr.net", "https://cdnjs.cloudflare.com")
CSP_CONNECT_SRC = ("'self'",)
CSP_FRAME_SRC = ("'none'",)
CSP_OBJECT_SRC = ("'none'",)

# CORS settings (if API is accessed from different domains)
if os.environ.get('ENABLE_CORS') == 'True':
    CORS_ALLOWED_ORIGINS = os.environ.get('CORS_ALLOWED_ORIGINS', '').split(',')
    CORS_ALLOW_CREDENTIALS = True
    CORS_ALLOW_ALL_ORIGINS = False

# Custom settings validation
def validate_production_settings():
    """Validate critical production settings"""
    required_settings = [
        'SECRET_KEY',
        'DATABASE_URL',
        'ALLOWED_HOSTS',
    ]
    
    missing_settings = []
    for setting in required_settings:
        if not os.environ.get(setting):
            missing_settings.append(setting)
    
    if missing_settings:
        raise ValueError(f"Missing required environment variables: {', '.join(missing_settings)}")
    
    # Validate SECRET_KEY strength
    if len(SECRET_KEY) < 50:
        raise ValueError("SECRET_KEY must be at least 50 characters long")
    
    # Validate ALLOWED_HOSTS
    if not ALLOWED_HOSTS or ALLOWED_HOSTS == ['']:
        raise ValueError("ALLOWED_HOSTS must be properly configured")

# Run validation
validate_production_settings()

# Environment-specific overrides
ENVIRONMENT = os.environ.get('ENVIRONMENT', 'production')

if ENVIRONMENT == 'staging':
    # Staging-specific settings
    DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'
    LOGGING['root']['level'] = 'DEBUG'
    CELERY_TASK_ALWAYS_EAGER = True
    
elif ENVIRONMENT == 'production':
    # Production-specific settings
    DEBUG = False
    SECURE_SSL_REDIRECT = True
    SECURE_HSTS_SECONDS = 31536000
    
    # Additional security for production
    SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
    USE_TZ = True
    
    # Optimize for production
    CONN_MAX_AGE = 600
    DATABASES['default']['CONN_MAX_AGE'] = 600

# Final configuration summary
print(f"School ERP System - Production Configuration Loaded")
print(f"Environment: {ENVIRONMENT}")
print(f"Debug Mode: {DEBUG}")
print(f"Database: {DATABASES['default']['ENGINE']}")
print(f"Cache Backend: {CACHES['default']['BACKEND']}")
print(f"Static Files: {STATICFILES_STORAGE}")
print(f"Media Storage: {DEFAULT_FILE_STORAGE if 'DEFAULT_FILE_STORAGE' in locals() else 'Local'}")
print(f"Celery Broker: {CELERY_BROKER_URL}")
print(f"Feature Flags: {sum(1 for flag in FEATURE_FLAGS.values() if flag)} enabled")