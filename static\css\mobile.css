/* Mobile-First CSS Framework for School ERP */

/* Base Mobile Styles (320px and up) */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 3rem;
    
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    
    --transition: all 0.15s ease-in-out;
}

/* Reset and Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--dark-color);
    background-color: #fff;
    margin: 0;
    padding: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Mobile Container */
.container-mobile {
    width: 100%;
    padding-left: var(--spacing-md);
    padding-right: var(--spacing-md);
    margin-left: auto;
    margin-right: auto;
}

/* Mobile Grid System */
.row-mobile {
    display: flex;
    flex-wrap: wrap;
    margin-left: calc(var(--spacing-sm) * -1);
    margin-right: calc(var(--spacing-sm) * -1);
}

.col-mobile {
    flex: 1 0 0%;
    padding-left: var(--spacing-sm);
    padding-right: var(--spacing-sm);
}

.col-mobile-12 { flex: 0 0 100%; max-width: 100%; }
.col-mobile-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.col-mobile-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.col-mobile-9 { flex: 0 0 75%; max-width: 75%; }
.col-mobile-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-mobile-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.col-mobile-6 { flex: 0 0 50%; max-width: 50%; }
.col-mobile-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.col-mobile-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-mobile-3 { flex: 0 0 25%; max-width: 25%; }
.col-mobile-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-mobile-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }

/* Mobile Navigation */
.navbar-mobile {
    background-color: var(--primary-color);
    color: white;
    padding: var(--spacing-sm) 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: var(--box-shadow);
}

.navbar-mobile .container-mobile {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.navbar-brand-mobile {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: white;
    text-decoration: none;
}

.navbar-toggler-mobile {
    background: none;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.navbar-toggler-mobile:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.navbar-collapse-mobile {
    position: fixed;
    top: 0;
    left: -100%;
    width: 280px;
    height: 100vh;
    background-color: white;
    box-shadow: var(--box-shadow-lg);
    transition: left 0.3s ease;
    z-index: 1001;
    overflow-y: auto;
}

.navbar-collapse-mobile.show {
    left: 0;
}

.navbar-overlay-mobile {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.navbar-overlay-mobile.show {
    opacity: 1;
    visibility: visible;
}

.navbar-header-mobile {
    padding: var(--spacing-lg);
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.navbar-close-mobile {
    background: none;
    border: none;
    color: white;
    font-size: var(--font-size-xl);
    cursor: pointer;
    padding: var(--spacing-xs);
}

.navbar-nav-mobile {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item-mobile {
    border-bottom: 1px solid #eee;
}

.nav-link-mobile {
    display: flex;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--dark-color);
    text-decoration: none;
    transition: var(--transition);
}

.nav-link-mobile:hover {
    background-color: var(--light-color);
    color: var(--primary-color);
}

.nav-link-mobile i {
    margin-right: var(--spacing-sm);
    width: 20px;
    text-align: center;
}

.nav-submenu-mobile {
    list-style: none;
    margin: 0;
    padding: 0;
    background-color: #f8f9fa;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.nav-submenu-mobile.show {
    max-height: 500px;
}

.nav-submenu-mobile .nav-link-mobile {
    padding-left: calc(var(--spacing-lg) + 20px + var(--spacing-sm));
    font-size: var(--font-size-sm);
}

/* Mobile Cards */
.card-mobile {
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: var(--spacing-md);
    overflow: hidden;
}

.card-header-mobile {
    padding: var(--spacing-md);
    background-color: var(--light-color);
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

.card-body-mobile {
    padding: var(--spacing-md);
}

.card-footer-mobile {
    padding: var(--spacing-md);
    background-color: var(--light-color);
    border-top: 1px solid #dee2e6;
}

/* Mobile Buttons */
.btn-mobile {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    transition: var(--transition);
    min-height: 44px; /* Touch target size */
    min-width: 44px;
}

.btn-mobile:disabled {
    opacity: 0.65;
    cursor: not-allowed;
}

.btn-primary-mobile {
    color: white;
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary-mobile:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.btn-secondary-mobile {
    color: white;
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-outline-primary-mobile {
    color: var(--primary-color);
    background-color: transparent;
    border-color: var(--primary-color);
}

.btn-outline-primary-mobile:hover {
    color: white;
    background-color: var(--primary-color);
}

.btn-sm-mobile {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    min-height: 36px;
}

.btn-lg-mobile {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-lg);
    min-height: 52px;
}

.btn-block-mobile {
    display: block;
    width: 100%;
}

/* Mobile Forms */
.form-group-mobile {
    margin-bottom: var(--spacing-md);
}

.form-label-mobile {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--dark-color);
}

.form-control-mobile {
    display: block;
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--dark-color);
    background-color: white;
    border: 1px solid #ced4da;
    border-radius: var(--border-radius);
    transition: var(--transition);
    min-height: 44px; /* Touch target size */
}

.form-control-mobile:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-control-mobile::placeholder {
    color: #6c757d;
    opacity: 1;
}

.form-select-mobile {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right var(--spacing-sm) center;
    background-size: 16px 12px;
    padding-right: calc(var(--spacing-md) + 16px + var(--spacing-sm));
}

.form-check-mobile {
    display: block;
    min-height: 1.5rem;
    padding-left: 1.5em;
    margin-bottom: var(--spacing-xs);
}

.form-check-input-mobile {
    width: 1em;
    height: 1em;
    margin-top: 0.25em;
    margin-left: -1.5em;
    vertical-align: top;
    background-color: white;
    border: 1px solid rgba(0, 0, 0, 0.25);
    appearance: none;
    color-adjust: exact;
}

.form-check-input-mobile[type="checkbox"] {
    border-radius: 0.25em;
}

.form-check-input-mobile[type="radio"] {
    border-radius: 50%;
}

.form-check-input-mobile:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-label-mobile {
    color: var(--dark-color);
    cursor: pointer;
}

/* Mobile Tables */
.table-mobile {
    width: 100%;
    margin-bottom: var(--spacing-md);
    color: var(--dark-color);
    border-collapse: collapse;
}

.table-mobile th,
.table-mobile td {
    padding: var(--spacing-sm);
    vertical-align: top;
    border-bottom: 1px solid #dee2e6;
}

.table-mobile th {
    font-weight: 600;
    color: var(--dark-color);
    background-color: var(--light-color);
}

/* Mobile Table Responsive */
.table-responsive-mobile {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table-responsive-mobile .table-mobile {
    margin-bottom: 0;
}

/* Mobile List Group */
.list-group-mobile {
    display: flex;
    flex-direction: column;
    padding-left: 0;
    margin-bottom: 0;
    border-radius: var(--border-radius);
}

.list-group-item-mobile {
    position: relative;
    display: block;
    padding: var(--spacing-md);
    color: var(--dark-color);
    text-decoration: none;
    background-color: white;
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-top-width: 0;
}

.list-group-item-mobile:first-child {
    border-top-width: 1px;
    border-top-left-radius: var(--border-radius);
    border-top-right-radius: var(--border-radius);
}

.list-group-item-mobile:last-child {
    border-bottom-left-radius: var(--border-radius);
    border-bottom-right-radius: var(--border-radius);
}

.list-group-item-mobile:hover {
    background-color: var(--light-color);
}

.list-group-item-mobile.active {
    color: white;
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Mobile Alerts */
.alert-mobile {
    position: relative;
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    border: 1px solid transparent;
    border-radius: var(--border-radius);
}

.alert-primary-mobile {
    color: #004085;
    background-color: #cce7ff;
    border-color: #b3d7ff;
}

.alert-success-mobile {
    color: #0f5132;
    background-color: #d1e7dd;
    border-color: #badbcc;
}

.alert-danger-mobile {
    color: #842029;
    background-color: #f8d7da;
    border-color: #f5c2c7;
}

.alert-warning-mobile {
    color: #664d03;
    background-color: #fff3cd;
    border-color: #ffecb5;
}

/* Mobile Modals */
.modal-mobile {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1050;
    display: none;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    outline: 0;
}

.modal-mobile.show {
    display: block;
}

.modal-dialog-mobile {
    position: relative;
    width: auto;
    margin: var(--spacing-sm);
    pointer-events: none;
}

.modal-content-mobile {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: white;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
    outline: 0;
}

.modal-header-mobile {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    border-bottom: 1px solid #dee2e6;
    border-top-left-radius: var(--border-radius);
    border-top-right-radius: var(--border-radius);
}

.modal-title-mobile {
    margin-bottom: 0;
    line-height: 1.5;
    font-weight: 600;
}

.modal-body-mobile {
    position: relative;
    flex: 1 1 auto;
    padding: var(--spacing-md);
}

.modal-footer-mobile {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-end;
    padding: var(--spacing-md);
    border-top: 1px solid #dee2e6;
    border-bottom-right-radius: var(--border-radius);
    border-bottom-left-radius: var(--border-radius);
    gap: var(--spacing-sm);
}

.modal-backdrop-mobile {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1040;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
}

/* Mobile Utilities */
.d-mobile-none { display: none !important; }
.d-mobile-inline { display: inline !important; }
.d-mobile-inline-block { display: inline-block !important; }
.d-mobile-block { display: block !important; }
.d-mobile-flex { display: flex !important; }

.text-mobile-left { text-align: left !important; }
.text-mobile-center { text-align: center !important; }
.text-mobile-right { text-align: right !important; }

.m-mobile-0 { margin: 0 !important; }
.m-mobile-1 { margin: var(--spacing-xs) !important; }
.m-mobile-2 { margin: var(--spacing-sm) !important; }
.m-mobile-3 { margin: var(--spacing-md) !important; }
.m-mobile-4 { margin: var(--spacing-lg) !important; }
.m-mobile-5 { margin: var(--spacing-xl) !important; }

.p-mobile-0 { padding: 0 !important; }
.p-mobile-1 { padding: var(--spacing-xs) !important; }
.p-mobile-2 { padding: var(--spacing-sm) !important; }
.p-mobile-3 { padding: var(--spacing-md) !important; }
.p-mobile-4 { padding: var(--spacing-lg) !important; }
.p-mobile-5 { padding: var(--spacing-xl) !important; }

/* Touch-friendly interactions */
.touch-target {
    min-height: 44px;
    min-width: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Tablet Styles (768px and up) */
@media (min-width: 768px) {
    .container-mobile {
        max-width: 720px;
    }
    
    .col-tablet-12 { flex: 0 0 100%; max-width: 100%; }
    .col-tablet-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-tablet-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-tablet-9 { flex: 0 0 75%; max-width: 75%; }
    .col-tablet-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-tablet-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-tablet-6 { flex: 0 0 50%; max-width: 50%; }
    .col-tablet-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-tablet-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-tablet-3 { flex: 0 0 25%; max-width: 25%; }
    .col-tablet-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-tablet-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    
    .d-tablet-none { display: none !important; }
    .d-tablet-inline { display: inline !important; }
    .d-tablet-inline-block { display: inline-block !important; }
    .d-tablet-block { display: block !important; }
    .d-tablet-flex { display: flex !important; }
    
    .modal-dialog-mobile {
        max-width: 500px;
        margin: var(--spacing-xl) auto;
    }
}

/* Desktop Styles (992px and up) */
@media (min-width: 992px) {
    .container-mobile {
        max-width: 960px;
    }
    
    .col-desktop-12 { flex: 0 0 100%; max-width: 100%; }
    .col-desktop-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-desktop-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-desktop-9 { flex: 0 0 75%; max-width: 75%; }
    .col-desktop-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-desktop-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-desktop-6 { flex: 0 0 50%; max-width: 50%; }
    .col-desktop-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-desktop-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-desktop-3 { flex: 0 0 25%; max-width: 25%; }
    .col-desktop-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-desktop-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    
    .d-desktop-none { display: none !important; }
    .d-desktop-inline { display: inline !important; }
    .d-desktop-inline-block { display: inline-block !important; }
    .d-desktop-block { display: block !important; }
    .d-desktop-flex { display: flex !important; }
    
    .navbar-toggler-mobile {
        display: none;
    }
    
    .navbar-collapse-mobile {
        position: static;
        width: auto;
        height: auto;
        background-color: transparent;
        box-shadow: none;
        overflow-y: visible;
        display: flex !important;
        flex-basis: auto;
    }
    
    .navbar-nav-mobile {
        display: flex;
        flex-direction: row;
        margin-left: auto;
    }
    
    .nav-item-mobile {
        border-bottom: none;
    }
    
    .nav-link-mobile {
        color: rgba(255, 255, 255, 0.8);
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .nav-link-mobile:hover {
        color: white;
        background-color: rgba(255, 255, 255, 0.1);
    }
}

/* Large Desktop Styles (1200px and up) */
@media (min-width: 1200px) {
    .container-mobile {
        max-width: 1140px;
    }
}

/* Extra Large Desktop Styles (1400px and up) */
@media (min-width: 1400px) {
    .container-mobile {
        max-width: 1320px;
    }
}

/* Print Styles */
@media print {
    .navbar-mobile,
    .btn-mobile,
    .navbar-toggler-mobile {
        display: none !important;
    }
    
    .container-mobile {
        max-width: none !important;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* Optimize for high DPI displays */
    body {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --dark-color: #f8f9fa;
        --light-color: #343a40;
    }
    
    body {
        background-color: #121212;
        color: var(--dark-color);
    }
    
    .card-mobile {
        background-color: #1e1e1e;
        border-color: #333;
    }
    
    .form-control-mobile {
        background-color: #1e1e1e;
        border-color: #333;
        color: var(--dark-color);
    }
    
    .table-mobile th {
        background-color: #1e1e1e;
        color: var(--dark-color);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}