/* RTL (Right-to-Left) Layout Styles for Arabic Language Support */

/* Base RTL Styles */
.rtl-layout {
    direction: rtl;
    text-align: right;
}

.rtl-layout * {
    direction: rtl;
}

/* Typography */
.rtl-layout h1,
.rtl-layout h2,
.rtl-layout h3,
.rtl-layout h4,
.rtl-layout h5,
.rtl-layout h6 {
    text-align: right;
}

.rtl-layout p,
.rtl-layout div,
.rtl-layout span {
    text-align: right;
}

/* Arabic Font Support */
.arabic-text,
.rtl-layout {
    font-family: 'Amiri', 'Noto Sans Arabic', 'Tahoma', 'Arial Unicode MS', sans-serif;
    line-height: 1.8;
}

/* Bootstrap RTL Overrides */
.rtl-layout .text-left {
    text-align: right !important;
}

.rtl-layout .text-right {
    text-align: left !important;
}

.rtl-layout .float-left {
    float: right !important;
}

.rtl-layout .float-right {
    float: left !important;
}

/* Flexbox RTL */
.rtl-layout .justify-content-start {
    justify-content: flex-end !important;
}

.rtl-layout .justify-content-end {
    justify-content: flex-start !important;
}

/* <PERSON><PERSON> and Padding RTL Adjustments */
.rtl-layout .ml-1 { margin-right: 0.25rem !important; margin-left: 0 !important; }
.rtl-layout .ml-2 { margin-right: 0.5rem !important; margin-left: 0 !important; }
.rtl-layout .ml-3 { margin-right: 1rem !important; margin-left: 0 !important; }
.rtl-layout .ml-4 { margin-right: 1.5rem !important; margin-left: 0 !important; }
.rtl-layout .ml-5 { margin-right: 3rem !important; margin-left: 0 !important; }
.rtl-layout .ml-auto { margin-right: auto !important; margin-left: 0 !important; }

.rtl-layout .mr-1 { margin-left: 0.25rem !important; margin-right: 0 !important; }
.rtl-layout .mr-2 { margin-left: 0.5rem !important; margin-right: 0 !important; }
.rtl-layout .mr-3 { margin-left: 1rem !important; margin-right: 0 !important; }
.rtl-layout .mr-4 { margin-left: 1.5rem !important; margin-right: 0 !important; }
.rtl-layout .mr-5 { margin-left: 3rem !important; margin-right: 0 !important; }
.rtl-layout .mr-auto { margin-left: auto !important; margin-right: 0 !important; }

.rtl-layout .pl-1 { padding-right: 0.25rem !important; padding-left: 0 !important; }
.rtl-layout .pl-2 { padding-right: 0.5rem !important; padding-left: 0 !important; }
.rtl-layout .pl-3 { padding-right: 1rem !important; padding-left: 0 !important; }
.rtl-layout .pl-4 { padding-right: 1.5rem !important; padding-left: 0 !important; }
.rtl-layout .pl-5 { padding-right: 3rem !important; padding-left: 0 !important; }

.rtl-layout .pr-1 { padding-left: 0.25rem !important; padding-right: 0 !important; }
.rtl-layout .pr-2 { padding-left: 0.5rem !important; padding-right: 0 !important; }
.rtl-layout .pr-3 { padding-left: 1rem !important; padding-right: 0 !important; }
.rtl-layout .pr-4 { padding-left: 1.5rem !important; padding-right: 0 !important; }
.rtl-layout .pr-5 { padding-left: 3rem !important; padding-right: 0 !important; }

/* Navigation RTL */
.rtl-layout .navbar-nav {
    flex-direction: row-reverse;
}

.rtl-layout .navbar-brand {
    margin-right: 0;
    margin-left: 1rem;
}

.rtl-layout .navbar-toggler {
    margin-left: 0;
    margin-right: auto;
}

.rtl-layout .nav-link {
    text-align: right;
}

/* Dropdown RTL */
.rtl-layout .dropdown-menu {
    right: 0;
    left: auto;
    text-align: right;
}

.rtl-layout .dropdown-item {
    text-align: right;
    padding-right: 1rem;
    padding-left: 3rem;
}

.rtl-layout .dropdown-toggle::after {
    margin-left: 0;
    margin-right: 0.255em;
}

/* Form RTL */
.rtl-layout .form-group label {
    text-align: right;
}

.rtl-layout .form-control {
    text-align: right;
}

.rtl-layout .form-check {
    text-align: right;
    padding-left: 0;
    padding-right: 1.25rem;
}

.rtl-layout .form-check-input {
    margin-left: 0;
    margin-right: -1.25rem;
}

.rtl-layout .input-group-prepend {
    order: 2;
}

.rtl-layout .input-group-append {
    order: 0;
}

.rtl-layout .input-group .form-control {
    order: 1;
}

/* Table RTL */
.rtl-layout .table th,
.rtl-layout .table td {
    text-align: right;
}

.rtl-layout .table th:first-child,
.rtl-layout .table td:first-child {
    border-right: 0;
}

.rtl-layout .table th:last-child,
.rtl-layout .table td:last-child {
    border-left: 0;
}

/* Card RTL */
.rtl-layout .card-header {
    text-align: right;
}

.rtl-layout .card-body {
    text-align: right;
}

.rtl-layout .card-title {
    text-align: right;
}

/* Sidebar RTL */
.rtl-layout .sidebar {
    right: 0;
    left: auto;
}

.rtl-layout .sidebar .nav-link {
    text-align: right;
}

.rtl-layout .sidebar .nav-link i {
    margin-left: 0.5rem;
    margin-right: 0;
    float: right;
}

/* Breadcrumb RTL */
.rtl-layout .breadcrumb {
    flex-direction: row-reverse;
}

.rtl-layout .breadcrumb-item + .breadcrumb-item::before {
    content: "\\";
    padding-right: 0.5rem;
    padding-left: 0;
    float: right;
}

/* Modal RTL */
.rtl-layout .modal-header {
    text-align: right;
}

.rtl-layout .modal-body {
    text-align: right;
}

.rtl-layout .modal-footer {
    justify-content: flex-start;
}

.rtl-layout .modal-footer .btn {
    margin-left: 0;
    margin-right: 0.25rem;
}

.rtl-layout .close {
    float: left;
}

/* Alert RTL */
.rtl-layout .alert {
    text-align: right;
}

.rtl-layout .alert-dismissible .btn-close {
    right: auto;
    left: 0;
    padding-right: 0;
    padding-left: 1.25rem;
}

/* Pagination RTL */
.rtl-layout .pagination {
    flex-direction: row-reverse;
}

/* Progress RTL */
.rtl-layout .progress-bar {
    right: 0;
    left: auto;
}

/* List Group RTL */
.rtl-layout .list-group-item {
    text-align: right;
}

/* Badge RTL */
.rtl-layout .badge {
    text-align: center;
}

/* Button Group RTL */
.rtl-layout .btn-group {
    flex-direction: row-reverse;
}

/* Tooltip RTL */
.rtl-layout .tooltip {
    text-align: center;
}

/* Popover RTL */
.rtl-layout .popover {
    text-align: right;
}

/* Carousel RTL */
.rtl-layout .carousel-control-prev {
    right: 0;
    left: auto;
}

.rtl-layout .carousel-control-next {
    left: 0;
    right: auto;
}

/* Icons RTL - Flip certain icons for RTL */
.rtl-layout .fa-chevron-left::before {
    content: "\f054"; /* chevron-right */
}

.rtl-layout .fa-chevron-right::before {
    content: "\f053"; /* chevron-left */
}

.rtl-layout .fa-angle-left::before {
    content: "\f105"; /* angle-right */
}

.rtl-layout .fa-angle-right::before {
    content: "\f104"; /* angle-left */
}

.rtl-layout .fa-arrow-left::before {
    content: "\f061"; /* arrow-right */
}

.rtl-layout .fa-arrow-right::before {
    content: "\f060"; /* arrow-left */
}

/* Number formatting for Arabic */
.arabic-numbers {
    font-family: 'Noto Sans Arabic', 'Arial Unicode MS', sans-serif;
}

/* Custom RTL utilities */
.rtl-text-align {
    text-align: right;
}

.ltr-text-align {
    text-align: left;
}

.rtl-float-right {
    float: left;
}

.rtl-float-left {
    float: right;
}

/* Language switcher specific styles */
.language-switcher {
    position: relative;
}

.rtl-layout .language-switcher .dropdown-menu {
    right: 0;
    left: auto;
}

.rtl-layout .language-switcher .dropdown-item {
    display: flex;
    align-items: center;
    flex-direction: row-reverse;
}

.rtl-layout .language-switcher .flag-icon {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* Print styles for RTL */
@media print {
    .rtl-layout {
        direction: rtl;
        text-align: right;
    }
    
    .rtl-layout .table th,
    .rtl-layout .table td {
        text-align: right;
    }
}}


/* Navigation RTL */
.rtl-layout .navbar-nav {
    flex-direction: row-reverse;
}

.rtl-layout .navbar-brand {
    margin-right: 0;
    margin-left: 1rem;
}

/* Forms RTL */
.rtl-layout .form-control {
    text-align: right;
}

.rtl-layout .form-group label {
    text-align: right;
}

.rtl-layout .form-check {
    text-align: right;
    padding-left: 0;
    padding-right: 1.25rem;
}

.rtl-layout .form-check-input {
    margin-left: 0;
    margin-right: -1.25rem;
}

/* Tables RTL */
.rtl-layout .table th,
.rtl-layout .table td {
    text-align: right;
}

/* Buttons RTL */
.rtl-layout .btn-group {
    flex-direction: row-reverse;
}

/* Dropdowns RTL */
.rtl-layout .dropdown-menu {
    right: 0;
    left: auto;
    text-align: right;
}

/* Modals RTL */
.rtl-layout .modal-header {
    text-align: right;
}

.rtl-layout .modal-body {
    text-align: right;
}

.rtl-layout .modal-footer {
    justify-content: flex-start;
}

/* Cards RTL */
.rtl-layout .card-header,
.rtl-layout .card-body,
.rtl-layout .card-footer {
    text-align: right;
}

/* Alerts RTL */
.rtl-layout .alert {
    text-align: right;
}

/* Breadcrumbs RTL */
.rtl-layout .breadcrumb {
    flex-direction: row-reverse;
}

.rtl-layout .breadcrumb-item + .breadcrumb-item::before {
    content: "\\";
    transform: scaleX(-1);
}

/* Pagination RTL */
.rtl-layout .pagination {
    flex-direction: row-reverse;
}

/* Margin and Padding RTL Utilities */
.rtl-layout .ml-1 { margin-right: 0.25rem !important; margin-left: 0 !important; }
.rtl-layout .ml-2 { margin-right: 0.5rem !important; margin-left: 0 !important; }
.rtl-layout .ml-3 { margin-right: 1rem !important; margin-left: 0 !important; }
.rtl-layout .ml-4 { margin-right: 1.5rem !important; margin-left: 0 !important; }
.rtl-layout .ml-5 { margin-right: 3rem !important; margin-left: 0 !important; }

.rtl-layout .mr-1 { margin-left: 0.25rem !important; margin-right: 0 !important; }
.rtl-layout .mr-2 { margin-left: 0.5rem !important; margin-right: 0 !important; }
.rtl-layout .mr-3 { margin-left: 1rem !important; margin-right: 0 !important; }
.rtl-layout .mr-4 { margin-left: 1.5rem !important; margin-right: 0 !important; }
.rtl-layout .mr-5 { margin-left: 3rem !important; margin-right: 0 !important; }

.rtl-layout .pl-1 { padding-right: 0.25rem !important; padding-left: 0 !important; }
.rtl-layout .pl-2 { padding-right: 0.5rem !important; padding-left: 0 !important; }
.rtl-layout .pl-3 { padding-right: 1rem !important; padding-left: 0 !important; }
.rtl-layout .pl-4 { padding-right: 1.5rem !important; padding-left: 0 !important; }
.rtl-layout .pl-5 { padding-right: 3rem !important; padding-left: 0 !important; }

.rtl-layout .pr-1 { padding-left: 0.25rem !important; padding-right: 0 !important; }
.rtl-layout .pr-2 { padding-left: 0.5rem !important; padding-right: 0 !important; }
.rtl-layout .pr-3 { padding-left: 1rem !important; padding-right: 0 !important; }
.rtl-layout .pr-4 { padding-left: 1.5rem !important; padding-right: 0 !important; }
.rtl-layout .pr-5 { padding-left: 3rem !important; padding-right: 0 !important; }

/* Arabic Number Display */
.arabic-number {
    font-family: 'Noto Sans Arabic', 'Tahoma', sans-serif;
    direction: ltr;
    display: inline-block;
}

/* Calendar RTL */
.rtl-layout .calendar {
    direction: rtl;
}

.rtl-layout .calendar .calendar-header {
    text-align: right;
}

/* Mobile RTL */
@media (max-width: 768px) {
    .rtl-layout .navbar-collapse {
        text-align: right;
    }
    
    .rtl-layout .sidebar {
        right: -250px;
        left: auto;
    }
    
    .rtl-layout .sidebar.active {
        right: 0;
    }
}

/* Arabic Font Loading */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');