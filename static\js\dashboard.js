/**
 * Interactive Dashboard JavaScript
 * Handles dashboard functionality, real-time updates, and widget management
 */

// Global variables
let grid;
let dashboardConfig;
let dashboardData;
let editMode = false;
let fullscreenMode = false;
let selectedWidgetType = null;
let selectedChartType = null;
let widgets = {};
let charts = {};
let realTimeConnection = null;
let refreshInterval = null;

// Initialize dashboard when page loads
function initializeDashboard() {
    // Load dashboard configuration
    const configScript = document.getElementById('dashboardConfig');
    const dataScript = document.getElementById('dashboardData');
    
    if (configScript) {
        try {
            dashboardConfig = JSON.parse(configScript.textContent);
        } catch (e) {
            console.error('Failed to parse dashboard config:', e);
            dashboardConfig = {};
        }
    }
    
    if (dataScript) {
        try {
            dashboardData = JSON.parse(dataScript.textContent);
        } catch (e) {
            console.error('Failed to parse dashboard data:', e);
            dashboardData = { widgets: [] };
        }
    }
    
    // Initialize GridStack
    initializeGrid();
    
    // Load widgets
    loadWidgets();
    
    // Setup real-time updates if enabled
    if (dashboardConfig.real_time) {
        setupRealTimeUpdates();
    }
    
    // Setup auto-refresh
    if (dashboardConfig.refresh_interval > 0) {
        setupAutoRefresh();
    }
    
    // Setup keyboard shortcuts
    setupKeyboardShortcuts();
}

function initializeGrid() {
    const gridElement = document.getElementById('gridStack');
    if (!gridElement) {
        console.error('Grid element not found');
        return;
    }
    
    grid = GridStack.init({
        cellHeight: 60,
        verticalMargin: 10,
        horizontalMargin: 10,
        minRow: 1,
        maxRow: 20,
        animate: true,
        resizable: {
            handles: 'e, se, s, sw, w'
        },
        draggable: {
            handle: '.widget-header'
        },
        disableOneColumnMode: true,
        float: false,
        staticGrid: true // Start in static mode
    }, gridElement);
    
    // Handle widget position changes
    grid.on('change', function(event, items) {
        if (editMode) {
            items.forEach(function(item) {
                updateWidgetPosition(item.id, {
                    x: item.x,
                    y: item.y,
                    w: item.w,
                    h: item.h
                });
            });
        }
    });
}

function loadWidgets() {
    if (!dashboardData || !dashboardData.widgets) {
        console.log('No widgets to load');
        return;
    }
    
    dashboardData.widgets.forEach(function(widgetData) {
        createWidget(widgetData);
    });
}

function createWidget(widgetData) {
    const widgetId = widgetData.id;
    const position = widgetData.position || {};
    
    // Create widget HTML
    const widgetHtml = `
        <div class="grid-stack-item-content">
            <div class="widget-header">
                <h6 class="widget-title">${widgetData.name}</h6>
                <div class="widget-actions">
                    <button type="button" class="widget-action" onclick="refreshWidget(${widgetId})" title="Refresh">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button type="button" class="widget-action" onclick="configureWidget(${widgetId})" title="Configure">
                        <i class="fas fa-cog"></i>
                    </button>
                    <button type="button" class="widget-action edit-only" onclick="removeWidget(${widgetId})" title="Remove" style="display: none;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="widget-body" id="widget-body-${widgetId}">
                <div class="widget-loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading widget data...</p>
                </div>
            </div>
        </div>
    `;
    
    // Add widget to grid
    const gridItem = grid.addWidget(widgetHtml, {
        x: position.x || 0,
        y: position.y || 0,
        w: position.w || 4,
        h: position.h || 3,
        id: widgetId.toString()
    });
    
    // Store widget reference
    widgets[widgetId] = {
        element: gridItem,
        data: widgetData,
        chart: null
    };
    
    // Load widget content
    loadWidgetContent(widgetId, widgetData);
}

function loadWidgetContent(widgetId, widgetData) {
    const widgetBody = document.getElementById(`widget-body-${widgetId}`);
    
    if (!widgetBody) {
        console.error(`Widget body not found for widget ${widgetId}`);
        return;
    }
    
    if (widgetData.error) {
        widgetBody.innerHTML = `
            <div class="widget-error">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <p>Error loading widget</p>
                <small>${widgetData.error}</small>
            </div>
        `;
        return;
    }
    
    if (!widgetData.data) {
        widgetBody.innerHTML = `
            <div class="widget-error">
                <i class="fas fa-exclamation-circle fa-2x mb-2"></i>
                <p>No data available</p>
            </div>
        `;
        return;
    }
    
    // Render widget based on type
    switch (widgetData.widget_type) {
        case 'metric':
            renderMetricWidget(widgetId, widgetData);
            break;
        case 'chart':
            renderChartWidget(widgetId, widgetData);
            break;
        case 'table':
            renderTableWidget(widgetId, widgetData);
            break;
        case 'gauge':
            renderGaugeWidget(widgetId, widgetData);
            break;
        case 'calendar':
            renderCalendarWidget(widgetId, widgetData);
            break;
        case 'text':
            renderTextWidget(widgetId, widgetData);
            break;
        default:
            widgetBody.innerHTML = `
                <div class="widget-error">
                    <i class="fas fa-question-circle fa-2x mb-2"></i>
                    <p>Unknown widget type: ${widgetData.widget_type}</p>
                </div>
            `;
    }
}

function renderMetricWidget(widgetId, widgetData) {
    const widgetBody = document.getElementById(`widget-body-${widgetId}`);
    const metrics = widgetData.data.metrics || [];
    
    if (metrics.length === 0) {
        widgetBody.innerHTML = '<p class="text-muted">No metrics available</p>';
        return;
    }
    
    let html = '<div class="row h-100">';
    
    metrics.forEach(function(metric, index) {
        const colClass = metrics.length === 1 ? 'col-12' : 
                        metrics.length === 2 ? 'col-6' : 
                        metrics.length === 3 ? 'col-4' : 'col-3';
        
        let value = metric.value;
        if (metric.format === 'currency') {
            value = '$' + value.toLocaleString();
        } else if (metric.format === 'percentage') {
            value = value + '%';
        } else if (metric.format === 'number') {
            value = value.toLocaleString();
        }
        
        html += `
            <div class="${colClass}">
                <div class="metric-widget h-100 d-flex flex-column justify-content-center">
                    <div class="metric-icon text-${metric.color}">
                        <i class="fas fa-${metric.icon}"></i>
                    </div>
                    <div class="metric-value text-${metric.color}">${value}</div>
                    <div class="metric-label">${metric.name}</div>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    widgetBody.innerHTML = html;
}

function renderChartWidget(widgetId, widgetData) {
    const widgetBody = document.getElementById(`widget-body-${widgetId}`);
    
    // Create canvas for chart
    widgetBody.innerHTML = `
        <div class="chart-container">
            <canvas id="chart-${widgetId}"></canvas>
        </div>
    `;
    
    const canvas = document.getElementById(`chart-${widgetId}`);
    if (!canvas) {
        console.error(`Canvas not found for chart ${widgetId}`);
        return;
    }
    
    const ctx = canvas.getContext('2d');
    
    // Destroy existing chart if it exists
    if (charts[widgetId]) {
        charts[widgetId].destroy();
    }
    
    // Create new chart
    const chartConfig = {
        type: widgetData.chart_type || 'line',
        data: widgetData.data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: false
                }
            },
            scales: widgetData.chart_type === 'pie' || widgetData.chart_type === 'doughnut' ? {} : {
                y: {
                    beginAtZero: true
                }
            }
        }
    };
    
    try {
        charts[widgetId] = new Chart(ctx, chartConfig);
    } catch (e) {
        console.error(`Failed to create chart for widget ${widgetId}:`, e);
        widgetBody.innerHTML = `
            <div class="widget-error">
                <i class="fas fa-chart-bar fa-2x mb-2"></i>
                <p>Error creating chart</p>
                <small>${e.message}</small>
            </div>
        `;
    }
}

function renderTableWidget(widgetId, widgetData) {
    const widgetBody = document.getElementById(`widget-body-${widgetId}`);
    const data = widgetData.data;
    
    if (!data.columns || !data.rows) {
        widgetBody.innerHTML = '<p class="text-muted">No table data available</p>';
        return;
    }
    
    let html = `
        <div class="table-container">
            <table class="table table-sm table-striped">
                <thead>
                    <tr>
    `;
    
    data.columns.forEach(function(column) {
        html += `<th>${column}</th>`;
    });
    
    html += `
                    </tr>
                </thead>
                <tbody>
    `;
    
    data.rows.forEach(function(row) {
        html += '<tr>';
        row.forEach(function(cell) {
            html += `<td>${cell || ''}</td>`;
        });
        html += '</tr>';
    });
    
    html += `
                </tbody>
            </table>
        </div>
    `;
    
    widgetBody.innerHTML = html;
}

function renderGaugeWidget(widgetId, widgetData) {
    const widgetBody = document.getElementById(`widget-body-${widgetId}`);
    const data = widgetData.data;
    
    // Simple gauge implementation
    const value = data.value || 0;
    const max = data.max || 100;
    const percentage = (value / max) * 100;
    
    widgetBody.innerHTML = `
        <div class="gauge-widget text-center">
            <div class="gauge-container" style="position: relative; width: 150px; height: 150px; margin: 0 auto;">
                <svg width="150" height="150" viewBox="0 0 150 150">
                    <circle cx="75" cy="75" r="60" fill="none" stroke="#e9ecef" stroke-width="10"/>
                    <circle cx="75" cy="75" r="60" fill="none" stroke="#007bff" stroke-width="10"
                            stroke-dasharray="${2 * Math.PI * 60}" 
                            stroke-dashoffset="${2 * Math.PI * 60 * (1 - percentage / 100)}"
                            transform="rotate(-90 75 75)"/>
                </svg>
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                    <div style="font-size: 24px; font-weight: bold;">${value}</div>
                    <div style="font-size: 12px; color: #6c757d;">${data.label || 'Value'}</div>
                </div>
            </div>
        </div>
    `;
}

function renderCalendarWidget(widgetId, widgetData) {
    const widgetBody = document.getElementById(`widget-body-${widgetId}`);
    
    // Simple calendar implementation
    widgetBody.innerHTML = `
        <div class="calendar-widget">
            <p class="text-center text-muted">Calendar widget - Coming soon</p>
        </div>
    `;
}

function renderTextWidget(widgetId, widgetData) {
    const widgetBody = document.getElementById(`widget-body-${widgetId}`);
    const data = widgetData.data;
    
    widgetBody.innerHTML = `
        <div class="text-widget">
            <div class="text-content">${data.content || 'No content available'}</div>
        </div>
    `;
}

// Dashboard control functions
function toggleEditMode() {
    editMode = !editMode;
    const editBtn = document.getElementById('editModeBtn');
    const addBtn = document.getElementById('addWidgetBtn');
    const editOnlyElements = document.querySelectorAll('.edit-only');
    
    if (editMode) {
        // Enable edit mode
        grid.enableMove(true);
        grid.enableResize(true);
        editBtn.classList.add('active');
        editBtn.innerHTML = '<i class="fas fa-save"></i>';
        editBtn.title = 'Save Changes';
        
        if (addBtn) addBtn.style.display = 'block';
        editOnlyElements.forEach(el => el.style.display = 'inline-block');
        
        showMessage('Edit mode enabled. You can now move and resize widgets.', 'info');
    } else {
        // Disable edit mode
        grid.enableMove(false);
        grid.enableResize(false);
        editBtn.classList.remove('active');
        editBtn.innerHTML = '<i class="fas fa-edit"></i>';
        editBtn.title = 'Edit Mode';
        
        if (addBtn) addBtn.style.display = 'none';
        editOnlyElements.forEach(el => el.style.display = 'none');
        
        showMessage('Edit mode disabled. Changes saved.', 'success');
    }
}

function toggleFullscreen() {
    const container = document.getElementById('dashboardContainer');
    const fullscreenBtn = container.querySelector('[onclick="toggleFullscreen()"]');
    
    if (!fullscreenMode) {
        container.classList.add('fullscreen-mode');
        fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
        fullscreenBtn.title = 'Exit Fullscreen';
        fullscreenMode = true;
    } else {
        container.classList.remove('fullscreen-mode');
        fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
        fullscreenBtn.title = 'Fullscreen';
        fullscreenMode = false;
    }
}

function toggleFilters() {
    const filtersPanel = document.getElementById('dashboardFilters');
    if (filtersPanel) {
        filtersPanel.classList.toggle('show');
    }
}

function refreshDashboard() {
    const refreshIcon = document.getElementById('refreshIcon');
    const refreshIndicator = document.getElementById('refreshIndicator');
    
    if (refreshIcon) {
        refreshIcon.style.display = 'none';
    }
    if (refreshIndicator) {
        refreshIndicator.style.display = 'inline-block';
    }
    
    // Refresh all widgets
    Object.keys(widgets).forEach(function(widgetId) {
        refreshWidget(widgetId);
    });
    
    setTimeout(function() {
        if (refreshIcon) {
            refreshIcon.style.display = 'inline-block';
        }
        if (refreshIndicator) {
            refreshIndicator.style.display = 'none';
        }
        showMessage('Dashboard refreshed successfully', 'success');
    }, 2000);
}

function refreshWidget(widgetId) {
    const widgetBody = document.getElementById(`widget-body-${widgetId}`);
    if (!widgetBody) return;
    
    // Show loading state
    widgetBody.innerHTML = `
        <div class="widget-loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Refreshing widget...</p>
        </div>
    `;
    
    // Make API call to refresh widget data
    fetch('/reports/api/dashboard/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken(),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            action: 'refresh_widget',
            widget_id: widgetId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update widget data and re-render
            widgets[widgetId].data = data.widget_data;
            loadWidgetContent(widgetId, data.widget_data);
        } else {
            widgetBody.innerHTML = `
                <div class="widget-error">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <p>Error refreshing widget</p>
                    <small>${data.error || 'Unknown error'}</small>
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error refreshing widget:', error);
        widgetBody.innerHTML = `
            <div class="widget-error">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <p>Error refreshing widget</p>
                <small>Network error</small>
            </div>
        `;
    });
}

function updateWidgetPosition(widgetId, position) {
    // Make API call to update widget position
    fetch('/reports/api/dashboard/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken(),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            action: 'update_widget_position',
            widget_id: widgetId,
            position: position
        })
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            console.error('Failed to update widget position:', data.error);
        }
    })
    .catch(error => {
        console.error('Error updating widget position:', error);
    });
}

function showAddWidgetModal() {
    const modal = new bootstrap.Modal(document.getElementById('addWidgetModal'));
    modal.show();
}

function selectWidgetType(widgetType, chartType) {
    selectedWidgetType = widgetType;
    selectedChartType = chartType;
    
    // Update UI to show selection
    const items = document.querySelectorAll('#addWidgetModal .list-group-item');
    items.forEach(item => item.classList.remove('active'));
    
    event.target.classList.add('active');
    
    // Enable confirm button
    const confirmBtn = document.getElementById('addWidgetConfirm');
    if (confirmBtn) {
        confirmBtn.disabled = false;
    }
}

function addSelectedWidget() {
    if (!selectedWidgetType) {
        showMessage('Please select a widget type', 'error');
        return;
    }
    
    // Show widget configuration modal
    const addModal = bootstrap.Modal.getInstance(document.getElementById('addWidgetModal'));
    addModal.hide();
    
    const configModal = new bootstrap.Modal(document.getElementById('widgetConfigModal'));
    configModal.show();
}

function saveWidgetConfig() {
    const form = document.getElementById('widgetConfigForm');
    const formData = new FormData(form);
    
    const widgetConfig = {
        name: document.getElementById('widgetName').value,
        widget_type: selectedWidgetType,
        chart_type: selectedChartType,
        data_source: {
            type: document.getElementById('widgetDataSource').value
        },
        config: {},
        refresh_interval: parseInt(document.getElementById('widgetRefreshInterval').value),
        auto_refresh: document.getElementById('widgetAutoRefresh').checked,
        position: {
            x: 0,
            y: 0,
            w: 4,
            h: 3
        }
    };
    
    // Make API call to add widget
    fetch('/reports/api/dashboard/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken(),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            action: 'add_widget',
            dashboard_id: dashboardConfig.id,
            widget_config: widgetConfig
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Add widget to dashboard
            createWidget(data.widget_data);
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('widgetConfigModal'));
            modal.hide();
            
            // Reset form
            form.reset();
            selectedWidgetType = null;
            selectedChartType = null;
            
            showMessage('Widget added successfully', 'success');
        } else {
            showMessage('Failed to add widget: ' + (data.error || 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        console.error('Error adding widget:', error);
        showMessage('Error adding widget', 'error');
    });
}

function removeWidget(widgetId) {
    if (!confirm('Are you sure you want to remove this widget?')) {
        return;
    }
    
    // Make API call to remove widget
    fetch('/reports/api/dashboard/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken(),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            action: 'remove_widget',
            widget_id: widgetId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove widget from grid
            const widget = widgets[widgetId];
            if (widget && widget.element) {
                grid.removeWidget(widget.element);
            }
            
            // Clean up chart if exists
            if (charts[widgetId]) {
                charts[widgetId].destroy();
                delete charts[widgetId];
            }
            
            // Remove from widgets object
            delete widgets[widgetId];
            
            showMessage('Widget removed successfully', 'success');
        } else {
            showMessage('Failed to remove widget: ' + (data.error || 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        console.error('Error removing widget:', error);
        showMessage('Error removing widget', 'error');
    });
}

function configureWidget(widgetId) {
    // Show widget configuration modal with current settings
    const widget = widgets[widgetId];
    if (!widget) return;
    
    // Populate form with current values
    document.getElementById('widgetName').value = widget.data.name;
    document.getElementById('widgetDataSource').value = widget.data.data_source.type || '';
    document.getElementById('widgetRefreshInterval').value = widget.data.refresh_interval || 300;
    document.getElementById('widgetAutoRefresh').checked = widget.data.auto_refresh !== false;
    
    // Store current widget ID for saving
    document.getElementById('widgetConfigForm').dataset.widgetId = widgetId;
    
    const modal = new bootstrap.Modal(document.getElementById('widgetConfigModal'));
    modal.show();
}

function exportDashboard(format) {
    // Make API call to export dashboard
    fetch('/reports/api/dashboard/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken(),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            action: 'export_dashboard',
            dashboard_id: dashboardConfig.id,
            format: format
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(`Dashboard exported successfully as ${format.toUpperCase()}`, 'success');
            // In a real implementation, this would trigger a download
        } else {
            showMessage('Export failed: ' + (data.error || 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        console.error('Error exporting dashboard:', error);
        showMessage('Error exporting dashboard', 'error');
    });
}

function shareDashboard() {
    // Show share modal or copy share link
    const shareUrl = window.location.href;
    
    if (navigator.clipboard) {
        navigator.clipboard.writeText(shareUrl).then(() => {
            showMessage('Dashboard link copied to clipboard', 'success');
        }).catch(() => {
            showMessage('Failed to copy link', 'error');
        });
    } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = shareUrl;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showMessage('Dashboard link copied to clipboard', 'success');
    }
}

function applyFilters() {
    const dateRange = document.getElementById('dateRangeFilter').value;
    const department = document.getElementById('departmentFilter').value;
    const grade = document.getElementById('gradeFilter').value;
    
    // Apply filters to all widgets
    Object.keys(widgets).forEach(function(widgetId) {
        // Update widget data source with filters
        const widget = widgets[widgetId];
        if (widget.data.data_source) {
            widget.data.data_source.filters = {
                date_range: dateRange,
                department: department,
                grade: grade
            };
        }
        
        // Refresh widget with new filters
        refreshWidget(widgetId);
    });
    
    showMessage('Filters applied successfully', 'success');
}

function clearFilters() {
    document.getElementById('dateRangeFilter').value = 'month';
    document.getElementById('departmentFilter').value = '';
    document.getElementById('gradeFilter').value = '';
    
    applyFilters();
}

// Real-time updates
function setupRealTimeUpdates() {
    // This would implement WebSocket connection for real-time updates
    console.log('Setting up real-time updates...');
    
    // For now, just use polling
    if (dashboardConfig.refresh_interval > 0) {
        setInterval(function() {
            // Refresh widgets that have auto_refresh enabled
            Object.keys(widgets).forEach(function(widgetId) {
                const widget = widgets[widgetId];
                if (widget.data.auto_refresh) {
                    refreshWidget(widgetId);
                }
            });
        }, dashboardConfig.refresh_interval * 1000);
    }
}

function setupAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
    
    refreshInterval = setInterval(function() {
        refreshDashboard();
    }, dashboardConfig.refresh_interval * 1000);
}

function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + E: Toggle edit mode
        if ((e.ctrlKey || e.metaKey) && e.key === 'e') {
            e.preventDefault();
            toggleEditMode();
        }
        
        // F11: Toggle fullscreen
        if (e.key === 'F11') {
            e.preventDefault();
            toggleFullscreen();
        }
        
        // Ctrl/Cmd + R: Refresh dashboard
        if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
            e.preventDefault();
            refreshDashboard();
        }
    });
}

// Utility functions
function getCsrfToken() {
    const token = document.querySelector('[name=csrfmiddlewaretoken]');
    return token ? token.value : '';
}

function showMessage(message, type = 'info') {
    // Create toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}

// Export functions for global access
window.initializeDashboard = initializeDashboard;
window.toggleEditMode = toggleEditMode;
window.toggleFullscreen = toggleFullscreen;
window.toggleFilters = toggleFilters;
window.refreshDashboard = refreshDashboard;
window.refreshWidget = refreshWidget;
window.showAddWidgetModal = showAddWidgetModal;
window.selectWidgetType = selectWidgetType;
window.addSelectedWidget = addSelectedWidget;
window.saveWidgetConfig = saveWidgetConfig;
window.removeWidget = removeWidget;
window.configureWidget = configureWidget;
window.exportDashboard = exportDashboard;
window.shareDashboard = shareDashboard;
window.applyFilters = applyFilters;
window.clearFilters = clearFilters;