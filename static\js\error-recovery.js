/**
 * Error Recovery and Network Resilience System
 * Provides automatic retry mechanisms and graceful degradation
 */

class ErrorRecoverySystem {
    constructor() {
        this.retryAttempts = new Map();
        this.maxRetries = 3;
        this.retryDelay = 1000;
        this.networkStatus = 'online';
        this.pendingRequests = new Map();
        
        this.init();
    }

    init() {
        // Monitor network status
        this.setupNetworkMonitoring();
        
        // Setup automatic retry for failed requests
        this.setupRequestInterception();
        
        // Setup offline/online handlers
        this.setupOfflineHandlers();
    }

    setupNetworkMonitoring() {
        // Check initial network status
        this.networkStatus = navigator.onLine ? 'online' : 'offline';
        
        // Listen for network changes
        window.addEventListener('online', () => {
            this.networkStatus = 'online';
            this.handleNetworkReconnection();
        });
        
        window.addEventListener('offline', () => {
            this.networkStatus = 'offline';
            this.handleNetworkDisconnection();
        });
        
        // Periodic connectivity check
        setInterval(() => {
            this.checkConnectivity();
        }, 30000); // Check every 30 seconds
    }

    async checkConnectivity() {
        try {
            const response = await fetch('/api/health-check/', {
                method: 'HEAD',
                cache: 'no-cache'
            });
            
            if (response.ok && this.networkStatus === 'offline') {
                this.networkStatus = 'online';
                this.handleNetworkReconnection();
            }
        } catch (error) {
            if (this.networkStatus === 'online') {
                this.networkStatus = 'offline';
                this.handleNetworkDisconnection();
            }
        }
    }

    handleNetworkDisconnection() {
        console.log('Network disconnected');
        
        if (window.toast) {
            window.toast.warning('Network connection lost. Some features may not work properly.', {
                autohide: false,
                showProgress: false
            });
        }
        
        // Add offline indicator to UI
        this.showOfflineIndicator();
    }

    handleNetworkReconnection() {
        console.log('Network reconnected');
        
        if (window.toast) {
            window.toast.success('Network connection restored!');
        }
        
        // Remove offline indicator
        this.hideOfflineIndicator();
        
        // Retry pending requests
        this.retryPendingRequests();
    }

    showOfflineIndicator() {
        let indicator = document.getElementById('offline-indicator');
        
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'offline-indicator';
            indicator.className = 'alert alert-warning position-fixed top-0 start-50 translate-middle-x mt-2';
            indicator.style.zIndex = '10000';
            indicator.innerHTML = `
                <i class="fas fa-wifi-slash me-2"></i>
                <strong>Offline Mode</strong> - Some features may not work properly
            `;
            document.body.appendChild(indicator);
        }
        
        indicator.style.display = 'block';
    }

    hideOfflineIndicator() {
        const indicator = document.getElementById('offline-indicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }

    setupRequestInterception() {
        // Intercept fetch requests for automatic retry
        const originalFetch = window.fetch;
        const errorRecovery = this;
        
        window.fetch = function(url, options = {}) {
            const requestId = Date.now() + '-' + Math.random().toString(36).substr(2, 9);
            
            return errorRecovery.fetchWithRetry(originalFetch, url, options, requestId);
        };
    }

    async fetchWithRetry(originalFetch, url, options, requestId, attempt = 1) {
        try {
            // Store request for potential retry
            this.pendingRequests.set(requestId, { url, options, attempt });
            
            const response = await originalFetch(url, options);
            
            // Remove from pending requests on success
            this.pendingRequests.delete(requestId);
            
            // Reset retry count on success
            this.retryAttempts.delete(url);
            
            return response;
            
        } catch (error) {
            console.error(`Request failed (attempt ${attempt}):`, error);
            
            // Check if we should retry
            if (this.shouldRetry(error, attempt)) {
                const delay = this.calculateRetryDelay(attempt);
                
                console.log(`Retrying request in ${delay}ms...`);
                
                await this.sleep(delay);
                
                return this.fetchWithRetry(originalFetch, url, options, requestId, attempt + 1);
            }
            
            // Remove from pending requests if max retries exceeded
            this.pendingRequests.delete(requestId);
            
            throw error;
        }
    }

    shouldRetry(error, attempt) {
        // Don't retry if max attempts reached
        if (attempt >= this.maxRetries) {
            return false;
        }
        
        // Retry on network errors
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            return true;
        }
        
        // Retry on timeout errors
        if (error.name === 'AbortError') {
            return true;
        }
        
        // Don't retry on client errors (4xx)
        if (error.status >= 400 && error.status < 500) {
            return false;
        }
        
        // Retry on server errors (5xx)
        if (error.status >= 500) {
            return true;
        }
        
        return false;
    }

    calculateRetryDelay(attempt) {
        // Exponential backoff with jitter
        const baseDelay = this.retryDelay * Math.pow(2, attempt - 1);
        const jitter = Math.random() * 1000;
        return Math.min(baseDelay + jitter, 10000); // Max 10 seconds
    }

    async retryPendingRequests() {
        const requests = Array.from(this.pendingRequests.values());
        
        for (const request of requests) {
            try {
                await this.sleep(Math.random() * 2000); // Stagger requests
                await fetch(request.url, request.options);
            } catch (error) {
                console.error('Failed to retry pending request:', error);
            }
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    setupOfflineHandlers() {
        // Handle form submissions when offline
        document.addEventListener('submit', (event) => {
            if (this.networkStatus === 'offline') {
                event.preventDefault();
                
                if (window.toast) {
                    window.toast.warning('Cannot submit form while offline. Please check your connection and try again.');
                }
                
                // Store form data for later submission
                this.storeOfflineFormData(event.target);
            }
        });
        
        // Handle button clicks when offline
        document.addEventListener('click', (event) => {
            const button = event.target.closest('button[data-requires-network]');
            
            if (button && this.networkStatus === 'offline') {
                event.preventDefault();
                
                if (window.toast) {
                    window.toast.warning('This action requires an internet connection. Please check your connection and try again.');
                }
            }
        });
    }

    storeOfflineFormData(form) {
        const formData = new FormData(form);
        const data = {};
        
        for (const [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        const offlineData = {
            action: form.action,
            method: form.method,
            data: data,
            timestamp: Date.now()
        };
        
        // Store in localStorage
        const offlineForms = JSON.parse(localStorage.getItem('offlineForms') || '[]');
        offlineForms.push(offlineData);
        localStorage.setItem('offlineForms', JSON.stringify(offlineForms));
        
        if (window.toast) {
            window.toast.info('Form data saved. It will be submitted when connection is restored.');
        }
    }

    async submitOfflineForms() {
        const offlineForms = JSON.parse(localStorage.getItem('offlineForms') || '[]');
        
        if (offlineForms.length === 0) {
            return;
        }
        
        const successfulSubmissions = [];
        
        for (let i = 0; i < offlineForms.length; i++) {
            const formData = offlineForms[i];
            
            try {
                const body = new FormData();
                for (const [key, value] of Object.entries(formData.data)) {
                    body.append(key, value);
                }
                
                const response = await fetch(formData.action, {
                    method: formData.method,
                    body: body
                });
                
                if (response.ok) {
                    successfulSubmissions.push(i);
                }
                
            } catch (error) {
                console.error('Failed to submit offline form:', error);
            }
        }
        
        // Remove successfully submitted forms
        if (successfulSubmissions.length > 0) {
            const remainingForms = offlineForms.filter((_, index) => 
                !successfulSubmissions.includes(index)
            );
            
            localStorage.setItem('offlineForms', JSON.stringify(remainingForms));
            
            if (window.toast) {
                window.toast.success(`${successfulSubmissions.length} offline form(s) submitted successfully!`);
            }
        }
    }

    // Public methods for manual error recovery
    retryLastFailedRequest() {
        // Implementation for manual retry
        console.log('Manual retry requested');
    }

    clearOfflineData() {
        localStorage.removeItem('offlineForms');
        this.pendingRequests.clear();
        this.retryAttempts.clear();
        
        if (window.toast) {
            window.toast.info('Offline data cleared.');
        }
    }

    getNetworkStatus() {
        return this.networkStatus;
    }

    getPendingRequestsCount() {
        return this.pendingRequests.size;
    }

    getOfflineFormsCount() {
        const offlineForms = JSON.parse(localStorage.getItem('offlineForms') || '[]');
        return offlineForms.length;
    }
}

// Form Validation Enhancement
class FormValidationEnhancer {
    constructor() {
        this.init();
    }

    init() {
        // Setup real-time validation
        this.setupRealtimeValidation();
        
        // Setup form submission validation
        this.setupSubmissionValidation();
    }

    setupRealtimeValidation() {
        document.addEventListener('input', (event) => {
            const field = event.target;
            
            if (field.hasAttribute('required') || field.hasAttribute('data-validate')) {
                this.validateField(field);
            }
        });
        
        document.addEventListener('blur', (event) => {
            const field = event.target;
            
            if (field.hasAttribute('required') || field.hasAttribute('data-validate')) {
                this.validateField(field);
            }
        });
    }

    setupSubmissionValidation() {
        document.addEventListener('submit', (event) => {
            const form = event.target;
            
            if (!this.validateForm(form)) {
                event.preventDefault();
                
                // Focus on first invalid field
                const firstInvalid = form.querySelector('.is-invalid');
                if (firstInvalid) {
                    firstInvalid.focus();
                }
                
                if (window.toast) {
                    window.toast.error('Please correct the errors in the form before submitting.');
                }
            }
        });
    }

    validateField(field) {
        const errors = [];
        
        // Required field validation
        if (field.hasAttribute('required') && !field.value.trim()) {
            errors.push('This field is required.');
        }
        
        // Email validation
        if (field.type === 'email' && field.value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(field.value)) {
                errors.push('Please enter a valid email address.');
            }
        }
        
        // Phone validation
        if (field.type === 'tel' && field.value) {
            const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
            if (!phoneRegex.test(field.value.replace(/\s/g, ''))) {
                errors.push('Please enter a valid phone number.');
            }
        }
        
        // Custom validation
        const customValidation = field.getAttribute('data-validate');
        if (customValidation) {
            const customErrors = this.runCustomValidation(field, customValidation);
            errors.push(...customErrors);
        }
        
        // Update field state
        this.updateFieldState(field, errors);
        
        return errors.length === 0;
    }

    runCustomValidation(field, validationType) {
        const errors = [];
        
        switch (validationType) {
            case 'min-length':
                const minLength = parseInt(field.getAttribute('data-min-length') || '0');
                if (field.value.length < minLength) {
                    errors.push(`Minimum length is ${minLength} characters.`);
                }
                break;
                
            case 'max-length':
                const maxLength = parseInt(field.getAttribute('data-max-length') || '255');
                if (field.value.length > maxLength) {
                    errors.push(`Maximum length is ${maxLength} characters.`);
                }
                break;
                
            case 'numeric':
                if (field.value && !/^\d+$/.test(field.value)) {
                    errors.push('Only numbers are allowed.');
                }
                break;
                
            case 'alphanumeric':
                if (field.value && !/^[a-zA-Z0-9]+$/.test(field.value)) {
                    errors.push('Only letters and numbers are allowed.');
                }
                break;
        }
        
        return errors;
    }

    updateFieldState(field, errors) {
        // Remove existing validation classes
        field.classList.remove('is-valid', 'is-invalid');
        
        // Remove existing feedback
        const existingFeedback = field.parentNode.querySelector('.invalid-feedback');
        if (existingFeedback) {
            existingFeedback.remove();
        }
        
        if (errors.length > 0) {
            // Add invalid state
            field.classList.add('is-invalid');
            
            // Add error feedback
            const feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            feedback.textContent = errors[0]; // Show first error
            field.parentNode.appendChild(feedback);
            
        } else if (field.value.trim()) {
            // Add valid state only if field has value
            field.classList.add('is-valid');
        }
    }

    validateForm(form) {
        let isValid = true;
        
        // Validate all required and data-validate fields
        const fields = form.querySelectorAll('[required], [data-validate]');
        
        fields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });
        
        return isValid;
    }
}

// Initialize systems
let errorRecovery, formValidator;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize error recovery system
    errorRecovery = new ErrorRecoverySystem();
    
    // Initialize form validation enhancer
    formValidator = new FormValidationEnhancer();
    
    // Make globally available
    window.errorRecovery = errorRecovery;
    window.formValidator = formValidator;
    
    // Setup periodic offline form submission attempts
    setInterval(() => {
        if (errorRecovery.getNetworkStatus() === 'online') {
            errorRecovery.submitOfflineForms();
        }
    }, 60000); // Check every minute
});

// CSS for error recovery UI
const errorRecoveryStyles = `
<style>
#offline-indicator {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        transform: translateY(-100%) translateX(-50%);
        opacity: 0;
    }
    to {
        transform: translateY(0) translateX(-50%);
        opacity: 1;
    }
}

.is-valid {
    border-color: #198754;
}

.is-invalid {
    border-color: #dc3545;
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #198754;
}

/* Loading states for network operations */
.network-loading {
    position: relative;
    pointer-events: none;
}

.network-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
}

.network-loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}
</style>
`;

// Inject styles
document.head.insertAdjacentHTML('beforeend', errorRecoveryStyles);