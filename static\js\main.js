// School ERP Main JavaScript

// Global variables
let isOnline = navigator.onLine;
let syncQueue = [];

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    checkOnlineStatus();
    initializeServiceWorker();
    initializeNavigation();
});

// Initialize application
function initializeApp() {
    console.log('School ERP System initialized');
    
    // Add fade-in animation to main content
    const mainContent = document.querySelector('main');
    if (mainContent) {
        mainContent.classList.add('fade-in');
    }
    
    // Initialize tooltips
    initializeTooltips();
    
    // Initialize form validation
    initializeFormValidation();
    
    // Setup CSRF token for AJAX requests
    setupCSRFToken();
}

// Setup event listeners
function setupEventListeners() {
    // Online/Offline status
    window.addEventListener('online', handleOnlineStatus);
    window.addEventListener('offline', handleOfflineStatus);
    
    // Form submissions
    document.addEventListener('submit', handleFormSubmission);
    
    // Navigation clicks
    document.addEventListener('click', handleNavigation);
    
    // Auto-save forms
    setupAutoSave();
}

// Initialize tooltips
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Initialize form validation
function initializeFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
}

// Setup CSRF token for AJAX requests
function setupCSRFToken() {
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
    if (csrfToken) {
        // Setup jQuery AJAX defaults
        if (typeof $ !== 'undefined') {
            $.ajaxSetup({
                beforeSend: function(xhr, settings) {
                    if (!csrfSafeMethod(settings.type) && !this.crossDomain) {
                        xhr.setRequestHeader("X-CSRFToken", csrfToken.value);
                    }
                }
            });
        }
        
        // Setup fetch defaults
        window.fetchWithCSRF = function(url, options = {}) {
            options.headers = options.headers || {};
            options.headers['X-CSRFToken'] = csrfToken.value;
            return fetch(url, options);
        };
    }
}

// Check if HTTP method is CSRF safe
function csrfSafeMethod(method) {
    return (/^(GET|HEAD|OPTIONS|TRACE)$/.test(method));
}

// Handle online status
function handleOnlineStatus() {
    isOnline = true;
    showNotification('You are back online!', 'success');
    syncOfflineData();
    updateOnlineIndicator(true);
}

// Handle offline status
function handleOfflineStatus() {
    isOnline = false;
    showNotification('You are offline. Some features may be limited.', 'warning');
    updateOnlineIndicator(false);
}

// Check online status
function checkOnlineStatus() {
    updateOnlineIndicator(isOnline);
}

// Update online indicator
function updateOnlineIndicator(online) {
    let indicator = document.getElementById('online-indicator');
    if (!indicator) {
        indicator = document.createElement('div');
        indicator.id = 'online-indicator';
        indicator.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            z-index: 9999;
            transition: all 0.3s ease;
        `;
        document.body.appendChild(indicator);
    }
    
    if (online) {
        indicator.textContent = 'Online';
        indicator.style.backgroundColor = '#28a745';
        indicator.style.color = 'white';
    } else {
        indicator.textContent = 'Offline';
        indicator.style.backgroundColor = '#dc3545';
        indicator.style.color = 'white';
    }
}

// Show notification
function showNotification(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = `
        top: 70px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    `;
    
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after duration
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, duration);
}

// Handle form submission
function handleFormSubmission(event) {
    const form = event.target;
    if (!form.matches('form')) return;
    
    // Add loading state to submit button
    const submitBtn = form.querySelector('button[type="submit"], input[type="submit"]');
    if (submitBtn) {
        const originalText = submitBtn.textContent || submitBtn.value;
        submitBtn.disabled = true;
        
        if (submitBtn.tagName === 'BUTTON') {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
        } else {
            submitBtn.value = 'Processing...';
        }
        
        // Restore button after 5 seconds (fallback)
        setTimeout(() => {
            submitBtn.disabled = false;
            if (submitBtn.tagName === 'BUTTON') {
                submitBtn.textContent = originalText;
            } else {
                submitBtn.value = originalText;
            }
        }, 5000);
    }
    
    // If offline, queue the form data
    if (!isOnline) {
        event.preventDefault();
        queueFormData(form);
        showNotification('Form saved offline. Will sync when online.', 'info');
    }
}

// Queue form data for offline sync
function queueFormData(form) {
    const formData = new FormData(form);
    const data = {
        url: form.action || window.location.href,
        method: form.method || 'POST',
        data: Object.fromEntries(formData),
        timestamp: new Date().toISOString()
    };
    
    syncQueue.push(data);
    localStorage.setItem('syncQueue', JSON.stringify(syncQueue));
}

// Sync offline data when back online
function syncOfflineData() {
    const storedQueue = localStorage.getItem('syncQueue');
    if (storedQueue) {
        syncQueue = JSON.parse(storedQueue);
        
        syncQueue.forEach(async (item, index) => {
            try {
                const response = await fetchWithCSRF(item.url, {
                    method: item.method,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(item.data)
                });
                
                if (response.ok) {
                    syncQueue.splice(index, 1);
                    showNotification('Data synced successfully!', 'success');
                }
            } catch (error) {
                console.error('Sync failed:', error);
            }
        });
        
        localStorage.setItem('syncQueue', JSON.stringify(syncQueue));
    }
}

// Handle navigation
function handleNavigation(event) {
    const link = event.target.closest('a');
    if (!link) return;
    
    // Add loading state to navigation links
    if (link.href && !link.href.startsWith('#') && !link.target) {
        const icon = link.querySelector('i');
        if (icon) {
            icon.className = 'fas fa-spinner fa-spin';
        }
    }
}

// Setup auto-save for forms
function setupAutoSave() {
    const forms = document.querySelectorAll('form[data-autosave]');
    
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            input.addEventListener('input', debounce(() => {
                saveFormData(form);
            }, 1000));
        });
        
        // Load saved data
        loadFormData(form);
    });
}

// Save form data to localStorage
function saveFormData(form) {
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);
    const formId = form.id || form.action;
    
    localStorage.setItem(`autosave_${formId}`, JSON.stringify(data));
    
    // Show save indicator
    showSaveIndicator();
}

// Load form data from localStorage
function loadFormData(form) {
    const formId = form.id || form.action;
    const savedData = localStorage.getItem(`autosave_${formId}`);
    
    if (savedData) {
        const data = JSON.parse(savedData);
        
        Object.keys(data).forEach(key => {
            const input = form.querySelector(`[name="${key}"]`);
            if (input && input.type !== 'password') {
                input.value = data[key];
            }
        });
    }
}

// Show save indicator
function showSaveIndicator() {
    let indicator = document.getElementById('save-indicator');
    if (!indicator) {
        indicator = document.createElement('div');
        indicator.id = 'save-indicator';
        indicator.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 10px 15px;
            background: #28a745;
            color: white;
            border-radius: 20px;
            font-size: 14px;
            z-index: 9999;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        indicator.textContent = 'Saved';
        document.body.appendChild(indicator);
    }
    
    indicator.style.opacity = '1';
    setTimeout(() => {
        indicator.style.opacity = '0';
    }, 2000);
}

// Debounce function
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Initialize Service Worker for offline support
function initializeServiceWorker() {
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('/static/js/sw.js')
            .then(registration => {
                console.log('Service Worker registered:', registration);
            })
            .catch(error => {
                console.log('Service Worker registration failed:', error);
            });
    }
}

// Utility functions
const Utils = {
    // Format currency
    formatCurrency: function(amount, currency = 'USD') {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency
        }).format(amount);
    },
    
    // Format date
    formatDate: function(date, locale = 'en-US') {
        return new Intl.DateTimeFormat(locale).format(new Date(date));
    },
    
    // Validate email
    validateEmail: function(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    },
    
    // Generate random ID
    generateId: function() {
        return Math.random().toString(36).substr(2, 9);
    },
    
    // Copy to clipboard
    copyToClipboard: function(text) {
        navigator.clipboard.writeText(text).then(() => {
            showNotification('Copied to clipboard!', 'success', 2000);
        });
    }
};

// Initialize navigation enhancements
function initializeNavigation() {
    // Add smooth scrolling to anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            const href = this.getAttribute('href');
            
            // Skip if href is just '#' or empty
            if (!href || href === '#' || href.length <= 1) {
                return;
            }
            
            e.preventDefault();
            
            try {
                const target = document.querySelector(href);
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            } catch (error) {
                console.warn('Invalid selector for smooth scrolling:', href);
            }
        });
    });

    // Add loading states to navigation links
    document.querySelectorAll('.section-nav-item, .breadcrumb-link').forEach(link => {
        link.addEventListener('click', function() {
            if (!this.classList.contains('active')) {
                const icon = this.querySelector('i');
                if (icon && !icon.classList.contains('fa-spin')) {
                    const originalClass = icon.className;
                    icon.className = 'fas fa-spinner fa-spin';

                    // Restore original icon after navigation
                    setTimeout(() => {
                        icon.className = originalClass;
                    }, 2000);
                }
            }
        });
    });

    // Add keyboard navigation support
    document.addEventListener('keydown', function(e) {
        // Alt + number keys for quick section navigation
        if (e.altKey && e.key >= '1' && e.key <= '7') {
            e.preventDefault();
            const sectionIndex = parseInt(e.key) - 1;
            const sectionLinks = document.querySelectorAll('.section-nav-item');
            if (sectionLinks[sectionIndex]) {
                sectionLinks[sectionIndex].click();
            }
        }
    });

    // Add progress indicator for page loads
    const progressBar = document.createElement('div');
    progressBar.id = 'page-load-progress';
    progressBar.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 0%;
        height: 3px;
        background: linear-gradient(90deg, #4c63d2, #6c5ce7);
        z-index: 9999;
        transition: width 0.3s ease;
        opacity: 0;
    `;
    document.body.appendChild(progressBar);

    // Show progress on navigation
    document.addEventListener('click', function(e) {
        const link = e.target.closest('a');
        if (link && link.href && !link.href.startsWith('#') && !link.target) {
            showPageLoadProgress();
        }
    });

    // Add section indicators
    updateSectionIndicators();
}

// Show page load progress
function showPageLoadProgress() {
    const progressBar = document.getElementById('page-load-progress');
    if (progressBar) {
        progressBar.style.opacity = '1';
        progressBar.style.width = '30%';

        setTimeout(() => {
            progressBar.style.width = '70%';
        }, 200);

        setTimeout(() => {
            progressBar.style.width = '100%';
            setTimeout(() => {
                progressBar.style.opacity = '0';
                progressBar.style.width = '0%';
            }, 300);
        }, 800);
    }
}

// Update section indicators based on current page
function updateSectionIndicators() {
    const currentPath = window.location.pathname;
    const sectionItems = document.querySelectorAll('.section-nav-item');

    sectionItems.forEach(item => {
        const href = item.getAttribute('href');
        if (href && currentPath.startsWith(href.split('/')[1])) {
            item.classList.add('active');
        }
    });
}

// Add notification for keyboard shortcuts
function showKeyboardShortcuts() {
    const shortcuts = [
        'Alt + 1-7: Quick section navigation',
        'Ctrl + /: Search',
        'Esc: Close modals/dropdowns'
    ];

    const shortcutHtml = shortcuts.map(shortcut => `<li>${shortcut}</li>`).join('');

    showNotification(`
        <strong>Keyboard Shortcuts:</strong>
        <ul style="margin: 0.5rem 0 0 0; padding-left: 1rem;">
            ${shortcutHtml}
        </ul>
    `, 'info', 8000);
}

// Export for use in other scripts
window.SchoolERP = {
    showNotification,
    Utils,
    isOnline: () => isOnline,
    syncOfflineData,
    showKeyboardShortcuts,
    updateSectionIndicators
};
