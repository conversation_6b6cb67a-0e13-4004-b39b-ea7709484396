/**
 * Mobile JavaScript Framework for School ERP
 * Provides touch-friendly interactions and mobile-specific functionality
 */

class MobileFramework {
    constructor() {
        this.init();
        this.bindEvents();
        this.setupTouchHandlers();
        this.initializeComponents();
    }

    init() {
        // Add mobile class to body
        document.body.classList.add('mobile-framework');
        
        // Set viewport meta tag if not present
        if (!document.querySelector('meta[name="viewport"]')) {
            const viewport = document.createElement('meta');
            viewport.name = 'viewport';
            viewport.content = 'width=device-width, initial-scale=1, shrink-to-fit=no';
            document.head.appendChild(viewport);
        }
        
        // Detect mobile device
        this.isMobile = this.detectMobile();
        this.isTouch = this.detectTouch();
        
        if (this.isMobile) {
            document.body.classList.add('is-mobile');
        }
        
        if (this.isTouch) {
            document.body.classList.add('is-touch');
        }
    }

    detectMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    detectTouch() {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    }

    bindEvents() {
        // Mobile navigation toggle
        document.addEventListener('click', (e) => {
            if (e.target.closest('.navbar-toggler-mobile')) {
                this.toggleMobileNav();
            }
            
            if (e.target.closest('.navbar-close-mobile') || e.target.closest('.navbar-overlay-mobile')) {
                this.closeMobileNav();
            }
            
            // Submenu toggle
            if (e.target.closest('.nav-item-mobile.has-submenu > .nav-link-mobile')) {
                e.preventDefault();
                this.toggleSubmenu(e.target.closest('.nav-item-mobile'));
            }
            
            // Modal triggers
            if (e.target.closest('[data-toggle="modal"]')) {
                const target = e.target.closest('[data-toggle="modal"]').getAttribute('data-target');
                this.showModal(target);
            }
            
            if (e.target.closest('.modal-backdrop-mobile') || e.target.closest('[data-dismiss="modal"]')) {
                this.hideModal();
            }
        });

        // Handle form submissions
        document.addEventListener('submit', (e) => {
            if (e.target.classList.contains('mobile-form')) {
                this.handleMobileForm(e);
            }
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // Handle orientation change
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.handleOrientationChange();
            }, 100);
        });

        // Handle scroll
        let scrollTimeout;
        window.addEventListener('scroll', () => {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                this.handleScroll();
            }, 100);
        });
    }

    setupTouchHandlers() {
        if (!this.isTouch) return;

        // Add touch feedback to buttons
        document.addEventListener('touchstart', (e) => {
            if (e.target.closest('.btn-mobile, .nav-link-mobile, .list-group-item-mobile')) {
                e.target.closest('.btn-mobile, .nav-link-mobile, .list-group-item-mobile').classList.add('touch-active');
            }
        });

        document.addEventListener('touchend', (e) => {
            setTimeout(() => {
                document.querySelectorAll('.touch-active').forEach(el => {
                    el.classList.remove('touch-active');
                });
            }, 150);
        });

        // Swipe gestures
        let startX, startY, startTime;
        
        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
            startTime = Date.now();
        });

        document.addEventListener('touchend', (e) => {
            if (!startX || !startY) return;

            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            const endTime = Date.now();

            const deltaX = endX - startX;
            const deltaY = endY - startY;
            const deltaTime = endTime - startTime;

            // Swipe detection
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50 && deltaTime < 300) {
                if (deltaX > 0) {
                    this.handleSwipeRight(e);
                } else {
                    this.handleSwipeLeft(e);
                }
            }

            startX = startY = null;
        });
    }

    initializeComponents() {
        // Initialize tooltips
        this.initTooltips();
        
        // Initialize dropdowns
        this.initDropdowns();
        
        // Initialize tabs
        this.initTabs();
        
        // Initialize accordions
        this.initAccordions();
        
        // Initialize infinite scroll
        this.initInfiniteScroll();
        
        // Initialize pull to refresh
        this.initPullToRefresh();
    }

    toggleMobileNav() {
        const navbar = document.querySelector('.navbar-collapse-mobile');
        const overlay = document.querySelector('.navbar-overlay-mobile');
        
        if (navbar && overlay) {
            navbar.classList.toggle('show');
            overlay.classList.toggle('show');
            document.body.classList.toggle('nav-open');
        }
    }

    closeMobileNav() {
        const navbar = document.querySelector('.navbar-collapse-mobile');
        const overlay = document.querySelector('.navbar-overlay-mobile');
        
        if (navbar && overlay) {
            navbar.classList.remove('show');
            overlay.classList.remove('show');
            document.body.classList.remove('nav-open');
        }
    }

    toggleSubmenu(item) {
        const submenu = item.querySelector('.nav-submenu-mobile');
        if (submenu) {
            submenu.classList.toggle('show');
            item.classList.toggle('submenu-open');
        }
    }

    showModal(target) {
        const modal = document.querySelector(target);
        if (modal) {
            modal.classList.add('show');
            document.body.classList.add('modal-open');
            
            // Create backdrop if not exists
            if (!modal.querySelector('.modal-backdrop-mobile')) {
                const backdrop = document.createElement('div');
                backdrop.className = 'modal-backdrop-mobile';
                modal.appendChild(backdrop);
            }
        }
    }

    hideModal() {
        const modal = document.querySelector('.modal-mobile.show');
        if (modal) {
            modal.classList.remove('show');
            document.body.classList.remove('modal-open');
        }
    }

    handleMobileForm(e) {
        const form = e.target;
        const submitBtn = form.querySelector('[type="submit"]');
        
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
            
            // Re-enable after 3 seconds (fallback)
            setTimeout(() => {
                submitBtn.disabled = false;
                submitBtn.innerHTML = submitBtn.getAttribute('data-original-text') || 'Submit';
            }, 3000);
        }
    }

    handleResize() {
        // Close mobile nav on resize to desktop
        if (window.innerWidth >= 992) {
            this.closeMobileNav();
        }
        
        // Adjust modal positioning
        this.adjustModals();
    }

    handleOrientationChange() {
        // Force repaint to fix iOS orientation bugs
        document.body.style.height = '100.1%';
        setTimeout(() => {
            document.body.style.height = '';
        }, 500);
        
        // Close mobile nav on orientation change
        this.closeMobileNav();
    }

    handleScroll() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // Hide/show navbar on scroll
        const navbar = document.querySelector('.navbar-mobile');
        if (navbar) {
            if (scrollTop > 100) {
                navbar.classList.add('navbar-scrolled');
            } else {
                navbar.classList.remove('navbar-scrolled');
            }
        }
        
        // Update scroll position for other components
        this.updateScrollPosition(scrollTop);
    }

    handleSwipeRight(e) {
        // Open mobile nav on swipe right from left edge
        if (e.changedTouches[0].clientX < 50) {
            this.toggleMobileNav();
        }
    }

    handleSwipeLeft(e) {
        // Close mobile nav on swipe left
        if (document.querySelector('.navbar-collapse-mobile.show')) {
            this.closeMobileNav();
        }
    }

    initTooltips() {
        // Simple tooltip implementation
        document.querySelectorAll('[data-toggle="tooltip"]').forEach(el => {
            el.addEventListener('mouseenter', this.showTooltip.bind(this));
            el.addEventListener('mouseleave', this.hideTooltip.bind(this));
            el.addEventListener('touchstart', this.showTooltip.bind(this));
            el.addEventListener('touchend', () => {
                setTimeout(() => this.hideTooltip(), 2000);
            });
        });
    }

    showTooltip(e) {
        const element = e.target;
        const text = element.getAttribute('title') || element.getAttribute('data-title');
        
        if (!text) return;
        
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip-mobile';
        tooltip.textContent = text;
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
        
        element._tooltip = tooltip;
    }

    hideTooltip(e) {
        if (e && e.target._tooltip) {
            e.target._tooltip.remove();
            delete e.target._tooltip;
        }
    }

    initDropdowns() {
        document.addEventListener('click', (e) => {
            if (e.target.closest('[data-toggle="dropdown"]')) {
                e.preventDefault();
                const dropdown = e.target.closest('.dropdown-mobile');
                const menu = dropdown.querySelector('.dropdown-menu-mobile');
                
                // Close other dropdowns
                document.querySelectorAll('.dropdown-menu-mobile.show').forEach(m => {
                    if (m !== menu) m.classList.remove('show');
                });
                
                menu.classList.toggle('show');
            } else {
                // Close all dropdowns when clicking outside
                document.querySelectorAll('.dropdown-menu-mobile.show').forEach(m => {
                    m.classList.remove('show');
                });
            }
        });
    }

    initTabs() {
        document.addEventListener('click', (e) => {
            if (e.target.closest('[data-toggle="tab"]')) {
                e.preventDefault();
                const tab = e.target.closest('[data-toggle="tab"]');
                const target = tab.getAttribute('href') || tab.getAttribute('data-target');
                
                // Remove active from all tabs and panes
                const tabContainer = tab.closest('.nav-tabs-mobile');
                tabContainer.querySelectorAll('.nav-link-mobile').forEach(t => t.classList.remove('active'));
                
                const contentContainer = document.querySelector('.tab-content-mobile');
                contentContainer.querySelectorAll('.tab-pane-mobile').forEach(p => p.classList.remove('active'));
                
                // Add active to current tab and pane
                tab.classList.add('active');
                document.querySelector(target).classList.add('active');
            }
        });
    }

    initAccordions() {
        document.addEventListener('click', (e) => {
            if (e.target.closest('[data-toggle="collapse"]')) {
                e.preventDefault();
                const trigger = e.target.closest('[data-toggle="collapse"]');
                const target = trigger.getAttribute('href') || trigger.getAttribute('data-target');
                const collapse = document.querySelector(target);
                
                if (collapse) {
                    collapse.classList.toggle('show');
                    trigger.classList.toggle('collapsed');
                }
            }
        });
    }

    initInfiniteScroll() {
        const containers = document.querySelectorAll('[data-infinite-scroll]');
        
        containers.forEach(container => {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadMoreContent(container);
                    }
                });
            }, { threshold: 0.1 });
            
            const sentinel = container.querySelector('.infinite-scroll-sentinel');
            if (sentinel) {
                observer.observe(sentinel);
            }
        });
    }

    initPullToRefresh() {
        if (!this.isTouch) return;
        
        let startY = 0;
        let currentY = 0;
        let pulling = false;
        
        document.addEventListener('touchstart', (e) => {
            if (window.scrollY === 0) {
                startY = e.touches[0].clientY;
                pulling = true;
            }
        });
        
        document.addEventListener('touchmove', (e) => {
            if (!pulling) return;
            
            currentY = e.touches[0].clientY;
            const pullDistance = currentY - startY;
            
            if (pullDistance > 0 && pullDistance < 100) {
                e.preventDefault();
                this.updatePullToRefresh(pullDistance);
            }
        });
        
        document.addEventListener('touchend', () => {
            if (pulling && currentY - startY > 60) {
                this.triggerRefresh();
            }
            
            pulling = false;
            this.resetPullToRefresh();
        });
    }

    loadMoreContent(container) {
        const url = container.getAttribute('data-infinite-scroll');
        const page = parseInt(container.getAttribute('data-page') || '1') + 1;
        
        fetch(`${url}?page=${page}`)
            .then(response => response.text())
            .then(html => {
                const temp = document.createElement('div');
                temp.innerHTML = html;
                
                const newItems = temp.querySelectorAll('.infinite-scroll-item');
                newItems.forEach(item => {
                    container.insertBefore(item, container.querySelector('.infinite-scroll-sentinel'));
                });
                
                container.setAttribute('data-page', page);
            })
            .catch(error => {
                console.error('Error loading more content:', error);
            });
    }

    updatePullToRefresh(distance) {
        const indicator = document.querySelector('.pull-to-refresh-indicator');
        if (indicator) {
            indicator.style.transform = `translateY(${distance}px)`;
            indicator.style.opacity = Math.min(distance / 60, 1);
        }
    }

    triggerRefresh() {
        // Trigger page refresh or custom refresh logic
        if (typeof window.onPullToRefresh === 'function') {
            window.onPullToRefresh();
        } else {
            window.location.reload();
        }
    }

    resetPullToRefresh() {
        const indicator = document.querySelector('.pull-to-refresh-indicator');
        if (indicator) {
            indicator.style.transform = '';
            indicator.style.opacity = '';
        }
    }

    adjustModals() {
        const modals = document.querySelectorAll('.modal-mobile.show');
        modals.forEach(modal => {
            const dialog = modal.querySelector('.modal-dialog-mobile');
            if (dialog) {
                // Center modal vertically
                const modalHeight = dialog.offsetHeight;
                const windowHeight = window.innerHeight;
                
                if (modalHeight < windowHeight) {
                    dialog.style.marginTop = Math.max(0, (windowHeight - modalHeight) / 2) + 'px';
                } else {
                    dialog.style.marginTop = '10px';
                }
            }
        });
    }

    updateScrollPosition(scrollTop) {
        // Update any scroll-dependent components
        document.documentElement.style.setProperty('--scroll-top', scrollTop + 'px');
    }

    // Utility methods
    showAlert(message, type = 'info', duration = 3000) {
        const alert = document.createElement('div');
        alert.className = `alert-mobile alert-${type}-mobile alert-dismissible`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, duration);
    }

    showLoading(message = 'Loading...') {
        const loading = document.createElement('div');
        loading.className = 'loading-overlay-mobile';
        loading.innerHTML = `
            <div class="loading-content-mobile">
                <div class="spinner-mobile"></div>
                <div class="loading-text-mobile">${message}</div>
            </div>
        `;
        
        document.body.appendChild(loading);
        document.body.classList.add('loading-active');
        
        return loading;
    }

    hideLoading() {
        const loading = document.querySelector('.loading-overlay-mobile');
        if (loading) {
            loading.remove();
            document.body.classList.remove('loading-active');
        }
    }

    vibrate(pattern = [100]) {
        if ('vibrate' in navigator) {
            navigator.vibrate(pattern);
        }
    }

    // API helpers for mobile
    async fetchWithLoading(url, options = {}) {
        const loading = this.showLoading();
        
        try {
            const response = await fetch(url, options);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || 'Request failed');
            }
            
            return data;
        } catch (error) {
            this.showAlert(error.message, 'danger');
            throw error;
        } finally {
            this.hideLoading();
        }
    }
}

// Initialize mobile framework when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.mobileFramework = new MobileFramework();
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MobileFramework;
}