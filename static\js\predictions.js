/**
 * Predictive Analytics JavaScript
 * Handles prediction charts, updates, and interactions
 */

let predictionData = {};
let charts = {};

function initializePredictions() {
    // Load prediction data
    const dataScript = document.getElementById('predictionData');
    if (dataScript) {
        try {
            predictionData = JSON.parse(dataScript.textContent);
        } catch (e) {
            console.error('Failed to parse prediction data:', e);
            predictionData = {};
        }
    }
    
    // Initialize charts
    initializeCharts();
    
    // Setup auto-refresh if enabled
    setupAutoRefresh();
}

function initializeCharts() {
    // Initialize enrollment chart
    if (predictionData.enrollment && predictionData.enrollment.success) {
        createEnrollmentChart();
    }
    
    // Initialize revenue chart
    if (predictionData.revenue && predictionData.revenue.success) {
        createRevenueChart();
    }
    
    // Initialize attendance chart
    if (predictionData.attendance && predictionData.attendance.success) {
        createAttendanceChart();
    }
}

function createEnrollmentChart() {
    const canvas = document.getElementById('enrollmentChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const predictions = predictionData.enrollment.predictions || [];
    
    const labels = predictions.map(p => p.month);
    const data = predictions.map(p => p.predicted_enrollment);
    const confidence = predictions.map(p => p.confidence * 100);
    
    charts.enrollment = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Predicted Enrollment',
                data: data,
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#667eea',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        afterLabel: function(context) {
                            const index = context.dataIndex;
                            return `Confidence: ${confidence[index]}%`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
}

function createRevenueChart() {
    const canvas = document.getElementById('revenueChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const predictions = predictionData.revenue.predictions || [];
    
    const labels = predictions.map(p => p.month);
    const data = predictions.map(p => p.predicted_revenue);
    
    charts.revenue = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Predicted Revenue',
                data: data,
                backgroundColor: 'rgba(17, 153, 142, 0.8)',
                borderColor: '#11998e',
                borderWidth: 1,
                borderRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `Revenue: $${context.parsed.y.toLocaleString()}`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
}

function createAttendanceChart() {
    const canvas = document.getElementById('attendanceChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const predictions = predictionData.attendance.predictions || [];
    
    // Group predictions by week for better visualization
    const weeklyData = groupPredictionsByWeek(predictions);
    
    const labels = weeklyData.map(w => w.week);
    const attendanceRates = weeklyData.map(w => w.avgAttendanceRate);
    
    charts.attendance = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Predicted Attendance Rate',
                data: attendanceRates,
                borderColor: '#f5576c',
                backgroundColor: 'rgba(245, 87, 108, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#f5576c',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 5
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `Attendance Rate: ${context.parsed.y.toFixed(1)}%`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
}

function groupPredictionsByWeek(predictions) {
    const weeks = {};
    
    predictions.forEach(pred => {
        const date = new Date(pred.date);
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        const weekKey = weekStart.toISOString().split('T')[0];
        
        if (!weeks[weekKey]) {
            weeks[weekKey] = {
                week: weekStart.toLocaleDateString(),
                rates: [],
                avgAttendanceRate: 0
            };
        }
        
        weeks[weekKey].rates.push(pred.predicted_attendance_rate);
    });
    
    // Calculate averages
    Object.keys(weeks).forEach(weekKey => {
        const week = weeks[weekKey];
        week.avgAttendanceRate = week.rates.reduce((sum, rate) => sum + rate, 0) / week.rates.length;
    });
    
    return Object.values(weeks).sort((a, b) => new Date(a.week) - new Date(b.week));
}

function refreshPredictions() {
    showLoadingOverlay();
    
    // Make API call to refresh all predictions
    fetch('/reports/api/predictive/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken(),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            action: 'get_alerts'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Refresh individual predictions
            refreshEnrollmentPrediction();
            refreshRevenuePrediction();
            refreshPerformancePrediction();
            refreshAttendancePrediction();
            
            showMessage('Predictions refreshed successfully', 'success');
        } else {
            showMessage('Failed to refresh predictions: ' + (data.error || 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        console.error('Error refreshing predictions:', error);
        showMessage('Error refreshing predictions', 'error');
    })
    .finally(() => {
        hideLoadingOverlay();
    });
}

function refreshEnrollmentPrediction() {
    const period = document.getElementById('predictionPeriod').value;
    
    fetch('/reports/api/predictive/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken(),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            action: 'predict_enrollment',
            months_ahead: parseInt(period)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            predictionData.enrollment = data.data;
            updateEnrollmentCard();
            if (charts.enrollment) {
                charts.enrollment.destroy();
            }
            createEnrollmentChart();
        }
    })
    .catch(error => {
        console.error('Error refreshing enrollment prediction:', error);
    });
}

function refreshRevenuePrediction() {
    const period = document.getElementById('predictionPeriod').value;
    
    fetch('/reports/api/predictive/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken(),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            action: 'predict_revenue',
            months_ahead: parseInt(period)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            predictionData.revenue = data.data;
            updateRevenueCard();
            if (charts.revenue) {
                charts.revenue.destroy();
            }
            createRevenueChart();
        }
    })
    .catch(error => {
        console.error('Error refreshing revenue prediction:', error);
    });
}

function refreshPerformancePrediction() {
    fetch('/reports/api/predictive/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken(),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            action: 'predict_performance'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            predictionData.performance = data.data;
            updatePerformanceCard();
        }
    })
    .catch(error => {
        console.error('Error refreshing performance prediction:', error);
    });
}

function refreshAttendancePrediction() {
    fetch('/reports/api/predictive/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken(),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            action: 'predict_attendance',
            months_ahead: 3
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            predictionData.attendance = data.data;
            updateAttendanceCard();
            if (charts.attendance) {
                charts.attendance.destroy();
            }
            createAttendanceChart();
        }
    })
    .catch(error => {
        console.error('Error refreshing attendance prediction:', error);
    });
}

function updatePredictions() {
    const period = document.getElementById('predictionPeriod').value;
    const confidence = document.getElementById('confidenceLevel').value;
    const modelType = document.getElementById('modelType').value;
    
    showLoadingOverlay();
    
    // Update all predictions with new parameters
    Promise.all([
        updatePredictionWithParams('predict_enrollment', { months_ahead: parseInt(period) }),
        updatePredictionWithParams('predict_revenue', { months_ahead: parseInt(period) }),
        updatePredictionWithParams('predict_performance', {}),
        updatePredictionWithParams('predict_attendance', { months_ahead: 3 })
    ])
    .then(() => {
        showMessage('Predictions updated successfully', 'success');
        // Refresh charts
        initializeCharts();
    })
    .catch(error => {
        console.error('Error updating predictions:', error);
        showMessage('Error updating predictions', 'error');
    })
    .finally(() => {
        hideLoadingOverlay();
    });
}

function updatePredictionWithParams(action, params) {
    return fetch('/reports/api/predictive/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken(),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            action: action,
            ...params
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update prediction data based on action
            if (action === 'predict_enrollment') {
                predictionData.enrollment = data.data;
            } else if (action === 'predict_revenue') {
                predictionData.revenue = data.data;
            } else if (action === 'predict_performance') {
                predictionData.performance = data.data;
            } else if (action === 'predict_attendance') {
                predictionData.attendance = data.data;
            }
        }
        return data;
    });
}

function updateEnrollmentCard() {
    // Update enrollment card content
    const card = document.getElementById('enrollmentCard');
    if (!card || !predictionData.enrollment || !predictionData.enrollment.success) return;
    
    // Update metrics in the card
    const predictions = predictionData.enrollment.predictions || [];
    const metricElements = card.querySelectorAll('.metric-value');
    
    predictions.slice(0, 3).forEach((pred, index) => {
        if (metricElements[index]) {
            metricElements[index].textContent = pred.predicted_enrollment;
        }
    });
}

function updateRevenueCard() {
    // Update revenue card content
    const card = document.getElementById('revenueCard');
    if (!card || !predictionData.revenue || !predictionData.revenue.success) return;
    
    // Update trend indicator
    const trendIndicator = card.querySelector('.trend-indicator');
    if (trendIndicator && predictionData.revenue.trends) {
        const trends = predictionData.revenue.trends;
        trendIndicator.className = `trend-indicator trend-${trends.trend}`;
        
        const icon = trendIndicator.querySelector('i');
        if (icon) {
            icon.className = trends.trend === 'increasing' ? 'fas fa-arrow-up me-1' :
                           trends.trend === 'decreasing' ? 'fas fa-arrow-down me-1' :
                           'fas fa-minus me-1';
        }
        
        const text = trendIndicator.childNodes[trendIndicator.childNodes.length - 1];
        if (text) {
            text.textContent = `${trends.growth_rate.toFixed(1)}%`;
        }
    }
}

function updatePerformanceCard() {
    // Update performance card content
    const card = document.getElementById('performanceCard');
    if (!card || !predictionData.performance || !predictionData.performance.success) return;
    
    // Update at-risk students count
    const atRiskStudents = predictionData.performance.at_risk_students || [];
    const alertElement = card.querySelector('.alert');
    
    if (alertElement) {
        const countElement = alertElement.querySelector('p');
        if (countElement) {
            countElement.textContent = `${atRiskStudents.length} students identified as at-risk`;
        }
    }
}

function updateAttendanceCard() {
    // Update attendance card content
    const card = document.getElementById('attendanceCard');
    if (!card || !predictionData.attendance || !predictionData.attendance.success) return;
    
    // Update pattern information
    const patterns = predictionData.attendance.patterns;
    if (patterns) {
        const metricElements = card.querySelectorAll('.metric-value');
        if (metricElements[0]) {
            metricElements[0].textContent = patterns.seasonal_trends.highest_month;
        }
        if (metricElements[1]) {
            metricElements[1].textContent = patterns.day_of_week_patterns.worst_day;
        }
    }
}

function showPredictionSettings() {
    const modal = new bootstrap.Modal(document.getElementById('predictionSettingsModal'));
    modal.show();
}

function savePredictionSettings() {
    const autoRefresh = document.getElementById('autoRefreshInterval').value;
    const enableNotifications = document.getElementById('enableNotifications').checked;
    const alertThreshold = document.getElementById('alertThreshold').value;
    
    // Save settings (this would typically make an API call)
    const settings = {
        autoRefreshInterval: parseInt(autoRefresh),
        enableNotifications: enableNotifications,
        alertThreshold: parseInt(alertThreshold)
    };
    
    // Store in localStorage for now
    localStorage.setItem('predictionSettings', JSON.stringify(settings));
    
    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('predictionSettingsModal'));
    modal.hide();
    
    // Setup auto-refresh with new settings
    setupAutoRefresh();
    
    showMessage('Settings saved successfully', 'success');
}

function setupAutoRefresh() {
    // Clear existing interval
    if (window.predictionRefreshInterval) {
        clearInterval(window.predictionRefreshInterval);
    }
    
    // Get settings
    const settings = JSON.parse(localStorage.getItem('predictionSettings') || '{}');
    const interval = settings.autoRefreshInterval || 0;
    
    if (interval > 0) {
        window.predictionRefreshInterval = setInterval(() => {
            refreshPredictions();
        }, interval * 1000);
    }
}

function showLoadingOverlay() {
    const cards = document.querySelectorAll('.prediction-card');
    cards.forEach(card => {
        const overlay = document.createElement('div');
        overlay.className = 'loading-overlay';
        overlay.innerHTML = `
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        `;
        card.style.position = 'relative';
        card.appendChild(overlay);
    });
}

function hideLoadingOverlay() {
    const overlays = document.querySelectorAll('.loading-overlay');
    overlays.forEach(overlay => {
        overlay.remove();
    });
}

function getCsrfToken() {
    const token = document.querySelector('[name=csrfmiddlewaretoken]');
    return token ? token.value : '';
}

function showMessage(message, type = 'info') {
    // Create toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}

// Export functions for global access
window.initializePredictions = initializePredictions;
window.refreshPredictions = refreshPredictions;
window.updatePredictions = updatePredictions;
window.showPredictionSettings = showPredictionSettings;
window.savePredictionSettings = savePredictionSettings;