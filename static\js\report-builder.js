/**
 * Report Builder JavaScript
 * Handles drag-and-drop functionality and query building
 */

let reportBuilderState = {
    fields: [],
    filters: [],
    grouping: [],
    sorting: [],
    aggregations: [],
    dataSources: []
};

let availableModels = {};
let operators = {
    'equals': '=',
    'not_equals': '!=',
    'greater_than': '>',
    'less_than': '<',
    'greater_equal': '>=',
    'less_equal': '<=',
    'contains': 'LIKE',
    'starts_with': 'LIKE',
    'ends_with': 'LIKE',
    'in': 'IN',
    'not_in': 'NOT IN',
    'is_null': 'IS NULL',
    'is_not_null': 'IS NOT NULL',
    'between': 'BETWEEN'
};

function initializeReportBuilder() {
    // Load configuration from page
    loadConfiguration();
    
    // Initialize drag and drop
    initializeDragAndDrop();
    
    // Initialize drop zones
    initializeDropZones();
    
    // Load existing configuration if available
    loadExistingConfiguration();
    
    // Update SQL preview
    updateSQLPreview();
}

function loadConfiguration() {
    // Load available models
    const modelsScript = document.getElementById('availableModels');
    if (modelsScript) {
        try {
            availableModels = JSON.parse(modelsScript.textContent);
        } catch (e) {
            console.error('Failed to load available models:', e);
        }
    }
}

function initializeDragAndDrop() {
    // Make field items draggable
    const fieldItems = document.querySelectorAll('.field-item');
    fieldItems.forEach(item => {
        item.addEventListener('dragstart', handleDragStart);
        item.addEventListener('dragend', handleDragEnd);
    });
}

function initializeDropZones() {
    const dropZones = [
        { id: 'fieldsDropZone', type: 'fields' },
        { id: 'groupingDropZone', type: 'grouping' },
        { id: 'sortingDropZone', type: 'sorting' }
    ];
    
    dropZones.forEach(zone => {
        const element = document.getElementById(zone.id);
        if (element) {
            element.addEventListener('dragover', handleDragOver);
            element.addEventListener('drop', (e) => handleDrop(e, zone.type));
            element.addEventListener('dragenter', handleDragEnter);
            element.addEventListener('dragleave', handleDragLeave);
        }
    });
}

function handleDragStart(e) {
    e.target.classList.add('dragging');
    
    const fieldData = {
        model: e.target.dataset.model,
        field: e.target.dataset.field,
        type: e.target.dataset.type,
        verboseName: e.target.dataset.verboseName
    };
    
    e.dataTransfer.setData('application/json', JSON.stringify(fieldData));
    e.dataTransfer.effectAllowed = 'copy';
}

function handleDragEnd(e) {
    e.target.classList.remove('dragging');
}

function handleDragOver(e) {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
}

function handleDragEnter(e) {
    e.preventDefault();
    e.target.classList.add('drag-over');
}

function handleDragLeave(e) {
    e.target.classList.remove('drag-over');
}

function handleDrop(e, dropType) {
    e.preventDefault();
    e.target.classList.remove('drag-over');
    
    try {
        const fieldData = JSON.parse(e.dataTransfer.getData('application/json'));
        addFieldToDropZone(fieldData, dropType);
        updateSQLPreview();
    } catch (error) {
        console.error('Error handling drop:', error);
    }
}

function addFieldToDropZone(fieldData, dropType) {
    const dropZone = document.getElementById(`${dropType}DropZone`);
    
    // Check if field already exists
    const existingField = Array.from(dropZone.children).find(child => 
        child.dataset && child.dataset.field === fieldData.field && child.dataset.model === fieldData.model
    );
    
    if (existingField) {
        return; // Field already added
    }
    
    // Create dropped field element
    const droppedField = createDroppedFieldElement(fieldData, dropType);
    
    // Add to drop zone
    if (dropZone.querySelector('p')) {
        dropZone.innerHTML = ''; // Remove placeholder text
    }
    
    dropZone.appendChild(droppedField);
    dropZone.classList.add('has-items');
    
    // Update state
    updateReportBuilderState(fieldData, dropType, 'add');
}

function createDroppedFieldElement(fieldData, dropType) {
    const div = document.createElement('div');
    div.className = 'dropped-field';
    div.dataset.model = fieldData.model;
    div.dataset.field = fieldData.field;
    div.dataset.type = fieldData.type;
    
    let content = `
        <i class="fas fa-grip-vertical me-2"></i>
        ${fieldData.verboseName}
        <small class="text-muted ms-1">(${fieldData.type})</small>
    `;
    
    // Add sorting direction for sorting drop zone
    if (dropType === 'sorting') {
        content += `
            <select class="form-select form-select-sm d-inline-block ms-2" style="width: auto;" onchange="updateSortDirection(this)">
                <option value="ASC">ASC</option>
                <option value="DESC">DESC</option>
            </select>
        `;
    }
    
    content += `
        <button type="button" class="remove-field" onclick="removeField(this, '${dropType}')">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    div.innerHTML = content;
    return div;
}

function removeField(button, dropType) {
    const fieldElement = button.closest('.dropped-field');
    const fieldData = {
        model: fieldElement.dataset.model,
        field: fieldElement.dataset.field,
        type: fieldElement.dataset.type
    };
    
    // Remove from DOM
    fieldElement.remove();
    
    // Update state
    updateReportBuilderState(fieldData, dropType, 'remove');
    
    // Check if drop zone is empty
    const dropZone = document.getElementById(`${dropType}DropZone`);
    if (dropZone.children.length === 0) {
        dropZone.classList.remove('has-items');
        dropZone.innerHTML = getDropZonePlaceholder(dropType);
    }
    
    updateSQLPreview();
}

function getDropZonePlaceholder(dropType) {
    const placeholders = {
        fields: '<i class="fas fa-mouse-pointer fa-2x text-muted mb-2"></i><p class="text-muted mb-0">Drop fields here</p>',
        grouping: '<i class="fas fa-layer-group fa-2x text-muted mb-2"></i><p class="text-muted mb-0">Drop fields here to group by</p>',
        sorting: '<i class="fas fa-sort fa-2x text-muted mb-2"></i><p class="text-muted mb-0">Drop fields here to sort by</p>'
    };
    
    return placeholders[dropType] || '';
}

function updateReportBuilderState(fieldData, dropType, action) {
    const stateKey = dropType;
    
    if (action === 'add') {
        if (!reportBuilderState[stateKey]) {
            reportBuilderState[stateKey] = [];
        }
        
        const fieldInfo = {
            model: fieldData.model,
            field_name: fieldData.field,
            field_type: fieldData.type,
            verbose_name: fieldData.verboseName,
            table_alias: getTableAlias(fieldData.model)
        };
        
        if (dropType === 'sorting') {
            fieldInfo.direction = 'ASC';
        }
        
        reportBuilderState[stateKey].push(fieldInfo);
        
        // Add to data sources if not already present
        if (!reportBuilderState.dataSources.find(ds => ds.model === fieldData.model)) {
            reportBuilderState.dataSources.push({
                model: fieldData.model,
                alias: getTableAlias(fieldData.model)
            });
        }
    } else if (action === 'remove') {
        if (reportBuilderState[stateKey]) {
            reportBuilderState[stateKey] = reportBuilderState[stateKey].filter(item => 
                !(item.model === fieldData.model && item.field_name === fieldData.field)
            );
        }
    }
}

function getTableAlias(modelName) {
    // Generate table alias (t1, t2, etc.)
    const existingAliases = reportBuilderState.dataSources.map(ds => ds.alias);
    let aliasIndex = 1;
    let alias = `t${aliasIndex}`;
    
    while (existingAliases.includes(alias)) {
        aliasIndex++;
        alias = `t${aliasIndex}`;
    }
    
    return alias;
}

function addAggregation(functionName) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add ${functionName} Aggregation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Field</label>
                        <select class="form-select" id="aggField">
                            <option value="">Select a field</option>
                            ${generateFieldOptions()}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Alias</label>
                        <input type="text" class="form-control" id="aggAlias" placeholder="${functionName.toLowerCase()}_field">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="confirmAggregation('${functionName}')">Add</button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
    
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

function generateFieldOptions() {
    let options = '';
    
    for (const [modelName, modelInfo] of Object.entries(availableModels)) {
        for (const [fieldName, fieldInfo] of Object.entries(modelInfo.fields)) {
            if (fieldInfo.type !== 'CharField' || fieldName === 'id') { // Only numeric fields for most aggregations
                options += `<option value="${modelName}.${fieldName}">${modelInfo.verbose_name}.${fieldInfo.verbose_name}</option>`;
            }
        }
    }
    
    return options;
}

function confirmAggregation(functionName) {
    const fieldSelect = document.getElementById('aggField');
    const aliasInput = document.getElementById('aggAlias');
    
    if (!fieldSelect.value) {
        alert('Please select a field');
        return;
    }
    
    const [modelName, fieldName] = fieldSelect.value.split('.');
    const alias = aliasInput.value || `${functionName.toLowerCase()}_${fieldName}`;
    
    const aggregation = {
        function: functionName,
        model: modelName,
        field_name: fieldName,
        alias: alias,
        table_alias: getTableAlias(modelName)
    };
    
    if (!reportBuilderState.aggregations) {
        reportBuilderState.aggregations = [];
    }
    
    reportBuilderState.aggregations.push(aggregation);
    
    // Add aggregation to fields display
    const fieldsDropZone = document.getElementById('fieldsDropZone');
    const aggElement = document.createElement('div');
    aggElement.className = 'dropped-field';
    aggElement.innerHTML = `
        <i class="fas fa-calculator me-2"></i>
        ${functionName}(${fieldName}) as ${alias}
        <button type="button" class="remove-field" onclick="removeAggregation(this, ${reportBuilderState.aggregations.length - 1})">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    fieldsDropZone.appendChild(aggElement);
    fieldsDropZone.classList.add('has-items');
    
    // Close modal
    const modal = bootstrap.Modal.getInstance(document.querySelector('.modal.show'));
    modal.hide();
    
    updateSQLPreview();
}

function removeAggregation(button, index) {
    button.closest('.dropped-field').remove();
    reportBuilderState.aggregations.splice(index, 1);
    updateSQLPreview();
}

function addFilter() {
    const filterBuilder = document.getElementById('filterBuilder');
    
    // Remove placeholder text if present
    if (filterBuilder.querySelector('p')) {
        filterBuilder.innerHTML = '';
    }
    
    const filterRow = document.createElement('div');
    filterRow.className = 'filter-row';
    filterRow.innerHTML = `
        <select class="form-select" style="width: 200px;" onchange="updateFilterField(this)">
            <option value="">Select Field</option>
            ${generateFieldOptions()}
        </select>
        <select class="form-select" style="width: 150px;">
            ${generateOperatorOptions()}
        </select>
        <input type="text" class="form-control" placeholder="Value" style="width: 200px;">
        <select class="form-select" style="width: 100px;">
            <option value="AND">AND</option>
            <option value="OR">OR</option>
        </select>
        <button type="button" class="btn-remove-filter" onclick="removeFilter(this)">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    filterBuilder.appendChild(filterRow);
    updateFiltersFromDOM();
}

function generateOperatorOptions() {
    let options = '';
    for (const [key, value] of Object.entries(operators)) {
        const label = key.replace('_', ' ').toUpperCase();
        options += `<option value="${key}">${label}</option>`;
    }
    return options;
}

function removeFilter(button) {
    const filterRow = button.closest('.filter-row');
    filterRow.remove();
    
    const filterBuilder = document.getElementById('filterBuilder');
    if (filterBuilder.children.length === 0) {
        filterBuilder.innerHTML = '<p class="text-muted text-center">No filters added yet. Click \'Add Filter\' to get started.</p>';
    }
    
    updateFiltersFromDOM();
    updateSQLPreview();
}

function updateFiltersFromDOM() {
    const filterRows = document.querySelectorAll('.filter-row');
    reportBuilderState.filters = [];
    
    filterRows.forEach(row => {
        const selects = row.querySelectorAll('select');
        const input = row.querySelector('input');
        
        if (selects[0].value && selects[1].value) {
            const [modelName, fieldName] = selects[0].value.split('.');
            
            reportBuilderState.filters.push({
                model: modelName,
                field_name: fieldName,
                operator: selects[1].value,
                value: input.value,
                logical_operator: selects[2].value,
                table_alias: getTableAlias(modelName)
            });
        }
    });
}

function updateSortDirection(select) {
    const fieldElement = select.closest('.dropped-field');
    const fieldData = {
        model: fieldElement.dataset.model,
        field: fieldElement.dataset.field
    };
    
    // Update state
    const sortItem = reportBuilderState.sorting.find(item => 
        item.model === fieldData.model && item.field_name === fieldData.field
    );
    
    if (sortItem) {
        sortItem.direction = select.value;
    }
    
    updateSQLPreview();
}

function updateSQLPreview() {
    const config = getReportBuilderConfiguration();
    
    // Make API call to build SQL
    fetch('/reports/api/query-builder/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            action: 'build',
            query_config: config.query_config
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.query) {
            document.getElementById('sqlPreview').textContent = data.query.sql;
        } else {
            document.getElementById('sqlPreview').textContent = 'Error generating SQL query';
        }
    })
    .catch(error => {
        console.error('Error updating SQL preview:', error);
        document.getElementById('sqlPreview').textContent = 'Error generating SQL query';
    });
}

function validateQuery() {
    const config = getReportBuilderConfiguration();
    
    fetch('/reports/api/query-builder/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            action: 'validate',
            query_config: config.query_config
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.validation) {
            if (data.validation.valid) {
                showMessage('Query is valid', 'success');
            } else {
                showMessage('Query validation failed: ' + data.validation.errors.join(', '), 'error');
            }
        }
    })
    .catch(error => {
        console.error('Error validating query:', error);
        showMessage('Error validating query', 'error');
    });
}

function executeQuery() {
    const config = getReportBuilderConfiguration();
    
    // Show loading spinner
    document.getElementById('loadingSpinner').style.display = 'block';
    document.getElementById('queryResults').style.display = 'none';
    
    fetch('/reports/api/query-builder/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            action: 'execute',
            query_config: config.query_config
        })
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('loadingSpinner').style.display = 'none';
        
        if (data.success && data.results) {
            displayQueryResults(data.results);
        } else {
            showMessage('Query execution failed: ' + (data.error || 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        document.getElementById('loadingSpinner').style.display = 'none';
        console.error('Error executing query:', error);
        showMessage('Error executing query', 'error');
    });
}

function displayQueryResults(results) {
    const resultsDiv = document.getElementById('queryResults');
    const table = document.getElementById('resultsTable');
    const thead = table.querySelector('thead');
    const tbody = table.querySelector('tbody');
    const resultsInfo = document.getElementById('resultsInfo');
    
    // Clear previous results
    thead.innerHTML = '';
    tbody.innerHTML = '';
    
    if (results.results && results.results.length > 0) {
        // Create header
        const headerRow = document.createElement('tr');
        results.columns.forEach(column => {
            const th = document.createElement('th');
            th.textContent = column;
            headerRow.appendChild(th);
        });
        thead.appendChild(headerRow);
        
        // Create rows
        results.results.slice(0, 100).forEach(row => { // Limit to first 100 rows
            const tr = document.createElement('tr');
            results.columns.forEach(column => {
                const td = document.createElement('td');
                td.textContent = row[column] || '';
                tr.appendChild(td);
            });
            tbody.appendChild(tr);
        });
        
        // Update info
        resultsInfo.textContent = `Showing ${Math.min(100, results.row_count)} of ${results.row_count} rows (${results.execution_time.toFixed(3)}s)`;
        
        resultsDiv.style.display = 'block';
    } else {
        resultsInfo.textContent = 'No results found';
        resultsDiv.style.display = 'block';
    }
}

function getReportBuilderConfiguration() {
    // Update filters from DOM
    updateFiltersFromDOM();
    
    return {
        report_config: {
            name: 'Visual Report Builder',
            description: 'Report built using drag-and-drop interface'
        },
        query_config: {
            fields: reportBuilderState.fields,
            data_sources: reportBuilderState.dataSources,
            filters: reportBuilderState.filters,
            joins: [], // TODO: Implement joins
            grouping: reportBuilderState.grouping,
            sorting: reportBuilderState.sorting,
            aggregations: reportBuilderState.aggregations
        },
        layout_config: {
            theme: 'default',
            page_size: 'A4',
            orientation: 'portrait'
        },
        data_sources: reportBuilderState.dataSources
    };
}

function loadExistingConfiguration() {
    // Load existing configuration from page
    const configScript = document.getElementById('reportBuilderConfig');
    const queryScript = document.getElementById('queryConfig');
    
    if (configScript && queryScript) {
        try {
            const reportConfig = JSON.parse(configScript.textContent);
            const queryConfig = JSON.parse(queryScript.textContent);
            
            if (queryConfig.fields) {
                reportBuilderState.fields = queryConfig.fields;
                // TODO: Restore fields to drop zones
            }
            
            if (queryConfig.filters) {
                reportBuilderState.filters = queryConfig.filters;
                // TODO: Restore filters to filter builder
            }
            
            if (queryConfig.grouping) {
                reportBuilderState.grouping = queryConfig.grouping;
                // TODO: Restore grouping fields
            }
            
            if (queryConfig.sorting) {
                reportBuilderState.sorting = queryConfig.sorting;
                // TODO: Restore sorting fields
            }
            
            if (queryConfig.aggregations) {
                reportBuilderState.aggregations = queryConfig.aggregations;
                // TODO: Restore aggregations
            }
            
            if (queryConfig.data_sources) {
                reportBuilderState.dataSources = queryConfig.data_sources;
            }
            
        } catch (e) {
            console.error('Failed to load existing configuration:', e);
        }
    }
}