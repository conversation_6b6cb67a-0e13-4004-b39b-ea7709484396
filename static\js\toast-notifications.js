/**
 * Comprehensive Toast Notification System
 * Provides consistent error handling and user feedback across the application
 */

class ToastNotificationSystem {
    constructor() {
        this.toastContainer = null;
        this.init();
    }

    init() {
        // Create toast container if it doesn't exist
        this.createToastContainer();
        
        // Setup global error handlers
        this.setupGlobalErrorHandlers();
        
        // Setup AJAX error interceptors
        this.setupAjaxErrorInterceptors();
    }

    createToastContainer() {
        // Check if container already exists
        let container = document.getElementById('toast-container');
        
        if (!container) {
            container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }
        
        this.toastContainer = container;
    }

    /**
     * Show a toast notification
     * @param {string} type - success, error, warning, info
     * @param {string} message - The message to display
     * @param {object} options - Additional options
     */
    show(type, message, options = {}) {
        const defaults = {
            autohide: true,
            delay: type === 'error' ? 6000 : 4000,
            showProgress: true,
            allowHtml: false,
            position: 'top-right'
        };
        
        const config = { ...defaults, ...options };
        
        // Sanitize message if HTML is not allowed
        const sanitizedMessage = config.allowHtml ? message : this.escapeHtml(message);
        
        const toastId = 'toast-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
        
        const toastHtml = this.createToastHtml(toastId, type, sanitizedMessage, config);
        
        this.toastContainer.insertAdjacentHTML('beforeend', toastHtml);
        
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, {
            autohide: config.autohide,
            delay: config.delay
        });
        
        // Add progress bar animation if enabled
        if (config.showProgress && config.autohide) {
            this.animateProgressBar(toastElement, config.delay);
        }
        
        // Show the toast
        toast.show();
        
        // Clean up after toast is hidden
        toastElement.addEventListener('hidden.bs.toast', function() {
            this.remove();
        });
        
        // Return toast instance for manual control
        return toast;
    }

    createToastHtml(toastId, type, message, config) {
        const iconMap = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-triangle',
            warning: 'fas fa-exclamation-circle',
            info: 'fas fa-info-circle'
        };
        
        const colorMap = {
            success: 'success',
            error: 'danger',
            warning: 'warning',
            info: 'info'
        };
        
        const icon = iconMap[type] || iconMap.info;
        const color = colorMap[type] || colorMap.info;
        
        const progressBar = config.showProgress && config.autohide ? 
            `<div class="toast-progress-bar bg-${color}" style="height: 3px; width: 100%; position: absolute; bottom: 0; left: 0; transition: width ${config.delay}ms linear;"></div>` : '';
        
        return `
            <div id="${toastId}" class="toast align-items-center text-white bg-${color} border-0 position-relative" 
                 role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body d-flex align-items-center">
                        <i class="${icon} me-2"></i>
                        <span class="toast-message">${message}</span>
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" 
                            data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                ${progressBar}
            </div>
        `;
    }

    animateProgressBar(toastElement, delay) {
        const progressBar = toastElement.querySelector('.toast-progress-bar');
        if (progressBar) {
            // Start animation after a small delay to ensure smooth transition
            setTimeout(() => {
                progressBar.style.width = '0%';
            }, 50);
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Utility function for safe querySelector operations
    static safeQuerySelector(selector) {
        if (!selector || selector === '#' || selector.trim() === '') {
            return null;
        }
        
        try {
            return document.querySelector(selector);
        } catch (error) {
            console.warn('Invalid selector:', selector, error);
            return null;
        }
    }

    static safeQuerySelectorAll(selector) {
        if (!selector || selector === '#' || selector.trim() === '') {
            return [];
        }
        
        try {
            return document.querySelectorAll(selector);
        } catch (error) {
            console.warn('Invalid selector:', selector, error);
            return [];
        }
    }

    // Convenience methods
    success(message, options = {}) {
        return this.show('success', message, options);
    }

    error(message, options = {}) {
        return this.show('error', message, options);
    }

    warning(message, options = {}) {
        return this.show('warning', message, options);
    }

    info(message, options = {}) {
        return this.show('info', message, options);
    }

    // Setup global error handlers
    setupGlobalErrorHandlers() {
        // Handle uncaught JavaScript errors
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            
            // Don't show toast for certain types of errors that are handled elsewhere
            const errorMessage = event.error?.message || event.message || '';
            
            // Skip selector errors and other non-critical errors
            if (errorMessage.includes('querySelector') || 
                errorMessage.includes('is not a valid selector') ||
                errorMessage.includes('Failed to execute')) {
                console.warn('Selector error handled:', errorMessage);
                return;
            }
            
            this.error('An unexpected error occurred. Please refresh the page and try again.');
        });

        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            this.error('A network or processing error occurred. Please try again.');
        });
    }

    // Setup AJAX error interceptors
    setupAjaxErrorInterceptors() {
        // Intercept fetch requests
        const originalFetch = window.fetch;
        const toastSystem = this;
        
        window.fetch = function(...args) {
            return originalFetch.apply(this, args)
                .catch(error => {
                    console.error('Fetch error:', error);
                    
                    // Only show toast if it's a network error (not handled by application)
                    if (error.name === 'TypeError' && error.message.includes('fetch')) {
                        toastSystem.error('Network connection failed. Please check your internet connection.');
                    }
                    
                    throw error;
                });
        };

        // Intercept jQuery AJAX if available
        if (window.jQuery) {
            $(document).ajaxError(function(event, xhr, settings, thrownError) {
                // Only handle unhandled AJAX errors
                if (!settings.suppressGlobalErrorHandler) {
                    console.error('AJAX error:', xhr, thrownError);
                    
                    let message = 'An error occurred while processing your request.';
                    
                    if (xhr.status === 0) {
                        message = 'Network connection failed. Please check your internet connection.';
                    } else if (xhr.status >= 500) {
                        message = 'Server error occurred. Please try again later.';
                    } else if (xhr.status === 403) {
                        message = 'Access denied. You may not have permission for this action.';
                    } else if (xhr.status === 404) {
                        message = 'The requested resource was not found.';
                    }
                    
                    toastSystem.error(message);
                }
            });
        }
    }

    // Clear all toasts
    clearAll() {
        const toasts = this.toastContainer.querySelectorAll('.toast');
        toasts.forEach(toast => {
            const bsToast = bootstrap.Toast.getInstance(toast);
            if (bsToast) {
                bsToast.hide();
            }
        });
    }

    // Show loading toast (doesn't auto-hide)
    showLoading(message = 'Processing...') {
        return this.show('info', `<i class="fas fa-spinner fa-spin me-2"></i>${message}`, {
            autohide: false,
            allowHtml: true,
            showProgress: false
        });
    }

    // Hide specific toast
    hide(toast) {
        if (toast && typeof toast.hide === 'function') {
            toast.hide();
        }
    }
}

// Enhanced AJAX Helper Functions
class AjaxHelper {
    constructor(toastSystem) {
        this.toast = toastSystem;
    }

    /**
     * Enhanced fetch with comprehensive error handling
     * @param {string} url - The URL to fetch
     * @param {object} options - Fetch options
     * @param {object} config - Additional configuration
     */
    async fetch(url, options = {}, config = {}) {
        const defaults = {
            showLoading: true,
            showSuccess: true,
            showErrors: true,
            loadingMessage: 'Processing...',
            retries: 1,
            retryDelay: 1000
        };
        
        const settings = { ...defaults, ...config };
        let loadingToast = null;
        
        // Show loading toast
        if (settings.showLoading) {
            loadingToast = this.toast.showLoading(settings.loadingMessage);
        }
        
        try {
            const response = await this.fetchWithRetry(url, options, settings.retries, settings.retryDelay);
            
            // Hide loading toast
            if (loadingToast) {
                this.toast.hide(loadingToast);
            }
            
            // Handle HTTP errors
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            // Show success message if configured
            if (settings.showSuccess && data.message) {
                this.toast.success(data.message);
            }
            
            return data;
            
        } catch (error) {
            // Hide loading toast
            if (loadingToast) {
                this.toast.hide(loadingToast);
            }
            
            // Show error message if configured
            if (settings.showErrors) {
                this.handleError(error);
            }
            
            throw error;
        }
    }

    async fetchWithRetry(url, options, retries, delay) {
        for (let i = 0; i <= retries; i++) {
            try {
                return await fetch(url, options);
            } catch (error) {
                if (i === retries) {
                    throw error;
                }
                
                // Wait before retry
                await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
            }
        }
    }

    handleError(error) {
        console.error('AJAX Error:', error);
        
        let message = 'An error occurred while processing your request.';
        
        if (error.message.includes('Failed to fetch')) {
            message = 'Network connection failed. Please check your internet connection.';
        } else if (error.message.includes('HTTP 500')) {
            message = 'Server error occurred. Please try again later.';
        } else if (error.message.includes('HTTP 403')) {
            message = 'Access denied. You may not have permission for this action.';
        } else if (error.message.includes('HTTP 404')) {
            message = 'The requested resource was not found.';
        } else if (error.message.includes('HTTP 400')) {
            message = 'Invalid request. Please check your input and try again.';
        }
        
        this.toast.error(message);
    }

    /**
     * Enhanced form submission with error handling
     * @param {HTMLFormElement} form - The form element
     * @param {object} config - Configuration options
     */
    async submitForm(form, config = {}) {
        const defaults = {
            showLoading: true,
            showSuccess: true,
            resetForm: false,
            redirectOnSuccess: null,
            loadingMessage: 'Submitting...'
        };
        
        const settings = { ...defaults, ...config };
        
        try {
            const formData = new FormData(form);
            const response = await this.fetch(form.action, {
                method: form.method || 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': this.getCsrfToken()
                }
            }, {
                showLoading: settings.showLoading,
                showSuccess: settings.showSuccess,
                loadingMessage: settings.loadingMessage
            });
            
            // Reset form if configured
            if (settings.resetForm) {
                form.reset();
            }
            
            // Redirect if configured
            if (settings.redirectOnSuccess && response.redirect_url) {
                setTimeout(() => {
                    window.location.href = response.redirect_url;
                }, 1000);
            }
            
            return response;
            
        } catch (error) {
            throw error;
        }
    }

    getCsrfToken() {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrftoken') {
                return decodeURIComponent(value);
            }
        }
        return '';
    }
}

// Button State Management
class ButtonStateManager {
    constructor() {
        this.originalStates = new WeakMap();
    }

    /**
     * Set button to loading state
     * @param {HTMLButtonElement} button - The button element
     * @param {string} loadingText - Text to show while loading
     */
    setLoading(button, loadingText = 'Loading...') {
        if (!button) return;
        
        // Store original state
        this.originalStates.set(button, {
            innerHTML: button.innerHTML,
            disabled: button.disabled,
            className: button.className
        });
        
        // Set loading state
        button.innerHTML = `<i class="fas fa-spinner fa-spin me-2"></i>${loadingText}`;
        button.disabled = true;
        button.classList.add('btn-loading');
    }

    /**
     * Restore button to original state
     * @param {HTMLButtonElement} button - The button element
     */
    restore(button) {
        if (!button) return;
        
        const originalState = this.originalStates.get(button);
        if (originalState) {
            button.innerHTML = originalState.innerHTML;
            button.disabled = originalState.disabled;
            button.className = originalState.className;
            
            // Clean up
            this.originalStates.delete(button);
        }
    }

    /**
     * Set button to success state temporarily
     * @param {HTMLButtonElement} button - The button element
     * @param {string} successText - Text to show for success
     * @param {number} duration - How long to show success state
     */
    setSuccess(button, successText = 'Success!', duration = 2000) {
        if (!button) return;
        
        const originalState = this.originalStates.get(button);
        if (originalState) {
            button.innerHTML = `<i class="fas fa-check me-2"></i>${successText}`;
            button.classList.remove('btn-loading');
            button.classList.add('btn-success');
            
            setTimeout(() => {
                this.restore(button);
            }, duration);
        }
    }

    /**
     * Set button to error state temporarily
     * @param {HTMLButtonElement} button - The button element
     * @param {string} errorText - Text to show for error
     * @param {number} duration - How long to show error state
     */
    setError(button, errorText = 'Error', duration = 2000) {
        if (!button) return;
        
        const originalState = this.originalStates.get(button);
        if (originalState) {
            button.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i>${errorText}`;
            button.classList.remove('btn-loading');
            button.classList.add('btn-danger');
            
            setTimeout(() => {
                this.restore(button);
            }, duration);
        }
    }
}

// Initialize global instances
let toastSystem, ajaxHelper, buttonManager;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize systems
    toastSystem = new ToastNotificationSystem();
    ajaxHelper = new AjaxHelper(toastSystem);
    buttonManager = new ButtonStateManager();
    
    // Make globally available
    window.toast = toastSystem;
    window.ajax = ajaxHelper;
    window.buttonManager = buttonManager;
    
    // Make safe selector functions globally available
    window.safeQuerySelector = ToastNotificationSystem.safeQuerySelector;
    window.safeQuerySelectorAll = ToastNotificationSystem.safeQuerySelectorAll;
    
    // Legacy compatibility functions
    window.showToast = function(type, message, options) {
        return toastSystem.show(type, message, options);
    };
    
    window.showSuccess = function(message, options) {
        return toastSystem.success(message, options);
    };
    
    window.showError = function(message, options) {
        return toastSystem.error(message, options);
    };
    
    window.showWarning = function(message, options) {
        return toastSystem.warning(message, options);
    };
    
    window.showInfo = function(message, options) {
        return toastSystem.info(message, options);
    };
});

// CSS for toast progress bars and loading states
const toastStyles = `
<style>
.toast-progress-bar {
    transition: width linear;
}

.btn-loading {
    position: relative;
    pointer-events: none;
}

.toast-container {
    max-width: 400px;
}

.toast-message {
    word-break: break-word;
    flex: 1;
}

@media (max-width: 576px) {
    .toast-container {
        left: 1rem !important;
        right: 1rem !important;
        max-width: none;
    }
}

/* Loading overlay improvements */
#loading-overlay {
    backdrop-filter: blur(2px);
}

#loading-overlay .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Form validation styles */
.is-invalid {
    border-color: #dc3545;
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
}

/* Enhanced button states */
.btn-success {
    transition: all 0.3s ease;
}

.btn-danger {
    transition: all 0.3s ease;
}
</style>
`;

// Inject styles
document.head.insertAdjacentHTML('beforeend', toastStyles);