/**
 * Service Worker for School ERP System
 * Provides offline capabilities and caching
 */

const CACHE_NAME = 'school-erp-v1.0.0';
const OFFLINE_URL = '/offline/';

// Files to cache for offline use
const STATIC_CACHE_URLS = [
    '/',
    '/dashboard/',
    '/offline/',
    '/static/css/mobile.css',
    '/static/css/rtl.css',
    '/static/js/mobile.js',
    '/static/manifest.json',
    // Add more critical resources
];

// API endpoints to cache
const API_CACHE_URLS = [
    '/api/dashboard/stats/',
    '/api/user/profile/',
    '/api/notifications/unread/',
];

// Install event - cache static resources
self.addEventListener('install', event => {
    console.log('Service Worker installing...');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('Caching static resources...');
                return cache.addAll(STATIC_CACHE_URLS);
            })
            .then(() => {
                console.log('Static resources cached successfully');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Failed to cache static resources:', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== CACHE_NAME) {
                            console.log('Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker activated');
                return self.clients.claim();
            })
    );
});

// Fetch event - handle network requests
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }
    
    // Skip chrome-extension requests
    if (url.protocol === 'chrome-extension:') {
        return;
    }
    
    // Handle different types of requests
    if (url.pathname.startsWith('/api/')) {
        // API requests - network first, then cache
        event.respondWith(handleApiRequest(request));
    } else if (url.pathname.startsWith('/static/')) {
        // Static files - cache first, then network
        event.respondWith(handleStaticRequest(request));
    } else {
        // HTML pages - network first, then cache, then offline page
        event.respondWith(handlePageRequest(request));
    }
});

// Handle API requests with network-first strategy
async function handleApiRequest(request) {
    const cache = await caches.open(CACHE_NAME);
    
    try {
        // Try network first
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            // Cache successful responses
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.log('Network failed for API request, trying cache:', request.url);
        
        // Fallback to cache
        const cachedResponse = await cache.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Return error response if no cache
        return new Response(
            JSON.stringify({ 
                error: 'Network unavailable', 
                offline: true 
            }),
            {
                status: 503,
                statusText: 'Service Unavailable',
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
}

// Handle static file requests with cache-first strategy
async function handleStaticRequest(request) {
    const cache = await caches.open(CACHE_NAME);
    
    // Try cache first
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
        return cachedResponse;
    }
    
    try {
        // Fallback to network
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            // Cache the response
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.log('Failed to fetch static resource:', request.url);
        
        // Return a generic error response for static files
        return new Response('Resource not available offline', {
            status: 404,
            statusText: 'Not Found'
        });
    }
}

// Handle page requests with network-first strategy
async function handlePageRequest(request) {
    const cache = await caches.open(CACHE_NAME);
    
    try {
        // Try network first
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            // Cache successful HTML responses
            if (networkResponse.headers.get('content-type')?.includes('text/html')) {
                cache.put(request, networkResponse.clone());
            }
        }
        
        return networkResponse;
    } catch (error) {
        console.log('Network failed for page request, trying cache:', request.url);
        
        // Try cache
        const cachedResponse = await cache.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Fallback to offline page for navigation requests
        if (request.mode === 'navigate') {
            const offlineResponse = await cache.match(OFFLINE_URL);
            if (offlineResponse) {
                return offlineResponse;
            }
        }
        
        // Return generic offline response
        return new Response(
            `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Offline - School ERP</title>
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <style>
                    body {
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        text-align: center;
                        padding: 2rem;
                        background-color: #f8f9fa;
                    }
                    .offline-container {
                        max-width: 400px;
                        margin: 0 auto;
                        background: white;
                        padding: 2rem;
                        border-radius: 0.5rem;
                        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
                    }
                    .offline-icon {
                        font-size: 4rem;
                        color: #6c757d;
                        margin-bottom: 1rem;
                    }
                    .offline-title {
                        font-size: 1.5rem;
                        font-weight: 600;
                        margin-bottom: 1rem;
                        color: #343a40;
                    }
                    .offline-message {
                        color: #6c757d;
                        margin-bottom: 2rem;
                    }
                    .retry-button {
                        background-color: #007bff;
                        color: white;
                        border: none;
                        padding: 0.75rem 1.5rem;
                        border-radius: 0.375rem;
                        cursor: pointer;
                        font-size: 1rem;
                    }
                    .retry-button:hover {
                        background-color: #0056b3;
                    }
                </style>
            </head>
            <body>
                <div class="offline-container">
                    <div class="offline-icon">📱</div>
                    <h1 class="offline-title">You're Offline</h1>
                    <p class="offline-message">
                        It looks like you're not connected to the internet. 
                        Please check your connection and try again.
                    </p>
                    <button class="retry-button" onclick="window.location.reload()">
                        Try Again
                    </button>
                </div>
            </body>
            </html>
            `,
            {
                status: 200,
                statusText: 'OK',
                headers: { 'Content-Type': 'text/html' }
            }
        );
    }
}

// Background sync for form submissions
self.addEventListener('sync', event => {
    console.log('Background sync triggered:', event.tag);
    
    if (event.tag === 'form-submission') {
        event.waitUntil(syncFormSubmissions());
    } else if (event.tag === 'attendance-sync') {
        event.waitUntil(syncAttendanceData());
    }
});

// Sync form submissions when back online
async function syncFormSubmissions() {
    try {
        const cache = await caches.open(CACHE_NAME);
        const pendingForms = await cache.match('/offline/pending-forms');
        
        if (pendingForms) {
            const forms = await pendingForms.json();
            
            for (const form of forms) {
                try {
                    await fetch(form.url, {
                        method: form.method,
                        headers: form.headers,
                        body: form.body
                    });
                    
                    console.log('Synced form submission:', form.url);
                } catch (error) {
                    console.error('Failed to sync form:', form.url, error);
                }
            }
            
            // Clear pending forms after sync
            await cache.delete('/offline/pending-forms');
        }
    } catch (error) {
        console.error('Error syncing form submissions:', error);
    }
}

// Sync attendance data when back online
async function syncAttendanceData() {
    try {
        const cache = await caches.open(CACHE_NAME);
        const pendingAttendance = await cache.match('/offline/pending-attendance');
        
        if (pendingAttendance) {
            const attendanceData = await pendingAttendance.json();
            
            for (const record of attendanceData) {
                try {
                    await fetch('/api/attendance/bulk-create/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': record.csrfToken
                        },
                        body: JSON.stringify(record.data)
                    });
                    
                    console.log('Synced attendance data:', record.date);
                } catch (error) {
                    console.error('Failed to sync attendance:', record.date, error);
                }
            }
            
            // Clear pending attendance after sync
            await cache.delete('/offline/pending-attendance');
        }
    } catch (error) {
        console.error('Error syncing attendance data:', error);
    }
}

// Push notification handling
self.addEventListener('push', event => {
    console.log('Push notification received:', event);
    
    const options = {
        body: 'You have new notifications',
        icon: '/static/images/icons/icon-192x192.png',
        badge: '/static/images/icons/badge-72x72.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: 'View',
                icon: '/static/images/icons/checkmark.png'
            },
            {
                action: 'close',
                title: 'Close',
                icon: '/static/images/icons/xmark.png'
            }
        ]
    };
    
    if (event.data) {
        const data = event.data.json();
        options.body = data.body || options.body;
        options.title = data.title || 'School ERP';
        options.data = { ...options.data, ...data };
    }
    
    event.waitUntil(
        self.registration.showNotification('School ERP', options)
    );
});

// Notification click handling
self.addEventListener('notificationclick', event => {
    console.log('Notification clicked:', event);
    
    event.notification.close();
    
    if (event.action === 'explore') {
        // Open the app
        event.waitUntil(
            clients.openWindow('/')
        );
    } else if (event.action === 'close') {
        // Just close the notification
        return;
    } else {
        // Default action - open the app
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});

// Message handling from main thread
self.addEventListener('message', event => {
    console.log('Message received in SW:', event.data);
    
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    } else if (event.data && event.data.type === 'CACHE_FORM') {
        // Cache form data for offline submission
        cachePendingForm(event.data.form);
    } else if (event.data && event.data.type === 'CACHE_ATTENDANCE') {
        // Cache attendance data for offline submission
        cachePendingAttendance(event.data.attendance);
    }
});

// Cache pending form submissions
async function cachePendingForm(formData) {
    try {
        const cache = await caches.open(CACHE_NAME);
        const existingForms = await cache.match('/offline/pending-forms');
        
        let forms = [];
        if (existingForms) {
            forms = await existingForms.json();
        }
        
        forms.push({
            ...formData,
            timestamp: Date.now()
        });
        
        await cache.put(
            '/offline/pending-forms',
            new Response(JSON.stringify(forms), {
                headers: { 'Content-Type': 'application/json' }
            })
        );
        
        console.log('Cached pending form submission');
    } catch (error) {
        console.error('Error caching pending form:', error);
    }
}

// Cache pending attendance data
async function cachePendingAttendance(attendanceData) {
    try {
        const cache = await caches.open(CACHE_NAME);
        const existingAttendance = await cache.match('/offline/pending-attendance');
        
        let attendance = [];
        if (existingAttendance) {
            attendance = await existingAttendance.json();
        }
        
        attendance.push({
            ...attendanceData,
            timestamp: Date.now()
        });
        
        await cache.put(
            '/offline/pending-attendance',
            new Response(JSON.stringify(attendance), {
                headers: { 'Content-Type': 'application/json' }
            })
        );
        
        console.log('Cached pending attendance data');
    } catch (error) {
        console.error('Error caching pending attendance:', error);
    }
}

// Periodic background sync
self.addEventListener('periodicsync', event => {
    console.log('Periodic sync triggered:', event.tag);
    
    if (event.tag === 'daily-sync') {
        event.waitUntil(performDailySync());
    }
});

// Perform daily sync operations
async function performDailySync() {
    try {
        // Sync critical data
        await fetch('/api/sync/daily/', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        });
        
        console.log('Daily sync completed');
    } catch (error) {
        console.error('Daily sync failed:', error);
    }
}

console.log('Service Worker loaded successfully');