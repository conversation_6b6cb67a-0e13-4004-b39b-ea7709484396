{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ rule.name }} - {% trans "Attendance Rule" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .detail-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .detail-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
        padding: 2rem;
    }
    .info-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }
    .info-item:last-child {
        margin-bottom: 0;
    }
    .info-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        margin-right: 1rem;
    }
    .badge-large {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }
    .target-badge {
        margin: 0.25rem;
        font-size: 0.8rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'academics:dashboard' %}">{% trans "Academic Dashboard" %}</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'academics:attendance_rules' %}">{% trans "Attendance Rules" %}</a></li>
                    <li class="breadcrumb-item active">{{ rule.name }}</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Rule Details -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card detail-card">
                <div class="detail-header">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h2 class="mb-2">{{ rule.name }}</h2>
                            <p class="mb-0 opacity-75">
                                <i class="fas fa-tag me-2"></i>{{ rule.get_rule_type_display }}
                            </p>
                        </div>
                        <div class="text-end">
                            {% if rule.is_active %}
                                <span class="badge bg-success badge-large">
                                    <i class="fas fa-check-circle me-1"></i>{% trans "Active" %}
                                </span>
                            {% else %}
                                <span class="badge bg-secondary badge-large">
                                    <i class="fas fa-pause-circle me-1"></i>{% trans "Inactive" %}
                                </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Basic Information -->
                    <div class="info-section">
                        <h5 class="mb-3">
                            <i class="fas fa-info-circle text-primary me-2"></i>{% trans "Rule Information" %}
                        </h5>
                        
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <div>
                                <strong>{% trans "Rule Value" %}</strong>
                                <br>
                                <span class="text-muted">
                                    {{ rule.rule_value }}{% if rule.rule_type == 'minimum_attendance' %}%{% elif rule.rule_type == 'late_threshold' %} {% trans "minutes" %}{% endif %}
                                </span>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div>
                                <strong>{% trans "Applicable To" %}</strong>
                                <br>
                                <span class="badge bg-info">{{ rule.get_applicable_to_display }}</span>
                            </div>
                        </div>
                        
                        {% if rule.description %}
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-align-left"></i>
                            </div>
                            <div>
                                <strong>{% trans "Description" %}</strong>
                                <br>
                                <span class="text-muted">{{ rule.description }}</span>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Effective Period -->
                    <div class="info-section">
                        <h5 class="mb-3">
                            <i class="fas fa-calendar text-primary me-2"></i>{% trans "Effective Period" %}
                        </h5>
                        
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-calendar-plus"></i>
                            </div>
                            <div>
                                <strong>{% trans "Effective From" %}</strong>
                                <br>
                                <span class="text-muted">{{ rule.effective_from|date:"F d, Y" }}</span>
                            </div>
                        </div>
                        
                        {% if rule.effective_to %}
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-calendar-minus"></i>
                            </div>
                            <div>
                                <strong>{% trans "Effective To" %}</strong>
                                <br>
                                <span class="text-muted">{{ rule.effective_to|date:"F d, Y" }}</span>
                            </div>
                        </div>
                        {% else %}
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-infinity"></i>
                            </div>
                            <div>
                                <strong>{% trans "Duration" %}</strong>
                                <br>
                                <span class="badge bg-success">{% trans "Ongoing" %}</span>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Target Selection -->
                    {% if rule.target_grades.exists or rule.target_classes.exists or rule.target_subjects.exists %}
                    <div class="info-section">
                        <h5 class="mb-3">
                            <i class="fas fa-bullseye text-primary me-2"></i>{% trans "Target Selection" %}
                        </h5>
                        
                        {% if rule.target_grades.exists %}
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-layer-group"></i>
                            </div>
                            <div>
                                <strong>{% trans "Target Grades" %}</strong>
                                <br>
                                {% for grade in rule.target_grades.all %}
                                    <span class="badge bg-primary target-badge">{{ grade.name }}</span>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if rule.target_classes.exists %}
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div>
                                <strong>{% trans "Target Classes" %}</strong>
                                <br>
                                {% for class in rule.target_classes.all %}
                                    <span class="badge bg-info target-badge">{{ class.name }}</span>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if rule.target_subjects.exists %}
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-book"></i>
                            </div>
                            <div>
                                <strong>{% trans "Target Subjects" %}</strong>
                                <br>
                                {% for subject in rule.target_subjects.all %}
                                    <span class="badge bg-warning target-badge">{{ subject.name }}</span>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Actions Sidebar -->
        <div class="col-lg-4">
            <div class="card detail-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>{% trans "Actions" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'academics:attendance_rule_edit' rule.pk %}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>{% trans "Edit Rule" %}
                        </a>
                        
                        <a href="{% url 'academics:attendance_rules' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-list me-2"></i>{% trans "All Rules" %}
                        </a>
                        
                        <hr>
                        
                        <button class="btn btn-outline-info" onclick="printRule()">
                            <i class="fas fa-print me-2"></i>{% trans "Print Rule" %}
                        </button>
                        
                        <button class="btn btn-outline-success" onclick="exportRule()">
                            <i class="fas fa-download me-2"></i>{% trans "Export Rule" %}
                        </button>
                    </div>
                </div>
            </div>

            <!-- Rule Statistics -->
            <div class="card detail-card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>{% trans "Statistics" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <h6 class="text-muted">{% trans "Created" %}</h6>
                            <p class="mb-0">{{ rule.created_at|date:"M d, Y" }}</p>
                        </div>
                        
                        <div class="mb-3">
                            <h6 class="text-muted">{% trans "Last Modified" %}</h6>
                            <p class="mb-0">{{ rule.updated_at|date:"M d, Y" }}</p>
                        </div>
                        
                        <div>
                            <h6 class="text-muted">{% trans "Status" %}</h6>
                            {% if rule.is_active %}
                                <span class="badge bg-success">{% trans "Active" %}</span>
                            {% else %}
                                <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
function printRule() {
    window.print();
}

function exportRule() {
    // Implementation for exporting rule data
    alert('{% trans "Export functionality will be implemented" %}');
}
</script>
{% endblock %}