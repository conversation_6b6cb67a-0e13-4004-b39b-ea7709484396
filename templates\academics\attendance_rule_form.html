{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}
    {% if object %}
        {% trans "Edit Attendance Rule" %} - {{ object.name }}
    {% else %}
        {% trans "Add Attendance Rule" %}
    {% endif %} - {{ block.super }}
{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .form-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
        padding: 1.5rem;
    }
    .form-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
    }
    .btn-primary:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'academics:dashboard' %}">{% trans "Academic Dashboard" %}</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'academics:attendance_rules' %}">{% trans "Attendance Rules" %}</a></li>
                    <li class="breadcrumb-item active">
                        {% if object %}{% trans "Edit Rule" %}{% else %}{% trans "Add Rule" %}{% endif %}
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card form-card">
                <div class="form-header">
                    <h3 class="mb-0">
                        <i class="fas fa-rules me-2"></i>
                        {% if object %}
                            {% trans "Edit Attendance Rule" %}
                        {% else %}
                            {% trans "Add New Attendance Rule" %}
                        {% endif %}
                    </h3>
                    <p class="mb-0 mt-2 opacity-75">
                        {% trans "Configure attendance policies and rules for classes and grades" %}
                    </p>
                </div>
                
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <!-- Basic Information -->
                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-info-circle text-primary me-2"></i>{% trans "Basic Information" %}
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label">
                                        <i class="fas fa-tag me-1"></i>{{ form.name.label }}
                                    </label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="invalid-feedback d-block">{{ form.name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.rule_type.id_for_label }}" class="form-label">
                                        <i class="fas fa-cog me-1"></i>{{ form.rule_type.label }}
                                    </label>
                                    {{ form.rule_type }}
                                    {% if form.rule_type.errors %}
                                        <div class="invalid-feedback d-block">{{ form.rule_type.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.rule_value.id_for_label }}" class="form-label">
                                        <i class="fas fa-percentage me-1"></i>{{ form.rule_value.label }}
                                    </label>
                                    {{ form.rule_value }}
                                    {% if form.rule_value.errors %}
                                        <div class="invalid-feedback d-block">{{ form.rule_value.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.applicable_to.id_for_label }}" class="form-label">
                                        <i class="fas fa-users me-1"></i>{{ form.applicable_to.label }}
                                    </label>
                                    {{ form.applicable_to }}
                                    {% if form.applicable_to.errors %}
                                        <div class="invalid-feedback d-block">{{ form.applicable_to.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="{{ form.description.id_for_label }}" class="form-label">
                                    <i class="fas fa-align-left me-1"></i>{{ form.description.label }}
                                </label>
                                {{ form.description }}
                                {% if form.description.errors %}
                                    <div class="invalid-feedback d-block">{{ form.description.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Target Selection -->
                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-bullseye text-primary me-2"></i>{% trans "Target Selection" %}
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.target_grades.id_for_label }}" class="form-label">
                                        <i class="fas fa-layer-group me-1"></i>{{ form.target_grades.label }}
                                    </label>
                                    {{ form.target_grades }}
                                    {% if form.target_grades.errors %}
                                        <div class="invalid-feedback d-block">{{ form.target_grades.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.target_classes.id_for_label }}" class="form-label">
                                        <i class="fas fa-users me-1"></i>{{ form.target_classes.label }}
                                    </label>
                                    {{ form.target_classes }}
                                    {% if form.target_classes.errors %}
                                        <div class="invalid-feedback d-block">{{ form.target_classes.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.target_subjects.id_for_label }}" class="form-label">
                                        <i class="fas fa-book me-1"></i>{{ form.target_subjects.label }}
                                    </label>
                                    {{ form.target_subjects }}
                                    {% if form.target_subjects.errors %}
                                        <div class="invalid-feedback d-block">{{ form.target_subjects.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Effective Period -->
                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-calendar text-primary me-2"></i>{% trans "Effective Period" %}
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.effective_from.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-plus me-1"></i>{{ form.effective_from.label }}
                                    </label>
                                    {{ form.effective_from }}
                                    {% if form.effective_from.errors %}
                                        <div class="invalid-feedback d-block">{{ form.effective_from.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.effective_to.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-minus me-1"></i>{{ form.effective_to.label }}
                                    </label>
                                    {{ form.effective_to }}
                                    {% if form.effective_to.errors %}
                                        <div class="invalid-feedback d-block">{{ form.effective_to.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <div class="form-check mt-4">
                                        {{ form.is_active }}
                                        <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                            <i class="fas fa-toggle-on me-1"></i>{{ form.is_active.label }}
                                        </label>
                                    </div>
                                    {% if form.is_active.errors %}
                                        <div class="invalid-feedback d-block">{{ form.is_active.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'academics:attendance_rules' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>{% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                {% if object %}{% trans "Update Rule" %}{% else %}{% trans "Create Rule" %}{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add form-control class to all form fields
    const formFields = document.querySelectorAll('input, select, textarea');
    formFields.forEach(field => {
        if (!field.classList.contains('form-check-input')) {
            field.classList.add('form-control');
        }
    });
    
    // Handle rule type change to show/hide relevant fields
    const ruleTypeField = document.getElementById('{{ form.rule_type.id_for_label }}');
    if (ruleTypeField) {
        ruleTypeField.addEventListener('change', function() {
            const ruleValue = document.getElementById('{{ form.rule_value.id_for_label }}');
            const ruleValueLabel = ruleValue.previousElementSibling;
            
            if (this.value === 'minimum_attendance') {
                ruleValueLabel.innerHTML = '<i class="fas fa-percentage me-1"></i>{% trans "Minimum Percentage" %}';
                ruleValue.placeholder = '{% trans "e.g., 75" %}';
            } else if (this.value === 'late_threshold') {
                ruleValueLabel.innerHTML = '<i class="fas fa-clock me-1"></i>{% trans "Minutes Late" %}';
                ruleValue.placeholder = '{% trans "e.g., 15" %}';
            }
        });
    }
});
</script>
{% endblock %}