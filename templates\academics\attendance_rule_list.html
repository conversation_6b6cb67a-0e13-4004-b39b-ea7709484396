{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Attendance Rules" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .rule-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }
    .rule-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    .rule-type-badge {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }
    .status-active {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
    }
    .status-inactive {
        background: linear-gradient(135deg, #6c757d, #495057);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-rules text-primary me-2"></i>{% trans "Attendance Rules" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage attendance policies and rules for different classes and grades" %}</p>
                </div>
                <div>
                    <a href="{% url 'academics:attendance_rule_add' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>{% trans "Add Rule" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card rule-card status-active">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ active_rules_count|default:0 }}</h4>
                    <p class="mb-0">{% trans "Active Rules" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card rule-card status-inactive">
                <div class="card-body text-center">
                    <i class="fas fa-pause-circle fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ inactive_rules_count|default:0 }}</h4>
                    <p class="mb-0">{% trans "Inactive Rules" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card rule-card" style="background: linear-gradient(135deg, #17a2b8, #138496); color: white;">
                <div class="card-body text-center">
                    <i class="fas fa-percentage fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ minimum_attendance_rules|default:0 }}</h4>
                    <p class="mb-0">{% trans "Minimum Attendance" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card rule-card" style="background: linear-gradient(135deg, #ffc107, #e0a800); color: white;">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ late_threshold_rules|default:0 }}</h4>
                    <p class="mb-0">{% trans "Late Threshold" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Rules List -->
    <div class="row">
        <div class="col-12">
            <div class="card rule-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>{% trans "All Attendance Rules" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if object_list %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>{% trans "Rule Name" %}</th>
                                        <th>{% trans "Type" %}</th>
                                        <th>{% trans "Applicable To" %}</th>
                                        <th>{% trans "Value" %}</th>
                                        <th>{% trans "Effective Period" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for rule in object_list %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-rule-book text-primary me-2"></i>
                                                <div>
                                                    <strong>{{ rule.name }}</strong>
                                                    {% if rule.description %}
                                                        <br><small class="text-muted">{{ rule.description|truncatechars:50 }}</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge rule-type-badge bg-info">
                                                {{ rule.get_rule_type_display }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">
                                                {{ rule.get_applicable_to_display }}
                                            </span>
                                        </td>
                                        <td>
                                            <strong>{{ rule.rule_value }}{% if rule.rule_type == 'minimum_attendance' %}%{% elif rule.rule_type == 'late_threshold' %} min{% endif %}</strong>
                                        </td>
                                        <td>
                                            <small>
                                                {{ rule.effective_from|date:"M d, Y" }}
                                                {% if rule.effective_to %}
                                                    - {{ rule.effective_to|date:"M d, Y" }}
                                                {% else %}
                                                    - {% trans "Ongoing" %}
                                                {% endif %}
                                            </small>
                                        </td>
                                        <td>
                                            {% if rule.is_active %}
                                                <span class="badge bg-success">{% trans "Active" %}</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{% url 'academics:attendance_rule_detail' rule.pk %}" 
                                                   class="btn btn-outline-info" title="{% trans 'View Details' %}">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{% url 'academics:attendance_rule_edit' rule.pk %}" 
                                                   class="btn btn-outline-primary" title="{% trans 'Edit' %}">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-rules fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{% trans "No attendance rules found" %}</h5>
                            <p class="text-muted">{% trans "Create your first attendance rule to get started" %}</p>
                            <a href="{% url 'academics:attendance_rule_add' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>{% trans "Add First Rule" %}
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
{% endblock %}