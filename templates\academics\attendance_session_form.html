{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}
    {% if object %}
        {% trans "Edit Attendance Session" %}
    {% else %}
        {% trans "Add Attendance Session" %}
    {% endif %} - {{ block.super }}
{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .form-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .form-header {
        background: linear-gradient(135deg, #17a2b8, #138496);
        color: white;
        border-radius: 15px 15px 0 0;
        padding: 1.5rem;
    }
    .form-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    .method-card {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 1rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;
        margin-bottom: 1rem;
    }
    .method-card:hover {
        border-color: #17a2b8;
        background: #f0fdff;
    }
    .method-card.selected {
        border-color: #17a2b8;
        background: #d1ecf1;
    }
    .method-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        color: #17a2b8;
    }
    .form-control:focus {
        border-color: #17a2b8;
        box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
    }
    .btn-primary {
        background: linear-gradient(135deg, #17a2b8, #138496);
        border: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'academics:dashboard' %}">{% trans "Academic Dashboard" %}</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'academics:attendance_sessions' %}">{% trans "Attendance Sessions" %}</a></li>
                    <li class="breadcrumb-item active">
                        {% if object %}{% trans "Edit Session" %}{% else %}{% trans "Add Session" %}{% endif %}
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card form-card">
                <div class="form-header">
                    <h3 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        {% if object %}
                            {% trans "Edit Attendance Session" %}
                        {% else %}
                            {% trans "Add New Attendance Session" %}
                        {% endif %}
                    </h3>
                    <p class="mb-0 mt-2 opacity-75">
                        {% trans "Configure class attendance sessions for tracking student presence" %}
                    </p>
                </div>
                
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <!-- Session Information -->
                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-info-circle text-primary me-2"></i>{% trans "Session Information" %}
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="{{ form.class_subject.id_for_label }}" class="form-label">
                                        <i class="fas fa-chalkboard me-1"></i>{{ form.class_subject.label }}
                                    </label>
                                    {{ form.class_subject }}
                                    {% if form.class_subject.errors %}
                                        <div class="invalid-feedback d-block">{{ form.class_subject.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.session_date.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar me-1"></i>{{ form.session_date.label }}
                                    </label>
                                    {{ form.session_date }}
                                    {% if form.session_date.errors %}
                                        <div class="invalid-feedback d-block">{{ form.session_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.start_time.id_for_label }}" class="form-label">
                                        <i class="fas fa-clock me-1"></i>{{ form.start_time.label }}
                                    </label>
                                    {{ form.start_time }}
                                    {% if form.start_time.errors %}
                                        <div class="invalid-feedback d-block">{{ form.start_time.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.end_time.id_for_label }}" class="form-label">
                                        <i class="fas fa-clock me-1"></i>{{ form.end_time.label }}
                                    </label>
                                    {{ form.end_time }}
                                    {% if form.end_time.errors %}
                                        <div class="invalid-feedback d-block">{{ form.end_time.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="{{ form.session_topic.id_for_label }}" class="form-label">
                                        <i class="fas fa-tag me-1"></i>{{ form.session_topic.label }}
                                    </label>
                                    {{ form.session_topic }}
                                    {% if form.session_topic.errors %}
                                        <div class="invalid-feedback d-block">{{ form.session_topic.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.status.id_for_label }}" class="form-label">
                                        <i class="fas fa-toggle-on me-1"></i>{{ form.status.label }}
                                    </label>
                                    {{ form.status }}
                                    {% if form.status.errors %}
                                        <div class="invalid-feedback d-block">{{ form.status.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Attendance Method -->
                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-fingerprint text-primary me-2"></i>{% trans "Attendance Method" %}
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-2">
                                    <div class="method-card" data-method="manual">
                                        <i class="fas fa-hand-paper method-icon"></i>
                                        <h6>{% trans "Manual" %}</h6>
                                        <small class="text-muted">{% trans "Entry" %}</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="method-card" data-method="biometric">
                                        <i class="fas fa-fingerprint method-icon"></i>
                                        <h6>{% trans "Biometric" %}</h6>
                                        <small class="text-muted">{% trans "Scanner" %}</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="method-card" data-method="qr_code">
                                        <i class="fas fa-qrcode method-icon"></i>
                                        <h6>{% trans "QR Code" %}</h6>
                                        <small class="text-muted">{% trans "Scan" %}</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="method-card" data-method="rfid">
                                        <i class="fas fa-credit-card method-icon"></i>
                                        <h6>{% trans "RFID" %}</h6>
                                        <small class="text-muted">{% trans "Card" %}</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="method-card" data-method="mobile_app">
                                        <i class="fas fa-mobile-alt method-icon"></i>
                                        <h6>{% trans "Mobile" %}</h6>
                                        <small class="text-muted">{% trans "App" %}</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="method-card" data-method="facial_recognition">
                                        <i class="fas fa-user-circle method-icon"></i>
                                        <h6>{% trans "Facial" %}</h6>
                                        <small class="text-muted">{% trans "Recognition" %}</small>
                                    </div>
                                </div>
                            </div>
                            
                            {{ form.attendance_method }}
                            {% if form.attendance_method.errors %}
                                <div class="invalid-feedback d-block">{{ form.attendance_method.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'academics:attendance_sessions' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>{% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                {% if object %}{% trans "Update Session" %}{% else %}{% trans "Create Session" %}{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add form-control class to all form fields
    const formFields = document.querySelectorAll('input, select, textarea');
    formFields.forEach(field => {
        if (!field.classList.contains('form-check-input')) {
            field.classList.add('form-control');
        }
    });
    
    // Handle attendance method selection
    const methodCards = document.querySelectorAll('.method-card');
    const methodField = document.getElementById('{{ form.attendance_method.id_for_label }}');
    
    methodCards.forEach(card => {
        card.addEventListener('click', function() {
            // Remove selected class from all cards
            methodCards.forEach(c => c.classList.remove('selected'));
            
            // Add selected class to clicked card
            this.classList.add('selected');
            
            // Update hidden field value
            if (methodField) {
                methodField.value = this.dataset.method;
            }
        });
    });
    
    // Set initial selection if editing
    {% if object %}
    const currentMethod = '{{ object.attendance_method }}';
    const currentCard = document.querySelector(`[data-method="${currentMethod}"]`);
    if (currentCard) {
        currentCard.classList.add('selected');
    }
    {% endif %}
    
    // Set today's date as default
    const dateField = document.getElementById('{{ form.session_date.id_for_label }}');
    if (dateField && !dateField.value) {
        const today = new Date().toISOString().split('T')[0];
        dateField.value = today;
    }
});
</script>
{% endblock %}