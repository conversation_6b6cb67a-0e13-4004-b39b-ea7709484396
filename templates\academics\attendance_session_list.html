{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Attendance Sessions" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .session-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }
    .session-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    .status-scheduled {
        background: linear-gradient(135deg, #17a2b8, #138496);
        color: white;
    }
    .status-active {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
    }
    .status-completed {
        background: linear-gradient(135deg, #6f42c1, #5a32a3);
        color: white;
    }
    .status-cancelled {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-clock text-primary me-2"></i>{% trans "Attendance Sessions" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage class attendance sessions and tracking" %}</p>
                </div>
                <div>
                    <a href="{% url 'academics:attendance_session_add' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>{% trans "Add Session" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card session-card status-scheduled">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-alt fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ scheduled_count|default:0 }}</h4>
                    <p class="mb-0">{% trans "Scheduled" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card session-card status-active">
                <div class="card-body text-center">
                    <i class="fas fa-play-circle fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ active_count|default:0 }}</h4>
                    <p class="mb-0">{% trans "Active" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card session-card status-completed">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ completed_count|default:0 }}</h4>
                    <p class="mb-0">{% trans "Completed" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card session-card status-cancelled">
                <div class="card-body text-center">
                    <i class="fas fa-times-circle fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ cancelled_count|default:0 }}</h4>
                    <p class="mb-0">{% trans "Cancelled" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Sessions List -->
    <div class="row">
        <div class="col-12">
            <div class="card session-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>{% trans "All Attendance Sessions" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if sessions %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>{% trans "Date & Time" %}</th>
                                        <th>{% trans "Class Subject" %}</th>
                                        <th>{% trans "Topic" %}</th>
                                        <th>{% trans "Method" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for session in sessions %}
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>{{ session.session_date|date:"M d, Y" }}</strong>
                                                <br>
                                                <small class="text-muted">
                                                    {{ session.start_time|time:"H:i" }} - {{ session.end_time|time:"H:i" }}
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-chalkboard text-primary me-2"></i>
                                                <div>
                                                    <strong>{{ session.class_subject.subject.name }}</strong>
                                                    <br>
                                                    <small class="text-muted">{{ session.class_subject.class_obj.name }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            {% if session.session_topic %}
                                                {{ session.session_topic|truncatechars:30 }}
                                            {% else %}
                                                <span class="text-muted">{% trans "No topic specified" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                {{ session.get_attendance_method_display }}
                                            </span>
                                        </td>
                                        <td>
                                            {% if session.status == 'scheduled' %}
                                                <span class="badge bg-info">{% trans "Scheduled" %}</span>
                                            {% elif session.status == 'active' %}
                                                <span class="badge bg-success">{% trans "Active" %}</span>
                                            {% elif session.status == 'completed' %}
                                                <span class="badge bg-primary">{% trans "Completed" %}</span>
                                            {% elif session.status == 'cancelled' %}
                                                <span class="badge bg-danger">{% trans "Cancelled" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{% url 'academics:attendance_session_detail' session.pk %}" 
                                                   class="btn btn-outline-info" title="{% trans 'View Details' %}">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{% url 'academics:attendance_session_edit' session.pk %}" 
                                                   class="btn btn-outline-primary" title="{% trans 'Edit' %}">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                {% if session.status == 'scheduled' %}
                                                    <button class="btn btn-outline-success" title="{% trans 'Start Session' %}">
                                                        <i class="fas fa-play"></i>
                                                    </button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{% trans "No attendance sessions found" %}</h5>
                            <p class="text-muted">{% trans "Create your first attendance session to get started" %}</p>
                            <a href="{% url 'academics:attendance_session_add' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>{% trans "Add First Session" %}
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
{% endblock %}