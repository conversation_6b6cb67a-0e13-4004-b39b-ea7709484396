{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}
    {% if object %}
        {% trans "Edit Biometric Device" %} - {{ object.name }}
    {% else %}
        {% trans "Add Biometric Device" %}
    {% endif %} - {{ block.super }}
{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .form-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .form-header {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        border-radius: 15px 15px 0 0;
        padding: 1.5rem;
    }
    .form-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    .device-type-card {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 1rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;
        margin-bottom: 1rem;
    }
    .device-type-card:hover {
        border-color: #28a745;
        background: #f8fff9;
    }
    .device-type-card.selected {
        border-color: #28a745;
        background: #d4edda;
    }
    .device-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        color: #28a745;
    }
    .form-control:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }
    .btn-primary {
        background: linear-gradient(135deg, #28a745, #20c997);
        border: none;
    }
    .btn-primary:hover {
        background: linear-gradient(135deg, #218838, #1e7e34);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'academics:dashboard' %}">{% trans "Academic Dashboard" %}</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'academics:biometric_devices' %}">{% trans "Biometric Devices" %}</a></li>
                    <li class="breadcrumb-item active">
                        {% if object %}{% trans "Edit Device" %}{% else %}{% trans "Add Device" %}{% endif %}
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card form-card">
                <div class="form-header">
                    <h3 class="mb-0">
                        <i class="fas fa-fingerprint me-2"></i>
                        {% if object %}
                            {% trans "Edit Biometric Device" %}
                        {% else %}
                            {% trans "Add New Biometric Device" %}
                        {% endif %}
                    </h3>
                    <p class="mb-0 mt-2 opacity-75">
                        {% trans "Configure biometric devices for automated attendance tracking" %}
                    </p>
                </div>
                
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <!-- Device Type Selection -->
                        {% if not object %}
                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-microchip text-primary me-2"></i>{% trans "Device Type" %}
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="device-type-card" data-type="fingerprint">
                                        <i class="fas fa-fingerprint device-icon"></i>
                                        <h6>{% trans "Fingerprint" %}</h6>
                                        <small class="text-muted">{% trans "Scanner" %}</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="device-type-card" data-type="facial_recognition">
                                        <i class="fas fa-user-circle device-icon"></i>
                                        <h6>{% trans "Facial" %}</h6>
                                        <small class="text-muted">{% trans "Recognition" %}</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="device-type-card" data-type="iris_scanner">
                                        <i class="fas fa-eye device-icon"></i>
                                        <h6>{% trans "Iris" %}</h6>
                                        <small class="text-muted">{% trans "Scanner" %}</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="device-type-card" data-type="palm_scanner">
                                        <i class="fas fa-hand-paper device-icon"></i>
                                        <h6>{% trans "Palm" %}</h6>
                                        <small class="text-muted">{% trans "Scanner" %}</small>
                                    </div>
                                </div>
                            </div>
                            
                            {{ form.device_type }}
                            {% if form.device_type.errors %}
                                <div class="invalid-feedback d-block">{{ form.device_type.errors.0 }}</div>
                            {% endif %}
                        </div>
                        {% endif %}

                        <!-- Basic Information -->
                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-info-circle text-primary me-2"></i>{% trans "Basic Information" %}
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label">
                                        <i class="fas fa-tag me-1"></i>{{ form.name.label }}
                                    </label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="invalid-feedback d-block">{{ form.name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.device_id.id_for_label }}" class="form-label">
                                        <i class="fas fa-barcode me-1"></i>{{ form.device_id.label }}
                                    </label>
                                    {{ form.device_id }}
                                    {% if form.device_id.errors %}
                                        <div class="invalid-feedback d-block">{{ form.device_id.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.location.id_for_label }}" class="form-label">
                                        <i class="fas fa-map-marker-alt me-1"></i>{{ form.location.label }}
                                    </label>
                                    {{ form.location }}
                                    {% if form.location.errors %}
                                        <div class="invalid-feedback d-block">{{ form.location.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.status.id_for_label }}" class="form-label">
                                        <i class="fas fa-toggle-on me-1"></i>{{ form.status.label }}
                                    </label>
                                    {{ form.status }}
                                    {% if form.status.errors %}
                                        <div class="invalid-feedback d-block">{{ form.status.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Network Configuration -->
                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-network-wired text-primary me-2"></i>{% trans "Network Configuration" %}
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="{{ form.ip_address.id_for_label }}" class="form-label">
                                        <i class="fas fa-globe me-1"></i>{{ form.ip_address.label }}
                                    </label>
                                    {{ form.ip_address }}
                                    {% if form.ip_address.errors %}
                                        <div class="invalid-feedback d-block">{{ form.ip_address.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">{% trans "e.g., *************" %}</div>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.port.id_for_label }}" class="form-label">
                                        <i class="fas fa-plug me-1"></i>{{ form.port.label }}
                                    </label>
                                    {{ form.port }}
                                    {% if form.port.errors %}
                                        <div class="invalid-feedback d-block">{{ form.port.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">{% trans "e.g., 8080" %}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Advanced Configuration -->
                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-cogs text-primary me-2"></i>{% trans "Advanced Configuration" %}
                            </h5>
                            
                            <div class="mb-3">
                                <label for="{{ form.configuration.id_for_label }}" class="form-label">
                                    <i class="fas fa-code me-1"></i>{{ form.configuration.label }}
                                </label>
                                {{ form.configuration }}
                                {% if form.configuration.errors %}
                                    <div class="invalid-feedback d-block">{{ form.configuration.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">{% trans "JSON configuration for device-specific settings" %}</div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'academics:biometric_devices' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>{% trans "Cancel" %}
                            </a>
                            <div>
                                {% if object %}
                                <button type="button" class="btn btn-outline-info me-2" onclick="testConnection()">
                                    <i class="fas fa-plug me-2"></i>{% trans "Test Connection" %}
                                </button>
                                {% endif %}
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    {% if object %}{% trans "Update Device" %}{% else %}{% trans "Add Device" %}{% endif %}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add form-control class to all form fields
    const formFields = document.querySelectorAll('input, select, textarea');
    formFields.forEach(field => {
        if (!field.classList.contains('form-check-input')) {
            field.classList.add('form-control');
        }
    });
    
    // Handle device type selection
    const deviceTypeCards = document.querySelectorAll('.device-type-card');
    const deviceTypeField = document.getElementById('{{ form.device_type.id_for_label }}');
    
    deviceTypeCards.forEach(card => {
        card.addEventListener('click', function() {
            // Remove selected class from all cards
            deviceTypeCards.forEach(c => c.classList.remove('selected'));
            
            // Add selected class to clicked card
            this.classList.add('selected');
            
            // Update hidden field value
            if (deviceTypeField) {
                deviceTypeField.value = this.dataset.type;
            }
        });
    });
    
    // Set initial selection if editing
    {% if object %}
    const currentType = '{{ object.device_type }}';
    const currentCard = document.querySelector(`[data-type="${currentType}"]`);
    if (currentCard) {
        currentCard.classList.add('selected');
    }
    {% endif %}
});

function testConnection() {
    const ipAddress = document.getElementById('{{ form.ip_address.id_for_label }}').value;
    const port = document.getElementById('{{ form.port.id_for_label }}').value;
    
    if (!ipAddress) {
        alert('{% trans "Please enter an IP address first" %}');
        return;
    }
    
    // Show loading state
    const testBtn = event.target;
    const originalText = testBtn.innerHTML;
    testBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>{% trans "Testing..." %}';
    testBtn.disabled = true;
    
    // Simulate connection test (replace with actual implementation)
    setTimeout(() => {
        testBtn.innerHTML = originalText;
        testBtn.disabled = false;
        alert('{% trans "Connection test completed. Check device status." %}');
    }, 2000);
}
</script>
{% endblock %}