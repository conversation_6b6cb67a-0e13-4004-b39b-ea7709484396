{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Biometric Devices" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .device-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }
    .device-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    .status-active {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
    }
    .status-inactive {
        background: linear-gradient(135deg, #6c757d, #495057);
        color: white;
    }
    .status-maintenance {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: white;
    }
    .status-error {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
    }
    .device-icon {
        font-size: 2rem;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-fingerprint text-primary me-2"></i>{% trans "Biometric Devices" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage biometric devices for attendance tracking" %}</p>
                </div>
                <div>
                    <a href="{% url 'academics:biometric_device_add' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>{% trans "Add Device" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card device-card status-active">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle device-icon"></i>
                    <h4 class="mb-1">{{ active_count|default:0 }}</h4>
                    <p class="mb-0">{% trans "Active" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card device-card status-inactive">
                <div class="card-body text-center">
                    <i class="fas fa-pause-circle device-icon"></i>
                    <h4 class="mb-1">{{ inactive_count|default:0 }}</h4>
                    <p class="mb-0">{% trans "Inactive" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card device-card status-maintenance">
                <div class="card-body text-center">
                    <i class="fas fa-tools device-icon"></i>
                    <h4 class="mb-1">{{ maintenance_count|default:0 }}</h4>
                    <p class="mb-0">{% trans "Maintenance" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card device-card status-error">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-triangle device-icon"></i>
                    <h4 class="mb-1">{{ error_count|default:0 }}</h4>
                    <p class="mb-0">{% trans "Error" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Devices List -->
    <div class="row">
        <div class="col-12">
            <div class="card device-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>{% trans "All Biometric Devices" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if devices %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>{% trans "Device" %}</th>
                                        <th>{% trans "Type" %}</th>
                                        <th>{% trans "Location" %}</th>
                                        <th>{% trans "Connection" %}</th>
                                        <th>{% trans "Last Sync" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for device in devices %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                {% if device.device_type == 'fingerprint' %}
                                                    <i class="fas fa-fingerprint text-primary me-2"></i>
                                                {% elif device.device_type == 'facial_recognition' %}
                                                    <i class="fas fa-user-circle text-primary me-2"></i>
                                                {% elif device.device_type == 'iris_scanner' %}
                                                    <i class="fas fa-eye text-primary me-2"></i>
                                                {% elif device.device_type == 'palm_scanner' %}
                                                    <i class="fas fa-hand-paper text-primary me-2"></i>
                                                {% else %}
                                                    <i class="fas fa-microchip text-primary me-2"></i>
                                                {% endif %}
                                                <div>
                                                    <strong>{{ device.name }}</strong>
                                                    <br>
                                                    <small class="text-muted">ID: {{ device.device_id }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                {{ device.get_device_type_display }}
                                            </span>
                                        </td>
                                        <td>
                                            <i class="fas fa-map-marker-alt text-muted me-1"></i>
                                            {{ device.location }}
                                        </td>
                                        <td>
                                            {% if device.ip_address %}
                                                <div>
                                                    <strong>{{ device.ip_address }}</strong>
                                                    {% if device.port %}:{{ device.port }}{% endif %}
                                                </div>
                                            {% else %}
                                                <span class="text-muted">{% trans "Not configured" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if device.last_sync %}
                                                <small>{{ device.last_sync|date:"M d, Y H:i" }}</small>
                                            {% else %}
                                                <span class="text-muted">{% trans "Never" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if device.status == 'active' %}
                                                <span class="badge bg-success">{% trans "Active" %}</span>
                                            {% elif device.status == 'inactive' %}
                                                <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                                            {% elif device.status == 'maintenance' %}
                                                <span class="badge bg-warning">{% trans "Maintenance" %}</span>
                                            {% elif device.status == 'error' %}
                                                <span class="badge bg-danger">{% trans "Error" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{% url 'academics:biometric_device_detail' device.pk %}" 
                                                   class="btn btn-outline-info" title="{% trans 'View Details' %}">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{% url 'academics:biometric_device_edit' device.pk %}" 
                                                   class="btn btn-outline-primary" title="{% trans 'Edit' %}">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                {% if device.status == 'active' %}
                                                    <button class="btn btn-outline-warning" title="{% trans 'Test Connection' %}">
                                                        <i class="fas fa-plug"></i>
                                                    </button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-fingerprint fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{% trans "No biometric devices found" %}</h5>
                            <p class="text-muted">{% trans "Add your first biometric device to get started with automated attendance" %}</p>
                            <a href="{% url 'academics:biometric_device_add' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>{% trans "Add First Device" %}
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
{% endblock %}