{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ class_subject.subject.name }} - {{ class_subject.class_obj.name }} - {% trans "Class Subject" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .detail-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .detail-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
        padding: 2rem;
    }
    .info-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }
    .info-item:last-child {
        margin-bottom: 0;
    }
    .info-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        margin-right: 1rem;
    }
    .teacher-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        margin-right: 1rem;
    }
    .badge-large {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">{{ class_subject.subject.name }} - {{ class_subject.class_obj.name }}</h1>
                    <p class="text-muted">{% trans "Class Subject Assignment Details" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Class Subject Details -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card detail-card">
                <div class="detail-header">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h2 class="mb-2">{{ class_subject.subject.name }}</h2>
                            <p class="mb-0 opacity-75">
                                <i class="fas fa-users me-2"></i>{{ class_subject.class_obj.name }} - {{ class_subject.class_obj.grade.name }}
                            </p>
                        </div>
                        <div class="text-end">
                            {% if class_subject.is_active %}
                                <span class="badge bg-success badge-large">
                                    <i class="fas fa-check-circle me-1"></i>{% trans "Active" %}
                                </span>
                            {% else %}
                                <span class="badge bg-secondary badge-large">
                                    <i class="fas fa-pause-circle me-1"></i>{% trans "Inactive" %}
                                </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Subject Information -->
                    <div class="info-section">
                        <h5 class="mb-3">
                            <i class="fas fa-book text-primary me-2"></i>{% trans "Subject Information" %}
                        </h5>
                        
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-tag"></i>
                            </div>
                            <div>
                                <strong>{% trans "Subject Code" %}</strong>
                                <br>
                                <span class="text-muted">{{ class_subject.subject.code|default:"Not specified" }}</span>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div>
                                <strong>{% trans "Description" %}</strong>
                                <br>
                                <span class="text-muted">{{ class_subject.subject.description|default:"No description available" }}</span>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div>
                                <strong>{% trans "Weekly Hours" %}</strong>
                                <br>
                                <span class="badge bg-primary">{{ class_subject.weekly_hours|default:0 }} {% trans "hours/week" %}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Class Information -->
                    <div class="info-section">
                        <h5 class="mb-3">
                            <i class="fas fa-users text-primary me-2"></i>{% trans "Class Information" %}
                        </h5>
                        
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-layer-group"></i>
                            </div>
                            <div>
                                <strong>{% trans "Grade Level" %}</strong>
                                <br>
                                <span class="text-muted">{{ class_subject.class_obj.grade.name }} (Level {{ class_subject.class_obj.grade.level }})</span>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-calendar"></i>
                            </div>
                            <div>
                                <strong>{% trans "Academic Year" %}</strong>
                                <br>
                                <span class="text-muted">{{ class_subject.academic_year.name }}</span>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div>
                                <strong>{% trans "Semester" %}</strong>
                                <br>
                                {% if class_subject.semester == 'first' %}
                                    <span class="badge bg-primary">{% trans "First Semester" %}</span>
                                {% elif class_subject.semester == 'second' %}
                                    <span class="badge bg-secondary">{% trans "Second Semester" %}</span>
                                {% elif class_subject.semester == 'full_year' %}
                                    <span class="badge bg-success">{% trans "Full Year" %}</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Teacher Information -->
                    {% if class_subject.teacher %}
                    <div class="info-section">
                        <h5 class="mb-3">
                            <i class="fas fa-chalkboard-teacher text-primary me-2"></i>{% trans "Teacher Information" %}
                        </h5>
                        
                        <div class="d-flex align-items-center">
                            <div class="teacher-avatar">
                                {{ class_subject.teacher.user.first_name|first|upper }}{{ class_subject.teacher.user.last_name|first|upper }}
                            </div>
                            <div>
                                <h6 class="mb-1">{{ class_subject.teacher.user.get_full_name }}</h6>
                                <p class="text-muted mb-1">{{ class_subject.teacher.employee_id|default:"" }}</p>
                                <p class="text-muted mb-0">{{ class_subject.teacher.user.email }}</p>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Actions Sidebar -->
        <div class="col-lg-4">
            <div class="card detail-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>{% trans "Actions" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'academics:class_subject_edit' class_subject.pk %}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>{% trans "Edit Assignment" %}
                        </a>
                        
                        <a href="{% url 'academics:schedules' %}?class_subject={{ class_subject.pk }}" class="btn btn-outline-success">
                            <i class="fas fa-calendar me-2"></i>{% trans "View Schedule" %}
                        </a>
                        
                        <a href="{% url 'academics:class_subjects' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-list me-2"></i>{% trans "All Assignments" %}
                        </a>
                        
                        <hr>
                        
                        <button class="btn btn-outline-info" onclick="printAssignment()">
                            <i class="fas fa-print me-2"></i>{% trans "Print Details" %}
                        </button>
                        
                        <button class="btn btn-outline-warning" onclick="exportAssignment()">
                            <i class="fas fa-download me-2"></i>{% trans "Export Data" %}
                        </button>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="card detail-card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>{% trans "Statistics" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <h6 class="text-muted">{% trans "Created" %}</h6>
                            <p class="mb-0">{{ class_subject.created_at|date:"M d, Y" }}</p>
                        </div>
                        
                        <div class="mb-3">
                            <h6 class="text-muted">{% trans "Last Modified" %}</h6>
                            <p class="mb-0">{{ class_subject.updated_at|date:"M d, Y" }}</p>
                        </div>
                        
                        <div>
                            <h6 class="text-muted">{% trans "Status" %}</h6>
                            {% if class_subject.is_active %}
                                <span class="badge bg-success">{% trans "Active" %}</span>
                            {% else %}
                                <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
function printAssignment() {
    window.print();
}

function exportAssignment() {
    // Implementation for exporting assignment data
    alert('{% trans "Export functionality will be implemented" %}');
}
</script>
{% endblock %}