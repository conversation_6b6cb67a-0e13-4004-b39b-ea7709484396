{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}
    {% if object %}
        {% trans "Edit Class Subject" %}
    {% else %}
        {% trans "Add Class Subject" %}
    {% endif %} - {{ block.super }}
{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .form-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
        padding: 1.5rem;
    }
    .form-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
    }
    .teacher-preview {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        background: white;
        margin-top: 1rem;
        display: none;
    }
    .teacher-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        margin-right: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'academics:dashboard' %}">{% trans "Academic Dashboard" %}</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'academics:class_subjects' %}">{% trans "Class Subjects" %}</a></li>
                    <li class="breadcrumb-item active">
                        {% if object %}{% trans "Edit Assignment" %}{% else %}{% trans "Add Assignment" %}{% endif %}
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card form-card">
                <div class="form-header">
                    <h3 class="mb-0">
                        <i class="fas fa-chalkboard me-2"></i>
                        {% if object %}
                            {% trans "Edit Class Subject Assignment" %}
                        {% else %}
                            {% trans "Add New Class Subject Assignment" %}
                        {% endif %}
                    </h3>
                    <p class="mb-0 mt-2 opacity-75">
                        {% trans "Assign subjects to classes and teachers for the academic year" %}
                    </p>
                </div>
                
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <!-- Basic Assignment -->
                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-link text-primary me-2"></i>{% trans "Subject Assignment" %}
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.subject.id_for_label }}" class="form-label">
                                        <i class="fas fa-book me-1"></i>{{ form.subject.label }}
                                    </label>
                                    {{ form.subject }}
                                    {% if form.subject.errors %}
                                        <div class="invalid-feedback d-block">{{ form.subject.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.class_obj.id_for_label }}" class="form-label">
                                        <i class="fas fa-users me-1"></i>{{ form.class_obj.label }}
                                    </label>
                                    {{ form.class_obj }}
                                    {% if form.class_obj.errors %}
                                        <div class="invalid-feedback d-block">{{ form.class_obj.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.teacher.id_for_label }}" class="form-label">
                                        <i class="fas fa-chalkboard-teacher me-1"></i>{{ form.teacher.label }}
                                    </label>
                                    {{ form.teacher }}
                                    {% if form.teacher.errors %}
                                        <div class="invalid-feedback d-block">{{ form.teacher.errors.0 }}</div>
                                    {% endif %}
                                    
                                    <!-- Teacher Preview -->
                                    <div id="teacher-preview" class="teacher-preview">
                                        <div class="d-flex align-items-center">
                                            <div class="teacher-avatar" id="teacher-avatar">
                                                <span id="teacher-initials"></span>
                                            </div>
                                            <div>
                                                <h6 class="mb-1" id="teacher-name"></h6>
                                                <small class="text-muted" id="teacher-details"></small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.academic_year.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar me-1"></i>{{ form.academic_year.label }}
                                    </label>
                                    {{ form.academic_year }}
                                    {% if form.academic_year.errors %}
                                        <div class="invalid-feedback d-block">{{ form.academic_year.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Schedule Information -->
                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-clock text-primary me-2"></i>{% trans "Schedule Information" %}
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.weekly_hours.id_for_label }}" class="form-label">
                                        <i class="fas fa-hourglass-half me-1"></i>{{ form.weekly_hours.label }}
                                    </label>
                                    {{ form.weekly_hours }}
                                    {% if form.weekly_hours.errors %}
                                        <div class="invalid-feedback d-block">{{ form.weekly_hours.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">{% trans "Hours per week for this subject" %}</div>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.semester.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-alt me-1"></i>{{ form.semester.label }}
                                    </label>
                                    {{ form.semester }}
                                    {% if form.semester.errors %}
                                        <div class="invalid-feedback d-block">{{ form.semester.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <div class="form-check mt-4">
                                        {{ form.is_active }}
                                        <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                            <i class="fas fa-toggle-on me-1"></i>{{ form.is_active.label }}
                                        </label>
                                    </div>
                                    {% if form.is_active.errors %}
                                        <div class="invalid-feedback d-block">{{ form.is_active.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'academics:class_subjects' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>{% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                {% if object %}{% trans "Update Assignment" %}{% else %}{% trans "Create Assignment" %}{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add form-control class to all form fields
    const formFields = document.querySelectorAll('input, select, textarea');
    formFields.forEach(field => {
        if (!field.classList.contains('form-check-input')) {
            field.classList.add('form-control');
        }
    });
    
    // Handle teacher selection preview
    const teacherField = document.getElementById('{{ form.teacher.id_for_label }}');
    const teacherPreview = document.getElementById('teacher-preview');
    const teacherName = document.getElementById('teacher-name');
    const teacherDetails = document.getElementById('teacher-details');
    const teacherInitials = document.getElementById('teacher-initials');
    
    if (teacherField) {
        teacherField.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            
            if (this.value && selectedOption.text !== '---------') {
                // Extract teacher info from option text (assuming format: "Full Name (Employee ID)")
                const teacherText = selectedOption.text;
                const nameMatch = teacherText.match(/^([^(]+)/);
                const idMatch = teacherText.match(/\(([^)]+)\)/);
                
                if (nameMatch) {
                    const fullName = nameMatch[1].trim();
                    const nameParts = fullName.split(' ');
                    const initials = nameParts.map(part => part.charAt(0).toUpperCase()).join('');
                    
                    teacherName.textContent = fullName;
                    teacherDetails.textContent = idMatch ? `Employee ID: ${idMatch[1]}` : '';
                    teacherInitials.textContent = initials.substring(0, 2);
                    
                    teacherPreview.style.display = 'block';
                }
            } else {
                teacherPreview.style.display = 'none';
            }
        });
        
        // Trigger change event if there's already a selected value
        if (teacherField.value) {
            teacherField.dispatchEvent(new Event('change'));
        }
    }
    
    // Auto-calculate recommended weekly hours based on subject
    const subjectField = document.getElementById('{{ form.subject.id_for_label }}');
    const weeklyHoursField = document.getElementById('{{ form.weekly_hours.id_for_label }}');
    
    if (subjectField && weeklyHoursField) {
        subjectField.addEventListener('change', function() {
            if (!weeklyHoursField.value) {
                // Set default weekly hours based on subject type (this could be enhanced with actual data)
                weeklyHoursField.value = 4; // Default 4 hours per week
            }
        });
    }
});
</script>
{% endblock %}