{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Class Subjects" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .subject-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }
    .subject-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    .teacher-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        margin-right: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-chalkboard text-primary me-2"></i>{% trans "Class Subjects" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage subject assignments for classes and teachers" %}</p>
                </div>
                <div>
                    <a href="{% url 'academics:class_subject_add' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>{% trans "Add Class Subject" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card subject-card stats-card">
                <div class="card-body text-center">
                    <i class="fas fa-book fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ total_subjects|default:0 }}</h4>
                    <p class="mb-0">{% trans "Total Subjects" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card subject-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); color: white;">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ total_classes|default:0 }}</h4>
                    <p class="mb-0">{% trans "Classes" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card subject-card" style="background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%); color: white;">
                <div class="card-body text-center">
                    <i class="fas fa-chalkboard-teacher fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ total_teachers|default:0 }}</h4>
                    <p class="mb-0">{% trans "Teachers" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card subject-card" style="background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%); color: white;">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ total_hours|default:0 }}</h4>
                    <p class="mb-0">{% trans "Weekly Hours" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Class Subjects List -->
    <div class="row">
        <div class="col-12">
            <div class="card subject-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>{% trans "All Class Subjects" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if class_subjects %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>{% trans "Subject" %}</th>
                                        <th>{% trans "Class" %}</th>
                                        <th>{% trans "Teacher" %}</th>
                                        <th>{% trans "Weekly Hours" %}</th>
                                        <th>{% trans "Academic Year" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for class_subject in class_subjects %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-book text-primary me-2"></i>
                                                <div>
                                                    <strong>{{ class_subject.subject.name }}</strong>
                                                    {% if class_subject.subject.code %}
                                                        <br><small class="text-muted">{{ class_subject.subject.code }}</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-users text-info me-2"></i>
                                                <div>
                                                    <strong>{{ class_subject.class_obj.name }}</strong>
                                                    <br><small class="text-muted">{{ class_subject.class_obj.grade.name }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            {% if class_subject.teacher %}
                                                <div class="d-flex align-items-center">
                                                    <div class="teacher-avatar">
                                                        {{ class_subject.teacher.user.first_name|first|upper }}{{ class_subject.teacher.user.last_name|first|upper }}
                                                    </div>
                                                    <div>
                                                        <strong>{{ class_subject.teacher.user.get_full_name }}</strong>
                                                        <br><small class="text-muted">{{ class_subject.teacher.employee_id|default:"" }}</small>
                                                    </div>
                                                </div>
                                            {% else %}
                                                <span class="text-muted">{% trans "Not assigned" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">
                                                {{ class_subject.weekly_hours|default:0 }} {% trans "hrs/week" %}
                                            </span>
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ class_subject.academic_year.name }}</small>
                                        </td>
                                        <td>
                                            {% if class_subject.is_active %}
                                                <span class="badge bg-success">{% trans "Active" %}</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{% url 'academics:class_subject_detail' class_subject.pk %}" 
                                                   class="btn btn-outline-info" title="{% trans 'View Details' %}">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{% url 'academics:class_subject_edit' class_subject.pk %}" 
                                                   class="btn btn-outline-primary" title="{% trans 'Edit' %}">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'academics:schedules' %}?class_subject={{ class_subject.pk }}" 
                                                   class="btn btn-outline-success" title="{% trans 'View Schedule' %}">
                                                    <i class="fas fa-calendar"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-chalkboard fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{% trans "No class subjects found" %}</h5>
                            <p class="text-muted">{% trans "Assign subjects to classes to get started" %}</p>
                            <a href="{% url 'academics:class_subject_add' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>{% trans "Add First Assignment" %}
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
{% endblock %}