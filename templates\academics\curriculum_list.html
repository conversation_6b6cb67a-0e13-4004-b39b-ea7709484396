{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Curriculums" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .curriculum-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }
    .curriculum-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    .active-card {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
    }
    .subjects-card {
        background: linear-gradient(135deg, #17a2b8, #138496);
        color: white;
    }
    .grades-card {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-graduation-cap text-primary me-2"></i>{% trans "Curriculums" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage educational curriculums and academic frameworks" %}</p>
                </div>
                <div>
                    <a href="{% url 'academics:curriculum_add' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>{% trans "Add Curriculum" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card curriculum-card stats-card">
                <div class="card-body text-center">
                    <i class="fas fa-graduation-cap fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ total_curriculums|default:0 }}</h4>
                    <p class="mb-0">{% trans "Total Curriculums" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card curriculum-card active-card">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ active_curriculums|default:0 }}</h4>
                    <p class="mb-0">{% trans "Active" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card curriculum-card subjects-card">
                <div class="card-body text-center">
                    <i class="fas fa-book fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ total_subjects|default:0 }}</h4>
                    <p class="mb-0">{% trans "Subjects" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card curriculum-card grades-card">
                <div class="card-body text-center">
                    <i class="fas fa-layer-group fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ total_grades|default:0 }}</h4>
                    <p class="mb-0">{% trans "Grade Levels" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Curriculums List -->
    <div class="row">
        <div class="col-12">
            <div class="card curriculum-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>{% trans "All Curriculums" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if curriculums %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>{% trans "Curriculum" %}</th>
                                        <th>{% trans "Academic Year" %}</th>
                                        <th>{% trans "Grade Levels" %}</th>
                                        <th>{% trans "Subjects" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Created" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for curriculum in curriculums %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-graduation-cap text-primary me-2"></i>
                                                <div>
                                                    <strong>{{ curriculum.name }}</strong>
                                                    {% if curriculum.code %}
                                                        <br><small class="text-muted">{{ curriculum.code }}</small>
                                                    {% endif %}
                                                    {% if curriculum.description %}
                                                        <br><small class="text-muted">{{ curriculum.description|truncatechars:50 }}</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ curriculum.academic_year.name }}</small>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-wrap gap-1">
                                                {% for grade in curriculum.grades.all|slice:":3" %}
                                                    <span class="badge bg-secondary">{{ grade.name }}</span>
                                                {% endfor %}
                                                {% if curriculum.grades.count > 3 %}
                                                    <span class="badge bg-light text-dark">+{{ curriculum.grades.count|add:"-3" }}</span>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                {{ curriculum.subjects.count }} {% trans "subjects" %}
                                            </span>
                                        </td>
                                        <td>
                                            {% if curriculum.is_active %}
                                                <span class="badge bg-success">{% trans "Active" %}</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ curriculum.created_at|date:"M d, Y" }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{% url 'academics:curriculum_detail' curriculum.pk %}" 
                                                   class="btn btn-outline-info" title="{% trans 'View Details' %}">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{% url 'academics:curriculum_edit' curriculum.pk %}" 
                                                   class="btn btn-outline-primary" title="{% trans 'Edit' %}">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'academics:curriculum_subjects' %}?curriculum={{ curriculum.pk }}" 
                                                   class="btn btn-outline-success" title="{% trans 'View Subjects' %}">
                                                    <i class="fas fa-book"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-graduation-cap fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{% trans "No curriculums found" %}</h5>
                            <p class="text-muted">{% trans "Create your first curriculum to structure your educational programs" %}</p>
                            <a href="{% url 'academics:curriculum_add' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>{% trans "Add First Curriculum" %}
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
{% endblock %}