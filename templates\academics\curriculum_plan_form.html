{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}
    {% if object %}
        {% trans "Edit Curriculum Plan" %} - {{ object.name }}
    {% else %}
        {% trans "Add Curriculum Plan" %}
    {% endif %} - {{ block.super }}
{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .form-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .form-header {
        background: linear-gradient(135deg, #6f42c1, #5a32a3);
        color: white;
        border-radius: 15px 15px 0 0;
        padding: 1.5rem;
    }
    .form-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    .curriculum-type-card {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 1rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;
        margin-bottom: 1rem;
    }
    .curriculum-type-card:hover {
        border-color: #6f42c1;
        background: #f8f5ff;
    }
    .curriculum-type-card.selected {
        border-color: #6f42c1;
        background: #e7d9ff;
    }
    .type-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        color: #6f42c1;
    }
    .form-control:focus {
        border-color: #6f42c1;
        box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.25);
    }
    .btn-primary {
        background: linear-gradient(135deg, #6f42c1, #5a32a3);
        border: none;
    }
    .grade-selector {
        max-height: 200px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'academics:dashboard' %}">{% trans "Academic Dashboard" %}</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'academics:curriculum_plans' %}">{% trans "Curriculum Plans" %}</a></li>
                    <li class="breadcrumb-item active">
                        {% if object %}{% trans "Edit Plan" %}{% else %}{% trans "Add Plan" %}{% endif %}
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card form-card">
                <div class="form-header">
                    <h3 class="mb-0">
                        <i class="fas fa-sitemap me-2"></i>
                        {% if object %}
                            {% trans "Edit Curriculum Plan" %}
                        {% else %}
                            {% trans "Add New Curriculum Plan" %}
                        {% endif %}
                    </h3>
                    <p class="mb-0 mt-2 opacity-75">
                        {% trans "Create comprehensive curriculum plans for academic programs" %}
                    </p>
                </div>
                
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <!-- Basic Information -->
                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-info-circle text-primary me-2"></i>{% trans "Basic Information" %}
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label">
                                        <i class="fas fa-tag me-1"></i>{{ form.name.label }}
                                    </label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="invalid-feedback d-block">{{ form.name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.name_ar.id_for_label }}" class="form-label">
                                        <i class="fas fa-language me-1"></i>{{ form.name_ar.label }}
                                    </label>
                                    {{ form.name_ar }}
                                    {% if form.name_ar.errors %}
                                        <div class="invalid-feedback d-block">{{ form.name_ar.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.code.id_for_label }}" class="form-label">
                                        <i class="fas fa-barcode me-1"></i>{{ form.code.label }}
                                    </label>
                                    {{ form.code }}
                                    {% if form.code.errors %}
                                        <div class="invalid-feedback d-block">{{ form.code.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.academic_year.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar me-1"></i>{{ form.academic_year.label }}
                                    </label>
                                    {{ form.academic_year }}
                                    {% if form.academic_year.errors %}
                                        <div class="invalid-feedback d-block">{{ form.academic_year.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.duration_years.id_for_label }}" class="form-label">
                                        <i class="fas fa-hourglass-half me-1"></i>{{ form.duration_years.label }}
                                    </label>
                                    {{ form.duration_years }}
                                    {% if form.duration_years.errors %}
                                        <div class="invalid-feedback d-block">{{ form.duration_years.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="{{ form.description.id_for_label }}" class="form-label">
                                    <i class="fas fa-align-left me-1"></i>{{ form.description.label }}
                                </label>
                                {{ form.description }}
                                {% if form.description.errors %}
                                    <div class="invalid-feedback d-block">{{ form.description.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Curriculum Type -->
                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-graduation-cap text-primary me-2"></i>{% trans "Curriculum Type" %}
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-2">
                                    <div class="curriculum-type-card" data-type="national">
                                        <i class="fas fa-flag type-icon"></i>
                                        <h6>{% trans "National" %}</h6>
                                        <small class="text-muted">{% trans "Curriculum" %}</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="curriculum-type-card" data-type="international">
                                        <i class="fas fa-globe type-icon"></i>
                                        <h6>{% trans "International" %}</h6>
                                        <small class="text-muted">{% trans "Curriculum" %}</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="curriculum-type-card" data-type="bilingual">
                                        <i class="fas fa-language type-icon"></i>
                                        <h6>{% trans "Bilingual" %}</h6>
                                        <small class="text-muted">{% trans "Curriculum" %}</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="curriculum-type-card" data-type="specialized">
                                        <i class="fas fa-star type-icon"></i>
                                        <h6>{% trans "Specialized" %}</h6>
                                        <small class="text-muted">{% trans "Program" %}</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="curriculum-type-card" data-type="remedial">
                                        <i class="fas fa-hand-holding-heart type-icon"></i>
                                        <h6>{% trans "Remedial" %}</h6>
                                        <small class="text-muted">{% trans "Program" %}</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="curriculum-type-card" data-type="advanced">
                                        <i class="fas fa-rocket type-icon"></i>
                                        <h6>{% trans "Advanced" %}</h6>
                                        <small class="text-muted">{% trans "Program" %}</small>
                                    </div>
                                </div>
                            </div>
                            
                            {{ form.curriculum_type }}
                            {% if form.curriculum_type.errors %}
                                <div class="invalid-feedback d-block">{{ form.curriculum_type.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <!-- Academic Requirements -->
                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-certificate text-primary me-2"></i>{% trans "Academic Requirements" %}
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label for="{{ form.total_credit_hours.id_for_label }}" class="form-label">
                                        <i class="fas fa-coins me-1"></i>{{ form.total_credit_hours.label }}
                                    </label>
                                    {{ form.total_credit_hours }}
                                    {% if form.total_credit_hours.errors %}
                                        <div class="invalid-feedback d-block">{{ form.total_credit_hours.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-3 mb-3">
                                    <label for="{{ form.minimum_credit_hours.id_for_label }}" class="form-label">
                                        <i class="fas fa-coins me-1"></i>{{ form.minimum_credit_hours.label }}
                                    </label>
                                    {{ form.minimum_credit_hours }}
                                    {% if form.minimum_credit_hours.errors %}
                                        <div class="invalid-feedback d-block">{{ form.minimum_credit_hours.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-3 mb-3">
                                    <label for="{{ form.core_subjects_required.id_for_label }}" class="form-label">
                                        <i class="fas fa-star me-1"></i>{{ form.core_subjects_required.label }}
                                    </label>
                                    {{ form.core_subjects_required }}
                                    {% if form.core_subjects_required.errors %}
                                        <div class="invalid-feedback d-block">{{ form.core_subjects_required.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-3 mb-3">
                                    <label for="{{ form.elective_subjects_required.id_for_label }}" class="form-label">
                                        <i class="fas fa-hand-pointer me-1"></i>{{ form.elective_subjects_required.label }}
                                    </label>
                                    {{ form.elective_subjects_required }}
                                    {% if form.elective_subjects_required.errors %}
                                        <div class="invalid-feedback d-block">{{ form.elective_subjects_required.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.minimum_gpa_required.id_for_label }}" class="form-label">
                                        <i class="fas fa-chart-line me-1"></i>{{ form.minimum_gpa_required.label }}
                                    </label>
                                    {{ form.minimum_gpa_required }}
                                    {% if form.minimum_gpa_required.errors %}
                                        <div class="invalid-feedback d-block">{{ form.minimum_gpa_required.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="form-check mt-4">
                                        {{ form.is_active }}
                                        <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                            <i class="fas fa-toggle-on me-1"></i>{{ form.is_active.label }}
                                        </label>
                                    </div>
                                    {% if form.is_active.errors %}
                                        <div class="invalid-feedback d-block">{{ form.is_active.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Applicable Grades -->
                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-layer-group text-primary me-2"></i>{% trans "Applicable Grades" %}
                            </h5>
                            
                            <div class="grade-selector">
                                {{ form.grades }}
                                {% if form.grades.errors %}
                                    <div class="invalid-feedback d-block">{{ form.grades.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Effective Period -->
                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-calendar text-primary me-2"></i>{% trans "Effective Period" %}
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.effective_date.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-plus me-1"></i>{{ form.effective_date.label }}
                                    </label>
                                    {{ form.effective_date }}
                                    {% if form.effective_date.errors %}
                                        <div class="invalid-feedback d-block">{{ form.effective_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.expiry_date.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-minus me-1"></i>{{ form.expiry_date.label }}
                                    </label>
                                    {{ form.expiry_date }}
                                    {% if form.expiry_date.errors %}
                                        <div class="invalid-feedback d-block">{{ form.expiry_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information -->
                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-clipboard-list text-primary me-2"></i>{% trans "Additional Information" %}
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.objectives.id_for_label }}" class="form-label">
                                        <i class="fas fa-bullseye me-1"></i>{{ form.objectives.label }}
                                    </label>
                                    {{ form.objectives }}
                                    {% if form.objectives.errors %}
                                        <div class="invalid-feedback d-block">{{ form.objectives.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.prerequisites.id_for_label }}" class="form-label">
                                        <i class="fas fa-list-ul me-1"></i>{{ form.prerequisites.label }}
                                    </label>
                                    {{ form.prerequisites }}
                                    {% if form.prerequisites.errors %}
                                        <div class="invalid-feedback d-block">{{ form.prerequisites.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.assessment_policy.id_for_label }}" class="form-label">
                                        <i class="fas fa-clipboard-check me-1"></i>{{ form.assessment_policy.label }}
                                    </label>
                                    {{ form.assessment_policy }}
                                    {% if form.assessment_policy.errors %}
                                        <div class="invalid-feedback d-block">{{ form.assessment_policy.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.progression_rules.id_for_label }}" class="form-label">
                                        <i class="fas fa-arrow-up me-1"></i>{{ form.progression_rules.label }}
                                    </label>
                                    {{ form.progression_rules }}
                                    {% if form.progression_rules.errors %}
                                        <div class="invalid-feedback d-block">{{ form.progression_rules.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'academics:curriculum_plans' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>{% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                {% if object %}{% trans "Update Plan" %}{% else %}{% trans "Create Plan" %}{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add form-control class to all form fields
    const formFields = document.querySelectorAll('input, select, textarea');
    formFields.forEach(field => {
        if (!field.classList.contains('form-check-input')) {
            field.classList.add('form-control');
        }
    });
    
    // Handle curriculum type selection
    const typeCards = document.querySelectorAll('.curriculum-type-card');
    const typeField = document.getElementById('{{ form.curriculum_type.id_for_label }}');
    
    typeCards.forEach(card => {
        card.addEventListener('click', function() {
            // Remove selected class from all cards
            typeCards.forEach(c => c.classList.remove('selected'));
            
            // Add selected class to clicked card
            this.classList.add('selected');
            
            // Update hidden field value
            if (typeField) {
                typeField.value = this.dataset.type;
            }
        });
    });
    
    // Set initial selection if editing
    {% if object %}
    const currentType = '{{ object.curriculum_type }}';
    const currentCard = document.querySelector(`[data-type="${currentType}"]`);
    if (currentCard) {
        currentCard.classList.add('selected');
    }
    {% endif %}
    
    // Set today's date as default for effective date
    const effectiveDateField = document.getElementById('{{ form.effective_date.id_for_label }}');
    if (effectiveDateField && !effectiveDateField.value) {
        const today = new Date().toISOString().split('T')[0];
        effectiveDateField.value = today;
    }
});
</script>
{% endblock %}