{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Curriculum Plans" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .curriculum-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }
    .curriculum-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    .type-national {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    .type-international {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
    }
    .type-bilingual {
        background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
        color: white;
    }
    .type-specialized {
        background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
        color: white;
    }
    .curriculum-icon {
        font-size: 2rem;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-sitemap text-primary me-2"></i>{% trans "Curriculum Plans" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage comprehensive curriculum plans and academic structures" %}</p>
                </div>
                <div>
                    <a href="{% url 'academics:curriculum_plan_add' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>{% trans "Add Curriculum Plan" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card curriculum-card type-national">
                <div class="card-body text-center">
                    <i class="fas fa-flag curriculum-icon"></i>
                    <h4 class="mb-1">{{ national_count|default:0 }}</h4>
                    <p class="mb-0">{% trans "National" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card curriculum-card type-international">
                <div class="card-body text-center">
                    <i class="fas fa-globe curriculum-icon"></i>
                    <h4 class="mb-1">{{ international_count|default:0 }}</h4>
                    <p class="mb-0">{% trans "International" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card curriculum-card type-bilingual">
                <div class="card-body text-center">
                    <i class="fas fa-language curriculum-icon"></i>
                    <h4 class="mb-1">{{ bilingual_count|default:0 }}</h4>
                    <p class="mb-0">{% trans "Bilingual" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card curriculum-card type-specialized">
                <div class="card-body text-center">
                    <i class="fas fa-star curriculum-icon"></i>
                    <h4 class="mb-1">{{ specialized_count|default:0 }}</h4>
                    <p class="mb-0">{% trans "Specialized" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Curriculum Plans List -->
    <div class="row">
        <div class="col-12">
            <div class="card curriculum-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>{% trans "All Curriculum Plans" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if plans %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>{% trans "Curriculum Plan" %}</th>
                                        <th>{% trans "Type" %}</th>
                                        <th>{% trans "Academic Year" %}</th>
                                        <th>{% trans "Credit Hours" %}</th>
                                        <th>{% trans "Duration" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for plan in plans %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-graduation-cap text-primary me-2"></i>
                                                <div>
                                                    <strong>{{ plan.name }}</strong>
                                                    <br>
                                                    <small class="text-muted">{{ plan.code }}</small>
                                                    {% if plan.description %}
                                                        <br><small class="text-muted">{{ plan.description|truncatechars:50 }}</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            {% if plan.curriculum_type == 'national' %}
                                                <span class="badge bg-primary">{% trans "National" %}</span>
                                            {% elif plan.curriculum_type == 'international' %}
                                                <span class="badge bg-success">{% trans "International" %}</span>
                                            {% elif plan.curriculum_type == 'bilingual' %}
                                                <span class="badge bg-info">{% trans "Bilingual" %}</span>
                                            {% elif plan.curriculum_type == 'specialized' %}
                                                <span class="badge bg-warning">{% trans "Specialized" %}</span>
                                            {% elif plan.curriculum_type == 'remedial' %}
                                                <span class="badge bg-secondary">{% trans "Remedial" %}</span>
                                            {% elif plan.curriculum_type == 'advanced' %}
                                                <span class="badge bg-dark">{% trans "Advanced" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ plan.academic_year.name }}</small>
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{{ plan.total_credit_hours }}</strong> {% trans "total" %}
                                                <br>
                                                <small class="text-muted">{{ plan.minimum_credit_hours }} {% trans "minimum" %}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                {{ plan.duration_years }} {% trans "year(s)" %}
                                            </span>
                                        </td>
                                        <td>
                                            {% if plan.is_active %}
                                                {% if plan.is_current %}
                                                    <span class="badge bg-success">{% trans "Current" %}</span>
                                                {% else %}
                                                    <span class="badge bg-primary">{% trans "Active" %}</span>
                                                {% endif %}
                                            {% else %}
                                                <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{% url 'academics:curriculum_plan_detail' plan.pk %}" 
                                                   class="btn btn-outline-info" title="{% trans 'View Details' %}">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{% url 'academics:curriculum_plan_edit' plan.pk %}" 
                                                   class="btn btn-outline-primary" title="{% trans 'Edit' %}">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'academics:curriculum_subjects' %}?curriculum={{ plan.pk }}" 
                                                   class="btn btn-outline-success" title="{% trans 'View Subjects' %}">
                                                    <i class="fas fa-book"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-sitemap fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{% trans "No curriculum plans found" %}</h5>
                            <p class="text-muted">{% trans "Create your first curriculum plan to structure your academic programs" %}</p>
                            <a href="{% url 'academics:curriculum_plan_add' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>{% trans "Add First Plan" %}
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
{% endblock %}