{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}
    {% if object %}
        {% trans "Edit Curriculum Subject" %}
    {% else %}
        {% trans "Add Curriculum Subject" %}
    {% endif %} - {{ block.super }}
{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .form-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .form-header {
        background: linear-gradient(135deg, #17a2b8, #138496);
        color: white;
        border-radius: 15px 15px 0 0;
        padding: 1.5rem;
    }
    .form-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    .form-control:focus {
        border-color: #17a2b8;
        box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
    }
    .btn-primary {
        background: linear-gradient(135deg, #17a2b8, #138496);
        border: none;
    }
    .grade-selector {
        max-height: 200px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'academics:dashboard' %}">{% trans "Academic Dashboard" %}</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'academics:curriculum_subjects' %}">{% trans "Curriculum Subjects" %}</a></li>
                    <li class="breadcrumb-item active">
                        {% if object %}{% trans "Edit Mapping" %}{% else %}{% trans "Add Mapping" %}{% endif %}
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card form-card">
                <div class="form-header">
                    <h3 class="mb-0">
                        <i class="fas fa-list-alt me-2"></i>
                        {% if object %}
                            {% trans "Edit Curriculum Subject Mapping" %}
                        {% else %}
                            {% trans "Add New Curriculum Subject Mapping" %}
                        {% endif %}
                    </h3>
                    <p class="mb-0 mt-2 opacity-75">
                        {% trans "Map subjects to curriculum plans with specific requirements and settings" %}
                    </p>
                </div>
                
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <!-- Basic Mapping -->
                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-link text-primary me-2"></i>{% trans "Subject Mapping" %}
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.curriculum.id_for_label }}" class="form-label">
                                        <i class="fas fa-sitemap me-1"></i>{{ form.curriculum.label }}
                                    </label>
                                    {{ form.curriculum }}
                                    {% if form.curriculum.errors %}
                                        <div class="invalid-feedback d-block">{{ form.curriculum.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.subject.id_for_label }}" class="form-label">
                                        <i class="fas fa-book me-1"></i>{{ form.subject.label }}
                                    </label>
                                    {{ form.subject }}
                                    {% if form.subject.errors %}
                                        <div class="invalid-feedback d-block">{{ form.subject.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Academic Requirements -->
                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-certificate text-primary me-2"></i>{% trans "Academic Requirements" %}
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.credit_hours.id_for_label }}" class="form-label">
                                        <i class="fas fa-coins me-1"></i>{{ form.credit_hours.label }}
                                    </label>
                                    {{ form.credit_hours }}
                                    {% if form.credit_hours.errors %}
                                        <div class="invalid-feedback d-block">{{ form.credit_hours.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.weekly_hours.id_for_label }}" class="form-label">
                                        <i class="fas fa-clock me-1"></i>{{ form.weekly_hours.label }}
                                    </label>
                                    {{ form.weekly_hours }}
                                    {% if form.weekly_hours.errors %}
                                        <div class="invalid-feedback d-block">{{ form.weekly_hours.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.sequence_order.id_for_label }}" class="form-label">
                                        <i class="fas fa-sort-numeric-up me-1"></i>{{ form.sequence_order.label }}
                                    </label>
                                    {{ form.sequence_order }}
                                    {% if form.sequence_order.errors %}
                                        <div class="invalid-feedback d-block">{{ form.sequence_order.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Schedule Settings -->
                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-calendar text-primary me-2"></i>{% trans "Schedule Settings" %}
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.semester.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-alt me-1"></i>{{ form.semester.label }}
                                    </label>
                                    {{ form.semester }}
                                    {% if form.semester.errors %}
                                        <div class="invalid-feedback d-block">{{ form.semester.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="form-check mt-4">
                                        {{ form.is_mandatory }}
                                        <label class="form-check-label" for="{{ form.is_mandatory.id_for_label }}">
                                            <i class="fas fa-star me-1"></i>{{ form.is_mandatory.label }}
                                        </label>
                                    </div>
                                    {% if form.is_mandatory.errors %}
                                        <div class="invalid-feedback d-block">{{ form.is_mandatory.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Applicable Grades -->
                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-layer-group text-primary me-2"></i>{% trans "Applicable Grades" %}
                            </h5>
                            
                            <div class="grade-selector">
                                {{ form.applicable_grades }}
                                {% if form.applicable_grades.errors %}
                                    <div class="invalid-feedback d-block">{{ form.applicable_grades.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="form-text">{% trans "Select the grade levels where this subject will be taught in this curriculum" %}</div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'academics:curriculum_subjects' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>{% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                {% if object %}{% trans "Update Mapping" %}{% else %}{% trans "Create Mapping" %}{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add form-control class to all form fields
    const formFields = document.querySelectorAll('input, select, textarea');
    formFields.forEach(field => {
        if (!field.classList.contains('form-check-input')) {
            field.classList.add('form-control');
        }
    });
    
    // Auto-calculate recommended credit hours based on weekly hours
    const weeklyHoursField = document.getElementById('{{ form.weekly_hours.id_for_label }}');
    const creditHoursField = document.getElementById('{{ form.credit_hours.id_for_label }}');
    
    if (weeklyHoursField && creditHoursField) {
        weeklyHoursField.addEventListener('input', function() {
            if (!creditHoursField.value && this.value) {
                // Rough calculation: credit hours = weekly hours / 2
                const suggestedCredits = Math.max(1, Math.floor(this.value / 2));
                creditHoursField.value = suggestedCredits;
            }
        });
    }
    
    // Auto-increment sequence order
    const sequenceField = document.getElementById('{{ form.sequence_order.id_for_label }}');
    if (sequenceField && !sequenceField.value) {
        sequenceField.value = 1;
    }
});
</script>
{% endblock %}