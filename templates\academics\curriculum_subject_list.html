{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Curriculum Subjects" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .subject-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }
    .subject-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    .mandatory-card {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
    }
    .elective-card {
        background: linear-gradient(135deg, #17a2b8, #138496);
        color: white;
    }
    .credit-card {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: white;
    }
    .hours-card {
        background: linear-gradient(135deg, #6f42c1, #5a32a3);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-list-alt text-primary me-2"></i>{% trans "Curriculum Subjects" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage subject mappings within curriculum plans" %}</p>
                </div>
                <div>
                    <a href="{% url 'academics:curriculum_subject_add' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>{% trans "Add Subject Mapping" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card subject-card mandatory-card">
                <div class="card-body text-center">
                    <i class="fas fa-star fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ mandatory_count|default:0 }}</h4>
                    <p class="mb-0">{% trans "Mandatory" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card subject-card elective-card">
                <div class="card-body text-center">
                    <i class="fas fa-hand-pointer fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ elective_count|default:0 }}</h4>
                    <p class="mb-0">{% trans "Elective" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card subject-card credit-card">
                <div class="card-body text-center">
                    <i class="fas fa-certificate fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ total_credits|default:0 }}</h4>
                    <p class="mb-0">{% trans "Total Credits" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card subject-card hours-card">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ total_hours|default:0 }}</h4>
                    <p class="mb-0">{% trans "Weekly Hours" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Curriculum Subjects List -->
    <div class="row">
        <div class="col-12">
            <div class="card subject-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>{% trans "All Curriculum Subjects" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if curriculum_subjects %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>{% trans "Subject" %}</th>
                                        <th>{% trans "Curriculum Plan" %}</th>
                                        <th>{% trans "Credits" %}</th>
                                        <th>{% trans "Weekly Hours" %}</th>
                                        <th>{% trans "Semester" %}</th>
                                        <th>{% trans "Type" %}</th>
                                        <th>{% trans "Sequence" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for curriculum_subject in curriculum_subjects %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-book text-primary me-2"></i>
                                                <div>
                                                    <strong>{{ curriculum_subject.subject.name }}</strong>
                                                    {% if curriculum_subject.subject.code %}
                                                        <br><small class="text-muted">{{ curriculum_subject.subject.code }}</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{{ curriculum_subject.curriculum.name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ curriculum_subject.curriculum.code }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning text-dark">
                                                {{ curriculum_subject.credit_hours }} {% trans "credits" %}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                {{ curriculum_subject.weekly_hours }} {% trans "hrs/week" %}
                                            </span>
                                        </td>
                                        <td>
                                            {% if curriculum_subject.semester == 'first' %}
                                                <span class="badge bg-primary">{% trans "First" %}</span>
                                            {% elif curriculum_subject.semester == 'second' %}
                                                <span class="badge bg-secondary">{% trans "Second" %}</span>
                                            {% elif curriculum_subject.semester == 'full_year' %}
                                                <span class="badge bg-success">{% trans "Full Year" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if curriculum_subject.is_mandatory %}
                                                <span class="badge bg-success">{% trans "Mandatory" %}</span>
                                            {% else %}
                                                <span class="badge bg-info">{% trans "Elective" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">
                                                #{{ curriculum_subject.sequence_order }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{% url 'academics:curriculum_subject_detail' curriculum_subject.pk %}" 
                                                   class="btn btn-outline-info" title="{% trans 'View Details' %}">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{% url 'academics:curriculum_subject_edit' curriculum_subject.pk %}" 
                                                   class="btn btn-outline-primary" title="{% trans 'Edit' %}">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-list-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{% trans "No curriculum subjects found" %}</h5>
                            <p class="text-muted">{% trans "Map subjects to curriculum plans to get started" %}</p>
                            <a href="{% url 'academics:curriculum_subject_add' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>{% trans "Add First Mapping" %}
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
{% endblock %}