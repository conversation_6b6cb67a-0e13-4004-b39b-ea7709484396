{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Academic Dashboard" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .academic-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }
    .academic-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    .subjects-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
    }
    .teachers-card {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        border-radius: 15px;
    }
    .classes-card {
        background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
        color: white;
        border-radius: 15px;
    }
    .exams-card {
        background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
        color: white;
        border-radius: 15px;
    }
    .quick-action-card {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 15px;
        cursor: pointer;
        transition: transform 0.2s;
    }
    .quick-action-card:hover {
        transform: scale(1.05);
        color: white;
        text-decoration: none;
    }
    .schedule-item {
        border-left: 3px solid #007bff;
        padding-left: 1rem;
        margin-bottom: 1rem;
    }
    .performance-chart {
        height: 300px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-graduation-cap text-primary me-2"></i>{% trans "Academic Management Dashboard" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage subjects, classes, schedules, grades, and academic operations" %}</p>
                </div>
                <div>
                    <a href="{% url 'academics:subject_add' %}" class="btn btn-primary me-2">
                        <i class="fas fa-plus me-2"></i>{% trans "Add Subject" %}
                    </a>
                    <a href="{% url 'academics:schedule_create' %}" class="btn btn-success">
                        <i class="fas fa-calendar-plus me-2"></i>{% trans "Create Schedule" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card subjects-card">
                <div class="card-body text-center">
                    <i class="fas fa-book fa-2x mb-2"></i>
                    <h3 class="mb-1">{{ total_subjects }}</h3>
                    <p class="mb-0">{% trans "Total Subjects" %}</p>
                    <small class="opacity-75">{% trans "Active subjects" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card teachers-card">
                <div class="card-body text-center">
                    <i class="fas fa-chalkboard-teacher fa-2x mb-2"></i>
                    <h3 class="mb-1">{{ total_teachers }}</h3>
                    <p class="mb-0">{% trans "Teachers" %}</p>
                    <small class="opacity-75">{% trans "Teaching staff" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card classes-card">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h3 class="mb-1">{{ total_classes }}</h3>
                    <p class="mb-0">{% trans "Classes" %}</p>
                    <small class="opacity-75">{% trans "Active classes" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card exams-card">
                <div class="card-body text-center">
                    <i class="fas fa-clipboard-list fa-2x mb-2"></i>
                    <h3 class="mb-1">{{ upcoming_exams|length }}</h3>
                    <p class="mb-0">{% trans "Upcoming Exams" %}</p>
                    <small class="opacity-75">{% trans "This month" %}</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <h5 class="mb-3">{% trans "Academic Management" %}</h5>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'academics:subjects' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-book fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Subjects" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'academics:teachers' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-chalkboard-teacher fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Teachers" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'academics:schedules' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-calendar fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Schedules" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'academics:grades' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-chart-line fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Student Grades" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'academics:exams' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-clipboard-list fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Exams" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'academics:attendance' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-check fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Student Attendance" %}</h6>
                </div>
            </a>
        </div>
    </div>

    <!-- Additional Academic Features -->
    <div class="row mb-4">
        <div class="col-12">
            <h5 class="mb-3">{% trans "Advanced Features" %}</h5>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'academics:attendance_rules' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-rules fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Attendance Rules" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'academics:attendance_sessions' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Attendance Sessions" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'academics:biometric_devices' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-fingerprint fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Biometric Devices" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'academics:class_subjects' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-chalkboard fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Class Subjects" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'academics:curriculum_plans' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-sitemap fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Curriculum Plans" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'academics:curriculum_subjects' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-list-alt fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Curriculum Subjects" %}</h6>
                </div>
            </a>
        </div>
    </div>

    <!-- More Academic Features -->
    <div class="row mb-4">
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'academics:curriculums' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-graduation-cap fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Curriculums" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'academics:grade_capacity' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-users-cog fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Grade Capacity Management" %}</h6>
                </div>
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <!-- Today's Schedule -->
        <div class="col-lg-6 mb-4">
            <div class="card academic-card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar-day me-2"></i>{% trans "Today's Schedule" %}
                        </h5>
                        <a href="{% url 'academics:schedules' %}" class="btn btn-sm btn-outline-primary">
                            {% trans "View All" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% for schedule in todays_schedule %}
                    <div class="schedule-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ schedule.class_subject.subject.name }} - {{ schedule.class_subject.class_obj.name }}</h6>
                                <small class="text-muted">{% trans "Teacher" %}: {{ schedule.class_subject.teacher.user.get_full_name }}</small>
                            </div>
                            <div class="text-end">
                                <strong>{{ schedule.start_time|time:"H:i" }} - {{ schedule.end_time|time:"H:i" }}</strong>
                                <br>
                                <small class="text-muted">{% trans "Room" %} {{ schedule.room_number|default:"TBA" }}</small>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-calendar-day fa-2x mb-2"></i>
                        <br>
                        {% trans "No classes scheduled for today" %}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Academic Performance -->
        <div class="col-lg-6 mb-4">
            <div class="card academic-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>{% trans "Academic Performance" %}
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="performanceChart" class="performance-chart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Subject Overview and Upcoming Events -->
    <div class="row">
        <div class="col-lg-8 mb-4">
            <div class="card academic-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-book me-2"></i>{% trans "Subject Overview" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>{% trans "Subject" %}</th>
                                    <th>{% trans "Teacher" %}</th>
                                    <th>{% trans "Classes" %}</th>
                                    <th>{% trans "Students" %}</th>
                                    <th>{% trans "Avg Grade" %}</th>
                                    <th>{% trans "Status" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for subject in subject_overview %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-book text-primary me-2"></i>
                                            {{ subject.name }}
                                        </div>
                                    </td>
                                    <td>{{ subject.teacher_count }} {% trans "teacher(s)" %}</td>
                                    <td>{{ subject.class_count }}</td>
                                    <td>{{ subject.student_count }}</td>
                                    <td>
                                        {% if subject.avg_grade %}
                                            {% if subject.avg_grade >= 85 %}
                                                <span class="badge bg-success">{{ subject.avg_grade|floatformat:0 }}%</span>
                                            {% elif subject.avg_grade >= 70 %}
                                                <span class="badge bg-primary">{{ subject.avg_grade|floatformat:0 }}%</span>
                                            {% else %}
                                                <span class="badge bg-warning">{{ subject.avg_grade|floatformat:0 }}%</span>
                                            {% endif %}
                                        {% else %}
                                            <span class="badge bg-secondary">{% trans "N/A" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if subject.avg_grade %}
                                            {% if subject.avg_grade >= 85 %}
                                                <span class="badge bg-success">{% trans "Excellent" %}</span>
                                            {% elif subject.avg_grade >= 70 %}
                                                <span class="badge bg-primary">{% trans "Good" %}</span>
                                            {% else %}
                                                <span class="badge bg-warning">{% trans "Average" %}</span>
                                            {% endif %}
                                        {% else %}
                                            <span class="badge bg-secondary">{% trans "No Data" %}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center text-muted py-4">
                                        <i class="fas fa-book fa-2x mb-2"></i>
                                        <br>
                                        {% trans "No subjects found. Add subjects to get started." %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upcoming Events -->
        <div class="col-lg-4 mb-4">
            <div class="card academic-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>{% trans "Upcoming Events" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        {% for exam in upcoming_exams %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{{ exam.name }}</strong>
                                <br>
                                <small class="text-muted">{{ exam.class_subject.class_obj.name }} - {{ exam.class_subject.subject.name }}</small>
                            </div>
                            <span class="badge bg-primary rounded-pill">{{ exam.exam_date|date:"M d" }}</span>
                        </div>
                        {% empty %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-calendar-alt fa-2x mb-2"></i>
                            <br>
                            {% trans "No upcoming exams scheduled" %}
                        </div>
                        {% endfor %}
                    </div>
                    
                    <hr>
                    
                    <h6 class="mb-3">{% trans "Academic Calendar" %}</h6>
                    <div class="small">
                        <div class="d-flex justify-content-between mb-2">
                            <span>{% trans "Current Year" %}</span>
                            <span class="text-primary">{{ current_academic_year.name|default:"Not Set" }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>{% trans "Days Remaining" %}</span>
                            <span class="text-warning">{{ days_remaining }} {% trans "days" %}</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>{% trans "Academic Progress" %}</span>
                            <span class="text-success">{{ academic_progress|floatformat:0 }}%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Academic Performance Chart
        const ctx = document.getElementById('performanceChart').getContext('2d');
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: [
                    {% for subject in subject_overview %}
                        '{{ subject.name }}'{% if not forloop.last %},{% endif %}
                    {% empty %}
                        '{% trans "No Data" %}'
                    {% endfor %}
                ],
                datasets: [{
                    label: '{% trans "Average Grade %" %}',
                    data: [
                        {% for subject in subject_overview %}
                            {{ subject.avg_grade|default:0|floatformat:0 }}{% if not forloop.last %},{% endif %}
                        {% empty %}
                            0
                        {% endfor %}
                    ],
                    backgroundColor: [
                        '#667eea',
                        '#11998e',
                        '#fc466b',
                        '#fdbb2d',
                        '#764ba2'
                    ],
                    borderRadius: 10,
                    borderSkipped: false,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });
    });
</script>
{% endblock %}
