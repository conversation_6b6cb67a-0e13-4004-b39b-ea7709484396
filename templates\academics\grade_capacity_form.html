{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}
    {% if object %}
        {% trans "Edit Grade Capacity" %}
    {% else %}
        {% trans "Add Grade Capacity" %}
    {% endif %} - {{ block.super }}
{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .form-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .form-header {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: white;
        border-radius: 15px 15px 0 0;
        padding: 1.5rem;
    }
    .form-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    .form-control:focus {
        border-color: #ffc107;
        box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
    }
    .btn-primary {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        border: none;
        color: #212529;
    }
    .btn-primary:hover {
        background: linear-gradient(135deg, #e0a800, #d39e00);
        color: #212529;
    }
    .capacity-preview {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        background: white;
        margin-top: 1rem;
    }
    .progress-custom {
        height: 8px;
        border-radius: 4px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'academics:dashboard' %}">{% trans "Academic Dashboard" %}</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'academics:grade_capacity' %}">{% trans "Grade Capacity Management" %}</a></li>
                    <li class="breadcrumb-item active">
                        {% if object %}{% trans "Edit Capacity" %}{% else %}{% trans "Add Capacity" %}{% endif %}
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card form-card">
                <div class="form-header">
                    <h3 class="mb-0">
                        <i class="fas fa-users-cog me-2"></i>
                        {% if object %}
                            {% trans "Edit Grade Capacity Management" %}
                        {% else %}
                            {% trans "Add New Grade Capacity Management" %}
                        {% endif %}
                    </h3>
                    <p class="mb-0 mt-2 opacity-75">
                        {% trans "Configure enrollment capacity and class size management for grade levels" %}
                    </p>
                </div>
                
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <!-- Basic Information -->
                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-info-circle text-primary me-2"></i>{% trans "Basic Information" %}
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.grade.id_for_label }}" class="form-label">
                                        <i class="fas fa-layer-group me-1"></i>{{ form.grade.label }}
                                    </label>
                                    {{ form.grade }}
                                    {% if form.grade.errors %}
                                        <div class="invalid-feedback d-block">{{ form.grade.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.academic_year.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar me-1"></i>{{ form.academic_year.label }}
                                    </label>
                                    {{ form.academic_year }}
                                    {% if form.academic_year.errors %}
                                        <div class="invalid-feedback d-block">{{ form.academic_year.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Capacity Settings -->
                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-users text-primary me-2"></i>{% trans "Capacity Settings" %}
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.total_capacity.id_for_label }}" class="form-label">
                                        <i class="fas fa-users me-1"></i>{{ form.total_capacity.label }}
                                    </label>
                                    {{ form.total_capacity }}
                                    {% if form.total_capacity.errors %}
                                        <div class="invalid-feedback d-block">{{ form.total_capacity.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">{% trans "Maximum students for this grade" %}</div>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.current_enrollment.id_for_label }}" class="form-label">
                                        <i class="fas fa-user-check me-1"></i>{{ form.current_enrollment.label }}
                                    </label>
                                    {{ form.current_enrollment }}
                                    {% if form.current_enrollment.errors %}
                                        <div class="invalid-feedback d-block">{{ form.current_enrollment.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">{% trans "Currently enrolled students" %}</div>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.waiting_list_capacity.id_for_label }}" class="form-label">
                                        <i class="fas fa-hourglass-half me-1"></i>{{ form.waiting_list_capacity.label }}
                                    </label>
                                    {{ form.waiting_list_capacity }}
                                    {% if form.waiting_list_capacity.errors %}
                                        <div class="invalid-feedback d-block">{{ form.waiting_list_capacity.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">{% trans "Maximum waiting list size" %}</div>
                                </div>
                            </div>
                            
                            <!-- Capacity Preview -->
                            <div class="capacity-preview" id="capacity-preview" style="display: none;">
                                <h6 class="mb-3">{% trans "Capacity Overview" %}</h6>
                                <div class="row text-center">
                                    <div class="col-md-4">
                                        <h5 class="text-primary mb-1" id="preview-total">0</h5>
                                        <small class="text-muted">{% trans "Total Capacity" %}</small>
                                    </div>
                                    <div class="col-md-4">
                                        <h5 class="text-success mb-1" id="preview-enrolled">0</h5>
                                        <small class="text-muted">{% trans "Enrolled" %}</small>
                                    </div>
                                    <div class="col-md-4">
                                        <h5 class="text-info mb-1" id="preview-available">0</h5>
                                        <small class="text-muted">{% trans "Available" %}</small>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <div class="progress progress-custom">
                                        <div class="progress-bar bg-success" id="progress-bar" style="width: 0%"></div>
                                    </div>
                                    <small class="text-muted mt-1 d-block" id="progress-text">0% filled</small>
                                </div>
                            </div>
                        </div>

                        <!-- Class Size Management -->
                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-chalkboard text-primary me-2"></i>{% trans "Class Size Management" %}
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.minimum_class_size.id_for_label }}" class="form-label">
                                        <i class="fas fa-minus-circle me-1"></i>{{ form.minimum_class_size.label }}
                                    </label>
                                    {{ form.minimum_class_size }}
                                    {% if form.minimum_class_size.errors %}
                                        <div class="invalid-feedback d-block">{{ form.minimum_class_size.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.maximum_class_size.id_for_label }}" class="form-label">
                                        <i class="fas fa-plus-circle me-1"></i>{{ form.maximum_class_size.label }}
                                    </label>
                                    {{ form.maximum_class_size }}
                                    {% if form.maximum_class_size.errors %}
                                        <div class="invalid-feedback d-block">{{ form.maximum_class_size.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <div class="form-check mt-4">
                                        {{ form.auto_create_sections }}
                                        <label class="form-check-label" for="{{ form.auto_create_sections.id_for_label }}">
                                            <i class="fas fa-magic me-1"></i>{{ form.auto_create_sections.label }}
                                        </label>
                                    </div>
                                    {% if form.auto_create_sections.errors %}
                                        <div class="invalid-feedback d-block">{{ form.auto_create_sections.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Enrollment Period -->
                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-calendar-alt text-primary me-2"></i>{% trans "Enrollment Period" %}
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.enrollment_start_date.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-plus me-1"></i>{{ form.enrollment_start_date.label }}
                                    </label>
                                    {{ form.enrollment_start_date }}
                                    {% if form.enrollment_start_date.errors %}
                                        <div class="invalid-feedback d-block">{{ form.enrollment_start_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.enrollment_end_date.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-minus me-1"></i>{{ form.enrollment_end_date.label }}
                                    </label>
                                    {{ form.enrollment_end_date }}
                                    {% if form.enrollment_end_date.errors %}
                                        <div class="invalid-feedback d-block">{{ form.enrollment_end_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <div class="form-check mt-4">
                                        {{ form.is_enrollment_open }}
                                        <label class="form-check-label" for="{{ form.is_enrollment_open.id_for_label }}">
                                            <i class="fas fa-door-open me-1"></i>{{ form.is_enrollment_open.label }}
                                        </label>
                                    </div>
                                    {% if form.is_enrollment_open.errors %}
                                        <div class="invalid-feedback d-block">{{ form.is_enrollment_open.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'academics:grade_capacity' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>{% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                {% if object %}{% trans "Update Capacity" %}{% else %}{% trans "Create Capacity" %}{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add form-control class to all form fields
    const formFields = document.querySelectorAll('input, select, textarea');
    formFields.forEach(field => {
        if (!field.classList.contains('form-check-input')) {
            field.classList.add('form-control');
        }
    });
    
    // Capacity preview functionality
    const totalCapacityField = document.getElementById('{{ form.total_capacity.id_for_label }}');
    const currentEnrollmentField = document.getElementById('{{ form.current_enrollment.id_for_label }}');
    const capacityPreview = document.getElementById('capacity-preview');
    
    function updateCapacityPreview() {
        const total = parseInt(totalCapacityField.value) || 0;
        const enrolled = parseInt(currentEnrollmentField.value) || 0;
        const available = Math.max(0, total - enrolled);
        const percentage = total > 0 ? (enrolled / total) * 100 : 0;
        
        if (total > 0 || enrolled > 0) {
            capacityPreview.style.display = 'block';
            
            document.getElementById('preview-total').textContent = total;
            document.getElementById('preview-enrolled').textContent = enrolled;
            document.getElementById('preview-available').textContent = available;
            
            const progressBar = document.getElementById('progress-bar');
            progressBar.style.width = percentage + '%';
            
            // Change color based on capacity
            if (percentage >= 90) {
                progressBar.className = 'progress-bar bg-danger';
            } else if (percentage >= 75) {
                progressBar.className = 'progress-bar bg-warning';
            } else {
                progressBar.className = 'progress-bar bg-success';
            }
            
            document.getElementById('progress-text').textContent = percentage.toFixed(1) + '% filled';
        } else {
            capacityPreview.style.display = 'none';
        }
    }
    
    // Update preview on input change
    totalCapacityField.addEventListener('input', updateCapacityPreview);
    currentEnrollmentField.addEventListener('input', updateCapacityPreview);
    
    // Initial preview update
    updateCapacityPreview();
    
    // Validation for class size
    const minClassSize = document.getElementById('{{ form.minimum_class_size.id_for_label }}');
    const maxClassSize = document.getElementById('{{ form.maximum_class_size.id_for_label }}');
    
    function validateClassSize() {
        const min = parseInt(minClassSize.value) || 0;
        const max = parseInt(maxClassSize.value) || 0;
        
        if (min > 0 && max > 0 && min > max) {
            maxClassSize.setCustomValidity('Maximum class size must be greater than minimum class size');
        } else {
            maxClassSize.setCustomValidity('');
        }
    }
    
    minClassSize.addEventListener('input', validateClassSize);
    maxClassSize.addEventListener('input', validateClassSize);
});
</script>
{% endblock %}