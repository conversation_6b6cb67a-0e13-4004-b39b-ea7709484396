{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Grade Capacity Management" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .capacity-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }
    .capacity-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    .total-capacity-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    .enrolled-card {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
    }
    .available-card {
        background: linear-gradient(135deg, #17a2b8, #138496);
        color: white;
    }
    .waiting-card {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: white;
    }
    .progress-bar-custom {
        height: 8px;
        border-radius: 4px;
    }
    .capacity-progress {
        margin-top: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-users-cog text-primary me-2"></i>{% trans "Grade Capacity Management" %}
                    </h2>
                    <p class="text-muted">{% trans "Monitor and manage enrollment capacity for different grade levels" %}</p>
                </div>
                <div>
                    <a href="{% url 'academics:grade_capacity_add' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>{% trans "Add Capacity Rule" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card capacity-card total-capacity-card">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ total_capacity|default:0 }}</h4>
                    <p class="mb-0">{% trans "Total Capacity" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card capacity-card enrolled-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-check fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ total_enrolled|default:0 }}</h4>
                    <p class="mb-0">{% trans "Enrolled" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card capacity-card available-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-plus fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ total_available|default:0 }}</h4>
                    <p class="mb-0">{% trans "Available" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card capacity-card waiting-card">
                <div class="card-body text-center">
                    <i class="fas fa-hourglass-half fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ total_waiting|default:0 }}</h4>
                    <p class="mb-0">{% trans "Waiting List" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Capacity Management List -->
    <div class="row">
        <div class="col-12">
            <div class="card capacity-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>{% trans "Grade Capacity Overview" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if capacities %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>{% trans "Grade" %}</th>
                                        <th>{% trans "Academic Year" %}</th>
                                        <th>{% trans "Capacity" %}</th>
                                        <th>{% trans "Enrollment" %}</th>
                                        <th>{% trans "Class Size" %}</th>
                                        <th>{% trans "Enrollment Status" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for capacity in capacities %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-layer-group text-primary me-2"></i>
                                                <div>
                                                    <strong>{{ capacity.grade.name }}</strong>
                                                    <br><small class="text-muted">Level {{ capacity.grade.level }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ capacity.academic_year.name }}</small>
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{{ capacity.total_capacity }}</strong> {% trans "students" %}
                                                <div class="capacity-progress">
                                                    <div class="progress progress-bar-custom">
                                                        <div class="progress-bar bg-info" 
                                                             style="width: {{ capacity.enrollment_percentage }}%"
                                                             title="{{ capacity.enrollment_percentage|floatformat:1 }}% filled">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{{ capacity.current_enrollment }}</strong> / {{ capacity.total_capacity }}
                                                <br>
                                                <small class="text-muted">
                                                    {{ capacity.available_capacity }} {% trans "available" %}
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <span class="badge bg-secondary">
                                                    {{ capacity.minimum_class_size }} - {{ capacity.maximum_class_size }}
                                                </span>
                                                <br>
                                                <small class="text-muted">{% trans "students/class" %}</small>
                                            </div>
                                        </td>
                                        <td>
                                            {% if capacity.is_enrollment_open %}
                                                {% if capacity.is_full %}
                                                    <span class="badge bg-warning">{% trans "Full" %}</span>
                                                {% else %}
                                                    <span class="badge bg-success">{% trans "Open" %}</span>
                                                {% endif %}
                                            {% else %}
                                                <span class="badge bg-danger">{% trans "Closed" %}</span>
                                            {% endif %}
                                            <br>
                                            {% if capacity.enrollment_start_date and capacity.enrollment_end_date %}
                                                <small class="text-muted">
                                                    {{ capacity.enrollment_start_date|date:"M d" }} - {{ capacity.enrollment_end_date|date:"M d" }}
                                                </small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{% url 'academics:grade_capacity_detail' capacity.pk %}" 
                                                   class="btn btn-outline-info" title="{% trans 'View Details' %}">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{% url 'academics:grade_capacity_edit' capacity.pk %}" 
                                                   class="btn btn-outline-primary" title="{% trans 'Edit' %}">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                {% if capacity.auto_create_sections and capacity.needs_new_section %}
                                                    <button class="btn btn-outline-success" title="{% trans 'Create Section' %}">
                                                        <i class="fas fa-plus"></i>
                                                    </button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-users-cog fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{% trans "No capacity rules found" %}</h5>
                            <p class="text-muted">{% trans "Set up capacity management for your grade levels to control enrollment" %}</p>
                            <a href="{% url 'academics:grade_capacity_add' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>{% trans "Add First Rule" %}
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
{% endblock %}