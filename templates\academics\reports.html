{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Academic Reports" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .report-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
        cursor: pointer;
    }
    .report-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    .report-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }
    .attendance-reports {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    .grade-reports {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
    }
    .performance-reports {
        background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
        color: white;
    }
    .curriculum-reports {
        background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
        color: white;
    }
    .teacher-reports {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
    }
    .student-reports {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-chart-bar text-primary me-2"></i>{% trans "Academic Reports" %}
                    </h2>
                    <p class="text-muted">{% trans "Generate comprehensive reports for academic performance and analytics" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Categories -->
    <div class="row mb-4">
        <!-- Attendance Reports -->
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card report-card attendance-reports" onclick="location.href='{% url 'academics:attendance_reports' %}'">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-check report-icon"></i>
                    <h4 class="mb-2">{% trans "Attendance Reports" %}</h4>
                    <p class="mb-3">{% trans "Student attendance analytics, trends, and summaries" %}</p>
                    <div class="row text-center">
                        <div class="col-4">
                            <h6 class="mb-0">{{ attendance_stats.daily|default:0 }}</h6>
                            <small>{% trans "Daily" %}</small>
                        </div>
                        <div class="col-4">
                            <h6 class="mb-0">{{ attendance_stats.monthly|default:0 }}</h6>
                            <small>{% trans "Monthly" %}</small>
                        </div>
                        <div class="col-4">
                            <h6 class="mb-0">{{ attendance_stats.yearly|default:0 }}</h6>
                            <small>{% trans "Yearly" %}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Grade Reports -->
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card report-card grade-reports" onclick="location.href='{% url 'academics:grade_reports' %}'">
                <div class="card-body text-center">
                    <i class="fas fa-chart-line report-icon"></i>
                    <h4 class="mb-2">{% trans "Grade Reports" %}</h4>
                    <p class="mb-3">{% trans "Academic performance, transcripts, and grade analysis" %}</p>
                    <div class="row text-center">
                        <div class="col-4">
                            <h6 class="mb-0">{{ grade_stats.subjects|default:0 }}</h6>
                            <small>{% trans "Subjects" %}</small>
                        </div>
                        <div class="col-4">
                            <h6 class="mb-0">{{ grade_stats.students|default:0 }}</h6>
                            <small>{% trans "Students" %}</small>
                        </div>
                        <div class="col-4">
                            <h6 class="mb-0">{{ grade_stats.avg_grade|default:0 }}%</h6>
                            <small>{% trans "Avg Grade" %}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Reports -->
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card report-card performance-reports" onclick="location.href='{% url 'academics:performance_reports' %}'">
                <div class="card-body text-center">
                    <i class="fas fa-trophy report-icon"></i>
                    <h4 class="mb-2">{% trans "Performance Reports" %}</h4>
                    <p class="mb-3">{% trans "Student performance analytics and improvement tracking" %}</p>
                    <div class="row text-center">
                        <div class="col-4">
                            <h6 class="mb-0">{{ performance_stats.excellent|default:0 }}</h6>
                            <small>{% trans "Excellent" %}</small>
                        </div>
                        <div class="col-4">
                            <h6 class="mb-0">{{ performance_stats.good|default:0 }}</h6>
                            <small>{% trans "Good" %}</small>
                        </div>
                        <div class="col-4">
                            <h6 class="mb-0">{{ performance_stats.needs_improvement|default:0 }}</h6>
                            <small>{% trans "Needs Work" %}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Curriculum Reports -->
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card report-card curriculum-reports" onclick="location.href='{% url 'academics:curriculum' %}'">
                <div class="card-body text-center">
                    <i class="fas fa-sitemap report-icon"></i>
                    <h4 class="mb-2">{% trans "Curriculum Reports" %}</h4>
                    <p class="mb-3">{% trans "Curriculum coverage, progress, and effectiveness analysis" %}</p>
                    <div class="row text-center">
                        <div class="col-4">
                            <h6 class="mb-0">{{ curriculum_stats.plans|default:0 }}</h6>
                            <small>{% trans "Plans" %}</small>
                        </div>
                        <div class="col-4">
                            <h6 class="mb-0">{{ curriculum_stats.subjects|default:0 }}</h6>
                            <small>{% trans "Subjects" %}</small>
                        </div>
                        <div class="col-4">
                            <h6 class="mb-0">{{ curriculum_stats.completion|default:0 }}%</h6>
                            <small>{% trans "Complete" %}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Teacher Reports -->
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card report-card teacher-reports" onclick="location.href='{% url 'academics:teachers' %}'">
                <div class="card-body text-center">
                    <i class="fas fa-chalkboard-teacher report-icon"></i>
                    <h4 class="mb-2">{% trans "Teacher Reports" %}</h4>
                    <p class="mb-3">{% trans "Teacher performance, workload, and effectiveness metrics" %}</p>
                    <div class="row text-center">
                        <div class="col-4">
                            <h6 class="mb-0">{{ teacher_stats.total|default:0 }}</h6>
                            <small>{% trans "Teachers" %}</small>
                        </div>
                        <div class="col-4">
                            <h6 class="mb-0">{{ teacher_stats.classes|default:0 }}</h6>
                            <small>{% trans "Classes" %}</small>
                        </div>
                        <div class="col-4">
                            <h6 class="mb-0">{{ teacher_stats.avg_rating|default:0 }}</h6>
                            <small>{% trans "Avg Rating" %}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Student Reports -->
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card report-card student-reports" onclick="location.href='{% url 'academics:progress_reports' %}'">
                <div class="card-body text-center">
                    <i class="fas fa-user-graduate report-icon"></i>
                    <h4 class="mb-2">{% trans "Student Reports" %}</h4>
                    <p class="mb-3">{% trans "Individual student progress, achievements, and development" %}</p>
                    <div class="row text-center">
                        <div class="col-4">
                            <h6 class="mb-0">{{ student_stats.total|default:0 }}</h6>
                            <small>{% trans "Students" %}</small>
                        </div>
                        <div class="col-4">
                            <h6 class="mb-0">{{ student_stats.active|default:0 }}</h6>
                            <small>{% trans "Active" %}</small>
                        </div>
                        <div class="col-4">
                            <h6 class="mb-0">{{ student_stats.graduated|default:0 }}</h6>
                            <small>{% trans "Graduated" %}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Report Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card report-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>{% trans "Quick Reports" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'academics:attendance_summary' %}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-calendar-day me-2"></i>{% trans "Today's Attendance" %}
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'academics:grade_export' %}" class="btn btn-outline-success w-100">
                                <i class="fas fa-download me-2"></i>{% trans "Export Grades" %}
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'academics:class_summary_reports' %}" class="btn btn-outline-info w-100">
                                <i class="fas fa-users me-2"></i>{% trans "Class Summary" %}
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'academics:transcripts' %}" class="btn btn-outline-warning w-100">
                                <i class="fas fa-certificate me-2"></i>{% trans "Transcripts" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
    // Add click handlers for report cards
    document.querySelectorAll('.report-card[onclick]').forEach(card => {
        card.style.cursor = 'pointer';
    });
</script>
{% endblock %}