{% load static %}
{% load i18n %}
{% load dashboard_tags %}
{% load localization_tags %}
<!DOCTYPE html>
<html lang="{% get_current_language %}" dir="{% language_direction %}" class="{% rtl_css_classes %}"
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{% trans "School ERP System" %}{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    {% if is_rtl %}
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    {% else %}
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    {% endif %}
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    
    <!-- RTL CSS Support -->
    {% rtl_css %}
    
    <!-- Loading overlay styles -->
    <style>
        #loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .navbar-nav .dropdown-menu {
            max-height: 400px;
            overflow-y: auto;
        }
        
        /* Fix navbar width overflow */
        .navbar-nav {
            flex-wrap: wrap;
        }
        
        .navbar-nav .nav-item {
            white-space: nowrap;
        }
        
        .navbar-nav .dropdown-menu {
            min-width: 200px;
            max-width: 300px;
        }
        
        /* Responsive navbar adjustments */
        @media (max-width: 1200px) {
            .navbar-nav .nav-link {
                font-size: 0.9rem;
                padding: 0.5rem 0.75rem;
            }
        }
        
        @media (max-width: 992px) {
            .navbar-nav .nav-link {
                font-size: 0.85rem;
                padding: 0.4rem 0.6rem;
            }
            
            .navbar-nav .dropdown-menu {
                position: static !important;
                transform: none !important;
                width: 100%;
                box-shadow: none;
                border: none;
                margin-top: 0;
            }
        }
        
        /* Responsive navbar adjustments */
        @media (max-width: 1200px) {
            .navbar-nav .nav-link {
                font-size: 0.9rem;
                padding: 0.5rem 0.75rem;
            }
        }
        
        @media (max-width: 992px) {
            .navbar-nav .nav-link {
                font-size: 0.85rem;
                padding: 0.4rem 0.6rem;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    {% if user.is_authenticated %}
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand" href="{% url 'accounts:dashboard' %}">
                    <i class="fas fa-school"></i> {% trans "School ERP" %}
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        {% block nav_items %}
                            <!-- Main Dashboard -->
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'accounts:dashboard' %}">
                                    <i class="fas fa-home"></i> {% trans "Dashboard" %}
                                </a>
                            </li>

                            <!-- Students -->
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-users"></i> {% trans "Students" %}
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'students:dashboard' %}">{% trans "Dashboard" %}</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{% url 'students:list' %}">{% trans "All Students" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'students:add_student' %}">{% trans "Add Student" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'students:add_parent' %}">{% trans "Add Parent" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'students:class_list' %}">{% trans "Classes" %}</a></li>
                                </ul>
                            </li>

                            <!-- Academics -->
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-graduation-cap"></i> {% trans "Academics" %}
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'academics:dashboard' %}">{% trans "Dashboard" %}</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{% url 'academics:subjects' %}">{% trans "Subjects" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'academics:teachers' %}">{% trans "Teachers" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'academics:schedules' %}">{% trans "Schedules" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'academics:grades' %}">{% trans "Grades" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'academics:exams' %}">{% trans "Exams" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'academics:attendance' %}">{% trans "Attendance" %}</a></li>
                                </ul>
                            </li>

                            <!-- HR -->
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user-tie"></i> {% trans "HR" %}
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'hr:dashboard' %}">{% trans "Dashboard" %}</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{% url 'hr:employees' %}">{% trans "Employees" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'hr:attendance' %}">{% trans "Attendance" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'hr:payroll' %}">{% trans "Payroll" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'hr:holidays' %}">{% trans "Holidays" %}</a></li>
                                </ul>
                            </li>

                            <!-- Finance -->
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-chart-line"></i> {% trans "Finance" %}
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'finance:dashboard' %}">{% trans "Dashboard" %}</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{% url 'finance:accounts_tree' %}">{% trans "Accounts" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'finance:fees_payment' %}">{% trans "Student Fees" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'finance:daily_entries' %}">{% trans "Transactions" %}</a></li>
                                </ul>
                            </li>

                            <!-- Reports -->
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-chart-bar"></i> {% trans "Reports" %}
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'reports:dashboard' %}">{% trans "Dashboard" %}</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{% url 'reports:students' %}">{% trans "Student Reports" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'reports:academic' %}">{% trans "Academic Reports" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'reports:financial' %}">{% trans "Financial Reports" %}</a></li>
                                </ul>
                            </li>

                            <!-- Health -->
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-heartbeat"></i> {% trans "Health" %}
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'health:dashboard' %}">{% trans "Dashboard" %}</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{% url 'health:profile_list' %}">{% trans "Health Profiles" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'health:incident_dashboard' %}">{% trans "Incidents" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'health:health_monitoring_dashboard' %}">{% trans "Monitoring" %}</a></li>
                                </ul>
                            </li>

                            <!-- Inventory -->
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-boxes"></i> {% trans "Inventory" %}
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'inventory:dashboard' %}">{% trans "Dashboard" %}</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{% url 'inventory:item_list' %}">{% trans "Items" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'inventory:maintenance_dashboard' %}">{% trans "Maintenance" %}</a></li>
                                </ul>
                            </li>

                            <!-- Library -->
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-book"></i> {% trans "Library" %}
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'library:dashboard' %}">{% trans "Dashboard" %}</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{% url 'library:catalog' %}">{% trans "Catalog" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'library:digital_library' %}">{% trans "Digital Library" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'library:borrowing_system' %}">{% trans "Borrowing" %}</a></li>
                                </ul>
                            </li>

                            <!-- Transportation -->
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-bus"></i> {% trans "Transportation" %}
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'transportation:dashboard' %}">{% trans "Dashboard" %}</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{% url 'transportation:vehicle_list' %}">{% trans "Vehicles" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'transportation:route_list' %}">{% trans "Routes" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'transportation:driver_list' %}">{% trans "Drivers" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'transportation:stop_list' %}">{% trans "Bus Stops" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'transportation:student_list' %}">{% trans "Student Assignments" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'transportation:attendance_list' %}">{% trans "Attendance" %}</a></li>
                                    <li><a class="dropdown-item" href="{% url 'transportation:analytics' %}">{% trans "Analytics" %}</a></li>
                                </ul>
                            </li>

                            <!-- More Apps -->
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-h"></i> {% trans "More" %}
                                </a>
                                <ul class="dropdown-menu">
                                    <li><h6 class="dropdown-header">{% trans "Communication" %}</h6></li>
                                    <li><a class="dropdown-item" href="#" onclick="showComingSoon('Communications')">
                                        <i class="fas fa-bullhorn"></i> {% trans "Communications" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="showComingSoon('Notifications')">
                                        <i class="fas fa-bell"></i> {% trans "Notifications" %}
                                    </a></li>
                                    

                                    
                                    {% if user.user_type == 'admin' %}
                                        <li><hr class="dropdown-divider"></li>
                                        <li><h6 class="dropdown-header">{% trans "System Admin" %}</h6></li>
                                        <li><a class="dropdown-item" href="{% url 'core:admin_dashboard' %}">
                                            <i class="fas fa-tachometer-alt"></i> {% trans "Admin Dashboard" %}
                                        </a></li>
                                        <li><a class="dropdown-item" href="{% url 'core:localization_dashboard' %}">
                                            <i class="fas fa-language"></i> {% trans "Localization" %}
                                        </a></li>
                                        <li><a class="dropdown-item" href="{% url 'integrations:dashboard' %}">
                                            <i class="fas fa-plug"></i> {% trans "Integrations" %}
                                        </a></li>
                                        <li><a class="dropdown-item" href="{% url 'webhooks:manage' %}">
                                            <i class="fas fa-link"></i> {% trans "Webhooks" %}
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="showComingSoon('Audit Logs')">
                                            <i class="fas fa-clipboard-list"></i> {% trans "Audit Logs" %}
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="showComingSoon('Calendar')">
                                            <i class="fas fa-calendar"></i> {% trans "Calendar" %}
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="showComingSoon('Encryption')">
                                            <i class="fas fa-lock"></i> {% trans "Encryption" %}
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="showComingSoon('Mobile Auth')">
                                            <i class="fas fa-mobile-alt"></i> {% trans "Mobile Auth" %}
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="{% url 'core:settings' %}">
                                            <i class="fas fa-sliders-h"></i> {% trans "Settings" %}
                                        </a></li>
                                        <li><a class="dropdown-item" href="{% url 'core:backup' %}">
                                            <i class="fas fa-download"></i> {% trans "Backup" %}
                                        </a></li>
                                    {% endif %}
                                </ul>
                            </li>
                        {% endblock %}
                    </ul>
                    
                    <ul class="navbar-nav">
                        <!-- School Switcher -->
                        <li class="nav-item">
                            {% include 'core/school_switcher.html' %}
                        </li>
                        
                        <!-- Language Switcher -->
                        <li class="nav-item">
                            {% language_switcher %}
                        </li>
                        
                        <!-- User Menu -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> {{ user.first_name|default:user.username }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'accounts:profile' %}">
                                    <i class="fas fa-user-edit"></i> {% trans "Profile" %}
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'accounts:logout' %}">
                                    <i class="fas fa-sign-out-alt"></i> {% trans "Logout" %}
                                </a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    {% endif %}
    
    <!-- Skip to main content link for accessibility -->
    <a href="#main-content" class="skip-link">{% trans "Skip to main content" %}</a>

    <!-- Loading overlay -->
    <div id="loading-overlay" class="d-none">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <!-- Toast notification container -->
    <div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;"></div>

    <!-- Main Content -->
    <main class="container-fluid" id="main-content">
        <!-- Breadcrumb Navigation -->
        {% if user.is_authenticated %}
            <div class="row">
                <div class="col-12">
                    {% breadcrumb_nav %}
                </div>
            </div>
        {% endif %}

        <!-- Messages -->
        {% if messages %}
            <div class="row">
                <div class="col-12">
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            <i class="fas fa-info-circle me-2"></i>{{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}

        <!-- Page Content -->
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="bg-light text-center text-lg-start mt-5">
        <div class="text-center p-3">
            © 2025 {% trans "School ERP System" %} - {% trans "All rights reserved" %}
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Toast Notification System -->
    <script src="{% static 'js/toast-notifications.js' %}"></script>
    
    <!-- Error Recovery System -->
    <script src="{% static 'js/error-recovery.js' %}"></script>
    
    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>
    
    <!-- Loading animation and utility scripts -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loadingOverlay = document.getElementById('loading-overlay');
            
            // Show loading on navigation clicks
            document.addEventListener('click', function(e) {
                const target = e.target.closest('a');
                if (target && target.getAttribute('href') && 
                    target.getAttribute('href') !== '#' && 
                    !target.getAttribute('href').startsWith('javascript:') &&
                    !target.hasAttribute('onclick')) {
                    loadingOverlay.classList.remove('d-none');
                    
                    // Hide loading after 3 seconds as fallback
                    setTimeout(() => {
                        loadingOverlay.classList.add('d-none');
                    }, 3000);
                }
            });
            
            // Hide loading overlay when page loads
            window.addEventListener('load', function() {
                loadingOverlay.classList.add('d-none');
            });
            
            // Hide loading on page visibility change (back button, etc.)
            document.addEventListener('visibilitychange', function() {
                if (!document.hidden) {
                    loadingOverlay.classList.add('d-none');
                }
            });
        });
        
        // Coming soon modal function
        function showComingSoon(feature) {
            alert(`${feature} module is coming soon! This feature is currently under development.`);
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
