{% load i18n %}
{% load localization_tags %}

<div class="calendar-widget {% if LANGUAGE_CODE == 'ar' %}rtl-layout arabic-text{% endif %}" 
     data-calendar-type="{{ calendar.type }}" 
     data-year="{{ calendar.year }}" 
     data-month="{{ calendar.month }}">
    
    <!-- Calendar Header -->
    <div class="calendar-header d-flex justify-content-between align-items-center mb-3">
        <button type="button" class="btn btn-outline-primary btn-sm calendar-nav" data-action="prev">
            <i class="fas fa-chevron-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %}"></i>
        </button>
        
        <div class="calendar-title text-center">
            <h5 class="mb-0">{{ calendar.month_name }} {{ calendar.year_display }}</h5>
            {% if calendar.type == 'hijri' %}
                <small class="text-muted">
                    {% trans "Hijri Calendar" %}
                </small>
            {% else %}
                <small class="text-muted">
                    {% trans "Gregorian Calendar" %}
                </small>
            {% endif %}
        </div>
        
        <button type="button" class="btn btn-outline-primary btn-sm calendar-nav" data-action="next">
            <i class="fas fa-chevron-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %}"></i>
        </button>
    </div>
    
    <!-- Calendar Type Switcher -->
    <div class="calendar-type-switcher text-center mb-3">
        <div class="btn-group btn-group-sm" role="group">
            <button type="button" class="btn {% if calendar.type == 'gregorian' %}btn-primary{% else %}btn-outline-primary{% endif %} calendar-type-btn" 
                    data-type="gregorian">
                {% if LANGUAGE_CODE == 'ar' %}
                    ميلادي
                {% else %}
                    {% trans "Gregorian" %}
                {% endif %}
            </button>
            <button type="button" class="btn {% if calendar.type == 'hijri' %}btn-primary{% else %}btn-outline-primary{% endif %} calendar-type-btn" 
                    data-type="hijri">
                {% if LANGUAGE_CODE == 'ar' %}
                    هجري
                {% else %}
                    {% trans "Hijri" %}
                {% endif %}
            </button>
        </div>
    </div>
    
    <!-- Calendar Grid -->
    <div class="calendar-grid">
        <!-- Day Headers -->
        <div class="calendar-day-headers row no-gutters mb-2">
            {% if LANGUAGE_CODE == 'ar' %}
                <!-- Arabic day names (RTL) -->
                <div class="col calendar-day-header text-center font-weight-bold">
                    {% trans "Sun" %}
                </div>
                <div class="col calendar-day-header text-center font-weight-bold">
                    {% trans "Sat" %}
                </div>
                <div class="col calendar-day-header text-center font-weight-bold">
                    {% trans "Fri" %}
                </div>
                <div class="col calendar-day-header text-center font-weight-bold">
                    {% trans "Thu" %}
                </div>
                <div class="col calendar-day-header text-center font-weight-bold">
                    {% trans "Wed" %}
                </div>
                <div class="col calendar-day-header text-center font-weight-bold">
                    {% trans "Tue" %}
                </div>
                <div class="col calendar-day-header text-center font-weight-bold">
                    {% trans "Mon" %}
                </div>
            {% else %}
                <!-- English day names (LTR) -->
                <div class="col calendar-day-header text-center font-weight-bold">
                    {% trans "Mon" %}
                </div>
                <div class="col calendar-day-header text-center font-weight-bold">
                    {% trans "Tue" %}
                </div>
                <div class="col calendar-day-header text-center font-weight-bold">
                    {% trans "Wed" %}
                </div>
                <div class="col calendar-day-header text-center font-weight-bold">
                    {% trans "Thu" %}
                </div>
                <div class="col calendar-day-header text-center font-weight-bold">
                    {% trans "Fri" %}
                </div>
                <div class="col calendar-day-header text-center font-weight-bold">
                    {% trans "Sat" %}
                </div>
                <div class="col calendar-day-header text-center font-weight-bold">
                    {% trans "Sun" %}
                </div>
            {% endif %}
        </div>
        
        <!-- Calendar Days -->
        {% for week in calendar.calendar_matrix %}
        <div class="calendar-week row no-gutters mb-1">
            {% for day in week %}
            <div class="col calendar-day-cell">
                {% if day > 0 %}
                    <div class="calendar-day p-2 text-center position-relative 
                         {% if day == today_day and calendar.year == today_year and calendar.month == today_month %}calendar-day-today{% endif %}
                         {% if day in weekend_days %}calendar-day-weekend{% endif %}
                         {% if day in event_days %}calendar-day-has-events{% endif %}"
                         data-day="{{ day }}"
                         data-date="{{ calendar.year }}-{{ calendar.month|stringformat:'02d' }}-{{ day|stringformat:'02d' }}">
                        
                        <span class="calendar-day-number">
                            {% if LANGUAGE_CODE == 'ar' %}
                                {{ day|arabic_digits }}
                            {% else %}
                                {{ day }}
                            {% endif %}
                        </span>
                        
                        <!-- Event indicators -->
                        {% if day in event_days %}
                        <div class="calendar-day-events">
                            {% for event in event_days|get_item:day %}
                            <div class="calendar-event-indicator" 
                                 style="background-color: {{ event.color }}"
                                 title="{{ event.get_display_title:LANGUAGE_CODE }}"
                                 data-toggle="tooltip">
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}
                        
                        <!-- Hijri date display for Gregorian calendar -->
                        {% if calendar.type == 'gregorian' and show_dual_dates %}
                        <div class="calendar-hijri-date">
                            <small class="text-muted">
                                {% if LANGUAGE_CODE == 'ar' %}
                                    {{ day|get_hijri_day:calendar.year:calendar.month|arabic_digits }}
                                {% else %}
                                    {{ day|get_hijri_day:calendar.year:calendar.month }}
                                {% endif %}
                            </small>
                        </div>
                        {% endif %}
                        
                        <!-- Gregorian date display for Hijri calendar -->
                        {% if calendar.type == 'hijri' and show_dual_dates %}
                        <div class="calendar-gregorian-date">
                            <small class="text-muted">
                                {% if LANGUAGE_CODE == 'ar' %}
                                    {{ day|get_gregorian_day:calendar.year:calendar.month|arabic_digits }}
                                {% else %}
                                    {{ day|get_gregorian_day:calendar.year:calendar.month }}
                                {% endif %}
                            </small>
                        </div>
                        {% endif %}
                    </div>
                {% else %}
                    <div class="calendar-day p-2 calendar-day-empty"></div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        {% endfor %}
    </div>
    
    <!-- Calendar Legend -->
    {% if events %}
    <div class="calendar-legend mt-3">
        <h6 class="mb-2">
            {% if LANGUAGE_CODE == 'ar' %}
                الأحداث
            {% else %}
                {% trans "Events" %}
            {% endif %}
        </h6>
        <div class="calendar-legend-items">
            {% for event_type, event_info in event_types.items %}
            <div class="calendar-legend-item d-inline-flex align-items-center mr-3 mb-1">
                <div class="calendar-legend-color" style="background-color: {{ event_info.color }}"></div>
                <small class="ml-1">
                    {% if LANGUAGE_CODE == 'ar' %}
                        {{ event_info.name_ar }}
                    {% else %}
                        {{ event_info.name_en }}
                    {% endif %}
                </small>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- Today Button -->
    <div class="calendar-actions text-center mt-3">
        <button type="button" class="btn btn-sm btn-outline-primary calendar-today-btn">
            <i class="fas fa-calendar-day"></i>
            {% if LANGUAGE_CODE == 'ar' %}
                اليوم
            {% else %}
                {% trans "Today" %}
            {% endif %}
        </button>
        
        {% if show_dual_dates %}
        <button type="button" class="btn btn-sm btn-outline-secondary calendar-dual-toggle" 
                data-toggle="tooltip" 
                title="{% if LANGUAGE_CODE == 'ar' %}إظهار/إخفاء التواريخ المزدوجة{% else %}{% trans 'Toggle dual dates' %}{% endif %}">
            <i class="fas fa-calendar-alt"></i>
        </button>
        {% endif %}
    </div>
</div>

<!-- Event Details Modal -->
<div class="modal fade" id="calendarEventModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    {% if LANGUAGE_CODE == 'ar' %}
                        تفاصيل الحدث
                    {% else %}
                        {% trans "Event Details" %}
                    {% endif %}
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="calendarEventContent">
                <!-- Event details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    {% if LANGUAGE_CODE == 'ar' %}
                        إغلاق
                    {% else %}
                        {% trans "Close" %}
                    {% endif %}
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.calendar-widget {
    max-width: 400px;
    margin: 0 auto;
}

.calendar-day-header {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    padding: 0.5rem;
    font-size: 0.875rem;
}

.calendar-day-cell {
    border: 1px solid #dee2e6;
    min-height: 60px;
}

.calendar-day {
    cursor: pointer;
    transition: background-color 0.2s;
    min-height: 58px;
}

.calendar-day:hover {
    background-color: #f8f9fa;
}

.calendar-day-today {
    background-color: #007bff !important;
    color: white;
}

.calendar-day-weekend {
    background-color: #fff3cd;
}

.calendar-day-has-events {
    font-weight: bold;
}

.calendar-day-empty {
    background-color: #f8f9fa;
    opacity: 0.5;
}

.calendar-day-number {
    font-size: 1rem;
    font-weight: 500;
}

.calendar-day-events {
    position: absolute;
    bottom: 2px;
    left: 2px;
    right: 2px;
    display: flex;
    justify-content: center;
    gap: 2px;
}

.calendar-event-indicator {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    display: inline-block;
}

.calendar-hijri-date,
.calendar-gregorian-date {
    position: absolute;
    bottom: 2px;
    right: 2px;
    font-size: 0.7rem;
}

.rtl-layout .calendar-hijri-date,
.rtl-layout .calendar-gregorian-date {
    right: auto;
    left: 2px;
}

.calendar-legend {
    border-top: 1px solid #dee2e6;
    padding-top: 1rem;
}

.calendar-legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    margin-right: 0.25rem;
}

.rtl-layout .calendar-legend-color {
    margin-right: 0;
    margin-left: 0.25rem;
}

.calendar-type-switcher .btn {
    min-width: 80px;
}

/* Mobile responsive */
@media (max-width: 576px) {
    .calendar-widget {
        max-width: 100%;
    }
    
    .calendar-day-header {
        font-size: 0.75rem;
        padding: 0.25rem;
    }
    
    .calendar-day-cell {
        min-height: 40px;
    }
    
    .calendar-day {
        min-height: 38px;
        padding: 0.25rem !important;
    }
    
    .calendar-day-number {
        font-size: 0.875rem;
    }
}

/* Print styles */
@media print {
    .calendar-actions,
    .calendar-type-switcher {
        display: none !important;
    }
    
    .calendar-widget {
        max-width: none;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calendarWidget = document.querySelector('.calendar-widget');
    if (!calendarWidget) return;
    
    // Calendar navigation
    calendarWidget.addEventListener('click', function(e) {
        if (e.target.closest('.calendar-nav')) {
            const action = e.target.closest('.calendar-nav').dataset.action;
            const currentType = calendarWidget.dataset.calendarType;
            const currentYear = parseInt(calendarWidget.dataset.year);
            const currentMonth = parseInt(calendarWidget.dataset.month);
            
            let newYear = currentYear;
            let newMonth = currentMonth;
            
            if (action === 'prev') {
                newMonth--;
                if (newMonth < 1) {
                    newMonth = 12;
                    newYear--;
                }
            } else if (action === 'next') {
                newMonth++;
                if (newMonth > 12) {
                    newMonth = 1;
                    newYear++;
                }
            }
            
            loadCalendar(currentType, newYear, newMonth);
        }
    });
    
    // Calendar type switching
    calendarWidget.addEventListener('click', function(e) {
        if (e.target.closest('.calendar-type-btn')) {
            const newType = e.target.closest('.calendar-type-btn').dataset.type;
            const currentYear = parseInt(calendarWidget.dataset.year);
            const currentMonth = parseInt(calendarWidget.dataset.month);
            
            loadCalendar(newType, currentYear, currentMonth);
        }
    });
    
    // Today button
    calendarWidget.addEventListener('click', function(e) {
        if (e.target.closest('.calendar-today-btn')) {
            const today = new Date();
            const currentType = calendarWidget.dataset.calendarType;
            
            loadCalendar(currentType, today.getFullYear(), today.getMonth() + 1);
        }
    });
    
    // Day click
    calendarWidget.addEventListener('click', function(e) {
        if (e.target.closest('.calendar-day') && !e.target.closest('.calendar-day-empty')) {
            const dayElement = e.target.closest('.calendar-day');
            const date = dayElement.dataset.date;
            
            // Load event details for this date
            loadEventDetails(date);
        }
    });
    
    // Dual dates toggle
    calendarWidget.addEventListener('click', function(e) {
        if (e.target.closest('.calendar-dual-toggle')) {
            const hijriDates = calendarWidget.querySelectorAll('.calendar-hijri-date, .calendar-gregorian-date');
            hijriDates.forEach(el => {
                el.style.display = el.style.display === 'none' ? 'block' : 'none';
            });
        }
    });
    
    function loadCalendar(type, year, month) {
        // Show loading state
        calendarWidget.style.opacity = '0.5';
        
        // Make AJAX request to load new calendar
        fetch(`/calendar/widget/?type=${type}&year=${year}&month=${month}`)
            .then(response => response.text())
            .then(html => {
                calendarWidget.outerHTML = html;
                // Re-initialize event listeners
                document.dispatchEvent(new Event('DOMContentLoaded'));
            })
            .catch(error => {
                console.error('Error loading calendar:', error);
                calendarWidget.style.opacity = '1';
            });
    }
    
    function loadEventDetails(date) {
        const modal = document.getElementById('calendarEventModal');
        const content = document.getElementById('calendarEventContent');
        
        // Show loading state
        content.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';
        
        // Show modal
        $(modal).modal('show');
        
        // Load event details
        fetch(`/calendar/events/?date=${date}`)
            .then(response => response.text())
            .then(html => {
                content.innerHTML = html;
            })
            .catch(error => {
                console.error('Error loading event details:', error);
                content.innerHTML = '<div class="alert alert-danger">Error loading event details.</div>';
            });
    }
    
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
});
</script>