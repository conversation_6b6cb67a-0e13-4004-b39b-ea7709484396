{% load i18n %}
{% load localization_tags %}

<div class="language-switcher dropdown">
    <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="languageDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
        <i class="fas fa-globe"></i>
        {{ current_language_name }}
    </button>
    <div class="dropdown-menu {% if is_rtl %}dropdown-menu-right{% else %}dropdown-menu-left{% endif %}" aria-labelledby="languageDropdown">
        {% for code, name in available_languages %}
            {% if code != current_language %}
                <a class="dropdown-item" href="{% language_switch_url code current_url %}">
                    <span class="flag-icon flag-icon-{% if code == 'ar' %}sa{% else %}{{ code }}{% endif %}"></span>
                    {{ name }}
                </a>
            {% endif %}
        {% endfor %}
    </div>
</div>

<style>
.language-switcher {
    display: inline-block;
}

.language-switcher .dropdown-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
}

.language-switcher .flag-icon {
    margin-{% if is_rtl %}left{% else %}right{% endif %}: 0.5rem;
    width: 20px;
    height: 15px;
}

.language-switcher .dropdown-toggle {
    border: 1px solid #dee2e6;
    background-color: transparent;
}

.language-switcher .dropdown-toggle:hover {
    background-color: #f8f9fa;
}

{% if is_rtl %}
.language-switcher .dropdown-menu {
    text-align: right;
}

.language-switcher .dropdown-item {
    flex-direction: row-reverse;
}
{% endif %}
</style>