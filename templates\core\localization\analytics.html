{% extends "base.html" %}
{% load i18n %}
{% load localization_tags %}

{% block title %}{% trans "Translation Analytics" %}{% endblock %}

{% block extra_css %}
    {% rtl_css %}
    <style>
        .analytics-dashboard {
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        .analytics-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .metric-card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            height: 100%;
        }
        
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .metric-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .metric-change {
            font-size: 0.85rem;
            margin-top: 0.5rem;
        }
        
        .metric-change.positive {
            color: #28a745;
        }
        
        .metric-change.negative {
            color: #dc3545;
        }
        
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        .language-progress {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .language-item {
            display: flex;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #f1f3f4;
        }
        
        .language-item:last-child {
            border-bottom: none;
        }
        
        .language-flag {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            margin-right: 1rem;
            font-size: 0.9rem;
        }
        
        .language-info {
            flex-grow: 1;
        }
        
        .language-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .language-stats {
            font-size: 0.85rem;
            color: #6c757d;
        }
        
        .progress-bar-container {
            width: 120px;
            margin-left: 1rem;
        }
        
        .progress-percentage {
            text-align: center;
            font-weight: bold;
            margin-bottom: 0.25rem;
        }
        
        .progress {
            height: 8px;
            border-radius: 4px;
        }
        
        .filter-controls {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .date-range-picker {
            display: flex;
            gap: 1rem;
            align-items: end;
        }
        
        .export-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        @media (max-width: 768px) {
            .analytics-header {
                padding: 1.5rem;
            }
            
            .metric-number {
                font-size: 2rem;
            }
            
            .date-range-picker {
                flex-direction: column;
                align-items: stretch;
            }
            
            .export-buttons {
                flex-direction: column;
            }
            
            .language-flag {
                width: 35px;
                height: 35px;
                font-size: 0.8rem;
            }
        }
    </style>
{% endblock %}

{% block content %}
<div class="analytics-dashboard {% if is_rtl %}rtl-layout{% endif %}">
    <div class="container-fluid">
        <!-- Header -->
        <div class="analytics-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1>
                        <i class="fas fa-chart-line"></i>
                        {% trans "Translation Analytics" %}
                    </h1>
                    <p class="mb-0 opacity-75">
                        {% trans "Comprehensive insights into translation progress and quality" %}
                    </p>
                </div>
                <div class="export-buttons">
                    <button class="btn btn-light" onclick="exportAnalytics('pdf')">
                        <i class="fas fa-file-pdf"></i> {% trans "Export PDF" %}
                    </button>
                    <button class="btn btn-light" onclick="exportAnalytics('excel')">
                        <i class="fas fa-file-excel"></i> {% trans "Export Excel" %}
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Filter Controls -->
        <div class="filter-controls">
            <div class="row align-items-end">
                <div class="col-md-6">
                    <div class="date-range-picker">
                        <div class="form-group">
                            <label for="dateFrom">{% trans "From Date" %}</label>
                            <input type="date" class="form-control" id="dateFrom" value="{{ date_from }}">
                        </div>
                        <div class="form-group">
                            <label for="dateTo">{% trans "To Date" %}</label>
                            <input type="date" class="form-control" id="dateTo" value="{{ date_to }}">
                        </div>
                        <button class="btn btn-primary" onclick="updateAnalytics()">
                            <i class="fas fa-sync"></i> {% trans "Update" %}
                        </button>
                    </div>
                </div>
                <div class="col-md-6 text-right">
                    <div class="form-group">
                        <label for="languageFilter">{% trans "Filter by Language" %}</label>
                        <select class="form-control" id="languageFilter" onchange="filterByLanguage()">
                            <option value="">{% trans "All Languages" %}</option>
                            {% for language_code, data in analytics_data.items %}
                                <option value="{{ language_code }}">{{ data.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Key Metrics -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="metric-card">
                    <div class="metric-number text-primary">
                        {{ total_languages|format_number }}
                    </div>
                    <div class="metric-label">{% trans "Active Languages" %}</div>
                    <div class="metric-change positive">
                        <i class="fas fa-arrow-up"></i> +2 {% trans "this month" %}
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="metric-card">
                    <div class="metric-number text-success">
                        {{ overall_completion|format_number }}%
                    </div>
                    <div class="metric-label">{% trans "Overall Completion" %}</div>
                    <div class="metric-change positive">
                        <i class="fas fa-arrow-up"></i> +5.2% {% trans "this week" %}
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="metric-card">
                    <div class="metric-number text-info">
                        {{ total_strings|format_number }}
                    </div>
                    <div class="metric-label">{% trans "Total Strings" %}</div>
                    <div class="metric-change positive">
                        <i class="fas fa-arrow-up"></i> +127 {% trans "new strings" %}
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="metric-card">
                    <div class="metric-number text-warning">
                        {{ pending_translations|format_number }}
                    </div>
                    <div class="metric-label">{% trans "Pending Translations" %}</div>
                    <div class="metric-change negative">
                        <i class="fas fa-arrow-down"></i> -23 {% trans "this week" %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Charts Row -->
        <div class="row">
            <div class="col-lg-8">
                <!-- Completion Progress Chart -->
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="fas fa-chart-bar"></i>
                        {% trans "Translation Progress by Language" %}
                    </h5>
                    <canvas id="progressChart" height="300"></canvas>
                </div>
                
                <!-- Timeline Chart -->
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="fas fa-chart-line"></i>
                        {% trans "Translation Activity Timeline" %}
                    </h5>
                    <canvas id="timelineChart" height="250"></canvas>
                </div>
            </div>
            
            <div class="col-lg-4">
                <!-- Language Progress List -->
                <div class="language-progress">
                    <h5 class="mb-3">
                        <i class="fas fa-globe"></i>
                        {% trans "Language Status" %}
                    </h5>
                    
                    {% for language_code, data in analytics_data.items %}
                        {% if not data.error %}
                            <div class="language-item" data-language="{{ language_code }}">
                                <div class="language-flag" style="background: {{ data.color|default:'#6c757d' }}">
                                    {{ language_code|upper }}
                                </div>
                                <div class="language-info">
                                    <div class="language-name">{{ data.name }}</div>
                                    <div class="language-stats">
                                        {{ data.translated|format_number }}/{{ data.total|format_number }} {% trans "strings" %}
                                        {% if data.fuzzy > 0 %}
                                            • {{ data.fuzzy|format_number }} {% trans "fuzzy" %}
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="progress-bar-container">
                                    <div class="progress-percentage">{{ data.completion_percentage|format_number }}%</div>
                                    <div class="progress">
                                        <div class="progress-bar 
                                                    {% if data.completion_percentage >= 90 %}bg-success
                                                    {% elif data.completion_percentage >= 70 %}bg-info
                                                    {% elif data.completion_percentage >= 50 %}bg-warning
                                                    {% else %}bg-danger{% endif %}" 
                                             style="width: {{ data.completion_percentage }}%"></div>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    {% endfor %}
                </div>
                
                <!-- Quality Metrics -->
                <div class="chart-container mt-3">
                    <h5 class="mb-3">
                        <i class="fas fa-shield-alt"></i>
                        {% trans "Quality Metrics" %}
                    </h5>
                    <canvas id="qualityChart" height="200"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Detailed Statistics Table -->
        <div class="chart-container">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0">
                    <i class="fas fa-table"></i>
                    {% trans "Detailed Statistics" %}
                </h5>
                <div class="btn-group" role="group">
                    <button class="btn btn-sm btn-outline-secondary" onclick="toggleView('table')">
                        <i class="fas fa-table"></i> {% trans "Table" %}
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="toggleView('cards')">
                        <i class="fas fa-th"></i> {% trans "Cards" %}
                    </button>
                </div>
            </div>
            
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="thead-light">
                        <tr>
                            <th>{% trans "Language" %}</th>
                            <th>{% trans "Total Strings" %}</th>
                            <th>{% trans "Translated" %}</th>
                            <th>{% trans "Untranslated" %}</th>
                            <th>{% trans "Fuzzy" %}</th>
                            <th>{% trans "Completion" %}</th>
                            <th>{% trans "Last Updated" %}</th>
                            <th>{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for language_code, data in analytics_data.items %}
                            {% if not data.error %}
                                <tr data-language="{{ language_code }}">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="language-flag mr-2" style="background: {{ data.color|default:'#6c757d' }}; width: 30px; height: 30px;">
                                                {{ language_code|upper }}
                                            </div>
                                            <div>
                                                <strong>{{ data.name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ language_code }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ data.total|format_number }}</td>
                                    <td>
                                        <span class="text-success">{{ data.translated|format_number }}</span>
                                    </td>
                                    <td>
                                        <span class="text-danger">{{ data.untranslated|format_number }}</span>
                                    </td>
                                    <td>
                                        <span class="text-warning">{{ data.fuzzy|format_number }}</span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="progress mr-2" style="width: 60px; height: 6px;">
                                                <div class="progress-bar 
                                                            {% if data.completion_percentage >= 90 %}bg-success
                                                            {% elif data.completion_percentage >= 70 %}bg-info
                                                            {% elif data.completion_percentage >= 50 %}bg-warning
                                                            {% else %}bg-danger{% endif %}" 
                                                     style="width: {{ data.completion_percentage }}%"></div>
                                            </div>
                                            <span class="font-weight-bold">{{ data.completion_percentage|format_number }}%</span>
                                        </div>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ data.last_updated|format_datetime_localized|default:"N/A" }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'core:translation_editor' language_code %}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'core:export_translations' language_code %}" 
                                               class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-download"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endif %}
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Chart colors
    const chartColors = {
        primary: '#007bff',
        success: '#28a745',
        warning: '#ffc107',
        danger: '#dc3545',
        info: '#17a2b8',
        secondary: '#6c757d'
    };
    
    // Progress Chart
    const progressCtx = document.getElementById('progressChart').getContext('2d');
    const progressChart = new Chart(progressCtx, {
        type: 'bar',
        data: {
            labels: [{% for language_code, data in analytics_data.items %}{% if not data.error %}'{{ data.name }}'{% if not forloop.last %},{% endif %}{% endif %}{% endfor %}],
            datasets: [{
                label: '{% trans "Translated" %}',
                data: [{% for language_code, data in analytics_data.items %}{% if not data.error %}{{ data.translated }}{% if not forloop.last %},{% endif %}{% endif %}{% endfor %}],
                backgroundColor: chartColors.success,
                borderColor: chartColors.success,
                borderWidth: 1
            }, {
                label: '{% trans "Untranslated" %}',
                data: [{% for language_code, data in analytics_data.items %}{% if not data.error %}{{ data.untranslated }}{% if not forloop.last %},{% endif %}{% endif %}{% endfor %}],
                backgroundColor: chartColors.danger,
                borderColor: chartColors.danger,
                borderWidth: 1
            }, {
                label: '{% trans "Fuzzy" %}',
                data: [{% for language_code, data in analytics_data.items %}{% if not data.error %}{{ data.fuzzy }}{% if not forloop.last %},{% endif %}{% endif %}{% endfor %}],
                backgroundColor: chartColors.warning,
                borderColor: chartColors.warning,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    stacked: true
                },
                y: {
                    stacked: true,
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            }
        }
    });
    
    // Timeline Chart (mock data for demonstration)
    const timelineCtx = document.getElementById('timelineChart').getContext('2d');
    const timelineChart = new Chart(timelineCtx, {
        type: 'line',
        data: {
            labels: ['{% trans "Jan" %}', '{% trans "Feb" %}', '{% trans "Mar" %}', '{% trans "Apr" %}', '{% trans "May" %}', '{% trans "Jun" %}'],
            datasets: [{
                label: '{% trans "Translations Added" %}',
                data: [120, 190, 300, 500, 200, 300],
                borderColor: chartColors.primary,
                backgroundColor: chartColors.primary + '20',
                tension: 0.4,
                fill: true
            }, {
                label: '{% trans "Translations Updated" %}',
                data: [80, 120, 200, 300, 150, 250],
                borderColor: chartColors.info,
                backgroundColor: chartColors.info + '20',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                }
            }
        }
    });
    
    // Quality Chart
    const qualityCtx = document.getElementById('qualityChart').getContext('2d');
    const qualityChart = new Chart(qualityCtx, {
        type: 'doughnut',
        data: {
            labels: ['{% trans "High Quality" %}', '{% trans "Good Quality" %}', '{% trans "Needs Review" %}'],
            datasets: [{
                data: [65, 25, 10],
                backgroundColor: [chartColors.success, chartColors.info, chartColors.warning],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // Filter functions
    function updateAnalytics() {
        const dateFrom = document.getElementById('dateFrom').value;
        const dateTo = document.getElementById('dateTo').value;
        
        // Update URL with date parameters and reload
        const url = new URL(window.location);
        url.searchParams.set('date_from', dateFrom);
        url.searchParams.set('date_to', dateTo);
        window.location.href = url.toString();
    }
    
    function filterByLanguage() {
        const selectedLanguage = document.getElementById('languageFilter').value;
        const languageItems = document.querySelectorAll('[data-language]');
        
        languageItems.forEach(item => {
            if (selectedLanguage === '' || item.dataset.language === selectedLanguage) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });
    }
    
    function exportAnalytics(format) {
        const url = `{% url 'core:export_analytics' %}?format=${format}`;
        window.open(url, '_blank');
    }
    
    function toggleView(viewType) {
        // Toggle between table and card views
        const table = document.querySelector('.table-responsive');
        const buttons = document.querySelectorAll('.btn-group button');
        
        buttons.forEach(btn => btn.classList.remove('active'));
        event.target.classList.add('active');
        
        if (viewType === 'cards') {
            // Convert table to cards (implementation would go here)
            console.log('Switching to cards view');
        } else {
            // Show table view
            table.style.display = 'block';
        }
    }
    
    // Auto-refresh every 5 minutes
    setInterval(() => {
        if (document.visibilityState === 'visible') {
            location.reload();
        }
    }, 300000);
    
    // Initialize tooltips
    $(document).ready(function() {
        $('[data-toggle="tooltip"]').tooltip();
    });
</script>
{% endblock %}