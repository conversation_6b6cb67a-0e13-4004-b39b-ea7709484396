{% extends "base.html" %}
{% load i18n %}
{% load localization_tags %}

{% block title %}{% trans "Localization Management" %}{% endblock %}

{% block extra_css %}
    {% rtl_css %}
    <style>
        .translation-stats {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .language-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .language-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        
        .progress-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            margin-right: 1rem;
        }
        
        .progress-excellent {
            background: linear-gradient(135deg, #28a745, #20c997);
        }
        
        .progress-good {
            background: linear-gradient(135deg, #17a2b8, #007bff);
        }
        
        .progress-fair {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }
        
        .progress-poor {
            background: linear-gradient(135deg, #dc3545, #e83e8c);
        }
        
        .recent-updates {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .update-item {
            border-left: 3px solid #007bff;
            padding: 1rem;
            margin-bottom: 1rem;
            background: #f8f9fa;
            border-radius: 0 8px 8px 0;
        }
        
        .update-time {
            font-size: 0.85rem;
            color: #6c757d;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        @media (max-width: 768px) {
            .stat-card {
                margin-bottom: 1rem;
            }
            
            .progress-circle {
                width: 50px;
                height: 50px;
                font-size: 0.9rem;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
{% endblock %}

{% block content %}
<div class="container-fluid {% if is_rtl %}rtl-layout{% endif %}">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>
                    <i class="fas fa-language"></i>
                    {% trans "Localization Management" %}
                </h1>
                <div class="action-buttons">
                    <a href="{% url 'core:translation_analytics' %}" class="btn btn-info">
                        <i class="fas fa-chart-bar"></i> {% trans "Analytics" %}
                    </a>
                    <a href="{% url 'core:translation_validation' %}" class="btn btn-warning">
                        <i class="fas fa-check-circle"></i> {% trans "Validation" %}
                    </a>
                    <button class="btn btn-success" onclick="compileTranslations()">
                        <i class="fas fa-cogs"></i> {% trans "Compile Translations" %}
                    </button>
                </div>
            </div>
            
            <!-- Translation Statistics Overview -->
            <div class="translation-stats">
                <h3 class="mb-4">{% trans "Translation Overview" %}</h3>
                <div class="row">
                    {% for language_code, stats in translation_stats.items %}
                        <div class="col-md-3 col-sm-6 mb-3">
                            <div class="stat-card">
                                <div class="stat-number">{{ stats.completion_percentage|format_number }}%</div>
                                <div class="stat-label">
                                    {{ stats.language_name }}
                                    <br>
                                    <small>{{ stats.translated_count|format_number }}/{{ stats.total_count|format_number }} {% trans "strings" %}</small>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
            
            <!-- Language Management Cards -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-globe"></i>
                                {% trans "Language Status" %}
                            </h5>
                        </div>
                        <div class="card-body">
                            {% for language in languages %}
                                <div class="language-card">
                                    <div class="d-flex align-items-center">
                                        <div class="progress-circle progress-{{ language.progress_class }}">
                                            {{ language.completion_percentage|format_number }}%
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">
                                                {{ language.name }}
                                                {% if language.is_rtl %}
                                                    <span class="badge badge-info">RTL</span>
                                                {% endif %}
                                                {% if language.is_default %}
                                                    <span class="badge badge-primary">{% trans "Default" %}</span>
                                                {% endif %}
                                            </h6>
                                            <p class="mb-2 text-muted">
                                                {{ language.translated_strings|format_number }} {% trans "of" %} 
                                                {{ language.total_strings|format_number }} {% trans "strings translated" %}
                                            </p>
                                            <div class="progress" style="height: 6px;">
                                                <div class="progress-bar bg-{{ language.progress_class }}" 
                                                     style="width: {{ language.completion_percentage }}%"></div>
                                            </div>
                                        </div>
                                        <div class="ml-3">
                                            <div class="btn-group" role="group">
                                                <a href="{% url 'core:edit_translations' language.code %}" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i> {% trans "Edit" %}
                                                </a>
                                                <a href="{% url 'core:export_translations' language.code %}" 
                                                   class="btn btn-sm btn-outline-secondary">
                                                    <i class="fas fa-download"></i> {% trans "Export" %}
                                                </a>
                                                {% if not language.is_default %}
                                                    <button class="btn btn-sm btn-outline-danger" 
                                                            onclick="confirmDeleteLanguage('{{ language.code }}')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                    
                                    {% if language.missing_keys %}
                                        <div class="mt-3">
                                            <small class="text-warning">
                                                <i class="fas fa-exclamation-triangle"></i>
                                                {{ language.missing_keys|length }} {% trans "missing translations" %}
                                            </small>
                                            <div class="collapse" id="missing-{{ language.code }}">
                                                <div class="mt-2">
                                                    {% for key in language.missing_keys|slice:":5" %}
                                                        <span class="badge badge-warning mr-1">{{ key }}</span>
                                                    {% endfor %}
                                                    {% if language.missing_keys|length > 5 %}
                                                        <span class="text-muted">{% trans "and" %} {{ language.missing_keys|length|add:"-5" }} {% trans "more" %}</span>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            <button class="btn btn-link btn-sm p-0" 
                                                    data-toggle="collapse" 
                                                    data-target="#missing-{{ language.code }}">
                                                {% trans "Show missing" %}
                                            </button>
                                        </div>
                                    {% endif %}
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <!-- Recent Updates -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-clock"></i>
                                {% trans "Recent Updates" %}
                            </h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="recent-updates">
                                {% for update in recent_updates %}
                                    <div class="update-item">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <strong>{{ update.key }}</strong>
                                                <br>
                                                <small class="text-muted">{{ update.language_name }}</small>
                                            </div>
                                            <div class="update-time">
                                                {{ update.updated_at|format_datetime_localized }}
                                            </div>
                                        </div>
                                        <div class="mt-2">
                                            <small class="text-success">{{ update.new_value|truncatechars:50 }}</small>
                                        </div>
                                    </div>
                                {% empty %}
                                    <div class="text-center p-4 text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        {% trans "No recent updates" %}
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-bolt"></i>
                                {% trans "Quick Actions" %}
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" onclick="addNewLanguage()">
                                    <i class="fas fa-plus"></i> {% trans "Add Language" %}
                                </button>
                                <button class="btn btn-info" onclick="scanMissingTranslations()">
                                    <i class="fas fa-search"></i> {% trans "Scan Missing" %}
                                </button>
                                <button class="btn btn-warning" onclick="validateAllTranslations()">
                                    <i class="fas fa-check-double"></i> {% trans "Validate All" %}
                                </button>
                                <a href="{% url 'core:translation_import' %}" class="btn btn-success">
                                    <i class="fas fa-upload"></i> {% trans "Import Translations" %}
                                </a>
                                <button class="btn btn-secondary" onclick="exportAllTranslations()">
                                    <i class="fas fa-download"></i> {% trans "Export All" %}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Language Modal -->
<div class="modal fade" id="addLanguageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Add New Language" %}</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="addLanguageForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="languageCode">{% trans "Language Code" %}</label>
                        <input type="text" class="form-control" id="languageCode" 
                               placeholder="e.g., fr, es, de" required>
                        <small class="form-text text-muted">
                            {% trans "ISO 639-1 language code" %}
                        </small>
                    </div>
                    <div class="form-group">
                        <label for="languageName">{% trans "Language Name" %}</label>
                        <input type="text" class="form-control" id="languageName" 
                               placeholder="e.g., Français, Español" required>
                    </div>
                    <div class="form-group">
                        <label for="languageNameNative">{% trans "Native Name" %}</label>
                        <input type="text" class="form-control" id="languageNameNative" 
                               placeholder="e.g., Français, Español">
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="isRTL">
                        <label class="form-check-label" for="isRTL">
                            {% trans "Right-to-Left (RTL) Language" %}
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        {% trans "Cancel" %}
                    </button>
                    <button type="submit" class="btn btn-primary">
                        {% trans "Add Language" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteLanguageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Confirm Delete" %}</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>{% trans "Are you sure you want to delete this language? This action cannot be undone." %}</p>
                <p class="text-danger">
                    <strong>{% trans "Warning:" %}</strong> 
                    {% trans "All translations for this language will be permanently lost." %}
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    {% trans "Cancel" %}
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    {% trans "Delete Language" %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let languageToDelete = null;
    
    function addNewLanguage() {
        $('#addLanguageModal').modal('show');
    }
    
    function confirmDeleteLanguage(languageCode) {
        languageToDelete = languageCode;
        $('#deleteLanguageModal').modal('show');
    }
    
    function compileTranslations() {
        if (confirm('{% trans "Compile all translation files? This may take a few moments." %}')) {
            fetch('{% url "core:compile_translations" %}', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}',
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', '{% trans "Translations compiled successfully!" %}');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showAlert('error', data.message || '{% trans "Failed to compile translations." %}');
                }
            })
            .catch(error => {
                showAlert('error', '{% trans "An error occurred while compiling translations." %}');
            });
        }
    }
    
    function scanMissingTranslations() {
        showAlert('info', '{% trans "Scanning for missing translations..." %}');
        
        fetch('{% url "core:scan_missing_translations" %}', {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', `{% trans "Found" %} ${data.missing_count} {% trans "missing translations." %}`);
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('error', data.message || '{% trans "Failed to scan translations." %}');
            }
        })
        .catch(error => {
            showAlert('error', '{% trans "An error occurred while scanning translations." %}');
        });
    }
    
    function validateAllTranslations() {
        showAlert('info', '{% trans "Validating all translations..." %}');
        
        fetch('{% url "core:validate_translations" %}', {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.errors.length > 0) {
                    showAlert('warning', `{% trans "Validation completed with" %} ${data.errors.length} {% trans "errors." %}`);
                } else {
                    showAlert('success', '{% trans "All translations are valid!" %}');
                }
            } else {
                showAlert('error', data.message || '{% trans "Failed to validate translations." %}');
            }
        })
        .catch(error => {
            showAlert('error', '{% trans "An error occurred while validating translations." %}');
        });
    }
    
    function exportAllTranslations() {
        window.location.href = '{% url "core:export_all_translations" %}';
    }
    
    function showAlert(type, message) {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 
                          type === 'warning' ? 'alert-warning' : 'alert-info';
        
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `;
        
        $('.container-fluid').prepend(alertHtml);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            $('.alert').alert('close');
        }, 5000);
    }
    
    // Handle add language form submission
    $('#addLanguageForm').on('submit', function(e) {
        e.preventDefault();
        
        const formData = {
            code: $('#languageCode').val(),
            name: $('#languageName').val(),
            native_name: $('#languageNameNative').val(),
            is_rtl: $('#isRTL').is(':checked')
        };
        
        fetch('{% url "core:add_language" %}', {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                $('#addLanguageModal').modal('hide');
                showAlert('success', '{% trans "Language added successfully!" %}');
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('error', data.message || '{% trans "Failed to add language." %}');
            }
        })
        .catch(error => {
            showAlert('error', '{% trans "An error occurred while adding the language." %}');
        });
    });
    
    // Handle delete confirmation
    $('#confirmDeleteBtn').on('click', function() {
        if (languageToDelete) {
            fetch(`{% url "core:delete_language" "PLACEHOLDER" %}`.replace('PLACEHOLDER', languageToDelete), {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}',
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    $('#deleteLanguageModal').modal('hide');
                    showAlert('success', '{% trans "Language deleted successfully!" %}');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showAlert('error', data.message || '{% trans "Failed to delete language." %}');
                }
            })
            .catch(error => {
                showAlert('error', '{% trans "An error occurred while deleting the language." %}');
            });
        }
    });
    
    // Reset modal forms when closed
    $('.modal').on('hidden.bs.modal', function() {
        $(this).find('form')[0]?.reset();
        languageToDelete = null;
    });
</script>
{% endblock %}