{% extends "base.html" %}
{% load i18n %}
{% load localization_tags %}

{% block title %}{% trans "Translation Testing Tools" %}{% endblock %}

{% block extra_css %}
    {% rtl_css %}
    <style>
        .testing-dashboard {
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        .test-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .summary-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .summary-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .summary-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .test-result {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #dee2e6;
        }
        
        .test-result.pass {
            border-left-color: #28a745;
        }
        
        .test-result.fail {
            border-left-color: #dc3545;
        }
        
        .test-result.warn {
            border-left-color: #ffc107;
        }
        
        .test-status {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .test-status.pass {
            background: #d4edda;
            color: #155724;
        }
        
        .test-status.fail {
            background: #f8d7da;
            color: #721c24;
        }
        
        .test-status.warn {
            background: #fff3cd;
            color: #856404;
        }
        
        .test-name {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .test-details {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .run-tests-btn {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            z-index: 1000;
        }
        
        @media (max-width: 768px) {
            .test-summary {
                padding: 1.5rem;
            }
            
            .summary-number {
                font-size: 1.5rem;
            }
            
            .run-tests-btn {
                bottom: 1rem;
                right: 1rem;
            }
        }
    </style>
{% endblock %}

{% block content %}
<div class="testing-dashboard {% if is_rtl %}rtl-layout{% endif %}">
    <div class="container-fluid">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1>
                    <i class="fas fa-vial"></i>
                    {% trans "Translation Testing Tools" %}
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item">
                            <a href="{% url 'core:localization_dashboard' %}">
                                {% trans "Localization" %}
                            </a>
                        </li>
                        <li class="breadcrumb-item active">{% trans "Testing Tools" %}</li>
                    </ol>
                </nav>
            </div>
            <div>
                <button class="btn btn-primary" onclick="runAllTests()">
                    <i class="fas fa-play"></i> {% trans "Run All Tests" %}
                </button>
            </div>
        </div>
        
        <!-- Test Summary -->
        <div class="test-summary">
            <h3 class="mb-4">{% trans "Test Results Summary" %}</h3>
            <div class="row">
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="summary-card">
                        <div class="summary-number text-success">{{ passed_tests|format_number }}</div>
                        <div class="summary-label">{% trans "Passed" %}</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="summary-card">
                        <div class="summary-number text-danger">{{ failed_tests|format_number }}</div>
                        <div class="summary-label">{% trans "Failed" %}</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="summary-card">
                        <div class="summary-number text-warning">{{ warning_tests|format_number }}</div>
                        <div class="summary-label">{% trans "Warnings" %}</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="summary-card">
                        <div class="summary-number">{{ total_tests|format_number }}</div>
                        <div class="summary-label">{% trans "Total Tests" %}</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list-check"></i>
                            {% trans "Detailed Test Results" %}
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        {% for result in test_results %}
                            <div class="test-result {{ result.status|lower }}">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <div class="test-name">{{ result.test_name }}</div>
                                        <div class="test-details">{{ result.details }}</div>
                                    </div>
                                    <div class="test-status {{ result.status|lower }}">
                                        {% if result.status == 'PASS' %}
                                            <i class="fas fa-check-circle mr-1"></i>
                                        {% elif result.status == 'FAIL' %}
                                            <i class="fas fa-times-circle mr-1"></i>
                                        {% elif result.status == 'WARN' %}
                                            <i class="fas fa-exclamation-triangle mr-1"></i>
                                        {% endif %}
                                        {{ result.status }}
                                    </div>
                                </div>
                            </div>
                        {% empty %}
                            <div class="text-center p-5">
                                <i class="fas fa-flask fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">{% trans "No test results available" %}</h5>
                                <p class="text-muted">{% trans "Click 'Run All Tests' to start testing" %}</p>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Categories -->
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-file-check"></i>
                            {% trans "File Integrity Tests" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">{% trans "Verify that all translation files can be loaded and parsed correctly." %}</p>
                        <button class="btn btn-outline-primary btn-sm" onclick="runTestCategory('integrity')">
                            <i class="fas fa-play"></i> {% trans "Run Tests" %}
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-code"></i>
                            {% trans "Placeholder Consistency" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">{% trans "Check that all placeholders in translations match the source strings." %}</p>
                        <button class="btn btn-outline-primary btn-sm" onclick="runTestCategory('placeholders')">
                            <i class="fas fa-play"></i> {% trans "Run Tests" %}
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-pie"></i>
                            {% trans "Completion Thresholds" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">{% trans "Verify that translation completion meets quality thresholds." %}</p>
                        <button class="btn btn-outline-primary btn-sm" onclick="runTestCategory('completion')">
                            <i class="fas fa-play"></i> {% trans "Run Tests" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Advanced Testing Options -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-cogs"></i>
                            {% trans "Advanced Testing Options" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="testLanguage">{% trans "Test Specific Language" %}</label>
                                    <select class="form-control" id="testLanguage">
                                        <option value="">{% trans "All Languages" %}</option>
                                        {% for language_code, language_name in languages %}
                                            <option value="{{ language_code }}">{{ language_name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="testType">{% trans "Test Type" %}</label>
                                    <select class="form-control" id="testType">
                                        <option value="all">{% trans "All Tests" %}</option>
                                        <option value="integrity">{% trans "File Integrity" %}</option>
                                        <option value="placeholders">{% trans "Placeholder Consistency" %}</option>
                                        <option value="completion">{% trans "Completion Thresholds" %}</option>
                                        <option value="html">{% trans "HTML Tag Consistency" %}</option>
                                        <option value="fuzzy">{% trans "Fuzzy Translation Check" %}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-primary" onclick="runCustomTest()">
                                <i class="fas fa-play"></i> {% trans "Run Custom Test" %}
                            </button>
                            <button class="btn btn-outline-secondary" onclick="exportTestResults()">
                                <i class="fas fa-download"></i> {% trans "Export Results" %}
                            </button>
                            <button class="btn btn-outline-info" onclick="scheduleTests()">
                                <i class="fas fa-clock"></i> {% trans "Schedule Tests" %}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Floating Run Tests Button -->
    <div class="run-tests-btn">
        <button class="btn btn-success btn-lg rounded-circle" onclick="runAllTests()" title="{% trans 'Run All Tests' %}">
            <i class="fas fa-play"></i>
        </button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function runAllTests() {
        showAlert('info', '{% trans "Running all tests..." %}');
        
        // Show loading state
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> {% trans "Running..." %}';
        button.disabled = true;
        
        // Simulate test execution (in real implementation, this would be an AJAX call)
        setTimeout(() => {
            button.innerHTML = originalText;
            button.disabled = false;
            showAlert('success', '{% trans "All tests completed successfully!" %}');
            // Reload page to show updated results
            location.reload();
        }, 3000);
    }
    
    function runTestCategory(category) {
        showAlert('info', `{% trans "Running" %} ${category} {% trans "tests..." %}`);
        
        // In a real implementation, this would make an AJAX call to run specific test category
        setTimeout(() => {
            showAlert('success', `${category} {% trans "tests completed!" %}`);
        }, 2000);
    }
    
    function runCustomTest() {
        const language = document.getElementById('testLanguage').value;
        const testType = document.getElementById('testType').value;
        
        let message = '{% trans "Running custom test" %}';
        if (language) {
            message += ` {% trans "for" %} ${language}`;
        }
        if (testType !== 'all') {
            message += ` (${testType})`;
        }
        
        showAlert('info', message + '...');
        
        // Simulate custom test execution
        setTimeout(() => {
            showAlert('success', '{% trans "Custom test completed!" %}');
        }, 2000);
    }
    
    function exportTestResults() {
        // Create CSV content
        let csvContent = "Test Name,Status,Details\n";
        
        {% for result in test_results %}
            csvContent += `"{{ result.test_name|escapejs }}","{{ result.status }}","{{ result.details|escapejs }}"\n`;
        {% endfor %}
        
        // Create and download file
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'translation_test_results.csv';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        
        showAlert('success', '{% trans "Test results exported successfully!" %}');
    }
    
    function scheduleTests() {
        // This would open a modal or redirect to a scheduling interface
        showAlert('info', '{% trans "Test scheduling feature coming soon!" %}');
    }
    
    function showAlert(type, message) {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 
                          type === 'warning' ? 'alert-warning' : 'alert-info';
        
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
                 style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
                ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', alertHtml);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            const alert = document.querySelector('.alert:last-of-type');
            if (alert) {
                $(alert).alert('close');
            }
        }, 5000);
    }
    
    // Auto-refresh test results every 30 seconds if tests are running
    let autoRefreshInterval;
    
    function startAutoRefresh() {
        autoRefreshInterval = setInterval(() => {
            if (document.visibilityState === 'visible') {
                // Check if tests are still running (in real implementation)
                // location.reload();
            }
        }, 30000);
    }
    
    function stopAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
        }
    }
    
    // Initialize
    $(document).ready(function() {
        // Add tooltips
        $('[data-toggle="tooltip"]').tooltip();
        
        // Start auto-refresh if needed
        // startAutoRefresh();
    });
    
    // Clean up on page unload
    window.addEventListener('beforeunload', stopAutoRefresh);
</script>
{% endblock %}