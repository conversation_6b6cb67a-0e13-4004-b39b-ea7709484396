{% extends "base.html" %}
{% load i18n %}
{% load localization_tags %}

{% block title %}{% trans "Translation Editor" %} - {{ language_name }}{% endblock %}

{% block extra_css %}
    {% rtl_css %}
    <style>
        .translation-editor {
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        .editor-header {
            background: white;
            border-bottom: 1px solid #dee2e6;
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .translation-entry {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 1rem;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .translation-entry:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .translation-entry.fuzzy {
            border-left: 4px solid #ffc107;
        }
        
        .translation-entry.untranslated {
            border-left: 4px solid #dc3545;
        }
        
        .translation-entry.translated {
            border-left: 4px solid #28a745;
        }
        
        .entry-header {
            background: #f8f9fa;
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: between;
            align-items: center;
        }
        
        .entry-flags {
            display: flex;
            gap: 0.5rem;
        }
        
        .flag-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        
        .entry-body {
            padding: 1rem;
        }
        
        .source-text {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 0.75rem;
            margin-bottom: 1rem;
            font-family: monospace;
            white-space: pre-wrap;
        }
        
        .translation-input {
            width: 100%;
            min-height: 80px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 0.75rem;
            font-family: inherit;
            resize: vertical;
        }
        
        .translation-input:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        
        .entry-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1rem;
        }
        
        .entry-meta {
            font-size: 0.85rem;
            color: #6c757d;
        }
        
        .search-filters {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stats-bar {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
        }
        
        .progress-stats {
            display: flex;
            justify-content: space-around;
            text-align: center;
        }
        
        .stat-item {
            flex: 1;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #007bff;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .floating-save {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            z-index: 1000;
        }
        
        @media (max-width: 768px) {
            .progress-stats {
                flex-direction: column;
                gap: 1rem;
            }
            
            .entry-actions {
                flex-direction: column;
                gap: 0.5rem;
            }
            
            .floating-save {
                bottom: 1rem;
                right: 1rem;
            }
        }
    </style>
{% endblock %}

{% block content %}
<div class="translation-editor {% if is_rtl %}rtl-layout{% endif %}">
    <div class="container-fluid">
        <!-- Header -->
        <div class="editor-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1>
                        <i class="fas fa-edit"></i>
                        {% trans "Translation Editor" %} - {{ language_name }}
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item">
                                <a href="{% url 'core:localization_dashboard' %}">
                                    {% trans "Localization" %}
                                </a>
                            </li>
                            <li class="breadcrumb-item active">{{ language_name }}</li>
                        </ol>
                    </nav>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-success" onclick="saveAllChanges()">
                        <i class="fas fa-save"></i> {% trans "Save All" %}
                    </button>
                    <a href="{% url 'core:export_translations' language_code %}" class="btn btn-outline-secondary">
                        <i class="fas fa-download"></i> {% trans "Export" %}
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Statistics Bar -->
        <div class="stats-bar">
            <div class="progress-stats">
                <div class="stat-item">
                    <div class="stat-number">{{ total_entries|format_number }}</div>
                    <div class="stat-label">{% trans "Total Strings" %}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number text-success">{{ translated_entries|format_number }}</div>
                    <div class="stat-label">{% trans "Translated" %}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number text-warning">{{ fuzzy_entries|format_number }}</div>
                    <div class="stat-label">{% trans "Fuzzy" %}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number text-danger">{{ total_entries|add:"-"|add:translated_entries|format_number }}</div>
                    <div class="stat-label">{% trans "Untranslated" %}</div>
                </div>
            </div>
        </div>
        
        <!-- Search and Filters -->
        <div class="search-filters">
            <form method="get" class="row align-items-end">
                <div class="col-md-4">
                    <label for="search" class="form-label">{% trans "Search" %}</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ search_query }}" placeholder="{% trans 'Search in source text...' %}">
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">{% trans "Status" %}</label>
                    <select class="form-control" id="status" name="status">
                        <option value="all" {% if status_filter == 'all' %}selected{% endif %}>
                            {% trans "All" %}
                        </option>
                        <option value="translated" {% if status_filter == 'translated' %}selected{% endif %}>
                            {% trans "Translated" %}
                        </option>
                        <option value="untranslated" {% if status_filter == 'untranslated' %}selected{% endif %}>
                            {% trans "Untranslated" %}
                        </option>
                        <option value="fuzzy" {% if status_filter == 'fuzzy' %}selected{% endif %}>
                            {% trans "Fuzzy" %}
                        </option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> {% trans "Filter" %}
                    </button>
                    <a href="{% url 'core:translation_editor' language_code %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> {% trans "Clear" %}
                    </a>
                </div>
                <div class="col-md-2 text-right">
                    <small class="text-muted">
                        {{ entries|length }} {% trans "entries shown" %}
                    </small>
                </div>
            </form>
        </div>
        
        <!-- Translation Entries -->
        <div class="translation-entries">
            {% for entry in entries %}
                <div class="translation-entry {% if entry.is_fuzzy %}fuzzy{% elif entry.is_translated %}translated{% else %}untranslated{% endif %}" 
                     data-msgid="{{ entry.msgid }}">
                    <div class="entry-header">
                        <div class="entry-flags">
                            {% if entry.is_fuzzy %}
                                <span class="badge badge-warning flag-badge">{% trans "Fuzzy" %}</span>
                            {% endif %}
                            {% if not entry.is_translated %}
                                <span class="badge badge-danger flag-badge">{% trans "Untranslated" %}</span>
                            {% endif %}
                            {% if entry.is_translated and not entry.is_fuzzy %}
                                <span class="badge badge-success flag-badge">{% trans "Translated" %}</span>
                            {% endif %}
                        </div>
                        <div class="entry-meta">
                            {% if entry.occurrences %}
                                <small>
                                    <i class="fas fa-map-marker-alt"></i>
                                    {% for occurrence in entry.occurrences|slice:":3" %}
                                        {{ occurrence.0 }}:{{ occurrence.1 }}{% if not forloop.last %}, {% endif %}
                                    {% endfor %}
                                    {% if entry.occurrences|length > 3 %}
                                        {% trans "and" %} {{ entry.occurrences|length|add:"-3" }} {% trans "more" %}
                                    {% endif %}
                                </small>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="entry-body">
                        <!-- Source Text -->
                        <div class="mb-3">
                            <label class="form-label">
                                <strong>{% trans "Source" %} (English):</strong>
                            </label>
                            <div class="source-text">{{ entry.msgid }}</div>
                            {% if entry.msgctxt %}
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    {% trans "Context" %}: {{ entry.msgctxt }}
                                </small>
                            {% endif %}
                        </div>
                        
                        <!-- Translation Input -->
                        <div class="mb-3">
                            <label class="form-label">
                                <strong>{% trans "Translation" %} ({{ language_name }}):</strong>
                            </label>
                            <textarea class="translation-input" 
                                      data-msgid="{{ entry.msgid }}"
                                      placeholder="{% trans 'Enter translation...' %}"
                                      {% if language_code == 'ar' %}dir="rtl"{% endif %}>{{ entry.msgstr }}</textarea>
                        </div>
                        
                        <!-- Comments -->
                        {% if entry.comment %}
                            <div class="mb-3">
                                <label class="form-label">
                                    <strong>{% trans "Comment" %}:</strong>
                                </label>
                                <div class="alert alert-info">
                                    {{ entry.comment }}
                                </div>
                            </div>
                        {% endif %}
                        
                        <!-- Actions -->
                        <div class="entry-actions">
                            <div class="entry-meta">
                                {% if entry.is_fuzzy %}
                                    <small class="text-warning">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        {% trans "This translation needs review" %}
                                    </small>
                                {% endif %}
                            </div>
                            <div>
                                <button class="btn btn-sm btn-success" 
                                        onclick="saveTranslation('{{ entry.msgid|escapejs }}')">
                                    <i class="fas fa-check"></i> {% trans "Save" %}
                                </button>
                                {% if entry.is_fuzzy %}
                                    <button class="btn btn-sm btn-warning" 
                                            onclick="markAsReviewed('{{ entry.msgid|escapejs }}')">
                                        <i class="fas fa-eye"></i> {% trans "Mark Reviewed" %}
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            {% empty %}
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">{% trans "No translations found" %}</h5>
                    <p class="text-muted">{% trans "Try adjusting your search criteria" %}</p>
                </div>
            {% endfor %}
        </div>
    </div>
    
    <!-- Floating Save Button -->
    <div class="floating-save">
        <button class="btn btn-success btn-lg rounded-circle" onclick="saveAllChanges()" title="{% trans 'Save All Changes' %}">
            <i class="fas fa-save"></i>
        </button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let unsavedChanges = new Set();
    
    // Track changes in translation inputs
    document.querySelectorAll('.translation-input').forEach(input => {
        input.addEventListener('input', function() {
            const msgid = this.dataset.msgid;
            unsavedChanges.add(msgid);
            updateSaveButton();
        });
    });
    
    function updateSaveButton() {
        const saveButton = document.querySelector('.floating-save button');
        if (unsavedChanges.size > 0) {
            saveButton.classList.add('btn-warning');
            saveButton.classList.remove('btn-success');
            saveButton.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
            saveButton.title = `${unsavedChanges.size} {% trans "unsaved changes" %}`;
        } else {
            saveButton.classList.add('btn-success');
            saveButton.classList.remove('btn-warning');
            saveButton.innerHTML = '<i class="fas fa-save"></i>';
            saveButton.title = '{% trans "All changes saved" %}';
        }
    }
    
    function saveTranslation(msgid) {
        const input = document.querySelector(`textarea[data-msgid="${msgid}"]`);
        const msgstr = input.value;
        
        fetch('{% url "core:update_translation" %}', {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                'language_code': '{{ language_code }}',
                'msgid': msgid,
                'msgstr': msgstr
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                unsavedChanges.delete(msgid);
                updateSaveButton();
                showAlert('success', '{% trans "Translation saved successfully" %}');
                
                // Update entry status
                const entry = input.closest('.translation-entry');
                entry.classList.remove('untranslated', 'fuzzy');
                entry.classList.add('translated');
                
                // Update badges
                const flagsContainer = entry.querySelector('.entry-flags');
                flagsContainer.innerHTML = '<span class="badge badge-success flag-badge">{% trans "Translated" %}</span>';
            } else {
                showAlert('error', data.error || '{% trans "Failed to save translation" %}');
            }
        })
        .catch(error => {
            showAlert('error', '{% trans "An error occurred while saving" %}');
        });
    }
    
    function saveAllChanges() {
        if (unsavedChanges.size === 0) {
            showAlert('info', '{% trans "No changes to save" %}');
            return;
        }
        
        const promises = Array.from(unsavedChanges).map(msgid => {
            const input = document.querySelector(`textarea[data-msgid="${msgid}"]`);
            const msgstr = input.value;
            
            return fetch('{% url "core:update_translation" %}', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}',
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    'language_code': '{{ language_code }}',
                    'msgid': msgid,
                    'msgstr': msgstr
                })
            }).then(response => response.json());
        });
        
        Promise.all(promises)
            .then(results => {
                const successful = results.filter(r => r.success).length;
                const failed = results.length - successful;
                
                if (failed === 0) {
                    unsavedChanges.clear();
                    updateSaveButton();
                    showAlert('success', `{% trans "All" %} ${successful} {% trans "translations saved successfully" %}`);
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showAlert('warning', `${successful} {% trans "saved," %} ${failed} {% trans "failed" %}`);
                }
            })
            .catch(error => {
                showAlert('error', '{% trans "An error occurred while saving translations" %}');
            });
    }
    
    function markAsReviewed(msgid) {
        // This would mark a fuzzy translation as reviewed
        const input = document.querySelector(`textarea[data-msgid="${msgid}"]`);
        const msgstr = input.value;
        
        if (!msgstr.trim()) {
            showAlert('warning', '{% trans "Please provide a translation before marking as reviewed" %}');
            return;
        }
        
        saveTranslation(msgid);
    }
    
    function showAlert(type, message) {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 
                          type === 'warning' ? 'alert-warning' : 'alert-info';
        
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
                 style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
                ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', alertHtml);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            const alert = document.querySelector('.alert:last-of-type');
            if (alert) {
                $(alert).alert('close');
            }
        }, 5000);
    }
    
    // Warn about unsaved changes when leaving page
    window.addEventListener('beforeunload', function(e) {
        if (unsavedChanges.size > 0) {
            e.preventDefault();
            e.returnValue = '{% trans "You have unsaved changes. Are you sure you want to leave?" %}';
        }
    });
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl+S or Cmd+S to save all
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            saveAllChanges();
        }
    });
</script>
{% endblock %}