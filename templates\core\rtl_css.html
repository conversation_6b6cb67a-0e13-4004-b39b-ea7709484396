{% if is_rtl %}
<style>
/* RTL Layout Styles */
body {
    direction: rtl;
    text-align: right;
}

/* Bootstrap RTL Overrides */
.text-left {
    text-align: right !important;
}

.text-right {
    text-align: left !important;
}

.float-left {
    float: right !important;
}

.float-right {
    float: left !important;
}

/* Margin and Padding RTL */
.ml-1 { margin-right: 0.25rem !important; margin-left: 0 !important; }
.ml-2 { margin-right: 0.5rem !important; margin-left: 0 !important; }
.ml-3 { margin-right: 1rem !important; margin-left: 0 !important; }
.ml-4 { margin-right: 1.5rem !important; margin-left: 0 !important; }
.ml-5 { margin-right: 3rem !important; margin-left: 0 !important; }
.ml-auto { margin-right: auto !important; margin-left: 0 !important; }

.mr-1 { margin-left: 0.25rem !important; margin-right: 0 !important; }
.mr-2 { margin-left: 0.5rem !important; margin-right: 0 !important; }
.mr-3 { margin-left: 1rem !important; margin-right: 0 !important; }
.mr-4 { margin-left: 1.5rem !important; margin-right: 0 !important; }
.mr-5 { margin-left: 3rem !important; margin-right: 0 !important; }
.mr-auto { margin-left: auto !important; margin-right: 0 !important; }

.pl-1 { padding-right: 0.25rem !important; padding-left: 0 !important; }
.pl-2 { padding-right: 0.5rem !important; padding-left: 0 !important; }
.pl-3 { padding-right: 1rem !important; padding-left: 0 !important; }
.pl-4 { padding-right: 1.5rem !important; padding-left: 0 !important; }
.pl-5 { padding-right: 3rem !important; padding-left: 0 !important; }

.pr-1 { padding-left: 0.25rem !important; padding-right: 0 !important; }
.pr-2 { padding-left: 0.5rem !important; padding-right: 0 !important; }
.pr-3 { padding-left: 1rem !important; padding-right: 0 !important; }
.pr-4 { padding-left: 1.5rem !important; padding-right: 0 !important; }
.pr-5 { padding-left: 3rem !important; padding-right: 0 !important; }

/* Navigation RTL */
.navbar-nav {
    flex-direction: row-reverse;
}

.navbar-brand {
    margin-right: 0;
    margin-left: 1rem;
}

.navbar-toggler {
    margin-left: 0;
    margin-right: auto;
}

/* Dropdown RTL */
.dropdown-menu {
    right: 0;
    left: auto;
    text-align: right;
}

.dropdown-item {
    text-align: right;
}

/* Form RTL */
.form-group label {
    text-align: right;
}

.form-control {
    text-align: right;
}

.input-group-prepend {
    order: 2;
}

.input-group-append {
    order: 0;
}

.input-group .form-control {
    order: 1;
}

/* Table RTL */
.table th,
.table td {
    text-align: right;
}

.table th:first-child,
.table td:first-child {
    border-right: 0;
}

.table th:last-child,
.table td:last-child {
    border-left: 0;
}

/* Card RTL */
.card-header {
    text-align: right;
}

.card-body {
    text-align: right;
}

/* Sidebar RTL */
.sidebar {
    right: 0;
    left: auto;
}

.sidebar .nav-link {
    text-align: right;
}

.sidebar .nav-link i {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* Breadcrumb RTL */
.breadcrumb {
    flex-direction: row-reverse;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "\\";
    padding-right: 0.5rem;
    padding-left: 0;
}

/* Modal RTL */
.modal-header {
    text-align: right;
}

.modal-body {
    text-align: right;
}

.modal-footer {
    justify-content: flex-start;
}

.modal-footer .btn {
    margin-left: 0;
    margin-right: 0.25rem;
}

/* Alert RTL */
.alert {
    text-align: right;
}

.alert-dismissible .close {
    right: auto;
    left: 0;
    padding-right: 0;
    padding-left: 1.25rem;
}

/* Pagination RTL */
.pagination {
    flex-direction: row-reverse;
}

/* Progress RTL */
.progress-bar {
    right: 0;
    left: auto;
}

/* Custom RTL Classes */
.rtl-layout {
    direction: rtl;
    text-align: right;
}

.rtl-layout .container,
.rtl-layout .container-fluid {
    direction: rtl;
}

.rtl-layout .row {
    direction: rtl;
}

/* Arabic Font Support */
.arabic-text {
    font-family: 'Amiri', 'Noto Sans Arabic', 'Arial Unicode MS', sans-serif;
    line-height: 1.8;
}

/* Number formatting for Arabic */
.arabic-numbers {
    font-family: 'Noto Sans Arabic', 'Arial Unicode MS', sans-serif;
}

/* Fix for icons in RTL */
.fa, .fas, .far, .fal, .fab {
    display: inline-block;
    transform: scaleX(-1);
}

/* Don't flip certain icons */
.fa-search,
.fa-user,
.fa-home,
.fa-cog,
.fa-bell,
.fa-envelope,
.fa-calendar,
.fa-clock,
.fa-phone,
.fa-globe {
    transform: none;
}
</style>

<!-- Arabic Font Loading -->
<link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Noto+Sans+Arabic:wght@400;700&display=swap" rel="stylesheet">
{% endif %}