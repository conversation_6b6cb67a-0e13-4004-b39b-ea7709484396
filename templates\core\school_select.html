{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Select School" %}{% endblock %}

{% block extra_css %}
<style>
    .school-card {
        transition: all 0.3s ease;
        cursor: pointer;
        border: 2px solid transparent;
    }
    
    .school-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        border-color: #007bff;
    }
    
    .school-card.selected {
        border-color: #28a745;
        background-color: #f8fff9;
    }
    
    .school-card .card-body {
        padding: 2rem;
    }
    
    .school-icon {
        font-size: 4rem;
        color: #007bff;
        margin-bottom: 1rem;
    }
    
    .school-card.selected .school-icon {
        color: #28a745;
    }
    
    .school-info {
        margin-bottom: 1.5rem;
    }
    
    .school-name {
        font-size: 1.25rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }
    
    .school-details {
        color: #6c757d;
        font-size: 0.9rem;
        line-height: 1.4;
    }
    
    .select-btn {
        transition: all 0.3s ease;
    }
    
    .select-btn:hover {
        transform: translateY(-2px);
    }
    
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 3rem;
    }
    
    .continue-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 2rem;
        margin-top: 2rem;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-md-8">
                <h1 class="display-4 mb-3">
                    <i class="fas fa-school me-3"></i>
                    {% trans "Select Your School" %}
                </h1>
                <p class="lead">
                    {% trans "Choose the school you want to work with. You can switch between schools at any time." %}
                </p>
            </div>
        </div>
    </div>
</div>

<div class="container">
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}

    {% if schools %}
        <form method="post" id="schoolSelectForm">
            {% csrf_token %}
            <input type="hidden" name="next" value="{{ next }}">
            
            <div class="row">
                {% for school in schools %}
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card school-card h-100 {% if selected_school and school.id == selected_school.id %}selected{% endif %}" 
                             data-school-id="{{ school.id }}">
                            <div class="card-body text-center">
                                <div class="school-icon">
                                    <i class="fas fa-university"></i>
                                </div>
                                
                                <div class="school-info">
                                    <h5 class="school-name">{{ school.name }}</h5>
                                    <div class="school-details">
                                        <div class="mb-1">
                                            <i class="fas fa-code me-2"></i>
                                            <strong>{% trans "Code:" %}</strong> {{ school.code }}
                                        </div>
                                        <div class="mb-1">
                                            <i class="fas fa-map-marker-alt me-2"></i>
                                            <strong>{% trans "Address:" %}</strong> {{ school.address|truncatechars:50 }}
                                        </div>
                                        {% if school.phone %}
                                            <div class="mb-1">
                                                <i class="fas fa-phone me-2"></i>
                                                {{ school.phone }}
                                            </div>
                                        {% endif %}
                                        {% if school.email %}
                                            <div>
                                                <i class="fas fa-envelope me-2"></i>
                                                {{ school.email }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <button type="submit" name="school_id" value="{{ school.id }}" 
                                        class="btn btn-primary select-btn w-100">
                                    {% if selected_school and school.id == selected_school.id %}
                                        <i class="fas fa-check me-2"></i>
                                        {% trans "Currently Selected" %}
                                    {% else %}
                                        <i class="fas fa-arrow-right me-2"></i>
                                        {% trans "Select School" %}
                                    {% endif %}
                                </button>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </form>
        
        {% if selected_school %}
            <div class="continue-section">
                <h4 class="mb-3">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    {% trans "Current School:" %} {{ selected_school.name }}
                </h4>
                <p class="text-muted mb-3">
                    {% trans "You can continue with your current school or select a different one above." %}
                </p>
                <a href="{{ next }}" class="btn btn-success btn-lg">
                    <i class="fas fa-arrow-right me-2"></i>
                    {% trans "Continue to Dashboard" %}
                </a>
            </div>
        {% endif %}
        
    {% else %}
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-exclamation-triangle text-warning" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                        <h4 class="mb-3">{% trans "No Schools Available" %}</h4>
                        <p class="text-muted mb-4">
                            {% trans "You don't have access to any schools. Please contact your administrator for assistance." %}
                        </p>
                        <a href="{% url 'accounts:logout' %}" class="btn btn-secondary">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            {% trans "Logout" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add click handlers for school cards
    const schoolCards = document.querySelectorAll('.school-card');
    
    schoolCards.forEach(card => {
        card.addEventListener('click', function(e) {
            // Don't trigger if clicking the button directly
            if (e.target.tagName === 'BUTTON' || e.target.closest('button')) {
                return;
            }
            
            // Find and click the button in this card
            const button = card.querySelector('button[type="submit"]');
            if (button) {
                button.click();
            }
        });
    });
    
    // Add loading state to form submission
    const form = document.getElementById('schoolSelectForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            const submitButton = e.submitter;
            if (submitButton) {
                
                // Add a hidden input with the school_id to ensure it's submitted
                const hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.name = 'school_id';
                hiddenInput.value = submitButton.value;
                form.appendChild(hiddenInput);
                
                // Update button text but don't disable it (disabled buttons don't submit their values)
                const originalText = submitButton.innerHTML;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>{% trans "Selecting..." %}';
                
                // Prevent double-clicks by adding a flag
                if (submitButton.dataset.submitting === 'true') {
                    e.preventDefault();
                    return false;
                }
                submitButton.dataset.submitting = 'true';
            }
            // Don't prevent default - let the form submit normally
        });
    }
});
</script>
{% endblock %}