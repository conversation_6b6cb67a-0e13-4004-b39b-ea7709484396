{% load i18n %}

<!-- School Switcher Component -->
{% if current_school and available_schools.count > 1 %}
<div class="dropdown">
    <button class="btn btn-outline-light dropdown-toggle d-flex align-items-center" 
            type="button" 
            id="schoolSwitcher" 
            data-bs-toggle="dropdown" 
            aria-expanded="false"
            title="{% trans 'Switch School' %}">
        <i class="fas fa-school me-2"></i>
        <span id="currentSchoolName" class="d-none d-md-inline">
            {{ current_school.name|truncatechars:20 }}
        </span>
        <span class="d-md-none">
            {{ current_school.code }}
        </span>
    </button>
    
    <ul class="dropdown-menu dropdown-menu-end shadow" style="min-width: 320px; max-width: 400px;">
        <li>
            <h6 class="dropdown-header d-flex align-items-center">
                <i class="fas fa-school me-2 text-primary"></i>
                {% trans "Available Schools" %}
            </h6>
        </li>
        <li><hr class="dropdown-divider"></li>
        
        <!-- Current School Info -->
        <li class="px-3 py-2 bg-light">
            <div class="d-flex align-items-center">
                <i class="fas fa-check-circle text-success me-2"></i>
                <div class="flex-grow-1">
                    <div class="fw-bold text-success small">{% trans "Current School" %}</div>
                    <div class="text-dark">{{ current_school.name }}</div>
                    <small class="text-muted">{{ current_school.code }}</small>
                </div>
            </div>
        </li>
        <li><hr class="dropdown-divider"></li>
        
        <!-- Available Schools -->
        {% for school in available_schools %}
            {% if school.id != current_school.id %}
            <li>
                <a class="dropdown-item school-option py-2" 
                   href="#" 
                   data-school-id="{{ school.id }}"
                   data-school-name="{{ school.name }}"
                   data-school-code="{{ school.code }}">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-university text-primary me-3"></i>
                        <div class="flex-grow-1">
                            <div class="fw-bold">{{ school.name }}</div>
                            <small class="text-muted">{{ school.code }}</small>
                            {% if school.address %}
                                <div class="text-muted small">{{ school.address|truncatechars:40 }}</div>
                            {% endif %}
                        </div>
                        <i class="fas fa-arrow-right text-muted"></i>
                    </div>
                </a>
            </li>
            {% endif %}
        {% endfor %}
        
        <li><hr class="dropdown-divider"></li>
        <li>
            <a class="dropdown-item text-center py-2" href="{% url 'core:school_select' %}">
                <i class="fas fa-cog me-2"></i>
                {% trans "Manage Schools" %}
            </a>
        </li>
    </ul>
</div>

<!-- School Switcher JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle school switching
    const schoolOptions = document.querySelectorAll('.school-option');
    const loadingOverlay = document.getElementById('loading-overlay');
    
    schoolOptions.forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            
            const schoolId = this.getAttribute('data-school-id');
            const schoolName = this.getAttribute('data-school-name');
            const schoolCode = this.getAttribute('data-school-code');
            
            if (!schoolId) return;
            
            // Show loading overlay
            if (loadingOverlay) {
                loadingOverlay.classList.remove('d-none');
            }
            
            // Disable the dropdown to prevent multiple clicks
            const dropdown = document.getElementById('schoolSwitcher');
            if (dropdown) {
                dropdown.disabled = true;
            }
            
            // Make AJAX request to switch school
            fetch('{% url "core:switch_school" %}', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `school_id=${schoolId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    showToast('success', data.message || `{% trans "Successfully switched to" %} ${schoolName}`);
                    
                    // Update the current school display
                    const currentSchoolName = document.getElementById('currentSchoolName');
                    if (currentSchoolName) {
                        currentSchoolName.textContent = schoolName.length > 20 ? 
                            schoolName.substring(0, 17) + '...' : schoolName;
                    }
                    
                    // Update mobile display
                    const mobileDisplay = dropdown.querySelector('.d-md-none');
                    if (mobileDisplay) {
                        mobileDisplay.textContent = schoolCode;
                    }
                    
                    // Reload page after a short delay to show new school data
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    // Show error message
                    showToast('error', data.message || '{% trans "Failed to switch school. Please try again." %}');
                    
                    // Re-enable dropdown
                    if (dropdown) {
                        dropdown.disabled = false;
                    }
                    
                    // Hide loading overlay
                    if (loadingOverlay) {
                        loadingOverlay.classList.add('d-none');
                    }
                }
            })
            .catch(error => {
                console.error('School switching error:', error);
                showToast('error', '{% trans "An error occurred while switching schools. Please try again." %}');
                
                // Re-enable dropdown
                if (dropdown) {
                    dropdown.disabled = false;
                }
                
                // Hide loading overlay
                if (loadingOverlay) {
                    loadingOverlay.classList.add('d-none');
                }
            });
        });
    });
    
    // Add hover effects
    schoolOptions.forEach(option => {
        option.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
        });
        
        option.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
});

// Utility function to get CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Toast notification function
function showToast(type, message) {
    // Create toast element
    const toastId = 'toast-' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0" 
             role="alert" aria-live="assertive" aria-atomic="true" 
             style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" 
                        data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;
    
    // Add toast to page
    document.body.insertAdjacentHTML('beforeend', toastHtml);
    
    // Initialize and show toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: 4000
    });
    
    toast.show();
    
    // Remove toast element after it's hidden
    toastElement.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}
</script>

<!-- Additional CSS for better styling -->
<style>
.school-option {
    transition: all 0.2s ease;
    border-radius: 0.375rem;
    margin: 0.125rem 0.5rem;
}

.school-option:hover {
    background-color: #f8f9fa !important;
    transform: translateX(2px);
}

.school-option:active {
    transform: translateX(1px);
}

#schoolSwitcher {
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.2s ease;
}

#schoolSwitcher:hover {
    border-color: rgba(255, 255, 255, 0.5);
    background-color: rgba(255, 255, 255, 0.1);
}

#schoolSwitcher:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.dropdown-menu {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

@media (max-width: 768px) {
    .dropdown-menu {
        min-width: 280px !important;
        max-width: 90vw !important;
    }
}
</style>
{% endif %}