{% extends 'base.html' %}
{% load i18n %}
{% load finance_filters %}

{% block title %}{{ account.full_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-file-invoice-dollar"></i> {{ account.full_name }}
                    </h3>
                    <div class="card-tools">
                        <a href="{% url 'finance:edit_account' account.pk %}" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> {% trans "Edit" %}
                        </a>
                        <a href="{% url 'finance:account_ledger' account.pk %}" class="btn btn-info btn-sm">
                            <i class="fas fa-book"></i> {% trans "Ledger" %}
                        </a>
                        <a href="{% url 'finance:accounts_tree' %}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> {% trans "Back" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="30%">{% trans "Account Code" %}:</th>
                                    <td>{{ account.code }}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "Account Name" %}:</th>
                                    <td>{{ account.name }}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "Account Type" %}:</th>
                                    <td>
                                        <span class="badge badge-primary">{{ account.account_type.get_type_display }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>{% trans "Parent Account" %}:</th>
                                    <td>
                                        {% if account.parent %}
                                            <a href="{% url 'finance:account_detail' account.parent.pk %}">
                                                {{ account.parent.full_name }}
                                            </a>
                                        {% else %}
                                            {% trans "Root Account" %}
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>{% trans "Level" %}:</th>
                                    <td>{{ account.level }}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "Is Header" %}:</th>
                                    <td>
                                        {% if account.is_header %}
                                            <span class="badge badge-info">{% trans "Yes" %}</span>
                                        {% else %}
                                            <span class="badge badge-secondary">{% trans "No" %}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="30%">{% trans "Opening Balance" %}:</th>
                                    <td>{{ account.opening_balance|floatformat:2 }}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "Current Balance" %}:</th>
                                    <td>
                                        <strong class="{% if account.current_balance >= 0 %}text-success{% else %}text-danger{% endif %}">
                                            {{ account.current_balance|floatformat:2 }}
                                        </strong>
                                    </td>
                                </tr>
                                <tr>
                                    <th>{% trans "Allow Manual Entries" %}:</th>
                                    <td>
                                        {% if account.allow_manual_entries %}
                                            <span class="badge badge-success">{% trans "Yes" %}</span>
                                        {% else %}
                                            <span class="badge badge-danger">{% trans "No" %}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>{% trans "Is Reconcilable" %}:</th>
                                    <td>
                                        {% if account.is_reconcilable %}
                                            <span class="badge badge-warning">{% trans "Yes" %}</span>
                                        {% else %}
                                            <span class="badge badge-secondary">{% trans "No" %}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>{% trans "Status" %}:</th>
                                    <td>
                                        {% if account.is_active %}
                                            <span class="badge badge-success">{% trans "Active" %}</span>
                                        {% else %}
                                            <span class="badge badge-danger">{% trans "Inactive" %}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>{% trans "Created" %}:</th>
                                    <td>{{ account.created_at|date:"Y-m-d H:i" }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    {% if account.description %}
                    <div class="row mt-3">
                        <div class="col-12">
                            <h5>{% trans "Description" %}</h5>
                            <p class="text-muted">{{ account.description }}</p>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Child Accounts -->
                    {% if account.children.all %}
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>{% trans "Child Accounts" %}</h5>
                            <div class="table-responsive">
                                <table class="table table-sm table-striped">
                                    <thead>
                                        <tr>
                                            <th>{% trans "Code" %}</th>
                                            <th>{% trans "Name" %}</th>
                                            <th>{% trans "Type" %}</th>
                                            <th>{% trans "Balance" %}</th>
                                            <th>{% trans "Actions" %}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for child in account.children.all %}
                                        <tr>
                                            <td>{{ child.code }}</td>
                                            <td>{{ child.name }}</td>
                                            <td>{{ child.account_type.get_type_display }}</td>
                                            <td class="{% if child.current_balance >= 0 %}text-success{% else %}text-danger{% endif %}">
                                                {{ child.current_balance|floatformat:2 }}
                                            </td>
                                            <td>
                                                <a href="{% url 'finance:account_detail' child.pk %}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Recent Transactions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>{% trans "Recent Transactions" %}</h5>
                            <div class="table-responsive">
                                <table class="table table-sm table-striped">
                                    <thead>
                                        <tr>
                                            <th>{% trans "Date" %}</th>
                                            <th>{% trans "Reference" %}</th>
                                            <th>{% trans "Description" %}</th>
                                            <th>{% trans "Debit" %}</th>
                                            <th>{% trans "Credit" %}</th>
                                            <th>{% trans "Balance" %}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for entry in recent_entries %}
                                        <tr>
                                            <td>{{ entry.entry_date|date:"Y-m-d" }}</td>
                                            <td>{{ entry.reference_number|default:"-" }}</td>
                                            <td>{{ entry.description|truncatechars:50 }}</td>
                                            <td class="text-right">
                                                {% if entry.debit_amount %}
                                                    {{ entry.debit_amount|floatformat:2 }}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td class="text-right">
                                                {% if entry.credit_amount %}
                                                    {{ entry.credit_amount|floatformat:2 }}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td class="text-right">{{ entry.running_balance|floatformat:2 }}</td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="6" class="text-center text-muted">
                                                {% trans "No recent transactions" %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% if recent_entries %}
                            <div class="text-center mt-3">
                                <a href="{% url 'finance:account_ledger' account.pk %}" class="btn btn-primary">
                                    {% trans "View Full Ledger" %}
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}