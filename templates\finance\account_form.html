{% extends 'base.html' %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}
    {% if object %}
        {% trans "Edit Account" %}
    {% else %}
        {% trans "Add Account" %}
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        {% if object %}
                            <i class="fas fa-edit"></i> {% trans "Edit Account" %}
                        {% else %}
                            <i class="fas fa-plus"></i> {% trans "Add Account" %}
                        {% endif %}
                    </h3>
                    <div class="card-tools">
                        <a href="{% url 'finance:accounts_tree' %}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> {% trans "Back to Accounts" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.code.id_for_label }}" class="form-label">
                                        {{ form.code.label }} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.code }}
                                    {% if form.code.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.code.errors.0 }}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        {% trans "Unique account code (e.g., 1000, 1100)" %}
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.name.id_for_label }}" class="form-label">
                                        {{ form.name.label }} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.name.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.account_type.id_for_label }}" class="form-label">
                                        {{ form.account_type.label }} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.account_type }}
                                    {% if form.account_type.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.account_type.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.parent.id_for_label }}" class="form-label">
                                        {{ form.parent.label }}
                                    </label>
                                    {{ form.parent }}
                                    {% if form.parent.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.parent.errors.0 }}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        {% trans "Select parent account for hierarchy" %}
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                {{ form.description.label }}
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.description.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.opening_balance.id_for_label }}" class="form-label">
                                        {{ form.opening_balance.label }}
                                    </label>
                                    {{ form.opening_balance }}
                                    {% if form.opening_balance.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.opening_balance.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="custom-control custom-checkbox mt-4">
                                        {{ form.is_header }}
                                        <label class="custom-control-label" for="{{ form.is_header.id_for_label }}">
                                            {{ form.is_header.label }}
                                        </label>
                                    </div>
                                    {% if form.is_header.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.is_header.errors.0 }}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        {% trans "Header accounts cannot have transactions posted to them" %}
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="custom-control custom-checkbox">
                                        {{ form.allow_manual_entries }}
                                        <label class="custom-control-label" for="{{ form.allow_manual_entries.id_for_label }}">
                                            {{ form.allow_manual_entries.label }}
                                        </label>
                                    </div>
                                    {% if form.allow_manual_entries.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.allow_manual_entries.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="custom-control custom-checkbox">
                                        {{ form.is_reconcilable }}
                                        <label class="custom-control-label" for="{{ form.is_reconcilable.id_for_label }}">
                                            {{ form.is_reconcilable.label }}
                                        </label>
                                    </div>
                                    {% if form.is_reconcilable.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.is_reconcilable.errors.0 }}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        {% trans "Account requires reconciliation (e.g., bank accounts)" %}
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                {% if object %}
                                    {% trans "Update Account" %}
                                {% else %}
                                    {% trans "Create Account" %}
                                {% endif %}
                            </button>
                            <a href="{% url 'finance:accounts_tree' %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> {% trans "Cancel" %}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add form validation
    const form = document.querySelector('.needs-validation');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // Handle header account checkbox
    const isHeaderCheckbox = document.getElementById('{{ form.is_header.id_for_label }}');
    const allowManualEntriesCheckbox = document.getElementById('{{ form.allow_manual_entries.id_for_label }}');
    
    if (isHeaderCheckbox && allowManualEntriesCheckbox) {
        isHeaderCheckbox.addEventListener('change', function() {
            if (this.checked) {
                allowManualEntriesCheckbox.checked = false;
                allowManualEntriesCheckbox.disabled = true;
            } else {
                allowManualEntriesCheckbox.disabled = false;
            }
        });
    }
});
</script>
{% endblock %}