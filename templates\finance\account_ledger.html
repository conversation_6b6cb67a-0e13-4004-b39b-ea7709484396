{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Account Ledger" %} - {{ account.code }} {{ account.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-book-open"></i>
                        {% trans "Account Ledger" %} - {{ account.code }} {{ account.name }}
                    </h3>
                    <div>
                        <a href="{% url 'finance:account_detail' account.pk %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            {% trans "Back to Account" %}
                        </a>
                        <button class="btn btn-primary" onclick="window.print()">
                            <i class="fas fa-print"></i>
                            {% trans "Print Ledger" %}
                        </button>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Account Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">{% trans "Account Information" %}</h6>
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <th width="40%">{% trans "Account Code" %}:</th>
                                            <td>{{ account.code }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Account Name" %}:</th>
                                            <td>{{ account.name }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Account Type" %}:</th>
                                            <td>{{ account.account_type.name }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Balance Type" %}:</th>
                                            <td>
                                                <span class="badge badge-{{ account.balance_type|yesno:'primary,success' }}">
                                                    {{ account.balance_type|title }}
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">{% trans "Period Information" %}</h6>
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <th width="40%">{% trans "Period From" %}:</th>
                                            <td>{{ ledger_data.period.start_date|date:"Y-m-d" }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Period To" %}:</th>
                                            <td>{{ ledger_data.period.end_date|date:"Y-m-d" }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Opening Balance" %}:</th>
                                            <td class="font-weight-bold">
                                                {{ ledger_data.opening_balance|floatformat:2 }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Closing Balance" %}:</th>
                                            <td class="font-weight-bold">
                                                {{ ledger_data.closing_balance|floatformat:2 }}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Period Filter -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-filter"></i>
                                        {% trans "Filter Period" %}
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <form method="get" class="form-inline">
                                        <div class="form-group mr-3">
                                            <label for="start_date" class="mr-2">{% trans "From" %}:</label>
                                            <input type="date" class="form-control" id="start_date" name="start_date" 
                                                   value="{{ request.GET.start_date|default:'' }}">
                                        </div>
                                        <div class="form-group mr-3">
                                            <label for="end_date" class="mr-2">{% trans "To" %}:</label>
                                            <input type="date" class="form-control" id="end_date" name="end_date" 
                                                   value="{{ request.GET.end_date|default:'' }}">
                                        </div>
                                        <button type="submit" class="btn btn-primary mr-2">
                                            <i class="fas fa-search"></i>
                                            {% trans "Filter" %}
                                        </button>
                                        <a href="{% url 'finance:account_ledger' account.pk %}" class="btn btn-secondary">
                                            <i class="fas fa-times"></i>
                                            {% trans "Clear" %}
                                        </a>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Ledger Entries -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-list"></i>
                                        {% trans "Ledger Entries" %}
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead class="thead-dark">
                                                <tr>
                                                    <th>{% trans "Date" %}</th>
                                                    <th>{% trans "Transaction ID" %}</th>
                                                    <th>{% trans "Description" %}</th>
                                                    <th>{% trans "Reference" %}</th>
                                                    <th class="text-right">{% trans "Debit" %}</th>
                                                    <th class="text-right">{% trans "Credit" %}</th>
                                                    <th class="text-right">{% trans "Balance" %}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <!-- Opening Balance Row -->
                                                <tr class="table-info">
                                                    <td>{{ ledger_data.period.start_date|date:"Y-m-d" }}</td>
                                                    <td>-</td>
                                                    <td><strong>{% trans "Opening Balance" %}</strong></td>
                                                    <td>-</td>
                                                    <td class="text-right">-</td>
                                                    <td class="text-right">-</td>
                                                    <td class="text-right font-weight-bold">
                                                        {{ ledger_data.opening_balance|floatformat:2 }}
                                                    </td>
                                                </tr>
                                                
                                                <!-- Transaction Entries -->
                                                {% for entry in ledger_data.entries %}
                                                <tr>
                                                    <td>{{ entry.date|date:"Y-m-d" }}</td>
                                                    <td>
                                                        <a href="{% url 'finance:transaction_detail' entry.transaction.pk %}">
                                                            {{ entry.transaction_id }}
                                                        </a>
                                                    </td>
                                                    <td>{{ entry.description }}</td>
                                                    <td>{{ entry.reference|default:"-" }}</td>
                                                    <td class="text-right">
                                                        {% if entry.debit_amount > 0 %}
                                                            {{ entry.debit_amount|floatformat:2 }}
                                                        {% else %}
                                                            -
                                                        {% endif %}
                                                    </td>
                                                    <td class="text-right">
                                                        {% if entry.credit_amount > 0 %}
                                                            {{ entry.credit_amount|floatformat:2 }}
                                                        {% else %}
                                                            -
                                                        {% endif %}
                                                    </td>
                                                    <td class="text-right font-weight-bold">
                                                        {{ entry.balance|floatformat:2 }}
                                                    </td>
                                                </tr>
                                                {% empty %}
                                                <tr>
                                                    <td colspan="7" class="text-center">
                                                        <div class="alert alert-info mb-0">
                                                            <i class="fas fa-info-circle"></i>
                                                            {% trans "No transactions found for the selected period." %}
                                                        </div>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                                
                                                <!-- Closing Balance Row -->
                                                {% if ledger_data.entries %}
                                                <tr class="table-success">
                                                    <td>{{ ledger_data.period.end_date|date:"Y-m-d" }}</td>
                                                    <td>-</td>
                                                    <td><strong>{% trans "Closing Balance" %}</strong></td>
                                                    <td>-</td>
                                                    <td class="text-right">-</td>
                                                    <td class="text-right">-</td>
                                                    <td class="text-right font-weight-bold">
                                                        {{ ledger_data.closing_balance|floatformat:2 }}
                                                    </td>
                                                </tr>
                                                {% endif %}
                                            </tbody>
                                        </table>
                                    </div>
                                    
                                    <!-- Summary -->
                                    {% if ledger_data.entries %}
                                    <div class="row mt-4">
                                        <div class="col-md-4">
                                            <div class="card bg-primary text-white">
                                                <div class="card-body text-center">
                                                    <h6 class="card-title">{% trans "Total Debits" %}</h6>
                                                    <h4>{{ ledger_data.total_debits|floatformat:2 }}</h4>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="card bg-success text-white">
                                                <div class="card-body text-center">
                                                    <h6 class="card-title">{% trans "Total Credits" %}</h6>
                                                    <h4>{{ ledger_data.total_credits|floatformat:2 }}</h4>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="card bg-info text-white">
                                                <div class="card-body text-center">
                                                    <h6 class="card-title">{% trans "Net Movement" %}</h6>
                                                    <h4>{{ ledger_data.net_movement|floatformat:2 }}</h4>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .card-header .btn,
    .card-body form,
    .no-print {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .table {
        font-size: 12px;
    }
    
    .table th,
    .table td {
        padding: 0.3rem !important;
    }
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.075);
}

.font-weight-bold {
    font-weight: 600 !important;
}

.badge {
    font-size: 0.8em;
}
</style>
{% endblock %}