{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Account Reports" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-bar"></i> {% trans "Account Reports" %}
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Account Statement -->
                        <div class="col-md-4 mb-3">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <i class="fas fa-file-alt fa-3x text-primary mb-3"></i>
                                    <h5 class="card-title">{% trans "Account Statement" %}</h5>
                                    <p class="card-text">{% trans "View detailed account transactions" %}</p>
                                    <a href="{% url 'finance:account_statement' %}" class="btn btn-primary">
                                        {% trans "Generate Report" %}
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Trial Balance -->
                        <div class="col-md-4 mb-3">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <i class="fas fa-balance-scale fa-3x text-success mb-3"></i>
                                    <h5 class="card-title">{% trans "Trial Balance" %}</h5>
                                    <p class="card-text">{% trans "View trial balance report" %}</p>
                                    <a href="{% url 'finance:trial_balance' %}" class="btn btn-success">
                                        {% trans "Generate Report" %}
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Account Ledger -->
                        <div class="col-md-4 mb-3">
                            <div class="card border-info">
                                <div class="card-body text-center">
                                    <i class="fas fa-book fa-3x text-info mb-3"></i>
                                    <h5 class="card-title">{% trans "Account Ledger" %}</h5>
                                    <p class="card-text">{% trans "View account ledger details" %}</p>
                                    <button class="btn btn-info" onclick="selectAccountForLedger()">
                                        {% trans "Select Account" %}
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Monthly Statement -->
                        <div class="col-md-4 mb-3">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <i class="fas fa-calendar-alt fa-3x text-warning mb-3"></i>
                                    <h5 class="card-title">{% trans "Monthly Statement" %}</h5>
                                    <p class="card-text">{% trans "View monthly account statements" %}</p>
                                    <a href="{% url 'finance:monthly_statement' %}" class="btn btn-warning">
                                        {% trans "Generate Report" %}
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Opening Balances -->
                        <div class="col-md-4 mb-3">
                            <div class="card border-secondary">
                                <div class="card-body text-center">
                                    <i class="fas fa-calculator fa-3x text-secondary mb-3"></i>
                                    <h5 class="card-title">{% trans "Opening Balances" %}</h5>
                                    <p class="card-text">{% trans "View and manage opening balances" %}</p>
                                    <a href="{% url 'finance:opening_balances' %}" class="btn btn-secondary">
                                        {% trans "Manage Balances" %}
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Cash & Bank Statement -->
                        <div class="col-md-4 mb-3">
                            <div class="card border-dark">
                                <div class="card-body text-center">
                                    <i class="fas fa-university fa-3x text-dark mb-3"></i>
                                    <h5 class="card-title">{% trans "Cash & Bank Statement" %}</h5>
                                    <p class="card-text">{% trans "View cash and bank account statements" %}</p>
                                    <a href="{% url 'finance:cash_bank_statement' %}" class="btn btn-dark">
                                        {% trans "Generate Report" %}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Account Search -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">{% trans "Quick Account Search" %}</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>{% trans "Search Account" %}</label>
                                                <input type="text" class="form-control" id="account-search" 
                                                       placeholder="{% trans 'Enter account code or name...' %}">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>{% trans "Account Type" %}</label>
                                                <select class="form-control" id="account-type-filter">
                                                    <option value="">{% trans "All Types" %}</option>
                                                    <option value="asset">{% trans "Asset" %}</option>
                                                    <option value="liability">{% trans "Liability" %}</option>
                                                    <option value="equity">{% trans "Equity" %}</option>
                                                    <option value="revenue">{% trans "Revenue" %}</option>
                                                    <option value="expense">{% trans "Expense" %}</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="account-search-results" class="mt-3"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Account Selection Modal -->
<div class="modal fade" id="accountSelectionModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Select Account for Ledger" %}</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>{% trans "Code" %}</th>
                                <th>{% trans "Name" %}</th>
                                <th>{% trans "Type" %}</th>
                                <th>{% trans "Balance" %}</th>
                                <th>{% trans "Action" %}</th>
                            </tr>
                        </thead>
                        <tbody id="accounts-table-body">
                            {% for account in accounts %}
                            <tr>
                                <td>{{ account.code }}</td>
                                <td>{{ account.name }}</td>
                                <td>{{ account.account_type.get_type_display }}</td>
                                <td class="text-right">{{ account.current_balance|floatformat:2 }}</td>
                                <td>
                                    <button class="btn btn-sm btn-primary" onclick="viewAccountLedger('{{ account.id }}')">
                                        {% trans "View Ledger" %}
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function selectAccountForLedger() {
    $('#accountSelectionModal').modal('show');
}

function viewAccountLedger(accountId) {
    window.location.href = `/finance/accounts/${accountId}/ledger/`;
}

// Account search functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('account-search');
    const typeFilter = document.getElementById('account-type-filter');
    const resultsContainer = document.getElementById('account-search-results');

    function searchAccounts() {
        const query = searchInput.value;
        const type = typeFilter.value;
        
        if (query.length < 2 && !type) {
            resultsContainer.innerHTML = '';
            return;
        }

        // Show loading
        resultsContainer.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> {% trans "Searching..." %}</div>';

        // Simulate API call
        setTimeout(() => {
            resultsContainer.innerHTML = `
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>{% trans "Code" %}</th>
                                <th>{% trans "Name" %}</th>
                                <th>{% trans "Type" %}</th>
                                <th>{% trans "Balance" %}</th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="5" class="text-center text-muted">
                                    {% trans "Search functionality will be implemented here" %}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            `;
        }, 500);
    }

    searchInput.addEventListener('input', debounce(searchAccounts, 300));
    typeFilter.addEventListener('change', searchAccounts);
});

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
{% endblock %}