{% extends "base.html" %}
{% load i18n %}
{% load localization_tags %}

{% block title %}{% trans "Invoice" %} - {{ invoice.number }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% url 'django.views.static.serve' path='css/rtl.css' %}">
<style>
    .invoice-header {
        border-bottom: 2px solid #007bff;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
    }
    
    .invoice-details {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 0.5rem;
        margin-bottom: 2rem;
    }
    
    .invoice-table {
        border: 1px solid #dee2e6;
    }
    
    .invoice-table th {
        background-color: #007bff;
        color: white;
        font-weight: bold;
    }
    
    .invoice-total {
        background-color: #28a745;
        color: white;
        font-weight: bold;
        font-size: 1.2rem;
    }
    
    .arabic-amount {
        font-family: 'Noto Sans Arabic', 'Tahoma', sans-serif;
        font-size: 1.1rem;
        font-weight: bold;
    }
    
    @media print {
        .no-print {
            display: none !important;
        }
        
        .invoice-container {
            max-width: none;
            margin: 0;
            padding: 0;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid invoice-container {% if LANGUAGE_CODE == 'ar' %}rtl-layout arabic-text{% endif %}">
    <!-- Invoice Header -->
    <div class="invoice-header">
        <div class="row">
            <div class="col-md-6">
                <h1 class="h2 mb-3">
                    {% if LANGUAGE_CODE == 'ar' %}
                        فاتورة رقم: <span class="arabic-number">{{ invoice.number|arabic_digits }}</span>
                    {% else %}
                        {% trans "Invoice" %} #{{ invoice.number }}
                    {% endif %}
                </h1>
                <p class="text-muted">
                    {% if LANGUAGE_CODE == 'ar' %}
                        تاريخ الإصدار: {{ invoice.date|format_date_localized }}
                    {% else %}
                        {% trans "Issue Date" %}: {{ invoice.date|format_date_localized }}
                    {% endif %}
                </p>
                {% if invoice.due_date %}
                <p class="text-muted">
                    {% if LANGUAGE_CODE == 'ar' %}
                        تاريخ الاستحقاق: {{ invoice.due_date|format_date_localized }}
                    {% else %}
                        {% trans "Due Date" %}: {{ invoice.due_date|format_date_localized }}
                    {% endif %}
                </p>
                {% endif %}
            </div>
            <div class="col-md-6 text-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %}">
                <div class="school-info">
                    <h3>
                        {% if LANGUAGE_CODE == 'ar' %}
                            {{ SCHOOL_NAME_AR|default:SCHOOL_NAME }}
                        {% else %}
                            {{ SCHOOL_NAME }}
                        {% endif %}
                    </h3>
                    <p class="mb-1">
                        {% if LANGUAGE_CODE == 'ar' %}
                            العنوان: {{ school.address_ar|default:school.address }}
                        {% else %}
                            {{ school.address }}
                        {% endif %}
                    </p>
                    <p class="mb-1">
                        {% trans "Phone" %}: {{ school.phone }}
                    </p>
                    <p class="mb-0">
                        {% trans "Email" %}: {{ school.email }}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Student/Parent Information -->
    <div class="invoice-details">
        <div class="row">
            <div class="col-md-6">
                <h4>
                    {% if LANGUAGE_CODE == 'ar' %}
                        معلومات الطالب
                    {% else %}
                        {% trans "Student Information" %}
                    {% endif %}
                </h4>
                <p class="mb-1">
                    <strong>
                        {% if LANGUAGE_CODE == 'ar' %}
                            الاسم: {{ invoice.student.name_ar|default:invoice.student.full_name }}
                        {% else %}
                            {% trans "Name" %}: {{ invoice.student.full_name }}
                        {% endif %}
                    </strong>
                </p>
                <p class="mb-1">
                    {% if LANGUAGE_CODE == 'ar' %}
                        رقم الطالب: <span class="arabic-number">{{ invoice.student.student_id|arabic_digits }}</span>
                    {% else %}
                        {% trans "Student ID" %}: {{ invoice.student.student_id }}
                    {% endif %}
                </p>
                <p class="mb-1">
                    {% if LANGUAGE_CODE == 'ar' %}
                        الصف: {{ invoice.student.current_class.name_ar|default:invoice.student.current_class.name }}
                    {% else %}
                        {% trans "Class" %}: {{ invoice.student.current_class.name }}
                    {% endif %}
                </p>
            </div>
            <div class="col-md-6">
                <h4>
                    {% if LANGUAGE_CODE == 'ar' %}
                        معلومات ولي الأمر
                    {% else %}
                        {% trans "Parent Information" %}
                    {% endif %}
                </h4>
                {% if invoice.student.parent %}
                <p class="mb-1">
                    <strong>
                        {% if LANGUAGE_CODE == 'ar' %}
                            الاسم: {{ invoice.student.parent.name_ar|default:invoice.student.parent.full_name }}
                        {% else %}
                            {% trans "Name" %}: {{ invoice.student.parent.full_name }}
                        {% endif %}
                    </strong>
                </p>
                <p class="mb-1">
                    {% trans "Phone" %}: {{ invoice.student.parent.phone }}
                </p>
                <p class="mb-1">
                    {% trans "Email" %}: {{ invoice.student.parent.email }}
                </p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Invoice Items -->
    <div class="table-responsive">
        <table class="table table-bordered invoice-table">
            <thead>
                <tr>
                    <th>
                        {% if LANGUAGE_CODE == 'ar' %}
                            البند
                        {% else %}
                            {% trans "Item" %}
                        {% endif %}
                    </th>
                    <th class="text-center">
                        {% if LANGUAGE_CODE == 'ar' %}
                            الكمية
                        {% else %}
                            {% trans "Quantity" %}
                        {% endif %}
                    </th>
                    <th class="text-center">
                        {% if LANGUAGE_CODE == 'ar' %}
                            السعر الوحدة
                        {% else %}
                            {% trans "Unit Price" %}
                        {% endif %}
                    </th>
                    <th class="text-center">
                        {% if LANGUAGE_CODE == 'ar' %}
                            المجموع
                        {% else %}
                            {% trans "Total" %}
                        {% endif %}
                    </th>
                </tr>
            </thead>
            <tbody>
                {% for item in invoice.items.all %}
                <tr>
                    <td>
                        {% if LANGUAGE_CODE == 'ar' %}
                            {{ item.description_ar|default:item.description }}
                        {% else %}
                            {{ item.description }}
                        {% endif %}
                    </td>
                    <td class="text-center">
                        <span class="arabic-number">{{ item.quantity|format_number }}</span>
                    </td>
                    <td class="text-center">
                        <span class="arabic-amount">{{ item.unit_price|format_number }} 
                            {% if LANGUAGE_CODE == 'ar' %}ريال{% else %}SAR{% endif %}
                        </span>
                    </td>
                    <td class="text-center">
                        <span class="arabic-amount">{{ item.total|format_number }} 
                            {% if LANGUAGE_CODE == 'ar' %}ريال{% else %}SAR{% endif %}
                        </span>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
            <tfoot>
                {% if invoice.subtotal != invoice.total %}
                <tr>
                    <td colspan="3" class="text-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %}">
                        <strong>
                            {% if LANGUAGE_CODE == 'ar' %}
                                المجموع الفرعي:
                            {% else %}
                                {% trans "Subtotal" %}:
                            {% endif %}
                        </strong>
                    </td>
                    <td class="text-center">
                        <strong class="arabic-amount">{{ invoice.subtotal|format_number }} 
                            {% if LANGUAGE_CODE == 'ar' %}ريال{% else %}SAR{% endif %}
                        </strong>
                    </td>
                </tr>
                {% endif %}
                
                {% if invoice.tax_amount %}
                <tr>
                    <td colspan="3" class="text-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %}">
                        <strong>
                            {% if LANGUAGE_CODE == 'ar' %}
                                الضريبة ({{ invoice.tax_rate|format_number }}%):
                            {% else %}
                                {% trans "Tax" %} ({{ invoice.tax_rate }}%):
                            {% endif %}
                        </strong>
                    </td>
                    <td class="text-center">
                        <strong class="arabic-amount">{{ invoice.tax_amount|format_number }} 
                            {% if LANGUAGE_CODE == 'ar' %}ريال{% else %}SAR{% endif %}
                        </strong>
                    </td>
                </tr>
                {% endif %}
                
                {% if invoice.discount_amount %}
                <tr>
                    <td colspan="3" class="text-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %}">
                        <strong>
                            {% if LANGUAGE_CODE == 'ar' %}
                                الخصم:
                            {% else %}
                                {% trans "Discount" %}:
                            {% endif %}
                        </strong>
                    </td>
                    <td class="text-center">
                        <strong class="arabic-amount">-{{ invoice.discount_amount|format_number }} 
                            {% if LANGUAGE_CODE == 'ar' %}ريال{% else %}SAR{% endif %}
                        </strong>
                    </td>
                </tr>
                {% endif %}
                
                <tr class="invoice-total">
                    <td colspan="3" class="text-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %}">
                        <strong>
                            {% if LANGUAGE_CODE == 'ar' %}
                                المجموع الإجمالي:
                            {% else %}
                                {% trans "Grand Total" %}:
                            {% endif %}
                        </strong>
                    </td>
                    <td class="text-center">
                        <strong class="arabic-amount">{{ invoice.total|format_number }} 
                            {% if LANGUAGE_CODE == 'ar' %}ريال{% else %}SAR{% endif %}
                        </strong>
                    </td>
                </tr>
            </tfoot>
        </table>
    </div>

    <!-- Payment Information -->
    {% if invoice.payment_method or invoice.payment_date %}
    <div class="row mt-4">
        <div class="col-md-6">
            <h5>
                {% if LANGUAGE_CODE == 'ar' %}
                    معلومات الدفع
                {% else %}
                    {% trans "Payment Information" %}
                {% endif %}
            </h5>
            {% if invoice.payment_method %}
            <p class="mb-1">
                {% if LANGUAGE_CODE == 'ar' %}
                    طريقة الدفع: {{ invoice.get_payment_method_display }}
                {% else %}
                    {% trans "Payment Method" %}: {{ invoice.get_payment_method_display }}
                {% endif %}
            </p>
            {% endif %}
            {% if invoice.payment_date %}
            <p class="mb-1">
                {% if LANGUAGE_CODE == 'ar' %}
                    تاريخ الدفع: {{ invoice.payment_date|format_date_localized }}
                {% else %}
                    {% trans "Payment Date" %}: {{ invoice.payment_date|format_date_localized }}
                {% endif %}
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Notes -->
    {% if invoice.notes %}
    <div class="row mt-4">
        <div class="col-12">
            <h5>
                {% if LANGUAGE_CODE == 'ar' %}
                    ملاحظات
                {% else %}
                    {% trans "Notes" %}
                {% endif %}
            </h5>
            <p>
                {% if LANGUAGE_CODE == 'ar' %}
                    {{ invoice.notes_ar|default:invoice.notes }}
                {% else %}
                    {{ invoice.notes }}
                {% endif %}
            </p>
        </div>
    </div>
    {% endif %}

    <!-- Footer -->
    <div class="row mt-5 pt-4 border-top">
        <div class="col-12 text-center">
            <p class="text-muted">
                {% if LANGUAGE_CODE == 'ar' %}
                    شكراً لكم لاختياركم مدرستنا
                {% else %}
                    {% trans "Thank you for choosing our school" %}
                {% endif %}
            </p>
            <p class="small text-muted">
                {% if LANGUAGE_CODE == 'ar' %}
                    تم إنشاء هذه الفاتورة إلكترونياً في {{ invoice.created_at|format_datetime_localized }}
                {% else %}
                    {% trans "This invoice was generated electronically on" %} {{ invoice.created_at|format_datetime_localized }}
                {% endif %}
            </p>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mt-4 no-print">
        <div class="col-12 text-center">
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fas fa-print"></i>
                {% if LANGUAGE_CODE == 'ar' %}
                    طباعة
                {% else %}
                    {% trans "Print" %}
                {% endif %}
            </button>
            <a href="{% url 'finance:invoice_pdf' invoice.id %}" class="btn btn-secondary">
                <i class="fas fa-download"></i>
                {% if LANGUAGE_CODE == 'ar' %}
                    تحميل PDF
                {% else %}
                    {% trans "Download PDF" %}
                {% endif %}
            </a>
            <a href="{% url 'finance:invoices' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i>
                {% if LANGUAGE_CODE == 'ar' %}
                    رجوع للفواتير
                {% else %}
                    {% trans "Back to Invoices" %}
                {% endif %}
            </a>
        </div>
    </div>
</div>
{% endblock %}