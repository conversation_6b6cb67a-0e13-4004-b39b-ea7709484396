{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Transaction Audit Logs" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-history"></i>
                        {% trans "Transaction Audit Logs" %}
                    </h3>
                    <div>
                        <a href="{% url 'finance:transactions_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            {% trans "Back to Transactions" %}
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Filter Form -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-filter"></i>
                                        {% trans "Filter Logs" %}
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <form method="get" class="form-inline">
                                        <div class="form-group mr-3">
                                            <label for="transaction_id" class="mr-2">{% trans "Transaction ID" %}:</label>
                                            <input type="text" class="form-control" id="transaction_id" name="transaction_id" 
                                                   value="{{ request.GET.transaction_id|default:'' }}" placeholder="{% trans 'Enter transaction ID' %}">
                                        </div>
                                        <div class="form-group mr-3">
                                            <label for="action" class="mr-2">{% trans "Action" %}:</label>
                                            <select class="form-control" id="action" name="action">
                                                <option value="">{% trans "All Actions" %}</option>
                                                <option value="created" {% if request.GET.action == 'created' %}selected{% endif %}>{% trans "Created" %}</option>
                                                <option value="modified" {% if request.GET.action == 'modified' %}selected{% endif %}>{% trans "Modified" %}</option>
                                                <option value="approved" {% if request.GET.action == 'approved' %}selected{% endif %}>{% trans "Approved" %}</option>
                                                <option value="posted" {% if request.GET.action == 'posted' %}selected{% endif %}>{% trans "Posted" %}</option>
                                                <option value="cancelled" {% if request.GET.action == 'cancelled' %}selected{% endif %}>{% trans "Cancelled" %}</option>
                                            </select>
                                        </div>
                                        <div class="form-group mr-3">
                                            <label for="user" class="mr-2">{% trans "User" %}:</label>
                                            <input type="text" class="form-control" id="user" name="user" 
                                                   value="{{ request.GET.user|default:'' }}" placeholder="{% trans 'Enter username' %}">
                                        </div>
                                        <div class="form-group mr-3">
                                            <label for="date_from" class="mr-2">{% trans "From" %}:</label>
                                            <input type="date" class="form-control" id="date_from" name="date_from" 
                                                   value="{{ request.GET.date_from|default:'' }}">
                                        </div>
                                        <div class="form-group mr-3">
                                            <label for="date_to" class="mr-2">{% trans "To" %}:</label>
                                            <input type="date" class="form-control" id="date_to" name="date_to" 
                                                   value="{{ request.GET.date_to|default:'' }}">
                                        </div>
                                        <button type="submit" class="btn btn-primary mr-2">
                                            <i class="fas fa-search"></i>
                                            {% trans "Filter" %}
                                        </button>
                                        <a href="{% url 'finance:audit_logs' %}" class="btn btn-secondary">
                                            <i class="fas fa-times"></i>
                                            {% trans "Clear" %}
                                        </a>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Summary Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-2">
                            <div class="info-box">
                                <span class="info-box-icon bg-info">
                                    <i class="fas fa-list"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Total Logs" %}</span>
                                    <span class="info-box-number">{{ total_logs }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="info-box">
                                <span class="info-box-icon bg-success">
                                    <i class="fas fa-plus"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Created" %}</span>
                                    <span class="info-box-number">{{ created_count }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="info-box">
                                <span class="info-box-icon bg-warning">
                                    <i class="fas fa-check"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Approved" %}</span>
                                    <span class="info-box-number">{{ approved_count }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="info-box">
                                <span class="info-box-icon bg-primary">
                                    <i class="fas fa-paper-plane"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Posted" %}</span>
                                    <span class="info-box-number">{{ posted_count }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="info-box">
                                <span class="info-box-icon bg-secondary">
                                    <i class="fas fa-edit"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Modified" %}</span>
                                    <span class="info-box-number">{{ modified_count }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="info-box">
                                <span class="info-box-icon bg-danger">
                                    <i class="fas fa-times"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Cancelled" %}</span>
                                    <span class="info-box-number">{{ cancelled_count }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Audit Logs Table -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-table"></i>
                                        {% trans "Audit Log Entries" %}
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead class="thead-dark">
                                                <tr>
                                                    <th>{% trans "Timestamp" %}</th>
                                                    <th>{% trans "Transaction ID" %}</th>
                                                    <th>{% trans "Action" %}</th>
                                                    <th>{% trans "User" %}</th>
                                                    <th>{% trans "IP Address" %}</th>
                                                    <th>{% trans "Notes" %}</th>
                                                    <th>{% trans "Changes" %}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for log in audit_logs %}
                                                <tr>
                                                    <td>
                                                        <span class="text-nowrap">{{ log.timestamp|date:"Y-m-d H:i:s" }}</span>
                                                    </td>
                                                    <td>
                                                        <a href="{% url 'finance:transaction_detail' log.transaction.pk %}">
                                                            {{ log.transaction.transaction_id }}
                                                        </a>
                                                    </td>
                                                    <td>
                                                        {% if log.action == 'created' %}
                                                            <span class="badge badge-success">
                                                                <i class="fas fa-plus"></i>
                                                                {{ log.get_action_display }}
                                                            </span>
                                                        {% elif log.action == 'modified' %}
                                                            <span class="badge badge-secondary">
                                                                <i class="fas fa-edit"></i>
                                                                {{ log.get_action_display }}
                                                            </span>
                                                        {% elif log.action == 'approved' %}
                                                            <span class="badge badge-warning">
                                                                <i class="fas fa-check"></i>
                                                                {{ log.get_action_display }}
                                                            </span>
                                                        {% elif log.action == 'posted' %}
                                                            <span class="badge badge-primary">
                                                                <i class="fas fa-paper-plane"></i>
                                                                {{ log.get_action_display }}
                                                            </span>
                                                        {% elif log.action == 'cancelled' %}
                                                            <span class="badge badge-danger">
                                                                <i class="fas fa-times"></i>
                                                                {{ log.get_action_display }}
                                                            </span>
                                                        {% else %}
                                                            <span class="badge badge-info">
                                                                {{ log.get_action_display }}
                                                            </span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="avatar-sm mr-2">
                                                                <div class="avatar-title bg-primary rounded-circle">
                                                                    {{ log.user.first_name.0|default:log.user.username.0|upper }}
                                                                </div>
                                                            </div>
                                                            <div>
                                                                <div class="font-weight-bold">
                                                                    {{ log.user.get_full_name|default:log.user.username }}
                                                                </div>
                                                                <small class="text-muted">{{ log.user.username }}</small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="text-muted">{{ log.ip_address|default:"-" }}</span>
                                                    </td>
                                                    <td>
                                                        {% if log.notes %}
                                                            <span class="text-truncate" style="max-width: 200px;" title="{{ log.notes }}">
                                                                {{ log.notes|truncatechars:50 }}
                                                            </span>
                                                        {% else %}
                                                            <span class="text-muted">-</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        {% if log.changes %}
                                                            <button class="btn btn-sm btn-outline-info" 
                                                                    data-toggle="modal" 
                                                                    data-target="#changesModal{{ log.id }}">
                                                                <i class="fas fa-eye"></i>
                                                                {% trans "View" %}
                                                            </button>
                                                        {% else %}
                                                            <span class="text-muted">-</span>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% empty %}
                                                <tr>
                                                    <td colspan="7" class="text-center">
                                                        <div class="alert alert-info mb-0">
                                                            <i class="fas fa-info-circle"></i>
                                                            {% trans "No audit logs found matching the current filters." %}
                                                        </div>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    
                                    <!-- Pagination -->
                                    {% if is_paginated %}
                                    <nav aria-label="{% trans 'Audit logs pagination' %}">
                                        <ul class="pagination justify-content-center">
                                            {% if page_obj.has_previous %}
                                                <li class="page-item">
                                                    <a class="page-link" href="?page=1{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">
                                                        {% trans "First" %}
                                                    </a>
                                                </li>
                                                <li class="page-item">
                                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">
                                                        {% trans "Previous" %}
                                                    </a>
                                                </li>
                                            {% endif %}
                                            
                                            <li class="page-item active">
                                                <span class="page-link">
                                                    {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                                </span>
                                            </li>
                                            
                                            {% if page_obj.has_next %}
                                                <li class="page-item">
                                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">
                                                        {% trans "Next" %}
                                                    </a>
                                                </li>
                                                <li class="page-item">
                                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">
                                                        {% trans "Last" %}
                                                    </a>
                                                </li>
                                            {% endif %}
                                        </ul>
                                    </nav>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Changes Modals -->
{% for log in audit_logs %}
    {% if log.changes %}
    <div class="modal fade" id="changesModal{{ log.id }}" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-history"></i>
                        {% trans "Changes Details" %} - {{ log.transaction.transaction_id }}
                    </h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>{% trans "Action" %}:</strong> {{ log.get_action_display }}
                        </div>
                        <div class="col-md-6">
                            <strong>{% trans "Timestamp" %}:</strong> {{ log.timestamp|date:"Y-m-d H:i:s" }}
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>{% trans "User" %}:</strong> {{ log.user.get_full_name|default:log.user.username }}
                        </div>
                        <div class="col-md-6">
                            <strong>{% trans "IP Address" %}:</strong> {{ log.ip_address|default:"-" }}
                        </div>
                    </div>
                    {% if log.notes %}
                    <div class="row mb-3">
                        <div class="col-12">
                            <strong>{% trans "Notes" %}:</strong>
                            <p class="mt-1">{{ log.notes }}</p>
                        </div>
                    </div>
                    {% endif %}
                    <div class="row">
                        <div class="col-12">
                            <strong>{% trans "Changes" %}:</strong>
                            <pre class="bg-light p-3 mt-1"><code>{{ log.changes|pprint }}</code></pre>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        {% trans "Close" %}
                    </button>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
{% endfor %}

<style>
.info-box {
    display: block;
    min-height: 90px;
    background: #fff;
    width: 100%;
    box-shadow: 0 1px 1px rgba(0,0,0,0.1);
    border-radius: 2px;
    margin-bottom: 15px;
}

.info-box-icon {
    border-top-left-radius: 2px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 2px;
    display: block;
    float: left;
    height: 90px;
    width: 90px;
    text-align: center;
    font-size: 45px;
    line-height: 90px;
    background: rgba(0,0,0,0.2);
    color: rgba(255,255,255,0.8);
}

.info-box-content {
    padding: 5px 10px;
    margin-left: 90px;
}

.info-box-text {
    text-transform: uppercase;
    font-weight: bold;
    font-size: 12px;
}

.info-box-number {
    display: block;
    font-weight: bold;
    font-size: 18px;
}

.avatar-sm {
    height: 2rem;
    width: 2rem;
}

.avatar-title {
    align-items: center;
    background-color: #6c757d;
    color: #fff;
    display: flex;
    font-size: 0.875rem;
    font-weight: 500;
    height: 100%;
    justify-content: center;
    width: 100%;
}

.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.badge {
    font-size: 0.8em;
}

pre code {
    font-size: 0.875rem;
    color: #495057;
}
</style>
{% endblock %}