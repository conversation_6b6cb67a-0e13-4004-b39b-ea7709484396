{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Balance Sheet" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-balance-scale"></i> {% trans "Balance Sheet" %}
                    </h3>
                    <div class="card-tools">
                        <button class="btn btn-primary btn-sm" onclick="window.print()">
                            <i class="fas fa-print"></i> {% trans "Print" %}
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <h4>{{ school.name }}</h4>
                        <h5>{% trans "Balance Sheet" %}</h5>
                        <p>{% trans "As of" %} {{ as_of_date|date:"F d, Y" }}</p>
                    </div>

                    <div class="row">
                        <!-- Assets -->
                        <div class="col-md-6">
                            <h5 class="text-primary">{% trans "ASSETS" %}</h5>
                            <table class="table table-sm">
                                <tbody>
                                    {% for asset in assets %}
                                    <tr>
                                        <td>{{ asset.name }}</td>
                                        <td class="text-right">{{ asset.balance|floatformat:2 }}</td>
                                    </tr>
                                    {% endfor %}
                                    <tr class="font-weight-bold border-top">
                                        <td>{% trans "Total Assets" %}</td>
                                        <td class="text-right">{{ total_assets|floatformat:2 }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Liabilities & Equity -->
                        <div class="col-md-6">
                            <h5 class="text-danger">{% trans "LIABILITIES" %}</h5>
                            <table class="table table-sm">
                                <tbody>
                                    {% for liability in liabilities %}
                                    <tr>
                                        <td>{{ liability.name }}</td>
                                        <td class="text-right">{{ liability.balance|floatformat:2 }}</td>
                                    </tr>
                                    {% endfor %}
                                    <tr class="font-weight-bold border-top">
                                        <td>{% trans "Total Liabilities" %}</td>
                                        <td class="text-right">{{ total_liabilities|floatformat:2 }}</td>
                                    </tr>
                                </tbody>
                            </table>

                            <h5 class="text-success mt-4">{% trans "EQUITY" %}</h5>
                            <table class="table table-sm">
                                <tbody>
                                    {% for equity in equity_accounts %}
                                    <tr>
                                        <td>{{ equity.name }}</td>
                                        <td class="text-right">{{ equity.balance|floatformat:2 }}</td>
                                    </tr>
                                    {% endfor %}
                                    <tr class="font-weight-bold border-top">
                                        <td>{% trans "Total Equity" %}</td>
                                        <td class="text-right">{{ total_equity|floatformat:2 }}</td>
                                    </tr>
                                    <tr class="font-weight-bold border-top">
                                        <td>{% trans "Total Liabilities & Equity" %}</td>
                                        <td class="text-right">{{ total_liabilities_equity|floatformat:2 }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}