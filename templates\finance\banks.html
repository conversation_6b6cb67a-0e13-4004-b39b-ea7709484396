{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Banks" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-university"></i> {% trans "Banks" %}
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#addBankModal">
                            <i class="fas fa-plus"></i> {% trans "Add Bank" %}
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Bank Name" %}</th>
                                    <th>{% trans "Account Number" %}</th>
                                    <th>{% trans "Account Name" %}</th>
                                    <th>{% trans "Current Balance" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for bank in banks %}
                                <tr>
                                    <td>
                                        <strong>{{ bank.bank_name }}</strong>
                                        <br><small class="text-muted">{{ bank.branch_name }}</small>
                                    </td>
                                    <td>{{ bank.account_number }}</td>
                                    <td>{{ bank.account_name }}</td>
                                    <td class="text-right">
                                        <strong class="{% if bank.current_balance >= 0 %}text-success{% else %}text-danger{% endif %}">
                                            {{ bank.current_balance|floatformat:2 }}
                                        </strong>
                                    </td>
                                    <td>
                                        {% if bank.is_active %}
                                            <span class="badge badge-success">{% trans "Active" %}</span>
                                        {% else %}
                                            <span class="badge badge-secondary">{% trans "Inactive" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-info" onclick="viewBank('{{ bank.id }}')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-warning" onclick="editBank('{{ bank.id }}')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-success" onclick="viewStatement('{{ bank.id }}')">
                                                <i class="fas fa-file-alt"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center">
                                        {% trans "No banks found" %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Bank Modal -->
<div class="modal fade" id="addBankModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Add Bank Account" %}</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="addBankForm">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>{% trans "Bank Name" %}</label>
                                <input type="text" class="form-control" name="bank_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>{% trans "Branch Name" %}</label>
                                <input type="text" class="form-control" name="branch_name">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>{% trans "Account Number" %}</label>
                                <input type="text" class="form-control" name="account_number" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>{% trans "Account Name" %}</label>
                                <input type="text" class="form-control" name="account_name" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>{% trans "Opening Balance" %}</label>
                                <input type="number" class="form-control" name="opening_balance" step="0.01" value="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>{% trans "IBAN" %}</label>
                                <input type="text" class="form-control" name="iban">
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>{% trans "Notes" %}</label>
                        <textarea class="form-control" name="notes" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" name="is_active" id="is_active" checked>
                            <label class="custom-control-label" for="is_active">
                                {% trans "Active" %}
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{% trans "Cancel" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "Save" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function viewBank(id) {
    console.log('View bank:', id);
}

function editBank(id) {
    console.log('Edit bank:', id);
}

function viewStatement(id) {
    console.log('View statement for bank:', id);
}

document.getElementById('addBankForm').addEventListener('submit', function(e) {
    e.preventDefault();
    console.log('Add bank form submitted');
});
</script>
{% endblock %}