{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Add Budget Item" %} - {{ budget.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% trans "Add Budget Item" %}</h3>
                    <div class="card-tools">
                        <a href="{% url 'finance:budget_detail' budget.pk %}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> {% trans "Back to Budget" %}
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Budget Info -->
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> {% trans "Budget" %}: {{ budget.name }}</h5>
                        <p class="mb-0">
                            <strong>{% trans "Period" %}:</strong> {{ budget.start_date|date:"M d, Y" }} - {{ budget.end_date|date:"M d, Y" }}<br>
                            <strong>{% trans "Current Total" %}:</strong> {{ budget.get_total_allocated|floatformat:2 }}
                        </p>
                    </div>

                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.account.id_for_label }}">{{ form.account.label }} <span class="text-danger">*</span></label>
                                    {{ form.account }}
                                    {% if form.account.errors %}
                                        <div class="text-danger">{{ form.account.errors }}</div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        {% trans "Select the account for this budget allocation" %}
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.cost_center.id_for_label }}">{{ form.cost_center.label }}</label>
                                    {{ form.cost_center }}
                                    {% if form.cost_center.errors %}
                                        <div class="text-danger">{{ form.cost_center.errors }}</div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        {% trans "Optional: Assign to a specific cost center" %}
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.allocated_amount.id_for_label }}">{{ form.allocated_amount.label }} <span class="text-danger">*</span></label>
                                    {{ form.allocated_amount }}
                                    {% if form.allocated_amount.errors %}
                                        <div class="text-danger">{{ form.allocated_amount.errors }}</div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        {% trans "Enter the budget allocation amount for this account" %}
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="{{ form.notes.id_for_label }}">{{ form.notes.label }}</label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="text-danger">{{ form.notes.errors }}</div>
                            {% endif %}
                            <small class="form-text text-muted">
                                {% trans "Optional notes about this budget allocation" %}
                            </small>
                        </div>

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> {% trans "Add Budget Item" %}
                            </button>
                            <a href="{% url 'finance:budget_detail' budget.pk %}" class="btn btn-secondary">
                                {% trans "Cancel" %}
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Existing Budget Items -->
            {% if budget.items.exists %}
            <div class="card mt-4">
                <div class="card-header">
                    <h4 class="card-title">{% trans "Existing Budget Items" %}</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>{% trans "Account" %}</th>
                                    <th>{% trans "Cost Center" %}</th>
                                    <th>{% trans "Amount" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in budget.items.all %}
                                <tr>
                                    <td>
                                        <strong>{{ item.account.code }}</strong><br>
                                        <small>{{ item.account.name }}</small>
                                    </td>
                                    <td>
                                        {% if item.cost_center %}
                                            {{ item.cost_center.name }}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ item.allocated_amount|floatformat:2 }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr class="font-weight-bold">
                                    <td colspan="2">{% trans "Total" %}</td>
                                    <td>{{ budget.get_total_allocated|floatformat:2 }}</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add validation for duplicate account/cost center combinations
    const accountSelect = document.getElementById('{{ form.account.id_for_label }}');
    const costCenterSelect = document.getElementById('{{ form.cost_center.id_for_label }}');
    
    function checkDuplicate() {
        const accountId = accountSelect.value;
        const costCenterId = costCenterSelect.value || 'null';
        
        // This would need to be populated from the backend with existing combinations
        // For now, just a placeholder for the validation logic
    }
    
    accountSelect.addEventListener('change', checkDuplicate);
    costCenterSelect.addEventListener('change', checkDuplicate);
});
</script>
{% endblock %}