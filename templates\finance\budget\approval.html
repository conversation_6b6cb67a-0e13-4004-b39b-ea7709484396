{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Budget Approval" %} - {{ budget.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% trans "Budget Approval" %}</h3>
                    <div class="card-tools">
                        <a href="{% url 'finance:budget_detail' budget.pk %}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> {% trans "Back to Budget" %}
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Budget Summary -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h4>{{ budget.name }}</h4>
                            {% if budget.name_ar %}
                                <p class="text-muted">{{ budget.name_ar }}</p>
                            {% endif %}
                            <p><strong>{% trans "Type" %}:</strong> {{ budget.get_budget_type_display }}</p>
                            <p><strong>{% trans "Period" %}:</strong> {{ budget.start_date|date:"M d, Y" }} - {{ budget.end_date|date:"M d, Y" }}</p>
                            <p><strong>{% trans "Financial Year" %}:</strong> {{ budget.financial_year }}</p>
                        </div>
                        <div class="col-md-6">
                            <div class="info-box">
                                <span class="info-box-icon bg-blue">
                                    <i class="fas fa-dollar-sign"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Total Budget Amount" %}</span>
                                    <span class="info-box-number">{{ total_allocated|floatformat:2 }}</span>
                                </div>
                            </div>
                            <div class="info-box">
                                <span class="info-box-icon bg-green">
                                    <i class="fas fa-list"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Budget Items" %}</span>
                                    <span class="info-box-number">{{ items_count }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% if budget.description %}
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5>{% trans "Description" %}</h5>
                            <p>{{ budget.description }}</p>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Budget Items Preview -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5>{% trans "Budget Items" %}</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>{% trans "Account" %}</th>
                                            <th>{% trans "Cost Center" %}</th>
                                            <th>{% trans "Allocated Amount" %}</th>
                                            <th>{% trans "Notes" %}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in budget.items.all %}
                                        <tr>
                                            <td>
                                                <strong>{{ item.account.code }}</strong><br>
                                                <small>{{ item.account.name }}</small>
                                            </td>
                                            <td>
                                                {% if item.cost_center %}
                                                    {{ item.cost_center.name }}
                                                {% else %}
                                                    <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ item.allocated_amount|floatformat:2 }}</td>
                                            <td>
                                                {% if item.notes %}
                                                    {{ item.notes|truncatewords:10 }}
                                                {% else %}
                                                    <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Approval Form -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">{% trans "Approval Decision" %}</h5>
                                </div>
                                <div class="card-body">
                                    <form method="post">
                                        {% csrf_token %}
                                        
                                        <div class="form-group">
                                            <label for="{{ form.action.id_for_label }}">{{ form.action.label }}</label>
                                            {{ form.action }}
                                            {% if form.action.errors %}
                                                <div class="text-danger">{{ form.action.errors }}</div>
                                            {% endif %}
                                        </div>

                                        <div class="form-group">
                                            <label for="{{ form.comments.id_for_label }}">{{ form.comments.label }}</label>
                                            {{ form.comments }}
                                            {% if form.comments.errors %}
                                                <div class="text-danger">{{ form.comments.errors }}</div>
                                            {% endif %}
                                            <small class="form-text text-muted">{{ form.comments.help_text }}</small>
                                        </div>

                                        {% if form.non_field_errors %}
                                            <div class="alert alert-danger">
                                                {{ form.non_field_errors }}
                                            </div>
                                        {% endif %}

                                        <div class="form-group">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-check"></i> {% trans "Submit Decision" %}
                                            </button>
                                            <a href="{% url 'finance:budget_detail' budget.pk %}" class="btn btn-secondary">
                                                {% trans "Cancel" %}
                                            </a>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const actionSelect = document.getElementById('{{ form.action.id_for_label }}');
    const commentsField = document.getElementById('{{ form.comments.id_for_label }}');
    
    actionSelect.addEventListener('change', function() {
        if (this.value === 'reject' || this.value === 'request_changes') {
            commentsField.required = true;
            commentsField.parentElement.querySelector('label').innerHTML += ' <span class="text-danger">*</span>';
        } else {
            commentsField.required = false;
            const label = commentsField.parentElement.querySelector('label');
            label.innerHTML = label.innerHTML.replace(' <span class="text-danger">*</span>', '');
        }
    });
});
</script>
{% endblock %}