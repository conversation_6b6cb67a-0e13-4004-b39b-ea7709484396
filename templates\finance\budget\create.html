{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Create Budget" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% trans "Create New Budget" %}</h3>
                    <div class="card-tools">
                        <a href="{% url 'finance:budget_list' %}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> {% trans "Back to List" %}
                        </a>
                    </div>
                </div>
                
                <form method="post">
                    {% csrf_token %}
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.name.id_for_label }}">{{ form.name.label }}</label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="text-danger">{{ form.name.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.name_ar.id_for_label }}">{{ form.name_ar.label }}</label>
                                    {{ form.name_ar }}
                                    {% if form.name_ar.errors %}
                                        <div class="text-danger">{{ form.name_ar.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.budget_type.id_for_label }}">{{ form.budget_type.label }}</label>
                                    {{ form.budget_type }}
                                    {% if form.budget_type.errors %}
                                        <div class="text-danger">{{ form.budget_type.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.financial_year.id_for_label }}">{{ form.financial_year.label }}</label>
                                    {{ form.financial_year }}
                                    {% if form.financial_year.errors %}
                                        <div class="text-danger">{{ form.financial_year.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.start_date.id_for_label }}">{{ form.start_date.label }}</label>
                                    {{ form.start_date }}
                                    {% if form.start_date.errors %}
                                        <div class="text-danger">{{ form.start_date.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.end_date.id_for_label }}">{{ form.end_date.label }}</label>
                                    {{ form.end_date }}
                                    {% if form.end_date.errors %}
                                        <div class="text-danger">{{ form.end_date.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="{{ form.description.id_for_label }}">{{ form.description.label }}</label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger">{{ form.description.errors }}</div>
                            {% endif %}
                        </div>

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> {% trans "Create Budget" %}
                        </button>
                        <a href="{% url 'finance:budget_list' %}" class="btn btn-secondary">
                            {% trans "Cancel" %}
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}