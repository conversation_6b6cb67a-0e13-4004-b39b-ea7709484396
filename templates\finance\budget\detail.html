{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{{ budget.name }} - {% trans "Budget Details" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Budget Header -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="card-title">{{ budget.name }}</h3>
                        {% if budget.name_ar %}
                            <p class="text-muted mb-0">{{ budget.name_ar }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <span class="badge badge-lg 
                            {% if budget.status == 'active' %}badge-success
                            {% elif budget.status == 'approved' %}badge-info
                            {% elif budget.status == 'pending_approval' %}badge-warning
                            {% elif budget.status == 'draft' %}badge-secondary
                            {% else %}badge-danger{% endif %}">
                            {{ budget.get_status_display }}
                        </span>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>{% trans "Type" %}:</strong><br>
                            {{ budget.get_budget_type_display }}
                        </div>
                        <div class="col-md-3">
                            <strong>{% trans "Period" %}:</strong><br>
                            {{ budget.start_date|date:"M d, Y" }} - {{ budget.end_date|date:"M d, Y" }}
                        </div>
                        <div class="col-md-3">
                            <strong>{% trans "Financial Year" %}:</strong><br>
                            {{ budget.financial_year }}
                        </div>
                        <div class="col-md-3">
                            <strong>{% trans "Created" %}:</strong><br>
                            {{ budget.created_at|date:"M d, Y" }}
                        </div>
                    </div>
                    
                    {% if budget.description %}
                    <div class="row mt-3">
                        <div class="col-12">
                            <strong>{% trans "Description" %}:</strong><br>
                            {{ budget.description }}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Budget Summary -->
            <div class="row mt-4">
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-blue">
                            <i class="fas fa-dollar-sign"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">{% trans "Total Allocated" %}</span>
                            <span class="info-box-number">{{ total_allocated|floatformat:2 }}</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-green">
                            <i class="fas fa-chart-line"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">{% trans "Total Spent" %}</span>
                            <span class="info-box-number">{{ total_spent|floatformat:2 }}</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon {% if variance < 0 %}bg-red{% else %}bg-yellow{% endif %}">
                            <i class="fas fa-balance-scale"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">{% trans "Variance" %}</span>
                            <span class="info-box-number">{{ variance|floatformat:2 }}</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon 
                            {% if utilization > 90 %}bg-red
                            {% elif utilization > 75 %}bg-yellow
                            {% else %}bg-green{% endif %}">
                            <i class="fas fa-percentage"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">{% trans "Utilization" %}</span>
                            <span class="info-box-number">{{ utilization|floatformat:1 }}%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Budget Items -->
            <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title">{% trans "Budget Items" %}</h4>
                    {% if can_edit %}
                        <a href="{% url 'finance:budget_add_item' budget_pk=budget.pk %}" 
                           class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> {% trans "Add Item" %}
                        </a>
                    {% endif %}
                </div>
                
                <div class="card-body">
                    {% if items_data %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>{% trans "Account" %}</th>
                                        <th>{% trans "Cost Center" %}</th>
                                        <th>{% trans "Allocated" %}</th>
                                        <th>{% trans "Spent" %}</th>
                                        <th>{% trans "Variance" %}</th>
                                        <th>{% trans "Utilization" %}</th>
                                        <th>{% trans "Status" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for item_data in items_data %}
                                    {% with item=item_data.item %}
                                    <tr>
                                        <td>
                                            <strong>{{ item.account.code }}</strong><br>
                                            <small>{{ item.account.name }}</small>
                                        </td>
                                        <td>
                                            {% if item.cost_center %}
                                                {{ item.cost_center.name }}
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ item.allocated_amount|floatformat:2 }}</td>
                                        <td>{{ item.spent_amount|floatformat:2 }}</td>
                                        <td class="{% if item_data.variance < 0 %}text-danger{% else %}text-success{% endif %}">
                                            {{ item_data.variance|floatformat:2 }}
                                        </td>
                                        <td>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar 
                                                    {% if item_data.utilization > 90 %}bg-danger
                                                    {% elif item_data.utilization > 75 %}bg-warning
                                                    {% else %}bg-success{% endif %}"
                                                    role="progressbar" 
                                                    style="width: {{ item_data.utilization }}%">
                                                    {{ item_data.utilization|floatformat:1 }}%
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge 
                                                {% if item_data.status == 'over_budget' %}badge-danger
                                                {% else %}badge-success{% endif %}">
                                                {% if item_data.status == 'over_budget' %}
                                                    {% trans "Over Budget" %}
                                                {% else %}
                                                    {% trans "On Track" %}
                                                {% endif %}
                                            </span>
                                        </td>
                                    </tr>
                                    {% endwith %}
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <p class="text-muted">{% trans "No budget items added yet." %}</p>
                            {% if can_edit %}
                                <a href="{% url 'finance:budget_add_item' budget_pk=budget.pk %}" 
                                   class="btn btn-primary">
                                    <i class="fas fa-plus"></i> {% trans "Add First Item" %}
                                </a>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Actions -->
            <div class="card mt-4">
                <div class="card-body">
                    <div class="btn-group" role="group">
                        {% if can_edit %}
                            <a href="{% url 'finance:budget_update' budget.pk %}" 
                               class="btn btn-secondary">
                                <i class="fas fa-edit"></i> {% trans "Edit Budget" %}
                            </a>
                            {% if budget.items.exists %}
                                <form method="post" action="{% url 'finance:budget_submit_approval' budget.pk %}" 
                                      style="display: inline;">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-warning"
                                            onclick="return confirm('{% trans "Submit this budget for approval?" %}')">
                                        <i class="fas fa-paper-plane"></i> {% trans "Submit for Approval" %}
                                    </button>
                                </form>
                            {% endif %}
                        {% endif %}
                        
                        {% if can_approve %}
                            <a href="{% url 'finance:budget_approval' budget.pk %}" 
                               class="btn btn-success">
                                <i class="fas fa-check"></i> {% trans "Review & Approve" %}
                            </a>
                        {% endif %}
                        
                        {% if budget.status in 'active,approved' %}
                            <a href="{% url 'finance:budget_variance_report' budget.pk %}" 
                               class="btn btn-info">
                                <i class="fas fa-chart-line"></i> {% trans "Variance Report" %}
                            </a>
                        {% endif %}
                        
                        <a href="{% url 'finance:budget_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> {% trans "Back to List" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}