{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Budget Management" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">{% trans "Budget Management" %}</h3>
                    <a href="{% url 'finance:budget_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> {% trans "Create Budget" %}
                    </a>
                </div>
                
                <div class="card-body">
                    <!-- Filters -->
                    <form method="get" class="mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <select name="status" class="form-control">
                                    <option value="">{% trans "All Statuses" %}</option>
                                    {% for value, label in status_choices %}
                                        <option value="{{ value }}" {% if current_status == value %}selected{% endif %}>
                                            {{ label }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4">
                                <select name="budget_type" class="form-control">
                                    <option value="">{% trans "All Types" %}</option>
                                    {% for value, label in type_choices %}
                                        <option value="{{ value }}" {% if current_type == value %}selected{% endif %}>
                                            {{ label }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-secondary">
                                    <i class="fas fa-filter"></i> {% trans "Filter" %}
                                </button>
                                <a href="{% url 'finance:budget_list' %}" class="btn btn-outline-secondary">
                                    {% trans "Clear" %}
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Budget List -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Name" %}</th>
                                    <th>{% trans "Type" %}</th>
                                    <th>{% trans "Period" %}</th>
                                    <th>{% trans "Total Budget" %}</th>
                                    <th>{% trans "Utilization" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for budget in budgets %}
                                <tr>
                                    <td>
                                        <strong>{{ budget.name }}</strong>
                                        {% if budget.name_ar %}
                                            <br><small class="text-muted">{{ budget.name_ar }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ budget.get_budget_type_display }}</td>
                                    <td>
                                        {{ budget.start_date|date:"M d, Y" }} - 
                                        {{ budget.end_date|date:"M d, Y" }}
                                    </td>
                                    <td>{{ budget.get_total_allocated|floatformat:2 }}</td>
                                    <td>
                                        {% with utilization=budget.get_utilization_percentage %}
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar 
                                                    {% if utilization > 90 %}bg-danger
                                                    {% elif utilization > 75 %}bg-warning
                                                    {% else %}bg-success{% endif %}"
                                                    role="progressbar" 
                                                    style="width: {{ utilization }}%"
                                                    aria-valuenow="{{ utilization }}" 
                                                    aria-valuemin="0" 
                                                    aria-valuemax="100">
                                                    {{ utilization|floatformat:1 }}%
                                                </div>
                                            </div>
                                        {% endwith %}
                                    </td>
                                    <td>
                                        <span class="badge 
                                            {% if budget.status == 'active' %}badge-success
                                            {% elif budget.status == 'approved' %}badge-info
                                            {% elif budget.status == 'pending_approval' %}badge-warning
                                            {% elif budget.status == 'draft' %}badge-secondary
                                            {% else %}badge-danger{% endif %}">
                                            {{ budget.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'finance:budget_detail' budget.pk %}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if budget.status == 'draft' %}
                                                <a href="{% url 'finance:budget_update' budget.pk %}" 
                                                   class="btn btn-sm btn-outline-secondary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            {% endif %}
                                            {% if budget.status in 'active,approved' %}
                                                <a href="{% url 'finance:budget_variance_report' budget.pk %}" 
                                                   class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-chart-line"></i>
                                                </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center">
                                        {% trans "No budgets found." %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if is_paginated %}
                    <nav aria-label="Budget pagination">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1">{% trans "First" %}</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">
                                        {% trans "Previous" %}
                                    </a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">
                                        {% trans "Next" %}
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">
                                        {% trans "Last" %}
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}