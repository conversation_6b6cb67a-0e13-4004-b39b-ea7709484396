{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Budget Monitoring" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Dashboard Overview -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% trans "Budget Performance Dashboard" %}</h3>
                </div>
                <div class="card-body">
                    {% if dashboard_data.overview %}
                    <div class="row">
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-blue">
                                    <i class="fas fa-list"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Active Budgets" %}</span>
                                    <span class="info-box-number">{{ dashboard_data.overview.total_active_budgets }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-green">
                                    <i class="fas fa-dollar-sign"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Total Allocated" %}</span>
                                    <span class="info-box-number">{{ dashboard_data.overview.total_allocated|floatformat:0 }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-yellow">
                                    <i class="fas fa-chart-line"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Total Spent" %}</span>
                                    <span class="info-box-number">{{ dashboard_data.overview.total_spent|floatformat:0 }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-red">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Over Threshold" %}</span>
                                    <span class="info-box-number">{{ dashboard_data.overview.budgets_over_threshold }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Overall Utilization -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <h5>{% trans "Overall Budget Utilization" %}: {{ dashboard_data.overview.overall_utilization|floatformat:1 }}%</h5>
                            <div class="progress" style="height: 30px;">
                                <div class="progress-bar 
                                    {% if dashboard_data.overview.overall_utilization > 90 %}bg-danger
                                    {% elif dashboard_data.overview.overall_utilization > 75 %}bg-warning
                                    {% else %}bg-success{% endif %}"
                                    role="progressbar" 
                                    style="width: {{ dashboard_data.overview.overall_utilization }}%">
                                    {{ dashboard_data.overview.overall_utilization|floatformat:1 }}%
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">{% trans "Budget Monitoring Filters" %}</h4>
                </div>
                <div class="card-body">
                    <form method="get">
                        <div class="row">
                            <div class="col-md-3">
                                {{ filter_form.budget.label_tag }}
                                {{ filter_form.budget }}
                            </div>
                            <div class="col-md-3">
                                {{ filter_form.budget_type.label_tag }}
                                {{ filter_form.budget_type }}
                            </div>
                            <div class="col-md-3">
                                {{ filter_form.status.label_tag }}
                                {{ filter_form.status }}
                            </div>
                            <div class="col-md-3">
                                {{ filter_form.variance_threshold.label_tag }}
                                {{ filter_form.variance_threshold }}
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter"></i> {% trans "Apply Filters" %}
                                </button>
                                <a href="{% url 'finance:budget_monitoring' %}" class="btn btn-secondary">
                                    {% trans "Clear Filters" %}
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Budget Reports -->
    <div class="row mt-4">
        <div class="col-12">
            {% for report in budget_reports %}
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title">{{ report.budget.name }}</h4>
                    <div>
                        <span class="badge badge-info">{{ report.budget.type|title }}</span>
                        <a href="{% url 'finance:budget_variance_report' report.budget.id %}" 
                           class="btn btn-sm btn-outline-primary ml-2">
                            <i class="fas fa-chart-line"></i> {% trans "Detailed Report" %}
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Summary Row -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <strong>{% trans "Period" %}:</strong><br>
                            {{ report.budget.period }}
                        </div>
                        <div class="col-md-3">
                            <strong>{% trans "Total Allocated" %}:</strong><br>
                            {{ report.summary.total_allocated|floatformat:2 }}
                        </div>
                        <div class="col-md-3">
                            <strong>{% trans "Total Spent" %}:</strong><br>
                            {{ report.summary.total_spent|floatformat:2 }}
                        </div>
                        <div class="col-md-3">
                            <strong>{% trans "Utilization" %}:</strong><br>
                            <span class="{% if report.summary.utilization_percentage > 90 %}text-danger{% elif report.summary.utilization_percentage > 75 %}text-warning{% else %}text-success{% endif %}">
                                {{ report.summary.utilization_percentage|floatformat:1 }}%
                            </span>
                        </div>
                    </div>

                    <!-- Progress Bar -->
                    <div class="progress mb-3" style="height: 25px;">
                        <div class="progress-bar 
                            {% if report.summary.utilization_percentage > 90 %}bg-danger
                            {% elif report.summary.utilization_percentage > 75 %}bg-warning
                            {% else %}bg-success{% endif %}"
                            role="progressbar" 
                            style="width: {{ report.summary.utilization_percentage }}%">
                            {{ report.summary.utilization_percentage|floatformat:1 }}%
                        </div>
                    </div>

                    <!-- Top Variance Items -->
                    {% if report.items %}
                    <h6>{% trans "Budget Items Summary" %}</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>{% trans "Account" %}</th>
                                    <th>{% trans "Allocated" %}</th>
                                    <th>{% trans "Spent" %}</th>
                                    <th>{% trans "Variance" %}</th>
                                    <th>{% trans "Status" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in report.items|slice:":5" %}
                                <tr>
                                    <td>
                                        <strong>{{ item.account_code }}</strong><br>
                                        <small>{{ item.account_name }}</small>
                                    </td>
                                    <td>{{ item.allocated_amount|floatformat:2 }}</td>
                                    <td>{{ item.spent_amount|floatformat:2 }}</td>
                                    <td class="{% if item.variance_amount < 0 %}text-danger{% else %}text-success{% endif %}">
                                        {{ item.variance_amount|floatformat:2 }}
                                        ({{ item.variance_percentage|floatformat:1 }}%)
                                    </td>
                                    <td>
                                        <span class="badge 
                                            {% if item.status == 'over_budget' %}badge-danger
                                            {% elif item.status == 'warning' %}badge-warning
                                            {% elif item.status == 'under_utilized' %}badge-info
                                            {% else %}badge-success{% endif %}">
                                            {% if item.status == 'over_budget' %}{% trans "Over Budget" %}
                                            {% elif item.status == 'warning' %}{% trans "Warning" %}
                                            {% elif item.status == 'under_utilized' %}{% trans "Under Utilized" %}
                                            {% else %}{% trans "On Track" %}{% endif %}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% if report.items|length > 5 %}
                        <p class="text-muted">
                            {% trans "Showing top 5 items." %} 
                            <a href="{% url 'finance:budget_variance_report' report.budget.id %}">
                                {% trans "View all items" %}
                            </a>
                        </p>
                    {% endif %}
                    {% endif %}
                </div>
            </div>
            {% empty %}
            <div class="card">
                <div class="card-body text-center">
                    <p class="text-muted">{% trans "No budget reports available with current filters." %}</p>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<script>
// Auto-refresh every 5 minutes
setTimeout(function() {
    location.reload();
}, 300000);
</script>
{% endblock %}