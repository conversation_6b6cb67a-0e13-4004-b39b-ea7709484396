{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Budget Variance Report" %} - {{ budget.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Report Header -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="card-title">{% trans "Budget Variance Report" %}</h3>
                        <p class="card-text">{{ budget.name }}</p>
                    </div>
                    <div>
                        <button onclick="window.print()" class="btn btn-secondary btn-sm">
                            <i class="fas fa-print"></i> {% trans "Print" %}
                        </button>
                        <a href="{% url 'finance:budget_detail' budget.pk %}" class="btn btn-primary btn-sm">
                            <i class="fas fa-arrow-left"></i> {% trans "Back to Budget" %}
                        </a>
                    </div>
                </div>
                
                {% if variance_report %}
                <div class="card-body">
                    <!-- Budget Summary -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>{% trans "Budget Information" %}</h5>
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td><strong>{% trans "Budget Name" %}:</strong></td>
                                    <td>{{ variance_report.budget.name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Type" %}:</strong></td>
                                    <td>{{ variance_report.budget.type|title }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Period" %}:</strong></td>
                                    <td>{{ variance_report.budget.period }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Status" %}:</strong></td>
                                    <td>
                                        <span class="badge badge-info">{{ variance_report.budget.status|title }}</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>{% trans "Financial Summary" %}</h5>
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td><strong>{% trans "Total Allocated" %}:</strong></td>
                                    <td class="text-right">{{ variance_report.summary.total_allocated|floatformat:2 }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Total Spent" %}:</strong></td>
                                    <td class="text-right">{{ variance_report.summary.total_spent|floatformat:2 }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Variance" %}:</strong></td>
                                    <td class="text-right {% if variance_report.summary.variance_amount < 0 %}text-danger{% else %}text-success{% endif %}">
                                        {{ variance_report.summary.variance_amount|floatformat:2 }}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Utilization" %}:</strong></td>
                                    <td class="text-right">
                                        <span class="{% if variance_report.summary.utilization_percentage > 90 %}text-danger{% elif variance_report.summary.utilization_percentage > 75 %}text-warning{% else %}text-success{% endif %}">
                                            {{ variance_report.summary.utilization_percentage|floatformat:1 }}%
                                        </span>
                                    </td>
                                </tr>
                                {% if variance_report.summary.days_remaining %}
                                <tr>
                                    <td><strong>{% trans "Days Remaining" %}:</strong></td>
                                    <td class="text-right">{{ variance_report.summary.days_remaining }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>

                    <!-- Overall Progress -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5>{% trans "Overall Budget Utilization" %}</h5>
                            <div class="progress" style="height: 30px;">
                                <div class="progress-bar 
                                    {% if variance_report.summary.utilization_percentage > 90 %}bg-danger
                                    {% elif variance_report.summary.utilization_percentage > 75 %}bg-warning
                                    {% else %}bg-success{% endif %}"
                                    role="progressbar" 
                                    style="width: {{ variance_report.summary.utilization_percentage }}%">
                                    {{ variance_report.summary.utilization_percentage|floatformat:1 }}%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Detailed Variance Analysis -->
            {% if variance_report.items %}
            <div class="card mt-4">
                <div class="card-header">
                    <h4 class="card-title">{% trans "Detailed Variance Analysis" %}</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Account" %}</th>
                                    <th>{% trans "Cost Center" %}</th>
                                    <th class="text-right">{% trans "Allocated" %}</th>
                                    <th class="text-right">{% trans "Spent" %}</th>
                                    <th class="text-right">{% trans "Variance Amount" %}</th>
                                    <th class="text-right">{% trans "Variance %" %}</th>
                                    <th class="text-right">{% trans "Utilization %" %}</th>
                                    <th class="text-center">{% trans "Status" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in variance_report.items %}
                                <tr>
                                    <td>
                                        <strong>{{ item.account_code }}</strong><br>
                                        <small class="text-muted">{{ item.account_name }}</small>
                                    </td>
                                    <td>
                                        {% if item.cost_center %}
                                            {{ item.cost_center }}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-right">{{ item.allocated_amount|floatformat:2 }}</td>
                                    <td class="text-right">{{ item.spent_amount|floatformat:2 }}</td>
                                    <td class="text-right {% if item.variance_amount < 0 %}text-danger{% else %}text-success{% endif %}">
                                        {{ item.variance_amount|floatformat:2 }}
                                    </td>
                                    <td class="text-right {% if item.variance_percentage < -10 %}text-danger{% elif item.variance_percentage < -5 %}text-warning{% else %}text-success{% endif %}">
                                        {{ item.variance_percentage|floatformat:1 }}%
                                    </td>
                                    <td class="text-right">
                                        <div class="progress" style="height: 20px; min-width: 80px;">
                                            <div class="progress-bar 
                                                {% if item.utilization_percentage > 90 %}bg-danger
                                                {% elif item.utilization_percentage > 75 %}bg-warning
                                                {% else %}bg-success{% endif %}"
                                                role="progressbar" 
                                                style="width: {{ item.utilization_percentage }}%">
                                                {{ item.utilization_percentage|floatformat:0 }}%
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge 
                                            {% if item.status == 'over_budget' %}badge-danger
                                            {% elif item.status == 'warning' %}badge-warning
                                            {% elif item.status == 'under_utilized' %}badge-info
                                            {% else %}badge-success{% endif %}">
                                            {% if item.status == 'over_budget' %}{% trans "Over Budget" %}
                                            {% elif item.status == 'warning' %}{% trans "Warning" %}
                                            {% elif item.status == 'under_utilized' %}{% trans "Under Utilized" %}
                                            {% else %}{% trans "On Track" %}{% endif %}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot class="font-weight-bold">
                                <tr class="table-info">
                                    <td colspan="2">{% trans "TOTAL" %}</td>
                                    <td class="text-right">{{ variance_report.summary.total_allocated|floatformat:2 }}</td>
                                    <td class="text-right">{{ variance_report.summary.total_spent|floatformat:2 }}</td>
                                    <td class="text-right {% if variance_report.summary.variance_amount < 0 %}text-danger{% else %}text-success{% endif %}">
                                        {{ variance_report.summary.variance_amount|floatformat:2 }}
                                    </td>
                                    <td class="text-right">-</td>
                                    <td class="text-right">{{ variance_report.summary.utilization_percentage|floatformat:1 }}%</td>
                                    <td class="text-center">-</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Analysis Summary -->
            <div class="card mt-4">
                <div class="card-header">
                    <h4 class="card-title">{% trans "Analysis Summary" %}</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-danger">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Over Budget" %}</span>
                                    <span class="info-box-number">
                                        {% with over_budget=variance_report.items|length %}
                                            {% for item in variance_report.items %}
                                                {% if item.status == 'over_budget' %}{{ forloop.counter0|add:1 }}{% endif %}
                                            {% endfor %}
                                        {% endwith %}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-warning">
                                    <i class="fas fa-exclamation"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Warning" %}</span>
                                    <span class="info-box-number">
                                        {% for item in variance_report.items %}
                                            {% if item.status == 'warning' %}{{ forloop.counter0|add:1 }}{% endif %}
                                        {% endfor %}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-success">
                                    <i class="fas fa-check"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "On Track" %}</span>
                                    <span class="info-box-number">
                                        {% for item in variance_report.items %}
                                            {% if item.status == 'on_track' %}{{ forloop.counter0|add:1 }}{% endif %}
                                        {% endfor %}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-info">
                                    <i class="fas fa-arrow-down"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Under Utilized" %}</span>
                                    <span class="info-box-number">
                                        {% for item in variance_report.items %}
                                            {% if item.status == 'under_utilized' %}{{ forloop.counter0|add:1 }}{% endif %}
                                        {% endfor %}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Report Footer -->
            <div class="card mt-4">
                <div class="card-body text-center">
                    <p class="text-muted mb-0">
                        {% trans "Report generated on" %} {{ variance_report.generated_at|date:"F d, Y \a\t H:i" }}
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .card-tools, .btn {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .progress {
        border: 1px solid #ddd;
    }
}
</style>
{% endblock %}