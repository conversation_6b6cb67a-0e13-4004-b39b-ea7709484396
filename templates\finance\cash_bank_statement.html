{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Cash & Bank Statement" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-university"></i> {% trans "Cash & Bank Statement" %}
                    </h3>
                    <div class="card-tools">
                        <button class="btn btn-primary btn-sm" onclick="window.print()">
                            <i class="fas fa-print"></i> {% trans "Print" %}
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <select class="form-control" id="account-filter">
                                <option value="">{% trans "All Cash & Bank Accounts" %}</option>
                                {% for account in cash_bank_accounts %}
                                    <option value="{{ account.id }}">{{ account.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="date-from" value="{{ date_from|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="date-to" value="{{ date_to|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-info" onclick="generateStatement()">
                                <i class="fas fa-search"></i> {% trans "Generate" %}
                            </button>
                        </div>
                    </div>

                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4>{{ opening_balance|floatformat:2|default:"0.00" }}</h4>
                                    <p class="mb-0">{% trans "Opening Balance" %}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4>{{ total_receipts|floatformat:2|default:"0.00" }}</h4>
                                    <p class="mb-0">{% trans "Total Receipts" %}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h4>{{ total_payments|floatformat:2|default:"0.00" }}</h4>
                                    <p class="mb-0">{% trans "Total Payments" %}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4>{{ closing_balance|floatformat:2|default:"0.00" }}</h4>
                                    <p class="mb-0">{% trans "Closing Balance" %}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Statement Details -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Date" %}</th>
                                    <th>{% trans "Description" %}</th>
                                    <th>{% trans "Reference" %}</th>
                                    <th>{% trans "Receipts" %}</th>
                                    <th>{% trans "Payments" %}</th>
                                    <th>{% trans "Balance" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Opening Balance Row -->
                                <tr class="table-info">
                                    <td>{{ date_from|date:"Y-m-d" }}</td>
                                    <td><strong>{% trans "Opening Balance" %}</strong></td>
                                    <td>-</td>
                                    <td class="text-right">-</td>
                                    <td class="text-right">-</td>
                                    <td class="text-right"><strong>{{ opening_balance|floatformat:2 }}</strong></td>
                                </tr>

                                {% for transaction in transactions %}
                                <tr>
                                    <td>{{ transaction.date|date:"Y-m-d" }}</td>
                                    <td>{{ transaction.description }}</td>
                                    <td>{{ transaction.reference|default:"-" }}</td>
                                    <td class="text-right">
                                        {% if transaction.type == 'receipt' %}
                                            <span class="text-success">{{ transaction.amount|floatformat:2 }}</span>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td class="text-right">
                                        {% if transaction.type == 'payment' %}
                                            <span class="text-danger">{{ transaction.amount|floatformat:2 }}</span>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td class="text-right">
                                        <strong class="{% if transaction.running_balance >= 0 %}text-success{% else %}text-danger{% endif %}">
                                            {{ transaction.running_balance|floatformat:2 }}
                                        </strong>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center text-muted">
                                        {% trans "No transactions found for the selected period" %}
                                    </td>
                                </tr>
                                {% endfor %}

                                <!-- Closing Balance Row -->
                                {% if transactions %}
                                <tr class="table-warning">
                                    <td>{{ date_to|date:"Y-m-d" }}</td>
                                    <td><strong>{% trans "Closing Balance" %}</strong></td>
                                    <td>-</td>
                                    <td class="text-right">-</td>
                                    <td class="text-right">-</td>
                                    <td class="text-right"><strong>{{ closing_balance|floatformat:2 }}</strong></td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Reconciliation Section -->
                    {% if selected_account %}
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>{% trans "Bank Reconciliation" %}</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>{% trans "Book Balance" %}</h6>
                                            <table class="table table-sm">
                                                <tr>
                                                    <td>{% trans "Balance per books" %}</td>
                                                    <td class="text-right">{{ book_balance|floatformat:2 }}</td>
                                                </tr>
                                                <tr>
                                                    <td>{% trans "Add: Deposits in transit" %}</td>
                                                    <td class="text-right">{{ deposits_in_transit|floatformat:2|default:"0.00" }}</td>
                                                </tr>
                                                <tr>
                                                    <td>{% trans "Less: Outstanding checks" %}</td>
                                                    <td class="text-right">({{ outstanding_checks|floatformat:2|default:"0.00" }})</td>
                                                </tr>
                                                <tr class="font-weight-bold">
                                                    <td>{% trans "Adjusted book balance" %}</td>
                                                    <td class="text-right">{{ adjusted_book_balance|floatformat:2 }}</td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>{% trans "Bank Balance" %}</h6>
                                            <table class="table table-sm">
                                                <tr>
                                                    <td>{% trans "Balance per bank statement" %}</td>
                                                    <td class="text-right">{{ bank_statement_balance|floatformat:2|default:"0.00" }}</td>
                                                </tr>
                                                <tr>
                                                    <td>{% trans "Add: Bank charges" %}</td>
                                                    <td class="text-right">{{ bank_charges|floatformat:2|default:"0.00" }}</td>
                                                </tr>
                                                <tr>
                                                    <td>{% trans "Less: Bank interest" %}</td>
                                                    <td class="text-right">({{ bank_interest|floatformat:2|default:"0.00" }})</td>
                                                </tr>
                                                <tr class="font-weight-bold">
                                                    <td>{% trans "Adjusted bank balance" %}</td>
                                                    <td class="text-right">{{ adjusted_bank_balance|floatformat:2 }}</td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                    
                                    {% if reconciliation_difference %}
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        {% trans "Reconciliation difference:" %} {{ reconciliation_difference|floatformat:2 }}
                                    </div>
                                    {% else %}
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle"></i>
                                        {% trans "Account is reconciled - balances match!" %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function generateStatement() {
    const account = document.getElementById('account-filter').value;
    const dateFrom = document.getElementById('date-from').value;
    const dateTo = document.getElementById('date-to').value;
    
    const params = new URLSearchParams();
    if (account) params.append('account', account);
    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);
    
    window.location.search = params.toString();
}
</script>
{% endblock %}