{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Cost Centers" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-building"></i> {% trans "Cost Centers" %}
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#addCostCenterModal">
                            <i class="fas fa-plus"></i> {% trans "Add Cost Center" %}
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Code" %}</th>
                                    <th>{% trans "Name" %}</th>
                                    <th>{% trans "Manager" %}</th>
                                    <th>{% trans "Budget Amount" %}</th>
                                    <th>{% trans "Total Expenses" %}</th>
                                    <th>{% trans "Utilization %" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for cost_center in cost_centers %}
                                <tr>
                                    <td>{{ cost_center.code }}</td>
                                    <td>{{ cost_center.name }}</td>
                                    <td>{{ cost_center.manager|default:"-" }}</td>
                                    <td>{{ cost_center.budget_amount|floatformat:2 }}</td>
                                    <td>{{ cost_center.total_expenses|floatformat:2 }}</td>
                                    <td>
                                        <div class="progress">
                                            <div class="progress-bar" role="progressbar" 
                                                 style="width: {{ cost_center.budget_utilization }}%"
                                                 aria-valuenow="{{ cost_center.budget_utilization }}" 
                                                 aria-valuemin="0" aria-valuemax="100">
                                                {{ cost_center.budget_utilization|floatformat:1 }}%
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-info" onclick="editCostCenter('{{ cost_center.id }}')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="deleteCostCenter('{{ cost_center.id }}')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center">{% trans "No cost centers found" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Cost Center Modal -->
<div class="modal fade" id="addCostCenterModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Add Cost Center" %}</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="addCostCenterForm">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="form-group">
                        <label>{% trans "Code" %}</label>
                        <input type="text" class="form-control" name="code" required>
                    </div>
                    <div class="form-group">
                        <label>{% trans "Name" %}</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="form-group">
                        <label>{% trans "Budget Amount" %}</label>
                        <input type="number" class="form-control" name="budget_amount" step="0.01" min="0">
                    </div>
                    <div class="form-group">
                        <label>{% trans "Description" %}</label>
                        <textarea class="form-control" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{% trans "Cancel" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "Save" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editCostCenter(id) {
    // Implementation for editing cost center
    console.log('Edit cost center:', id);
}

function deleteCostCenter(id) {
    if (confirm('{% trans "Are you sure you want to delete this cost center?" %}')) {
        // Implementation for deleting cost center
        console.log('Delete cost center:', id);
    }
}
</script>
{% endblock %}