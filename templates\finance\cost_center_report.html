{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Cost Center Report" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-bar"></i> {% trans "Cost Center Report" %}
                    </h3>
                    <div class="card-tools">
                        <a href="{% url 'finance:cost_center' %}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> {% trans "Back to Cost Centers" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <select class="form-control" id="cost-center-filter">
                                <option value="">{% trans "All Cost Centers" %}</option>
                                {% for cost_center in cost_centers %}
                                    <option value="{{ cost_center.id }}">{{ cost_center.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="date-from" value="{{ date_from|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="date-to" value="{{ date_to|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-primary" onclick="generateReport()">
                                <i class="fas fa-chart-bar"></i> {% trans "Generate Report" %}
                            </button>
                        </div>
                    </div>

                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4>{{ total_budget|floatformat:2|default:"0.00" }}</h4>
                                    <p class="mb-0">{% trans "Total Budget" %}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4>{{ total_spent|floatformat:2|default:"0.00" }}</h4>
                                    <p class="mb-0">{% trans "Total Spent" %}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4>{{ total_remaining|floatformat:2|default:"0.00" }}</h4>
                                    <p class="mb-0">{% trans "Remaining Budget" %}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4>{{ utilization_percentage|floatformat:1|default:"0.0" }}%</h4>
                                    <p class="mb-0">{% trans "Utilization" %}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Report -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Cost Center" %}</th>
                                    <th>{% trans "Manager" %}</th>
                                    <th>{% trans "Budget Amount" %}</th>
                                    <th>{% trans "Spent Amount" %}</th>
                                    <th>{% trans "Remaining" %}</th>
                                    <th>{% trans "Utilization %" %}</th>
                                    <th>{% trans "Status" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for report_item in report_data %}
                                <tr>
                                    <td>
                                        <strong>{{ report_item.cost_center.name }}</strong>
                                        <br><small class="text-muted">{{ report_item.cost_center.code }}</small>
                                    </td>
                                    <td>{{ report_item.cost_center.manager|default:"-" }}</td>
                                    <td class="text-right">{{ report_item.budget_amount|floatformat:2 }}</td>
                                    <td class="text-right">{{ report_item.spent_amount|floatformat:2 }}</td>
                                    <td class="text-right">
                                        <span class="{% if report_item.remaining < 0 %}text-danger{% else %}text-success{% endif %}">
                                            {{ report_item.remaining|floatformat:2 }}
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <div class="progress">
                                            <div class="progress-bar {% if report_item.utilization > 90 %}bg-danger{% elif report_item.utilization > 75 %}bg-warning{% else %}bg-success{% endif %}" 
                                                 role="progressbar" 
                                                 style="width: {{ report_item.utilization }}%"
                                                 aria-valuenow="{{ report_item.utilization }}" 
                                                 aria-valuemin="0" 
                                                 aria-valuemax="100">
                                                {{ report_item.utilization|floatformat:1 }}%
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        {% if report_item.utilization > 100 %}
                                            <span class="badge badge-danger">{% trans "Over Budget" %}</span>
                                        {% elif report_item.utilization > 90 %}
                                            <span class="badge badge-warning">{% trans "Near Limit" %}</span>
                                        {% elif report_item.utilization > 50 %}
                                            <span class="badge badge-success">{% trans "On Track" %}</span>
                                        {% else %}
                                            <span class="badge badge-info">{% trans "Under Utilized" %}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center text-muted">
                                        {% trans "No cost center data found for the selected period" %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function generateReport() {
    const costCenter = document.getElementById('cost-center-filter').value;
    const dateFrom = document.getElementById('date-from').value;
    const dateTo = document.getElementById('date-to').value;
    
    const params = new URLSearchParams();
    if (costCenter) params.append('cost_center', costCenter);
    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);
    
    window.location.search = params.toString();
}
</script>
{% endblock %}