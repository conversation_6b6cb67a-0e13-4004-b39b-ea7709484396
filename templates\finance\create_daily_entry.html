{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Create Daily Entry" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-plus"></i> {% trans "Create Daily Entry" %}
                    </h3>
                    <div class="card-tools">
                        <a href="{% url 'finance:daily_entries' %}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> {% trans "Back" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>{% trans "Entry Type" %} <span class="text-danger">*</span></label>
                                    <select class="form-control" name="entry_type" required>
                                        <option value="">{% trans "Select Type" %}</option>
                                        <option value="receipt">{% trans "Receipt" %}</option>
                                        <option value="payment">{% trans "Payment" %}</option>
                                        <option value="journal">{% trans "Journal Entry" %}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>{% trans "Date" %} <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" name="entry_date" value="{{ today|date:'Y-m-d' }}" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>{% trans "Reference Number" %}</label>
                                    <input type="text" class="form-control" name="reference_number" placeholder="{% trans 'Auto-generated if empty' %}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>{% trans "Amount" %} <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" name="amount" step="0.01" min="0" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>{% trans "Description" %} <span class="text-danger">*</span></label>
                            <textarea class="form-control" name="description" rows="3" required></textarea>
                        </div>

                        <div class="form-group">
                            <label>{% trans "Notes" %}</label>
                            <textarea class="form-control" name="notes" rows="2"></textarea>
                        </div>

                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> {% trans "Create Entry" %}
                            </button>
                            <a href="{% url 'finance:daily_entries' %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> {% trans "Cancel" %}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.needs-validation');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
});
</script>
{% endblock %}