{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Daily Entries" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-calendar-day"></i> {% trans "Daily Entries" %}
                    </h3>
                    <div class="card-tools">
                        <a href="{% url 'finance:create_daily_entry' %}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> {% trans "Create Entry" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <input type="date" class="form-control" id="date-filter" value="{{ today|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-4">
                            <select class="form-control" id="type-filter">
                                <option value="">{% trans "All Types" %}</option>
                                <option value="receipt">{% trans "Receipt" %}</option>
                                <option value="payment">{% trans "Payment" %}</option>
                                <option value="journal">{% trans "Journal" %}</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-info" onclick="loadDailyEntries()">
                                <i class="fas fa-search"></i> {% trans "Search" %}
                            </button>
                        </div>
                    </div>

                    <div id="daily-entries-content">
                        <div class="text-center py-5">
                            <i class="fas fa-calendar-day fa-5x text-muted mb-3"></i>
                            <h4 class="text-muted">{% trans "Daily Entries" %}</h4>
                            <p class="text-muted">{% trans "Select a date to view daily entries" %}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function loadDailyEntries() {
    const date = document.getElementById('date-filter').value;
    const type = document.getElementById('type-filter').value;
    const content = document.getElementById('daily-entries-content');
    
    content.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> {% trans "Loading..." %}</div>';
    
    // Simulate loading
    setTimeout(() => {
        content.innerHTML = `
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>{% trans "Time" %}</th>
                            <th>{% trans "Type" %}</th>
                            <th>{% trans "Reference" %}</th>
                            <th>{% trans "Description" %}</th>
                            <th>{% trans "Amount" %}</th>
                            <th>{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="6" class="text-center text-muted">
                                {% trans "No entries found for selected date" %}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        `;
    }, 1000);
}

document.addEventListener('DOMContentLoaded', function() {
    loadDailyEntries();
});
</script>
{% endblock %}