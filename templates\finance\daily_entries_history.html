{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Daily Entries History" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-history"></i> {% trans "Daily Entries History" %}
                    </h3>
                    <div class="card-tools">
                        <a href="{% url 'finance:daily_entries' %}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> {% trans "Back to Daily Entries" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="date-from" placeholder="{% trans 'From Date' %}">
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="date-to" placeholder="{% trans 'To Date' %}">
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="type-filter">
                                <option value="">{% trans "All Types" %}</option>
                                <option value="receipt">{% trans "Receipt" %}</option>
                                <option value="payment">{% trans "Payment" %}</option>
                                <option value="journal">{% trans "Journal" %}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-info" onclick="loadHistory()">
                                <i class="fas fa-search"></i> {% trans "Search" %}
                            </button>
                        </div>
                    </div>

                    <div id="history-content">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>{% trans "Date" %}</th>
                                        <th>{% trans "Time" %}</th>
                                        <th>{% trans "Type" %}</th>
                                        <th>{% trans "Reference" %}</th>
                                        <th>{% trans "Description" %}</th>
                                        <th>{% trans "Amount" %}</th>
                                        <th>{% trans "User" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for entry in entries %}
                                    <tr>
                                        <td>{{ entry.date|date:"Y-m-d" }}</td>
                                        <td>{{ entry.created_at|time:"H:i" }}</td>
                                        <td>
                                            <span class="badge badge-{% if entry.type == 'receipt' %}success{% elif entry.type == 'payment' %}danger{% else %}info{% endif %}">
                                                {{ entry.get_type_display }}
                                            </span>
                                        </td>
                                        <td>{{ entry.reference|default:"-" }}</td>
                                        <td>{{ entry.description|truncatechars:50 }}</td>
                                        <td class="text-right">{{ entry.amount|floatformat:2 }}</td>
                                        <td>{{ entry.created_by.get_full_name|default:entry.created_by.username }}</td>
                                        <td>
                                            <button class="btn btn-sm btn-info" onclick="viewEntry('{{ entry.id }}')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="8" class="text-center text-muted">
                                            {% trans "No entries found" %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function loadHistory() {
    const dateFrom = document.getElementById('date-from').value;
    const dateTo = document.getElementById('date-to').value;
    const type = document.getElementById('type-filter').value;
    
    // Build query parameters
    const params = new URLSearchParams();
    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);
    if (type) params.append('type', type);
    
    // Reload page with filters
    window.location.search = params.toString();
}

function viewEntry(id) {
    console.log('View entry:', id);
}
</script>
{% endblock %}