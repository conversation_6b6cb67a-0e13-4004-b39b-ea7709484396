{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Fee Calculation" %}{% endblock %}

{% block extra_css %}
<style>
    .fee-breakdown-card {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        margin-bottom: 1rem;
    }
    .fee-item {
        border-bottom: 1px solid #f8f9fa;
        padding: 0.75rem;
    }
    .fee-item:last-child {
        border-bottom: none;
    }
    .amount-display {
        font-weight: 600;
        font-size: 1.1em;
    }
    .discount-amount {
        color: #28a745;
    }
    .overdue {
        color: #dc3545;
    }
    .paid {
        color: #28a745;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-calculator"></i>
                        {% trans "Fee Calculation" %}
                    </h3>
                </div>
                <div class="card-body">
                    <form id="fee-calculation-form" method="post">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="student_id">{% trans "Student" %}</label>
                                    <select name="student_id" id="student_id" class="form-control" required>
                                        <option value="">{% trans "Select Student" %}</option>
                                        <!-- Students will be loaded via AJAX based on grade selection -->
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="grade_id">{% trans "Grade" %}</label>
                                    <select name="grade_id" id="grade_id" class="form-control">
                                        <option value="">{% trans "All Grades" %}</option>
                                        {% for grade in grades %}
                                        <option value="{{ grade.id }}">{{ grade.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="academic_year_id">{% trans "Academic Year" %}</label>
                                    <select name="academic_year_id" id="academic_year_id" class="form-control">
                                        <option value="">{% trans "Current Year" %}</option>
                                        {% for year in academic_years %}
                                        <option value="{{ year.id }}" {% if year.is_current %}selected{% endif %}>
                                            {{ year.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-calculator"></i>
                                    {% trans "Calculate Fees" %}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Fee Breakdown Results -->
    <div class="row" id="fee-results" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">{% trans "Fee Breakdown" %}</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-money-bill"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Total Amount" %}</span>
                                    <span class="info-box-number" id="total-amount">0.00</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-success">
                                <span class="info-box-icon"><i class="fas fa-percentage"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Total Discount" %}</span>
                                    <span class="info-box-number" id="total-discount">0.00</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-warning">
                                <span class="info-box-icon"><i class="fas fa-calculator"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Net Amount" %}</span>
                                    <span class="info-box-number" id="net-amount">0.00</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-primary">
                                <span class="info-box-icon"><i class="fas fa-exclamation"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Mandatory Fees" %}</span>
                                    <span class="info-box-number" id="mandatory-fees">0.00</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>{% trans "Fee Details" %}</h5>
                            <div class="fee-breakdown-card">
                                <div id="fee-details-container">
                                    <!-- Fee details will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Load students when grade changes
    $('#grade_id').change(function() {
        const gradeId = $(this).val();
        const studentSelect = $('#student_id');
        
        studentSelect.html('<option value="">{% trans "Loading..." %}</option>');
        
        if (gradeId) {
            $.ajax({
                url: '{% url "students:api_students_by_grade" %}',
                data: { grade_id: gradeId },
                success: function(data) {
                    studentSelect.html('<option value="">{% trans "Select Student" %}</option>');
                    data.students.forEach(function(student) {
                        studentSelect.append(
                            `<option value="${student.id}">${student.full_name} (${student.student_id})</option>`
                        );
                    });
                },
                error: function() {
                    studentSelect.html('<option value="">{% trans "Error loading students" %}</option>');
                }
            });
        } else {
            studentSelect.html('<option value="">{% trans "Select Student" %}</option>');
        }
    });

    // Handle form submission
    $('#fee-calculation-form').submit(function(e) {
        e.preventDefault();
        
        const formData = $(this).serialize();
        
        $.ajax({
            url: '{% url "finance:fee_calculation" %}',
            method: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    displayFeeBreakdown(response.fee_breakdown);
                    $('#fee-results').show();
                } else {
                    alert('Error: ' + response.error);
                }
            },
            error: function() {
                alert('{% trans "Error calculating fees" %}');
            }
        });
    });

    function displayFeeBreakdown(breakdown) {
        // Update summary boxes
        $('#total-amount').text(breakdown.total_amount);
        $('#total-discount').text(breakdown.total_discount);
        $('#net-amount').text(breakdown.net_amount);
        $('#mandatory-fees').text(breakdown.mandatory_fees);

        // Update fee details
        const container = $('#fee-details-container');
        container.empty();

        breakdown.fees.forEach(function(fee) {
            const statusClass = fee.is_paid ? 'paid' : (new Date(fee.due_date) < new Date() ? 'overdue' : '');
            const statusIcon = fee.is_paid ? 'fa-check-circle' : (new Date(fee.due_date) < new Date() ? 'fa-exclamation-triangle' : 'fa-clock');
            const statusText = fee.is_paid ? '{% trans "Paid" %}' : (new Date(fee.due_date) < new Date() ? '{% trans "Overdue" %}' : '{% trans "Pending" %}');

            const feeItem = `
                <div class="fee-item">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <strong>${fee.fee_type}</strong>
                            ${fee.is_mandatory ? '<span class="badge badge-warning ml-2">{% trans "Mandatory" %}</span>' : '<span class="badge badge-info ml-2">{% trans "Optional" %}</span>'}
                        </div>
                        <div class="col-md-2">
                            <span class="amount-display">${fee.amount}</span>
                        </div>
                        <div class="col-md-2">
                            <span class="discount-amount">-${fee.discount_amount}</span>
                        </div>
                        <div class="col-md-2">
                            <span class="amount-display">${fee.net_amount}</span>
                        </div>
                        <div class="col-md-2">
                            <i class="fas ${statusIcon} ${statusClass}"></i>
                            <span class="${statusClass}">${statusText}</span>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-12">
                            <small class="text-muted">{% trans "Due Date" %}: ${fee.due_date}</small>
                        </div>
                    </div>
                </div>
            `;
            container.append(feeItem);
        });
    }
});
</script>
{% endblock %}