{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Fee Collection Report" %}{% endblock %}

{% block extra_css %}
<style>
    .report-summary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .metric-card {
        background: white;
        border-radius: 8px;
        padding: 15px;
        margin: 10px 0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .metric-value {
        font-size: 2em;
        font-weight: bold;
        color: #2c3e50;
    }
    .metric-label {
        color: #7f8c8d;
        font-size: 0.9em;
        text-transform: uppercase;
    }
    .progress-custom {
        height: 25px;
        border-radius: 15px;
    }
    .table-responsive {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Report Filters -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-bar"></i>
                        {% trans "Fee Collection Report" %}
                    </h3>
                </div>
                <div class="card-body">
                    <form method="get" class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="academic_year">{% trans "Academic Year" %}</label>
                                <select name="academic_year" id="academic_year" class="form-control">
                                    <option value="">{% trans "Current Year" %}</option>
                                    {% for year in academic_years %}
                                    <option value="{{ year.id }}" {% if request.GET.academic_year == year.id|stringformat:"s" %}selected{% endif %}>
                                        {{ year.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="start_date">{% trans "Start Date" %}</label>
                                <input type="date" name="start_date" id="start_date" class="form-control" value="{{ request.GET.start_date }}">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="end_date">{% trans "End Date" %}</label>
                                <input type="date" name="end_date" id="end_date" class="form-control" value="{{ request.GET.end_date }}">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i>
                                        {% trans "Generate Report" %}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    {% if collection_report %}
    <!-- Report Summary -->
    <div class="row">
        <div class="col-12">
            <div class="report-summary">
                <h4>{% trans "Collection Summary" %}</h4>
                <p>
                    {% trans "Period" %}: {{ collection_report.period.start_date }} - {{ collection_report.period.end_date }}
                    {% if collection_report.academic_year %}
                    | {% trans "Academic Year" %}: {{ collection_report.academic_year.name }}
                    {% endif %}
                </p>
                
                <div class="row">
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-value">{{ collection_report.summary.total_fees_amount|floatformat:2 }}</div>
                            <div class="metric-label">{% trans "Total Fees" %}</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-value">{{ collection_report.summary.total_collected|floatformat:2 }}</div>
                            <div class="metric-label">{% trans "Collected" %}</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-value">{{ collection_report.summary.total_outstanding|floatformat:2 }}</div>
                            <div class="metric-label">{% trans "Outstanding" %}</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-value">{{ collection_report.summary.collection_rate|floatformat:1 }}%</div>
                            <div class="metric-label">{% trans "Collection Rate" %}</div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-12">
                        <div class="progress progress-custom">
                            <div class="progress-bar bg-success" role="progressbar" 
                                 style="width: {{ collection_report.summary.collection_rate }}%">
                                {{ collection_report.summary.collection_rate|floatformat:1 }}%
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Collection by Grade -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">{% trans "Collection by Grade" %}</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Grade" %}</th>
                                    <th>{% trans "Students" %}</th>
                                    <th>{% trans "Total Fees" %}</th>
                                    <th>{% trans "Collected" %}</th>
                                    <th>{% trans "Rate" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for grade_data in collection_report.by_grade %}
                                <tr>
                                    <td>{{ grade_data.grade }}</td>
                                    <td>{{ grade_data.students_count }}</td>
                                    <td>{{ grade_data.total_fees|floatformat:2 }}</td>
                                    <td>{{ grade_data.collected|floatformat:2 }}</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar" role="progressbar" 
                                                 style="width: {{ grade_data.collection_rate }}%">
                                                {{ grade_data.collection_rate|floatformat:1 }}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center">{% trans "No data available" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Collection by Fee Type -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">{% trans "Collection by Fee Type" %}</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Fee Type" %}</th>
                                    <th>{% trans "Total Fees" %}</th>
                                    <th>{% trans "Collected" %}</th>
                                    <th>{% trans "Outstanding" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for fee_data in collection_report.by_fee_type %}
                                <tr>
                                    <td>{{ fee_data.fee_type }}</td>
                                    <td>{{ fee_data.total_fees|floatformat:2 }}</td>
                                    <td>{{ fee_data.collected|floatformat:2 }}</td>
                                    <td>{{ fee_data.outstanding|floatformat:2 }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center">{% trans "No data available" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Methods and Recent Payments -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">{% trans "Payment Methods" %}</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Method" %}</th>
                                    <th>{% trans "Count" %}</th>
                                    <th>{% trans "Total Amount" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for method_data in collection_report.by_payment_method %}
                                <tr>
                                    <td>{{ method_data.payment_method|capfirst }}</td>
                                    <td>{{ method_data.count }}</td>
                                    <td>{{ method_data.total|floatformat:2 }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="3" class="text-center">{% trans "No payment data available" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Payments -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">{% trans "Recent Payments" %}</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Receipt" %}</th>
                                    <th>{% trans "Student" %}</th>
                                    <th>{% trans "Amount" %}</th>
                                    <th>{% trans "Date" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in collection_report.recent_payments %}
                                <tr>
                                    <td>{{ payment.receipt_number }}</td>
                                    <td>{{ payment.student.full_name }}</td>
                                    <td>{{ payment.amount|floatformat:2 }}</td>
                                    <td>{{ payment.payment_date }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center">{% trans "No recent payments" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Overdue Fees -->
    {% if collection_report.overdue_fees %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title text-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        {% trans "Overdue Fees" %}
                    </h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Student" %}</th>
                                    <th>{% trans "Fee Type" %}</th>
                                    <th>{% trans "Amount" %}</th>
                                    <th>{% trans "Due Date" %}</th>
                                    <th>{% trans "Days Overdue" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for fee in collection_report.overdue_fees %}
                                <tr>
                                    <td>{{ fee.student.full_name }}</td>
                                    <td>{{ fee.grade_fee.fee_type.name }}</td>
                                    <td>{{ fee.net_amount|floatformat:2 }}</td>
                                    <td>{{ fee.due_date }}</td>
                                    <td class="text-danger">
                                        {% now "Y-m-d" as today %}
                                        {{ fee.due_date|timesince:today|floatformat:0 }} {% trans "days" %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Export Options -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center">
                    <h5>{% trans "Export Report" %}</h5>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-success" onclick="exportReport('excel')">
                            <i class="fas fa-file-excel"></i>
                            {% trans "Export to Excel" %}
                        </button>
                        <button type="button" class="btn btn-danger" onclick="exportReport('pdf')">
                            <i class="fas fa-file-pdf"></i>
                            {% trans "Export to PDF" %}
                        </button>
                        <button type="button" class="btn btn-info" onclick="window.print()">
                            <i class="fas fa-print"></i>
                            {% trans "Print Report" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function exportReport(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);
    
    const exportUrl = '{% url "finance:fee_collection_report_export" %}?' + params.toString();
    window.open(exportUrl, '_blank');
}

// Set default dates if not provided
$(document).ready(function() {
    if (!$('#start_date').val()) {
        const startOfYear = new Date(new Date().getFullYear(), 0, 1);
        $('#start_date').val(startOfYear.toISOString().split('T')[0]);
    }
    
    if (!$('#end_date').val()) {
        const today = new Date();
        $('#end_date').val(today.toISOString().split('T')[0]);
    }
});
</script>
{% endblock %}