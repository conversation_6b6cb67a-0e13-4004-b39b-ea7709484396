{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Fee Items" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-money-bill"></i> {% trans "Fee Items" %}
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#addFeeTypeModal">
                            <i class="fas fa-plus"></i> {% trans "Add Fee Type" %}
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Fee Type" %}</th>
                                    <th>{% trans "Description" %}</th>
                                    <th>{% trans "Account" %}</th>
                                    <th>{% trans "Is Mandatory" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for fee_type in fee_types %}
                                <tr>
                                    <td>
                                        <strong>{{ fee_type.name }}</strong>
                                        {% if fee_type.name_ar %}
                                            <br><small class="text-muted">{{ fee_type.name_ar }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ fee_type.description|default:"-" }}</td>
                                    <td>{{ fee_type.account.full_name }}</td>
                                    <td>
                                        {% if fee_type.is_mandatory %}
                                            <span class="badge badge-success">{% trans "Yes" %}</span>
                                        {% else %}
                                            <span class="badge badge-secondary">{% trans "No" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-info" onclick="viewFeeType('{{ fee_type.id }}')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" onclick="editFeeType('{{ fee_type.id }}')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="deleteFeeType('{{ fee_type.id }}')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center">
                                        {% trans "No fee types found" %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Fee Type Modal -->
<div class="modal fade" id="addFeeTypeModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Add Fee Type" %}</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="addFeeTypeForm">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="form-group">
                        <label>{% trans "Fee Type Name" %}</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="form-group">
                        <label>{% trans "Fee Type Name (Arabic)" %}</label>
                        <input type="text" class="form-control" name="name_ar">
                    </div>
                    <div class="form-group">
                        <label>{% trans "Account" %}</label>
                        <select class="form-control" name="account" required>
                            <option value="">{% trans "Select Account" %}</option>
                            {% for account in accounts %}
                                <option value="{{ account.id }}">{{ account.full_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label>{% trans "Description" %}</label>
                        <textarea class="form-control" name="description" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" name="is_mandatory" id="is_mandatory">
                            <label class="custom-control-label" for="is_mandatory">
                                {% trans "Is Mandatory" %}
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{% trans "Cancel" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "Save" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function viewFeeType(id) {
    // Implementation for viewing fee type
    console.log('View fee type:', id);
}

function editFeeType(id) {
    // Implementation for editing fee type
    console.log('Edit fee type:', id);
}

function deleteFeeType(id) {
    if (confirm('{% trans "Are you sure you want to delete this fee type?" %}')) {
        // Implementation for deleting fee type
        console.log('Delete fee type:', id);
    }
}

document.getElementById('addFeeTypeForm').addEventListener('submit', function(e) {
    e.preventDefault();
    // Implementation for adding fee type
    console.log('Add fee type form submitted');
});
</script>
{% endblock %}