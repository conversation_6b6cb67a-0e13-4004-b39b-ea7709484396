{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Financial Years" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-calendar"></i> {% trans "Financial Years" %}
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#addFinancialYearModal">
                            <i class="fas fa-plus"></i> {% trans "Add Financial Year" %}
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Name" %}</th>
                                    <th>{% trans "Start Date" %}</th>
                                    <th>{% trans "End Date" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for year in financial_years %}
                                <tr>
                                    <td>
                                        <strong>{{ year.name }}</strong>
                                        {% if year.is_current %}
                                            <span class="badge badge-success ml-2">{% trans "Current" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ year.start_date|date:"Y-m-d" }}</td>
                                    <td>{{ year.end_date|date:"Y-m-d" }}</td>
                                    <td>
                                        {% if year.is_active %}
                                            <span class="badge badge-success">{% trans "Active" %}</span>
                                        {% else %}
                                            <span class="badge badge-secondary">{% trans "Inactive" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-info" onclick="viewFinancialYear('{{ year.id }}')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-warning" onclick="editFinancialYear('{{ year.id }}')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            {% if not year.is_current %}
                                            <button type="button" class="btn btn-sm btn-success" onclick="setCurrentYear('{{ year.id }}')">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center">
                                        {% trans "No financial years found" %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Financial Year Modal -->
<div class="modal fade" id="addFinancialYearModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Add Financial Year" %}</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="addFinancialYearForm">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="form-group">
                        <label>{% trans "Name" %}</label>
                        <input type="text" class="form-control" name="name" required placeholder="e.g., 2025">
                    </div>
                    <div class="form-group">
                        <label>{% trans "Start Date" %}</label>
                        <input type="date" class="form-control" name="start_date" required>
                    </div>
                    <div class="form-group">
                        <label>{% trans "End Date" %}</label>
                        <input type="date" class="form-control" name="end_date" required>
                    </div>
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" name="is_current" id="is_current">
                            <label class="custom-control-label" for="is_current">
                                {% trans "Set as Current Year" %}
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{% trans "Cancel" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "Save" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function viewFinancialYear(id) {
    console.log('View financial year:', id);
}

function editFinancialYear(id) {
    console.log('Edit financial year:', id);
}

function setCurrentYear(id) {
    if (confirm('{% trans "Set this as the current financial year?" %}')) {
        console.log('Set current year:', id);
    }
}

document.getElementById('addFinancialYearForm').addEventListener('submit', function(e) {
    e.preventDefault();
    console.log('Add financial year form submitted');
});
</script>
{% endblock %}