{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Grade Fees" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-graduation-cap"></i> {% trans "Grade Fees" %}
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#addGradeFeeModal">
                            <i class="fas fa-plus"></i> {% trans "Add Grade Fee" %}
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <select class="form-control" id="grade-filter">
                                <option value="">{% trans "All Grades" %}</option>
                                {% for grade in grades %}
                                    <option value="{{ grade.id }}">{{ grade.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <select class="form-control" id="fee-type-filter">
                                <option value="">{% trans "All Fee Types" %}</option>
                                {% for fee_type in fee_types %}
                                    <option value="{{ fee_type.id }}">{{ fee_type.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <select class="form-control" id="academic-year-filter">
                                <option value="">{% trans "All Academic Years" %}</option>
                                {% for year in academic_years %}
                                    <option value="{{ year.id }}" {% if year.is_current %}selected{% endif %}>
                                        {{ year.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Grade" %}</th>
                                    <th>{% trans "Fee Type" %}</th>
                                    <th>{% trans "Academic Year" %}</th>
                                    <th>{% trans "Amount" %}</th>
                                    <th>{% trans "Due Date" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for grade_fee in grade_fees %}
                                <tr>
                                    <td>
                                        <strong>{{ grade_fee.grade.name }}</strong>
                                        <br><small class="text-muted">{{ grade_fee.grade.level }}</small>
                                    </td>
                                    <td>
                                        {{ grade_fee.fee_type.name }}
                                        {% if grade_fee.fee_type.is_mandatory %}
                                            <span class="badge badge-success badge-sm">{% trans "Mandatory" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ grade_fee.academic_year.name }}</td>
                                    <td class="text-right">
                                        <strong>{{ grade_fee.amount|floatformat:2 }}</strong>
                                    </td>
                                    <td>
                                        {% if grade_fee.due_date %}
                                            {{ grade_fee.due_date|date:"Y-m-d" }}
                                        {% else %}
                                            <span class="text-muted">{% trans "No due date" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-info" onclick="viewGradeFee('{{ grade_fee.id }}')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-warning" onclick="editGradeFee('{{ grade_fee.id }}')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" onclick="deleteGradeFee('{{ grade_fee.id }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center">
                                        {% trans "No grade fees found" %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if is_paginated %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1">&laquo; {% trans "First" %}</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">{% trans "Last" %} &raquo;</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Grade Fee Modal -->
<div class="modal fade" id="addGradeFeeModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Add Grade Fee" %}</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="addGradeFeeForm">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="form-group">
                        <label>{% trans "Grade" %}</label>
                        <select class="form-control" name="grade" required>
                            <option value="">{% trans "Select Grade" %}</option>
                            {% for grade in grades %}
                                <option value="{{ grade.id }}">{{ grade.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label>{% trans "Fee Type" %}</label>
                        <select class="form-control" name="fee_type" required>
                            <option value="">{% trans "Select Fee Type" %}</option>
                            {% for fee_type in fee_types %}
                                <option value="{{ fee_type.id }}">{{ fee_type.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label>{% trans "Academic Year" %}</label>
                        <select class="form-control" name="academic_year" required>
                            {% for year in academic_years %}
                                <option value="{{ year.id }}" {% if year.is_current %}selected{% endif %}>
                                    {{ year.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label>{% trans "Amount" %}</label>
                        <input type="number" class="form-control" name="amount" step="0.01" min="0" required>
                    </div>
                    <div class="form-group">
                        <label>{% trans "Due Date" %}</label>
                        <input type="date" class="form-control" name="due_date">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{% trans "Cancel" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "Save" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function viewGradeFee(id) {
    // Implementation for viewing grade fee
    console.log('View grade fee:', id);
}

function editGradeFee(id) {
    // Implementation for editing grade fee
    console.log('Edit grade fee:', id);
}

function deleteGradeFee(id) {
    if (confirm('{% trans "Are you sure you want to delete this grade fee?" %}')) {
        // Implementation for deleting grade fee
        console.log('Delete grade fee:', id);
    }
}

// Filter functionality
document.addEventListener('DOMContentLoaded', function() {
    const gradeFilter = document.getElementById('grade-filter');
    const feeTypeFilter = document.getElementById('fee-type-filter');
    const academicYearFilter = document.getElementById('academic-year-filter');

    function applyFilters() {
        const params = new URLSearchParams();
        
        if (gradeFilter.value) params.append('grade', gradeFilter.value);
        if (feeTypeFilter.value) params.append('fee_type', feeTypeFilter.value);
        if (academicYearFilter.value) params.append('academic_year', academicYearFilter.value);
        
        window.location.search = params.toString();
    }

    gradeFilter.addEventListener('change', applyFilters);
    feeTypeFilter.addEventListener('change', applyFilters);
    academicYearFilter.addEventListener('change', applyFilters);
});

document.getElementById('addGradeFeeForm').addEventListener('submit', function(e) {
    e.preventDefault();
    // Implementation for adding grade fee
    console.log('Add grade fee form submitted');
});
</script>
{% endblock %}