{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Income Statement" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-line"></i> {% trans "Income Statement" %}
                    </h3>
                    <div class="card-tools">
                        <button class="btn btn-primary btn-sm" onclick="window.print()">
                            <i class="fas fa-print"></i> {% trans "Print" %}
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <h4>{{ school.name }}</h4>
                        <h5>{% trans "Income Statement" %}</h5>
                        <p>{% trans "For the period" %} {{ period_start|date:"F d, Y" }} {% trans "to" %} {{ period_end|date:"F d, Y" }}</p>
                    </div>

                    <div class="row">
                        <div class="col-md-8 offset-md-2">
                            <!-- Revenue -->
                            <h5 class="text-success">{% trans "REVENUE" %}</h5>
                            <table class="table table-sm">
                                <tbody>
                                    {% for revenue in revenues %}
                                    <tr>
                                        <td>{{ revenue.name }}</td>
                                        <td class="text-right">{{ revenue.amount|floatformat:2 }}</td>
                                    </tr>
                                    {% endfor %}
                                    <tr class="font-weight-bold border-top">
                                        <td>{% trans "Total Revenue" %}</td>
                                        <td class="text-right">{{ total_revenue|floatformat:2 }}</td>
                                    </tr>
                                </tbody>
                            </table>

                            <!-- Expenses -->
                            <h5 class="text-danger mt-4">{% trans "EXPENSES" %}</h5>
                            <table class="table table-sm">
                                <tbody>
                                    {% for expense in expenses %}
                                    <tr>
                                        <td>{{ expense.name }}</td>
                                        <td class="text-right">{{ expense.amount|floatformat:2 }}</td>
                                    </tr>
                                    {% endfor %}
                                    <tr class="font-weight-bold border-top">
                                        <td>{% trans "Total Expenses" %}</td>
                                        <td class="text-right">{{ total_expenses|floatformat:2 }}</td>
                                    </tr>
                                </tbody>
                            </table>

                            <!-- Net Income -->
                            <table class="table table-sm">
                                <tbody>
                                    <tr class="font-weight-bold border-top border-bottom">
                                        <td class="h5">{% trans "Net Income" %}</td>
                                        <td class="text-right h5 {% if net_income >= 0 %}text-success{% else %}text-danger{% endif %}">
                                            {{ net_income|floatformat:2 }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}