{% extends 'base.html' %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}
    {% if object %}
        {% trans "Edit Invoice" %}
    {% else %}
        {% trans "Create Invoice" %}
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        {% if object %}
                            <i class="fas fa-edit"></i> {% trans "Edit Invoice" %}
                        {% else %}
                            <i class="fas fa-plus"></i> {% trans "Create Invoice" %}
                        {% endif %}
                    </h3>
                    <div class="card-tools">
                        <a href="{% url 'finance:invoices' %}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> {% trans "Back to Invoices" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.student.id_for_label }}" class="form-label">
                                        {{ form.student.label }} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.student }}
                                    {% if form.student.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.student.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.invoice_number.id_for_label }}" class="form-label">
                                        {{ form.invoice_number.label }}
                                    </label>
                                    {{ form.invoice_number }}
                                    {% if form.invoice_number.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.invoice_number.errors.0 }}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        {% trans "Leave blank to auto-generate" %}
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.invoice_date.id_for_label }}" class="form-label">
                                        {{ form.invoice_date.label }} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.invoice_date }}
                                    {% if form.invoice_date.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.invoice_date.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.due_date.id_for_label }}" class="form-label">
                                        {{ form.due_date.label }} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.due_date }}
                                    {% if form.due_date.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.due_date.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                {{ form.description.label }}
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.description.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Invoice Items -->
                        <div class="form-group">
                            <label class="form-label">{% trans "Invoice Items" %}</label>
                            <div id="invoice-items-container">
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="invoice-items-table">
                                        <thead>
                                            <tr>
                                                <th>{% trans "Description" %}</th>
                                                <th>{% trans "Quantity" %}</th>
                                                <th>{% trans "Unit Price" %}</th>
                                                <th>{% trans "Total" %}</th>
                                                <th>{% trans "Actions" %}</th>
                                            </tr>
                                        </thead>
                                        <tbody id="invoice-items-tbody">
                                            <!-- Invoice items will be added here -->
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <th colspan="3" class="text-right">{% trans "Total Amount" %}:</th>
                                                <th id="total-amount">0.00</th>
                                                <th></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                                <button type="button" class="btn btn-success btn-sm" onclick="addInvoiceItem()">
                                    <i class="fas fa-plus"></i> {% trans "Add Item" %}
                                </button>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">
                                {{ form.notes.label }}
                            </label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.notes.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                {% if object %}
                                    {% trans "Update Invoice" %}
                                {% else %}
                                    {% trans "Create Invoice" %}
                                {% endif %}
                            </button>
                            <a href="{% url 'finance:invoices' %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> {% trans "Cancel" %}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let itemCounter = 0;

document.addEventListener('DOMContentLoaded', function() {
    // Add form validation
    const form = document.querySelector('.needs-validation');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // Add initial invoice item
    addInvoiceItem();
});

function addInvoiceItem() {
    const tbody = document.getElementById('invoice-items-tbody');
    const row = document.createElement('tr');
    row.innerHTML = `
        <td>
            <input type="text" class="form-control" name="items[${itemCounter}][description]" required>
        </td>
        <td>
            <input type="number" class="form-control" name="items[${itemCounter}][quantity]" 
                   min="1" value="1" onchange="calculateItemTotal(${itemCounter})" required>
        </td>
        <td>
            <input type="number" class="form-control" name="items[${itemCounter}][unit_price]" 
                   step="0.01" min="0" onchange="calculateItemTotal(${itemCounter})" required>
        </td>
        <td>
            <span class="item-total" id="item-total-${itemCounter}">0.00</span>
        </td>
        <td>
            <button type="button" class="btn btn-danger btn-sm" onclick="removeInvoiceItem(this)">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;
    tbody.appendChild(row);
    itemCounter++;
}

function removeInvoiceItem(button) {
    const row = button.closest('tr');
    row.remove();
    calculateTotalAmount();
}

function calculateItemTotal(index) {
    const quantityInput = document.querySelector(`input[name="items[${index}][quantity]"]`);
    const unitPriceInput = document.querySelector(`input[name="items[${index}][unit_price]"]`);
    const totalSpan = document.getElementById(`item-total-${index}`);
    
    const quantity = parseFloat(quantityInput.value) || 0;
    const unitPrice = parseFloat(unitPriceInput.value) || 0;
    const total = quantity * unitPrice;
    
    totalSpan.textContent = total.toFixed(2);
    calculateTotalAmount();
}

function calculateTotalAmount() {
    const itemTotals = document.querySelectorAll('.item-total');
    let total = 0;
    
    itemTotals.forEach(span => {
        total += parseFloat(span.textContent) || 0;
    });
    
    document.getElementById('total-amount').textContent = total.toFixed(2);
}
</script>
{% endblock %}