{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Invoice Generation" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-file-invoice"></i>
                        {% trans "Invoice Generation" %}
                    </h3>
                </div>
                <div class="card-body">
                    <!-- Navigation Tabs -->
                    <ul class="nav nav-tabs" id="invoiceTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <a class="nav-link active" id="single-tab" data-toggle="tab" href="#single" role="tab">
                                {% trans "Single Student Invoice" %}
                            </a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" id="bulk-tab" data-toggle="tab" href="#bulk" role="tab">
                                {% trans "Bulk Invoice Generation" %}
                            </a>
                        </li>
                    </ul>

                    <div class="tab-content" id="invoiceTabContent">
                        <!-- Single Student Invoice Tab -->
                        <div class="tab-pane fade show active" id="single" role="tabpanel">
                            <form method="post" class="mt-3">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="generate_single">
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="student_id">{% trans "Student" %} *</label>
                                            <select name="student_id" id="student_id" class="form-control" required>
                                                <option value="">{% trans "Select Student" %}</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="academic_year_id">{% trans "Academic Year" %}</label>
                                            <select name="academic_year_id" id="academic_year_id" class="form-control">
                                                <option value="">{% trans "Current Year" %}</option>
                                                {% for year in academic_years %}
                                                <option value="{{ year.id }}" {% if year.is_current %}selected{% endif %}>
                                                    {{ year.name }}
                                                </option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>{% trans "Fee Types" %}</label>
                                            <div class="form-check-list" style="max-height: 200px; overflow-y: auto; border: 1px solid #ced4da; padding: 10px; border-radius: 4px;">
                                                {% for fee_type in fee_types %}
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="fee_types" value="{{ fee_type.id }}" id="fee_type_{{ fee_type.id }}">
                                                    <label class="form-check-label" for="fee_type_{{ fee_type.id }}">
                                                        {{ fee_type.name }}
                                                        {% if fee_type.is_mandatory %}
                                                        <span class="badge badge-warning">{% trans "Mandatory" %}</span>
                                                        {% endif %}
                                                    </label>
                                                </div>
                                                {% endfor %}
                                            </div>
                                            <small class="form-text text-muted">{% trans "Leave empty to include all unpaid fees" %}</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="due_date">{% trans "Due Date" %}</label>
                                            <input type="date" name="due_date" id="due_date" class="form-control">
                                            <small class="form-text text-muted">{% trans "Leave empty for default (30 days from today)" %}</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-file-invoice"></i>
                                            {% trans "Generate Invoice" %}
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Bulk Invoice Generation Tab -->
                        <div class="tab-pane fade" id="bulk" role="tabpanel">
                            <form method="post" class="mt-3">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="generate_bulk">
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="grade_id">{% trans "Grade" %} *</label>
                                            <select name="grade_id" id="grade_id" class="form-control" required>
                                                <option value="">{% trans "Select Grade" %}</option>
                                                {% for grade in grades %}
                                                <option value="{{ grade.id }}">{{ grade.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="bulk_academic_year_id">{% trans "Academic Year" %}</label>
                                            <select name="academic_year_id" id="bulk_academic_year_id" class="form-control">
                                                <option value="">{% trans "Current Year" %}</option>
                                                {% for year in academic_years %}
                                                <option value="{{ year.id }}" {% if year.is_current %}selected{% endif %}>
                                                    {{ year.name }}
                                                </option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label>{% trans "Fee Types" %}</label>
                                            <div class="form-check-list" style="max-height: 200px; overflow-y: auto; border: 1px solid #ced4da; padding: 10px; border-radius: 4px;">
                                                {% for fee_type in fee_types %}
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="fee_types" value="{{ fee_type.id }}" id="bulk_fee_type_{{ fee_type.id }}">
                                                    <label class="form-check-label" for="bulk_fee_type_{{ fee_type.id }}">
                                                        {{ fee_type.name }}
                                                        {% if fee_type.is_mandatory %}
                                                        <span class="badge badge-warning">{% trans "Mandatory" %}</span>
                                                        {% endif %}
                                                    </label>
                                                </div>
                                                {% endfor %}
                                            </div>
                                            <small class="form-text text-muted">{% trans "Leave empty to include all unpaid fees" %}</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12">
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle"></i>
                                            {% trans "This will generate invoices for all students in the selected grade who have unpaid fees." %}
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-warning" onclick="return confirm('{% trans "Are you sure you want to generate bulk invoices?" %}')">
                                            <i class="fas fa-file-invoice-dollar"></i>
                                            {% trans "Generate Bulk Invoices" %}
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Invoices -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">{% trans "Recent Invoices" %}</h4>
                    <div class="card-tools">
                        <a href="{% url 'finance:invoices' %}" class="btn btn-sm btn-primary">
                            {% trans "View All Invoices" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Invoice Number" %}</th>
                                    <th>{% trans "Student" %}</th>
                                    <th>{% trans "Date" %}</th>
                                    <th>{% trans "Amount" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody id="recent-invoices">
                                <!-- Recent invoices will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Load students for single invoice generation
    function loadStudents(gradeId = null) {
        const studentSelect = $('#student_id');
        studentSelect.html('<option value="">{% trans "Loading..." %}</option>');
        
        const url = gradeId ? 
            '{% url "students:api_students_by_grade" %}?grade_id=' + gradeId :
            '{% url "students:api_all_students" %}';
        
        $.ajax({
            url: url,
            success: function(data) {
                studentSelect.html('<option value="">{% trans "Select Student" %}</option>');
                data.students.forEach(function(student) {
                    studentSelect.append(
                        `<option value="${student.id}">${student.full_name} (${student.student_id}) - ${student.grade}</option>`
                    );
                });
            },
            error: function() {
                studentSelect.html('<option value="">{% trans "Error loading students" %}</option>');
            }
        });
    }

    // Load all students initially
    loadStudents();

    // Load recent invoices
    function loadRecentInvoices() {
        $.ajax({
            url: '{% url "finance:api_recent_invoices" %}',
            success: function(data) {
                const tbody = $('#recent-invoices');
                tbody.empty();
                
                if (data.invoices.length === 0) {
                    tbody.append('<tr><td colspan="6" class="text-center">{% trans "No recent invoices found" %}</td></tr>');
                    return;
                }
                
                data.invoices.forEach(function(invoice) {
                    const statusClass = {
                        'draft': 'secondary',
                        'sent': 'primary',
                        'paid': 'success',
                        'overdue': 'danger',
                        'cancelled': 'dark'
                    }[invoice.status] || 'secondary';
                    
                    const row = `
                        <tr>
                            <td>${invoice.invoice_number}</td>
                            <td>${invoice.student_name}</td>
                            <td>${invoice.invoice_date}</td>
                            <td>${invoice.total_amount}</td>
                            <td><span class="badge badge-${statusClass}">${invoice.status}</span></td>
                            <td>
                                <a href="/finance/invoices/${invoice.id}/" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                    `;
                    tbody.append(row);
                });
            },
            error: function() {
                $('#recent-invoices').html('<tr><td colspan="6" class="text-center text-danger">{% trans "Error loading recent invoices" %}</td></tr>');
            }
        });
    }

    // Load recent invoices on page load
    loadRecentInvoices();

    // Handle tab switching
    $('#invoiceTabs a').on('click', function (e) {
        e.preventDefault();
        $(this).tab('show');
    });

    // Set default due date to 30 days from today
    const today = new Date();
    const defaultDueDate = new Date(today.getTime() + (30 * 24 * 60 * 60 * 1000));
    const formattedDate = defaultDueDate.toISOString().split('T')[0];
    $('#due_date').val(formattedDate);
});
</script>
{% endblock %}