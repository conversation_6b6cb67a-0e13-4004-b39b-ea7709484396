{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Invoices" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-file-invoice"></i> {% trans "Invoices" %}
                    </h3>
                    <div class="card-tools">
                        <a href="{% url 'finance:add_invoice' %}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> {% trans "Create Invoice" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <input type="text" class="form-control" placeholder="{% trans 'Search by invoice number...' %}" id="search-input">
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="status-filter">
                                <option value="">{% trans "All Statuses" %}</option>
                                <option value="draft">{% trans "Draft" %}</option>
                                <option value="sent">{% trans "Sent" %}</option>
                                <option value="paid">{% trans "Paid" %}</option>
                                <option value="overdue">{% trans "Overdue" %}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="date-from" placeholder="{% trans 'From Date' %}">
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="date-to" placeholder="{% trans 'To Date' %}">
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Invoice #" %}</th>
                                    <th>{% trans "Student" %}</th>
                                    <th>{% trans "Date" %}</th>
                                    <th>{% trans "Due Date" %}</th>
                                    <th>{% trans "Amount" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in invoices %}
                                <tr>
                                    <td>
                                        <strong>{{ invoice.invoice_number }}</strong>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ invoice.student.full_name }}</strong><br>
                                            <small class="text-muted">{{ invoice.student.student_id }}</small>
                                        </div>
                                    </td>
                                    <td>{{ invoice.invoice_date|date:"Y-m-d" }}</td>
                                    <td>
                                        {{ invoice.due_date|date:"Y-m-d" }}
                                        {% if invoice.is_overdue %}
                                            <br><small class="text-danger">{% trans "Overdue" %}</small>
                                        {% endif %}
                                    </td>
                                    <td class="text-right">
                                        <strong>{{ invoice.total_amount|floatformat:2 }}</strong>
                                    </td>
                                    <td>
                                        {% if invoice.status == 'draft' %}
                                            <span class="badge badge-secondary">{% trans "Draft" %}</span>
                                        {% elif invoice.status == 'sent' %}
                                            <span class="badge badge-info">{% trans "Sent" %}</span>
                                        {% elif invoice.status == 'paid' %}
                                            <span class="badge badge-success">{% trans "Paid" %}</span>
                                        {% elif invoice.status == 'overdue' %}
                                            <span class="badge badge-danger">{% trans "Overdue" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-info" onclick="viewInvoice('{{ invoice.id }}')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-warning" onclick="editInvoice('{{ invoice.id }}')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-success" onclick="printInvoice('{{ invoice.id }}')">
                                                <i class="fas fa-print"></i>
                                            </button>
                                            {% if invoice.status != 'paid' %}
                                            <button type="button" class="btn btn-sm btn-primary" onclick="markAsPaid('{{ invoice.id }}')">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center">
                                        {% trans "No invoices found" %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if is_paginated %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1">&laquo; {% trans "First" %}</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">{% trans "Last" %} &raquo;</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function viewInvoice(invoiceId) {
    // Implementation for viewing invoice
    window.location.href = `/finance/invoices/${invoiceId}/`;
}

function editInvoice(invoiceId) {
    // Implementation for editing invoice
    window.location.href = `/finance/invoices/${invoiceId}/edit/`;
}

function printInvoice(invoiceId) {
    // Implementation for printing invoice
    window.open(`/finance/invoices/${invoiceId}/print/`, '_blank');
}

function markAsPaid(invoiceId) {
    if (confirm('{% trans "Mark this invoice as paid?" %}')) {
        // Implementation for marking invoice as paid
        fetch(`/finance/invoices/${invoiceId}/mark-paid/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('{% trans "Error marking invoice as paid" %}');
            }
        });
    }
}

// Filter functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search-input');
    const statusFilter = document.getElementById('status-filter');
    const dateFromFilter = document.getElementById('date-from');
    const dateToFilter = document.getElementById('date-to');

    function applyFilters() {
        const params = new URLSearchParams();
        
        if (searchInput.value) params.append('search', searchInput.value);
        if (statusFilter.value) params.append('status', statusFilter.value);
        if (dateFromFilter.value) params.append('date_from', dateFromFilter.value);
        if (dateToFilter.value) params.append('date_to', dateToFilter.value);
        
        window.location.search = params.toString();
    }

    searchInput.addEventListener('input', debounce(applyFilters, 500));
    statusFilter.addEventListener('change', applyFilters);
    dateFromFilter.addEventListener('change', applyFilters);
    dateToFilter.addEventListener('change', applyFilters);
});

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
{% endblock %}