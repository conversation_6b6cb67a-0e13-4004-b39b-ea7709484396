{% extends 'base.html' %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{% trans "Journal Entry Interface" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-book"></i>
                        {% trans "Journal Entry Interface" %}
                    </h3>
                    <div>
                        <a href="{% url 'finance:transactions_list' %}" class="btn btn-secondary">
                            <i class="fas fa-list"></i>
                            {% trans "View Transactions" %}
                        </a>
                        <a href="{% url 'finance:add_transaction' %}" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            {% trans "New Transaction" %}
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Quick Stats -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-info">
                                    <i class="fas fa-exchange-alt"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Today's Transactions" %}</span>
                                    <span class="info-box-number">{{ today_transactions }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-warning">
                                    <i class="fas fa-clock"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Pending Approval" %}</span>
                                    <span class="info-box-number">{{ pending_approval }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-secondary">
                                    <i class="fas fa-edit"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Draft Transactions" %}</span>
                                    <span class="info-box-number">{{ draft_transactions }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-success">
                                    <i class="fas fa-check"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans "Posted Today" %}</span>
                                    <span class="info-box-number">{{ posted_today }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Entry Form -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-plus-circle"></i>
                                        {% trans "Quick Journal Entry" %}
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <form id="quick-entry-form" method="post">
                                        {% csrf_token %}
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label for="entry_date">{% trans "Date" %}</label>
                                                <input type="date" class="form-control" id="entry_date" name="entry_date" 
                                                       value="{{ today|date:'Y-m-d' }}" required>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="entry_type">{% trans "Entry Type" %}</label>
                                                <select class="form-control" id="entry_type" name="entry_type">
                                                    <option value="manual">{% trans "Manual Entry" %}</option>
                                                    <option value="adjustment">{% trans "Adjustment Entry" %}</option>
                                                </select>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="description">{% trans "Description" %}</label>
                                            <textarea class="form-control" id="description" name="description" 
                                                    rows="2" placeholder="{% trans 'Enter transaction description' %}" required></textarea>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="reference">{% trans "Reference" %}</label>
                                            <input type="text" class="form-control" id="reference" name="reference" 
                                                   placeholder="{% trans 'Optional reference number' %}">
                                        </div>
                                        
                                        <!-- Journal Entries -->
                                        <div class="card">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">{% trans "Journal Entries" %}</h6>
                                                <button type="button" id="add-journal-entry" class="btn btn-sm btn-success">
                                                    <i class="fas fa-plus"></i>
                                                    {% trans "Add Entry" %}
                                                </button>
                                            </div>
                                            <div class="card-body">
                                                <div id="journal-entries-container">
                                                    <!-- Entries will be added here -->
                                                </div>
                                                
                                                <!-- Totals -->
                                                <div class="row mt-3">
                                                    <div class="col-md-6">
                                                        <div class="text-center">
                                                            <strong>{% trans "Total Debits" %}: <span id="total-debits">0.00</span></strong>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="text-center">
                                                            <strong>{% trans "Total Credits" %}: <span id="total-credits">0.00</span></strong>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="alert mt-2" id="balance-alert">
                                                    <i class="fas fa-info-circle"></i>
                                                    <span id="balance-message">{% trans "Add entries to see balance status" %}</span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="mt-3 d-flex justify-content-between">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="requires_approval" name="requires_approval">
                                                <label class="form-check-label" for="requires_approval">
                                                    {% trans "Requires Approval" %}
                                                </label>
                                            </div>
                                            <div>
                                                <button type="submit" name="action" value="draft" class="btn btn-outline-primary">
                                                    <i class="fas fa-save"></i>
                                                    {% trans "Save as Draft" %}
                                                </button>
                                                <button type="submit" name="action" value="create" class="btn btn-primary" id="create-transaction-btn" disabled>
                                                    <i class="fas fa-check"></i>
                                                    {% trans "Create Transaction" %}
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <!-- Account Search -->
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-search"></i>
                                        {% trans "Account Search" %}
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <input type="text" class="form-control" id="account-search" 
                                           placeholder="{% trans 'Search accounts...' %}">
                                    <div id="account-results" class="mt-2" style="max-height: 200px; overflow-y: auto;">
                                        <!-- Search results will appear here -->
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Recent Transactions -->
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-history"></i>
                                        {% trans "Recent Transactions" %}
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="list-group list-group-flush">
                                        {% for transaction in recent_transactions %}
                                        <div class="list-group-item px-0">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1">
                                                        <a href="{% url 'finance:transaction_detail' transaction.pk %}">
                                                            {{ transaction.transaction_id }}
                                                        </a>
                                                    </h6>
                                                    <p class="mb-1 small">{{ transaction.description|truncatechars:40 }}</p>
                                                    <small class="text-muted">{{ transaction.transaction_date|date:"M d, Y" }}</small>
                                                </div>
                                                <div class="text-right">
                                                    <span class="badge badge-{{ transaction.status|yesno:'success,warning,secondary' }}">
                                                        {{ transaction.get_status_display }}
                                                    </span>
                                                    <br>
                                                    <small class="text-muted">{{ transaction.total_amount|floatformat:2 }}</small>
                                                </div>
                                            </div>
                                        </div>
                                        {% empty %}
                                        <div class="text-center text-muted py-3">
                                            <i class="fas fa-inbox"></i>
                                            <p class="mb-0">{% trans "No recent transactions" %}</p>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Journal Entry Template -->
<div id="journal-entry-template" style="display: none;">
    <div class="row mb-2 journal-entry-row">
        <div class="col-md-4">
            <select class="form-control entry-account" name="entry_account" required>
                <option value="">{% trans "Select Account" %}</option>
                {% for account in accounts %}
                    <option value="{{ account.id }}" data-code="{{ account.code }}">
                        {{ account.code }} - {{ account.name }}
                    </option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-2">
            <input type="number" class="form-control entry-debit" name="entry_debit" 
                   step="0.01" min="0" placeholder="{% trans 'Debit' %}">
        </div>
        <div class="col-md-2">
            <input type="number" class="form-control entry-credit" name="entry_credit" 
                   step="0.01" min="0" placeholder="{% trans 'Credit' %}">
        </div>
        <div class="col-md-3">
            <input type="text" class="form-control entry-description" name="entry_description" 
                   placeholder="{% trans 'Entry description' %}">
        </div>
        <div class="col-md-1">
            <button type="button" class="btn btn-sm btn-outline-danger remove-entry-btn">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let entryCounter = 0;
    const entriesContainer = document.getElementById('journal-entries-container');
    const addEntryBtn = document.getElementById('add-journal-entry');
    const entryTemplate = document.getElementById('journal-entry-template');
    const createBtn = document.getElementById('create-transaction-btn');
    const totalDebitsEl = document.getElementById('total-debits');
    const totalCreditsEl = document.getElementById('total-credits');
    const balanceAlertEl = document.getElementById('balance-alert');
    const balanceMessageEl = document.getElementById('balance-message');
    const accountSearch = document.getElementById('account-search');
    const accountResults = document.getElementById('account-results');

    // Add initial entries
    addJournalEntry();
    addJournalEntry();

    // Add entry button
    addEntryBtn.addEventListener('click', addJournalEntry);

    // Account search
    accountSearch.addEventListener('input', function() {
        const query = this.value.toLowerCase();
        if (query.length < 2) {
            accountResults.innerHTML = '';
            return;
        }

        const accounts = Array.from(document.querySelectorAll('#journal-entry-template option[value]'))
            .filter(option => option.value && (
                option.textContent.toLowerCase().includes(query) ||
                option.dataset.code.toLowerCase().includes(query)
            ))
            .slice(0, 10);

        accountResults.innerHTML = accounts.map(option => `
            <div class="account-result p-2 border-bottom" data-account-id="${option.value}" style="cursor: pointer;">
                <strong>${option.dataset.code}</strong> - ${option.textContent.split(' - ')[1]}
            </div>
        `).join('');

        // Add click handlers
        accountResults.querySelectorAll('.account-result').forEach(result => {
            result.addEventListener('click', function() {
                const accountId = this.dataset.accountId;
                const emptyAccountSelect = document.querySelector('.entry-account[value=""]');
                if (emptyAccountSelect) {
                    emptyAccountSelect.value = accountId;
                    emptyAccountSelect.focus();
                }
                accountResults.innerHTML = '';
                accountSearch.value = '';
            });
        });
    });

    // Form submission
    document.getElementById('quick-entry-form').addEventListener('submit', function(e) {
        const action = e.submitter.value;
        if (action === 'create' && !isBalanced()) {
            e.preventDefault();
            alert('{% trans "Transaction must be balanced before creating." %}');
            return false;
        }
    });

    function addJournalEntry() {
        entryCounter++;
        const entryHtml = entryTemplate.innerHTML;
        const entryDiv = document.createElement('div');
        entryDiv.innerHTML = entryHtml;
        
        // Update field names
        const fields = entryDiv.querySelectorAll('input, select');
        fields.forEach(field => {
            const baseName = field.name;
            field.name = `entry_${entryCounter}_${baseName.replace('entry_', '')}`;
        });

        entriesContainer.appendChild(entryDiv);

        // Add event listeners
        const removeBtn = entryDiv.querySelector('.remove-entry-btn');
        removeBtn.addEventListener('click', function() {
            if (entriesContainer.children.length > 2) {
                entryDiv.remove();
                updateTotals();
            } else {
                alert('{% trans "At least two entries are required." %}');
            }
        });

        const debitInput = entryDiv.querySelector('.entry-debit');
        const creditInput = entryDiv.querySelector('.entry-credit');
        
        debitInput.addEventListener('input', function() {
            if (this.value && parseFloat(this.value) > 0) {
                creditInput.value = '';
            }
            updateTotals();
        });

        creditInput.addEventListener('input', function() {
            if (this.value && parseFloat(this.value) > 0) {
                debitInput.value = '';
            }
            updateTotals();
        });

        updateTotals();
    }

    function updateTotals() {
        let totalDebits = 0;
        let totalCredits = 0;

        document.querySelectorAll('.entry-debit').forEach(input => {
            totalDebits += parseFloat(input.value) || 0;
        });

        document.querySelectorAll('.entry-credit').forEach(input => {
            totalCredits += parseFloat(input.value) || 0;
        });

        totalDebitsEl.textContent = totalDebits.toFixed(2);
        totalCreditsEl.textContent = totalCredits.toFixed(2);

        // Update balance status
        const balanced = Math.abs(totalDebits - totalCredits) < 0.01;
        const hasEntries = totalDebits > 0 || totalCredits > 0;

        if (!hasEntries) {
            balanceAlertEl.className = 'alert mt-2 alert-info';
            balanceMessageEl.innerHTML = '<i class="fas fa-info-circle"></i> {% trans "Add entries to see balance status" %}';
            createBtn.disabled = true;
        } else if (balanced && totalDebits > 0) {
            balanceAlertEl.className = 'alert mt-2 alert-success';
            balanceMessageEl.innerHTML = '<i class="fas fa-check-circle"></i> {% trans "Transaction is balanced" %}';
            createBtn.disabled = false;
        } else {
            balanceAlertEl.className = 'alert mt-2 alert-danger';
            balanceMessageEl.innerHTML = '<i class="fas fa-exclamation-triangle"></i> {% trans "Transaction is not balanced" %}';
            createBtn.disabled = true;
        }
    }

    function isBalanced() {
        const totalDebits = parseFloat(totalDebitsEl.textContent) || 0;
        const totalCredits = parseFloat(totalCreditsEl.textContent) || 0;
        return Math.abs(totalDebits - totalCredits) < 0.01 && totalDebits > 0;
    }
});
</script>

<style>
.info-box {
    display: block;
    min-height: 90px;
    background: #fff;
    width: 100%;
    box-shadow: 0 1px 1px rgba(0,0,0,0.1);
    border-radius: 2px;
    margin-bottom: 15px;
}

.info-box-icon {
    border-top-left-radius: 2px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 2px;
    display: block;
    float: left;
    height: 90px;
    width: 90px;
    text-align: center;
    font-size: 45px;
    line-height: 90px;
    background: rgba(0,0,0,0.2);
    color: rgba(255,255,255,0.8);
}

.info-box-content {
    padding: 5px 10px;
    margin-left: 90px;
}

.info-box-text {
    text-transform: uppercase;
    font-weight: bold;
    font-size: 12px;
}

.info-box-number {
    display: block;
    font-weight: bold;
    font-size: 18px;
}

.journal-entry-row {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 10px;
    margin-bottom: 10px;
}

.account-result:hover {
    background-color: #f8f9fa;
}

.entry-debit:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.entry-credit:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}
</style>
{% endblock %}