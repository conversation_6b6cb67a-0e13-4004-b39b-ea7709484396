{% extends 'base.html' %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}
    {% if object %}
        {% trans "Edit Journal Entry" %}
    {% else %}
        {% trans "Add Journal Entry" %}
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        {% if object %}
                            <i class="fas fa-edit"></i> {% trans "Edit Journal Entry" %}
                        {% else %}
                            <i class="fas fa-plus"></i> {% trans "Add Journal Entry" %}
                        {% endif %}
                    </h3>
                    <div class="card-tools">
                        <a href="{% url 'finance:journal_entries' %}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> {% trans "Back to Journal Entries" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.entry_date.id_for_label }}" class="form-label">
                                        {{ form.entry_date.label }} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.entry_date }}
                                    {% if form.entry_date.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.entry_date.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.reference_number.id_for_label }}" class="form-label">
                                        {{ form.reference_number.label }}
                                    </label>
                                    {{ form.reference_number }}
                                    {% if form.reference_number.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.reference_number.errors.0 }}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        {% trans "Leave blank to auto-generate" %}
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.account.id_for_label }}" class="form-label">
                                        {{ form.account.label }} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.account }}
                                    {% if form.account.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.account.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.cost_center.id_for_label }}" class="form-label">
                                        {{ form.cost_center.label }}
                                    </label>
                                    {{ form.cost_center }}
                                    {% if form.cost_center.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.cost_center.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.debit_amount.id_for_label }}" class="form-label">
                                        {{ form.debit_amount.label }}
                                    </label>
                                    {{ form.debit_amount }}
                                    {% if form.debit_amount.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.debit_amount.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.credit_amount.id_for_label }}" class="form-label">
                                        {{ form.credit_amount.label }}
                                    </label>
                                    {{ form.credit_amount }}
                                    {% if form.credit_amount.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.credit_amount.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                {{ form.description.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.description.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">
                                {{ form.notes.label }}
                            </label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.notes.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                {{ form.is_posted }}
                                <label class="custom-control-label" for="{{ form.is_posted.id_for_label }}">
                                    {{ form.is_posted.label }}
                                </label>
                            </div>
                            {% if form.is_posted.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.is_posted.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                {% if object %}
                                    {% trans "Update Journal Entry" %}
                                {% else %}
                                    {% trans "Create Journal Entry" %}
                                {% endif %}
                            </button>
                            <a href="{% url 'finance:journal_entries' %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> {% trans "Cancel" %}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add form validation
    const form = document.querySelector('.needs-validation');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // Auto-calculate balance
    const debitInput = document.getElementById('{{ form.debit_amount.id_for_label }}');
    const creditInput = document.getElementById('{{ form.credit_amount.id_for_label }}');
    
    function updateBalance() {
        const debit = parseFloat(debitInput.value) || 0;
        const credit = parseFloat(creditInput.value) || 0;
        
        if (debit > 0 && credit > 0) {
            alert('{% trans "Please enter either debit or credit amount, not both." %}');
        }
    }
    
    if (debitInput && creditInput) {
        debitInput.addEventListener('input', updateBalance);
        creditInput.addEventListener('input', updateBalance);
    }
});
</script>
{% endblock %}