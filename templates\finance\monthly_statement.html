{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Monthly Account Statement" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-calendar-alt"></i> {% trans "Monthly Account Statement" %}
                    </h3>
                    <div class="card-tools">
                        <button class="btn btn-primary btn-sm" onclick="window.print()">
                            <i class="fas fa-print"></i> {% trans "Print" %}
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <select class="form-control" id="account-filter">
                                <option value="">{% trans "Select Account" %}</option>
                                {% for account in accounts %}
                                    <option value="{{ account.id }}" {% if account.id == selected_account.id %}selected{% endif %}>
                                        {{ account.code }} - {{ account.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="month" class="form-control" id="month-filter" value="{{ selected_month }}">
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="year-filter">
                                {% for year in available_years %}
                                    <option value="{{ year }}" {% if year == selected_year %}selected{% endif %}>{{ year }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-info" onclick="generateStatement()">
                                <i class="fas fa-search"></i> {% trans "Generate" %}
                            </button>
                        </div>
                    </div>

                    {% if selected_account %}
                    <!-- Account Information -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h5>{{ selected_account.code }} - {{ selected_account.name }}</h5>
                                            <p class="mb-1"><strong>{% trans "Account Type:" %}</strong> {{ selected_account.account_type.get_type_display }}</p>
                                            <p class="mb-0"><strong>{% trans "Period:" %}</strong> {{ period_display }}</p>
                                        </div>
                                        <div class="col-md-6 text-right">
                                            <h6>{% trans "Opening Balance:" %} 
                                                <span class="{% if opening_balance >= 0 %}text-success{% else %}text-danger{% endif %}">
                                                    {{ opening_balance|floatformat:2 }}
                                                </span>
                                            </h6>
                                            <h6>{% trans "Closing Balance:" %} 
                                                <span class="{% if closing_balance >= 0 %}text-success{% else %}text-danger{% endif %}">
                                                    {{ closing_balance|floatformat:2 }}
                                                </span>
                                            </h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Monthly Summary -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4>{{ total_debits|floatformat:2|default:"0.00" }}</h4>
                                    <p class="mb-0">{% trans "Total Debits" %}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4>{{ total_credits|floatformat:2|default:"0.00" }}</h4>
                                    <p class="mb-0">{% trans "Total Credits" %}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4>{{ net_change|floatformat:2|default:"0.00" }}</h4>
                                    <p class="mb-0">{% trans "Net Change" %}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4>{{ transaction_count|default:"0" }}</h4>
                                    <p class="mb-0">{% trans "Transactions" %}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Transaction Details -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Date" %}</th>
                                    <th>{% trans "Description" %}</th>
                                    <th>{% trans "Reference" %}</th>
                                    <th>{% trans "Debit" %}</th>
                                    <th>{% trans "Credit" %}</th>
                                    <th>{% trans "Balance" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Opening Balance -->
                                <tr class="table-info">
                                    <td>{{ period_start|date:"Y-m-d" }}</td>
                                    <td><strong>{% trans "Opening Balance" %}</strong></td>
                                    <td>-</td>
                                    <td class="text-right">-</td>
                                    <td class="text-right">-</td>
                                    <td class="text-right">
                                        <strong class="{% if opening_balance >= 0 %}text-success{% else %}text-danger{% endif %}">
                                            {{ opening_balance|floatformat:2 }}
                                        </strong>
                                    </td>
                                </tr>

                                {% for transaction in transactions %}
                                <tr>
                                    <td>{{ transaction.entry_date|date:"Y-m-d" }}</td>
                                    <td>{{ transaction.description }}</td>
                                    <td>{{ transaction.reference_number|default:"-" }}</td>
                                    <td class="text-right">
                                        {% if transaction.debit_amount %}
                                            <span class="text-danger">{{ transaction.debit_amount|floatformat:2 }}</span>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td class="text-right">
                                        {% if transaction.credit_amount %}
                                            <span class="text-success">{{ transaction.credit_amount|floatformat:2 }}</span>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td class="text-right">
                                        <strong class="{% if transaction.running_balance >= 0 %}text-success{% else %}text-danger{% endif %}">
                                            {{ transaction.running_balance|floatformat:2 }}
                                        </strong>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center text-muted">
                                        {% trans "No transactions found for the selected period" %}
                                    </td>
                                </tr>
                                {% endfor %}

                                <!-- Closing Balance -->
                                {% if transactions %}
                                <tr class="table-warning">
                                    <td>{{ period_end|date:"Y-m-d" }}</td>
                                    <td><strong>{% trans "Closing Balance" %}</strong></td>
                                    <td>-</td>
                                    <td class="text-right">-</td>
                                    <td class="text-right">-</td>
                                    <td class="text-right">
                                        <strong class="{% if closing_balance >= 0 %}text-success{% else %}text-danger{% endif %}">
                                            {{ closing_balance|floatformat:2 }}
                                        </strong>
                                    </td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Monthly Analysis -->
                    {% if transactions %}
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>{% trans "Transaction Analysis" %}</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tr>
                                            <td>{% trans "Largest Debit:" %}</td>
                                            <td class="text-right text-danger">{{ largest_debit|floatformat:2|default:"0.00" }}</td>
                                        </tr>
                                        <tr>
                                            <td>{% trans "Largest Credit:" %}</td>
                                            <td class="text-right text-success">{{ largest_credit|floatformat:2|default:"0.00" }}</td>
                                        </tr>
                                        <tr>
                                            <td>{% trans "Average Transaction:" %}</td>
                                            <td class="text-right">{{ average_transaction|floatformat:2|default:"0.00" }}</td>
                                        </tr>
                                        <tr>
                                            <td>{% trans "Days with Activity:" %}</td>
                                            <td class="text-right">{{ active_days|default:"0" }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>{% trans "Balance Trend" %}</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="balanceTrendChart" height="150"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    {% else %}
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle fa-2x mb-2"></i>
                        <h5>{% trans "Select Account and Period" %}</h5>
                        <p>{% trans "Please select an account and period to generate the monthly statement." %}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function generateStatement() {
    const account = document.getElementById('account-filter').value;
    const month = document.getElementById('month-filter').value;
    const year = document.getElementById('year-filter').value;
    
    if (!account) {
        alert('{% trans "Please select an account" %}');
        return;
    }
    
    const params = new URLSearchParams();
    params.append('account', account);
    if (month) params.append('month', month);
    if (year) params.append('year', year);
    
    window.location.search = params.toString();
}

// Balance trend chart
{% if transactions %}
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('balanceTrendChart');
    if (ctx) {
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: {{ chart_dates|safe }},
                datasets: [{
                    label: '{% trans "Balance" %}',
                    data: {{ chart_balances|safe }},
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: false
                    }
                }
            }
        });
    }
});
{% endif %}
</script>
{% endblock %}