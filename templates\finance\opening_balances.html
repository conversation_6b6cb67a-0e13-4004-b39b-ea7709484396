{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Opening Balances" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-calculator"></i> {% trans "Opening Balances" %}
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary btn-sm" onclick="updateAllBalances()">
                            <i class="fas fa-sync"></i> {% trans "Update All Balances" %}
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        {% trans "Opening balances are used to set the initial balance for accounts at the beginning of a financial period." %}
                    </div>

                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <select class="form-control" id="account-type-filter">
                                <option value="">{% trans "All Account Types" %}</option>
                                <option value="asset">{% trans "Assets" %}</option>
                                <option value="liability">{% trans "Liabilities" %}</option>
                                <option value="equity">{% trans "Equity" %}</option>
                                <option value="revenue">{% trans "Revenue" %}</option>
                                <option value="expense">{% trans "Expenses" %}</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <input type="text" class="form-control" id="search-account" placeholder="{% trans 'Search accounts...' %}">
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-info" onclick="filterAccounts()">
                                <i class="fas fa-search"></i> {% trans "Filter" %}
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Account Code" %}</th>
                                    <th>{% trans "Account Name" %}</th>
                                    <th>{% trans "Account Type" %}</th>
                                    <th>{% trans "Current Opening Balance" %}</th>
                                    <th>{% trans "New Opening Balance" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for account in accounts %}
                                <tr>
                                    <td>{{ account.code }}</td>
                                    <td>{{ account.name }}</td>
                                    <td>
                                        <span class="badge badge-{% if account.account_type.type == 'asset' %}primary{% elif account.account_type.type == 'liability' %}danger{% elif account.account_type.type == 'equity' %}success{% elif account.account_type.type == 'revenue' %}info{% else %}warning{% endif %}">
                                            {{ account.account_type.get_type_display }}
                                        </span>
                                    </td>
                                    <td class="text-right">
                                        <span class="{% if account.opening_balance >= 0 %}text-success{% else %}text-danger{% endif %}">
                                            {{ account.opening_balance|floatformat:2 }}
                                        </span>
                                    </td>
                                    <td>
                                        <input type="number" 
                                               class="form-control text-right" 
                                               id="balance-{{ account.id }}" 
                                               value="{{ account.opening_balance }}" 
                                               step="0.01">
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-success" onclick="updateBalance('{{ account.id }}')">
                                            <i class="fas fa-save"></i> {% trans "Update" %}
                                        </button>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center text-muted">
                                        {% trans "No accounts found" %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Summary -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>{% trans "Balance Summary" %}</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-2">
                                            <div class="text-center">
                                                <h4 class="text-primary">{{ total_assets|floatformat:2|default:"0.00" }}</h4>
                                                <p class="mb-0">{% trans "Total Assets" %}</p>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="text-center">
                                                <h4 class="text-danger">{{ total_liabilities|floatformat:2|default:"0.00" }}</h4>
                                                <p class="mb-0">{% trans "Total Liabilities" %}</p>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="text-center">
                                                <h4 class="text-success">{{ total_equity|floatformat:2|default:"0.00" }}</h4>
                                                <p class="mb-0">{% trans "Total Equity" %}</p>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="text-center">
                                                <h4 class="text-info">{{ total_revenue|floatformat:2|default:"0.00" }}</h4>
                                                <p class="mb-0">{% trans "Total Revenue" %}</p>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="text-center">
                                                <h4 class="text-warning">{{ total_expenses|floatformat:2|default:"0.00" }}</h4>
                                                <p class="mb-0">{% trans "Total Expenses" %}</p>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="text-center">
                                                <h4 class="{% if balance_check == 0 %}text-success{% else %}text-danger{% endif %}">
                                                    {{ balance_check|floatformat:2|default:"0.00" }}
                                                </h4>
                                                <p class="mb-0">{% trans "Balance Check" %}</p>
                                                <small class="text-muted">{% trans "(Should be 0)" %}</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateBalance(accountId) {
    const newBalance = document.getElementById(`balance-${accountId}`).value;
    
    // Here you would make an AJAX call to update the balance
    console.log(`Updating account ${accountId} balance to ${newBalance}`);
    
    // Simulate success
    alert('{% trans "Balance updated successfully!" %}');
}

function updateAllBalances() {
    if (confirm('{% trans "Are you sure you want to update all opening balances? This action cannot be undone." %}')) {
        // Here you would make an AJAX call to update all balances
        console.log('Updating all balances');
        alert('{% trans "All balances updated successfully!" %}');
    }
}

function filterAccounts() {
    const accountType = document.getElementById('account-type-filter').value;
    const searchTerm = document.getElementById('search-account').value;
    
    const params = new URLSearchParams();
    if (accountType) params.append('account_type', accountType);
    if (searchTerm) params.append('search', searchTerm);
    
    window.location.search = params.toString();
}
</script>
{% endblock %}