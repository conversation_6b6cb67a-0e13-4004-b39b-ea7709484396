{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Payment Processing Dashboard" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">{% trans "Payment Processing Dashboard" %}</h1>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                {% trans "Today's Payments" %}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_payments_today }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-credit-card fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                {% trans "Today's Revenue" %}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_amount_today }} SAR</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                {% trans "Successful Transactions" %}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ successful_transactions }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                {% trans "Failed Transactions" %}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ failed_transactions }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Payment Methods Chart -->
        <div class="col-xl-6 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "Payment Methods Distribution" %}</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4 pb-2">
                        <canvas id="paymentMethodsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-xl-6 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "Quick Actions" %}</h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="{% url 'finance:process_payment' %}" class="list-group-item list-group-item-action">
                            <i class="fas fa-plus-circle text-success mr-2"></i>
                            {% trans "Process New Payment" %}
                        </a>
                        <a href="{% url 'finance:payment_gateways' %}" class="list-group-item list-group-item-action">
                            <i class="fas fa-cog text-primary mr-2"></i>
                            {% trans "Manage Gateways" %}
                        </a>
                        <a href="{% url 'finance:payment_analytics' %}" class="list-group-item list-group-item-action">
                            <i class="fas fa-chart-bar text-info mr-2"></i>
                            {% trans "View Analytics" %}
                        </a>
                        <a href="{% url 'finance:payment_reminders' %}" class="list-group-item list-group-item-action">
                            <i class="fas fa-bell text-warning mr-2"></i>
                            {% trans "Payment Reminders" %}
                        </a>
                    </div>
                </div>
            </div>

            <!-- System Status -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "System Status" %}</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 font-weight-bold text-success">{{ active_gateways }}</div>
                                <div class="text-xs text-uppercase">{% trans "Active Gateways" %}</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 font-weight-bold text-warning">{{ pending_refunds }}</div>
                                <div class="text-xs text-uppercase">{% trans "Pending Refunds" %}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Transactions -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "Recent Transactions" %}</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>{% trans "Transaction ID" %}</th>
                                    <th>{% trans "Student" %}</th>
                                    <th>{% trans "Gateway" %}</th>
                                    <th>{% trans "Amount" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Date" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in recent_transactions %}
                                <tr>
                                    <td>{{ transaction.transaction_id }}</td>
                                    <td>{{ transaction.payment.student.get_full_name }}</td>
                                    <td>{{ transaction.gateway.name }}</td>
                                    <td>{{ transaction.amount }} SAR</td>
                                    <td>
                                        <span class="badge badge-{% if transaction.status == 'completed' %}success{% elif transaction.status == 'failed' %}danger{% else %}warning{% endif %}">
                                            {{ transaction.get_status_display }}
                                        </span>
                                    </td>
                                    <td>{{ transaction.created_at|date:"Y-m-d H:i" }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center">{% trans "No recent transactions" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Payment Methods Chart
var ctx = document.getElementById('paymentMethodsChart').getContext('2d');
var paymentMethodsChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: {{ payment_method_data.labels|safe }},
        datasets: [{
            data: {{ payment_method_data.data|safe }},
            backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b'],
            hoverBackgroundColor: ['#2e59d9', '#17a673', '#2c9faf', '#f4b619', '#e02d1b'],
            hoverBorderColor: "rgba(234, 236, 244, 1)",
        }],
    },
    options: {
        maintainAspectRatio: false,
        tooltips: {
            backgroundColor: "rgb(255,255,255)",
            bodyFontColor: "#858796",
            borderColor: '#dddfeb',
            borderWidth: 1,
            xPadding: 15,
            yPadding: 15,
            displayColors: false,
            caretPadding: 10,
        },
        legend: {
            display: true
        },
        cutoutPercentage: 80,
    },
});
</script>
{% endblock %}