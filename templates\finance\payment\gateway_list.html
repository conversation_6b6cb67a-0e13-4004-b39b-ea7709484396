{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Payment Gateways" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">{% trans "Payment Gateways" %}</h1>
                <a href="{% url 'finance:add_payment_gateway' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> {% trans "Add Gateway" %}
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "Configured Gateways" %}</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>{% trans "Name" %}</th>
                                    <th>{% trans "Type" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Mode" %}</th>
                                    <th>{% trans "Transaction Fee" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for gateway in gateways %}
                                <tr>
                                    <td>
                                        <strong>{{ gateway.name }}</strong>
                                        {% if gateway.webhook_url %}
                                        <br><small class="text-muted">
                                            <i class="fas fa-link"></i> {% trans "Webhook configured" %}
                                        </small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge badge-info">{{ gateway.get_gateway_type_display }}</span>
                                    </td>
                                    <td>
                                        {% if gateway.is_enabled %}
                                            <span class="badge badge-success">{% trans "Enabled" %}</span>
                                        {% else %}
                                            <span class="badge badge-secondary">{% trans "Disabled" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if gateway.is_sandbox %}
                                            <span class="badge badge-warning">{% trans "Sandbox" %}</span>
                                        {% else %}
                                            <span class="badge badge-success">{% trans "Live" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if gateway.transaction_fee_percentage > 0 %}
                                            {{ gateway.transaction_fee_percentage }}%
                                        {% endif %}
                                        {% if gateway.transaction_fee_fixed > 0 %}
                                            {% if gateway.transaction_fee_percentage > 0 %} + {% endif %}
                                            {{ gateway.transaction_fee_fixed }} SAR
                                        {% endif %}
                                        {% if gateway.transaction_fee_percentage == 0 and gateway.transaction_fee_fixed == 0 %}
                                            {% trans "Free" %}
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'finance:edit_payment_gateway' gateway.pk %}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-info"
                                                    data-toggle="modal" 
                                                    data-target="#testModal{{ gateway.pk }}">
                                                <i class="fas fa-vial"></i>
                                            </button>
                                            {% if gateway.is_enabled %}
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-warning"
                                                    onclick="toggleGateway('{{ gateway.pk }}', false)">
                                                <i class="fas fa-pause"></i>
                                            </button>
                                            {% else %}
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-success"
                                                    onclick="toggleGateway('{{ gateway.pk }}', true)">
                                                <i class="fas fa-play"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>

                                <!-- Test Modal -->
                                <div class="modal fade" id="testModal{{ gateway.pk }}" tabindex="-1">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">{% trans "Test Gateway" %}: {{ gateway.name }}</h5>
                                                <button type="button" class="close" data-dismiss="modal">
                                                    <span>&times;</span>
                                                </button>
                                            </div>
                                            <div class="modal-body">
                                                <form id="testForm{{ gateway.pk }}">
                                                    <div class="form-group">
                                                        <label>{% trans "Test Amount" %}</label>
                                                        <input type="number" class="form-control" 
                                                               name="amount" value="10.00" step="0.01" min="0.01">
                                                    </div>
                                                    <div class="form-group">
                                                        <label>{% trans "Test Description" %}</label>
                                                        <input type="text" class="form-control" 
                                                               name="description" value="Test transaction">
                                                    </div>
                                                </form>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                                                    {% trans "Cancel" %}
                                                </button>
                                                <button type="button" class="btn btn-primary" 
                                                        onclick="testGateway('{{ gateway.pk }}')">
                                                    {% trans "Run Test" %}
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center">
                                        {% trans "No payment gateways configured" %}
                                        <br><br>
                                        <a href="{% url 'finance:add_payment_gateway' %}" class="btn btn-primary">
                                            {% trans "Add Your First Gateway" %}
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if is_paginated %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1">{% trans "First" %}</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">{% trans "Last" %}</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleGateway(gatewayId, enable) {
    // Implementation for enabling/disabling gateway
    fetch(`/finance/payment/gateway/${gatewayId}/toggle/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            'enabled': enable
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    });
}

function testGateway(gatewayId) {
    const form = document.getElementById(`testForm${gatewayId}`);
    const formData = new FormData(form);
    
    fetch(`/finance/payment/gateway/${gatewayId}/test/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('{% trans "Gateway test successful!" %}');
            $(`#testModal${gatewayId}`).modal('hide');
        } else {
            alert('{% trans "Gateway test failed:" %} ' + data.error);
        }
    });
}
</script>
{% endblock %}