{% extends 'base.html' %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}
    {% if object %}
        {% trans "Edit Payment" %}
    {% else %}
        {% trans "Add Payment" %}
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        {% if object %}
                            <i class="fas fa-edit"></i> {% trans "Edit Payment" %}
                        {% else %}
                            <i class="fas fa-plus"></i> {% trans "Add Payment" %}
                        {% endif %}
                    </h3>
                    <div class="card-tools">
                        <a href="{% url 'finance:payments' %}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> {% trans "Back to Payments" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.student.id_for_label }}" class="form-label">
                                        {{ form.student.label }} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.student }}
                                    {% if form.student.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.student.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.receipt_number.id_for_label }}" class="form-label">
                                        {{ form.receipt_number.label }}
                                    </label>
                                    {{ form.receipt_number }}
                                    {% if form.receipt_number.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.receipt_number.errors.0 }}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        {% trans "Leave blank to auto-generate" %}
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.amount.id_for_label }}" class="form-label">
                                        {{ form.amount.label }} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.amount }}
                                    {% if form.amount.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.amount.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.payment_date.id_for_label }}" class="form-label">
                                        {{ form.payment_date.label }} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.payment_date }}
                                    {% if form.payment_date.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.payment_date.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.payment_method.id_for_label }}" class="form-label">
                                        {{ form.payment_method.label }} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.payment_method }}
                                    {% if form.payment_method.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.payment_method.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.reference_number.id_for_label }}" class="form-label">
                                        {{ form.reference_number.label }}
                                    </label>
                                    {{ form.reference_number }}
                                    {% if form.reference_number.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.reference_number.errors.0 }}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        {% trans "Check number, transaction ID, etc." %}
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">
                                {{ form.notes.label }}
                            </label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.notes.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Fee Items Selection -->
                        <div class="form-group">
                            <label class="form-label">{% trans "Fee Items" %}</label>
                            <div id="fee-items-container">
                                <div class="alert alert-info">
                                    {% trans "Select a student first to see available fee items" %}
                                </div>
                            </div>
                        </div>

                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                {% if object %}
                                    {% trans "Update Payment" %}
                                {% else %}
                                    {% trans "Create Payment" %}
                                {% endif %}
                            </button>
                            <a href="{% url 'finance:payments' %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> {% trans "Cancel" %}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add form validation
    const form = document.querySelector('.needs-validation');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // Handle student selection to load fee items
    const studentSelect = document.getElementById('{{ form.student.id_for_label }}');
    const feeItemsContainer = document.getElementById('fee-items-container');
    
    if (studentSelect) {
        studentSelect.addEventListener('change', function() {
            const studentId = this.value;
            if (studentId) {
                loadStudentFeeItems(studentId);
            } else {
                feeItemsContainer.innerHTML = '<div class="alert alert-info">{% trans "Select a student first to see available fee items" %}</div>';
            }
        });
    }

    function loadStudentFeeItems(studentId) {
        // Show loading
        feeItemsContainer.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> {% trans "Loading fee items..." %}</div>';
        
        // Fetch fee items for the student
        fetch(`/finance/api/student-fee-items/${studentId}/`)
            .then(response => response.json())
            .then(data => {
                if (data.fee_items && data.fee_items.length > 0) {
                    let html = '<div class="table-responsive"><table class="table table-sm table-striped">';
                    html += '<thead><tr><th>{% trans "Fee Type" %}</th><th>{% trans "Amount" %}</th><th>{% trans "Due Date" %}</th><th>{% trans "Status" %}</th><th>{% trans "Select" %}</th></tr></thead><tbody>';
                    
                    data.fee_items.forEach(item => {
                        html += `<tr>
                            <td>${item.fee_type}</td>
                            <td>${item.amount}</td>
                            <td>${item.due_date}</td>
                            <td><span class="badge badge-${item.is_paid ? 'success' : 'warning'}">${item.is_paid ? '{% trans "Paid" %}' : '{% trans "Unpaid" %}'}</span></td>
                            <td><input type="checkbox" name="fee_items" value="${item.id}" ${item.is_paid ? 'disabled' : ''}></td>
                        </tr>`;
                    });
                    
                    html += '</tbody></table></div>';
                    feeItemsContainer.innerHTML = html;
                } else {
                    feeItemsContainer.innerHTML = '<div class="alert alert-warning">{% trans "No unpaid fee items found for this student" %}</div>';
                }
            })
            .catch(error => {
                console.error('Error loading fee items:', error);
                feeItemsContainer.innerHTML = '<div class="alert alert-danger">{% trans "Error loading fee items" %}</div>';
            });
    }
});
</script>
{% endblock %}