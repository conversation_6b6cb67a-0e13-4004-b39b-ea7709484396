{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load widget_tweaks %}

{% block title %}{% trans "Payment History" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .payment-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .payment-card:hover {
        transform: translateY(-2px);
    }
    .payment-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    .payment-status {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
        border-radius: 20px;
    }
    .status-paid {
        background-color: #d4edda;
        color: #155724;
    }
    .status-pending {
        background-color: #fff3cd;
        color: #856404;
    }
    .status-failed {
        background-color: #f8d7da;
        color: #721c24;
    }
    .status-refunded {
        background-color: #d1ecf1;
        color: #0c5460;
    }
    .payment-amount {
        font-size: 1.2rem;
        font-weight: bold;
    }
    .payment-method-icon {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.5rem;
    }
    .method-cash {
        background-color: #28a745;
        color: white;
    }
    .method-card {
        background-color: #007bff;
        color: white;
    }
    .method-bank {
        background-color: #6f42c1;
        color: white;
    }
    .method-online {
        background-color: #fd7e14;
        color: white;
    }
    .timeline-item {
        border-left: 3px solid #e9ecef;
        padding-left: 1rem;
        margin-bottom: 1rem;
        position: relative;
    }
    .timeline-item::before {
        content: '';
        position: absolute;
        left: -6px;
        top: 0;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: #007bff;
    }
    .timeline-item.success::before {
        background: #28a745;
    }
    .timeline-item.warning::before {
        background: #ffc107;
    }
    .timeline-item.danger::before {
        background: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-history text-success me-2"></i>{% trans "Payment History" %}
                    </h2>
                    <p class="text-muted">{% trans "View and manage all student payment transactions" %}</p>
                </div>
                <div>
                    <a href="{% url 'finance:fees_payment' %}" class="btn btn-primary me-2">
                        <i class="fas fa-plus me-2"></i>{% trans "New Payment" %}
                    </a>
                    <button class="btn btn-success">
                        <i class="fas fa-file-export me-2"></i>{% trans "Export History" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card payment-card text-center">
                <div class="card-body">
                    <i class="fas fa-dollar-sign fa-2x text-success mb-2"></i>
                    <h3 class="mb-1 text-success">$125,450</h3>
                    <p class="mb-0">{% trans "Total Collected" %}</p>
                    <small class="text-muted">{% trans "This month" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card payment-card text-center">
                <div class="card-body">
                    <i class="fas fa-receipt fa-2x text-primary mb-2"></i>
                    <h3 class="mb-1 text-primary">1,247</h3>
                    <p class="mb-0">{% trans "Total Transactions" %}</p>
                    <small class="text-muted">{% trans "This month" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card payment-card text-center">
                <div class="card-body">
                    <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                    <h3 class="mb-1 text-warning">23</h3>
                    <p class="mb-0">{% trans "Pending Payments" %}</p>
                    <small class="text-muted">{% trans "Awaiting confirmation" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card payment-card text-center">
                <div class="card-body">
                    <i class="fas fa-undo fa-2x text-info mb-2"></i>
                    <h3 class="mb-1 text-info">5</h3>
                    <p class="mb-0">{% trans "Refunds" %}</p>
                    <small class="text-muted">{% trans "This month" %}</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card payment-card">
                <div class="card-body">
                    <h5 class="card-title mb-3">{% trans "Filter Payments" %}</h5>
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="student" class="form-label">{% trans "Student" %}</label>
                            <select class="form-select" id="student" name="student">
                                <option value="">{% trans "All Students" %}</option>
                                <option value="1">أحمد محمد علي - STU001</option>
                                <option value="2">فاطمة أحمد حسن - STU002</option>
                                <option value="3">محمد عبدالله سالم - STU003</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">{% trans "Status" %}</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">{% trans "All Status" %}</option>
                                <option value="paid">{% trans "Paid" %}</option>
                                <option value="pending">{% trans "Pending" %}</option>
                                <option value="failed">{% trans "Failed" %}</option>
                                <option value="refunded">{% trans "Refunded" %}</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="method" class="form-label">{% trans "Payment Method" %}</label>
                            <select class="form-select" id="method" name="method">
                                <option value="">{% trans "All Methods" %}</option>
                                <option value="cash">{% trans "Cash" %}</option>
                                <option value="card">{% trans "Credit Card" %}</option>
                                <option value="bank">{% trans "Bank Transfer" %}</option>
                                <option value="online">{% trans "Online Payment" %}</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">{% trans "From Date" %}</label>
                            <input type="date" class="form-control" id="date_from" name="date_from">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">{% trans "To Date" %}</label>
                            <input type="date" class="form-control" id="date_to" name="date_to">
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment History Table -->
    <div class="row">
        <div class="col-12">
            <div class="card payment-card">
                <div class="payment-header card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>{% trans "Payment Transactions" %}
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>{% trans "Receipt #" %}</th>
                                    <th>{% trans "Date" %}</th>
                                    <th>{% trans "Student" %}</th>
                                    <th>{% trans "Fee Type" %}</th>
                                    <th>{% trans "Amount" %}</th>
                                    <th>{% trans "Method" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Sample payment records -->
                                <tr>
                                    <td><strong>RCP-2025-001</strong></td>
                                    <td>2025-01-13</td>
                                    <td>
                                        <div>
                                            <strong>أحمد محمد علي</strong><br>
                                            <small class="text-muted">{{ student.student_id|default:"STU001" }} - {{ student.current_class|default:"Grade 1-A" }}</small>
                                        </div>
                                    </td>
                                    <td>{% trans "Tuition Fee" %}</td>
                                    <td class="payment-amount text-success">$1,500.00</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="payment-method-icon method-cash">
                                                <i class="fas fa-money-bill"></i>
                                            </div>
                                            {% trans "Cash" %}
                                        </div>
                                    </td>
                                    <td><span class="payment-status status-paid">{% trans "Paid" %}</span></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" title="{% trans 'View Receipt' %}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-success" title="{% trans 'Print Receipt' %}">
                                                <i class="fas fa-print"></i>
                                            </button>
                                            <button class="btn btn-outline-info" title="{% trans 'Send Email' %}">
                                                <i class="fas fa-envelope"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>RCP-2025-002</strong></td>
                                    <td>2025-01-12</td>
                                    <td>
                                        <div>
                                            <strong>فاطمة أحمد حسن</strong><br>
                                            <small class="text-muted">STU002 - Grade 2-B</small>
                                        </div>
                                    </td>
                                    <td>{% trans "Transportation Fee" %}</td>
                                    <td class="payment-amount text-warning">$350.00</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="payment-method-icon method-online">
                                                <i class="fas fa-globe"></i>
                                            </div>
                                            {% trans "Online" %}
                                        </div>
                                    </td>
                                    <td><span class="payment-status status-pending">{% trans "Pending" %}</span></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" title="{% trans 'View Details' %}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-warning" title="{% trans 'Confirm Payment' %}">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" title="{% trans 'Cancel Payment' %}">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>RCP-2025-003</strong></td>
                                    <td>2025-01-11</td>
                                    <td>
                                        <div>
                                            <strong>محمد عبدالله سالم</strong><br>
                                            <small class="text-muted">STU003 - Grade 1-B</small>
                                        </div>
                                    </td>
                                    <td>{% trans "Activity Fee" %}</td>
                                    <td class="payment-amount text-success">$200.00</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="payment-method-icon method-card">
                                                <i class="fas fa-credit-card"></i>
                                            </div>
                                            {% trans "Credit Card" %}
                                        </div>
                                    </td>
                                    <td><span class="payment-status status-paid">{% trans "Paid" %}</span></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" title="{% trans 'View Receipt' %}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-success" title="{% trans 'Print Receipt' %}">
                                                <i class="fas fa-print"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" title="{% trans 'Refund' %}">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>RCP-2025-004</strong></td>
                                    <td>2025-01-10</td>
                                    <td>
                                        <div>
                                            <strong>سارة محمد أحمد</strong><br>
                                            <small class="text-muted">STU004 - Grade 3-A</small>
                                        </div>
                                    </td>
                                    <td>{% trans "Book Fee" %}</td>
                                    <td class="payment-amount text-danger">$150.00</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="payment-method-icon method-bank">
                                                <i class="fas fa-university"></i>
                                            </div>
                                            {% trans "Bank Transfer" %}
                                        </div>
                                    </td>
                                    <td><span class="payment-status status-failed">{% trans "Failed" %}</span></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" title="{% trans 'View Details' %}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-success" title="{% trans 'Retry Payment' %}">
                                                <i class="fas fa-redo"></i>
                                            </button>
                                            <button class="btn btn-outline-info" title="{% trans 'Contact Student' %}">
                                                <i class="fas fa-phone"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">{% trans "Showing 4 of 1,247 transactions" %}</span>
                        <nav>
                            <ul class="pagination pagination-sm mb-0">
                                <li class="page-item disabled">
                                    <span class="page-link">{% trans "Previous" %}</span>
                                </li>
                                <li class="page-item active">
                                    <span class="page-link">1</span>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">2</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">3</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">{% trans "Next" %}</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Details Modal -->
<div class="modal fade" id="paymentDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Payment Details" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>{% trans "Transaction Information" %}</h6>
                        <div class="timeline-item success">
                            <strong>{% trans "Payment Initiated" %}</strong><br>
                            <small class="text-muted">2025-01-13 10:30 AM</small>
                        </div>
                        <div class="timeline-item success">
                            <strong>{% trans "Payment Processed" %}</strong><br>
                            <small class="text-muted">2025-01-13 10:31 AM</small>
                        </div>
                        <div class="timeline-item success">
                            <strong>{% trans "Receipt Generated" %}</strong><br>
                            <small class="text-muted">2025-01-13 10:32 AM</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>{% trans "Payment Summary" %}</h6>
                        <table class="table table-sm">
                            <tr>
                                <td>{% trans "Receipt Number" %}:</td>
                                <td><strong>RCP-2025-001</strong></td>
                            </tr>
                            <tr>
                                <td>{% trans "Amount" %}:</td>
                                <td><strong>$1,500.00</strong></td>
                            </tr>
                            <tr>
                                <td>{% trans "Method" %}:</td>
                                <td>{% trans "Cash" %}</td>
                            </tr>
                            <tr>
                                <td>{% trans "Status" %}:</td>
                                <td><span class="payment-status status-paid">{% trans "Paid" %}</span></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
                <button type="button" class="btn btn-primary">{% trans "Print Receipt" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize date inputs with current month
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    
    document.getElementById('date_from').value = firstDay.toISOString().split('T')[0];
    document.getElementById('date_to').value = today.toISOString().split('T')[0];

    // Handle action buttons
    document.querySelectorAll('.btn-outline-primary').forEach(btn => {
        btn.addEventListener('click', function() {
            // Show payment details modal
            const modal = new bootstrap.Modal(document.getElementById('paymentDetailsModal'));
            modal.show();
        });
    });

    // Handle print buttons
    document.querySelectorAll('.btn-outline-success').forEach(btn => {
        btn.addEventListener('click', function() {
            alert('{% trans "Print functionality would be implemented here" %}');
        });
    });

    // Handle email buttons
    document.querySelectorAll('.btn-outline-info').forEach(btn => {
        btn.addEventListener('click', function() {
            alert('{% trans "Email functionality would be implemented here" %}');
        });
    });
});
</script>
{% endblock %}
