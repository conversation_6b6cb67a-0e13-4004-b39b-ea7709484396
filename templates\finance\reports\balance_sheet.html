{% extends 'base.html' %}
{% load i18n %}
{% load static %}
{% load finance_filters %}

{% block title %}{% trans "Balance Sheet" %}{% endblock %}

{% block extra_css %}
<style>
    .report-header {
        text-align: center;
        margin-bottom: 30px;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
    }
    .balance-sheet-section {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .section-header {
        background: #f8f9fa;
        padding: 10px 15px;
        margin: -20px -20px 15px -20px;
        border-radius: 8px 8px 0 0;
        font-weight: bold;
        color: #495057;
    }
    .account-line {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #f1f1f1;
    }
    .account-line:last-child {
        border-bottom: none;
    }
    .account-line.total {
        font-weight: bold;
        border-top: 2px solid #dee2e6;
        border-bottom: 3px double #dee2e6;
        margin-top: 10px;
        padding-top: 10px;
    }
    .account-line.subtotal {
        font-weight: bold;
        border-top: 1px solid #dee2e6;
        margin-top: 5px;
        padding-top: 5px;
    }
    .account-code {
        color: #6c757d;
        font-size: 0.9em;
        margin-right: 10px;
    }
    .amount {
        font-family: 'Courier New', monospace;
        text-align: right;
        min-width: 120px;
    }
    .balance-check {
        background: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 5px;
        padding: 15px;
        margin-top: 20px;
    }
    .balance-check.unbalanced {
        background: #f8d7da;
        border-color: #f5c6cb;
    }
    .filter-form {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    @media print {
        .filter-form, .btn, .no-print {
            display: none !important;
        }
        .balance-sheet-section {
            box-shadow: none;
            border: 1px solid #ddd;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Filter Form -->
    <div class="filter-form no-print">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="as_of_date" class="form-label">{% trans "As of Date" %}</label>
                <input type="date" class="form-control" id="as_of_date" name="as_of_date" 
                       value="{{ as_of_date|date:'Y-m-d' }}">
            </div>
            <div class="col-md-4">
                <div class="form-check mt-4">
                    <input class="form-check-input" type="checkbox" id="include_zero" name="include_zero" 
                           value="true" {% if request.GET.include_zero == 'true' %}checked{% endif %}>
                    <label class="form-check-label" for="include_zero">
                        {% trans "Include Zero Balances" %}
                    </label>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> {% trans "Generate Report" %}
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                        <i class="fas fa-print"></i> {% trans "Print" %}
                    </button>
                </div>
            </div>
        </form>
    </div>

    {% if balance_sheet %}
    <!-- Report Header -->
    <div class="report-header">
        <h1>{{ balance_sheet.school }}</h1>
        <h2>{% trans "Balance Sheet" %}</h2>
        <h3>{% trans "As of" %} {{ balance_sheet.as_of_date|date:"F d, Y" }}</h3>
    </div>

    <div class="row">
        <!-- Assets Section -->
        <div class="col-md-6">
            <div class="balance-sheet-section">
                <div class="section-header">
                    <i class="fas fa-building"></i> {% trans "ASSETS" %}
                </div>

                <!-- Current Assets -->
                {% if balance_sheet.assets.current_assets %}
                <h6 class="mt-3 mb-2">{% trans "Current Assets" %}</h6>
                {% for asset in balance_sheet.assets.current_assets %}
                <div class="account-line">
                    <span>
                        <span class="account-code">{{ asset.code }}</span>
                        {{ asset.name }}
                        {% if asset.name_ar %}<br><small class="text-muted">{{ asset.name_ar }}</small>{% endif %}
                    </span>
                    <span class="amount">{{ asset.balance|floatformat:2 }}</span>
                </div>
                {% endfor %}
                {% endif %}

                <!-- Fixed Assets -->
                {% if balance_sheet.assets.fixed_assets %}
                <h6 class="mt-3 mb-2">{% trans "Fixed Assets" %}</h6>
                {% for asset in balance_sheet.assets.fixed_assets %}
                <div class="account-line">
                    <span>
                        <span class="account-code">{{ asset.code }}</span>
                        {{ asset.name }}
                        {% if asset.name_ar %}<br><small class="text-muted">{{ asset.name_ar }}</small>{% endif %}
                    </span>
                    <span class="amount">{{ asset.balance|floatformat:2 }}</span>
                </div>
                {% endfor %}
                {% endif %}

                <!-- Other Assets -->
                {% if balance_sheet.assets.other_assets %}
                <h6 class="mt-3 mb-2">{% trans "Other Assets" %}</h6>
                {% for asset in balance_sheet.assets.other_assets %}
                <div class="account-line">
                    <span>
                        <span class="account-code">{{ asset.code }}</span>
                        {{ asset.name }}
                        {% if asset.name_ar %}<br><small class="text-muted">{{ asset.name_ar }}</small>{% endif %}
                    </span>
                    <span class="amount">{{ asset.balance|floatformat:2 }}</span>
                </div>
                {% endfor %}
                {% endif %}

                <!-- Total Assets -->
                <div class="account-line total">
                    <span><strong>{% trans "TOTAL ASSETS" %}</strong></span>
                    <span class="amount"><strong>{{ balance_sheet.assets.total_assets|floatformat:2 }}</strong></span>
                </div>
            </div>
        </div>

        <!-- Liabilities and Equity Section -->
        <div class="col-md-6">
            <!-- Liabilities -->
            <div class="balance-sheet-section">
                <div class="section-header">
                    <i class="fas fa-credit-card"></i> {% trans "LIABILITIES" %}
                </div>

                <!-- Current Liabilities -->
                {% if balance_sheet.liabilities.current_liabilities %}
                <h6 class="mt-3 mb-2">{% trans "Current Liabilities" %}</h6>
                {% for liability in balance_sheet.liabilities.current_liabilities %}
                <div class="account-line">
                    <span>
                        <span class="account-code">{{ liability.code }}</span>
                        {{ liability.name }}
                        {% if liability.name_ar %}<br><small class="text-muted">{{ liability.name_ar }}</small>{% endif %}
                    </span>
                    <span class="amount">{{ liability.balance|floatformat:2 }}</span>
                </div>
                {% endfor %}
                {% endif %}

                <!-- Long-term Liabilities -->
                {% if balance_sheet.liabilities.long_term_liabilities %}
                <h6 class="mt-3 mb-2">{% trans "Long-term Liabilities" %}</h6>
                {% for liability in balance_sheet.liabilities.long_term_liabilities %}
                <div class="account-line">
                    <span>
                        <span class="account-code">{{ liability.code }}</span>
                        {{ liability.name }}
                        {% if liability.name_ar %}<br><small class="text-muted">{{ liability.name_ar }}</small>{% endif %}
                    </span>
                    <span class="amount">{{ liability.balance|floatformat:2 }}</span>
                </div>
                {% endfor %}
                {% endif %}

                <!-- Total Liabilities -->
                <div class="account-line subtotal">
                    <span><strong>{% trans "Total Liabilities" %}</strong></span>
                    <span class="amount"><strong>{{ balance_sheet.liabilities.total_liabilities|floatformat:2 }}</strong></span>
                </div>
            </div>

            <!-- Equity -->
            <div class="balance-sheet-section">
                <div class="section-header">
                    <i class="fas fa-chart-pie"></i> {% trans "EQUITY" %}
                </div>

                {% for equity in balance_sheet.equity.items %}
                <div class="account-line">
                    <span>
                        <span class="account-code">{{ equity.code }}</span>
                        {{ equity.name }}
                        {% if equity.name_ar %}<br><small class="text-muted">{{ equity.name_ar }}</small>{% endif %}
                    </span>
                    <span class="amount">{{ equity.balance|floatformat:2 }}</span>
                </div>
                {% endfor %}

                <!-- Total Equity -->
                <div class="account-line subtotal">
                    <span><strong>{% trans "Total Equity" %}</strong></span>
                    <span class="amount"><strong>{{ balance_sheet.equity.total_equity|floatformat:2 }}</strong></span>
                </div>

                <!-- Total Liabilities and Equity -->
                <div class="account-line total">
                    <span><strong>{% trans "TOTAL LIABILITIES & EQUITY" %}</strong></span>
                    <span class="amount"><strong>{{ balance_sheet.total_liabilities_equity|floatformat:2 }}</strong></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Balance Check -->
    <div class="balance-check {% if not balance_sheet.is_balanced %}unbalanced{% endif %}">
        <div class="row">
            <div class="col-md-6">
                <strong>{% trans "Balance Check:" %}</strong>
                {% if balance_sheet.is_balanced %}
                    <span class="text-success">
                        <i class="fas fa-check-circle"></i> {% trans "Balanced" %}
                    </span>
                {% else %}
                    <span class="text-danger">
                        <i class="fas fa-exclamation-triangle"></i> {% trans "Not Balanced" %}
                    </span>
                {% endif %}
            </div>
            <div class="col-md-6 text-end">
                <strong>{% trans "Difference:" %}</strong>
                {{ balance_sheet.assets.total_assets|subtract:balance_sheet.total_liabilities_equity|floatformat:2 }}
            </div>
        </div>
    </div>

    {% else %}
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle"></i>
        {% trans "Unable to generate balance sheet. Please check your account setup and try again." %}
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form when date changes
    document.getElementById('as_of_date').addEventListener('change', function() {
        this.form.submit();
    });
    
    // Auto-submit form when checkbox changes
    document.getElementById('include_zero').addEventListener('change', function() {
        this.form.submit();
    });
});
</script>
{% endblock %}