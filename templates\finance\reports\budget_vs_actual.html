{% extends 'base.html' %}
{% load i18n %}
{% load static %}
{% load finance_filters %}

{% block title %}{% trans "Budget vs Actual Report" %}{% endblock %}

{% block extra_css %}
<style>
    .report-header {
        text-align: center;
        margin-bottom: 30px;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
    }
    .budget-section {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .section-header {
        background: #f8f9fa;
        padding: 10px 15px;
        margin: -20px -20px 15px -20px;
        border-radius: 8px 8px 0 0;
        font-weight: bold;
        color: #495057;
    }
    .budget-line {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
        gap: 15px;
        padding: 12px 0;
        border-bottom: 1px solid #f1f1f1;
        align-items: center;
    }
    .budget-line:last-child {
        border-bottom: none;
    }
    .budget-line.header {
        font-weight: bold;
        background: #f8f9fa;
        padding: 12px 15px;
        margin: 0 -20px 15px -20px;
        border-bottom: 2px solid #dee2e6;
    }
    .budget-line.total {
        font-weight: bold;
        border-top: 2px solid #dee2e6;
        border-bottom: 3px double #dee2e6;
        margin-top: 10px;
        padding-top: 15px;
        font-size: 1.1em;
        background: #f8f9fa;
    }
    .account-info {
        display: flex;
        flex-direction: column;
    }
    .account-code {
        color: #6c757d;
        font-size: 0.9em;
    }
    .account-name {
        font-weight: 500;
    }
    .cost-center {
        color: #6c757d;
        font-size: 0.8em;
        font-style: italic;
    }
    .amount {
        font-family: 'Courier New', monospace;
        text-align: right;
    }
    .variance-positive {
        color: #28a745;
    }
    .variance-negative {
        color: #dc3545;
    }
    .utilization-bar {
        width: 100%;
        height: 20px;
        background: #e9ecef;
        border-radius: 10px;
        overflow: hidden;
        position: relative;
    }
    .utilization-fill {
        height: 100%;
        border-radius: 10px;
        transition: width 0.3s ease;
    }
    .utilization-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 0.8em;
        font-weight: bold;
        color: white;
        text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
    }
    .summary-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    .summary-card {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        text-align: center;
    }
    .summary-card.budget {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
    }
    .summary-card.actual {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        color: white;
    }
    .summary-card.variance {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #333;
    }
    .summary-card.utilization {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        color: #333;
    }
    .filter-form {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    .variance-analysis {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .analysis-item {
        padding: 10px;
        margin: 5px 0;
        border-radius: 5px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .analysis-item.favorable {
        background: #d4edda;
        border-left: 4px solid #28a745;
    }
    .analysis-item.unfavorable {
        background: #f8d7da;
        border-left: 4px solid #dc3545;
    }
    .analysis-item.over-budget {
        background: #fff3cd;
        border-left: 4px solid #ffc107;
    }
    @media print {
        .filter-form, .btn, .no-print {
            display: none !important;
        }
        .budget-section, .summary-card, .variance-analysis {
            box-shadow: none;
            border: 1px solid #ddd;
        }
    }
    @media (max-width: 768px) {
        .budget-line {
            grid-template-columns: 1fr;
            gap: 5px;
        }
        .budget-line.header {
            display: none;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Filter Form -->
    <div class="filter-form no-print">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="budget_id" class="form-label">{% trans "Select Budget" %}</label>
                <select class="form-select" id="budget_id" name="budget_id" required>
                    <option value="">{% trans "Choose a budget..." %}</option>
                    {% for budget in budgets %}
                    <option value="{{ budget.id }}" {% if budget.id|stringformat:"s" == selected_budget_id %}selected{% endif %}>
                        {{ budget.name }} ({{ budget.start_date|date:"M Y" }} - {{ budget.end_date|date:"M Y" }})
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <label for="as_of_date" class="form-label">{% trans "As of Date" %}</label>
                <input type="date" class="form-control" id="as_of_date" name="as_of_date" 
                       value="{{ as_of_date|date:'Y-m-d' }}">
            </div>
            <div class="col-md-4">
                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> {% trans "Generate Report" %}
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                        <i class="fas fa-print"></i> {% trans "Print" %}
                    </button>
                </div>
            </div>
        </form>
    </div>

    {% if budget_report %}
    <!-- Report Header -->
    <div class="report-header">
        <h1>{{ budget_report.school }}</h1>
        <h2>{% trans "Budget vs Actual Report" %}</h2>
        <h3>{{ budget_report.budget.name }}</h3>
        <p>{{ budget_report.budget.period.start_date|date:"F d, Y" }} - {{ budget_report.budget.period.end_date|date:"F d, Y" }}</p>
        <p>{% trans "As of" %} {{ budget_report.as_of_date|date:"F d, Y" }}</p>
    </div>

    <!-- Summary Cards -->
    <div class="summary-cards">
        <div class="summary-card budget">
            <h5>{% trans "Total Budget" %}</h5>
            <h2>{{ budget_report.summary.total_budget|floatformat:2 }}</h2>
        </div>
        <div class="summary-card actual">
            <h5>{% trans "Total Actual" %}</h5>
            <h2>{{ budget_report.summary.total_actual|floatformat:2 }}</h2>
        </div>
        <div class="summary-card variance">
            <h5>{% trans "Total Variance" %}</h5>
            <h2 class="{% if budget_report.summary.total_variance >= 0 %}text-success{% else %}text-danger{% endif %}">
                {{ budget_report.summary.total_variance|floatformat:2 }}
            </h2>
        </div>
        <div class="summary-card utilization">
            <h5>{% trans "Utilization" %}</h5>
            <h2>{{ budget_report.summary.utilization_percentage }}%</h2>
        </div>
    </div>

    <!-- Budget Details -->
    <div class="budget-section">
        <div class="section-header">
            <i class="fas fa-chart-bar"></i> {% trans "Budget vs Actual Details" %}
        </div>

        <div class="budget-line header">
            <div>{% trans "Account" %}</div>
            <div class="text-center">{% trans "Budget" %}</div>
            <div class="text-center">{% trans "Actual" %}</div>
            <div class="text-center">{% trans "Variance" %}</div>
            <div class="text-center">{% trans "Utilization" %}</div>
        </div>

        {% for item in budget_report.items %}
        <div class="budget-line">
            <div class="account-info">
                <div class="account-name">
                    <span class="account-code">{{ item.account.code }}</span>
                    {{ item.account.name }}
                </div>
                {% if item.account.name_ar %}
                <small class="text-muted">{{ item.account.name_ar }}</small>
                {% endif %}
                {% if item.cost_center.name %}
                <div class="cost-center">{{ item.cost_center.name }}</div>
                {% endif %}
            </div>
            <div class="amount">{{ item.budget_amount|floatformat:2 }}</div>
            <div class="amount">{{ item.actual_amount|floatformat:2 }}</div>
            <div class="amount {% if item.variance >= 0 %}variance-positive{% else %}variance-negative{% endif %}">
                {{ item.variance|floatformat:2 }}
            </div>
            <div>
                <div class="utilization-bar">
                    <div class="utilization-fill 
                        {% if item.utilization_percentage <= 75 %}bg-success
                        {% elif item.utilization_percentage <= 90 %}bg-warning
                        {% else %}bg-danger{% endif %}"
                        style="width: {{ item.utilization_percentage|floatformat:0 }}%">
                    </div>
                    <div class="utilization-text">{{ item.utilization_percentage|floatformat:0 }}%</div>
                </div>
            </div>
        </div>
        {% endfor %}

        <div class="budget-line total">
            <div><strong>{% trans "TOTAL" %}</strong></div>
            <div class="amount"><strong>{{ budget_report.summary.total_budget|floatformat:2 }}</strong></div>
            <div class="amount"><strong>{{ budget_report.summary.total_actual|floatformat:2 }}</strong></div>
            <div class="amount {% if budget_report.summary.total_variance >= 0 %}variance-positive{% else %}variance-negative{% endif %}">
                <strong>{{ budget_report.summary.total_variance|floatformat:2 }}</strong>
            </div>
            <div class="amount"><strong>{{ budget_report.summary.utilization_percentage }}%</strong></div>
        </div>
    </div>

    <!-- Variance Analysis -->
    <div class="variance-analysis">
        <h5><i class="fas fa-analytics"></i> {% trans "Variance Analysis" %}</h5>
        
        <div class="row">
            <div class="col-md-4">
                <h6 class="text-success">{% trans "Favorable Variances" %}</h6>
                {% for item in budget_report.variance_analysis.favorable_variances %}
                <div class="analysis-item favorable">
                    <span>{{ item.account.name }}</span>
                    <span>{{ item.variance|floatformat:2 }}</span>
                </div>
                {% empty %}
                <p class="text-muted">{% trans "No favorable variances" %}</p>
                {% endfor %}
            </div>
            
            <div class="col-md-4">
                <h6 class="text-danger">{% trans "Unfavorable Variances" %}</h6>
                {% for item in budget_report.variance_analysis.unfavorable_variances %}
                <div class="analysis-item unfavorable">
                    <span>{{ item.account.name }}</span>
                    <span>{{ item.variance|floatformat:2 }}</span>
                </div>
                {% empty %}
                <p class="text-muted">{% trans "No unfavorable variances" %}</p>
                {% endfor %}
            </div>
            
            <div class="col-md-4">
                <h6 class="text-warning">{% trans "Over Budget Items" %}</h6>
                {% for item in budget_report.variance_analysis.over_budget_items %}
                <div class="analysis-item over-budget">
                    <span>{{ item.account.name }}</span>
                    <span>{{ item.utilization_percentage|floatformat:0 }}%</span>
                </div>
                {% empty %}
                <p class="text-muted">{% trans "No over budget items" %}</p>
                {% endfor %}
            </div>
        </div>
    </div>

    {% elif not selected_budget_id %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i>
        {% trans "Please select a budget to generate the report." %}
    </div>
    {% else %}
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle"></i>
        {% trans "Unable to generate budget vs actual report. Please check your budget setup and try again." %}
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form when budget changes
    document.getElementById('budget_id').addEventListener('change', function() {
        if (this.value) {
            this.form.submit();
        }
    });
    
    // Auto-submit form when date changes
    document.getElementById('as_of_date').addEventListener('change', function() {
        if (document.getElementById('budget_id').value) {
            this.form.submit();
        }
    });
});
</script>
{% endblock %}