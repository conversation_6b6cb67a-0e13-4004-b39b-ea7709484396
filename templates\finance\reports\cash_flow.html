{% extends 'base.html' %}
{% load i18n %}
{% load static %}
{% load finance_filters %}

{% block title %}{% trans "Cash Flow Statement" %}{% endblock %}

{% block extra_css %}
<style>
    .report-header {
        text-align: center;
        margin-bottom: 30px;
        padding: 20px;
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        color: white;
        border-radius: 10px;
    }
    .cash-flow-section {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .section-header {
        background: #f8f9fa;
        padding: 10px 15px;
        margin: -20px -20px 15px -20px;
        border-radius: 8px 8px 0 0;
        font-weight: bold;
        color: #495057;
    }
    .section-header.operating {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #333;
    }
    .section-header.investing {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        color: #333;
    }
    .section-header.financing {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    .cash-flow-line {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #f1f1f1;
    }
    .cash-flow-line:last-child {
        border-bottom: none;
    }
    .cash-flow-line.total {
        font-weight: bold;
        border-top: 2px solid #dee2e6;
        border-bottom: 3px double #dee2e6;
        margin-top: 10px;
        padding-top: 10px;
        font-size: 1.1em;
    }
    .cash-flow-line.subtotal {
        font-weight: bold;
        border-top: 1px solid #dee2e6;
        margin-top: 5px;
        padding-top: 5px;
    }
    .amount {
        font-family: 'Courier New', monospace;
        text-align: right;
        min-width: 120px;
    }
    .inflow {
        color: #28a745;
    }
    .outflow {
        color: #dc3545;
    }
    .cash-summary {
        background: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 5px;
        padding: 20px;
        margin-top: 20px;
    }
    .cash-summary.negative {
        background: #f8d7da;
        border-color: #f5c6cb;
    }
    .filter-form {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    .activity-item {
        padding: 5px 0;
        font-size: 0.9em;
    }
    .activity-date {
        color: #6c757d;
        font-size: 0.8em;
    }
    @media print {
        .filter-form, .btn, .no-print {
            display: none !important;
        }
        .cash-flow-section {
            box-shadow: none;
            border: 1px solid #ddd;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Filter Form -->
    <div class="filter-form no-print">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="start_date" class="form-label">{% trans "Start Date" %}</label>
                <input type="date" class="form-control" id="start_date" name="start_date" 
                       value="{{ start_date|date:'Y-m-d' }}" required>
            </div>
            <div class="col-md-4">
                <label for="end_date" class="form-label">{% trans "End Date" %}</label>
                <input type="date" class="form-control" id="end_date" name="end_date" 
                       value="{{ end_date|date:'Y-m-d' }}" required>
            </div>
            <div class="col-md-4">
                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> {% trans "Generate Report" %}
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                        <i class="fas fa-print"></i> {% trans "Print" %}
                    </button>
                </div>
            </div>
        </form>
    </div>

    {% if cash_flow %}
    <!-- Report Header -->
    <div class="report-header">
        <h1>{{ cash_flow.school }}</h1>
        <h2>{% trans "Cash Flow Statement" %}</h2>
        <h3>{{ cash_flow.period.start_date|date:"F d, Y" }} - {{ cash_flow.period.end_date|date:"F d, Y" }}</h3>
    </div>

    <!-- Operating Activities -->
    <div class="cash-flow-section">
        <div class="section-header operating">
            <i class="fas fa-cogs"></i> {% trans "CASH FLOWS FROM OPERATING ACTIVITIES" %}
        </div>

        <!-- Cash Receipts -->
        {% if cash_flow.operating_activities.cash_receipts %}
        <h6 class="mt-3 mb-2 text-success">{% trans "Cash Receipts" %}</h6>
        {% for receipt in cash_flow.operating_activities.cash_receipts %}
        <div class="activity-item">
            <div class="cash-flow-line">
                <span>
                    {{ receipt.description }}
                    <br><small class="activity-date">{{ receipt.date|date:"M d, Y" }}</small>
                </span>
                <span class="amount inflow">{{ receipt.amount|floatformat:2 }}</span>
            </div>
        </div>
        {% endfor %}
        {% endif %}

        <!-- Cash Payments -->
        {% if cash_flow.operating_activities.cash_payments %}
        <h6 class="mt-3 mb-2 text-danger">{% trans "Cash Payments" %}</h6>
        {% for payment in cash_flow.operating_activities.cash_payments %}
        <div class="activity-item">
            <div class="cash-flow-line">
                <span>
                    {{ payment.description }}
                    <br><small class="activity-date">{{ payment.date|date:"M d, Y" }}</small>
                </span>
                <span class="amount outflow">({{ payment.amount|floatformat:2 }})</span>
            </div>
        </div>
        {% endfor %}
        {% endif %}

        <!-- Net Operating Cash Flow -->
        <div class="cash-flow-line subtotal">
            <span><strong>{% trans "Net Cash from Operating Activities" %}</strong></span>
            <span class="amount {% if cash_flow.operating_activities.net_operating_cash >= 0 %}inflow{% else %}outflow{% endif %}">
                <strong>
                    {% if cash_flow.operating_activities.net_operating_cash >= 0 %}
                        {{ cash_flow.operating_activities.net_operating_cash|floatformat:2 }}
                    {% else %}
                        ({{ cash_flow.operating_activities.net_operating_cash|abs_value|floatformat:2 }})
                    {% endif %}
                </strong>
            </span>
        </div>
    </div>

    <!-- Investing Activities -->
    <div class="cash-flow-section">
        <div class="section-header investing">
            <i class="fas fa-chart-line"></i> {% trans "CASH FLOWS FROM INVESTING ACTIVITIES" %}
        </div>

        {% if cash_flow.investing_activities.items %}
        {% for item in cash_flow.investing_activities.items %}
        <div class="cash-flow-line">
            <span>
                {{ item.description }}
                <br><small class="activity-date">{{ item.date|date:"M d, Y" }}</small>
            </span>
            <span class="amount {% if item.type == 'inflow' %}inflow{% else %}outflow{% endif %}">
                {% if item.type == 'inflow' %}
                    {{ item.amount|floatformat:2 }}
                {% else %}
                    ({{ item.amount|floatformat:2 }})
                {% endif %}
            </span>
        </div>
        {% endfor %}
        {% else %}
        <p class="text-muted">{% trans "No investing activities for this period" %}</p>
        {% endif %}

        <!-- Net Investing Cash Flow -->
        <div class="cash-flow-line subtotal">
            <span><strong>{% trans "Net Cash from Investing Activities" %}</strong></span>
            <span class="amount {% if cash_flow.investing_activities.net_investing_cash >= 0 %}inflow{% else %}outflow{% endif %}">
                <strong>
                    {% if cash_flow.investing_activities.net_investing_cash >= 0 %}
                        {{ cash_flow.investing_activities.net_investing_cash|floatformat:2 }}
                    {% else %}
                        ({{ cash_flow.investing_activities.net_investing_cash|abs_value|floatformat:2 }})
                    {% endif %}
                </strong>
            </span>
        </div>
    </div>

    <!-- Financing Activities -->
    <div class="cash-flow-section">
        <div class="section-header financing">
            <i class="fas fa-university"></i> {% trans "CASH FLOWS FROM FINANCING ACTIVITIES" %}
        </div>

        {% if cash_flow.financing_activities.items %}
        {% for item in cash_flow.financing_activities.items %}
        <div class="cash-flow-line">
            <span>
                {{ item.description }}
                <br><small class="activity-date">{{ item.date|date:"M d, Y" }}</small>
            </span>
            <span class="amount {% if item.type == 'inflow' %}inflow{% else %}outflow{% endif %}">
                {% if item.type == 'inflow' %}
                    {{ item.amount|floatformat:2 }}
                {% else %}
                    ({{ item.amount|floatformat:2 }})
                {% endif %}
            </span>
        </div>
        {% endfor %}
        {% else %}
        <p class="text-muted">{% trans "No financing activities for this period" %}</p>
        {% endif %}

        <!-- Net Financing Cash Flow -->
        <div class="cash-flow-line subtotal">
            <span><strong>{% trans "Net Cash from Financing Activities" %}</strong></span>
            <span class="amount {% if cash_flow.financing_activities.net_financing_cash >= 0 %}inflow{% else %}outflow{% endif %}">
                <strong>
                    {% if cash_flow.financing_activities.net_financing_cash >= 0 %}
                        {{ cash_flow.financing_activities.net_financing_cash|floatformat:2 }}
                    {% else %}
                        ({{ cash_flow.financing_activities.net_financing_cash|abs_value|floatformat:2 }})
                    {% endif %}
                </strong>
            </span>
        </div>
    </div>

    <!-- Cash Summary -->
    <div class="cash-summary {% if cash_flow.net_change_in_cash < 0 %}negative{% endif %}">
        <div class="row">
            <div class="col-md-6">
                <div class="cash-flow-line">
                    <span><strong>{% trans "Net Change in Cash" %}</strong></span>
                    <span class="amount {% if cash_flow.net_change_in_cash >= 0 %}inflow{% else %}outflow{% endif %}">
                        <strong>
                            {% if cash_flow.net_change_in_cash >= 0 %}
                                {{ cash_flow.net_change_in_cash|floatformat:2 }}
                            {% else %}
                                ({{ cash_flow.net_change_in_cash|abs_value|floatformat:2 }})
                            {% endif %}
                        </strong>
                    </span>
                </div>
                <div class="cash-flow-line">
                    <span>{% trans "Cash at Beginning of Period" %}</span>
                    <span class="amount">{{ cash_flow.beginning_cash|floatformat:2 }}</span>
                </div>
                <div class="cash-flow-line total">
                    <span><strong>{% trans "Cash at End of Period" %}</strong></span>
                    <span class="amount"><strong>{{ cash_flow.ending_cash|floatformat:2 }}</strong></span>
                </div>
            </div>
            <div class="col-md-6">
                <div class="text-center">
                    {% if cash_flow.net_change_in_cash >= 0 %}
                        <i class="fas fa-arrow-up text-success fa-3x"></i>
                        <h5 class="text-success mt-2">{% trans "Cash Increased" %}</h5>
                    {% else %}
                        <i class="fas fa-arrow-down text-danger fa-3x"></i>
                        <h5 class="text-danger mt-2">{% trans "Cash Decreased" %}</h5>
                    {% endif %}
                    <p class="text-muted">
                        {% trans "Change:" %} 
                        {% if cash_flow.beginning_cash != 0 %}
                            {{ cash_flow.net_change_in_cash|percentage:cash_flow.beginning_cash|floatformat:1 }}%
                        {% else %}
                            N/A
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>

    {% else %}
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle"></i>
        {% trans "Unable to generate cash flow statement. Please check your account setup and try again." %}
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form when dates change
    document.getElementById('start_date').addEventListener('change', function() {
        this.form.submit();
    });
    
    document.getElementById('end_date').addEventListener('change', function() {
        this.form.submit();
    });
});
</script>
{% endblock %}