{% extends 'base.html' %}
{% load i18n %}
{% load static %}
{% load finance_filters %}

{% block title %}{% trans "Financial Dashboard" %}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css" rel="stylesheet">
<style>
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .metric-card.revenue {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    .metric-card.expense {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }
    .metric-card.profit {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #333;
    }
    .metric-card.cash {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        color: #333;
    }
    .chart-container {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }
    .report-link {
        display: block;
        padding: 15px;
        background: white;
        border-radius: 8px;
        text-decoration: none;
        color: #333;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
        margin-bottom: 10px;
    }
    .report-link:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        text-decoration: none;
        color: #333;
    }
    .budget-progress {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 10px;
    }
    .progress {
        height: 8px;
        border-radius: 4px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">{% trans "Financial Dashboard" %}</h1>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                        {% trans "Quick Reports" %}
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{% url 'finance:balance_sheet_report' %}">{% trans "Balance Sheet" %}</a></li>
                        <li><a class="dropdown-item" href="{% url 'finance:profit_loss_report' %}">{% trans "Profit & Loss" %}</a></li>
                        <li><a class="dropdown-item" href="{% url 'finance:cash_flow_report' %}">{% trans "Cash Flow" %}</a></li>
                        <li><a class="dropdown-item" href="{% url 'finance:budget_vs_actual_report' %}">{% trans "Budget vs Actual" %}</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    {% if dashboard_data %}
    <!-- Key Metrics Row -->
    <div class="row">
        <div class="col-md-3">
            <div class="metric-card revenue">
                <h5>{% trans "Total Revenue" %}</h5>
                <h2>{{ dashboard_data.key_metrics.total_revenue|floatformat:2 }}</h2>
                <small>{% trans "Last" %} {{ dashboard_data.period.days }} {% trans "days" %}</small>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card expense">
                <h5>{% trans "Total Expenses" %}</h5>
                <h2>{{ dashboard_data.key_metrics.total_expenses|floatformat:2 }}</h2>
                <small>{% trans "Last" %} {{ dashboard_data.period.days }} {% trans "days" %}</small>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card profit">
                <h5>{% trans "Net Income" %}</h5>
                <h2>{{ dashboard_data.key_metrics.net_income|floatformat:2 }}</h2>
                <small>{% trans "Profit Margin:" %} {{ dashboard_data.key_metrics.profit_margin }}%</small>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card cash">
                <h5>{% trans "Cash Position" %}</h5>
                <h2>{{ dashboard_data.cash_position.total_cash|floatformat:2 }}</h2>
                <small>{% trans "Outstanding:" %} {{ dashboard_data.key_metrics.outstanding_fees|floatformat:2 }}</small>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row">
        <div class="col-md-6">
            <div class="chart-container">
                <h5>{% trans "Revenue Breakdown" %}</h5>
                <canvas id="revenueChart" width="400" height="200"></canvas>
            </div>
        </div>
        <div class="col-md-6">
            <div class="chart-container">
                <h5>{% trans "Expense Breakdown" %}</h5>
                <canvas id="expenseChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Trends and Budget Performance -->
    <div class="row">
        <div class="col-md-8">
            <div class="chart-container">
                <h5>{% trans "Financial Trends" %}</h5>
                <canvas id="trendsChart" width="400" height="200"></canvas>
            </div>
        </div>
        <div class="col-md-4">
            <div class="chart-container">
                <h5>{% trans "Budget Performance" %}</h5>
                {% for budget in dashboard_data.budget_performance.budgets %}
                <div class="budget-progress">
                    <div class="d-flex justify-content-between">
                        <span>{{ budget.name }}</span>
                        <span>{{ budget.utilization }}%</span>
                    </div>
                    <div class="progress mt-2">
                        <div class="progress-bar {% if budget.utilization > 90 %}bg-danger{% elif budget.utilization > 75 %}bg-warning{% else %}bg-success{% endif %}" 
                             style="width: {{ budget.utilization }}%"></div>
                    </div>
                    <small class="text-muted">
                        {{ budget.spent|floatformat:2 }} / {{ budget.allocated|floatformat:2 }}
                    </small>
                </div>
                {% empty %}
                <p class="text-muted">{% trans "No active budgets found" %}</p>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Quick Access Reports -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <h5>{% trans "Financial Reports" %}</h5>
                <div class="row">
                    <div class="col-md-3">
                        <a href="{% url 'finance:balance_sheet_report' %}" class="report-link">
                            <i class="fas fa-balance-scale text-primary"></i>
                            <strong>{% trans "Balance Sheet" %}</strong>
                            <br><small class="text-muted">{% trans "Assets, Liabilities & Equity" %}</small>
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{% url 'finance:profit_loss_report' %}" class="report-link">
                            <i class="fas fa-chart-line text-success"></i>
                            <strong>{% trans "Profit & Loss" %}</strong>
                            <br><small class="text-muted">{% trans "Revenue & Expenses" %}</small>
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{% url 'finance:cash_flow_report' %}" class="report-link">
                            <i class="fas fa-money-bill-wave text-info"></i>
                            <strong>{% trans "Cash Flow" %}</strong>
                            <br><small class="text-muted">{% trans "Cash Inflows & Outflows" %}</small>
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{% url 'finance:budget_vs_actual_report' %}" class="report-link">
                            <i class="fas fa-chart-bar text-warning"></i>
                            <strong>{% trans "Budget vs Actual" %}</strong>
                            <br><small class="text-muted">{% trans "Budget Performance" %}</small>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% else %}
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle"></i>
        {% trans "Unable to load dashboard data. Please check your financial setup." %}
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
{% if dashboard_data %}
<script>
// Revenue Breakdown Chart
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(revenueCtx, {
    type: 'doughnut',
    data: {
        labels: [
            {% for item in dashboard_data.revenue_analysis.breakdown %}
            '{{ item.account }}',
            {% endfor %}
        ],
        datasets: [{
            data: [
                {% for item in dashboard_data.revenue_analysis.breakdown %}
                {{ item.amount }},
                {% endfor %}
            ],
            backgroundColor: [
                '#4facfe', '#00f2fe', '#667eea', '#764ba2', '#a8edea'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Expense Breakdown Chart
const expenseCtx = document.getElementById('expenseChart').getContext('2d');
const expenseChart = new Chart(expenseCtx, {
    type: 'doughnut',
    data: {
        labels: [
            {% for item in dashboard_data.expense_analysis.breakdown %}
            '{{ item.account }}',
            {% endfor %}
        ],
        datasets: [{
            data: [
                {% for item in dashboard_data.expense_analysis.breakdown %}
                {{ item.amount }},
                {% endfor %}
            ],
            backgroundColor: [
                '#fa709a', '#fee140', '#ffecd2', '#fcb69f', '#ff9a9e'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Trends Chart
const trendsCtx = document.getElementById('trendsChart').getContext('2d');
const trendsChart = new Chart(trendsCtx, {
    type: 'line',
    data: {
        labels: [
            {% for item in dashboard_data.trends.monthly_data %}
            '{{ item.month }}',
            {% endfor %}
        ],
        datasets: [{
            label: '{% trans "Revenue" %}',
            data: [
                {% for item in dashboard_data.trends.monthly_data %}
                {{ item.revenue }},
                {% endfor %}
            ],
            borderColor: '#4facfe',
            backgroundColor: 'rgba(79, 172, 254, 0.1)',
            tension: 0.4
        }, {
            label: '{% trans "Expenses" %}',
            data: [
                {% for item in dashboard_data.trends.monthly_data %}
                {{ item.expenses }},
                {% endfor %}
            ],
            borderColor: '#fa709a',
            backgroundColor: 'rgba(250, 112, 154, 0.1)',
            tension: 0.4
        }, {
            label: '{% trans "Net Income" %}',
            data: [
                {% for item in dashboard_data.trends.monthly_data %}
                {{ item.net_income }},
                {% endfor %}
            ],
            borderColor: '#a8edea',
            backgroundColor: 'rgba(168, 237, 234, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        },
        plugins: {
            legend: {
                position: 'top'
            }
        }
    }
});
</script>
{% endif %}
{% endblock %}