{% extends 'base.html' %}
{% load i18n %}
{% load static %}
{% load finance_filters %}

{% block title %}{% trans "Profit & Loss Statement" %}{% endblock %}

{% block extra_css %}
<style>
    .report-header {
        text-align: center;
        margin-bottom: 30px;
        padding: 20px;
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        border-radius: 10px;
    }
    .pl-section {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .section-header {
        background: #f8f9fa;
        padding: 10px 15px;
        margin: -20px -20px 15px -20px;
        border-radius: 8px 8px 0 0;
        font-weight: bold;
        color: #495057;
    }
    .section-header.revenue {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #333;
    }
    .section-header.expense {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        color: #333;
    }
    .account-line {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #f1f1f1;
    }
    .account-line:last-child {
        border-bottom: none;
    }
    .account-line.total {
        font-weight: bold;
        border-top: 2px solid #dee2e6;
        border-bottom: 3px double #dee2e6;
        margin-top: 10px;
        padding-top: 10px;
        font-size: 1.1em;
    }
    .account-line.subtotal {
        font-weight: bold;
        border-top: 1px solid #dee2e6;
        margin-top: 5px;
        padding-top: 5px;
    }
    .account-code {
        color: #6c757d;
        font-size: 0.9em;
        margin-right: 10px;
    }
    .amount {
        font-family: 'Courier New', monospace;
        text-align: right;
        min-width: 120px;
    }
    .net-income {
        background: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 5px;
        padding: 20px;
        margin-top: 20px;
        text-align: center;
    }
    .net-income.loss {
        background: #f8d7da;
        border-color: #f5c6cb;
    }
    .filter-form {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    .summary-metrics {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .metric-item {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: 1px solid #f1f1f1;
    }
    .metric-item:last-child {
        border-bottom: none;
    }
    @media print {
        .filter-form, .btn, .no-print {
            display: none !important;
        }
        .pl-section, .summary-metrics {
            box-shadow: none;
            border: 1px solid #ddd;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Filter Form -->
    <div class="filter-form no-print">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label for="start_date" class="form-label">{% trans "Start Date" %}</label>
                <input type="date" class="form-control" id="start_date" name="start_date" 
                       value="{{ start_date|date:'Y-m-d' }}" required>
            </div>
            <div class="col-md-3">
                <label for="end_date" class="form-label">{% trans "End Date" %}</label>
                <input type="date" class="form-control" id="end_date" name="end_date" 
                       value="{{ end_date|date:'Y-m-d' }}" required>
            </div>
            <div class="col-md-3">
                <div class="form-check mt-4">
                    <input class="form-check-input" type="checkbox" id="include_zero" name="include_zero" 
                           value="true" {% if request.GET.include_zero == 'true' %}checked{% endif %}>
                    <label class="form-check-label" for="include_zero">
                        {% trans "Include Zero Balances" %}
                    </label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> {% trans "Generate Report" %}
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                        <i class="fas fa-print"></i> {% trans "Print" %}
                    </button>
                </div>
            </div>
        </form>
    </div>

    {% if pl_statement %}
    <!-- Report Header -->
    <div class="report-header">
        <h1>{{ pl_statement.school }}</h1>
        <h2>{% trans "Profit & Loss Statement" %}</h2>
        <h3>{{ pl_statement.period.start_date|date:"F d, Y" }} - {{ pl_statement.period.end_date|date:"F d, Y" }}</h3>
    </div>

    <!-- Summary Metrics -->
    <div class="summary-metrics">
        <h5><i class="fas fa-chart-line"></i> {% trans "Summary" %}</h5>
        <div class="metric-item">
            <span>{% trans "Total Revenue" %}</span>
            <span class="amount text-success">{{ pl_statement.revenue.total_revenue|floatformat:2 }}</span>
        </div>
        <div class="metric-item">
            <span>{% trans "Total Expenses" %}</span>
            <span class="amount text-danger">{{ pl_statement.expenses.total_expenses|floatformat:2 }}</span>
        </div>
        <div class="metric-item">
            <span><strong>{% trans "Net Income" %}</strong></span>
            <span class="amount {% if pl_statement.net_income >= 0 %}text-success{% else %}text-danger{% endif %}">
                <strong>{{ pl_statement.net_income|floatformat:2 }}</strong>
            </span>
        </div>
    </div>

    <div class="row">
        <!-- Revenue Section -->
        <div class="col-md-6">
            <div class="pl-section">
                <div class="section-header revenue">
                    <i class="fas fa-arrow-up"></i> {% trans "REVENUE" %}
                </div>

                <!-- Operating Revenue -->
                {% if pl_statement.revenue.operating_revenue %}
                <h6 class="mt-3 mb-2">{% trans "Operating Revenue" %}</h6>
                {% for revenue in pl_statement.revenue.operating_revenue %}
                <div class="account-line">
                    <span>
                        <span class="account-code">{{ revenue.code }}</span>
                        {{ revenue.name }}
                        {% if revenue.name_ar %}<br><small class="text-muted">{{ revenue.name_ar }}</small>{% endif %}
                    </span>
                    <span class="amount">{{ revenue.balance|floatformat:2 }}</span>
                </div>
                {% endfor %}
                {% endif %}

                <!-- Other Revenue -->
                {% if pl_statement.revenue.other_revenue %}
                <h6 class="mt-3 mb-2">{% trans "Other Revenue" %}</h6>
                {% for revenue in pl_statement.revenue.other_revenue %}
                <div class="account-line">
                    <span>
                        <span class="account-code">{{ revenue.code }}</span>
                        {{ revenue.name }}
                        {% if revenue.name_ar %}<br><small class="text-muted">{{ revenue.name_ar }}</small>{% endif %}
                    </span>
                    <span class="amount">{{ revenue.balance|floatformat:2 }}</span>
                </div>
                {% endfor %}
                {% endif %}

                <!-- Total Revenue -->
                <div class="account-line total">
                    <span><strong>{% trans "TOTAL REVENUE" %}</strong></span>
                    <span class="amount text-success"><strong>{{ pl_statement.revenue.total_revenue|floatformat:2 }}</strong></span>
                </div>
            </div>
        </div>

        <!-- Expenses Section -->
        <div class="col-md-6">
            <div class="pl-section">
                <div class="section-header expense">
                    <i class="fas fa-arrow-down"></i> {% trans "EXPENSES" %}
                </div>

                <!-- Operating Expenses -->
                {% if pl_statement.expenses.operating_expenses %}
                <h6 class="mt-3 mb-2">{% trans "Operating Expenses" %}</h6>
                {% for expense in pl_statement.expenses.operating_expenses %}
                <div class="account-line">
                    <span>
                        <span class="account-code">{{ expense.code }}</span>
                        {{ expense.name }}
                        {% if expense.name_ar %}<br><small class="text-muted">{{ expense.name_ar }}</small>{% endif %}
                    </span>
                    <span class="amount">{{ expense.balance|floatformat:2 }}</span>
                </div>
                {% endfor %}
                {% endif %}

                <!-- Administrative Expenses -->
                {% if pl_statement.expenses.administrative_expenses %}
                <h6 class="mt-3 mb-2">{% trans "Administrative Expenses" %}</h6>
                {% for expense in pl_statement.expenses.administrative_expenses %}
                <div class="account-line">
                    <span>
                        <span class="account-code">{{ expense.code }}</span>
                        {{ expense.name }}
                        {% if expense.name_ar %}<br><small class="text-muted">{{ expense.name_ar }}</small>{% endif %}
                    </span>
                    <span class="amount">{{ expense.balance|floatformat:2 }}</span>
                </div>
                {% endfor %}
                {% endif %}

                <!-- Other Expenses -->
                {% if pl_statement.expenses.other_expenses %}
                <h6 class="mt-3 mb-2">{% trans "Other Expenses" %}</h6>
                {% for expense in pl_statement.expenses.other_expenses %}
                <div class="account-line">
                    <span>
                        <span class="account-code">{{ expense.code }}</span>
                        {{ expense.name }}
                        {% if expense.name_ar %}<br><small class="text-muted">{{ expense.name_ar }}</small>{% endif %}
                    </span>
                    <span class="amount">{{ expense.balance|floatformat:2 }}</span>
                </div>
                {% endfor %}
                {% endif %}

                <!-- Total Expenses -->
                <div class="account-line total">
                    <span><strong>{% trans "TOTAL EXPENSES" %}</strong></span>
                    <span class="amount text-danger"><strong>{{ pl_statement.expenses.total_expenses|floatformat:2 }}</strong></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Net Income -->
    <div class="net-income {% if pl_statement.net_income < 0 %}loss{% endif %}">
        <h3>
            {% if pl_statement.net_income >= 0 %}
                <i class="fas fa-arrow-up text-success"></i> {% trans "NET INCOME" %}
            {% else %}
                <i class="fas fa-arrow-down text-danger"></i> {% trans "NET LOSS" %}
            {% endif %}
        </h3>
        <h2 class="{% if pl_statement.net_income >= 0 %}text-success{% else %}text-danger{% endif %}">
            {{ pl_statement.net_income|floatformat:2 }}
        </h2>
        {% if pl_statement.revenue.total_revenue > 0 %}
        <p class="mb-0">
            {% trans "Profit Margin:" %} 
            <strong>
                {{ pl_statement.net_income|percentage:pl_statement.revenue.total_revenue|floatformat:2 }}%
            </strong>
        </p>
        {% endif %}
    </div>

    {% else %}
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle"></i>
        {% trans "Unable to generate profit & loss statement. Please check your account setup and try again." %}
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form when dates change
    document.getElementById('start_date').addEventListener('change', function() {
        this.form.submit();
    });
    
    document.getElementById('end_date').addEventListener('change', function() {
        this.form.submit();
    });
    
    // Auto-submit form when checkbox changes
    document.getElementById('include_zero').addEventListener('change', function() {
        this.form.submit();
    });
});
</script>
{% endblock %}