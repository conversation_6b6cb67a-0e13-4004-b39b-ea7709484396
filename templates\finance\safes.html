{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Safes" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-vault"></i> {% trans "Safes" %}
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#addSafeModal">
                            <i class="fas fa-plus"></i> {% trans "Add Safe" %}
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Safe Name" %}</th>
                                    <th>{% trans "Location" %}</th>
                                    <th>{% trans "Current Balance" %}</th>
                                    <th>{% trans "Responsible Person" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for safe in safes %}
                                <tr>
                                    <td>
                                        <strong>{{ safe.name }}</strong>
                                    </td>
                                    <td>{{ safe.location|default:"-" }}</td>
                                    <td class="text-right">
                                        <strong class="{% if safe.current_balance >= 0 %}text-success{% else %}text-danger{% endif %}">
                                            {{ safe.current_balance|floatformat:2 }}
                                        </strong>
                                    </td>
                                    <td>{{ safe.responsible_person|default:"-" }}</td>
                                    <td>
                                        {% if safe.is_active %}
                                            <span class="badge badge-success">{% trans "Active" %}</span>
                                        {% else %}
                                            <span class="badge badge-secondary">{% trans "Inactive" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-info" onclick="viewSafe('{{ safe.id }}')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-warning" onclick="editSafe('{{ safe.id }}')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-success" onclick="viewTransactions('{{ safe.id }}')">
                                                <i class="fas fa-list"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center">
                                        {% trans "No safes found" %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Safe Modal -->
<div class="modal fade" id="addSafeModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Add Safe" %}</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="addSafeForm">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="form-group">
                        <label>{% trans "Safe Name" %}</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="form-group">
                        <label>{% trans "Location" %}</label>
                        <input type="text" class="form-control" name="location">
                    </div>
                    <div class="form-group">
                        <label>{% trans "Opening Balance" %}</label>
                        <input type="number" class="form-control" name="opening_balance" step="0.01" value="0">
                    </div>
                    <div class="form-group">
                        <label>{% trans "Responsible Person" %}</label>
                        <input type="text" class="form-control" name="responsible_person">
                    </div>
                    <div class="form-group">
                        <label>{% trans "Notes" %}</label>
                        <textarea class="form-control" name="notes" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" name="is_active" id="is_active" checked>
                            <label class="custom-control-label" for="is_active">
                                {% trans "Active" %}
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{% trans "Cancel" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "Save" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function viewSafe(id) {
    console.log('View safe:', id);
}

function editSafe(id) {
    console.log('Edit safe:', id);
}

function viewTransactions(id) {
    console.log('View transactions for safe:', id);
}

document.getElementById('addSafeForm').addEventListener('submit', function(e) {
    e.preventDefault();
    console.log('Add safe form submitted');
});
</script>
{% endblock %}