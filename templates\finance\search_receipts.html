{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Search Receipts" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-search"></i> {% trans "Search Receipts" %}
                    </h3>
                </div>
                <div class="card-body">
                    <!-- Search Filters -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>{% trans "Search Criteria" %}</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>{% trans "Receipt Number" %}</label>
                                        <input type="text" class="form-control" id="receipt-number" placeholder="{% trans 'Enter receipt number' %}">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>{% trans "Student Name" %}</label>
                                        <input type="text" class="form-control" id="student-name" placeholder="{% trans 'Enter student name' %}">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>{% trans "From Date" %}</label>
                                        <input type="date" class="form-control" id="date-from">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>{% trans "To Date" %}</label>
                                        <input type="date" class="form-control" id="date-to">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>{% trans "Payment Method" %}</label>
                                        <select class="form-control" id="payment-method">
                                            <option value="">{% trans "All Methods" %}</option>
                                            <option value="cash">{% trans "Cash" %}</option>
                                            <option value="bank_transfer">{% trans "Bank Transfer" %}</option>
                                            <option value="check">{% trans "Check" %}</option>
                                            <option value="credit_card">{% trans "Credit Card" %}</option>
                                            <option value="online">{% trans "Online Payment" %}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>{% trans "Amount Range" %}</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="amount-from" placeholder="{% trans 'From' %}" step="0.01">
                                            <div class="input-group-append">
                                                <span class="input-group-text">-</span>
                                            </div>
                                            <input type="number" class="form-control" id="amount-to" placeholder="{% trans 'To' %}" step="0.01">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>{% trans "Received By" %}</label>
                                        <select class="form-control" id="received-by">
                                            <option value="">{% trans "All Users" %}</option>
                                            {% for user in users %}
                                                <option value="{{ user.id }}">{{ user.get_full_name|default:user.username }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>&nbsp;</label>
                                        <div>
                                            <button class="btn btn-primary" onclick="searchReceipts()">
                                                <i class="fas fa-search"></i> {% trans "Search" %}
                                            </button>
                                            <button class="btn btn-secondary" onclick="clearFilters()">
                                                <i class="fas fa-times"></i> {% trans "Clear" %}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Search Results -->
                    <div id="search-results">
                        {% if receipts %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>{% trans "Receipt #" %}</th>
                                        <th>{% trans "Date" %}</th>
                                        <th>{% trans "Student" %}</th>
                                        <th>{% trans "Amount" %}</th>
                                        <th>{% trans "Payment Method" %}</th>
                                        <th>{% trans "Received By" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for receipt in receipts %}
                                    <tr>
                                        <td>
                                            <strong>{{ receipt.receipt_number }}</strong>
                                        </td>
                                        <td>{{ receipt.payment_date|date:"Y-m-d" }}</td>
                                        <td>
                                            <div>
                                                <strong>{{ receipt.student.full_name }}</strong>
                                                <br><small class="text-muted">{{ receipt.student.student_id }}</small>
                                            </div>
                                        </td>
                                        <td class="text-right">
                                            <strong>{{ receipt.amount|floatformat:2 }}</strong>
                                        </td>
                                        <td>
                                            <span class="badge badge-info">{{ receipt.get_payment_method_display }}</span>
                                        </td>
                                        <td>{{ receipt.received_by.get_full_name|default:receipt.received_by.username }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-info" onclick="viewReceipt('{{ receipt.id }}')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-success" onclick="printReceipt('{{ receipt.id }}')">
                                                    <i class="fas fa-print"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-warning" onclick="duplicateReceipt('{{ receipt.id }}')">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if is_paginated %}
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1">&laquo; {% trans "First" %}</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                                    </li>
                                {% endif %}

                                <li class="page-item active">
                                    <span class="page-link">
                                        {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">{% trans "Last" %} &raquo;</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}

                        <!-- Summary -->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="row text-center">
                                            <div class="col-md-3">
                                                <h5>{{ total_receipts|default:"0" }}</h5>
                                                <p class="mb-0">{% trans "Total Receipts" %}</p>
                                            </div>
                                            <div class="col-md-3">
                                                <h5>{{ total_amount|floatformat:2|default:"0.00" }}</h5>
                                                <p class="mb-0">{% trans "Total Amount" %}</p>
                                            </div>
                                            <div class="col-md-3">
                                                <h5>{{ average_amount|floatformat:2|default:"0.00" }}</h5>
                                                <p class="mb-0">{% trans "Average Amount" %}</p>
                                            </div>
                                            <div class="col-md-3">
                                                <h5>{{ date_range|default:"-" }}</h5>
                                                <p class="mb-0">{% trans "Date Range" %}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% else %}
                        <div class="alert alert-info text-center">
                            <i class="fas fa-search fa-2x mb-2"></i>
                            <h5>{% trans "No Results" %}</h5>
                            <p>{% trans "Use the search criteria above to find receipts." %}</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function searchReceipts() {
    const params = new URLSearchParams();
    
    const receiptNumber = document.getElementById('receipt-number').value;
    const studentName = document.getElementById('student-name').value;
    const dateFrom = document.getElementById('date-from').value;
    const dateTo = document.getElementById('date-to').value;
    const paymentMethod = document.getElementById('payment-method').value;
    const amountFrom = document.getElementById('amount-from').value;
    const amountTo = document.getElementById('amount-to').value;
    const receivedBy = document.getElementById('received-by').value;
    
    if (receiptNumber) params.append('receipt_number', receiptNumber);
    if (studentName) params.append('student_name', studentName);
    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);
    if (paymentMethod) params.append('payment_method', paymentMethod);
    if (amountFrom) params.append('amount_from', amountFrom);
    if (amountTo) params.append('amount_to', amountTo);
    if (receivedBy) params.append('received_by', receivedBy);
    
    window.location.search = params.toString();
}

function clearFilters() {
    document.getElementById('receipt-number').value = '';
    document.getElementById('student-name').value = '';
    document.getElementById('date-from').value = '';
    document.getElementById('date-to').value = '';
    document.getElementById('payment-method').value = '';
    document.getElementById('amount-from').value = '';
    document.getElementById('amount-to').value = '';
    document.getElementById('received-by').value = '';
}

function viewReceipt(id) {
    window.location.href = `/finance/receipts/${id}/`;
}

function printReceipt(id) {
    window.open(`/finance/receipts/${id}/print/`, '_blank');
}

function duplicateReceipt(id) {
    if (confirm('{% trans "Create a duplicate of this receipt?" %}')) {
        window.location.href = `/finance/receipts/${id}/duplicate/`;
    }
}
</script>
{% endblock %}