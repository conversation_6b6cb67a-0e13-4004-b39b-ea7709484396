{% extends 'base.html' %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{% trans "Transaction Approval" %} - {{ transaction.transaction_id }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-check-circle"></i>
                        {% trans "Transaction Approval" %} - {{ transaction.transaction_id }}
                    </h3>
                    <div>
                        <a href="{% url 'finance:transaction_detail' transaction.pk %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            {% trans "Back to Transaction" %}
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Transaction Summary -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-info-circle"></i>
                                        {% trans "Transaction Information" %}
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <th width="40%">{% trans "Transaction ID" %}:</th>
                                            <td>{{ transaction.transaction_id }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Date" %}:</th>
                                            <td>{{ transaction.transaction_date|date:"Y-m-d" }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Type" %}:</th>
                                            <td>
                                                <span class="badge badge-info">
                                                    {{ transaction.get_transaction_type_display }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Amount" %}:</th>
                                            <td class="font-weight-bold">{{ transaction.total_amount|floatformat:2 }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Status" %}:</th>
                                            <td>
                                                <span class="badge badge-warning">
                                                    {{ transaction.get_status_display }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Created By" %}:</th>
                                            <td>{{ transaction.created_by.get_full_name|default:transaction.created_by.username }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Created At" %}:</th>
                                            <td>{{ transaction.created_at|date:"Y-m-d H:i:s" }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-comment"></i>
                                        {% trans "Description" %}
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p>{{ transaction.description }}</p>
                                    {% if transaction.reference %}
                                    <p><strong>{% trans "Reference" %}:</strong> {{ transaction.reference }}</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Transaction Entries -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-list"></i>
                                        {% trans "Transaction Entries" %}
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>{% trans "Account" %}</th>
                                                    <th>{% trans "Description" %}</th>
                                                    <th>{% trans "Cost Center" %}</th>
                                                    <th class="text-right">{% trans "Debit" %}</th>
                                                    <th class="text-right">{% trans "Credit" %}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for entry in entries %}
                                                <tr>
                                                    <td>
                                                        <strong>{{ entry.account.code }}</strong><br>
                                                        <small class="text-muted">{{ entry.account.name }}</small>
                                                    </td>
                                                    <td>{{ entry.description }}</td>
                                                    <td>
                                                        {% if entry.cost_center %}
                                                            {{ entry.cost_center.code }} - {{ entry.cost_center.name }}
                                                        {% else %}
                                                            -
                                                        {% endif %}
                                                    </td>
                                                    <td class="text-right">
                                                        {% if entry.debit_amount > 0 %}
                                                            {{ entry.debit_amount|floatformat:2 }}
                                                        {% else %}
                                                            -
                                                        {% endif %}
                                                    </td>
                                                    <td class="text-right">
                                                        {% if entry.credit_amount > 0 %}
                                                            {{ entry.credit_amount|floatformat:2 }}
                                                        {% else %}
                                                            -
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                            <tfoot>
                                                <tr class="font-weight-bold">
                                                    <td colspan="3">{% trans "Total" %}</td>
                                                    <td class="text-right">{{ transaction.total_debits|floatformat:2 }}</td>
                                                    <td class="text-right">{{ transaction.total_credits|floatformat:2 }}</td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                    
                                    <!-- Balance Check -->
                                    <div class="mt-3">
                                        {% if transaction.is_balanced %}
                                            <div class="alert alert-success">
                                                <i class="fas fa-check-circle"></i>
                                                {% trans "Transaction is balanced" %} ({% trans "Debits" %} = {% trans "Credits" %})
                                            </div>
                                        {% else %}
                                            <div class="alert alert-danger">
                                                <i class="fas fa-exclamation-triangle"></i>
                                                {% trans "Transaction is not balanced" %} - {% trans "Cannot approve unbalanced transaction" %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Approval Form -->
                    {% if transaction.is_balanced %}
                    <div class="row">
                        <div class="col-md-8 offset-md-2">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-clipboard-check"></i>
                                        {% trans "Approval Decision" %}
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <form method="post">
                                        {% csrf_token %}
                                        
                                        <div class="form-group mb-4">
                                            <label class="form-label">{% trans "Action" %}</label>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="action" id="approve" value="approve" required>
                                                <label class="form-check-label" for="approve">
                                                    <i class="fas fa-check text-success"></i>
                                                    {% trans "Approve Transaction" %}
                                                </label>
                                                <small class="form-text text-muted">
                                                    {% trans "Approve this transaction for posting" %}
                                                </small>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="action" id="reject" value="reject" required>
                                                <label class="form-check-label" for="reject">
                                                    <i class="fas fa-times text-danger"></i>
                                                    {% trans "Reject Transaction" %}
                                                </label>
                                                <small class="form-text text-muted">
                                                    {% trans "Reject this transaction and return to draft status" %}
                                                </small>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group mb-4">
                                            <label for="approval_notes">{% trans "Approval Notes" %}</label>
                                            <textarea class="form-control" id="approval_notes" name="approval_notes" 
                                                    rows="4" placeholder="{% trans 'Optional notes about your approval decision...' %}"></textarea>
                                            <small class="form-text text-muted">
                                                {% trans "These notes will be recorded in the audit trail" %}
                                            </small>
                                        </div>
                                        
                                        <div class="d-flex justify-content-between">
                                            <a href="{% url 'finance:transaction_detail' transaction.pk %}" class="btn btn-secondary">
                                                <i class="fas fa-arrow-left"></i>
                                                {% trans "Cancel" %}
                                            </a>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-check"></i>
                                                {% trans "Submit Decision" %}
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>{% trans "Cannot Approve" %}</strong>
                                <p class="mb-0">{% trans "This transaction cannot be approved because it is not balanced. Please contact the transaction creator to fix the entries." %}</p>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const approveRadio = document.getElementById('approve');
    const rejectRadio = document.getElementById('reject');
    const notesTextarea = document.getElementById('approval_notes');
    const submitBtn = document.querySelector('button[type="submit"]');

    // Update submit button text based on selection
    function updateSubmitButton() {
        if (approveRadio.checked) {
            submitBtn.innerHTML = '<i class="fas fa-check"></i> {% trans "Approve Transaction" %}';
            submitBtn.className = 'btn btn-success';
        } else if (rejectRadio.checked) {
            submitBtn.innerHTML = '<i class="fas fa-times"></i> {% trans "Reject Transaction" %}';
            submitBtn.className = 'btn btn-danger';
        } else {
            submitBtn.innerHTML = '<i class="fas fa-check"></i> {% trans "Submit Decision" %}';
            submitBtn.className = 'btn btn-primary';
        }
    }

    // Add event listeners
    approveRadio.addEventListener('change', updateSubmitButton);
    rejectRadio.addEventListener('change', updateSubmitButton);

    // Make notes required for rejection
    rejectRadio.addEventListener('change', function() {
        if (this.checked) {
            notesTextarea.required = true;
            notesTextarea.placeholder = '{% trans "Please provide a reason for rejection..." %}';
        }
    });

    approveRadio.addEventListener('change', function() {
        if (this.checked) {
            notesTextarea.required = false;
            notesTextarea.placeholder = '{% trans "Optional notes about your approval decision..." %}';
        }
    });

    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        if (rejectRadio.checked && !notesTextarea.value.trim()) {
            e.preventDefault();
            alert('{% trans "Please provide a reason for rejection." %}');
            notesTextarea.focus();
            return false;
        }

        // Confirm action
        const action = approveRadio.checked ? '{% trans "approve" %}' : '{% trans "reject" %}';
        if (!confirm('{% trans "Are you sure you want to" %} ' + action + ' {% trans "this transaction?" %}')) {
            e.preventDefault();
            return false;
        }
    });
});
</script>

<style>
.form-check {
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
    transition: all 0.2s ease;
}

.form-check:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
}

.form-check-input:checked + .form-check-label {
    font-weight: 600;
}

.form-check-input:checked ~ .form-text {
    color: #495057 !important;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.table th {
    border-top: none;
    font-weight: 600;
}

.font-weight-bold {
    font-weight: 600 !important;
}
</style>
{% endblock %}