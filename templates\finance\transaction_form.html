{% extends 'base.html' %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}
    {% if object %}
        {% trans "Edit Transaction" %} - {{ object.transaction_id }}
    {% else %}
        {% trans "New Transaction" %}
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-exchange-alt"></i>
                        {% if object %}
                            {% trans "Edit Transaction" %} - {{ object.transaction_id }}
                        {% else %}
                            {% trans "New Transaction" %}
                        {% endif %}
                    </h3>
                    <div>
                        <a href="{% url 'finance:transactions_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            {% trans "Back to List" %}
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <form method="post" id="transaction-form">
                        {% csrf_token %}
                        
                        <!-- Transaction Information -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-info-circle"></i>
                                    {% trans "Transaction Information" %}
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="{{ form.transaction_date.id_for_label }}">{{ form.transaction_date.label }}</label>
                                            {{ form.transaction_date }}
                                            {% if form.transaction_date.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {{ form.transaction_date.errors.0 }}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="{{ form.transaction_type.id_for_label }}">{{ form.transaction_type.label }}</label>
                                            {{ form.transaction_type }}
                                            {% if form.transaction_type.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {{ form.transaction_type.errors.0 }}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label for="{{ form.description.id_for_label }}">{{ form.description.label }}</label>
                                    {{ form.description }}
                                    {% if form.description.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.description.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="form-group mb-3">
                                            <label for="{{ form.reference.id_for_label }}">{{ form.reference.label }}</label>
                                            {{ form.reference }}
                                            {% if form.reference.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {{ form.reference.errors.0 }}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group mb-3">
                                            <div class="form-check">
                                                {{ form.requires_approval }}
                                                <label class="form-check-label" for="{{ form.requires_approval.id_for_label }}">
                                                    {{ form.requires_approval.label }}
                                                </label>
                                            </div>
                                            <small class="form-text text-muted">{{ form.requires_approval.help_text }}</small>
                                            {% if form.requires_approval.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {{ form.requires_approval.errors.0 }}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Transaction Entries -->
                        <div class="card mb-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-list"></i>
                                    {% trans "Transaction Entries" %}
                                </h5>
                                <button type="button" id="add-entry-btn" class="btn btn-sm btn-success">
                                    <i class="fas fa-plus"></i>
                                    {% trans "Add Entry" %}
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="entries-container">
                                    <!-- Entries will be added here dynamically -->
                                </div>
                                
                                <!-- Balance Summary -->
                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">{% trans "Total Debits" %}</h6>
                                                <h4 id="total-debits" class="text-primary">0.00</h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">{% trans "Total Credits" %}</h6>
                                                <h4 id="total-credits" class="text-success">0.00</h4>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="alert mt-3" id="balance-status">
                                    <i class="fas fa-info-circle"></i>
                                    <span id="balance-message">{% trans "Transaction must be balanced (debits = credits)" %}</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Form Actions -->
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <a href="{% url 'finance:transactions_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-times"></i>
                                        {% trans "Cancel" %}
                                    </a>
                                    <div>
                                        <button type="submit" name="action" value="save_draft" class="btn btn-outline-primary">
                                            <i class="fas fa-save"></i>
                                            {% trans "Save as Draft" %}
                                        </button>
                                        <button type="submit" name="action" value="save" class="btn btn-primary" id="save-transaction-btn" disabled>
                                            <i class="fas fa-check"></i>
                                            {% trans "Save Transaction" %}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Entry Template (Hidden) -->
<div id="entry-template" style="display: none;">
    <div class="card mb-3 entry-card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="mb-0">{% trans "Entry" %} <span class="entry-number"></span></h6>
            <button type="button" class="btn btn-sm btn-outline-danger remove-entry-btn">
                <i class="fas fa-trash"></i>
                {% trans "Remove" %}
            </button>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label>{% trans "Account" %}</label>
                        <select class="form-control entry-account" name="entry_account" required>
                            <option value="">{% trans "Select Account" %}</option>
                            {% for account in accounts %}
                                <option value="{{ account.id }}" data-code="{{ account.code }}" data-name="{{ account.name }}">
                                    {{ account.code }} - {{ account.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label>{% trans "Cost Center" %}</label>
                        <select class="form-control entry-cost-center" name="entry_cost_center">
                            <option value="">{% trans "Select Cost Center" %}</option>
                            {% for cost_center in cost_centers %}
                                <option value="{{ cost_center.id }}">
                                    {{ cost_center.code }} - {{ cost_center.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="form-group mb-3">
                <label>{% trans "Description" %}</label>
                <input type="text" class="form-control entry-description" name="entry_description" placeholder="{% trans 'Entry description' %}">
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label>{% trans "Debit Amount" %}</label>
                        <input type="number" class="form-control entry-debit" name="entry_debit" step="0.01" min="0" placeholder="0.00">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label>{% trans "Credit Amount" %}</label>
                        <input type="number" class="form-control entry-credit" name="entry_credit" step="0.01" min="0" placeholder="0.00">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label>{% trans "Reference" %}</label>
                        <input type="text" class="form-control entry-reference" name="entry_reference" placeholder="{% trans 'Entry reference' %}">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let entryCounter = 0;
    const entriesContainer = document.getElementById('entries-container');
    const addEntryBtn = document.getElementById('add-entry-btn');
    const entryTemplate = document.getElementById('entry-template');
    const saveBtn = document.getElementById('save-transaction-btn');
    const totalDebitsEl = document.getElementById('total-debits');
    const totalCreditsEl = document.getElementById('total-credits');
    const balanceStatusEl = document.getElementById('balance-status');
    const balanceMessageEl = document.getElementById('balance-message');

    // Add initial entries
    addEntry();
    addEntry();

    // Add entry button click handler
    addEntryBtn.addEventListener('click', function() {
        addEntry();
    });

    // Form submission handler
    document.getElementById('transaction-form').addEventListener('submit', function(e) {
        const action = e.submitter.value;
        if (action === 'save' && !isBalanced()) {
            e.preventDefault();
            alert('{% trans "Transaction must be balanced before saving." %}');
            return false;
        }
    });

    function addEntry() {
        entryCounter++;
        const entryHtml = entryTemplate.innerHTML;
        const entryDiv = document.createElement('div');
        entryDiv.innerHTML = entryHtml;
        entryDiv.querySelector('.entry-number').textContent = entryCounter;
        
        // Update form field names
        const fields = entryDiv.querySelectorAll('input, select');
        fields.forEach(field => {
            const baseName = field.name;
            field.name = `entry_${entryCounter}_${baseName.replace('entry_', '')}`;
            field.id = `entry_${entryCounter}_${baseName.replace('entry_', '')}`;
        });

        entriesContainer.appendChild(entryDiv);

        // Add event listeners
        const removeBtn = entryDiv.querySelector('.remove-entry-btn');
        removeBtn.addEventListener('click', function() {
            if (entriesContainer.children.length > 2) {
                entryDiv.remove();
                updateTotals();
            } else {
                alert('{% trans "At least two entries are required for a transaction." %}');
            }
        });

        // Add amount change listeners
        const debitInput = entryDiv.querySelector('.entry-debit');
        const creditInput = entryDiv.querySelector('.entry-credit');
        
        debitInput.addEventListener('input', function() {
            if (this.value && parseFloat(this.value) > 0) {
                creditInput.value = '';
            }
            updateTotals();
        });

        creditInput.addEventListener('input', function() {
            if (this.value && parseFloat(this.value) > 0) {
                debitInput.value = '';
            }
            updateTotals();
        });

        updateTotals();
    }

    function updateTotals() {
        let totalDebits = 0;
        let totalCredits = 0;

        document.querySelectorAll('.entry-debit').forEach(input => {
            const value = parseFloat(input.value) || 0;
            totalDebits += value;
        });

        document.querySelectorAll('.entry-credit').forEach(input => {
            const value = parseFloat(input.value) || 0;
            totalCredits += value;
        });

        totalDebitsEl.textContent = totalDebits.toFixed(2);
        totalCreditsEl.textContent = totalCredits.toFixed(2);

        // Update balance status
        const balanced = Math.abs(totalDebits - totalCredits) < 0.01;
        const hasEntries = totalDebits > 0 || totalCredits > 0;

        if (!hasEntries) {
            balanceStatusEl.className = 'alert mt-3 alert-info';
            balanceMessageEl.innerHTML = '<i class="fas fa-info-circle"></i> {% trans "Add transaction entries to see balance status" %}';
            saveBtn.disabled = true;
        } else if (balanced && totalDebits > 0) {
            balanceStatusEl.className = 'alert mt-3 alert-success';
            balanceMessageEl.innerHTML = '<i class="fas fa-check-circle"></i> {% trans "Transaction is balanced" %}';
            saveBtn.disabled = false;
        } else {
            balanceStatusEl.className = 'alert mt-3 alert-danger';
            balanceMessageEl.innerHTML = '<i class="fas fa-exclamation-triangle"></i> {% trans "Transaction is not balanced" %}';
            saveBtn.disabled = true;
        }
    }

    function isBalanced() {
        const totalDebits = parseFloat(totalDebitsEl.textContent) || 0;
        const totalCredits = parseFloat(totalCreditsEl.textContent) || 0;
        return Math.abs(totalDebits - totalCredits) < 0.01 && totalDebits > 0;
    }
});
</script>

<style>
.entry-card {
    border-left: 4px solid #007bff;
}

.entry-card:nth-child(even) {
    border-left-color: #28a745;
}

#balance-status {
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.entry-debit:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.entry-credit:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}
</style>
{% endblock %}