{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Transaction Detail" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-exchange-alt"></i> {% trans "Transaction Detail" %}
                    </h3>
                    <div class="card-tools">
                        <a href="{% url 'finance:transactions' %}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> {% trans "Back to Transactions" %}
                        </a>
                        {% if not transaction.is_posted %}
                        <button class="btn btn-warning btn-sm" onclick="editTransaction()">
                            <i class="fas fa-edit"></i> {% trans "Edit" %}
                        </button>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>{% trans "Transaction Information" %}</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>{% trans "Reference Number" %}:</strong></td>
                                    <td>{{ transaction.reference_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Date" %}:</strong></td>
                                    <td>{{ transaction.transaction_date|date:"F d, Y" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Type" %}:</strong></td>
                                    <td>
                                        <span class="badge badge-{% if transaction.transaction_type == 'receipt' %}success{% elif transaction.transaction_type == 'payment' %}danger{% else %}info{% endif %}">
                                            {{ transaction.get_transaction_type_display }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Status" %}:</strong></td>
                                    <td>
                                        {% if transaction.is_posted %}
                                            <span class="badge badge-success">{% trans "Posted" %}</span>
                                        {% else %}
                                            <span class="badge badge-warning">{% trans "Draft" %}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Total Amount" %}:</strong></td>
                                    <td><strong class="text-primary">{{ transaction.total_amount|floatformat:2 }}</strong></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>{% trans "Additional Information" %}</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>{% trans "Created By" %}:</strong></td>
                                    <td>{{ transaction.created_by.get_full_name|default:transaction.created_by.username }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Created Date" %}:</strong></td>
                                    <td>{{ transaction.created_at|date:"F d, Y H:i" }}</td>
                                </tr>
                                {% if transaction.updated_by %}
                                <tr>
                                    <td><strong>{% trans "Last Updated By" %}:</strong></td>
                                    <td>{{ transaction.updated_by.get_full_name|default:transaction.updated_by.username }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Last Updated" %}:</strong></td>
                                    <td>{{ transaction.updated_at|date:"F d, Y H:i" }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>

                    {% if transaction.description %}
                    <div class="row mt-3">
                        <div class="col-12">
                            <h5>{% trans "Description" %}</h5>
                            <p class="text-muted">{{ transaction.description }}</p>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Transaction Entries -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>{% trans "Transaction Entries" %}</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>{% trans "Account" %}</th>
                                            <th>{% trans "Description" %}</th>
                                            <th class="text-right">{% trans "Debit" %}</th>
                                            <th class="text-right">{% trans "Credit" %}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for entry in transaction.entries.all %}
                                        <tr>
                                            <td>
                                                <strong>{{ entry.account.code }}</strong> - {{ entry.account.name }}
                                            </td>
                                            <td>{{ entry.description|default:"-" }}</td>
                                            <td class="text-right">
                                                {% if entry.debit_amount %}
                                                    {{ entry.debit_amount|floatformat:2 }}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td class="text-right">
                                                {% if entry.credit_amount %}
                                                    {{ entry.credit_amount|floatformat:2 }}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="4" class="text-center text-muted">
                                                {% trans "No entries found" %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                    <tfoot>
                                        <tr class="font-weight-bold">
                                            <td colspan="2">{% trans "Total" %}</td>
                                            <td class="text-right">
                                                {{ transaction.entries.all|sum_debit|floatformat:2 }}
                                            </td>
                                            <td class="text-right">
                                                {{ transaction.entries.all|sum_credit|floatformat:2 }}
                                            </td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function editTransaction() {
    console.log('Edit transaction');
}
</script>
{% endblock %}