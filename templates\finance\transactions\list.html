{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Transactions" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-exchange-alt"></i> {% trans "Transactions" %}
                    </h3>
                    <div class="card-tools">
                        <a href="{% url 'finance:add_journal_entry' %}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> {% trans "New Transaction" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="date-from" placeholder="{% trans 'From Date' %}">
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="date-to" placeholder="{% trans 'To Date' %}">
                        </div>
                        <div class="col-md-3">
                            <input type="text" class="form-control" id="reference-search" placeholder="{% trans 'Reference Number' %}">
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-info" onclick="filterTransactions()">
                                <i class="fas fa-search"></i> {% trans "Search" %}
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Date" %}</th>
                                    <th>{% trans "Reference" %}</th>
                                    <th>{% trans "Description" %}</th>
                                    <th>{% trans "Type" %}</th>
                                    <th>{% trans "Amount" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in transactions %}
                                <tr>
                                    <td>{{ transaction.transaction_date|date:"Y-m-d" }}</td>
                                    <td>
                                        <strong>{{ transaction.reference_number }}</strong>
                                    </td>
                                    <td>{{ transaction.description|truncatechars:50 }}</td>
                                    <td>
                                        <span class="badge badge-{% if transaction.transaction_type == 'receipt' %}success{% elif transaction.transaction_type == 'payment' %}danger{% else %}info{% endif %}">
                                            {{ transaction.get_transaction_type_display }}
                                        </span>
                                    </td>
                                    <td class="text-right">{{ transaction.total_amount|floatformat:2 }}</td>
                                    <td>
                                        {% if transaction.is_posted %}
                                            <span class="badge badge-success">{% trans "Posted" %}</span>
                                        {% else %}
                                            <span class="badge badge-warning">{% trans "Draft" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'finance:transaction_detail' transaction.pk %}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if not transaction.is_posted %}
                                            <button class="btn btn-sm btn-warning" onclick="editTransaction('{{ transaction.id }}')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center text-muted">
                                        {% trans "No transactions found" %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if is_paginated %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1">&laquo; {% trans "First" %}</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">{% trans "Last" %} &raquo;</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function filterTransactions() {
    const dateFrom = document.getElementById('date-from').value;
    const dateTo = document.getElementById('date-to').value;
    const reference = document.getElementById('reference-search').value;
    
    const params = new URLSearchParams();
    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);
    if (reference) params.append('reference', reference);
    
    window.location.search = params.toString();
}

function editTransaction(id) {
    console.log('Edit transaction:', id);
}
</script>
{% endblock %}