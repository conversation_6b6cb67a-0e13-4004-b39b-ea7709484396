{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Health Alerts Dashboard" %}{% endblock %}

{% block extra_css %}
<style>
    .alert-card {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        text-align: center;
    }
    
    .alert-card.warning {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        color: #333;
    }
    
    .alert-card.info {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    }
    
    .metric-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .alert-item {
        border-left: 4px solid #dc3545;
        padding: 15px;
        margin-bottom: 15px;
        background-color: #f8f9fa;
        border-radius: 5px;
        transition: all 0.3s ease;
    }
    
    .alert-item:hover {
        transform: translateX(5px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .alert-item.critical { border-left-color: #dc3545; }
    .alert-item.high { border-left-color: #fd7e14; }
    .alert-item.medium { border-left-color: #ffc107; }
    .alert-item.low { border-left-color: #17a2b8; }
    
    .alert-item.acknowledged { opacity: 0.7; }
    .alert-item.resolved { opacity: 0.5; }
    
    .severity-bar {
        height: 20px;
        border-radius: 10px;
        margin-bottom: 5px;
    }
    
    .severity-critical { background: linear-gradient(90deg, #dc3545 0%, #c82333 100%); }
    .severity-high { background: linear-gradient(90deg, #fd7e14 0%, #e8590c 100%); }
    .severity-medium { background: linear-gradient(90deg, #ffc107 0%, #e0a800 100%); }
    .severity-low { background: linear-gradient(90deg, #17a2b8 0%, #138496 100%); }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-bell text-danger"></i> {% trans "Health Alerts Dashboard" %}</h2>
                <div>
                    <a href="{% url 'health:dashboard' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> {% trans "Back to Health Dashboard" %}
                    </a>
                    <button class="btn btn-primary" onclick="createAlertRule()">
                        <i class="fas fa-plus"></i> {% trans "Create Alert Rule" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="alert-card">
                <div class="metric-number">{{ active_alerts }}</div>
                <div>{% trans "Active Alerts" %}</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="alert-card warning">
                <div class="metric-number">{{ overdue_alerts }}</div>
                <div>{% trans "Overdue Alerts" %}</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="alert-card info">
                <div class="metric-number">{{ total_alerts }}</div>
                <div>{% trans "Total Alerts" %}</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="alert-card" style="background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);">
                <div class="metric-number">{{ alert_rules.count }}</div>
                <div>{% trans "Alert Rules" %}</div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="status" class="form-label">{% trans "Status" %}</label>
                    <select class="form-control" id="status" name="status">
                        <option value="">{% trans "All Status" %}</option>
                        {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="severity" class="form-label">{% trans "Severity" %}</label>
                    <select class="form-control" id="severity" name="severity">
                        <option value="">{% trans "All Severities" %}</option>
                        {% for value, label in severity_choices %}
                        <option value="{{ value }}" {% if severity_filter == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-6">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter"></i> {% trans "Apply Filter" %}
                        </button>
                        <a href="{% url 'health:health_alerts_dashboard' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> {% trans "Clear" %}
                        </a>
                        <button type="button" class="btn btn-success" onclick="acknowledgeAllAlerts()">
                            <i class="fas fa-check-double"></i> {% trans "Acknowledge All" %}
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="row">
        <!-- Alert Statistics -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> {% trans "Alerts by Severity" %}</h5>
                </div>
                <div class="card-body">
                    {% for severity_name, count in severity_stats.items %}
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{{ severity_name }}</span>
                            <strong>{{ count }}</strong>
                        </div>
                        <div class="severity-bar severity-{{ severity_name|lower }}" style="width: {% if count > 0 %}{{ count|floatformat:0 }}%{% else %}0%{% endif %}"></div>
                    </div>
                    {% empty %}
                    <p class="text-muted">{% trans "No alert data available" %}</p>
                    {% endfor %}
                </div>
            </div>

            <!-- Alert Rules -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-cogs"></i> {% trans "Active Alert Rules" %}</h5>
                </div>
                <div class="card-body">
                    {% for rule in alert_rules %}
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                        <div>
                            <strong>{{ rule.name }}</strong>
                            <br>
                            <small class="text-muted">{{ rule.description|truncatewords:8 }}</small>
                        </div>
                        <div>
                            <span class="badge bg-{% if rule.severity == 'critical' %}danger{% elif rule.severity == 'high' %}warning{% else %}info{% endif %}">
                                {{ rule.get_severity_display }}
                            </span>
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted">{% trans "No active alert rules" %}</p>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Alerts List -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list"></i> {% trans "Health Alerts" %} ({{ page_obj.paginator.count }} {% trans "total" %})</h5>
                </div>
                <div class="card-body">
                    {% if page_obj %}
                    {% for alert in page_obj %}
                    <div class="alert-item {{ alert.alert_rule.severity }} {{ alert.status }}">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-2">
                                    <h6 class="mb-0 me-2">{{ alert.title }}</h6>
                                    <span class="badge bg-{% if alert.alert_rule.severity == 'critical' %}danger{% elif alert.alert_rule.severity == 'high' %}warning{% elif alert.alert_rule.severity == 'medium' %}info{% else %}secondary{% endif %}">
                                        {{ alert.alert_rule.get_severity_display }}
                                    </span>
                                </div>
                                <p class="mb-2">{{ alert.message }}</p>
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted">
                                            <i class="fas fa-clock"></i> {% trans "Triggered:" %} {{ alert.triggered_at|date:"M d, Y H:i" }}
                                        </small>
                                    </div>
                                    <div class="col-md-6">
                                        {% if alert.acknowledged_at %}
                                        <small class="text-muted">
                                            <i class="fas fa-user-check"></i> {% trans "Acknowledged by:" %} {{ alert.acknowledged_by.get_full_name }}
                                        </small>
                                        {% endif %}
                                    </div>
                                </div>
                                {% if alert.affected_students.exists %}
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-users"></i> {% trans "Affected students:" %}
                                        {% for student in alert.affected_students.all|slice:":3" %}
                                            {{ student.get_full_name }}{% if not forloop.last %}, {% endif %}
                                        {% endfor %}
                                        {% if alert.affected_students.count > 3 %}
                                            {% trans "and" %} {{ alert.affected_students.count|add:"-3" }} {% trans "more" %}
                                        {% endif %}
                                    </small>
                                </div>
                                {% endif %}
                            </div>
                            <div class="ms-3">
                                <div class="btn-group-vertical btn-group-sm" role="group">
                                    {% if alert.status == 'active' %}
                                    <button class="btn btn-outline-success" title="{% trans 'Acknowledge' %}" 
                                            onclick="acknowledgeAlert({{ alert.id }})">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button class="btn btn-outline-primary" title="{% trans 'Resolve' %}" 
                                            onclick="resolveAlert({{ alert.id }})">
                                        <i class="fas fa-check-double"></i>
                                    </button>
                                    {% endif %}
                                    <button class="btn btn-outline-info" title="{% trans 'View Details' %}" 
                                            onclick="viewAlertDetails({{ alert.id }})">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        {% if alert.status != 'active' %}
                        <div class="mt-2 pt-2 border-top">
                            <div class="row">
                                {% if alert.acknowledged_at %}
                                <div class="col-md-6">
                                    <small class="text-success">
                                        <i class="fas fa-check"></i> {% trans "Acknowledged" %} {{ alert.acknowledged_at|date:"M d, H:i" }}
                                    </small>
                                </div>
                                {% endif %}
                                {% if alert.resolved_at %}
                                <div class="col-md-6">
                                    <small class="text-primary">
                                        <i class="fas fa-check-double"></i> {% trans "Resolved" %} {{ alert.resolved_at|date:"M d, H:i" }}
                                    </small>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}

                    <!-- Pagination -->
                    {% if page_obj.has_other_pages %}
                    <nav aria-label="Health alerts pagination">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.severity %}&severity={{ request.GET.severity }}{% endif %}">{% trans "First" %}</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.severity %}&severity={{ request.GET.severity }}{% endif %}">{% trans "Previous" %}</a>
                                </li>
                            {% endif %}
                            
                            <li class="page-item active">
                                <span class="page-link">
                                    {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>
                            
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.severity %}&severity={{ request.GET.severity }}{% endif %}">{% trans "Next" %}</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.severity %}&severity={{ request.GET.severity }}{% endif %}">{% trans "Last" %}</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-bell fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">{% trans "No Health Alerts Found" %}</h5>
                        <p class="text-muted">{% trans "No health alerts match your current filters." %}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Alert Details Modal -->
<div class="modal fade" id="alertDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Alert Details" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="alertDetailsContent">
                <!-- Content loaded via AJAX -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function acknowledgeAlert(alertId) {
    if (confirm('{% trans "Acknowledge this alert?" %}')) {
        // In a real implementation, you would send an AJAX request to acknowledge the alert
        alert('{% trans "Alert acknowledged (this would be implemented via AJAX)" %}');
        location.reload();
    }
}

function resolveAlert(alertId) {
    const resolution = prompt('{% trans "Enter resolution notes:" %}');
    if (resolution) {
        // In a real implementation, you would send an AJAX request to resolve the alert
        alert('{% trans "Alert resolved (this would be implemented via AJAX)" %}');
        location.reload();
    }
}

function acknowledgeAllAlerts() {
    if (confirm('{% trans "Acknowledge all active alerts?" %}')) {
        // In a real implementation, you would send an AJAX request to acknowledge all alerts
        alert('{% trans "All alerts acknowledged (this would be implemented via AJAX)" %}');
        location.reload();
    }
}

function viewAlertDetails(alertId) {
    $('#alertDetailsContent').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> {% trans "Loading..." %}</div>');
    $('#alertDetailsModal').modal('show');
    
    // In a real implementation, you would load details via AJAX
    setTimeout(function() {
        $('#alertDetailsContent').html('<p>{% trans "Alert details would be loaded here via AJAX." %}</p>');
    }, 1000);
}

function createAlertRule() {
    // In a real implementation, you would redirect to create alert rule form
    alert('{% trans "Create alert rule functionality would be implemented here" %}');
}

$(document).ready(function() {
    // Auto-submit form when filters change
    $('#status, #severity').change(function() {
        $(this).closest('form').submit();
    });
});
</script>
{% endblock %}