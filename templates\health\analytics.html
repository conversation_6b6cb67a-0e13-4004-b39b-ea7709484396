{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Health Analytics" %}{% endblock %}

{% block extra_css %}
<style>
    .analytics-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 25px;
        margin-bottom: 20px;
        text-align: center;
        transition: transform 0.3s ease;
    }
    .analytics-card:hover {
        transform: translateY(-5px);
    }
    .analytics-card.vaccination {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    .analytics-card.screening {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
    .analytics-card.incident {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    .analytics-card.compliance {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        color: #333;
    }
    
    .metric-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .chart-container {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .alert-item {
        border-left: 4px solid #dc3545;
        padding: 10px;
        margin-bottom: 10px;
        background-color: #f8f9fa;
        border-radius: 5px;
    }
    
    .alert-item.medium {
        border-left-color: #ffc107;
    }
    
    .alert-item.low {
        border-left-color: #17a2b8;
    }
    
    .grade-stat-row {
        transition: all 0.3s ease;
    }
    
    .grade-stat-row:hover {
        background-color: #f8f9fa;
        transform: translateX(5px);
    }
    
    .condition-bar {
        height: 20px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        border-radius: 10px;
        margin-bottom: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-chart-bar text-primary"></i> {% trans "Health Analytics Dashboard" %}</h2>
                <div>
                    <a href="{% url 'health:dashboard' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> {% trans "Back to Health Dashboard" %}
                    </a>
                    <button class="btn btn-success" onclick="exportAnalytics()">
                        <i class="fas fa-download"></i> {% trans "Export Report" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="date_from" class="form-label">{% trans "From Date" %}</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from|date:'Y-m-d' }}">
                </div>
                <div class="col-md-4">
                    <label for="date_to" class="form-label">{% trans "To Date" %}</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to|date:'Y-m-d' }}">
                </div>
                <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter"></i> {% trans "Apply Filter" %}
                        </button>
                        <a href="{% url 'health:analytics' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> {% trans "Clear" %}
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="analytics-card vaccination">
                <div class="metric-number">{{ vaccination_completion_rate }}%</div>
                <h5>{% trans "Vaccination Rate" %}</h5>
                <p class="mb-0">
                    <small>{{ overdue_vaccinations }} {% trans "overdue" %}</small>
                </p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="analytics-card screening">
                <div class="metric-number">{{ total_screenings }}</div>
                <h5>{% trans "Screenings Completed" %}</h5>
                <p class="mb-0">
                    <small>{{ overdue_screenings }} {% trans "overdue" %}</small>
                </p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="analytics-card incident">
                <div class="metric-number">{{ total_incidents }}</div>
                <h5>{% trans "Medical Incidents" %}</h5>
                <p class="mb-0">
                    <small>{% trans "This period" %}</small>
                </p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="analytics-card compliance">
                <div class="metric-number">{{ compliance_rate }}%</div>
                <h5>{% trans "Compliance Rate" %}</h5>
                <p class="mb-0">
                    <small>{{ active_alerts }} {% trans "active alerts" %}</small>
                </p>
            </div>
        </div>
    </div>

    <!-- Charts Row 1 -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="chart-container">
                <h5><i class="fas fa-chart-line"></i> {% trans "Incident Trends (Last 12 Months)" %}</h5>
                <canvas id="incidentTrendChart" width="400" height="200"></canvas>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="chart-container">
                <h5><i class="fas fa-chart-pie"></i> {% trans "BMI Distribution" %}</h5>
                <canvas id="bmiChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Charts Row 2 -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="chart-container">
                <h5><i class="fas fa-chart-bar"></i> {% trans "Health Statistics by Grade" %}</h5>
                <canvas id="gradeStatsChart" width="400" height="200"></canvas>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-disease"></i> {% trans "Top Chronic Conditions" %}</h5>
                </div>
                <div class="card-body">
                    {% for condition, count in top_chronic_conditions %}
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{{ condition }}</span>
                            <strong>{{ count }} {% trans "students" %}</strong>
                        </div>
                        <div class="condition-bar" style="width: {{ count|floatformat:0 }}%"></div>
                    </div>
                    {% empty %}
                    <p class="text-muted">{% trans "No chronic conditions recorded" %}</p>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Data Tables -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-graduation-cap"></i> {% trans "Health Statistics by Grade" %}</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>{% trans "Grade" %}</th>
                                    <th>{% trans "Students" %}</th>
                                    <th>{% trans "Incidents" %}</th>
                                    <th>{% trans "Incident Rate" %}</th>
                                    <th>{% trans "Status" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stat in grade_stats %}
                                <tr class="grade-stat-row">
                                    <td><strong>{{ stat.grade }}</strong></td>
                                    <td>{{ stat.students }}</td>
                                    <td>{{ stat.incidents }}</td>
                                    <td>{{ stat.incident_rate|floatformat:1 }}%</td>
                                    <td>
                                        {% if stat.incident_rate < 5 %}
                                            <span class="badge bg-success">{% trans "Low Risk" %}</span>
                                        {% elif stat.incident_rate < 10 %}
                                            <span class="badge bg-warning">{% trans "Medium Risk" %}</span>
                                        {% else %}
                                            <span class="badge bg-danger">{% trans "High Risk" %}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-exclamation-triangle"></i> {% trans "Recent Health Alerts" %}</h5>
                </div>
                <div class="card-body">
                    {% for alert in recent_alerts %}
                    <div class="alert-item {{ alert.alert_rule.severity }}">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <strong>{{ alert.title }}</strong>
                                <br>
                                <small class="text-muted">{{ alert.triggered_at|date:"M d, Y H:i" }}</small>
                                <br>
                                <small>{{ alert.message|truncatewords:10 }}</small>
                            </div>
                            <span class="badge bg-{% if alert.alert_rule.severity == 'critical' %}danger{% elif alert.alert_rule.severity == 'high' %}warning{% else %}info{% endif %}">
                                {{ alert.get_status_display }}
                            </span>
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted">{% trans "No recent alerts" %}</p>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Incident Trend Chart
    const trendCtx = document.getElementById('incidentTrendChart').getContext('2d');
    new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: {{ trend_labels|safe }},
            datasets: [{
                label: '{% trans "Medical Incidents" %}',
                data: {{ incident_trends|safe }},
                borderColor: '#f093fb',
                backgroundColor: 'rgba(240, 147, 251, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
    
    // BMI Distribution Chart
    const bmiCtx = document.getElementById('bmiChart').getContext('2d');
    new Chart(bmiCtx, {
        type: 'doughnut',
        data: {
            labels: ['{% trans "Underweight" %}', '{% trans "Normal" %}', '{% trans "Overweight" %}', '{% trans "Obese" %}'],
            datasets: [{
                data: [
                    {{ bmi_distribution.underweight }},
                    {{ bmi_distribution.normal }},
                    {{ bmi_distribution.overweight }},
                    {{ bmi_distribution.obese }}
                ],
                backgroundColor: [
                    '#17a2b8',
                    '#28a745',
                    '#ffc107',
                    '#dc3545'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // Grade Statistics Chart
    const gradeCtx = document.getElementById('gradeStatsChart').getContext('2d');
    new Chart(gradeCtx, {
        type: 'bar',
        data: {
            labels: [{% for stat in grade_stats %}'{{ stat.grade }}'{% if not forloop.last %},{% endif %}{% endfor %}],
            datasets: [{
                label: '{% trans "Students" %}',
                data: [{% for stat in grade_stats %}{{ stat.students }}{% if not forloop.last %},{% endif %}{% endfor %}],
                backgroundColor: 'rgba(102, 126, 234, 0.8)',
                borderColor: '#667eea',
                borderWidth: 1
            }, {
                label: '{% trans "Incidents" %}',
                data: [{% for stat in grade_stats %}{{ stat.incidents }}{% if not forloop.last %},{% endif %}{% endfor %}],
                backgroundColor: 'rgba(240, 147, 251, 0.8)',
                borderColor: '#f093fb',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});

function exportAnalytics() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'pdf');
    window.open(`${window.location.pathname}?${params.toString()}`, '_blank');
}
</script>
{% endblock %}