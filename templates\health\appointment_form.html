{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Medical Appointment" %} - {% trans "School ERP" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-calendar-plus text-primary me-2"></i>
                    {% if appointment %}
                        {% trans "Edit Medical Appointment" %}
                    {% else %}
                        {% trans "Schedule Medical Appointment" %}
                    {% endif %}
                </h1>
                <a href="{% url 'health:appointment_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    {% trans "Back to Appointments" %}
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-md me-2"></i>
                        {% trans "Appointment Details" %}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.health_profile.id_for_label }}" class="form-label">
                                        <i class="fas fa-user me-1"></i>
                                        {% trans "Student" %}
                                    </label>
                                    {{ form.health_profile }}
                                    {% if form.health_profile.errors %}
                                        <div class="text-danger small">{{ form.health_profile.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.appointment_type.id_for_label }}" class="form-label">
                                        <i class="fas fa-stethoscope me-1"></i>
                                        {% trans "Appointment Type" %}
                                    </label>
                                    {{ form.appointment_type }}
                                    {% if form.appointment_type.errors %}
                                        <div class="text-danger small">{{ form.appointment_type.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.appointment_date.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar me-1"></i>
                                        {% trans "Date" %}
                                    </label>
                                    {{ form.appointment_date }}
                                    {% if form.appointment_date.errors %}
                                        <div class="text-danger small">{{ form.appointment_date.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.appointment_time.id_for_label }}" class="form-label">
                                        <i class="fas fa-clock me-1"></i>
                                        {% trans "Time" %}
                                    </label>
                                    {{ form.appointment_time }}
                                    {% if form.appointment_time.errors %}
                                        <div class="text-danger small">{{ form.appointment_time.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.provider_name.id_for_label }}" class="form-label">
                                <i class="fas fa-user-md me-1"></i>
                                {% trans "Healthcare Provider" %}
                            </label>
                            {{ form.provider_name }}
                            {% if form.provider_name.errors %}
                                <div class="text-danger small">{{ form.provider_name.errors }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.location.id_for_label }}" class="form-label">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                {% trans "Location" %}
                            </label>
                            {{ form.location }}
                            {% if form.location.errors %}
                                <div class="text-danger small">{{ form.location.errors }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.purpose.id_for_label }}" class="form-label">
                                <i class="fas fa-clipboard me-1"></i>
                                {% trans "Purpose/Reason" %}
                            </label>
                            {{ form.purpose }}
                            {% if form.purpose.errors %}
                                <div class="text-danger small">{{ form.purpose.errors }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">
                                <i class="fas fa-sticky-note me-1"></i>
                                {% trans "Additional Notes" %}
                            </label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="text-danger small">{{ form.notes.errors }}</div>
                            {% endif %}
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    {{ form.parent_notified }}
                                    <label class="form-check-label" for="{{ form.parent_notified.id_for_label }}">
                                        <i class="fas fa-bell me-1"></i>
                                        {% trans "Parent Notified" %}
                                    </label>
                                    {% if form.parent_notified.errors %}
                                        <div class="text-danger small">{{ form.parent_notified.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    {{ form.reminder_sent }}
                                    <label class="form-check-label" for="{{ form.reminder_sent.id_for_label }}">
                                        <i class="fas fa-envelope me-1"></i>
                                        {% trans "Reminder Sent" %}
                                    </label>
                                    {% if form.reminder_sent.errors %}
                                        <div class="text-danger small">{{ form.reminder_sent.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'health:appointment_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>
                                {% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                {% if appointment %}
                                    {% trans "Update Appointment" %}
                                {% else %}
                                    {% trans "Schedule Appointment" %}
                                {% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        {% trans "Appointment Guidelines" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb me-1"></i> {% trans "Tips" %}</h6>
                        <ul class="mb-0 small">
                            <li>{% trans "Schedule appointments at least 24 hours in advance" %}</li>
                            <li>{% trans "Ensure parent/guardian consent for medical appointments" %}</li>
                            <li>{% trans "Include all relevant medical information" %}</li>
                            <li>{% trans "Set reminders for important follow-up appointments" %}</li>
                        </ul>
                    </div>
                </div>
            </div>

            {% if appointment %}
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>
                        {% trans "Appointment History" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">{% trans "Created" %}</h6>
                                <p class="timeline-text small text-muted">
                                    {{ appointment.created_at|date:"M d, Y H:i" }}
                                </p>
                            </div>
                        </div>
                        {% if appointment.updated_at != appointment.created_at %}
                        <div class="timeline-item">
                            <div class="timeline-marker bg-warning"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">{% trans "Last Updated" %}</h6>
                                <p class="timeline-text small text-muted">
                                    {{ appointment.updated_at|date:"M d, Y H:i" }}
                                </p>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -31px;
    top: 15px;
    width: 2px;
    height: calc(100% + 5px);
    background-color: #dee2e6;
}

.timeline-title {
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.timeline-text {
    margin-bottom: 0;
}
</style>
{% endblock %}