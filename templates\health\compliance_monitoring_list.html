{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Compliance Monitoring" %} - {% trans "School ERP" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-shield-alt text-primary me-2"></i>
                    {% trans "Compliance Monitoring" %}
                </h1>
                <div class="d-flex gap-2">
                    <a href="#" class="btn btn-outline-primary">
                        <i class="fas fa-download me-1"></i>
                        {% trans "Export Report" %}
                    </a>
                    <a href="#" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        {% trans "Add Compliance Record" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="compliance_type" class="form-label">{% trans "Compliance Type" %}</label>
                            <select name="compliance_type" id="compliance_type" class="form-select">
                                <option value="">{% trans "All Types" %}</option>
                                <option value="vaccination">{% trans "Vaccination" %}</option>
                                <option value="medical_exam">{% trans "Medical Examination" %}</option>
                                <option value="health_screening">{% trans "Health Screening" %}</option>
                                <option value="documentation">{% trans "Documentation" %}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">{% trans "Status" %}</label>
                            <select name="status" id="status" class="form-select">
                                <option value="">{% trans "All Statuses" %}</option>
                                <option value="compliant">{% trans "Compliant" %}</option>
                                <option value="non_compliant">{% trans "Non-Compliant" %}</option>
                                <option value="pending">{% trans "Pending" %}</option>
                                <option value="expired">{% trans "Expired" %}</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">{% trans "From Date" %}</label>
                            <input type="date" name="date_from" id="date_from" class="form-control" value="{{ date_from }}">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">{% trans "To Date" %}</label>
                            <input type="date" name="date_to" id="date_to" class="form-control" value="{{ date_to }}">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>
                                    {% trans "Filter" %}
                                </button>
                                <a href="{% url 'health:compliance_monitoring_list' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="mb-0">{{ compliant_count|default:0 }}</h3>
                            <p class="mb-0">{% trans "Compliant" %}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="mb-0">{{ non_compliant_count|default:0 }}</h3>
                            <p class="mb-0">{% trans "Non-Compliant" %}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="mb-0">{{ pending_count|default:0 }}</h3>
                            <p class="mb-0">{% trans "Pending" %}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="mb-0">{{ expired_count|default:0 }}</h3>
                            <p class="mb-0">{% trans "Expired" %}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-times fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Compliance Records Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        {% trans "Compliance Records" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if compliance_records %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>{% trans "Student" %}</th>
                                        <th>{% trans "Compliance Type" %}</th>
                                        <th>{% trans "Requirement" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Due Date" %}</th>
                                        <th>{% trans "Completion Date" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for record in compliance_records %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-2">
                                                    <div class="avatar-title bg-primary rounded-circle">
                                                        {{ record.student.first_name|first }}{{ record.student.last_name|first }}
                                                    </div>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">{{ record.student.get_full_name }}</h6>
                                                    <small class="text-muted">{{ record.student.student_id }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-light text-dark">
                                                {{ record.get_compliance_type_display }}
                                            </span>
                                        </td>
                                        <td>{{ record.requirement_name }}</td>
                                        <td>
                                            {% if record.status == 'compliant' %}
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>
                                                    {% trans "Compliant" %}
                                                </span>
                                            {% elif record.status == 'non_compliant' %}
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-times me-1"></i>
                                                    {% trans "Non-Compliant" %}
                                                </span>
                                            {% elif record.status == 'pending' %}
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-clock me-1"></i>
                                                    {% trans "Pending" %}
                                                </span>
                                            {% elif record.status == 'expired' %}
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-calendar-times me-1"></i>
                                                    {% trans "Expired" %}
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if record.due_date %}
                                                {{ record.due_date|date:"M d, Y" }}
                                                {% if record.is_overdue %}
                                                    <small class="text-danger">
                                                        <i class="fas fa-exclamation-triangle"></i>
                                                        {% trans "Overdue" %}
                                                    </small>
                                                {% endif %}
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if record.completion_date %}
                                                {{ record.completion_date|date:"M d, Y" }}
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="#" class="btn btn-outline-primary" title="{% trans 'View Details' %}">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="#" class="btn btn-outline-secondary" title="{% trans 'Edit' %}">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="#" class="btn btn-outline-info" title="{% trans 'Update Status' %}">
                                                    <i class="fas fa-sync"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if is_paginated %}
                        <nav aria-label="{% trans 'Compliance records pagination' %}">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1">&laquo; {% trans "First" %}</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                                    </li>
                                {% endif %}

                                <li class="page-item active">
                                    <span class="page-link">
                                        {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">{% trans "Last" %} &raquo;</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-shield-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{% trans "No compliance records found" %}</h5>
                            <p class="text-muted">{% trans "Start by adding compliance monitoring records for students." %}</p>
                            <a href="#" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                {% trans "Add First Record" %}
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
}
</style>
{% endblock %}