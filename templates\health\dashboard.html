{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Health Management Dashboard" %}{% endblock %}

{% block extra_css %}
<style>
    .health-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .health-card.warning {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    .health-card.success {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    .health-card.info {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
    .alert-item {
        border-left: 4px solid #007bff;
        padding: 15px;
        margin-bottom: 10px;
        background: #f8f9fa;
        border-radius: 5px;
    }
    .alert-item.critical {
        border-left-color: #dc3545;
    }
    .alert-item.high {
        border-left-color: #fd7e14;
    }
    .alert-item.medium {
        border-left-color: #ffc107;
    }
    .screening-item {
        border-left: 4px solid #28a745;
        padding: 10px;
        margin-bottom: 8px;
        background: #f8f9fa;
        border-radius: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-heartbeat"></i> {% trans "Health Management Dashboard" %}</h2>
                <div>
                    <a href="{% url 'health:incident_dashboard' %}" class="btn btn-danger">
                        <i class="fas fa-exclamation-triangle"></i> {% trans "Incident Dashboard" %}
                    </a>
                    <a href="{% url 'health:health_monitoring_dashboard' %}" class="btn btn-success">
                        <i class="fas fa-heartbeat"></i> {% trans "Health Monitoring" %}
                    </a>
                    <a href="{% url 'health:vaccination_tracking' %}" class="btn btn-warning">
                        <i class="fas fa-syringe"></i> {% trans "Vaccination Tracking" %}
                    </a>
                    <a href="{% url 'health:health_alerts_dashboard' %}" class="btn btn-danger">
                        <i class="fas fa-bell"></i> {% trans "Health Alerts" %}
                    </a>
                    <a href="{% url 'health:profile_list' %}" class="btn btn-primary">
                        <i class="fas fa-users"></i> {% trans "View All Profiles" %}
                    </a>
                    <a href="{% url 'health:analytics' %}" class="btn btn-info">
                        <i class="fas fa-chart-bar"></i> {% trans "Analytics" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-md-3">
            <div class="health-card">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ total_students }}</h3>
                        <p class="mb-0">{% trans "Total Students" %}</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="health-card success">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ students_with_profiles }}</h3>
                        <p class="mb-0">{% trans "Health Profiles" %}</p>
                        <small>{{ profile_completion_rate }}% {% trans "completion" %}</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-md fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="health-card warning">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ critical_allergies }}</h3>
                        <p class="mb-0">{% trans "Critical Allergies" %}</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="health-card info">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ critical_medications }}</h3>
                        <p class="mb-0">{% trans "Critical Medications" %}</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-pills fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Statistics -->
    <div class="row">
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3>{{ overdue_vaccinations }}</h3>
                            <p class="mb-0">{% trans "Overdue Vaccinations" %}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-syringe fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3>{{ follow_up_needed }}</h3>
                            <p class="mb-0">{% trans "Follow-ups Needed" %}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3>{{ recent_incidents }}</h3>
                            <p class="mb-0">{% trans "Recent Incidents" %}</p>
                            <small>{% trans "Last 7 days" %}</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3>{{ active_incidents }}</h3>
                            <p class="mb-0">{% trans "Active Incidents" %}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clipboard-list fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Active Health Alerts -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-bell"></i> {% trans "Active Health Alerts" %}</h5>
                    <a href="{% url 'health:profile_list' %}?health_filter=has_alerts" class="btn btn-sm btn-outline-primary float-end">
                        {% trans "View All" %}
                    </a>
                </div>
                <div class="card-body">
                    {% if active_alerts %}
                        {% for alert in active_alerts %}
                            <div class="alert-item {{ alert.priority }}">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <strong>{{ alert.health_profile.student.get_full_name }}</strong>
                                        <br>
                                        <span class="text-primary">{{ alert.title }}</span>
                                        <br>
                                        <small class="text-muted">{{ alert.description|truncatechars:80 }}</small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-{% if alert.priority == 'critical' %}danger{% elif alert.priority == 'high' %}warning{% elif alert.priority == 'medium' %}info{% else %}secondary{% endif %}">
                                            {{ alert.get_priority_display }}
                                        </span>
                                        <br>
                                        <a href="{% url 'health:profile_detail' alert.health_profile.student.id %}" class="btn btn-sm btn-outline-primary mt-1">
                                            {% trans "View" %}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">{% trans "No active health alerts." %}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Health Screenings -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-stethoscope"></i> {% trans "Recent Health Screenings" %}</h5>
                    <a href="{% url 'health:reports' %}?type=screenings" class="btn btn-sm btn-outline-primary float-end">
                        {% trans "View All" %}
                    </a>
                </div>
                <div class="card-body">
                    {% if recent_screenings %}
                        {% for screening in recent_screenings %}
                            <div class="screening-item">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <strong>{{ screening.health_profile.student.get_full_name }}</strong>
                                        <br>
                                        <span class="text-info">{{ screening.get_screening_type_display }}</span>
                                        <br>
                                        <small class="text-muted">{{ screening.date }} - {{ screening.get_result_display }}</small>
                                    </div>
                                    <div class="text-end">
                                        {% if screening.referral_needed %}
                                            <span class="badge bg-warning">{% trans "Referral Needed" %}</span>
                                        {% else %}
                                            <span class="badge bg-success">{{ screening.get_result_display }}</span>
                                        {% endif %}
                                        <br>
                                        <a href="{% url 'health:profile_detail' screening.health_profile.student.id %}" class="btn btn-sm btn-outline-primary mt-1">
                                            {% trans "View" %}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">{% trans "No recent health screenings." %}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-bolt"></i> {% trans "Quick Actions" %}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2">
                            <a href="{% url 'health:incident_create' %}" class="btn btn-danger btn-block mb-2">
                                <i class="fas fa-plus me-1"></i> {% trans "Report Incident" %}
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{% url 'health:incident_dashboard' %}" class="btn btn-warning btn-block mb-2">
                                <i class="fas fa-tachometer-alt me-1"></i> {% trans "Incident Dashboard" %}
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{% url 'health:profile_list' %}" class="btn btn-primary btn-block mb-2">
                                <i class="fas fa-users me-1"></i> {% trans "View Profiles" %}
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{% url 'health:profile_list' %}?health_filter=has_allergies" class="btn btn-info btn-block mb-2">
                                <i class="fas fa-exclamation-triangle me-1"></i> {% trans "Allergies" %}
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{% url 'health:analytics' %}" class="btn btn-success btn-block mb-2">
                                <i class="fas fa-chart-bar me-1"></i> {% trans "Analytics" %}
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{% url 'health:reports' %}" class="btn btn-secondary btn-block mb-2">
                                <i class="fas fa-file-alt me-1"></i> {% trans "Reports" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Health Tips -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-lightbulb"></i> {% trans "Health Management Tips" %}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-shield-alt"></i> {% trans "Prevention" %}</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled mb-0 small">
                                        <li><i class="fas fa-check text-success"></i> {% trans "Keep vaccination records up to date" %}</li>
                                        <li><i class="fas fa-check text-success"></i> {% trans "Regular health screenings" %}</li>
                                        <li><i class="fas fa-check text-success"></i> {% trans "Monitor chronic conditions" %}</li>
                                        <li><i class="fas fa-check text-success"></i> {% trans "Update emergency contacts" %}</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0"><i class="fas fa-exclamation-triangle"></i> {% trans "Critical Alerts" %}</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled mb-0 small">
                                        <li><i class="fas fa-exclamation-triangle text-danger"></i> {% trans "Life-threatening allergies" %}</li>
                                        <li><i class="fas fa-exclamation-triangle text-warning"></i> {% trans "Critical medications" %}</li>
                                        <li><i class="fas fa-exclamation-triangle text-info"></i> {% trans "Activity restrictions" %}</li>
                                        <li><i class="fas fa-exclamation-triangle text-secondary"></i> {% trans "Emergency procedures" %}</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0"><i class="fas fa-chart-line"></i> {% trans "Best Practices" %}</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled mb-0 small">
                                        <li><i class="fas fa-sync text-success"></i> {% trans "Regular profile updates" %}</li>
                                        <li><i class="fas fa-sync text-info"></i> {% trans "Parent communication" %}</li>
                                        <li><i class="fas fa-sync text-warning"></i> {% trans "Staff training on health issues" %}</li>
                                        <li><i class="fas fa-sync text-primary"></i> {% trans "Emergency response drills" %}</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh dashboard every 10 minutes
    setTimeout(function() {
        location.reload();
    }, 600000);
</script>
{% endblock %}