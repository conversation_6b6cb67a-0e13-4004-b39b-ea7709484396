{% extends 'base.html' %}
{% load static %}

{% block title %}Incident Management Dashboard{% endblock %}

{% block extra_css %}
<style>
    .incident-card {
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
    }
    .incident-card.severity-minor { border-left-color: #28a745; }
    .incident-card.severity-moderate { border-left-color: #ffc107; }
    .incident-card.severity-serious { border-left-color: #fd7e14; }
    .incident-card.severity-critical { border-left-color: #dc3545; }
    
    .incident-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .follow-up-overdue {
        background-color: #fff5f5;
        border-left: 4px solid #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-heartbeat text-danger"></i> Incident Management Dashboard</h2>
                <div>
                    <a href="{% url 'health:incident_create' %}" class="btn btn-danger">
                        <i class="fas fa-plus"></i> Report New Incident
                    </a>
                    <a href="{% url 'health:incident_list' %}" class="btn btn-primary">
                        <i class="fas fa-list"></i> All Incidents
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-number">{{ stats.today_incidents }}</div>
                <div>Today's Incidents</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="stat-number">{{ stats.this_month_incidents }}</div>
                <div>This Month</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <div class="stat-number">{{ stats.active_incidents }}</div>
                <div>Active Incidents</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                <div class="stat-number">{{ stats.pending_follow_ups }}</div>
                <div>Pending Follow-ups</div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Incidents -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-clock text-primary"></i> Recent Incidents (Last 7 Days)</h5>
                </div>
                <div class="card-body">
                    {% if recent_incidents %}
                        {% for incident in recent_incidents %}
                        <div class="incident-card card mb-3 severity-{{ incident.severity }}">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="card-title mb-1">
                                            <a href="{% url 'health:incident_detail' incident.id %}" class="text-decoration-none">
                                                {{ incident.incident_id }}
                                            </a>
                                        </h6>
                                        <p class="card-text mb-1">
                                            <strong>{{ incident.health_profile.student.get_full_name }}</strong>
                                        </p>
                                        <p class="card-text mb-1">
                                            <small class="text-muted">
                                                {{ incident.get_incident_type_display }} - {{ incident.location }}
                                            </small>
                                        </p>
                                        <p class="card-text">
                                            {{ incident.description|truncatewords:15 }}
                                        </p>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-{{ incident.severity|yesno:'danger,warning,success,info' }}">
                                            {{ incident.get_severity_display }}
                                        </span>
                                        <br>
                                        <small class="text-muted">
                                            {{ incident.incident_date|date:"M d" }} {{ incident.incident_time|time:"H:i" }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">No recent incidents in the last 7 days.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Incidents Requiring Attention -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-exclamation-triangle text-warning"></i> Incidents Requiring Attention</h5>
                </div>
                <div class="card-body">
                    {% if attention_incidents %}
                        {% for incident in attention_incidents %}
                        <div class="incident-card card mb-3 severity-{{ incident.severity }}">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="card-title mb-1">
                                            <a href="{% url 'health:incident_detail' incident.id %}" class="text-decoration-none">
                                                {{ incident.incident_id }}
                                            </a>
                                        </h6>
                                        <p class="card-text mb-1">
                                            <strong>{{ incident.health_profile.student.get_full_name }}</strong>
                                        </p>
                                        <p class="card-text mb-1">
                                            <small class="text-muted">
                                                {{ incident.get_incident_type_display }} - {{ incident.location }}
                                            </small>
                                        </p>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-{{ incident.status|yesno:'warning,primary,success,info,danger' }}">
                                            {{ incident.get_status_display }}
                                        </span>
                                        <br>
                                        <span class="badge bg-{{ incident.severity|yesno:'danger,warning,success,info' }}">
                                            {{ incident.get_severity_display }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">No incidents currently requiring attention.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Overdue Follow-ups -->
    {% if overdue_follow_ups %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-calendar-times text-danger"></i> Overdue Follow-ups</h5>
                </div>
                <div class="card-body">
                    {% for follow_up in overdue_follow_ups %}
                    <div class="card mb-2 follow-up-overdue">
                        <div class="card-body py-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>{{ follow_up.incident.incident_id }}</strong> - 
                                    {{ follow_up.incident.health_profile.student.get_full_name }}
                                    <br>
                                    <small class="text-muted">
                                        {{ follow_up.get_follow_up_type_display }} - Due: {{ follow_up.scheduled_date }}
                                    </small>
                                </div>
                                <div>
                                    <span class="badge bg-{{ follow_up.priority|yesno:'danger,warning,success,info' }}">
                                        {{ follow_up.get_priority_display }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-bolt text-warning"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{% url 'health:incident_create' %}" class="btn btn-outline-danger btn-block mb-2">
                                <i class="fas fa-plus"></i> Report Incident
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'health:incident_list' %}" class="btn btn-outline-primary btn-block mb-2">
                                <i class="fas fa-list"></i> View All Incidents
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'health:incident_analytics' %}" class="btn btn-outline-info btn-block mb-2">
                                <i class="fas fa-chart-bar"></i> Analytics
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'health:dashboard' %}" class="btn btn-outline-success btn-block mb-2">
                                <i class="fas fa-heartbeat"></i> Health Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-refresh every 5 minutes
    setTimeout(function() {
        location.reload();
    }, 300000);
});
</script>
{% endblock %}