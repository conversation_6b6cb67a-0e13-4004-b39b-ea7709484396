{% extends 'base.html' %}
{% load static %}

{% block title %}Incident {{ incident.incident_id }}{% endblock %}

{% block extra_css %}
<style>
    .incident-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
    }
    
    .severity-critical { border-left: 4px solid #dc3545; }
    .severity-serious { border-left: 4px solid #fd7e14; }
    .severity-moderate { border-left: 4px solid #ffc107; }
    .severity-minor { border-left: 4px solid #28a745; }
    
    .info-card {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .timeline-item {
        border-left: 3px solid #007bff;
        padding-left: 15px;
        margin-bottom: 20px;
        position: relative;
    }
    
    .timeline-item::before {
        content: '';
        position: absolute;
        left: -6px;
        top: 0;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: #007bff;
    }
    
    .timeline-item.treatment::before { background-color: #28a745; }
    .timeline-item.follow-up::before { background-color: #ffc107; }
    .timeline-item.notification::before { background-color: #17a2b8; }
    
    .status-update-form {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .quick-actions {
        position: sticky;
        top: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Incident Header -->
    <div class="incident-header severity-{{ incident.severity }}">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2><i class="fas fa-exclamation-triangle"></i> {{ incident.incident_id }}</h2>
                <h4>{{ incident.health_profile.student.get_full_name }}</h4>
                <p class="mb-0">
                    {{ incident.get_incident_type_display }} - {{ incident.location }}
                    <br>
                    {{ incident.incident_date|date:"F d, Y" }} at {{ incident.incident_time|time:"H:i" }}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <span class="badge bg-light text-dark fs-6 mb-2">
                    {{ incident.get_severity_display }}
                </span>
                <br>
                <span class="badge bg-{% if incident.status == 'resolved' %}success{% elif incident.status == 'in_treatment' %}primary{% elif incident.status == 'reported' %}warning{% else %}info{% endif %} fs-6">
                    {{ incident.get_status_display }}
                </span>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Incident Details -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle"></i> Incident Details</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Student:</strong> 
                            <a href="{% url 'health:profile_detail' incident.health_profile.student.id %}">
                                {{ incident.health_profile.student.get_full_name }}
                            </a>
                            <br>
                            <strong>Student ID:</strong> {{ incident.health_profile.student.student_id }}<br>
                            {% if incident.health_profile.student.grade %}
                            <strong>Grade:</strong> {{ incident.health_profile.student.grade.name }}<br>
                            {% endif %}
                            {% if incident.health_profile.student.class_enrolled %}
                            <strong>Class:</strong> {{ incident.health_profile.student.class_enrolled.name }}<br>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <strong>Reported By:</strong> 
                            {% if incident.reported_by %}
                                {{ incident.reported_by.get_full_name }}
                            {% else %}
                                <span class="text-muted">Not specified</span>
                            {% endif %}
                            <br>
                            <strong>Date/Time:</strong> {{ incident.incident_date|date:"F d, Y" }} at {{ incident.incident_time|time:"H:i" }}<br>
                            <strong>Location:</strong> {{ incident.location }}<br>
                            {% if incident.witnessed_by %}
                            <strong>Witnesses:</strong> {{ incident.witnessed_by }}<br>
                            {% endif %}
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-12">
                            <strong>Description:</strong>
                            <p>{{ incident.description }}</p>
                            
                            {% if incident.symptoms_observed %}
                            <strong>Symptoms Observed:</strong>
                            <p>{{ incident.symptoms_observed }}</p>
                            {% endif %}
                            
                            {% if incident.immediate_action_taken %}
                            <strong>Immediate Action Taken:</strong>
                            <p>{{ incident.immediate_action_taken }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Medical Response -->
            {% if incident.first_aid_given or incident.emergency_services_called or incident.transported_to_hospital %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-hand-holding-medical"></i> Medical Response</h5>
                </div>
                <div class="card-body">
                    {% if incident.first_aid_given %}
                    <div class="info-card">
                        <h6><i class="fas fa-first-aid text-success"></i> First Aid Given</h6>
                        <p>{{ incident.first_aid_details|default:"Details not provided" }}</p>
                    </div>
                    {% endif %}
                    
                    {% if incident.emergency_services_called %}
                    <div class="info-card">
                        <h6><i class="fas fa-ambulance text-danger"></i> Emergency Services Called</h6>
                        <p>{{ incident.emergency_service_details|default:"Details not provided" }}</p>
                    </div>
                    {% endif %}
                    
                    {% if incident.transported_to_hospital %}
                    <div class="info-card">
                        <h6><i class="fas fa-hospital text-warning"></i> Transported to Hospital</h6>
                        <p>{{ incident.hospital_details|default:"Details not provided" }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- Parent Notification -->
            {% if incident.parent_notified %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-phone"></i> Parent/Guardian Notification</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Notified:</strong> Yes<br>
                            <strong>Time:</strong> {{ incident.parent_notification_time|date:"F d, Y H:i" }}<br>
                            <strong>Method:</strong> {{ incident.parent_notification_method }}
                        </div>
                        <div class="col-md-6">
                            {% if incident.parent_response %}
                            <strong>Parent Response:</strong>
                            <p>{{ incident.parent_response }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Treatments -->
            {% if treatments %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-pills"></i> Treatments Provided</h5>
                </div>
                <div class="card-body">
                    {% for treatment in treatments %}
                    <div class="timeline-item treatment">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6>{{ treatment.get_treatment_type_display }}</h6>
                                <p class="mb-1">{{ treatment.treatment_description }}</p>
                                {% if treatment.medication_given %}
                                <p class="mb-1"><strong>Medication:</strong> {{ treatment.medication_given }}</p>
                                {% endif %}
                                {% if treatment.student_response %}
                                <p class="mb-1"><strong>Student Response:</strong> {{ treatment.student_response }}</p>
                                {% endif %}
                                <small class="text-muted">
                                    {{ treatment.treatment_time|date:"F d, Y H:i" }}
                                    {% if treatment.administered_by %}
                                    by {{ treatment.administered_by.get_full_name }}
                                    {% endif %}
                                </small>
                            </div>
                            {% if treatment.effectiveness %}
                            <span class="badge bg-{% if treatment.effectiveness == 'effective' %}success{% elif treatment.effectiveness == 'partially_effective' %}warning{% else %}danger{% endif %}">
                                {{ treatment.get_effectiveness_display }}
                            </span>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Follow-ups -->
            {% if follow_ups %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-calendar-check"></i> Follow-up Actions</h5>
                </div>
                <div class="card-body">
                    {% for follow_up in follow_ups %}
                    <div class="timeline-item follow-up">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6>{{ follow_up.get_follow_up_type_display }}</h6>
                                <p class="mb-1">{{ follow_up.description }}</p>
                                {% if follow_up.outcome %}
                                <p class="mb-1"><strong>Outcome:</strong> {{ follow_up.outcome }}</p>
                                {% endif %}
                                <small class="text-muted">
                                    Scheduled: {{ follow_up.scheduled_date|date:"F d, Y" }}
                                    {% if follow_up.assigned_to %}
                                    - Assigned to: {{ follow_up.assigned_to.get_full_name }}
                                    {% endif %}
                                </small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-{% if follow_up.status == 'completed' %}success{% elif follow_up.status == 'in_progress' %}primary{% elif follow_up.status == 'pending' %}warning{% else %}secondary{% endif %}">
                                    {{ follow_up.get_status_display }}
                                </span>
                                <br>
                                <span class="badge bg-{% if follow_up.priority == 'urgent' %}danger{% elif follow_up.priority == 'high' %}warning{% else %}info{% endif %}">
                                    {{ follow_up.get_priority_display }}
                                </span>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Notifications -->
            {% if notifications %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-bell"></i> Notifications Sent</h5>
                </div>
                <div class="card-body">
                    {% for notification in notifications %}
                    <div class="timeline-item notification">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6>{{ notification.get_notification_type_display }}</h6>
                                <p class="mb-1">
                                    <strong>To:</strong> {{ notification.recipient_name }} ({{ notification.recipient_relationship }})
                                </p>
                                <p class="mb-1">
                                    <strong>Method:</strong> {{ notification.get_method_display }}
                                </p>
                                {% if notification.subject %}
                                <p class="mb-1"><strong>Subject:</strong> {{ notification.subject }}</p>
                                {% endif %}
                                <small class="text-muted">
                                    Scheduled: {{ notification.scheduled_time|date:"F d, Y H:i" }}
                                    {% if notification.sent_time %}
                                    - Sent: {{ notification.sent_time|date:"F d, Y H:i" }}
                                    {% endif %}
                                </small>
                            </div>
                            <span class="badge bg-{% if notification.status == 'read' %}success{% elif notification.status == 'delivered' %}info{% elif notification.status == 'sent' %}primary{% elif notification.status == 'pending' %}warning{% else %}danger{% endif %}">
                                {{ notification.get_status_display }}
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Additional Information -->
            {% if incident.activity_restrictions or incident.notes or incident.incident_report_file %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-info"></i> Additional Information</h5>
                </div>
                <div class="card-body">
                    {% if incident.activity_restrictions %}
                    <div class="info-card">
                        <h6><i class="fas fa-ban text-warning"></i> Activity Restrictions</h6>
                        <p>{{ incident.activity_restrictions }}</p>
                    </div>
                    {% endif %}
                    
                    {% if incident.return_to_class_time %}
                    <div class="info-card">
                        <h6><i class="fas fa-clock text-info"></i> Return to Class</h6>
                        <p>{{ incident.return_to_class_time|date:"F d, Y H:i" }}</p>
                    </div>
                    {% endif %}
                    
                    {% if incident.photos_taken %}
                    <div class="info-card">
                        <h6><i class="fas fa-camera text-secondary"></i> Documentation</h6>
                        <p>Photos were taken of the incident.</p>
                    </div>
                    {% endif %}
                    
                    {% if incident.incident_report_file %}
                    <div class="info-card">
                        <h6><i class="fas fa-file-pdf text-danger"></i> Incident Report File</h6>
                        <a href="{{ incident.incident_report_file.url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-download"></i> Download Report
                        </a>
                    </div>
                    {% endif %}
                    
                    {% if incident.notes %}
                    <div class="info-card">
                        <h6><i class="fas fa-sticky-note text-warning"></i> Additional Notes</h6>
                        <p>{{ incident.notes }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <div class="quick-actions">
                <!-- Status Update -->
                {% if incident.status != 'resolved' %}
                <div class="card mb-3">
                    <div class="card-header">
                        <h6><i class="fas fa-edit"></i> Update Status</h6>
                    </div>
                    <div class="card-body">
                        <form id="statusUpdateForm">
                            {% csrf_token %}
                            <div class="mb-3">
                                <select class="form-control" id="newStatus" name="status">
                                    <option value="">Select new status...</option>
                                    <option value="reported" {% if incident.status == 'reported' %}selected{% endif %}>Reported</option>
                                    <option value="in_treatment" {% if incident.status == 'in_treatment' %}selected{% endif %}>In Treatment</option>
                                    <option value="resolved" {% if incident.status == 'resolved' %}selected{% endif %}>Resolved</option>
                                    <option value="referred" {% if incident.status == 'referred' %}selected{% endif %}>Referred to External Care</option>
                                    <option value="follow_up_required" {% if incident.status == 'follow_up_required' %}selected{% endif %}>Follow-up Required</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="fas fa-save"></i> Update Status
                            </button>
                        </form>
                    </div>
                </div>
                {% endif %}

                <!-- Quick Actions -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6><i class="fas fa-bolt"></i> Quick Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{% url 'health:incident_edit' incident.id %}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-edit"></i> Edit Incident
                            </a>
                            <a href="{% url 'health:incident_treatment_add' incident.id %}" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-pills"></i> Add Treatment
                            </a>
                            <a href="{% url 'health:incident_follow_up_add' incident.id %}" class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-calendar-plus"></i> Add Follow-up
                            </a>
                            <a href="{% url 'health:incident_notification_add' incident.id %}" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-bell"></i> Send Notification
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Student Health Summary -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6><i class="fas fa-user-md"></i> Student Health Summary</h6>
                    </div>
                    <div class="card-body">
                        <p><strong>Blood Type:</strong> {{ incident.health_profile.blood_type|default:"Unknown" }}</p>
                        {% if incident.health_profile.allergies.count > 0 %}
                        <p><strong>Allergies:</strong> {{ incident.health_profile.allergies.count }} active</p>
                        {% endif %}
                        {% if incident.health_profile.medications.count > 0 %}
                        <p><strong>Medications:</strong> {{ incident.health_profile.medications.count }} active</p>
                        {% endif %}
                        <a href="{% url 'health:profile_detail' incident.health_profile.student.id %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye"></i> View Full Profile
                        </a>
                    </div>
                </div>

                <!-- Navigation -->
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-navigation"></i> Navigation</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{% url 'health:incident_list' %}" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-list"></i> All Incidents
                            </a>
                            <a href="{% url 'health:incident_dashboard' %}" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                            <a href="{% url 'health:dashboard' %}" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-heartbeat"></i> Health Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Status update form
    $('#statusUpdateForm').submit(function(e) {
        e.preventDefault();
        
        const newStatus = $('#newStatus').val();
        if (!newStatus) {
            alert('Please select a status.');
            return;
        }
        
        $.ajax({
            url: '{% url "health:incident_status_update" incident.id %}',
            method: 'POST',
            data: {
                'status': newStatus,
                'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function() {
                alert('An error occurred while updating the status.');
            }
        });
    });
});
</script>
{% endblock %}