{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .form-section {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .form-section h5 {
        color: #495057;
        border-bottom: 2px solid #007bff;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }
    
    .required-field label::after {
        content: " *";
        color: #dc3545;
    }
    
    .severity-critical { border-left: 4px solid #dc3545; }
    .severity-serious { border-left: 4px solid #fd7e14; }
    .severity-moderate { border-left: 4px solid #ffc107; }
    .severity-minor { border-left: 4px solid #28a745; }
    
    .conditional-field {
        display: none;
        margin-top: 15px;
        padding: 15px;
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-exclamation-triangle text-danger"></i> {{ title }}</h2>
                <div>
                    <a href="{% url 'health:incident_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                    {% if student %}
                    <a href="{% url 'health:profile_detail' student.id %}" class="btn btn-info">
                        <i class="fas fa-user"></i> Student Profile
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    {% if student %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i>
        <strong>Student:</strong> {{ student.get_full_name }} ({{ student.student_id }})
        {% if student.grade %}
        - {{ student.grade.name }}
        {% endif %}
        {% if student.class_enrolled %}
        - {{ student.class_enrolled.name }}
        {% endif %}
    </div>
    {% endif %}

    <form method="post" enctype="multipart/form-data" id="incidentForm">
        {% csrf_token %}
        
        <!-- Basic Incident Information -->
        <div class="form-section">
            <h5><i class="fas fa-info-circle"></i> Basic Incident Information</h5>
            <div class="row">
                {% if not student %}
                <div class="col-md-6 required-field">
                    <label for="{{ form.health_profile.id_for_label }}" class="form-label">Student</label>
                    {{ form.health_profile }}
                    {% if form.health_profile.errors %}
                        <div class="text-danger">{{ form.health_profile.errors }}</div>
                    {% endif %}
                </div>
                {% endif %}
                <div class="col-md-3 required-field">
                    <label for="{{ form.incident_type.id_for_label }}" class="form-label">Incident Type</label>
                    {{ form.incident_type }}
                    {% if form.incident_type.errors %}
                        <div class="text-danger">{{ form.incident_type.errors }}</div>
                    {% endif %}
                </div>
                <div class="col-md-3 required-field">
                    <label for="{{ form.severity.id_for_label }}" class="form-label">Severity</label>
                    {{ form.severity }}
                    {% if form.severity.errors %}
                        <div class="text-danger">{{ form.severity.errors }}</div>
                    {% endif %}
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-3 required-field">
                    <label for="{{ form.incident_date.id_for_label }}" class="form-label">Date</label>
                    {{ form.incident_date }}
                    {% if form.incident_date.errors %}
                        <div class="text-danger">{{ form.incident_date.errors }}</div>
                    {% endif %}
                </div>
                <div class="col-md-3 required-field">
                    <label for="{{ form.incident_time.id_for_label }}" class="form-label">Time</label>
                    {{ form.incident_time }}
                    {% if form.incident_time.errors %}
                        <div class="text-danger">{{ form.incident_time.errors }}</div>
                    {% endif %}
                </div>
                <div class="col-md-6 required-field">
                    <label for="{{ form.location.id_for_label }}" class="form-label">Location</label>
                    {{ form.location }}
                    {% if form.location.errors %}
                        <div class="text-danger">{{ form.location.errors }}</div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Incident Description -->
        <div class="form-section">
            <h5><i class="fas fa-file-alt"></i> Incident Description</h5>
            <div class="row">
                <div class="col-md-12 required-field">
                    <label for="{{ form.description.id_for_label }}" class="form-label">Description of Incident</label>
                    {{ form.description }}
                    {% if form.description.errors %}
                        <div class="text-danger">{{ form.description.errors }}</div>
                    {% endif %}
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-12">
                    <label for="{{ form.symptoms_observed.id_for_label }}" class="form-label">Symptoms Observed</label>
                    {{ form.symptoms_observed }}
                    {% if form.symptoms_observed.errors %}
                        <div class="text-danger">{{ form.symptoms_observed.errors }}</div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- People Involved -->
        <div class="form-section">
            <h5><i class="fas fa-users"></i> People Involved</h5>
            <div class="row">
                <div class="col-md-6">
                    <label for="{{ form.reported_by.id_for_label }}" class="form-label">Reported By</label>
                    {{ form.reported_by }}
                    {% if form.reported_by.errors %}
                        <div class="text-danger">{{ form.reported_by.errors }}</div>
                    {% endif %}
                </div>
                <div class="col-md-6">
                    <label for="{{ form.witnessed_by.id_for_label }}" class="form-label">Witnesses</label>
                    {{ form.witnessed_by }}
                    {% if form.witnessed_by.errors %}
                        <div class="text-danger">{{ form.witnessed_by.errors }}</div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Initial Response -->
        <div class="form-section">
            <h5><i class="fas fa-hand-holding-medical"></i> Initial Response</h5>
            <div class="row">
                <div class="col-md-12">
                    <label for="{{ form.immediate_action_taken.id_for_label }}" class="form-label">Immediate Action Taken</label>
                    {{ form.immediate_action_taken }}
                    {% if form.immediate_action_taken.errors %}
                        <div class="text-danger">{{ form.immediate_action_taken.errors }}</div>
                    {% endif %}
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-3">
                    <div class="form-check">
                        {{ form.first_aid_given }}
                        <label class="form-check-label" for="{{ form.first_aid_given.id_for_label }}">
                            First Aid Given
                        </label>
                    </div>
                </div>
                <div class="col-md-9">
                    <div id="first_aid_details_field" class="conditional-field">
                        <label for="{{ form.first_aid_details.id_for_label }}" class="form-label">First Aid Details</label>
                        {{ form.first_aid_details }}
                        {% if form.first_aid_details.errors %}
                            <div class="text-danger">{{ form.first_aid_details.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Emergency Services -->
        <div class="form-section">
            <h5><i class="fas fa-ambulance"></i> Emergency Services</h5>
            <div class="row">
                <div class="col-md-3">
                    <div class="form-check">
                        {{ form.emergency_services_called }}
                        <label class="form-check-label" for="{{ form.emergency_services_called.id_for_label }}">
                            Emergency Services Called
                        </label>
                    </div>
                </div>
                <div class="col-md-9">
                    <div id="emergency_details_field" class="conditional-field">
                        <label for="{{ form.emergency_service_details.id_for_label }}" class="form-label">Emergency Service Details</label>
                        {{ form.emergency_service_details }}
                        {% if form.emergency_service_details.errors %}
                            <div class="text-danger">{{ form.emergency_service_details.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-3">
                    <div class="form-check">
                        {{ form.transported_to_hospital }}
                        <label class="form-check-label" for="{{ form.transported_to_hospital.id_for_label }}">
                            Transported to Hospital
                        </label>
                    </div>
                </div>
                <div class="col-md-9">
                    <div id="hospital_details_field" class="conditional-field">
                        <label for="{{ form.hospital_details.id_for_label }}" class="form-label">Hospital Details</label>
                        {{ form.hospital_details }}
                        {% if form.hospital_details.errors %}
                            <div class="text-danger">{{ form.hospital_details.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Parent Notification -->
        <div class="form-section">
            <h5><i class="fas fa-phone"></i> Parent/Guardian Notification</h5>
            <div class="row">
                <div class="col-md-3">
                    <div class="form-check">
                        {{ form.parent_notified }}
                        <label class="form-check-label" for="{{ form.parent_notified.id_for_label }}">
                            Parent/Guardian Notified
                        </label>
                    </div>
                </div>
                <div class="col-md-9">
                    <div id="parent_notification_fields" class="conditional-field">
                        <div class="row">
                            <div class="col-md-4">
                                <label for="{{ form.parent_notification_time.id_for_label }}" class="form-label">Notification Time</label>
                                {{ form.parent_notification_time }}
                                {% if form.parent_notification_time.errors %}
                                    <div class="text-danger">{{ form.parent_notification_time.errors }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-4">
                                <label for="{{ form.parent_notification_method.id_for_label }}" class="form-label">Method</label>
                                {{ form.parent_notification_method }}
                                {% if form.parent_notification_method.errors %}
                                    <div class="text-danger">{{ form.parent_notification_method.errors }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-4">
                                <label for="{{ form.parent_response.id_for_label }}" class="form-label">Parent Response</label>
                                {{ form.parent_response }}
                                {% if form.parent_response.errors %}
                                    <div class="text-danger">{{ form.parent_response.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Follow-up -->
        <div class="form-section">
            <h5><i class="fas fa-calendar-check"></i> Follow-up</h5>
            <div class="row">
                <div class="col-md-3">
                    <div class="form-check">
                        {{ form.follow_up_required }}
                        <label class="form-check-label" for="{{ form.follow_up_required.id_for_label }}">
                            Follow-up Required
                        </label>
                    </div>
                </div>
                <div class="col-md-9">
                    <div id="follow_up_fields" class="conditional-field">
                        <label for="{{ form.follow_up_instructions.id_for_label }}" class="form-label">Follow-up Instructions</label>
                        {{ form.follow_up_instructions }}
                        {% if form.follow_up_instructions.errors %}
                            <div class="text-danger">{{ form.follow_up_instructions.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-4">
                    <label for="{{ form.return_to_class_time.id_for_label }}" class="form-label">Return to Class Time</label>
                    {{ form.return_to_class_time }}
                    {% if form.return_to_class_time.errors %}
                        <div class="text-danger">{{ form.return_to_class_time.errors }}</div>
                    {% endif %}
                </div>
                <div class="col-md-8">
                    <label for="{{ form.activity_restrictions.id_for_label }}" class="form-label">Activity Restrictions</label>
                    {{ form.activity_restrictions }}
                    {% if form.activity_restrictions.errors %}
                        <div class="text-danger">{{ form.activity_restrictions.errors }}</div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Documentation -->
        <div class="form-section">
            <h5><i class="fas fa-camera"></i> Documentation</h5>
            <div class="row">
                <div class="col-md-3">
                    <div class="form-check">
                        {{ form.photos_taken }}
                        <label class="form-check-label" for="{{ form.photos_taken.id_for_label }}">
                            Photos Taken
                        </label>
                    </div>
                </div>
                <div class="col-md-9">
                    <label for="{{ form.incident_report_file.id_for_label }}" class="form-label">Incident Report File</label>
                    {{ form.incident_report_file }}
                    {% if form.incident_report_file.errors %}
                        <div class="text-danger">{{ form.incident_report_file.errors }}</div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Additional Notes -->
        <div class="form-section">
            <h5><i class="fas fa-sticky-note"></i> Additional Notes</h5>
            <div class="row">
                <div class="col-md-12">
                    <label for="{{ form.notes.id_for_label }}" class="form-label">Notes</label>
                    {{ form.notes }}
                    {% if form.notes.errors %}
                        <div class="text-danger">{{ form.notes.errors }}</div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between">
                    <a href="{% url 'health:incident_list' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-save"></i> Save Incident Report
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Show/hide conditional fields based on checkbox states
    function toggleConditionalField(checkboxId, fieldId) {
        const checkbox = $('#' + checkboxId);
        const field = $('#' + fieldId);
        
        function toggle() {
            if (checkbox.is(':checked')) {
                field.show();
            } else {
                field.hide();
            }
        }
        
        // Initial state
        toggle();
        
        // On change
        checkbox.change(toggle);
    }
    
    // Set up conditional fields
    toggleConditionalField('id_first_aid_given', 'first_aid_details_field');
    toggleConditionalField('id_emergency_services_called', 'emergency_details_field');
    toggleConditionalField('id_transported_to_hospital', 'hospital_details_field');
    toggleConditionalField('id_parent_notified', 'parent_notification_fields');
    toggleConditionalField('id_follow_up_required', 'follow_up_fields');
    
    // Update form styling based on severity
    $('#id_severity').change(function() {
        const severity = $(this).val();
        const form = $('#incidentForm');
        
        // Remove existing severity classes
        form.removeClass('severity-minor severity-moderate severity-serious severity-critical');
        
        // Add new severity class
        if (severity) {
            form.addClass('severity-' + severity);
        }
    });
    
    // Trigger initial severity styling
    $('#id_severity').trigger('change');
    
    // Form validation
    $('#incidentForm').submit(function(e) {
        let isValid = true;
        
        // Check required fields
        $('.required-field input, .required-field select, .required-field textarea').each(function() {
            if ($(this).prop('required') && !$(this).val()) {
                isValid = false;
                $(this).addClass('is-invalid');
            } else {
                $(this).removeClass('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields.');
        }
    });
});
</script>
{% endblock %}