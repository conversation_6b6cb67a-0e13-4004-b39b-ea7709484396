{% extends 'base.html' %}
{% load static %}

{% block title %}Medical Incidents{% endblock %}

{% block extra_css %}
<style>
    .incident-row.severity-critical { border-left: 4px solid #dc3545; }
    .incident-row.severity-serious { border-left: 4px solid #fd7e14; }
    .incident-row.severity-moderate { border-left: 4px solid #ffc107; }
    .incident-row.severity-minor { border-left: 4px solid #28a745; }
    
    .filter-card {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .status-badge {
        font-size: 0.8rem;
        padding: 4px 8px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-clipboard-list text-primary"></i> Medical Incidents</h2>
                <div>
                    <a href="{% url 'health:incident_dashboard' %}" class="btn btn-info">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    <a href="{% url 'health:incident_create' %}" class="btn btn-danger">
                        <i class="fas fa-plus"></i> Report New Incident
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label for="q" class="form-label">Search</label>
                <input type="text" class="form-control" id="q" name="q" value="{{ query }}" 
                       placeholder="Search incidents, students, locations...">
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">Status</label>
                <select class="form-control" id="status" name="status">
                    <option value="">All Statuses</option>
                    {% for value, label in status_choices %}
                    <option value="{{ value }}" {% if current_status == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="severity" class="form-label">Severity</label>
                <select class="form-control" id="severity" name="severity">
                    <option value="">All Severities</option>
                    {% for value, label in severity_choices %}
                    <option value="{{ value }}" {% if current_severity == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="type" class="form-label">Type</label>
                <select class="form-control" id="type" name="type">
                    <option value="">All Types</option>
                    {% for value, label in type_choices %}
                    <option value="{{ value }}" {% if current_type == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-1">
                <label for="date_from" class="form-label">From</label>
                <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
            </div>
            <div class="col-md-1">
                <label for="date_to" class="form-label">To</label>
                <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Filter
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Incidents Table -->
    <div class="card">
        <div class="card-body">
            {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Incident ID</th>
                            <th>Student</th>
                            <th>Type</th>
                            <th>Severity</th>
                            <th>Status</th>
                            <th>Date/Time</th>
                            <th>Location</th>
                            <th>Reported By</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for incident in page_obj %}
                        <tr class="incident-row severity-{{ incident.severity }}">
                            <td>
                                <a href="{% url 'health:incident_detail' incident.id %}" class="text-decoration-none">
                                    <strong>{{ incident.incident_id }}</strong>
                                </a>
                                {% if incident.is_recent %}
                                <span class="badge bg-warning ms-1">New</span>
                                {% endif %}
                            </td>
                            <td>
                                <div>
                                    <strong>{{ incident.health_profile.student.get_full_name }}</strong>
                                </div>
                                <small class="text-muted">
                                    {{ incident.health_profile.student.student_id }}
                                </small>
                            </td>
                            <td>
                                <span class="badge bg-secondary">
                                    {{ incident.get_incident_type_display }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-{% if incident.severity == 'critical' %}danger{% elif incident.severity == 'serious' %}warning{% elif incident.severity == 'moderate' %}info{% else %}success{% endif %}">
                                    {{ incident.get_severity_display }}
                                </span>
                            </td>
                            <td>
                                <span class="badge status-badge bg-{% if incident.status == 'resolved' %}success{% elif incident.status == 'in_treatment' %}primary{% elif incident.status == 'reported' %}warning{% else %}info{% endif %}">
                                    {{ incident.get_status_display }}
                                </span>
                            </td>
                            <td>
                                <div>{{ incident.incident_date|date:"M d, Y" }}</div>
                                <small class="text-muted">{{ incident.incident_time|time:"H:i" }}</small>
                            </td>
                            <td>{{ incident.location|truncatechars:20 }}</td>
                            <td>
                                {% if incident.reported_by %}
                                    {{ incident.reported_by.get_full_name }}
                                {% else %}
                                    <span class="text-muted">Not specified</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{% url 'health:incident_detail' incident.id %}" 
                                       class="btn btn-outline-primary" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'health:incident_edit' incident.id %}" 
                                       class="btn btn-outline-secondary" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% if incident.requires_attention %}
                                    <button class="btn btn-outline-warning" title="Requires Attention">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="Incidents pagination">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if query %}&q={{ query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_severity %}&severity={{ current_severity }}{% endif %}{% if current_type %}&type={{ current_type }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">First</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if query %}&q={{ query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_severity %}&severity={{ current_severity }}{% endif %}{% if current_type %}&type={{ current_type }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">Previous</a>
                    </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% if query %}&q={{ query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_severity %}&severity={{ current_severity }}{% endif %}{% if current_type %}&type={{ current_type }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if query %}&q={{ query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_severity %}&severity={{ current_severity }}{% endif %}{% if current_type %}&type={{ current_type }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">Next</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if query %}&q={{ query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_severity %}&severity={{ current_severity }}{% endif %}{% if current_type %}&type={{ current_type }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">Last</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}

            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No incidents found</h5>
                <p class="text-muted">No medical incidents match your current filters.</p>
                <a href="{% url 'health:incident_create' %}" class="btn btn-danger">
                    <i class="fas fa-plus"></i> Report First Incident
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-submit form when date inputs change
    $('#date_from, #date_to').change(function() {
        $(this).closest('form').submit();
    });
});
</script>
{% endblock %}