{% extends 'base.html' %}
{% load static %}

{% block title %}Health Monitoring Dashboard{% endblock %}

{% block extra_css %}
<style>
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        margin-bottom: 20px;
    }
    
    .metric-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .alert-card {
        border-left: 4px solid #dc3545;
        background-color: #f8d7da;
        border-color: #f5c6cb;
    }
    
    .warning-card {
        border-left: 4px solid #ffc107;
        background-color: #fff3cd;
        border-color: #ffeaa7;
    }
    
    .success-card {
        border-left: 4px solid #28a745;
        background-color: #d4edda;
        border-color: #c3e6cb;
    }
    
    .screening-item, .appointment-item {
        border-left: 3px solid #007bff;
        padding: 10px;
        margin-bottom: 10px;
        background-color: #f8f9fa;
        border-radius: 5px;
    }
    
    .overdue {
        border-left-color: #dc3545;
        background-color: #f8d7da;
    }
    
    .upcoming {
        border-left-color: #ffc107;
        background-color: #fff3cd;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-heartbeat text-primary"></i> Health Monitoring Dashboard</h2>
                <div>
                    <a href="{% url 'health:dashboard' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Health Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-number">{{ total_students }}</div>
                <div>Total Students</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="metric-number">{{ overdue_screenings }}</div>
                <div>Overdue Screenings</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <div class="metric-number">{{ compliance_issues }}</div>
                <div>Compliance Issues</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                <div class="metric-number">{{ completed_screenings_this_month }}</div>
                <div>Screenings This Month</div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{% url 'health:screening_schedule_create' %}" class="btn btn-primary btn-block">
                                <i class="fas fa-calendar-plus"></i> Schedule Screening
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'health:appointment_create' %}" class="btn btn-success btn-block">
                                <i class="fas fa-user-md"></i> Schedule Appointment
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'health:compliance_monitoring_list' %}" class="btn btn-warning btn-block">
                                <i class="fas fa-clipboard-check"></i> Check Compliance
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'health:health_trends_dashboard' %}" class="btn btn-info btn-block">
                                <i class="fas fa-chart-line"></i> View Trends
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Upcoming Screenings -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-calendar-alt"></i> Upcoming Screenings</h5>
                    <a href="{% url 'health:screening_schedule_list' %}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    {% if upcoming_screenings %}
                        {% for screening in upcoming_screenings %}
                        <div class="screening-item {% if screening.is_overdue %}overdue{% elif screening.days_until_due <= 7 %}upcoming{% endif %}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <strong>{{ screening.health_profile.student.get_full_name }}</strong>
                                    <br>
                                    <span class="text-muted">{{ screening.get_screening_type_display }}</span>
                                    <br>
                                    <small>{{ screening.scheduled_date|date:"M d, Y" }}
                                        {% if screening.scheduled_time %} at {{ screening.scheduled_time|time:"g:i A" }}{% endif %}
                                    </small>
                                </div>
                                <div>
                                    <span class="badge bg-{% if screening.is_overdue %}danger{% elif screening.days_until_due <= 7 %}warning{% else %}primary{% endif %}">
                                        {{ screening.get_status_display }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-check fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No upcoming screenings</h6>
                            <p class="text-muted">All screenings are up to date.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Upcoming Appointments -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-user-md"></i> Upcoming Appointments</h5>
                    <a href="{% url 'health:appointment_list' %}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    {% if upcoming_appointments %}
                        {% for appointment in upcoming_appointments %}
                        <div class="appointment-item {% if appointment.is_overdue %}overdue{% elif appointment.is_upcoming %}upcoming{% endif %}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <strong>{{ appointment.health_profile.student.get_full_name }}</strong>
                                    <br>
                                    <span class="text-muted">{{ appointment.title }}</span>
                                    <br>
                                    <small>{{ appointment.appointment_date|date:"M d, Y" }} at {{ appointment.appointment_time|time:"g:i A" }}</small>
                                    <br>
                                    <small class="text-muted">{{ appointment.provider_name }}</small>
                                </div>
                                <div>
                                    <span class="badge bg-{% if appointment.priority == 'urgent' %}danger{% elif appointment.priority == 'high' %}warning{% else %}primary{% endif %}">
                                        {{ appointment.get_priority_display }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-check fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No upcoming appointments</h6>
                            <p class="text-muted">No medical appointments scheduled.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts and Warnings -->
    {% if overdue_screenings > 0 or compliance_issues > 0 or expiring_compliance > 0 %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-exclamation-triangle text-warning"></i> Alerts & Warnings</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% if overdue_screenings > 0 %}
                        <div class="col-md-4">
                            <div class="alert alert-danger">
                                <h6><i class="fas fa-calendar-times"></i> Overdue Screenings</h6>
                                <p class="mb-1">{{ overdue_screenings }} screening{{ overdue_screenings|pluralize }} overdue</p>
                                <a href="{% url 'health:screening_schedule_list' %}?status=scheduled&overdue=1" class="btn btn-sm btn-outline-danger">
                                    View Overdue
                                </a>
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if compliance_issues > 0 %}
                        <div class="col-md-4">
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-clipboard-list"></i> Compliance Issues</h6>
                                <p class="mb-1">{{ compliance_issues }} compliance issue{{ compliance_issues|pluralize }}</p>
                                <a href="{% url 'health:compliance_monitoring_list' %}?status=non_compliant" class="btn btn-sm btn-outline-warning">
                                    View Issues
                                </a>
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if expiring_compliance > 0 %}
                        <div class="col-md-4">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-clock"></i> Expiring Compliance</h6>
                                <p class="mb-1">{{ expiring_compliance }} compliance record{{ expiring_compliance|pluralize }} expiring soon</p>
                                <a href="{% url 'health:compliance_monitoring_list' %}?expiring=1" class="btn btn-sm btn-outline-info">
                                    View Expiring
                                </a>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-refresh dashboard every 5 minutes
    setTimeout(function() {
        location.reload();
    }, 300000);
});
</script>
{% endblock %}