{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load widget_tweaks %}

{% block title %}{{ title }} - {% trans "Health" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">{{ title }}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'accounts:dashboard' %}">{% trans "Dashboard" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'health:dashboard' %}">{% trans "Health" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'health:profile_list' %}">{% trans "Profiles" %}</a></li>
                        <li class="breadcrumb-item active">{{ title }}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-md me-2"></i>
                        {{ title }}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <!-- Student Selection -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.student.id_for_label }}" class="form-label">
                                        {{ form.student.label }}
                                        {% if form.student.field.required %}<span class="text-danger">*</span>{% endif %}
                                    </label>
                                    {{ form.student|add_class:"form-select" }}
                                    {% if form.student.errors %}
                                        <div class="text-danger small">{{ form.student.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.blood_type.id_for_label }}" class="form-label">
                                        {{ form.blood_type.label }}
                                    </label>
                                    {{ form.blood_type|add_class:"form-select" }}
                                    {% if form.blood_type.errors %}
                                        <div class="text-danger small">{{ form.blood_type.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Physical Information -->
                        <h6 class="mb-3 text-primary">
                            <i class="fas fa-ruler me-1"></i>
                            {% trans "Physical Information" %}
                        </h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.height.id_for_label }}" class="form-label">
                                        {{ form.height.label }}
                                    </label>
                                    <div class="input-group">
                                        {{ form.height|add_class:"form-control" }}
                                        <span class="input-group-text">cm</span>
                                    </div>
                                    {% if form.height.errors %}
                                        <div class="text-danger small">{{ form.height.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.weight.id_for_label }}" class="form-label">
                                        {{ form.weight.label }}
                                    </label>
                                    <div class="input-group">
                                        {{ form.weight|add_class:"form-control" }}
                                        <span class="input-group-text">kg</span>
                                    </div>
                                    {% if form.weight.errors %}
                                        <div class="text-danger small">{{ form.weight.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.last_physical_exam.id_for_label }}" class="form-label">
                                        {{ form.last_physical_exam.label }}
                                    </label>
                                    {{ form.last_physical_exam|add_class:"form-control" }}
                                    {% if form.last_physical_exam.errors %}
                                        <div class="text-danger small">{{ form.last_physical_exam.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Medical Information -->
                        <h6 class="mb-3 text-primary">
                            <i class="fas fa-stethoscope me-1"></i>
                            {% trans "Medical Information" %}
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.chronic_conditions.id_for_label }}" class="form-label">
                                        {{ form.chronic_conditions.label }}
                                    </label>
                                    {{ form.chronic_conditions|add_class:"form-control" }}
                                    {% if form.chronic_conditions.errors %}
                                        <div class="text-danger small">{{ form.chronic_conditions.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.disabilities.id_for_label }}" class="form-label">
                                        {{ form.disabilities.label }}
                                    </label>
                                    {{ form.disabilities|add_class:"form-control" }}
                                    {% if form.disabilities.errors %}
                                        <div class="text-danger small">{{ form.disabilities.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="{{ form.special_needs.id_for_label }}" class="form-label">
                                        {{ form.special_needs.label }}
                                    </label>
                                    {{ form.special_needs|add_class:"form-control" }}
                                    {% if form.special_needs.errors %}
                                        <div class="text-danger small">{{ form.special_needs.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Emergency Contact -->
                        <h6 class="mb-3 text-primary">
                            <i class="fas fa-phone me-1"></i>
                            {% trans "Emergency Contact" %}
                        </h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.emergency_contact_name.id_for_label }}" class="form-label">
                                        {{ form.emergency_contact_name.label }}
                                    </label>
                                    {{ form.emergency_contact_name|add_class:"form-control" }}
                                    {% if form.emergency_contact_name.errors %}
                                        <div class="text-danger small">{{ form.emergency_contact_name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.emergency_contact_relationship.id_for_label }}" class="form-label">
                                        {{ form.emergency_contact_relationship.label }}
                                    </label>
                                    {{ form.emergency_contact_relationship|add_class:"form-control" }}
                                    {% if form.emergency_contact_relationship.errors %}
                                        <div class="text-danger small">{{ form.emergency_contact_relationship.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.emergency_contact_phone.id_for_label }}" class="form-label">
                                        {{ form.emergency_contact_phone.label }}
                                    </label>
                                    {{ form.emergency_contact_phone|add_class:"form-control" }}
                                    {% if form.emergency_contact_phone.errors %}
                                        <div class="text-danger small">{{ form.emergency_contact_phone.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Insurance Information -->
                        <h6 class="mb-3 text-primary">
                            <i class="fas fa-shield-alt me-1"></i>
                            {% trans "Insurance Information" %}
                        </h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.insurance_provider.id_for_label }}" class="form-label">
                                        {{ form.insurance_provider.label }}
                                    </label>
                                    {{ form.insurance_provider|add_class:"form-control" }}
                                    {% if form.insurance_provider.errors %}
                                        <div class="text-danger small">{{ form.insurance_provider.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.insurance_policy_number.id_for_label }}" class="form-label">
                                        {{ form.insurance_policy_number.label }}
                                    </label>
                                    {{ form.insurance_policy_number|add_class:"form-control" }}
                                    {% if form.insurance_policy_number.errors %}
                                        <div class="text-danger small">{{ form.insurance_policy_number.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.insurance_expiry_date.id_for_label }}" class="form-label">
                                        {{ form.insurance_expiry_date.label }}
                                    </label>
                                    {{ form.insurance_expiry_date|add_class:"form-control" }}
                                    {% if form.insurance_expiry_date.errors %}
                                        <div class="text-danger small">{{ form.insurance_expiry_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Additional Notes -->
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="{{ form.medical_notes.id_for_label }}" class="form-label">
                                        {{ form.medical_notes.label }}
                                    </label>
                                    {{ form.medical_notes|add_class:"form-control" }}
                                    {% if form.medical_notes.errors %}
                                        <div class="text-danger small">{{ form.medical_notes.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{% url 'health:profile_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>
                                        {% trans "Cancel" %}
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        {% trans "Save Profile" %}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}