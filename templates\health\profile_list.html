{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Health Profiles" %} - {% trans "School ERP" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-user-md text-primary me-2"></i>
                    {% trans "Health Profiles" %}
                </h1>
                <a href="{% url 'health:profile_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    {% trans "Add Profile" %}
                </a>
            </div>
        </div>
    </div>

    <!-- Filter Tabs -->
    <div class="row mb-4">
        <div class="col-12">
            <ul class="nav nav-tabs" id="healthTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab">
                        <i class="fas fa-users me-1"></i>
                        {% trans "All Profiles" %}
                        <span class="badge bg-secondary ms-1">{{ total_profiles }}</span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="allergies-tab" data-bs-toggle="tab" data-bs-target="#allergies" type="button" role="tab">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        {% trans "With Allergies" %}
                        <span class="badge bg-warning ms-1">{{ allergies_count }}</span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="medication-tab" data-bs-toggle="tab" data-bs-target="#medication" type="button" role="tab">
                        <i class="fas fa-pills me-1"></i>
                        {% trans "On Medication" %}
                        <span class="badge bg-info ms-1">{{ medication_count }}</span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="alerts-tab" data-bs-toggle="tab" data-bs-target="#alerts" type="button" role="tab">
                        <i class="fas fa-bell me-1"></i>
                        {% trans "Health Alerts" %}
                        <span class="badge bg-danger ms-1">{{ alerts_count }}</span>
                    </button>
                </li>
            </ul>
        </div>
    </div>

    <!-- Tab Content -->
    <div class="tab-content" id="healthTabsContent">
        <div class="tab-pane fade show active" id="all" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        {% trans "All Health Profiles" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if health_profiles %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>{% trans "Student" %}</th>
                                        <th>{% trans "Class" %}</th>
                                        <th>{% trans "Blood Type" %}</th>
                                        <th>{% trans "Allergies" %}</th>
                                        <th>{% trans "Medications" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for profile in health_profiles %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-2">
                                                    <div class="avatar-title bg-primary rounded-circle">
                                                        {{ profile.student.first_name|first }}{{ profile.student.last_name|first }}
                                                    </div>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">{{ profile.student.get_full_name }}</h6>
                                                    <small class="text-muted">{{ profile.student.student_id }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ profile.student.current_class|default:"-" }}</td>
                                        <td>
                                            <span class="badge bg-light text-dark">{{ profile.blood_type|default:"-" }}</span>
                                        </td>
                                        <td>
                                            {% if profile.allergies %}
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                                    {% trans "Yes" %}
                                                </span>
                                            {% else %}
                                                <span class="text-muted">{% trans "None" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if profile.current_medications %}
                                                <span class="badge bg-info">
                                                    <i class="fas fa-pills me-1"></i>
                                                    {% trans "Yes" %}
                                                </span>
                                            {% else %}
                                                <span class="text-muted">{% trans "None" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if profile.has_health_alerts %}
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-exclamation-circle me-1"></i>
                                                    {% trans "Alert" %}
                                                </span>
                                            {% else %}
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check-circle me-1"></i>
                                                    {% trans "Normal" %}
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="#" class="btn btn-outline-primary" title="{% trans 'View Profile' %}">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="#" class="btn btn-outline-secondary" title="{% trans 'Edit Profile' %}">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="#" class="btn btn-outline-info" title="{% trans 'Medical History' %}">
                                                    <i class="fas fa-history"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-user-md fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{% trans "No health profiles found" %}</h5>
                            <p class="text-muted">{% trans "Start by adding health profiles for students." %}</p>
                            <a href="{% url 'health:profile_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                {% trans "Add First Profile" %}
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Other tabs content would go here -->
        <div class="tab-pane fade" id="allergies" role="tabpanel">
            <div class="card">
                <div class="card-body">
                    <div class="text-center py-4">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                        <h5>{% trans "Students with Allergies" %}</h5>
                        <p class="text-muted">{% trans "This section will show students with known allergies." %}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="tab-pane fade" id="medication" role="tabpanel">
            <div class="card">
                <div class="card-body">
                    <div class="text-center py-4">
                        <i class="fas fa-pills fa-2x text-info mb-3"></i>
                        <h5>{% trans "Students on Medication" %}</h5>
                        <p class="text-muted">{% trans "This section will show students currently on medication." %}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="tab-pane fade" id="alerts" role="tabpanel">
            <div class="card">
                <div class="card-body">
                    <div class="text-center py-4">
                        <i class="fas fa-bell fa-2x text-danger mb-3"></i>
                        <h5>{% trans "Health Alerts" %}</h5>
                        <p class="text-muted">{% trans "This section will show students with active health alerts." %}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
}
</style>
{% endblock %}