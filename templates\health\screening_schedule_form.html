{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .form-section {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .section-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 15px;
        border-bottom: 2px solid #dee2e6;
        padding-bottom: 5px;
    }
    
    .required-field {
        border-left: 3px solid #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-calendar-plus text-primary"></i> {{ title }}</h2>
                <div>
                    <a href="{% url 'health:screening_schedule_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                
                <!-- Basic Information -->
                <div class="form-section">
                    <h5 class="section-title"><i class="fas fa-info-circle"></i> Basic Information</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.health_profile.id_for_label }}" class="form-label">
                                    Student <span class="text-danger">*</span>
                                </label>
                                {{ form.health_profile }}
                                {% if form.health_profile.errors %}
                                    <div class="text-danger small">{{ form.health_profile.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.screening_type.id_for_label }}" class="form-label">
                                    Screening Type <span class="text-danger">*</span>
                                </label>
                                {{ form.screening_type }}
                                {% if form.screening_type.errors %}
                                    <div class="text-danger small">{{ form.screening_type.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.title.id_for_label }}" class="form-label">
                            Title <span class="text-danger">*</span>
                        </label>
                        {{ form.title }}
                        {% if form.title.errors %}
                            <div class="text-danger small">{{ form.title.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger small">{{ form.description.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>

                <!-- Scheduling Information -->
                <div class="form-section">
                    <h5 class="section-title"><i class="fas fa-calendar"></i> Scheduling</h5>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.scheduled_date.id_for_label }}" class="form-label">
                                    Scheduled Date <span class="text-danger">*</span>
                                </label>
                                {{ form.scheduled_date }}
                                {% if form.scheduled_date.errors %}
                                    <div class="text-danger small">{{ form.scheduled_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.scheduled_time.id_for_label }}" class="form-label">Scheduled Time</label>
                                {{ form.scheduled_time }}
                                {% if form.scheduled_time.errors %}
                                    <div class="text-danger small">{{ form.scheduled_time.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.frequency.id_for_label }}" class="form-label">Frequency</label>
                                {{ form.frequency }}
                                {% if form.frequency.errors %}
                                    <div class="text-danger small">{{ form.frequency.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.assigned_to.id_for_label }}" class="form-label">Assigned To</label>
                                {{ form.assigned_to }}
                                {% if form.assigned_to.errors %}
                                    <div class="text-danger small">{{ form.assigned_to.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.location.id_for_label }}" class="form-label">Location</label>
                                {{ form.location }}
                                {% if form.location.errors %}
                                    <div class="text-danger small">{{ form.location.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="form-section">
                    <h5 class="section-title"><i class="fas fa-clipboard-list"></i> Additional Information</h5>
                    
                    <div class="mb-3">
                        <label for="{{ form.requirements.id_for_label }}" class="form-label">Requirements</label>
                        {{ form.requirements }}
                        <small class="form-text text-muted">Special requirements or preparations needed for this screening</small>
                        {% if form.requirements.errors %}
                            <div class="text-danger small">{{ form.requirements.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">Notes</label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="d-flex justify-content-between">
                    <a href="{% url 'health:screening_schedule_list' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Schedule Screening
                    </button>
                </div>
            </form>
        </div>
        
        <!-- Help Panel -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-question-circle"></i> Help & Guidelines</h6>
                </div>
                <div class="card-body">
                    <h6>Screening Types:</h6>
                    <ul class="small">
                        <li><strong>Vision:</strong> Eye exams and vision tests</li>
                        <li><strong>Hearing:</strong> Audiometric testing</li>
                        <li><strong>Dental:</strong> Oral health examinations</li>
                        <li><strong>Physical:</strong> General health checkups</li>
                        <li><strong>BMI:</strong> Height and weight assessments</li>
                        <li><strong>Scoliosis:</strong> Spinal curvature screening</li>
                    </ul>
                    
                    <h6 class="mt-3">Frequency Guidelines:</h6>
                    <ul class="small">
                        <li><strong>Annual:</strong> Most routine screenings</li>
                        <li><strong>Bi-annual:</strong> Dental checkups</li>
                        <li><strong>As Needed:</strong> Follow-up screenings</li>
                    </ul>
                    
                    <div class="alert alert-info mt-3">
                        <small>
                            <i class="fas fa-info-circle"></i>
                            <strong>Tip:</strong> Schedule screenings well in advance to ensure adequate preparation time and staff availability.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-populate title based on screening type
    $('#id_screening_type').change(function() {
        const screeningType = $(this).find('option:selected').text();
        const currentTitle = $('#id_title').val();
        
        if (!currentTitle) {
            $('#id_title').val(screeningType);
        }
    });
    
    // Set minimum date to today
    const today = new Date().toISOString().split('T')[0];
    $('#id_scheduled_date').attr('min', today);
    
    // Form validation
    $('form').submit(function(e) {
        let isValid = true;
        
        // Check required fields
        const requiredFields = ['#id_health_profile', '#id_screening_type', '#id_title', '#id_scheduled_date'];
        
        requiredFields.forEach(function(field) {
            if (!$(field).val()) {
                $(field).addClass('is-invalid');
                isValid = false;
            } else {
                $(field).removeClass('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields.');
        }
    });
    
    // Remove validation styling on input
    $('input, select, textarea').on('input change', function() {
        $(this).removeClass('is-invalid');
    });
});
</script>
{% endblock %}