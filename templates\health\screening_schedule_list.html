{% extends 'base.html' %}
{% load static %}

{% block title %}Health Screening Schedules{% endblock %}

{% block extra_css %}
<style>
    .screening-row {
        transition: all 0.3s ease;
    }
    
    .screening-row:hover {
        background-color: #f8f9fa;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .overdue {
        border-left: 4px solid #dc3545;
    }
    
    .upcoming {
        border-left: 4px solid #ffc107;
    }
    
    .completed {
        border-left: 4px solid #28a745;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-calendar-alt text-primary"></i> Health Screening Schedules</h2>
                <div>
                    <a href="{% url 'health:health_monitoring_dashboard' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Monitoring
                    </a>
                    <a href="{% url 'health:screening_schedule_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Schedule Screening
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-control" id="status" name="status">
                        <option value="">All Status</option>
                        {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="screening_type" class="form-label">Type</label>
                    <select class="form-control" id="screening_type" name="screening_type">
                        <option value="">All Types</option>
                        {% for value, label in screening_types %}
                        <option value="{{ value }}" {% if screening_type_filter == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="date_from" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                </div>
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           placeholder="Student name, title..." value="{{ search_query }}">
                </div>
                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                        <a href="{% url 'health:screening_schedule_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Screening Schedules Table -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-list"></i> Screening Schedules ({{ page_obj.paginator.count }} total)</h5>
        </div>
        <div class="card-body">
            {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Student</th>
                            <th>Screening Type</th>
                            <th>Title</th>
                            <th>Scheduled Date</th>
                            <th>Status</th>
                            <th>Assigned To</th>
                            <th>Location</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for screening in page_obj %}
                        <tr class="screening-row {% if screening.is_overdue %}overdue{% elif screening.days_until_due <= 7 %}upcoming{% elif screening.status == 'completed' %}completed{% endif %}">
                            <td>
                                <div>
                                    <strong>{{ screening.health_profile.student.get_full_name }}</strong>
                                </div>
                                <small class="text-muted">{{ screening.health_profile.student.student_id }}</small>
                            </td>
                            <td>{{ screening.get_screening_type_display }}</td>
                            <td>{{ screening.title }}</td>
                            <td>
                                <div>
                                    {{ screening.scheduled_date|date:"M d, Y" }}
                                    {% if screening.scheduled_time %}
                                        <br><small class="text-muted">{{ screening.scheduled_time|time:"g:i A" }}</small>
                                    {% endif %}
                                </div>
                                {% if screening.is_overdue %}
                                    <small class="text-danger">
                                        <i class="fas fa-exclamation-triangle"></i> Overdue
                                    </small>
                                {% elif screening.days_until_due <= 7 and screening.days_until_due >= 0 %}
                                    <small class="text-warning">
                                        <i class="fas fa-clock"></i> Due in {{ screening.days_until_due }} day{{ screening.days_until_due|pluralize }}
                                    </small>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-{% if screening.status == 'completed' %}success{% elif screening.status == 'in_progress' %}warning{% elif screening.status == 'cancelled' %}danger{% else %}primary{% endif %}">
                                    {{ screening.get_status_display }}
                                </span>
                            </td>
                            <td>
                                {% if screening.assigned_to %}
                                    {{ screening.assigned_to.get_full_name }}
                                {% else %}
                                    <span class="text-muted">Not assigned</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if screening.location %}
                                    {{ screening.location }}
                                {% else %}
                                    <span class="text-muted">Not specified</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button class="btn btn-outline-primary" title="View Details" 
                                            onclick="viewScreeningDetails({{ screening.id }})">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    {% if screening.status != 'completed' %}
                                    <button class="btn btn-outline-success" title="Mark Complete" 
                                            onclick="markScreeningComplete({{ screening.id }})">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    {% endif %}
                                    <button class="btn btn-outline-warning" title="Edit" 
                                            onclick="editScreening({{ screening.id }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="Screening schedules pagination">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.screening_type %}&screening_type={{ request.GET.screening_type }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">First</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.screening_type %}&screening_type={{ request.GET.screening_type }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Previous</a>
                        </li>
                    {% endif %}
                    
                    <li class="page-item active">
                        <span class="page-link">
                            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                        </span>
                    </li>
                    
                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.screening_type %}&screening_type={{ request.GET.screening_type }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Next</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.screening_type %}&screening_type={{ request.GET.screening_type }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Last</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Screening Schedules Found</h5>
                <p class="text-muted">No screening schedules match your current filters.</p>
                <a href="{% url 'health:screening_schedule_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Schedule First Screening
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Screening Details Modal -->
<div class="modal fade" id="screeningDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Screening Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="screeningDetailsContent">
                <!-- Content loaded via AJAX -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function viewScreeningDetails(screeningId) {
    // Load screening details via AJAX
    $('#screeningDetailsContent').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>');
    $('#screeningDetailsModal').modal('show');
    
    // In a real implementation, you would load details via AJAX
    setTimeout(function() {
        $('#screeningDetailsContent').html('<p>Screening details would be loaded here via AJAX.</p>');
    }, 1000);
}

function markScreeningComplete(screeningId) {
    if (confirm('Mark this screening as completed?')) {
        // In a real implementation, you would send an AJAX request to update the status
        alert('Screening marked as completed (this would be implemented via AJAX)');
        location.reload();
    }
}

function editScreening(screeningId) {
    // In a real implementation, you would redirect to edit form or open modal
    alert('Edit screening functionality would be implemented here');
}

$(document).ready(function() {
    // Auto-submit form when filters change
    $('#status, #screening_type').change(function() {
        $(this).closest('form').submit();
    });
});
</script>
{% endblock %}