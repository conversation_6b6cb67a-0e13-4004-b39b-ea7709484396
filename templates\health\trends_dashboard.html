{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Health Trends Dashboard" %} - {% trans "School ERP" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-chart-line text-primary me-2"></i>
                    {% trans "Health Trends Dashboard" %}
                </h1>
                <div class="d-flex gap-2">
                    <div class="btn-group" role="group">
                        <input type="radio" class="btn-check" name="timeRange" id="week" autocomplete="off" checked>
                        <label class="btn btn-outline-primary" for="week">{% trans "Week" %}</label>

                        <input type="radio" class="btn-check" name="timeRange" id="month" autocomplete="off">
                        <label class="btn btn-outline-primary" for="month">{% trans "Month" %}</label>

                        <input type="radio" class="btn-check" name="timeRange" id="year" autocomplete="off">
                        <label class="btn btn-outline-primary" for="year">{% trans "Year" %}</label>
                    </div>
                    <a href="#" class="btn btn-success">
                        <i class="fas fa-download me-1"></i>
                        {% trans "Export Report" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="mb-0">{{ total_incidents|default:0 }}</h3>
                            <p class="mb-0">{% trans "Total Incidents" %}</p>
                            <small class="opacity-75">
                                {% if incident_trend > 0 %}
                                    <i class="fas fa-arrow-up"></i> +{{ incident_trend }}%
                                {% elif incident_trend < 0 %}
                                    <i class="fas fa-arrow-down"></i> {{ incident_trend }}%
                                {% else %}
                                    <i class="fas fa-minus"></i> 0%
                                {% endif %}
                                {% trans "vs last period" %}
                            </small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="mb-0">{{ vaccination_rate|default:0 }}%</h3>
                            <p class="mb-0">{% trans "Vaccination Rate" %}</p>
                            <small class="opacity-75">
                                {{ vaccinated_students|default:0 }}/{{ total_students|default:0 }} {% trans "students" %}
                            </small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-syringe fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="mb-0">{{ health_alerts|default:0 }}</h3>
                            <p class="mb-0">{% trans "Active Alerts" %}</p>
                            <small class="opacity-75">
                                {{ critical_alerts|default:0 }} {% trans "critical" %}
                            </small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-bell fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="mb-0">{{ compliance_rate|default:0 }}%</h3>
                            <p class="mb-0">{% trans "Compliance Rate" %}</p>
                            <small class="opacity-75">
                                {{ compliant_students|default:0 }}/{{ total_students|default:0 }} {% trans "students" %}
                            </small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-shield-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-area me-2"></i>
                        {% trans "Health Incidents Trend" %}
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="incidentsTrendChart" height="100"></canvas>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        {% trans "Incident Types" %}
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="incidentTypesChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Analysis Tables -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-thermometer-half me-2"></i>
                        {% trans "Common Health Issues" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if common_issues %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{% trans "Issue" %}</th>
                                        <th>{% trans "Cases" %}</th>
                                        <th>{% trans "Trend" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for issue in common_issues %}
                                    <tr>
                                        <td>{{ issue.name }}</td>
                                        <td>
                                            <span class="badge bg-primary">{{ issue.count }}</span>
                                        </td>
                                        <td>
                                            {% if issue.trend > 0 %}
                                                <span class="text-danger">
                                                    <i class="fas fa-arrow-up"></i> +{{ issue.trend }}%
                                                </span>
                                            {% elif issue.trend < 0 %}
                                                <span class="text-success">
                                                    <i class="fas fa-arrow-down"></i> {{ issue.trend }}%
                                                </span>
                                            {% else %}
                                                <span class="text-muted">
                                                    <i class="fas fa-minus"></i> 0%
                                                </span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-chart-bar fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">{% trans "No health issues data available" %}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar-check me-2"></i>
                        {% trans "Seasonal Patterns" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if seasonal_data %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{% trans "Month" %}</th>
                                        <th>{% trans "Incidents" %}</th>
                                        <th>{% trans "Primary Issue" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for month_data in seasonal_data %}
                                    <tr>
                                        <td>{{ month_data.month_name }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ month_data.incident_count }}</span>
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ month_data.primary_issue }}</small>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-calendar fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">{% trans "No seasonal data available" %}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Recommendations -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        {% trans "Health Recommendations" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-shield-virus me-1"></i> {% trans "Prevention" %}</h6>
                                <p class="mb-0 small">{% trans "Increase hand hygiene awareness campaigns during flu season" %}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-syringe me-1"></i> {% trans "Vaccination" %}</h6>
                                <p class="mb-0 small">{% trans "Schedule vaccination drives for students with incomplete records" %}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="alert alert-success">
                                <h6><i class="fas fa-user-md me-1"></i> {% trans "Screening" %}</h6>
                                <p class="mb-0 small">{% trans "Implement regular health screenings for early detection" %}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Incidents Trend Chart
    const incidentsTrendCtx = document.getElementById('incidentsTrendChart').getContext('2d');
    new Chart(incidentsTrendCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                label: '{% trans "Health Incidents" %}',
                data: [12, 19, 8, 15, 22, 18, 25, 20, 16, 14, 10, 8],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Incident Types Chart
    const incidentTypesCtx = document.getElementById('incidentTypesChart').getContext('2d');
    new Chart(incidentTypesCtx, {
        type: 'doughnut',
        data: {
            labels: ['{% trans "Injury" %}', '{% trans "Illness" %}', '{% trans "Allergic Reaction" %}', '{% trans "Other" %}'],
            datasets: [{
                data: [35, 25, 20, 20],
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 205, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Time range toggle functionality
    document.querySelectorAll('input[name="timeRange"]').forEach(radio => {
        radio.addEventListener('change', function() {
            // Update charts based on selected time range
            console.log('Time range changed to:', this.id);
            // Implement chart data update logic here
        });
    });
});
</script>
{% endblock %}