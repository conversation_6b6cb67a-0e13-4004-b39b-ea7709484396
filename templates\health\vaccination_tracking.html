{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Vaccination Tracking" %}{% endblock %}

{% block extra_css %}
<style>
    .vaccination-card {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        text-align: center;
    }
    
    .metric-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .vaccination-row {
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
    }
    
    .vaccination-row:hover {
        background-color: #f8f9fa;
        transform: translateX(5px);
    }
    
    .vaccination-row.overdue { border-left-color: #dc3545; }
    .vaccination-row.due { border-left-color: #ffc107; }
    .vaccination-row.completed { border-left-color: #28a745; }
    
    .coverage-bar {
        height: 20px;
        background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
        border-radius: 10px;
        margin-bottom: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-syringe text-primary"></i> {% trans "Vaccination Tracking" %}</h2>
                <div>
                    <a href="{% url 'health:dashboard' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> {% trans "Back to Health Dashboard" %}
                    </a>
                    <button class="btn btn-success" onclick="exportVaccinationReport()">
                        <i class="fas fa-download"></i> {% trans "Export Report" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="vaccination-card">
                <div class="metric-number">{{ completion_rate }}%</div>
                <div>{% trans "Completion Rate" %}</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="vaccination-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="metric-number">{{ overdue_count }}</div>
                <div>{% trans "Overdue" %}</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="vaccination-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                <div class="metric-number">{{ completed_count }}</div>
                <div>{% trans "Completed" %}</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="vaccination-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="metric-number">{{ total_schedules }}</div>
                <div>{% trans "Total Schedules" %}</div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-2">
                    <label for="status" class="form-label">{% trans "Status" %}</label>
                    <select class="form-control" id="status" name="status">
                        <option value="">{% trans "All Status" %}</option>
                        {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="vaccine" class="form-label">{% trans "Vaccine" %}</label>
                    <select class="form-control" id="vaccine" name="vaccine">
                        <option value="">{% trans "All Vaccines" %}</option>
                        {% for value, label in vaccine_choices %}
                        <option value="{{ value }}" {% if vaccine_filter == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="grade" class="form-label">{% trans "Grade" %}</label>
                    <select class="form-control" id="grade" name="grade">
                        <option value="">{% trans "All Grades" %}</option>
                        {% for grade in grades %}
                        <option value="{{ grade.id }}" {% if grade_filter == grade.id|stringformat:'s' %}selected{% endif %}>
                            {{ grade.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <div class="form-check mt-4">
                        <input class="form-check-input" type="checkbox" id="overdue_only" name="overdue_only" value="1" {% if overdue_only %}checked{% endif %}>
                        <label class="form-check-label" for="overdue_only">
                            {% trans "Overdue Only" %}
                        </label>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter"></i> {% trans "Apply Filter" %}
                        </button>
                        <a href="{% url 'health:vaccination_tracking' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> {% trans "Clear" %}
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="row">
        <!-- Vaccination Coverage -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> {% trans "Vaccination Coverage" %}</h5>
                </div>
                <div class="card-body">
                    {% for vaccine_name, coverage in vaccine_coverage.items %}
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{{ vaccine_name }}</span>
                            <strong>{{ coverage.rate|floatformat:1 }}%</strong>
                        </div>
                        <div class="coverage-bar" style="width: {{ coverage.rate }}%"></div>
                        <small class="text-muted">{{ coverage.completed }}/{{ coverage.total }} {% trans "completed" %}</small>
                    </div>
                    {% empty %}
                    <p class="text-muted">{% trans "No vaccination data available" %}</p>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Vaccination Schedules Table -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list"></i> {% trans "Vaccination Schedules" %} ({{ page_obj.paginator.count }} {% trans "total" %})</h5>
                </div>
                <div class="card-body">
                    {% if page_obj %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>{% trans "Student" %}</th>
                                    <th>{% trans "Vaccine" %}</th>
                                    <th>{% trans "Dose" %}</th>
                                    <th>{% trans "Due Date" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Completed Date" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for schedule in page_obj %}
                                <tr class="vaccination-row {% if schedule.is_overdue %}overdue{% elif schedule.status == 'due' %}due{% elif schedule.status == 'completed' %}completed{% endif %}">
                                    <td>
                                        <div>
                                            <strong>{{ schedule.health_profile.student.get_full_name }}</strong>
                                        </div>
                                        <small class="text-muted">{{ schedule.health_profile.student.student_id }}</small>
                                    </td>
                                    <td>{{ schedule.get_vaccine_name_display }}</td>
                                    <td>
                                        <span class="badge bg-info">{% trans "Dose" %} {{ schedule.dose_number }}</span>
                                    </td>
                                    <td>
                                        <div>
                                            {{ schedule.due_date|date:"M d, Y" }}
                                        </div>
                                        {% if schedule.is_overdue %}
                                            <small class="text-danger">
                                                <i class="fas fa-exclamation-triangle"></i> {{ schedule.days_overdue }} {% trans "days overdue" %}
                                            </small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-{% if schedule.status == 'completed' %}success{% elif schedule.status == 'overdue' %}danger{% elif schedule.status == 'due' %}warning{% else %}primary{% endif %}">
                                            {{ schedule.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if schedule.completed_date %}
                                            {{ schedule.completed_date|date:"M d, Y" }}
                                            {% if schedule.administered_by %}
                                                <br><small class="text-muted">{% trans "by" %} {{ schedule.administered_by.get_full_name }}</small>
                                            {% endif %}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            {% if schedule.status != 'completed' %}
                                            <button class="btn btn-outline-success" title="{% trans 'Mark Complete' %}" 
                                                    onclick="markVaccinationComplete({{ schedule.id }})">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            {% endif %}
                                            <button class="btn btn-outline-primary" title="{% trans 'Send Reminder' %}" 
                                                    onclick="sendVaccinationReminder({{ schedule.id }})">
                                                <i class="fas fa-bell"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if page_obj.has_other_pages %}
                    <nav aria-label="Vaccination schedules pagination">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.vaccine %}&vaccine={{ request.GET.vaccine }}{% endif %}">{% trans "First" %}</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.vaccine %}&vaccine={{ request.GET.vaccine }}{% endif %}">{% trans "Previous" %}</a>
                                </li>
                            {% endif %}
                            
                            <li class="page-item active">
                                <span class="page-link">
                                    {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>
                            
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.vaccine %}&vaccine={{ request.GET.vaccine }}{% endif %}">{% trans "Next" %}</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.vaccine %}&vaccine={{ request.GET.vaccine }}{% endif %}">{% trans "Last" %}</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-syringe fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">{% trans "No Vaccination Schedules Found" %}</h5>
                        <p class="text-muted">{% trans "No vaccination schedules match your current filters." %}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function markVaccinationComplete(scheduleId) {
    if (confirm('{% trans "Mark this vaccination as completed?" %}')) {
        // In a real implementation, you would send an AJAX request to update the status
        alert('{% trans "Vaccination marked as completed (this would be implemented via AJAX)" %}');
        location.reload();
    }
}

function sendVaccinationReminder(scheduleId) {
    if (confirm('{% trans "Send vaccination reminder to parent/guardian?" %}')) {
        // In a real implementation, you would send an AJAX request to send reminder
        alert('{% trans "Reminder sent (this would be implemented via AJAX)" %}');
    }
}

function exportVaccinationReport() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'pdf');
    window.open(`${window.location.pathname}?${params.toString()}`, '_blank');
}

$(document).ready(function() {
    // Auto-submit form when filters change
    $('#status, #vaccine, #grade').change(function() {
        $(this).closest('form').submit();
    });
});
</script>
{% endblock %}