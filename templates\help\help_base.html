{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Help & Support" %}{% endblock %}

{% block extra_css %}
<style>
    .help-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .help-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 40px 20px;
        border-radius: 10px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .help-search {
        max-width: 600px;
        margin: 20px auto;
        position: relative;
    }
    
    .help-search input {
        width: 100%;
        padding: 15px 50px 15px 20px;
        border: 2px solid #e0e0e0;
        border-radius: 25px;
        font-size: 16px;
        outline: none;
        transition: border-color 0.3s;
    }
    
    .help-search input:focus {
        border-color: #667eea;
    }
    
    .help-search .search-icon {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        color: #999;
    }
    
    .help-categories {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin: 30px 0;
    }
    
    .help-category {
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 10px;
        padding: 25px;
        text-decoration: none;
        color: inherit;
        transition: all 0.3s;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    .help-category:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        text-decoration: none;
        color: inherit;
    }
    
    .help-category-icon {
        font-size: 2.5rem;
        color: #667eea;
        margin-bottom: 15px;
    }
    
    .help-category h3 {
        margin-bottom: 10px;
        color: #333;
    }
    
    .help-category p {
        color: #666;
        margin-bottom: 0;
    }
    
    .help-quick-links {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 25px;
        margin: 30px 0;
    }
    
    .help-quick-links h3 {
        margin-bottom: 20px;
        color: #333;
    }
    
    .quick-link {
        display: inline-block;
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 20px;
        padding: 8px 16px;
        margin: 5px;
        text-decoration: none;
        color: #667eea;
        font-size: 14px;
        transition: all 0.3s;
    }
    
    .quick-link:hover {
        background: #667eea;
        color: white;
        text-decoration: none;
    }
    
    .help-contact {
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 10px;
        padding: 25px;
        margin: 30px 0;
    }
    
    .contact-method {
        display: flex;
        align-items: center;
        margin: 15px 0;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
    }
    
    .contact-icon {
        font-size: 1.5rem;
        color: #667eea;
        margin-right: 15px;
        width: 30px;
        text-align: center;
    }
    
    .breadcrumb-help {
        background: transparent;
        padding: 0;
        margin-bottom: 20px;
    }
    
    .breadcrumb-help .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
        color: #667eea;
    }
    
    @media (max-width: 768px) {
        .help-categories {
            grid-template-columns: 1fr;
        }
        
        .help-header {
            padding: 30px 15px;
        }
        
        .contact-method {
            flex-direction: column;
            text-align: center;
        }
        
        .contact-icon {
            margin-right: 0;
            margin-bottom: 10px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="help-container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="breadcrumb-help">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item active">{% trans "Help & Support" %}</li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="help-header">
        <h1><i class="fas fa-question-circle"></i> {% trans "Help & Support Center" %}</h1>
        <p class="lead">{% trans "Find answers, get support, and learn how to make the most of your School ERP System" %}</p>
        
        <!-- Search -->
        <div class="help-search">
            <input type="text" id="helpSearch" placeholder="{% trans 'Search for help articles, guides, or FAQs...' %}">
            <i class="fas fa-search search-icon"></i>
        </div>
    </div>

    {% block help_content %}
    <!-- Help Categories -->
    <div class="help-categories">
        <a href="{% url 'help:getting_started' %}" class="help-category">
            <div class="help-category-icon">
                <i class="fas fa-rocket"></i>
            </div>
            <h3>{% trans "Getting Started" %}</h3>
            <p>{% trans "New to the system? Start here for basic setup and navigation guides." %}</p>
        </a>

        <a href="{% url 'help:user_guides' %}" class="help-category">
            <div class="help-category-icon">
                <i class="fas fa-book"></i>
            </div>
            <h3>{% trans "User Guides" %}</h3>
            <p>{% trans "Comprehensive guides for students, parents, teachers, and administrators." %}</p>
        </a>

        <a href="{% url 'help:faq' %}" class="help-category">
            <div class="help-category-icon">
                <i class="fas fa-question"></i>
            </div>
            <h3>{% trans "FAQ" %}</h3>
            <p>{% trans "Frequently asked questions and quick answers to common issues." %}</p>
        </a>

        <a href="{% url 'help:video_tutorials' %}" class="help-category">
            <div class="help-category-icon">
                <i class="fas fa-play-circle"></i>
            </div>
            <h3>{% trans "Video Tutorials" %}</h3>
            <p>{% trans "Step-by-step video guides for key features and processes." %}</p>
        </a>

        <a href="{% url 'help:troubleshooting' %}" class="help-category">
            <div class="help-category-icon">
                <i class="fas fa-tools"></i>
            </div>
            <h3>{% trans "Troubleshooting" %}</h3>
            <p>{% trans "Solutions for common technical issues and error messages." %}</p>
        </a>

        <a href="{% url 'help:contact_support' %}" class="help-category">
            <div class="help-category-icon">
                <i class="fas fa-headset"></i>
            </div>
            <h3>{% trans "Contact Support" %}</h3>
            <p>{% trans "Get personalized help from our support team when you need it." %}</p>
        </a>
    </div>

    <!-- Quick Links -->
    <div class="help-quick-links">
        <h3><i class="fas fa-bolt"></i> {% trans "Quick Links" %}</h3>
        <a href="#" class="quick-link">{% trans "Reset Password" %}</a>
        <a href="#" class="quick-link">{% trans "Update Profile" %}</a>
        <a href="#" class="quick-link">{% trans "Check Grades" %}</a>
        <a href="#" class="quick-link">{% trans "Submit Assignment" %}</a>
        <a href="#" class="quick-link">{% trans "View Schedule" %}</a>
        <a href="#" class="quick-link">{% trans "Pay Fees" %}</a>
        <a href="#" class="quick-link">{% trans "Library Books" %}</a>
        <a href="#" class="quick-link">{% trans "Transportation" %}</a>
        <a href="#" class="quick-link">{% trans "System Status" %}</a>
    </div>

    <!-- Contact Information -->
    <div class="help-contact">
        <h3><i class="fas fa-phone"></i> {% trans "Need More Help?" %}</h3>
        <p>{% trans "Can't find what you're looking for? Our support team is here to help!" %}</p>
        
        <div class="contact-method">
            <div class="contact-icon">
                <i class="fas fa-envelope"></i>
            </div>
            <div>
                <strong>{% trans "Email Support" %}</strong><br>
                <span class="text-muted"><EMAIL></span><br>
                <small>{% trans "Response within 24 hours" %}</small>
            </div>
        </div>

        <div class="contact-method">
            <div class="contact-icon">
                <i class="fas fa-phone"></i>
            </div>
            <div>
                <strong>{% trans "Phone Support" %}</strong><br>
                <span class="text-muted">+****************</span><br>
                <small>{% trans "Monday-Friday, 8:00 AM - 5:00 PM" %}</small>
            </div>
        </div>

        <div class="contact-method">
            <div class="contact-icon">
                <i class="fas fa-comments"></i>
            </div>
            <div>
                <strong>{% trans "Live Chat" %}</strong><br>
                <span class="text-muted">{% trans "Available during business hours" %}</span><br>
                <small>{% trans "Click the chat icon in the bottom right" %}</small>
            </div>
        </div>
    </div>
    {% endblock %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Help search functionality
    const searchInput = document.getElementById('helpSearch');
    
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const query = this.value.trim();
                if (query) {
                    // Redirect to search results page
                    window.location.href = `/help/search/?q=${encodeURIComponent(query)}`;
                }
            }
        });
    }
    
    // Add click tracking for help categories
    const helpCategories = document.querySelectorAll('.help-category');
    helpCategories.forEach(category => {
        category.addEventListener('click', function() {
            // Track help category clicks for analytics
            if (typeof gtag !== 'undefined') {
                gtag('event', 'help_category_click', {
                    'category': this.querySelector('h3').textContent
                });
            }
        });
    });
    
    // Quick links functionality
    const quickLinks = document.querySelectorAll('.quick-link');
    quickLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const linkText = this.textContent.trim();
            
            // Handle specific quick links
            switch(linkText) {
                case 'Reset Password':
                    window.location.href = '/accounts/password/reset/';
                    break;
                case 'Update Profile':
                    window.location.href = '/accounts/profile/';
                    break;
                case 'Check Grades':
                    window.location.href = '/academics/grades/';
                    break;
                case 'Submit Assignment':
                    window.location.href = '/academics/assignments/';
                    break;
                case 'View Schedule':
                    window.location.href = '/academics/schedule/';
                    break;
                case 'Pay Fees':
                    window.location.href = '/finance/payments/';
                    break;
                case 'Library Books':
                    window.location.href = '/library/';
                    break;
                case 'Transportation':
                    window.location.href = '/transportation/';
                    break;
                case 'System Status':
                    window.open('/status/', '_blank');
                    break;
                default:
                    e.preventDefault();
            }
        });
    });
});
</script>
{% endblock %}