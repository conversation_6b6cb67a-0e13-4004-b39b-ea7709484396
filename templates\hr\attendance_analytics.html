{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Attendance Analytics" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">{% trans "Attendance Analytics" %}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'hr:dashboard' %}">{% trans "HR" %}</a></li>
                        <li class="breadcrumb-item active">{% trans "Analytics" %}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Overall Statistics -->
    <div class="row">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">{% trans "Total Employees" %}</h6>
                            <h3 class="mb-0">{{ overall_stats.total_employees }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">{% trans "Present Records" %}</h6>
                            <h3 class="mb-0">{{ overall_stats.present_records }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">{% trans "Absent Records" %}</h6>
                            <h3 class="mb-0">{{ overall_stats.absent_records }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-times-circle fa-2x text-danger"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">{% trans "Attendance Rate" %}</h6>
                            <h3 class="mb-0">{{ overall_stats.attendance_rate }}%</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Statistics -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans "Department-wise Attendance Statistics" %}</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Department" %}</th>
                                    <th>{% trans "Total Employees" %}</th>
                                    <th>{% trans "Present Records" %}</th>
                                    <th>{% trans "Absent Records" %}</th>
                                    <th>{% trans "Late Records" %}</th>
                                    <th>{% trans "Attendance Rate" %}</th>
                                    <th>{% trans "Punctuality Rate" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for dept in department_stats %}
                                <tr>
                                    <td>
                                        <strong>{{ dept.department_name }}</strong>
                                    </td>
                                    <td>{{ dept.total_employees }}</td>
                                    <td>
                                        <span class="badge badge-success">{{ dept.present_records }}</span>
                                    </td>
                                    <td>
                                        <span class="badge badge-danger">{{ dept.absent_records }}</span>
                                    </td>
                                    <td>
                                        <span class="badge badge-warning">{{ dept.late_records }}</span>
                                    </td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-{% if dept.attendance_rate >= 90 %}success{% elif dept.attendance_rate >= 75 %}warning{% else %}danger{% endif %}" 
                                                 role="progressbar" style="width: {{ dept.attendance_rate }}%">
                                                {{ dept.attendance_rate }}%
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-{% if dept.punctuality_rate >= 90 %}success{% elif dept.punctuality_rate >= 75 %}warning{% else %}danger{% endif %}" 
                                                 role="progressbar" style="width: {{ dept.punctuality_rate }}%">
                                                {{ dept.punctuality_rate }}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center text-muted">
                                        {% trans "No department data available" %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance Trends Chart -->
    {% if attendance_trends %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans "Attendance Trends" %} ({{ attendance_trends.period }})</h5>
                </div>
                <div class="card-body">
                    <canvas id="attendanceTrendsChart" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <div class="row">
        <!-- Top Performers -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans "Top Performers" %}</h5>
                </div>
                <div class="card-body">
                    {% for performer in top_performers %}
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm me-2">
                                <span class="avatar-title bg-success rounded-circle">
                                    {{ performer.employee.user.first_name|first }}{{ performer.employee.user.last_name|first }}
                                </span>
                            </div>
                            <div>
                                <h6 class="mb-0">{{ performer.employee.user.first_name }} {{ performer.employee.user.last_name }}</h6>
                                <small class="text-muted">{{ performer.employee.position.title }}</small>
                            </div>
                        </div>
                        <div class="text-right">
                            <span class="badge badge-success">{{ performer.stats.attendance_rate }}%</span>
                            <br>
                            <small class="text-muted">{{ performer.stats.present_days }}/{{ performer.stats.working_days }} days</small>
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted text-center">{% trans "No performance data available" %}</p>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Employees Needing Attention -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans "Needs Attention" %}</h5>
                </div>
                <div class="card-body">
                    {% for employee in attention_needed %}
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm me-2">
                                <span class="avatar-title bg-warning rounded-circle">
                                    {{ employee.employee.user.first_name|first }}{{ employee.employee.user.last_name|first }}
                                </span>
                            </div>
                            <div>
                                <h6 class="mb-0">{{ employee.employee.user.first_name }} {{ employee.employee.user.last_name }}</h6>
                                <small class="text-muted">{{ employee.employee.position.title }}</small>
                            </div>
                        </div>
                        <div class="text-right">
                            <span class="badge badge-{% if employee.stats.attendance_rate < 60 %}danger{% else %}warning{% endif %}">
                                {{ employee.stats.attendance_rate }}%
                            </span>
                            <br>
                            <small class="text-muted">{{ employee.stats.absent_days }} absent days</small>
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted text-center">{% trans "All employees performing well!" %}</p>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
{% if attendance_trends %}
// Attendance Trends Chart
const trendsCtx = document.getElementById('attendanceTrendsChart').getContext('2d');
const trendsChart = new Chart(trendsCtx, {
    type: 'line',
    data: {
        labels: [
            {% for month in attendance_trends.monthly_trends %}
                '{{ month.month_name }}'{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: '{% trans "Attendance Rate (%)" %}',
            data: [
                {% for month in attendance_trends.monthly_trends %}
                    {{ month.attendance_rate }}{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1,
            fill: true
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: '{% trans "Monthly Attendance Trends" %}'
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                max: 100,
                ticks: {
                    callback: function(value) {
                        return value + '%';
                    }
                }
            }
        }
    }
});
{% endif %}
</script>
{% endblock %}