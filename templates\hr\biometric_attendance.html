{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Biometric Attendance" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">{% trans "Biometric Attendance Management" %}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'hr:dashboard' %}">{% trans "HR" %}</a></li>
                        <li class="breadcrumb-item active">{% trans "Biometric Attendance" %}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Device Status Cards -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans "Biometric Devices Status" %}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for device in biometric_devices %}
                        <div class="col-md-4 mb-3">
                            <div class="card border-left-{% if device.status == 'active' %}success{% else %}danger{% endif %}">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">{{ device.device_name }}</h6>
                                            <p class="card-text">
                                                <small class="text-muted">
                                                    {% trans "Type" %}: {{ device.device_type|title }}<br>
                                                    {% trans "ID" %}: {{ device.device_id }}
                                                </small>
                                            </p>
                                        </div>
                                        <div class="text-right">
                                            <span class="badge badge-{% if device.status == 'active' %}success{% else %}danger{% endif %}">
                                                {{ device.status|title }}
                                            </span>
                                            <br>
                                            <small class="text-muted">
                                                {% trans "Last sync" %}: {{ device.last_sync|timesince }} {% trans "ago" %}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Biometric Records -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans "Recent Biometric Attendance Records" %}</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Employee" %}</th>
                                    <th>{% trans "Date" %}</th>
                                    <th>{% trans "Check In" %}</th>
                                    <th>{% trans "Check Out" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Total Hours" %}</th>
                                    <th>{% trans "Recorded At" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in recent_biometric_records %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-2">
                                                <span class="avatar-title bg-primary rounded-circle">
                                                    {{ record.employee.user.first_name|first }}{{ record.employee.user.last_name|first }}
                                                </span>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ record.employee.user.first_name }} {{ record.employee.user.last_name }}</h6>
                                                <small class="text-muted">{{ record.employee.employee_id }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ record.date|date:"M d, Y" }}</td>
                                    <td>
                                        {% if record.check_in_time %}
                                            <span class="badge badge-success">{{ record.check_in_time|time:"H:i" }}</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if record.check_out_time %}
                                            <span class="badge badge-info">{{ record.check_out_time|time:"H:i" }}</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge badge-{% if record.status == 'present' %}success{% elif record.status == 'late' %}warning{% else %}danger{% endif %}">
                                            {{ record.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if record.total_hours %}
                                            {{ record.total_hours|floatformat:2 }}h
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ record.created_at|date:"M d, H:i" }}</small>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center text-muted">
                                        {% trans "No recent biometric attendance records found" %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Device Management Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans "Device Management" %}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <button class="btn btn-primary mb-2" onclick="syncAllDevices()">
                                <i class="fas fa-sync-alt"></i> {% trans "Sync All Devices" %}
                            </button>
                            <button class="btn btn-success mb-2" onclick="addNewDevice()">
                                <i class="fas fa-plus"></i> {% trans "Add New Device" %}
                            </button>
                        </div>
                        <div class="col-md-6">
                            <div class="text-right">
                                <button class="btn btn-info mb-2" onclick="exportBiometricData()">
                                    <i class="fas fa-download"></i> {% trans "Export Data" %}
                                </button>
                                <button class="btn btn-secondary mb-2" onclick="viewDeviceSettings()">
                                    <i class="fas fa-cog"></i> {% trans "Device Settings" %}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Device Sync Modal -->
<div class="modal fade" id="syncModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Syncing Devices" %}</h5>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">{% trans "Loading..." %}</span>
                    </div>
                    <p class="mt-2">{% trans "Synchronizing biometric devices..." %}</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function syncAllDevices() {
    $('#syncModal').modal('show');
    
    // Simulate sync process
    setTimeout(function() {
        $('#syncModal').modal('hide');
        alert('{% trans "All devices synchronized successfully!" %}');
        location.reload();
    }, 3000);
}

function addNewDevice() {
    // This would open a form to add a new biometric device
    alert('{% trans "Add new device functionality would be implemented here" %}');
}

function exportBiometricData() {
    // This would export biometric attendance data
    alert('{% trans "Export functionality would be implemented here" %}');
}

function viewDeviceSettings() {
    // This would show device configuration settings
    alert('{% trans "Device settings functionality would be implemented here" %}');
}
</script>
{% endblock %}