{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Bulk Attendance" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">{% trans "Bulk Attendance Marking" %}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'hr:dashboard' %}">{% trans "HR" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'hr:attendance' %}">{% trans "Attendance" %}</a></li>
                        <li class="breadcrumb-item active">{% trans "Bulk Marking" %}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans "Mark Attendance for Multiple Employees" %}</h5>
                </div>
                <div class="card-body">
                    <form method="post" id="bulkAttendanceForm">
                        {% csrf_token %}
                        
                        <!-- Date and Status Selection -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <label for="date" class="form-label">{% trans "Date" %}</label>
                                <input type="date" class="form-control" id="date" name="date" 
                                       value="{% now 'Y-m-d' %}" required>
                            </div>
                            <div class="col-md-4">
                                <label for="status" class="form-label">{% trans "Status" %}</label>
                                <select class="form-control" id="status" name="status" required>
                                    <option value="present">{% trans "Present" %}</option>
                                    <option value="absent">{% trans "Absent" %}</option>
                                    <option value="late">{% trans "Late" %}</option>
                                    <option value="half_day">{% trans "Half Day" %}</option>
                                    <option value="sick_leave">{% trans "Sick Leave" %}</option>
                                    <option value="vacation">{% trans "Vacation" %}</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="department_filter" class="form-label">{% trans "Filter by Department" %}</label>
                                <select class="form-control" id="department_filter">
                                    <option value="">{% trans "All Departments" %}</option>
                                    {% for dept in departments %}
                                        <option value="{{ dept.id }}">{{ dept.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <!-- Employee Selection -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6>{% trans "Select Employees" %}</h6>
                                    <div>
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAll()">
                                            {% trans "Select All" %}
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="selectNone()">
                                            {% trans "Select None" %}
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                                    <table class="table table-striped table-hover">
                                        <thead class="sticky-top bg-light">
                                            <tr>
                                                <th width="50">
                                                    <input type="checkbox" id="selectAllCheckbox" onchange="toggleAll()">
                                                </th>
                                                <th>{% trans "Employee" %}</th>
                                                <th>{% trans "Employee ID" %}</th>
                                                <th>{% trans "Department" %}</th>
                                                <th>{% trans "Position" %}</th>
                                            </tr>
                                        </thead>
                                        <tbody id="employeeTableBody">
                                            {% for employee in employees %}
                                            <tr data-department="{{ employee.position.department.id }}">
                                                <td>
                                                    <input type="checkbox" name="employees" 
                                                           value="{{ employee.employee_id }}" 
                                                           class="employee-checkbox">
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-sm me-2">
                                                            <span class="avatar-title bg-primary rounded-circle">
                                                                {{ employee.user.first_name|first }}{{ employee.user.last_name|first }}
                                                            </span>
                                                        </div>
                                                        <div>
                                                            <h6 class="mb-0">{{ employee.user.first_name }} {{ employee.user.last_name }}</h6>
                                                            <small class="text-muted">{{ employee.user.email }}</small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>{{ employee.employee_id }}</td>
                                                <td>{{ employee.position.department.name }}</td>
                                                <td>{{ employee.position.title }}</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-check"></i> {% trans "Mark Attendance" %}
                                </button>
                                <a href="{% url 'hr:attendance' %}" class="btn btn-secondary">
                                    {% trans "Cancel" %}
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Department filter functionality
document.getElementById('department_filter').addEventListener('change', function() {
    const selectedDept = this.value;
    const rows = document.querySelectorAll('#employeeTableBody tr');
    
    rows.forEach(row => {
        if (selectedDept === '' || row.dataset.department === selectedDept) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
    
    // Uncheck all when filtering
    selectNone();
});

// Select all visible employees
function selectAll() {
    const checkboxes = document.querySelectorAll('.employee-checkbox');
    checkboxes.forEach(checkbox => {
        const row = checkbox.closest('tr');
        if (row.style.display !== 'none') {
            checkbox.checked = true;
        }
    });
    updateSelectAllCheckbox();
}

// Select none
function selectNone() {
    const checkboxes = document.querySelectorAll('.employee-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    document.getElementById('selectAllCheckbox').checked = false;
}

// Toggle all based on header checkbox
function toggleAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    if (selectAllCheckbox.checked) {
        selectAll();
    } else {
        selectNone();
    }
}

// Update select all checkbox based on individual selections
function updateSelectAllCheckbox() {
    const checkboxes = document.querySelectorAll('.employee-checkbox');
    const visibleCheckboxes = Array.from(checkboxes).filter(cb => 
        cb.closest('tr').style.display !== 'none'
    );
    const checkedVisible = visibleCheckboxes.filter(cb => cb.checked);
    
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    selectAllCheckbox.checked = visibleCheckboxes.length > 0 && 
                               checkedVisible.length === visibleCheckboxes.length;
}

// Add event listeners to individual checkboxes
document.querySelectorAll('.employee-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', updateSelectAllCheckbox);
});

// Form validation
document.getElementById('bulkAttendanceForm').addEventListener('submit', function(e) {
    const checkedBoxes = document.querySelectorAll('.employee-checkbox:checked');
    if (checkedBoxes.length === 0) {
        e.preventDefault();
        alert('{% trans "Please select at least one employee" %}');
        return false;
    }
    
    const confirmation = confirm(
        `{% trans "Are you sure you want to mark attendance for" %} ${checkedBoxes.length} {% trans "employees?" %}`
    );
    
    if (!confirmation) {
        e.preventDefault();
        return false;
    }
});
</script>
{% endblock %}