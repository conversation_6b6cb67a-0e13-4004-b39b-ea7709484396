{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "QR Code Attendance" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">{% trans "QR Code Attendance" %}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'hr:dashboard' %}">{% trans "HR" %}</a></li>
                        <li class="breadcrumb-item active">{% trans "QR Attendance" %}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- QR Code Display -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans "Attendance QR Code" %}</h5>
                </div>
                <div class="card-body text-center">
                    <div id="qrcode" class="mb-3"></div>
                    <p class="text-muted">
                        {% trans "Scan this QR code with your mobile device to mark attendance" %}
                    </p>
                    <div class="alert alert-info">
                        <strong>{% trans "Location" %}:</strong> {{ qr_code_data.location|title }}<br>
                        <strong>{% trans "Valid Until" %}:</strong> {{ qr_code_data.expires_at|date:"H:i" }}
                    </div>
                    <button class="btn btn-primary" onclick="generateNewQR()">
                        <i class="fas fa-refresh"></i> {% trans "Generate New QR Code" %}
                    </button>
                </div>
            </div>
        </div>

        <!-- Manual QR Entry -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans "Manual QR Entry" %}</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="form-group">
                            <label for="employee_id">{% trans "Employee ID" %}</label>
                            <input type="text" class="form-control" id="employee_id" name="employee_id" 
                                   placeholder="{% trans 'Enter employee ID' %}" required>
                        </div>
                        <div class="form-group">
                            <label for="qr_token">{% trans "QR Token" %}</label>
                            <input type="text" class="form-control" id="qr_token" name="qr_token" 
                                   value="{{ qr_code_data.qr_token }}" readonly>
                        </div>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-check"></i> {% trans "Mark Attendance" %}
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- QR Code Instructions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans "How to Use QR Code Attendance" %}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <div class="avatar-lg mx-auto mb-3">
                                    <span class="avatar-title bg-primary rounded-circle">
                                        <i class="fas fa-mobile-alt fa-2x"></i>
                                    </span>
                                </div>
                                <h6>{% trans "Step 1: Open Camera" %}</h6>
                                <p class="text-muted">
                                    {% trans "Open your mobile device camera or QR code scanner app" %}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <div class="avatar-lg mx-auto mb-3">
                                    <span class="avatar-title bg-success rounded-circle">
                                        <i class="fas fa-qrcode fa-2x"></i>
                                    </span>
                                </div>
                                <h6>{% trans "Step 2: Scan QR Code" %}</h6>
                                <p class="text-muted">
                                    {% trans "Point your camera at the QR code displayed above" %}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <div class="avatar-lg mx-auto mb-3">
                                    <span class="avatar-title bg-info rounded-circle">
                                        <i class="fas fa-check-circle fa-2x"></i>
                                    </span>
                                </div>
                                <h6>{% trans "Step 3: Confirm" %}</h6>
                                <p class="text-muted">
                                    {% trans "Enter your employee ID when prompted to complete attendance" %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent QR Attendance -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans "Recent QR Code Attendance" %}</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Employee" %}</th>
                                    <th>{% trans "Time" %}</th>
                                    <th>{% trans "Type" %}</th>
                                    <th>{% trans "Location" %}</th>
                                    <th>{% trans "Status" %}</th>
                                </tr>
                            </thead>
                            <tbody id="recentQRAttendance">
                                <tr>
                                    <td colspan="5" class="text-center text-muted">
                                        {% trans "No recent QR attendance records" %}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include QR Code Library -->
<script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>

<script>
// Generate QR Code
document.addEventListener('DOMContentLoaded', function() {
    const qrData = {{ qr_code_json|safe }};
    
    QRCode.toCanvas(document.getElementById('qrcode'), qrData, {
        width: 256,
        height: 256,
        colorDark: '#000000',
        colorLight: '#ffffff',
        correctLevel: QRCode.CorrectLevel.M
    }, function (error) {
        if (error) console.error(error);
        console.log('QR code generated successfully!');
    });
});

function generateNewQR() {
    // Reload the page to generate a new QR code
    location.reload();
}

// Auto-refresh QR code every 30 minutes
setInterval(function() {
    location.reload();
}, 30 * 60 * 1000);

// Show countdown timer for QR code expiration
function updateCountdown() {
    const expiresAt = new Date('{{ qr_code_data.expires_at|date:"c" }}');
    const now = new Date();
    const timeLeft = expiresAt - now;
    
    if (timeLeft > 0) {
        const minutes = Math.floor(timeLeft / 60000);
        const seconds = Math.floor((timeLeft % 60000) / 1000);
        
        // Update countdown display if element exists
        const countdownElement = document.getElementById('countdown');
        if (countdownElement) {
            countdownElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }
    } else {
        // QR code expired, reload page
        location.reload();
    }
}

// Update countdown every second
setInterval(updateCountdown, 1000);
</script>
{% endblock %}