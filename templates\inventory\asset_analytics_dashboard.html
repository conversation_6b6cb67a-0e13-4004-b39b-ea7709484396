{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Asset Analytics Dashboard" %}{% endblock %}

{% block extra_css %}
<style>
    .analytics-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .analytics-card.warning {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    .analytics-card.success {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    .analytics-card.info {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
    .chart-container {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    .metric-card {
        text-align: center;
        padding: 20px;
        border-radius: 10px;
        background: #f8f9fa;
        border-left: 4px solid #007bff;
    }
    .asset-item {
        border-left: 4px solid #007bff;
        padding: 15px;
        margin-bottom: 10px;
        background: #f8f9fa;
        border-radius: 5px;
    }
    .asset-item.high-value {
        border-left-color: #28a745;
    }
    .asset-item.depreciated {
        border-left-color: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-line"></i> {% trans "Asset Analytics Dashboard" %}</h2>
                <div>
                    <a href="{% url 'inventory:dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-tachometer-alt"></i> {% trans "Main Dashboard" %}
                    </a>
                    <a href="{% url 'inventory:maintenance_analytics' %}" class="btn btn-info">
                        <i class="fas fa-tools"></i> {% trans "Maintenance Analytics" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="row">
        <div class="col-md-3">
            <div class="analytics-card">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ total_assets }}</h3>
                        <p class="mb-0">{% trans "Total Assets" %}</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-boxes fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="analytics-card success">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>${{ total_asset_value|floatformat:2 }}</h3>
                        <p class="mb-0">{% trans "Total Value" %}</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="analytics-card warning">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>${{ total_depreciation|floatformat:2 }}</h3>
                        <p class="mb-0">{% trans "Total Depreciation" %}</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line-down fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="analytics-card info">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>
                            {% if total_assets > 0 %}
                                {% widthratio assets_needing_maintenance total_assets 100 %}%
                            {% else %}
                                0%
                            {% endif %}
                        </h3>
                        <p class="mb-0">{% trans "Need Maintenance" %}</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-wrench fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Asset Distribution by Category -->
        <div class="col-md-6">
            <div class="chart-container">
                <h5><i class="fas fa-chart-pie"></i> {% trans "Assets by Category" %}</h5>
                {% if category_distribution %}
                    <canvas id="categoryChart" width="400" height="200"></canvas>
                {% else %}
                    <p class="text-muted text-center py-4">{% trans "No asset data available." %}</p>
                {% endif %}
            </div>
        </div>

        <!-- Asset Status Distribution -->
        <div class="col-md-6">
            <div class="chart-container">
                <h5><i class="fas fa-chart-doughnut"></i> {% trans "Assets by Status" %}</h5>
                {% if status_distribution %}
                    <canvas id="statusChart" width="400" height="200"></canvas>
                {% else %}
                    <p class="text-muted text-center py-4">{% trans "No status data available." %}</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Asset Age Analysis -->
        <div class="col-md-8">
            <div class="chart-container">
                <h5><i class="fas fa-chart-bar"></i> {% trans "Asset Age Distribution" %}</h5>
                {% if age_distribution %}
                    <canvas id="ageChart" width="400" height="200"></canvas>
                {% else %}
                    <p class="text-muted text-center py-4">{% trans "No age data available." %}</p>
                {% endif %}
            </div>
        </div>

        <!-- Top Assets by Value -->
        <div class="col-md-4">
            <div class="chart-container">
                <h5><i class="fas fa-trophy"></i> {% trans "Top Assets by Value" %}</h5>
                {% if top_assets_by_value %}
                    {% for asset in top_assets_by_value %}
                        <div class="asset-item high-value">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <strong>{{ asset.asset_tag }}</strong><br>
                                    <small class="text-muted">{{ asset.name|truncatechars:25 }}</small>
                                </div>
                                <div class="text-end">
                                    <span class="fw-bold">${{ asset.book_value|floatformat:2 }}</span>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center py-4">{% trans "No asset value data available." %}</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Depreciation Trends -->
        <div class="col-md-8">
            <div class="chart-container">
                <h5><i class="fas fa-chart-line"></i> {% trans "Depreciation Trends" %}</h5>
                {% if depreciation_trends %}
                    <canvas id="depreciationChart" width="400" height="200"></canvas>
                {% else %}
                    <p class="text-muted text-center py-4">{% trans "No depreciation trend data available." %}</p>
                {% endif %}
            </div>
        </div>

        <!-- Assets Needing Attention -->
        <div class="col-md-4">
            <div class="chart-container">
                <h5><i class="fas fa-exclamation-triangle"></i> {% trans "Assets Needing Attention" %}</h5>
                {% if assets_needing_attention %}
                    {% for asset in assets_needing_attention %}
                        <div class="asset-item {% if asset.maintenance_due %}depreciated{% endif %}">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <strong>{{ asset.asset_tag }}</strong><br>
                                    <small class="text-muted">{{ asset.name|truncatechars:25 }}</small>
                                </div>
                                <div class="text-end">
                                    {% if asset.maintenance_due %}
                                        <span class="badge bg-danger">{% trans "Maintenance Due" %}</span>
                                    {% elif asset.condition == 'poor' %}
                                        <span class="badge bg-warning">{% trans "Poor Condition" %}</span>
                                    {% elif asset.book_value < asset.depreciation_threshold %}
                                        <span class="badge bg-info">{% trans "Highly Depreciated" %}</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center py-4">{% trans "All assets are in good condition." %}</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Detailed Analytics Tables -->
    <div class="row">
        <div class="col-md-6">
            <div class="chart-container">
                <h5><i class="fas fa-table"></i> {% trans "Category Performance" %}</h5>
                {% if category_performance %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Category" %}</th>
                                    <th>{% trans "Count" %}</th>
                                    <th>{% trans "Total Value" %}</th>
                                    <th>{% trans "Avg Age" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for category in category_performance %}
                                    <tr>
                                        <td>{{ category.name }}</td>
                                        <td>{{ category.asset_count }}</td>
                                        <td>${{ category.total_value|floatformat:2 }}</td>
                                        <td>{{ category.avg_age|floatformat:1 }} {% trans "years" %}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center py-4">{% trans "No category performance data available." %}</p>
                {% endif %}
            </div>
        </div>

        <div class="col-md-6">
            <div class="chart-container">
                <h5><i class="fas fa-map-marker-alt"></i> {% trans "Location Analysis" %}</h5>
                {% if location_analysis %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Location" %}</th>
                                    <th>{% trans "Assets" %}</th>
                                    <th>{% trans "Total Value" %}</th>
                                    <th>{% trans "Utilization" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for location in location_analysis %}
                                    <tr>
                                        <td>{{ location.name|default:"Unassigned" }}</td>
                                        <td>{{ location.asset_count }}</td>
                                        <td>${{ location.total_value|floatformat:2 }}</td>
                                        <td>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar" role="progressbar" 
                                                     style="width: {{ location.utilization_rate }}%">
                                                    {{ location.utilization_rate|floatformat:0 }}%
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center py-4">{% trans "No location analysis data available." %}</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Asset Lifecycle Analysis -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <h5><i class="fas fa-recycle"></i> {% trans "Asset Lifecycle Analysis" %}</h5>
                {% if lifecycle_analysis %}
                    <div class="row">
                        <div class="col-md-3">
                            <div class="metric-card">
                                <h4>{{ lifecycle_analysis.new_assets }}</h4>
                                <p class="mb-0">{% trans "New Assets" %}<br><small class="text-muted">(< 1 year)</small></p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <h4>{{ lifecycle_analysis.mature_assets }}</h4>
                                <p class="mb-0">{% trans "Mature Assets" %}<br><small class="text-muted">(1-5 years)</small></p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <h4>{{ lifecycle_analysis.aging_assets }}</h4>
                                <p class="mb-0">{% trans "Aging Assets" %}<br><small class="text-muted">(5-10 years)</small></p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <h4>{{ lifecycle_analysis.legacy_assets }}</h4>
                                <p class="mb-0">{% trans "Legacy Assets" %}<br><small class="text-muted">(> 10 years)</small></p>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <p class="text-muted text-center py-4">{% trans "No lifecycle analysis data available." %}</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Category Distribution Chart
    {% if category_distribution %}
        const categoryCtx = document.getElementById('categoryChart').getContext('2d');
        new Chart(categoryCtx, {
            type: 'pie',
            data: {
                labels: [
                    {% for item in category_distribution %}
                        '{{ item.name }}'{% if not forloop.last %},{% endif %}
                    {% endfor %}
                ],
                datasets: [{
                    data: [
                        {% for item in category_distribution %}
                            {{ item.count }}{% if not forloop.last %},{% endif %}
                        {% endfor %}
                    ],
                    backgroundColor: [
                        '#007bff', '#28a745', '#ffc107', '#dc3545', '#17a2b8', 
                        '#6f42c1', '#e83e8c', '#fd7e14', '#20c997', '#6c757d'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    {% endif %}

    // Status Distribution Chart
    {% if status_distribution %}
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: [
                    {% for item in status_distribution %}
                        '{{ item.status|capfirst }}'{% if not forloop.last %},{% endif %}
                    {% endfor %}
                ],
                datasets: [{
                    data: [
                        {% for item in status_distribution %}
                            {{ item.count }}{% if not forloop.last %},{% endif %}
                        {% endfor %}
                    ],
                    backgroundColor: [
                        '#28a745', '#ffc107', '#6c757d', '#dc3545', '#17a2b8'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    {% endif %}

    // Age Distribution Chart
    {% if age_distribution %}
        const ageCtx = document.getElementById('ageChart').getContext('2d');
        new Chart(ageCtx, {
            type: 'bar',
            data: {
                labels: [
                    {% for item in age_distribution %}
                        '{{ item.age_range }}'{% if not forloop.last %},{% endif %}
                    {% endfor %}
                ],
                datasets: [{
                    label: '{% trans "Number of Assets" %}',
                    data: [
                        {% for item in age_distribution %}
                            {{ item.count }}{% if not forloop.last %},{% endif %}
                        {% endfor %}
                    ],
                    backgroundColor: 'rgba(0, 123, 255, 0.8)',
                    borderColor: '#007bff',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '{% trans "Number of Assets" %}'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '{% trans "Age Range (Years)" %}'
                        }
                    }
                }
            }
        });
    {% endif %}

    // Depreciation Trends Chart
    {% if depreciation_trends %}
        const depreciationCtx = document.getElementById('depreciationChart').getContext('2d');
        new Chart(depreciationCtx, {
            type: 'line',
            data: {
                labels: [
                    {% for item in depreciation_trends %}
                        '{{ item.year }}'{% if not forloop.last %},{% endif %}
                    {% endfor %}
                ],
                datasets: [{
                    label: '{% trans "Purchase Value" %}',
                    data: [
                        {% for item in depreciation_trends %}
                            {{ item.purchase_value|default:0 }}{% if not forloop.last %},{% endif %}
                        {% endfor %}
                    ],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    fill: false
                }, {
                    label: '{% trans "Current Value" %}',
                    data: [
                        {% for item in depreciation_trends %}
                            {{ item.current_value|default:0 }}{% if not forloop.last %},{% endif %}
                        {% endfor %}
                    ],
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    fill: false
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '{% trans "Value ($)" %}'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '{% trans "Year" %}'
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    }
                }
            }
        });
    {% endif %}
</script>
{% endblock %}