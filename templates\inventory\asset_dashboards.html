{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Asset Dashboards" %}{% endblock %}

{% block extra_css %}
<style>
    .dashboard-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        transition: transform 0.3s ease;
    }
    .dashboard-card:hover {
        transform: translateY(-5px);
    }
    .dashboard-card.success {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    .dashboard-card.warning {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    .dashboard-card.info {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
    .dashboard-card.danger {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    }
    .chart-container {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    .metric-item {
        text-align: center;
        padding: 15px;
        border-radius: 8px;
        background: #f8f9fa;
        border-left: 4px solid #007bff;
        margin-bottom: 15px;
    }
    .trend-up {
        color: #28a745;
    }
    .trend-down {
        color: #dc3545;
    }
    .trend-stable {
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-tachometer-alt"></i> {% trans "Asset Dashboards" %}</h2>
                <div>
                    <a href="{% url 'inventory:asset_analytics_dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-chart-line"></i> {% trans "Analytics Dashboard" %}
                    </a>
                    <a href="{% url 'inventory:dashboard' %}" class="btn btn-info">
                        <i class="fas fa-home"></i> {% trans "Main Dashboard" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Executive Summary -->
    <div class="row">
        <div class="col-md-3">
            <div class="dashboard-card">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ total_assets }}</h3>
                        <p class="mb-0">{% trans "Total Assets" %}</p>
                        <small class="opacity-75">{% trans "All assets in system" %}</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-boxes fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="dashboard-card success">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ active_assets }}</h3>
                        <p class="mb-0">{% trans "Active Assets" %}</p>
                        <small class="opacity-75">{% trans "Currently in use" %}</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="dashboard-card warning">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ maintenance_assets }}</h3>
                        <p class="mb-0">{% trans "Under Maintenance" %}</p>
                        <small class="opacity-75">{% trans "Currently being serviced" %}</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-tools fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="dashboard-card danger">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ retired_assets }}</h3>
                        <p class="mb-0">{% trans "Retired Assets" %}</p>
                        <small class="opacity-75">{% trans "End of lifecycle" %}</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-archive fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Financial Overview -->
    <div class="row">
        <div class="col-md-4">
            <div class="chart-container">
                <h5><i class="fas fa-dollar-sign text-success"></i> {% trans "Financial Overview" %}</h5>
                <div class="row">
                    <div class="col-12">
                        <div class="metric-item">
                            <h4 class="text-success">${{ total_asset_value|floatformat:2 }}</h4>
                            <p class="mb-0">{% trans "Total Asset Value" %}</p>
                            <small class="text-muted">{% trans "Original purchase value" %}</small>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="metric-item">
                            <h4 class="text-info">${{ current_asset_value|floatformat:2 }}</h4>
                            <p class="mb-0">{% trans "Current Book Value" %}</p>
                            <small class="text-muted">{% trans "After depreciation" %}</small>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="metric-item">
                            <h4 class="text-warning">${{ monthly_maintenance_cost|floatformat:2 }}</h4>
                            <p class="mb-0">{% trans "Monthly Maintenance Cost" %}</p>
                            <small class="text-muted">{% trans "Current month expenses" %}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Utilization Metrics -->
        <div class="col-md-4">
            <div class="chart-container">
                <h5><i class="fas fa-chart-pie text-primary"></i> {% trans "Utilization Metrics" %}</h5>
                <div class="text-center mb-4">
                    <div class="position-relative d-inline-block">
                        <canvas id="utilizationChart" width="150" height="150"></canvas>
                        <div class="position-absolute top-50 start-50 translate-middle">
                            <h3 class="mb-0">{{ utilization_rate }}%</h3>
                            <small class="text-muted">{% trans "Utilization" %}</small>
                        </div>
                    </div>
                </div>
                <div class="row text-center">
                    <div class="col-6">
                        <div class="metric-item">
                            <h5 class="text-success">{{ active_assets }}</h5>
                            <small>{% trans "Total Active" %}</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="metric-item">
                            <h5 class="text-primary">{% widthratio utilization_rate 100 active_assets %}</h5>
                            <small>{% trans "Assigned" %}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Maintenance Overview -->
        <div class="col-md-4">
            <div class="chart-container">
                <h5><i class="fas fa-wrench text-warning"></i> {% trans "Maintenance Overview" %}</h5>
                <div class="row">
                    <div class="col-12">
                        <div class="metric-item">
                            <h4 class="{% if maintenance_due > 0 %}text-danger{% else %}text-success{% endif %}">
                                {{ maintenance_due }}
                            </h4>
                            <p class="mb-0">{% trans "Maintenance Due" %}</p>
                            <small class="text-muted">{% trans "Assets requiring attention" %}</small>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="metric-item">
                            <h4 class="text-info">{{ recent_maintenance }}</h4>
                            <p class="mb-0">{% trans "Recent Maintenance" %}</p>
                            <small class="text-muted">{% trans "Last 30 days" %}</small>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="metric-item">
                            <h4 class="text-primary">{{ recent_acquisitions }}</h4>
                            <p class="mb-0">{% trans "New Acquisitions" %}</p>
                            <small class="text-muted">{% trans "Last 30 days" %}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Trends and Analytics -->
    <div class="row">
        <div class="col-md-8">
            <div class="chart-container">
                <h5><i class="fas fa-chart-line"></i> {% trans "Asset Acquisition Trends" %}</h5>
                {% if monthly_trends %}
                    <canvas id="trendsChart" width="400" height="200"></canvas>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">{% trans "No trend data available" %}</h6>
                        <p class="text-muted">{% trans "Asset acquisition data will appear here over time." %}</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-md-4">
            <div class="chart-container">
                <h5><i class="fas fa-bolt"></i> {% trans "Quick Actions" %}</h5>
                <div class="d-grid gap-2">
                    <a href="{% url 'inventory:item_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> {% trans "Add New Asset" %}
                    </a>
                    <a href="{% url 'inventory:maintenance_schedule' %}" class="btn btn-warning">
                        <i class="fas fa-calendar-plus"></i> {% trans "Schedule Maintenance" %}
                    </a>
                    <a href="{% url 'inventory:asset_utilization_report' %}" class="btn btn-info">
                        <i class="fas fa-chart-bar"></i> {% trans "Utilization Report" %}
                    </a>
                    <a href="{% url 'inventory:asset_depreciation_report' %}" class="btn btn-secondary">
                        <i class="fas fa-chart-line-down"></i> {% trans "Depreciation Report" %}
                    </a>
                    <a href="{% url 'inventory:replacement_planning_tools' %}" class="btn btn-danger">
                        <i class="fas fa-tools"></i> {% trans "Replacement Planning" %}
                    </a>
                    <a href="{% url 'inventory:reports' %}" class="btn btn-success">
                        <i class="fas fa-file-export"></i> {% trans "Export Reports" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Performance Indicators -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <h5><i class="fas fa-trophy"></i> {% trans "Key Performance Indicators" %}</h5>
                <div class="row">
                    <div class="col-md-3">
                        <div class="metric-item">
                            <h4 class="text-success">
                                {% if total_assets > 0 %}
                                    {% widthratio active_assets total_assets 100 %}%
                                {% else %}
                                    0%
                                {% endif %}
                            </h4>
                            <p class="mb-0">{% trans "Asset Availability" %}</p>
                            <small class="text-muted">{% trans "Active vs Total" %}</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-item">
                            <h4 class="text-info">{{ utilization_rate }}%</h4>
                            <p class="mb-0">{% trans "Utilization Rate" %}</p>
                            <small class="text-muted">{% trans "Assigned vs Active" %}</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-item">
                            <h4 class="{% if maintenance_due > 0 %}text-warning{% else %}text-success{% endif %}">
                                {% if active_assets > 0 %}
                                    {% widthratio maintenance_due active_assets 100 %}%
                                {% else %}
                                    0%
                                {% endif %}
                            </h4>
                            <p class="mb-0">{% trans "Maintenance Due Rate" %}</p>
                            <small class="text-muted">{% trans "Due vs Active" %}</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-item">
                            <h4 class="text-primary">
                                {% if total_asset_value > 0 %}
                                    {% widthratio current_asset_value total_asset_value 100 %}%
                                {% else %}
                                    0%
                                {% endif %}
                            </h4>
                            <p class="mb-0">{% trans "Value Retention" %}</p>
                            <small class="text-muted">{% trans "Current vs Original" %}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Utilization Doughnut Chart
    const utilizationCtx = document.getElementById('utilizationChart').getContext('2d');
    new Chart(utilizationCtx, {
        type: 'doughnut',
        data: {
            labels: ['{% trans "Assigned" %}', '{% trans "Unassigned" %}'],
            datasets: [{
                data: [
                    {% widthratio utilization_rate 100 active_assets %},
                    {{ active_assets }} - {% widthratio utilization_rate 100 active_assets %}
                ],
                backgroundColor: ['#007bff', '#e9ecef'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: false,
            maintainAspectRatio: false,
            cutout: '70%',
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // Trends Chart
    {% if monthly_trends %}
        const trendsCtx = document.getElementById('trendsChart').getContext('2d');
        new Chart(trendsCtx, {
            type: 'line',
            data: {
                labels: [
                    {% for trend in monthly_trends %}
                        '{{ trend.month }}'{% if not forloop.last %},{% endif %}
                    {% endfor %}
                ],
                datasets: [{
                    label: '{% trans "New Acquisitions" %}',
                    data: [
                        {% for trend in monthly_trends %}
                            {{ trend.acquisitions }}{% if not forloop.last %},{% endif %}
                        {% endfor %}
                    ],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    fill: true,
                    tension: 0.4
                }, {
                    label: '{% trans "Total Value ($)" %}',
                    data: [
                        {% for trend in monthly_trends %}
                            {{ trend.total_value|default:0 }}{% if not forloop.last %},{% endif %}
                        {% endfor %}
                    ],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    fill: false,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '{% trans "Number of Assets" %}'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: '{% trans "Value ($)" %}'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    }
                }
            }
        });
    {% endif %}
</script>
{% endblock %}