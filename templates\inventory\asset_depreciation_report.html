{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Asset Depreciation Report" %}{% endblock %}

{% block extra_css %}
<style>
    .depreciation-card {
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
    }
    .depreciation-card.high {
        border-left-color: #dc3545;
        background-color: rgba(220, 53, 69, 0.05);
    }
    .depreciation-card.medium {
        border-left-color: #ffc107;
        background-color: rgba(255, 193, 7, 0.05);
    }
    .depreciation-card.low {
        border-left-color: #28a745;
        background-color: rgba(40, 167, 69, 0.05);
    }
    .chart-container {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    .metric-card {
        text-align: center;
        padding: 20px;
        border-radius: 10px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-line-down"></i> {% trans "Asset Depreciation Report" %}</h2>
                <div>
                    <a href="{% url 'inventory:asset_analytics_dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-tachometer-alt"></i> {% trans "Analytics Dashboard" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Overall Depreciation Metrics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="metric-card">
                <h3>${{ depreciation_data.total_purchase_value|floatformat:2 }}</h3>
                <p class="mb-0">{% trans "Total Purchase Value" %}</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <h3>${{ depreciation_data.total_current_value|floatformat:2 }}</h3>
                <p class="mb-0">{% trans "Current Book Value" %}</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <h3>${{ depreciation_data.total_depreciation|floatformat:2 }}</h3>
                <p class="mb-0">{% trans "Total Depreciation" %}</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                <h3>{{ depreciation_data.depreciation_rate|floatformat:1 }}%</h3>
                <p class="mb-0">{% trans "Depreciation Rate" %}</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Depreciation by Category -->
        <div class="col-md-6">
            <div class="chart-container">
                <h5><i class="fas fa-chart-pie"></i> {% trans "Depreciation by Category" %}</h5>
                {% if depreciation_data.by_category %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Category" %}</th>
                                    <th>{% trans "Assets" %}</th>
                                    <th>{% trans "Purchase Value" %}</th>
                                    <th>{% trans "Current Value" %}</th>
                                    <th>{% trans "Depreciation" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for category, data in depreciation_data.by_category.items %}
                                    <tr>
                                        <td>{{ category }}</td>
                                        <td>{{ data.count }}</td>
                                        <td>${{ data.purchase_value|floatformat:2 }}</td>
                                        <td>${{ data.current_value|floatformat:2 }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="me-2">${{ data.depreciation|floatformat:2 }}</span>
                                                {% with rate=data.depreciation|div:data.purchase_value|mul:100 %}
                                                    <span class="badge 
                                                        {% if rate >= 70 %}bg-danger
                                                        {% elif rate >= 40 %}bg-warning
                                                        {% else %}bg-success{% endif %}">
                                                        {{ rate|floatformat:1 }}%
                                                    </span>
                                                {% endwith %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center py-4">{% trans "No category data available." %}</p>
                {% endif %}
            </div>
        </div>

        <!-- Depreciation Methods -->
        <div class="col-md-6">
            <div class="chart-container">
                <h5><i class="fas fa-cogs"></i> {% trans "Depreciation Methods" %}</h5>
                {% if depreciation_methods %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Method" %}</th>
                                    <th>{% trans "Assets" %}</th>
                                    <th>{% trans "Total Value" %}</th>
                                    <th>{% trans "Avg Depreciation" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for method in depreciation_methods %}
                                    <tr>
                                        <td>
                                            <span class="badge bg-secondary">
                                                {{ method.depreciation_method|capfirst|default:"Unknown" }}
                                            </span>
                                        </td>
                                        <td>{{ method.count }}</td>
                                        <td>${{ method.total_value|floatformat:2 }}</td>
                                        <td>${{ method.avg_depreciation|floatformat:2 }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center py-4">{% trans "No depreciation method data available." %}</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- High Depreciation Assets -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <h5><i class="fas fa-exclamation-triangle"></i> {% trans "Assets with High Depreciation (>50%)" %}</h5>
                {% if high_depreciation_assets %}
                    <div class="row">
                        {% for item in high_depreciation_assets %}
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card depreciation-card 
                                    {% if item.depreciation_rate >= 80 %}high
                                    {% elif item.depreciation_rate >= 60 %}medium
                                    {% else %}low{% endif %}">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="card-title">{{ item.asset.asset_tag }}</h6>
                                                <p class="card-text">{{ item.asset.name|truncatechars:30 }}</p>
                                            </div>
                                            <span class="badge 
                                                {% if item.depreciation_rate >= 80 %}bg-danger
                                                {% elif item.depreciation_rate >= 60 %}bg-warning
                                                {% else %}bg-info{% endif %}">
                                                {{ item.depreciation_rate|floatformat:1 }}%
                                            </span>
                                        </div>
                                        <div class="mb-2">
                                            <small class="text-muted">
                                                <i class="fas fa-tag"></i> {{ item.asset.category.name }}
                                            </small>
                                        </div>
                                        <div class="mb-2">
                                            <small class="text-muted">
                                                <i class="fas fa-calendar"></i> {% trans "Age:" %} {{ item.asset.age_in_years|floatformat:1 }} {% trans "years" %}
                                            </small>
                                        </div>
                                        <div class="row">
                                            <div class="col-6">
                                                <small class="text-muted">{% trans "Purchase:" %}</small><br>
                                                <strong>${{ item.asset.purchase_price|floatformat:2 }}</strong>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">{% trans "Current:" %}</small><br>
                                                <strong>${{ item.asset.book_value|floatformat:2 }}</strong>
                                            </div>
                                        </div>
                                        <div class="progress mt-2" style="height: 8px;">
                                            <div class="progress-bar 
                                                {% if item.depreciation_rate >= 80 %}bg-danger
                                                {% elif item.depreciation_rate >= 60 %}bg-warning
                                                {% else %}bg-info{% endif %}" 
                                                 role="progressbar" 
                                                 style="width: {{ item.depreciation_rate }}%">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h5 class="text-success">{% trans "No High Depreciation Assets!" %}</h5>
                        <p class="text-muted">{% trans "All assets have reasonable depreciation levels." %}</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Replacement Candidates -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <h5><i class="fas fa-exchange-alt"></i> {% trans "Replacement Candidates" %}</h5>
                <p class="text-muted">{% trans "Assets with high depreciation (>70%) and poor condition or old age (>5 years)" %}</p>
                {% if replacement_candidates %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Asset" %}</th>
                                    <th>{% trans "Category" %}</th>
                                    <th>{% trans "Age" %}</th>
                                    <th>{% trans "Condition" %}</th>
                                    <th>{% trans "Depreciation" %}</th>
                                    <th>{% trans "Current Value" %}</th>
                                    <th>{% trans "Priority" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in replacement_candidates %}
                                    <tr>
                                        <td>
                                            <strong>{{ item.asset.asset_tag }}</strong><br>
                                            <small class="text-muted">{{ item.asset.name|truncatechars:30 }}</small>
                                        </td>
                                        <td>{{ item.asset.category.name }}</td>
                                        <td>{{ item.age|floatformat:1 }} {% trans "years" %}</td>
                                        <td>
                                            <span class="badge bg-{% if item.asset.condition == 'poor' %}danger{% elif item.asset.condition == 'damaged' %}danger{% elif item.asset.condition == 'fair' %}warning{% else %}secondary{% endif %}">
                                                {{ item.asset.get_condition_display }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="progress flex-grow-1 me-2" style="height: 20px; width: 60px;">
                                                    <div class="progress-bar bg-danger" role="progressbar" 
                                                         style="width: {{ item.depreciation_rate }}%">
                                                    </div>
                                                </div>
                                                <span class="small">{{ item.depreciation_rate|floatformat:1 }}%</span>
                                            </div>
                                        </td>
                                        <td>${{ item.asset.book_value|floatformat:2 }}</td>
                                        <td>
                                            {% if item.depreciation_rate >= 90 and item.age >= 8 %}
                                                <span class="badge bg-danger">{% trans "Critical" %}</span>
                                            {% elif item.depreciation_rate >= 80 and item.age >= 6 %}
                                                <span class="badge bg-warning">{% trans "High" %}</span>
                                            {% else %}
                                                <span class="badge bg-info">{% trans "Medium" %}</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-thumbs-up fa-3x text-success mb-3"></i>
                        <h5 class="text-success">{% trans "No Immediate Replacements Needed!" %}</h5>
                        <p class="text-muted">{% trans "All assets are in acceptable condition for their age." %}</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Depreciation Insights -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <h5><i class="fas fa-lightbulb"></i> {% trans "Depreciation Insights & Recommendations" %}</h5>
                <div class="row">
                    <div class="col-md-4">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0"><i class="fas fa-chart-line"></i> {% trans "Depreciation Trends" %}</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-arrow-down text-danger"></i> {% trans "Overall depreciation rate:" %} {{ depreciation_data.depreciation_rate|floatformat:1 }}%</li>
                                    <li><i class="fas fa-dollar-sign text-warning"></i> {% trans "Total value lost:" %} ${{ depreciation_data.total_depreciation|floatformat:2 }}</li>
                                    <li><i class="fas fa-percentage text-info"></i> {% trans "Average annual depreciation varies by category" %}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0"><i class="fas fa-exclamation-triangle"></i> {% trans "Action Required" %}</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-exchange-alt text-danger"></i> {{ replacement_candidates|length }} {% trans "assets need replacement" %}</li>
                                    <li><i class="fas fa-chart-line-down text-warning"></i> {{ high_depreciation_assets|length }} {% trans "assets highly depreciated" %}</li>
                                    <li><i class="fas fa-eye text-info"></i> {% trans "Review depreciation methods" %}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0"><i class="fas fa-thumbs-up"></i> {% trans "Optimization Tips" %}</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-wrench text-success"></i> {% trans "Regular maintenance extends life" %}</li>
                                    <li><i class="fas fa-recycle text-info"></i> {% trans "Consider refurbishment options" %}</li>
                                    <li><i class="fas fa-calendar text-warning"></i> {% trans "Plan replacements in advance" %}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add tooltips for better user experience
    document.addEventListener('DOMContentLoaded', function() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}