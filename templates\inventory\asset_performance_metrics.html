{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Asset Performance Metrics" %}{% endblock %}

{% block extra_css %}
<style>
    .kpi-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 25px;
        margin-bottom: 20px;
        text-align: center;
        transition: transform 0.3s ease;
    }
    .kpi-card:hover {
        transform: translateY(-5px);
    }
    .kpi-card.availability {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    .kpi-card.reliability {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
    .kpi-card.efficiency {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    .kpi-card.cost {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        color: #333;
    }
    .chart-container {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    .performance-indicator {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        font-weight: bold;
        margin: 0 auto 10px;
    }
    .performance-excellent { background: #28a745; color: white; }
    .performance-good { background: #17a2b8; color: white; }
    .performance-average { background: #ffc107; color: #333; }
    .performance-poor { background: #dc3545; color: white; }
    
    .asset-row {
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
    }
    .asset-row:hover {
        background-color: #f8f9fa;
        transform: translateX(5px);
    }
    .asset-row.excellent { border-left-color: #28a745; }
    .asset-row.good { border-left-color: #17a2b8; }
    .asset-row.average { border-left-color: #ffc107; }
    .asset-row.poor { border-left-color: #dc3545; }
    
    .metric-trend {
        font-size: 12px;
        margin-left: 5px;
    }
    .trend-up { color: #28a745; }
    .trend-down { color: #dc3545; }
    .trend-stable { color: #6c757d; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-chart-line text-primary"></i> {% trans "Asset Performance Metrics" %}</h2>
                <div>
                    <a href="{% url 'inventory:dashboard' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> {% trans "Back to Dashboard" %}
                    </a>
                    <button class="btn btn-success" onclick="exportReport()">
                        <i class="fas fa-download"></i> {% trans "Export Report" %}
                    </button>
                    <button class="btn btn-info" onclick="refreshMetrics()">
                        <i class="fas fa-sync-alt"></i> {% trans "Refresh" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="date_from" class="form-label">{% trans "From Date" %}</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                </div>
                <div class="col-md-3">
                    <label for="date_to" class="form-label">{% trans "To Date" %}</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                </div>
                <div class="col-md-3">
                    <label for="category" class="form-label">{% trans "Category" %}</label>
                    <select class="form-control" id="category" name="category">
                        <option value="">{% trans "All Categories" %}</option>
                        {% for cat in categories %}
                        <option value="{{ cat.id }}" {% if current_category == cat.id|stringformat:'s' %}selected{% endif %}>
                            {{ cat.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter"></i> {% trans "Apply Filter" %}
                        </button>
                        <a href="{% url 'inventory:asset_performance_metrics' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> {% trans "Clear" %}
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Key Performance Indicators -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="kpi-card availability">
                <div class="performance-indicator performance-{{ overall_availability_class }}">
                    {{ overall_availability|floatformat:1 }}%
                </div>
                <h5>{% trans "Overall Availability" %}</h5>
                <p class="mb-0">
                    <span class="metric-trend trend-{{ availability_trend }}">
                        <i class="fas fa-arrow-{{ availability_trend }}"></i> {{ availability_change }}%
                    </span>
                </p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="kpi-card reliability">
                <div class="performance-indicator performance-{{ overall_reliability_class }}">
                    {{ overall_reliability|floatformat:1 }}%
                </div>
                <h5>{% trans "Overall Reliability" %}</h5>
                <p class="mb-0">
                    <span class="metric-trend trend-{{ reliability_trend }}">
                        <i class="fas fa-arrow-{{ reliability_trend }}"></i> {{ reliability_change }}%
                    </span>
                </p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="kpi-card efficiency">
                <div class="performance-indicator performance-{{ overall_efficiency_class }}">
                    {{ overall_efficiency|floatformat:1 }}%
                </div>
                <h5>{% trans "Overall Efficiency" %}</h5>
                <p class="mb-0">
                    <span class="metric-trend trend-{{ efficiency_trend }}">
                        <i class="fas fa-arrow-{{ efficiency_trend }}"></i> {{ efficiency_change }}%
                    </span>
                </p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="kpi-card cost">
                <div class="performance-indicator performance-{{ cost_efficiency_class }}">
                    ${{ avg_maintenance_cost|floatformat:0 }}
                </div>
                <h5>{% trans "Avg Maintenance Cost" %}</h5>
                <p class="mb-0">
                    <span class="metric-trend trend-{{ cost_trend }}">
                        <i class="fas fa-arrow-{{ cost_trend }}"></i> ${{ cost_change }}
                    </span>
                </p>
            </div>
        </div>
    </div>

    <!-- Performance Charts -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="chart-container">
                <h5><i class="fas fa-chart-bar"></i> {% trans "Performance Trends" %}</h5>
                <canvas id="performanceTrendChart" width="400" height="200"></canvas>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="chart-container">
                <h5><i class="fas fa-chart-pie"></i> {% trans "Asset Status Distribution" %}</h5>
                <canvas id="statusDistributionChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="chart-container">
                <h5><i class="fas fa-chart-line"></i> {% trans "Maintenance Cost Trends" %}</h5>
                <canvas id="costTrendChart" width="400" height="200"></canvas>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="chart-container">
                <h5><i class="fas fa-chart-area"></i> {% trans "Utilization Patterns" %}</h5>
                <canvas id="utilizationChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Detailed Asset Performance Table -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-table"></i> {% trans "Detailed Asset Performance" %} ({{ assets|length }} {% trans "assets" %})</h5>
        </div>
        <div class="card-body">
            {% if assets %}
            <div class="table-responsive">
                <table class="table table-hover" id="assetPerformanceTable">
                    <thead class="table-dark">
                        <tr>
                            <th>{% trans "Asset" %}</th>
                            <th>{% trans "Category" %}</th>
                            <th>{% trans "Availability" %}</th>
                            <th>{% trans "Reliability" %}</th>
                            <th>{% trans "Efficiency" %}</th>
                            <th>{% trans "Maintenance Cost" %}</th>
                            <th>{% trans "Performance Score" %}</th>
                            <th>{% trans "Last Maintenance" %}</th>
                            <th>{% trans "Status" %}</th>
                            <th>{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for asset in assets %}
                        <tr class="asset-row {{ asset.performance_class }}">
                            <td>
                                <div>
                                    <strong>{{ asset.name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ asset.asset_tag }}</small>
                                </div>
                            </td>
                            <td>{{ asset.category.name }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="progress me-2" style="width: 60px; height: 8px;">
                                        <div class="progress-bar bg-{% if asset.availability >= 95 %}success{% elif asset.availability >= 85 %}warning{% else %}danger{% endif %}" 
                                             style="width: {{ asset.availability }}%"></div>
                                    </div>
                                    <span>{{ asset.availability|floatformat:1 }}%</span>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="progress me-2" style="width: 60px; height: 8px;">
                                        <div class="progress-bar bg-{% if asset.reliability >= 90 %}success{% elif asset.reliability >= 75 %}warning{% else %}danger{% endif %}" 
                                             style="width: {{ asset.reliability }}%"></div>
                                    </div>
                                    <span>{{ asset.reliability|floatformat:1 }}%</span>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="progress me-2" style="width: 60px; height: 8px;">
                                        <div class="progress-bar bg-{% if asset.efficiency >= 80 %}success{% elif asset.efficiency >= 60 %}warning{% else %}danger{% endif %}" 
                                             style="width: {{ asset.efficiency }}%"></div>
                                    </div>
                                    <span>{{ asset.efficiency|floatformat:1 }}%</span>
                                </div>
                            </td>
                            <td>
                                ${{ asset.maintenance_cost|floatformat:0 }}
                                {% if asset.cost_trend %}
                                <span class="metric-trend trend-{{ asset.cost_trend }}">
                                    <i class="fas fa-arrow-{{ asset.cost_trend }}"></i>
                                </span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-{% if asset.performance_score >= 85 %}success{% elif asset.performance_score >= 70 %}warning{% else %}danger{% endif %} fs-6">
                                    {{ asset.performance_score|floatformat:0 }}
                                </span>
                            </td>
                            <td>
                                {% if asset.last_maintenance %}
                                    {{ asset.last_maintenance|date:"M d, Y" }}
                                    <br>
                                    <small class="text-muted">{{ asset.days_since_maintenance }} {% trans "days ago" %}</small>
                                {% else %}
                                    <span class="text-muted">{% trans "Never" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-{% if asset.status == 'active' %}success{% elif asset.status == 'maintenance' %}warning{% elif asset.status == 'repair' %}danger{% else %}secondary{% endif %}">
                                    {{ asset.get_status_display }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{% url 'inventory:asset_detail' asset.id %}" 
                                       class="btn btn-outline-primary" title="{% trans 'View Details' %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button class="btn btn-outline-info" title="{% trans 'Performance History' %}" 
                                            onclick="showPerformanceHistory({{ asset.id }})">
                                        <i class="fas fa-chart-line"></i>
                                    </button>
                                    <a href="{% url 'inventory:maintenance_schedule' %}?asset={{ asset.id }}" 
                                       class="btn btn-outline-warning" title="{% trans 'Schedule Maintenance' %}">
                                        <i class="fas fa-wrench"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">{% trans "No Performance Data Available" %}</h5>
                <p class="text-muted">{% trans "No assets found for the selected criteria." %}</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Performance Insights -->
    <div class="row mt-4">
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h6><i class="fas fa-trophy"></i> {% trans "Top Performers" %}</h6>
                </div>
                <div class="card-body">
                    {% for asset in top_performers %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <strong>{{ asset.name }}</strong>
                            <br>
                            <small class="text-muted">{{ asset.category.name }}</small>
                        </div>
                        <span class="badge bg-success">{{ asset.performance_score|floatformat:0 }}</span>
                    </div>
                    {% empty %}
                    <p class="text-muted">{% trans "No data available" %}</p>
                    {% endfor %}
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h6><i class="fas fa-exclamation-triangle"></i> {% trans "Needs Attention" %}</h6>
                </div>
                <div class="card-body">
                    {% for asset in needs_attention %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <strong>{{ asset.name }}</strong>
                            <br>
                            <small class="text-muted">{{ asset.issue }}</small>
                        </div>
                        <span class="badge bg-warning">{{ asset.performance_score|floatformat:0 }}</span>
                    </div>
                    {% empty %}
                    <p class="text-muted">{% trans "No issues detected" %}</p>
                    {% endfor %}
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h6><i class="fas fa-dollar-sign"></i> {% trans "Cost Analysis" %}</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "Total Maintenance Cost:" %}</span>
                            <strong>${{ total_maintenance_cost|floatformat:0 }}</strong>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "Cost per Asset:" %}</span>
                            <strong>${{ cost_per_asset|floatformat:0 }}</strong>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "Projected Annual Cost:" %}</span>
                            <strong>${{ projected_annual_cost|floatformat:0 }}</strong>
                        </div>
                    </div>
                    <div>
                        <div class="d-flex justify-content-between">
                            <span>{% trans "Cost Efficiency:" %}</span>
                            <span class="badge bg-{% if cost_efficiency >= 85 %}success{% elif cost_efficiency >= 70 %}warning{% else %}danger{% endif %}">
                                {{ cost_efficiency|floatformat:0 }}%
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Performance History Modal -->
<div class="modal fade" id="performanceHistoryModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Performance History" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <canvas id="historyChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#assetPerformanceTable').DataTable({
        responsive: true,
        pageLength: 25,
        order: [[6, 'desc']], // Sort by performance score
        columnDefs: [
            { orderable: false, targets: [9] } // Actions column
        ]
    });
    
    // Performance Trend Chart
    const trendCtx = document.getElementById('performanceTrendChart').getContext('2d');
    new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: {{ trend_labels|safe }},
            datasets: [{
                label: '{% trans "Availability" %}',
                data: {{ availability_trend_data|safe }},
                borderColor: '#4facfe',
                backgroundColor: 'rgba(79, 172, 254, 0.1)',
                tension: 0.4
            }, {
                label: '{% trans "Reliability" %}',
                data: {{ reliability_trend_data|safe }},
                borderColor: '#43e97b',
                backgroundColor: 'rgba(67, 233, 123, 0.1)',
                tension: 0.4
            }, {
                label: '{% trans "Efficiency" %}',
                data: {{ efficiency_trend_data|safe }},
                borderColor: '#f093fb',
                backgroundColor: 'rgba(240, 147, 251, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // Status Distribution Chart
    const statusCtx = document.getElementById('statusDistributionChart').getContext('2d');
    new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: {{ status_labels|safe }},
            datasets: [{
                data: {{ status_data|safe }},
                backgroundColor: [
                    '#28a745',
                    '#ffc107',
                    '#dc3545',
                    '#6c757d'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // Cost Trend Chart
    const costCtx = document.getElementById('costTrendChart').getContext('2d');
    new Chart(costCtx, {
        type: 'bar',
        data: {
            labels: {{ cost_trend_labels|safe }},
            datasets: [{
                label: '{% trans "Maintenance Cost" %}',
                data: {{ cost_trend_data|safe }},
                backgroundColor: 'rgba(255, 193, 7, 0.8)',
                borderColor: '#ffc107',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
    
    // Utilization Chart
    const utilizationCtx = document.getElementById('utilizationChart').getContext('2d');
    new Chart(utilizationCtx, {
        type: 'area',
        data: {
            labels: {{ utilization_labels|safe }},
            datasets: [{
                label: '{% trans "Average Utilization" %}',
                data: {{ utilization_data|safe }},
                borderColor: '#17a2b8',
                backgroundColor: 'rgba(23, 162, 184, 0.2)',
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
});

function showPerformanceHistory(assetId) {
    $('#performanceHistoryModal').modal('show');
    
    // Load performance history data via AJAX
    $.get(`/inventory/api/asset-performance-history/${assetId}/`, function(data) {
        const ctx = document.getElementById('historyChart').getContext('2d');
        
        // Destroy existing chart if it exists
        if (window.historyChartInstance) {
            window.historyChartInstance.destroy();
        }
        
        window.historyChartInstance = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [{
                    label: '{% trans "Performance Score" %}',
                    data: data.scores,
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }).fail(function() {
        $('#historyChart').parent().html('<p class="text-center text-muted">{% trans "Unable to load performance history" %}</p>');
    });
}

function exportReport() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'pdf');
    window.open(`${window.location.pathname}?${params.toString()}`, '_blank');
}

function refreshMetrics() {
    location.reload();
}

// Auto-refresh every 5 minutes
setInterval(function() {
    if (document.visibilityState === 'visible') {
        location.reload();
    }
}, 300000);
</script>
{% endblock %}