{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Asset Replacement Planning" %}{% endblock %}

{% block extra_css %}
<style>
    .replacement-card {
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
    }
    .replacement-card.critical {
        border-left-color: #dc3545;
        background-color: rgba(220, 53, 69, 0.05);
    }
    .replacement-card.high {
        border-left-color: #fd7e14;
        background-color: rgba(253, 126, 20, 0.05);
    }
    .replacement-card.medium {
        border-left-color: #ffc107;
        background-color: rgba(255, 193, 7, 0.05);
    }
    .replacement-card.low {
        border-left-color: #28a745;
        background-color: rgba(40, 167, 69, 0.05);
    }
    .chart-container {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    .priority-indicator {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 10px;
    }
    .priority-critical { background-color: #dc3545; }
    .priority-high { background-color: #fd7e14; }
    .priority-medium { background-color: #ffc107; }
    .priority-low { background-color: #28a745; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-exchange-alt"></i> {% trans "Asset Replacement Planning" %}</h2>
                <div>
                    <a href="{% url 'inventory:asset_analytics_dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-tachometer-alt"></i> {% trans "Analytics Dashboard" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Budget Summary -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3>${{ total_replacement_cost|floatformat:2 }}</h3>
                            <p class="mb-0">{% trans "Estimated Budget Needed" %}</p>
                            <small>{% trans "Top 10 priority assets" %}</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3>{{ approaching_eol|length }}</h3>
                            <p class="mb-0">{% trans "Assets Near End of Life" %}</p>
                            <small>{% trans "Within 2 years" %}</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-hourglass-end fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3>{{ replacement_priority|length }}</h3>
                            <p class="mb-0">{% trans "High Priority Replacements" %}</p>
                            <small>{% trans "Immediate attention needed" %}</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Assets Approaching End of Life -->
        <div class="col-md-6">
            <div class="chart-container">
                <h5><i class="fas fa-hourglass-end"></i> {% trans "Assets Approaching End of Life" %}</h5>
                <p class="text-muted">{% trans "Assets within 2 years of their expected useful life" %}</p>
                {% if approaching_eol %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Asset" %}</th>
                                    <th>{% trans "Category" %}</th>
                                    <th>{% trans "Remaining Life" %}</th>
                                    <th>{% trans "Est. Cost" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in approaching_eol %}
                                    <tr>
                                        <td>
                                            <strong>{{ item.asset.asset_tag }}</strong><br>
                                            <small class="text-muted">{{ item.asset.name|truncatechars:25 }}</small>
                                        </td>
                                        <td>{{ item.asset.category.name }}</td>
                                        <td>
                                            <span class="badge 
                                                {% if item.remaining_life <= 0.5 %}bg-danger
                                                {% elif item.remaining_life <= 1 %}bg-warning
                                                {% else %}bg-info{% endif %}">
                                                {{ item.remaining_life|floatformat:1 }} {% trans "years" %}
                                            </span>
                                        </td>
                                        <td>${{ item.replacement_cost_estimate|floatformat:2 }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                        <p class="text-muted">{% trans "No assets approaching end of life in the next 2 years." %}</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Replacement Priority Matrix -->
        <div class="col-md-6">
            <div class="chart-container">
                <h5><i class="fas fa-sort-amount-down"></i> {% trans "Replacement Priority Matrix" %}</h5>
                <p class="text-muted">{% trans "Priority based on age, condition, maintenance frequency, and depreciation" %}</p>
                
                <!-- Priority Legend -->
                <div class="mb-3">
                    <div class="d-flex flex-wrap">
                        <span class="me-3"><span class="priority-indicator priority-critical"></span>{% trans "Critical (>90)" %}</span>
                        <span class="me-3"><span class="priority-indicator priority-high"></span>{% trans "High (70-90)" %}</span>
                        <span class="me-3"><span class="priority-indicator priority-medium"></span>{% trans "Medium (50-70)" %}</span>
                        <span class="me-3"><span class="priority-indicator priority-low"></span>{% trans "Low (<50)" %}</span>
                    </div>
                </div>

                {% if replacement_priority %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Asset" %}</th>
                                    <th>{% trans "Priority Score" %}</th>
                                    <th>{% trans "Est. Cost" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in replacement_priority %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="priority-indicator 
                                                    {% if item.priority_score >= 90 %}priority-critical
                                                    {% elif item.priority_score >= 70 %}priority-high
                                                    {% elif item.priority_score >= 50 %}priority-medium
                                                    {% else %}priority-low{% endif %}"></span>
                                                <div>
                                                    <strong>{{ item.asset.asset_tag }}</strong><br>
                                                    <small class="text-muted">{{ item.asset.name|truncatechars:20 }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="progress flex-grow-1 me-2" style="height: 20px; width: 80px;">
                                                    <div class="progress-bar 
                                                        {% if item.priority_score >= 90 %}bg-danger
                                                        {% elif item.priority_score >= 70 %}bg-warning
                                                        {% elif item.priority_score >= 50 %}bg-info
                                                        {% else %}bg-success{% endif %}" 
                                                         role="progressbar" 
                                                         style="width: {{ item.priority_score }}%">
                                                    </div>
                                                </div>
                                                <span class="small">{{ item.priority_score|floatformat:0 }}</span>
                                            </div>
                                        </td>
                                        <td>${{ item.estimated_cost|floatformat:2 }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-thumbs-up fa-2x text-success mb-2"></i>
                        <p class="text-muted">{% trans "No high-priority replacements needed at this time." %}</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Detailed Replacement Cards -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <h5><i class="fas fa-list-alt"></i> {% trans "Detailed Replacement Analysis" %}</h5>
                {% if replacement_priority %}
                    <div class="row">
                        {% for item in replacement_priority %}
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card replacement-card 
                                    {% if item.priority_score >= 90 %}critical
                                    {% elif item.priority_score >= 70 %}high
                                    {% elif item.priority_score >= 50 %}medium
                                    {% else %}low{% endif %}">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title">{{ item.asset.asset_tag }}</h6>
                                            <span class="badge 
                                                {% if item.priority_score >= 90 %}bg-danger
                                                {% elif item.priority_score >= 70 %}bg-warning
                                                {% elif item.priority_score >= 50 %}bg-info
                                                {% else %}bg-success{% endif %}">
                                                {{ item.priority_score|floatformat:0 }}
                                            </span>
                                        </div>
                                        <p class="card-text">{{ item.asset.name|truncatechars:40 }}</p>
                                        
                                        <div class="row mb-2">
                                            <div class="col-6">
                                                <small class="text-muted">{% trans "Category:" %}</small><br>
                                                <span class="small">{{ item.asset.category.name }}</span>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">{% trans "Age:" %}</small><br>
                                                <span class="small">{{ item.asset.age_in_years|floatformat:1 }} {% trans "years" %}</span>
                                            </div>
                                        </div>
                                        
                                        <div class="row mb-2">
                                            <div class="col-6">
                                                <small class="text-muted">{% trans "Condition:" %}</small><br>
                                                <span class="badge bg-{% if item.asset.condition == 'poor' %}danger{% elif item.asset.condition == 'fair' %}warning{% else %}secondary{% endif %} small">
                                                    {{ item.asset.get_condition_display }}
                                                </span>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">{% trans "Location:" %}</small><br>
                                                <span class="small">{{ item.asset.location.name|default:"Unassigned"|truncatechars:15 }}</span>
                                            </div>
                                        </div>
                                        
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <small class="text-muted">{% trans "Current Value:" %}</small><br>
                                                <strong>${{ item.asset.book_value|floatformat:2 }}</strong>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">{% trans "Est. Replacement:" %}</small><br>
                                                <strong>${{ item.estimated_cost|floatformat:2 }}</strong>
                                            </div>
                                        </div>
                                        
                                        <!-- Priority Factors -->
                                        <div class="mb-2">
                                            <small class="text-muted">{% trans "Priority Factors:" %}</small>
                                            <div class="mt-1">
                                                {% if item.asset.age_in_years > item.asset.useful_life_years %}
                                                    <span class="badge bg-danger small me-1">{% trans "Exceeded Life" %}</span>
                                                {% endif %}
                                                {% if item.asset.condition in 'poor,damaged' %}
                                                    <span class="badge bg-warning small me-1">{% trans "Poor Condition" %}</span>
                                                {% endif %}
                                                {% with maintenance_count=item.asset.maintenance_records.count %}
                                                    {% if maintenance_count > 4 %}
                                                        <span class="badge bg-info small me-1">{% trans "High Maintenance" %}</span>
                                                    {% endif %}
                                                {% endwith %}
                                            </div>
                                        </div>
                                        
                                        <div class="d-flex justify-content-between">
                                            <small class="text-muted">
                                                {% if item.priority_score >= 90 %}
                                                    <i class="fas fa-exclamation-triangle text-danger"></i> {% trans "Replace ASAP" %}
                                                {% elif item.priority_score >= 70 %}
                                                    <i class="fas fa-clock text-warning"></i> {% trans "Plan Soon" %}
                                                {% elif item.priority_score >= 50 %}
                                                    <i class="fas fa-calendar text-info"></i> {% trans "Monitor" %}
                                                {% else %}
                                                    <i class="fas fa-check text-success"></i> {% trans "Good Condition" %}
                                                {% endif %}
                                            </small>
                                            <a href="{% url 'inventory:maintenance_history' item.asset.id %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-history"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h5 class="text-success">{% trans "All Assets in Good Condition!" %}</h5>
                        <p class="text-muted">{% trans "No immediate replacement planning required." %}</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Replacement Planning Guidelines -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <h5><i class="fas fa-lightbulb"></i> {% trans "Replacement Planning Guidelines" %}</h5>
                <div class="row">
                    <div class="col-md-3">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h6 class="mb-0"><i class="fas fa-exclamation-triangle"></i> {% trans "Critical Priority" %}</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0 small">
                                    <li><i class="fas fa-check text-danger"></i> {% trans "Exceeded useful life" %}</li>
                                    <li><i class="fas fa-check text-danger"></i> {% trans "Poor/damaged condition" %}</li>
                                    <li><i class="fas fa-check text-danger"></i> {% trans "High maintenance costs" %}</li>
                                    <li><i class="fas fa-check text-danger"></i> {% trans "Safety concerns" %}</li>
                                </ul>
                                <div class="mt-2">
                                    <strong>{% trans "Action:" %}</strong> {% trans "Replace immediately" %}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0"><i class="fas fa-clock"></i> {% trans "High Priority" %}</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0 small">
                                    <li><i class="fas fa-check text-warning"></i> {% trans "Near end of life" %}</li>
                                    <li><i class="fas fa-check text-warning"></i> {% trans "Frequent repairs" %}</li>
                                    <li><i class="fas fa-check text-warning"></i> {% trans "Declining performance" %}</li>
                                    <li><i class="fas fa-check text-warning"></i> {% trans "High depreciation" %}</li>
                                </ul>
                                <div class="mt-2">
                                    <strong>{% trans "Action:" %}</strong> {% trans "Plan within 6 months" %}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0"><i class="fas fa-calendar"></i> {% trans "Medium Priority" %}</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0 small">
                                    <li><i class="fas fa-check text-info"></i> {% trans "Moderate age" %}</li>
                                    <li><i class="fas fa-check text-info"></i> {% trans "Fair condition" %}</li>
                                    <li><i class="fas fa-check text-info"></i> {% trans "Regular maintenance" %}</li>
                                    <li><i class="fas fa-check text-info"></i> {% trans "Budget planning" %}</li>
                                </ul>
                                <div class="mt-2">
                                    <strong>{% trans "Action:" %}</strong> {% trans "Plan within 1-2 years" %}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0"><i class="fas fa-thumbs-up"></i> {% trans "Low Priority" %}</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0 small">
                                    <li><i class="fas fa-check text-success"></i> {% trans "Good condition" %}</li>
                                    <li><i class="fas fa-check text-success"></i> {% trans "Low maintenance" %}</li>
                                    <li><i class="fas fa-check text-success"></i> {% trans "Within useful life" %}</li>
                                    <li><i class="fas fa-check text-success"></i> {% trans "Cost effective" %}</li>
                                </ul>
                                <div class="mt-2">
                                    <strong>{% trans "Action:" %}</strong> {% trans "Monitor regularly" %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add interactive features
    document.addEventListener('DOMContentLoaded', function() {
        // Add tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        
        // Highlight critical priority items
        const criticalCards = document.querySelectorAll('.replacement-card.critical');
        criticalCards.forEach(card => {
            card.style.boxShadow = '0 4px 15px rgba(220, 53, 69, 0.3)';
        });
    });
</script>
{% endblock %}