{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Asset Utilization Report" %}{% endblock %}

{% block extra_css %}
<style>
    .utilization-card {
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
    }
    .utilization-card.high {
        border-left-color: #28a745;
        background-color: rgba(40, 167, 69, 0.05);
    }
    .utilization-card.medium {
        border-left-color: #ffc107;
        background-color: rgba(255, 193, 7, 0.05);
    }
    .utilization-card.low {
        border-left-color: #dc3545;
        background-color: rgba(220, 53, 69, 0.05);
    }
    .chart-container {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-line"></i> {% trans "Asset Utilization Report" %}</h2>
                <div>
                    <a href="{% url 'inventory:asset_analytics_dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-tachometer-alt"></i> {% trans "Analytics Dashboard" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="start_date" class="form-label">{% trans "Start Date" %}</label>
                    <input type="date" name="start_date" id="start_date" class="form-control" 
                           value="{{ start_date|date:'Y-m-d' }}">
                </div>
                <div class="col-md-4">
                    <label for="end_date" class="form-label">{% trans "End Date" %}</label>
                    <input type="date" name="end_date" id="end_date" class="form-control" 
                           value="{{ end_date|date:'Y-m-d' }}">
                </div>
                <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter"></i> {% trans "Apply Filter" %}
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Overall Utilization Metrics -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3>{{ utilization_data.total_assets }}</h3>
                            <p class="mb-0">{% trans "Total Assets" %}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-boxes fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3>{{ utilization_data.assigned_assets }}</h3>
                            <p class="mb-0">{% trans "Assigned Assets" %}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3>{{ utilization_data.utilization_rate }}%</h3>
                            <p class="mb-0">{% trans "Overall Utilization" %}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-percentage fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Utilization by Category -->
        <div class="col-md-6">
            <div class="chart-container">
                <h5><i class="fas fa-chart-bar"></i> {% trans "Utilization by Category" %}</h5>
                {% if utilization_data.by_category %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Category" %}</th>
                                    <th>{% trans "Total" %}</th>
                                    <th>{% trans "Assigned" %}</th>
                                    <th>{% trans "Utilization" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for category in utilization_data.by_category %}
                                    <tr>
                                        <td>{{ category.category__name|default:"Unknown" }}</td>
                                        <td>{{ category.total }}</td>
                                        <td>{{ category.assigned }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="progress flex-grow-1 me-2" style="height: 20px;">
                                                    <div class="progress-bar 
                                                        {% if category.utilization >= 80 %}bg-success
                                                        {% elif category.utilization >= 50 %}bg-warning
                                                        {% else %}bg-danger{% endif %}" 
                                                         role="progressbar" 
                                                         style="width: {{ category.utilization }}%">
                                                    </div>
                                                </div>
                                                <span class="small">{{ category.utilization|floatformat:1 }}%</span>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center py-4">{% trans "No category data available." %}</p>
                {% endif %}
            </div>
        </div>

        <!-- Location Utilization -->
        <div class="col-md-6">
            <div class="chart-container">
                <h5><i class="fas fa-map-marker-alt"></i> {% trans "Location Utilization" %}</h5>
                {% if location_utilization %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Location" %}</th>
                                    <th>{% trans "Assets" %}</th>
                                    <th>{% trans "Assigned" %}</th>
                                    <th>{% trans "Rate" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for location in location_utilization %}
                                    <tr>
                                        <td>{{ location.location.name|default:"Unassigned" }}</td>
                                        <td>{{ location.total_assets }}</td>
                                        <td>{{ location.assigned_assets }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="progress flex-grow-1 me-2" style="height: 20px;">
                                                    <div class="progress-bar 
                                                        {% if location.utilization_rate >= 80 %}bg-success
                                                        {% elif location.utilization_rate >= 50 %}bg-warning
                                                        {% else %}bg-danger{% endif %}" 
                                                         role="progressbar" 
                                                         style="width: {{ location.utilization_rate }}%">
                                                    </div>
                                                </div>
                                                <span class="small">{{ location.utilization_rate|floatformat:1 }}%</span>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center py-4">{% trans "No location data available." %}</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Underutilized Assets -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <h5><i class="fas fa-exclamation-triangle"></i> {% trans "Underutilized Assets" %}</h5>
                {% if underutilized_assets %}
                    <div class="row">
                        {% for asset in underutilized_assets %}
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card utilization-card low">
                                    <div class="card-body">
                                        <h6 class="card-title">{{ asset.asset_tag }}</h6>
                                        <p class="card-text">{{ asset.name }}</p>
                                        <div class="mb-2">
                                            <small class="text-muted">
                                                <i class="fas fa-tag"></i> {{ asset.category.name }}
                                            </small>
                                        </div>
                                        <div class="mb-2">
                                            <small class="text-muted">
                                                <i class="fas fa-map-marker-alt"></i> {{ asset.location|default:"No location" }}
                                            </small>
                                        </div>
                                        <div class="mb-2">
                                            <small class="text-muted">
                                                <i class="fas fa-calendar"></i> {% trans "Age:" %} {{ asset.age_in_years|floatformat:1 }} {% trans "years" %}
                                            </small>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <span class="badge bg-danger">{% trans "Unassigned" %}</span>
                                            <small class="text-muted">${{ asset.purchase_price|floatformat:2 }}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h5 class="text-success">{% trans "All Assets Well Utilized!" %}</h5>
                        <p class="text-muted">{% trans "No underutilized assets found." %}</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Recommendations -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <h5><i class="fas fa-lightbulb"></i> {% trans "Utilization Recommendations" %}</h5>
                <div class="row">
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0"><i class="fas fa-arrow-up"></i> {% trans "Improve Utilization" %}</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success"></i> {% trans "Assign unassigned assets" %}</li>
                                    <li><i class="fas fa-check text-success"></i> {% trans "Redistribute assets between locations" %}</li>
                                    <li><i class="fas fa-check text-success"></i> {% trans "Consider asset sharing programs" %}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0"><i class="fas fa-eye"></i> {% trans "Monitor Closely" %}</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-exclamation-triangle text-warning"></i> {% trans "Assets with <50% utilization" %}</li>
                                    <li><i class="fas fa-exclamation-triangle text-warning"></i> {% trans "High-value underutilized assets" %}</li>
                                    <li><i class="fas fa-exclamation-triangle text-warning"></i> {% trans "Location capacity issues" %}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0"><i class="fas fa-chart-line"></i> {% trans "Optimize" %}</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-sync text-info"></i> {% trans "Regular utilization reviews" %}</li>
                                    <li><i class="fas fa-sync text-info"></i> {% trans "Asset rotation schedules" %}</li>
                                    <li><i class="fas fa-sync text-info"></i> {% trans "Usage pattern analysis" %}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Set default date range to current month
    document.addEventListener('DOMContentLoaded', function() {
        const startDateInput = document.getElementById('start_date');
        const endDateInput = document.getElementById('end_date');
        
        if (!startDateInput.value) {
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            startDateInput.value = firstDay.toISOString().split('T')[0];
        }
        
        if (!endDateInput.value) {
            const today = new Date();
            endDateInput.value = today.toISOString().split('T')[0];
        }
    });
</script>
{% endblock %}