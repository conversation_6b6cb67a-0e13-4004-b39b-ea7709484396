{% extends 'base.html' %}
{% load static %}

{% block title %}Inventory Dashboard{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">Inventory Dashboard</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'accounts:dashboard' %}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Inventory</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate" title="Total Items">Total Items</h5>
                            <h3 class="my-2 py-1">{{ total_items }}</h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <div id="total-items-chart" data-colors="#0acf97"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate" title="Low Stock">Low Stock</h5>
                            <h3 class="my-2 py-1 text-warning">{{ low_stock_items }}</h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <div id="low-stock-chart" data-colors="#ffbc00"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate" title="Out of Stock">Out of Stock</h5>
                            <h3 class="my-2 py-1 text-danger">{{ out_of_stock_items }}</h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <div id="out-stock-chart" data-colors="#fa5c7c"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate" title="Stock Value">Stock Value</h5>
                            <h3 class="my-2 py-1">${{ stock_valuation.total_value|floatformat:2 }}</h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <div id="stock-value-chart" data-colors="#6c757d"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Transactions -->
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">Recent Transactions</h4>
                    <div class="dropdown">
                        <a href="#" class="dropdown-toggle arrow-none card-drop" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="mdi mdi-dots-vertical"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end">
                            <a href="{% url 'inventory:transaction_list' %}" class="dropdown-item">View All</a>
                        </div>
                    </div>
                </div>
                <div class="card-body pt-0">
                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap table-hover mb-0">
                            <tbody>
                                {% for transaction in recent_transactions %}
                                <tr>
                                    <td>
                                        <h5 class="font-14 my-1">{{ transaction.item.name }}</h5>
                                        <span class="text-muted font-13">{{ transaction.item.item_code }}</span>
                                    </td>
                                    <td>
                                        <span class="badge badge-{{ transaction.transaction_type|yesno:'success,danger' }}-lighten">
                                            {{ transaction.get_transaction_type_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="text-muted font-13">{{ transaction.quantity }}</span>
                                    </td>
                                    <td>
                                        <span class="text-muted font-13">{{ transaction.transaction_date|date:"M d, Y" }}</span>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center text-muted">No recent transactions</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Alerts -->
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">Active Alerts</h4>
                    <div class="dropdown">
                        <a href="#" class="dropdown-toggle arrow-none card-drop" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="mdi mdi-dots-vertical"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end">
                            <a href="{% url 'inventory:alert_list' %}" class="dropdown-item">View All</a>
                        </div>
                    </div>
                </div>
                <div class="card-body pt-0">
                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap table-hover mb-0">
                            <tbody>
                                {% for alert in active_alerts %}
                                <tr>
                                    <td>
                                        <h5 class="font-14 my-1">{{ alert.item.name }}</h5>
                                        <span class="text-muted font-13">{{ alert.item.item_code }}</span>
                                    </td>
                                    <td>
                                        <span class="badge badge-{{ alert.alert_type|yesno:'warning,danger' }}-lighten">
                                            {{ alert.get_alert_type_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="text-muted font-13">{{ alert.alert_date|date:"M d, Y" }}</span>
                                    </td>
                                    <td>
                                        <a href="{% url 'inventory:alert_resolve' alert.id %}" class="btn btn-xs btn-light">
                                            <i class="mdi mdi-check"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center text-muted">No active alerts</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">Quick Actions</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2">
                            <a href="{% url 'inventory:item_create' %}" class="btn btn-primary btn-block mb-2">
                                <i class="mdi mdi-plus-circle me-1"></i> Add New Item
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{% url 'inventory:stock_transfer' %}" class="btn btn-info btn-block mb-2">
                                <i class="mdi mdi-swap-horizontal me-1"></i> Transfer Stock
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{% url 'inventory:purchase_order_create' %}" class="btn btn-success btn-block mb-2">
                                <i class="mdi mdi-cart-plus me-1"></i> Create Purchase Order
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{% url 'inventory:maintenance_dashboard' %}" class="btn btn-warning btn-block mb-2">
                                <i class="fas fa-tools me-1"></i> Maintenance
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{% url 'inventory:work_order_list' %}" class="btn btn-dark btn-block mb-2">
                                <i class="fas fa-clipboard-list me-1"></i> Work Orders
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{% url 'inventory:asset_analytics_dashboard' %}" class="btn btn-info btn-block mb-2">
                                <i class="fas fa-chart-bar me-1"></i> Asset Analytics
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{% url 'inventory:reports' %}" class="btn btn-secondary btn-block mb-2">
                                <i class="mdi mdi-chart-line me-1"></i> View Reports
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add any dashboard-specific JavaScript here
    $(document).ready(function() {
        // Initialize dashboard widgets
    });
</script>
{% endblock %}