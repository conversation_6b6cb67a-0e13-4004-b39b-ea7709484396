{% extends 'base.html' %}
{% load static %}

{% block title %}{{ item.name }} - Item Details{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">{{ item.name }}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'accounts:dashboard' %}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">Inventory</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'inventory:item_list' %}">Items</a></li>
                        <li class="breadcrumb-item active">{{ item.item_code }}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Item Information -->
        <div class="col-xl-8">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h4 class="header-title">Item Information</h4>
                        </div>
                        <div class="col-auto">
                            <div class="btn-group">
                                <a href="{% url 'inventory:item_edit' item.id %}" class="btn btn-primary">
                                    <i class="mdi mdi-pencil me-1"></i> Edit
                                </a>
                                <a href="{% url 'inventory:stock_adjustment' item.id %}" class="btn btn-info">
                                    <i class="mdi mdi-plus-minus me-1"></i> Adjust Stock
                                </a>
                                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                                    <i class="mdi mdi-delete me-1"></i> Delete
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-4">
                            {% if item.image %}
                                <img src="{{ item.image.url }}" alt="{{ item.name }}" 
                                     class="img-fluid rounded">
                            {% else %}
                                <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                     style="height: 200px;">
                                    <i class="mdi mdi-image-outline text-muted" style="font-size: 48px;"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-lg-8">
                            <table class="table table-borderless mb-0">
                                <tbody>
                                    <tr>
                                        <td class="fw-bold" width="150">Item Code:</td>
                                        <td>{{ item.item_code }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Name:</td>
                                        <td>{{ item.name }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Description:</td>
                                        <td>{{ item.description|default:"-" }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Category:</td>
                                        <td>
                                            {% if item.category %}
                                                <span class="badge badge-soft-primary">{{ item.category.name }}</span>
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Primary Location:</td>
                                        <td>{{ item.primary_location.name|default:"-" }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Primary Supplier:</td>
                                        <td>{{ item.primary_supplier.name|default:"-" }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Unit of Measure:</td>
                                        <td>{{ item.unit_of_measure|default:"-" }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Barcode:</td>
                                        <td>{{ item.barcode|default:"-" }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Status:</td>
                                        <td>
                                            <span class="badge badge-{% if item.status == 'active' %}success{% else %}secondary{% endif %}-lighten">
                                                {{ item.get_status_display }}
                                            </span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    {% if item.notes %}
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>Notes:</h6>
                            <p class="text-muted">{{ item.notes }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h4 class="header-title">Recent Transactions</h4>
                        </div>
                        <div class="col-auto">
                            <a href="{% url 'inventory:transaction_list' %}?item={{ item.id }}" class="btn btn-sm btn-outline-primary">
                                View All
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Date</th>
                                    <th>Type</th>
                                    <th>Quantity</th>
                                    <th>From</th>
                                    <th>To</th>
                                    <th>Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in transactions %}
                                <tr>
                                    <td>{{ transaction.transaction_date|date:"M d, Y H:i" }}</td>
                                    <td>
                                        <span class="badge badge-{% if transaction.transaction_type == 'in' %}success{% elif transaction.transaction_type == 'out' %}danger{% else %}info{% endif %}-lighten">
                                            {{ transaction.get_transaction_type_display }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if transaction.transaction_type == 'out' %}-{% endif %}{{ transaction.quantity }}
                                    </td>
                                    <td>{{ transaction.from_location.name|default:"-" }}</td>
                                    <td>{{ transaction.to_location.name|default:"-" }}</td>
                                    <td>{{ transaction.notes|default:"-"|truncatechars:50 }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center text-muted py-4">
                                        No transactions found for this item.
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stock Information Sidebar -->
        <div class="col-xl-4">
            <!-- Stock Levels -->
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">Stock Information</h4>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h3 class="{% if item.current_stock <= item.minimum_stock %}text-danger{% else %}text-success{% endif %}">
                                {{ item.current_stock }}
                            </h3>
                            <p class="text-muted mb-0">Current Stock</p>
                        </div>
                        <div class="col-6">
                            <h3 class="text-warning">{{ item.minimum_stock }}</h3>
                            <p class="text-muted mb-0">Minimum Level</p>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <div class="progress" style="height: 8px;">
                            {% widthratio item.current_stock item.maximum_stock 100 as stock_percentage %}
                            <div class="progress-bar {% if stock_percentage <= 25 %}bg-danger{% elif stock_percentage <= 50 %}bg-warning{% else %}bg-success{% endif %}" 
                                 style="width: {{ stock_percentage }}%"></div>
                        </div>
                        <div class="d-flex justify-content-between mt-1">
                            <small class="text-muted">0</small>
                            <small class="text-muted">{{ item.maximum_stock }}</small>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12">
                            <table class="table table-sm table-borderless mb-0">
                                <tbody>
                                    <tr>
                                        <td>Maximum Stock:</td>
                                        <td class="text-end">{{ item.maximum_stock }}</td>
                                    </tr>
                                    <tr>
                                        <td>Unit Cost:</td>
                                        <td class="text-end">${{ item.unit_cost|floatformat:2 }}</td>
                                    </tr>
                                    <tr class="table-light">
                                        <td class="fw-bold">Total Value:</td>
                                        <td class="text-end fw-bold">
                                            ${% widthratio item.current_stock 1 item.unit_cost as total_value %}{{ total_value|floatformat:2 }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Alerts -->
            {% if alerts %}
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">Active Alerts</h4>
                </div>
                <div class="card-body">
                    {% for alert in alerts %}
                    <div class="alert alert-{% if alert.alert_type == 'low_stock' %}warning{% else %}danger{% endif %} alert-dismissible fade show" role="alert">
                        <strong>{{ alert.get_alert_type_display }}</strong><br>
                        {{ alert.message }}
                        <small class="d-block mt-1 text-muted">{{ alert.alert_date|date:"M d, Y H:i" }}</small>
                        <a href="{% url 'inventory:alert_resolve' alert.id %}" class="btn btn-sm btn-outline-dark mt-2">
                            Resolve
                        </a>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">Quick Actions</h4>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'inventory:stock_adjustment' item.id %}" class="btn btn-primary">
                            <i class="mdi mdi-plus-minus me-1"></i> Adjust Stock
                        </a>
                        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#transferModal">
                            <i class="mdi mdi-swap-horizontal me-1"></i> Transfer Stock
                        </button>
                        <a href="{% url 'inventory:item_edit' item.id %}" class="btn btn-secondary">
                            <i class="mdi mdi-pencil me-1"></i> Edit Item
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Confirm Delete</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong>{{ item.name }}</strong>?</p>
                <p class="text-muted">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="{% url 'inventory:item_delete' item.id %}" class="btn btn-danger">Delete</a>
            </div>
        </div>
    </div>
</div>

<!-- Transfer Stock Modal -->
<div class="modal fade" id="transferModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="{% url 'inventory:stock_transfer' %}">
                {% csrf_token %}
                <input type="hidden" name="item" value="{{ item.id }}">
                <div class="modal-header">
                    <h4 class="modal-title">Transfer Stock</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">From Location</label>
                        <select name="from_location" class="form-select" required>
                            <option value="">Select location...</option>
                            <!-- Locations will be populated via JavaScript -->
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">To Location</label>
                        <select name="to_location" class="form-select" required>
                            <option value="">Select location...</option>
                            <!-- Locations will be populated via JavaScript -->
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Quantity</label>
                        <input type="number" name="quantity" class="form-control" min="1" max="{{ item.current_stock }}" required>
                        <div class="form-text">Available: {{ item.current_stock }}</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Notes</label>
                        <textarea name="notes" class="form-control" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Transfer</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Load locations for transfer modal
    $('#transferModal').on('show.bs.modal', function() {
        // You can load locations via AJAX here if needed
    });
});
</script>
{% endblock %}