{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">{{ title }}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'accounts:dashboard' %}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">Inventory</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'inventory:item_list' %}">Items</a></li>
                        <li class="breadcrumb-item active">{{ title }}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">{{ title }}</h4>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}

                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-lg-6">
                                <h5 class="mb-3">Basic Information</h5>
                                
                                <div class="mb-3">
                                    <label for="{{ form.item_code.id_for_label }}" class="form-label">
                                        Item Code <span class="text-danger">*</span>
                                    </label>
                                    {{ form.item_code }}
                                    {% if form.item_code.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.item_code.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label">
                                        Name <span class="text-danger">*</span>
                                    </label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.name.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                                    {{ form.description }}
                                    {% if form.description.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.description.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.category.id_for_label }}" class="form-label">Category</label>
                                    {{ form.category }}
                                    {% if form.category.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.category.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.primary_location.id_for_label }}" class="form-label">Primary Location</label>
                                    {{ form.primary_location }}
                                    {% if form.primary_location.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.primary_location.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Stock and Financial Information -->
                            <div class="col-lg-6">
                                <h5 class="mb-3">Stock & Financial Information</h5>
                                
                                <div class="mb-3">
                                    <label for="{{ form.current_stock.id_for_label }}" class="form-label">Current Stock</label>
                                    {{ form.current_stock }}
                                    {% if form.current_stock.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.current_stock.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.minimum_stock.id_for_label }}" class="form-label">Minimum Stock Level</label>
                                    {{ form.minimum_stock }}
                                    {% if form.minimum_stock.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.minimum_stock.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.maximum_stock.id_for_label }}" class="form-label">Maximum Stock Level</label>
                                    {{ form.maximum_stock }}
                                    {% if form.maximum_stock.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.maximum_stock.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.unit_cost.id_for_label }}" class="form-label">Unit Cost</label>
                                    {{ form.unit_cost }}
                                    {% if form.unit_cost.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.unit_cost.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.unit_of_measure.id_for_label }}" class="form-label">Unit of Measure</label>
                                    {{ form.unit_of_measure }}
                                    {% if form.unit_of_measure.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.unit_of_measure.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Additional Information -->
                            <div class="col-lg-6">
                                <h5 class="mb-3">Additional Information</h5>
                                
                                <div class="mb-3">
                                    <label for="{{ form.primary_supplier.id_for_label }}" class="form-label">Primary Supplier</label>
                                    {{ form.primary_supplier }}
                                    {% if form.primary_supplier.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.primary_supplier.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.barcode.id_for_label }}" class="form-label">Barcode</label>
                                    {{ form.barcode }}
                                    {% if form.barcode.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.barcode.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.status.id_for_label }}" class="form-label">Status</label>
                                    {{ form.status }}
                                    {% if form.status.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.status.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Image Upload -->
                            <div class="col-lg-6">
                                <h5 class="mb-3">Item Image</h5>
                                
                                {% if item and item.image %}
                                <div class="mb-3">
                                    <img src="{{ item.image.url }}" alt="{{ item.name }}" 
                                         class="img-thumbnail" style="max-width: 200px;">
                                </div>
                                {% endif %}

                                <div class="mb-3">
                                    <label for="{{ form.image.id_for_label }}" class="form-label">Upload Image</label>
                                    {{ form.image }}
                                    {% if form.image.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.image.errors.0 }}
                                        </div>
                                    {% endif %}
                                    <div class="form-text">
                                        Supported formats: JPG, PNG, GIF. Max size: 5MB.
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.notes.id_for_label }}" class="form-label">Notes</label>
                                    {{ form.notes }}
                                    {% if form.notes.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.notes.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="text-end">
                                    <a href="{% url 'inventory:item_list' %}" class="btn btn-secondary me-2">
                                        <i class="mdi mdi-arrow-left me-1"></i> Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="mdi mdi-content-save me-1"></i> Save Item
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-generate item code if empty
    $('#id_name').on('blur', function() {
        var name = $(this).val();
        var itemCode = $('#id_item_code').val();
        
        if (name && !itemCode) {
            // Generate a simple code from the name
            var code = name.toUpperCase().replace(/[^A-Z0-9]/g, '').substring(0, 10);
            if (code.length < 3) {
                code = code + '001';
            }
            $('#id_item_code').val(code);
        }
    });
    
    // Image preview
    $('#id_image').change(function() {
        var file = this.files[0];
        if (file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                var preview = '<div class="mb-3"><img src="' + e.target.result + '" alt="Preview" class="img-thumbnail" style="max-width: 200px;"></div>';
                $('#id_image').parent().prepend(preview);
            };
            reader.readAsDataURL(file);
        }
    });
});
</script>
{% endblock %}