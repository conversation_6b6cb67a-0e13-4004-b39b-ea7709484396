{% extends 'base.html' %}
{% load static %}

{% block title %}Inventory Items{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">Inventory Items</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'accounts:dashboard' %}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">Inventory</a></li>
                        <li class="breadcrumb-item active">Items</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h4 class="header-title">All Items</h4>
                        </div>
                        <div class="col-auto">
                            <a href="{% url 'inventory:item_create' %}" class="btn btn-primary">
                                <i class="mdi mdi-plus-circle me-1"></i> Add New Item
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Search and Filter Form -->
                    <form method="get" class="mb-3">
                        <div class="row g-2">
                            <div class="col-md-4">
                                {{ search_form.query }}
                            </div>
                            <div class="col-md-2">
                                {{ search_form.category }}
                            </div>
                            <div class="col-md-2">
                                {{ search_form.location }}
                            </div>
                            <div class="col-md-2">
                                {{ search_form.status }}
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="mdi mdi-magnify"></i> Search
                                </button>
                                <a href="{% url 'inventory:item_list' %}" class="btn btn-secondary">
                                    <i class="mdi mdi-refresh"></i>
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Bulk Actions -->
                    <form method="post" action="{% url 'inventory:bulk_action' %}" id="bulk-form">
                        {% csrf_token %}
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <select name="action" class="form-select" id="bulk-action">
                                    <option value="">Select Action...</option>
                                    <option value="activate">Activate Selected</option>
                                    <option value="deactivate">Deactivate Selected</option>
                                    <option value="delete">Delete Selected</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-warning" id="bulk-submit" disabled>
                                    Apply Action
                                </button>
                            </div>
                        </div>

                        <!-- Items Table -->
                        <div class="table-responsive">
                            <table class="table table-centered table-nowrap table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" id="select-all">
                                                <label class="form-check-label" for="select-all"></label>
                                            </div>
                                        </th>
                                        <th>Item Code</th>
                                        <th>Name</th>
                                        <th>Category</th>
                                        <th>Location</th>
                                        <th>Current Stock</th>
                                        <th>Min Stock</th>
                                        <th>Unit Cost</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for item in page_obj %}
                                    <tr>
                                        <td>
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input item-checkbox" 
                                                       name="items" value="{{ item.id }}">
                                                <label class="form-check-label"></label>
                                            </div>
                                        </td>
                                        <td>
                                            <a href="{% url 'inventory:item_detail' item.id %}" class="text-body fw-bold">
                                                {{ item.item_code }}
                                            </a>
                                        </td>
                                        <td>{{ item.name }}</td>
                                        <td>
                                            {% if item.category %}
                                                <span class="badge badge-soft-primary">{{ item.category.name }}</span>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if item.primary_location %}
                                                {{ item.primary_location.name }}
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="{% if item.current_stock <= item.minimum_stock %}text-danger{% endif %}">
                                                {{ item.current_stock }}
                                            </span>
                                        </td>
                                        <td>{{ item.minimum_stock }}</td>
                                        <td>${{ item.unit_cost|floatformat:2 }}</td>
                                        <td>
                                            <span class="badge badge-{% if item.status == 'active' %}success{% else %}secondary{% endif %}-lighten">
                                                {{ item.get_status_display }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{% url 'inventory:item_detail' item.id %}" 
                                                   class="btn btn-xs btn-light" title="View">
                                                    <i class="mdi mdi-eye"></i>
                                                </a>
                                                <a href="{% url 'inventory:item_edit' item.id %}" 
                                                   class="btn btn-xs btn-light" title="Edit">
                                                    <i class="mdi mdi-pencil"></i>
                                                </a>
                                                <a href="{% url 'inventory:stock_adjustment' item.id %}" 
                                                   class="btn btn-xs btn-light" title="Adjust Stock">
                                                    <i class="mdi mdi-plus-minus"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="10" class="text-center text-muted py-4">
                                            No inventory items found.
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </form>

                    <!-- Pagination -->
                    {% if page_obj.has_other_pages %}
                    <div class="row mt-3">
                        <div class="col-sm-12 col-md-5">
                            <div class="dataTables_info">
                                Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} 
                                of {{ page_obj.paginator.count }} entries
                            </div>
                        </div>
                        <div class="col-sm-12 col-md-7">
                            <div class="dataTables_paginate paging_simple_numbers">
                                <ul class="pagination pagination-rounded justify-content-end">
                                    {% if page_obj.has_previous %}
                                        <li class="paginate_button page-item previous">
                                            <a href="?page={{ page_obj.previous_page_number }}" class="page-link">Previous</a>
                                        </li>
                                    {% endif %}
                                    
                                    {% for num in page_obj.paginator.page_range %}
                                        {% if page_obj.number == num %}
                                            <li class="paginate_button page-item active">
                                                <span class="page-link">{{ num }}</span>
                                            </li>
                                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                            <li class="paginate_button page-item">
                                                <a href="?page={{ num }}" class="page-link">{{ num }}</a>
                                            </li>
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if page_obj.has_next %}
                                        <li class="paginate_button page-item next">
                                            <a href="?page={{ page_obj.next_page_number }}" class="page-link">Next</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Select all checkbox functionality
    $('#select-all').change(function() {
        $('.item-checkbox').prop('checked', this.checked);
        toggleBulkActions();
    });
    
    // Individual checkbox change
    $('.item-checkbox').change(function() {
        toggleBulkActions();
    });
    
    // Toggle bulk actions based on selection
    function toggleBulkActions() {
        var checkedCount = $('.item-checkbox:checked').length;
        $('#bulk-submit').prop('disabled', checkedCount === 0);
    }
    
    // Bulk form submission
    $('#bulk-form').submit(function(e) {
        var action = $('#bulk-action').val();
        var checkedCount = $('.item-checkbox:checked').length;
        
        if (!action) {
            e.preventDefault();
            alert('Please select an action.');
            return false;
        }
        
        if (checkedCount === 0) {
            e.preventDefault();
            alert('Please select at least one item.');
            return false;
        }
        
        if (action === 'delete') {
            if (!confirm('Are you sure you want to delete the selected items? This action cannot be undone.')) {
                e.preventDefault();
                return false;
            }
        }
    });
});
</script>
{% endblock %}