{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Maintenance Analytics" %}{% endblock %}

{% block extra_css %}
<style>
    .analytics-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .chart-container {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    .metric-card {
        text-align: center;
        padding: 20px;
        border-radius: 10px;
        background: #f8f9fa;
        border-left: 4px solid #007bff;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-bar"></i> {% trans "Maintenance Analytics" %}</h2>
                <div>
                    <a href="{% url 'inventory:maintenance_dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-tachometer-alt"></i> {% trans "Dashboard" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="start_date" class="form-label">{% trans "Start Date" %}</label>
                    <input type="date" name="start_date" id="start_date" class="form-control" 
                           value="{{ start_date|date:'Y-m-d' }}">
                </div>
                <div class="col-md-4">
                    <label for="end_date" class="form-label">{% trans "End Date" %}</label>
                    <input type="date" name="end_date" id="end_date" class="form-control" 
                           value="{{ end_date|date:'Y-m-d' }}">
                </div>
                <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter"></i> {% trans "Apply Filter" %}
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="row">
        <div class="col-md-3">
            <div class="analytics-card">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>${{ maintenance_costs.total_cost|floatformat:2 }}</h3>
                        <p class="mb-0">{% trans "Total Cost" %}</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="analytics-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ maintenance_costs.record_count }}</h3>
                        <p class="mb-0">{% trans "Total Records" %}</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clipboard-list fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="analytics-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>
                            {% if maintenance_costs.record_count > 0 %}
                                ${{ maintenance_costs.total_cost|floatformat:2|div:maintenance_costs.record_count }}
                            {% else %}
                                $0.00
                            {% endif %}
                        </h3>
                        <p class="mb-0">{% trans "Avg Cost" %}</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calculator fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="analytics-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>
                            {% for type_stat in maintenance_type_stats %}
                                {% if type_stat.maintenance_type == 'preventive' %}
                                    {% widthratio type_stat.count maintenance_costs.record_count 100 %}%
                                    {% break %}
                                {% endif %}
                            {% empty %}
                                0%
                            {% endfor %}
                        </h3>
                        <p class="mb-0">{% trans "Preventive %" %}</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-shield-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Maintenance by Status -->
        <div class="col-md-6">
            <div class="chart-container">
                <h5><i class="fas fa-chart-pie"></i> {% trans "Maintenance by Status" %}</h5>
                {% if maintenance_by_status %}
                    <canvas id="statusChart" width="400" height="200"></canvas>
                {% else %}
                    <p class="text-muted text-center py-4">{% trans "No data available for the selected period." %}</p>
                {% endif %}
            </div>
        </div>

        <!-- Maintenance by Type -->
        <div class="col-md-6">
            <div class="chart-container">
                <h5><i class="fas fa-chart-doughnut"></i> {% trans "Maintenance by Type" %}</h5>
                {% if maintenance_type_stats %}
                    <canvas id="typeChart" width="400" height="200"></canvas>
                {% else %}
                    <p class="text-muted text-center py-4">{% trans "No data available for the selected period." %}</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Cost Trends -->
        <div class="col-md-8">
            <div class="chart-container">
                <h5><i class="fas fa-chart-line"></i> {% trans "Maintenance Cost Trends" %}</h5>
                {% if maintenance_trends %}
                    <canvas id="trendsChart" width="400" height="200"></canvas>
                {% else %}
                    <p class="text-muted text-center py-4">{% trans "No trend data available for the selected period." %}</p>
                {% endif %}
            </div>
        </div>

        <!-- Top Assets by Cost -->
        <div class="col-md-4">
            <div class="chart-container">
                <h5><i class="fas fa-trophy"></i> {% trans "Top Assets by Cost" %}</h5>
                {% if top_assets_by_cost %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>{% trans "Asset" %}</th>
                                    <th>{% trans "Cost" %}</th>
                                    <th>{% trans "Count" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for asset in top_assets_by_cost %}
                                    <tr>
                                        <td>
                                            <strong>{{ asset.asset__asset_tag }}</strong><br>
                                            <small class="text-muted">{{ asset.asset__name|truncatechars:20 }}</small>
                                        </td>
                                        <td>${{ asset.total_cost|floatformat:2 }}</td>
                                        <td>{{ asset.maintenance_count }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center py-4">{% trans "No asset data available." %}</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Detailed Breakdown -->
    <div class="row">
        <div class="col-md-6">
            <div class="chart-container">
                <h5><i class="fas fa-list"></i> {% trans "Cost by Type" %}</h5>
                {% if maintenance_costs.by_type %}
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>{% trans "Type" %}</th>
                                    <th>{% trans "Count" %}</th>
                                    <th>{% trans "Total Cost" %}</th>
                                    <th>{% trans "Avg Cost" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in maintenance_costs.by_type %}
                                    <tr>
                                        <td>
                                            <span class="badge bg-secondary">
                                                {{ item.maintenance_type|capfirst }}
                                            </span>
                                        </td>
                                        <td>{{ item.count }}</td>
                                        <td>${{ item.total_cost|floatformat:2 }}</td>
                                        <td>${{ item.total_cost|floatformat:2|div:item.count }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center py-4">{% trans "No type breakdown available." %}</p>
                {% endif %}
            </div>
        </div>

        <div class="col-md-6">
            <div class="chart-container">
                <h5><i class="fas fa-tags"></i> {% trans "Cost by Category" %}</h5>
                {% if maintenance_costs.by_category %}
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>{% trans "Category" %}</th>
                                    <th>{% trans "Count" %}</th>
                                    <th>{% trans "Total Cost" %}</th>
                                    <th>{% trans "Avg Cost" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in maintenance_costs.by_category %}
                                    <tr>
                                        <td>{{ item.asset__category__name|default:"Unknown" }}</td>
                                        <td>{{ item.count }}</td>
                                        <td>${{ item.total_cost|floatformat:2 }}</td>
                                        <td>${{ item.total_cost|floatformat:2|div:item.count }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center py-4">{% trans "No category breakdown available." %}</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Status Chart
    {% if maintenance_by_status %}
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        new Chart(statusCtx, {
            type: 'pie',
            data: {
                labels: [
                    {% for item in maintenance_by_status %}
                        '{{ item.status|capfirst }}'{% if not forloop.last %},{% endif %}
                    {% endfor %}
                ],
                datasets: [{
                    data: [
                        {% for item in maintenance_by_status %}
                            {{ item.count }}{% if not forloop.last %},{% endif %}
                        {% endfor %}
                    ],
                    backgroundColor: [
                        '#28a745', '#ffc107', '#17a2b8', '#dc3545', '#6c757d'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    {% endif %}

    // Type Chart
    {% if maintenance_type_stats %}
        const typeCtx = document.getElementById('typeChart').getContext('2d');
        new Chart(typeCtx, {
            type: 'doughnut',
            data: {
                labels: [
                    {% for item in maintenance_type_stats %}
                        '{{ item.maintenance_type|capfirst }}'{% if not forloop.last %},{% endif %}
                    {% endfor %}
                ],
                datasets: [{
                    data: [
                        {% for item in maintenance_type_stats %}
                            {{ item.count }}{% if not forloop.last %},{% endif %}
                        {% endfor %}
                    ],
                    backgroundColor: [
                        '#007bff', '#28a745', '#dc3545', '#ffc107', '#17a2b8'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    {% endif %}

    // Trends Chart
    {% if maintenance_trends %}
        const trendsCtx = document.getElementById('trendsChart').getContext('2d');
        new Chart(trendsCtx, {
            type: 'line',
            data: {
                labels: [
                    {% for item in maintenance_trends %}
                        '{{ item.month }}'{% if not forloop.last %},{% endif %}
                    {% endfor %}
                ],
                datasets: [{
                    label: '{% trans "Maintenance Count" %}',
                    data: [
                        {% for item in maintenance_trends %}
                            {{ item.count }}{% if not forloop.last %},{% endif %}
                        {% endfor %}
                    ],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    yAxisID: 'y'
                }, {
                    label: '{% trans "Total Cost" %}',
                    data: [
                        {% for item in maintenance_trends %}
                            {{ item.total_cost|default:0 }}{% if not forloop.last %},{% endif %}
                        {% endfor %}
                    ],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: '{% trans "Count" %}'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: '{% trans "Cost ($)" %}'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    }
                }
            }
        });
    {% endif %}
</script>
{% endblock %}