{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Cancel Maintenance" %} - {{ maintenance.asset.asset_tag }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-times-circle"></i> {% trans "Cancel Maintenance" %}</h2>
                <a href="{% url 'inventory:maintenance_detail' maintenance.id %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> {% trans "Back to Details" %}
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-exclamation-triangle"></i> {% trans "Cancel Maintenance Confirmation" %}</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>{% trans "Warning:" %}</strong> {% trans "You are about to cancel this maintenance record. This action cannot be undone." %}
                    </div>

                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="reason" class="form-label">
                                {% trans "Cancellation Reason" %} <span class="text-danger">*</span>
                            </label>
                            <select name="reason" id="reason" class="form-select" required>
                                <option value="">{% trans "Select a reason..." %}</option>
                                <option value="no_longer_needed">{% trans "No longer needed" %}</option>
                                <option value="asset_disposed">{% trans "Asset disposed/retired" %}</option>
                                <option value="rescheduled">{% trans "Rescheduled to different date" %}</option>
                                <option value="parts_unavailable">{% trans "Parts/materials unavailable" %}</option>
                                <option value="technician_unavailable">{% trans "Technician unavailable" %}</option>
                                <option value="budget_constraints">{% trans "Budget constraints" %}</option>
                                <option value="safety_concerns">{% trans "Safety concerns identified" %}</option>
                                <option value="other">{% trans "Other" %}</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="additional_notes" class="form-label">{% trans "Additional Notes" %}</label>
                            <textarea name="additional_notes" id="additional_notes" class="form-control" rows="4" 
                                      placeholder="{% trans 'Provide additional details about the cancellation...' %}"></textarea>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="confirm_cancel" required>
                            <label class="form-check-label" for="confirm_cancel">
                                {% trans "I confirm that I want to cancel this maintenance record" %}
                            </label>
                        </div>

                        <div class="d-flex justify-content-end">
                            <a href="{% url 'inventory:maintenance_detail' maintenance.id %}" class="btn btn-secondary me-2">
                                {% trans "Keep Maintenance" %}
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-times"></i> {% trans "Cancel Maintenance" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Maintenance Summary -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6><i class="fas fa-info-circle"></i> {% trans "Maintenance Summary" %}</h6>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <td><strong>{% trans "Asset:" %}</strong></td>
                            <td>{{ maintenance.asset.asset_tag }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Type:" %}</strong></td>
                            <td>
                                <span class="badge bg-secondary">
                                    {{ maintenance.get_maintenance_type_display }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Scheduled:" %}</strong></td>
                            <td>{{ maintenance.scheduled_date }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Status:" %}</strong></td>
                            <td>
                                <span class="badge bg-{% if maintenance.status == 'in_progress' %}warning{% else %}info{% endif %}">
                                    {{ maintenance.get_status_display }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Estimated Cost:" %}</strong></td>
                            <td>${{ maintenance.cost|floatformat:2 }}</td>
                        </tr>
                    </table>
                    
                    <div class="mt-3">
                        <h6>{% trans "Description:" %}</h6>
                        <p class="text-muted small">{{ maintenance.description }}</p>
                    </div>
                </div>
            </div>

            <!-- Asset Information -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6><i class="fas fa-box"></i> {% trans "Asset Information" %}</h6>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <td><strong>{% trans "Name:" %}</strong></td>
                            <td>{{ maintenance.asset.name }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Category:" %}</strong></td>
                            <td>{{ maintenance.asset.category.name }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Location:" %}</strong></td>
                            <td>{{ maintenance.asset.location|default:"N/A" }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Current Status:" %}</strong></td>
                            <td>
                                <span class="badge bg-{% if maintenance.asset.status == 'active' %}success{% elif maintenance.asset.status == 'maintenance' %}warning{% else %}secondary{% endif %}">
                                    {{ maintenance.asset.get_status_display }}
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Alternatives -->
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-lightbulb"></i> {% trans "Consider These Alternatives" %}</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-primary">{% trans "Reschedule" %}</h6>
                        <small class="text-muted">
                            {% trans "If the timing isn't right, consider rescheduling to a more appropriate date." %}
                        </small>
                        <div class="mt-2">
                            <a href="{% url 'inventory:maintenance_schedule_asset' maintenance.asset.id %}" class="btn btn-outline-primary btn-sm">
                                {% trans "Schedule New" %}
                            </a>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-info">{% trans "Modify" %}</h6>
                        <small class="text-muted">
                            {% trans "Update the maintenance details instead of cancelling completely." %}
                        </small>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-success">{% trans "Defer" %}</h6>
                        <small class="text-muted">
                            {% trans "If not urgent, consider deferring to the next maintenance cycle." %}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const reasonSelect = document.getElementById('reason');
        const additionalNotes = document.getElementById('additional_notes');
        
        reasonSelect.addEventListener('change', function() {
            if (this.value === 'other') {
                additionalNotes.required = true;
                additionalNotes.placeholder = '{% trans "Please specify the reason for cancellation..." %}';
            } else {
                additionalNotes.required = false;
                additionalNotes.placeholder = '{% trans "Provide additional details about the cancellation..." %}';
            }
        });
    });
</script>
{% endblock %}