{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Complete Maintenance" %} - {{ maintenance.asset.asset_tag }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-check-circle"></i> {% trans "Complete Maintenance" %}</h2>
                <a href="{% url 'inventory:maintenance_detail' maintenance.id %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> {% trans "Back to Details" %}
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-check-circle"></i> {% trans "Complete Maintenance Work" %}</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.completed_date.id_for_label }}" class="form-label">
                                        {% trans "Completion Date" %} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.completed_date }}
                                    {% if form.completed_date.errors %}
                                        <div class="text-danger">{{ form.completed_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.cost.id_for_label }}" class="form-label">
                                        {% trans "Actual Cost" %}
                                    </label>
                                    {{ form.cost }}
                                    {% if form.cost.errors %}
                                        <div class="text-danger">{{ form.cost.errors.0 }}</div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        {% trans "Estimated cost:" %} ${{ maintenance.cost|floatformat:2 }}
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.performed_by.id_for_label }}" class="form-label">
                                        {% trans "Performed By" %}
                                    </label>
                                    {{ form.performed_by }}
                                    {% if form.performed_by.errors %}
                                        <div class="text-danger">{{ form.performed_by.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.asset_condition.id_for_label }}" class="form-label">
                                        {% trans "Asset Condition After Maintenance" %}
                                    </label>
                                    {{ form.asset_condition }}
                                    {% if form.asset_condition.errors %}
                                        <div class="text-danger">{{ form.asset_condition.errors.0 }}</div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        {% trans "Current condition:" %} {{ maintenance.asset.get_condition_display }}
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.next_maintenance_date.id_for_label }}" class="form-label">
                                {% trans "Next Maintenance Date" %}
                            </label>
                            {{ form.next_maintenance_date }}
                            {% if form.next_maintenance_date.errors %}
                                <div class="text-danger">{{ form.next_maintenance_date.errors.0 }}</div>
                            {% endif %}
                            <small class="form-text text-muted">
                                {% trans "Schedule the next maintenance for this asset" %}
                            </small>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.completion_notes.id_for_label }}" class="form-label">
                                {% trans "Completion Notes" %}
                            </label>
                            {{ form.completion_notes }}
                            {% if form.completion_notes.errors %}
                                <div class="text-danger">{{ form.completion_notes.errors.0 }}</div>
                            {% endif %}
                            <small class="form-text text-muted">
                                {% trans "Describe the work performed, parts replaced, issues found, etc." %}
                            </small>
                        </div>

                        <div class="d-flex justify-content-end">
                            <a href="{% url 'inventory:maintenance_detail' maintenance.id %}" class="btn btn-secondary me-2">
                                {% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check"></i> {% trans "Complete Maintenance" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Maintenance Summary -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6><i class="fas fa-info-circle"></i> {% trans "Maintenance Summary" %}</h6>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <td><strong>{% trans "Asset:" %}</strong></td>
                            <td>{{ maintenance.asset.asset_tag }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Type:" %}</strong></td>
                            <td>
                                <span class="badge bg-secondary">
                                    {{ maintenance.get_maintenance_type_display }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Scheduled:" %}</strong></td>
                            <td>{{ maintenance.scheduled_date }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Status:" %}</strong></td>
                            <td>
                                <span class="badge bg-warning">
                                    {{ maintenance.get_status_display }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Estimated Cost:" %}</strong></td>
                            <td>${{ maintenance.cost|floatformat:2 }}</td>
                        </tr>
                    </table>
                    
                    <div class="mt-3">
                        <h6>{% trans "Description:" %}</h6>
                        <p class="text-muted small">{{ maintenance.description }}</p>
                    </div>
                </div>
            </div>

            <!-- Asset Information -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6><i class="fas fa-box"></i> {% trans "Asset Information" %}</h6>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <td><strong>{% trans "Name:" %}</strong></td>
                            <td>{{ maintenance.asset.name }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Category:" %}</strong></td>
                            <td>{{ maintenance.asset.category.name }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Location:" %}</strong></td>
                            <td>{{ maintenance.asset.location|default:"N/A" }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Current Status:" %}</strong></td>
                            <td>
                                <span class="badge bg-{% if maintenance.asset.status == 'active' %}success{% elif maintenance.asset.status == 'maintenance' %}warning{% else %}secondary{% endif %}">
                                    {{ maintenance.asset.get_status_display }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Condition:" %}</strong></td>
                            <td>
                                <span class="badge bg-{% if maintenance.asset.condition == 'excellent' %}success{% elif maintenance.asset.condition == 'good' %}info{% elif maintenance.asset.condition == 'fair' %}warning{% else %}danger{% endif %}">
                                    {{ maintenance.asset.get_condition_display }}
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Completion Guidelines -->
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-lightbulb"></i> {% trans "Completion Guidelines" %}</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-primary">{% trans "Documentation" %}</h6>
                        <small class="text-muted">
                            {% trans "Record all work performed, parts used, and any issues discovered during maintenance." %}
                        </small>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-success">{% trans "Asset Condition" %}</h6>
                        <small class="text-muted">
                            {% trans "Update the asset condition based on its state after maintenance completion." %}
                        </small>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-info">{% trans "Next Maintenance" %}</h6>
                        <small class="text-muted">
                            {% trans "Schedule the next maintenance based on manufacturer recommendations or usage patterns." %}
                        </small>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-warning">{% trans "Cost Tracking" %}</h6>
                        <small class="text-muted">
                            {% trans "Record actual costs including labor, parts, and external services for accurate budgeting." %}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set completion date to today by default
        const completedDateInput = document.getElementById('{{ form.completed_date.id_for_label }}');
        if (completedDateInput && !completedDateInput.value) {
            const today = new Date().toISOString().split('T')[0];
            completedDateInput.value = today;
        }
        
        // Auto-suggest next maintenance date based on type
        const nextMaintenanceDateInput = document.getElementById('{{ form.next_maintenance_date.id_for_label }}');
        const maintenanceType = '{{ maintenance.maintenance_type }}';
        
        if (nextMaintenanceDateInput && !nextMaintenanceDateInput.value && maintenanceType === 'preventive') {
            const completionDate = new Date(completedDateInput.value || new Date());
            // Add 6 months for preventive maintenance
            completionDate.setMonth(completionDate.getMonth() + 6);
            nextMaintenanceDateInput.value = completionDate.toISOString().split('T')[0];
        }
        
        // Update next maintenance date when completion date changes
        completedDateInput.addEventListener('change', function() {
            if (maintenanceType === 'preventive' && this.value) {
                const completionDate = new Date(this.value);
                completionDate.setMonth(completionDate.getMonth() + 6);
                nextMaintenanceDateInput.value = completionDate.toISOString().split('T')[0];
            }
        });
    });
</script>
{% endblock %}