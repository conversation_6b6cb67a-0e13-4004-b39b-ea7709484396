{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Maintenance Dashboard" %}{% endblock %}

{% block extra_css %}
<style>
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .stat-card.warning {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    .stat-card.success {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    .stat-card.info {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
    .maintenance-item {
        border-left: 4px solid #007bff;
        padding: 15px;
        margin-bottom: 10px;
        background: #f8f9fa;
        border-radius: 5px;
    }
    .maintenance-item.overdue {
        border-left-color: #dc3545;
    }
    .maintenance-item.due-soon {
        border-left-color: #ffc107;
    }
    .chart-container {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-tools"></i> {% trans "Maintenance Dashboard" %}</h2>
                <div>
                    <a href="{% url 'inventory:maintenance_schedule' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> {% trans "Schedule Maintenance" %}
                    </a>
                    <a href="{% url 'inventory:work_order_list' %}" class="btn btn-info">
                        <i class="fas fa-clipboard-list"></i> {% trans "Work Orders" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ total_assets }}</h3>
                        <p class="mb-0">{% trans "Total Assets" %}</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-boxes fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card warning">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ maintenance_due }}</h3>
                        <p class="mb-0">{% trans "Maintenance Due" %}</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card info">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ in_maintenance }}</h3>
                        <p class="mb-0">{% trans "In Maintenance" %}</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-wrench fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card success">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>${{ maintenance_costs.total_cost|floatformat:2 }}</h3>
                        <p class="mb-0">{% trans "Monthly Cost" %}</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Assets Due for Maintenance -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-clock"></i> {% trans "Assets Due for Maintenance" %}</h5>
                    <a href="{% url 'inventory:maintenance_due' %}" class="btn btn-sm btn-outline-primary float-end">
                        {% trans "View All" %}
                    </a>
                </div>
                <div class="card-body">
                    {% if assets_due %}
                        {% for asset in assets_due %}
                            <div class="maintenance-item {% if asset.next_maintenance_date < today %}overdue{% elif asset.next_maintenance_date <= next_week %}due-soon{% endif %}">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <strong>{{ asset.asset_tag }}</strong> - {{ asset.name }}
                                        <br>
                                        <small class="text-muted">
                                            {% trans "Location:" %} {{ asset.location|default:"N/A" }}
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-warning">
                                            {{ asset.next_maintenance_date }}
                                        </span>
                                        <br>
                                        <a href="{% url 'inventory:maintenance_schedule_asset' asset.id %}" class="btn btn-sm btn-primary mt-1">
                                            {% trans "Schedule" %}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">{% trans "No assets due for maintenance in the next 7 days." %}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Upcoming Maintenance -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-calendar-alt"></i> {% trans "Upcoming Maintenance" %}</h5>
                    <a href="{% url 'inventory:maintenance_list' %}" class="btn btn-sm btn-outline-primary float-end">
                        {% trans "View All" %}
                    </a>
                </div>
                <div class="card-body">
                    {% if upcoming_maintenance %}
                        {% for maintenance in upcoming_maintenance %}
                            <div class="maintenance-item">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <strong>{{ maintenance.asset.asset_tag }}</strong> - {{ maintenance.asset.name }}
                                        <br>
                                        <small class="text-muted">
                                            {{ maintenance.get_maintenance_type_display }} - {{ maintenance.description|truncatechars:50 }}
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-info">
                                            {{ maintenance.scheduled_date }}
                                        </span>
                                        <br>
                                        <a href="{% url 'inventory:maintenance_detail' maintenance.id %}" class="btn btn-sm btn-outline-primary mt-1">
                                            {% trans "View" %}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">{% trans "No upcoming maintenance scheduled." %}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <!-- Recent Maintenance Activities -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-history"></i> {% trans "Recent Maintenance Activities" %}</h5>
                </div>
                <div class="card-body">
                    {% if recent_maintenance %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>{% trans "Asset" %}</th>
                                        <th>{% trans "Type" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Date" %}</th>
                                        <th>{% trans "Cost" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for maintenance in recent_maintenance %}
                                        <tr>
                                            <td>
                                                <strong>{{ maintenance.asset.asset_tag }}</strong><br>
                                                <small class="text-muted">{{ maintenance.asset.name }}</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    {{ maintenance.get_maintenance_type_display }}
                                                </span>
                                            </td>
                                            <td>
                                                {% if maintenance.status == 'completed' %}
                                                    <span class="badge bg-success">{{ maintenance.get_status_display }}</span>
                                                {% elif maintenance.status == 'in_progress' %}
                                                    <span class="badge bg-warning">{{ maintenance.get_status_display }}</span>
                                                {% elif maintenance.status == 'scheduled' %}
                                                    <span class="badge bg-info">{{ maintenance.get_status_display }}</span>
                                                {% else %}
                                                    <span class="badge bg-danger">{{ maintenance.get_status_display }}</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ maintenance.scheduled_date }}</td>
                                            <td>${{ maintenance.cost|floatformat:2 }}</td>
                                            <td>
                                                <a href="{% url 'inventory:maintenance_detail' maintenance.id %}" class="btn btn-sm btn-outline-primary">
                                                    {% trans "View" %}
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted">{% trans "No recent maintenance activities." %}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Maintenance Cost Breakdown -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie"></i> {% trans "Cost Breakdown" %}</h5>
                </div>
                <div class="card-body">
                    {% if maintenance_costs.by_type %}
                        {% for item in maintenance_costs.by_type %}
                            <div class="d-flex justify-content-between mb-2">
                                <span>{{ item.maintenance_type|capfirst }}</span>
                                <span class="fw-bold">${{ item.total_cost|floatformat:2 }}</span>
                            </div>
                            <div class="progress mb-3" style="height: 5px;">
                                <div class="progress-bar" role="progressbar" 
                                     style="width: {% widthratio item.total_cost maintenance_costs.total_cost 100 %}%">
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">{% trans "No maintenance costs recorded this month." %}</p>
                    {% endif %}
                    
                    <div class="mt-3 pt-3 border-top">
                        <div class="d-flex justify-content-between">
                            <strong>{% trans "Total:" %}</strong>
                            <strong>${{ maintenance_costs.total_cost|floatformat:2 }}</strong>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <a href="{% url 'inventory:maintenance_analytics' %}" class="btn btn-outline-primary btn-sm w-100">
                            {% trans "View Analytics" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh dashboard every 5 minutes
    setTimeout(function() {
        location.reload();
    }, 300000);
</script>
{% endblock %}