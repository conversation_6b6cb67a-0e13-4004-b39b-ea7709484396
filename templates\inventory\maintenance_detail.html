{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Maintenance Details" %} - {{ maintenance.asset.asset_tag }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-tools"></i> {% trans "Maintenance Details" %}</h2>
                <div>
                    {% if maintenance.status == 'scheduled' %}
                        <a href="{% url 'inventory:work_order_start' maintenance.id %}" class="btn btn-success">
                            <i class="fas fa-play"></i> {% trans "Start Work" %}
                        </a>
                    {% endif %}
                    {% if maintenance.status in 'scheduled,in_progress' %}
                        <a href="{% url 'inventory:maintenance_complete' maintenance.id %}" class="btn btn-warning">
                            <i class="fas fa-check"></i> {% trans "Complete" %}
                        </a>
                        <a href="{% url 'inventory:maintenance_cancel' maintenance.id %}" class="btn btn-danger">
                            <i class="fas fa-times"></i> {% trans "Cancel" %}
                        </a>
                    {% endif %}
                    <a href="{% url 'inventory:maintenance_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> {% trans "Back to List" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <!-- Maintenance Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle"></i> {% trans "Maintenance Information" %}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>{% trans "Maintenance Type:" %}</strong></td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            {{ maintenance.get_maintenance_type_display }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Status:" %}</strong></td>
                                    <td>
                                        {% if maintenance.status == 'completed' %}
                                            <span class="badge bg-success">{{ maintenance.get_status_display }}</span>
                                        {% elif maintenance.status == 'in_progress' %}
                                            <span class="badge bg-warning">{{ maintenance.get_status_display }}</span>
                                        {% elif maintenance.status == 'scheduled' %}
                                            <span class="badge bg-info">{{ maintenance.get_status_display }}</span>
                                        {% else %}
                                            <span class="badge bg-danger">{{ maintenance.get_status_display }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Scheduled Date:" %}</strong></td>
                                    <td>{{ maintenance.scheduled_date }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Completed Date:" %}</strong></td>
                                    <td>{{ maintenance.completed_date|default:"-" }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>{% trans "Cost:" %}</strong></td>
                                    <td>${{ maintenance.cost|floatformat:2 }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Performed By:" %}</strong></td>
                                    <td>{{ maintenance.performed_by|default:"-" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Supplier:" %}</strong></td>
                                    <td>{{ maintenance.supplier|default:"-" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Next Maintenance:" %}</strong></td>
                                    <td>{{ maintenance.next_maintenance_date|default:"-" }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>{% trans "Description:" %}</h6>
                            <p class="text-muted">{{ maintenance.description }}</p>
                        </div>
                    </div>
                    
                    {% if maintenance.notes %}
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6>{% trans "Notes:" %}</h6>
                                <p class="text-muted">{{ maintenance.notes|linebreaks }}</p>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Timeline -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-history"></i> {% trans "Maintenance Timeline" %}</h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">{% trans "Maintenance Scheduled" %}</h6>
                                <p class="timeline-text">
                                    {% trans "Maintenance scheduled for" %} {{ maintenance.scheduled_date }}
                                    {% if maintenance.created_by %}
                                        {% trans "by" %} {{ maintenance.created_by.get_full_name|default:maintenance.created_by.username }}
                                    {% endif %}
                                </p>
                                <small class="text-muted">{{ maintenance.created_at }}</small>
                            </div>
                        </div>
                        
                        {% if maintenance.status == 'in_progress' or maintenance.status == 'completed' %}
                            <div class="timeline-item">
                                <div class="timeline-marker bg-warning"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">{% trans "Work Started" %}</h6>
                                    <p class="timeline-text">
                                        {% trans "Maintenance work started" %}
                                        {% if maintenance.performed_by %}
                                            {% trans "by" %} {{ maintenance.performed_by }}
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        {% endif %}
                        
                        {% if maintenance.status == 'completed' %}
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">{% trans "Maintenance Completed" %}</h6>
                                    <p class="timeline-text">
                                        {% trans "Maintenance completed on" %} {{ maintenance.completed_date }}
                                        {% if maintenance.cost > 0 %}
                                            {% trans "with total cost of" %} ${{ maintenance.cost|floatformat:2 }}
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        {% endif %}
                        
                        {% if maintenance.status == 'cancelled' %}
                            <div class="timeline-item">
                                <div class="timeline-marker bg-danger"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">{% trans "Maintenance Cancelled" %}</h6>
                                    <p class="timeline-text">{% trans "Maintenance was cancelled" %}</p>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Asset Information -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6><i class="fas fa-box"></i> {% trans "Asset Information" %}</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        {% if maintenance.asset.image %}
                            <img src="{{ maintenance.asset.image.url }}" alt="{{ maintenance.asset.name }}" 
                                 class="img-fluid rounded" style="max-height: 150px;">
                        {% else %}
                            <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                 style="height: 150px;">
                                <i class="fas fa-box fa-3x text-muted"></i>
                            </div>
                        {% endif %}
                    </div>
                    
                    <table class="table table-sm">
                        <tr>
                            <td><strong>{% trans "Asset Tag:" %}</strong></td>
                            <td>{{ maintenance.asset.asset_tag }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Name:" %}</strong></td>
                            <td>{{ maintenance.asset.name }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Category:" %}</strong></td>
                            <td>{{ maintenance.asset.category.name }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Location:" %}</strong></td>
                            <td>{{ maintenance.asset.location|default:"N/A" }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Status:" %}</strong></td>
                            <td>
                                <span class="badge bg-{% if maintenance.asset.status == 'active' %}success{% elif maintenance.asset.status == 'maintenance' %}warning{% else %}secondary{% endif %}">
                                    {{ maintenance.asset.get_status_display }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Condition:" %}</strong></td>
                            <td>
                                <span class="badge bg-{% if maintenance.asset.condition == 'excellent' %}success{% elif maintenance.asset.condition == 'good' %}info{% elif maintenance.asset.condition == 'fair' %}warning{% else %}danger{% endif %}">
                                    {{ maintenance.asset.get_condition_display }}
                                </span>
                            </td>
                        </tr>
                    </table>
                    
                    <div class="mt-3">
                        <a href="{% url 'inventory:maintenance_history' maintenance.asset.id %}" 
                           class="btn btn-outline-primary btn-sm w-100">
                            <i class="fas fa-history"></i> {% trans "View All Maintenance" %}
                        </a>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-bolt"></i> {% trans "Quick Actions" %}</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        {% if maintenance.status == 'scheduled' %}
                            <a href="{% url 'inventory:work_order_start' maintenance.id %}" class="btn btn-success btn-sm">
                                <i class="fas fa-play"></i> {% trans "Start Work Order" %}
                            </a>
                        {% endif %}
                        
                        {% if maintenance.status in 'scheduled,in_progress' %}
                            <a href="{% url 'inventory:maintenance_complete' maintenance.id %}" class="btn btn-warning btn-sm">
                                <i class="fas fa-check"></i> {% trans "Mark Complete" %}
                            </a>
                        {% endif %}
                        
                        <a href="{% url 'inventory:maintenance_schedule_asset' maintenance.asset.id %}" class="btn btn-info btn-sm">
                            <i class="fas fa-plus"></i> {% trans "Schedule New Maintenance" %}
                        </a>
                        
                        <a href="{% url 'inventory:maintenance_analytics' %}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-chart-bar"></i> {% trans "View Analytics" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }
    
    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }
    
    .timeline-marker {
        position: absolute;
        left: -22px;
        top: 5px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 0 0 2px #dee2e6;
    }
    
    .timeline-content {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        border-left: 3px solid #007bff;
    }
    
    .timeline-title {
        margin-bottom: 5px;
        font-size: 14px;
        font-weight: 600;
    }
    
    .timeline-text {
        margin-bottom: 5px;
        font-size: 13px;
    }
</style>
{% endblock %}