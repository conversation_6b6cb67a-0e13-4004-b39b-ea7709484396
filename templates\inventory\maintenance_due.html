{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Maintenance Due" %}{% endblock %}

{% block extra_css %}
<style>
    .asset-card {
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
    }
    .asset-card.overdue {
        border-left-color: #dc3545;
        background-color: rgba(220, 53, 69, 0.05);
    }
    .asset-card.due-soon {
        border-left-color: #ffc107;
        background-color: rgba(255, 193, 7, 0.05);
    }
    .asset-card.upcoming {
        border-left-color: #28a745;
        background-color: rgba(40, 167, 69, 0.05);
    }
    .asset-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    .urgency-badge {
        position: absolute;
        top: 10px;
        right: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-exclamation-triangle"></i> {% trans "Assets Due for Maintenance" %}</h2>
                <div>
                    <a href="{% url 'inventory:maintenance_schedule' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> {% trans "Schedule Maintenance" %}
                    </a>
                    <a href="{% url 'inventory:maintenance_dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-tachometer-alt"></i> {% trans "Dashboard" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Options -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="days" class="form-label">{% trans "Show maintenance due within" %}</label>
                    <select name="days" id="days" class="form-select">
                        <option value="7" {% if days_ahead == 7 %}selected{% endif %}>{% trans "7 days" %}</option>
                        <option value="14" {% if days_ahead == 14 %}selected{% endif %}>{% trans "14 days" %}</option>
                        <option value="30" {% if days_ahead == 30 %}selected{% endif %}>{% trans "30 days" %}</option>
                        <option value="60" {% if days_ahead == 60 %}selected{% endif %}>{% trans "60 days" %}</option>
                        <option value="90" {% if days_ahead == 90 %}selected{% endif %}>{% trans "90 days" %}</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-outline-secondary">
                            <i class="fas fa-filter"></i> {% trans "Apply Filter" %}
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3>{{ overdue|length }}</h3>
                            <p class="mb-0">{% trans "Overdue" %}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3>{{ due_soon|length }}</h3>
                            <p class="mb-0">{% trans "Due Soon (7 days)" %}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3>{{ upcoming|length }}</h3>
                            <p class="mb-0">{% trans "Upcoming" %}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Overdue Assets -->
    {% if overdue %}
        <div class="row mb-4">
            <div class="col-12">
                <h4 class="text-danger"><i class="fas fa-exclamation-circle"></i> {% trans "Overdue Maintenance" %}</h4>
                <div class="row">
                    {% for asset in overdue %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card asset-card overdue position-relative">
                                <span class="badge bg-danger urgency-badge">{% trans "OVERDUE" %}</span>
                                <div class="card-body">
                                    <h6 class="card-title">{{ asset.asset_tag }}</h6>
                                    <p class="card-text">{{ asset.name }}</p>
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-map-marker-alt"></i> {{ asset.location|default:"No location" }}
                                        </small>
                                    </div>
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar"></i> {% trans "Due:" %} {{ asset.next_maintenance_date }}
                                        </small>
                                    </div>
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-tag"></i> {{ asset.category.name }}
                                        </small>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <a href="{% url 'inventory:maintenance_schedule_asset' asset.id %}" class="btn btn-danger btn-sm">
                                            <i class="fas fa-plus"></i> {% trans "Schedule Now" %}
                                        </a>
                                        <a href="{% url 'inventory:maintenance_history' asset.id %}" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-history"></i> {% trans "History" %}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Due Soon Assets -->
    {% if due_soon %}
        <div class="row mb-4">
            <div class="col-12">
                <h4 class="text-warning"><i class="fas fa-clock"></i> {% trans "Due Soon (Next 7 Days)" %}</h4>
                <div class="row">
                    {% for asset in due_soon %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card asset-card due-soon position-relative">
                                <span class="badge bg-warning urgency-badge">{% trans "DUE SOON" %}</span>
                                <div class="card-body">
                                    <h6 class="card-title">{{ asset.asset_tag }}</h6>
                                    <p class="card-text">{{ asset.name }}</p>
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-map-marker-alt"></i> {{ asset.location|default:"No location" }}
                                        </small>
                                    </div>
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar"></i> {% trans "Due:" %} {{ asset.next_maintenance_date }}
                                        </small>
                                    </div>
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-tag"></i> {{ asset.category.name }}
                                        </small>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <a href="{% url 'inventory:maintenance_schedule_asset' asset.id %}" class="btn btn-warning btn-sm">
                                            <i class="fas fa-plus"></i> {% trans "Schedule" %}
                                        </a>
                                        <a href="{% url 'inventory:maintenance_history' asset.id %}" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-history"></i> {% trans "History" %}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Upcoming Assets -->
    {% if upcoming %}
        <div class="row mb-4">
            <div class="col-12">
                <h4 class="text-success"><i class="fas fa-calendar-alt"></i> {% trans "Upcoming Maintenance" %}</h4>
                <div class="row">
                    {% for asset in upcoming %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card asset-card upcoming position-relative">
                                <span class="badge bg-success urgency-badge">{% trans "UPCOMING" %}</span>
                                <div class="card-body">
                                    <h6 class="card-title">{{ asset.asset_tag }}</h6>
                                    <p class="card-text">{{ asset.name }}</p>
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-map-marker-alt"></i> {{ asset.location|default:"No location" }}
                                        </small>
                                    </div>
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar"></i> {% trans "Due:" %} {{ asset.next_maintenance_date }}
                                        </small>
                                    </div>
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-tag"></i> {{ asset.category.name }}
                                        </small>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <a href="{% url 'inventory:maintenance_schedule_asset' asset.id %}" class="btn btn-success btn-sm">
                                            <i class="fas fa-plus"></i> {% trans "Schedule" %}
                                        </a>
                                        <a href="{% url 'inventory:maintenance_history' asset.id %}" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-history"></i> {% trans "History" %}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    {% endif %}

    <!-- No Assets Due -->
    {% if not overdue and not due_soon and not upcoming %}
        <div class="row">
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h5 class="text-success">{% trans "All Assets Up to Date!" %}</h5>
                    <p class="text-muted">{% trans "No assets require maintenance within the selected time period." %}</p>
                    <a href="{% url 'inventory:maintenance_dashboard' %}" class="btn btn-primary">
                        <i class="fas fa-tachometer-alt"></i> {% trans "Back to Dashboard" %}
                    </a>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh every 5 minutes to keep data current
    setTimeout(function() {
        location.reload();
    }, 300000);
    
    // Add tooltips to urgency badges
    document.addEventListener('DOMContentLoaded', function() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}