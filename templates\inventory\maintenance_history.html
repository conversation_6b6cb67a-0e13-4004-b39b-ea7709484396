{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Maintenance History" %} - {{ asset.asset_tag }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-history"></i> {% trans "Maintenance History" %} - {{ asset.asset_tag }}</h2>
                <div>
                    <a href="{% url 'inventory:maintenance_schedule_asset' asset.id %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> {% trans "Schedule New Maintenance" %}
                    </a>
                    <a href="{% url 'inventory:maintenance_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> {% trans "Back to List" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <!-- Maintenance Records -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list"></i> {% trans "Maintenance Records" %}</h5>
                </div>
                <div class="card-body">
                    {% if maintenance_records %}
                        <div class="timeline">
                            {% for maintenance in maintenance_records %}
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-{% if maintenance.status == 'completed' %}success{% elif maintenance.status == 'in_progress' %}warning{% elif maintenance.status == 'scheduled' %}info{% else %}danger{% endif %}"></div>
                                    <div class="timeline-content">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="timeline-title">
                                                    {{ maintenance.get_maintenance_type_display }}
                                                    <span class="badge bg-{% if maintenance.status == 'completed' %}success{% elif maintenance.status == 'in_progress' %}warning{% elif maintenance.status == 'scheduled' %}info{% else %}danger{% endif %} ms-2">
                                                        {{ maintenance.get_status_display }}
                                                    </span>
                                                </h6>
                                                <p class="timeline-text mb-2">{{ maintenance.description }}</p>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <small class="text-muted">
                                                            <i class="fas fa-calendar"></i> 
                                                            {% trans "Scheduled:" %} {{ maintenance.scheduled_date }}
                                                        </small>
                                                        {% if maintenance.completed_date %}
                                                            <br>
                                                            <small class="text-muted">
                                                                <i class="fas fa-check"></i> 
                                                                {% trans "Completed:" %} {{ maintenance.completed_date }}
                                                            </small>
                                                        {% endif %}
                                                    </div>
                                                    <div class="col-md-6">
                                                        {% if maintenance.cost > 0 %}
                                                            <small class="text-muted">
                                                                <i class="fas fa-dollar-sign"></i> 
                                                                {% trans "Cost:" %} ${{ maintenance.cost|floatformat:2 }}
                                                            </small>
                                                            <br>
                                                        {% endif %}
                                                        {% if maintenance.performed_by %}
                                                            <small class="text-muted">
                                                                <i class="fas fa-user"></i> 
                                                                {% trans "By:" %} {{ maintenance.performed_by }}
                                                            </small>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                                {% if maintenance.notes %}
                                                    <div class="mt-2">
                                                        <small class="text-muted">
                                                            <strong>{% trans "Notes:" %}</strong> {{ maintenance.notes|truncatechars:100 }}
                                                        </small>
                                                    </div>
                                                {% endif %}
                                            </div>
                                            <div class="timeline-actions">
                                                <a href="{% url 'inventory:maintenance_detail' maintenance.id %}" 
                                                   class="btn btn-sm btn-outline-primary" title="{% trans 'View Details' %}">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-tools fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{% trans "No Maintenance Records" %}</h5>
                            <p class="text-muted">{% trans "This asset has no maintenance history yet." %}</p>
                            <a href="{% url 'inventory:maintenance_schedule_asset' asset.id %}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> {% trans "Schedule First Maintenance" %}
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Asset Information -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6><i class="fas fa-box"></i> {% trans "Asset Information" %}</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        {% if asset.image %}
                            <img src="{{ asset.image.url }}" alt="{{ asset.name }}" 
                                 class="img-fluid rounded" style="max-height: 150px;">
                        {% else %}
                            <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                 style="height: 150px;">
                                <i class="fas fa-box fa-3x text-muted"></i>
                            </div>
                        {% endif %}
                    </div>
                    
                    <table class="table table-sm">
                        <tr>
                            <td><strong>{% trans "Asset Tag:" %}</strong></td>
                            <td>{{ asset.asset_tag }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Name:" %}</strong></td>
                            <td>{{ asset.name }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Category:" %}</strong></td>
                            <td>{{ asset.category.name }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Location:" %}</strong></td>
                            <td>{{ asset.location|default:"N/A" }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Status:" %}</strong></td>
                            <td>
                                <span class="badge bg-{% if asset.status == 'active' %}success{% elif asset.status == 'maintenance' %}warning{% else %}secondary{% endif %}">
                                    {{ asset.get_status_display }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Condition:" %}</strong></td>
                            <td>
                                <span class="badge bg-{% if asset.condition == 'excellent' %}success{% elif asset.condition == 'good' %}info{% elif asset.condition == 'fair' %}warning{% else %}danger{% endif %}">
                                    {{ asset.get_condition_display }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Purchase Date:" %}</strong></td>
                            <td>{{ asset.purchase_date }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Last Maintenance:" %}</strong></td>
                            <td>{{ asset.last_maintenance_date|default:"Never" }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Next Maintenance:" %}</strong></td>
                            <td>{{ asset.next_maintenance_date|default:"Not scheduled" }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Maintenance Statistics -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6><i class="fas fa-chart-bar"></i> {% trans "Maintenance Statistics" %}</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-primary">{{ total_maintenance }}</h4>
                            <small class="text-muted">{% trans "Total Records" %}</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ completed_maintenance }}</h4>
                            <small class="text-muted">{% trans "Completed" %}</small>
                        </div>
                    </div>
                    <hr>
                    <div class="text-center">
                        <h4 class="text-info">${{ total_cost|floatformat:2 }}</h4>
                        <small class="text-muted">{% trans "Total Cost" %}</small>
                    </div>
                    
                    {% if maintenance_by_type %}
                        <hr>
                        <h6>{% trans "By Type:" %}</h6>
                        {% for type_stat in maintenance_by_type %}
                            <div class="d-flex justify-content-between mb-2">
                                <span class="small">{{ type_stat.maintenance_type|capfirst }}</span>
                                <span class="small">
                                    {{ type_stat.count }} 
                                    {% if type_stat.total_cost %}
                                        (${{ type_stat.total_cost|floatformat:2 }})
                                    {% endif %}
                                </span>
                            </div>
                        {% endfor %}
                    {% endif %}
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-bolt"></i> {% trans "Quick Actions" %}</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'inventory:maintenance_schedule_asset' asset.id %}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> {% trans "Schedule Maintenance" %}
                        </a>
                        
                        {% if asset.maintenance_due %}
                            <a href="{% url 'inventory:maintenance_schedule_asset' asset.id %}" class="btn btn-warning btn-sm">
                                <i class="fas fa-exclamation-triangle"></i> {% trans "Maintenance Overdue" %}
                            </a>
                        {% endif %}
                        
                        <a href="{% url 'inventory:maintenance_analytics' %}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-chart-bar"></i> {% trans "View Analytics" %}
                        </a>
                        
                        <a href="{% url 'inventory:item_detail' asset.id %}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-info-circle"></i> {% trans "Asset Details" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }
    
    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 30px;
    }
    
    .timeline-marker {
        position: absolute;
        left: -22px;
        top: 5px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 0 0 2px #dee2e6;
    }
    
    .timeline-content {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        border-left: 3px solid #007bff;
        position: relative;
    }
    
    .timeline-title {
        margin-bottom: 10px;
        font-size: 16px;
        font-weight: 600;
    }
    
    .timeline-text {
        margin-bottom: 10px;
        font-size: 14px;
        color: #6c757d;
    }
    
    .timeline-actions {
        position: absolute;
        top: 15px;
        right: 15px;
    }
</style>
{% endblock %}