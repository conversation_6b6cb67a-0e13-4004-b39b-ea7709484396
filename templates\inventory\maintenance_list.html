{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Maintenance Records" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-list"></i> {% trans "Maintenance Records" %}</h2>
                <div>
                    <a href="{% url 'inventory:maintenance_schedule' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> {% trans "Schedule Maintenance" %}
                    </a>
                    <a href="{% url 'inventory:maintenance_dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-tachometer-alt"></i> {% trans "Dashboard" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="status" class="form-label">{% trans "Status" %}</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">{% trans "All Statuses" %}</option>
                        <option value="scheduled" {% if current_status == 'scheduled' %}selected{% endif %}>{% trans "Scheduled" %}</option>
                        <option value="in_progress" {% if current_status == 'in_progress' %}selected{% endif %}>{% trans "In Progress" %}</option>
                        <option value="completed" {% if current_status == 'completed' %}selected{% endif %}>{% trans "Completed" %}</option>
                        <option value="cancelled" {% if current_status == 'cancelled' %}selected{% endif %}>{% trans "Cancelled" %}</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="type" class="form-label">{% trans "Type" %}</label>
                    <select name="type" id="type" class="form-select">
                        <option value="">{% trans "All Types" %}</option>
                        <option value="preventive" {% if current_type == 'preventive' %}selected{% endif %}>{% trans "Preventive" %}</option>
                        <option value="corrective" {% if current_type == 'corrective' %}selected{% endif %}>{% trans "Corrective" %}</option>
                        <option value="emergency" {% if current_type == 'emergency' %}selected{% endif %}>{% trans "Emergency" %}</option>
                        <option value="upgrade" {% if current_type == 'upgrade' %}selected{% endif %}>{% trans "Upgrade" %}</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="asset" class="form-label">{% trans "Asset" %}</label>
                    <select name="asset" id="asset" class="form-select">
                        <option value="">{% trans "All Assets" %}</option>
                        {% for asset in assets %}
                            <option value="{{ asset.id }}" {% if current_asset == asset.id|stringformat:"s" %}selected{% endif %}>
                                {{ asset.asset_tag }} - {{ asset.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="q" class="form-label">{% trans "Search" %}</label>
                    <div class="input-group">
                        <input type="text" name="q" id="q" class="form-control" 
                               placeholder="{% trans 'Search maintenance records...' %}" value="{{ query }}">
                        <button type="submit" class="btn btn-outline-secondary">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Maintenance Records Table -->
    <div class="card">
        <div class="card-body">
            {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% trans "Asset" %}</th>
                                <th>{% trans "Type" %}</th>
                                <th>{% trans "Status" %}</th>
                                <th>{% trans "Scheduled Date" %}</th>
                                <th>{% trans "Completed Date" %}</th>
                                <th>{% trans "Cost" %}</th>
                                <th>{% trans "Performed By" %}</th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for maintenance in page_obj %}
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ maintenance.asset.asset_tag }}</strong>
                                            <br>
                                            <small class="text-muted">{{ maintenance.asset.name }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            {{ maintenance.get_maintenance_type_display }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if maintenance.status == 'completed' %}
                                            <span class="badge bg-success">{{ maintenance.get_status_display }}</span>
                                        {% elif maintenance.status == 'in_progress' %}
                                            <span class="badge bg-warning">{{ maintenance.get_status_display }}</span>
                                        {% elif maintenance.status == 'scheduled' %}
                                            <span class="badge bg-info">{{ maintenance.get_status_display }}</span>
                                        {% else %}
                                            <span class="badge bg-danger">{{ maintenance.get_status_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ maintenance.scheduled_date }}</td>
                                    <td>{{ maintenance.completed_date|default:"-" }}</td>
                                    <td>${{ maintenance.cost|floatformat:2 }}</td>
                                    <td>{{ maintenance.performed_by|default:"-" }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'inventory:maintenance_detail' maintenance.id %}" 
                                               class="btn btn-sm btn-outline-primary" title="{% trans 'View Details' %}">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if maintenance.status == 'scheduled' %}
                                                <a href="{% url 'inventory:work_order_start' maintenance.id %}" 
                                                   class="btn btn-sm btn-outline-success" title="{% trans 'Start Work' %}">
                                                    <i class="fas fa-play"></i>
                                                </a>
                                            {% endif %}
                                            {% if maintenance.status in 'scheduled,in_progress' %}
                                                <a href="{% url 'inventory:maintenance_complete' maintenance.id %}" 
                                                   class="btn btn-sm btn-outline-warning" title="{% trans 'Complete' %}">
                                                    <i class="fas fa-check"></i>
                                                </a>
                                                <a href="{% url 'inventory:maintenance_cancel' maintenance.id %}" 
                                                   class="btn btn-sm btn-outline-danger" title="{% trans 'Cancel' %}">
                                                    <i class="fas fa-times"></i>
                                                </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <nav aria-label="{% trans 'Maintenance pagination' %}">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}{% if request.GET.asset %}&asset={{ request.GET.asset }}{% endif %}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}">
                                        {% trans "First" %}
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}{% if request.GET.asset %}&asset={{ request.GET.asset }}{% endif %}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}">
                                        {% trans "Previous" %}
                                    </a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}{% if request.GET.asset %}&asset={{ request.GET.asset }}{% endif %}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}">
                                        {% trans "Next" %}
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}{% if request.GET.asset %}&asset={{ request.GET.asset }}{% endif %}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}">
                                        {% trans "Last" %}
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-tools fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">{% trans "No maintenance records found" %}</h5>
                    <p class="text-muted">{% trans "Try adjusting your search criteria or schedule new maintenance." %}</p>
                    <a href="{% url 'inventory:maintenance_schedule' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> {% trans "Schedule Maintenance" %}
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}