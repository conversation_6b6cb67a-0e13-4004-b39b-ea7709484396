{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-calendar-plus"></i> {{ title }}</h2>
                <a href="{% url 'inventory:maintenance_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> {% trans "Back to List" %}
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-calendar-plus"></i> {% trans "Schedule Maintenance" %}</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        {% if not asset %}
                            <div class="mb-3">
                                <label for="{{ form.asset.id_for_label }}" class="form-label">
                                    {% trans "Asset" %} <span class="text-danger">*</span>
                                </label>
                                <select name="{{ form.asset.name }}" id="{{ form.asset.id_for_label }}" 
                                        class="form-select" required>
                                    <option value="">{% trans "Select Asset" %}</option>
                                    {% for asset_option in assets %}
                                        <option value="{{ asset_option.id }}" 
                                                {% if form.asset.value == asset_option.id %}selected{% endif %}>
                                            {{ asset_option.asset_tag }} - {{ asset_option.name }}
                                        </option>
                                    {% endfor %}
                                </select>
                                {% if form.asset.errors %}
                                    <div class="text-danger">{{ form.asset.errors.0 }}</div>
                                {% endif %}
                            </div>
                        {% else %}
                            <div class="mb-3">
                                <label class="form-label">{% trans "Asset" %}</label>
                                <div class="form-control-plaintext">
                                    <strong>{{ asset.asset_tag }}</strong> - {{ asset.name }}
                                    <br>
                                    <small class="text-muted">
                                        {% trans "Category:" %} {{ asset.category.name }} | 
                                        {% trans "Location:" %} {{ asset.location|default:"N/A" }}
                                    </small>
                                </div>
                                <input type="hidden" name="asset" value="{{ asset.id }}">
                            </div>
                        {% endif %}

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.maintenance_type.id_for_label }}" class="form-label">
                                        {% trans "Maintenance Type" %} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.maintenance_type }}
                                    {% if form.maintenance_type.errors %}
                                        <div class="text-danger">{{ form.maintenance_type.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.scheduled_date.id_for_label }}" class="form-label">
                                        {% trans "Scheduled Date" %} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.scheduled_date }}
                                    {% if form.scheduled_date.errors %}
                                        <div class="text-danger">{{ form.scheduled_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                {% trans "Description" %} <span class="text-danger">*</span>
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger">{{ form.description.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.cost.id_for_label }}" class="form-label">
                                        {% trans "Estimated Cost" %}
                                    </label>
                                    {{ form.cost }}
                                    {% if form.cost.errors %}
                                        <div class="text-danger">{{ form.cost.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.performed_by.id_for_label }}" class="form-label">
                                        {% trans "Performed By" %}
                                    </label>
                                    {{ form.performed_by }}
                                    {% if form.performed_by.errors %}
                                        <div class="text-danger">{{ form.performed_by.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.supplier.id_for_label }}" class="form-label">
                                        {% trans "Supplier" %}
                                    </label>
                                    {{ form.supplier }}
                                    {% if form.supplier.errors %}
                                        <div class="text-danger">{{ form.supplier.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.next_maintenance_date.id_for_label }}" class="form-label">
                                        {% trans "Next Maintenance Date" %}
                                    </label>
                                    {{ form.next_maintenance_date }}
                                    {% if form.next_maintenance_date.errors %}
                                        <div class="text-danger">{{ form.next_maintenance_date.errors.0 }}</div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        {% trans "For preventive maintenance scheduling" %}
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">
                                {% trans "Notes" %}
                            </label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="text-danger">{{ form.notes.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="d-flex justify-content-end">
                            <a href="{% url 'inventory:maintenance_list' %}" class="btn btn-secondary me-2">
                                {% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> {% trans "Schedule Maintenance" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            {% if asset %}
                <!-- Asset Information -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6><i class="fas fa-info-circle"></i> {% trans "Asset Information" %}</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>{% trans "Asset Tag:" %}</strong></td>
                                <td>{{ asset.asset_tag }}</td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "Name:" %}</strong></td>
                                <td>{{ asset.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "Category:" %}</strong></td>
                                <td>{{ asset.category.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "Location:" %}</strong></td>
                                <td>{{ asset.location|default:"N/A" }}</td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "Status:" %}</strong></td>
                                <td>
                                    <span class="badge bg-{% if asset.status == 'active' %}success{% elif asset.status == 'maintenance' %}warning{% else %}secondary{% endif %}">
                                        {{ asset.get_status_display }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "Last Maintenance:" %}</strong></td>
                                <td>{{ asset.last_maintenance_date|default:"Never" }}</td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "Next Maintenance:" %}</strong></td>
                                <td>{{ asset.next_maintenance_date|default:"Not scheduled" }}</td>
                            </tr>
                        </table>
                        
                        <div class="mt-3">
                            <a href="{% url 'inventory:maintenance_history' asset.id %}" class="btn btn-outline-primary btn-sm w-100">
                                <i class="fas fa-history"></i> {% trans "View Maintenance History" %}
                            </a>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Maintenance Guidelines -->
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-lightbulb"></i> {% trans "Maintenance Guidelines" %}</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-primary">{% trans "Preventive Maintenance" %}</h6>
                        <small class="text-muted">
                            {% trans "Regular scheduled maintenance to prevent breakdowns and extend asset life." %}
                        </small>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-warning">{% trans "Corrective Maintenance" %}</h6>
                        <small class="text-muted">
                            {% trans "Maintenance performed to fix identified problems or defects." %}
                        </small>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-danger">{% trans "Emergency Maintenance" %}</h6>
                        <small class="text-muted">
                            {% trans "Urgent maintenance required to address immediate safety or operational issues." %}
                        </small>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-info">{% trans "Upgrade Maintenance" %}</h6>
                        <small class="text-muted">
                            {% trans "Maintenance involving improvements or upgrades to asset functionality." %}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Set minimum date to today
    document.addEventListener('DOMContentLoaded', function() {
        const scheduledDateInput = document.getElementById('{{ form.scheduled_date.id_for_label }}');
        const nextMaintenanceDateInput = document.getElementById('{{ form.next_maintenance_date.id_for_label }}');
        
        if (scheduledDateInput) {
            const today = new Date().toISOString().split('T')[0];
            scheduledDateInput.setAttribute('min', today);
        }
        
        // Update next maintenance date based on scheduled date and type
        const maintenanceTypeSelect = document.getElementById('{{ form.maintenance_type.id_for_label }}');
        
        function updateNextMaintenanceDate() {
            if (scheduledDateInput.value && maintenanceTypeSelect.value === 'preventive') {
                const scheduledDate = new Date(scheduledDateInput.value);
                // Add 6 months for next preventive maintenance
                scheduledDate.setMonth(scheduledDate.getMonth() + 6);
                nextMaintenanceDateInput.value = scheduledDate.toISOString().split('T')[0];
            }
        }
        
        if (scheduledDateInput && maintenanceTypeSelect && nextMaintenanceDateInput) {
            scheduledDateInput.addEventListener('change', updateNextMaintenanceDate);
            maintenanceTypeSelect.addEventListener('change', updateNextMaintenanceDate);
        }
    });
</script>
{% endblock %}