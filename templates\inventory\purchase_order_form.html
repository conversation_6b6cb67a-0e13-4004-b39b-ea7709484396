{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Create Purchase Order" %} - {% trans "Inventory" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">{% trans "Create Purchase Order" %}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'accounts:dashboard' %}">{% trans "Dashboard" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">{% trans "Inventory" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'inventory:purchase_order_list' %}">{% trans "Purchase Orders" %}</a></li>
                        <li class="breadcrumb-item active">{% trans "Create" %}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>
                        {% trans "Purchase Order Details" %}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="supplier" class="form-label">{% trans "Supplier" %}</label>
                                    <select name="supplier" id="supplier" class="form-select" required>
                                        <option value="">{% trans "Select Supplier" %}</option>
                                        {% for supplier in suppliers %}
                                        <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="order_date" class="form-label">{% trans "Order Date" %}</label>
                                    <input type="date" name="order_date" id="order_date" class="form-control" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="expected_delivery" class="form-label">{% trans "Expected Delivery" %}</label>
                                    <input type="date" name="expected_delivery" id="expected_delivery" class="form-control">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="priority" class="form-label">{% trans "Priority" %}</label>
                                    <select name="priority" id="priority" class="form-select">
                                        <option value="normal">{% trans "Normal" %}</option>
                                        <option value="high">{% trans "High" %}</option>
                                        <option value="urgent">{% trans "Urgent" %}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="notes" class="form-label">{% trans "Notes" %}</label>
                                    <textarea name="notes" id="notes" class="form-control" rows="3" placeholder="{% trans 'Additional notes or requirements' %}"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Order Items Section -->
                        <div class="row">
                            <div class="col-12">
                                <h6 class="mb-3">{% trans "Order Items" %}</h6>
                                <div id="order-items">
                                    <div class="order-item-row mb-3 p-3 border rounded">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <label class="form-label">{% trans "Item" %}</label>
                                                <select name="items[]" class="form-select" required>
                                                    <option value="">{% trans "Select Item" %}</option>
                                                    {% for item in items %}
                                                    <option value="{{ item.id }}">{{ item.name }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">{% trans "Quantity" %}</label>
                                                <input type="number" name="quantities[]" class="form-control" min="1" required>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">{% trans "Unit Price" %}</label>
                                                <input type="number" name="unit_prices[]" class="form-control" step="0.01" min="0">
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">{% trans "Notes" %}</label>
                                                <input type="text" name="item_notes[]" class="form-control" placeholder="{% trans 'Item notes' %}">
                                            </div>
                                            <div class="col-md-1">
                                                <label class="form-label">&nbsp;</label>
                                                <button type="button" class="btn btn-danger btn-sm remove-item">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" id="add-item" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-plus me-1"></i>
                                    {% trans "Add Item" %}
                                </button>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>
                                    {% trans "Create Purchase Order" %}
                                </button>
                                <a href="{% url 'inventory:purchase_order_list' %}" class="btn btn-secondary ms-2">
                                    {% trans "Cancel" %}
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const addItemBtn = document.getElementById('add-item');
    const orderItemsContainer = document.getElementById('order-items');
    
    addItemBtn.addEventListener('click', function() {
        const firstRow = orderItemsContainer.querySelector('.order-item-row');
        const newRow = firstRow.cloneNode(true);
        
        // Clear values in the new row
        newRow.querySelectorAll('input, select').forEach(input => {
            input.value = '';
        });
        
        orderItemsContainer.appendChild(newRow);
    });
    
    // Remove item functionality
    orderItemsContainer.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-item') || e.target.closest('.remove-item')) {
            const rows = orderItemsContainer.querySelectorAll('.order-item-row');
            if (rows.length > 1) {
                e.target.closest('.order-item-row').remove();
            }
        }
    });
});
</script>
{% endblock %}