{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Asset Replacement Planning Tools" %}{% endblock %}

{% block extra_css %}
<style>
    .planning-card {
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
        margin-bottom: 20px;
    }
    .planning-card.urgent {
        border-left-color: #dc3545;
        background-color: rgba(220, 53, 69, 0.05);
    }
    .planning-card.warning {
        border-left-color: #ffc107;
        background-color: rgba(255, 193, 7, 0.05);
    }
    .planning-card.info {
        border-left-color: #17a2b8;
        background-color: rgba(23, 162, 184, 0.05);
    }
    .chart-container {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    .budget-year {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        transition: all 0.3s ease;
    }
    .budget-year:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .asset-item {
        border-left: 4px solid #007bff;
        padding: 15px;
        margin-bottom: 10px;
        background: #f8f9fa;
        border-radius: 5px;
    }
    .asset-item.high-cost {
        border-left-color: #dc3545;
    }
    .asset-item.medium-cost {
        border-left-color: #ffc107;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-tools"></i> {% trans "Asset Replacement Planning Tools" %}</h2>
                <div>
                    <a href="{% url 'inventory:asset_analytics_dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-tachometer-alt"></i> {% trans "Analytics Dashboard" %}
                    </a>
                    <a href="{% url 'inventory:asset_replacement_planning' %}" class="btn btn-info">
                        <i class="fas fa-chart-line"></i> {% trans "Replacement Planning" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Assets Approaching End of Life -->
    <div class="row">
        <div class="col-md-6">
            <div class="chart-container">
                <h5><i class="fas fa-exclamation-triangle text-warning"></i> {% trans "Assets Approaching End of Life" %}</h5>
                {% if approaching_eol %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Asset" %}</th>
                                    <th>{% trans "Category" %}</th>
                                    <th>{% trans "Remaining Life" %}</th>
                                    <th>{% trans "Est. Replacement Cost" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in approaching_eol %}
                                    <tr class="{% if item.remaining_life <= 1 %}table-danger{% elif item.remaining_life <= 2 %}table-warning{% endif %}">
                                        <td>
                                            <strong>{{ item.asset.asset_tag }}</strong><br>
                                            <small class="text-muted">{{ item.asset.name|truncatechars:30 }}</small>
                                        </td>
                                        <td>{{ item.asset.category.name }}</td>
                                        <td>
                                            <span class="badge {% if item.remaining_life <= 1 %}bg-danger{% elif item.remaining_life <= 2 %}bg-warning{% else %}bg-info{% endif %}">
                                                {{ item.remaining_life|floatformat:1 }} {% trans "years" %}
                                            </span>
                                        </td>
                                        <td>${{ item.replacement_cost_estimate|floatformat:2 }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h6 class="text-success">{% trans "No Assets Approaching End of Life" %}</h6>
                        <p class="text-muted">{% trans "All assets are within their useful life period." %}</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Assets Past End of Life -->
        <div class="col-md-6">
            <div class="chart-container">
                <h5><i class="fas fa-times-circle text-danger"></i> {% trans "Assets Past End of Life" %}</h5>
                {% if past_eol %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Asset" %}</th>
                                    <th>{% trans "Category" %}</th>
                                    <th>{% trans "Age" %}</th>
                                    <th>{% trans "Priority" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for asset in past_eol %}
                                    <tr class="table-danger">
                                        <td>
                                            <strong>{{ asset.asset_tag }}</strong><br>
                                            <small class="text-muted">{{ asset.name|truncatechars:30 }}</small>
                                        </td>
                                        <td>{{ asset.category.name }}</td>
                                        <td>{{ asset.age|floatformat:1 }} {% trans "years" %}</td>
                                        <td>
                                            <span class="badge bg-danger">{% trans "High" %}</span>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h6 class="text-success">{% trans "No Assets Past End of Life" %}</h6>
                        <p class="text-muted">{% trans "All assets are within their useful life period." %}</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- High Maintenance Cost Assets -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <h5><i class="fas fa-dollar-sign text-warning"></i> {% trans "High Maintenance Cost Assets" %}</h5>
                {% if high_maintenance_assets %}
                    <div class="row">
                        {% for asset in high_maintenance_assets %}
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="asset-item {% if asset.annual_maintenance_cost > 1000 %}high-cost{% elif asset.annual_maintenance_cost > 500 %}medium-cost{% endif %}">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1">{{ asset.asset_tag }}</h6>
                                            <p class="mb-2 text-muted">{{ asset.name|truncatechars:25 }}</p>
                                            <small class="text-muted">
                                                <i class="fas fa-tag"></i> {{ asset.category.name }}<br>
                                                <i class="fas fa-map-marker-alt"></i> {{ asset.location|default:"No location" }}
                                            </small>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge {% if asset.annual_maintenance_cost > 1000 %}bg-danger{% elif asset.annual_maintenance_cost > 500 %}bg-warning{% else %}bg-info{% endif %}">
                                                ${{ asset.annual_maintenance_cost|floatformat:2 }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h6 class="text-success">{% trans "No High Maintenance Cost Assets" %}</h6>
                        <p class="text-muted">{% trans "All assets have reasonable maintenance costs." %}</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Replacement Cost Estimates by Category -->
    <div class="row">
        <div class="col-md-8">
            <div class="chart-container">
                <h5><i class="fas fa-chart-bar"></i> {% trans "Replacement Cost Estimates by Category" %}</h5>
                {% if replacement_estimates %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Category" %}</th>
                                    <th>{% trans "Total Assets" %}</th>
                                    <th>{% trans "Avg Age" %}</th>
                                    <th>{% trans "Due for Replacement" %}</th>
                                    <th>{% trans "Est. Total Cost" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for category in replacement_estimates %}
                                    <tr>
                                        <td>{{ category.category__name|default:"Unknown" }}</td>
                                        <td>{{ category.asset_count }}</td>
                                        <td>{{ category.avg_age|floatformat:1 }} {% trans "years" %}</td>
                                        <td>
                                            {% if category.assets_due_replacement > 0 %}
                                                <span class="badge bg-warning">{{ category.assets_due_replacement }}</span>
                                            {% else %}
                                                <span class="badge bg-success">0</span>
                                            {% endif %}
                                        </td>
                                        <td>${{ category.total_replacement_cost|floatformat:2 }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center py-4">{% trans "No replacement cost data available." %}</p>
                {% endif %}
            </div>
        </div>

        <!-- Budget Planning Summary -->
        <div class="col-md-4">
            <div class="chart-container">
                <h5><i class="fas fa-calculator"></i> {% trans "5-Year Budget Planning" %}</h5>
                {% if budget_planning %}
                    {% for year_data in budget_planning %}
                        <div class="budget-year">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ year_data.year }}</h6>
                                    <small class="text-muted">{{ year_data.assets_due }} {% trans "assets due" %}</small>
                                </div>
                                <div class="text-end">
                                    <span class="fw-bold {% if year_data.estimated_cost > 10000 %}text-danger{% elif year_data.estimated_cost > 5000 %}text-warning{% else %}text-success{% endif %}">
                                        ${{ year_data.estimated_cost|floatformat:2 }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                    
                    <div class="mt-3 pt-3 border-top">
                        <div class="d-flex justify-content-between">
                            <strong>{% trans "Total 5-Year Cost:" %}</strong>
                            <strong class="text-primary">
                                ${% widthratio budget_planning.0.estimated_cost 1 1 %}{% for year in budget_planning %}{% if not forloop.first %}{% widthratio year.estimated_cost 1 1 %}{% endif %}{% endfor %}
                            </strong>
                        </div>
                    </div>
                {% else %}
                    <p class="text-muted text-center py-4">{% trans "No budget planning data available." %}</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Recommendations -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <h5><i class="fas fa-lightbulb"></i> {% trans "Replacement Planning Recommendations" %}</h5>
                <div class="row">
                    <div class="col-md-4">
                        <div class="planning-card urgent">
                            <div class="card-body">
                                <h6 class="card-title text-danger">
                                    <i class="fas fa-exclamation-triangle"></i> {% trans "Immediate Action Required" %}
                                </h6>
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-arrow-right text-danger"></i> {% trans "Replace assets past end of life" %}</li>
                                    <li><i class="fas fa-arrow-right text-danger"></i> {% trans "Budget for high-priority replacements" %}</li>
                                    <li><i class="fas fa-arrow-right text-danger"></i> {% trans "Consider emergency procurement" %}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="planning-card warning">
                            <div class="card-body">
                                <h6 class="card-title text-warning">
                                    <i class="fas fa-clock"></i> {% trans "Plan Within 1-2 Years" %}
                                </h6>
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-arrow-right text-warning"></i> {% trans "Prepare replacement budgets" %}</li>
                                    <li><i class="fas fa-arrow-right text-warning"></i> {% trans "Research new technologies" %}</li>
                                    <li><i class="fas fa-arrow-right text-warning"></i> {% trans "Negotiate with suppliers" %}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="planning-card info">
                            <div class="card-body">
                                <h6 class="card-title text-info">
                                    <i class="fas fa-chart-line"></i> {% trans "Long-term Planning" %}
                                </h6>
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-arrow-right text-info"></i> {% trans "Develop 5-year replacement strategy" %}</li>
                                    <li><i class="fas fa-arrow-right text-info"></i> {% trans "Consider asset lifecycle optimization" %}</li>
                                    <li><i class="fas fa-arrow-right text-info"></i> {% trans "Plan for technology upgrades" %}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add any interactive functionality here
    document.addEventListener('DOMContentLoaded', function() {
        // Highlight urgent items
        const urgentItems = document.querySelectorAll('.table-danger');
        urgentItems.forEach(item => {
            item.style.animation = 'pulse 2s infinite';
        });
    });
</script>

<style>
@keyframes pulse {
    0% { background-color: rgba(220, 53, 69, 0.1); }
    50% { background-color: rgba(220, 53, 69, 0.2); }
    100% { background-color: rgba(220, 53, 69, 0.1); }
}
</style>
{% endblock %}