{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Inventory Reports" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">{% trans "Inventory Reports" %}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'accounts:dashboard' %}">{% trans "Dashboard" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">{% trans "Inventory" %}</a></li>
                        <li class="breadcrumb-item active">{% trans "Reports" %}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Summary Cards -->
    <div class="row">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-boxes text-primary mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-primary">{{ total_items|default:0 }}</h4>
                    <p class="mb-0">{% trans "Total Items" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-dollar-sign text-success mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-success">${{ total_value|default:0|floatformat:2 }}</h4>
                    <p class="mb-0">{% trans "Total Value" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-triangle text-warning mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-warning">{{ low_stock_items|default:0 }}</h4>
                    <p class="mb-0">{% trans "Low Stock Items" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-ban text-danger mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-danger">{{ out_of_stock_items|default:0 }}</h4>
                    <p class="mb-0">{% trans "Out of Stock" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Options -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        {% trans "Inventory Reports" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('stock_levels')">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">{% trans "Stock Levels Report" %}</h6>
                                <small><i class="fas fa-download"></i></small>
                            </div>
                            <p class="mb-1">{% trans "Current stock levels for all inventory items" %}</p>
                        </a>
                        
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('low_stock')">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">{% trans "Low Stock Alert Report" %}</h6>
                                <small><i class="fas fa-download"></i></small>
                            </div>
                            <p class="mb-1">{% trans "Items that need to be restocked" %}</p>
                        </a>
                        
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('valuation')">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">{% trans "Inventory Valuation Report" %}</h6>
                                <small><i class="fas fa-download"></i></small>
                            </div>
                            <p class="mb-1">{% trans "Total value of inventory by category" %}</p>
                        </a>
                        
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('movement')">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">{% trans "Inventory Movement Report" %}</h6>
                                <small><i class="fas fa-download"></i></small>
                            </div>
                            <p class="mb-1">{% trans "Items received and issued over time" %}</p>
                        </a>
                        
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('aging')">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">{% trans "Inventory Aging Report" %}</h6>
                                <small><i class="fas fa-download"></i></small>
                            </div>
                            <p class="mb-1">{% trans "Items by age and turnover rate" %}</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-filter me-2"></i>
                        {% trans "Custom Report Generator" %}
                    </h5>
                </div>
                <div class="card-body">
                    <form id="customReportForm">
                        <div class="mb-3">
                            <label class="form-label">{% trans "Report Type" %}</label>
                            <select class="form-select" name="report_type" required>
                                <option value="">{% trans "Select Report Type" %}</option>
                                <option value="summary">{% trans "Summary Report" %}</option>
                                <option value="detailed">{% trans "Detailed Report" %}</option>
                                <option value="category">{% trans "Category Report" %}</option>
                                <option value="supplier">{% trans "Supplier Report" %}</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">{% trans "Category" %}</label>
                            <select class="form-select" name="category">
                                <option value="">{% trans "All Categories" %}</option>
                                {% for category in categories %}
                                <option value="{{ category.id }}">{{ category.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">{% trans "From Date" %}</label>
                                    <input type="date" class="form-control" name="from_date">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">{% trans "To Date" %}</label>
                                    <input type="date" class="form-control" name="to_date">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">{% trans "Format" %}</label>
                            <div class="btn-group w-100" role="group">
                                <input type="radio" class="btn-check" name="format" id="pdf" value="pdf" checked>
                                <label class="btn btn-outline-primary" for="pdf">
                                    <i class="fas fa-file-pdf me-1"></i>PDF
                                </label>
                                
                                <input type="radio" class="btn-check" name="format" id="excel" value="excel">
                                <label class="btn btn-outline-success" for="excel">
                                    <i class="fas fa-file-excel me-1"></i>Excel
                                </label>
                                
                                <input type="radio" class="btn-check" name="format" id="csv" value="csv">
                                <label class="btn btn-outline-info" for="csv">
                                    <i class="fas fa-file-csv me-1"></i>CSV
                                </label>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-download me-1"></i>{% trans "Generate Report" %}
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Reports -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>
                        {% trans "Recent Reports" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Report Name" %}</th>
                                    <th>{% trans "Type" %}</th>
                                    <th>{% trans "Generated Date" %}</th>
                                    <th>{% trans "Generated By" %}</th>
                                    <th>{% trans "Format" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for report in recent_reports %}
                                <tr>
                                    <td>{{ report.name }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ report.get_type_display }}</span>
                                    </td>
                                    <td>{{ report.created_at|date:"M d, Y H:i" }}</td>
                                    <td>{{ report.created_by.get_full_name }}</td>
                                    <td>
                                        <span class="badge bg-secondary">{{ report.format|upper }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ report.file.url }}" class="btn btn-outline-primary" target="_blank">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            <button class="btn btn-outline-danger" onclick="deleteReport({{ report.id }})">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center">
                                        <div class="py-4">
                                            <i class="fas fa-file-alt text-muted mb-3" style="font-size: 3rem;"></i>
                                            <h5 class="text-muted">{% trans "No Reports Generated Yet" %}</h5>
                                            <p class="text-muted">{% trans "Generate your first inventory report using the options above." %}</p>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% csrf_token %}

<script>
// Get CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

function generateReport(reportType) {
    // Show loading
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>{% trans "Generating..." %}';
    
    // Generate predefined report
    fetch(`/inventory/reports/generate/${reportType}/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCookie('csrftoken'),
            'Content-Type': 'application/json',
        },
    })
    .then(response => {
        if (response.ok) {
            return response.blob();
        } else {
            throw new Error('Network response was not ok');
        }
    })
    .then(blob => {
        // Create download link
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `${reportType}_report_${new Date().toISOString().split('T')[0]}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        
        button.innerHTML = originalText;
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{% trans "Error generating report. Please try again." %}');
        button.innerHTML = originalText;
    });
}

function deleteReport(reportId) {
    if (confirm('{% trans "Are you sure you want to delete this report?" %}')) {
        fetch(`/inventory/reports/${reportId}/delete/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
            },
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('{% trans "Error deleting report. Please try again." %}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{% trans "Error deleting report. Please try again." %}');
        });
    }
}

// Handle custom report form submission
document.getElementById('customReportForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitButton = this.querySelector('button[type="submit"]');
    const originalText = submitButton.innerHTML;
    
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>{% trans "Generating..." %}';
    submitButton.disabled = true;
    
    fetch('/inventory/reports/custom/', {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCookie('csrftoken'),
        },
        body: formData
    })
    .then(response => {
        if (response.ok) {
            return response.blob();
        } else {
            throw new Error('Network response was not ok');
        }
    })
    .then(blob => {
        // Create download link
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        
        const format = formData.get('format');
        const reportType = formData.get('report_type');
        a.download = `${reportType}_report_${new Date().toISOString().split('T')[0]}.${format}`;
        
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        
        submitButton.innerHTML = originalText;
        submitButton.disabled = false;
        
        // Reset form
        this.reset();
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{% trans "Error generating report. Please try again." %}');
        submitButton.innerHTML = originalText;
        submitButton.disabled = false;
    });
});
</script>
{% endblock %}