{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Stock Transfer" %} - {% trans "Inventory" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">{% trans "Stock Transfer" %}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'accounts:dashboard' %}">{% trans "Dashboard" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">{% trans "Inventory" %}</a></li>
                        <li class="breadcrumb-item active">{% trans "Stock Transfer" %}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exchange-alt me-2"></i>
                        {% trans "Transfer Stock Between Locations" %}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="item" class="form-label">{% trans "Item" %}</label>
                                    <select name="item" id="item" class="form-select" required>
                                        <option value="">{% trans "Select Item" %}</option>
                                        {% for item in items %}
                                        <option value="{{ item.id }}">{{ item.name }} ({{ item.item_code }})</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="quantity" class="form-label">{% trans "Quantity" %}</label>
                                    <input type="number" name="quantity" id="quantity" class="form-control" min="1" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="from_location" class="form-label">{% trans "From Location" %}</label>
                                    <select name="from_location" id="from_location" class="form-select" required>
                                        <option value="">{% trans "Select Source Location" %}</option>
                                        {% for location in locations %}
                                        <option value="{{ location.id }}">{{ location.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="to_location" class="form-label">{% trans "To Location" %}</label>
                                    <select name="to_location" id="to_location" class="form-select" required>
                                        <option value="">{% trans "Select Destination Location" %}</option>
                                        {% for location in locations %}
                                        <option value="{{ location.id }}">{{ location.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="notes" class="form-label">{% trans "Notes" %}</label>
                                    <textarea name="notes" id="notes" class="form-control" rows="3" placeholder="{% trans 'Transfer notes or reason' %}"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-exchange-alt me-1"></i>
                                    {% trans "Transfer Stock" %}
                                </button>
                                <a href="{% url 'inventory:dashboard' %}" class="btn btn-secondary ms-2">
                                    {% trans "Cancel" %}
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}