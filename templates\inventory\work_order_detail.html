{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Work Order" %} WO-{{ work_order.id|stringformat:"04d" }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-clipboard-list"></i> {% trans "Work Order" %} WO-{{ work_order.id|stringformat:"04d" }}</h2>
                <div>
                    {% if work_order.status == 'scheduled' %}
                        <a href="{% url 'inventory:work_order_start' work_order.id %}" class="btn btn-success">
                            <i class="fas fa-play"></i> {% trans "Start Work" %}
                        </a>
                    {% endif %}
                    {% if work_order.status == 'in_progress' %}
                        <a href="{% url 'inventory:maintenance_complete' work_order.id %}" class="btn btn-warning">
                            <i class="fas fa-check"></i> {% trans "Complete Work" %}
                        </a>
                    {% endif %}
                    <a href="{% url 'inventory:work_order_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> {% trans "Back to List" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <!-- Work Order Details -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle"></i> {% trans "Work Order Details" %}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>{% trans "Work Order #:" %}</strong></td>
                                    <td>WO-{{ work_order.id|stringformat:"04d" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Asset:" %}</strong></td>
                                    <td>{{ work_order.asset.asset_tag }} - {{ work_order.asset.name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Type:" %}</strong></td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            {{ work_order.get_maintenance_type_display }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Priority:" %}</strong></td>
                                    <td>
                                        {% if work_order.maintenance_type == 'emergency' %}
                                            <span class="badge bg-danger">{% trans "High" %}</span>
                                        {% elif work_order.maintenance_type == 'corrective' %}
                                            <span class="badge bg-warning">{% trans "Medium" %}</span>
                                        {% else %}
                                            <span class="badge bg-success">{% trans "Normal" %}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>{% trans "Status:" %}</strong></td>
                                    <td>
                                        {% if work_order.status == 'in_progress' %}
                                            <span class="badge bg-warning">
                                                <i class="fas fa-spinner fa-spin"></i> {{ work_order.get_status_display }}
                                            </span>
                                        {% elif work_order.status == 'scheduled' %}
                                            <span class="badge bg-info">
                                                <i class="fas fa-clock"></i> {{ work_order.get_status_display }}
                                            </span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Scheduled Date:" %}</strong></td>
                                    <td>
                                        {{ work_order.scheduled_date }}
                                        {% if work_order.scheduled_date < today %}
                                            <br><small class="text-danger">{% trans "Overdue" %}</small>
                                        {% elif work_order.scheduled_date == today %}
                                            <br><small class="text-warning">{% trans "Due Today" %}</small>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Assigned To:" %}</strong></td>
                                    <td>{{ work_order.performed_by|default:"-" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Estimated Cost:" %}</strong></td>
                                    <td>${{ work_order.cost|floatformat:2 }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>{% trans "Work Description:" %}</h6>
                            <p class="text-muted">{{ work_order.description }}</p>
                        </div>
                    </div>
                    
                    {% if work_order.notes %}
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6>{% trans "Notes:" %}</h6>
                                <p class="text-muted">{{ work_order.notes|linebreaks }}</p>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Work Instructions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-clipboard-check"></i> {% trans "Work Instructions" %}</h5>
                </div>
                <div class="card-body">
                    <div class="checklist">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="safety-check">
                            <label class="form-check-label" for="safety-check">
                                <strong>{% trans "Safety Check" %}</strong> - {% trans "Ensure all safety protocols are followed before starting work" %}
                            </label>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="tools-check">
                            <label class="form-check-label" for="tools-check">
                                <strong>{% trans "Tools & Materials" %}</strong> - {% trans "Verify all required tools and materials are available" %}
                            </label>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="isolation-check">
                            <label class="form-check-label" for="isolation-check">
                                <strong>{% trans "Asset Isolation" %}</strong> - {% trans "Properly isolate the asset from power/utilities if required" %}
                            </label>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="work-performed">
                            <label class="form-check-label" for="work-performed">
                                <strong>{% trans "Work Performed" %}</strong> - {% trans "Complete the maintenance work as described" %}
                            </label>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="testing-check">
                            <label class="form-check-label" for="testing-check">
                                <strong>{% trans "Testing" %}</strong> - {% trans "Test asset functionality after maintenance" %}
                            </label>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="cleanup-check">
                            <label class="form-check-label" for="cleanup-check">
                                <strong>{% trans "Cleanup" %}</strong> - {% trans "Clean work area and dispose of waste properly" %}
                            </label>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="documentation-check">
                            <label class="form-check-label" for="documentation-check">
                                <strong>{% trans "Documentation" %}</strong> - {% trans "Complete work order documentation and notes" %}
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Progress Timeline -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-history"></i> {% trans "Work Progress" %}</h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">{% trans "Work Order Created" %}</h6>
                                <p class="timeline-text">
                                    {% trans "Work order scheduled for" %} {{ work_order.scheduled_date }}
                                </p>
                                <small class="text-muted">{{ work_order.created_at }}</small>
                            </div>
                        </div>
                        
                        {% if work_order.status == 'in_progress' %}
                            <div class="timeline-item">
                                <div class="timeline-marker bg-warning"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">{% trans "Work Started" %}</h6>
                                    <p class="timeline-text">
                                        {% trans "Maintenance work is currently in progress" %}
                                        {% if work_order.performed_by %}
                                            {% trans "by" %} {{ work_order.performed_by }}
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Asset Information -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6><i class="fas fa-box"></i> {% trans "Asset Information" %}</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        {% if work_order.asset.image %}
                            <img src="{{ work_order.asset.image.url }}" alt="{{ work_order.asset.name }}" 
                                 class="img-fluid rounded" style="max-height: 150px;">
                        {% else %}
                            <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                 style="height: 150px;">
                                <i class="fas fa-box fa-3x text-muted"></i>
                            </div>
                        {% endif %}
                    </div>
                    
                    <table class="table table-sm">
                        <tr>
                            <td><strong>{% trans "Asset Tag:" %}</strong></td>
                            <td>{{ work_order.asset.asset_tag }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Name:" %}</strong></td>
                            <td>{{ work_order.asset.name }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Category:" %}</strong></td>
                            <td>{{ work_order.asset.category.name }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Location:" %}</strong></td>
                            <td>{{ work_order.asset.location|default:"N/A" }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Status:" %}</strong></td>
                            <td>
                                <span class="badge bg-{% if work_order.asset.status == 'active' %}success{% elif work_order.asset.status == 'maintenance' %}warning{% else %}secondary{% endif %}">
                                    {{ work_order.asset.get_status_display }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Condition:" %}</strong></td>
                            <td>
                                <span class="badge bg-{% if work_order.asset.condition == 'excellent' %}success{% elif work_order.asset.condition == 'good' %}info{% elif work_order.asset.condition == 'fair' %}warning{% else %}danger{% endif %}">
                                    {{ work_order.asset.get_condition_display }}
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Supplier Information -->
            {% if work_order.supplier %}
                <div class="card mb-3">
                    <div class="card-header">
                        <h6><i class="fas fa-truck"></i> {% trans "Supplier Information" %}</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>{% trans "Name:" %}</strong></td>
                                <td>{{ work_order.supplier.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "Contact:" %}</strong></td>
                                <td>{{ work_order.supplier.contact_person|default:"-" }}</td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "Phone:" %}</strong></td>
                                <td>{{ work_order.supplier.phone|default:"-" }}</td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "Email:" %}</strong></td>
                                <td>{{ work_order.supplier.email|default:"-" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            {% endif %}

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-bolt"></i> {% trans "Quick Actions" %}</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        {% if work_order.status == 'scheduled' %}
                            <a href="{% url 'inventory:work_order_start' work_order.id %}" class="btn btn-success btn-sm">
                                <i class="fas fa-play"></i> {% trans "Start Work Order" %}
                            </a>
                        {% endif %}
                        
                        {% if work_order.status == 'in_progress' %}
                            <a href="{% url 'inventory:maintenance_complete' work_order.id %}" class="btn btn-warning btn-sm">
                                <i class="fas fa-check"></i> {% trans "Complete Work" %}
                            </a>
                        {% endif %}
                        
                        <a href="{% url 'inventory:maintenance_history' work_order.asset.id %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-history"></i> {% trans "Maintenance History" %}
                        </a>
                        
                        <a href="{% url 'inventory:maintenance_analytics' %}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-chart-bar"></i> {% trans "View Analytics" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }
    
    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }
    
    .timeline-marker {
        position: absolute;
        left: -22px;
        top: 5px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 0 0 2px #dee2e6;
    }
    
    .timeline-content {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        border-left: 3px solid #007bff;
    }
    
    .timeline-title {
        margin-bottom: 5px;
        font-size: 14px;
        font-weight: 600;
    }
    
    .timeline-text {
        margin-bottom: 5px;
        font-size: 13px;
    }
    
    .checklist .form-check {
        padding: 10px;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        background: #f8f9fa;
    }
    
    .checklist .form-check:hover {
        background: #e9ecef;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Save checklist progress to localStorage
    document.addEventListener('DOMContentLoaded', function() {
        const workOrderId = '{{ work_order.id }}';
        const checkboxes = document.querySelectorAll('.checklist input[type="checkbox"]');
        
        // Load saved progress
        checkboxes.forEach(checkbox => {
            const saved = localStorage.getItem(`wo_${workOrderId}_${checkbox.id}`);
            if (saved === 'true') {
                checkbox.checked = true;
            }
        });
        
        // Save progress on change
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                localStorage.setItem(`wo_${workOrderId}_${this.id}`, this.checked);
            });
        });
    });
</script>
{% endblock %}