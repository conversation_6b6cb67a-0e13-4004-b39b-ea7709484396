{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Work Orders" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-clipboard-list"></i> {% trans "Work Orders" %}</h2>
                <div>
                    <a href="{% url 'inventory:maintenance_schedule' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> {% trans "Schedule Maintenance" %}
                    </a>
                    <a href="{% url 'inventory:maintenance_dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-tachometer-alt"></i> {% trans "Dashboard" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="status" class="form-label">{% trans "Status" %}</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">{% trans "All Statuses" %}</option>
                        <option value="scheduled" {% if current_status == 'scheduled' %}selected{% endif %}>{% trans "Scheduled" %}</option>
                        <option value="in_progress" {% if current_status == 'in_progress' %}selected{% endif %}>{% trans "In Progress" %}</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="type" class="form-label">{% trans "Type" %}</label>
                    <select name="type" id="type" class="form-select">
                        <option value="">{% trans "All Types" %}</option>
                        <option value="preventive" {% if current_type == 'preventive' %}selected{% endif %}>{% trans "Preventive" %}</option>
                        <option value="corrective" {% if current_type == 'corrective' %}selected{% endif %}>{% trans "Corrective" %}</option>
                        <option value="emergency" {% if current_type == 'emergency' %}selected{% endif %}>{% trans "Emergency" %}</option>
                        <option value="upgrade" {% if current_type == 'upgrade' %}selected{% endif %}>{% trans "Upgrade" %}</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-outline-secondary">
                            <i class="fas fa-filter"></i> {% trans "Filter" %}
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Work Orders Table -->
    <div class="card">
        <div class="card-body">
            {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% trans "Work Order #" %}</th>
                                <th>{% trans "Asset" %}</th>
                                <th>{% trans "Type" %}</th>
                                <th>{% trans "Status" %}</th>
                                <th>{% trans "Scheduled Date" %}</th>
                                <th>{% trans "Priority" %}</th>
                                <th>{% trans "Assigned To" %}</th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for work_order in page_obj %}
                                <tr>
                                    <td>
                                        <strong>WO-{{ work_order.id|stringformat:"04d" }}</strong>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ work_order.asset.asset_tag }}</strong>
                                            <br>
                                            <small class="text-muted">{{ work_order.asset.name }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            {{ work_order.get_maintenance_type_display }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if work_order.status == 'in_progress' %}
                                            <span class="badge bg-warning">
                                                <i class="fas fa-spinner fa-spin"></i> {{ work_order.get_status_display }}
                                            </span>
                                        {% elif work_order.status == 'scheduled' %}
                                            <span class="badge bg-info">
                                                <i class="fas fa-clock"></i> {{ work_order.get_status_display }}
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {{ work_order.scheduled_date }}
                                        {% if work_order.scheduled_date < today %}
                                            <br><small class="text-danger">{% trans "Overdue" %}</small>
                                        {% elif work_order.scheduled_date == today %}
                                            <br><small class="text-warning">{% trans "Due Today" %}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if work_order.maintenance_type == 'emergency' %}
                                            <span class="badge bg-danger">{% trans "High" %}</span>
                                        {% elif work_order.maintenance_type == 'corrective' %}
                                            <span class="badge bg-warning">{% trans "Medium" %}</span>
                                        {% else %}
                                            <span class="badge bg-success">{% trans "Normal" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ work_order.performed_by|default:"-" }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'inventory:work_order_detail' work_order.id %}" 
                                               class="btn btn-sm btn-outline-primary" title="{% trans 'View Details' %}">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if work_order.status == 'scheduled' %}
                                                <a href="{% url 'inventory:work_order_start' work_order.id %}" 
                                                   class="btn btn-sm btn-success" title="{% trans 'Start Work' %}">
                                                    <i class="fas fa-play"></i>
                                                </a>
                                            {% endif %}
                                            {% if work_order.status == 'in_progress' %}
                                                <a href="{% url 'inventory:maintenance_complete' work_order.id %}" 
                                                   class="btn btn-sm btn-warning" title="{% trans 'Complete' %}">
                                                    <i class="fas fa-check"></i>
                                                </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <nav aria-label="{% trans 'Work order pagination' %}">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}">
                                        {% trans "First" %}
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}">
                                        {% trans "Previous" %}
                                    </a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}">
                                        {% trans "Next" %}
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}">
                                        {% trans "Last" %}
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">{% trans "No work orders found" %}</h5>
                    <p class="text-muted">{% trans "All maintenance work is up to date or schedule new maintenance to create work orders." %}</p>
                    <a href="{% url 'inventory:maintenance_schedule' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> {% trans "Schedule Maintenance" %}
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .table tr.table-danger {
        background-color: rgba(220, 53, 69, 0.1);
    }
    .table tr.table-warning {
        background-color: rgba(255, 193, 7, 0.1);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Add row highlighting based on due dates
    document.addEventListener('DOMContentLoaded', function() {
        const rows = document.querySelectorAll('tbody tr');
        const today = new Date();
        
        rows.forEach(row => {
            const dateCell = row.cells[4]; // Scheduled date column
            if (dateCell) {
                const dateText = dateCell.textContent.trim().split('\n')[0];
                const scheduledDate = new Date(dateText);
                
                if (scheduledDate < today) {
                    row.classList.add('table-danger');
                } else if (scheduledDate.toDateString() === today.toDateString()) {
                    row.classList.add('table-warning');
                }
            }
        });
    });
</script>
{% endblock %}