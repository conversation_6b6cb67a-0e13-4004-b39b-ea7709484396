{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Start Work Order" %} WO-{{ work_order.id|stringformat:"04d" }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-play-circle"></i> {% trans "Start Work Order" %} WO-{{ work_order.id|stringformat:"04d" }}</h2>
                <a href="{% url 'inventory:work_order_detail' work_order.id %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> {% trans "Back to Work Order" %}
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-play-circle"></i> {% trans "Confirm Work Order Start" %}</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        {% trans "Starting this work order will:" %}
                        <ul class="mb-0 mt-2">
                            <li>{% trans "Change the work order status to 'In Progress'" %}</li>
                            <li>{% trans "Update the asset status to 'Under Maintenance'" %}</li>
                            <li>{% trans "Record the start time for tracking purposes" %}</li>
                        </ul>
                    </div>

                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-4">
                            <h6>{% trans "Pre-Work Safety Checklist" %}</h6>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="safety-protocols" required>
                                <label class="form-check-label" for="safety-protocols">
                                    {% trans "I have reviewed and understand all safety protocols for this work" %}
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="tools-available" required>
                                <label class="form-check-label" for="tools-available">
                                    {% trans "All required tools and materials are available and in good condition" %}
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="area-secured" required>
                                <label class="form-check-label" for="area-secured">
                                    {% trans "Work area has been properly secured and isolated if necessary" %}
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="permissions-obtained" required>
                                <label class="form-check-label" for="permissions-obtained">
                                    {% trans "All necessary permissions and approvals have been obtained" %}
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="start_notes" class="form-label">{% trans "Start Notes (Optional)" %}</label>
                            <textarea name="start_notes" id="start_notes" class="form-control" rows="3" 
                                      placeholder="{% trans 'Any additional notes or observations before starting work...' %}"></textarea>
                        </div>

                        <div class="d-flex justify-content-end">
                            <a href="{% url 'inventory:work_order_detail' work_order.id %}" class="btn btn-secondary me-2">
                                {% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-success" id="start-btn" disabled>
                                <i class="fas fa-play"></i> {% trans "Start Work Order" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Work Order Summary -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6><i class="fas fa-clipboard-list"></i> {% trans "Work Order Summary" %}</h6>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <td><strong>{% trans "Work Order #:" %}</strong></td>
                            <td>WO-{{ work_order.id|stringformat:"04d" }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Asset:" %}</strong></td>
                            <td>{{ work_order.asset.asset_tag }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Type:" %}</strong></td>
                            <td>
                                <span class="badge bg-secondary">
                                    {{ work_order.get_maintenance_type_display }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Priority:" %}</strong></td>
                            <td>
                                {% if work_order.maintenance_type == 'emergency' %}
                                    <span class="badge bg-danger">{% trans "High" %}</span>
                                {% elif work_order.maintenance_type == 'corrective' %}
                                    <span class="badge bg-warning">{% trans "Medium" %}</span>
                                {% else %}
                                    <span class="badge bg-success">{% trans "Normal" %}</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Scheduled:" %}</strong></td>
                            <td>{{ work_order.scheduled_date }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Estimated Cost:" %}</strong></td>
                            <td>${{ work_order.cost|floatformat:2 }}</td>
                        </tr>
                    </table>
                    
                    <div class="mt-3">
                        <h6>{% trans "Description:" %}</h6>
                        <p class="text-muted small">{{ work_order.description }}</p>
                    </div>
                </div>
            </div>

            <!-- Asset Information -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6><i class="fas fa-box"></i> {% trans "Asset Information" %}</h6>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <td><strong>{% trans "Name:" %}</strong></td>
                            <td>{{ work_order.asset.name }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Location:" %}</strong></td>
                            <td>{{ work_order.asset.location|default:"N/A" }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Current Status:" %}</strong></td>
                            <td>
                                <span class="badge bg-{% if work_order.asset.status == 'active' %}success{% elif work_order.asset.status == 'maintenance' %}warning{% else %}secondary{% endif %}">
                                    {{ work_order.asset.get_status_display }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "Condition:" %}</strong></td>
                            <td>
                                <span class="badge bg-{% if work_order.asset.condition == 'excellent' %}success{% elif work_order.asset.condition == 'good' %}info{% elif work_order.asset.condition == 'fair' %}warning{% else %}danger{% endif %}">
                                    {{ work_order.asset.get_condition_display }}
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Safety Guidelines -->
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-shield-alt"></i> {% trans "Safety Guidelines" %}</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-danger">{% trans "Emergency Procedures" %}</h6>
                        <small class="text-muted">
                            {% trans "Know the location of emergency stops, fire extinguishers, and first aid equipment." %}
                        </small>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-warning">{% trans "Personal Protective Equipment" %}</h6>
                        <small class="text-muted">
                            {% trans "Wear appropriate PPE including safety glasses, gloves, and protective clothing." %}
                        </small>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-info">{% trans "Lockout/Tagout" %}</h6>
                        <small class="text-muted">
                            {% trans "Follow proper lockout/tagout procedures when working on powered equipment." %}
                        </small>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-success">{% trans "Communication" %}</h6>
                        <small class="text-muted">
                            {% trans "Maintain communication with supervisors and report any safety concerns immediately." %}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const checkboxes = document.querySelectorAll('input[type="checkbox"][required]');
        const startBtn = document.getElementById('start-btn');
        
        function checkAllBoxes() {
            const allChecked = Array.from(checkboxes).every(checkbox => checkbox.checked);
            startBtn.disabled = !allChecked;
        }
        
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', checkAllBoxes);
        });
        
        // Initial check
        checkAllBoxes();
    });
</script>
{% endblock %}