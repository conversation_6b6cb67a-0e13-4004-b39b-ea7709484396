{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Borrowing History" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">{% trans "Borrowing History" %}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'accounts:dashboard' %}">{% trans "Dashboard" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'library:dashboard' %}">{% trans "Library" %}</a></li>
                        <li class="breadcrumb-item active">{% trans "Borrowing History" %}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">{% trans "Student" %}</label>
                            <select name="student" class="form-select">
                                <option value="">{% trans "All Students" %}</option>
                                {% for student in students %}
                                <option value="{{ student.id }}" {% if request.GET.student == student.id|stringformat:"s" %}selected{% endif %}>
                                    {{ student.full_name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">{% trans "Book" %}</label>
                            <input type="text" name="book" class="form-control" value="{{ request.GET.book }}" placeholder="{% trans 'Search by book title' %}">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">{% trans "From Date" %}</label>
                            <input type="date" name="from_date" class="form-control" value="{{ request.GET.from_date }}">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">{% trans "To Date" %}</label>
                            <input type="date" name="to_date" class="form-control" value="{{ request.GET.to_date }}">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>{% trans "Filter" %}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- History Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>
                        {% trans "Complete Borrowing History" %}
                    </h5>
                    <button class="btn btn-success" onclick="exportHistory()">
                        <i class="fas fa-download me-1"></i>{% trans "Export History" %}
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Student" %}</th>
                                    <th>{% trans "Book" %}</th>
                                    <th>{% trans "Issue Date" %}</th>
                                    <th>{% trans "Due Date" %}</th>
                                    <th>{% trans "Return Date" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Fine" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for issue in borrowing_history %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-2">
                                                <span class="avatar-title bg-primary rounded-circle">
                                                    {{ issue.student.first_name|first }}{{ issue.student.last_name|first }}
                                                </span>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ issue.student.full_name }}</h6>
                                                <small class="text-muted">{{ issue.student.student_id }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <h6 class="mb-0">{{ issue.book.title }}</h6>
                                            <small class="text-muted">{{ issue.book.author }}</small>
                                        </div>
                                    </td>
                                    <td>{{ issue.issue_date }}</td>
                                    <td>{{ issue.due_date }}</td>
                                    <td>
                                        {% if issue.return_date %}
                                            {{ issue.return_date }}
                                        {% else %}
                                            <span class="text-muted">{% trans "Not returned" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if issue.return_date %}
                                            {% if issue.return_date <= issue.due_date %}
                                                <span class="badge bg-success">{% trans "Returned on time" %}</span>
                                            {% else %}
                                                <span class="badge bg-warning">{% trans "Returned late" %}</span>
                                            {% endif %}
                                        {% else %}
                                            {% if issue.due_date < today %}
                                                <span class="badge bg-danger">{% trans "Overdue" %}</span>
                                            {% else %}
                                                <span class="badge bg-info">{% trans "Borrowed" %}</span>
                                            {% endif %}
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if issue.fine_amount %}
                                            <span class="text-warning fw-bold">${{ issue.fine_amount }}</span>
                                        {% else %}
                                            <span class="text-muted">{% trans "No fine" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="viewDetails({{ issue.id }})">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            {% if not issue.return_date %}
                                            <button class="btn btn-outline-success" onclick="returnBook({{ issue.id }})">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center">
                                        <div class="py-4">
                                            <i class="fas fa-book text-muted mb-3" style="font-size: 3rem;"></i>
                                            <h5 class="text-muted">{% trans "No borrowing history found" %}</h5>
                                            <p class="text-muted">{% trans "No books have been borrowed yet." %}</p>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if is_paginated %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1">{% trans "First" %}</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                                </li>
                            {% endif %}
                            
                            <li class="page-item active">
                                <span class="page-link">{{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}</span>
                            </li>
                            
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">{% trans "Last" %}</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% csrf_token %}

<script>
// Get CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

function viewDetails(issueId) {
    // Implementation for viewing issue details
    window.open(`/library/issues/${issueId}/`, '_blank');
}

function returnBook(issueId) {
    if (confirm('{% trans "Mark this book as returned?" %}')) {
        // Show loading state
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;
        
        // AJAX call to return book using the correct API endpoint
        const formData = new FormData();
        formData.append('borrowing_id', issueId);
        
        fetch('/library/api/return/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
            },
            body: formData
        })
        .then(response => {
            if (response.ok) {
                return response.json();
            } else {
                throw new Error('Network response was not ok');
            }
        })
        .then(data => {
            if (data.success) {
                alert('{% trans "Book returned successfully!" %}');
                location.reload();
            } else {
                alert(data.message || '{% trans "Error returning book. Please try again." %}');
                button.innerHTML = originalText;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{% trans "Error returning book. Please try again." %}');
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
}

function exportHistory() {
    // Get current filter parameters
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'true');
    window.open(`/library/borrowing/history/?${params.toString()}`, '_blank');
}

// Add event listeners when document is ready
document.addEventListener('DOMContentLoaded', function() {
    // Remove onclick attributes and add proper event listeners
    document.querySelectorAll('button[onclick*="returnBook"]').forEach(button => {
        const onclickAttr = button.getAttribute('onclick');
        const issueId = onclickAttr.match(/\d+/)[0];
        button.removeAttribute('onclick');
        button.addEventListener('click', function(e) {
            e.preventDefault();
            returnBook(issueId);
        });
    });
    
    document.querySelectorAll('button[onclick*="viewDetails"]').forEach(button => {
        const onclickAttr = button.getAttribute('onclick');
        const issueId = onclickAttr.match(/\d+/)[0];
        button.removeAttribute('onclick');
        button.addEventListener('click', function(e) {
            e.preventDefault();
            viewDetails(issueId);
        });
    });
});
</script>
{% endblock %}