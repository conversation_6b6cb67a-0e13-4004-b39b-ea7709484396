{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Borrowing System" %} - {% trans "Library" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">{% trans "Borrowing System" %}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'accounts:dashboard' %}">{% trans "Dashboard" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'library:dashboard' %}">{% trans "Library" %}</a></li>
                        <li class="breadcrumb-item active">{% trans "Borrowing" %}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <button type="button" class="btn btn-primary btn-lg w-100 mb-2" id="borrowBookBtn">
                                <i class="fas fa-plus me-2"></i>
                                {% trans "Borrow Book" %}
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-success btn-lg w-100 mb-2" id="returnBookBtn">
                                <i class="fas fa-undo me-2"></i>
                                {% trans "Return Book" %}
                            </button>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'library:borrowing_history' %}" class="btn btn-info btn-lg w-100 mb-2">
                                <i class="fas fa-history me-2"></i>
                                {% trans "Borrowing History" %}
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'library:overdue_books' %}" class="btn btn-warning btn-lg w-100 mb-2">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                {% trans "Overdue Books" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Borrowings -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-book-open me-2"></i>
                        {% trans "Active Borrowings" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if active_borrowings %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>{% trans "Borrower" %}</th>
                                        <th>{% trans "Book" %}</th>
                                        <th>{% trans "Borrow Date" %}</th>
                                        <th>{% trans "Due Date" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for borrowing in active_borrowings %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-2">
                                                    <div class="avatar-title bg-primary rounded-circle">
                                                        {{ borrowing.borrower_name|first }}
                                                    </div>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">{{ borrowing.borrower_name }}</h6>
                                                    <small class="text-muted">{{ borrowing.borrower_id }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <h6 class="mb-0">{{ borrowing.book.title }}</h6>
                                                <small class="text-muted">{{ borrowing.book.author }}</small>
                                            </div>
                                        </td>
                                        <td>{{ borrowing.borrow_date|date:"M d, Y" }}</td>
                                        <td>
                                            {{ borrowing.due_date|date:"M d, Y" }}
                                            {% if borrowing.is_overdue %}
                                                <span class="badge bg-danger ms-1">{% trans "Overdue" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if borrowing.status == 'active' %}
                                                <span class="badge bg-success">{% trans "Active" %}</span>
                                            {% elif borrowing.status == 'overdue' %}
                                                <span class="badge bg-danger">{% trans "Overdue" %}</span>
                                            {% elif borrowing.status == 'renewed' %}
                                                <span class="badge bg-info">{% trans "Renewed" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <button type="button" class="btn btn-outline-success return-book-btn" 
                                                        data-borrowing-id="{{ borrowing.id }}"
                                                        data-book-title="{{ borrowing.book.title }}"
                                                        title="{% trans 'Return Book' %}">
                                                    <i class="fas fa-undo"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-info renew-book-btn" 
                                                        data-borrowing-id="{{ borrowing.id }}"
                                                        data-book-title="{{ borrowing.book.title }}"
                                                        title="{% trans 'Renew' %}">
                                                    <i class="fas fa-sync"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-book-open fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{% trans "No active borrowings" %}</h5>
                            <p class="text-muted">{% trans "All books have been returned or no books are currently borrowed." %}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Returns -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>
                        {% trans "Recent Returns" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_returns %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>{% trans "Borrower" %}</th>
                                        <th>{% trans "Book" %}</th>
                                        <th>{% trans "Return Date" %}</th>
                                        <th>{% trans "Condition" %}</th>
                                        <th>{% trans "Fine" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for return in recent_returns %}
                                    <tr>
                                        <td>{{ return.borrower_name }}</td>
                                        <td>{{ return.book.title }}</td>
                                        <td>{{ return.return_date|date:"M d, Y" }}</td>
                                        <td>
                                            {% if return.return_condition == 'good' %}
                                                <span class="badge bg-success">{% trans "Good" %}</span>
                                            {% elif return.return_condition == 'damaged' %}
                                                <span class="badge bg-warning">{% trans "Damaged" %}</span>
                                            {% elif return.return_condition == 'lost' %}
                                                <span class="badge bg-danger">{% trans "Lost" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if return.fine_amount %}
                                                <span class="text-danger">${{ return.fine_amount }}</span>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <p class="text-muted">{% trans "No recent returns" %}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
}
</style>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced error handling and user feedback for library operations
    
    // Get CSRF token helper
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
    
    // Borrow Book Modal
    const borrowBookBtn = document.getElementById('borrowBookBtn');
    if (borrowBookBtn) {
        borrowBookBtn.addEventListener('click', function() {
            showBorrowBookModal();
        });
    }
    
    // Return Book buttons with enhanced error handling
    const returnBookBtns = document.querySelectorAll('.return-book-btn');
    returnBookBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const borrowingId = this.getAttribute('data-borrowing-id');
            const bookTitle = this.getAttribute('data-book-title');
            returnBook(borrowingId, bookTitle, this);
        });
    });
    
    // Renew Book buttons with enhanced error handling
    const renewBookBtns = document.querySelectorAll('.renew-book-btn');
    renewBookBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const borrowingId = this.getAttribute('data-borrowing-id');
            const bookTitle = this.getAttribute('data-book-title');
            renewBook(borrowingId, bookTitle, this);
        });
    });
    
    function showBorrowBookModal() {
        // Create modal HTML
        const modalHtml = `
            <div class="modal fade" id="borrowBookModal" tabindex="-1" aria-labelledby="borrowBookModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="borrowBookModalLabel">
                                <i class="fas fa-plus me-2"></i>{% trans "Borrow Book" %}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form id="borrowBookForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="bookSearch" class="form-label">{% trans "Search Book" %}</label>
                                            <input type="text" class="form-control" id="bookSearch" 
                                                   placeholder="{% trans 'Enter book title, ISBN, or barcode' %}">
                                            <div id="bookSearchResults" class="mt-2"></div>
                                        </div>
                                        <input type="hidden" id="selectedBookId" name="book_id">
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="borrowerType" class="form-label">{% trans "Borrower Type" %}</label>
                                            <select class="form-select" id="borrowerType" name="borrower_type" required>
                                                <option value="">{% trans "Select type" %}</option>
                                                <option value="student">{% trans "Student" %}</option>
                                                <option value="teacher">{% trans "Teacher" %}</option>
                                                <option value="staff">{% trans "Staff" %}</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="borrowerId" class="form-label">{% trans "Borrower ID" %}</label>
                                            <input type="text" class="form-control" id="borrowerId" name="borrower_id" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="borrowerName" class="form-label">{% trans "Borrower Name" %}</label>
                                            <input type="text" class="form-control" id="borrowerName" name="borrower_name" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="borrowerEmail" class="form-label">{% trans "Email" %}</label>
                                            <input type="email" class="form-control" id="borrowerEmail" name="borrower_email">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="borrowerPhone" class="form-label">{% trans "Phone" %}</label>
                                            <input type="tel" class="form-control" id="borrowerPhone" name="borrower_phone">
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                            <button type="button" class="btn btn-primary" id="confirmBorrowBtn">
                                <i class="fas fa-check me-2"></i>{% trans "Borrow Book" %}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Remove existing modal if any
        const existingModal = document.getElementById('borrowBookModal');
        if (existingModal) {
            existingModal.remove();
        }
        
        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // Initialize modal
        const modal = new bootstrap.Modal(document.getElementById('borrowBookModal'));
        modal.show();
        
        // Setup book search with enhanced error handling
        setupBookSearch();
        
        // Setup form submission with comprehensive validation
        document.getElementById('confirmBorrowBtn').addEventListener('click', function() {
            submitBorrowForm();
        });
    }
    
    function setupBookSearch() {
        const bookSearch = document.getElementById('bookSearch');
        const searchResults = document.getElementById('bookSearchResults');
        let searchTimeout;
        
        bookSearch.addEventListener('input', function() {
            const query = this.value.trim();
            
            clearTimeout(searchTimeout);
            
            if (query.length < 2) {
                searchResults.innerHTML = '';
                return;
            }
            
            searchTimeout = setTimeout(async () => {
                try {
                    const response = await fetch(`/library/api/search/?q=${encodeURIComponent(query)}&type=books`, {
                        headers: {
                            'X-CSRFToken': getCookie('csrftoken')
                        }
                    });
                    
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    
                    const data = await response.json();
                    displayBookSearchResults(data.results);
                    
                } catch (error) {
                    console.error('Search error:', error);
                    
                    let errorMessage = '{% trans "Search failed. Please try again." %}';
                    if (error.message.includes('Failed to fetch')) {
                        errorMessage = '{% trans "Network connection failed. Please check your internet connection." %}';
                    } else if (error.message.includes('HTTP 500')) {
                        errorMessage = '{% trans "Server error occurred. Please try again later." %}';
                    }
                    
                    searchResults.innerHTML = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>${errorMessage}</div>`;
                    
                    // Show toast notification for better user feedback
                    if (window.toast) {
                        window.toast.error(errorMessage);
                    }
                }
            }, 300);
        });
    }
    
    function displayBookSearchResults(books) {
        const searchResults = document.getElementById('bookSearchResults');
        
        if (books.length === 0) {
            searchResults.innerHTML = '<div class="alert alert-info">{% trans "No books found." %}</div>';
            return;
        }
        
        let html = '<div class="list-group">';
        books.forEach(book => {
            html += `
                <a href="#" class="list-group-item list-group-item-action book-result" 
                   data-book-id="${book.id}" data-book-title="${book.title}">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">${book.title}</h6>
                        <small class="${book.available ? 'text-success' : 'text-danger'}">
                            ${book.available ? '{% trans "Available" %}' : '{% trans "Not Available" %}'}
                        </small>
                    </div>
                    <p class="mb-1">${book.authors}</p>
                    <small>ISBN: ${book.isbn || 'N/A'} | Barcode: ${book.barcode || 'N/A'}</small>
                </a>
            `;
        });
        html += '</div>';
        
        searchResults.innerHTML = html;
        
        // Add click handlers for book selection
        document.querySelectorAll('.book-result').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const bookId = this.getAttribute('data-book-id');
                const bookTitle = this.getAttribute('data-book-title');
                
                document.getElementById('selectedBookId').value = bookId;
                document.getElementById('bookSearch').value = bookTitle;
                searchResults.innerHTML = '';
            });
        });
    }
    
    async function submitBorrowForm() {
        const form = document.getElementById('borrowBookForm');
        const formData = new FormData(form);
        const confirmBtn = document.getElementById('confirmBorrowBtn');
        
        // Enhanced form validation
        const requiredFields = [
            { name: 'book_id', label: '{% trans "Book" %}' },
            { name: 'borrower_type', label: '{% trans "Borrower Type" %}' },
            { name: 'borrower_id', label: '{% trans "Borrower ID" %}' },
            { name: 'borrower_name', label: '{% trans "Borrower Name" %}' }
        ];
        
        const missingFields = [];
        for (const field of requiredFields) {
            if (!formData.get(field.name)) {
                missingFields.push(field.label);
            }
        }
        
        if (missingFields.length > 0) {
            const message = `{% trans "Please fill in the following required fields" %}: ${missingFields.join(', ')}`;
            if (window.toast) {
                window.toast.error(message);
            }
            return;
        }
        
        // Use button state manager for better UX
        if (window.buttonManager) {
            window.buttonManager.setLoading(confirmBtn, '{% trans "Processing..." %}');
        } else {
            // Fallback for loading state
            const originalText = confirmBtn.innerHTML;
            confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>{% trans "Processing..." %}';
            confirmBtn.disabled = true;
        }
        
        try {
            const response = await fetch('/library/api/borrow/', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                },
                body: formData
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            if (data.success) {
                // Show success feedback
                if (window.toast) {
                    window.toast.success(data.message);
                }
                
                if (window.buttonManager) {
                    window.buttonManager.setSuccess(confirmBtn, '{% trans "Success!" %}');
                }
                
                // Close modal and refresh page
                bootstrap.Modal.getInstance(document.getElementById('borrowBookModal')).hide();
                setTimeout(() => location.reload(), 1000);
                
            } else {
                throw new Error(data.error || '{% trans "Failed to borrow book." %}');
            }
            
        } catch (error) {
            console.error('Borrow error:', error);
            
            // Determine error message
            let errorMessage = '{% trans "An error occurred. Please try again." %}';
            
            if (error.message.includes('Failed to fetch')) {
                errorMessage = '{% trans "Network connection failed. Please check your internet connection." %}';
            } else if (error.message.includes('HTTP 500')) {
                errorMessage = '{% trans "Server error occurred. Please try again later." %}';
            } else if (error.message.includes('HTTP 403')) {
                errorMessage = '{% trans "Access denied. You may not have permission for this action." %}';
            } else if (error.message.includes('HTTP 400')) {
                errorMessage = '{% trans "Invalid request. Please check your input and try again." %}';
            } else if (error.message && !error.message.includes('HTTP')) {
                errorMessage = error.message;
            }
            
            // Show error feedback
            if (window.toast) {
                window.toast.error(errorMessage);
            }
            
            if (window.buttonManager) {
                window.buttonManager.setError(confirmBtn, '{% trans "Error" %}');
            } else {
                // Restore button state
                confirmBtn.innerHTML = originalText;
                confirmBtn.disabled = false;
            }
        }
    }
    
    async function returnBook(borrowingId, bookTitle, button) {
        // Enhanced confirmation dialog
        const confirmMessage = `{% trans "Are you sure you want to return" %} "${bookTitle}"?\n\n{% trans "This action cannot be undone." %}`;
        
        if (!confirm(confirmMessage)) {
            return;
        }
        
        // Use button state manager for better UX
        if (window.buttonManager) {
            window.buttonManager.setLoading(button, '{% trans "Returning..." %}');
        } else {
            // Fallback for loading state
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            button.disabled = true;
        }
        
        try {
            const formData = new FormData();
            formData.append('borrowing_id', borrowingId);
            
            const response = await fetch('/library/api/return/', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                },
                body: formData
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            if (data.success) {
                // Show success feedback
                if (window.toast) {
                    window.toast.success(data.message);
                }
                
                if (window.buttonManager) {
                    window.buttonManager.setSuccess(button, '{% trans "Returned!" %}');
                }
                
                // Refresh page after delay
                setTimeout(() => location.reload(), 1000);
                
            } else {
                throw new Error(data.error || '{% trans "Failed to return book." %}');
            }
            
        } catch (error) {
            console.error('Return error:', error);
            
            // Determine error message
            let errorMessage = '{% trans "An error occurred. Please try again." %}';
            
            if (error.message.includes('Failed to fetch')) {
                errorMessage = '{% trans "Network connection failed. Please check your internet connection." %}';
            } else if (error.message.includes('HTTP 500')) {
                errorMessage = '{% trans "Server error occurred. Please try again later." %}';
            } else if (error.message.includes('HTTP 403')) {
                errorMessage = '{% trans "Access denied. You may not have permission for this action." %}';
            } else if (error.message.includes('HTTP 404')) {
                errorMessage = '{% trans "Borrowing record not found. The book may have already been returned." %}';
            } else if (error.message && !error.message.includes('HTTP')) {
                errorMessage = error.message;
            }
            
            // Show error feedback
            if (window.toast) {
                window.toast.error(errorMessage);
            }
            
            if (window.buttonManager) {
                window.buttonManager.setError(button, '{% trans "Error" %}');
            } else {
                // Restore button state
                button.innerHTML = originalText;
                button.disabled = false;
            }
        }
    }
    
    async function renewBook(borrowingId, bookTitle, button) {
        // Enhanced confirmation dialog with renewal information
        const confirmMessage = `{% trans "Are you sure you want to renew" %} "${bookTitle}"?\n\n{% trans "This will extend the due date by the standard renewal period." %}`;
        
        if (!confirm(confirmMessage)) {
            return;
        }
        
        // Use button state manager for better UX
        if (window.buttonManager) {
            window.buttonManager.setLoading(button, '{% trans "Renewing..." %}');
        } else {
            // Fallback for loading state
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            button.disabled = true;
        }
        
        try {
            const formData = new FormData();
            formData.append('borrowing_id', borrowingId);
            
            const response = await fetch('/library/api/renew/', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                },
                body: formData
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            if (data.success) {
                // Show success feedback with new due date if available
                let successMessage = data.message;
                if (data.new_due_date) {
                    successMessage += ` {% trans "New due date" %}: ${data.new_due_date}`;
                }
                
                if (window.toast) {
                    window.toast.success(successMessage);
                }
                
                if (window.buttonManager) {
                    window.buttonManager.setSuccess(button, '{% trans "Renewed!" %}');
                }
                
                // Refresh page after delay
                setTimeout(() => location.reload(), 1000);
                
            } else {
                throw new Error(data.error || '{% trans "Failed to renew book." %}');
            }
            
        } catch (error) {
            console.error('Renew error:', error);
            
            // Determine error message
            let errorMessage = '{% trans "An error occurred. Please try again." %}';
            
            if (error.message.includes('Failed to fetch')) {
                errorMessage = '{% trans "Network connection failed. Please check your internet connection." %}';
            } else if (error.message.includes('HTTP 500')) {
                errorMessage = '{% trans "Server error occurred. Please try again later." %}';
            } else if (error.message.includes('HTTP 403')) {
                errorMessage = '{% trans "Access denied. You may not have permission for this action." %}';
            } else if (error.message.includes('HTTP 404')) {
                errorMessage = '{% trans "Borrowing record not found. The book may have already been returned." %}';
            } else if (error.message.includes('HTTP 400')) {
                errorMessage = '{% trans "Cannot renew this book. It may have reached the maximum renewal limit or be overdue." %}';
            } else if (error.message && !error.message.includes('HTTP')) {
                errorMessage = error.message;
            }
            
            // Show error feedback
            if (window.toast) {
                window.toast.error(errorMessage);
            }
            
            if (window.buttonManager) {
                window.buttonManager.setError(button, '{% trans "Error" %}');
            } else {
                // Restore button state
                button.innerHTML = originalText;
                button.disabled = false;
            }
        }
    }
});
</script>
{% endblock %}