{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Library Catalog" %} - {% trans "Library" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">{% trans "Library Catalog" %}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'accounts:dashboard' %}">{% trans "Dashboard" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'library:dashboard' %}">{% trans "Library" %}</a></li>
                        <li class="breadcrumb-item active">{% trans "Catalog" %}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">{% trans "Search Books" %}</label>
                            <input type="text" name="search" id="search" class="form-control" 
                                   placeholder="{% trans 'Title, author, ISBN...' %}" value="{{ search_query }}">
                        </div>
                        <div class="col-md-2">
                            <label for="category" class="form-label">{% trans "Category" %}</label>
                            <select name="category" id="category" class="form-select">
                                <option value="">{% trans "All Categories" %}</option>
                                {% for category in categories %}
                                <option value="{{ category.id }}" {% if category.id|stringformat:"s" == category_filter %}selected{% endif %}>
                                    {{ category.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="availability" class="form-label">{% trans "Availability" %}</label>
                            <select name="availability" id="availability" class="form-select">
                                <option value="">{% trans "All Books" %}</option>
                                <option value="available" {% if availability_filter == 'available' %}selected{% endif %}>
                                    {% trans "Available" %}
                                </option>
                                <option value="borrowed" {% if availability_filter == 'borrowed' %}selected{% endif %}>
                                    {% trans "Borrowed" %}
                                </option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="format" class="form-label">{% trans "Format" %}</label>
                            <select name="format" id="format" class="form-select">
                                <option value="">{% trans "All Formats" %}</option>
                                <option value="physical" {% if format_filter == 'physical' %}selected{% endif %}>
                                    {% trans "Physical" %}
                                </option>
                                <option value="digital" {% if format_filter == 'digital' %}selected{% endif %}>
                                    {% trans "Digital" %}
                                </option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                                <a href="{% url 'library:catalog' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Books Grid -->
    <div class="row">
        {% for book in books %}
        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
            <div class="card book-card h-100">
                <div class="book-cover">
                    {% if book.cover_image %}
                        <img src="{{ book.cover_image.url }}" class="card-img-top" alt="{{ book.title }}">
                    {% else %}
                        <div class="no-cover d-flex align-items-center justify-content-center">
                            <i class="fas fa-book fa-3x text-muted"></i>
                        </div>
                    {% endif %}
                    <div class="book-overlay">
                        <div class="book-actions">
                            <a href="#" class="btn btn-primary btn-sm" title="{% trans 'View Details' %}">
                                <i class="fas fa-eye"></i>
                            </a>
                            {% if book.available_copies > 0 %}
                                <a href="#" class="btn btn-success btn-sm" title="{% trans 'Borrow' %}">
                                    <i class="fas fa-plus"></i>
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="card-body d-flex flex-column">
                    <h6 class="card-title">{{ book.title|truncatechars:50 }}</h6>
                    <p class="card-text text-muted small">{{ book.author|truncatechars:30 }}</p>
                    <div class="mt-auto">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">{{ book.category.name }}</small>
                            <div class="availability-status">
                                {% if book.available_copies > 0 %}
                                    <span class="badge bg-success">
                                        {{ book.available_copies }} {% trans "available" %}
                                    </span>
                                {% else %}
                                    <span class="badge bg-danger">{% trans "Not available" %}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="book-meta mt-2">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                {{ book.publication_year|default:"-" }}
                            </small>
                            {% if book.isbn %}
                                <small class="text-muted ms-2">
                                    <i class="fas fa-barcode me-1"></i>
                                    {{ book.isbn }}
                                </small>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">{% trans "No books found" %}</h5>
                <p class="text-muted">{% trans "Try adjusting your search criteria or browse all books." %}</p>
                <a href="{% url 'library:catalog' %}" class="btn btn-primary">
                    {% trans "View All Books" %}
                </a>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="row">
        <div class="col-12">
            <nav aria-label="{% trans 'Book catalog pagination' %}">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if availability_filter %}&availability={{ availability_filter }}{% endif %}{% if format_filter %}&format={{ format_filter }}{% endif %}">&laquo; {% trans "First" %}</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if availability_filter %}&availability={{ availability_filter }}{% endif %}{% if format_filter %}&format={{ format_filter }}{% endif %}">{% trans "Previous" %}</a>
                        </li>
                    {% endif %}

                    <li class="page-item active">
                        <span class="page-link">
                            {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                        </span>
                    </li>

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if availability_filter %}&availability={{ availability_filter }}{% endif %}{% if format_filter %}&format={{ format_filter }}{% endif %}">{% trans "Next" %}</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if availability_filter %}&availability={{ availability_filter }}{% endif %}{% if format_filter %}&format={{ format_filter }}{% endif %}">{% trans "Last" %} &raquo;</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}
</div>

<style>
.book-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.book-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.book-cover {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.book-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-cover {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.book-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.book-card:hover .book-overlay {
    opacity: 1;
}

.book-actions {
    display: flex;
    gap: 10px;
}

.book-meta {
    font-size: 0.8em;
}
</style>
{% endblock %}