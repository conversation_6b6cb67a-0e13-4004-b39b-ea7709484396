{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Library Analytics Dashboard" %}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/library.css' %}">
<style>
.analytics-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px 0;
    margin-bottom: 30px;
}

.metric-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    margin-bottom: 20px;
    transition: transform 0.3s ease;
    border-left: 4px solid #007bff;
}

.metric-card:hover {
    transform: translateY(-5px);
}

.metric-card.collection { border-left-color: #28a745; }
.metric-card.activity { border-left-color: #007bff; }
.metric-card.digital { border-left-color: #ffc107; }
.metric-card.users { border-left-color: #dc3545; }

.metric-number {
    font-size: 2.5em;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.metric-label {
    color: #666;
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.metric-icon {
    font-size: 2em;
    margin-bottom: 10px;
    opacity: 0.7;
}

.chart-container {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.chart-title {
    font-size: 1.2em;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 10px;
}

.date-filter {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.filter-row {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    min-width: 150px;
}

.filter-group label {
    font-weight: 500;
    margin-bottom: 5px;
    color: #333;
}

.trend-chart {
    height: 300px;
    width: 100%;
}

.top-items-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.top-item {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
}

.top-item:last-child {
    border-bottom: none;
}

.item-info {
    flex-grow: 1;
}

.item-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.item-meta {
    color: #666;
    font-size: 0.9em;
}

.item-count {
    background: #007bff;
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.9em;
    font-weight: 600;
}

.comparison-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.comparison-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    margin: 10px 0;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-books { background: #28a745; }
.progress-digital { background: #007bff; }
.progress-borrowed { background: #ffc107; }
.progress-overdue { background: #dc3545; }

.export-controls {
    text-align: right;
    margin-bottom: 20px;
}

.btn-export {
    background: #28a745;
    border-color: #28a745;
    color: white;
    margin-left: 10px;
}

.alert-summary {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.alert-item {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 10px;
}

.alert-item:last-child {
    margin-bottom: 0;
}

.alert-icon {
    color: #856404;
    margin-right: 10px;
}

.no-data {
    text-align: center;
    padding: 40px;
    color: #666;
}

.no-data i {
    font-size: 3em;
    margin-bottom: 20px;
    opacity: 0.5;
}

@media (max-width: 768px) {
    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-group {
        min-width: auto;
        width: 100%;
    }
    
    .comparison-grid {
        grid-template-columns: 1fr;
    }
    
    .export-controls {
        text-align: center;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="analytics-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-chart-line me-3"></i>
                    {% trans "Library Analytics Dashboard" %}
                </h1>
                <p class="mb-0 opacity-75">
                    {% trans "Comprehensive library statistics and insights" %}
                </p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="btn-group" role="group">
                    <a href="{% url 'library:catalog' %}" class="btn btn-light">
                        <i class="fas fa-books me-2"></i>
                        {% trans "Catalog" %}
                    </a>
                    <a href="{% url 'library:digital_library' %}" class="btn btn-light">
                        <i class="fas fa-tablet-alt me-2"></i>
                        {% trans "Digital Library" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Date Filter -->
    <div class="date-filter">
        <form method="get" class="mb-0">
            <div class="filter-row">
                <div class="filter-group">
                    <label for="date_from">{% trans "From Date" %}</label>
                    <input type="date" 
                           id="date_from" 
                           name="date_from" 
                           class="form-control" 
                           value="{{ date_from|date:'Y-m-d' }}">
                </div>
                <div class="filter-group">
                    <label for="date_to">{% trans "To Date" %}</label>
                    <input type="date" 
                           id="date_to" 
                           name="date_to" 
                           class="form-control" 
                           value="{{ date_to|date:'Y-m-d' }}">
                </div>
                <div class="filter-group">
                    <label for="report_type">{% trans "Report Type" %}</label>
                    <select name="report_type" id="report_type" class="form-select">
                        <option value="overview" {% if report_type == 'overview' %}selected{% endif %}>
                            {% trans "Overview" %}
                        </option>
                        <option value="collection" {% if report_type == 'collection' %}selected{% endif %}>
                            {% trans "Collection Analysis" %}
                        </option>
                        <option value="usage" {% if report_type == 'usage' %}selected{% endif %}>
                            {% trans "Usage Analysis" %}
                        </option>
                        <option value="digital" {% if report_type == 'digital' %}selected{% endif %}>
                            {% trans "Digital Resources" %}
                        </option>
                    </select>
                </div>
                <div class="filter-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-2"></i>
                        {% trans "Apply Filter" %}
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Export Controls -->
    <div class="export-controls">
        <button class="btn btn-export" onclick="exportData('pdf')">
            <i class="fas fa-file-pdf me-2"></i>
            {% trans "Export PDF" %}
        </button>
        <button class="btn btn-export" onclick="exportData('excel')">
            <i class="fas fa-file-excel me-2"></i>
            {% trans "Export Excel" %}
        </button>
    </div>

    <!-- Alert Summary -->
    {% if alerts %}
        <div class="alert-summary">
            <h6 class="mb-3">
                <i class="fas fa-exclamation-triangle me-2"></i>
                {% trans "Attention Required" %}
            </h6>
            {% for alert in alerts %}
                <div class="alert-item">
                    <div>
                        <i class="fas {{ alert.icon }} alert-icon"></i>
                        {{ alert.message }}
                    </div>
                    <span class="badge bg-warning">{{ alert.count }}</span>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Key Metrics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="metric-card collection">
                <div class="metric-icon text-success">
                    <i class="fas fa-books"></i>
                </div>
                <div class="metric-number">{{ total_books }}</div>
                <div class="metric-label">{% trans "Total Books" %}</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card digital">
                <div class="metric-icon text-warning">
                    <i class="fas fa-tablet-alt"></i>
                </div>
                <div class="metric-number">{{ total_digital }}</div>
                <div class="metric-label">{% trans "Digital Resources" %}</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card activity">
                <div class="metric-icon text-primary">
                    <i class="fas fa-exchange-alt"></i>
                </div>
                <div class="metric-number">{{ active_borrowings }}</div>
                <div class="metric-label">{% trans "Active Borrowings" %}</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card users">
                <div class="metric-icon text-danger">
                    <i class="fas fa-users"></i>
                </div>
                <div class="metric-number">{{ active_users }}</div>
                <div class="metric-label">{% trans "Active Users" %}</div>
            </div>
        </div>
    </div>

    <!-- Collection vs Usage Comparison -->
    <div class="comparison-grid">
        <div class="comparison-card">
            <h6 class="mb-3">{% trans "Collection Overview" %}</h6>
            <div class="mb-3">
                <div class="d-flex justify-content-between mb-1">
                    <span>{% trans "Physical Books" %}</span>
                    <span>{{ total_books }}</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill progress-books" 
                         style="width: {% widthratio total_books total_resources 100 %}%"></div>
                </div>
            </div>
            <div class="mb-3">
                <div class="d-flex justify-content-between mb-1">
                    <span>{% trans "Digital Resources" %}</span>
                    <span>{{ total_digital }}</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill progress-digital" 
                         style="width: {% widthratio total_digital total_resources 100 %}%"></div>
                </div>
            </div>
        </div>

        <div class="comparison-card">
            <h6 class="mb-3">{% trans "Usage Overview" %}</h6>
            <div class="mb-3">
                <div class="d-flex justify-content-between mb-1">
                    <span>{% trans "Currently Borrowed" %}</span>
                    <span>{{ active_borrowings }}</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill progress-borrowed" 
                         style="width: {% widthratio active_borrowings total_books 100 %}%"></div>
                </div>
            </div>
            <div class="mb-3">
                <div class="d-flex justify-content-between mb-1">
                    <span>{% trans "Overdue Items" %}</span>
                    <span>{{ overdue_count }}</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill progress-overdue" 
                         style="width: {% widthratio overdue_count active_borrowings 100 %}%"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Daily Activity Trend -->
        <div class="col-lg-8 mb-4">
            <div class="chart-container">
                <h3 class="chart-title">
                    <i class="fas fa-chart-line me-2"></i>
                    {% trans "Daily Activity Trend" %}
                </h3>
                {% if daily_activity %}
                    <canvas id="activity-trend-chart" class="trend-chart"></canvas>
                {% else %}
                    <div class="no-data">
                        <i class="fas fa-chart-line"></i>
                        <p>{% trans "No activity data available for the selected period." %}</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Top Categories -->
        <div class="col-lg-4 mb-4">
            <div class="chart-container">
                <h3 class="chart-title">
                    <i class="fas fa-layer-group me-2"></i>
                    {% trans "Top Categories" %}
                </h3>
                {% if top_categories %}
                    <ul class="top-items-list">
                        {% for category in top_categories %}
                            <li class="top-item">
                                <div class="item-info">
                                    <div class="item-title">{{ category.name }}</div>
                                    <div class="item-meta">
                                        {{ category.book_count }} {% trans "books" %}
                                    </div>
                                </div>
                                <div class="item-count">{{ category.usage_count }}</div>
                            </li>
                        {% endfor %}
                    </ul>
                {% else %}
                    <div class="no-data">
                        <i class="fas fa-layer-group"></i>
                        <p>{% trans "No category data available." %}</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Most Popular Books -->
        <div class="col-lg-6 mb-4">
            <div class="chart-container">
                <h3 class="chart-title">
                    <i class="fas fa-star me-2"></i>
                    {% trans "Most Popular Books" %}
                </h3>
                {% if popular_books %}
                    <ul class="top-items-list">
                        {% for book in popular_books %}
                            <li class="top-item">
                                <div class="item-info">
                                    <div class="item-title">{{ book.title }}</div>
                                    <div class="item-meta">
                                        {{ book.get_authors_display }}
                                        {% if book.category %}
                                            • {{ book.category.name }}
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="item-count">{{ book.borrow_count }}</div>
                            </li>
                        {% endfor %}
                    </ul>
                {% else %}
                    <div class="no-data">
                        <i class="fas fa-star"></i>
                        <p>{% trans "No popular books data available." %}</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Active Users -->
        <div class="col-lg-6 mb-4">
            <div class="chart-container">
                <h3 class="chart-title">
                    <i class="fas fa-users me-2"></i>
                    {% trans "Most Active Users" %}
                </h3>
                {% if active_users_list %}
                    <ul class="top-items-list">
                        {% for user in active_users_list %}
                            <li class="top-item">
                                <div class="item-info">
                                    <div class="item-title">{{ user.borrower_name }}</div>
                                    <div class="item-meta">
                                        {{ user.borrower_type|title }}
                                        {% if user.last_activity %}
                                            • {% trans "Last activity:" %} {{ user.last_activity|date:"M d" }}
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="item-count">{{ user.borrow_count }}</div>
                            </li>
                        {% endfor %}
                    </ul>
                {% else %}
                    <div class="no-data">
                        <i class="fas fa-users"></i>
                        <p>{% trans "No active users data available." %}</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Collection Health -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <h3 class="chart-title">
                    <i class="fas fa-heartbeat me-2"></i>
                    {% trans "Collection Health" %}
                </h3>
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center p-3">
                            <div class="metric-number text-success">{{ available_percentage }}%</div>
                            <div class="metric-label">{% trans "Available" %}</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3">
                            <div class="metric-number text-warning">{{ borrowed_percentage }}%</div>
                            <div class="metric-label">{% trans "Borrowed" %}</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3">
                            <div class="metric-number text-danger">{{ overdue_percentage }}%</div>
                            <div class="metric-label">{% trans "Overdue" %}</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3">
                            <div class="metric-number text-info">{{ damaged_count }}</div>
                            <div class="metric-label">{% trans "Damaged/Lost" %}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Daily Activity Trend Chart
    {% if daily_activity %}
        const ctx = document.getElementById('activity-trend-chart').getContext('2d');
        const activityChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [
                    {% for day in daily_activity %}
                        '{{ day.date|date:"M d" }}'{% if not forloop.last %},{% endif %}
                    {% endfor %}
                ],
                datasets: [{
                    label: '{% trans "Borrowings" %}',
                    data: [
                        {% for day in daily_activity %}
                            {{ day.borrowings }}{% if not forloop.last %},{% endif %}
                        {% endfor %}
                    ],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }, {
                    label: '{% trans "Returns" %}',
                    data: [
                        {% for day in daily_activity %}
                            {{ day.returns }}{% if not forloop.last %},{% endif %}
                        {% endfor %}
                    ],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }, {
                    label: '{% trans "Digital Usage" %}',
                    data: [
                        {% for day in daily_activity %}
                            {{ day.digital_usage }}{% if not forloop.last %},{% endif %}
                        {% endfor %}
                    ],
                    borderColor: '#ffc107',
                    backgroundColor: 'rgba(255, 193, 7, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    }
                }
            }
        });
    {% endif %}
});

function exportData(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);
    
    const exportUrl = '{% url "library:comprehensive_analytics" %}?' + params.toString();
    window.open(exportUrl, '_blank');
}

// Auto-refresh data every 10 minutes
setInterval(function() {
    if (document.visibilityState === 'visible') {
        location.reload();
    }
}, 10 * 60 * 1000);
</script>
{% endblock %}