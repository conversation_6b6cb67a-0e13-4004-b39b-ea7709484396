{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Library Dashboard" %}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/library.css' %}">
<style>
.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px 0;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    margin-bottom: 20px;
    transition: transform 0.3s ease;
    border-left: 4px solid #007bff;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card.warning {
    border-left-color: #ffc107;
}

.stat-card.success {
    border-left-color: #28a745;
}

.stat-card.danger {
    border-left-color: #dc3545;
}

.stat-number {
    font-size: 2.5em;
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
}

.stat-label {
    color: #666;
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stat-icon {
    font-size: 2em;
    margin-bottom: 10px;
    opacity: 0.7;
}

.dashboard-section {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.section-title {
    font-size: 1.2em;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 10px;
}

.quick-action-btn {
    display: block;
    width: 100%;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 8px;
    text-decoration: none;
    color: white;
    text-align: center;
    transition: all 0.3s ease;
}

.quick-action-btn:hover {
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.quick-action-btn.primary { background: #007bff; }
.quick-action-btn.success { background: #28a745; }
.quick-action-btn.info { background: #17a2b8; }
.quick-action-btn.secondary { background: #6c757d; }

.alert-item {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    border-left: 4px solid;
}

.alert-warning {
    background: #fff3cd;
    border-left-color: #ffc107;
    color: #856404;
}

.alert-info {
    background: #d1ecf1;
    border-left-color: #17a2b8;
    color: #0c5460;
}

.alert-danger {
    background: #f8d7da;
    border-left-color: #dc3545;
    color: #721c24;
}

.book-list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.book-list-item:last-child {
    border-bottom: none;
}

.book-info {
    flex-grow: 1;
}

.book-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.book-meta {
    color: #666;
    font-size: 0.9em;
}

.book-count {
    background: #007bff;
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.9em;
    font-weight: 600;
}

.category-tag {
    display: inline-block;
    background: #f8f9fa;
    color: #495057;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8em;
    margin-right: 5px;
    margin-bottom: 5px;
}

@media (max-width: 768px) {
    .dashboard-header {
        padding: 20px 0;
    }
    
    .stat-number {
        font-size: 2em;
    }
    
    .dashboard-section {
        padding: 15px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="dashboard-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-tachometer-alt me-3"></i>
                    {% trans "Library Dashboard" %}
                </h1>
                <p class="mb-0 opacity-75">
                    {% trans "Overview of library operations and key metrics" %}
                </p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="btn-group" role="group">
                    <a href="{% url 'library:statistics' %}" class="btn btn-light">
                        <i class="fas fa-chart-bar me-2"></i>
                        {% trans "Full Analytics" %}
                    </a>
                    <a href="{% url 'library:reports' %}" class="btn btn-outline-light">
                        <i class="fas fa-file-alt me-2"></i>
                        {% trans "Reports" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Key Metrics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="stat-card">
                <div class="stat-icon text-primary">
                    <i class="fas fa-books"></i>
                </div>
                <div class="stat-number">{{ total_books }}</div>
                <div class="stat-label">{% trans "Total Books" %}</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stat-card">
                <div class="stat-icon text-info">
                    <i class="fas fa-tablet-alt"></i>
                </div>
                <div class="stat-number">{{ total_digital }}</div>
                <div class="stat-label">{% trans "Digital Resources" %}</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stat-card {% if overdue_count > 0 %}warning{% else %}success{% endif %}">
                <div class="stat-icon {% if overdue_count > 0 %}text-warning{% else %}text-success{% endif %}">
                    <i class="fas fa-hand-holding"></i>
                </div>
                <div class="stat-number">{{ active_borrowings }}</div>
                <div class="stat-label">{% trans "Active Borrowings" %}</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stat-card {% if overdue_count > 0 %}danger{% else %}success{% endif %}">
                <div class="stat-icon {% if overdue_count > 0 %}text-danger{% else %}text-success{% endif %}">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-number">{{ overdue_count }}</div>
                <div class="stat-label">{% trans "Overdue Books" %}</div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row mb-4">
        <div class="col-lg-4 col-md-6">
            <div class="stat-card">
                <div class="stat-icon text-success">
                    <i class="fas fa-arrow-up"></i>
                </div>
                <div class="stat-number">{{ recent_borrowings }}</div>
                <div class="stat-label">{% trans "Borrowings (7 days)" %}</div>
            </div>
        </div>
        <div class="col-lg-4 col-md-6">
            <div class="stat-card">
                <div class="stat-icon text-primary">
                    <i class="fas fa-arrow-down"></i>
                </div>
                <div class="stat-number">{{ recent_returns }}</div>
                <div class="stat-label">{% trans "Returns (7 days)" %}</div>
            </div>
        </div>
        <div class="col-lg-4 col-md-6">
            <div class="stat-card">
                <div class="stat-icon text-info">
                    <i class="fas fa-eye"></i>
                </div>
                <div class="stat-number">{{ digital_views_week }}</div>
                <div class="stat-label">{% trans "Digital Views (7 days)" %}</div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Alerts and Notifications -->
        <div class="col-lg-4 mb-4">
            <div class="dashboard-section">
                <h3 class="section-title">
                    <i class="fas fa-bell me-2"></i>
                    {% trans "Alerts & Notifications" %}
                </h3>
                {% if alerts %}
                    {% for alert in alerts %}
                        <div class="alert-item alert-{{ alert.type }}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <i class="{{ alert.icon }} me-2"></i>
                                    <strong>{{ alert.title }}</strong>
                                    <p class="mb-1">{{ alert.message }}</p>
                                </div>
                                <a href="{{ alert.action_url }}" class="btn btn-sm btn-outline-secondary">
                                    {{ alert.action_text }}
                                </a>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                        <p class="text-muted">{% trans "No alerts at this time" %}</p>
                    </div>
                {% endif %}
            </div>

            <!-- Quick Actions -->
            <div class="dashboard-section">
                <h3 class="section-title">
                    <i class="fas fa-bolt me-2"></i>
                    {% trans "Quick Actions" %}
                </h3>
                {% for action in quick_actions %}
                    <a href="{{ action.url }}" class="quick-action-btn {{ action.color }}">
                        <i class="{{ action.icon }} me-2"></i>
                        {{ action.title }}
                    </a>
                {% endfor %}
            </div>
        </div>

        <!-- Popular Books This Month -->
        <div class="col-lg-4 mb-4">
            <div class="dashboard-section">
                <h3 class="section-title">
                    <i class="fas fa-star me-2"></i>
                    {% trans "Popular Books (30 days)" %}
                </h3>
                {% if popular_books_month %}
                    {% for book in popular_books_month %}
                        <div class="book-list-item">
                            <div class="book-info">
                                <div class="book-title">{{ book.title }}</div>
                                <div class="book-meta">
                                    {% if book.get_authors_display %}
                                        {{ book.get_authors_display }}
                                    {% endif %}
                                    {% if book.category %}
                                        • {{ book.category.name }}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="book-count">{{ book.borrow_count }}</div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-book fa-2x text-muted mb-2"></i>
                        <p class="text-muted">{% trans "No borrowing activity this month" %}</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Recent Acquisitions & High Demand Categories -->
        <div class="col-lg-4 mb-4">
            <div class="dashboard-section">
                <h3 class="section-title">
                    <i class="fas fa-plus me-2"></i>
                    {% trans "Recent Acquisitions" %}
                </h3>
                {% if recent_acquisitions %}
                    {% for book in recent_acquisitions %}
                        <div class="book-list-item">
                            <div class="book-info">
                                <div class="book-title">{{ book.title }}</div>
                                <div class="book-meta">
                                    {{ book.acquisition_date|date:"M d, Y" }}
                                    {% if book.category %}
                                        • {{ book.category.name }}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                        <p class="text-muted">{% trans "No recent acquisitions" %}</p>
                    </div>
                {% endif %}
            </div>

            <div class="dashboard-section">
                <h3 class="section-title">
                    <i class="fas fa-fire me-2"></i>
                    {% trans "High Demand Categories" %}
                </h3>
                {% if high_demand_categories %}
                    {% for category in high_demand_categories %}
                        <span class="category-tag">
                            {{ category.name }}
                            <span class="badge bg-primary ms-1">{{ category.borrow_count }}</span>
                        </span>
                    {% endfor %}
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-lightbulb me-1"></i>
                            {% trans "Consider acquiring more books in these popular categories" %}
                        </small>
                    </div>
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-chart-bar fa-2x text-muted mb-2"></i>
                        <p class="text-muted">{% trans "No category data available" %}</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh dashboard every 5 minutes
    setInterval(function() {
        if (document.visibilityState === 'visible') {
            location.reload();
        }
    }, 5 * 60 * 1000);
    
    // Add click tracking for quick actions
    document.querySelectorAll('.quick-action-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            // Could send analytics data here
            console.log('Quick action clicked:', this.textContent.trim());
        });
    });
});
</script>
{% endblock %}