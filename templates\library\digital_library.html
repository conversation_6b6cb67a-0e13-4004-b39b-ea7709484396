{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Digital Library" %}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/library.css' %}">
<style>
.resource-card {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    transition: box-shadow 0.3s ease;
}

.resource-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.resource-thumbnail {
    width: 80px;
    height: 100px;
    object-fit: cover;
    border-radius: 4px;
}

.resource-type-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
    text-transform: uppercase;
}

.resource-type-ebook { background-color: #e3f2fd; color: #1976d2; }
.resource-type-audiobook { background-color: #f3e5f5; color: #7b1fa2; }
.resource-type-video { background-color: #e8f5e8; color: #388e3c; }
.resource-type-document { background-color: #fff3e0; color: #f57c00; }

.access-type-badge {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 0.7em;
    margin-left: 5px;
}

.access-open { background-color: #c8e6c9; color: #2e7d32; }
.access-restricted { background-color: #ffcdd2; color: #c62828; }
.access-licensed { background-color: #fff9c4; color: #f57f17; }

.file-size {
    color: #666;
    font-size: 0.9em;
}

.usage-stats {
    color: #888;
    font-size: 0.8em;
}

.search-filters {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.filter-row {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    min-width: 150px;
}

.filter-group label {
    font-weight: 500;
    margin-bottom: 5px;
    color: #333;
}

.btn-read-online {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

.btn-download {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

.btn-preview {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-tablet-alt me-2"></i>
                    {% trans "Digital Library" %}
                </h2>
                <div class="d-flex gap-2">
                    <a href="{% url 'library:catalog' %}" class="btn btn-outline-primary">
                        <i class="fas fa-books me-1"></i>
                        {% trans "Physical Books" %}
                    </a>
                    <a href="{% url 'library:statistics' %}" class="btn btn-outline-info">
                        <i class="fas fa-chart-bar me-1"></i>
                        {% trans "Statistics" %}
                    </a>
                </div>
            </div>

            <!-- Search and Filters -->
            <div class="search-filters">
                <form method="get" class="mb-0">
                    <div class="filter-row mb-3">
                        <div class="filter-group flex-grow-1">
                            <label for="search">{% trans "Search" %}</label>
                            <input type="text" 
                                   id="search" 
                                   name="q" 
                                   class="form-control" 
                                   value="{{ query }}" 
                                   placeholder="{% trans 'Search by title, author, keywords...' %}">
                        </div>
                        <div class="filter-group">
                            <label for="category">{% trans "Category" %}</label>
                            <select name="category" id="category" class="form-select">
                                <option value="">{% trans "All Categories" %}</option>
                                {% for category in categories %}
                                    <option value="{{ category.id }}" 
                                            {% if category.id|stringformat:"s" == selected_category %}selected{% endif %}>
                                        {{ category.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="type">{% trans "Resource Type" %}</label>
                            <select name="type" id="type" class="form-select">
                                <option value="">{% trans "All Types" %}</option>
                                {% for value, label in type_choices %}
                                    <option value="{{ value }}" 
                                            {% if value == selected_type %}selected{% endif %}>
                                        {{ label }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <div class="filter-row">
                        <div class="filter-group">
                            <label for="format">{% trans "File Format" %}</label>
                            <select name="format" id="format" class="form-select">
                                <option value="">{% trans "All Formats" %}</option>
                                {% for value, label in format_choices %}
                                    <option value="{{ value }}" 
                                            {% if value == selected_format %}selected{% endif %}>
                                        {{ label }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="access">{% trans "Access Type" %}</label>
                            <select name="access" id="access" class="form-select">
                                <option value="">{% trans "All Access Types" %}</option>
                                {% for value, label in access_choices %}
                                    <option value="{{ value }}" 
                                            {% if value == selected_access %}selected{% endif %}>
                                        {{ label }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="filter-group align-self-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>
                                {% trans "Search" %}
                            </button>
                            <a href="{% url 'library:digital_library' %}" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-times me-1"></i>
                                {% trans "Clear" %}
                            </a>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Results Summary -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <span class="text-muted">
                        {% blocktrans count counter=total_resources %}
                            {{ counter }} digital resource found
                        {% plural %}
                            {{ counter }} digital resources found
                        {% endblocktrans %}
                    </span>
                </div>
                <div class="btn-group" role="group">
                    <input type="radio" class="btn-check" name="view-mode" id="grid-view" checked>
                    <label class="btn btn-outline-secondary" for="grid-view">
                        <i class="fas fa-th"></i>
                    </label>
                    <input type="radio" class="btn-check" name="view-mode" id="list-view">
                    <label class="btn btn-outline-secondary" for="list-view">
                        <i class="fas fa-list"></i>
                    </label>
                </div>
            </div>

            <!-- Digital Resources Grid -->
            <div class="row" id="resources-grid">
                {% for resource in page_obj %}
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="resource-card h-100">
                            <div class="d-flex">
                                <div class="me-3">
                                    {% if resource.thumbnail %}
                                        <img src="{{ resource.thumbnail.url }}" 
                                             alt="{{ resource.title }}" 
                                             class="resource-thumbnail">
                                    {% else %}
                                        <div class="resource-thumbnail bg-light d-flex align-items-center justify-content-center">
                                            {% if resource.resource_type == 'ebook' %}
                                                <i class="fas fa-book fa-2x text-muted"></i>
                                            {% elif resource.resource_type == 'audiobook' %}
                                                <i class="fas fa-headphones fa-2x text-muted"></i>
                                            {% elif resource.resource_type == 'video' %}
                                                <i class="fas fa-play-circle fa-2x text-muted"></i>
                                            {% elif resource.resource_type == 'document' %}
                                                <i class="fas fa-file-alt fa-2x text-muted"></i>
                                            {% else %}
                                                <i class="fas fa-file fa-2x text-muted"></i>
                                            {% endif %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="mb-1">
                                            <a href="{% url 'library:digital_resource_detail' resource.id %}" 
                                               class="text-decoration-none">
                                                {{ resource.title }}
                                            </a>
                                        </h6>
                                        <span class="resource-type-badge resource-type-{{ resource.resource_type }}">
                                            {{ resource.get_resource_type_display }}
                                        </span>
                                    </div>
                                    
                                    {% if resource.get_authors_display %}
                                        <p class="text-muted mb-1">
                                            <i class="fas fa-user me-1"></i>
                                            {{ resource.get_authors_display }}
                                        </p>
                                    {% endif %}
                                    
                                    {% if resource.category %}
                                        <p class="text-muted mb-1">
                                            <i class="fas fa-folder me-1"></i>
                                            {{ resource.category.name }}
                                        </p>
                                    {% endif %}
                                    
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="file-size">
                                            <i class="fas fa-file me-1"></i>
                                            {{ resource.file_format|upper }}
                                            {% if resource.file_size_mb %}
                                                ({{ resource.file_size_mb }} MB)
                                            {% endif %}
                                        </span>
                                        <span class="access-type-badge access-{{ resource.access_type }}">
                                            {{ resource.get_access_type_display }}
                                        </span>
                                    </div>
                                    
                                    <div class="usage-stats mb-3">
                                        <i class="fas fa-eye me-1"></i>
                                        {{ resource.view_count }} {% trans "views" %}
                                        <i class="fas fa-download ms-2 me-1"></i>
                                        {{ resource.download_count }} {% trans "downloads" %}
                                    </div>
                                    
                                    <div class="btn-group w-100" role="group">
                                        {% if resource.file_path or resource.external_url %}
                                            {% if resource.resource_type == 'ebook' or resource.resource_type == 'document' %}
                                                <a href="{% url 'library:read_online' resource.id %}" 
                                                   class="btn btn-read-online btn-sm">
                                                    <i class="fas fa-book-open me-1"></i>
                                                    {% trans "Read" %}
                                                </a>
                                            {% elif resource.resource_type == 'video' %}
                                                <a href="{% url 'library:watch_online' resource.id %}" 
                                                   class="btn btn-read-online btn-sm">
                                                    <i class="fas fa-play me-1"></i>
                                                    {% trans "Watch" %}
                                                </a>
                                            {% elif resource.resource_type == 'audiobook' %}
                                                <a href="{% url 'library:listen_online' resource.id %}" 
                                                   class="btn btn-read-online btn-sm">
                                                    <i class="fas fa-headphones me-1"></i>
                                                    {% trans "Listen" %}
                                                </a>
                                            {% endif %}
                                            
                                            {% if resource.access_type != 'restricted' %}
                                                <a href="{% url 'library:download_resource' resource.id %}" 
                                                   class="btn btn-download btn-sm">
                                                    <i class="fas fa-download me-1"></i>
                                                    {% trans "Download" %}
                                                </a>
                                            {% endif %}
                                        {% endif %}
                                        
                                        {% if resource.preview_url %}
                                            <a href="{{ resource.preview_url }}" 
                                               target="_blank" 
                                               class="btn btn-preview btn-sm">
                                                <i class="fas fa-external-link-alt me-1"></i>
                                                {% trans "Preview" %}
                                            </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {% empty %}
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">{% trans "No digital resources found" %}</h4>
                            <p class="text-muted">
                                {% if query %}
                                    {% trans "Try adjusting your search criteria or browse all resources." %}
                                {% else %}
                                    {% trans "No digital resources are currently available." %}
                                {% endif %}
                            </p>
                            {% if query %}
                                <a href="{% url 'library:digital_library' %}" class="btn btn-primary">
                                    {% trans "Browse All Resources" %}
                                </a>
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
                <nav aria-label="{% trans 'Digital resources pagination' %}">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?{% if query %}q={{ query }}&{% endif %}{% if selected_category %}category={{ selected_category }}&{% endif %}{% if selected_type %}type={{ selected_type }}&{% endif %}{% if selected_format %}format={{ selected_format }}&{% endif %}{% if selected_access %}access={{ selected_access }}&{% endif %}page={{ page_obj.previous_page_number }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?{% if query %}q={{ query }}&{% endif %}{% if selected_category %}category={{ selected_category }}&{% endif %}{% if selected_type %}type={{ selected_type }}&{% endif %}{% if selected_format %}format={{ selected_format }}&{% endif %}{% if selected_access %}access={{ selected_access }}&{% endif %}page={{ num }}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?{% if query %}q={{ query }}&{% endif %}{% if selected_category %}category={{ selected_category }}&{% endif %}{% if selected_type %}type={{ selected_type }}&{% endif %}{% if selected_format %}format={{ selected_format }}&{% endif %}{% if selected_access %}access={{ selected_access }}&{% endif %}page={{ page_obj.next_page_number }}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // View mode toggle
    const gridView = document.getElementById('grid-view');
    const listView = document.getElementById('list-view');
    const resourcesGrid = document.getElementById('resources-grid');
    
    listView.addEventListener('change', function() {
        if (this.checked) {
            resourcesGrid.className = 'row';
            resourcesGrid.querySelectorAll('.col-lg-4').forEach(col => {
                col.className = 'col-12 mb-2';
            });
        }
    });
    
    gridView.addEventListener('change', function() {
        if (this.checked) {
            resourcesGrid.className = 'row';
            resourcesGrid.querySelectorAll('.col-12').forEach(col => {
                col.className = 'col-lg-4 col-md-6 mb-4';
            });
        }
    });
});
</script>
{% endblock %}