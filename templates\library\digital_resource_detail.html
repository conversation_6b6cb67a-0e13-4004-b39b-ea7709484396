{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ resource.title }} - {% trans "Digital Library" %}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/library.css' %}">
<style>
.resource-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px 0;
    margin-bottom: 30px;
}

.resource-thumbnail-large {
    width: 200px;
    height: 250px;
    object-fit: cover;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.resource-info {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.resource-actions {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.action-btn {
    margin-right: 10px;
    margin-bottom: 10px;
}

.metadata-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    width: 30%;
}

.usage-stats {
    background: #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.stat-item {
    text-align: center;
    padding: 10px;
}

.stat-number {
    font-size: 1.5em;
    font-weight: bold;
    color: #495057;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9em;
}

.reader-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-top: 20px;
}

.pdf-viewer {
    width: 100%;
    height: 600px;
    border: none;
    border-radius: 4px;
}

.video-player {
    width: 100%;
    height: 400px;
    border-radius: 4px;
}

.audio-player {
    width: 100%;
    margin: 20px 0;
}

.access-warning {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.copyright-info {
    background-color: #f8f9fa;
    border-left: 4px solid #007bff;
    padding: 15px;
    margin-top: 20px;
}

.related-resources {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.related-item {
    border-bottom: 1px solid #eee;
    padding: 10px 0;
}

.related-item:last-child {
    border-bottom: none;
}
</style>
{% endblock %}

{% block content %}
<div class="resource-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-3">
                {% if resource.thumbnail %}
                    <img src="{{ resource.thumbnail.url }}" 
                         alt="{{ resource.title }}" 
                         class="resource-thumbnail-large">
                {% else %}
                    <div class="resource-thumbnail-large bg-light d-flex align-items-center justify-content-center">
                        {% if resource.resource_type == 'ebook' %}
                            <i class="fas fa-book fa-4x text-muted"></i>
                        {% elif resource.resource_type == 'audiobook' %}
                            <i class="fas fa-headphones fa-4x text-muted"></i>
                        {% elif resource.resource_type == 'video' %}
                            <i class="fas fa-play-circle fa-4x text-muted"></i>
                        {% elif resource.resource_type == 'document' %}
                            <i class="fas fa-file-alt fa-4x text-muted"></i>
                        {% else %}
                            <i class="fas fa-file fa-4x text-muted"></i>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
            <div class="col-md-9">
                <h1 class="mb-3">{{ resource.title }}</h1>
                {% if resource.title_ar %}
                    <h2 class="h4 mb-3 opacity-75">{{ resource.title_ar }}</h2>
                {% endif %}
                
                {% if resource.get_authors_display %}
                    <p class="h5 mb-2">
                        <i class="fas fa-user me-2"></i>
                        {{ resource.get_authors_display }}
                    </p>
                {% endif %}
                
                <div class="d-flex flex-wrap gap-2 mb-3">
                    <span class="badge bg-primary">{{ resource.get_resource_type_display }}</span>
                    <span class="badge bg-secondary">{{ resource.file_format|upper }}</span>
                    {% if resource.language %}
                        <span class="badge bg-info">{{ resource.get_language_display }}</span>
                    {% endif %}
                    {% if resource.category %}
                        <span class="badge bg-success">{{ resource.category.name }}</span>
                    {% endif %}
                    <span class="badge bg-warning text-dark">{{ resource.get_access_type_display }}</span>
                </div>
                
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="{% url 'library:digital_library' %}" class="text-white">
                                {% trans "Digital Library" %}
                            </a>
                        </li>
                        {% if resource.category %}
                            <li class="breadcrumb-item">
                                <a href="{% url 'library:digital_library' %}?category={{ resource.category.id }}" 
                                   class="text-white">
                                    {{ resource.category.name }}
                                </a>
                            </li>
                        {% endif %}
                        <li class="breadcrumb-item active text-white" aria-current="page">
                            {{ resource.title|truncatechars:50 }}
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-lg-8">
            <!-- Resource Actions -->
            <div class="resource-actions">
                <h5 class="mb-3">
                    <i class="fas fa-play-circle me-2"></i>
                    {% trans "Access Resource" %}
                </h5>
                
                {% if resource.access_type == 'restricted' and not user.is_staff %}
                    <div class="access-warning">
                        <i class="fas fa-lock me-2"></i>
                        {% trans "This resource has restricted access. Please contact the librarian for access." %}
                    </div>
                {% else %}
                    <div class="d-flex flex-wrap">
                        {% if resource.file_path or resource.external_url %}
                            {% if resource.resource_type == 'ebook' or resource.resource_type == 'document' %}
                                <button class="btn btn-success action-btn" onclick="openReader()">
                                    <i class="fas fa-book-open me-2"></i>
                                    {% trans "Read Online" %}
                                </button>
                            {% elif resource.resource_type == 'video' %}
                                <button class="btn btn-success action-btn" onclick="openPlayer()">
                                    <i class="fas fa-play me-2"></i>
                                    {% trans "Watch Online" %}
                                </button>
                            {% elif resource.resource_type == 'audiobook' %}
                                <button class="btn btn-success action-btn" onclick="openPlayer()">
                                    <i class="fas fa-headphones me-2"></i>
                                    {% trans "Listen Online" %}
                                </button>
                            {% endif %}
                            
                            {% if resource.access_type != 'restricted' %}
                                <a href="{% url 'library:download_resource' resource.id %}" 
                                   class="btn btn-primary action-btn">
                                    <i class="fas fa-download me-2"></i>
                                    {% trans "Download" %}
                                    {% if resource.file_size_mb %}
                                        ({{ resource.file_size_mb }} MB)
                                    {% endif %}
                                </a>
                            {% endif %}
                        {% endif %}
                        
                        {% if resource.preview_url %}
                            <a href="{{ resource.preview_url }}" 
                               target="_blank" 
                               class="btn btn-outline-secondary action-btn">
                                <i class="fas fa-external-link-alt me-2"></i>
                                {% trans "Preview" %}
                            </a>
                        {% endif %}
                        
                        <button class="btn btn-outline-info action-btn" onclick="shareResource()">
                            <i class="fas fa-share me-2"></i>
                            {% trans "Share" %}
                        </button>
                        
                        <button class="btn btn-outline-warning action-btn" onclick="reportIssue()">
                            <i class="fas fa-flag me-2"></i>
                            {% trans "Report Issue" %}
                        </button>
                    </div>
                {% endif %}
            </div>

            <!-- Online Reader/Player Container -->
            <div id="reader-container" class="reader-container" style="display: none;">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0">
                        <i class="fas fa-book-open me-2"></i>
                        {% trans "Online Reader" %}
                    </h5>
                    <button class="btn btn-sm btn-outline-secondary" onclick="closeReader()">
                        <i class="fas fa-times me-1"></i>
                        {% trans "Close" %}
                    </button>
                </div>
                
                <div id="reader-content">
                    <!-- Content will be loaded here -->
                </div>
            </div>

            <!-- Resource Description -->
            {% if resource.description %}
                <div class="resource-info">
                    <h5 class="mb-3">
                        <i class="fas fa-info-circle me-2"></i>
                        {% trans "Description" %}
                    </h5>
                    <p>{{ resource.description|linebreaks }}</p>
                </div>
            {% endif %}

            <!-- Copyright Information -->
            {% if resource.copyright_info or resource.license_type %}
                <div class="copyright-info">
                    <h6 class="mb-2">
                        <i class="fas fa-copyright me-2"></i>
                        {% trans "Copyright & License" %}
                    </h6>
                    {% if resource.license_type %}
                        <p class="mb-1">
                            <strong>{% trans "License:" %}</strong> {{ resource.license_type }}
                        </p>
                    {% endif %}
                    {% if resource.copyright_info %}
                        <p class="mb-0">{{ resource.copyright_info }}</p>
                    {% endif %}
                </div>
            {% endif %}
        </div>

        <div class="col-lg-4">
            <!-- Usage Statistics -->
            <div class="usage-stats">
                <h6 class="mb-3">
                    <i class="fas fa-chart-bar me-2"></i>
                    {% trans "Usage Statistics" %}
                </h6>
                <div class="row">
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="stat-number">{{ resource.view_count }}</div>
                            <div class="stat-label">{% trans "Views" %}</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="stat-number">{{ resource.download_count }}</div>
                            <div class="stat-label">{% trans "Downloads" %}</div>
                        </div>
                    </div>
                </div>
                {% if resource.last_accessed %}
                    <p class="text-muted text-center mt-2 mb-0">
                        <small>
                            {% trans "Last accessed:" %} {{ resource.last_accessed|date:"M d, Y H:i" }}
                        </small>
                    </p>
                {% endif %}
            </div>

            <!-- Resource Details -->
            <div class="resource-info">
                <h6 class="mb-3">
                    <i class="fas fa-list me-2"></i>
                    {% trans "Resource Details" %}
                </h6>
                <table class="table table-sm metadata-table">
                    <tbody>
                        <tr>
                            <th>{% trans "Type" %}</th>
                            <td>{{ resource.get_resource_type_display }}</td>
                        </tr>
                        <tr>
                            <th>{% trans "Format" %}</th>
                            <td>{{ resource.file_format|upper }}</td>
                        </tr>
                        {% if resource.file_size_mb %}
                            <tr>
                                <th>{% trans "File Size" %}</th>
                                <td>{{ resource.file_size_mb }} MB</td>
                            </tr>
                        {% endif %}
                        {% if resource.duration %}
                            <tr>
                                <th>{% trans "Duration" %}</th>
                                <td>{{ resource.duration }}</td>
                            </tr>
                        {% endif %}
                        <tr>
                            <th>{% trans "Language" %}</th>
                            <td>{{ resource.get_language_display }}</td>
                        </tr>
                        <tr>
                            <th>{% trans "Access Type" %}</th>
                            <td>{{ resource.get_access_type_display }}</td>
                        </tr>
                        {% if resource.publication_date %}
                            <tr>
                                <th>{% trans "Published" %}</th>
                                <td>{{ resource.publication_date|date:"M d, Y" }}</td>
                            </tr>
                        {% endif %}
                        {% if resource.rating %}
                            <tr>
                                <th>{% trans "Rating" %}</th>
                                <td>
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= resource.rating %}
                                            <i class="fas fa-star text-warning"></i>
                                        {% else %}
                                            <i class="far fa-star text-muted"></i>
                                        {% endif %}
                                    {% endfor %}
                                    ({{ resource.rating }}/5)
                                </td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>

            <!-- Keywords -->
            {% if resource.keywords %}
                <div class="resource-info">
                    <h6 class="mb-3">
                        <i class="fas fa-tags me-2"></i>
                        {% trans "Keywords" %}
                    </h6>
                    <div class="d-flex flex-wrap gap-1">
                        {% for keyword in resource.keywords|split:"," %}
                            <span class="badge bg-light text-dark">{{ keyword.strip }}</span>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            <!-- Related Resources -->
            <div class="related-resources">
                <h6 class="mb-3">
                    <i class="fas fa-link me-2"></i>
                    {% trans "Related Resources" %}
                </h6>
                <div id="related-resources-list">
                    <p class="text-muted">{% trans "Loading related resources..." %}</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentResourceId = '{{ resource.id }}';

function openReader() {
    const container = document.getElementById('reader-container');
    const content = document.getElementById('reader-content');
    
    container.style.display = 'block';
    container.scrollIntoView({ behavior: 'smooth' });
    
    // Load content based on resource type
    {% if resource.resource_type == 'ebook' or resource.resource_type == 'document' %}
        {% if resource.file_path %}
            content.innerHTML = `
                <iframe src="{% url 'library:read_online' resource.id %}" 
                        class="pdf-viewer" 
                        title="{{ resource.title }}">
                </iframe>
            `;
        {% elif resource.external_url %}
            content.innerHTML = `
                <iframe src="{{ resource.external_url }}" 
                        class="pdf-viewer" 
                        title="{{ resource.title }}">
                </iframe>
            `;
        {% endif %}
    {% elif resource.resource_type == 'video' %}
        openPlayer();
    {% elif resource.resource_type == 'audiobook' %}
        openPlayer();
    {% endif %}
}

function openPlayer() {
    const container = document.getElementById('reader-container');
    const content = document.getElementById('reader-content');
    
    container.style.display = 'block';
    container.scrollIntoView({ behavior: 'smooth' });
    
    {% if resource.resource_type == 'video' %}
        {% if resource.file_path %}
            content.innerHTML = `
                <video controls class="video-player">
                    <source src="{{ resource.file_path.url }}" type="video/mp4">
                    {% trans "Your browser does not support the video tag." %}
                </video>
            `;
        {% elif resource.external_url %}
            content.innerHTML = `
                <iframe src="{{ resource.external_url }}" 
                        class="video-player" 
                        title="{{ resource.title }}" 
                        allowfullscreen>
                </iframe>
            `;
        {% endif %}
    {% elif resource.resource_type == 'audiobook' %}
        {% if resource.file_path %}
            content.innerHTML = `
                <audio controls class="audio-player">
                    <source src="{{ resource.file_path.url }}" type="audio/mpeg">
                    {% trans "Your browser does not support the audio element." %}
                </audio>
            `;
        {% elif resource.external_url %}
            content.innerHTML = `
                <iframe src="{{ resource.external_url }}" 
                        class="video-player" 
                        title="{{ resource.title }}">
                </iframe>
            `;
        {% endif %}
    {% endif %}
}

function closeReader() {
    document.getElementById('reader-container').style.display = 'none';
}

function shareResource() {
    if (navigator.share) {
        navigator.share({
            title: '{{ resource.title }}',
            text: '{{ resource.description|truncatechars:100 }}',
            url: window.location.href
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(window.location.href).then(() => {
            alert('{% trans "Link copied to clipboard!" %}');
        });
    }
}

function reportIssue() {
    // Simple modal or redirect to report form
    const issue = prompt('{% trans "Please describe the issue with this resource:" %}');
    if (issue) {
        fetch('{% url "library:report_issue" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: JSON.stringify({
                resource_id: currentResourceId,
                issue_description: issue
            })
        }).then(response => {
            if (response.ok) {
                alert('{% trans "Thank you for your report. We will investigate the issue." %}');
            } else {
                alert('{% trans "Failed to submit report. Please try again." %}');
            }
        });
    }
}

// Load related resources
document.addEventListener('DOMContentLoaded', function() {
    fetch(`{% url 'library:related_resources' resource.id %}`)
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('related-resources-list');
            if (data.resources && data.resources.length > 0) {
                container.innerHTML = data.resources.map(resource => `
                    <div class="related-item">
                        <h6 class="mb-1">
                            <a href="/library/digital/${resource.id}/" class="text-decoration-none">
                                ${resource.title}
                            </a>
                        </h6>
                        <small class="text-muted">
                            ${resource.resource_type} • ${resource.authors || 'Unknown Author'}
                        </small>
                    </div>
                `).join('');
            } else {
                container.innerHTML = '<p class="text-muted">{% trans "No related resources found." %}</p>';
            }
        })
        .catch(error => {
            document.getElementById('related-resources-list').innerHTML = 
                '<p class="text-muted">{% trans "Failed to load related resources." %}</p>';
        });
});
</script>
{% endblock %}