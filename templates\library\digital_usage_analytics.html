{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Digital Library Analytics" %}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/library.css' %}">
<style>
.analytics-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px 0;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    margin-bottom: 20px;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 2.5em;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stat-icon {
    font-size: 2em;
    margin-bottom: 10px;
    opacity: 0.7;
}

.chart-container {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.chart-title {
    font-size: 1.2em;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 10px;
}

.date-filter {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.filter-row {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    min-width: 150px;
}

.filter-group label {
    font-weight: 500;
    margin-bottom: 5px;
    color: #333;
}

.popular-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.popular-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
}

.popular-item:last-child {
    border-bottom: none;
}

.popular-info {
    flex-grow: 1;
}

.popular-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.popular-meta {
    color: #666;
    font-size: 0.9em;
}

.popular-count {
    background: #007bff;
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.9em;
    font-weight: 600;
}

.usage-trend {
    height: 300px;
    position: relative;
}

.trend-chart {
    width: 100%;
    height: 100%;
}

.action-breakdown {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 20px;
}

.action-item {
    text-align: center;
    flex: 1;
    min-width: 120px;
}

.action-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 10px;
    color: white;
    font-size: 1.5em;
    font-weight: bold;
}

.action-view { background: #28a745; }
.action-download { background: #007bff; }
.action-watch { background: #dc3545; }
.action-listen { background: #ffc107; color: #333; }
.action-share { background: #6f42c1; }

.type-breakdown {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.type-item {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
}

.type-bar {
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    margin: 10px 0;
    overflow: hidden;
}

.type-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.type-ebook { background: #007bff; }
.type-audiobook { background: #28a745; }
.type-video { background: #dc3545; }
.type-document { background: #ffc107; }
.type-other { background: #6c757d; }

.export-controls {
    text-align: right;
    margin-bottom: 20px;
}

.btn-export {
    background: #28a745;
    border-color: #28a745;
    color: white;
    margin-left: 10px;
}

.no-data {
    text-align: center;
    padding: 40px;
    color: #666;
}

.no-data i {
    font-size: 3em;
    margin-bottom: 20px;
    opacity: 0.5;
}

@media (max-width: 768px) {
    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-group {
        min-width: auto;
        width: 100%;
    }
    
    .action-breakdown {
        flex-direction: column;
    }
    
    .type-breakdown {
        grid-template-columns: 1fr;
    }
    
    .export-controls {
        text-align: center;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="analytics-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-chart-line me-3"></i>
                    {% trans "Digital Library Analytics" %}
                </h1>
                <p class="mb-0 opacity-75">
                    {% trans "Usage statistics and insights for digital resources" %}
                </p>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{% url 'library:digital_library' %}" class="btn btn-light">
                    <i class="fas fa-arrow-left me-2"></i>
                    {% trans "Back to Digital Library" %}
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Date Filter -->
    <div class="date-filter">
        <form method="get" class="mb-0">
            <div class="filter-row">
                <div class="filter-group">
                    <label for="date_from">{% trans "From Date" %}</label>
                    <input type="date" 
                           id="date_from" 
                           name="date_from" 
                           class="form-control" 
                           value="{{ date_from|date:'Y-m-d' }}">
                </div>
                <div class="filter-group">
                    <label for="date_to">{% trans "To Date" %}</label>
                    <input type="date" 
                           id="date_to" 
                           name="date_to" 
                           class="form-control" 
                           value="{{ date_to|date:'Y-m-d' }}">
                </div>
                <div class="filter-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-2"></i>
                        {% trans "Apply Filter" %}
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Export Controls -->
    <div class="export-controls">
        <button class="btn btn-export" onclick="exportData('pdf')">
            <i class="fas fa-file-pdf me-2"></i>
            {% trans "Export PDF" %}
        </button>
        <button class="btn btn-export" onclick="exportData('excel')">
            <i class="fas fa-file-excel me-2"></i>
            {% trans "Export Excel" %}
        </button>
    </div>

    <!-- Overview Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="stat-card">
                <div class="stat-icon text-primary">
                    <i class="fas fa-tablet-alt"></i>
                </div>
                <div class="stat-number">{{ total_resources }}</div>
                <div class="stat-label">{% trans "Total Resources" %}</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stat-card">
                <div class="stat-icon text-success">
                    <i class="fas fa-eye"></i>
                </div>
                <div class="stat-number">{{ total_usage }}</div>
                <div class="stat-label">{% trans "Total Usage" %}</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stat-card">
                <div class="stat-icon text-info">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number">{{ unique_users }}</div>
                <div class="stat-label">{% trans "Unique Users" %}</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stat-card">
                <div class="stat-icon text-warning">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div class="stat-number">
                    {% if unique_users > 0 %}
                        {{ total_usage|floatformat:0|div:unique_users|floatformat:1 }}
                    {% else %}
                        0
                    {% endif %}
                </div>
                <div class="stat-label">{% trans "Avg Usage per User" %}</div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Usage by Action -->
        <div class="col-lg-6 mb-4">
            <div class="chart-container">
                <h3 class="chart-title">
                    <i class="fas fa-play-circle me-2"></i>
                    {% trans "Usage by Action" %}
                </h3>
                {% if usage_by_action %}
                    <div class="action-breakdown">
                        {% for action in usage_by_action %}
                            <div class="action-item">
                                <div class="action-circle action-{{ action.action }}">
                                    {{ action.count }}
                                </div>
                                <div class="action-label">
                                    {% if action.action == 'view' %}
                                        {% trans "Views" %}
                                    {% elif action.action == 'download' %}
                                        {% trans "Downloads" %}
                                    {% elif action.action == 'watch' %}
                                        {% trans "Watches" %}
                                    {% elif action.action == 'listen' %}
                                        {% trans "Listens" %}
                                    {% elif action.action == 'share' %}
                                        {% trans "Shares" %}
                                    {% else %}
                                        {{ action.action|title }}
                                    {% endif %}
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="no-data">
                        <i class="fas fa-chart-pie"></i>
                        <p>{% trans "No usage data available for the selected period." %}</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Usage by Resource Type -->
        <div class="col-lg-6 mb-4">
            <div class="chart-container">
                <h3 class="chart-title">
                    <i class="fas fa-layer-group me-2"></i>
                    {% trans "Usage by Resource Type" %}
                </h3>
                {% if usage_by_type %}
                    <div class="type-breakdown">
                        {% for type in usage_by_type %}
                            <div class="type-item">
                                <div class="type-label">
                                    {% if type.resource__resource_type == 'ebook' %}
                                        {% trans "E-books" %}
                                    {% elif type.resource__resource_type == 'audiobook' %}
                                        {% trans "Audiobooks" %}
                                    {% elif type.resource__resource_type == 'video' %}
                                        {% trans "Videos" %}
                                    {% elif type.resource__resource_type == 'document' %}
                                        {% trans "Documents" %}
                                    {% else %}
                                        {{ type.resource__resource_type|title }}
                                    {% endif %}
                                </div>
                                <div class="type-bar">
                                    <div class="type-fill type-{{ type.resource__resource_type }}" 
                                         style="width: {% widthratio type.count usage_by_type.0.count 100 %}%"></div>
                                </div>
                                <div class="type-count">{{ type.count }} {% trans "uses" %}</div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="no-data">
                        <i class="fas fa-layer-group"></i>
                        <p>{% trans "No resource type data available." %}</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Daily Usage Trend -->
        <div class="col-12 mb-4">
            <div class="chart-container">
                <h3 class="chart-title">
                    <i class="fas fa-chart-line me-2"></i>
                    {% trans "Daily Usage Trend" %}
                </h3>
                {% if daily_usage %}
                    <div class="usage-trend">
                        <canvas id="usage-trend-chart" class="trend-chart"></canvas>
                    </div>
                {% else %}
                    <div class="no-data">
                        <i class="fas fa-chart-line"></i>
                        <p>{% trans "No daily usage data available for the selected period." %}</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Most Popular Resources -->
        <div class="col-12">
            <div class="chart-container">
                <h3 class="chart-title">
                    <i class="fas fa-star me-2"></i>
                    {% trans "Most Popular Resources" %}
                </h3>
                {% if popular_resources %}
                    <ul class="popular-list">
                        {% for resource in popular_resources %}
                            <li class="popular-item">
                                <div class="popular-info">
                                    <div class="popular-title">
                                        <a href="{% url 'library:digital_resource_detail' resource.id %}" 
                                           class="text-decoration-none">
                                            {{ resource.title }}
                                        </a>
                                    </div>
                                    <div class="popular-meta">
                                        {{ resource.get_resource_type_display }}
                                        {% if resource.get_authors_display %}
                                            • {{ resource.get_authors_display }}
                                        {% endif %}
                                        {% if resource.category %}
                                            • {{ resource.category.name }}
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="popular-count">
                                    {{ resource.total_usage }} {% trans "uses" %}
                                </div>
                            </li>
                        {% endfor %}
                    </ul>
                {% else %}
                    <div class="no-data">
                        <i class="fas fa-star"></i>
                        <p>{% trans "No popular resources data available." %}</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Daily Usage Trend Chart
    {% if daily_usage %}
        const ctx = document.getElementById('usage-trend-chart').getContext('2d');
        const dailyUsageChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [
                    {% for day in daily_usage %}
                        '{{ day.day|date:"M d" }}'{% if not forloop.last %},{% endif %}
                    {% endfor %}
                ],
                datasets: [{
                    label: '{% trans "Daily Usage" %}',
                    data: [
                        {% for day in daily_usage %}
                            {{ day.count }}{% if not forloop.last %},{% endif %}
                        {% endfor %}
                    ],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    {% endif %}
});

function exportData(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);
    
    const exportUrl = '{% url "library:digital_usage_analytics" %}?' + params.toString();
    window.open(exportUrl, '_blank');
}

// Auto-refresh data every 5 minutes
setInterval(function() {
    if (document.visibilityState === 'visible') {
        location.reload();
    }
}, 5 * 60 * 1000);
</script>
{% endblock %}