{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Listening" %}: {{ resource.title }}{% endblock %}

{% block extra_css %}
<style>
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: white;
}

.audio-player-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 40px 20px;
}

.audio-header {
    text-align: center;
    margin-bottom: 40px;
}

.audio-title {
    font-size: 2.5em;
    font-weight: 300;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.audio-subtitle {
    font-size: 1.2em;
    opacity: 0.8;
    margin-bottom: 20px;
}

.audio-artwork {
    width: 300px;
    height: 300px;
    border-radius: 50%;
    margin: 0 auto 30px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    overflow: hidden;
    position: relative;
    animation: rotate 20s linear infinite;
    animation-play-state: paused;
}

.audio-artwork.playing {
    animation-play-state: running;
}

.audio-artwork img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.audio-artwork-placeholder {
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4em;
    color: rgba(255,255,255,0.5);
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.audio-player {
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.2);
}

.audio-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
}

.control-btn {
    background: rgba(255,255,255,0.2);
    border: none;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.2em;
}

.control-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: scale(1.1);
}

.play-pause-btn {
    width: 70px;
    height: 70px;
    font-size: 1.5em;
    background: rgba(255,255,255,0.3);
}

.progress-container {
    margin-bottom: 20px;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(255,255,255,0.2);
    border-radius: 3px;
    cursor: pointer;
    position: relative;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: white;
    border-radius: 3px;
    width: 0%;
    transition: width 0.1s ease;
}

.progress-handle {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    background: white;
    border-radius: 50%;
    cursor: pointer;
    left: 0%;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

.time-display {
    display: flex;
    justify-content: space-between;
    font-size: 0.9em;
    opacity: 0.8;
}

.volume-container {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

.volume-slider {
    flex-grow: 1;
    height: 4px;
    background: rgba(255,255,255,0.2);
    border-radius: 2px;
    outline: none;
    cursor: pointer;
}

.additional-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.control-option {
    display: flex;
    align-items: center;
    gap: 5px;
    background: rgba(255,255,255,0.1);
    padding: 8px 12px;
    border-radius: 20px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.control-option:hover {
    background: rgba(255,255,255,0.2);
}

.control-option select {
    background: transparent;
    border: none;
    color: white;
    outline: none;
    cursor: pointer;
}

.audio-info {
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.info-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.close-controls {
    text-align: center;
    margin-top: 30px;
}

.close-btn {
    background: rgba(220, 53, 69, 0.8);
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: rgba(220, 53, 69, 1);
    color: white;
}

.loading-state {
    text-align: center;
    padding: 40px;
    opacity: 0.7;
}

.error-state {
    text-align: center;
    padding: 40px;
    background: rgba(220, 53, 69, 0.2);
    border-radius: 15px;
    margin: 20px 0;
}

@media (max-width: 768px) {
    .audio-player-container {
        padding: 20px 15px;
    }
    
    .audio-title {
        font-size: 2em;
    }
    
    .audio-artwork {
        width: 250px;
        height: 250px;
    }
    
    .audio-player {
        padding: 20px;
    }
    
    .audio-controls {
        gap: 15px;
    }
    
    .control-btn {
        width: 45px;
        height: 45px;
        font-size: 1em;
    }
    
    .play-pause-btn {
        width: 60px;
        height: 60px;
        font-size: 1.3em;
    }
    
    .additional-controls {
        gap: 10px;
    }
    
    .control-option {
        padding: 6px 10px;
        font-size: 0.9em;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="audio-player-container">
    <div class="audio-header">
        <h1 class="audio-title">{{ resource.title }}</h1>
        {% if resource.get_authors_display %}
            <p class="audio-subtitle">{{ resource.get_authors_display }}</p>
        {% endif %}
    </div>

    <div class="audio-artwork" id="audio-artwork">
        {% if resource.thumbnail %}
            <img src="{{ resource.thumbnail.url }}" alt="{{ resource.title }}">
        {% else %}
            <div class="audio-artwork-placeholder">
                <i class="fas fa-music"></i>
            </div>
        {% endif %}
    </div>

    <div class="audio-player">
        {% if resource.file_path %}
            <audio id="audio-element" preload="metadata" onloadstart="showLoading()" oncanplay="hideLoading()" onerror="showError()">
                <source src="{{ resource.file_path.url }}" type="audio/mpeg">
                <source src="{{ resource.file_path.url }}" type="audio/wav">
                <source src="{{ resource.file_path.url }}" type="audio/ogg">
                {% trans "Your browser does not support the audio element." %}
            </audio>
        {% elif resource.external_url %}
            <div class="loading-state" id="loading-state">
                <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                <p>{% trans "Loading audio..." %}</p>
            </div>
            <iframe id="audio-iframe" src="{{ resource.external_url }}" style="display: none;" onload="hideLoading()"></iframe>
        {% else %}
            <div class="error-state">
                <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                <h4>{% trans "Audio Not Available" %}</h4>
                <p>{% trans "The audio content for this resource is not currently available." %}</p>
            </div>
        {% endif %}

        <div class="audio-controls">
            <button class="control-btn" onclick="skipBackward()" title="{% trans 'Skip backward 30s' %}">
                <i class="fas fa-backward"></i>
            </button>
            
            <button class="control-btn" onclick="skipBackward(10)" title="{% trans 'Skip backward 10s' %}">
                <i class="fas fa-step-backward"></i>
            </button>
            
            <button class="control-btn play-pause-btn" id="play-pause-btn" onclick="togglePlayPause()" title="{% trans 'Play/Pause' %}">
                <i class="fas fa-play"></i>
            </button>
            
            <button class="control-btn" onclick="skipForward(10)" title="{% trans 'Skip forward 10s' %}">
                <i class="fas fa-step-forward"></i>
            </button>
            
            <button class="control-btn" onclick="skipForward()" title="{% trans 'Skip forward 30s' %}">
                <i class="fas fa-forward"></i>
            </button>
        </div>

        <div class="progress-container">
            <div class="progress-bar" id="progress-bar" onclick="seekTo(event)">
                <div class="progress-fill" id="progress-fill"></div>
                <div class="progress-handle" id="progress-handle"></div>
            </div>
            <div class="time-display">
                <span id="current-time">0:00</span>
                <span id="total-time">0:00</span>
            </div>
        </div>

        <div class="volume-container">
            <i class="fas fa-volume-down"></i>
            <input type="range" class="volume-slider" id="volume-slider" min="0" max="100" value="100" onchange="changeVolume()">
            <i class="fas fa-volume-up"></i>
        </div>

        <div class="additional-controls">
            <div class="control-option">
                <i class="fas fa-tachometer-alt"></i>
                <select id="speed-select" onchange="changePlaybackSpeed()">
                    <option value="0.5">0.5x</option>
                    <option value="0.75">0.75x</option>
                    <option value="1" selected>1x</option>
                    <option value="1.25">1.25x</option>
                    <option value="1.5">1.5x</option>
                    <option value="2">2x</option>
                </select>
            </div>
            
            <div class="control-option" onclick="toggleLoop()">
                <i class="fas fa-redo" id="loop-icon"></i>
                <span>{% trans "Loop" %}</span>
            </div>
            
            <div class="control-option" onclick="toggleShuffle()">
                <i class="fas fa-random" id="shuffle-icon"></i>
                <span>{% trans "Shuffle" %}</span>
            </div>
        </div>
    </div>

    <div class="audio-info">
        <div class="info-row">
            <strong>{% trans "Duration:" %}</strong>
            <span id="duration-display">
                {% if resource.duration %}
                    {{ resource.duration }}
                {% else %}
                    {% trans "Unknown" %}
                {% endif %}
            </span>
        </div>
        <div class="info-row">
            <strong>{% trans "Format:" %}</strong>
            <span>{{ resource.file_format|upper }}</span>
        </div>
        {% if resource.file_size_mb %}
            <div class="info-row">
                <strong>{% trans "File Size:" %}</strong>
                <span>{{ resource.file_size_mb }} MB</span>
            </div>
        {% endif %}
        {% if resource.category %}
            <div class="info-row">
                <strong>{% trans "Category:" %}</strong>
                <span>{{ resource.category.name }}</span>
            </div>
        {% endif %}
    </div>

    <div class="close-controls">
        <a href="{% url 'library:digital_resource_detail' resource.id %}" class="close-btn">
            <i class="fas fa-times"></i>
            {% trans "Close Player" %}
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let audioElement = null;
let isPlaying = false;
let isLooping = false;
let listenStartTime = Date.now();
let lastListenPosition = 0;

document.addEventListener('DOMContentLoaded', function() {
    audioElement = document.getElementById('audio-element');
    
    if (audioElement) {
        setupAudioPlayer();
        loadListenPosition();
        setInterval(saveListenProgress, 30000); // Every 30 seconds
    }
});

function setupAudioPlayer() {
    audioElement.addEventListener('loadedmetadata', function() {
        document.getElementById('total-time').textContent = formatTime(audioElement.duration);
        document.getElementById('duration-display').textContent = formatTime(audioElement.duration);
    });
    
    audioElement.addEventListener('timeupdate', updateProgress);
    audioElement.addEventListener('ended', onAudioEnded);
    audioElement.addEventListener('play', onPlay);
    audioElement.addEventListener('pause', onPause);
    
    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts);
}

function loadListenPosition() {
    const positionKey = 'listen-position-{{ resource.id }}';
    const savedData = localStorage.getItem(positionKey);
    
    if (savedData) {
        const data = JSON.parse(savedData);
        const timeDiff = Date.now() - data.timestamp;
        
        // Only restore position if saved within last 24 hours
        if (timeDiff < 24 * 60 * 60 * 1000 && data.position > 10) {
            audioElement.addEventListener('loadedmetadata', function() {
                if (confirm('{% trans "Resume from where you left off?" %}')) {
                    audioElement.currentTime = data.position;
                }
            });
        }
    }
}

function saveListenProgress() {
    if (!audioElement) return;
    
    const positionKey = 'listen-position-{{ resource.id }}';
    const sessionData = {
        position: audioElement.currentTime,
        duration: audioElement.duration,
        timestamp: Date.now(),
        listenTime: Date.now() - listenStartTime
    };
    
    localStorage.setItem(positionKey, JSON.stringify(sessionData));
    lastListenPosition = audioElement.currentTime;
    
    // Send to server for analytics
    fetch('{% url "library:api_listen_progress" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token }}'
        },
        body: JSON.stringify({
            resource_id: '{{ resource.id }}',
            position: sessionData.position,
            duration: sessionData.duration,
            listen_time: sessionData.listenTime
        })
    });
}

function togglePlayPause() {
    if (!audioElement) return;
    
    if (isPlaying) {
        audioElement.pause();
    } else {
        audioElement.play();
    }
}

function onPlay() {
    isPlaying = true;
    document.getElementById('play-pause-btn').innerHTML = '<i class="fas fa-pause"></i>';
    document.getElementById('audio-artwork').classList.add('playing');
}

function onPause() {
    isPlaying = false;
    document.getElementById('play-pause-btn').innerHTML = '<i class="fas fa-play"></i>';
    document.getElementById('audio-artwork').classList.remove('playing');
}

function onAudioEnded() {
    if (isLooping) {
        audioElement.currentTime = 0;
        audioElement.play();
    } else {
        onPause();
    }
}

function skipBackward(seconds = 30) {
    if (!audioElement) return;
    audioElement.currentTime = Math.max(0, audioElement.currentTime - seconds);
}

function skipForward(seconds = 30) {
    if (!audioElement) return;
    audioElement.currentTime = Math.min(audioElement.duration, audioElement.currentTime + seconds);
}

function updateProgress() {
    if (!audioElement) return;
    
    const progress = (audioElement.currentTime / audioElement.duration) * 100;
    document.getElementById('progress-fill').style.width = progress + '%';
    document.getElementById('progress-handle').style.left = progress + '%';
    document.getElementById('current-time').textContent = formatTime(audioElement.currentTime);
}

function seekTo(event) {
    if (!audioElement) return;
    
    const progressBar = document.getElementById('progress-bar');
    const rect = progressBar.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const percentage = clickX / rect.width;
    
    audioElement.currentTime = percentage * audioElement.duration;
}

function changeVolume() {
    if (!audioElement) return;
    
    const volume = document.getElementById('volume-slider').value / 100;
    audioElement.volume = volume;
}

function changePlaybackSpeed() {
    if (!audioElement) return;
    
    const speed = document.getElementById('speed-select').value;
    audioElement.playbackRate = parseFloat(speed);
}

function toggleLoop() {
    isLooping = !isLooping;
    const icon = document.getElementById('loop-icon');
    
    if (isLooping) {
        icon.style.color = '#ffc107';
        audioElement.loop = true;
    } else {
        icon.style.color = 'white';
        audioElement.loop = false;
    }
}

function toggleShuffle() {
    // Placeholder for shuffle functionality
    const icon = document.getElementById('shuffle-icon');
    icon.style.color = icon.style.color === 'rgb(255, 193, 7)' ? 'white' : '#ffc107';
}

function formatTime(seconds) {
    if (isNaN(seconds)) return '0:00';
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

function handleKeyboardShortcuts(e) {
    if (!audioElement) return;
    
    switch(e.key) {
        case ' ':
            e.preventDefault();
            togglePlayPause();
            break;
        case 'ArrowLeft':
            e.preventDefault();
            skipBackward(10);
            break;
        case 'ArrowRight':
            e.preventDefault();
            skipForward(10);
            break;
        case 'ArrowUp':
            e.preventDefault();
            const currentVolume = parseInt(document.getElementById('volume-slider').value);
            document.getElementById('volume-slider').value = Math.min(100, currentVolume + 10);
            changeVolume();
            break;
        case 'ArrowDown':
            e.preventDefault();
            const currentVolumeDown = parseInt(document.getElementById('volume-slider').value);
            document.getElementById('volume-slider').value = Math.max(0, currentVolumeDown - 10);
            changeVolume();
            break;
        case 'l':
            e.preventDefault();
            toggleLoop();
            break;
        case 'm':
            e.preventDefault();
            audioElement.muted = !audioElement.muted;
            break;
    }
}

function showLoading() {
    const loadingState = document.getElementById('loading-state');
    if (loadingState) {
        loadingState.style.display = 'block';
    }
}

function hideLoading() {
    const loadingState = document.getElementById('loading-state');
    if (loadingState) {
        loadingState.style.display = 'none';
    }
}

function showError() {
    hideLoading();
    const player = document.querySelector('.audio-player');
    player.innerHTML = `
        <div class="error-state">
            <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
            <h4>{% trans "Audio Error" %}</h4>
            <p>{% trans "There was an error loading the audio. Please try again later." %}</p>
            <button class="control-btn" onclick="location.reload()">
                <i class="fas fa-redo me-1"></i>
                {% trans "Retry" %}
            </button>
        </div>
    `;
}

// Handle page unload
window.addEventListener('beforeunload', function() {
    saveListenProgress();
});
</script>
{% endblock %}