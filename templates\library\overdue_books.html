{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Overdue Books" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">{% trans "Overdue Books" %}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'accounts:dashboard' %}">{% trans "Dashboard" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'library:dashboard' %}">{% trans "Library" %}</a></li>
                        <li class="breadcrumb-item active">{% trans "Overdue Books" %}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        {% trans "Overdue Books Report" %}
                    </h5>
                    <div class="btn-group">
                        <button class="btn btn-warning" onclick="sendReminders()">
                            <i class="fas fa-bell me-1"></i>{% trans "Send Reminders" %}
                        </button>
                        <button class="btn btn-info" onclick="exportReport()">
                            <i class="fas fa-download me-1"></i>{% trans "Export" %}
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Student" %}</th>
                                    <th>{% trans "Book Title" %}</th>
                                    <th>{% trans "Author" %}</th>
                                    <th>{% trans "Issue Date" %}</th>
                                    <th>{% trans "Due Date" %}</th>
                                    <th>{% trans "Days Overdue" %}</th>
                                    <th>{% trans "Fine Amount" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for issue in overdue_issues %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-2">
                                                <span class="avatar-title bg-primary rounded-circle">
                                                    {{ issue.student.first_name|first }}{{ issue.student.last_name|first }}
                                                </span>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ issue.student.full_name }}</h6>
                                                <small class="text-muted">{{ issue.student.student_id }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ issue.book.title }}</td>
                                    <td>{{ issue.book.author }}</td>
                                    <td>{{ issue.issue_date }}</td>
                                    <td>{{ issue.due_date }}</td>
                                    <td>
                                        <span class="badge bg-danger">{{ issue.days_overdue }} {% trans "days" %}</span>
                                    </td>
                                    <td>
                                        <span class="text-warning fw-bold">${{ issue.fine_amount|default:0 }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="sendReminder({{ issue.id }})">
                                                <i class="fas fa-bell"></i>
                                            </button>
                                            <button class="btn btn-outline-success" onclick="returnBook({{ issue.id }})">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center">
                                        <div class="py-4">
                                            <i class="fas fa-check-circle text-success mb-3" style="font-size: 3rem;"></i>
                                            <h5 class="text-success">{% trans "No Overdue Books!" %}</h5>
                                            <p class="text-muted">{% trans "All books have been returned on time." %}</p>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% csrf_token %}

<script>
// Get CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

function sendReminders() {
    if (confirm('{% trans "Send reminders to all students with overdue books?" %}')) {
        // For now, simulate bulk reminder sending
        setTimeout(() => {
            alert('{% trans "Reminders sent successfully!" %}');
        }, 1000);
    }
}

function sendReminder(issueId) {
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    button.disabled = true;
    
    // For now, simulate sending reminder since we don't have the endpoint
    setTimeout(() => {
        alert('{% trans "Reminder sent!" %}');
        button.innerHTML = originalText;
        button.disabled = false;
    }, 1000);
}

function returnBook(issueId) {
    if (confirm('{% trans "Mark this book as returned?" %}')) {
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;
        
        const formData = new FormData();
        formData.append('borrowing_id', issueId);
        
        fetch('/library/api/return/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
            },
            body: formData
        })
        .then(response => {
            if (response.ok) {
                return response.json();
            } else {
                throw new Error('Network response was not ok');
            }
        })
        .then(data => {
            if (data.success) {
                alert('{% trans "Book returned successfully!" %}');
                location.reload();
            } else {
                alert(data.message || '{% trans "Error returning book. Please try again." %}');
                button.innerHTML = originalText;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{% trans "Error returning book. Please try again." %}');
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
}

function exportReport() {
    window.open('/library/reports/overdue/export/', '_blank');
}

// Add event listeners when document is ready
document.addEventListener('DOMContentLoaded', function() {
    // Add click handlers to buttons
    document.querySelectorAll('[onclick^="sendReminder"]').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const issueId = this.getAttribute('onclick').match(/\d+/)[0];
            sendReminder(issueId);
        });
    });
    
    document.querySelectorAll('[onclick^="returnBook"]').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const issueId = this.getAttribute('onclick').match(/\d+/)[0];
            returnBook(issueId);
        });
    });
});
</script>
{% endblock %}