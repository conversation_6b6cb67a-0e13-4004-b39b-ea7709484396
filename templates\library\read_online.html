{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Reading" %}: {{ resource.title }}{% endblock %}

{% block extra_css %}
<style>
body {
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
}

.reader-header {
    background: #fff;
    border-bottom: 1px solid #ddd;
    padding: 10px 20px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    display: flex;
    justify-content: between;
    align-items: center;
}

.reader-title {
    font-size: 1.1em;
    font-weight: 600;
    color: #333;
    margin: 0;
    flex-grow: 1;
}

.reader-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.reader-content {
    margin-top: 60px;
    height: calc(100vh - 60px);
    background: white;
}

.pdf-viewer {
    width: 100%;
    height: 100%;
    border: none;
}

.text-reader {
    max-width: 800px;
    margin: 0 auto;
    padding: 40px 20px;
    line-height: 1.6;
    font-size: 16px;
    color: #333;
}

.reader-toolbar {
    display: flex;
    gap: 5px;
    align-items: center;
}

.toolbar-btn {
    background: none;
    border: 1px solid #ddd;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.toolbar-btn:hover {
    background: #f0f0f0;
}

.font-size-control {
    display: flex;
    align-items: center;
    gap: 5px;
}

.font-size-control input {
    width: 60px;
    padding: 2px 5px;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.progress-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    height: 3px;
    background: #007bff;
    transition: width 0.3s ease;
    z-index: 1001;
}

.reading-progress {
    font-size: 12px;
    color: #666;
    margin-left: 10px;
}

.fullscreen-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
}

.close-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 5px 15px;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
}

.bookmark-btn {
    background: #ffc107;
    color: #333;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
}

.bookmark-btn.active {
    background: #ff9800;
    color: white;
}

@media (max-width: 768px) {
    .reader-header {
        padding: 10px;
        flex-wrap: wrap;
    }
    
    .reader-title {
        font-size: 1em;
        margin-bottom: 5px;
        width: 100%;
    }
    
    .reader-controls {
        width: 100%;
        justify-content: space-between;
    }
    
    .reader-content {
        margin-top: 80px;
        height: calc(100vh - 80px);
    }
    
    .text-reader {
        padding: 20px 15px;
        font-size: 14px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="reader-header">
    <h1 class="reader-title">{{ resource.title }}</h1>
    <div class="reader-controls">
        <div class="reader-toolbar">
            {% if resource.resource_type == 'document' or resource.file_format == 'pdf' %}
                <button class="toolbar-btn" onclick="zoomIn()">
                    <i class="fas fa-search-plus"></i>
                </button>
                <button class="toolbar-btn" onclick="zoomOut()">
                    <i class="fas fa-search-minus"></i>
                </button>
                <button class="toolbar-btn" onclick="fitToWidth()">
                    <i class="fas fa-arrows-alt-h"></i>
                </button>
            {% else %}
                <div class="font-size-control">
                    <button class="toolbar-btn" onclick="decreaseFontSize()">A-</button>
                    <input type="number" id="font-size" value="16" min="12" max="24" onchange="changeFontSize()">
                    <button class="toolbar-btn" onclick="increaseFontSize()">A+</button>
                </div>
            {% endif %}
            
            <button class="bookmark-btn" onclick="toggleBookmark()" id="bookmark-btn">
                <i class="fas fa-bookmark"></i>
            </button>
            
            <button class="fullscreen-btn" onclick="toggleFullscreen()">
                <i class="fas fa-expand"></i>
            </button>
        </div>
        
        <div class="reading-progress">
            <span id="progress-text">0%</span>
        </div>
        
        <a href="{% url 'library:digital_resource_detail' resource.id %}" class="close-btn">
            <i class="fas fa-times"></i>
            {% trans "Close" %}
        </a>
    </div>
</div>

<div class="reader-content" id="reader-content">
    {% if resource.file_path %}
        {% if resource.file_format == 'pdf' %}
            <iframe src="{{ resource.file_path.url }}#toolbar=1&navpanes=1&scrollbar=1" 
                    class="pdf-viewer" 
                    id="pdf-viewer"
                    title="{{ resource.title }}">
            </iframe>
        {% elif resource.file_format in 'txt,html' %}
            <div class="text-reader" id="text-reader">
                <!-- Text content will be loaded here -->
                <p>{% trans "Loading content..." %}</p>
            </div>
        {% else %}
            <div class="text-center p-5">
                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                <h4>{% trans "Unsupported Format" %}</h4>
                <p>{% trans "This file format cannot be displayed in the browser." %}</p>
                <a href="{% url 'library:download_resource' resource.id %}" class="btn btn-primary">
                    <i class="fas fa-download me-2"></i>
                    {% trans "Download File" %}
                </a>
            </div>
        {% endif %}
    {% elif resource.external_url %}
        <iframe src="{{ resource.external_url }}" 
                class="pdf-viewer" 
                title="{{ resource.title }}">
        </iframe>
    {% else %}
        <div class="text-center p-5">
            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
            <h4>{% trans "Content Not Available" %}</h4>
            <p>{% trans "The content for this resource is not currently available." %}</p>
        </div>
    {% endif %}
</div>

<div class="progress-bar" id="progress-bar" style="width: 0%"></div>
{% endblock %}

{% block extra_js %}
<script>
let currentFontSize = 16;
let isBookmarked = false;
let readingStartTime = Date.now();
let lastScrollPosition = 0;

document.addEventListener('DOMContentLoaded', function() {
    // Load user preferences
    loadUserPreferences();
    
    // Track reading progress
    trackReadingProgress();
    
    // Auto-save reading position
    setInterval(saveReadingPosition, 30000); // Every 30 seconds
    
    // Load text content if needed
    {% if resource.file_format in 'txt,html' %}
        loadTextContent();
    {% endif %}
});

function loadUserPreferences() {
    const savedFontSize = localStorage.getItem('reader-font-size');
    if (savedFontSize) {
        currentFontSize = parseInt(savedFontSize);
        document.getElementById('font-size').value = currentFontSize;
        changeFontSize();
    }
    
    const bookmarkKey = 'bookmark-{{ resource.id }}';
    isBookmarked = localStorage.getItem(bookmarkKey) === 'true';
    updateBookmarkButton();
}

function increaseFontSize() {
    if (currentFontSize < 24) {
        currentFontSize += 2;
        document.getElementById('font-size').value = currentFontSize;
        changeFontSize();
    }
}

function decreaseFontSize() {
    if (currentFontSize > 12) {
        currentFontSize -= 2;
        document.getElementById('font-size').value = currentFontSize;
        changeFontSize();
    }
}

function changeFontSize() {
    currentFontSize = parseInt(document.getElementById('font-size').value);
    const textReader = document.getElementById('text-reader');
    if (textReader) {
        textReader.style.fontSize = currentFontSize + 'px';
    }
    localStorage.setItem('reader-font-size', currentFontSize);
}

function zoomIn() {
    const iframe = document.getElementById('pdf-viewer');
    if (iframe) {
        iframe.contentWindow.postMessage({action: 'zoom', direction: 'in'}, '*');
    }
}

function zoomOut() {
    const iframe = document.getElementById('pdf-viewer');
    if (iframe) {
        iframe.contentWindow.postMessage({action: 'zoom', direction: 'out'}, '*');
    }
}

function fitToWidth() {
    const iframe = document.getElementById('pdf-viewer');
    if (iframe) {
        iframe.contentWindow.postMessage({action: 'fit', type: 'width'}, '*');
    }
}

function toggleBookmark() {
    isBookmarked = !isBookmarked;
    const bookmarkKey = 'bookmark-{{ resource.id }}';
    localStorage.setItem(bookmarkKey, isBookmarked.toString());
    updateBookmarkButton();
    
    // Save bookmark to server
    fetch('{% url "library:api_bookmark" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token }}'
        },
        body: JSON.stringify({
            resource_id: '{{ resource.id }}',
            bookmarked: isBookmarked
        })
    });
}

function updateBookmarkButton() {
    const btn = document.getElementById('bookmark-btn');
    if (isBookmarked) {
        btn.classList.add('active');
        btn.innerHTML = '<i class="fas fa-bookmark"></i>';
    } else {
        btn.classList.remove('active');
        btn.innerHTML = '<i class="far fa-bookmark"></i>';
    }
}

function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
    } else {
        document.exitFullscreen();
    }
}

function trackReadingProgress() {
    const content = document.getElementById('reader-content');
    const progressBar = document.getElementById('progress-bar');
    const progressText = document.getElementById('progress-text');
    
    function updateProgress() {
        const scrollTop = content.scrollTop || window.pageYOffset;
        const scrollHeight = content.scrollHeight || document.body.scrollHeight;
        const clientHeight = content.clientHeight || window.innerHeight;
        
        const progress = Math.min(100, Math.max(0, (scrollTop / (scrollHeight - clientHeight)) * 100));
        
        progressBar.style.width = progress + '%';
        progressText.textContent = Math.round(progress) + '%';
        
        lastScrollPosition = scrollTop;
    }
    
    content.addEventListener('scroll', updateProgress);
    window.addEventListener('scroll', updateProgress);
    
    // Initial update
    updateProgress();
}

function saveReadingPosition() {
    const positionKey = 'reading-position-{{ resource.id }}';
    const sessionData = {
        position: lastScrollPosition,
        timestamp: Date.now(),
        duration: Date.now() - readingStartTime
    };
    
    localStorage.setItem(positionKey, JSON.stringify(sessionData));
    
    // Send to server for analytics
    fetch('{% url "library:api_reading_progress" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token }}'
        },
        body: JSON.stringify({
            resource_id: '{{ resource.id }}',
            position: lastScrollPosition,
            duration: sessionData.duration
        })
    });
}

function loadTextContent() {
    fetch('{% url "library:api_text_content" resource.id %}')
        .then(response => response.text())
        .then(content => {
            document.getElementById('text-reader').innerHTML = content;
        })
        .catch(error => {
            document.getElementById('text-reader').innerHTML = 
                '<p class="text-danger">{% trans "Failed to load content." %}</p>';
        });
}

// Handle page unload
window.addEventListener('beforeunload', function() {
    saveReadingPosition();
});

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey || e.metaKey) {
        switch(e.key) {
            case '=':
            case '+':
                e.preventDefault();
                increaseFontSize();
                break;
            case '-':
                e.preventDefault();
                decreaseFontSize();
                break;
            case 'b':
                e.preventDefault();
                toggleBookmark();
                break;
            case 'f':
                e.preventDefault();
                toggleFullscreen();
                break;
        }
    }
    
    // ESC to exit fullscreen
    if (e.key === 'Escape' && document.fullscreenElement) {
        document.exitFullscreen();
    }
});
</script>
{% endblock %}