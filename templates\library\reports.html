{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Library Reports" %}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/library.css' %}">
<style>
.reports-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px 0;
    margin-bottom: 30px;
}

.report-section {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.report-filters {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.filter-row {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    min-width: 150px;
}

.filter-group label {
    font-weight: 500;
    margin-bottom: 5px;
    color: #333;
}

.report-type-tabs {
    border-bottom: 2px solid #f0f0f0;
    margin-bottom: 20px;
}

.report-type-tabs .nav-link {
    border: none;
    border-bottom: 2px solid transparent;
    color: #666;
    font-weight: 500;
}

.report-type-tabs .nav-link.active {
    border-bottom-color: #007bff;
    color: #007bff;
    background: none;
}

.export-controls {
    text-align: right;
    margin-bottom: 20px;
}

.btn-export {
    margin-left: 10px;
}

.report-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.report-table th,
.report-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.report-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.report-table tbody tr:hover {
    background-color: #f5f5f5;
}

.report-summary {
    background: #e9ecef;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.summary-item {
    display: inline-block;
    margin-right: 30px;
    margin-bottom: 10px;
}

.summary-label {
    font-weight: 600;
    color: #333;
}

.summary-value {
    color: #007bff;
    font-size: 1.2em;
    font-weight: bold;
}

.no-data {
    text-align: center;
    padding: 40px;
    color: #666;
}

.no-data i {
    font-size: 3em;
    margin-bottom: 20px;
    opacity: 0.5;
}

@media (max-width: 768px) {
    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-group {
        min-width: auto;
        width: 100%;
    }
    
    .export-controls {
        text-align: center;
    }
    
    .report-table {
        font-size: 0.9em;
    }
    
    .summary-item {
        display: block;
        margin-right: 0;
        text-align: center;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="reports-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-file-alt me-3"></i>
                    {% trans "Library Reports" %}
                </h1>
                <p class="mb-0 opacity-75">
                    {% trans "Generate detailed reports for library operations" %}
                </p>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{% url 'library:dashboard' %}" class="btn btn-light">
                    <i class="fas fa-arrow-left me-2"></i>
                    {% trans "Back to Dashboard" %}
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Report Filters -->
    <div class="report-section">
        <form method="get" class="mb-0">
            <div class="report-filters">
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="type">{% trans "Report Type" %}</label>
                        <select name="type" id="type" class="form-select">
                            <option value="summary" {% if report_type == 'summary' %}selected{% endif %}>
                                {% trans "Summary Report" %}
                            </option>
                            <option value="borrowing" {% if report_type == 'borrowing' %}selected{% endif %}>
                                {% trans "Borrowing Report" %}
                            </option>
                            <option value="popular" {% if report_type == 'popular' %}selected{% endif %}>
                                {% trans "Popular Books Report" %}
                            </option>
                            <option value="acquisition" {% if report_type == 'acquisition' %}selected{% endif %}>
                                {% trans "Acquisition Recommendations" %}
                            </option>
                            <option value="digital" {% if report_type == 'digital' %}selected{% endif %}>
                                {% trans "Digital Library Usage" %}
                            </option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="date_from">{% trans "From Date" %}</label>
                        <input type="date" 
                               id="date_from" 
                               name="date_from" 
                               class="form-control" 
                               value="{{ date_from|date:'Y-m-d' }}">
                    </div>
                    <div class="filter-group">
                        <label for="date_to">{% trans "To Date" %}</label>
                        <input type="date" 
                               id="date_to" 
                               name="date_to" 
                               class="form-control" 
                               value="{{ date_to|date:'Y-m-d' }}">
                    </div>
                    <div class="filter-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>
                            {% trans "Generate Report" %}
                        </button>
                    </div>
                </div>
            </div>
        </form>

        <!-- Export Controls -->
        <div class="export-controls">
            <a href="?{{ request.GET.urlencode }}&format=csv" class="btn btn-success btn-export">
                <i class="fas fa-file-csv me-2"></i>
                {% trans "Export CSV" %}
            </a>
            <a href="?{{ request.GET.urlencode }}&format=json" class="btn btn-info btn-export">
                <i class="fas fa-code me-2"></i>
                {% trans "Export JSON" %}
            </a>
            <button onclick="window.print()" class="btn btn-secondary btn-export">
                <i class="fas fa-print me-2"></i>
                {% trans "Print" %}
            </button>
        </div>
    </div>

    <!-- Report Content -->
    <div class="report-section">
        {% if report_type == 'borrowing' %}
            <h3>
                <i class="fas fa-hand-holding me-2"></i>
                {% trans "Borrowing Report" %}
            </h3>
            <p class="text-muted">
                {% blocktrans %}Report period: {{ date_from }} to {{ date_to }}{% endblocktrans %}
            </p>

            {% if borrowings %}
                <div class="report-summary">
                    <div class="summary-item">
                        <div class="summary-label">{% trans "Total Borrowings:" %}</div>
                        <div class="summary-value">{{ total_borrowings }}</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">{% trans "Returned:" %}</div>
                        <div class="summary-value">{{ returned_count }}</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">{% trans "Overdue:" %}</div>
                        <div class="summary-value">{{ overdue_count }}</div>
                    </div>
                </div>

                <table class="report-table">
                    <thead>
                        <tr>
                            <th>{% trans "Book Title" %}</th>
                            <th>{% trans "Borrower" %}</th>
                            <th>{% trans "Borrow Date" %}</th>
                            <th>{% trans "Due Date" %}</th>
                            <th>{% trans "Status" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for borrowing in borrowings %}
                            <tr>
                                <td>{{ borrowing.book.title }}</td>
                                <td>{{ borrowing.borrower_name }}</td>
                                <td>{{ borrowing.borrow_date|date:"M d, Y" }}</td>
                                <td>{{ borrowing.due_date|date:"M d, Y" }}</td>
                                <td>
                                    <span class="badge bg-{% if borrowing.status == 'returned' %}success{% elif borrowing.status == 'overdue' %}danger{% else %}primary{% endif %}">
                                        {{ borrowing.get_status_display }}
                                    </span>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <div class="no-data">
                    <i class="fas fa-hand-holding"></i>
                    <h4>{% trans "No Borrowing Data" %}</h4>
                    <p>{% trans "No borrowing activity found for the selected period." %}</p>
                </div>
            {% endif %}

        {% elif report_type == 'popular' %}
            <h3>
                <i class="fas fa-star me-2"></i>
                {% trans "Popular Books Report" %}
            </h3>
            <p class="text-muted">
                {% blocktrans %}Most borrowed books from {{ date_from }} to {{ date_to }}{% endblocktrans %}
            </p>

            {% if popular_books %}
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>{% trans "Rank" %}</th>
                            <th>{% trans "Book Title" %}</th>
                            <th>{% trans "Author(s)" %}</th>
                            <th>{% trans "Category" %}</th>
                            <th>{% trans "Borrow Count" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for book in popular_books %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td>{{ book.title }}</td>
                                <td>{{ book.get_authors_display|default:"Unknown" }}</td>
                                <td>{{ book.category.name|default:"Uncategorized" }}</td>
                                <td>
                                    <span class="badge bg-primary">{{ book.borrow_count }}</span>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <div class="no-data">
                    <i class="fas fa-star"></i>
                    <h4>{% trans "No Popular Books Data" %}</h4>
                    <p>{% trans "No books were borrowed during the selected period." %}</p>
                </div>
            {% endif %}

        {% elif report_type == 'acquisition' %}
            <h3>
                <i class="fas fa-shopping-cart me-2"></i>
                {% trans "Acquisition Recommendations" %}
            </h3>
            <p class="text-muted">
                {% trans "Categories with high demand and low availability" %}
            </p>

            {% if recommendations %}
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>{% trans "Category" %}</th>
                            <th>{% trans "Total Books" %}</th>
                            <th>{% trans "Available" %}</th>
                            <th>{% trans "Demand (Borrows)" %}</th>
                            <th>{% trans "Recommendation" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for category in recommendations %}
                            <tr>
                                <td>{{ category.name }}</td>
                                <td>{{ category.book_count }}</td>
                                <td>{{ category.available_count }}</td>
                                <td>{{ category.borrow_count }}</td>
                                <td>
                                    {% if category.borrow_count > category.book_count %}
                                        <span class="badge bg-danger">{% trans "High Priority" %}</span>
                                    {% elif category.available_count < 3 %}
                                        <span class="badge bg-warning">{% trans "Medium Priority" %}</span>
                                    {% else %}
                                        <span class="badge bg-info">{% trans "Low Priority" %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <div class="no-data">
                    <i class="fas fa-shopping-cart"></i>
                    <h4>{% trans "No Recommendations" %}</h4>
                    <p>{% trans "All categories have adequate stock levels." %}</p>
                </div>
            {% endif %}

        {% elif report_type == 'digital' %}
            <h3>
                <i class="fas fa-tablet-alt me-2"></i>
                {% trans "Digital Library Usage Report" %}
            </h3>
            <p class="text-muted">
                {% blocktrans %}Digital resource usage from {{ date_from }} to {{ date_to }}{% endblocktrans %}
            </p>

            {% if digital_usage %}
                <div class="report-summary">
                    <div class="summary-item">
                        <div class="summary-label">{% trans "Total Views:" %}</div>
                        <div class="summary-value">{{ total_views }}</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">{% trans "Total Downloads:" %}</div>
                        <div class="summary-value">{{ total_downloads }}</div>
                    </div>
                </div>

                <table class="report-table">
                    <thead>
                        <tr>
                            <th>{% trans "Resource Title" %}</th>
                            <th>{% trans "User" %}</th>
                            <th>{% trans "Action" %}</th>
                            <th>{% trans "Date" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for usage in digital_usage %}
                            <tr>
                                <td>{{ usage.resource.title }}</td>
                                <td>{{ usage.user.username }}</td>
                                <td>
                                    <span class="badge bg-{% if usage.action == 'view' %}info{% elif usage.action == 'download' %}success{% else %}secondary{% endif %}">
                                        {{ usage.get_action_display }}
                                    </span>
                                </td>
                                <td>{{ usage.created_at|date:"M d, Y H:i" }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <div class="no-data">
                    <i class="fas fa-tablet-alt"></i>
                    <h4>{% trans "No Digital Usage Data" %}</h4>
                    <p>{% trans "No digital library activity found for the selected period." %}</p>
                </div>
            {% endif %}

        {% else %}
            <!-- Summary Report (Default) -->
            <h3>
                <i class="fas fa-chart-pie me-2"></i>
                {% trans "Library Summary Report" %}
            </h3>
            <p class="text-muted">
                {% blocktrans %}Overview of library operations from {{ date_from }} to {{ date_to }}{% endblocktrans %}
            </p>

            <div class="row">
                <div class="col-md-6">
                    <h5>{% trans "Collection Statistics" %}</h5>
                    <ul class="list-unstyled">
                        <li><strong>{% trans "Total Books:" %}</strong> {{ total_books|default:0 }}</li>
                        <li><strong>{% trans "Digital Resources:" %}</strong> {{ total_digital|default:0 }}</li>
                        <li><strong>{% trans "Active Borrowings:" %}</strong> {{ active_borrowings|default:0 }}</li>
                        <li><strong>{% trans "Overdue Books:" %}</strong> {{ overdue_count|default:0 }}</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>{% trans "Activity Summary" %}</h5>
                    <ul class="list-unstyled">
                        <li><strong>{% trans "Recent Borrowings:" %}</strong> {{ recent_borrowings|default:0 }}</li>
                        <li><strong>{% trans "Recent Returns:" %}</strong> {{ recent_returns|default:0 }}</li>
                        <li><strong>{% trans "Digital Views:" %}</strong> {{ digital_views_week|default:0 }}</li>
                    </ul>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate report when type changes
    document.getElementById('type').addEventListener('change', function() {
        this.form.submit();
    });
    
    // Print styles
    const style = document.createElement('style');
    style.textContent = `
        @media print {
            .reports-header, .export-controls, .btn { display: none !important; }
            .report-section { box-shadow: none; border: 1px solid #ddd; }
            .report-table { font-size: 12px; }
        }
    `;
    document.head.appendChild(style);
});
</script>
{% endblock %}