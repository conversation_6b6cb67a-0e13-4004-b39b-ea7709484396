{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Library Analytics" %}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/library.css' %}">
<style>
.analytics-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px 0;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    margin-bottom: 20px;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 2.5em;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stat-icon {
    font-size: 2em;
    margin-bottom: 10px;
    opacity: 0.7;
}

.chart-container {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.chart-title {
    font-size: 1.2em;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 10px;
}

.date-filter {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}
</style>
{% endblock %}

{% block content %}
<div class="analytics-header">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1><i class="fas fa-chart-bar me-3"></i>{% trans "Library Analytics" %}</h1>
                <p class="lead">{% trans "Comprehensive library statistics and insights" %}</p>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- Date Filter -->
    <div class="row">
        <div class="col-12">
            <div class="date-filter">
                <form method="get" class="row g-3 align-items-end">
                    <div class="col-md-3">
                        <label for="date_from" class="form-label">{% trans "From Date" %}</label>
                        <input type="date" name="date_from" id="date_from" class="form-control" value="{{ date_from }}">
                    </div>
                    <div class="col-md-3">
                        <label for="date_to" class="form-label">{% trans "To Date" %}</label>
                        <input type="date" name="date_to" id="date_to" class="form-control" value="{{ date_to }}">
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter me-1"></i>
                            {% trans "Apply Filter" %}
                        </button>
                    </div>
                    <div class="col-md-3 text-end">
                        <a href="{% url 'library:dashboard' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            {% trans "Back to Dashboard" %}
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="stat-card">
                <div class="stat-icon text-primary">
                    <i class="fas fa-book"></i>
                </div>
                <div class="stat-number text-primary">{{ total_books|default:0 }}</div>
                <div class="stat-label">{% trans "Total Books" %}</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stat-card">
                <div class="stat-icon text-success">
                    <i class="fas fa-exchange-alt"></i>
                </div>
                <div class="stat-number text-success">{{ total_borrowings|default:0 }}</div>
                <div class="stat-label">{% trans "Total Borrowings" %}</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stat-card">
                <div class="stat-icon text-warning">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-number text-warning">{{ active_borrowings|default:0 }}</div>
                <div class="stat-label">{% trans "Active Borrowings" %}</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stat-card">
                <div class="stat-icon text-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-number text-danger">{{ overdue_books|default:0 }}</div>
                <div class="stat-label">{% trans "Overdue Books" %}</div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row">
        <div class="col-lg-6">
            <div class="chart-container">
                <div class="chart-title">
                    <i class="fas fa-chart-line me-2"></i>
                    {% trans "Borrowing Trends" %}
                </div>
                <canvas id="borrowingTrendsChart" height="300"></canvas>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="chart-container">
                <div class="chart-title">
                    <i class="fas fa-chart-pie me-2"></i>
                    {% trans "Popular Categories" %}
                </div>
                <canvas id="categoriesChart" height="300"></canvas>
            </div>
        </div>
    </div>

    <!-- Popular Books -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <div class="chart-title">
                    <i class="fas fa-star me-2"></i>
                    {% trans "Most Popular Books" %}
                </div>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% trans "Book Title" %}</th>
                                <th>{% trans "Author" %}</th>
                                <th>{% trans "Category" %}</th>
                                <th>{% trans "Times Borrowed" %}</th>
                                <th>{% trans "Availability" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for book in popular_books %}
                            <tr>
                                <td>{{ book.title }}</td>
                                <td>{{ book.author }}</td>
                                <td>{{ book.category.name }}</td>
                                <td><span class="badge bg-primary">{{ book.borrow_count }}</span></td>
                                <td>
                                    {% if book.available_copies > 0 %}
                                        <span class="badge bg-success">{% trans "Available" %}</span>
                                    {% else %}
                                        <span class="badge bg-danger">{% trans "Not Available" %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center text-muted">{% trans "No data available" %}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}