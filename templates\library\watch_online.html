{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Watching" %}: {{ resource.title }}{% endblock %}

{% block extra_css %}
<style>
body {
    margin: 0;
    padding: 0;
    background-color: #000;
    color: white;
}

.video-header {
    background: rgba(0,0,0,0.8);
    padding: 15px 20px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: opacity 0.3s ease;
}

.video-header.hidden {
    opacity: 0;
    pointer-events: none;
}

.video-title {
    font-size: 1.2em;
    font-weight: 600;
    margin: 0;
    flex-grow: 1;
}

.video-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.video-container {
    position: relative;
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.video-player {
    width: 100%;
    height: 100%;
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.video-iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.control-btn {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s ease;
}

.control-btn:hover {
    background: rgba(255,255,255,0.3);
}

.close-btn {
    background: #dc3545;
    border: none;
    color: white;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    font-size: 14px;
}

.video-info {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    background: rgba(0,0,0,0.7);
    padding: 15px;
    border-radius: 8px;
    display: none;
}

.video-info.show {
    display: block;
}

.playback-speed {
    display: flex;
    align-items: center;
    gap: 5px;
}

.playback-speed select {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 4px 8px;
    border-radius: 3px;
}

.quality-selector {
    display: flex;
    align-items: center;
    gap: 5px;
}

.quality-selector select {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 4px 8px;
    border-radius: 3px;
}

.video-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: rgba(255,255,255,0.3);
}

.video-progress-bar {
    height: 100%;
    background: #007bff;
    width: 0%;
    transition: width 0.1s ease;
}

.loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 2em;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.error-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    background: rgba(220, 53, 69, 0.9);
    padding: 20px;
    border-radius: 8px;
    max-width: 400px;
}

@media (max-width: 768px) {
    .video-header {
        padding: 10px;
        flex-wrap: wrap;
    }
    
    .video-title {
        font-size: 1em;
        margin-bottom: 5px;
        width: 100%;
    }
    
    .video-controls {
        width: 100%;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 5px;
    }
    
    .control-btn {
        padding: 6px 10px;
        font-size: 12px;
    }
    
    .video-info {
        bottom: 10px;
        left: 10px;
        right: 10px;
        padding: 10px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="video-header" id="video-header">
    <h1 class="video-title">{{ resource.title }}</h1>
    <div class="video-controls">
        <div class="playback-speed">
            <label for="speed-select">{% trans "Speed:" %}</label>
            <select id="speed-select" onchange="changePlaybackSpeed()">
                <option value="0.5">0.5x</option>
                <option value="0.75">0.75x</option>
                <option value="1" selected>1x</option>
                <option value="1.25">1.25x</option>
                <option value="1.5">1.5x</option>
                <option value="2">2x</option>
            </select>
        </div>
        
        <div class="quality-selector">
            <label for="quality-select">{% trans "Quality:" %}</label>
            <select id="quality-select" onchange="changeQuality()">
                <option value="auto" selected>{% trans "Auto" %}</option>
                <option value="720p">720p</option>
                <option value="480p">480p</option>
                <option value="360p">360p</option>
            </select>
        </div>
        
        <button class="control-btn" onclick="toggleInfo()">
            <i class="fas fa-info-circle"></i>
            {% trans "Info" %}
        </button>
        
        <button class="control-btn" onclick="toggleFullscreen()">
            <i class="fas fa-expand"></i>
            {% trans "Fullscreen" %}
        </button>
        
        <a href="{% url 'library:digital_resource_detail' resource.id %}" class="close-btn">
            <i class="fas fa-times"></i>
            {% trans "Close" %}
        </a>
    </div>
</div>

<div class="video-container" id="video-container">
    {% if resource.file_path %}
        <video class="video-player" 
               id="video-player" 
               controls 
               preload="metadata"
               onloadstart="showLoading()"
               oncanplay="hideLoading()"
               onerror="showError()"
               ontimeupdate="updateProgress()">
            <source src="{{ resource.file_path.url }}" type="video/mp4">
            <source src="{{ resource.file_path.url }}" type="video/webm">
            <source src="{{ resource.file_path.url }}" type="video/ogg">
            {% trans "Your browser does not support the video tag." %}
        </video>
    {% elif resource.external_url %}
        <iframe class="video-iframe" 
                id="video-iframe"
                src="{{ resource.external_url }}" 
                title="{{ resource.title }}" 
                allowfullscreen
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture">
        </iframe>
    {% else %}
        <div class="error-message">
            <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
            <h4>{% trans "Video Not Available" %}</h4>
            <p>{% trans "The video content for this resource is not currently available." %}</p>
        </div>
    {% endif %}
    
    <div class="loading-spinner" id="loading-spinner" style="display: none;">
        <i class="fas fa-spinner"></i>
    </div>
    
    <div class="video-progress" id="video-progress">
        <div class="video-progress-bar" id="progress-bar"></div>
    </div>
</div>

<div class="video-info" id="video-info">
    <h5>{{ resource.title }}</h5>
    {% if resource.get_authors_display %}
        <p><strong>{% trans "Authors: <AUTHORS>
    {% endif %}
    {% if resource.duration %}
        <p><strong>{% trans "Duration:" %}</strong> {{ resource.duration }}</p>
    {% endif %}
    {% if resource.description %}
        <p><strong>{% trans "Description:" %}</strong></p>
        <p>{{ resource.description|truncatewords:50 }}</p>
    {% endif %}
    <p><strong>{% trans "Format:" %}</strong> {{ resource.file_format|upper }}</p>
    {% if resource.file_size_mb %}
        <p><strong>{% trans "File Size:" %}</strong> {{ resource.file_size_mb }} MB</p>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
let videoPlayer = null;
let watchStartTime = Date.now();
let lastWatchPosition = 0;
let headerTimeout = null;

document.addEventListener('DOMContentLoaded', function() {
    videoPlayer = document.getElementById('video-player');
    
    if (videoPlayer) {
        // Load saved position
        loadWatchPosition();
        
        // Auto-hide header
        setupHeaderAutoHide();
        
        // Track watch progress
        setInterval(saveWatchProgress, 30000); // Every 30 seconds
        
        // Keyboard shortcuts
        setupKeyboardShortcuts();
    }
});

function loadWatchPosition() {
    const positionKey = 'watch-position-{{ resource.id }}';
    const savedData = localStorage.getItem(positionKey);
    
    if (savedData && videoPlayer) {
        const data = JSON.parse(savedData);
        const timeDiff = Date.now() - data.timestamp;
        
        // Only restore position if saved within last 24 hours
        if (timeDiff < 24 * 60 * 60 * 1000 && data.position > 10) {
            videoPlayer.addEventListener('loadedmetadata', function() {
                if (confirm('{% trans "Resume from where you left off?" %}')) {
                    videoPlayer.currentTime = data.position;
                }
            });
        }
    }
}

function saveWatchProgress() {
    if (!videoPlayer) return;
    
    const positionKey = 'watch-position-{{ resource.id }}';
    const sessionData = {
        position: videoPlayer.currentTime,
        duration: videoPlayer.duration,
        timestamp: Date.now(),
        watchTime: Date.now() - watchStartTime
    };
    
    localStorage.setItem(positionKey, JSON.stringify(sessionData));
    lastWatchPosition = videoPlayer.currentTime;
    
    // Send to server for analytics
    fetch('{% url "library:api_watch_progress" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token }}'
        },
        body: JSON.stringify({
            resource_id: '{{ resource.id }}',
            position: sessionData.position,
            duration: sessionData.duration,
            watch_time: sessionData.watchTime
        })
    });
}

function setupHeaderAutoHide() {
    const header = document.getElementById('video-header');
    const container = document.getElementById('video-container');
    
    function showHeader() {
        header.classList.remove('hidden');
        clearTimeout(headerTimeout);
        headerTimeout = setTimeout(() => {
            if (videoPlayer && !videoPlayer.paused) {
                header.classList.add('hidden');
            }
        }, 3000);
    }
    
    container.addEventListener('mousemove', showHeader);
    container.addEventListener('click', showHeader);
    
    if (videoPlayer) {
        videoPlayer.addEventListener('play', () => {
            headerTimeout = setTimeout(() => {
                header.classList.add('hidden');
            }, 3000);
        });
        
        videoPlayer.addEventListener('pause', () => {
            header.classList.remove('hidden');
            clearTimeout(headerTimeout);
        });
    }
}

function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        if (!videoPlayer) return;
        
        switch(e.key) {
            case ' ':
                e.preventDefault();
                if (videoPlayer.paused) {
                    videoPlayer.play();
                } else {
                    videoPlayer.pause();
                }
                break;
            case 'ArrowLeft':
                e.preventDefault();
                videoPlayer.currentTime = Math.max(0, videoPlayer.currentTime - 10);
                break;
            case 'ArrowRight':
                e.preventDefault();
                videoPlayer.currentTime = Math.min(videoPlayer.duration, videoPlayer.currentTime + 10);
                break;
            case 'ArrowUp':
                e.preventDefault();
                videoPlayer.volume = Math.min(1, videoPlayer.volume + 0.1);
                break;
            case 'ArrowDown':
                e.preventDefault();
                videoPlayer.volume = Math.max(0, videoPlayer.volume - 0.1);
                break;
            case 'f':
                e.preventDefault();
                toggleFullscreen();
                break;
            case 'm':
                e.preventDefault();
                videoPlayer.muted = !videoPlayer.muted;
                break;
        }
    });
}

function changePlaybackSpeed() {
    const speed = document.getElementById('speed-select').value;
    if (videoPlayer) {
        videoPlayer.playbackRate = parseFloat(speed);
    }
}

function changeQuality() {
    const quality = document.getElementById('quality-select').value;
    // This would typically involve switching video sources
    console.log('Quality changed to:', quality);
}

function toggleInfo() {
    const info = document.getElementById('video-info');
    info.classList.toggle('show');
}

function toggleFullscreen() {
    const container = document.getElementById('video-container');
    
    if (!document.fullscreenElement) {
        container.requestFullscreen().catch(err => {
            console.error('Error attempting to enable fullscreen:', err);
        });
    } else {
        document.exitFullscreen();
    }
}

function updateProgress() {
    if (!videoPlayer) return;
    
    const progress = (videoPlayer.currentTime / videoPlayer.duration) * 100;
    document.getElementById('progress-bar').style.width = progress + '%';
}

function showLoading() {
    document.getElementById('loading-spinner').style.display = 'block';
}

function hideLoading() {
    document.getElementById('loading-spinner').style.display = 'none';
}

function showError() {
    hideLoading();
    const container = document.getElementById('video-container');
    container.innerHTML = `
        <div class="error-message">
            <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
            <h4>{% trans "Video Error" %}</h4>
            <p>{% trans "There was an error loading the video. Please try again later." %}</p>
            <button class="control-btn" onclick="location.reload()">
                <i class="fas fa-redo me-1"></i>
                {% trans "Retry" %}
            </button>
        </div>
    `;
}

// Handle page unload
window.addEventListener('beforeunload', function() {
    saveWatchProgress();
});

// Handle fullscreen changes
document.addEventListener('fullscreenchange', function() {
    const fullscreenBtn = document.querySelector('.control-btn i.fa-expand, .control-btn i.fa-compress');
    if (fullscreenBtn) {
        if (document.fullscreenElement) {
            fullscreenBtn.className = 'fas fa-compress';
        } else {
            fullscreenBtn.className = 'fas fa-expand';
        }
    }
});
</script>
{% endblock %}