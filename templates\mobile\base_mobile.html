{% load static %}
{% load i18n %}
{% load localization_tags %}
<!DOCTYPE html>
<html lang="{{ LANGUAGE_CODE }}" dir="{{ LANGUAGE_DIRECTION }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="{% block meta_description %}School ERP Mobile Application{% endblock %}">
    <meta name="author" content="School ERP Team">
    <meta name="theme-color" content="#007bff">
    
    <!-- PWA Meta Tags -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="School ERP">
    
    <!-- Favicon and Icons -->
    <link rel="icon" type="image/x-icon" href="{% static 'images/favicon.ico' %}">
    <link rel="apple-touch-icon" href="{% static 'images/apple-touch-icon.png' %}">
    <link rel="manifest" href="{% static 'manifest.json' %}">
    
    <title>{% block title %}School ERP{% endblock %}</title>
    
    <!-- CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{% static 'css/mobile.css' %}" rel="stylesheet">
    {% if LANGUAGE_CODE == 'ar' %}
        <link href="{% static 'css/rtl.css' %}" rel="stylesheet">
    {% endif %}
    
    {% block extra_css %}{% endblock %}
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="{% static 'js/mobile.js' %}" as="script">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    
    <!-- Service Worker Registration -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('{% static "sw.js" %}')
                    .then(function(registration) {
                        console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    </script>
</head>

<body class="{% if LANGUAGE_CODE == 'ar' %}rtl-layout arabic-text{% endif %} {% block body_class %}{% endblock %}">
    <!-- Skip to main content for accessibility -->
    <a class="sr-only sr-only-focusable" href="#main-content">
        {% trans "Skip to main content" %}
    </a>
    
    <!-- Mobile Navigation -->
    <nav class="navbar-mobile">
        <div class="container-mobile">
            <a class="navbar-brand-mobile" href="{% url 'core:dashboard' %}">
                {% if LANGUAGE_CODE == 'ar' %}
                    {{ SCHOOL_NAME_AR|default:SCHOOL_NAME }}
                {% else %}
                    {{ SCHOOL_NAME }}
                {% endif %}
            </a>
            
            <button class="navbar-toggler-mobile d-desktop-none" type="button">
                <i class="fas fa-bars"></i>
                <span class="sr-only">{% trans "Toggle navigation" %}</span>
            </button>
            
            <!-- Desktop Navigation -->
            <div class="navbar-collapse-mobile">
                <div class="navbar-header-mobile d-desktop-none">
                    <span class="navbar-brand-mobile">
                        {% if LANGUAGE_CODE == 'ar' %}
                            {{ SCHOOL_NAME_AR|default:SCHOOL_NAME }}
                        {% else %}
                            {{ SCHOOL_NAME }}
                        {% endif %}
                    </span>
                    <button class="navbar-close-mobile" type="button">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <ul class="navbar-nav-mobile">
                    <li class="nav-item-mobile">
                        <a class="nav-link-mobile" href="{% url 'core:dashboard' %}">
                            <i class="fas fa-home"></i>
                            {% trans "Dashboard" %}
                        </a>
                    </li>
                    
                    {% if user.is_authenticated %}
                        <!-- Students Menu -->
                        {% if 'students' in user.get_accessible_modules %}
                        <li class="nav-item-mobile has-submenu">
                            <a class="nav-link-mobile" href="#">
                                <i class="fas fa-user-graduate"></i>
                                {% trans "Students" %}
                                <i class="fas fa-chevron-down float-right"></i>
                            </a>
                            <ul class="nav-submenu-mobile">
                                <li class="nav-item-mobile">
                                    <a class="nav-link-mobile" href="{% url 'students:student_list' %}">
                                        {% trans "All Students" %}
                                    </a>
                                </li>
                                <li class="nav-item-mobile">
                                    <a class="nav-link-mobile" href="{% url 'students:student_create' %}">
                                        {% trans "Add Student" %}
                                    </a>
                                </li>
                                <li class="nav-item-mobile">
                                    <a class="nav-link-mobile" href="{% url 'students:class_list' %}">
                                        {% trans "Classes" %}
                                    </a>
                                </li>
                            </ul>
                        </li>
                        {% endif %}
                        
                        <!-- Academics Menu -->
                        {% if 'academics' in user.get_accessible_modules %}
                        <li class="nav-item-mobile has-submenu">
                            <a class="nav-link-mobile" href="#">
                                <i class="fas fa-book"></i>
                                {% trans "Academics" %}
                                <i class="fas fa-chevron-down float-right"></i>
                            </a>
                            <ul class="nav-submenu-mobile">
                                <li class="nav-item-mobile">
                                    <a class="nav-link-mobile" href="{% url 'academics:subject_list' %}">
                                        {% trans "Subjects" %}
                                    </a>
                                </li>
                                <li class="nav-item-mobile">
                                    <a class="nav-link-mobile" href="{% url 'academics:attendance' %}">
                                        {% trans "Attendance" %}
                                    </a>
                                </li>
                                <li class="nav-item-mobile">
                                    <a class="nav-link-mobile" href="{% url 'academics:grades' %}">
                                        {% trans "Grades" %}
                                    </a>
                                </li>
                            </ul>
                        </li>
                        {% endif %}
                        
                        <!-- Finance Menu -->
                        {% if 'finance' in user.get_accessible_modules %}
                        <li class="nav-item-mobile has-submenu">
                            <a class="nav-link-mobile" href="#">
                                <i class="fas fa-money-bill-wave"></i>
                                {% trans "Finance" %}
                                <i class="fas fa-chevron-down float-right"></i>
                            </a>
                            <ul class="nav-submenu-mobile">
                                <li class="nav-item-mobile">
                                    <a class="nav-link-mobile" href="{% url 'finance:dashboard' %}">
                                        {% trans "Finance Dashboard" %}
                                    </a>
                                </li>
                                <li class="nav-item-mobile">
                                    <a class="nav-link-mobile" href="{% url 'finance:invoices' %}">
                                        {% trans "Invoices" %}
                                    </a>
                                </li>
                                <li class="nav-item-mobile">
                                    <a class="nav-link-mobile" href="{% url 'finance:payments' %}">
                                        {% trans "Payments" %}
                                    </a>
                                </li>
                            </ul>
                        </li>
                        {% endif %}
                        
                        <!-- User Menu -->
                        <li class="nav-item-mobile has-submenu">
                            <a class="nav-link-mobile" href="#">
                                <i class="fas fa-user"></i>
                                {{ user.get_full_name|default:user.username }}
                                <i class="fas fa-chevron-down float-right"></i>
                            </a>
                            <ul class="nav-submenu-mobile">
                                <li class="nav-item-mobile">
                                    <a class="nav-link-mobile" href="{% url 'accounts:profile' %}">
                                        {% trans "Profile" %}
                                    </a>
                                </li>
                                <li class="nav-item-mobile">
                                    <a class="nav-link-mobile" href="{% url 'accounts:settings' %}">
                                        {% trans "Settings" %}
                                    </a>
                                </li>
                                <li class="nav-item-mobile">
                                    <a class="nav-link-mobile" href="{% url 'accounts:logout' %}">
                                        {% trans "Logout" %}
                                    </a>
                                </li>
                            </ul>
                        </li>
                        
                        <!-- Language Switcher -->
                        <li class="nav-item-mobile">
                            {% language_switcher %}
                        </li>
                    {% else %}
                        <li class="nav-item-mobile">
                            <a class="nav-link-mobile" href="{% url 'accounts:login' %}">
                                <i class="fas fa-sign-in-alt"></i>
                                {% trans "Login" %}
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Mobile Navigation Overlay -->
    <div class="navbar-overlay-mobile"></div>
    
    <!-- Pull to Refresh Indicator -->
    <div class="pull-to-refresh-indicator" style="display: none;">
        <i class="fas fa-sync-alt"></i>
        <span>{% trans "Pull to refresh" %}</span>
    </div>
    
    <!-- Main Content -->
    <main id="main-content" class="main-content-mobile">
        {% block breadcrumb %}
        {% if breadcrumbs %}
        <nav aria-label="breadcrumb" class="breadcrumb-mobile">
            <div class="container-mobile">
                <ol class="breadcrumb">
                    {% for crumb in breadcrumbs %}
                        {% if forloop.last %}
                            <li class="breadcrumb-item active" aria-current="page">{{ crumb.title }}</li>
                        {% else %}
                            <li class="breadcrumb-item">
                                <a href="{{ crumb.url }}">{{ crumb.title }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}
                </ol>
            </div>
        </nav>
        {% endif %}
        {% endblock %}
        
        <!-- Messages -->
        {% if messages %}
        <div class="container-mobile">
            {% for message in messages %}
            <div class="alert-mobile alert-{{ message.tags }}-mobile alert-dismissible" role="alert">
                {{ message }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            {% endfor %}
        </div>
        {% endif %}
        
        <!-- Page Content -->
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="footer-mobile">
        <div class="container-mobile">
            <div class="row-mobile">
                <div class="col-mobile-12 text-mobile-center">
                    <p class="mb-0">
                        &copy; {% now "Y" %} 
                        {% if LANGUAGE_CODE == 'ar' %}
                            {{ SCHOOL_NAME_AR|default:SCHOOL_NAME }}
                        {% else %}
                            {{ SCHOOL_NAME }}
                        {% endif %}
                        . {% trans "All rights reserved" %}.
                    </p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Loading Overlay Template -->
    <div class="loading-overlay-mobile" style="display: none;">
        <div class="loading-content-mobile">
            <div class="spinner-mobile"></div>
            <div class="loading-text-mobile">{% trans "Loading..." %}</div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="{% static 'js/mobile.js' %}"></script>
    
    {% block extra_js %}{% endblock %}
    
    <!-- Analytics and Tracking -->
    {% block analytics %}{% endblock %}
    
    <!-- Additional Mobile Styles -->
    <style>
        /* Loading Overlay */
        .loading-overlay-mobile {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .loading-content-mobile {
            background-color: white;
            padding: 2rem;
            border-radius: 0.5rem;
            text-align: center;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        
        .spinner-mobile {
            width: 2rem;
            height: 2rem;
            border: 0.25em solid #f3f3f3;
            border-top: 0.25em solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text-mobile {
            color: #6c757d;
            font-size: 0.875rem;
        }
        
        /* Pull to Refresh */
        .pull-to-refresh-indicator {
            position: fixed;
            top: -60px;
            left: 50%;
            transform: translateX(-50%);
            background-color: white;
            padding: 1rem;
            border-radius: 0 0 0.5rem 0.5rem;
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
            text-align: center;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .pull-to-refresh-indicator i {
            display: block;
            margin-bottom: 0.5rem;
            color: #007bff;
        }
        
        /* Footer */
        .footer-mobile {
            background-color: #f8f9fa;
            padding: 2rem 0;
            margin-top: 3rem;
            border-top: 1px solid #dee2e6;
        }
        
        /* Breadcrumb */
        .breadcrumb-mobile {
            background-color: #f8f9fa;
            padding: 0.75rem 0;
            margin-bottom: 1rem;
        }
        
        .breadcrumb {
            display: flex;
            flex-wrap: wrap;
            padding: 0;
            margin-bottom: 0;
            list-style: none;
        }
        
        .breadcrumb-item + .breadcrumb-item::before {
            content: "/";
            color: #6c757d;
            padding: 0 0.5rem;
        }
        
        .breadcrumb-item a {
            color: #007bff;
            text-decoration: none;
        }
        
        .breadcrumb-item.active {
            color: #6c757d;
        }
        
        /* Touch feedback */
        .touch-active {
            background-color: rgba(0, 0, 0, 0.1) !important;
            transform: scale(0.98);
        }
        
        /* Screen reader only */
        .sr-only {
            position: absolute !important;
            width: 1px !important;
            height: 1px !important;
            padding: 0 !important;
            margin: -1px !important;
            overflow: hidden !important;
            clip: rect(0, 0, 0, 0) !important;
            white-space: nowrap !important;
            border: 0 !important;
        }
        
        .sr-only-focusable:focus {
            position: static !important;
            width: auto !important;
            height: auto !important;
            padding: 0.25rem 0.5rem !important;
            margin: 0 !important;
            overflow: visible !important;
            clip: auto !important;
            white-space: normal !important;
            background-color: #007bff !important;
            color: white !important;
            text-decoration: none !important;
        }
        
        /* Navbar scrolled state */
        .navbar-mobile.navbar-scrolled {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        /* Body states */
        body.nav-open {
            overflow: hidden;
        }
        
        body.modal-open {
            overflow: hidden;
        }
        
        body.loading-active {
            overflow: hidden;
        }
        
        /* RTL adjustments for mobile */
        .rtl-layout .navbar-toggler-mobile {
            margin-right: 0;
            margin-left: auto;
        }
        
        .rtl-layout .nav-link-mobile i {
            margin-left: 0.5rem;
            margin-right: 0;
        }
        
        .rtl-layout .nav-submenu-mobile .nav-link-mobile {
            padding-right: calc(var(--spacing-lg) + 20px + var(--spacing-sm));
            padding-left: var(--spacing-lg);
        }
        
        .rtl-layout .breadcrumb-item + .breadcrumb-item::before {
            content: "\\";
        }
    </style>
</body>
</html>