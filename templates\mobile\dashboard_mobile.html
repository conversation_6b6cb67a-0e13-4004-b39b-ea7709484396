{% extends "mobile/base_mobile.html" %}
{% load static %}
{% load i18n %}
{% load localization_tags %}

{% block title %}{% trans "Dashboard" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .dashboard-mobile {
        padding: 1rem 0;
    }
    
    .stats-grid-mobile {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .stat-card-mobile {
        background: linear-gradient(135deg, var(--primary-color), #0056b3);
        color: white;
        padding: 1.5rem;
        border-radius: 0.75rem;
        text-align: center;
        box-shadow: 0 0.25rem 0.5rem rgba(0, 123, 255, 0.2);
        transition: transform 0.2s ease;
    }
    
    .stat-card-mobile:hover {
        transform: translateY(-2px);
    }
    
    .stat-card-mobile.success {
        background: linear-gradient(135deg, var(--success-color), #1e7e34);
    }
    
    .stat-card-mobile.warning {
        background: linear-gradient(135deg, var(--warning-color), #d39e00);
        color: #212529;
    }
    
    .stat-card-mobile.danger {
        background: linear-gradient(135deg, var(--danger-color), #bd2130);
    }
    
    .stat-icon-mobile {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        opacity: 0.8;
    }
    
    .stat-number-mobile {
        font-size: 1.75rem;
        font-weight: bold;
        margin-bottom: 0.25rem;
    }
    
    .stat-label-mobile {
        font-size: 0.875rem;
        opacity: 0.9;
    }
    
    .quick-actions-mobile {
        margin-bottom: 2rem;
    }
    
    .action-grid-mobile {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1rem;
    }
    
    .action-card-mobile {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 1.5rem 1rem;
        text-align: center;
        text-decoration: none;
        color: var(--dark-color);
        transition: all 0.2s ease;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    
    .action-card-mobile:hover {
        color: var(--primary-color);
        border-color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
        text-decoration: none;
    }
    
    .action-icon-mobile {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        color: var(--primary-color);
    }
    
    .action-label-mobile {
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .recent-activity-mobile {
        background: white;
        border-radius: 0.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        overflow: hidden;
    }
    
    .activity-header-mobile {
        padding: 1rem;
        background: var(--light-color);
        border-bottom: 1px solid #dee2e6;
        font-weight: 600;
    }
    
    .activity-list-mobile {
        max-height: 300px;
        overflow-y: auto;
    }
    
    .activity-item-mobile {
        padding: 1rem;
        border-bottom: 1px solid #f8f9fa;
        display: flex;
        align-items: center;
    }
    
    .activity-item-mobile:last-child {
        border-bottom: none;
    }
    
    .activity-icon-mobile {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1rem;
    }
    
    .activity-icon-mobile.primary {
        background: rgba(0, 123, 255, 0.1);
        color: var(--primary-color);
    }
    
    .activity-icon-mobile.success {
        background: rgba(40, 167, 69, 0.1);
        color: var(--success-color);
    }
    
    .activity-icon-mobile.warning {
        background: rgba(255, 193, 7, 0.1);
        color: var(--warning-color);
    }
    
    .activity-content-mobile {
        flex: 1;
    }
    
    .activity-title-mobile {
        font-weight: 500;
        margin-bottom: 0.25rem;
        font-size: 0.875rem;
    }
    
    .activity-time-mobile {
        font-size: 0.75rem;
        color: #6c757d;
    }
    
    .notifications-mobile {
        position: fixed;
        bottom: 1rem;
        right: 1rem;
        z-index: 1000;
    }
    
    .notification-badge-mobile {
        background: var(--danger-color);
        color: white;
        border-radius: 50%;
        padding: 0.5rem;
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.2);
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .notification-badge-mobile:hover {
        transform: scale(1.1);
    }
    
    .notification-count-mobile {
        position: absolute;
        top: -5px;
        right: -5px;
        background: white;
        color: var(--danger-color);
        border-radius: 50%;
        width: 20px;
        height: 20px;
        font-size: 0.75rem;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    /* RTL adjustments */
    .rtl-layout .activity-icon-mobile {
        margin-left: 1rem;
        margin-right: 0;
    }
    
    .rtl-layout .notifications-mobile {
        right: auto;
        left: 1rem;
    }
    
    .rtl-layout .notification-count-mobile {
        right: auto;
        left: -5px;
    }
    
    /* Responsive adjustments */
    @media (max-width: 576px) {
        .stats-grid-mobile {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .action-grid-mobile {
            grid-template-columns: repeat(3, 1fr);
        }
        
        .stat-card-mobile {
            padding: 1rem;
        }
        
        .stat-number-mobile {
            font-size: 1.5rem;
        }
        
        .stat-icon-mobile {
            font-size: 1.5rem;
        }
        
        .action-card-mobile {
            padding: 1rem 0.5rem;
        }
        
        .action-icon-mobile {
            font-size: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-mobile dashboard-mobile">
    <!-- Welcome Section -->
    <div class="welcome-section-mobile mb-4">
        <h1 class="h3 mb-2">
            {% if LANGUAGE_CODE == 'ar' %}
                مرحباً، {{ user.get_full_name|default:user.username }}
            {% else %}
                {% trans "Welcome" %}, {{ user.get_full_name|default:user.username }}
            {% endif %}
        </h1>
        <p class="text-muted mb-0">
            {% if LANGUAGE_CODE == 'ar' %}
                {{ today|format_date_localized }}
            {% else %}
                {{ today|format_date_localized }}
            {% endif %}
        </p>
    </div>
    
    <!-- Statistics Cards -->
    <div class="stats-grid-mobile">
        <div class="stat-card-mobile">
            <div class="stat-icon-mobile">
                <i class="fas fa-user-graduate"></i>
            </div>
            <div class="stat-number-mobile">
                {{ stats.total_students|format_number }}
            </div>
            <div class="stat-label-mobile">
                {% trans "Total Students" %}
            </div>
        </div>
        
        <div class="stat-card-mobile success">
            <div class="stat-icon-mobile">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-number-mobile">
                {{ stats.present_today|format_number }}
            </div>
            <div class="stat-label-mobile">
                {% trans "Present Today" %}
            </div>
        </div>
        
        <div class="stat-card-mobile warning">
            <div class="stat-icon-mobile">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stat-number-mobile">
                {{ stats.absent_today|format_number }}
            </div>
            <div class="stat-label-mobile">
                {% trans "Absent Today" %}
            </div>
        </div>
        
        <div class="stat-card-mobile danger">
            <div class="stat-icon-mobile">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-number-mobile">
                {{ stats.late_today|format_number }}
            </div>
            <div class="stat-label-mobile">
                {% trans "Late Today" %}
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="quick-actions-mobile">
        <h5 class="mb-3">
            <i class="fas fa-bolt"></i>
            {% trans "Quick Actions" %}
        </h5>
        
        <div class="action-grid-mobile">
            {% if 'academics' in user.get_accessible_modules %}
            <a href="{% url 'academics:attendance' %}" class="action-card-mobile">
                <div class="action-icon-mobile">
                    <i class="fas fa-clipboard-check"></i>
                </div>
                <div class="action-label-mobile">
                    {% trans "Take Attendance" %}
                </div>
            </a>
            {% endif %}
            
            {% if 'students' in user.get_accessible_modules %}
            <a href="{% url 'students:student_create' %}" class="action-card-mobile">
                <div class="action-icon-mobile">
                    <i class="fas fa-user-plus"></i>
                </div>
                <div class="action-label-mobile">
                    {% trans "Add Student" %}
                </div>
            </a>
            {% endif %}
            
            {% if 'academics' in user.get_accessible_modules %}
            <a href="{% url 'academics:grades' %}" class="action-card-mobile">
                <div class="action-icon-mobile">
                    <i class="fas fa-star"></i>
                </div>
                <div class="action-label-mobile">
                    {% trans "Enter Grades" %}
                </div>
            </a>
            {% endif %}
            
            {% if 'communications' in user.get_accessible_modules %}
            <a href="{% url 'communications:compose' %}" class="action-card-mobile">
                <div class="action-icon-mobile">
                    <i class="fas fa-envelope"></i>
                </div>
                <div class="action-label-mobile">
                    {% trans "Send Message" %}
                </div>
            </a>
            {% endif %}
            
            {% if 'reports' in user.get_accessible_modules %}
            <a href="{% url 'reports:dashboard' %}" class="action-card-mobile">
                <div class="action-icon-mobile">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div class="action-label-mobile">
                    {% trans "View Reports" %}
                </div>
            </a>
            {% endif %}
            
            {% if 'finance' in user.get_accessible_modules %}
            <a href="{% url 'finance:invoices' %}" class="action-card-mobile">
                <div class="action-icon-mobile">
                    <i class="fas fa-file-invoice"></i>
                </div>
                <div class="action-label-mobile">
                    {% trans "Invoices" %}
                </div>
            </a>
            {% endif %}
        </div>
    </div>
    
    <!-- Recent Activity -->
    <div class="recent-activity-mobile">
        <div class="activity-header-mobile">
            <i class="fas fa-history"></i>
            {% trans "Recent Activity" %}
        </div>
        
        <div class="activity-list-mobile">
            {% for activity in recent_activities %}
            <div class="activity-item-mobile">
                <div class="activity-icon-mobile {{ activity.type }}">
                    <i class="{{ activity.icon }}"></i>
                </div>
                <div class="activity-content-mobile">
                    <div class="activity-title-mobile">
                        {% if LANGUAGE_CODE == 'ar' %}
                            {{ activity.title_ar|default:activity.title }}
                        {% else %}
                            {{ activity.title }}
                        {% endif %}
                    </div>
                    <div class="activity-time-mobile">
                        {{ activity.created_at|timesince }} {% trans "ago" %}
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="activity-item-mobile">
                <div class="activity-content-mobile text-center text-muted">
                    <i class="fas fa-info-circle"></i>
                    {% trans "No recent activity" %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- Floating Notifications Button -->
{% if notifications_count > 0 %}
<div class="notifications-mobile">
    <div class="notification-badge-mobile" data-toggle="modal" data-target="#notificationsModal">
        <i class="fas fa-bell"></i>
        <span class="notification-count-mobile">{{ notifications_count|format_number }}</span>
    </div>
</div>
{% endif %}

<!-- Notifications Modal -->
<div class="modal-mobile" id="notificationsModal" tabindex="-1">
    <div class="modal-dialog-mobile">
        <div class="modal-content-mobile">
            <div class="modal-header-mobile">
                <h5 class="modal-title-mobile">
                    <i class="fas fa-bell"></i>
                    {% trans "Notifications" %}
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body-mobile">
                <div class="list-group-mobile">
                    {% for notification in notifications %}
                    <div class="list-group-item-mobile">
                        <div class="d-flex align-items-start">
                            <div class="notification-icon mr-3">
                                <i class="{{ notification.icon }} text-{{ notification.type }}"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ notification.title }}</h6>
                                <p class="mb-1">{{ notification.message }}</p>
                                <small class="text-muted">{{ notification.created_at|timesince }} {% trans "ago" %}</small>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="list-group-item-mobile text-center text-muted">
                        <i class="fas fa-info-circle"></i>
                        {% trans "No notifications" %}
                    </div>
                    {% endfor %}
                </div>
            </div>
            <div class="modal-footer-mobile">
                <a href="{% url 'communications:notifications' %}" class="btn-mobile btn-primary-mobile">
                    {% trans "View All" %}
                </a>
                <button type="button" class="btn-mobile btn-secondary-mobile" data-dismiss="modal">
                    {% trans "Close" %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh dashboard data every 5 minutes
    setInterval(function() {
        if (navigator.onLine) {
            fetch('/api/dashboard/stats/')
                .then(response => response.json())
                .then(data => {
                    // Update statistics
                    updateDashboardStats(data);
                })
                .catch(error => {
                    console.log('Failed to refresh dashboard data:', error);
                });
        }
    }, 5 * 60 * 1000);
    
    // Pull to refresh functionality
    window.onPullToRefresh = function() {
        window.location.reload();
    };
    
    // Handle notification clicks
    document.querySelectorAll('.notification-badge-mobile').forEach(function(badge) {
        badge.addEventListener('click', function() {
            // Mark notifications as read
            fetch('/api/notifications/mark-read/', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json'
                }
            });
        });
    });
    
    // Vibrate on important actions (if supported)
    document.querySelectorAll('.stat-card-mobile.danger, .notification-badge-mobile').forEach(function(element) {
        element.addEventListener('click', function() {
            if (window.mobileFramework) {
                window.mobileFramework.vibrate([100]);
            }
        });
    });
});

function updateDashboardStats(data) {
    // Update stat numbers
    const statNumbers = document.querySelectorAll('.stat-number-mobile');
    if (statNumbers[0]) statNumbers[0].textContent = data.total_students;
    if (statNumbers[1]) statNumbers[1].textContent = data.present_today;
    if (statNumbers[2]) statNumbers[2].textContent = data.absent_today;
    if (statNumbers[3]) statNumbers[3].textContent = data.late_today;
    
    // Update notification count
    const notificationCount = document.querySelector('.notification-count-mobile');
    if (notificationCount && data.notifications_count) {
        notificationCount.textContent = data.notifications_count;
    }
}
</script>
{% endblock %}