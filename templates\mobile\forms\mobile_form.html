{% load static %}
{% load i18n %}
{% load widget_tweaks %}

<!-- Mobile-Optimized Form Template -->
<form class="mobile-form {% block form_class %}{% endblock %}" 
      method="{% block form_method %}post{% endblock %}" 
      action="{% block form_action %}{% endblock %}"
      {% block form_attrs %}{% endblock %}>
    
    {% csrf_token %}
    
    <!-- Form Header -->
    {% block form_header %}
    <div class="card-mobile">
        <div class="card-header-mobile">
            <h5 class="mb-0">
                <i class="{% block form_icon %}fas fa-edit{% endblock %}"></i>
                {% block form_title %}{% trans "Form" %}{% endblock %}
            </h5>
        </div>
        <div class="card-body-mobile">
    {% endblock %}
    
    <!-- Form Fields -->
    {% block form_fields %}
        {% for field in form %}
            <div class="form-group-mobile">
                <!-- Field Label -->
                <label class="form-label-mobile" for="{{ field.id_for_label }}">
                    {{ field.label }}
                    {% if field.field.required %}
                        <span class="text-danger">*</span>
                    {% endif %}
                </label>
                
                <!-- Field Input -->
                {% if field.field.widget.input_type == 'checkbox' %}
                    <div class="form-check-mobile">
                        {{ field|add_class:"form-check-input-mobile" }}
                        <label class="form-check-label-mobile" for="{{ field.id_for_label }}">
                            {{ field.label }}
                        </label>
                    </div>
                {% elif field.field.widget.input_type == 'radio' %}
                    <div class="form-radio-group-mobile">
                        {% for choice in field.field.choices %}
                            <div class="form-check-mobile">
                                <input type="radio" 
                                       class="form-check-input-mobile" 
                                       name="{{ field.name }}" 
                                       value="{{ choice.0 }}" 
                                       id="{{ field.id_for_label }}_{{ forloop.counter }}"
                                       {% if choice.0 == field.value %}checked{% endif %}>
                                <label class="form-check-label-mobile" 
                                       for="{{ field.id_for_label }}_{{ forloop.counter }}">
                                    {{ choice.1 }}
                                </label>
                            </div>
                        {% endfor %}
                    </div>
                {% elif field.field.widget.input_type == 'select' %}
                    {{ field|add_class:"form-control-mobile form-select-mobile" }}
                {% elif field.field.widget.input_type == 'textarea' %}
                    {{ field|add_class:"form-control-mobile"|attr:"rows:4" }}
                {% elif field.field.widget.input_type == 'file' %}
                    <div class="file-input-mobile">
                        {{ field|add_class:"form-control-mobile" }}
                        <label for="{{ field.id_for_label }}" class="file-input-label-mobile">
                            <i class="fas fa-upload"></i>
                            {% trans "Choose file" %}
                        </label>
                    </div>
                {% elif field.field.widget.input_type == 'date' %}
                    {{ field|add_class:"form-control-mobile"|attr:"type:date" }}
                {% elif field.field.widget.input_type == 'time' %}
                    {{ field|add_class:"form-control-mobile"|attr:"type:time" }}
                {% elif field.field.widget.input_type == 'datetime-local' %}
                    {{ field|add_class:"form-control-mobile"|attr:"type:datetime-local" }}
                {% elif field.field.widget.input_type == 'email' %}
                    {{ field|add_class:"form-control-mobile"|attr:"type:email"|attr:"autocomplete:email" }}
                {% elif field.field.widget.input_type == 'tel' %}
                    {{ field|add_class:"form-control-mobile"|attr:"type:tel"|attr:"autocomplete:tel" }}
                {% elif field.field.widget.input_type == 'url' %}
                    {{ field|add_class:"form-control-mobile"|attr:"type:url" }}
                {% elif field.field.widget.input_type == 'number' %}
                    {{ field|add_class:"form-control-mobile"|attr:"type:number"|attr:"inputmode:numeric" }}
                {% elif field.field.widget.input_type == 'password' %}
                    <div class="password-input-mobile">
                        {{ field|add_class:"form-control-mobile"|attr:"type:password"|attr:"autocomplete:current-password" }}
                        <button type="button" class="password-toggle-mobile" data-toggle="password">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                {% else %}
                    {{ field|add_class:"form-control-mobile" }}
                {% endif %}
                
                <!-- Field Help Text -->
                {% if field.help_text %}
                    <small class="form-text-mobile text-muted">
                        {{ field.help_text }}
                    </small>
                {% endif %}
                
                <!-- Field Errors -->
                {% if field.errors %}
                    <div class="invalid-feedback-mobile">
                        {% for error in field.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
        {% endfor %}
        
        <!-- Non-field Errors -->
        {% if form.non_field_errors %}
            <div class="alert-mobile alert-danger-mobile">
                {% for error in form.non_field_errors %}
                    <div>{{ error }}</div>
                {% endfor %}
            </div>
        {% endif %}
    {% endblock %}
    
    <!-- Form Footer -->
    {% block form_footer %}
        </div>
        <div class="card-footer-mobile">
            <div class="form-actions-mobile">
                <button type="submit" class="btn-mobile btn-primary-mobile btn-block-mobile">
                    <i class="{% block submit_icon %}fas fa-save{% endblock %}"></i>
                    {% block submit_text %}{% trans "Save" %}{% endblock %}
                </button>
                
                {% block additional_buttons %}
                <a href="{% block cancel_url %}javascript:history.back(){% endblock %}" 
                   class="btn-mobile btn-secondary-mobile btn-block-mobile">
                    <i class="fas fa-times"></i>
                    {% trans "Cancel" %}
                </a>
                {% endblock %}
            </div>
        </div>
    </div>
    {% endblock %}
</form>

<!-- Mobile Form Styles -->
<style>
    .mobile-form {
        margin-bottom: 2rem;
    }
    
    .form-text-mobile {
        display: block;
        margin-top: 0.25rem;
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .invalid-feedback-mobile {
        display: block;
        margin-top: 0.25rem;
    }
    
    .form-radio-group-mobile {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .file-input-mobile {
        position: relative;
    }
    
    .file-input-mobile input[type="file"] {
        position: absolute;
        opacity: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
    }
    
    .file-input-label-mobile {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 1rem;
        background-color: #f8f9fa;
        border: 2px dashed #dee2e6;
        border-radius: 0.375rem;
        cursor: pointer;
        transition: all 0.15s ease-in-out;
        min-height: 44px;
    }
    
    .file-input-label-mobile:hover {
        background-color: #e9ecef;
        border-color: #007bff;
    }
    
    .file-input-label-mobile i {
        margin-right: 0.5rem;
    }
    
    .password-input-mobile {
        position: relative;
    }
    
    .password-toggle-mobile {
        position: absolute;
        right: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #6c757d;
        cursor: pointer;
        padding: 0.25rem;
        min-height: 24px;
        min-width: 24px;
    }
    
    .password-toggle-mobile:hover {
        color: #007bff;
    }
    
    .form-actions-mobile {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .form-actions-mobile .btn-mobile {
        margin-bottom: 0;
    }
    
    /* RTL adjustments */
    .rtl-layout .file-input-label-mobile i {
        margin-left: 0.5rem;
        margin-right: 0;
    }
    
    .rtl-layout .password-toggle-mobile {
        right: auto;
        left: 0.75rem;
    }
    
    /* Tablet and desktop adjustments */
    @media (min-width: 768px) {
        .form-actions-mobile {
            flex-direction: row;
            justify-content: flex-end;
        }
        
        .form-actions-mobile .btn-mobile {
            width: auto;
            min-width: 120px;
        }
    }
    
    /* Focus states for better accessibility */
    .form-control-mobile:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    .form-check-input-mobile:focus {
        outline: none;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    /* Error states */
    .form-control-mobile.is-invalid {
        border-color: #dc3545;
    }
    
    .form-control-mobile.is-invalid:focus {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }
    
    /* Success states */
    .form-control-mobile.is-valid {
        border-color: #28a745;
    }
    
    .form-control-mobile.is-valid:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }
</style>

<!-- Mobile Form JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password toggle functionality
    document.querySelectorAll('[data-toggle="password"]').forEach(function(toggle) {
        toggle.addEventListener('click', function() {
            const input = this.parentElement.querySelector('input[type="password"], input[type="text"]');
            const icon = this.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });
    
    // File input feedback
    document.querySelectorAll('input[type="file"]').forEach(function(input) {
        input.addEventListener('change', function() {
            const label = this.parentElement.querySelector('.file-input-label-mobile');
            if (this.files.length > 0) {
                label.innerHTML = '<i class="fas fa-check"></i> ' + this.files.length + ' file(s) selected';
                label.style.backgroundColor = '#d4edda';
                label.style.borderColor = '#28a745';
                label.style.color = '#155724';
            } else {
                label.innerHTML = '<i class="fas fa-upload"></i> Choose file';
                label.style.backgroundColor = '';
                label.style.borderColor = '';
                label.style.color = '';
            }
        });
    });
    
    // Form validation feedback
    document.querySelectorAll('.mobile-form').forEach(function(form) {
        form.addEventListener('submit', function(e) {
            let isValid = true;
            
            // Check required fields
            form.querySelectorAll('[required]').forEach(function(field) {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                    field.classList.add('is-valid');
                }
            });
            
            // Check email fields
            form.querySelectorAll('input[type="email"]').forEach(function(field) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (field.value && !emailRegex.test(field.value)) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else if (field.value) {
                    field.classList.remove('is-invalid');
                    field.classList.add('is-valid');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                // Scroll to first invalid field
                const firstInvalid = form.querySelector('.is-invalid');
                if (firstInvalid) {
                    firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    firstInvalid.focus();
                }
            }
        });
    });
    
    // Auto-resize textareas
    document.querySelectorAll('textarea.form-control-mobile').forEach(function(textarea) {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
        
        // Initial resize
        textarea.style.height = 'auto';
        textarea.style.height = textarea.scrollHeight + 'px';
    });
    
    // Numeric input formatting
    document.querySelectorAll('input[type="number"]').forEach(function(input) {
        input.addEventListener('input', function() {
            // Add thousand separators for display
            if (this.value) {
                const value = parseFloat(this.value);
                if (!isNaN(value)) {
                    // Store original value
                    this.dataset.originalValue = this.value;
                }
            }
        });
    });
});
</script>