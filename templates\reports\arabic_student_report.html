{% extends "base.html" %}
{% load i18n %}
{% load localization_tags %}

{% block title %}
{% if LANGUAGE_CODE == 'ar' %}
    تقرير الطالب - {{ student.name_ar|default:student.full_name }}
{% else %}
    {% trans "Student Report" %} - {{ student.full_name }}
{% endif %}
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% url 'django.views.static.serve' path='css/rtl.css' %}">
<style>
    .report-header {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        padding: 2rem;
        border-radius: 0.5rem;
        margin-bottom: 2rem;
    }
    
    .student-photo {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        border: 4px solid white;
        object-fit: cover;
    }
    
    .info-card {
        background-color: #f8f9fa;
        border-left: 4px solid #007bff;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border-radius: 0.5rem;
    }
    
    .rtl-layout .info-card {
        border-left: none;
        border-right: 4px solid #007bff;
    }
    
    .grade-badge {
        font-size: 1.2rem;
        padding: 0.5rem 1rem;
        border-radius: 2rem;
    }
    
    .grade-excellent {
        background-color: #28a745;
        color: white;
    }
    
    .grade-good {
        background-color: #17a2b8;
        color: white;
    }
    
    .grade-average {
        background-color: #ffc107;
        color: #212529;
    }
    
    .grade-poor {
        background-color: #dc3545;
        color: white;
    }
    
    .attendance-chart {
        height: 200px;
    }
    
    .arabic-grade {
        font-family: 'Noto Sans Arabic', 'Tahoma', sans-serif;
        font-weight: bold;
        font-size: 1.1rem;
    }
    
    @media print {
        .no-print {
            display: none !important;
        }
        
        .report-container {
            max-width: none;
            margin: 0;
            padding: 0;
        }
        
        .report-header {
            background: #007bff !important;
            -webkit-print-color-adjust: exact;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid report-container {% if LANGUAGE_CODE == 'ar' %}rtl-layout arabic-text{% endif %}">
    <!-- Report Header -->
    <div class="report-header">
        <div class="row align-items-center">
            <div class="col-md-2 text-center">
                {% if student.photo %}
                    <img src="{{ student.photo.url }}" alt="Student Photo" class="student-photo">
                {% else %}
                    <div class="student-photo bg-light d-flex align-items-center justify-content-center">
                        <i class="fas fa-user fa-3x text-muted"></i>
                    </div>
                {% endif %}
            </div>
            <div class="col-md-8">
                <h1 class="h2 mb-2">
                    {% if LANGUAGE_CODE == 'ar' %}
                        تقرير الطالب الشامل
                    {% else %}
                        {% trans "Comprehensive Student Report" %}
                    {% endif %}
                </h1>
                <h3 class="mb-1">
                    {% if LANGUAGE_CODE == 'ar' %}
                        {{ student.name_ar|default:student.full_name }}
                    {% else %}
                        {{ student.full_name }}
                    {% endif %}
                </h3>
                <p class="mb-0">
                    {% if LANGUAGE_CODE == 'ar' %}
                        رقم الطالب: <span class="arabic-number">{{ student.student_id|arabic_digits }}</span>
                    {% else %}
                        {% trans "Student ID" %}: {{ student.student_id }}
                    {% endif %}
                </p>
            </div>
            <div class="col-md-2 text-center">
                <div class="text-center">
                    <p class="mb-1">
                        {% if LANGUAGE_CODE == 'ar' %}
                            تاريخ التقرير
                        {% else %}
                            {% trans "Report Date" %}
                        {% endif %}
                    </p>
                    <h4>{{ report_date|format_date_localized }}</h4>
                </div>
            </div>
        </div>
    </div>

    <!-- Basic Information -->
    <div class="row">
        <div class="col-md-6">
            <div class="info-card">
                <h4 class="mb-3">
                    {% if LANGUAGE_CODE == 'ar' %}
                        المعلومات الأساسية
                    {% else %}
                        {% trans "Basic Information" %}
                    {% endif %}
                </h4>
                <div class="row">
                    <div class="col-6">
                        <p class="mb-2">
                            <strong>
                                {% if LANGUAGE_CODE == 'ar' %}
                                    تاريخ الميلاد:
                                {% else %}
                                    {% trans "Date of Birth" %}:
                                {% endif %}
                            </strong><br>
                            {{ student.date_of_birth|format_date_localized }}
                        </p>
                        <p class="mb-2">
                            <strong>
                                {% if LANGUAGE_CODE == 'ar' %}
                                    الجنس:
                                {% else %}
                                    {% trans "Gender" %}:
                                {% endif %}
                            </strong><br>
                            {% if LANGUAGE_CODE == 'ar' %}
                                {% if student.gender == 'M' %}ذكر{% else %}أنثى{% endif %}
                            {% else %}
                                {{ student.get_gender_display }}
                            {% endif %}
                        </p>
                        <p class="mb-2">
                            <strong>
                                {% if LANGUAGE_CODE == 'ar' %}
                                    الجنسية:
                                {% else %}
                                    {% trans "Nationality" %}:
                                {% endif %}
                            </strong><br>
                            {% if LANGUAGE_CODE == 'ar' %}
                                {{ student.nationality_ar|default:student.nationality }}
                            {% else %}
                                {{ student.nationality }}
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-6">
                        <p class="mb-2">
                            <strong>
                                {% if LANGUAGE_CODE == 'ar' %}
                                    الصف الحالي:
                                {% else %}
                                    {% trans "Current Class" %}:
                                {% endif %}
                            </strong><br>
                            {% if LANGUAGE_CODE == 'ar' %}
                                {{ student.current_class.name_ar|default:student.current_class.name }}
                            {% else %}
                                {{ student.current_class.name }}
                            {% endif %}
                        </p>
                        <p class="mb-2">
                            <strong>
                                {% if LANGUAGE_CODE == 'ar' %}
                                    السنة الدراسية:
                                {% else %}
                                    {% trans "Academic Year" %}:
                                {% endif %}
                            </strong><br>
                            {{ student.current_academic_year.name }}
                        </p>
                        <p class="mb-2">
                            <strong>
                                {% if LANGUAGE_CODE == 'ar' %}
                                    تاريخ التسجيل:
                                {% else %}
                                    {% trans "Enrollment Date" %}:
                                {% endif %}
                            </strong><br>
                            {{ student.enrollment_date|format_date_localized }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="info-card">
                <h4 class="mb-3">
                    {% if LANGUAGE_CODE == 'ar' %}
                        معلومات ولي الأمر
                    {% else %}
                        {% trans "Parent Information" %}
                    {% endif %}
                </h4>
                {% if student.parent %}
                <div class="row">
                    <div class="col-6">
                        <p class="mb-2">
                            <strong>
                                {% if LANGUAGE_CODE == 'ar' %}
                                    اسم الأب:
                                {% else %}
                                    {% trans "Father's Name" %}:
                                {% endif %}
                            </strong><br>
                            {% if LANGUAGE_CODE == 'ar' %}
                                {{ student.parent.father_name_ar|default:student.parent.father_name }}
                            {% else %}
                                {{ student.parent.father_name }}
                            {% endif %}
                        </p>
                        <p class="mb-2">
                            <strong>
                                {% if LANGUAGE_CODE == 'ar' %}
                                    اسم الأم:
                                {% else %}
                                    {% trans "Mother's Name" %}:
                                {% endif %}
                            </strong><br>
                            {% if LANGUAGE_CODE == 'ar' %}
                                {{ student.parent.mother_name_ar|default:student.parent.mother_name }}
                            {% else %}
                                {{ student.parent.mother_name }}
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-6">
                        <p class="mb-2">
                            <strong>
                                {% if LANGUAGE_CODE == 'ar' %}
                                    رقم الهاتف:
                                {% else %}
                                    {% trans "Phone" %}:
                                {% endif %}
                            </strong><br>
                            {{ student.parent.phone }}
                        </p>
                        <p class="mb-2">
                            <strong>
                                {% if LANGUAGE_CODE == 'ar' %}
                                    البريد الإلكتروني:
                                {% else %}
                                    {% trans "Email" %}:
                                {% endif %}
                            </strong><br>
                            {{ student.parent.email }}
                        </p>
                    </div>
                </div>
                {% else %}
                <p class="text-muted">
                    {% if LANGUAGE_CODE == 'ar' %}
                        لا توجد معلومات ولي أمر مسجلة
                    {% else %}
                        {% trans "No parent information available" %}
                    {% endif %}
                </p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Academic Performance -->
    <div class="row">
        <div class="col-12">
            <div class="info-card">
                <h4 class="mb-3">
                    {% if LANGUAGE_CODE == 'ar' %}
                        الأداء الأكاديمي
                    {% else %}
                        {% trans "Academic Performance" %}
                    {% endif %}
                </h4>
                
                <!-- Overall GPA -->
                <div class="row mb-4">
                    <div class="col-md-3 text-center">
                        <h5>
                            {% if LANGUAGE_CODE == 'ar' %}
                                المعدل العام
                            {% else %}
                                {% trans "Overall GPA" %}
                            {% endif %}
                        </h5>
                        <div class="grade-badge {% if student.gpa >= 90 %}grade-excellent{% elif student.gpa >= 80 %}grade-good{% elif student.gpa >= 70 %}grade-average{% else %}grade-poor{% endif %}">
                            <span class="arabic-grade">{{ student.gpa|format_number }}</span>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <h5>
                            {% if LANGUAGE_CODE == 'ar' %}
                                الترتيب في الصف
                            {% else %}
                                {% trans "Class Rank" %}
                            {% endif %}
                        </h5>
                        <div class="grade-badge grade-good">
                            <span class="arabic-number">{{ student.class_rank|arabic_digits }}</span> / 
                            <span class="arabic-number">{{ student.total_students|arabic_digits }}</span>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <h5>
                            {% if LANGUAGE_CODE == 'ar' %}
                                نسبة الحضور
                            {% else %}
                                {% trans "Attendance Rate" %}
                            {% endif %}
                        </h5>
                        <div class="grade-badge {% if student.attendance_rate >= 95 %}grade-excellent{% elif student.attendance_rate >= 85 %}grade-good{% elif student.attendance_rate >= 75 %}grade-average{% else %}grade-poor{% endif %}">
                            <span class="arabic-number">{{ student.attendance_rate|format_number }}%</span>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <h5>
                            {% if LANGUAGE_CODE == 'ar' %}
                                التقدير العام
                            {% else %}
                                {% trans "Overall Grade" %}
                            {% endif %}
                        </h5>
                        <div class="grade-badge {% if student.overall_grade == 'A' %}grade-excellent{% elif student.overall_grade == 'B' %}grade-good{% elif student.overall_grade == 'C' %}grade-average{% else %}grade-poor{% endif %}">
                            {% if LANGUAGE_CODE == 'ar' %}
                                {% if student.overall_grade == 'A' %}ممتاز
                                {% elif student.overall_grade == 'B' %}جيد جداً
                                {% elif student.overall_grade == 'C' %}جيد
                                {% elif student.overall_grade == 'D' %}مقبول
                                {% else %}ضعيف{% endif %}
                            {% else %}
                                {{ student.overall_grade }}
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Subject Grades -->
                <h5 class="mb-3">
                    {% if LANGUAGE_CODE == 'ar' %}
                        درجات المواد
                    {% else %}
                        {% trans "Subject Grades" %}
                    {% endif %}
                </h5>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="thead-light">
                            <tr>
                                <th>
                                    {% if LANGUAGE_CODE == 'ar' %}
                                        المادة
                                    {% else %}
                                        {% trans "Subject" %}
                                    {% endif %}
                                </th>
                                <th class="text-center">
                                    {% if LANGUAGE_CODE == 'ar' %}
                                        الفصل الأول
                                    {% else %}
                                        {% trans "First Semester" %}
                                    {% endif %}
                                </th>
                                <th class="text-center">
                                    {% if LANGUAGE_CODE == 'ar' %}
                                        الفصل الثاني
                                    {% else %}
                                        {% trans "Second Semester" %}
                                    {% endif %}
                                </th>
                                <th class="text-center">
                                    {% if LANGUAGE_CODE == 'ar' %}
                                        المعدل النهائي
                                    {% else %}
                                        {% trans "Final Average" %}
                                    {% endif %}
                                </th>
                                <th class="text-center">
                                    {% if LANGUAGE_CODE == 'ar' %}
                                        التقدير
                                    {% else %}
                                        {% trans "Grade" %}
                                    {% endif %}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for grade in student.subject_grades %}
                            <tr>
                                <td>
                                    {% if LANGUAGE_CODE == 'ar' %}
                                        {{ grade.subject.name_ar|default:grade.subject.name }}
                                    {% else %}
                                        {{ grade.subject.name }}
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    <span class="arabic-number">{{ grade.first_semester|format_number }}</span>
                                </td>
                                <td class="text-center">
                                    <span class="arabic-number">{{ grade.second_semester|format_number }}</span>
                                </td>
                                <td class="text-center">
                                    <span class="arabic-number">{{ grade.final_average|format_number }}</span>
                                </td>
                                <td class="text-center">
                                    <span class="badge {% if grade.letter_grade == 'A' %}badge-success{% elif grade.letter_grade == 'B' %}badge-info{% elif grade.letter_grade == 'C' %}badge-warning{% else %}badge-danger{% endif %}">
                                        {% if LANGUAGE_CODE == 'ar' %}
                                            {% if grade.letter_grade == 'A' %}ممتاز
                                            {% elif grade.letter_grade == 'B' %}جيد جداً
                                            {% elif grade.letter_grade == 'C' %}جيد
                                            {% elif grade.letter_grade == 'D' %}مقبول
                                            {% else %}ضعيف{% endif %}
                                        {% else %}
                                            {{ grade.letter_grade }}
                                        {% endif %}
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance Summary -->
    <div class="row">
        <div class="col-md-6">
            <div class="info-card">
                <h4 class="mb-3">
                    {% if LANGUAGE_CODE == 'ar' %}
                        ملخص الحضور
                    {% else %}
                        {% trans "Attendance Summary" %}
                    {% endif %}
                </h4>
                <div class="row text-center">
                    <div class="col-3">
                        <h5 class="text-success">
                            <span class="arabic-number">{{ student.attendance.present_days|arabic_digits }}</span>
                        </h5>
                        <small>
                            {% if LANGUAGE_CODE == 'ar' %}
                                أيام الحضور
                            {% else %}
                                {% trans "Present" %}
                            {% endif %}
                        </small>
                    </div>
                    <div class="col-3">
                        <h5 class="text-danger">
                            <span class="arabic-number">{{ student.attendance.absent_days|arabic_digits }}</span>
                        </h5>
                        <small>
                            {% if LANGUAGE_CODE == 'ar' %}
                                أيام الغياب
                            {% else %}
                                {% trans "Absent" %}
                            {% endif %}
                        </small>
                    </div>
                    <div class="col-3">
                        <h5 class="text-warning">
                            <span class="arabic-number">{{ student.attendance.late_days|arabic_digits }}</span>
                        </h5>
                        <small>
                            {% if LANGUAGE_CODE == 'ar' %}
                                أيام التأخير
                            {% else %}
                                {% trans "Late" %}
                            {% endif %}
                        </small>
                    </div>
                    <div class="col-3">
                        <h5 class="text-info">
                            <span class="arabic-number">{{ student.attendance.excused_days|arabic_digits }}</span>
                        </h5>
                        <small>
                            {% if LANGUAGE_CODE == 'ar' %}
                                غياب بعذر
                            {% else %}
                                {% trans "Excused" %}
                            {% endif %}
                        </small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="info-card">
                <h4 class="mb-3">
                    {% if LANGUAGE_CODE == 'ar' %}
                        الأنشطة والسلوك
                    {% else %}
                        {% trans "Activities & Behavior" %}
                    {% endif %}
                </h4>
                <div class="row">
                    <div class="col-6">
                        <p class="mb-2">
                            <strong>
                                {% if LANGUAGE_CODE == 'ar' %}
                                    تقييم السلوك:
                                {% else %}
                                    {% trans "Behavior Rating" %}:
                                {% endif %}
                            </strong><br>
                            <span class="badge {% if student.behavior_rating >= 4 %}badge-success{% elif student.behavior_rating >= 3 %}badge-info{% elif student.behavior_rating >= 2 %}badge-warning{% else %}badge-danger{% endif %}">
                                {% if LANGUAGE_CODE == 'ar' %}
                                    {% if student.behavior_rating >= 4 %}ممتاز
                                    {% elif student.behavior_rating >= 3 %}جيد جداً
                                    {% elif student.behavior_rating >= 2 %}جيد
                                    {% else %}يحتاج تحسين{% endif %}
                                {% else %}
                                    {{ student.behavior_rating }}/5
                                {% endif %}
                            </span>
                        </p>
                        <p class="mb-2">
                            <strong>
                                {% if LANGUAGE_CODE == 'ar' %}
                                    الأنشطة المشارك بها:
                                {% else %}
                                    {% trans "Activities Participated" %}:
                                {% endif %}
                            </strong><br>
                            <span class="arabic-number">{{ student.activities_count|arabic_digits }}</span>
                        </p>
                    </div>
                    <div class="col-6">
                        <p class="mb-2">
                            <strong>
                                {% if LANGUAGE_CODE == 'ar' %}
                                    الإنجازات:
                                {% else %}
                                    {% trans "Achievements" %}:
                                {% endif %}
                            </strong><br>
                            <span class="arabic-number">{{ student.achievements_count|arabic_digits }}</span>
                        </p>
                        <p class="mb-2">
                            <strong>
                                {% if LANGUAGE_CODE == 'ar' %}
                                    المخالفات:
                                {% else %}
                                    {% trans "Violations" %}:
                                {% endif %}
                            </strong><br>
                            <span class="arabic-number">{{ student.violations_count|arabic_digits }}</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Teacher Comments -->
    {% if student.teacher_comments %}
    <div class="row">
        <div class="col-12">
            <div class="info-card">
                <h4 class="mb-3">
                    {% if LANGUAGE_CODE == 'ar' %}
                        تعليقات المعلمين
                    {% else %}
                        {% trans "Teacher Comments" %}
                    {% endif %}
                </h4>
                {% for comment in student.teacher_comments %}
                <div class="border-left-primary p-3 mb-3" style="border-left: 3px solid #007bff;">
                    <div class="row">
                        <div class="col-md-8">
                            <p class="mb-1">
                                {% if LANGUAGE_CODE == 'ar' %}
                                    {{ comment.comment_ar|default:comment.comment }}
                                {% else %}
                                    {{ comment.comment }}
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-4 text-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %}">
                            <small class="text-muted">
                                {% if LANGUAGE_CODE == 'ar' %}
                                    {{ comment.teacher.name_ar|default:comment.teacher.full_name }}
                                {% else %}
                                    {{ comment.teacher.full_name }}
                                {% endif %}
                                <br>
                                {{ comment.date|format_date_localized }}
                            </small>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Report Footer -->
    <div class="row mt-4 pt-4 border-top">
        <div class="col-md-6">
            <p class="mb-1">
                <strong>
                    {% if LANGUAGE_CODE == 'ar' %}
                        تم إنشاء التقرير بواسطة:
                    {% else %}
                        {% trans "Report Generated By" %}:
                    {% endif %}
                </strong>
            </p>
            <p class="text-muted">
                {% if LANGUAGE_CODE == 'ar' %}
                    {{ report.generated_by.name_ar|default:report.generated_by.full_name }}
                {% else %}
                    {{ report.generated_by.full_name }}
                {% endif %}
            </p>
        </div>
        <div class="col-md-6 text-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %}">
            <p class="mb-1">
                <strong>
                    {% if LANGUAGE_CODE == 'ar' %}
                        تاريخ الإنشاء:
                    {% else %}
                        {% trans "Generated On" %}:
                    {% endif %}
                </strong>
            </p>
            <p class="text-muted">{{ report.created_at|format_datetime_localized }}</p>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mt-4 no-print">
        <div class="col-12 text-center">
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fas fa-print"></i>
                {% if LANGUAGE_CODE == 'ar' %}
                    طباعة التقرير
                {% else %}
                    {% trans "Print Report" %}
                {% endif %}
            </button>
            <a href="{% url 'reports:student_report_pdf' student.id %}" class="btn btn-secondary">
                <i class="fas fa-download"></i>
                {% if LANGUAGE_CODE == 'ar' %}
                    تحميل PDF
                {% else %}
                    {% trans "Download PDF" %}
                {% endif %}
            </a>
            <a href="{% url 'reports:student_reports' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i>
                {% if LANGUAGE_CODE == 'ar' %}
                    رجوع للتقارير
                {% else %}
                    {% trans "Back to Reports" %}
                {% endif %}
            </a>
        </div>
    </div>
</div>
{% endblock %}