<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Report Distribution</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .content {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 0 0 8px 8px;
        }
        
        .report-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        
        .report-info h3 {
            margin-top: 0;
            color: #007bff;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: bold;
            color: #495057;
        }
        
        .info-value {
            color: #6c757d;
        }
        
        .download-section {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin: 20px 0;
        }
        
        .download-button {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 10px;
        }
        
        .download-button:hover {
            background: #0056b3;
        }
        
        .footer {
            text-align: center;
            color: #6c757d;
            font-size: 12px;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .warning strong {
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 Report Ready</h1>
        <p>Your requested report has been generated successfully</p>
    </div>
    
    <div class="content">
        <p>Hello,</p>
        
        <p>The report you requested has been generated and is ready for download. Please find the details below:</p>
        
        <div class="report-info">
            <h3>Report Information</h3>
            
            <div class="info-row">
                <span class="info-label">Report Name:</span>
                <span class="info-value">{{ report_execution.template.name }}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Report Type:</span>
                <span class="info-value">{{ report_execution.template.get_report_type_display }}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Generated On:</span>
                <span class="info-value">{{ report_execution.created_at|date:"F d, Y H:i" }}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Generated By:</span>
                <span class="info-value">{{ report_execution.executed_by.get_full_name|default:report_execution.executed_by.username }}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Total Records:</span>
                <span class="info-value">{{ report_execution.row_count|default:"N/A" }}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Execution Time:</span>
                <span class="info-value">{{ report_execution.execution_time|default:"N/A" }}</span>
            </div>
        </div>
        
        {% if config.download_url %}
        <div class="download-section">
            <h3>📥 Download Report</h3>
            <p>Click the button below to download your report:</p>
            <a href="{{ config.download_url }}" class="download-button">Download Report</a>
            
            <div class="warning">
                <strong>Note:</strong> This download link will expire in {{ config.expires_days|default:"7" }} days for security reasons.
            </div>
        </div>
        {% endif %}
        
        {% if report_execution.template.description %}
        <div class="report-info">
            <h3>Report Description</h3>
            <p>{{ report_execution.template.description }}</p>
        </div>
        {% endif %}
        
        {% if config.parameters_used %}
        <div class="report-info">
            <h3>Parameters Used</h3>
            {% for key, value in config.parameters_used.items %}
            <div class="info-row">
                <span class="info-label">{{ key|title }}:</span>
                <span class="info-value">{{ value }}</span>
            </div>
            {% endfor %}
        </div>
        {% endif %}
        
        <p>If you have any questions about this report or need assistance, please contact the system administrator.</p>
        
        <p>Best regards,<br>
        School ERP System</p>
    </div>
    
    <div class="footer">
        <p>This is an automated message from the School Management System.</p>
        <p>Please do not reply to this email.</p>
        <p>© {{ report_execution.created_at|date:"Y" }} School Management System. All rights reserved.</p>
    </div>
</body>
</html>