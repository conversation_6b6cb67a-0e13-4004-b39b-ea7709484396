<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ config.title|default:"Report" }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            color: #333;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #007bff;
            margin: 0;
            font-size: 28px;
        }
        
        .header .meta {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
        }
        
        .summary {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .summary h3 {
            margin-top: 0;
            color: #495057;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .data-table th {
            background-color: #007bff;
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: bold;
        }
        
        .data-table td {
            padding: 10px 12px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .data-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .data-table tr:hover {
            background-color: #e3f2fd;
        }
        
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #666;
            font-size: 12px;
            border-top: 1px solid #dee2e6;
            padding-top: 20px;
        }
        
        .no-data {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 40px;
        }
        
        @media print {
            body {
                margin: 0;
            }
            
            .header {
                border-bottom: 2px solid #000;
            }
            
            .data-table th {
                background-color: #000 !important;
                -webkit-print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ config.title|default:"Report" }}</h1>
        <div class="meta">
            Generated on: {{ generated_at|date:"Y-m-d H:i:s" }}
        </div>
    </div>
    
    {% if config.description %}
    <div class="summary">
        <h3>Description</h3>
        <p>{{ config.description }}</p>
    </div>
    {% endif %}
    
    {% if report_data.data %}
    <div class="summary">
        <h3>Summary</h3>
        <p><strong>Total Records:</strong> {{ report_data.data|length }}</p>
        {% if report_data.columns %}
        <p><strong>Columns:</strong> {{ report_data.columns|length }}</p>
        {% endif %}
    </div>
    
    {% if report_data.columns %}
    <table class="data-table">
        <thead>
            <tr>
                {% for column in report_data.columns %}
                <th>{{ column }}</th>
                {% endfor %}
            </tr>
        </thead>
        <tbody>
            {% for row in report_data.data %}
            <tr>
                {% if row.items %}
                    {% for column in report_data.columns %}
                    <td>{{ row|lookup:column|default:"" }}</td>
                    {% endfor %}
                {% else %}
                    {% for cell in row %}
                    <td>{{ cell|default:"" }}</td>
                    {% endfor %}
                {% endif %}
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% endif %}
    
    {% else %}
    <div class="no-data">
        <p>No data available for this report.</p>
    </div>
    {% endif %}
    
    <div class="footer">
        <p>This report was generated automatically by the School ERP System.</p>
        <p>© {{ generated_at|date:"Y" }} School Management System. All rights reserved.</p>
    </div>
</body>
</html>