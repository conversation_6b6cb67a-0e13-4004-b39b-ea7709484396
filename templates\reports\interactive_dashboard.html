{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ dashboard.name }} - {% trans "Interactive Dashboard" %}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/gridstack@8.4.0/dist/gridstack.min.css" rel="stylesheet">
<link href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css" rel="stylesheet">
<style>
.dashboard-container {
    height: calc(100vh - 120px);
    overflow: hidden;
}

.dashboard-header {
    background: #fff;
    border-bottom: 1px solid #e9ecef;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dashboard-content {
    height: calc(100% - 70px);
    padding: 20px;
    background: #f8f9fa;
    overflow-y: auto;
}

.grid-stack {
    background: transparent;
}

.grid-stack-item-content {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.widget-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 50px;
}

.widget-title {
    font-weight: 600;
    color: #495057;
    margin: 0;
    font-size: 14px;
}

.widget-actions {
    display: flex;
    gap: 8px;
}

.widget-action {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.widget-action:hover {
    background: #e9ecef;
    color: #495057;
}

.widget-body {
    flex: 1;
    padding: 16px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 0;
}

.widget-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6c757d;
}

.widget-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #dc3545;
}

.metric-widget {
    text-align: center;
}

.metric-value {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 8px;
}

.metric-label {
    font-size: 0.9rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-icon {
    font-size: 3rem;
    margin-bottom: 16px;
    opacity: 0.7;
}

.chart-container {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 200px;
}

.table-container {
    width: 100%;
    height: 100%;
    overflow: auto;
}

.table-container table {
    width: 100%;
    font-size: 0.875rem;
}

.dashboard-toolbar {
    display: flex;
    gap: 12px;
    align-items: center;
}

.toolbar-group {
    display: flex;
    gap: 8px;
    align-items: center;
}

.toolbar-separator {
    width: 1px;
    height: 24px;
    background: #dee2e6;
    margin: 0 8px;
}

.refresh-indicator {
    display: none;
    color: #28a745;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fullscreen-mode {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background: #f8f9fa;
}

.fullscreen-mode .dashboard-header {
    background: #343a40;
    color: #fff;
}

.fullscreen-mode .dashboard-header .btn {
    color: #fff;
    border-color: #fff;
}

.add-widget-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #007bff;
    color: #fff;
    border: none;
    font-size: 24px;
    box-shadow: 0 4px 12px rgba(0,123,255,0.3);
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1000;
}

.add-widget-btn:hover {
    background: #0056b3;
    transform: scale(1.1);
}

.dashboard-filters {
    background: #fff;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: none;
}

.dashboard-filters.show {
    display: block;
}

.filter-group {
    display: flex;
    gap: 16px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.filter-item label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #495057;
}

.filter-item select,
.filter-item input {
    padding: 6px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.875rem;
}

.widget-resize-handle {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 20px;
    height: 20px;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 6 6"><path d="M6 6H0V4.5h1.5V3H3v1.5h1.5V6z" fill="%23999"/></svg>') no-repeat center;
    cursor: se-resize;
    opacity: 0.5;
}

.widget-resize-handle:hover {
    opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        gap: 12px;
        padding: 12px 16px;
    }
    
    .dashboard-toolbar {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .dashboard-content {
        padding: 12px;
    }
    
    .add-widget-btn {
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
}

/* Dark theme support */
.theme-dark {
    background: #1a1a1a;
    color: #e9ecef;
}

.theme-dark .dashboard-header {
    background: #2d3748;
    border-bottom-color: #4a5568;
}

.theme-dark .dashboard-content {
    background: #1a1a1a;
}

.theme-dark .grid-stack-item-content {
    background: #2d3748;
    border-color: #4a5568;
    color: #e9ecef;
}

.theme-dark .widget-header {
    background: #4a5568;
    border-bottom-color: #718096;
}

.theme-dark .dashboard-filters {
    background: #2d3748;
    border-color: #4a5568;
}
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container" id="dashboardContainer">
    <!-- Dashboard Header -->
    <div class="dashboard-header">
        <div class="d-flex align-items-center">
            <h4 class="mb-0 me-3">
                <i class="fas fa-tachometer-alt me-2"></i>
                {{ dashboard.name }}
            </h4>
            {% if dashboard.description %}
                <small class="text-muted">{{ dashboard.description }}</small>
            {% endif %}
        </div>
        
        <div class="dashboard-toolbar">
            <div class="toolbar-group">
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleFilters()" title="{% trans 'Filters' %}">
                    <i class="fas fa-filter"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshDashboard()" title="{% trans 'Refresh' %}">
                    <i class="fas fa-sync-alt" id="refreshIcon"></i>
                </button>
                <div class="refresh-indicator" id="refreshIndicator">
                    <i class="fas fa-spinner"></i>
                </div>
            </div>
            
            <div class="toolbar-separator"></div>
            
            <div class="toolbar-group">
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleEditMode()" id="editModeBtn" title="{% trans 'Edit Mode' %}">
                    <i class="fas fa-edit"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleFullscreen()" title="{% trans 'Fullscreen' %}">
                    <i class="fas fa-expand"></i>
                </button>
            </div>
            
            <div class="toolbar-separator"></div>
            
            <div class="toolbar-group">
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-download me-1"></i>
                        {% trans "Export" %}
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="exportDashboard('pdf')">
                            <i class="fas fa-file-pdf me-2"></i>PDF
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="exportDashboard('png')">
                            <i class="fas fa-image me-2"></i>PNG
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="exportDashboard('svg')">
                            <i class="fas fa-vector-square me-2"></i>SVG
                        </a></li>
                    </ul>
                </div>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="shareDashboard()" title="{% trans 'Share' %}">
                    <i class="fas fa-share-alt"></i>
                </button>
            </div>
            
            <div class="toolbar-separator"></div>
            
            <a href="{% url 'reports:dashboard' %}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                {% trans "Back" %}
            </a>
        </div>
    </div>
    
    <!-- Dashboard Filters -->
    <div class="dashboard-filters" id="dashboardFilters">
        <div class="filter-group">
            <div class="filter-item">
                <label>{% trans "Date Range" %}</label>
                <select class="form-select form-select-sm" id="dateRangeFilter">
                    <option value="today">{% trans "Today" %}</option>
                    <option value="week">{% trans "This Week" %}</option>
                    <option value="month" selected>{% trans "This Month" %}</option>
                    <option value="quarter">{% trans "This Quarter" %}</option>
                    <option value="year">{% trans "This Year" %}</option>
                    <option value="custom">{% trans "Custom Range" %}</option>
                </select>
            </div>
            
            <div class="filter-item">
                <label>{% trans "Department" %}</label>
                <select class="form-select form-select-sm" id="departmentFilter">
                    <option value="">{% trans "All Departments" %}</option>
                    <option value="academic">{% trans "Academic" %}</option>
                    <option value="finance">{% trans "Finance" %}</option>
                    <option value="hr">{% trans "Human Resources" %}</option>
                    <option value="admin">{% trans "Administration" %}</option>
                </select>
            </div>
            
            <div class="filter-item">
                <label>{% trans "Grade Level" %}</label>
                <select class="form-select form-select-sm" id="gradeFilter">
                    <option value="">{% trans "All Grades" %}</option>
                    <option value="elementary">{% trans "Elementary" %}</option>
                    <option value="middle">{% trans "Middle School" %}</option>
                    <option value="high">{% trans "High School" %}</option>
                </select>
            </div>
            
            <div class="filter-item">
                <button type="button" class="btn btn-sm btn-primary" onclick="applyFilters()">
                    <i class="fas fa-check me-1"></i>
                    {% trans "Apply" %}
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearFilters()">
                    <i class="fas fa-times me-1"></i>
                    {% trans "Clear" %}
                </button>
            </div>
        </div>
    </div>
    
    <!-- Dashboard Content -->
    <div class="dashboard-content">
        <div class="grid-stack" id="gridStack">
            <!-- Widgets will be dynamically loaded here -->
        </div>
    </div>
    
    <!-- Add Widget Button -->
    <button type="button" class="add-widget-btn" onclick="showAddWidgetModal()" title="{% trans 'Add Widget' %}" style="display: none;" id="addWidgetBtn">
        <i class="fas fa-plus"></i>
    </button>
</div>

<!-- Add Widget Modal -->
<div class="modal fade" id="addWidgetModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Add Widget" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6>{% trans "Chart Widgets" %}</h6>
                        <div class="list-group list-group-flush">
                            <a href="#" class="list-group-item list-group-item-action" onclick="selectWidgetType('chart', 'line')">
                                <i class="fas fa-chart-line me-2"></i>{% trans "Line Chart" %}
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="selectWidgetType('chart', 'bar')">
                                <i class="fas fa-chart-bar me-2"></i>{% trans "Bar Chart" %}
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="selectWidgetType('chart', 'pie')">
                                <i class="fas fa-chart-pie me-2"></i>{% trans "Pie Chart" %}
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="selectWidgetType('chart', 'doughnut')">
                                <i class="fas fa-circle-notch me-2"></i>{% trans "Doughnut Chart" %}
                            </a>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h6>{% trans "Data Widgets" %}</h6>
                        <div class="list-group list-group-flush">
                            <a href="#" class="list-group-item list-group-item-action" onclick="selectWidgetType('metric', null)">
                                <i class="fas fa-tachometer-alt me-2"></i>{% trans "Metric Widget" %}
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="selectWidgetType('table', null)">
                                <i class="fas fa-table me-2"></i>{% trans "Data Table" %}
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="selectWidgetType('gauge', null)">
                                <i class="fas fa-gauge me-2"></i>{% trans "Gauge Widget" %}
                            </a>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h6>{% trans "Other Widgets" %}</h6>
                        <div class="list-group list-group-flush">
                            <a href="#" class="list-group-item list-group-item-action" onclick="selectWidgetType('calendar', null)">
                                <i class="fas fa-calendar me-2"></i>{% trans "Calendar" %}
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="selectWidgetType('text', null)">
                                <i class="fas fa-font me-2"></i>{% trans "Text Widget" %}
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="selectWidgetType('image', null)">
                                <i class="fas fa-image me-2"></i>{% trans "Image Widget" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary" onclick="addSelectedWidget()" disabled id="addWidgetConfirm">
                    {% trans "Add Widget" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Widget Configuration Modal -->
<div class="modal fade" id="widgetConfigModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Widget Configuration" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="widgetConfigForm">
                    <div class="mb-3">
                        <label class="form-label">{% trans "Widget Name" %}</label>
                        <input type="text" class="form-control" id="widgetName" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">{% trans "Data Source" %}</label>
                        <select class="form-select" id="widgetDataSource">
                            <option value="">{% trans "Select Data Source" %}</option>
                            <option value="students">{% trans "Students" %}</option>
                            <option value="finance">{% trans "Finance" %}</option>
                            <option value="attendance">{% trans "Attendance" %}</option>
                            <option value="grades">{% trans "Grades" %}</option>
                            <option value="custom">{% trans "Custom Query" %}</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">{% trans "Refresh Interval (seconds)" %}</label>
                        <input type="number" class="form-control" id="widgetRefreshInterval" value="300" min="30">
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="widgetAutoRefresh" checked>
                            <label class="form-check-label" for="widgetAutoRefresh">
                                {% trans "Auto Refresh" %}
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary" onclick="saveWidgetConfig()">
                    {% trans "Save Configuration" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Dashboard Data -->
<script type="application/json" id="dashboardData">
{{ dashboard_data|safe }}
</script>

<script type="application/json" id="dashboardConfig">
{
    "id": {{ dashboard.id }},
    "name": "{{ dashboard.name|escapejs }}",
    "type": "{{ dashboard.dashboard_type }}",
    "editable": {% if dashboard.user == request.user %}true{% else %}false{% endif %},
    "real_time": {{ dashboard.settings.auto_refresh|yesno:"true,false" }},
    "refresh_interval": {{ dashboard.settings.refresh_interval|default:300 }}
}
</script>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/gridstack@8.4.0/dist/gridstack-all.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
<script src="{% static 'js/dashboard.js' %}"></script>
<script>
// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
});

let grid;
let dashboardConfig;
let dashboardData;
let editMode = false;
let fullscreenMode = false;
let selectedWidgetType = null;
let selectedChartType = null;
let widgets = {};
let charts = {};

function initializeDashboard() {
    // Load dashboard configuration
    const configScript = document.getElementById('dashboardConfig');
    const dataScript = document.getElementById('dashboardData');
    
    if (configScript) {
        dashboardConfig = JSON.parse(configScript.textContent);
    }
    
    if (dataScript) {
        dashboardData = JSON.parse(dataScript.textContent);
    }
    
    // Initialize GridStack
    initializeGrid();
    
    // Load widgets
    loadWidgets();
    
    // Setup real-time updates if enabled
    if (dashboardConfig.real_time) {
        setupRealTimeUpdates();
    }
    
    // Setup auto-refresh
    if (dashboardConfig.refresh_interval > 0) {
        setInterval(refreshDashboard, dashboardConfig.refresh_interval * 1000);
    }
}

function initializeGrid() {
    grid = GridStack.init({
        cellHeight: 60,
        verticalMargin: 10,
        horizontalMargin: 10,
        minRow: 1,
        maxRow: 20,
        animate: true,
        resizable: {
            handles: 'e, se, s, sw, w'
        },
        draggable: {
            handle: '.widget-header'
        },
        disableOneColumnMode: true,
        float: false
    });
    
    // Handle widget position changes
    grid.on('change', function(event, items) {
        if (editMode) {
            items.forEach(function(item) {
                updateWidgetPosition(item.id, {
                    x: item.x,
                    y: item.y,
                    w: item.w,
                    h: item.h
                });
            });
        }
    });
    
    // Disable dragging and resizing by default
    grid.enableMove(false);
    grid.enableResize(false);
}

function loadWidgets() {
    if (!dashboardData || !dashboardData.widgets) {
        return;
    }
    
    dashboardData.widgets.forEach(function(widgetData) {
        createWidget(widgetData);
    });
}

function createWidget(widgetData) {
    const widgetId = widgetData.id;
    const position = widgetData.position;
    
    // Create widget HTML
    const widgetHtml = `
        <div class="grid-stack-item-content">
            <div class="widget-header">
                <h6 class="widget-title">${widgetData.name}</h6>
                <div class="widget-actions">
                    <button type="button" class="widget-action" onclick="refreshWidget(${widgetId})" title="Refresh">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button type="button" class="widget-action" onclick="configureWidget(${widgetId})" title="Configure">
                        <i class="fas fa-cog"></i>
                    </button>
                    <button type="button" class="widget-action" onclick="removeWidget(${widgetId})" title="Remove" style="display: none;" class="edit-only">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="widget-body" id="widget-body-${widgetId}">
                <div class="widget-loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading widget data...</p>
                </div>
            </div>
        </div>
    `;
    
    // Add widget to grid
    const gridItem = grid.addWidget(widgetHtml, {
        x: position.x || 0,
        y: position.y || 0,
        w: position.w || 4,
        h: position.h || 3,
        id: widgetId.toString()
    });
    
    // Store widget reference
    widgets[widgetId] = {
        element: gridItem,
        data: widgetData,
        chart: null
    };
    
    // Load widget content
    loadWidgetContent(widgetId, widgetData);
}

function loadWidgetContent(widgetId, widgetData) {
    const widgetBody = document.getElementById(`widget-body-${widgetId}`);
    
    if (widgetData.error) {
        widgetBody.innerHTML = `
            <div class="widget-error">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <p>Error loading widget</p>
                <small>${widgetData.error}</small>
            </div>
        `;
        return;
    }
    
    if (!widgetData.data) {
        widgetBody.innerHTML = `
            <div class="widget-error">
                <i class="fas fa-exclamation-circle fa-2x mb-2"></i>
                <p>No data available</p>
            </div>
        `;
        return;
    }
    
    // Render widget based on type
    switch (widgetData.widget_type) {
        case 'metric':
            renderMetricWidget(widgetId, widgetData);
            break;
        case 'chart':
            renderChartWidget(widgetId, widgetData);
            break;
        case 'table':
            renderTableWidget(widgetId, widgetData);
            break;
        case 'gauge':
            renderGaugeWidget(widgetId, widgetData);
            break;
        case 'calendar':
            renderCalendarWidget(widgetId, widgetData);
            break;
        case 'text':
            renderTextWidget(widgetId, widgetData);
            break;
        default:
            widgetBody.innerHTML = `
                <div class="widget-error">
                    <i class="fas fa-question-circle fa-2x mb-2"></i>
                    <p>Unknown widget type: ${widgetData.widget_type}</p>
                </div>
            `;
    }
}

function renderMetricWidget(widgetId, widgetData) {
    const widgetBody = document.getElementById(`widget-body-${widgetId}`);
    const metrics = widgetData.data.metrics || [];
    
    if (metrics.length === 0) {
        widgetBody.innerHTML = '<p class="text-muted">No metrics available</p>';
        return;
    }
    
    let html = '<div class="row h-100">';
    
    metrics.forEach(function(metric, index) {
        const colClass = metrics.length === 1 ? 'col-12' : 
                        metrics.length === 2 ? 'col-6' : 
                        metrics.length === 3 ? 'col-4' : 'col-3';
        
        let value = metric.value;
        if (metric.format === 'currency') {
            value = '$' + value.toLocaleString();
        } else if (metric.format === 'percentage') {
            value = value + '%';
        } else if (metric.format === 'number') {
            value = value.toLocaleString();
        }
        
        html += `
            <div class="${colClass}">
                <div class="metric-widget h-100 d-flex flex-column justify-content-center">
                    <div class="metric-icon text-${metric.color}">
                        <i class="fas fa-${metric.icon}"></i>
                    </div>
                    <div class="metric-value text-${metric.color}">${value}</div>
                    <div class="metric-label">${metric.name}</div>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    widgetBody.innerHTML = html;
}

function renderChartWidget(widgetId, widgetData) {
    const widgetBody = document.getElementById(`widget-body-${widgetId}`);
    
    // Create canvas for chart
    widgetBody.innerHTML = `
        <div class="chart-container">
            <canvas id="chart-${widgetId}"></canvas>
        </div>
    `;
    
    const canvas = document.getElementById(`chart-${widgetId}`);
    const ctx = canvas.getContext('2d');
    
    // Destroy existing chart if it exists
    if (charts[widgetId]) {
        charts[widgetId].destroy();
    }
    
    // Create new chart
    const chartConfig = {
        type: widgetData.chart_type || 'line',
        data: widgetData.data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: false
                }
            },
            scales: widgetData.chart_type === 'pie' || widgetData.chart_type === 'doughnut' ? {} : {
                y: {
                    beginAtZero: true
                }
            }
        }
    };
    
    charts[widgetId] = new Chart(ctx, chartConfig);
}

function renderTableWidget(widgetId, widgetData) {
    const widgetBody = document.getElementById(`widget-body-${widgetId}`);
    const data = widgetData.data;
    
    if (!data.columns || !data.rows) {
        widgetBody.innerHTML = '<p class="text-muted">No table data available</p>';
        return;
    }
    
    let html = `
        <div class="table-container">
            <table class="table table-sm table-striped">
                <thead>
                    <tr>
    `;
    
    data.columns.forEach(function(column) {
        html += `<th>${column}</th>`;
    });
    
    html += `
                    </tr>
                </thead>
                <tbody>
    `;
    
    data.rows.forEach(function(row) {
        html += '<tr>';
        row.forEach(function(cell) {
            html += `<td>${cell}</td>`;
        });
        html += '</tr>';
    });
    
    html += `
                </tbody>
            </table>
        </div>
    `;
    
    widgetBody.innerHTML = html;
}

function renderGaugeWidget(widgetId, widgetData) {
    const widgetBody = document.getElementById(`widget-body-${widgetId}`);
    // Gauge widget implementation would go here
    widgetBody.innerHTML = '<p class="text-muted">Gauge widget - Coming soon</p>';
}

function renderCalendarWidget(widgetId, widgetData) {
    const widgetBody = document.getElementById(`widget-body-${widgetId}`);
    // Calendar widget implementation would go here
    widgetBody.innerHTML = '<p class="text-muted">Calendar widget - Coming soon</p>';
}

function renderTextWidget(widgetId, widgetData) {
    const widgetBody = document.getElementById(`widget-body-${widgetId}`);
    const content = widgetData.data.content || 'No content available';
    widgetBody.innerHTML = `<div class="p-3">${content}</div>`;
}

function toggleEditMode() {
    editMode = !editMode;
    const editBtn = document.getElementById('editModeBtn');
    const addBtn = document.getElementById('addWidgetBtn');
    const editOnlyElements = document.querySelectorAll('.edit-only');
    
    if (editMode) {
        editBtn.classList.add('active');
        editBtn.innerHTML = '<i class="fas fa-save"></i>';
        addBtn.style.display = 'block';
        grid.enableMove(true);
        grid.enableResize(true);
        
        editOnlyElements.forEach(function(element) {
            element.style.display = 'inline-block';
        });
    } else {
        editBtn.classList.remove('active');
        editBtn.innerHTML = '<i class="fas fa-edit"></i>';
        addBtn.style.display = 'none';
        grid.enableMove(false);
        grid.enableResize(false);
        
        editOnlyElements.forEach(function(element) {
            element.style.display = 'none';
        });
        
        // Save dashboard layout
        saveDashboardLayout();
    }
}

function toggleFullscreen() {
    const container = document.getElementById('dashboardContainer');
    fullscreenMode = !fullscreenMode;
    
    if (fullscreenMode) {
        container.classList.add('fullscreen-mode');
    } else {
        container.classList.remove('fullscreen-mode');
    }
}

function toggleFilters() {
    const filters = document.getElementById('dashboardFilters');
    filters.classList.toggle('show');
}

function refreshDashboard() {
    const refreshIcon = document.getElementById('refreshIcon');
    const refreshIndicator = document.getElementById('refreshIndicator');
    
    refreshIcon.style.display = 'none';
    refreshIndicator.style.display = 'inline-block';
    
    // Refresh all widgets
    Object.keys(widgets).forEach(function(widgetId) {
        refreshWidget(widgetId);
    });
    
    setTimeout(function() {
        refreshIcon.style.display = 'inline-block';
        refreshIndicator.style.display = 'none';
    }, 2000);
}

function refreshWidget(widgetId) {
    // Implementation for refreshing individual widget
    console.log('Refreshing widget:', widgetId);
}

function configureWidget(widgetId) {
    // Show widget configuration modal
    const modal = new bootstrap.Modal(document.getElementById('widgetConfigModal'));
    modal.show();
}

function removeWidget(widgetId) {
    if (confirm('Are you sure you want to remove this widget?')) {
        grid.removeWidget(widgets[widgetId].element);
        delete widgets[widgetId];
        
        if (charts[widgetId]) {
            charts[widgetId].destroy();
            delete charts[widgetId];
        }
    }
}

function showAddWidgetModal() {
    const modal = new bootstrap.Modal(document.getElementById('addWidgetModal'));
    modal.show();
}

function selectWidgetType(type, chartType) {
    selectedWidgetType = type;
    selectedChartType = chartType;
    
    // Update UI to show selection
    document.querySelectorAll('#addWidgetModal .list-group-item').forEach(function(item) {
        item.classList.remove('active');
    });
    
    event.target.classList.add('active');
    document.getElementById('addWidgetConfirm').disabled = false;
}

function addSelectedWidget() {
    if (!selectedWidgetType) return;
    
    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('addWidgetModal'));
    modal.hide();
    
    // Create new widget
    const newWidget = {
        id: Date.now(), // Temporary ID
        name: 'New ' + selectedWidgetType.charAt(0).toUpperCase() + selectedWidgetType.slice(1),
        widget_type: selectedWidgetType,
        chart_type: selectedChartType,
        position: { x: 0, y: 0, w: 4, h: 3 },
        data: getSampleData(selectedWidgetType, selectedChartType)
    };
    
    createWidget(newWidget);
    
    // Reset selection
    selectedWidgetType = null;
    selectedChartType = null;
}

function getSampleData(widgetType, chartType) {
    // Return sample data based on widget type
    if (widgetType === 'metric') {
        return {
            metrics: [{
                name: 'Sample Metric',
                value: 100,
                format: 'number',
                icon: 'chart-bar',
                color: 'primary'
            }]
        };
    } else if (widgetType === 'chart') {
        return {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
            datasets: [{
                label: 'Sample Data',
                data: [12, 19, 3, 5, 2],
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        };
    } else if (widgetType === 'table') {
        return {
            columns: ['Name', 'Value'],
            rows: [
                ['Sample 1', '100'],
                ['Sample 2', '200']
            ]
        };
    }
    
    return {};
}

function applyFilters() {
    // Implementation for applying dashboard filters
    console.log('Applying filters');
    refreshDashboard();
}

function clearFilters() {
    // Reset all filter controls
    document.getElementById('dateRangeFilter').value = 'month';
    document.getElementById('departmentFilter').value = '';
    document.getElementById('gradeFilter').value = '';
    
    applyFilters();
}

function exportDashboard(format) {
    // Implementation for dashboard export
    console.log('Exporting dashboard as:', format);
}

function shareDashboard() {
    // Implementation for dashboard sharing
    console.log('Sharing dashboard');
}

function updateWidgetPosition(widgetId, position) {
    // Send position update to server
    fetch(`/reports/api/widgets/${widgetId}/position/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ position: position })
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            console.error('Failed to update widget position:', data.error);
        }
    })
    .catch(error => {
        console.error('Error updating widget position:', error);
    });
}

function saveDashboardLayout() {
    const layout = [];
    
    Object.keys(widgets).forEach(function(widgetId) {
        const element = widgets[widgetId].element;
        const gridItem = element.gridstackNode;
        
        layout.push({
            id: widgetId,
            x: gridItem.x,
            y: gridItem.y,
            w: gridItem.w,
            h: gridItem.h
        });
    });
    
    // Send layout to server
    fetch(`/reports/api/dashboards/${dashboardConfig.id}/layout/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ layout: layout })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Dashboard layout saved successfully');
        } else {
            console.error('Failed to save dashboard layout:', data.error);
        }
    })
    .catch(error => {
        console.error('Error saving dashboard layout:', error);
    });
}

function setupRealTimeUpdates() {
    // WebSocket implementation for real-time updates would go here
    console.log('Setting up real-time updates for dashboard:', dashboardConfig.id);
}

function saveWidgetConfig() {
    // Implementation for saving widget configuration
    const modal = bootstrap.Modal.getInstance(document.getElementById('widgetConfigModal'));
    modal.hide();
}
</script>
{% endblock %}