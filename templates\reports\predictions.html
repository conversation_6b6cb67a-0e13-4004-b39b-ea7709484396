{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Predictive Analytics" %}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css" rel="stylesheet">
<style>
.prediction-card {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    background: #fff;
}

.prediction-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.prediction-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

.prediction-body {
    padding: 20px;
}

.metric-value {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 8px;
}

.metric-label {
    font-size: 0.9rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.trend-indicator {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.trend-up {
    background: #d4edda;
    color: #155724;
}

.trend-down {
    background: #f8d7da;
    color: #721c24;
}

.trend-stable {
    background: #fff3cd;
    color: #856404;
}

.alert-item {
    border-left: 4px solid;
    padding: 12px 16px;
    margin-bottom: 12px;
    border-radius: 0 8px 8px 0;
}

.alert-high {
    border-left-color: #dc3545;
    background: #f8d7da;
    color: #721c24;
}

.alert-medium {
    border-left-color: #ffc107;
    background: #fff3cd;
    color: #856404;
}

.alert-low {
    border-left-color: #17a2b8;
    background: #d1ecf1;
    color: #0c5460;
}

.chart-container {
    position: relative;
    height: 300px;
    margin: 20px 0;
}

.prediction-controls {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    border-radius: 8px;
}

.confidence-bar {
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 8px;
}

.confidence-fill {
    height: 100%;
    background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%);
    transition: width 0.3s ease;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-crystal-ball me-2"></i>
                    {% trans "Predictive Analytics" %}
                </h2>
                <div>
                    <button type="button" class="btn btn-outline-primary" onclick="refreshPredictions()">
                        <i class="fas fa-sync-alt me-1"></i>
                        {% trans "Refresh Predictions" %}
                    </button>
                    <button type="button" class="btn btn-primary" onclick="showPredictionSettings()">
                        <i class="fas fa-cog me-1"></i>
                        {% trans "Settings" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Prediction Controls -->
    <div class="prediction-controls">
        <div class="row">
            <div class="col-md-3">
                <label class="form-label">{% trans "Prediction Period" %}</label>
                <select class="form-select" id="predictionPeriod">
                    <option value="3">{% trans "3 Months" %}</option>
                    <option value="6" selected>{% trans "6 Months" %}</option>
                    <option value="12">{% trans "12 Months" %}</option>
                    <option value="24">{% trans "24 Months" %}</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">{% trans "Confidence Level" %}</label>
                <select class="form-select" id="confidenceLevel">
                    <option value="80">80%</option>
                    <option value="90" selected>90%</option>
                    <option value="95">95%</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">{% trans "Model Type" %}</label>
                <select class="form-select" id="modelType">
                    <option value="auto" selected>{% trans "Auto Select" %}</option>
                    <option value="linear">{% trans "Linear Regression" %}</option>
                    <option value="forest">{% trans "Random Forest" %}</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="button" class="btn btn-primary w-100" onclick="updatePredictions()">
                    <i class="fas fa-chart-line me-1"></i>
                    {% trans "Update Predictions" %}
                </button>
            </div>
        </div>
    </div>

    <!-- Predictive Alerts -->
    {% if predictive_alerts.success and predictive_alerts.alerts %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        {% trans "Predictive Alerts" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% for alert in predictive_alerts.alerts %}
                    <div class="alert-item alert-{{ alert.severity }}">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <strong>{{ alert.message }}</strong>
                                {% if alert.type == 'performance_alert' %}
                                    <p class="mb-0 mt-1">
                                        <small>{{ alert.student_count }} {% trans "students need attention" %}</small>
                                    </p>
                                {% endif %}
                            </div>
                            <span class="badge bg-{{ alert.severity }}">
                                {% trans "Priority" %}: {{ alert.priority }}
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Prediction Cards -->
    <div class="row">
        <!-- Enrollment Prediction -->
        <div class="col-lg-6 mb-4">
            <div class="prediction-card" id="enrollmentCard">
                <div class="prediction-header">
                    <h4 class="mb-0">
                        <i class="fas fa-user-graduate me-2"></i>
                        {% trans "Student Enrollment" %}
                    </h4>
                    <p class="mb-0 opacity-75">{% trans "Predicted enrollment trends" %}</p>
                </div>
                <div class="prediction-body">
                    {% if enrollment_prediction.success %}
                        <div class="chart-container">
                            <canvas id="enrollmentChart"></canvas>
                        </div>
                        <div class="row text-center">
                            {% for pred in enrollment_prediction.predictions|slice:":3" %}
                            <div class="col-4">
                                <div class="metric-value text-primary">{{ pred.predicted_enrollment }}</div>
                                <div class="metric-label">{{ pred.month }}</div>
                                <div class="confidence-bar">
                                    <div class="confidence-fill" style="width: {{ pred.confidence|floatformat:0 }}%"></div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-exclamation-circle fa-3x text-muted mb-3"></i>
                            <p class="text-muted">{{ enrollment_prediction.error }}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Revenue Prediction -->
        <div class="col-lg-6 mb-4">
            <div class="prediction-card" id="revenueCard">
                <div class="prediction-header" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                    <h4 class="mb-0">
                        <i class="fas fa-dollar-sign me-2"></i>
                        {% trans "Revenue Trends" %}
                    </h4>
                    <p class="mb-0 opacity-75">{% trans "Financial forecasting" %}</p>
                </div>
                <div class="prediction-body">
                    {% if revenue_prediction.success %}
                        <div class="chart-container">
                            <canvas id="revenueChart"></canvas>
                        </div>
                        {% if revenue_prediction.trends %}
                        <div class="text-center">
                            <span class="trend-indicator trend-{{ revenue_prediction.trends.trend }}">
                                {% if revenue_prediction.trends.trend == 'increasing' %}
                                    <i class="fas fa-arrow-up me-1"></i>
                                {% elif revenue_prediction.trends.trend == 'decreasing' %}
                                    <i class="fas fa-arrow-down me-1"></i>
                                {% else %}
                                    <i class="fas fa-minus me-1"></i>
                                {% endif %}
                                {{ revenue_prediction.trends.growth_rate|floatformat:1 }}%
                            </span>
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-exclamation-circle fa-3x text-muted mb-3"></i>
                            <p class="text-muted">{{ revenue_prediction.error }}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Performance Prediction -->
        <div class="col-lg-6 mb-4">
            <div class="prediction-card" id="performanceCard">
                <div class="prediction-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <h4 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        {% trans "Academic Performance" %}
                    </h4>
                    <p class="mb-0 opacity-75">{% trans "Student performance predictions" %}</p>
                </div>
                <div class="prediction-body">
                    {% if performance_prediction.success %}
                        {% if performance_prediction.at_risk_students %}
                        <div class="alert alert-warning">
                            <h6>
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                {% trans "At-Risk Students" %}
                            </h6>
                            <p class="mb-2">
                                {{ performance_prediction.at_risk_students|length }} {% trans "students identified as at-risk" %}
                            </p>
                            <div class="row">
                                {% for student in performance_prediction.at_risk_students|slice:":3" %}
                                <div class="col-4 text-center">
                                    <div class="metric-value text-warning">{{ student.predicted_percentage|floatformat:1 }}%</div>
                                    <div class="metric-label">{% trans "Student" %} {{ student.student_id }}</div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if performance_prediction.recommendations %}
                        <h6>{% trans "Recommendations" %}</h6>
                        <ul class="list-unstyled">
                            {% for rec in performance_prediction.recommendations %}
                            <li class="mb-2">
                                <i class="fas fa-lightbulb text-warning me-2"></i>
                                {{ rec }}
                            </li>
                            {% endfor %}
                        </ul>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-exclamation-circle fa-3x text-muted mb-3"></i>
                            <p class="text-muted">{{ performance_prediction.error }}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Attendance Prediction -->
        <div class="col-lg-6 mb-4">
            <div class="prediction-card" id="attendanceCard">
                <div class="prediction-header" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                    <h4 class="mb-0">
                        <i class="fas fa-calendar-check me-2"></i>
                        {% trans "Attendance Patterns" %}
                    </h4>
                    <p class="mb-0 opacity-75">{% trans "Attendance forecasting" %}</p>
                </div>
                <div class="prediction-body">
                    {% if attendance_prediction.success %}
                        <div class="chart-container">
                            <canvas id="attendanceChart"></canvas>
                        </div>
                        
                        {% if attendance_prediction.patterns %}
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="metric-value text-info">
                                    {{ attendance_prediction.patterns.seasonal_trends.highest_month }}
                                </div>
                                <div class="metric-label">{% trans "Best Month" %}</div>
                            </div>
                            <div class="col-6">
                                <div class="metric-value text-warning">
                                    {{ attendance_prediction.patterns.day_of_week_patterns.worst_day }}
                                </div>
                                <div class="metric-label">{% trans "Challenging Day" %}</div>
                            </div>
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-exclamation-circle fa-3x text-muted mb-3"></i>
                            <p class="text-muted">{{ attendance_prediction.error }}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Model Performance Metrics -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        {% trans "Model Performance Metrics" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% if enrollment_prediction.success and enrollment_prediction.model_metrics %}
                        <div class="col-md-3">
                            <h6>{% trans "Enrollment Model" %}</h6>
                            <div class="metric-value text-primary">{{ enrollment_prediction.model_metrics.accuracy|floatformat:1 }}%</div>
                            <div class="metric-label">{% trans "Accuracy" %}</div>
                            <small class="text-muted">R² Score: {{ enrollment_prediction.model_metrics.r2_score|floatformat:3 }}</small>
                        </div>
                        {% endif %}
                        
                        {% if revenue_prediction.success and revenue_prediction.model_metrics %}
                        <div class="col-md-3">
                            <h6>{% trans "Revenue Model" %}</h6>
                            <div class="metric-value text-success">{{ revenue_prediction.model_metrics.accuracy|floatformat:1 }}%</div>
                            <div class="metric-label">{% trans "Accuracy" %}</div>
                            <small class="text-muted">MAE: {{ revenue_prediction.model_metrics.mae|floatformat:2 }}</small>
                        </div>
                        {% endif %}
                        
                        {% if performance_prediction.success and performance_prediction.model_metrics %}
                        <div class="col-md-3">
                            <h6>{% trans "Performance Model" %}</h6>
                            <div class="metric-value text-info">{{ performance_prediction.model_metrics.accuracy|floatformat:1 }}%</div>
                            <div class="metric-label">{% trans "Accuracy" %}</div>
                            <small class="text-muted">MSE: {{ performance_prediction.model_metrics.mse|floatformat:2 }}</small>
                        </div>
                        {% endif %}
                        
                        {% if attendance_prediction.success and attendance_prediction.model_metrics %}
                        <div class="col-md-3">
                            <h6>{% trans "Attendance Model" %}</h6>
                            <div class="metric-value text-warning">{{ attendance_prediction.model_metrics.accuracy|floatformat:1 }}%</div>
                            <div class="metric-label">{% trans "Accuracy" %}</div>
                            <small class="text-muted">R² Score: {{ attendance_prediction.model_metrics.r2_score|floatformat:3 }}</small>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Prediction Settings Modal -->
<div class="modal fade" id="predictionSettingsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Prediction Settings" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="predictionSettingsForm">
                    <div class="mb-3">
                        <label class="form-label">{% trans "Auto Refresh Interval" %}</label>
                        <select class="form-select" id="autoRefreshInterval">
                            <option value="0">{% trans "Disabled" %}</option>
                            <option value="3600">{% trans "1 Hour" %}</option>
                            <option value="21600">{% trans "6 Hours" %}</option>
                            <option value="86400" selected>{% trans "24 Hours" %}</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enableNotifications" checked>
                            <label class="form-check-label" for="enableNotifications">
                                {% trans "Enable Predictive Alerts" %}
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">{% trans "Alert Threshold" %}</label>
                        <input type="range" class="form-range" id="alertThreshold" min="1" max="10" value="5">
                        <div class="d-flex justify-content-between">
                            <small>{% trans "Low" %}</small>
                            <small>{% trans "High" %}</small>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary" onclick="savePredictionSettings()">
                    {% trans "Save Settings" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Prediction Data -->
<script type="application/json" id="predictionData">
{
    "enrollment": {{ enrollment_prediction|safe }},
    "revenue": {{ revenue_prediction|safe }},
    "performance": {{ performance_prediction|safe }},
    "attendance": {{ attendance_prediction|safe }}
}
</script>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
<script src="{% static 'js/predictions.js' %}"></script>
<script>
// Initialize predictions when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializePredictions();
});
</script>
{% endblock %}