{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Report Builder" %}{% endblock %}

{% block extra_css %}
<link href="{% static 'css/report-builder.css' %}" rel="stylesheet">
<link href="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.css" rel="stylesheet">
<style>
.drag-drop-area {
    min-height: 200px;
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.drag-drop-area.drag-over {
    border-color: #007bff;
    background-color: #e3f2fd;
}

.field-item {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
    margin: 4px;
    cursor: move;
    display: inline-block;
    transition: all 0.2s ease;
}

.field-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0,123,255,0.2);
}

.field-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.model-section {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 15px;
    overflow: hidden;
}

.model-header {
    background: #f8f9fa;
    padding: 12px 15px;
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.model-fields {
    padding: 15px;
    display: none;
}

.model-fields.show {
    display: block;
}

.query-builder-panel {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.filter-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
}

.btn-add-filter {
    background: #28a745;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
}

.btn-remove-filter {
    background: #dc3545;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
}

.sql-preview {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    white-space: pre-wrap;
    max-height: 200px;
    overflow-y: auto;
}

.report-builder-tabs {
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 20px;
}

.report-builder-tabs .nav-link {
    border: none;
    border-bottom: 2px solid transparent;
    color: #6c757d;
}

.report-builder-tabs .nav-link.active {
    border-bottom-color: #007bff;
    color: #007bff;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-chart-bar me-2"></i>
                    {% trans "Report Builder" %}
                </h2>
                <div>
                    <a href="{% url 'reports:report_builder_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        {% trans "New Report Builder" %}
                    </a>
                    <a href="{% url 'reports:report_templates' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-list me-1"></i>
                        {% trans "View Templates" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Existing Report Builders -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-folder me-2"></i>
                        {% trans "My Report Builders" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if report_builders %}
                        <div class="list-group list-group-flush">
                            {% for builder in report_builders %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ builder.name }}</h6>
                                    <small class="text-muted">
                                        {% if builder.description %}
                                            {{ builder.description|truncatechars:50 }}
                                        {% else %}
                                            {% trans "No description" %}
                                        {% endif %}
                                    </small>
                                </div>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'reports:report_builder_designer' builder.pk %}" 
                                       class="btn btn-outline-primary" title="{% trans 'Design' %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'reports:report_builder_edit' builder.pk %}" 
                                       class="btn btn-outline-secondary" title="{% trans 'Settings' %}">
                                        <i class="fas fa-cog"></i>
                                    </a>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                            <p class="text-muted">{% trans "No report builders found" %}</p>
                            <a href="{% url 'reports:report_builder_create' %}" class="btn btn-primary">
                                {% trans "Create Your First Report Builder" %}
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Quick Start Guide -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-rocket me-2"></i>
                        {% trans "Quick Start Guide" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-primary mb-3">
                                <div class="card-body text-center">
                                    <i class="fas fa-plus-circle fa-3x text-primary mb-3"></i>
                                    <h5>{% trans "Create Report Builder" %}</h5>
                                    <p class="text-muted">
                                        {% trans "Start by creating a new report builder with drag-and-drop interface" %}
                                    </p>
                                    <a href="{% url 'reports:report_builder_create' %}" class="btn btn-primary">
                                        {% trans "Get Started" %}
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success mb-3">
                                <div class="card-body text-center">
                                    <i class="fas fa-magic fa-3x text-success mb-3"></i>
                                    <h5>{% trans "Design Your Report" %}</h5>
                                    <p class="text-muted">
                                        {% trans "Use the visual designer to build queries without writing SQL" %}
                                    </p>
                                    <a href="#" class="btn btn-success" onclick="showDesignDemo()">
                                        {% trans "See Demo" %}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-info mb-3">
                                <div class="card-body text-center">
                                    <i class="fas fa-share-alt fa-3x text-info mb-3"></i>
                                    <h5>{% trans "Share Reports" %}</h5>
                                    <p class="text-muted">
                                        {% trans "Share your reports with colleagues or schedule automatic delivery" %}
                                    </p>
                                    <a href="{% url 'reports:scheduled' %}" class="btn btn-info">
                                        {% trans "Learn More" %}
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-warning mb-3">
                                <div class="card-body text-center">
                                    <i class="fas fa-download fa-3x text-warning mb-3"></i>
                                    <h5>{% trans "Export Reports" %}</h5>
                                    <p class="text-muted">
                                        {% trans "Export your reports to PDF, Excel, CSV, or other formats" %}
                                    </p>
                                    <a href="#" class="btn btn-warning" onclick="showExportOptions()">
                                        {% trans "View Options" %}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Features Overview -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-star me-2"></i>
                        {% trans "Key Features" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="feature-item mb-3">
                                <i class="fas fa-mouse-pointer text-primary me-2"></i>
                                <strong>{% trans "Drag & Drop Interface" %}</strong>
                                <p class="text-muted small">
                                    {% trans "Build reports visually without writing SQL code" %}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-item mb-3">
                                <i class="fas fa-filter text-success me-2"></i>
                                <strong>{% trans "Advanced Filtering" %}</strong>
                                <p class="text-muted small">
                                    {% trans "Apply complex filters and conditions to your data" %}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-item mb-3">
                                <i class="fas fa-chart-line text-info me-2"></i>
                                <strong>{% trans "Data Visualization" %}</strong>
                                <p class="text-muted small">
                                    {% trans "Create charts and graphs from your report data" %}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="feature-item mb-3">
                                <i class="fas fa-clock text-warning me-2"></i>
                                <strong>{% trans "Scheduled Reports" %}</strong>
                                <p class="text-muted small">
                                    {% trans "Automatically generate and deliver reports" %}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-item mb-3">
                                <i class="fas fa-users text-danger me-2"></i>
                                <strong>{% trans "Collaboration" %}</strong>
                                <p class="text-muted small">
                                    {% trans "Share report builders and templates with your team" %}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-item mb-3">
                                <i class="fas fa-mobile-alt text-secondary me-2"></i>
                                <strong>{% trans "Mobile Friendly" %}</strong>
                                <p class="text-muted small">
                                    {% trans "Access and view reports on any device" %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Demo Modal -->
<div class="modal fade" id="designDemoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Report Designer Demo" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <img src="{% static 'images/report-builder-demo.png' %}" 
                         alt="{% trans 'Report Builder Demo' %}" 
                         class="img-fluid rounded"
                         style="max-height: 400px;">
                    <p class="mt-3 text-muted">
                        {% trans "Drag fields from the left panel to build your report structure" %}
                    </p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {% trans "Close" %}
                </button>
                <a href="{% url 'reports:report_builder_create' %}" class="btn btn-primary">
                    {% trans "Try It Now" %}
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Export Options Modal -->
<div class="modal fade" id="exportOptionsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Export Options" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="list-group">
                    <div class="list-group-item d-flex align-items-center">
                        <i class="fas fa-file-pdf text-danger me-3"></i>
                        <div>
                            <strong>PDF</strong>
                            <p class="mb-0 text-muted small">{% trans "Professional formatted reports" %}</p>
                        </div>
                    </div>
                    <div class="list-group-item d-flex align-items-center">
                        <i class="fas fa-file-excel text-success me-3"></i>
                        <div>
                            <strong>Excel</strong>
                            <p class="mb-0 text-muted small">{% trans "Spreadsheet format for analysis" %}</p>
                        </div>
                    </div>
                    <div class="list-group-item d-flex align-items-center">
                        <i class="fas fa-file-csv text-primary me-3"></i>
                        <div>
                            <strong>CSV</strong>
                            <p class="mb-0 text-muted small">{% trans "Raw data for import/export" %}</p>
                        </div>
                    </div>
                    <div class="list-group-item d-flex align-items-center">
                        <i class="fas fa-code text-info me-3"></i>
                        <div>
                            <strong>JSON</strong>
                            <p class="mb-0 text-muted small">{% trans "Structured data for APIs" %}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {% trans "Close" %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
function showDesignDemo() {
    const modal = new bootstrap.Modal(document.getElementById('designDemoModal'));
    modal.show();
}

function showExportOptions() {
    const modal = new bootstrap.Modal(document.getElementById('exportOptionsModal'));
    modal.show();
}

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}