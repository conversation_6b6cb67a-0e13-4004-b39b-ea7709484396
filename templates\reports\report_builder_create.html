{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Create Report Builder" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-plus me-2"></i>
                    {% trans "Create Report Builder" %}
                </h2>
                <a href="{% url 'reports:report_builder' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    {% trans "Back to Report Builder" %}
                </a>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        {% trans "Report Builder Information" %}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label">
                                        {{ form.name.label }}
                                        <span class="text-danger">*</span>
                                    </label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.name.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.name_ar.id_for_label }}" class="form-label">
                                        {{ form.name_ar.label }}
                                    </label>
                                    {{ form.name_ar }}
                                    {% if form.name_ar.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.name_ar.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                {{ form.description.label }}
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.description.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        {{ form.is_public }}
                                        <label class="form-check-label" for="{{ form.is_public.id_for_label }}">
                                            {{ form.is_public.label }}
                                        </label>
                                    </div>
                                    <small class="text-muted">{{ form.is_public.help_text }}</small>
                                    {% if form.is_public.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.is_public.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        {{ form.is_template }}
                                        <label class="form-check-label" for="{{ form.is_template.id_for_label }}">
                                            {{ form.is_template.label }}
                                        </label>
                                    </div>
                                    <small class="text-muted">{{ form.is_template.help_text }}</small>
                                    {% if form.is_template.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.is_template.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'reports:report_builder' %}" class="btn btn-secondary">
                                {% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                {% trans "Create Report Builder" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Help Card -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        {% trans "What is a Report Builder?" %}
                    </h6>
                </div>
                <div class="card-body">
                    <p class="mb-3">
                        {% trans "A Report Builder is a visual tool that allows you to create reports without writing SQL code. You can:" %}
                    </p>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            {% trans "Drag and drop fields from different data sources" %}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            {% trans "Apply filters and conditions visually" %}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            {% trans "Group and sort data easily" %}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            {% trans "Preview results before generating the final report" %}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            {% trans "Save and reuse report configurations" %}
                        </li>
                    </ul>
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>{% trans "Tip:" %}</strong>
                        {% trans "After creating the report builder, you'll be able to design your report using the visual interface." %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}