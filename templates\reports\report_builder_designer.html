{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Report Designer" %} - {{ report_builder.name }}{% endblock %}

{% block extra_css %}
<link href="{% static 'css/report-builder.css' %}" rel="stylesheet">
<link href="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.css" rel="stylesheet">
<style>
.designer-container {
    height: calc(100vh - 120px);
    overflow: hidden;
}

.designer-sidebar {
    height: 100%;
    overflow-y: auto;
    border-right: 1px solid #e9ecef;
    background: #f8f9fa;
}

.designer-main {
    height: 100%;
    overflow-y: auto;
}

.field-palette {
    padding: 15px;
}

.model-section {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 15px;
    overflow: hidden;
    background: white;
}

.model-header {
    background: #fff;
    padding: 12px 15px;
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
    display: flex;
    justify-content: between;
    align-items: center;
    font-weight: 500;
}

.model-header:hover {
    background: #f8f9fa;
}

.model-fields {
    padding: 10px;
    display: none;
    max-height: 300px;
    overflow-y: auto;
}

.model-fields.show {
    display: block;
}

.field-item {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 6px 10px;
    margin: 3px;
    cursor: move;
    display: block;
    font-size: 13px;
    transition: all 0.2s ease;
}

.field-item:hover {
    border-color: #007bff;
    background: #f0f8ff;
}

.field-item.dragging {
    opacity: 0.5;
    transform: rotate(2deg);
}

.drop-zone {
    min-height: 100px;
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    background-color: #fafafa;
    transition: all 0.3s ease;
    margin-bottom: 15px;
}

.drop-zone.drag-over {
    border-color: #007bff;
    background-color: #e3f2fd;
}

.drop-zone.has-items {
    border-style: solid;
    background-color: #fff;
    text-align: left;
}

.dropped-field {
    background: #e3f2fd;
    border: 1px solid #2196f3;
    border-radius: 4px;
    padding: 8px 12px;
    margin: 5px;
    display: inline-block;
    position: relative;
}

.dropped-field .remove-field {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.query-tabs {
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 20px;
}

.query-tabs .nav-link {
    border: none;
    border-bottom: 2px solid transparent;
    color: #6c757d;
    padding: 12px 20px;
}

.query-tabs .nav-link.active {
    border-bottom-color: #007bff;
    color: #007bff;
    background: none;
}

.filter-builder {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.filter-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    padding: 10px;
    background: white;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.sql-preview {
    background: #2d3748;
    color: #e2e8f0;
    border-radius: 8px;
    padding: 20px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    white-space: pre-wrap;
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 15px;
}

.results-table {
    max-height: 400px;
    overflow: auto;
    border: 1px solid #e9ecef;
    border-radius: 8px;
}

.btn-toolbar {
    margin-bottom: 20px;
}

.loading-spinner {
    display: none;
    text-align: center;
    padding: 20px;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 15px;
    display: none;
}

.success-message {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 15px;
    display: none;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid p-0">
    <!-- Header -->
    <div class="bg-white border-bottom px-4 py-3">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0">
                    <i class="fas fa-magic me-2"></i>
                    {{ report_builder.name }}
                </h4>
                <small class="text-muted">{% trans "Report Designer" %}</small>
            </div>
            <div class="btn-toolbar">
                <div class="btn-group me-2">
                    <button type="button" class="btn btn-outline-secondary" onclick="saveConfiguration()">
                        <i class="fas fa-save me-1"></i>
                        {% trans "Save" %}
                    </button>
                    <button type="button" class="btn btn-outline-primary" onclick="previewReport()">
                        <i class="fas fa-eye me-1"></i>
                        {% trans "Preview" %}
                    </button>
                    <button type="button" class="btn btn-primary" onclick="generateReport()">
                        <i class="fas fa-play me-1"></i>
                        {% trans "Generate" %}
                    </button>
                </div>
                <a href="{% url 'reports:report_builder' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    {% trans "Back" %}
                </a>
            </div>
        </div>
    </div>

    <div class="designer-container">
        <div class="row g-0 h-100">
            <!-- Sidebar -->
            <div class="col-md-3 designer-sidebar">
                <div class="field-palette">
                    <h6 class="mb-3">
                        <i class="fas fa-database me-2"></i>
                        {% trans "Data Sources" %}
                    </h6>
                    
                    {% for model_name, model_info in available_models.items %}
                    <div class="model-section">
                        <div class="model-header" onclick="toggleModelFields('{{ model_name }}')">
                            <span>
                                <i class="fas fa-table me-2"></i>
                                {{ model_info.verbose_name }}
                            </span>
                            <i class="fas fa-chevron-down toggle-icon" id="toggle-{{ model_name }}"></i>
                        </div>
                        <div class="model-fields" id="fields-{{ model_name }}">
                            {% for field_name, field_info in model_info.fields.items %}
                            <div class="field-item" 
                                 draggable="true"
                                 data-model="{{ model_name }}"
                                 data-field="{{ field_name }}"
                                 data-type="{{ field_info.type }}"
                                 data-verbose-name="{{ field_info.verbose_name }}">
                                <i class="fas fa-grip-vertical me-2"></i>
                                {{ field_info.verbose_name }}
                                <small class="text-muted">({{ field_info.type }})</small>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Main Designer Area -->
            <div class="col-md-9 designer-main">
                <div class="p-4">
                    <!-- Messages -->
                    <div class="error-message" id="errorMessage"></div>
                    <div class="success-message" id="successMessage"></div>
                    
                    <!-- Query Builder Tabs -->
                    <ul class="nav nav-tabs query-tabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#fields-tab">
                                <i class="fas fa-columns me-1"></i>
                                {% trans "Fields" %}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#filters-tab">
                                <i class="fas fa-filter me-1"></i>
                                {% trans "Filters" %}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#grouping-tab">
                                <i class="fas fa-layer-group me-1"></i>
                                {% trans "Grouping" %}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#sorting-tab">
                                <i class="fas fa-sort me-1"></i>
                                {% trans "Sorting" %}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#sql-tab">
                                <i class="fas fa-code me-1"></i>
                                {% trans "SQL" %}
                            </a>
                        </li>
                    </ul>

                    <div class="tab-content">
                        <!-- Fields Tab -->
                        <div class="tab-pane fade show active" id="fields-tab">
                            <h6>{% trans "Select Fields" %}</h6>
                            <p class="text-muted">{% trans "Drag fields from the left panel to include them in your report" %}</p>
                            
                            <div class="drop-zone" id="fieldsDropZone">
                                <i class="fas fa-mouse-pointer fa-2x text-muted mb-2"></i>
                                <p class="text-muted mb-0">{% trans "Drop fields here" %}</p>
                            </div>

                            <div class="mt-3">
                                <h6>{% trans "Aggregations" %}</h6>
                                <div class="row">
                                    {% for func in aggregation_functions %}
                                    <div class="col-md-2 mb-2">
                                        <button type="button" class="btn btn-outline-secondary btn-sm w-100" 
                                                onclick="addAggregation('{{ func }}')">
                                            {{ func }}
                                        </button>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>

                        <!-- Filters Tab -->
                        <div class="tab-pane fade" id="filters-tab">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6>{% trans "Filter Conditions" %}</h6>
                                <button type="button" class="btn btn-sm btn-primary" onclick="addFilter()">
                                    <i class="fas fa-plus me-1"></i>
                                    {% trans "Add Filter" %}
                                </button>
                            </div>
                            
                            <div class="filter-builder" id="filterBuilder">
                                <p class="text-muted text-center">
                                    {% trans "No filters added yet. Click 'Add Filter' to get started." %}
                                </p>
                            </div>
                        </div>

                        <!-- Grouping Tab -->
                        <div class="tab-pane fade" id="grouping-tab">
                            <h6>{% trans "Group By Fields" %}</h6>
                            <p class="text-muted">{% trans "Group your data by specific fields" %}</p>
                            
                            <div class="drop-zone" id="groupingDropZone">
                                <i class="fas fa-layer-group fa-2x text-muted mb-2"></i>
                                <p class="text-muted mb-0">{% trans "Drop fields here to group by" %}</p>
                            </div>
                        </div>

                        <!-- Sorting Tab -->
                        <div class="tab-pane fade" id="sorting-tab">
                            <h6>{% trans "Sort Order" %}</h6>
                            <p class="text-muted">{% trans "Define how your data should be sorted" %}</p>
                            
                            <div class="drop-zone" id="sortingDropZone">
                                <i class="fas fa-sort fa-2x text-muted mb-2"></i>
                                <p class="text-muted mb-0">{% trans "Drop fields here to sort by" %}</p>
                            </div>
                        </div>

                        <!-- SQL Tab -->
                        <div class="tab-pane fade" id="sql-tab">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6>{% trans "Generated SQL Query" %}</h6>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-secondary" onclick="validateQuery()">
                                        <i class="fas fa-check me-1"></i>
                                        {% trans "Validate" %}
                                    </button>
                                    <button type="button" class="btn btn-outline-primary" onclick="executeQuery()">
                                        <i class="fas fa-play me-1"></i>
                                        {% trans "Execute" %}
                                    </button>
                                </div>
                            </div>
                            
                            <div class="sql-preview" id="sqlPreview">
                                {% trans "SQL query will appear here as you build your report..." %}
                            </div>

                            <div class="loading-spinner" id="loadingSpinner">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">{% trans "Loading..." %}</span>
                                </div>
                                <p class="mt-2">{% trans "Executing query..." %}</p>
                            </div>

                            <div id="queryResults" style="display: none;">
                                <h6>{% trans "Query Results" %}</h6>
                                <div class="results-table">
                                    <table class="table table-sm table-striped" id="resultsTable">
                                        <thead></thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                                <div class="mt-2">
                                    <small class="text-muted" id="resultsInfo"></small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Configuration Data -->
<script type="application/json" id="reportBuilderConfig">
{{ report_builder.report_config|default:"{}" }}
</script>

<script type="application/json" id="queryConfig">
{{ report_builder.query_config|default:"{}" }}
</script>

<script type="application/json" id="availableModels">
{{ available_models|safe }}
</script>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script src="{% static 'js/report-builder.js' %}"></script>
<script>
// Initialize the report builder
document.addEventListener('DOMContentLoaded', function() {
    initializeReportBuilder();
});

function toggleModelFields(modelName) {
    const fieldsDiv = document.getElementById(`fields-${modelName}`);
    const toggleIcon = document.getElementById(`toggle-${modelName}`);
    
    if (fieldsDiv.classList.contains('show')) {
        fieldsDiv.classList.remove('show');
        toggleIcon.classList.remove('fa-chevron-up');
        toggleIcon.classList.add('fa-chevron-down');
    } else {
        fieldsDiv.classList.add('show');
        toggleIcon.classList.remove('fa-chevron-down');
        toggleIcon.classList.add('fa-chevron-up');
    }
}

function showMessage(message, type = 'success') {
    const messageDiv = document.getElementById(type === 'error' ? 'errorMessage' : 'successMessage');
    messageDiv.textContent = message;
    messageDiv.style.display = 'block';
    
    setTimeout(() => {
        messageDiv.style.display = 'none';
    }, 5000);
}

function saveConfiguration() {
    const config = getReportBuilderConfiguration();
    
    fetch(window.location.href, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('{% trans "Configuration saved successfully" %}', 'success');
        } else {
            showMessage(data.error || '{% trans "Failed to save configuration" %}', 'error');
        }
    })
    .catch(error => {
        showMessage('{% trans "An error occurred while saving" %}', 'error');
        console.error('Error:', error);
    });
}

function previewReport() {
    const config = getReportBuilderConfiguration();
    
    // Validate configuration first
    if (!config.query_config.fields || config.query_config.fields.length === 0) {
        showMessage('{% trans "Please add at least one field to preview the report" %}', 'error');
        return;
    }
    
    executeQuery();
}

function generateReport() {
    const config = getReportBuilderConfiguration();
    
    // Save configuration and redirect to report generation
    fetch(window.location.href, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Redirect to report template creation with this builder
            window.location.href = '{% url "reports:report_template_create" %}?builder={{ report_builder.pk }}';
        } else {
            showMessage(data.error || '{% trans "Failed to save configuration" %}', 'error');
        }
    })
    .catch(error => {
        showMessage('{% trans "An error occurred" %}', 'error');
        console.error('Error:', error);
    });
}
</script>
{% endblock %}