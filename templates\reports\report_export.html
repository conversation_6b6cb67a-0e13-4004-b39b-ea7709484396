{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Export Report" %} - {{ execution.template.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-download me-2"></i>
                    {% trans "Export Report" %}
                </h2>
                <a href="{% url 'reports:report_view' execution.id %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    {% trans "Back to Report" %}
                </a>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <!-- Report Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        {% trans "Report Information" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>{% trans "Report Name:" %}</strong> {{ execution.template.name }}</p>
                            <p><strong>{% trans "Report Type:" %}</strong> {{ execution.template.get_report_type_display }}</p>
                            <p><strong>{% trans "Generated On:" %}</strong> {{ execution.created_at|date:"F d, Y H:i" }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>{% trans "Generated By:" %}</strong> {{ execution.executed_by.get_full_name|default:execution.executed_by.username }}</p>
                            <p><strong>{% trans "Total Records:" %}</strong> {{ execution.row_count|default:"N/A" }}</p>
                            <p><strong>{% trans "Execution Time:" %}</strong> {{ execution.execution_time|default:"N/A" }}</p>
                        </div>
                    </div>
                    {% if execution.template.description %}
                    <div class="mt-3">
                        <strong>{% trans "Description:" %}</strong>
                        <p class="text-muted">{{ execution.template.description }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Export Options -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-export me-2"></i>
                        {% trans "Export Options" %}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-4">
                            <label class="form-label">{% trans "Select Export Format" %}</label>
                            <div class="row">
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card export-option">
                                        <div class="card-body text-center">
                                            <input type="radio" name="format" value="pdf" id="format_pdf" checked class="form-check-input">
                                            <label for="format_pdf" class="form-check-label w-100">
                                                <i class="fas fa-file-pdf fa-3x text-danger mb-2"></i>
                                                <h6>PDF</h6>
                                                <small class="text-muted">{% trans "Professional formatted document" %}</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card export-option">
                                        <div class="card-body text-center">
                                            <input type="radio" name="format" value="excel" id="format_excel" class="form-check-input">
                                            <label for="format_excel" class="form-check-label w-100">
                                                <i class="fas fa-file-excel fa-3x text-success mb-2"></i>
                                                <h6>Excel</h6>
                                                <small class="text-muted">{% trans "Spreadsheet for analysis" %}</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card export-option">
                                        <div class="card-body text-center">
                                            <input type="radio" name="format" value="csv" id="format_csv" class="form-check-input">
                                            <label for="format_csv" class="form-check-label w-100">
                                                <i class="fas fa-file-csv fa-3x text-primary mb-2"></i>
                                                <h6>CSV</h6>
                                                <small class="text-muted">{% trans "Raw data for import/export" %}</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card export-option">
                                        <div class="card-body text-center">
                                            <input type="radio" name="format" value="html" id="format_html" class="form-check-input">
                                            <label for="format_html" class="form-check-label w-100">
                                                <i class="fas fa-file-code fa-3x text-warning mb-2"></i>
                                                <h6>HTML</h6>
                                                <small class="text-muted">{% trans "Web page format" %}</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card export-option">
                                        <div class="card-body text-center">
                                            <input type="radio" name="format" value="json" id="format_json" class="form-check-input">
                                            <label for="format_json" class="form-check-label w-100">
                                                <i class="fas fa-code fa-3x text-info mb-2"></i>
                                                <h6>JSON</h6>
                                                <small class="text-muted">{% trans "Structured data for APIs" %}</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card export-option">
                                        <div class="card-body text-center">
                                            <input type="radio" name="format" value="xml" id="format_xml" class="form-check-input">
                                            <label for="format_xml" class="form-check-label w-100">
                                                <i class="fas fa-file-code fa-3x text-secondary mb-2"></i>
                                                <h6>XML</h6>
                                                <small class="text-muted">{% trans "Structured markup format" %}</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'reports:report_view' execution.id %}" class="btn btn-secondary">
                                {% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-download me-1"></i>
                                {% trans "Export Report" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.export-option {
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.export-option:hover {
    border-color: #007bff;
    box-shadow: 0 4px 8px rgba(0,123,255,0.2);
}

.export-option input[type="radio"] {
    display: none;
}

.export-option input[type="radio"]:checked + label {
    color: #007bff;
}

.export-option input[type="radio"]:checked ~ .card {
    border-color: #007bff;
    background-color: #f0f8ff;
}

.form-check-label {
    cursor: pointer;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle export option selection
    const exportOptions = document.querySelectorAll('.export-option');
    const radioInputs = document.querySelectorAll('input[name="format"]');
    
    exportOptions.forEach(option => {
        option.addEventListener('click', function() {
            const radio = this.querySelector('input[type="radio"]');
            radio.checked = true;
            
            // Update visual state
            exportOptions.forEach(opt => opt.classList.remove('selected'));
            this.classList.add('selected');
        });
    });
    
    // Set initial state
    const checkedRadio = document.querySelector('input[name="format"]:checked');
    if (checkedRadio) {
        checkedRadio.closest('.export-option').classList.add('selected');
    }
});
</script>
{% endblock %}