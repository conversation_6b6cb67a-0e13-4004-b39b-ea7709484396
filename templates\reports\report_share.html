{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Share Report" %} - {{ execution.template.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-share-alt me-2"></i>
                    {% trans "Share Report" %}
                </h2>
                <a href="{% url 'reports:report_view' execution.id %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    {% trans "Back to Report" %}
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <!-- Report Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        {% trans "Report Information" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>{% trans "Report Name:" %}</strong> {{ execution.template.name }}</p>
                            <p><strong>{% trans "Report Type:" %}</strong> {{ execution.template.get_report_type_display }}</p>
                            <p><strong>{% trans "Generated On:" %}</strong> {{ execution.created_at|date:"F d, Y H:i" }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>{% trans "Generated By:" %}</strong> {{ execution.executed_by.get_full_name|default:execution.executed_by.username }}</p>
                            <p><strong>{% trans "Total Records:" %}</strong> {{ execution.row_count|default:"N/A" }}</p>
                            <p><strong>{% trans "Execution Time:" %}</strong> {{ execution.execution_time|default:"N/A" }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Create New Share -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-plus me-2"></i>
                        {% trans "Create Share Link" %}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="share_type" class="form-label">{% trans "Share Type" %}</label>
                                    <select name="share_type" id="share_type" class="form-select">
                                        <option value="private_link">{% trans "Private Link" %}</option>
                                        <option value="public_link">{% trans "Public Link" %}</option>
                                    </select>
                                    <small class="text-muted">{% trans "Private links require the exact URL to access" %}</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="expires_days" class="form-label">{% trans "Expires In (Days)" %}</label>
                                    <select name="expires_days" id="expires_days" class="form-select">
                                        <option value="1">1 {% trans "day" %}</option>
                                        <option value="3">3 {% trans "days" %}</option>
                                        <option value="7" selected>7 {% trans "days" %}</option>
                                        <option value="14">14 {% trans "days" %}</option>
                                        <option value="30">30 {% trans "days" %}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="max_access_count" class="form-label">{% trans "Maximum Downloads" %}</label>
                                    <input type="number" name="max_access_count" id="max_access_count" 
                                           class="form-control" min="1" max="100" placeholder="{% trans 'Unlimited' %}">
                                    <small class="text-muted">{% trans "Leave empty for unlimited downloads" %}</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <input type="checkbox" name="send_email" id="send_email" class="form-check-input">
                                        <label for="send_email" class="form-check-label">
                                            {% trans "Send via Email" %}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3" id="email_section" style="display: none;">
                            <label for="email_recipients" class="form-label">{% trans "Email Recipients" %}</label>
                            <textarea name="email_recipients" id="email_recipients" class="form-control" rows="3"
                                      placeholder="{% trans 'Enter email addresses, one per line' %}"></textarea>
                            <small class="text-muted">{% trans "Enter one email address per line" %}</small>
                        </div>
                        
                        <div class="d-flex justify-content-end gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-share me-1"></i>
                                {% trans "Create Share Link" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Existing Shares -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-link me-2"></i>
                        {% trans "Existing Shares" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if existing_shares %}
                        {% for share in existing_shares %}
                        <div class="share-item mb-3 p-3 border rounded">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <span class="badge bg-{{ share.share_type|yesno:'primary,success' }}">
                                    {{ share.get_share_type_display }}
                                </span>
                                <small class="text-muted">
                                    {{ share.created_at|date:"M d, Y" }}
                                </small>
                            </div>
                            
                            <div class="mb-2">
                                <small class="text-muted">{% trans "Token:" %}</small>
                                <code class="small">{{ share.share_token|truncatechars:20 }}...</code>
                            </div>
                            
                            <div class="row text-center">
                                <div class="col-4">
                                    <small class="text-muted d-block">{% trans "Downloads" %}</small>
                                    <strong>{{ share.access_count }}</strong>
                                    {% if share.max_access_count %}
                                        <small class="text-muted">/ {{ share.max_access_count }}</small>
                                    {% endif %}
                                </div>
                                <div class="col-4">
                                    <small class="text-muted d-block">{% trans "Expires" %}</small>
                                    {% if share.expires_at %}
                                        <strong class="{% if share.expires_at < now %}text-danger{% endif %}">
                                            {{ share.expires_at|date:"M d" }}
                                        </strong>
                                    {% else %}
                                        <strong class="text-success">{% trans "Never" %}</strong>
                                    {% endif %}
                                </div>
                                <div class="col-4">
                                    <small class="text-muted d-block">{% trans "Status" %}</small>
                                    {% if share.is_active and share.expires_at > now %}
                                        <span class="badge bg-success">{% trans "Active" %}</span>
                                    {% else %}
                                        <span class="badge bg-danger">{% trans "Expired" %}</span>
                                    {% endif %}
                                </div>
                            </div>
                            
                            {% if share.is_active and share.expires_at > now %}
                            <div class="mt-2">
                                <div class="input-group input-group-sm">
                                    <input type="text" class="form-control" 
                                           value="{{ request.build_absolute_uri }}{% url 'reports:shared_report_download' share.share_token %}"
                                           readonly>
                                    <button class="btn btn-outline-secondary" type="button" 
                                            onclick="copyToClipboard(this)">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-link fa-3x text-muted mb-3"></i>
                            <p class="text-muted">{% trans "No shares created yet" %}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle email section
    const sendEmailCheckbox = document.getElementById('send_email');
    const emailSection = document.getElementById('email_section');
    
    sendEmailCheckbox.addEventListener('change', function() {
        emailSection.style.display = this.checked ? 'block' : 'none';
    });
});

function copyToClipboard(button) {
    const input = button.parentElement.querySelector('input');
    input.select();
    input.setSelectionRange(0, 99999); // For mobile devices
    
    try {
        document.execCommand('copy');
        
        // Show feedback
        const originalIcon = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check text-success"></i>';
        
        setTimeout(() => {
            button.innerHTML = originalIcon;
        }, 2000);
        
        // Show toast notification if available
        if (typeof showToast === 'function') {
            showToast('Link copied to clipboard!', 'success');
        }
    } catch (err) {
        console.error('Failed to copy: ', err);
        if (typeof showToast === 'function') {
            showToast('Failed to copy link', 'error');
        }
    }
}
</script>
{% endblock %}