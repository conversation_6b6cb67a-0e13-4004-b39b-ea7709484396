{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Second Language" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 col-xl-2">
            {% include 'students/includes/student_nav.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <h2>
                        <i class="fas fa-language text-primary me-2"></i>{% trans "Second Language Management" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage student second language selections and assignments" %}</p>
                </div>
            </div>

            <!-- Language Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <h3 class="mb-1">245</h3>
                            <p class="mb-0">{% trans "Total Students" %}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-check-circle fa-2x mb-2"></i>
                            <h3 class="mb-1">198</h3>
                            <p class="mb-0">{% trans "Assigned" %}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-dark">
                        <div class="card-body text-center">
                            <i class="fas fa-clock fa-2x mb-2"></i>
                            <h3 class="mb-1">47</h3>
                            <p class="mb-0">{% trans "Pending" %}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-globe fa-2x mb-2"></i>
                            <h3 class="mb-1">4</h3>
                            <p class="mb-0">{% trans "Languages" %}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Language Distribution -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">{% trans "Language Distribution" %}</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <div>
                                            <i class="fas fa-flag me-2 text-danger"></i>{% trans "French" %}
                                        </div>
                                        <div>
                                            <span class="badge bg-danger">89 {% trans "students" %}</span>
                                        </div>
                                    </div>
                                    <div class="progress mb-3">
                                        <div class="progress-bar bg-danger" style="width: 45%"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <div>
                                            <i class="fas fa-flag me-2 text-warning"></i>{% trans "Spanish" %}
                                        </div>
                                        <div>
                                            <span class="badge bg-warning">67 {% trans "students" %}</span>
                                        </div>
                                    </div>
                                    <div class="progress mb-3">
                                        <div class="progress-bar bg-warning" style="width: 34%"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <div>
                                            <i class="fas fa-flag me-2 text-success"></i>{% trans "German" %}
                                        </div>
                                        <div>
                                            <span class="badge bg-success">42 {% trans "students" %}</span>
                                        </div>
                                    </div>
                                    <div class="progress mb-3">
                                        <div class="progress-bar bg-success" style="width: 21%"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <div>
                                            <i class="fas fa-flag me-2 text-info"></i>{% trans "Italian" %}
                                        </div>
                                        <div>
                                            <span class="badge bg-info">47 {% trans "students" %}</span>
                                        </div>
                                    </div>
                                    <div class="progress mb-3">
                                        <div class="progress-bar bg-info" style="width: 24%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">{% trans "Quick Assignment" %}</h5>
                        </div>
                        <div class="card-body">
                            <form method="post">
                                {% csrf_token %}
                                <div class="mb-3">
                                    <label for="student" class="form-label">{% trans "Student" %}</label>
                                    <select class="form-select" id="student" name="student_id" required>
                                        <option value="">{% trans "Select student..." %}</option>
                                        <!-- Add student options here -->
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="language" class="form-label">{% trans "Second Language" %}</label>
                                    <select class="form-select" id="language" name="language" required>
                                        <option value="">{% trans "Select language..." %}</option>
                                        <option value="french">{% trans "French" %}</option>
                                        <option value="spanish">{% trans "Spanish" %}</option>
                                        <option value="german">{% trans "German" %}</option>
                                        <option value="italian">{% trans "Italian" %}</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-plus me-2"></i>{% trans "Assign Language" %}
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Students List -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">{% trans "Student Language Assignments" %}</h5>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="fas fa-filter me-2"></i>{% trans "Filter by Language" %}
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="?language=all">{% trans "All Languages" %}</a></li>
                                        <li><a class="dropdown-item" href="?language=french">{% trans "French" %}</a></li>
                                        <li><a class="dropdown-item" href="?language=spanish">{% trans "Spanish" %}</a></li>
                                        <li><a class="dropdown-item" href="?language=german">{% trans "German" %}</a></li>
                                        <li><a class="dropdown-item" href="?language=italian">{% trans "Italian" %}</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="?language=unassigned">{% trans "Unassigned" %}</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>{% trans "Student" %}</th>
                                            <th>{% trans "Class" %}</th>
                                            <th>{% trans "Second Language" %}</th>
                                            <th>{% trans "Level" %}</th>
                                            <th>{% trans "Teacher" %}</th>
                                            <th>{% trans "Status" %}</th>
                                            <th>{% trans "Actions" %}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Sample data - replace with actual data -->
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                                        <span class="text-white fw-bold">AM</span>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0">Ahmed Mohamed</h6>
                                                        <small class="text-muted">ID: ST001</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ student.current_class|default:"Grade 10-A" }}</td>
                                            <td>
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-flag me-1"></i>French
                                                </span>
                                            </td>
                                            <td>Beginner</td>
                                            <td>Ms. Claire Dubois</td>
                                            <td>
                                                <span class="badge bg-success">{% trans "Active" %}</span>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="bg-info rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                                        <span class="text-white fw-bold">FH</span>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0">Fatima Hassan</h6>
                                                        <small class="text-muted">ID: ST002</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>Grade 9-B</td>
                                            <td>
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-flag me-1"></i>Spanish
                                                </span>
                                            </td>
                                            <td>Intermediate</td>
                                            <td>Mr. Carlos Rodriguez</td>
                                            <td>
                                                <span class="badge bg-success">{% trans "Active" %}</span>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="bg-warning rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                                        <span class="text-white fw-bold">OK</span>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0">Omar Khalid</h6>
                                                        <small class="text-muted">ID: ST003</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ student.current_class|default:"Grade 11-A" }}</td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-clock me-1"></i>{% trans "Unassigned" %}
                                                </span>
                                            </td>
                                            <td>-</td>
                                            <td>-</td>
                                            <td>
                                                <span class="badge bg-warning">{% trans "Pending" %}</span>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#assignModal">
                                                    <i class="fas fa-plus"></i> {% trans "Assign" %}
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Assignment Modal -->
<div class="modal fade" id="assignModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Assign Second Language" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="modalLanguage" class="form-label">{% trans "Select Language" %}</label>
                        <select class="form-select" id="modalLanguage" required>
                            <option value="">{% trans "Choose language..." %}</option>
                            <option value="french">{% trans "French" %}</option>
                            <option value="spanish">{% trans "Spanish" %}</option>
                            <option value="german">{% trans "German" %}</option>
                            <option value="italian">{% trans "Italian" %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="modalLevel" class="form-label">{% trans "Starting Level" %}</label>
                        <select class="form-select" id="modalLevel" required>
                            <option value="">{% trans "Choose level..." %}</option>
                            <option value="beginner">{% trans "Beginner" %}</option>
                            <option value="intermediate">{% trans "Intermediate" %}</option>
                            <option value="advanced">{% trans "Advanced" %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="modalTeacher" class="form-label">{% trans "Teacher" %}</label>
                        <select class="form-select" id="modalTeacher" required>
                            <option value="">{% trans "Choose teacher..." %}</option>
                            <option value="1">Ms. Claire Dubois (French)</option>
                            <option value="2">Mr. Carlos Rodriguez (Spanish)</option>
                            <option value="3">Dr. Hans Mueller (German)</option>
                            <option value="4">Prof. Marco Rossi (Italian)</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary">{% trans "Assign Language" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}