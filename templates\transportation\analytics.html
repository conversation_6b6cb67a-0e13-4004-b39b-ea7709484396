{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Transportation Analytics" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">{% trans "Transportation Analytics" %}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'accounts:dashboard' %}">{% trans "Dashboard" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'transportation:dashboard' %}">{% trans "Transportation" %}</a></li>
                        <li class="breadcrumb-item active">{% trans "Analytics" %}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics Cards -->
    <div class="row">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-bus text-primary mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-primary">{{ total_vehicles|default:0 }}</h4>
                    <p class="mb-0">{% trans "Total Vehicles" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-route text-success mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-success">{{ total_routes|default:0 }}</h4>
                    <p class="mb-0">{% trans "Active Routes" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-users text-info mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-info">{{ total_students|default:0 }}</h4>
                    <p class="mb-0">{% trans "Students Using Transport" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-percentage text-warning mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-warning">{{ attendance_rate|default:0 }}%</h4>
                    <p class="mb-0">{% trans "Attendance Rate" %}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">{% trans "Route Utilization" %}</h5>
                </div>
                <div class="card-body">
                    <canvas id="routeUtilizationChart"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">{% trans "Monthly Attendance Trends" %}</h5>
                </div>
                <div class="card-body">
                    <canvas id="attendanceTrendChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Route Utilization Chart
const routeCtx = document.getElementById('routeUtilizationChart').getContext('2d');
new Chart(routeCtx, {
    type: 'doughnut',
    data: {
        labels: [{% for route in routes %}'{{ route.name }}'{% if not forloop.last %}, {% endif %}{% endfor %}],
        datasets: [{
            data: [{% for route in routes %}{{ route.student_count|default:0 }}{% if not forloop.last %}, {% endif %}{% endfor %}],
            backgroundColor: ['#007bff', '#28a745', '#ffc107', '#dc3545', '#6f42c1']
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Attendance Trend Chart
const attendanceCtx = document.getElementById('attendanceTrendChart').getContext('2d');
new Chart(attendanceCtx, {
    type: 'line',
    data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
            label: '{% trans "Attendance Rate" %}',
            data: [85, 88, 92, 89, 94, 91],
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                max: 100
            }
        }
    }
});
</script>
{% endblock %}