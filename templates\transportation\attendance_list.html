{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Transportation Attendance" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">{% trans "Transportation Attendance" %}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'accounts:dashboard' %}">{% trans "Dashboard" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'transportation:dashboard' %}">{% trans "Transportation" %}</a></li>
                        <li class="breadcrumb-item active">{% trans "Attendance" %}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clipboard-check me-2"></i>
                        {% trans "Student Transportation Attendance" %}
                    </h5>
                    <div class="btn-group">
                        <a href="{% url 'transportation:attendance_record' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>{% trans "Record Attendance" %}
                        </a>
                        <a href="{% url 'transportation:attendance_bulk' %}" class="btn btn-success">
                            <i class="fas fa-users me-1"></i>{% trans "Bulk Attendance" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Date" %}</th>
                                    <th>{% trans "Route" %}</th>
                                    <th>{% trans "Vehicle" %}</th>
                                    <th>{% trans "Students Present" %}</th>
                                    <th>{% trans "Students Absent" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for attendance in attendance_records %}
                                <tr>
                                    <td>{{ attendance.date }}</td>
                                    <td>{{ attendance.route.name }}</td>
                                    <td>{{ attendance.vehicle.vehicle_number }}</td>
                                    <td><span class="badge bg-success">{{ attendance.present_count }}</span></td>
                                    <td><span class="badge bg-danger">{{ attendance.absent_count }}</span></td>
                                    <td>
                                        <a href="#" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center">{% trans "No attendance records found" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}