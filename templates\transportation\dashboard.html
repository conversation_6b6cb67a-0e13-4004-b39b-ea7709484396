{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Transportation Dashboard" %} - {% trans "School ERP" %}{% endblock %}

{% block extra_css %}
<style>
.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.alert-card {
    border-left: 4px solid #dc3545;
    background: #fff5f5;
}

.efficiency-card {
    border-left: 4px solid #28a745;
    background: #f8fff9;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">
                    <i class="fas fa-bus me-2"></i>
                    {% trans "Transportation Dashboard" %}
                </h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'accounts:dashboard' %}">{% trans "Dashboard" %}</a></li>
                        <li class="breadcrumb-item active">{% trans "Transportation" %}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="stat-card">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="stat-number">{{ total_vehicles|default:0 }}</div>
                        <div class="stat-label">{% trans "Total Vehicles" %}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-bus fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stat-card">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="stat-number">{{ active_routes|default:0 }}</div>
                        <div class="stat-label">{% trans "Active Routes" %}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-route fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stat-card">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="stat-number">{{ total_drivers|default:0 }}</div>
                        <div class="stat-label">{% trans "Total Drivers" %}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-tie fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stat-card">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="stat-number">{{ total_students|default:0 }}</div>
                        <div class="stat-label">{% trans "Students Using Transport" %}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        {% trans "Quick Actions" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2">
                            <a href="{% url 'transportation:vehicle_create' %}" class="btn btn-primary btn-block mb-2">
                                <i class="fas fa-plus me-1"></i>
                                {% trans "Add Vehicle" %}
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{% url 'transportation:route_create' %}" class="btn btn-success btn-block mb-2">
                                <i class="fas fa-plus me-1"></i>
                                {% trans "Create Route" %}
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{% url 'transportation:driver_create' %}" class="btn btn-info btn-block mb-2">
                                <i class="fas fa-plus me-1"></i>
                                {% trans "Add Driver" %}
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{% url 'transportation:stop_create' %}" class="btn btn-warning btn-block mb-2">
                                <i class="fas fa-plus me-1"></i>
                                {% trans "Add Bus Stop" %}
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{% url 'transportation:student_assign' %}" class="btn btn-secondary btn-block mb-2">
                                <i class="fas fa-user-plus me-1"></i>
                                {% trans "Assign Student" %}
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{% url 'transportation:analytics' %}" class="btn btn-dark btn-block mb-2">
                                <i class="fas fa-chart-bar me-1"></i>
                                {% trans "Analytics" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts and Notifications -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card alert-card">
                <div class="card-header">
                    <h5 class="card-title mb-0 text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        {% trans "Maintenance Due" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if maintenance_due %}
                        <div class="list-group list-group-flush">
                            {% for vehicle in maintenance_due %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ vehicle.vehicle_number }}</h6>
                                    <small class="text-muted">{{ vehicle.license_plate }} - {{ vehicle.make }} {{ vehicle.model }}</small>
                                </div>
                                <span class="badge bg-danger">{{ vehicle.next_maintenance_date|date:"M d" }}</span>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <p class="text-muted mb-0">{% trans "No vehicles require maintenance" %}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card efficiency-card">
                <div class="card-header">
                    <h5 class="card-title mb-0 text-success">
                        <i class="fas fa-chart-line me-2"></i>
                        {% trans "Route Efficiency" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if route_efficiency %}
                        <div class="list-group list-group-flush">
                            {% for route in route_efficiency|slice:":5" %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ route.name }}</h6>
                                    <small class="text-muted">{{ route.current_occupancy }}/{{ route.max_capacity }} students</small>
                                </div>
                                <div class="text-end">
                                    <div class="progress" style="width: 100px; height: 8px;">
                                        <div class="progress-bar bg-success" style="width: {{ route.occupancy_percentage }}%"></div>
                                    </div>
                                    <small class="text-muted">{{ route.occupancy_percentage|floatformat:0 }}%</small>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-route fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">{% trans "No active routes found" %}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>
                        {% trans "Recent Route Optimizations" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_optimizations %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>{% trans "Route" %}</th>
                                        <th>{% trans "Type" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Savings" %}</th>
                                        <th>{% trans "Date" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for optimization in recent_optimizations %}
                                    <tr>
                                        <td>{{ optimization.route.name }}</td>
                                        <td>{{ optimization.get_optimization_type_display }}</td>
                                        <td>
                                            {% if optimization.status == 'completed' %}
                                                <span class="badge bg-success">{% trans "Completed" %}</span>
                                            {% elif optimization.status == 'processing' %}
                                                <span class="badge bg-warning">{% trans "Processing" %}</span>
                                            {% elif optimization.status == 'failed' %}
                                                <span class="badge bg-danger">{% trans "Failed" %}</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{% trans "Pending" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if optimization.fuel_savings_percentage %}
                                                {{ optimization.fuel_savings_percentage }}% fuel
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>{{ optimization.created_at|date:"M d, Y" }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-cogs fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">{% trans "No recent optimizations" %}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-refresh dashboard every 5 minutes
    setInterval(function() {
        location.reload();
    }, 300000);
});
</script>
{% endblock %}