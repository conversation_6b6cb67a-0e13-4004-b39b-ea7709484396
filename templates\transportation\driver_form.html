{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load widget_tweaks %}

{% block title %}
    {% if object %}
        {% trans "Edit Driver" %} - {{ object.full_name }}
    {% else %}
        {% trans "Add Driver" %}
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">
                    {% if object %}
                        {% trans "Edit Driver" %} - {{ object.full_name }}
                    {% else %}
                        {% trans "Add Driver" %}
                    {% endif %}
                </h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'accounts:dashboard' %}">{% trans "Dashboard" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'transportation:dashboard' %}">{% trans "Transportation" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'transportation:driver_list' %}">{% trans "Drivers" %}</a></li>
                        <li class="breadcrumb-item active">
                            {% if object %}{% trans "Edit" %}{% else %}{% trans "Add" %}{% endif %}
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-tie me-2"></i>
                        {% if object %}
                            {% trans "Edit Driver" %} - {{ object.full_name }}
                        {% else %}
                            {% trans "Add New Driver" %}
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <!-- Employee Information -->
                        <h6 class="mb-3 text-primary">
                            <i class="fas fa-user me-1"></i>
                            {% trans "Employee Information" %}
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="employee" class="form-label">
                                        {% trans "Employee" %}
                                        <span class="text-danger">*</span>
                                    </label>
                                    <select name="employee" id="employee" class="form-select" required>
                                        <option value="">{% trans "Select Employee" %}</option>
                                        <!-- Employee options would be populated by the view -->
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">
                                        {% trans "Status" %}
                                    </label>
                                    <select name="status" id="status" class="form-select">
                                        <option value="active" selected>{% trans "Active" %}</option>
                                        <option value="inactive">{% trans "Inactive" %}</option>
                                        <option value="suspended">{% trans "Suspended" %}</option>
                                        <option value="terminated">{% trans "Terminated" %}</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- License Information -->
                        <h6 class="mb-3 text-primary">
                            <i class="fas fa-id-card me-1"></i>
                            {% trans "License Information" %}
                        </h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="license_number" class="form-label">
                                        {% trans "License Number" %}
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" name="license_number" id="license_number" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="license_type" class="form-label">
                                        {% trans "License Type" %}
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" name="license_type" id="license_type" class="form-control" 
                                           placeholder="{% trans 'e.g., CDL, Class B' %}" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="license_expiry" class="form-label">
                                        {% trans "License Expiry Date" %}
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input type="date" name="license_expiry" id="license_expiry" class="form-control" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="medical_certificate_expiry" class="form-label">
                                        {% trans "Medical Certificate Expiry" %}
                                    </label>
                                    <input type="date" name="medical_certificate_expiry" id="medical_certificate_expiry" class="form-control">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="experience_years" class="form-label">
                                        {% trans "Years of Experience" %}
                                    </label>
                                    <input type="number" name="experience_years" id="experience_years" class="form-control" min="0" value="0">
                                </div>
                            </div>
                        </div>

                        <!-- Emergency Contact -->
                        <h6 class="mb-3 text-primary">
                            <i class="fas fa-phone me-1"></i>
                            {% trans "Emergency Contact" %}
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="emergency_contact_name" class="form-label">
                                        {% trans "Emergency Contact Name" %}
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" name="emergency_contact_name" id="emergency_contact_name" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="emergency_contact_phone" class="form-label">
                                        {% trans "Emergency Contact Phone" %}
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input type="tel" name="emergency_contact_phone" id="emergency_contact_phone" 
                                           class="form-control" placeholder="+1234567890" required>
                                </div>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="notes" class="form-label">
                                        {% trans "Notes" %}
                                    </label>
                                    <textarea name="notes" id="notes" class="form-control" rows="3" 
                                              placeholder="{% trans 'Additional notes about the driver' %}"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{% url 'transportation:driver_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>
                                        {% trans "Cancel" %}
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        {% if object %}
                                            {% trans "Update Driver" %}
                                        {% else %}
                                            {% trans "Add Driver" %}
                                        {% endif %}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}