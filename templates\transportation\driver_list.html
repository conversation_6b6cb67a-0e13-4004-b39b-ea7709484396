{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Drivers" %} - {% trans "Transportation" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">{% trans "Transportation Drivers" %}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'accounts:dashboard' %}">{% trans "Dashboard" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'transportation:dashboard' %}">{% trans "Transportation" %}</a></li>
                        <li class="breadcrumb-item active">{% trans "Drivers" %}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <form method="get" class="row g-3">
                                <div class="col-md-4">
                                    <input type="text" name="search" class="form-control" 
                                           placeholder="{% trans 'Search drivers...' %}" 
                                           value="{{ request.GET.search }}">
                                </div>
                                <div class="col-md-3">
                                    <select name="status" class="form-select">
                                        <option value="">{% trans "All Status" %}</option>
                                        <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>{% trans "Active" %}</option>
                                        <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>{% trans "Inactive" %}</option>
                                        <option value="suspended" {% if request.GET.status == 'suspended' %}selected{% endif %}>{% trans "Suspended" %}</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> {% trans "Filter" %}
                                    </button>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{% url 'transportation:driver_create' %}" class="btn btn-success">
                                <i class="fas fa-plus me-1"></i>
                                {% trans "Add Driver" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Drivers List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-tie me-2"></i>
                        {% trans "Transportation Drivers" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if drivers %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>{% trans "Driver" %}</th>
                                        <th>{% trans "License" %}</th>
                                        <th>{% trans "Experience" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "License Expiry" %}</th>
                                        <th>{% trans "Emergency Contact" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for driver in drivers %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-2">
                                                    <div class="avatar-title bg-primary rounded-circle">
                                                        {{ driver.employee.user.first_name|first }}{{ driver.employee.user.last_name|first }}
                                                    </div>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">{{ driver.full_name }}</h6>
                                                    <small class="text-muted">{{ driver.employee.employee_id }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{{ driver.license_number }}</strong>
                                                <br>
                                                <small class="text-muted">{{ driver.license_type }}</small>
                                            </div>
                                        </td>
                                        <td>{{ driver.experience_years }} {% trans "years" %}</td>
                                        <td>
                                            {% if driver.status == 'active' %}
                                                <span class="badge bg-success">{% trans "Active" %}</span>
                                            {% elif driver.status == 'inactive' %}
                                                <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                                            {% elif driver.status == 'suspended' %}
                                                <span class="badge bg-warning">{% trans "Suspended" %}</span>
                                            {% else %}
                                                <span class="badge bg-danger">{% trans "Terminated" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if driver.is_license_expiring_soon %}
                                                <span class="badge bg-warning">
                                                    {{ driver.license_expiry|date:"M d, Y" }}
                                                </span>
                                            {% else %}
                                                <span class="text-muted">{{ driver.license_expiry|date:"M d, Y" }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{{ driver.emergency_contact_name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ driver.emergency_contact_phone }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{% url 'transportation:driver_detail' driver.pk %}" 
                                                   class="btn btn-outline-primary" title="{% trans 'View Details' %}">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{% url 'transportation:driver_edit' driver.pk %}" 
                                                   class="btn btn-outline-secondary" title="{% trans 'Edit' %}">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if is_paginated %}
                        <nav aria-label="{% trans 'Driver pagination' %}">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1">&laquo; {% trans "First" %}</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                                    </li>
                                {% endif %}

                                <li class="page-item active">
                                    <span class="page-link">
                                        {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">{% trans "Last" %} &raquo;</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{% trans "No drivers found" %}</h5>
                            <p class="text-muted">{% trans "Start by adding your first driver to the system." %}</p>
                            <a href="{% url 'transportation:driver_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                {% trans "Add First Driver" %}
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
}
</style>
{% endblock %}