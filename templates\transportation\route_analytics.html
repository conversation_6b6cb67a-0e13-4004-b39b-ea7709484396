{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Route Analytics" %} - {{ route.name }}{% endblock %}

{% block extra_css %}
<style>
    .metric-card {
        border-left: 4px solid;
        transition: transform 0.2s;
    }
    .metric-card:hover {
        transform: translateY(-2px);
    }
    .metric-card.primary { border-left-color: #007bff; }
    .metric-card.success { border-left-color: #28a745; }
    .metric-card.warning { border-left-color: #ffc107; }
    .metric-card.danger { border-left-color: #dc3545; }
    .metric-card.info { border-left-color: #17a2b8; }
    
    .chart-container {
        position: relative;
        height: 300px;
        margin: 20px 0;
    }
    
    .analytics-tabs .nav-link {
        border-radius: 0;
        border-bottom: 3px solid transparent;
    }
    
    .analytics-tabs .nav-link.active {
        border-bottom-color: #007bff;
        background: none;
    }
    
    .performance-indicator {
        font-size: 2rem;
        font-weight: bold;
    }
    
    .trend-up { color: #28a745; }
    .trend-down { color: #dc3545; }
    .trend-stable { color: #6c757d; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">{% trans "Route Analytics" %}</h1>
                    <p class="text-muted">{{ route.name }} ({{ route.code }})</p>
                </div>
                <div>
                    <a href="{% url 'transportation:route_detail' route.pk %}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left"></i> {% trans "Back to Route" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Route Overview -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card metric-card info">
                <div class="card-body text-center">
                    <i class="fas fa-route fa-2x text-info mb-2"></i>
                    <h5>{% trans "Total Distance" %}</h5>
                    <div class="performance-indicator">{{ route.total_distance_km|floatformat:1 }} km</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card metric-card primary">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x text-primary mb-2"></i>
                    <h5>{% trans "Current Occupancy" %}</h5>
                    <div class="performance-indicator">{{ route.current_occupancy }}/{{ route.max_capacity }}</div>
                    <small class="text-muted">
                        {% widthratio route.current_occupancy route.max_capacity 100 %}% {% trans "capacity" %}
                    </small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card metric-card success">
                <div class="card-body text-center">
                    <i class="fas fa-map-marker-alt fa-2x text-success mb-2"></i>
                    <h5>{% trans "Total Stops" %}</h5>
                    <div class="performance-indicator">{{ route.stops.count }}</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card metric-card warning">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                    <h5>{% trans "Avg Duration" %}</h5>
                    <div class="performance-indicator">
                        {% if route.estimated_duration_minutes %}
                            {{ route.estimated_duration_minutes }} min
                        {% else %}
                            N/A
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics Tabs -->
    <div class="row">
        <div class="col-12">
            <ul class="nav nav-tabs analytics-tabs" id="analyticsTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="performance-tab" data-bs-toggle="tab" data-bs-target="#performance" type="button" role="tab">
                        {% trans "Performance" %}
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="efficiency-tab" data-bs-toggle="tab" data-bs-target="#efficiency" type="button" role="tab">
                        {% trans "Efficiency" %}
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="historical-tab" data-bs-toggle="tab" data-bs-target="#historical" type="button" role="tab">
                        {% trans "Historical Data" %}
                    </button>
                </li>
            </ul>
            
            <div class="tab-content" id="analyticsTabContent">
                <!-- Performance Tab -->
                <div class="tab-pane fade show active" id="performance" role="tabpanel">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <!-- 7 Days Performance -->
                                <div class="col-md-4">
                                    <h6 class="text-muted">{% trans "Last 7 Days" %}</h6>
                                    {% if performance_7d %}
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between">
                                                <span>{% trans "On-time Rate" %}</span>
                                                <span class="font-weight-bold">{{ performance_7d.on_time_rate|floatformat:1 }}%</span>
                                            </div>
                                            <div class="progress" style="height: 6px;">
                                                <div class="progress-bar bg-success" style="width: {{ performance_7d.on_time_rate }}%"></div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between">
                                                <span>{% trans "Avg Delay" %}</span>
                                                <span class="font-weight-bold">{{ performance_7d.avg_delay_minutes|floatformat:0 }} min</span>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between">
                                                <span>{% trans "Attendance Rate" %}</span>
                                                <span class="font-weight-bold">{{ performance_7d.attendance_rate|floatformat:1 }}%</span>
                                            </div>
                                        </div>
                                    {% else %}
                                        <p class="text-muted">{% trans "No data available" %}</p>
                                    {% endif %}
                                </div>
                                
                                <!-- 30 Days Performance -->
                                <div class="col-md-4">
                                    <h6 class="text-muted">{% trans "Last 30 Days" %}</h6>
                                    {% if performance_30d %}
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between">
                                                <span>{% trans "On-time Rate" %}</span>
                                                <span class="font-weight-bold">{{ performance_30d.on_time_rate|floatformat:1 }}%</span>
                                            </div>
                                            <div class="progress" style="height: 6px;">
                                                <div class="progress-bar bg-info" style="width: {{ performance_30d.on_time_rate }}%"></div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between">
                                                <span>{% trans "Avg Delay" %}</span>
                                                <span class="font-weight-bold">{{ performance_30d.avg_delay_minutes|floatformat:0 }} min</span>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between">
                                                <span>{% trans "Attendance Rate" %}</span>
                                                <span class="font-weight-bold">{{ performance_30d.attendance_rate|floatformat:1 }}%</span>
                                            </div>
                                        </div>
                                    {% else %}
                                        <p class="text-muted">{% trans "No data available" %}</p>
                                    {% endif %}
                                </div>
                                
                                <!-- 90 Days Performance -->
                                <div class="col-md-4">
                                    <h6 class="text-muted">{% trans "Last 90 Days" %}</h6>
                                    {% if performance_90d %}
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between">
                                                <span>{% trans "On-time Rate" %}</span>
                                                <span class="font-weight-bold">{{ performance_90d.on_time_rate|floatformat:1 }}%</span>
                                            </div>
                                            <div class="progress" style="height: 6px;">
                                                <div class="progress-bar bg-warning" style="width: {{ performance_90d.on_time_rate }}%"></div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between">
                                                <span>{% trans "Avg Delay" %}</span>
                                                <span class="font-weight-bold">{{ performance_90d.avg_delay_minutes|floatformat:0 }} min</span>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between">
                                                <span>{% trans "Attendance Rate" %}</span>
                                                <span class="font-weight-bold">{{ performance_90d.attendance_rate|floatformat:1 }}%</span>
                                            </div>
                                        </div>
                                    {% else %}
                                        <p class="text-muted">{% trans "No data available" %}</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Efficiency Tab -->
                <div class="tab-pane fade" id="efficiency" role="tabpanel">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>{% trans "Route Efficiency Metrics" %}</h6>
                                    <div class="chart-container">
                                        <canvas id="efficiencyChart"></canvas>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>{% trans "Fuel Consumption" %}</h6>
                                    <div class="chart-container">
                                        <canvas id="fuelChart"></canvas>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mt-4">
                                <div class="col-md-12">
                                    <h6>{% trans "Optimization Opportunities" %}</h6>
                                    <div class="alert alert-info">
                                        <i class="fas fa-lightbulb"></i>
                                        {% trans "Based on current data, this route could benefit from:" %}
                                        <ul class="mb-0 mt-2">
                                            <li>{% trans "Stop sequence optimization to reduce travel time" %}</li>
                                            <li>{% trans "Schedule adjustment during peak hours" %}</li>
                                            <li>{% trans "Capacity utilization improvement" %}</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Historical Data Tab -->
                <div class="tab-pane fade" id="historical" role="tabpanel">
                    <div class="card">
                        <div class="card-body">
                            <h6>{% trans "Historical Analytics" %}</h6>
                            {% if historical_analytics %}
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>{% trans "Period" %}</th>
                                                <th>{% trans "Total Distance" %}</th>
                                                <th>{% trans "Fuel Consumed" %}</th>
                                                <th>{% trans "On-time Rate" %}</th>
                                                <th>{% trans "Avg Students" %}</th>
                                                <th>{% trans "Incidents" %}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for analytics in historical_analytics %}
                                            <tr>
                                                <td>{{ analytics.date_from|date:"M d" }} - {{ analytics.date_to|date:"M d, Y" }}</td>
                                                <td>{{ analytics.total_distance_km|floatformat:1 }} km</td>
                                                <td>{{ analytics.fuel_consumed_liters|floatformat:1 }} L</td>
                                                <td>
                                                    <span class="badge badge-{% if analytics.on_time_percentage >= 90 %}success{% elif analytics.on_time_percentage >= 75 %}warning{% else %}danger{% endif %}">
                                                        {{ analytics.on_time_percentage|floatformat:1 }}%
                                                    </span>
                                                </td>
                                                <td>{{ analytics.average_students|floatformat:0 }}</td>
                                                <td>{{ analytics.incidents_count }}</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="text-center py-4">
                                    <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">{% trans "No historical data available yet." %}</p>
                                    <small class="text-muted">{% trans "Analytics will appear here once the route has been active for some time." %}</small>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Efficiency Chart
    const efficiencyCtx = document.getElementById('efficiencyChart');
    if (efficiencyCtx) {
        new Chart(efficiencyCtx, {
            type: 'line',
            data: {
                labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                datasets: [{
                    label: '{% trans "On-time Rate" %}',
                    data: [85, 88, 92, 89],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
    }
    
    // Fuel Chart
    const fuelCtx = document.getElementById('fuelChart');
    if (fuelCtx) {
        new Chart(fuelCtx, {
            type: 'bar',
            data: {
                labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                datasets: [{
                    label: '{% trans "Fuel (Liters)" %}',
                    data: [120, 115, 118, 122],
                    backgroundColor: '#28a745',
                    borderColor: '#1e7e34',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
});
</script>
{% endblock %}