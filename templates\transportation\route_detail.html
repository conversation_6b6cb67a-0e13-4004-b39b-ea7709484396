{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Route Details" %} - {{ route.name }}{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">{% trans "Dashboard" %}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'transportation:dashboard' %}">{% trans "Transportation" %}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'transportation:route_list' %}">{% trans "Routes" %}</a></li>
        <li class="breadcrumb-item active">{{ route.name }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-route me-2"></i>
                        {% trans "Route Details" %} - {{ route.name }}
                    </h4>
                    <div class="btn-group">
                        <a href="{% url 'transportation:route_update' route.pk %}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit me-1"></i>{% trans "Edit" %}
                        </a>
                        <a href="{% url 'transportation:route_optimize' route.pk %}" class="btn btn-warning btn-sm">
                            <i class="fas fa-cogs me-1"></i>{% trans "Optimize" %}
                        </a>
                        <a href="{% url 'transportation:route_analytics' route.pk %}" class="btn btn-info btn-sm">
                            <i class="fas fa-chart-bar me-1"></i>{% trans "Analytics" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>{% trans "Basic Information" %}</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>{% trans "Route Name" %}:</strong></td>
                                    <td>{{ route.name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Route Code" %}:</strong></td>
                                    <td>{{ route.code }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Status" %}:</strong></td>
                                    <td>
                                        <span class="badge bg-{% if route.status == 'active' %}success{% elif route.status == 'inactive' %}secondary{% else %}warning{% endif %}">
                                            {{ route.get_status_display }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Vehicle" %}:</strong></td>
                                    <td>
                                        {% if route.vehicle %}
                                            <a href="{% url 'transportation:vehicle_detail' route.vehicle.pk %}">
                                                {{ route.vehicle.vehicle_number }}
                                            </a>
                                        {% else %}
                                            <span class="text-muted">{% trans "Not assigned" %}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Primary Driver" %}:</strong></td>
                                    <td>
                                        {% if route.primary_driver %}
                                            <a href="{% url 'transportation:driver_detail' route.primary_driver.pk %}">
                                                {{ route.primary_driver.employee.get_full_name }}
                                            </a>
                                        {% else %}
                                            <span class="text-muted">{% trans "Not assigned" %}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Backup Driver" %}:</strong></td>
                                    <td>
                                        {% if route.backup_driver %}
                                            {{ route.backup_driver.employee.get_full_name }}
                                        {% else %}
                                            <span class="text-muted">{% trans "Not assigned" %}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>{% trans "Route Statistics" %}</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>{% trans "Total Distance" %}:</strong></td>
                                    <td>{{ route.total_distance_km|default:"N/A" }} km</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Estimated Duration" %}:</strong></td>
                                    <td>{{ route.estimated_duration_minutes|default:"N/A" }} {% trans "minutes" %}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Max Capacity" %}:</strong></td>
                                    <td>{{ route.max_capacity }} {% trans "students" %}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Current Occupancy" %}:</strong></td>
                                    <td>
                                        {{ route.current_occupancy }}/{{ route.max_capacity }}
                                        <div class="progress mt-1" style="height: 6px;">
                                            <div class="progress-bar" role="progressbar" 
                                                 style="width: {% widthratio route.current_occupancy route.max_capacity 100 %}%">
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Morning Start" %}:</strong></td>
                                    <td>{{ route.morning_start_time|default:"N/A" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Afternoon Start" %}:</strong></td>
                                    <td>{{ route.afternoon_start_time|default:"N/A" }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Route Stops -->
                    {% if route_stops %}
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>{% trans "Route Stops" %}</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>{% trans "Sequence" %}</th>
                                            <th>{% trans "Stop Name" %}</th>
                                            <th>{% trans "Morning Arrival" %}</th>
                                            <th>{% trans "Afternoon Arrival" %}</th>
                                            <th>{% trans "Students" %}</th>
                                            <th>{% trans "Actions" %}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for route_stop in route_stops %}
                                        <tr>
                                            <td>
                                                <span class="badge bg-primary">{{ route_stop.sequence_order }}</span>
                                            </td>
                                            <td>
                                                <a href="{% url 'transportation:stop_detail' route_stop.bus_stop.pk %}">
                                                    {{ route_stop.bus_stop.name }}
                                                </a>
                                            </td>
                                            <td>{{ route_stop.estimated_arrival_time_morning|default:"N/A" }}</td>
                                            <td>{{ route_stop.estimated_arrival_time_afternoon|default:"N/A" }}</td>
                                            <td>
                                                <span class="badge bg-info">
                                                    {{ route_stop.bus_stop.student_count|default:0 }}
                                                </span>
                                            </td>
                                            <td>
                                                <a href="{% url 'transportation:stop_detail' route_stop.bus_stop.pk %}" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    {% trans "View" %}
                                                </a>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Assigned Students -->
                    {% if assigned_students %}
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>{% trans "Assigned Students" %}</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>{% trans "Student" %}</th>
                                            <th>{% trans "Pickup Stop" %}</th>
                                            <th>{% trans "Drop-off Stop" %}</th>
                                            <th>{% trans "Status" %}</th>
                                            <th>{% trans "Actions" %}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for assignment in assigned_students %}
                                        <tr>
                                            <td>{{ assignment.student.get_full_name }}</td>
                                            <td>{{ assignment.pickup_stop.name }}</td>
                                            <td>{{ assignment.dropoff_stop.name|default:assignment.pickup_stop.name }}</td>
                                            <td>
                                                <span class="badge bg-{% if assignment.status == 'active' %}success{% else %}secondary{% endif %}">
                                                    {{ assignment.get_status_display }}
                                                </span>
                                            </td>
                                            <td>
                                                <a href="{% url 'transportation:student_detail' assignment.pk %}" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    {% trans "View" %}
                                                </a>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Performance Summary -->
                    {% if performance %}
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>{% trans "Performance Summary" %}</h5>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body text-center">
                                            <h4>{{ performance.avg_speed|floatformat:1 }} km/h</h4>
                                            <p class="mb-0">{% trans "Average Speed" %}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-success text-white">
                                        <div class="card-body text-center">
                                            <h4>{{ performance.on_time_percentage|floatformat:1 }}%</h4>
                                            <p class="mb-0">{% trans "On-Time Rate" %}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-info text-white">
                                        <div class="card-body text-center">
                                            <h4>{{ performance.fuel_efficiency|floatformat:1 }} L/100km</h4>
                                            <p class="mb-0">{% trans "Fuel Efficiency" %}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-warning text-white">
                                        <div class="card-body text-center">
                                            <h4>{{ performance.total_trips }}</h4>
                                            <p class="mb-0">{% trans "Total Trips" %}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Recent Optimizations -->
                    {% if recent_optimizations %}
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>{% trans "Recent Optimizations" %}</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>{% trans "Date" %}</th>
                                            <th>{% trans "Type" %}</th>
                                            <th>{% trans "Status" %}</th>
                                            <th>{% trans "Distance Savings" %}</th>
                                            <th>{% trans "Fuel Savings" %}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for optimization in recent_optimizations %}
                                        <tr>
                                            <td>{{ optimization.created_at|date:"M d, Y" }}</td>
                                            <td>{{ optimization.get_optimization_type_display }}</td>
                                            <td>
                                                <span class="badge bg-{% if optimization.status == 'completed' %}success{% elif optimization.status == 'failed' %}danger{% else %}warning{% endif %}">
                                                    {{ optimization.get_status_display }}
                                                </span>
                                            </td>
                                            <td>{{ optimization.distance_savings_km|default:"N/A" }} km</td>
                                            <td>{{ optimization.fuel_savings_percentage|default:"N/A" }}%</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}