{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load widget_tweaks %}

{% block title %}
    {% if object %}
        {% trans "Edit Route" %} - {{ object.name }}
    {% else %}
        {% trans "Create Route" %}
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">
                    {% if object %}
                        {% trans "Edit Route" %} - {{ object.name }}
                    {% else %}
                        {% trans "Create Route" %}
                    {% endif %}
                </h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'accounts:dashboard' %}">{% trans "Dashboard" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'transportation:dashboard' %}">{% trans "Transportation" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'transportation:route_list' %}">{% trans "Routes" %}</a></li>
                        <li class="breadcrumb-item active">
                            {% if object %}{% trans "Edit" %}{% else %}{% trans "Create" %}{% endif %}
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-route me-2"></i>
                        {% if object %}
                            {% trans "Edit Route" %} - {{ object.name }}
                        {% else %}
                            {% trans "Create New Route" %}
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label">
                                        {{ form.name.label }}
                                        {% if form.name.field.required %}<span class="text-danger">*</span>{% endif %}
                                    </label>
                                    {{ form.name|add_class:"form-control" }}
                                    {% if form.name.errors %}
                                        <div class="text-danger small">{{ form.name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.code.id_for_label }}" class="form-label">
                                        {{ form.code.label }}
                                        {% if form.code.field.required %}<span class="text-danger">*</span>{% endif %}
                                    </label>
                                    {{ form.code|add_class:"form-control" }}
                                    {% if form.code.errors %}
                                        <div class="text-danger small">{{ form.code.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.route_type.id_for_label }}" class="form-label">
                                        {{ form.route_type.label }}
                                    </label>
                                    {{ form.route_type|add_class:"form-select" }}
                                    {% if form.route_type.errors %}
                                        <div class="text-danger small">{{ form.route_type.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.status.id_for_label }}" class="form-label">
                                        {{ form.status.label }}
                                    </label>
                                    {{ form.status|add_class:"form-select" }}
                                    {% if form.status.errors %}
                                        <div class="text-danger small">{{ form.status.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Vehicle and Driver Assignment -->
                        <h6 class="mb-3 text-primary">
                            <i class="fas fa-bus me-1"></i>
                            {% trans "Vehicle & Driver Assignment" %}
                        </h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.vehicle.id_for_label }}" class="form-label">
                                        {{ form.vehicle.label }}
                                    </label>
                                    {{ form.vehicle|add_class:"form-select" }}
                                    {% if form.vehicle.errors %}
                                        <div class="text-danger small">{{ form.vehicle.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.primary_driver.id_for_label }}" class="form-label">
                                        {{ form.primary_driver.label }}
                                    </label>
                                    {{ form.primary_driver|add_class:"form-select" }}
                                    {% if form.primary_driver.errors %}
                                        <div class="text-danger small">{{ form.primary_driver.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.backup_driver.id_for_label }}" class="form-label">
                                        {{ form.backup_driver.label }}
                                    </label>
                                    {{ form.backup_driver|add_class:"form-select" }}
                                    {% if form.backup_driver.errors %}
                                        <div class="text-danger small">{{ form.backup_driver.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Schedule Information -->
                        <h6 class="mb-3 text-primary">
                            <i class="fas fa-clock me-1"></i>
                            {% trans "Schedule Information" %}
                        </h6>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="{{ form.start_time_morning.id_for_label }}" class="form-label">
                                        {{ form.start_time_morning.label }}
                                    </label>
                                    {{ form.start_time_morning|add_class:"form-control" }}
                                    {% if form.start_time_morning.errors %}
                                        <div class="text-danger small">{{ form.start_time_morning.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="{{ form.start_time_afternoon.id_for_label }}" class="form-label">
                                        {{ form.start_time_afternoon.label }}
                                    </label>
                                    {{ form.start_time_afternoon|add_class:"form-control" }}
                                    {% if form.start_time_afternoon.errors %}
                                        <div class="text-danger small">{{ form.start_time_afternoon.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="{{ form.estimated_duration_minutes.id_for_label }}" class="form-label">
                                        {{ form.estimated_duration_minutes.label }}
                                    </label>
                                    <div class="input-group">
                                        {{ form.estimated_duration_minutes|add_class:"form-control" }}
                                        <span class="input-group-text">{% trans "min" %}</span>
                                    </div>
                                    {% if form.estimated_duration_minutes.errors %}
                                        <div class="text-danger small">{{ form.estimated_duration_minutes.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="{{ form.total_distance_km.id_for_label }}" class="form-label">
                                        {{ form.total_distance_km.label }}
                                    </label>
                                    <div class="input-group">
                                        {{ form.total_distance_km|add_class:"form-control" }}
                                        <span class="input-group-text">{% trans "km" %}</span>
                                    </div>
                                    {% if form.total_distance_km.errors %}
                                        <div class="text-danger small">{{ form.total_distance_km.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Capacity and Fees -->
                        <h6 class="mb-3 text-primary">
                            <i class="fas fa-users me-1"></i>
                            {% trans "Capacity & Fees" %}
                        </h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.max_capacity.id_for_label }}" class="form-label">
                                        {{ form.max_capacity.label }}
                                    </label>
                                    {{ form.max_capacity|add_class:"form-control" }}
                                    {% if form.max_capacity.errors %}
                                        <div class="text-danger small">{{ form.max_capacity.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.current_occupancy.id_for_label }}" class="form-label">
                                        {{ form.current_occupancy.label }}
                                    </label>
                                    {{ form.current_occupancy|add_class:"form-control" }}
                                    {% if form.current_occupancy.errors %}
                                        <div class="text-danger small">{{ form.current_occupancy.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.monthly_fee.id_for_label }}" class="form-label">
                                        {{ form.monthly_fee.label }}
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        {{ form.monthly_fee|add_class:"form-control" }}
                                    </div>
                                    {% if form.monthly_fee.errors %}
                                        <div class="text-danger small">{{ form.monthly_fee.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="{{ form.notes.id_for_label }}" class="form-label">
                                        {{ form.notes.label }}
                                    </label>
                                    {{ form.notes|add_class:"form-control" }}
                                    {% if form.notes.errors %}
                                        <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{% url 'transportation:route_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>
                                        {% trans "Cancel" %}
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        {% if object %}
                                            {% trans "Update Route" %}
                                        {% else %}
                                            {% trans "Create Route" %}
                                        {% endif %}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}