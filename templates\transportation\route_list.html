{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Routes" %} - {% trans "Transportation" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">{% trans "Transportation Routes" %}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'accounts:dashboard' %}">{% trans "Dashboard" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'transportation:dashboard' %}">{% trans "Transportation" %}</a></li>
                        <li class="breadcrumb-item active">{% trans "Routes" %}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <form method="get" class="row g-3">
                                <div class="col-md-4">
                                    <input type="text" name="search" class="form-control" 
                                           placeholder="{% trans 'Search routes...' %}" 
                                           value="{{ request.GET.search }}">
                                </div>
                                <div class="col-md-3">
                                    <select name="status" class="form-select">
                                        <option value="">{% trans "All Status" %}</option>
                                        <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>{% trans "Active" %}</option>
                                        <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>{% trans "Inactive" %}</option>
                                        <option value="planning" {% if request.GET.status == 'planning' %}selected{% endif %}>{% trans "Planning" %}</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> {% trans "Filter" %}
                                    </button>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{% url 'transportation:route_create' %}" class="btn btn-success">
                                <i class="fas fa-plus me-1"></i>
                                {% trans "Create Route" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Routes List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-route me-2"></i>
                        {% trans "Transportation Routes" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if routes %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>{% trans "Route" %}</th>
                                        <th>{% trans "Vehicle" %}</th>
                                        <th>{% trans "Driver" %}</th>
                                        <th>{% trans "Capacity" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Distance" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for route in routes %}
                                    <tr>
                                        <td>
                                            <div>
                                                <h6 class="mb-0">{{ route.name }}</h6>
                                                <small class="text-muted">{{ route.code }}</small>
                                                <br>
                                                <span class="badge bg-light text-dark">
                                                    {{ route.get_route_type_display }}
                                                </span>
                                            </div>
                                        </td>
                                        <td>
                                            {% if route.vehicle %}
                                                <div>
                                                    <strong>{{ route.vehicle.vehicle_number }}</strong>
                                                    <br>
                                                    <small class="text-muted">{{ route.vehicle.license_plate }}</small>
                                                </div>
                                            {% else %}
                                                <span class="text-muted">{% trans "Not assigned" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if route.primary_driver %}
                                                <div>
                                                    <strong>{{ route.primary_driver.full_name }}</strong>
                                                    <br>
                                                    <small class="text-muted">{{ route.primary_driver.license_number }}</small>
                                                </div>
                                            {% else %}
                                                <span class="text-muted">{% trans "Not assigned" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="progress me-2" style="width: 60px; height: 8px;">
                                                    <div class="progress-bar bg-success" 
                                                         style="width: {{ route.occupancy_percentage }}%"></div>
                                                </div>
                                                <small>{{ route.current_occupancy }}/{{ route.max_capacity }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            {% if route.status == 'active' %}
                                                <span class="badge bg-success">{% trans "Active" %}</span>
                                            {% elif route.status == 'inactive' %}
                                                <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                                            {% elif route.status == 'planning' %}
                                                <span class="badge bg-warning">{% trans "Planning" %}</span>
                                            {% else %}
                                                <span class="badge bg-danger">{% trans "Suspended" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if route.total_distance_km %}
                                                {{ route.total_distance_km }} km
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{% url 'transportation:route_detail' route.pk %}" 
                                                   class="btn btn-outline-primary" title="{% trans 'View Details' %}">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{% url 'transportation:route_edit' route.pk %}" 
                                                   class="btn btn-outline-secondary" title="{% trans 'Edit' %}">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'transportation:route_optimize' route.pk %}" 
                                                   class="btn btn-outline-success" title="{% trans 'Optimize' %}">
                                                    <i class="fas fa-cogs"></i>
                                                </a>
                                                <a href="{% url 'transportation:route_analytics' route.pk %}" 
                                                   class="btn btn-outline-info" title="{% trans 'Analytics' %}">
                                                    <i class="fas fa-chart-bar"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if is_paginated %}
                        <nav aria-label="{% trans 'Route pagination' %}">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1">&laquo; {% trans "First" %}</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                                    </li>
                                {% endif %}

                                <li class="page-item active">
                                    <span class="page-link">
                                        {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">{% trans "Last" %} &raquo;</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-route fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{% trans "No routes found" %}</h5>
                            <p class="text-muted">{% trans "Start by creating your first transportation route." %}</p>
                            <a href="{% url 'transportation:route_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                {% trans "Create First Route" %}
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}