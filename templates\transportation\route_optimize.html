{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Route Optimization" %} - {{ route.name }}{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">{% trans "Dashboard" %}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'transportation:dashboard' %}">{% trans "Transportation" %}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'transportation:route_list' %}">{% trans "Routes" %}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'transportation:route_detail' route.pk %}">{{ route.name }}</a></li>
        <li class="breadcrumb-item active">{% trans "Optimize" %}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        {% trans "Route Optimization" %} - {{ route.name }}
                    </h4>
                    <a href="{% url 'transportation:route_detail' route.pk %}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>{% trans "Back to Route" %}
                    </a>
                </div>
                <div class="card-body">
                    <!-- Current Route Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>{% trans "Current Route Information" %}</h5>
                            <div class="card bg-light">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-6">
                                            <p><strong>{% trans "Total Distance" %}:</strong></p>
                                            <h4 class="text-primary">{{ route.total_distance_km|default:"N/A" }} km</h4>
                                        </div>
                                        <div class="col-6">
                                            <p><strong>{% trans "Estimated Duration" %}:</strong></p>
                                            <h4 class="text-info">{{ route.estimated_duration_minutes|default:"N/A" }} min</h4>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-6">
                                            <p><strong>{% trans "Number of Stops" %}:</strong></p>
                                            <h4 class="text-success">{{ route.stops.count }}</h4>
                                        </div>
                                        <div class="col-6">
                                            <p><strong>{% trans "Students" %}:</strong></p>
                                            <h4 class="text-warning">{{ route.current_occupancy }}</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5>{% trans "Optimization Options" %}</h5>
                            <form method="post" id="optimizationForm">
                                {% csrf_token %}
                                <div class="mb-3">
                                    <label for="optimization_type" class="form-label">{% trans "Optimization Type" %}</label>
                                    <select name="optimization_type" id="optimization_type" class="form-select">
                                        <option value="nearest_neighbor">{% trans "Nearest Neighbor" %}</option>
                                        <option value="genetic_algorithm">{% trans "Genetic Algorithm" %}</option>
                                        <option value="simulated_annealing">{% trans "Simulated Annealing" %}</option>
                                        <option value="ant_colony">{% trans "Ant Colony Optimization" %}</option>
                                    </select>
                                    <div class="form-text">{% trans "Choose the optimization algorithm to use" %}</div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="consider_traffic" id="consider_traffic" checked>
                                        <label class="form-check-label" for="consider_traffic">
                                            {% trans "Consider Traffic Conditions" %}
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="minimize_fuel" id="minimize_fuel" checked>
                                        <label class="form-check-label" for="minimize_fuel">
                                            {% trans "Minimize Fuel Consumption" %}
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="balance_load" id="balance_load">
                                        <label class="form-check-label" for="balance_load">
                                            {% trans "Balance Student Load" %}
                                        </label>
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn btn-primary" id="optimizeBtn">
                                    <i class="fas fa-play me-1"></i>{% trans "Start Optimization" %}
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Optimization Progress -->
                    <div id="optimizationProgress" class="row mb-4" style="display: none;">
                        <div class="col-12">
                            <h5>{% trans "Optimization Progress" %}</h5>
                            <div class="card">
                                <div class="card-body">
                                    <div class="progress mb-3">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                             role="progressbar" style="width: 0%" id="progressBar">
                                        </div>
                                    </div>
                                    <p id="progressText">{% trans "Initializing optimization..." %}</p>
                                    <div id="optimizationResults" style="display: none;">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="card bg-success text-white">
                                                    <div class="card-body text-center">
                                                        <h4 id="distanceSavings">0 km</h4>
                                                        <p class="mb-0">{% trans "Distance Saved" %}</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="card bg-info text-white">
                                                    <div class="card-body text-center">
                                                        <h4 id="timeSavings">0 min</h4>
                                                        <p class="mb-0">{% trans "Time Saved" %}</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="card bg-warning text-white">
                                                    <div class="card-body text-center">
                                                        <h4 id="fuelSavings">0%</h4>
                                                        <p class="mb-0">{% trans "Fuel Savings" %}</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="card bg-primary text-white">
                                                    <div class="card-body text-center">
                                                        <h4 id="newStopOrder">{{ route.stops.count }}</h4>
                                                        <p class="mb-0">{% trans "Optimized Stops" %}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mt-3">
                                            <button class="btn btn-success" id="applyOptimization">
                                                <i class="fas fa-check me-1"></i>{% trans "Apply Optimization" %}
                                            </button>
                                            <button class="btn btn-secondary" id="discardOptimization">
                                                <i class="fas fa-times me-1"></i>{% trans "Discard" %}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Route Visualization -->
                    <div class="row">
                        <div class="col-12">
                            <h5>{% trans "Route Visualization" %}</h5>
                            <div class="card">
                                <div class="card-body">
                                    <div id="routeMap" style="height: 400px; background: #f8f9fa; border-radius: 8px;">
                                        <div class="d-flex align-items-center justify-content-center h-100">
                                            <div class="text-center">
                                                <i class="fas fa-map fa-3x text-muted mb-3"></i>
                                                <h5 class="text-muted">{% trans "Route Map" %}</h5>
                                                <p class="text-muted">{% trans "Visual representation of the route and stops" %}</p>
                                                <p class="text-muted">{% trans "Map integration required" %}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Optimization History -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>{% trans "Optimization History" %}</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>{% trans "Date" %}</th>
                                            <th>{% trans "Type" %}</th>
                                            <th>{% trans "Status" %}</th>
                                            <th>{% trans "Distance Savings" %}</th>
                                            <th>{% trans "Fuel Savings" %}</th>
                                            <th>{% trans "Duration" %}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td colspan="6" class="text-center text-muted">
                                                {% trans "No optimization history available" %}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.getElementById('optimizationForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Show progress section
    document.getElementById('optimizationProgress').style.display = 'block';
    document.getElementById('optimizeBtn').disabled = true;
    
    // Simulate optimization progress
    let progress = 0;
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    
    const interval = setInterval(() => {
        progress += Math.random() * 20;
        if (progress > 100) progress = 100;
        
        progressBar.style.width = progress + '%';
        
        if (progress < 30) {
            progressText.textContent = '{% trans "Analyzing current route..." %}';
        } else if (progress < 60) {
            progressText.textContent = '{% trans "Calculating optimal paths..." %}';
        } else if (progress < 90) {
            progressText.textContent = '{% trans "Evaluating solutions..." %}';
        } else {
            progressText.textContent = '{% trans "Finalizing optimization..." %}';
        }
        
        if (progress >= 100) {
            clearInterval(interval);
            progressText.textContent = '{% trans "Optimization completed!" %}';
            
            // Show results
            document.getElementById('optimizationResults').style.display = 'block';
            document.getElementById('distanceSavings').textContent = '2.5 km';
            document.getElementById('timeSavings').textContent = '8 min';
            document.getElementById('fuelSavings').textContent = '12%';
            
            document.getElementById('optimizeBtn').disabled = false;
        }
    }, 500);
    
    // In a real implementation, you would make an AJAX call to start the optimization
    // and poll for progress updates
});

document.getElementById('applyOptimization').addEventListener('click', function() {
    if (confirm('{% trans "Are you sure you want to apply this optimization? This will update the route stops order." %}')) {
        // Apply the optimization
        alert('{% trans "Optimization applied successfully!" %}');
        window.location.href = '{% url "transportation:route_detail" route.pk %}';
    }
});

document.getElementById('discardOptimization').addEventListener('click', function() {
    document.getElementById('optimizationProgress').style.display = 'none';
    document.getElementById('optimizationResults').style.display = 'none';
});
</script>
{% endblock %}