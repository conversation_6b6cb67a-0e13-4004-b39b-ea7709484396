{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load widget_tweaks %}

{% block title %}
    {% if object %}
        {% trans "Edit Bus Stop" %} - {{ object.name }}
    {% else %}
        {% trans "Add Bus Stop" %}
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">
                    {% if object %}
                        {% trans "Edit Bus Stop" %} - {{ object.name }}
                    {% else %}
                        {% trans "Add Bus Stop" %}
                    {% endif %}
                </h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'accounts:dashboard' %}">{% trans "Dashboard" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'transportation:dashboard' %}">{% trans "Transportation" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'transportation:stop_list' %}">{% trans "Bus Stops" %}</a></li>
                        <li class="breadcrumb-item active">
                            {% if object %}{% trans "Edit" %}{% else %}{% trans "Add" %}{% endif %}
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        {% if object %}
                            {% trans "Edit Bus Stop" %} - {{ object.name }}
                        {% else %}
                            {% trans "Add New Bus Stop" %}
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <!-- Basic Information -->
                        <h6 class="mb-3 text-primary">
                            <i class="fas fa-info-circle me-1"></i>
                            {% trans "Basic Information" %}
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">
                                        {% trans "Stop Name" %}
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" name="name" id="name" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="code" class="form-label">
                                        {% trans "Stop Code" %}
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" name="code" id="code" class="form-control" 
                                           placeholder="{% trans 'Unique identifier' %}" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">
                                        {% trans "Status" %}
                                    </label>
                                    <select name="status" id="status" class="form-select">
                                        <option value="active" selected>{% trans "Active" %}</option>
                                        <option value="inactive">{% trans "Inactive" %}</option>
                                        <option value="temporary">{% trans "Temporary" %}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="landmark" class="form-label">
                                        {% trans "Nearby Landmark" %}
                                    </label>
                                    <input type="text" name="landmark" id="landmark" class="form-control" 
                                           placeholder="{% trans 'e.g., Near City Mall' %}">
                                </div>
                            </div>
                        </div>

                        <!-- Location Information -->
                        <h6 class="mb-3 text-primary">
                            <i class="fas fa-map me-1"></i>
                            {% trans "Location Information" %}
                        </h6>
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="address" class="form-label">
                                        {% trans "Address" %}
                                        <span class="text-danger">*</span>
                                    </label>
                                    <textarea name="address" id="address" class="form-control" rows="3" 
                                              placeholder="{% trans 'Full address of the bus stop' %}" required></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="latitude" class="form-label">
                                        {% trans "Latitude" %}
                                    </label>
                                    <input type="number" name="latitude" id="latitude" class="form-control" 
                                           step="0.00000001" min="-90" max="90" 
                                           placeholder="{% trans 'e.g., 24.7136' %}">
                                    <button type="button" class="btn btn-outline-secondary btn-sm mt-1" id="getCurrentLocation">
                                        <i class="fas fa-crosshairs"></i> {% trans "Get Current Location" %}
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="longitude" class="form-label">
                                        {% trans "Longitude" %}
                                    </label>
                                    <input type="number" name="longitude" id="longitude" class="form-control" 
                                           step="0.00000001" min="-180" max="180" 
                                           placeholder="{% trans 'e.g., 46.6753' %}">
                                </div>
                            </div>
                        </div>

                        <!-- Safety and Accessibility -->
                        <h6 class="mb-3 text-primary">
                            <i class="fas fa-shield-alt me-1"></i>
                            {% trans "Safety & Accessibility" %}
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="safety_rating" class="form-label">
                                        {% trans "Safety Rating" %}
                                    </label>
                                    <select name="safety_rating" id="safety_rating" class="form-select">
                                        <option value="1">1 - {% trans "Poor" %}</option>
                                        <option value="2">2 - {% trans "Fair" %}</option>
                                        <option value="3">3 - {% trans "Good" %}</option>
                                        <option value="4">4 - {% trans "Very Good" %}</option>
                                        <option value="5" selected>5 - {% trans "Excellent" %}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">{% trans "Accessibility Features" %}</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="accessibility_features" value="wheelchair_accessible" id="wheelchair">
                                        <label class="form-check-label" for="wheelchair">
                                            {% trans "Wheelchair Accessible" %}
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="accessibility_features" value="covered_shelter" id="shelter">
                                        <label class="form-check-label" for="shelter">
                                            {% trans "Covered Shelter" %}
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="accessibility_features" value="seating" id="seating">
                                        <label class="form-check-label" for="seating">
                                            {% trans "Seating Available" %}
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="accessibility_features" value="lighting" id="lighting">
                                        <label class="form-check-label" for="lighting">
                                            {% trans "Good Lighting" %}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="notes" class="form-label">
                                        {% trans "Notes" %}
                                    </label>
                                    <textarea name="notes" id="notes" class="form-control" rows="3" 
                                              placeholder="{% trans 'Additional notes about this bus stop' %}"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{% url 'transportation:stop_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>
                                        {% trans "Cancel" %}
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        {% if object %}
                                            {% trans "Update Bus Stop" %}
                                        {% else %}
                                            {% trans "Add Bus Stop" %}
                                        {% endif %}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
$(document).ready(function() {
    // Get current location functionality
    $('#getCurrentLocation').click(function() {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(function(position) {
                $('#latitude').val(position.coords.latitude);
                $('#longitude').val(position.coords.longitude);
            }, function(error) {
                alert('{% trans "Unable to get your location. Please enter coordinates manually." %}');
            });
        } else {
            alert('{% trans "Geolocation is not supported by this browser." %}');
        }
    });
});
</script>
{% endblock %}
{% endblock %}