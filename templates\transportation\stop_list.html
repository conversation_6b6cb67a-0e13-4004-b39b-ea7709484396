{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Bus Stops" %} - {% trans "Transportation" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">{% trans "Bus Stops" %}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'accounts:dashboard' %}">{% trans "Dashboard" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'transportation:dashboard' %}">{% trans "Transportation" %}</a></li>
                        <li class="breadcrumb-item active">{% trans "Bus Stops" %}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <form method="get" class="row g-3">
                                <div class="col-md-4">
                                    <input type="text" name="search" class="form-control" 
                                           placeholder="{% trans 'Search bus stops...' %}" 
                                           value="{{ request.GET.search }}">
                                </div>
                                <div class="col-md-3">
                                    <select name="status" class="form-select">
                                        <option value="">{% trans "All Status" %}</option>
                                        <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>{% trans "Active" %}</option>
                                        <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>{% trans "Inactive" %}</option>
                                        <option value="temporary" {% if request.GET.status == 'temporary' %}selected{% endif %}>{% trans "Temporary" %}</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> {% trans "Filter" %}
                                    </button>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{% url 'transportation:stop_create' %}" class="btn btn-success">
                                <i class="fas fa-plus me-1"></i>
                                {% trans "Add Bus Stop" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bus Stops List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        {% trans "Bus Stops" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if stops %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>{% trans "Stop" %}</th>
                                        <th>{% trans "Address" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Safety Rating" %}</th>
                                        <th>{% trans "Accessibility" %}</th>
                                        <th>{% trans "Location" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for stop in stops %}
                                    <tr>
                                        <td>
                                            <div>
                                                <h6 class="mb-0">{{ stop.name }}</h6>
                                                <small class="text-muted">{{ stop.code }}</small>
                                                {% if stop.landmark %}
                                                    <br>
                                                    <small class="text-info">
                                                        <i class="fas fa-map-pin"></i> {{ stop.landmark }}
                                                    </small>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ stop.address|truncatechars:50 }}</small>
                                        </td>
                                        <td>
                                            {% if stop.status == 'active' %}
                                                <span class="badge bg-success">{% trans "Active" %}</span>
                                            {% elif stop.status == 'inactive' %}
                                                <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                                            {% else %}
                                                <span class="badge bg-warning">{% trans "Temporary" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                {% for i in "12345" %}
                                                    {% if forloop.counter <= stop.safety_rating %}
                                                        <i class="fas fa-star text-warning"></i>
                                                    {% else %}
                                                        <i class="far fa-star text-muted"></i>
                                                    {% endif %}
                                                {% endfor %}
                                                <small class="ms-1 text-muted">({{ stop.safety_rating }}/5)</small>
                                            </div>
                                        </td>
                                        <td>
                                            {% if stop.accessibility_features %}
                                                <div class="d-flex flex-wrap gap-1">
                                                    {% for feature in stop.accessibility_features %}
                                                        {% if feature == 'wheelchair_accessible' %}
                                                            <span class="badge bg-info" title="{% trans 'Wheelchair Accessible' %}">
                                                                <i class="fas fa-wheelchair"></i>
                                                            </span>
                                                        {% elif feature == 'covered_shelter' %}
                                                            <span class="badge bg-success" title="{% trans 'Covered Shelter' %}">
                                                                <i class="fas fa-home"></i>
                                                            </span>
                                                        {% elif feature == 'seating' %}
                                                            <span class="badge bg-primary" title="{% trans 'Seating Available' %}">
                                                                <i class="fas fa-chair"></i>
                                                            </span>
                                                        {% elif feature == 'lighting' %}
                                                            <span class="badge bg-warning" title="{% trans 'Good Lighting' %}">
                                                                <i class="fas fa-lightbulb"></i>
                                                            </span>
                                                        {% endif %}
                                                    {% endfor %}
                                                </div>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if stop.coordinates %}
                                                <a href="{{ stop.google_maps_url }}" target="_blank" 
                                                   class="btn btn-outline-info btn-sm" title="{% trans 'View on Map' %}">
                                                    <i class="fas fa-map"></i>
                                                </a>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{% url 'transportation:stop_detail' stop.pk %}" 
                                                   class="btn btn-outline-primary" title="{% trans 'View Details' %}">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{% url 'transportation:stop_edit' stop.pk %}" 
                                                   class="btn btn-outline-secondary" title="{% trans 'Edit' %}">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if is_paginated %}
                        <nav aria-label="{% trans 'Bus stop pagination' %}">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1">&laquo; {% trans "First" %}</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                                    </li>
                                {% endif %}

                                <li class="page-item active">
                                    <span class="page-link">
                                        {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">{% trans "Last" %} &raquo;</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{% trans "No bus stops found" %}</h5>
                            <p class="text-muted">{% trans "Start by adding your first bus stop to the system." %}</p>
                            <a href="{% url 'transportation:stop_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                {% trans "Add First Bus Stop" %}
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}