{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Student Transportation Details" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">{% trans "Student Transportation Details" %}</h3>
                    <div>
                        <a href="{% url 'transportation:student_update' assignment.pk %}" class="btn btn-primary">
                            <i class="fas fa-edit"></i> {% trans "Edit Assignment" %}
                        </a>
                        <a href="{% url 'transportation:student_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> {% trans "Back to List" %}
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        <!-- Student Information -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">{% trans "Student Information" %}</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>{% trans "Name" %}:</strong></td>
                                            <td>{{ assignment.student.first_name }} {{ assignment.student.last_name }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Student ID" %}:</strong></td>
                                            <td>{{ assignment.student.student_id }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Grade" %}:</strong></td>
                                            <td>{{ assignment.student.current_grade|default:"N/A" }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Status" %}:</strong></td>
                                            <td>
                                                <span class="badge badge-{% if assignment.student.status == 'active' %}success{% else %}secondary{% endif %}">
                                                    {{ assignment.student.get_status_display }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Phone" %}:</strong></td>
                                            <td>{{ assignment.student.phone|default:"N/A" }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Transportation Assignment -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">{% trans "Transportation Assignment" %}</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>{% trans "Route" %}:</strong></td>
                                            <td>
                                                <a href="{% url 'transportation:route_detail' assignment.route.pk %}">
                                                    {{ assignment.route.name }} ({{ assignment.route.code }})
                                                </a>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Status" %}:</strong></td>
                                            <td>
                                                <span class="badge badge-{% if assignment.status == 'active' %}success{% elif assignment.status == 'suspended' %}warning{% else %}secondary{% endif %}">
                                                    {{ assignment.get_status_display }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Start Date" %}:</strong></td>
                                            <td>{{ assignment.start_date }}</td>
                                        </tr>
                                        {% if assignment.end_date %}
                                        <tr>
                                            <td><strong>{% trans "End Date" %}:</strong></td>
                                            <td>{{ assignment.end_date }}</td>
                                        </tr>
                                        {% endif %}
                                        <tr>
                                            <td><strong>{% trans "Monthly Fee" %}:</strong></td>
                                            <td>${{ assignment.monthly_fee }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <!-- Pickup/Drop-off Information -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">{% trans "Pickup Information" %}</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>{% trans "Stop" %}:</strong></td>
                                            <td>
                                                <a href="{% url 'transportation:stop_detail' assignment.pickup_stop.pk %}">
                                                    {{ assignment.pickup_stop.name }} ({{ assignment.pickup_stop.code }})
                                                </a>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Address" %}:</strong></td>
                                            <td>{{ assignment.pickup_stop.address }}</td>
                                        </tr>
                                        {% if assignment.pickup_stop.landmark %}
                                        <tr>
                                            <td><strong>{% trans "Landmark" %}:</strong></td>
                                            <td>{{ assignment.pickup_stop.landmark }}</td>
                                        </tr>
                                        {% endif %}
                                        <tr>
                                            <td><strong>{% trans "Safety Rating" %}:</strong></td>
                                            <td>
                                                {% for i in "12345" %}
                                                    {% if forloop.counter <= assignment.pickup_stop.safety_rating %}
                                                        <i class="fas fa-star text-warning"></i>
                                                    {% else %}
                                                        <i class="far fa-star text-muted"></i>
                                                    {% endif %}
                                                {% endfor %}
                                            </td>
                                        </tr>
                                        {% if assignment.pickup_stop.coordinates %}
                                        <tr>
                                            <td><strong>{% trans "Location" %}:</strong></td>
                                            <td>
                                                <a href="{{ assignment.pickup_stop.google_maps_url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-map-marker-alt"></i> {% trans "View on Map" %}
                                                </a>
                                            </td>
                                        </tr>
                                        {% endif %}
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">{% trans "Drop-off Information" %}</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>{% trans "Stop" %}:</strong></td>
                                            <td>
                                                <a href="{% url 'transportation:stop_detail' assignment.dropoff_stop.pk %}">
                                                    {{ assignment.dropoff_stop.name }} ({{ assignment.dropoff_stop.code }})
                                                </a>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Address" %}:</strong></td>
                                            <td>{{ assignment.dropoff_stop.address }}</td>
                                        </tr>
                                        {% if assignment.dropoff_stop.landmark %}
                                        <tr>
                                            <td><strong>{% trans "Landmark" %}:</strong></td>
                                            <td>{{ assignment.dropoff_stop.landmark }}</td>
                                        </tr>
                                        {% endif %}
                                        <tr>
                                            <td><strong>{% trans "Safety Rating" %}:</strong></td>
                                            <td>
                                                {% for i in "12345" %}
                                                    {% if forloop.counter <= assignment.dropoff_stop.safety_rating %}
                                                        <i class="fas fa-star text-warning"></i>
                                                    {% else %}
                                                        <i class="far fa-star text-muted"></i>
                                                    {% endif %}
                                                {% endfor %}
                                            </td>
                                        </tr>
                                        {% if assignment.dropoff_stop.coordinates %}
                                        <tr>
                                            <td><strong>{% trans "Location" %}:</strong></td>
                                            <td>
                                                <a href="{{ assignment.dropoff_stop.google_maps_url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-map-marker-alt"></i> {% trans "View on Map" %}
                                                </a>
                                            </td>
                                        </tr>
                                        {% endif %}
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <!-- Route Information -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">{% trans "Route Details" %}</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>{% trans "Vehicle" %}:</strong></td>
                                            <td>
                                                {% if assignment.route.vehicle %}
                                                    <a href="{% url 'transportation:vehicle_detail' assignment.route.vehicle.pk %}">
                                                        {{ assignment.route.vehicle.vehicle_number }} - {{ assignment.route.vehicle.license_plate }}
                                                    </a>
                                                {% else %}
                                                    <span class="text-muted">{% trans "Not assigned" %}</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Primary Driver" %}:</strong></td>
                                            <td>
                                                {% if assignment.route.primary_driver %}
                                                    <a href="{% url 'transportation:driver_detail' assignment.route.primary_driver.pk %}">
                                                        {{ assignment.route.primary_driver.full_name }}
                                                    </a>
                                                {% else %}
                                                    <span class="text-muted">{% trans "Not assigned" %}</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Route Type" %}:</strong></td>
                                            <td>{{ assignment.route.get_route_type_display }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Morning Start" %}:</strong></td>
                                            <td>{{ assignment.route.start_time_morning|default:"N/A" }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Afternoon Start" %}:</strong></td>
                                            <td>{{ assignment.route.start_time_afternoon|default:"N/A" }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Total Distance" %}:</strong></td>
                                            <td>{{ assignment.route.total_distance_km }} km</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Occupancy" %}:</strong></td>
                                            <td>
                                                {{ assignment.route.current_occupancy }}/{{ assignment.route.max_capacity }}
                                                <div class="progress mt-1" style="height: 10px;">
                                                    <div class="progress-bar" role="progressbar" 
                                                         style="width: {{ assignment.route.occupancy_percentage }}%"
                                                         aria-valuenow="{{ assignment.route.occupancy_percentage }}" 
                                                         aria-valuemin="0" aria-valuemax="100">
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Emergency Contact Information -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">{% trans "Emergency Contact" %}</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>{% trans "Name" %}:</strong></td>
                                            <td>{{ assignment.emergency_contact_name }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "Phone" %}:</strong></td>
                                            <td>
                                                <a href="tel:{{ assignment.emergency_contact_phone }}">
                                                    {{ assignment.emergency_contact_phone }}
                                                </a>
                                            </td>
                                        </tr>
                                    </table>
                                    
                                    {% if assignment.special_needs %}
                                    <div class="mt-3">
                                        <h6>{% trans "Special Needs" %}</h6>
                                        <div class="alert alert-info">
                                            {{ assignment.special_needs }}
                                        </div>
                                    </div>
                                    {% endif %}
                                    
                                    {% if assignment.notes %}
                                    <div class="mt-3">
                                        <h6>{% trans "Notes" %}</h6>
                                        <div class="alert alert-secondary">
                                            {{ assignment.notes }}
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Attendance -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">{% trans "Recent Attendance" %}</h5>
                                </div>
                                <div class="card-body">
                                    {% if recent_attendance %}
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>{% trans "Date" %}</th>
                                                    <th>{% trans "Type" %}</th>
                                                    <th>{% trans "Status" %}</th>
                                                    <th>{% trans "Scheduled Time" %}</th>
                                                    <th>{% trans "Actual Time" %}</th>
                                                    <th>{% trans "Stop" %}</th>
                                                    <th>{% trans "Driver" %}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for attendance in recent_attendance %}
                                                <tr>
                                                    <td>{{ attendance.date }}</td>
                                                    <td>
                                                        <span class="badge badge-{% if attendance.attendance_type == 'pickup' %}primary{% else %}success{% endif %}">
                                                            {{ attendance.get_attendance_type_display }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-{% if attendance.status == 'present' %}success{% elif attendance.status == 'late' %}warning{% else %}danger{% endif %}">
                                                            {{ attendance.get_status_display }}
                                                        </span>
                                                    </td>
                                                    <td>{{ attendance.scheduled_time }}</td>
                                                    <td>{{ attendance.actual_time|default:"N/A" }}</td>
                                                    <td>{{ attendance.bus_stop.name }}</td>
                                                    <td>{{ attendance.driver.full_name|default:"N/A" }}</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% else %}
                                    <div class="text-center text-muted py-4">
                                        <i class="fas fa-calendar-times fa-3x mb-3"></i>
                                        <p>{% trans "No attendance records found" %}</p>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Fee History -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">{% trans "Fee History" %}</h5>
                                </div>
                                <div class="card-body">
                                    {% if fee_history %}
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>{% trans "Month" %}</th>
                                                    <th>{% trans "Amount" %}</th>
                                                    <th>{% trans "Status" %}</th>
                                                    <th>{% trans "Due Date" %}</th>
                                                    <th>{% trans "Paid Date" %}</th>
                                                    <th>{% trans "Actions" %}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for fee in fee_history %}
                                                <tr>
                                                    <td>{{ fee.month|date:"F Y" }}</td>
                                                    <td>${{ fee.total_fee }}</td>
                                                    <td>
                                                        <span class="badge badge-{% if fee.status == 'paid' %}success{% elif fee.status == 'overdue' %}danger{% elif fee.status == 'pending' %}warning{% else %}secondary{% endif %}">
                                                            {{ fee.get_status_display }}
                                                        </span>
                                                    </td>
                                                    <td>{{ fee.due_date }}</td>
                                                    <td>{{ fee.paid_date|default:"N/A" }}</td>
                                                    <td>
                                                        {% if fee.status != 'paid' %}
                                                        <a href="{% url 'transportation:fee_detail' fee.pk %}" class="btn btn-sm btn-outline-primary">
                                                            {% trans "View" %}
                                                        </a>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% else %}
                                    <div class="text-center text-muted py-4">
                                        <i class="fas fa-receipt fa-3x mb-3"></i>
                                        <p>{% trans "No fee records found" %}</p>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Notifications -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">{% trans "Recent Notifications" %}</h5>
                                </div>
                                <div class="card-body">
                                    {% if recent_notifications %}
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>{% trans "Date" %}</th>
                                                    <th>{% trans "Type" %}</th>
                                                    <th>{% trans "Channel" %}</th>
                                                    <th>{% trans "Subject" %}</th>
                                                    <th>{% trans "Status" %}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for notification in recent_notifications %}
                                                <tr>
                                                    <td>{{ notification.created_at|date:"M d, Y H:i" }}</td>
                                                    <td>
                                                        <span class="badge badge-info">
                                                            {{ notification.get_notification_type_display }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-secondary">
                                                            {{ notification.get_channel_display }}
                                                        </span>
                                                    </td>
                                                    <td>{{ notification.subject }}</td>
                                                    <td>
                                                        <span class="badge badge-{% if notification.status == 'delivered' %}success{% elif notification.status == 'sent' %}primary{% elif notification.status == 'failed' %}danger{% else %}warning{% endif %}">
                                                            {{ notification.get_status_display }}
                                                        </span>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% else %}
                                    <div class="text-center text-muted py-4">
                                        <i class="fas fa-bell-slash fa-3x mb-3"></i>
                                        <p>{% trans "No notifications found" %}</p>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">{% trans "Actions" %}</h5>
                                </div>
                                <div class="card-body">
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'transportation:student_update' assignment.pk %}" class="btn btn-primary">
                                            <i class="fas fa-edit"></i> {% trans "Edit Assignment" %}
                                        </a>
                                        
                                        {% if assignment.status == 'active' %}
                                        <button type="button" class="btn btn-warning" data-toggle="modal" data-target="#suspendModal">
                                            <i class="fas fa-pause"></i> {% trans "Suspend" %}
                                        </button>
                                        {% elif assignment.status == 'suspended' %}
                                        <form method="post" action="{% url 'transportation:student_activate' assignment.pk %}" style="display: inline;">
                                            {% csrf_token %}
                                            <button type="submit" class="btn btn-success">
                                                <i class="fas fa-play"></i> {% trans "Activate" %}
                                            </button>
                                        </form>
                                        {% endif %}
                                        
                                        <button type="button" class="btn btn-info" data-toggle="modal" data-target="#attendanceModal">
                                            <i class="fas fa-calendar-check"></i> {% trans "Record Attendance" %}
                                        </button>
                                        
                                        <button type="button" class="btn btn-secondary" data-toggle="modal" data-target="#notificationModal">
                                            <i class="fas fa-bell"></i> {% trans "Send Notification" %}
                                        </button>
                                        
                                        <a href="{% url 'transportation:student_report' assignment.pk %}" class="btn btn-outline-primary">
                                            <i class="fas fa-file-alt"></i> {% trans "Generate Report" %}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Suspend Modal -->
<div class="modal fade" id="suspendModal" tabindex="-1" role="dialog" aria-labelledby="suspendModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="suspendModalLabel">{% trans "Suspend Transportation" %}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="post" action="{% url 'transportation:student_suspend' assignment.pk %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="form-group">
                        <label for="suspend_reason">{% trans "Reason for Suspension" %}</label>
                        <textarea class="form-control" id="suspend_reason" name="reason" rows="3" required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="suspend_end_date">{% trans "Suspension End Date" %}</label>
                        <input type="date" class="form-control" id="suspend_end_date" name="end_date">
                        <small class="form-text text-muted">{% trans "Leave blank for indefinite suspension" %}</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{% trans "Cancel" %}</button>
                    <button type="submit" class="btn btn-warning">{% trans "Suspend" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Attendance Modal -->
<div class="modal fade" id="attendanceModal" tabindex="-1" role="dialog" aria-labelledby="attendanceModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="attendanceModalLabel">{% trans "Record Attendance" %}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="post" action="{% url 'transportation:record_attendance' assignment.pk %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="form-group">
                        <label for="attendance_type">{% trans "Type" %}</label>
                        <select class="form-control" id="attendance_type" name="attendance_type" required>
                            <option value="pickup">{% trans "Pickup" %}</option>
                            <option value="dropoff">{% trans "Drop-off" %}</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="attendance_status">{% trans "Status" %}</label>
                        <select class="form-control" id="attendance_status" name="status" required>
                            <option value="present">{% trans "Present" %}</option>
                            <option value="absent">{% trans "Absent" %}</option>
                            <option value="late">{% trans "Late" %}</option>
                            <option value="early">{% trans "Early" %}</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="attendance_time">{% trans "Actual Time" %}</label>
                        <input type="time" class="form-control" id="attendance_time" name="actual_time">
                        <small class="form-text text-muted">{% trans "Leave blank to use current time" %}</small>
                    </div>
                    <div class="form-group">
                        <label for="attendance_notes">{% trans "Notes" %}</label>
                        <textarea class="form-control" id="attendance_notes" name="notes" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{% trans "Cancel" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "Record" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Notification Modal -->
<div class="modal fade" id="notificationModal" tabindex="-1" role="dialog" aria-labelledby="notificationModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="notificationModalLabel">{% trans "Send Notification" %}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="post" action="{% url 'transportation:send_notification' assignment.pk %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="form-group">
                        <label for="notification_type">{% trans "Type" %}</label>
                        <select class="form-control" id="notification_type" name="notification_type" required>
                            <option value="pickup_reminder">{% trans "Pickup Reminder" %}</option>
                            <option value="dropoff_confirmation">{% trans "Drop-off Confirmation" %}</option>
                            <option value="delay_alert">{% trans "Delay Alert" %}</option>
                            <option value="route_change">{% trans "Route Change" %}</option>
                            <option value="fee_reminder">{% trans "Fee Reminder" %}</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="notification_channel">{% trans "Channel" %}</label>
                        <select class="form-control" id="notification_channel" name="channel" required>
                            <option value="sms">{% trans "SMS" %}</option>
                            <option value="email">{% trans "Email" %}</option>
                            <option value="whatsapp">{% trans "WhatsApp" %}</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="notification_subject">{% trans "Subject" %}</label>
                        <input type="text" class="form-control" id="notification_subject" name="subject" required>
                    </div>
                    <div class="form-group">
                        <label for="notification_message">{% trans "Message" %}</label>
                        <textarea class="form-control" id="notification_message" name="message" rows="4" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{% trans "Cancel" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "Send" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Auto-populate notification fields based on type
    $('#notification_type').change(function() {
        var type = $(this).val();
        var subject = '';
        var message = '';
        
        switch(type) {
            case 'pickup_reminder':
                subject = '{% trans "Bus Pickup Reminder" %}';
                message = '{% trans "Reminder: Bus pickup is scheduled for your child." %}';
                break;
            case 'dropoff_confirmation':
                subject = '{% trans "Drop-off Confirmation" %}';
                message = '{% trans "Your child has been safely dropped off." %}';
                break;
            case 'delay_alert':
                subject = '{% trans "Bus Delay Alert" %}';
                message = '{% trans "The bus is running late today." %}';
                break;
            case 'route_change':
                subject = '{% trans "Route Change Notification" %}';
                message = '{% trans "There has been a change to your child\'s bus route." %}';
                break;
            case 'fee_reminder':
                subject = '{% trans "Transportation Fee Reminder" %}';
                message = '{% trans "This is a reminder about your transportation fee payment." %}';
                break;
        }
        
        $('#notification_subject').val(subject);
        $('#notification_message').val(message);
    });
});
</script>
{% endblock %}                      <td>
                                                        <span class="badge badge-info">
                                                            {{ notification.get_notification_type_display }}
                                                        </span>
                                                    </td>
                                                    <td>{{ notification.get_channel_display }}</td>
                                                    <td>{{ notification.subject }}</td>
                                                    <td>
                                                        <span class="badge badge-{% if notification.status == 'delivered' %}success{% elif notification.status == 'sent' %}primary{% elif notification.status == 'failed' %}danger{% else %}secondary{% endif %}">
                                                            {{ notification.get_status_display }}
                                                        </span>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% else %}
                                    <div class="text-center text-muted py-4">
                                        <i class="fas fa-bell-slash fa-3x mb-3"></i>
                                        <p>{% trans "No notifications found" %}</p>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
    
    // Auto-refresh attendance data every 30 seconds
    setInterval(function() {
        // You can add AJAX call here to refresh attendance data
    }, 30000);
});
</script>
{% endblock %}