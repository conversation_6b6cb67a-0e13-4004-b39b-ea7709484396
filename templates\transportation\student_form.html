{% extends 'base.html' %}
{% load i18n %}

{% block title %}
    {% if object %}
        {% trans "Edit Student Transportation" %}
    {% else %}
        {% trans "Assign Student to Transportation" %}
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        {% if object %}
                            {% trans "Edit Student Transportation Assignment" %}
                        {% else %}
                            {% trans "Assign Student to Transportation" %}
                        {% endif %}
                    </h3>
                </div>
                
                <form method="post" class="card-body">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.student.id_for_label }}">{{ form.student.label }}</label>
                                {{ form.student }}
                                {% if form.student.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.student.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.route.id_for_label }}">{{ form.route.label }}</label>
                                {{ form.route }}
                                {% if form.route.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.route.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.pickup_stop.id_for_label }}">{{ form.pickup_stop.label }}</label>
                                {{ form.pickup_stop }}
                                {% if form.pickup_stop.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.pickup_stop.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.dropoff_stop.id_for_label }}">{{ form.dropoff_stop.label }}</label>
                                {{ form.dropoff_stop }}
                                {% if form.dropoff_stop.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.dropoff_stop.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="{{ form.status.id_for_label }}">{{ form.status.label }}</label>
                                {{ form.status }}
                                {% if form.status.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.status.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="{{ form.start_date.id_for_label }}">{{ form.start_date.label }}</label>
                                {{ form.start_date }}
                                {% if form.start_date.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.start_date.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="{{ form.end_date.id_for_label }}">{{ form.end_date.label }}</label>
                                {{ form.end_date }}
                                {% if form.end_date.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.end_date.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.monthly_fee.id_for_label }}">{{ form.monthly_fee.label }}</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    {{ form.monthly_fee }}
                                </div>
                                {% if form.monthly_fee.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.monthly_fee.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.emergency_contact_name.id_for_label }}">{{ form.emergency_contact_name.label }}</label>
                                {{ form.emergency_contact_name }}
                                {% if form.emergency_contact_name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.emergency_contact_name.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.emergency_contact_phone.id_for_label }}">{{ form.emergency_contact_phone.label }}</label>
                                {{ form.emergency_contact_phone }}
                                {% if form.emergency_contact_phone.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.emergency_contact_phone.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.special_needs.id_for_label }}">{{ form.special_needs.label }}</label>
                        {{ form.special_needs }}
                        {% if form.special_needs.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.special_needs.errors }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.notes.id_for_label }}">{{ form.notes.label }}</label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.notes.errors }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            {% if object %}
                                {% trans "Update Assignment" %}
                            {% else %}
                                {% trans "Create Assignment" %}
                            {% endif %}
                        </button>
                        <a href="{% url 'transportation:student_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> {% trans "Cancel" %}
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-populate monthly fee when route is selected
    const routeSelect = document.getElementById('{{ form.route.id_for_label }}');
    const feeInput = document.getElementById('{{ form.monthly_fee.id_for_label }}');
    
    if (routeSelect && feeInput) {
        routeSelect.addEventListener('change', function() {
            if (this.value) {
                // You could make an AJAX call here to get the route's default fee
                // For now, we'll leave it as is
            }
        });
    }
    
    // Filter stops based on selected route
    const pickupSelect = document.getElementById('{{ form.pickup_stop.id_for_label }}');
    const dropoffSelect = document.getElementById('{{ form.dropoff_stop.id_for_label }}');
    
    if (routeSelect && pickupSelect && dropoffSelect) {
        routeSelect.addEventListener('change', function() {
            const routeId = this.value;
            if (routeId) {
                // Make AJAX call to get route stops
                fetch(`/transportation/ajax/route-stops/${routeId}/`)
                    .then(response => response.json())
                    .then(data => {
                        // Clear existing options
                        pickupSelect.innerHTML = '<option value="">{% trans "Select pickup stop" %}</option>';
                        dropoffSelect.innerHTML = '<option value="">{% trans "Select drop-off stop" %}</option>';
                        
                        // Add new options
                        data.stops.forEach(stop => {
                            const option1 = new Option(stop.name, stop.id);
                            const option2 = new Option(stop.name, stop.id);
                            pickupSelect.add(option1);
                            dropoffSelect.add(option2);
                        });
                    })
                    .catch(error => {
                        console.error('Error fetching route stops:', error);
                    });
            }
        });
    }
});
</script>
{% endblock %}