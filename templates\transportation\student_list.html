{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Student Transportation" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">{% trans "Student Transportation Assignments" %}</h3>
                    <a href="{% url 'transportation:student_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> {% trans "Assign Student" %}
                    </a>
                </div>
                
                <div class="card-body">
                    <!-- Search and Filter Form -->
                    <form method="get" class="mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <input type="text" name="search" class="form-control" 
                                       placeholder="{% trans 'Search students...' %}" 
                                       value="{{ request.GET.search }}">
                            </div>
                            <div class="col-md-3">
                                <select name="status" class="form-control">
                                    <option value="">{% trans "All Statuses" %}</option>
                                    <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>
                                        {% trans "Active" %}
                                    </option>
                                    <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>
                                        {% trans "Inactive" %}
                                    </option>
                                    <option value="suspended" {% if request.GET.status == 'suspended' %}selected{% endif %}>
                                        {% trans "Suspended" %}
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select name="route" class="form-control">
                                    <option value="">{% trans "All Routes" %}</option>
                                    {% for route in routes %}
                                        <option value="{{ route.id }}" 
                                                {% if request.GET.route == route.id|stringformat:"s" %}selected{% endif %}>
                                            {{ route.name }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-secondary">
                                    <i class="fas fa-search"></i> {% trans "Search" %}
                                </button>
                            </div>
                        </div>
                    </form>
                    
                    <!-- Results Table -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Student" %}</th>
                                    <th>{% trans "Route" %}</th>
                                    <th>{% trans "Pickup Stop" %}</th>
                                    <th>{% trans "Drop-off Stop" %}</th>
                                    <th>{% trans "Monthly Fee" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for assignment in assignments %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div>
                                                <strong>{{ assignment.student.first_name }} {{ assignment.student.last_name }}</strong><br>
                                                <small class="text-muted">{{ assignment.student.student_id }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-info">{{ assignment.route.code }}</span><br>
                                        <small>{{ assignment.route.name }}</small>
                                    </td>
                                    <td>{{ assignment.pickup_stop.name }}</td>
                                    <td>{{ assignment.dropoff_stop.name }}</td>
                                    <td>${{ assignment.monthly_fee }}</td>
                                    <td>
                                        <span class="badge badge-{% if assignment.status == 'active' %}success{% elif assignment.status == 'suspended' %}warning{% else %}secondary{% endif %}">
                                            {{ assignment.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'transportation:student_detail' assignment.pk %}" 
                                               class="btn btn-sm btn-outline-primary" title="{% trans 'View Details' %}">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'transportation:student_update' assignment.pk %}" 
                                               class="btn btn-sm btn-outline-secondary" title="{% trans 'Edit' %}">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center text-muted">
                                        {% trans "No student transportation assignments found." %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if is_paginated %}
                    <nav aria-label="{% trans 'Page navigation' %}">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.route %}&route={{ request.GET.route }}{% endif %}">
                                        {% trans "First" %}
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.route %}&route={{ request.GET.route }}{% endif %}">
                                        {% trans "Previous" %}
                                    </a>
                                </li>
                            {% endif %}
                            
                            <li class="page-item active">
                                <span class="page-link">
                                    {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>
                            
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.route %}&route={{ request.GET.route }}{% endif %}">
                                        {% trans "Next" %}
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.route %}&route={{ request.GET.route }}{% endif %}">
                                        {% trans "Last" %}
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}