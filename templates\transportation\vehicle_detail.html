{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Vehicle Details" %} - {{ vehicle.vehicle_number }}{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">{% trans "Dashboard" %}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'transportation:dashboard' %}">{% trans "Transportation" %}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'transportation:vehicle_list' %}">{% trans "Vehicles" %}</a></li>
        <li class="breadcrumb-item active">{{ vehicle.vehicle_number }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-bus me-2"></i>
                        {% trans "Vehicle Details" %} - {{ vehicle.vehicle_number }}
                    </h4>
                    <div class="btn-group">
                        <a href="{% url 'transportation:vehicle_update' vehicle.pk %}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit me-1"></i>{% trans "Edit" %}
                        </a>
                        <a href="{% url 'transportation:vehicle_tracking' vehicle.pk %}" class="btn btn-info btn-sm">
                            <i class="fas fa-map-marker-alt me-1"></i>{% trans "Track" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>{% trans "Basic Information" %}</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>{% trans "Vehicle Number" %}:</strong></td>
                                    <td>{{ vehicle.vehicle_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "License Plate" %}:</strong></td>
                                    <td>{{ vehicle.license_plate }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Make" %}:</strong></td>
                                    <td>{{ vehicle.make }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Model" %}:</strong></td>
                                    <td>{{ vehicle.model }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Year" %}:</strong></td>
                                    <td>{{ vehicle.year }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Capacity" %}:</strong></td>
                                    <td>{{ vehicle.capacity }} {% trans "students" %}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Status" %}:</strong></td>
                                    <td>
                                        <span class="badge bg-{% if vehicle.status == 'active' %}success{% elif vehicle.status == 'maintenance' %}warning{% else %}danger{% endif %}">
                                            {{ vehicle.get_status_display }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>{% trans "Maintenance & Insurance" %}</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>{% trans "Last Maintenance" %}:</strong></td>
                                    <td>{{ vehicle.last_maintenance_date|default:"N/A" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Next Maintenance" %}:</strong></td>
                                    <td>{{ vehicle.next_maintenance_date|default:"N/A" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Insurance Expiry" %}:</strong></td>
                                    <td>{{ vehicle.insurance_expiry|default:"N/A" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Fuel Type" %}:</strong></td>
                                    <td>{{ vehicle.get_fuel_type_display|default:"N/A" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Mileage" %}:</strong></td>
                                    <td>{{ vehicle.current_mileage|default:"N/A" }} km</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    {% if assigned_routes %}
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>{% trans "Assigned Routes" %}</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>{% trans "Route Name" %}</th>
                                            <th>{% trans "Code" %}</th>
                                            <th>{% trans "Status" %}</th>
                                            <th>{% trans "Students" %}</th>
                                            <th>{% trans "Actions" %}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for route in assigned_routes %}
                                        <tr>
                                            <td>{{ route.name }}</td>
                                            <td>{{ route.code }}</td>
                                            <td>
                                                <span class="badge bg-{% if route.status == 'active' %}success{% else %}secondary{% endif %}">
                                                    {{ route.get_status_display }}
                                                </span>
                                            </td>
                                            <td>{{ route.current_occupancy }}/{{ route.max_capacity }}</td>
                                            <td>
                                                <a href="{% url 'transportation:route_detail' route.pk %}" class="btn btn-sm btn-outline-primary">
                                                    {% trans "View" %}
                                                </a>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    {% if recent_tracking %}
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>{% trans "Recent GPS Tracking" %}</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>{% trans "Timestamp" %}</th>
                                            <th>{% trans "Location" %}</th>
                                            <th>{% trans "Speed" %}</th>
                                            <th>{% trans "Engine Status" %}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for tracking in recent_tracking %}
                                        <tr>
                                            <td>{{ tracking.timestamp }}</td>
                                            <td>{{ tracking.latitude }}, {{ tracking.longitude }}</td>
                                            <td>{{ tracking.speed_kmh }} km/h</td>
                                            <td>
                                                <span class="badge bg-{% if tracking.engine_status %}success{% else %}danger{% endif %}">
                                                    {% if tracking.engine_status %}{% trans "On" %}{% else %}{% trans "Off" %}{% endif %}
                                                </span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}