{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load widget_tweaks %}

{% block title %}
    {% if object %}
        {% trans "Edit Vehicle" %} - {{ object.vehicle_number }}
    {% else %}
        {% trans "Add Vehicle" %}
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">
                    {% if object %}
                        {% trans "Edit Vehicle" %} - {{ object.vehicle_number }}
                    {% else %}
                        {% trans "Add Vehicle" %}
                    {% endif %}
                </h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'accounts:dashboard' %}">{% trans "Dashboard" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'transportation:dashboard' %}">{% trans "Transportation" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'transportation:vehicle_list' %}">{% trans "Vehicles" %}</a></li>
                        <li class="breadcrumb-item active">
                            {% if object %}{% trans "Edit" %}{% else %}{% trans "Add" %}{% endif %}
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bus me-2"></i>
                        {% if object %}{% trans "Edit Vehicle" %}{% else %}{% trans "Add New Vehicle" %}{% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <!-- Basic Information -->
                        <h6 class="mb-3 text-primary">
                            <i class="fas fa-info-circle me-1"></i>
                            {% trans "Basic Information" %}
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.vehicle_number.id_for_label }}" class="form-label">
                                        {{ form.vehicle_number.label }}
                                        {% if form.vehicle_number.field.required %}<span class="text-danger">*</span>{% endif %}
                                    </label>
                                    {{ form.vehicle_number|add_class:"form-control" }}
                                    {% if form.vehicle_number.errors %}
                                        <div class="text-danger small">{{ form.vehicle_number.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.license_plate.id_for_label }}" class="form-label">
                                        {{ form.license_plate.label }}
                                        {% if form.license_plate.field.required %}<span class="text-danger">*</span>{% endif %}
                                    </label>
                                    {{ form.license_plate|add_class:"form-control" }}
                                    {% if form.license_plate.errors %}
                                        <div class="text-danger small">{{ form.license_plate.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.vehicle_type.id_for_label }}" class="form-label">
                                        {{ form.vehicle_type.label }}
                                    </label>
                                    {{ form.vehicle_type|add_class:"form-select" }}
                                    {% if form.vehicle_type.errors %}
                                        <div class="text-danger small">{{ form.vehicle_type.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.make.id_for_label }}" class="form-label">
                                        {{ form.make.label }}
                                        {% if form.make.field.required %}<span class="text-danger">*</span>{% endif %}
                                    </label>
                                    {{ form.make|add_class:"form-control" }}
                                    {% if form.make.errors %}
                                        <div class="text-danger small">{{ form.make.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.model.id_for_label }}" class="form-label">
                                        {{ form.model.label }}
                                        {% if form.model.field.required %}<span class="text-danger">*</span>{% endif %}
                                    </label>
                                    {{ form.model|add_class:"form-control" }}
                                    {% if form.model.errors %}
                                        <div class="text-danger small">{{ form.model.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.year.id_for_label }}" class="form-label">
                                        {{ form.year.label }}
                                        {% if form.year.field.required %}<span class="text-danger">*</span>{% endif %}
                                    </label>
                                    {{ form.year|add_class:"form-control" }}
                                    {% if form.year.errors %}
                                        <div class="text-danger small">{{ form.year.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.capacity.id_for_label }}" class="form-label">
                                        {{ form.capacity.label }}
                                        {% if form.capacity.field.required %}<span class="text-danger">*</span>{% endif %}
                                    </label>
                                    {{ form.capacity|add_class:"form-control" }}
                                    {% if form.capacity.errors %}
                                        <div class="text-danger small">{{ form.capacity.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.fuel_type.id_for_label }}" class="form-label">
                                        {{ form.fuel_type.label }}
                                    </label>
                                    {{ form.fuel_type|add_class:"form-select" }}
                                    {% if form.fuel_type.errors %}
                                        <div class="text-danger small">{{ form.fuel_type.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Status and Dates -->
                        <h6 class="mb-3 text-primary">
                            <i class="fas fa-calendar me-1"></i>
                            {% trans "Status and Important Dates" %}
                        </h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.status.id_for_label }}" class="form-label">
                                        {{ form.status.label }}
                                    </label>
                                    {{ form.status|add_class:"form-select" }}
                                    {% if form.status.errors %}
                                        <div class="text-danger small">{{ form.status.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.purchase_date.id_for_label }}" class="form-label">
                                        {{ form.purchase_date.label }}
                                    </label>
                                    {{ form.purchase_date|add_class:"form-control" }}
                                    {% if form.purchase_date.errors %}
                                        <div class="text-danger small">{{ form.purchase_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.insurance_expiry.id_for_label }}" class="form-label">
                                        {{ form.insurance_expiry.label }}
                                    </label>
                                    {{ form.insurance_expiry|add_class:"form-control" }}
                                    {% if form.insurance_expiry.errors %}
                                        <div class="text-danger small">{{ form.insurance_expiry.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.registration_expiry.id_for_label }}" class="form-label">
                                        {{ form.registration_expiry.label }}
                                    </label>
                                    {{ form.registration_expiry|add_class:"form-control" }}
                                    {% if form.registration_expiry.errors %}
                                        <div class="text-danger small">{{ form.registration_expiry.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.last_maintenance_date.id_for_label }}" class="form-label">
                                        {{ form.last_maintenance_date.label }}
                                    </label>
                                    {{ form.last_maintenance_date|add_class:"form-control" }}
                                    {% if form.last_maintenance_date.errors %}
                                        <div class="text-danger small">{{ form.last_maintenance_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.next_maintenance_date.id_for_label }}" class="form-label">
                                        {{ form.next_maintenance_date.label }}
                                    </label>
                                    {{ form.next_maintenance_date|add_class:"form-control" }}
                                    {% if form.next_maintenance_date.errors %}
                                        <div class="text-danger small">{{ form.next_maintenance_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- GPS and Notes -->
                        <h6 class="mb-3 text-primary">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            {% trans "GPS and Additional Information" %}
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.gps_device_id.id_for_label }}" class="form-label">
                                        {{ form.gps_device_id.label }}
                                    </label>
                                    {{ form.gps_device_id|add_class:"form-control" }}
                                    {% if form.gps_device_id.errors %}
                                        <div class="text-danger small">{{ form.gps_device_id.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.notes.id_for_label }}" class="form-label">
                                        {{ form.notes.label }}
                                    </label>
                                    {{ form.notes|add_class:"form-control" }}
                                    {% if form.notes.errors %}
                                        <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{% url 'transportation:vehicle_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>
                                        {% trans "Cancel" %}
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        {% if object %}{% trans "Update Vehicle" %}{% else %}{% trans "Add Vehicle" %}{% endif %}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Set date input types
    $('input[name="purchase_date"], input[name="insurance_expiry"], input[name="registration_expiry"], input[name="last_maintenance_date"], input[name="next_maintenance_date"]').attr('type', 'date');
});
</script>
{% endblock %}