{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Vehicles" %} - {% trans "Transportation" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">{% trans "Vehicles" %}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'accounts:dashboard' %}">{% trans "Dashboard" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'transportation:dashboard' %}">{% trans "Transportation" %}</a></li>
                        <li class="breadcrumb-item active">{% trans "Vehicles" %}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <form method="get" class="row g-3">
                                <div class="col-md-4">
                                    <input type="text" name="search" class="form-control" 
                                           placeholder="{% trans 'Search vehicles...' %}" 
                                           value="{{ request.GET.search }}">
                                </div>
                                <div class="col-md-3">
                                    <select name="status" class="form-select">
                                        <option value="">{% trans "All Status" %}</option>
                                        <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>{% trans "Active" %}</option>
                                        <option value="maintenance" {% if request.GET.status == 'maintenance' %}selected{% endif %}>{% trans "Maintenance" %}</option>
                                        <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>{% trans "Inactive" %}</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> {% trans "Filter" %}
                                    </button>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{% url 'transportation:vehicle_create' %}" class="btn btn-success">
                                <i class="fas fa-plus me-1"></i>
                                {% trans "Add Vehicle" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vehicles List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bus me-2"></i>
                        {% trans "Vehicle Fleet" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if vehicles %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>{% trans "Vehicle" %}</th>
                                        <th>{% trans "Type" %}</th>
                                        <th>{% trans "Capacity" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Insurance" %}</th>
                                        <th>{% trans "Maintenance" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for vehicle in vehicles %}
                                    <tr>
                                        <td>
                                            <div>
                                                <h6 class="mb-0">{{ vehicle.vehicle_number }}</h6>
                                                <small class="text-muted">{{ vehicle.license_plate }}</small>
                                                <br>
                                                <small class="text-muted">{{ vehicle.make }} {{ vehicle.model }} ({{ vehicle.year }})</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-light text-dark">
                                                {{ vehicle.get_vehicle_type_display }}
                                            </span>
                                        </td>
                                        <td>{{ vehicle.capacity }} {% trans "seats" %}</td>
                                        <td>
                                            {% if vehicle.status == 'active' %}
                                                <span class="badge bg-success">{% trans "Active" %}</span>
                                            {% elif vehicle.status == 'maintenance' %}
                                                <span class="badge bg-warning">{% trans "Maintenance" %}</span>
                                            {% elif vehicle.status == 'inactive' %}
                                                <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                                            {% else %}
                                                <span class="badge bg-danger">{% trans "Retired" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if vehicle.insurance_expiry %}
                                                {% if vehicle.is_insurance_expiring_soon %}
                                                    <span class="badge bg-warning">
                                                        {{ vehicle.insurance_expiry|date:"M d, Y" }}
                                                    </span>
                                                {% else %}
                                                    <span class="text-muted">{{ vehicle.insurance_expiry|date:"M d, Y" }}</span>
                                                {% endif %}
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if vehicle.next_maintenance_date %}
                                                {% if vehicle.is_maintenance_due %}
                                                    <span class="badge bg-danger">
                                                        {% trans "Due" %} {{ vehicle.next_maintenance_date|date:"M d" }}
                                                    </span>
                                                {% else %}
                                                    <span class="text-muted">{{ vehicle.next_maintenance_date|date:"M d, Y" }}</span>
                                                {% endif %}
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{% url 'transportation:vehicle_detail' vehicle.pk %}" 
                                                   class="btn btn-outline-primary" title="{% trans 'View Details' %}">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{% url 'transportation:vehicle_edit' vehicle.pk %}" 
                                                   class="btn btn-outline-secondary" title="{% trans 'Edit' %}">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'transportation:vehicle_tracking' vehicle.pk %}" 
                                                   class="btn btn-outline-info" title="{% trans 'GPS Tracking' %}">
                                                    <i class="fas fa-map-marker-alt"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if is_paginated %}
                        <nav aria-label="{% trans 'Vehicle pagination' %}">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1">&laquo; {% trans "First" %}</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                                    </li>
                                {% endif %}

                                <li class="page-item active">
                                    <span class="page-link">
                                        {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">{% trans "Last" %} &raquo;</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-bus fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{% trans "No vehicles found" %}</h5>
                            <p class="text-muted">{% trans "Start by adding your first vehicle to the fleet." %}</p>
                            <a href="{% url 'transportation:vehicle_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                {% trans "Add First Vehicle" %}
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}