{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Vehicle Tracking" %} - {{ vehicle.vehicle_number }}{% endblock %}

{% block extra_css %}
<style>
    #map {
        height: 400px;
        width: 100%;
    }
    .tracking-info {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">{% trans "Dashboard" %}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'transportation:dashboard' %}">{% trans "Transportation" %}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'transportation:vehicle_list' %}">{% trans "Vehicles" %}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'transportation:vehicle_detail' vehicle.pk %}">{{ vehicle.vehicle_number }}</a></li>
        <li class="breadcrumb-item active">{% trans "Tracking" %}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        {% trans "Vehicle Tracking" %} - {{ vehicle.vehicle_number }}
                    </h4>
                    <div class="btn-group">
                        <button id="refreshLocation" class="btn btn-primary btn-sm">
                            <i class="fas fa-sync-alt me-1"></i>{% trans "Refresh" %}
                        </button>
                        <a href="{% url 'transportation:vehicle_detail' vehicle.pk %}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left me-1"></i>{% trans "Back" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if current_location %}
                    <div class="tracking-info">
                        <div class="row">
                            <div class="col-md-3">
                                <h6>{% trans "Current Location" %}</h6>
                                <p class="mb-0">{{ current_location.latitude }}, {{ current_location.longitude }}</p>
                            </div>
                            <div class="col-md-2">
                                <h6>{% trans "Speed" %}</h6>
                                <p class="mb-0">{{ current_location.speed_kmh }} km/h</p>
                            </div>
                            <div class="col-md-2">
                                <h6>{% trans "Engine" %}</h6>
                                <p class="mb-0">
                                    <span class="badge bg-{% if current_location.engine_status %}success{% else %}danger{% endif %}">
                                        {% if current_location.engine_status %}{% trans "On" %}{% else %}{% trans "Off" %}{% endif %}
                                    </span>
                                </p>
                            </div>
                            <div class="col-md-3">
                                <h6>{% trans "Last Update" %}</h6>
                                <p class="mb-0">{{ current_location.timestamp }}</p>
                            </div>
                            <div class="col-md-2">
                                <h6>{% trans "Status" %}</h6>
                                <p class="mb-0">
                                    <span class="badge bg-success">{% trans "Online" %}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        {% trans "No current location data available for this vehicle." %}
                    </div>
                    {% endif %}

                    <!-- Map Container -->
                    <div id="map" class="mb-4"></div>

                    <!-- Tracking History -->
                    {% if tracking_data %}
                    <div class="row">
                        <div class="col-12">
                            <h5>{% trans "Tracking History (Last 24 Hours)" %}</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>{% trans "Time" %}</th>
                                            <th>{% trans "Location" %}</th>
                                            <th>{% trans "Speed" %}</th>
                                            <th>{% trans "Engine" %}</th>
                                            <th>{% trans "Fuel Level" %}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for track in tracking_data %}
                                        <tr>
                                            <td>{{ track.timestamp|date:"H:i" }}</td>
                                            <td>{{ track.latitude|floatformat:6 }}, {{ track.longitude|floatformat:6 }}</td>
                                            <td>{{ track.speed_kmh }} km/h</td>
                                            <td>
                                                <span class="badge bg-{% if track.engine_status %}success{% else %}danger{% endif %}">
                                                    {% if track.engine_status %}{% trans "On" %}{% else %}{% trans "Off" %}{% endif %}
                                                </span>
                                            </td>
                                            <td>{{ track.fuel_level|default:"N/A" }}%</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Initialize map (using OpenStreetMap/Leaflet as example)
let map;
let vehicleMarker;

function initMap() {
    // Default center (you can set to school location)
    let defaultLat = {% if current_location %}{{ current_location.latitude }}{% else %}24.7136{% endif %};
    let defaultLng = {% if current_location %}{{ current_location.longitude }}{% else %}46.6753{% endif %};
    
    // This is a placeholder for map initialization
    // You would integrate with your preferred mapping service (Google Maps, Leaflet, etc.)
    document.getElementById('map').innerHTML = `
        <div class="d-flex align-items-center justify-content-center h-100 bg-light">
            <div class="text-center">
                <i class="fas fa-map fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">{% trans "Map Integration Required" %}</h5>
                <p class="text-muted">{% trans "Please integrate with your preferred mapping service" %}</p>
                {% if current_location %}
                <p><strong>{% trans "Current Location" %}:</strong> {{ current_location.latitude }}, {{ current_location.longitude }}</p>
                {% endif %}
            </div>
        </div>
    `;
}

// Refresh location
document.getElementById('refreshLocation').addEventListener('click', function() {
    // AJAX call to refresh vehicle location
    fetch(`/transportation/ajax/vehicle-location/{{ vehicle.id }}/`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('{% trans "Error loading location data" %}');
            } else {
                // Update map and info
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{% trans "Error refreshing location" %}');
        });
});

// Initialize map when page loads
document.addEventListener('DOMContentLoaded', function() {
    initMap();
});
</script>
{% endblock %}