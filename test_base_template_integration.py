#!/usr/bin/env python
"""
Test script for base template school switcher integration
"""

import os
import sys
import django
from django.test import TestCase, Client
from django.contrib.auth import get_user_model

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

from core.models import School

User = get_user_model()

def test_base_template_integration():
    """Test that base template properly includes school switcher"""
    
    print("Testing Base Template School Switcher Integration...")
    
    # Create test client
    client = Client()
    
    # Create test user
    try:
        user = User.objects.get(username='basetestuser')
        user.set_password('testpass123')
        user.is_superuser = True
        user.save()
    except User.DoesNotExist:
        user = User.objects.create_user(
            username='basetestuser',
            email='<EMAIL>',
            password='testpass123',
            is_superuser=True
        )
    
    # Get schools
    schools = School.objects.filter(is_active=True)[:2]
    if schools.count() < 2:
        # Create additional school if needed
        school2 = School.objects.create(
            code='BASE002',
            name='Base Test School 2',
            address='456 Base Avenue',
            phone='+1234567891',
            email='<EMAIL>',
            principal_name='Principal Base Two',
            established_date='2021-01-01'
        )
        schools = School.objects.filter(is_active=True)[:2]
    
    school1 = schools[0]
    print(f"Using school: {school1.name}")
    
    # Login user and select school
    login_success = client.login(username='basetestuser', password='testpass123')
    print(f"Login successful: {login_success}")
    
    response = client.post('/core/school/select/', {
        'school_id': str(school1.id),
        'next': '/accounts/dashboard/'
    }, HTTP_HOST='localhost')
    
    print(f"School selection: {response.status_code}")
    
    # Test 1: Check if school switcher appears in base template
    print("\n1. Testing school switcher in base template...")
    try:
        # Access a page that uses base template
        response = client.get('/accounts/dashboard/', HTTP_HOST='localhost')
        
        if response.status_code == 200:
            content = response.content.decode()
            
            # Check for school switcher elements
            switcher_checks = [
                ('School switcher component', 'school_switcher.html'),
                ('School switcher dropdown', 'schoolSwitcher'),
                ('Current school display', 'currentSchoolName'),
                ('Available schools', 'Available Schools'),
                ('School switching JavaScript', 'school-option'),
                ('School icons', 'fas fa-school'),
                ('University icons', 'fas fa-university'),
                ('Dropdown menu', 'dropdown-menu dropdown-menu-end'),
            ]
            
            for check_name, check_pattern in switcher_checks:
                if check_pattern in content:
                    print(f"   ✓ {check_name} found in base template")
                else:
                    print(f"   ❌ {check_name} missing from base template")
                    
        else:
            print(f"   ❌ Could not access page using base template: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Base template integration test failed: {e}")
    
    # Test 2: Test school switcher visibility conditions
    print("\n2. Testing school switcher visibility conditions...")
    try:
        response = client.get('/accounts/dashboard/', HTTP_HOST='localhost')
        
        if response.status_code == 200:
            content = response.content.decode()
            
            # Check if current school is displayed
            if school1.name in content or school1.code in content:
                print("   ✓ Current school name/code displayed in navbar")
            else:
                print("   ❌ Current school not displayed in navbar")
            
            # Check if switcher is visible (should be if multiple schools)
            if schools.count() > 1:
                if 'schoolSwitcher' in content:
                    print("   ✓ School switcher visible with multiple schools")
                else:
                    print("   ❌ School switcher not visible with multiple schools")
            else:
                print("   → Only one school available, switcher may be hidden")
                
        else:
            print(f"   ❌ Could not test visibility: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Visibility conditions test failed: {e}")
    
    # Test 3: Test school switcher across different pages
    print("\n3. Testing school switcher across different pages...")
    try:
        test_pages = [
            ('/accounts/dashboard/', 'Dashboard'),
            ('/students/dashboard/', 'Students Dashboard'),
            ('/library/dashboard/', 'Library Dashboard'),
            ('/academics/dashboard/', 'Academics Dashboard'),
        ]
        
        for url, page_name in test_pages:
            try:
                response = client.get(url, HTTP_HOST='localhost')
                
                if response.status_code == 200:
                    content = response.content.decode()
                    
                    if 'schoolSwitcher' in content:
                        print(f"   ✓ {page_name}: School switcher present")
                    else:
                        print(f"   ❌ {page_name}: School switcher missing")
                elif response.status_code == 302:
                    print(f"   → {page_name}: Redirected (normal)")
                else:
                    print(f"   → {page_name}: Status {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ {page_name}: Error {e}")
                
    except Exception as e:
        print(f"   ❌ Cross-page test failed: {e}")
    
    # Test 4: Test responsive design elements
    print("\n4. Testing responsive design in base template...")
    try:
        response = client.get('/accounts/dashboard/', HTTP_HOST='localhost')
        
        if response.status_code == 200:
            content = response.content.decode()
            
            # Check for responsive classes
            responsive_checks = [
                ('Mobile display class', 'd-md-none'),
                ('Desktop display class', 'd-none d-md-inline'),
                ('Responsive navbar', 'navbar-toggler'),
                ('Bootstrap grid', 'container-fluid'),
                ('Responsive dropdown', 'dropdown-menu-end'),
            ]
            
            for check_name, check_pattern in responsive_checks:
                if check_pattern in content:
                    print(f"   ✓ {check_name} found")
                else:
                    print(f"   ❌ {check_name} missing")
                    
        else:
            print(f"   ❌ Could not test responsive design: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Responsive design test failed: {e}")
    
    # Test 5: Test school switcher JavaScript functionality
    print("\n5. Testing school switcher JavaScript in base template...")
    try:
        response = client.get('/accounts/dashboard/', HTTP_HOST='localhost')
        
        if response.status_code == 200:
            content = response.content.decode()
            
            # Check for JavaScript functionality
            js_checks = [
                ('CSRF token function', 'getCookie'),
                ('Toast notifications', 'showToast'),
                ('Event listeners', 'addEventListener'),
                ('AJAX fetch calls', 'fetch('),
                ('Error handling', 'catch(error'),
                ('Bootstrap Toast', 'bootstrap.Toast'),
            ]
            
            for check_name, check_pattern in js_checks:
                if check_pattern in content:
                    print(f"   ✓ {check_name} implemented")
                else:
                    print(f"   ❌ {check_name} missing")
                    
        else:
            print(f"   ❌ Could not test JavaScript: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ JavaScript functionality test failed: {e}")
    
    print("\n✅ Base template school switcher integration tests completed!")
    
    # Cleanup
    User.objects.filter(username='basetestuser').delete()
    School.objects.filter(code='BASE002').delete()
    
    return True

if __name__ == '__main__':
    try:
        test_base_template_integration()
        print("\n🎉 Base template school switcher integration is working correctly!")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)