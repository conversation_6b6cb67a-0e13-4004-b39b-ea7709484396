#!/usr/bin/env python
"""
Basic URL testing script for School ERP system
Tests core functionality and basic URL accessibility
"""
import os
import sys
import django
from django.test import Client
from django.contrib.auth import get_user_model

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

def test_basic_functionality():
    """Test basic system functionality"""
    print("🧪 Testing basic system functionality...")
    
    # Test database connectivity
    try:
        User = get_user_model()
        user_count = User.objects.count()
        print(f"✅ Database connection: {user_count} users found")
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False
    
    # Test admin user exists
    try:
        admin_user = User.objects.get(username='admin')
        print(f"✅ Admin user exists: {admin_user.email}")
    except User.DoesNotExist:
        print("❌ Admin user not found")
        return False
    
    # Test models are working
    try:
        from core.models import School
        from students.models import Student, Grade
        from academics.models import Subject, Teacher
        
        school_count = School.objects.count()
        student_count = Student.objects.count()
        grade_count = Grade.objects.count()
        subject_count = Subject.objects.count()
        teacher_count = Teacher.objects.count()
        
        print(f"✅ Data populated:")
        print(f"   • Schools: {school_count}")
        print(f"   • Students: {student_count}")
        print(f"   • Grades: {grade_count}")
        print(f"   • Subjects: {subject_count}")
        print(f"   • Teachers: {teacher_count}")
        
    except Exception as e:
        print(f"❌ Model testing failed: {e}")
        return False
    
    # Test Django test client
    try:
        client = Client()
        
        # Test admin login
        login_success = client.login(username='admin', password='admin123')
        if login_success:
            print("✅ Admin login successful")
        else:
            print("❌ Admin login failed")
            return False
            
        # Test basic URL patterns
        basic_urls = [
            '/admin/',
            '/accounts/login/',
        ]
        
        for url in basic_urls:
            try:
                response = client.get(url, follow=True)
                if response.status_code in [200, 302]:
                    print(f"✅ {url} - Status: {response.status_code}")
                else:
                    print(f"❌ {url} - Status: {response.status_code}")
            except Exception as e:
                print(f"❌ {url} - Error: {e}")
        
    except Exception as e:
        print(f"❌ Client testing failed: {e}")
        return False
    
    return True

def test_user_authentication():
    """Test user authentication for different roles"""
    print("\n🔐 Testing user authentication...")
    
    User = get_user_model()
    client = Client()
    
    # Test different user types
    test_users = [
        ('admin', 'admin123', 'admin'),
        ('teacher01', 'password123', 'teacher'),
        ('parent001', 'password123', 'parent'),
        ('student0001', 'password123', 'student'),
    ]
    
    for username, password, user_type in test_users:
        try:
            user = User.objects.get(username=username)
            login_success = client.login(username=username, password=password)
            
            if login_success:
                print(f"✅ {user_type.title()} login successful: {username}")
                
                # Test dashboard access
                response = client.get('/accounts/dashboard/', follow=True)
                if response.status_code == 200:
                    print(f"   └─ Dashboard accessible")
                else:
                    print(f"   └─ Dashboard status: {response.status_code}")
                    
                client.logout()
            else:
                print(f"❌ {user_type.title()} login failed: {username}")
                
        except User.DoesNotExist:
            print(f"❌ {user_type.title()} user not found: {username}")
        except Exception as e:
            print(f"❌ {user_type.title()} test error: {e}")

def test_data_integrity():
    """Test data relationships and integrity"""
    print("\n🔗 Testing data relationships...")
    
    try:
        from students.models import Student, Class
        from academics.models import Teacher
        
        # Test student-class relationships
        students_with_classes = Student.objects.filter(current_class__isnull=False).count()
        total_students = Student.objects.count()
        print(f"✅ Students with classes: {students_with_classes}/{total_students}")
        
        # Test teacher-subject relationships
        teachers_with_subjects = Teacher.objects.filter(subjects__isnull=False).distinct().count()
        total_teachers = Teacher.objects.count()
        print(f"✅ Teachers with subjects: {teachers_with_subjects}/{total_teachers}")
        
        # Test class capacity
        classes = Class.objects.all()
        for class_obj in classes[:3]:  # Test first 3 classes
            student_count = class_obj.current_students_count
            max_capacity = class_obj.max_students
            print(f"✅ {class_obj}: {student_count}/{max_capacity} students")
            
    except Exception as e:
        print(f"❌ Data integrity test failed: {e}")

def main():
    """Main test function"""
    print("🚀 Starting basic system tests...")
    print("=" * 50)
    
    # Run basic functionality tests
    if not test_basic_functionality():
        print("\n❌ Basic functionality tests failed!")
        sys.exit(1)
    
    # Test user authentication
    test_user_authentication()
    
    # Test data integrity
    test_data_integrity()
    
    print("\n" + "=" * 50)
    print("🎉 Basic system tests completed!")
    print("\n💡 Next steps:")
    print("1. Start the development server: python manage.py runserver")
    print("2. Open browser to: http://127.0.0.1:8000/admin/")
    print("3. Login with: admin / admin123")
    print("4. Explore the system with different user roles")
    print("\n📊 System is ready for use!")

if __name__ == '__main__':
    main()