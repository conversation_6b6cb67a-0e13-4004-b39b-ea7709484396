#!/usr/bin/env python3
"""
Comprehensive Error Handling Test Suite
Tests all aspects of the enhanced error handling and user feedback system
"""

import os
import sys
import django
import asyncio
import json
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

from django.test import TestCase, Client
from django.contrib.auth import get_user_model
User = get_user_model()
from django.urls import reverse
from django.http import JsonResponse
from core.models import School
from library.models import Book, BookBorrowing, Category, Author, Publisher
from students.models import Student

class ErrorHandlingTestSuite:
    """Comprehensive test suite for error handling and user feedback"""
    
    def __init__(self):
        self.client = Client()
        self.test_results = []
        self.setup_test_data()
    
    def setup_test_data(self):
        """Setup test data for error handling tests"""
        print("Setting up test data...")
        
        # Create test school
        self.school = School.objects.get_or_create(
            name="Test School",
            code="TEST001",
            defaults={
                'address': 'Test Address',
                'phone': '1234567890',
                'email': '<EMAIL>'
            }
        )[0]
        
        # Create test user
        self.user = User.objects.get_or_create(
            username='testuser',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'User'
            }
        )[0]
        self.user.set_password('testpass123')
        self.user.save()
        
        # Create test library data
        self.category = Category.objects.get_or_create(
            name="Test Category",
            school=self.school,
            defaults={'code': 'TEST', 'is_active': True}
        )[0]
        
        self.author = Author.objects.get_or_create(
            first_name="Test",
            last_name="Author",
            school=self.school,
            defaults={'is_active': True}
        )[0]
        
        self.publisher = Publisher.objects.get_or_create(
            name="Test Publisher",
            school=self.school,
            defaults={'is_active': True}
        )[0]
        
        self.book = Book.objects.get_or_create(
            title="Test Book",
            school=self.school,
            defaults={
                'isbn': '1234567890123',
                'category': self.category,
                'publisher': self.publisher,
                'is_active': True,
                'total_copies': 1,
                'available_copies': 1,
                'rfid_tag': 'TEST001'
            }
        )[0]
        self.book.authors.add(self.author)
        
        print("✓ Test data setup complete")
    
    def login_user(self):
        """Login test user and set school context"""
        login_success = self.client.login(username='testuser', password='testpass123')
        if login_success:
            # Set school in session
            session = self.client.session
            session['selected_school_id'] = str(self.school.id)
            session.save()
            print("✓ User logged in successfully")
            return True
        else:
            print("✗ Failed to login user")
            return False
    
    def test_health_check_endpoint(self):
        """Test the health check endpoint for connectivity"""
        print("\n--- Testing Health Check Endpoint ---")
        
        try:
            response = self.client.get('/api/health-check/')
            
            if response.status_code == 200:
                print("✓ Health check endpoint responding correctly")
                self.test_results.append(("Health Check", "PASS", "Endpoint accessible"))
            else:
                print(f"✗ Health check returned status {response.status_code}")
                print(f"Response content: {response.content.decode('utf-8')[:200]}")
                self.test_results.append(("Health Check", "FAIL", f"Status {response.status_code}"))
                
        except Exception as e:
            print(f"✗ Health check endpoint error: {e}")
            self.test_results.append(("Health Check", "ERROR", str(e)))
    
    def test_library_borrow_error_handling(self):
        """Test library borrowing error scenarios"""
        print("\n--- Testing Library Borrow Error Handling ---")
        
        if not self.login_user():
            self.test_results.append(("Library Borrow Errors", "SKIP", "Login failed"))
            return
        
        # Test missing required fields
        response = self.client.post('/library/api/borrow/', {})
        
        if response.status_code == 400:
            try:
                data = response.json()
            except:
                print(f"Response content: {response.content.decode('utf-8')[:200]}")
                data = {}
            if 'error' in data and 'required fields' in data['error'].lower():
                print("✓ Missing fields error handled correctly")
                self.test_results.append(("Borrow Missing Fields", "PASS", "Proper error response"))
            else:
                print("✗ Missing fields error not properly formatted")
                self.test_results.append(("Borrow Missing Fields", "FAIL", "Error format incorrect"))
        else:
            print(f"✗ Expected 400 status, got {response.status_code}")
            self.test_results.append(("Borrow Missing Fields", "FAIL", f"Status {response.status_code}"))
        
        # Test invalid book ID
        response = self.client.post('/library/api/borrow/', {
            'book_id': '99999',
            'borrower_type': 'student',
            'borrower_id': 'STU001',
            'borrower_name': 'Test Student'
        })
        
        if response.status_code == 404:
            data = response.json()
            if 'error' in data and 'not found' in data['error'].lower():
                print("✓ Invalid book ID error handled correctly")
                self.test_results.append(("Borrow Invalid Book", "PASS", "Proper 404 response"))
            else:
                print("✗ Invalid book ID error not properly formatted")
                self.test_results.append(("Borrow Invalid Book", "FAIL", "Error format incorrect"))
        else:
            print(f"✗ Expected 404 status, got {response.status_code}")
            self.test_results.append(("Borrow Invalid Book", "FAIL", f"Status {response.status_code}"))
    
    def test_library_return_error_handling(self):
        """Test library return error scenarios"""
        print("\n--- Testing Library Return Error Handling ---")
        
        if not self.login_user():
            self.test_results.append(("Library Return Errors", "SKIP", "Login failed"))
            return
        
        # Test missing borrowing ID
        response = self.client.post('/library/api/return/', {})
        
        if response.status_code == 400:
            data = response.json()
            if 'error' in data and 'required' in data['error'].lower():
                print("✓ Missing borrowing ID error handled correctly")
                self.test_results.append(("Return Missing ID", "PASS", "Proper error response"))
            else:
                print("✗ Missing borrowing ID error not properly formatted")
                self.test_results.append(("Return Missing ID", "FAIL", "Error format incorrect"))
        else:
            print(f"✗ Expected 400 status, got {response.status_code}")
            self.test_results.append(("Return Missing ID", "FAIL", f"Status {response.status_code}"))
        
        # Test invalid borrowing ID
        response = self.client.post('/library/api/return/', {
            'borrowing_id': '99999'
        })
        
        if response.status_code == 404:
            data = response.json()
            if 'error' in data and 'not found' in data['error'].lower():
                print("✓ Invalid borrowing ID error handled correctly")
                self.test_results.append(("Return Invalid ID", "PASS", "Proper 404 response"))
            else:
                print("✗ Invalid borrowing ID error not properly formatted")
                self.test_results.append(("Return Invalid ID", "FAIL", "Error format incorrect"))
        else:
            print(f"✗ Expected 404 status, got {response.status_code}")
            self.test_results.append(("Return Invalid ID", "FAIL", f"Status {response.status_code}"))
    
    def test_library_renew_error_handling(self):
        """Test library renewal error scenarios"""
        print("\n--- Testing Library Renew Error Handling ---")
        
        if not self.login_user():
            self.test_results.append(("Library Renew Errors", "SKIP", "Login failed"))
            return
        
        # Test missing borrowing ID
        response = self.client.post('/library/api/renew/', {})
        
        if response.status_code == 400:
            data = response.json()
            if 'error' in data and 'required' in data['error'].lower():
                print("✓ Missing borrowing ID error handled correctly")
                self.test_results.append(("Renew Missing ID", "PASS", "Proper error response"))
            else:
                print("✗ Missing borrowing ID error not properly formatted")
                self.test_results.append(("Renew Missing ID", "FAIL", "Error format incorrect"))
        else:
            print(f"✗ Expected 400 status, got {response.status_code}")
            self.test_results.append(("Renew Missing ID", "FAIL", f"Status {response.status_code}"))
        
        # Test invalid borrowing ID
        response = self.client.post('/library/api/renew/', {
            'borrowing_id': '99999'
        })
        
        if response.status_code == 404:
            data = response.json()
            if 'error' in data and 'not found' in data['error'].lower():
                print("✓ Invalid borrowing ID error handled correctly")
                self.test_results.append(("Renew Invalid ID", "PASS", "Proper 404 response"))
            else:
                print("✗ Invalid borrowing ID error not properly formatted")
                self.test_results.append(("Renew Invalid ID", "FAIL", "Error format incorrect"))
        else:
            print(f"✗ Expected 404 status, got {response.status_code}")
            self.test_results.append(("Renew Invalid ID", "FAIL", f"Status {response.status_code}"))
    
    def test_library_search_error_handling(self):
        """Test library search error scenarios"""
        print("\n--- Testing Library Search Error Handling ---")
        
        if not self.login_user():
            self.test_results.append(("Library Search Errors", "SKIP", "Login failed"))
            return
        
        # Test search with short query
        response = self.client.get('/library/api/search/', {'q': 'a', 'type': 'books'})
        
        if response.status_code == 200:
            data = response.json()
            if 'results' in data and len(data['results']) == 0:
                print("✓ Short query handled correctly (empty results)")
                self.test_results.append(("Search Short Query", "PASS", "Empty results for short query"))
            else:
                print("✗ Short query should return empty results")
                self.test_results.append(("Search Short Query", "FAIL", "Should return empty results"))
        else:
            print(f"✗ Search endpoint returned status {response.status_code}")
            self.test_results.append(("Search Short Query", "FAIL", f"Status {response.status_code}"))
        
        # Test search with valid query
        response = self.client.get('/library/api/search/', {'q': 'test', 'type': 'books'})
        
        if response.status_code == 200:
            data = response.json()
            if 'results' in data:
                print("✓ Valid search query handled correctly")
                self.test_results.append(("Search Valid Query", "PASS", "Proper response format"))
            else:
                print("✗ Valid search query missing results field")
                self.test_results.append(("Search Valid Query", "FAIL", "Missing results field"))
        else:
            print(f"✗ Search endpoint returned status {response.status_code}")
            self.test_results.append(("Search Valid Query", "FAIL", f"Status {response.status_code}"))
    
    def test_school_switching_error_handling(self):
        """Test school switching error scenarios"""
        print("\n--- Testing School Switching Error Handling ---")
        
        if not self.login_user():
            self.test_results.append(("School Switch Errors", "SKIP", "Login failed"))
            return
        
        # Test switching to invalid school
        response = self.client.post('/school/switch/', {'school_id': '99999'})
        
        if response.status_code == 200:
            data = response.json()
            if 'success' in data and not data['success']:
                print("✓ Invalid school switch handled correctly")
                self.test_results.append(("Switch Invalid School", "PASS", "Proper error response"))
            else:
                print("✗ Invalid school switch should fail")
                self.test_results.append(("Switch Invalid School", "FAIL", "Should return error"))
        else:
            print(f"✗ School switch returned status {response.status_code}")
            self.test_results.append(("Switch Invalid School", "FAIL", f"Status {response.status_code}"))
        
        # Test switching to valid school
        response = self.client.post('/school/switch/', {'school_id': str(self.school.id)})
        
        if response.status_code == 200:
            data = response.json()
            if 'success' in data and data['success']:
                print("✓ Valid school switch handled correctly")
                self.test_results.append(("Switch Valid School", "PASS", "Successful switch"))
            else:
                print("✗ Valid school switch should succeed")
                self.test_results.append(("Switch Valid School", "FAIL", "Should succeed"))
        else:
            print(f"✗ School switch returned status {response.status_code}")
            self.test_results.append(("Switch Valid School", "FAIL", f"Status {response.status_code}"))
    
    def test_template_error_handling(self):
        """Test template rendering and error display"""
        print("\n--- Testing Template Error Handling ---")
        
        if not self.login_user():
            self.test_results.append(("Template Errors", "SKIP", "Login failed"))
            return
        
        # Test library borrowing system page
        response = self.client.get('/library/borrowing/')
        
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            # Check for toast notification system
            if 'toast-notifications.js' in content:
                print("✓ Toast notification system included in template")
                self.test_results.append(("Template Toast System", "PASS", "JS included"))
            else:
                print("✗ Toast notification system not found in template")
                self.test_results.append(("Template Toast System", "FAIL", "JS not included"))
            
            # Check for error recovery system
            if 'error-recovery.js' in content:
                print("✓ Error recovery system included in template")
                self.test_results.append(("Template Error Recovery", "PASS", "JS included"))
            else:
                print("✗ Error recovery system not found in template")
                self.test_results.append(("Template Error Recovery", "FAIL", "JS not included"))
            
            # Check for enhanced error handling in JavaScript
            if 'async function' in content and 'catch' in content:
                print("✓ Enhanced JavaScript error handling found")
                self.test_results.append(("Template JS Error Handling", "PASS", "Async/await with catch"))
            else:
                print("✗ Enhanced JavaScript error handling not found")
                self.test_results.append(("Template JS Error Handling", "FAIL", "Missing async/catch"))
                
        else:
            print(f"✗ Library borrowing page returned status {response.status_code}")
            self.test_results.append(("Template Loading", "FAIL", f"Status {response.status_code}"))
    
    def test_comprehensive_workflow(self):
        """Test complete workflow with error scenarios"""
        print("\n--- Testing Comprehensive Workflow ---")
        
        if not self.login_user():
            self.test_results.append(("Comprehensive Workflow", "SKIP", "Login failed"))
            return
        
        try:
            # Step 1: Successful book borrowing
            borrow_response = self.client.post('/library/api/borrow/', {
                'book_id': str(self.book.id),
                'borrower_type': 'student',
                'borrower_id': 'STU001',
                'borrower_name': 'Test Student',
                'borrower_email': '<EMAIL>'
            })
            
            if borrow_response.status_code == 200:
                borrow_data = borrow_response.json()
                if borrow_data.get('success'):
                    borrowing_id = borrow_data.get('borrowing_id')
                    print("✓ Book borrowed successfully")
                    
                    # Step 2: Successful book return
                    return_response = self.client.post('/library/api/return/', {
                        'borrowing_id': borrowing_id
                    })
                    
                    if return_response.status_code == 200:
                        return_data = return_response.json()
                        if return_data.get('success'):
                            print("✓ Book returned successfully")
                            self.test_results.append(("Complete Workflow", "PASS", "Borrow and return successful"))
                        else:
                            print("✗ Book return failed")
                            self.test_results.append(("Complete Workflow", "FAIL", "Return failed"))
                    else:
                        print(f"✗ Return returned status {return_response.status_code}")
                        self.test_results.append(("Complete Workflow", "FAIL", f"Return status {return_response.status_code}"))
                else:
                    print("✗ Book borrow failed")
                    self.test_results.append(("Complete Workflow", "FAIL", "Borrow failed"))
            else:
                print(f"✗ Borrow returned status {borrow_response.status_code}")
                self.test_results.append(("Complete Workflow", "FAIL", f"Borrow status {borrow_response.status_code}"))
                
        except Exception as e:
            print(f"✗ Workflow error: {e}")
            self.test_results.append(("Complete Workflow", "ERROR", str(e)))
    
    def generate_report(self):
        """Generate comprehensive test report"""
        print("\n" + "="*60)
        print("COMPREHENSIVE ERROR HANDLING TEST REPORT")
        print("="*60)
        print(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Total Tests: {len(self.test_results)}")
        
        passed = sum(1 for result in self.test_results if result[1] == "PASS")
        failed = sum(1 for result in self.test_results if result[1] == "FAIL")
        errors = sum(1 for result in self.test_results if result[1] == "ERROR")
        skipped = sum(1 for result in self.test_results if result[1] == "SKIP")
        
        print(f"Passed: {passed}")
        print(f"Failed: {failed}")
        print(f"Errors: {errors}")
        print(f"Skipped: {skipped}")
        print(f"Success Rate: {(passed / len(self.test_results) * 100):.1f}%")
        
        print("\nDetailed Results:")
        print("-" * 60)
        
        for test_name, status, details in self.test_results:
            status_symbol = {
                "PASS": "✓",
                "FAIL": "✗",
                "ERROR": "⚠",
                "SKIP": "⊝"
            }.get(status, "?")
            
            print(f"{status_symbol} {test_name:<30} {status:<6} {details}")
        
        # Save report to file
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total': len(self.test_results),
                'passed': passed,
                'failed': failed,
                'errors': errors,
                'skipped': skipped,
                'success_rate': passed / len(self.test_results) * 100
            },
            'results': [
                {
                    'test_name': name,
                    'status': status,
                    'details': details
                }
                for name, status, details in self.test_results
            ]
        }
        
        with open('error_handling_test_report.json', 'w') as f:
            json.dump(report_data, f, indent=2)
        
        print(f"\nReport saved to: error_handling_test_report.json")
        
        return passed == len(self.test_results)
    
    def run_all_tests(self):
        """Run all error handling tests"""
        print("Starting Comprehensive Error Handling Test Suite...")
        print("="*60)
        
        # Run all test methods
        self.test_health_check_endpoint()
        self.test_library_borrow_error_handling()
        self.test_library_return_error_handling()
        self.test_library_renew_error_handling()
        self.test_library_search_error_handling()
        self.test_school_switching_error_handling()
        self.test_template_error_handling()
        self.test_comprehensive_workflow()
        
        # Generate report
        all_passed = self.generate_report()
        
        if all_passed:
            print("\n🎉 All tests passed! Error handling system is working correctly.")
            return True
        else:
            print("\n⚠️  Some tests failed. Please review the results above.")
            return False

def main():
    """Main test runner"""
    try:
        test_suite = ErrorHandlingTestSuite()
        success = test_suite.run_all_tests()
        
        # Exit with appropriate code
        sys.exit(0 if success else 1)
        
    except Exception as e:
        print(f"Test suite failed to run: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()