#!/usr/bin/env python
"""
Test script for school context processors
"""

import os
import sys
import django
from django.test import Test<PERSON>ase, Client, RequestFactory
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.template import Context, Template

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

from core.models import School
from core.context_processors import school_context, dashboard_context, navigation_context

User = get_user_model()

def test_school_context_processors():
    """Test the school context processors functionality"""
    
    print("Testing School Context Processors...")
    
    # Create test client and request factory
    client = Client()
    factory = RequestFactory()
    
    # Create test user
    try:
        user = User.objects.get(username='contexttestuser')
        user.set_password('testpass123')
        user.is_superuser = True
        user.save()
    except User.DoesNotExist:
        user = User.objects.create_user(
            username='contexttestuser',
            email='<EMAIL>',
            password='testpass123',
            is_superuser=True
        )
    
    # Create test schools
    schools_data = [
        {
            'code': 'CTX001',
            'name': 'Context Test School 1',
            'address': '123 Context Street',
            'phone': '+1234567890',
            'email': '<EMAIL>',
            'principal_name': 'Principal Context One',
            'established_date': '2020-01-01'
        },
        {
            'code': 'CTX002',
            'name': 'Context Test School 2',
            'address': '456 Context Avenue',
            'phone': '+1234567891',
            'email': '<EMAIL>',
            'principal_name': 'Principal Context Two',
            'established_date': '2021-01-01'
        }
    ]
    
    created_schools = []
    for school_data in schools_data:
        school, created = School.objects.get_or_create(
            code=school_data['code'],
            defaults=school_data
        )
        created_schools.append(school)
    
    # Login user
    login_success = client.login(username='contexttestuser', password='testpass123')
    print(f"Login successful: {login_success}")
    
    # Select a school first
    first_school = created_schools[0]
    response = client.post(reverse('core:school_select'), {
        'school_id': str(first_school.id),
        'next': '/dashboard/'
    }, HTTP_HOST='localhost')
    
    print(f"School selection response: {response.status_code}")
    
    # Test 1: Test school_context processor directly
    print("1. Testing school_context processor...")
    try:
        # Create a mock request
        request = factory.get('/')
        request.user = user
        request.session = client.session
        
        # Call the context processor
        context = school_context(request)
        
        # Check context data
        context_checks = [
            ('current_school', context.get('current_school')),
            ('available_schools', context.get('available_schools')),
            ('has_multiple_schools', context.get('has_multiple_schools')),
            ('school_count', context.get('school_count')),
            ('school_name', context.get('school_name')),
            ('school_code', context.get('school_code')),
        ]
        
        for key, value in context_checks:
            if value is not None:
                print(f"   ✓ {key}: {value}")
            else:
                print(f"   ❌ {key}: missing")
        
        # Verify current school is set
        if context.get('current_school') and context['current_school'].name == first_school.name:
            print("   ✓ Current school correctly set")
        else:
            print("   ❌ Current school not correctly set")
            
        # Verify multiple schools detected
        if context.get('has_multiple_schools') and context['school_count'] >= 2:
            print("   ✓ Multiple schools correctly detected")
        else:
            print("   ❌ Multiple schools not correctly detected")
            
    except Exception as e:
        print(f"   ❌ School context processor test failed: {e}")
    
    # Test 2: Test context processors in template rendering
    print("2. Testing context processors in template rendering...")
    try:
        # Access a page that uses the base template
        response = client.get('/', HTTP_HOST='localhost')
        
        if response.status_code == 302:
            # Follow redirect
            response = client.get(response.url, HTTP_HOST='localhost')
        
        if response.status_code == 200:
            content = response.content.decode()
            
            # Check if school context is available in template
            template_checks = [
                ('Current school name', first_school.name in content),
                ('School switcher component', 'schoolSwitcher' in content),
                ('Available schools', 'Available Schools' in content),
                ('School code', first_school.code in content),
            ]
            
            for check_name, check_result in template_checks:
                if check_result:
                    print(f"   ✓ {check_name} found in template")
                else:
                    print(f"   ❌ {check_name} not found in template")
                    
        else:
            print(f"   ❌ Could not access template: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Template rendering test failed: {e}")
    
    # Test 3: Test dashboard_context processor
    print("3. Testing dashboard_context processor...")
    try:
        # Create a mock request
        request = factory.get('/')
        request.user = user
        request.session = client.session
        
        # Call the dashboard context processor
        context = dashboard_context(request)
        
        # Check if context data is provided
        dashboard_keys = ['recent_students', 'user_student', 'user_teacher', 'user_employee']
        found_keys = [key for key in dashboard_keys if key in context]
        
        if found_keys:
            print(f"   ✓ Dashboard context keys found: {found_keys}")
        else:
            print("   → No specific dashboard context (normal for test user)")
            
    except Exception as e:
        print(f"   ❌ Dashboard context processor test failed: {e}")
    
    # Test 4: Test navigation_context processor
    print("4. Testing navigation_context processor...")
    try:
        # Create a mock request
        request = factory.get('/')
        request.user = user
        request.session = client.session
        
        # Call the navigation context processor
        context = navigation_context(request)
        
        # Check navigation permissions
        nav_checks = [
            ('can_manage_students', context.get('can_manage_students')),
            ('can_manage_hr', context.get('can_manage_hr')),
            ('can_manage_academics', context.get('can_manage_academics')),
            ('can_view_reports', context.get('can_view_reports')),
            ('can_manage_finance', context.get('can_manage_finance')),
        ]
        
        for key, value in nav_checks:
            if value is not None:
                print(f"   ✓ {key}: {value}")
            else:
                print(f"   → {key}: not set")
                
    except Exception as e:
        print(f"   ❌ Navigation context processor test failed: {e}")
    
    # Test 5: Test context availability across different pages
    print("5. Testing context availability across different pages...")
    try:
        # Test different pages to ensure context is available
        test_urls = [
            ('/', 'Root/Dashboard'),
            ('/students/dashboard/', 'Students Dashboard'),
            ('/library/dashboard/', 'Library Dashboard'),
        ]
        
        for url, page_name in test_urls:
            try:
                response = client.get(url, HTTP_HOST='localhost')
                
                # Follow redirects
                redirect_count = 0
                while response.status_code == 302 and redirect_count < 5:
                    response = client.get(response.url, HTTP_HOST='localhost')
                    redirect_count += 1
                
                if response.status_code == 200:
                    content = response.content.decode()
                    
                    # Check if school context is available
                    if first_school.name in content or first_school.code in content:
                        print(f"   ✓ {page_name}: School context available")
                    else:
                        print(f"   ❌ {page_name}: School context not available")
                else:
                    print(f"   → {page_name}: Status {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ {page_name}: Error {e}")
                
    except Exception as e:
        print(f"   ❌ Cross-page context test failed: {e}")
    
    print("\n✅ School context processors tests completed!")
    
    # Cleanup
    User.objects.filter(username='contexttestuser').delete()
    School.objects.filter(code__in=['CTX001', 'CTX002']).delete()
    
    return True

if __name__ == '__main__':
    try:
        test_school_context_processors()
        print("\n🎉 School context processors are working correctly!")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)