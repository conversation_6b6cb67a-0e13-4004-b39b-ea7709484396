#!/usr/bin/env python
"""
Core functionality validation test
Tests the essential fixes without complex model dependencies
"""

import os
import sys
import django
import json
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from core.models import School
from academics.models import Subject, Teacher, ClassSubject, Schedule
from hr.models import Department

User = get_user_model()


class CoreFunctionalityValidator:
    """Core functionality validation"""
    
    def __init__(self):
        self.client = Client()
        self.results = []
        
    def log_result(self, test_name, success, message=""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        self.results.append({
            'test': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat()
        })
        print(f"{status} {test_name}: {message}")
    
    def test_department_model_fix(self):
        """Test that Department model works correctly"""
        print("\n🏢 Testing Department Model Fix...")
        
        try:
            # Get existing school or create one
            school = School.objects.first()
            if not school:
                school = School.objects.create(
                    name="Test School",
                    code="CORE_TEST",
                    address="Test Address",
                    phone="+1234567890",
                    email="<EMAIL>",
                    principal_name="Test Principal",
                    established_date="2020-01-01"
                )
            
            # Test Department creation
            dept = Department.objects.create(
                name="Test Department",
                code="TEST_DEPT",
                school=school
            )
            
            dept_works = dept.school == school
            self.log_result("Department Creation", dept_works, f"Department created: {dept.name}")
            
            # Test Teacher-Department relationship
            user = User.objects.create_user(
                username='test_teacher_dept',
                password='test123',
                email='<EMAIL>',
                user_type='teacher'
            )
            
            teacher = Teacher.objects.create(
                user=user,
                employee_id="T_DEPT_001",
                qualification="Test Qualification",
                hire_date="2020-01-01",
                experience_years=1,
                department=dept,
                school=school
            )
            
            relationship_works = teacher.department == dept
            self.log_result("Teacher-Department Relationship", relationship_works, 
                          f"Teacher assigned to department: {teacher.department.name}")
            
            return True
            
        except Exception as e:
            self.log_result("Department Model Fix", False, f"Error: {str(e)}")
            return False
    
    def test_school_context_fix(self):
        """Test that school context is properly handled"""
        print("\n🏫 Testing School Context Fix...")
        
        try:
            # Get school
            school = School.objects.first()
            if not school:
                self.log_result("School Context Fix", False, "No school available")
                return False
            
            # Create user and login
            user = User.objects.create_user(
                username='test_context_user',
                password='test123',
                email='<EMAIL>',
                user_type='admin',
                is_superuser=True
            )
            
            login_success = self.client.login(username='test_context_user', password='test123')
            self.log_result("User Login", login_success, "User logged in for context test")
            
            if not login_success:
                return False
            
            # Test school selection
            response = self.client.post('/core/school/select/', {
                'school_id': str(school.id)
            })
            
            selection_works = response.status_code in [200, 302]
            self.log_result("School Selection", selection_works, 
                          f"School selection response: {response.status_code}")
            
            # Test AJAX school switch
            response = self.client.post('/core/school/switch/', {
                'school_id': str(school.id)
            }, HTTP_X_REQUESTED_WITH='XMLHttpRequest')
            
            ajax_works = response.status_code == 200
            if ajax_works:
                try:
                    data = response.json()
                    ajax_works = data.get('success', False)
                    message = f"AJAX switch successful: {data.get('school_name', 'Unknown')}"
                except:
                    message = "AJAX response received"
            else:
                message = f"AJAX switch status: {response.status_code}"
            
            self.log_result("AJAX School Switch", ajax_works, message)
            
            return selection_works and ajax_works
            
        except Exception as e:
            self.log_result("School Context Fix", False, f"Error: {str(e)}")
            return False
    
    def test_integrity_error_fix(self):
        """Test that IntegrityError is fixed for academic models"""
        print("\n🔧 Testing IntegrityError Fix...")
        
        try:
            # Get school and create basic data
            school = School.objects.first()
            if not school:
                self.log_result("IntegrityError Fix", False, "No school available")
                return False
            
            # Create subject
            subject = Subject.objects.create(
                name="Test Subject",
                code="TEST_SUBJ",
                school=school,
                credit_hours=3
            )
            
            subject_works = subject.school == school
            self.log_result("Subject Creation with School", subject_works, 
                          f"Subject created: {subject.name}")
            
            # Test that models properly inherit school context
            models_work = hasattr(subject, 'school') and subject.school == school
            self.log_result("Model School Context", models_work, 
                          "Models properly maintain school context")
            
            return subject_works and models_work
            
        except Exception as e:
            self.log_result("IntegrityError Fix", False, f"Error: {str(e)}")
            return False
    
    def test_performance_optimization(self):
        """Test that performance optimization works"""
        print("\n⚡ Testing Performance Optimization...")
        
        try:
            # Test that performance module imports correctly
            import core.performance_optimization
            
            import_works = True
            self.log_result("Performance Module Import", import_works, 
                          "Performance optimization module imports successfully")
            
            # Test that school utils work efficiently
            from core.school_utils import get_user_schools
            
            user = User.objects.filter(is_superuser=True).first()
            if user:
                schools = get_user_schools(user)
                utils_work = schools is not None
                self.log_result("School Utils Performance", utils_work, 
                              f"School utils return {schools.count()} schools")
            else:
                self.log_result("School Utils Performance", False, "No superuser available")
                utils_work = False
            
            return import_works and utils_work
            
        except Exception as e:
            self.log_result("Performance Optimization", False, f"Error: {str(e)}")
            return False
    
    def run_validation(self):
        """Run all validation tests"""
        print("🚀 Starting Core Functionality Validation")
        print("=" * 50)
        
        start_time = datetime.now()
        
        # Run tests
        dept_test = self.test_department_model_fix()
        context_test = self.test_school_context_fix()
        integrity_test = self.test_integrity_error_fix()
        performance_test = self.test_performance_optimization()
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 VALIDATION SUMMARY")
        print("=" * 50)
        
        total_tests = len(self.results)
        passed_tests = len([r for r in self.results if r['success']])
        failed_tests = total_tests - passed_tests
        
        print(f"⏱️  Duration: {duration:.2f} seconds")
        print(f"📈 Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"📊 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        # Key fixes validation
        key_fixes = {
            'Department Model': dept_test,
            'School Context': context_test,
            'IntegrityError Resolution': integrity_test,
            'Performance Optimization': performance_test
        }
        
        print(f"\n🔑 KEY FIXES STATUS:")
        for fix_name, status in key_fixes.items():
            status_icon = "✅" if status else "❌"
            print(f"   {status_icon} {fix_name}")
        
        if failed_tests > 0:
            print(f"\n❌ FAILED TESTS:")
            for result in self.results:
                if not result['success']:
                    print(f"   • {result['test']}: {result['message']}")
        
        # Save results
        results_file = f'core_validation_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(results_file, 'w') as f:
            json.dump({
                'summary': {
                    'total_tests': total_tests,
                    'passed_tests': passed_tests,
                    'failed_tests': failed_tests,
                    'success_rate': (passed_tests/total_tests)*100,
                    'duration': duration,
                    'key_fixes': key_fixes
                },
                'results': self.results
            }, f, indent=2)
        
        print(f"\n📄 Results saved to: {results_file}")
        
        all_key_fixes_work = all(key_fixes.values())
        
        if all_key_fixes_work:
            print("\n🎉 ALL KEY FIXES ARE WORKING!")
        else:
            print("\n⚠️  Some key fixes need attention")
        
        return all_key_fixes_work


if __name__ == '__main__':
    validator = CoreFunctionalityValidator()
    success = validator.run_validation()
    
    print(f"\n{'🎉 CORE FUNCTIONALITY VALIDATION PASSED' if success else '❌ CORE FUNCTIONALITY VALIDATION FAILED'}")
    sys.exit(0 if success else 1)