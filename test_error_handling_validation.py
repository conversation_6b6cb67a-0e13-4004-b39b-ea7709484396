#!/usr/bin/env python3
"""
Error Handling Validation Test
Tests the implementation of comprehensive error handling components
"""

import os
import sys
import json
from datetime import datetime

def test_file_exists(file_path, description):
    """Test if a file exists and return result"""
    if os.path.exists(file_path):
        print(f"✓ {description}")
        return ("File Exists", "PASS", f"{file_path} found")
    else:
        print(f"✗ {description}")
        return ("File Exists", "FAIL", f"{file_path} not found")

def test_file_content(file_path, search_terms, description):
    """Test if file contains required content"""
    if not os.path.exists(file_path):
        print(f"✗ {description} - File not found")
        return ("File Content", "FAIL", f"{file_path} not found")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        missing_terms = []
        for term in search_terms:
            if term not in content:
                missing_terms.append(term)
        
        if not missing_terms:
            print(f"✓ {description}")
            return ("File Content", "PASS", f"All required content found in {file_path}")
        else:
            print(f"✗ {description} - Missing: {', '.join(missing_terms)}")
            return ("File Content", "FAIL", f"Missing terms: {', '.join(missing_terms)}")
            
    except Exception as e:
        print(f"✗ {description} - Error reading file: {e}")
        return ("File Content", "ERROR", str(e))

def main():
    """Main validation function"""
    print("Error Handling Implementation Validation")
    print("="*50)
    
    results = []
    
    # Test 1: Toast Notification System
    print("\n--- Testing Toast Notification System ---")
    result = test_file_exists("static/js/toast-notifications.js", "Toast notification system file exists")
    results.append(result)
    
    if result[1] == "PASS":
        toast_terms = [
            "class ToastNotificationSystem",
            "show(type, message, options",
            "success(message, options",
            "error(message, options",
            "setupGlobalErrorHandlers",
            "setupAjaxErrorInterceptors"
        ]
        result = test_file_content("static/js/toast-notifications.js", toast_terms, "Toast system has required functionality")
        results.append(result)
    
    # Test 2: Error Recovery System
    print("\n--- Testing Error Recovery System ---")
    result = test_file_exists("static/js/error-recovery.js", "Error recovery system file exists")
    results.append(result)
    
    if result[1] == "PASS":
        recovery_terms = [
            "class ErrorRecoverySystem",
            "setupNetworkMonitoring",
            "fetchWithRetry",
            "shouldRetry",
            "handleNetworkDisconnection",
            "handleNetworkReconnection"
        ]
        result = test_file_content("static/js/error-recovery.js", recovery_terms, "Error recovery system has required functionality")
        results.append(result)
    
    # Test 3: Base Template Integration
    print("\n--- Testing Base Template Integration ---")
    result = test_file_exists("templates/base.html", "Base template exists")
    results.append(result)
    
    if result[1] == "PASS":
        base_terms = [
            "toast-notifications.js",
            "error-recovery.js",
            "toast-container"
        ]
        result = test_file_content("templates/base.html", base_terms, "Base template includes error handling systems")
        results.append(result)
    
    # Test 4: Library Template Enhancement
    print("\n--- Testing Library Template Enhancement ---")
    result = test_file_exists("templates/library/borrowing_system.html", "Library borrowing template exists")
    results.append(result)
    
    if result[1] == "PASS":
        library_terms = [
            "async function",
            "try {",
            "catch (error)",
            "window.toast",
            "window.buttonManager",
            "Enhanced error handling"
        ]
        result = test_file_content("templates/library/borrowing_system.html", library_terms, "Library template has enhanced error handling")
        results.append(result)
    
    # Test 5: Library Views Enhancement
    print("\n--- Testing Library Views Enhancement ---")
    result = test_file_exists("library/views.py", "Library views file exists")
    results.append(result)
    
    if result[1] == "PASS":
        views_terms = [
            "except BookBorrowing.DoesNotExist:",
            "except ValueError as e:",
            "except PermissionError as e:",
            "log_security_event",
            "'success': False",
            "'error':"
        ]
        result = test_file_content("library/views.py", views_terms, "Library views have enhanced error handling")
        results.append(result)
    
    # Test 6: Health Check Endpoint
    print("\n--- Testing Health Check Endpoint ---")
    result = test_file_exists("core/views.py", "Core views file exists")
    results.append(result)
    
    if result[1] == "PASS":
        health_terms = [
            "def health_check",
            "JsonResponse",
            "'status': 'ok'"
        ]
        result = test_file_content("core/views.py", health_terms, "Health check endpoint implemented")
        results.append(result)
    
    result = test_file_exists("core/urls.py", "Core URLs file exists")
    results.append(result)
    
    if result[1] == "PASS":
        url_terms = [
            "api/health-check/",
            "views.health_check"
        ]
        result = test_file_content("core/urls.py", url_terms, "Health check URL configured")
        results.append(result)
    
    # Test 7: JavaScript Features
    print("\n--- Testing JavaScript Features ---")
    
    # Check for comprehensive error handling features
    js_features = [
        ("static/js/toast-notifications.js", [
            "ToastNotificationSystem",
            "AjaxHelper", 
            "ButtonStateManager",
            "setLoading",
            "setSuccess",
            "setError"
        ], "Toast system has all required classes"),
        
        ("static/js/error-recovery.js", [
            "ErrorRecoverySystem",
            "FormValidationEnhancer",
            "validateField",
            "validateForm",
            "retryPendingRequests"
        ], "Error recovery has all required classes")
    ]
    
    for file_path, terms, description in js_features:
        result = test_file_content(file_path, terms, description)
        results.append(result)
    
    # Generate Report
    print("\n" + "="*50)
    print("VALIDATION REPORT")
    print("="*50)
    print(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Total Tests: {len(results)}")
    
    passed = sum(1 for result in results if result[1] == "PASS")
    failed = sum(1 for result in results if result[1] == "FAIL")
    errors = sum(1 for result in results if result[1] == "ERROR")
    
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Errors: {errors}")
    print(f"Success Rate: {(passed / len(results) * 100):.1f}%")
    
    print("\nDetailed Results:")
    print("-" * 50)
    
    for test_name, status, details in results:
        status_symbol = {
            "PASS": "✓",
            "FAIL": "✗",
            "ERROR": "⚠"
        }.get(status, "?")
        
        print(f"{status_symbol} {test_name:<20} {status:<6} {details}")
    
    # Save report
    report_data = {
        'timestamp': datetime.now().isoformat(),
        'summary': {
            'total': len(results),
            'passed': passed,
            'failed': failed,
            'errors': errors,
            'success_rate': passed / len(results) * 100
        },
        'results': [
            {
                'test_name': name,
                'status': status,
                'details': details
            }
            for name, status, details in results
        ]
    }
    
    with open('error_handling_validation_report.json', 'w') as f:
        json.dump(report_data, f, indent=2)
    
    print(f"\nReport saved to: error_handling_validation_report.json")
    
    if passed == len(results):
        print("\n🎉 All validations passed! Error handling implementation is complete.")
        return True
    else:
        print("\n⚠️  Some validations failed. Please review the results above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)