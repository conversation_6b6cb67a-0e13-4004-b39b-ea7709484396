#!/usr/bin/env python
"""
Final integration testing and system validation
Tests complete school selection workflow, IntegrityError resolution, 
library operations, and end-to-end functionality
"""

import os
import sys
import django
import json
import time
from datetime import datetime, date

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.db import transaction
from core.models import School, AcademicYear, Semester
from academics.models import Subject, Teacher, ClassSubject, Schedule
from hr.models import Department
from students.models import Grade, Class, Student
from library.models import Book, BookCopy, BookBorrowing

User = get_user_model()


class FinalIntegrationTestSuite:
    """Final integration test suite for complete system validation"""
    
    def __init__(self):
        self.client = Client()
        self.test_results = []
        self.setup_complete = False
        
    def log_result(self, test_name, success, message="", details=None):
        """Log test result with details"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = {
            'test': test_name,
            'status': status,
            'success': success,
            'message': message,
            'details': details or {},
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        print(f"{status} {test_name}: {message}")
        if details and not success:
            print(f"    Details: {details}")
    
    def setup_test_environment(self):
        """Setup comprehensive test environment"""
        print("\n🔧 Setting up test environment...")
        
        try:
            # Create or get test schools
            self.school1, created = School.objects.get_or_create(
                code="INTEG001",
                defaults={
                    'name': "Integration Test School 1",
                    'address': "123 Integration Street",
                    'phone': "+1234567890",
                    'email': "<EMAIL>",
                    'principal_name': "Test Principal 1",
                    'established_date': date(2020, 1, 1)
                }
            )
            
            self.school2, created = School.objects.get_or_create(
                code="INTEG002",
                defaults={
                    'name': "Integration Test School 2",
                    'address': "456 Integration Avenue",
                    'phone': "+1234567891",
                    'email': "<EMAIL>",
                    'principal_name': "Test Principal 2",
                    'established_date': date(2020, 1, 1)
                }
            )
            
            # Create test users
            self.admin_user, created = User.objects.get_or_create(
                username='integration_admin',
                defaults={
                    'email': '<EMAIL>',
                    'user_type': 'admin',
                    'is_superuser': True,
                    'is_staff': True
                }
            )
            if created:
                self.admin_user.set_password('admin123')
                self.admin_user.save()
            
            self.teacher_user, created = User.objects.get_or_create(
                username='integration_teacher',
                defaults={
                    'email': '<EMAIL>',
                    'user_type': 'teacher'
                }
            )
            if created:
                self.teacher_user.set_password('teacher123')
                self.teacher_user.save()
            
            # Create academic structure for school1
            self.academic_year, created = AcademicYear.objects.get_or_create(
                name="2024-2025",
                school=self.school1,
                defaults={
                    'start_date': date(2024, 9, 1),
                    'end_date': date(2025, 6, 30),
                    'is_current': True
                }
            )
            
            self.semester, created = Semester.objects.get_or_create(
                academic_year=self.academic_year,
                name="Fall 2024",
                school=self.school1,
                defaults={
                    'start_date': date(2024, 9, 1),
                    'end_date': date(2025, 1, 31),
                    'is_current': True
                }
            )
            
            self.department, created = Department.objects.get_or_create(
                name="Mathematics",
                code="MATH_INTEG",
                school=self.school1
            )
            
            self.grade, created = Grade.objects.get_or_create(
                name="Grade 10",
                level=10,
                school=self.school1
            )
            
            self.class_obj, created = Class.objects.get_or_create(
                name="10A",
                grade=self.grade,
                academic_year=self.academic_year,
                school=self.school1,
                defaults={'max_students': 30}
            )
            
            self.subject, created = Subject.objects.get_or_create(
                name="Mathematics",
                code="MATH101_INTEG",
                school=self.school1,
                defaults={
                    'description': "Integration Test Mathematics",
                    'credit_hours': 3
                }
            )
            
            self.teacher, created = Teacher.objects.get_or_create(
                user=self.teacher_user,
                employee_id="T001_INTEG",
                school=self.school1,
                defaults={
                    'qualification': "MSc Mathematics",
                    'hire_date': date(2020, 1, 1),
                    'experience_years': 5,
                    'department': self.department
                }
            )
            
            # Add subject to teacher
            self.teacher.subjects.add(self.subject)
            
            # Create student
            self.student, created = Student.objects.get_or_create(
                student_id="S001_INTEG",
                school=self.school1,
                defaults={
                    'first_name': "Integration",
                    'last_name': "Student",
                    'date_of_birth': date(2005, 1, 1),
                    'admission_date': date(2024, 9, 1),
                    'current_class': self.class_obj
                }
            )
            
            # Create library book
            self.book, created = Book.objects.get_or_create(
                barcode="INTEG001",
                school=self.school1,
                defaults={
                    'title': "Integration Test Book",
                    'isbn': "1234567890123",
                    'call_number': "INTEG.001",
                    'publication_date': date(2023, 1, 1)
                }
            )
            
            self.book_copy, created = BookCopy.objects.get_or_create(
                book=self.book,
                copy_number=1,
                school=self.school1,
                defaults={'status': 'available'}
            )
            
            self.setup_complete = True
            self.log_result("Environment Setup", True, "Test environment created successfully")
            
        except Exception as e:
            self.log_result("Environment Setup", False, f"Setup failed: {str(e)}", {'error': str(e)})
            raise
    
    def test_school_selection_workflow(self):
        """Test complete school selection workflow from login to operations"""
        print("\n🏫 Testing school selection workflow...")
        
        try:
            # Test 1: User login
            login_success = self.client.login(username='integration_admin', password='admin123')
            self.log_result("User Login", login_success, "Admin user login")
            
            if not login_success:
                return
            
            # Test 2: School selection page access
            response = self.client.get('/core/school/select/')
            page_loads = response.status_code in [200, 302]
            self.log_result("School Selection Page", page_loads, 
                          f"Page access (status: {response.status_code})")
            
            # Test 3: School selection POST
            response = self.client.post('/core/school/select/', {
                'school_id': str(self.school1.id)
            })
            selection_works = response.status_code in [200, 302]
            self.log_result("School Selection POST", selection_works,
                          f"School selection (status: {response.status_code})")
            
            # Test 4: AJAX school switching
            response = self.client.post('/core/school/switch/', {
                'school_id': str(self.school2.id)
            }, HTTP_X_REQUESTED_WITH='XMLHttpRequest')
            
            ajax_works = response.status_code == 200
            if ajax_works:
                try:
                    data = response.json()
                    ajax_works = data.get('success', False)
                    message = f"AJAX switch successful: {data.get('school_name', 'Unknown')}"
                except:
                    message = "AJAX response received but JSON parsing failed"
            else:
                message = f"AJAX switch failed (status: {response.status_code})"
            
            self.log_result("AJAX School Switch", ajax_works, message)
            
            # Test 5: Session persistence
            response = self.client.get('/dashboard/')
            persistence_works = response.status_code != 302  # Should not redirect to school selection
            self.log_result("Session Persistence", persistence_works,
                          f"School selection persists (status: {response.status_code})")
            
        except Exception as e:
            self.log_result("School Selection Workflow", False, f"Workflow failed: {str(e)}")
    
    def test_integrity_error_resolution(self):
        """Test that IntegrityError issues are resolved for academic schedule creation"""
        print("\n🔧 Testing IntegrityError resolution...")
        
        try:
            # Login and select school
            self.client.login(username='integration_admin', password='admin123')
            self.client.post('/core/school/select/', {'school_id': str(self.school1.id)})
            
            # Test 1: ClassSubject creation with proper school context
            try:
                class_subject, created = ClassSubject.objects.get_or_create(
                    class_obj=self.class_obj,
                    subject=self.subject,
                    teacher=self.teacher,
                    academic_year=self.academic_year,
                    semester=self.semester,
                    school=self.school1,
                    defaults={'weekly_hours': 3}
                )
                
                school_context_works = class_subject.school == self.school1
                self.log_result("ClassSubject School Context", school_context_works,
                              f"ClassSubject created with correct school: {class_subject.school.name}")
                
            except Exception as e:
                self.log_result("ClassSubject Creation", False, f"Failed: {str(e)}")
                return
            
            # Test 2: Schedule creation without IntegrityError
            try:
                schedule, created = Schedule.objects.get_or_create(
                    class_subject=class_subject,
                    day_of_week='monday',
                    start_time='09:00',
                    school=self.school1,
                    defaults={
                        'end_time': '10:00',
                        'room_number': '101'
                    }
                )
                
                schedule_works = schedule.school == self.school1
                self.log_result("Schedule Creation", schedule_works,
                              f"Schedule created without IntegrityError: {schedule.school.name}")
                
            except Exception as e:
                self.log_result("Schedule Creation", False, f"IntegrityError still occurs: {str(e)}")
            
            # Test 3: Schedule creation via web interface
            response = self.client.post('/academics/schedules/create/', {
                'class_id': str(self.class_obj.id),
                'subject_id': str(self.subject.id),
                'teacher_id': str(self.teacher.id),
                'day_of_week': 'tuesday',
                'start_time': '10:00',
                'end_time': '11:00',
                'room_number': '102'
            })
            
            web_creation_works = response.status_code in [200, 302]
            self.log_result("Web Schedule Creation", web_creation_works,
                          f"Schedule creation via web (status: {response.status_code})")
            
        except Exception as e:
            self.log_result("IntegrityError Resolution", False, f"Test failed: {str(e)}")
    
    def test_library_operations(self):
        """Test library borrowing and returning functionality with proper school context"""
        print("\n📚 Testing library operations...")
        
        try:
            # Login and select school
            self.client.login(username='integration_admin', password='admin123')
            self.client.post('/core/school/select/', {'school_id': str(self.school1.id)})
            
            # Test 1: Book borrowing API
            response = self.client.post('/library/api/borrow/', {
                'book_copy_id': str(self.book_copy.id),
                'student_id': str(self.student.id)
            }, HTTP_X_REQUESTED_WITH='XMLHttpRequest')
            
            borrow_works = response.status_code in [200, 400]  # 400 might be validation error
            if response.status_code == 200:
                try:
                    data = response.json()
                    borrow_works = data.get('success', False)
                    message = f"Book borrowed successfully: {data.get('message', 'No message')}"
                except:
                    message = "Borrow API responded but JSON parsing failed"
            else:
                message = f"Borrow API response (status: {response.status_code})"
            
            self.log_result("Library Borrow API", borrow_works, message)
            
            # Test 2: Book return API
            response = self.client.post('/library/api/return/', {
                'book_copy_id': str(self.book_copy.id),
                'student_id': str(self.student.id)
            }, HTTP_X_REQUESTED_WITH='XMLHttpRequest')
            
            return_works = response.status_code in [200, 400]
            if response.status_code == 200:
                try:
                    data = response.json()
                    return_works = data.get('success', False)
                    message = f"Book returned successfully: {data.get('message', 'No message')}"
                except:
                    message = "Return API responded but JSON parsing failed"
            else:
                message = f"Return API response (status: {response.status_code})"
            
            self.log_result("Library Return API", return_works, message)
            
            # Test 3: Library data isolation
            # Switch to school2 and verify we can't access school1's books
            self.client.post('/core/school/select/', {'school_id': str(self.school2.id)})
            
            # Try to borrow school1's book while in school2 context
            response = self.client.post('/library/api/borrow/', {
                'book_copy_id': str(self.book_copy.id),
                'student_id': str(self.student.id)
            }, HTTP_X_REQUESTED_WITH='XMLHttpRequest')
            
            isolation_works = response.status_code in [400, 404]  # Should fail due to school isolation
            self.log_result("Library Data Isolation", isolation_works,
                          f"Cross-school access properly blocked (status: {response.status_code})")
            
        except Exception as e:
            self.log_result("Library Operations", False, f"Test failed: {str(e)}")
    
    def test_school_switching_and_data_filtering(self):
        """Test end-to-end school switching and data filtering"""
        print("\n🔄 Testing school switching and data filtering...")
        
        try:
            # Login
            self.client.login(username='integration_admin', password='admin123')
            
            # Test 1: Create data in school1
            self.client.post('/core/school/select/', {'school_id': str(self.school1.id)})
            
            school1_subject, created = Subject.objects.get_or_create(
                name="School1 Exclusive Subject",
                code="SCH1_EXCL",
                school=self.school1,
                defaults={'credit_hours': 2}
            )
            
            # Test 2: Switch to school2 and create different data
            self.client.post('/core/school/select/', {'school_id': str(self.school2.id)})
            
            school2_subject, created = Subject.objects.get_or_create(
                name="School2 Exclusive Subject",
                code="SCH2_EXCL",
                school=self.school2,
                defaults={'credit_hours': 2}
            )
            
            # Test 3: Verify data isolation
            school1_subjects = Subject.objects.filter(school=self.school1)
            school2_subjects = Subject.objects.filter(school=self.school2)
            
            isolation_works = (
                school1_subject in school1_subjects and
                school2_subject in school2_subjects and
                school1_subject not in school2_subjects and
                school2_subject not in school1_subjects
            )
            
            self.log_result("Data Isolation", isolation_works,
                          f"School1: {school1_subjects.count()} subjects, School2: {school2_subjects.count()} subjects")
            
            # Test 4: Performance under switching
            start_time = time.time()
            for i in range(5):
                school_id = self.school1.id if i % 2 == 0 else self.school2.id
                response = self.client.post('/core/school/switch/', {
                    'school_id': str(school_id)
                }, HTTP_X_REQUESTED_WITH='XMLHttpRequest')
            end_time = time.time()
            
            performance_good = (end_time - start_time) < 2.0  # Should complete in under 2 seconds
            self.log_result("Switching Performance", performance_good,
                          f"5 switches completed in {end_time - start_time:.2f} seconds")
            
        except Exception as e:
            self.log_result("School Switching and Data Filtering", False, f"Test failed: {str(e)}")
    
    def test_department_model_functionality(self):
        """Test that Department model works correctly as a proper model"""
        print("\n🏢 Testing Department model functionality...")
        
        try:
            # Test 1: Department model exists and works
            dept_count_before = Department.objects.filter(school=self.school1).count()
            
            new_dept = Department.objects.create(
                name="Integration Test Department",
                code="INTEG_DEPT",
                description="Department created during integration testing",
                school=self.school1
            )
            
            dept_count_after = Department.objects.filter(school=self.school1).count()
            dept_creation_works = dept_count_after > dept_count_before
            
            self.log_result("Department Creation", dept_creation_works,
                          f"Department created: {new_dept.name}")
            
            # Test 2: Teacher-Department relationship
            self.teacher.department = new_dept
            self.teacher.save()
            
            relationship_works = self.teacher.department == new_dept
            self.log_result("Teacher-Department Relationship", relationship_works,
                          f"Teacher assigned to department: {self.teacher.department.name}")
            
            # Test 3: Department accessible from templates (via related objects)
            teachers_in_dept = new_dept.teachers.all()
            template_access_works = self.teacher in teachers_in_dept
            
            self.log_result("Department Template Access", template_access_works,
                          f"Department accessible via reverse relationship: {teachers_in_dept.count()} teachers")
            
        except Exception as e:
            self.log_result("Department Model Functionality", False, f"Test failed: {str(e)}")
    
    def run_all_tests(self):
        """Run all integration tests"""
        print("🚀 Starting Final Integration Test Suite")
        print("=" * 60)
        
        start_time = time.time()
        
        try:
            # Setup
            if not self.setup_complete:
                self.setup_test_environment()
            
            if not self.setup_complete:
                print("❌ Setup failed, aborting tests")
                return False
            
            # Run all tests
            self.test_school_selection_workflow()
            self.test_integrity_error_resolution()
            self.test_library_operations()
            self.test_school_switching_and_data_filtering()
            self.test_department_model_functionality()
            
        except Exception as e:
            print(f"💥 Critical error during testing: {e}")
            self.log_result("Critical Error", False, str(e))
        
        end_time = time.time()
        
        # Generate summary
        print("\n" + "=" * 60)
        print("📊 FINAL INTEGRATION TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['success']])
        failed_tests = total_tests - passed_tests
        
        print(f"⏱️  Total Time: {end_time - start_time:.2f} seconds")
        print(f"📈 Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"📊 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ FAILED TESTS ({failed_tests}):")
            for result in self.test_results:
                if not result['success']:
                    print(f"   • {result['test']}: {result['message']}")
        else:
            print("\n🎉 ALL TESTS PASSED!")
        
        # Save detailed results
        results_file = f'integration_test_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(results_file, 'w') as f:
            json.dump({
                'summary': {
                    'total_tests': total_tests,
                    'passed_tests': passed_tests,
                    'failed_tests': failed_tests,
                    'success_rate': (passed_tests/total_tests)*100,
                    'execution_time': end_time - start_time,
                    'timestamp': datetime.now().isoformat()
                },
                'test_results': self.test_results
            }, f, indent=2)
        
        print(f"\n📄 Detailed results saved to: {results_file}")
        
        return failed_tests == 0


if __name__ == '__main__':
    test_suite = FinalIntegrationTestSuite()
    success = test_suite.run_all_tests()
    
    print(f"\n{'🎉 INTEGRATION TESTS COMPLETED SUCCESSFULLY' if success else '❌ INTEGRATION TESTS FAILED'}")
    sys.exit(0 if success else 1)