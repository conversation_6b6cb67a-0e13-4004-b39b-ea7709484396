#!/usr/bin/env python
"""
Final test to confirm school selection is working correctly
"""

import os
import sys
import django
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

from core.models import School

User = get_user_model()

def test_final_school_selection():
    """Final test to confirm school selection is working"""
    
    print("Final School Selection Test...")
    
    # Create test client
    client = Client()
    
    # Create test user
    try:
        user = User.objects.get(username='finaltestuser')
        user.set_password('testpass123')
        user.is_superuser = True
        user.save()
    except User.DoesNotExist:
        user = User.objects.create_user(
            username='finaltestuser',
            email='<EMAIL>',
            password='testpass123',
            is_superuser=True
        )
    
    # Get an existing school
    school = School.objects.filter(is_active=True).first()
    if not school:
        print("❌ No active schools found!")
        return False
    
    print(f"Using school: {school.name} (ID: {school.id})")
    
    # Login user
    login_success = client.login(username='finaltestuser', password='testpass123')
    print(f"Login successful: {login_success}")
    
    # Test complete workflow
    print("\n1. Testing complete school selection workflow...")
    
    # Step 1: Try to access dashboard (should redirect to school selection)
    response = client.get('/accounts/dashboard/', HTTP_HOST='localhost')
    print(f"   Dashboard access: {response.status_code}")
    
    if response.status_code == 302 and 'school/select' in response.url:
        print("   ✓ Correctly redirected to school selection")
        
        # Step 2: Access school selection page
        response = client.get(response.url, HTTP_HOST='localhost')
        print(f"   School selection page: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✓ School selection page loads")
            
            # Step 3: Submit school selection
            response = client.post('/core/school/select/', {
                'school_id': str(school.id),
                'next': '/accounts/dashboard/'
            }, HTTP_HOST='localhost')
            
            print(f"   School selection POST: {response.status_code}")
            
            if response.status_code == 302:
                print(f"   ✓ Redirected to: {response.url}")
                
                # Step 4: Follow redirect to dashboard
                response = client.get(response.url, HTTP_HOST='localhost')
                print(f"   Dashboard after selection: {response.status_code}")
                
                if response.status_code == 200:
                    print("   ✓ Successfully accessed dashboard after school selection")
                    
                    # Step 5: Verify school is in session
                    session = client.session
                    if session.get('selected_school_id') == str(school.id):
                        print("   ✓ School correctly stored in session")
                    else:
                        print("   ❌ School not stored in session")
                        
                    # Step 6: Test that subsequent requests don't redirect
                    response = client.get('/students/dashboard/', HTTP_HOST='localhost')
                    print(f"   Students dashboard: {response.status_code}")
                    
                    if response.status_code == 200:
                        print("   ✓ No more redirections - school selection working!")
                    elif response.status_code == 302:
                        if 'school/select' in response.url:
                            print("   ❌ Still being redirected to school selection")
                        else:
                            print("   → Normal redirect (not to school selection)")
                    else:
                        print(f"   → Unexpected status: {response.status_code}")
                        
                else:
                    print(f"   ❌ Dashboard not accessible: {response.status_code}")
            else:
                print(f"   ❌ School selection didn't redirect: {response.status_code}")
        else:
            print(f"   ❌ School selection page failed: {response.status_code}")
    else:
        print(f"   → Dashboard response: {response.status_code} (might already have school selected)")
    
    # Test navbar school switcher
    print("\n2. Testing navbar school switcher...")
    
    # Get another school for switching
    other_schools = School.objects.filter(is_active=True).exclude(id=school.id)[:1]
    if other_schools:
        other_school = other_schools[0]
        print(f"   Switching to: {other_school.name}")
        
        # Test AJAX school switching
        response = client.post('/core/school/switch/', {
            'school_id': str(other_school.id)
        }, HTTP_HOST='localhost')
        
        print(f"   AJAX switch response: {response.status_code}")
        
        if response.status_code == 200:
            import json
            data = json.loads(response.content)
            if data.get('success'):
                print("   ✓ AJAX school switching works")
                
                # Verify session updated
                session = client.session
                if session.get('selected_school_id') == str(other_school.id):
                    print("   ✓ Session updated via AJAX")
                else:
                    print("   ❌ Session not updated via AJAX")
            else:
                print(f"   ❌ AJAX switch failed: {data.get('message')}")
        else:
            print(f"   ❌ AJAX switch error: {response.status_code}")
    else:
        print("   → Only one school available, skipping switch test")
    
    print("\n✅ Final school selection test completed!")
    
    # Cleanup
    User.objects.filter(username='finaltestuser').delete()
    
    return True

if __name__ == '__main__':
    try:
        test_final_school_selection()
        print("\n🎉 School selection system is fully functional!")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)