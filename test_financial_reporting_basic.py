#!/usr/bin/env python
"""
Basic test script for financial reporting functionality
"""

import os
import sys
import django
from decimal import Decimal
from datetime import date, timedelta

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

# Now import Django models
from django.contrib.auth import get_user_model
from core.models import School, AcademicYear
from finance.models import Account, AccountType, FinancialYear
from finance.reporting_services import FinancialReportingService

User = get_user_model()


def test_basic_functionality():
    """Test basic financial reporting functionality"""
    print("Testing Financial Reporting Services...")
    
    try:
        # Create test school
        import uuid
        unique_code = f"TEST{uuid.uuid4().hex[:4].upper()}"
        school = School.objects.create(
            name="Test School",
            code=unique_code,
            address="123 Test St",
            phone="************",
            email="<EMAIL>",
            established_date=date.today()
        )
        print("✓ School created successfully")
        
        # Create test user
        unique_username = f"testuser{uuid.uuid4().hex[:4]}"
        user = User.objects.create_user(
            username=unique_username,
            email="<EMAIL>",
            password="testpass123"
        )
        print("✓ User created successfully")
        
        # Create account type
        asset_type = AccountType.objects.create(
            school=school,
            name="Assets",
            type="asset",
            created_by=user
        )
        print("✓ Account type created successfully")
        
        # Create account
        cash_account = Account.objects.create(
            school=school,
            code="1110",
            name="Cash",
            account_type=asset_type,
            opening_balance=Decimal('10000.00'),
            created_by=user
        )
        print("✓ Account created successfully")
        
        # Test reporting service
        reporting_service = FinancialReportingService(school)
        print("✓ Reporting service initialized successfully")
        
        # Test balance sheet generation
        balance_sheet = reporting_service.generate_balance_sheet()
        print("✓ Balance sheet generated successfully")
        print(f"  - Total assets: {balance_sheet['assets']['total_assets']}")
        
        # Test dashboard data generation
        dashboard_data = reporting_service.generate_financial_dashboard_data()
        print("✓ Dashboard data generated successfully")
        print(f"  - Key metrics available: {list(dashboard_data['key_metrics'].keys())}")
        
        print("\n🎉 All tests passed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up test data
        try:
            School.objects.filter(code__startswith="TEST").delete()
            User.objects.filter(username__startswith="testuser").delete()
            print("✓ Test data cleaned up")
        except:
            pass


if __name__ == "__main__":
    success = test_basic_functionality()
    sys.exit(0 if success else 1)