#!/usr/bin/env python
"""
Frontend tests for AJAX operations and UI interactions
"""

import os
import sys
import django
import json
from unittest.mock import patch

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from core.models import School
from students.models import Student, Grade, Class
from library.models import Book, BookCopy
from core.models import AcademicYear

User = get_user_model()


class FrontendAjaxTestCase(TestCase):
    """Test case for frontend AJAX operations"""
    
    def setUp(self):
        """Set up test data"""
        self.client = Client()
        
        # Create test school
        self.school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test Street",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date="2020-01-01"
        )
        
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            email='<EMAIL>',
            user_type='admin',
            is_superuser=True
        )
        
        # Create academic year
        self.academic_year = AcademicYear.objects.create(
            name="2024-2025",
            start_date="2024-09-01",
            end_date="2025-06-30",
            is_current=True,
            school=self.school
        )
        
        # Create grade and class
        self.grade = Grade.objects.create(
            name="Grade 10",
            level=10,
            school=self.school
        )
        
        self.class_obj = Class.objects.create(
            name="10A",
            grade=self.grade,
            academic_year=self.academic_year,
            max_students=30,
            school=self.school
        )
        
        # Create test student
        self.student = Student.objects.create(
            first_name="Test",
            last_name="Student",
            student_id="S001",
            current_class=self.class_obj,
            school=self.school
        )
        
        # Create test book
        self.book = Book.objects.create(
            title="Test Book",
            isbn="1234567890123",
            barcode="TEST001",
            call_number="TEST.001",
            school=self.school
        )
        
        # Create book copy
        self.book_copy = BookCopy.objects.create(
            book=self.book,
            copy_number=1,
            status='available',
            school=self.school
        )
    
    def test_school_switcher_ajax(self):
        """Test school switcher AJAX functionality"""
        self.client.force_login(self.user)
        
        # Test AJAX school switch
        response = self.client.post('/core/school/switch/', {
            'school_id': str(self.school.id)
        }, HTTP_X_REQUESTED_WITH='XMLHttpRequest')
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data.get('success'))
        self.assertEqual(data.get('school_name'), self.school.name)
    
    def test_library_borrow_ajax(self):
        """Test library borrowing AJAX functionality"""
        self.client.force_login(self.user)
        
        # Select school first
        self.client.post('/core/school/select/', {'school_id': str(self.school.id)})
        
        # Test book borrowing
        response = self.client.post('/library/api/borrow/', {
            'book_copy_id': str(self.book_copy.id),
            'student_id': str(self.student.id)
        }, HTTP_X_REQUESTED_WITH='XMLHttpRequest')
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data.get('success'))
    
    def test_library_return_ajax(self):
        """Test library return AJAX functionality"""
        self.client.force_login(self.user)
        
        # Select school first
        self.client.post('/core/school/select/', {'school_id': str(self.school.id)})
        
        # First borrow a book
        self.client.post('/library/api/borrow/', {
            'book_copy_id': str(self.book_copy.id),
            'student_id': str(self.student.id)
        }, HTTP_X_REQUESTED_WITH='XMLHttpRequest')
        
        # Then return it
        response = self.client.post('/library/api/return/', {
            'book_copy_id': str(self.book_copy.id),
            'student_id': str(self.student.id)
        }, HTTP_X_REQUESTED_WITH='XMLHttpRequest')
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data.get('success'))
    
    def test_error_handling_ajax(self):
        """Test AJAX error handling"""
        self.client.force_login(self.user)
        
        # Test invalid school switch
        response = self.client.post('/core/school/switch/', {
            'school_id': 'invalid-id'
        }, HTTP_X_REQUESTED_WITH='XMLHttpRequest')
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertFalse(data.get('success'))
        self.assertIn('message', data)
    
    def test_ui_loading_states(self):
        """Test UI loading states and button management"""
        self.client.force_login(self.user)
        
        # This would typically be tested with Selenium or similar
        # For now, we test that the endpoints respond correctly
        response = self.client.get('/core/school/select/')
        self.assertContains(response, 'loading', status_code=200)
    
    def test_toast_notifications(self):
        """Test toast notification system"""
        self.client.force_login(self.user)
        
        # Test successful operation shows success message
        response = self.client.post('/core/school/select/', {
            'school_id': str(self.school.id)
        }, follow=True)
        
        # Check for success message in messages framework
        messages = list(response.context['messages'])
        self.assertTrue(any('success' in str(message).lower() for message in messages))


if __name__ == '__main__':
    import unittest
    unittest.main()