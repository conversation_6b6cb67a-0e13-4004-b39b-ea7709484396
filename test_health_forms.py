#!/usr/bin/env python
"""
Test script to verify health forms are working correctly
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

def test_health_forms_import():
    """Test that health forms can be imported without errors"""
    print("🧪 Testing health forms import...")
    
    try:
        from health.forms import (
            HealthProfileForm, 
            AllergyForm, 
            MedicationForm, 
            MedicalHistoryForm,
            MedicalIncidentForm,
            HealthScreeningScheduleForm,
            MedicalAppointmentForm,
            ComplianceMonitoringForm
        )
        print("✅ All health forms imported successfully")
        return True
    except SyntaxError as e:
        print(f"❌ Syntax error in health forms: {e}")
        return False
    except ImportError as e:
        print(f"❌ Import error in health forms: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_form_initialization():
    """Test that forms can be initialized with school parameter"""
    print("\n🔧 Testing form initialization with school parameter...")
    
    try:
        from health.forms import HealthProfileForm, AllergyForm, MedicationForm
        from core.models import School
        
        # Get or create a test school
        school = School.objects.first()
        if not school:
            print("⚠️ No school found in database - creating test school")
            school = School.objects.create(
                name="Test School",
                code="TEST001"
            )
        
        # Test form initialization with school parameter
        health_form = HealthProfileForm(school=school)
        allergy_form = AllergyForm(school=school)
        medication_form = MedicationForm(school=school)
        
        print("✅ Forms initialized successfully with school parameter")
        print(f"   - HealthProfileForm: OK")
        print(f"   - AllergyForm: OK")
        print(f"   - MedicationForm: OK")
        
        return True
        
    except Exception as e:
        print(f"❌ Form initialization failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing Health Forms Fix...")
    print("=" * 50)
    
    # Test imports
    import_ok = test_health_forms_import()
    
    # Test initialization
    init_ok = test_form_initialization()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 HEALTH FORMS TEST SUMMARY")
    print("=" * 50)
    
    print(f"📦 Form Imports: {'✅ PASS' if import_ok else '❌ FAIL'}")
    print(f"🔧 Form Initialization: {'✅ PASS' if init_ok else '❌ FAIL'}")
    
    if import_ok and init_ok:
        print("\n🎉 ALL HEALTH FORMS TESTS PASSED!")
        print("✅ Syntax error fixed")
        print("✅ Forms can be imported")
        print("✅ Forms handle school parameter correctly")
        print("✅ BaseModelForm error resolved")
        
        return True
    else:
        print("\n❌ SOME TESTS FAILED")
        print("Please check the errors above")
        
        return False

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)