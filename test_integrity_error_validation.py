#!/usr/bin/env python
"""
Focused test to validate IntegrityError resolution for academic schedules
This test specifically validates that Task 15 requirements are met
"""

import os
import sys
import django
import uuid
from datetime import datetime, date

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.db import IntegrityError
from core.models import School, AcademicYear, Semester
from academics.models import Subject, Teacher, ClassSubject, Schedule
from hr.models import Department
from students.models import Grade, Class

User = get_user_model()


class IntegrityErrorValidationTest:
    """Test class to validate IntegrityError resolution"""
    
    def __init__(self):
        self.client = Client()
        self.test_results = []
        
    def log_result(self, test_name, success, details=""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {details}")
        self.test_results.append({
            'test': test_name,
            'success': success,
            'details': details
        })
    
    def setup_test_data(self):
        """Setup test data for validation"""
        try:
            # Create school with unique code
            unique_code = f"INT{str(uuid.uuid4())[:8].upper()}"
            self.school = School.objects.create(
                name="Test School for Integrity",
                code=unique_code,
                address="123 Test Street",
                phone="************",
                email="<EMAIL>",
                website="https://integrity.com",
                principal_name="Test Principal",
                established_date=date.today(),
                timezone="UTC",
                currency="USD",
                academic_year_start_month=9,
                is_active=True
            )
            
            # Create academic year
            self.academic_year = AcademicYear.objects.create(
                school=self.school,
                name="2025-2026",
                start_date=date(2025, 9, 1),
                end_date=date(2026, 6, 30),
                is_current=True
            )
            
            # Create semester
            self.semester = Semester.objects.create(
                school=self.school,
                academic_year=self.academic_year,
                name="First Semester",
                start_date=date(2025, 9, 1),
                end_date=date(2026, 1, 31),
                is_current=True
            )
            
            # Create department
            unique_dept_code = f"DEPT{str(uuid.uuid4())[:8].upper()}"
            self.department = Department.objects.create(
                school=self.school,
                name="Mathematics Department",
                code=unique_dept_code,
                description="Mathematics Department"
            )
            
            # Create teacher
            unique_username = f"teacher_{str(uuid.uuid4())[:8]}"
            teacher_user = User.objects.create_user(
                username=unique_username,
                email=f"teacher_{str(uuid.uuid4())[:8]}@integrity.com",
                password="testpass123",
                first_name="Test",
                last_name="Teacher"
            )
            
            unique_emp_id = f"T{str(uuid.uuid4())[:8].upper()}"
            self.teacher = Teacher.objects.create(
                school=self.school,
                user=teacher_user,
                employee_id=unique_emp_id,
                department=self.department,
                hire_date=date.today(),
                qualification="Master's in Mathematics"
            )
            
            # Create subject
            unique_subject_code = f"SUBJ{str(uuid.uuid4())[:8].upper()}"
            self.subject = Subject.objects.create(
                school=self.school,
                name="Mathematics",
                code=unique_subject_code,
                credit_hours=3
            )
            
            # Create grade
            self.grade = Grade.objects.create(
                school=self.school,
                name="Grade 10",
                level=10,
                max_capacity=30
            )
            
            # Create class
            self.class_obj = Class.objects.create(
                school=self.school,
                academic_year=self.academic_year,
                grade=self.grade,
                name="Section A",
                max_students=30
            )
            
            self.log_result("Test Data Setup", True, "All test data created successfully")
            return True
            
        except Exception as e:
            self.log_result("Test Data Setup", False, f"Failed: {str(e)}")
            return False
    
    def test_schedule_creation_without_integrity_error(self):
        """Test that schedule creation no longer produces IntegrityError"""
        try:
            # First create a ClassSubject
            class_subject = ClassSubject.objects.create(
                school=self.school,
                class_obj=self.class_obj,
                subject=self.subject,
                teacher=self.teacher,
                academic_year=self.academic_year,
                semester=self.semester
            )
            
            self.log_result("ClassSubject Creation", True, 
                          f"ClassSubject created with school: {class_subject.school.name}")
            
            # Now test Schedule creation - this should NOT produce IntegrityError
            schedule = Schedule.objects.create(
                school=self.school,  # Explicitly set school
                class_subject=class_subject,
                day_of_week='monday',
                start_time='09:00',
                end_time='10:00',
                room_number='101'
            )
            
            # Verify schedule has proper school context
            schedule_works = (
                schedule.school == self.school and
                schedule.class_subject.school == self.school
            )
            
            self.log_result("Schedule Creation (No IntegrityError)", schedule_works,
                          f"Schedule created successfully with school: {schedule.school.name}")
            
            return schedule_works
            
        except IntegrityError as e:
            self.log_result("Schedule Creation (No IntegrityError)", False, 
                          f"IntegrityError still occurs: {str(e)}")
            return False
        except Exception as e:
            self.log_result("Schedule Creation (No IntegrityError)", False, 
                          f"Other error: {str(e)}")
            return False
    
    def test_multiple_schedule_creation(self):
        """Test creating multiple schedules for different days"""
        try:
            # Use the existing ClassSubject from the first test
            class_subject = ClassSubject.objects.filter(
                school=self.school,
                class_obj=self.class_obj,
                subject=self.subject,
                teacher=self.teacher,
                academic_year=self.academic_year,
                semester=self.semester
            ).first()

            if not class_subject:
                # Create new one if not exists
                class_subject = ClassSubject.objects.create(
                    school=self.school,
                    class_obj=self.class_obj,
                    subject=self.subject,
                    teacher=self.teacher,
                    academic_year=self.academic_year,
                    semester=self.semester
                )

            # Create multiple schedules for different days
            schedules_created = 0
            days = ['tuesday', 'wednesday', 'thursday', 'friday']  # Skip monday as it might exist

            for i, day in enumerate(days):
                schedule = Schedule.objects.create(
                    school=self.school,
                    class_subject=class_subject,
                    day_of_week=day,
                    start_time=f'{9+i}:00',
                    end_time=f'{10+i}:00',
                    room_number=f'10{i+2}'  # Start from 102
                )
                schedules_created += 1

            success = schedules_created == len(days)
            self.log_result("Multiple Schedule Creation", success,
                          f"Created {schedules_created} schedules successfully")

            return success

        except Exception as e:
            self.log_result("Multiple Schedule Creation", False, f"Failed: {str(e)}")
            return False
    
    def run_all_tests(self):
        """Run all integrity validation tests"""
        print("🔍 Starting IntegrityError Validation Tests")
        print("=" * 60)
        
        if not self.setup_test_data():
            return False
        
        results = []
        results.append(self.test_schedule_creation_without_integrity_error())
        results.append(self.test_multiple_schedule_creation())
        
        # Summary
        passed = sum(results)
        total = len(results)
        success_rate = (passed / total) * 100 if total > 0 else 0
        
        print("\n" + "=" * 60)
        print("📊 INTEGRITY ERROR VALIDATION SUMMARY")
        print("=" * 60)
        print(f"📈 Total Tests: {total}")
        print(f"✅ Passed: {passed}")
        print(f"❌ Failed: {total - passed}")
        print(f"📊 Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("\n✅ INTEGRITY ERROR VALIDATION PASSED")
            print("🎉 Academic schedule creation IntegrityError has been resolved!")
        else:
            print("\n❌ INTEGRITY ERROR VALIDATION FAILED")
            print("⚠️  IntegrityError issues still exist in academic schedule creation")
        
        return success_rate >= 80


if __name__ == "__main__":
    validator = IntegrityErrorValidationTest()
    success = validator.run_all_tests()
    sys.exit(0 if success else 1)
