#!/usr/bin/env python
"""
Test script for library borrowing AJAX functionality
"""

import os
import sys
import django
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

from core.models import School
from library.models import Book, BookCopy, Category, Author, Publisher

User = get_user_model()

def test_library_borrowing_ajax():
    """Test the library borrowing AJAX functionality"""
    
    print("Testing Library Borrowing AJAX Functionality...")
    
    # Create test client
    client = Client()
    
    # Create test user
    try:
        user = User.objects.get(username='librarytestuser')
        user.set_password('testpass123')
        user.is_superuser = True
        user.save()
    except User.DoesNotExist:
        user = User.objects.create_user(
            username='librarytestuser',
            email='<EMAIL>',
            password='testpass123',
            is_superuser=True
        )
    
    # Get or create a school
    school = School.objects.filter(is_active=True).first()
    if not school:
        school = School.objects.create(
            code='LIB001',
            name='Library Test School',
            address='123 Library Street',
            phone='+1234567890',
            email='<EMAIL>',
            principal_name='Principal Library',
            established_date='2020-01-01'
        )
    
    print(f"Using school: {school.name}")
    
    # Login user and select school
    login_success = client.login(username='librarytestuser', password='testpass123')
    print(f"Login successful: {login_success}")
    
    # Select school
    response = client.post('/core/school/select/', {
        'school_id': str(school.id),
        'next': '/library/borrowing/'
    }, HTTP_HOST='localhost')
    
    print(f"School selection: {response.status_code}")
    
    # Test 1: Check if borrowing system page loads
    print("\n1. Testing borrowing system page...")
    try:
        response = client.get('/library/borrowing/', HTTP_HOST='localhost')
        print(f"   Borrowing system page: {response.status_code}")
        
        if response.status_code == 200:
            content = response.content.decode()
            
            # Check for key elements
            page_checks = [
                ('Borrow Book button', 'id="borrowBookBtn"'),
                ('Return Book button', 'id="returnBookBtn"'),
                ('Active Borrowings section', 'Active Borrowings'),
                ('AJAX JavaScript', 'showBorrowBookModal'),
                ('Toast notifications', 'showToast'),
                ('Book search functionality', 'setupBookSearch'),
                ('Return book functionality', 'returnBook'),
                ('Renew book functionality', 'renewBook'),
            ]
            
            for check_name, check_pattern in page_checks:
                if check_pattern in content:
                    print(f"   ✓ {check_name} found")
                else:
                    print(f"   ❌ {check_name} missing")
                    
        else:
            print(f"   ❌ Borrowing system page failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Borrowing system page test failed: {e}")
    
    # Test 2: Test API endpoints availability
    print("\n2. Testing API endpoints...")
    
    api_endpoints = [
        ('/library/api/search/', 'Book search API'),
        ('/library/api/borrow/', 'Book borrowing API'),
        ('/library/api/return/', 'Book return API'),
        ('/library/api/renew/', 'Book renewal API'),
    ]
    
    for endpoint, description in api_endpoints:
        try:
            # Test GET for search, POST for others
            if 'search' in endpoint:
                response = client.get(f'{endpoint}?q=test', HTTP_HOST='localhost')
            else:
                response = client.post(endpoint, {}, HTTP_HOST='localhost')
            
            print(f"   {description}: {response.status_code}")
            
            if response.status_code in [200, 400]:  # 400 is expected for missing data
                print(f"   ✓ {description} endpoint accessible")
            else:
                print(f"   ❌ {description} endpoint issue: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ {description} test failed: {e}")
    
    # Test 3: Test book search API
    print("\n3. Testing book search API...")
    try:
        response = client.get('/library/api/search/?q=test&type=books', HTTP_HOST='localhost')
        print(f"   Search API response: {response.status_code}")
        
        if response.status_code == 200:
            import json
            data = json.loads(response.content)
            
            if 'results' in data:
                print(f"   ✓ Search API returns results structure")
                print(f"   Found {len(data['results'])} books")
            else:
                print("   ❌ Search API missing results structure")
        else:
            print(f"   ❌ Search API failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Search API test failed: {e}")
    
    # Test 4: Test borrowing API with missing data (should return error)
    print("\n4. Testing borrowing API validation...")
    try:
        response = client.post('/library/api/borrow/', {
            # Missing required fields
        }, HTTP_HOST='localhost')
        
        print(f"   Borrow API (missing data): {response.status_code}")
        
        if response.status_code == 400:
            import json
            data = json.loads(response.content)
            
            if 'error' in data:
                print("   ✓ Borrow API properly validates required fields")
            else:
                print("   ❌ Borrow API missing error response")
        else:
            print(f"   → Borrow API response: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Borrow API validation test failed: {e}")
    
    # Test 5: Test return API with missing data (should return error)
    print("\n5. Testing return API validation...")
    try:
        response = client.post('/library/api/return/', {
            # Missing borrowing_id
        }, HTTP_HOST='localhost')
        
        print(f"   Return API (missing data): {response.status_code}")
        
        if response.status_code == 400:
            import json
            data = json.loads(response.content)
            
            if 'error' in data:
                print("   ✓ Return API properly validates required fields")
            else:
                print("   ❌ Return API missing error response")
        else:
            print(f"   → Return API response: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Return API validation test failed: {e}")
    
    # Test 6: Check JavaScript functionality structure
    print("\n6. Testing JavaScript functionality structure...")
    try:
        response = client.get('/library/borrowing/', HTTP_HOST='localhost')
        
        if response.status_code == 200:
            content = response.content.decode()
            
            js_checks = [
                ('CSRF token function', 'getCookie'),
                ('Toast notification function', 'showToast'),
                ('Borrow book modal', 'showBorrowBookModal'),
                ('Book search setup', 'setupBookSearch'),
                ('Form submission', 'submitBorrowForm'),
                ('Return book function', 'function returnBook'),
                ('Renew book function', 'function renewBook'),
                ('Event listeners', 'addEventListener'),
                ('AJAX fetch calls', 'fetch('),
                ('Error handling', 'catch(error'),
            ]
            
            for check_name, check_pattern in js_checks:
                if check_pattern in content:
                    print(f"   ✓ {check_name} implemented")
                else:
                    print(f"   ❌ {check_name} missing")
                    
        else:
            print(f"   ❌ Could not check JavaScript: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ JavaScript structure test failed: {e}")
    
    print("\n✅ Library borrowing AJAX functionality tests completed!")
    
    # Cleanup
    User.objects.filter(username='librarytestuser').delete()
    
    return True

if __name__ == '__main__':
    try:
        test_library_borrowing_ajax()
        print("\n🎉 Library borrowing AJAX functionality is implemented correctly!")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)