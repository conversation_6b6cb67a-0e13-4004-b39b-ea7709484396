#!/usr/bin/env python
"""
Test library borrowing system with proper school context
This test validates that Task 15 library requirements are met
"""

import os
import sys
import django
import uuid
from datetime import datetime, date

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.db import IntegrityError
from core.models import School, AcademicYear, Semester
from library.models import Book, BookCopy, BookBorrowing, Author, Publisher, Category
from students.models import Grade, Class, Student, Parent

User = get_user_model()


class LibrarySchoolContextTest:
    """Test class to validate library operations with school context"""
    
    def __init__(self):
        self.client = Client()
        self.test_results = []
        
    def log_result(self, test_name, success, details=""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {details}")
        self.test_results.append({
            'test': test_name,
            'success': success,
            'details': details
        })
    
    def setup_test_data(self):
        """Setup test data for library validation"""
        try:
            # Create school
            unique_code = f"LIB{str(uuid.uuid4())[:8].upper()}"
            self.school = School.objects.create(
                name="Test Library School",
                code=unique_code,
                address="123 Library Street",
                phone="************",
                email="<EMAIL>",
                website="https://library.com",
                principal_name="Test Principal",
                established_date=date.today(),
                timezone="UTC",
                currency="USD",
                academic_year_start_month=9,
                is_active=True
            )
            
            # Create academic year
            self.academic_year = AcademicYear.objects.create(
                school=self.school,
                name="2025-2026",
                start_date=date(2025, 9, 1),
                end_date=date(2026, 6, 30),
                is_current=True
            )
            
            # Create semester
            self.semester = Semester.objects.create(
                school=self.school,
                academic_year=self.academic_year,
                name="First Semester",
                start_date=date(2025, 9, 1),
                end_date=date(2026, 1, 31),
                is_current=True
            )
            
            # Create parent user
            parent_user = User.objects.create_user(
                username=f"parent_{str(uuid.uuid4())[:8]}",
                email=f"parent_{str(uuid.uuid4())[:8]}@library.com",
                password="testpass123",
                first_name="Test",
                last_name="Parent"
            )
            
            # Create parent
            self.parent = Parent.objects.create(
                school=self.school,
                user=parent_user,
                father_name="Test Father",
                mother_name="Test Mother",
                father_phone="************"
            )
            
            # Create grade
            self.grade = Grade.objects.create(
                school=self.school,
                name="Grade 5",
                level=5,
                max_capacity=30
            )
            
            # Create class
            self.class_obj = Class.objects.create(
                school=self.school,
                academic_year=self.academic_year,
                grade=self.grade,
                name="Section A",
                max_students=30
            )
            
            # Create student user
            student_user = User.objects.create_user(
                username=f"student_{str(uuid.uuid4())[:8]}",
                email=f"student_{str(uuid.uuid4())[:8]}@library.com",
                password="testpass123",
                first_name="Test",
                last_name="Student"
            )
            
            # Create student
            unique_student_id = f"STU{str(uuid.uuid4())[:8].upper()}"
            unique_admission_no = f"ADM{str(uuid.uuid4())[:8].upper()}"
            self.student = Student.objects.create(
                school=self.school,
                user=student_user,
                student_id=unique_student_id,
                admission_number=unique_admission_no,
                first_name="Test",
                last_name="Student",
                date_of_birth=date(2010, 1, 1),
                gender="M",
                nationality="US",
                parent=self.parent,
                current_class=self.class_obj,
                admission_date=date.today()
            )
            
            # Create book
            unique_isbn = f"978{str(uuid.uuid4().int)[:10]}"
            self.book = Book.objects.create(
                school=self.school,
                title="Test Mathematics Book",
                isbn=unique_isbn,
                author="Test Author",
                publisher="Test Publisher",
                publication_year=2024,
                category="Mathematics",
                total_copies=5,
                available_copies=5
            )
            
            # Create book copy
            unique_barcode = f"BC{str(uuid.uuid4())[:10].upper()}"
            self.book_copy = BookCopy.objects.create(
                school=self.school,
                book=self.book,
                barcode=unique_barcode,
                status="available"
            )
            
            self.log_result("Library Test Data Setup", True, "All library test data created successfully")
            return True
            
        except Exception as e:
            self.log_result("Library Test Data Setup", False, f"Failed: {str(e)}")
            return False
    
    def test_book_borrowing_with_school_context(self):
        """Test that book borrowing works with proper school context"""
        try:
            # Create admin user and login
            admin_user = User.objects.create_superuser(
                username=f"admin_{str(uuid.uuid4())[:8]}",
                email=f"admin_{str(uuid.uuid4())[:8]}@library.com",
                password="testpass123"
            )
            
            self.client.force_login(admin_user)
            
            # Set school in session
            session = self.client.session
            session['selected_school_id'] = str(self.school.id)
            session.save()
            
            # Test borrowing via API
            response = self.client.post('/library/api/borrow/', {
                'book_id': str(self.book.id),
                'borrower_type': 'student',
                'borrower_id': str(self.student.id)
            })
            
            # Check if borrowing was successful
            borrowing_success = response.status_code in [200, 201]
            
            if borrowing_success:
                # Verify borrowing record was created with correct school
                borrowing = BookBorrowing.objects.filter(
                    school=self.school,
                    book=self.book,
                    borrower_id=str(self.student.id)
                ).first()
                
                school_context_correct = borrowing and borrowing.school == self.school
                self.log_result("Book Borrowing with School Context", school_context_correct,
                              f"Borrowing created with correct school: {borrowing.school.name if borrowing else 'None'}")
                return school_context_correct
            else:
                self.log_result("Book Borrowing with School Context", False,
                              f"Borrowing API failed with status: {response.status_code}")
                return False
            
        except Exception as e:
            self.log_result("Book Borrowing with School Context", False, f"Failed: {str(e)}")
            return False
    
    def test_book_returning_with_school_context(self):
        """Test that book returning works with proper school context"""
        try:
            # First create a borrowing record
            borrowing = BookBorrowing.objects.create(
                school=self.school,
                book=self.book,
                book_copy=self.book_copy,
                borrower_type='student',
                borrower_id=str(self.student.id),
                borrower_name=f"{self.student.first_name} {self.student.last_name}",
                borrower_email=self.student.user.email,
                borrower_phone=self.parent.father_phone
            )
            
            # Test returning via API
            response = self.client.post('/library/api/return/', {
                'borrowing_id': str(borrowing.id)
            })
            
            # Check if returning was successful
            returning_success = response.status_code in [200, 201]
            
            if returning_success:
                # Verify borrowing record was updated
                borrowing.refresh_from_db()
                return_successful = borrowing.returned_date is not None
                
                self.log_result("Book Returning with School Context", return_successful,
                              f"Book returned successfully: {return_successful}")
                return return_successful
            else:
                self.log_result("Book Returning with School Context", False,
                              f"Returning API failed with status: {response.status_code}")
                return False
            
        except Exception as e:
            self.log_result("Book Returning with School Context", False, f"Failed: {str(e)}")
            return False
    
    def test_library_data_filtering_by_school(self):
        """Test that library data is properly filtered by school"""
        try:
            # Create another school with books
            other_school = School.objects.create(
                name="Other Library School",
                code=f"OTHER{str(uuid.uuid4())[:8].upper()}",
                address="456 Other Street",
                phone="************",
                email="<EMAIL>",
                website="https://otherlibrary.com",
                principal_name="Other Principal",
                established_date=date.today(),
                timezone="UTC",
                currency="USD",
                academic_year_start_month=9,
                is_active=True
            )
            
            # Create book in other school
            other_book = Book.objects.create(
                school=other_school,
                title="Other School Book",
                isbn=f"979{str(uuid.uuid4().int)[:10]}",
                author="Other Author",
                publisher="Other Publisher",
                publication_year=2024,
                category="Science",
                total_copies=3,
                available_copies=3
            )
            
            # Test that books are filtered by school
            current_school_books = Book.objects.filter(school=self.school).count()
            other_school_books = Book.objects.filter(school=other_school).count()
            
            filtering_works = (current_school_books >= 1 and other_school_books >= 1)
            
            self.log_result("Library Data Filtering by School", filtering_works,
                          f"Current school books: {current_school_books}, Other school books: {other_school_books}")
            
            return filtering_works
            
        except Exception as e:
            self.log_result("Library Data Filtering by School", False, f"Failed: {str(e)}")
            return False
    
    def run_all_tests(self):
        """Run all library validation tests"""
        print("📚 Starting Library School Context Validation Tests")
        print("=" * 60)
        
        if not self.setup_test_data():
            return False
        
        results = []
        results.append(self.test_book_borrowing_with_school_context())
        results.append(self.test_book_returning_with_school_context())
        results.append(self.test_library_data_filtering_by_school())
        
        # Summary
        passed = sum(results)
        total = len(results)
        success_rate = (passed / total) * 100 if total > 0 else 0
        
        print("\n" + "=" * 60)
        print("📊 LIBRARY SCHOOL CONTEXT VALIDATION SUMMARY")
        print("=" * 60)
        print(f"📈 Total Tests: {total}")
        print(f"✅ Passed: {passed}")
        print(f"❌ Failed: {total - passed}")
        print(f"📊 Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("\n✅ LIBRARY SCHOOL CONTEXT VALIDATION PASSED")
            print("🎉 Library borrowing system works correctly with school context!")
        else:
            print("\n❌ LIBRARY SCHOOL CONTEXT VALIDATION FAILED")
            print("⚠️  Library system issues still exist with school context")
        
        return success_rate >= 80


if __name__ == "__main__":
    validator = LibrarySchoolContextTest()
    success = validator.run_all_tests()
    sys.exit(0 if success else 1)
