#!/usr/bin/env python
"""
Simple test script to verify the main fixes work
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from core.models import School
from academics.models import Teacher
from hr.models import Department

User = get_user_model()

def test_main_fixes():
    """Test the main fixes"""
    print("Testing Main Fixes")
    print("=" * 30)
    
    # Test 1: Department model works
    try:
        school = School.objects.first()
        if school:
            dept = Department.objects.filter(school=school).first()
            if dept:
                print("✓ Department model works correctly")
            else:
                print("✗ No departments found")
        else:
            print("✗ No schools found")
    except Exception as e:
        print(f"✗ Department model error: {e}")
    
    # Test 2: Teacher-Department relationship works
    try:
        teacher = Teacher.objects.first()
        if teacher and hasattr(teacher, 'department'):
            print("✓ Teacher-Department relationship works")
        else:
            print("✗ Teacher-Department relationship issue")
    except Exception as e:
        print(f"✗ Teacher-Department relationship error: {e}")
    
    # Test 3: School selection page loads (basic test)
    try:
        client = Client()
        user = User.objects.filter(is_superuser=True).first()
        if user:
            client.force_login(user)
            response = client.get('/core/school/select/')
            if response.status_code == 200:
                print("✓ School selection page loads")
            else:
                print(f"✗ School selection page error: {response.status_code}")
        else:
            print("✗ No superuser found for testing")
    except Exception as e:
        print(f"✗ School selection page error: {e}")
    
    # Test 4: Performance optimization imports
    try:
        import core.performance_optimization
        print("✓ Performance optimization module imports correctly")
    except Exception as e:
        print(f"✗ Performance optimization import error: {e}")
    
    # Test 5: Models inherit from BaseModel correctly
    try:
        from academics.models import ClassSubject, Schedule
        from core.models import BaseModel
        
        if issubclass(ClassSubject, BaseModel) and issubclass(Schedule, BaseModel):
            print("✓ Academic models inherit from BaseModel correctly")
        else:
            print("✗ Academic models inheritance issue")
    except Exception as e:
        print(f"✗ Model inheritance error: {e}")
    
    print("\nTest completed!")

if __name__ == '__main__':
    test_main_fixes()