#!/usr/bin/env python
"""
Comprehensive test suite for middleware functionality
Tests various user scenarios and edge cases
"""

import os
import sys
import django
from unittest.mock import Mock, patch

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

from django.test import TestCase, RequestFactory, Client
from django.contrib.auth import get_user_model
from django.contrib.sessions.middleware import SessionMiddleware
from django.contrib.auth.middleware import AuthenticationMiddleware
from django.http import HttpResponse
from core.models import School
from core.middleware import SchoolSelectionMiddleware, SchoolContextMiddleware
from core.school_utils import get_current_school, set_current_school

User = get_user_model()


class MiddlewareTestCase(TestCase):
    """Test case for middleware functionality"""
    
    def setUp(self):
        """Set up test data"""
        self.factory = RequestFactory()
        self.client = Client()
        
        # Create test school
        self.school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test Street",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date="2020-01-01"
        )
        
        # Create test users
        self.admin_user = User.objects.create_user(
            username='admin',
            password='admin123',
            email='<EMAIL>',
            user_type='admin',
            is_staff=True
        )
        
        self.teacher_user = User.objects.create_user(
            username='teacher',
            password='teacher123',
            email='<EMAIL>',
            user_type='teacher'
        )
        
        self.student_user = User.objects.create_user(
            username='student',
            password='student123',
            email='<EMAIL>',
            user_type='student'
        )
    
    def get_request(self, user=None, path='/'):
        """Create a request with session and authentication"""
        request = self.factory.get(path)
        
        # Add session
        session_middleware = SessionMiddleware(lambda req: HttpResponse())
        session_middleware.process_request(request)
        request.session.save()
        
        # Add authentication
        if user:
            request.user = user
        else:
            auth_middleware = AuthenticationMiddleware(lambda req: HttpResponse())
            auth_middleware.process_request(request)
        
        return request
    
    def test_school_selection_middleware_authenticated_user(self):
        """Test SchoolSelectionMiddleware with authenticated user"""
        middleware = SchoolSelectionMiddleware(lambda req: HttpResponse())
        request = self.get_request(self.admin_user)
        
        # Test without school selection
        response = middleware(request)
        self.assertEqual(response.status_code, 200)
        
        # Test with school selection
        set_current_school(request, self.school)
        response = middleware(request)
        self.assertEqual(response.status_code, 200)
    
    def test_school_selection_middleware_unauthenticated_user(self):
        """Test SchoolSelectionMiddleware with unauthenticated user"""
        middleware = SchoolSelectionMiddleware(lambda req: HttpResponse())
        request = self.get_request()
        
        response = middleware(request)
        self.assertEqual(response.status_code, 200)
    
    def test_school_context_middleware(self):
        """Test SchoolContextMiddleware"""
        middleware = SchoolContextMiddleware(lambda req: HttpResponse())
        request = self.get_request(self.admin_user)
        
        # Set school in session
        set_current_school(request, self.school)
        
        response = middleware(request)
        self.assertEqual(response.status_code, 200)
        self.assertTrue(hasattr(request, 'school'))
        self.assertEqual(request.school, self.school)
    
    def test_middleware_with_different_user_types(self):
        """Test middleware with different user types"""
        middleware = SchoolContextMiddleware(lambda req: HttpResponse())
        
        # Test with admin user
        request = self.get_request(self.admin_user)
        set_current_school(request, self.school)
        response = middleware(request)
        self.assertEqual(response.status_code, 200)
        
        # Test with teacher user
        request = self.get_request(self.teacher_user)
        set_current_school(request, self.school)
        response = middleware(request)
        self.assertEqual(response.status_code, 200)
        
        # Test with student user
        request = self.get_request(self.student_user)
        set_current_school(request, self.school)
        response = middleware(request)
        self.assertEqual(response.status_code, 200)
    
    def test_middleware_error_handling(self):
        """Test middleware error handling"""
        middleware = SchoolContextMiddleware(lambda req: HttpResponse())
        request = self.get_request(self.admin_user)
        
        # Test with invalid school ID in session
        request.session['selected_school_id'] = 'invalid-id'
        response = middleware(request)
        self.assertEqual(response.status_code, 200)
        
        # Test with non-existent school ID
        request.session['selected_school_id'] = '99999999-9999-9999-9999-999999999999'
        response = middleware(request)
        self.assertEqual(response.status_code, 200)
    
    def test_middleware_performance(self):
        """Test middleware performance with multiple requests"""
        middleware = SchoolContextMiddleware(lambda req: HttpResponse())
        
        # Test multiple requests to ensure caching works
        for i in range(10):
            request = self.get_request(self.admin_user)
            set_current_school(request, self.school)
            response = middleware(request)
            self.assertEqual(response.status_code, 200)


def run_middleware_tests():
    """Run all middleware tests"""
    import unittest
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(MiddlewareTestCase)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_middleware_tests()
    sys.exit(0 if success else 1)