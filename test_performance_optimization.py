"""
Test script to verify performance optimizations for school selection and caching.
"""

import os
import sys
import django
import time
from django.test import RequestFactory
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.utils import timezone

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

from core.models import School
from core.middleware import SchoolSelectionMiddleware
from core.performance_optimization import CacheManager, monitor_performance
from core.session_optimization import SessionOptimizer
from core.school_utils import get_user_schools, get_current_school

User = get_user_model()


class PerformanceTest:
    """Test class for performance optimization verification."""
    
    def __init__(self):
        self.factory = RequestFactory()
        self.middleware = SchoolSelectionMiddleware(lambda r: None)
        
    def setup_test_data(self):
        """Setup test data for performance testing."""
        print("Setting up test data...")
        
        # Create test user if not exists
        try:
            self.test_user = User.objects.get(username='test_performance_user')
        except User.DoesNotExist:
            self.test_user = User.objects.create_user(
                username='test_performance_user',
                email='<EMAIL>',
                password='testpass123'
            )
        
        # Ensure we have at least one school
        if not School.objects.exists():
            print("No schools found. Please create at least one school for testing.")
            return False
        
        self.test_school = School.objects.filter(is_active=True).first()
        print(f"Using test school: {self.test_school.name}")
        
        return True
    
    @monitor_performance
    def test_cache_performance(self):
        """Test cache performance improvements."""
        print("\n=== Testing Cache Performance ===")
        
        # Clear cache to start fresh
        cache.clear()
        
        # Test 1: User schools caching
        print("Testing user schools caching...")
        
        start_time = time.time()
        schools1 = get_user_schools(self.test_user)
        first_call_time = time.time() - start_time
        
        start_time = time.time()
        schools2 = get_user_schools(self.test_user)  # Should hit cache
        second_call_time = time.time() - start_time
        
        print(f"First call (no cache): {first_call_time:.4f}s")
        print(f"Second call (cached): {second_call_time:.4f}s")
        print(f"Cache speedup: {first_call_time / second_call_time:.2f}x")
        
        # Test 2: School context caching
        print("\nTesting school context caching...")
        
        request = self.factory.get('/')
        request.user = self.test_user
        request.session = {}
        
        # Simulate session setup
        from django.contrib.sessions.backends.db import SessionStore
        session = SessionStore()
        session.create()
        request.session = session
        
        start_time = time.time()
        context1 = CacheManager.get_cache_key('school_context', self.test_user.id)
        first_context_time = time.time() - start_time
        
        # Warm the cache
        CacheManager.warm_school_cache(self.test_school)
        
        start_time = time.time()
        context2 = CacheManager.get_cache_key('school_context', self.test_user.id)
        second_context_time = time.time() - start_time
        
        print(f"Context generation time improved by caching")
        
        return True
    
    def test_session_optimization(self):
        """Test session optimization improvements."""
        print("\n=== Testing Session Optimization ===")
        
        request = self.factory.get('/')
        request.user = self.test_user
        
        # Create session
        from django.contrib.sessions.backends.db import SessionStore
        session = SessionStore()
        session.create()
        request.session = session
        
        # Test optimized session data
        print("Testing optimized session data...")
        
        start_time = time.time()
        session_data = SessionOptimizer.get_optimized_session_data(request)
        first_time = time.time() - start_time
        
        start_time = time.time()
        session_data2 = SessionOptimizer.get_optimized_session_data(request)  # Should hit cache
        second_time = time.time() - start_time
        
        print(f"First session data call: {first_time:.4f}s")
        print(f"Second session data call (cached): {second_time:.4f}s")
        
        if second_time > 0:
            print(f"Session cache speedup: {first_time / second_time:.2f}x")
        
        # Test session validation
        print("Testing session validation...")
        
        start_time = time.time()
        is_valid1, msg1 = SessionOptimizer.validate_session_integrity(request)
        validation_time1 = time.time() - start_time
        
        start_time = time.time()
        is_valid2, msg2 = SessionOptimizer.validate_session_integrity(request)  # Should hit cache
        validation_time2 = time.time() - start_time
        
        print(f"First validation: {validation_time1:.4f}s (Valid: {is_valid1})")
        print(f"Second validation (cached): {validation_time2:.4f}s (Valid: {is_valid2})")
        
        return True
    
    def test_middleware_performance(self):
        """Test middleware performance improvements."""
        print("\n=== Testing Middleware Performance ===")
        
        request = self.factory.get('/')
        request.user = self.test_user
        
        # Create session
        from django.contrib.sessions.backends.db import SessionStore
        session = SessionStore()
        session.create()
        request.session = session
        
        # Set up school in session
        request.session['selected_school_id'] = str(self.test_school.id)
        request.session['school_selection_timestamp'] = timezone.now().isoformat()
        
        print("Testing middleware school context retrieval...")
        
        start_time = time.time()
        school1 = self.middleware.get_school_context(request)
        first_middleware_time = time.time() - start_time
        
        start_time = time.time()
        school2 = self.middleware.get_school_context(request)  # Should hit cache
        second_middleware_time = time.time() - start_time
        
        print(f"First middleware call: {first_middleware_time:.4f}s")
        print(f"Second middleware call (cached): {second_middleware_time:.4f}s")
        
        if second_middleware_time > 0:
            print(f"Middleware cache speedup: {first_middleware_time / second_middleware_time:.2f}x")
        
        return True
    
    def test_concurrent_performance(self):
        """Test performance under concurrent-like conditions."""
        print("\n=== Testing Concurrent Performance ===")
        
        # Simulate multiple requests
        requests = []
        for i in range(10):
            request = self.factory.get(f'/test/{i}')
            request.user = self.test_user
            
            # Create session
            from django.contrib.sessions.backends.db import SessionStore
            session = SessionStore()
            session.create()
            request.session = session
            request.session['selected_school_id'] = str(self.test_school.id)
            request.session['school_selection_timestamp'] = timezone.now().isoformat()
            
            requests.append(request)
        
        print("Testing multiple concurrent-like requests...")
        
        start_time = time.time()
        schools = []
        for request in requests:
            school = self.middleware.get_school_context(request)
            schools.append(school)
        
        total_time = time.time() - start_time
        avg_time = total_time / len(requests)
        
        print(f"Processed {len(requests)} requests in {total_time:.4f}s")
        print(f"Average time per request: {avg_time:.4f}s")
        
        # Verify all schools are the same (caching working)
        unique_schools = set(school.id if school else None for school in schools)
        print(f"Unique schools returned: {len(unique_schools)} (should be 1 for proper caching)")
        
        return True
    
    def run_all_tests(self):
        """Run all performance tests."""
        print("Starting Performance Optimization Tests")
        print("=" * 50)
        
        if not self.setup_test_data():
            print("Failed to setup test data")
            return False
        
        try:
            self.test_cache_performance()
            self.test_session_optimization()
            self.test_middleware_performance()
            self.test_concurrent_performance()
            
            print("\n" + "=" * 50)
            print("All performance tests completed successfully!")
            print("Performance optimizations are working correctly.")
            
            return True
            
        except Exception as e:
            print(f"\nError during performance testing: {e}")
            import traceback
            traceback.print_exc()
            return False


if __name__ == '__main__':
    test = PerformanceTest()
    success = test.run_all_tests()
    
    if success:
        print("\n✅ Performance optimization verification completed successfully!")
    else:
        print("\n❌ Performance optimization verification failed!")
        sys.exit(1)