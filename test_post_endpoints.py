#!/usr/bin/env python
"""
Test POST endpoints to verify school context and form handling
"""

import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from core.models import School, AcademicYear, Semester
from academics.models import Subject, Teacher, ClassSubject
from hr.models import Department
from students.models import Grade, Class

User = get_user_model()

def test_post_endpoints():
    """Test POST endpoints with proper school context"""
    print("Testing POST Endpoints")
    print("=" * 30)
    
    client = Client()
    
    # Get or create test data
    try:
        # Get superuser
        user = User.objects.filter(is_superuser=True).first()
        if not user:
            user = User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
        
        # Get school
        school = School.objects.first()
        if not school:
            school = School.objects.create(
                name="Test School",
                code="TEST999",
                address="Test Address",
                phone="+1234567890",
                email="<EMAIL>",
                principal_name="Test Principal",
                established_date="2020-01-01"
            )
        
        print(f"Using school: {school.name}")
        
        # Login
        client.force_login(user)
        
        # Test 1: School selection POST
        print("\n1. Testing school selection POST...")
        response = client.post('/core/school/select/', {
            'school_id': str(school.id)
        })
        
        if response.status_code in [200, 302]:
            print("✓ School selection POST works")
        else:
            print(f"✗ School selection POST failed: {response.status_code}")
            if hasattr(response, 'content'):
                print(f"Response: {response.content[:200]}")
        
        # Test 2: AJAX school switch
        print("\n2. Testing AJAX school switch...")
        response = client.post('/core/school/switch/', {
            'school_id': str(school.id)
        }, HTTP_X_REQUESTED_WITH='XMLHttpRequest')
        
        if response.status_code == 200:
            try:
                data = response.json()
                if data.get('success'):
                    print("✓ AJAX school switch works")
                else:
                    print(f"✗ AJAX school switch failed: {data.get('message')}")
            except:
                print("✗ AJAX school switch - invalid JSON response")
        else:
            print(f"✗ AJAX school switch failed: {response.status_code}")
        
        # Test 3: Create subject with school context
        print("\n3. Testing subject creation...")
        
        # First select school
        client.post('/core/school/select/', {'school_id': str(school.id)})
        
        response = client.post('/academics/subjects/add/', {
            'name': 'Test Subject',
            'code': 'TEST001',
            'description': 'Test subject description',
            'credit_hours': 3
        })
        
        if response.status_code in [200, 302]:
            print("✓ Subject creation works")
        else:
            print(f"✗ Subject creation failed: {response.status_code}")
        
        # Test 4: Test schedule creation (if we have the required data)
        print("\n4. Testing schedule creation...")
        
        # Get or create required data
        academic_year = AcademicYear.objects.filter(school=school).first()
        if not academic_year:
            academic_year = AcademicYear.objects.create(
                name="2024-2025",
                start_date="2024-09-01",
                end_date="2025-06-30",
                is_current=True,
                school=school
            )
        
        semester = Semester.objects.filter(school=school).first()
        if not semester:
            semester = Semester.objects.create(
                academic_year=academic_year,
                name="Fall 2024",
                start_date="2024-09-01",
                end_date="2025-01-31",
                is_current=True,
                school=school
            )
        
        grade = Grade.objects.filter(school=school).first()
        if not grade:
            grade = Grade.objects.create(
                name="Grade 10",
                level=10,
                school=school
            )
        
        class_obj = Class.objects.filter(school=school).first()
        if not class_obj:
            class_obj = Class.objects.create(
                name="10A",
                grade=grade,
                academic_year=academic_year,
                max_students=30,
                school=school
            )
        
        subject = Subject.objects.filter(school=school).first()
        teacher = Teacher.objects.filter(school=school).first()
        
        if subject and teacher and class_obj:
            response = client.post('/academics/schedules/create/', {
                'class_id': str(class_obj.id),
                'subject_id': str(subject.id),
                'teacher_id': str(teacher.id),
                'day_of_week': 'monday',
                'start_time': '09:00',
                'end_time': '10:00',
                'room_number': '101'
            })
            
            if response.status_code in [200, 302]:
                print("✓ Schedule creation works")
            else:
                print(f"✗ Schedule creation failed: {response.status_code}")
        else:
            print("✗ Schedule creation skipped - missing required data")
        
        print("\nPOST endpoint tests completed!")
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_post_endpoints()