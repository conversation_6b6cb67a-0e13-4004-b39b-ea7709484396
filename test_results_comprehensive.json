[{"test": "Setup Test Data", "status": "PASS", "message": "All test data created successfully", "timestamp": "2025-08-23T16:35:48.789109"}, {"test": "User Login", "status": "PASS", "message": "User logged in successfully", "timestamp": "2025-08-23T16:35:53.303006"}, {"test": "School Selection Page", "status": "FAIL", "message": "Status code: 400", "timestamp": "2025-08-23T16:36:08.307459"}, {"test": "Academic Model Creation", "status": "FAIL", "message": "Error: UNIQUE constraint failed: academics_classsubject.class_obj_id, academics_classsubject.subject_id, academics_classsubject.academic_year_id, academics_classsubject.semester_id", "timestamp": "2025-08-23T16:36:12.854598"}, {"test": "Schedule Creation View", "status": "FAIL", "message": "Status code: 400", "timestamp": "2025-08-23T16:36:18.556755"}, {"test": "Department Model Test", "status": "FAIL", "message": "Error: UNIQUE constraint failed: hr_department.code", "timestamp": "2025-08-23T16:36:18.560250"}, {"test": "Library Functionality", "status": "FAIL", "message": "Error: UNIQUE constraint failed: library_book.rfid_tag", "timestamp": "2025-08-23T16:36:24.320641"}, {"test": "Performance Cache", "status": "PASS", "message": "School statistics cached successfully", "timestamp": "2025-08-23T16:36:24.321229"}]