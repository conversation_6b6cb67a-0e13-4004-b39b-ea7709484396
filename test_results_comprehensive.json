[{"test": "Setup Test Data", "status": "PASS", "message": "All test data created successfully", "timestamp": "2025-08-14T17:52:09.876404"}, {"test": "User Login", "status": "PASS", "message": "User logged in successfully", "timestamp": "2025-08-14T17:52:12.613025"}, {"test": "School Selection Page", "status": "FAIL", "message": "Status code: 400", "timestamp": "2025-08-14T17:52:13.577048"}, {"test": "Academic Model Creation", "status": "FAIL", "message": "Error: UNIQUE constraint failed: academics_classsubject.class_obj_id, academics_classsubject.subject_id, academics_classsubject.academic_year_id, academics_classsubject.semester_id", "timestamp": "2025-08-14T17:52:16.366250"}, {"test": "Schedule Creation View", "status": "FAIL", "message": "Status code: 400", "timestamp": "2025-08-14T17:52:18.080383"}, {"test": "Department Model Test", "status": "FAIL", "message": "Error: UNIQUE constraint failed: hr_department.code", "timestamp": "2025-08-14T17:52:18.080383"}, {"test": "Library Functionality", "status": "FAIL", "message": "Error: UNIQUE constraint failed: library_book.rfid_tag", "timestamp": "2025-08-14T17:52:20.366417"}, {"test": "Performance Cache", "status": "PASS", "message": "School statistics cached successfully", "timestamp": "2025-08-14T17:52:20.366417"}]