#!/usr/bin/env python
"""
Test script to verify school selection redirection fix
"""

import os
import sys
import django
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

from core.models import School

User = get_user_model()

def test_school_selection_redirection():
    """Test that school selection properly redirects after selection"""
    
    print("Testing School Selection Redirection Fix...")
    
    # Create test client
    client = Client()
    
    # Create test user
    try:
        user = User.objects.get(username='redirecttestuser')
        user.set_password('testpass123')
        user.is_superuser = True
        user.save()
    except User.DoesNotExist:
        user = User.objects.create_user(
            username='redirecttestuser',
            email='<EMAIL>',
            password='testpass123',
            is_superuser=True
        )
    
    # Create test school
    school, created = School.objects.get_or_create(
        code='REDIR001',
        defaults={
            'name': 'Redirect Test School',
            'address': '123 Redirect Street',
            'phone': '+1234567890',
            'email': '<EMAIL>',
            'principal_name': 'Principal Redirect',
            'established_date': '2020-01-01'
        }
    )
    
    # Login user
    login_success = client.login(username='redirecttestuser', password='testpass123')
    print(f"Login successful: {login_success}")
    
    # Test 1: Test redirection to school selection when no school selected
    print("1. Testing redirection to school selection...")
    try:
        response = client.get('/library/borrowing/', HTTP_HOST='localhost')
        print(f"   Response status: {response.status_code}")
        
        if response.status_code == 302:
            redirect_url = response.url
            print(f"   Redirected to: {redirect_url}")
            
            if 'school/select' in redirect_url and 'next=' in redirect_url:
                print("   ✓ Correctly redirected to school selection with next parameter")
            else:
                print("   ❌ Incorrect redirection")
        else:
            print(f"   ❌ Expected redirect (302), got {response.status_code}")
    except Exception as e:
        print(f"   ❌ Redirection test failed: {e}")
    
    # Test 2: Test school selection with next parameter
    print("2. Testing school selection with next parameter...")
    try:
        # Access school selection page with next parameter
        response = client.get('/core/school/select/?next=/library/borrowing/', HTTP_HOST='localhost')
        print(f"   School selection page status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.content.decode()
            if 'next' in content and '/library/borrowing/' in content:
                print("   ✓ School selection page loaded with next parameter")
            else:
                print("   ❌ Next parameter not properly handled")
        else:
            print(f"   ❌ School selection page failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ School selection page test failed: {e}")
    
    # Test 3: Test school selection POST with redirection
    print("3. Testing school selection POST with redirection...")
    try:
        # Submit school selection form
        response = client.post('/core/school/select/', {
            'school_id': str(school.id),
            'next': '/library/borrowing/'
        }, HTTP_HOST='localhost')
        
        print(f"   POST response status: {response.status_code}")
        
        if response.status_code == 302:
            redirect_url = response.url
            print(f"   Redirected to: {redirect_url}")
            
            if redirect_url == '/library/borrowing/':
                print("   ✓ Correctly redirected to intended destination")
                
                # Verify session was updated
                session = client.session
                if session.get('selected_school_id') == str(school.id):
                    print("   ✓ School selection stored in session")
                else:
                    print("   ❌ School selection not stored in session")
            else:
                print(f"   ❌ Incorrect redirection target: {redirect_url}")
        else:
            print(f"   ❌ Expected redirect (302), got {response.status_code}")
    except Exception as e:
        print(f"   ❌ School selection POST test failed: {e}")
    
    # Test 4: Test that subsequent requests don't redirect to school selection
    print("4. Testing subsequent requests after school selection...")
    try:
        # Try to access the original destination
        response = client.get('/library/borrowing/', HTTP_HOST='localhost')
        print(f"   Library page response status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✓ Successfully accessed intended page after school selection")
        elif response.status_code == 302:
            redirect_url = response.url
            if 'school/select' in redirect_url:
                print("   ❌ Still being redirected to school selection")
            else:
                print(f"   → Redirected to: {redirect_url} (might be normal)")
        else:
            print(f"   ❌ Unexpected response: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Subsequent request test failed: {e}")
    
    # Test 5: Test dashboard redirection
    print("5. Testing dashboard redirection...")
    try:
        # Test default redirection (should go to dashboard)
        response = client.post('/core/school/select/', {
            'school_id': str(school.id)
            # No 'next' parameter - should default to dashboard
        }, HTTP_HOST='localhost')
        
        print(f"   Dashboard redirect response status: {response.status_code}")
        
        if response.status_code == 302:
            redirect_url = response.url
            print(f"   Redirected to: {redirect_url}")
            
            if '/dashboard/' in redirect_url:
                print("   ✓ Correctly redirected to dashboard by default")
            else:
                print(f"   ❌ Incorrect default redirection: {redirect_url}")
        else:
            print(f"   ❌ Expected redirect (302), got {response.status_code}")
    except Exception as e:
        print(f"   ❌ Dashboard redirection test failed: {e}")
    
    print("\n✅ School selection redirection tests completed!")
    
    # Cleanup
    User.objects.filter(username='redirecttestuser').delete()
    School.objects.filter(code='REDIR001').delete()
    
    return True

if __name__ == '__main__':
    try:
        test_school_selection_redirection()
        print("\n🎉 School selection redirection fix is working correctly!")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)