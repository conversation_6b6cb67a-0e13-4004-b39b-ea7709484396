#!/usr/bin/env python
"""
Test script for school selection views functionality
"""

import os
import sys
import django
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.contrib.messages import get_messages
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

from core.models import School
from core.school_utils import get_user_schools, set_current_school

User = get_user_model()

def test_school_selection_views():
    """Test the school selection views functionality"""
    
    print("Testing School Selection Views...")
    
    # Create test client
    client = Client()
    
    # Create test user (or get existing one)
    try:
        user = User.objects.get(username='testuser')
        user.set_password('testpass123')
        user.is_superuser = True
        user.save()
    except User.DoesNotExist:
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            is_superuser=True  # Make superuser to access all schools
        )
    
    # Create test schools (or get existing ones)
    school1, created = School.objects.get_or_create(
        code='TS001',
        defaults={
            'name': 'Test School 1',
            'address': '123 Test Street',
            'phone': '+1234567890',
            'email': '<EMAIL>',
            'principal_name': 'Principal One',
            'established_date': '2020-01-01'
        }
    )
    
    school2, created = School.objects.get_or_create(
        code='TS002',
        defaults={
            'name': 'Test School 2',
            'address': '456 Test Avenue',
            'phone': '+1234567891',
            'email': '<EMAIL>',
            'principal_name': 'Principal Two',
            'established_date': '2021-01-01'
        }
    )
    
    # Login user
    client.login(username='testuser', password='testpass123')
    
    # Test 1: School selection page loads
    print("1. Testing school selection page...")
    response = client.get(reverse('core:school_select'), HTTP_HOST='localhost')
    if response.status_code != 200:
        print(f"Response status: {response.status_code}")
        print(f"Response content: {response.content.decode()}")
    assert response.status_code == 200, f"Expected 200, got {response.status_code}"
    assert 'schools' in response.context, "Schools not in context"
    assert response.context['schools'].count() >= 2, f"Expected at least 2 schools, got {response.context['schools'].count()}"
    print("   ✓ School selection page loads correctly")
    
    # Test 2: School selection via POST
    print("2. Testing school selection via POST...")
    response = client.post(reverse('core:school_select'), {
        'school_id': str(school1.id),
        'next': '/dashboard/'
    }, HTTP_HOST='localhost')
    assert response.status_code == 302, f"Expected redirect (302), got {response.status_code}"
    
    # Check session
    session = client.session
    assert 'selected_school_id' in session, "School ID not stored in session"
    assert session['selected_school_id'] == str(school1.id), "Wrong school ID in session"
    print("   ✓ School selection via POST works correctly")
    
    # Test 3: AJAX school switching
    print("3. Testing AJAX school switching...")
    response = client.post(reverse('core:switch_school'), {
        'school_id': str(school2.id)
    }, HTTP_HOST='localhost')
    assert response.status_code == 200, f"Expected 200, got {response.status_code}"
    
    data = json.loads(response.content)
    assert data['success'] == True, f"Expected success=True, got {data}"
    assert data['school_name'] == 'Test School 2', f"Expected 'Test School 2', got {data['school_name']}"
    
    # Check session updated
    session = client.session
    assert session['selected_school_id'] == str(school2.id), "School not switched in session"
    print("   ✓ AJAX school switching works correctly")
    
    # Test 4: Get current school API
    print("4. Testing get current school API...")
    response = client.get(reverse('core:get_current_school'), HTTP_HOST='localhost')
    assert response.status_code == 200, f"Expected 200, got {response.status_code}"
    
    data = json.loads(response.content)
    assert data['success'] == True, f"Expected success=True, got {data}"
    assert data['school']['name'] == 'Test School 2', f"Expected 'Test School 2', got {data['school']['name']}"
    print("   ✓ Get current school API works correctly")
    
    # Test 5: Invalid school selection
    print("5. Testing invalid school selection...")
    response = client.post(reverse('core:switch_school'), {
        'school_id': 'invalid-id'
    }, HTTP_HOST='localhost')
    assert response.status_code == 200, f"Expected 200, got {response.status_code}"
    
    data = json.loads(response.content)
    assert data['success'] == False, f"Expected success=False for invalid ID, got {data}"
    print("   ✓ Invalid school selection handled correctly")
    
    # Test 6: Missing school ID
    print("6. Testing missing school ID...")
    response = client.post(reverse('core:switch_school'), {}, HTTP_HOST='localhost')
    assert response.status_code == 200, f"Expected 200, got {response.status_code}"
    
    data = json.loads(response.content)
    assert data['success'] == False, f"Expected success=False for missing ID, got {data}"
    print("   ✓ Missing school ID handled correctly")
    
    print("\n✅ All school selection view tests passed!")
    
    # Cleanup
    User.objects.filter(username='testuser').delete()
    School.objects.filter(code__in=['TS001', 'TS002']).delete()
    
    return True

if __name__ == '__main__':
    try:
        test_school_selection_views()
        print("\n🎉 School selection views implementation is working correctly!")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)