#!/usr/bin/env python
"""
Integration tests for school selection workflow and library operations
"""

import os
import sys
import django
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from core.models import School, AcademicYear, Semester
from academics.models import Subject, Teacher, ClassSubject, Schedule
from hr.models import Department
from students.models import Grade, Class, Student
from library.models import Book, BookCopy, BookBorrowing

User = get_user_model()


class SchoolSelectionWorkflowTestCase(TestCase):
    """Integration tests for school selection workflow"""
    
    def setUp(self):
        """Set up test data"""
        self.client = Client()
        
        # Create test schools
        self.school1 = School.objects.create(
            name="Primary School",
            code="PRI001",
            address="123 Primary Street",
            phone="+1234567890",
            email="<EMAIL>",
            principal_name="Primary Principal",
            established_date="2020-01-01"
        )
        
        self.school2 = School.objects.create(
            name="Secondary School",
            code="SEC001",
            address="456 Secondary Avenue",
            phone="+1234567891",
            email="<EMAIL>",
            principal_name="Secondary Principal",
            established_date="2020-01-01"
        )
        
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            email='<EMAIL>',
            user_type='admin',
            is_superuser=True
        )
        
        # Create academic data for school1
        self.academic_year = AcademicYear.objects.create(
            name="2024-2025",
            start_date="2024-09-01",
            end_date="2025-06-30",
            is_current=True,
            school=self.school1
        )
        
        self.semester = Semester.objects.create(
            academic_year=self.academic_year,
            name="Fall 2024",
            start_date="2024-09-01",
            end_date="2025-01-31",
            is_current=True,
            school=self.school1
        )
        
        self.department = Department.objects.create(
            name="Mathematics",
            code="MATH",
            school=self.school1
        )
        
        self.grade = Grade.objects.create(
            name="Grade 10",
            level=10,
            school=self.school1
        )
        
        self.class_obj = Class.objects.create(
            name="10A",
            grade=self.grade,
            academic_year=self.academic_year,
            max_students=30,
            school=self.school1
        )
        
        self.subject = Subject.objects.create(
            name="Mathematics",
            code="MATH101",
            description="Basic Mathematics",
            credit_hours=3,
            school=self.school1
        )
        
        # Create teacher
        teacher_user = User.objects.create_user(
            username='teacher1',
            password='teacherpass123',
            email='<EMAIL>',
            user_type='teacher'
        )
        
        self.teacher = Teacher.objects.create(
            user=teacher_user,
            employee_id="T001",
            qualification="MSc Mathematics",
            hire_date="2020-01-01",
            experience_years=5,
            department=self.department,
            school=self.school1
        )
        
        self.teacher.subjects.add(self.subject)
        
        # Create student
        self.student = Student.objects.create(
            first_name="John",
            last_name="Doe",
            student_id="S001",
            date_of_birth="2005-01-01",
            current_class=self.class_obj,
            school=self.school1
        )
        
        # Create library book
        self.book = Book.objects.create(
            title="Test Book",
            isbn="1234567890123",
            barcode="TEST001",
            call_number="TEST.001",
            publication_date="2023-01-01",
            school=self.school1
        )
        
        self.book_copy = BookCopy.objects.create(
            book=self.book,
            copy_number=1,
            status='available',
            school=self.school1
        )
    
    def test_complete_school_selection_workflow(self):
        """Test complete school selection workflow"""
        # Step 1: Login
        login_success = self.client.login(username='testuser', password='testpass123')
        self.assertTrue(login_success, "User should be able to login")
        
        # Step 2: Access school selection page
        response = self.client.get('/core/school/select/')
        self.assertIn(response.status_code, [200, 302], "School selection page should be accessible")
        
        # Step 3: Select school
        response = self.client.post('/core/school/select/', {
            'school_id': str(self.school1.id)
        })
        self.assertIn(response.status_code, [200, 302], "School selection should work")
        
        # Step 4: Test AJAX school switching
        response = self.client.post('/core/school/switch/', {
            'school_id': str(self.school2.id)
        }, HTTP_X_REQUESTED_WITH='XMLHttpRequest')
        
        if response.status_code == 200:
            try:
                data = response.json()
                self.assertTrue(data.get('success', False), "AJAX school switch should succeed")
            except:
                pass  # JSON parsing might fail in test environment
        
        # Step 5: Test school context in subsequent requests
        response = self.client.get('/core/school/current/')
        self.assertIn(response.status_code, [200, 404], "Current school API should be accessible")
    
    def test_academic_operations_with_school_context(self):
        """Test academic operations maintain proper school context"""
        # Login and select school
        self.client.login(username='testuser', password='testpass123')
        self.client.post('/core/school/select/', {'school_id': str(self.school1.id)})
        
        # Test subject creation
        response = self.client.post('/academics/subjects/add/', {
            'name': 'Test Subject',
            'code': 'TEST001',
            'description': 'Test subject description',
            'credit_hours': 3
        })
        self.assertIn(response.status_code, [200, 302], "Subject creation should work")
        
        # Verify subject was created with correct school
        if Subject.objects.filter(code='TEST001').exists():
            subject = Subject.objects.get(code='TEST001')
            self.assertEqual(subject.school, self.school1, "Subject should belong to selected school")
        
        # Test schedule creation
        class_subject, created = ClassSubject.objects.get_or_create(
            class_obj=self.class_obj,
            subject=self.subject,
            teacher=self.teacher,
            academic_year=self.academic_year,
            semester=self.semester,
            school=self.school1,
            defaults={'weekly_hours': 3}
        )
        
        response = self.client.post('/academics/schedules/create/', {
            'class_id': str(self.class_obj.id),
            'subject_id': str(self.subject.id),
            'teacher_id': str(self.teacher.id),
            'day_of_week': 'monday',
            'start_time': '09:00',
            'end_time': '10:00',
            'room_number': '101'
        })
        self.assertIn(response.status_code, [200, 302], "Schedule creation should work")
    
    def test_library_operations_with_school_context(self):
        """Test library operations maintain proper school context"""
        # Login and select school
        self.client.login(username='testuser', password='testpass123')
        self.client.post('/core/school/select/', {'school_id': str(self.school1.id)})
        
        # Test book borrowing API
        response = self.client.post('/library/api/borrow/', {
            'book_copy_id': str(self.book_copy.id),
            'student_id': str(self.student.id)
        }, HTTP_X_REQUESTED_WITH='XMLHttpRequest')
        
        # Response might be 200 (success) or 400 (validation error) or 404 (not found)
        self.assertIn(response.status_code, [200, 400, 404], "Borrow API should respond")
        
        # Test book return API
        response = self.client.post('/library/api/return/', {
            'book_copy_id': str(self.book_copy.id),
            'student_id': str(self.student.id)
        }, HTTP_X_REQUESTED_WITH='XMLHttpRequest')
        
        self.assertIn(response.status_code, [200, 400, 404], "Return API should respond")
    
    def test_multi_school_data_isolation(self):
        """Test that data is properly isolated between schools"""
        # Login and select school1
        self.client.login(username='testuser', password='testpass123')
        self.client.post('/core/school/select/', {'school_id': str(self.school1.id)})
        
        # Create data for school1
        subject1 = Subject.objects.create(
            name="School1 Subject",
            code="SCH1_001",
            school=self.school1
        )
        
        # Switch to school2
        self.client.post('/core/school/select/', {'school_id': str(self.school2.id)})
        
        # Create data for school2
        subject2 = Subject.objects.create(
            name="School2 Subject",
            code="SCH2_001",
            school=self.school2
        )
        
        # Verify data isolation
        school1_subjects = Subject.objects.filter(school=self.school1)
        school2_subjects = Subject.objects.filter(school=self.school2)
        
        self.assertIn(subject1, school1_subjects, "School1 should have its own subjects")
        self.assertIn(subject2, school2_subjects, "School2 should have its own subjects")
        self.assertNotIn(subject2, school1_subjects, "School1 should not see school2 subjects")
        self.assertNotIn(subject1, school2_subjects, "School2 should not see school1 subjects")
    
    def test_error_handling_and_recovery(self):
        """Test error handling and recovery mechanisms"""
        # Login
        self.client.login(username='testuser', password='testpass123')
        
        # Test invalid school selection
        response = self.client.post('/core/school/select/', {
            'school_id': 'invalid-id'
        })
        self.assertIn(response.status_code, [200, 302, 400], "Invalid school ID should be handled")
        
        # Test AJAX with invalid school ID
        response = self.client.post('/core/school/switch/', {
            'school_id': 'invalid-id'
        }, HTTP_X_REQUESTED_WITH='XMLHttpRequest')
        
        if response.status_code == 200:
            try:
                data = response.json()
                self.assertFalse(data.get('success', True), "Invalid school switch should fail")
            except:
                pass
        
        # Test operations without school selection
        response = self.client.post('/academics/subjects/add/', {
            'name': 'Test Subject',
            'code': 'TEST002',
            'description': 'Test subject description',
            'credit_hours': 3
        })
        # Should either work (if middleware handles it) or redirect to school selection
        self.assertIn(response.status_code, [200, 302, 400], "Operation without school should be handled")


def run_integration_tests():
    """Run all integration tests"""
    import unittest
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(SchoolSelectionWorkflowTestCase)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_integration_tests()
    sys.exit(0 if success else 1)