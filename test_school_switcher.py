#!/usr/bin/env python
"""
Test script for navbar school switcher component
"""

import os
import sys
import django
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

from core.models import School

User = get_user_model()

def test_navbar_school_switcher():
    """Test the navbar school switcher component"""
    
    print("Testing Navbar School Switcher Component...")
    
    # Create test client
    client = Client()
    
    # Create test user
    try:
        user = User.objects.get(username='switchertestuser')
        user.set_password('testpass123')
        user.is_superuser = True
        user.save()
    except User.DoesNotExist:
        user = User.objects.create_user(
            username='switchertestuser',
            email='<EMAIL>',
            password='testpass123',
            is_superuser=True
        )
    
    # Create multiple test schools for switcher testing
    schools_data = [
        {
            'code': 'SW001',
            'name': 'Switcher Test School 1',
            'address': '123 Switcher Street',
            'phone': '+1234567890',
            'email': '<EMAIL>',
            'principal_name': 'Principal Switcher One',
            'established_date': '2020-01-01'
        },
        {
            'code': 'SW002',
            'name': 'Switcher Test School 2',
            'address': '456 Switcher Avenue',
            'phone': '+1234567891',
            'email': '<EMAIL>',
            'principal_name': 'Principal Switcher Two',
            'established_date': '2021-01-01'
        },
        {
            'code': 'SW003',
            'name': 'Switcher Test School 3',
            'address': '789 Switcher Boulevard',
            'phone': '+1234567892',
            'email': '<EMAIL>',
            'principal_name': 'Principal Switcher Three',
            'established_date': '2022-01-01'
        }
    ]
    
    created_schools = []
    for school_data in schools_data:
        school, created = School.objects.get_or_create(
            code=school_data['code'],
            defaults=school_data
        )
        created_schools.append(school)
    
    # Login user
    login_success = client.login(username='switchertestuser', password='testpass123')
    print(f"Login successful: {login_success}")
    
    # First select a school to have a current school
    first_school = created_schools[0]
    response = client.post(reverse('core:school_select'), {
        'school_id': str(first_school.id),
        'next': '/dashboard/'
    }, HTTP_HOST='localhost')
    
    print(f"School selection response: {response.status_code}")
    
    # Test 1: Check if school switcher appears in navbar
    print("1. Testing school switcher in navbar...")
    try:
        # Access a page that uses the base template (like dashboard)
        response = client.get('/', HTTP_HOST='localhost')  # Root URL should redirect to dashboard
        
        if response.status_code in [200, 302]:
            if response.status_code == 302:
                # Follow redirect
                response = client.get(response.url, HTTP_HOST='localhost')
            
            content = response.content.decode()
            
            # Check for school switcher elements
            switcher_checks = [
                ('School switcher dropdown', 'id="schoolSwitcher"'),
                ('School switcher button', 'btn btn-outline-light dropdown-toggle'),
                ('Current school display', 'currentSchoolName'),
                ('Available schools dropdown', 'Available Schools'),
                ('School switching JavaScript', 'school-option'),
                ('AJAX switching functionality', 'core:switch_school'),
                ('Toast notifications', 'showToast'),
                ('School switcher CSS', '.school-option'),
                ('Dropdown menu', 'dropdown-menu dropdown-menu-end'),
                ('School icons', 'fas fa-school'),
                ('University icons', 'fas fa-university'),
                ('Current school indicator', 'fas fa-check-circle text-success')
            ]
            
            for check_name, check_pattern in switcher_checks:
                if check_pattern in content:
                    print(f"   ✓ {check_name} found")
                else:
                    print(f"   ❌ {check_name} missing")
            
            print("   ✓ School switcher component integrated in navbar")
        else:
            print(f"   ❌ Could not access page to test navbar: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Navbar integration test failed: {e}")
    
    # Test 2: Test school switcher visibility conditions
    print("2. Testing school switcher visibility conditions...")
    try:
        # Test with multiple schools (should show switcher)
        response = client.get('/', HTTP_HOST='localhost')
        if response.status_code == 302:
            response = client.get(response.url, HTTP_HOST='localhost')
        
        content = response.content.decode()
        
        # Should show switcher when multiple schools available
        if 'schoolSwitcher' in content:
            print("   ✓ School switcher visible with multiple schools")
        else:
            print("   ❌ School switcher not visible with multiple schools")
            
        # Check if current school name is displayed
        if first_school.name in content or first_school.code in content:
            print("   ✓ Current school name/code displayed")
        else:
            print("   ❌ Current school name/code not displayed")
            
    except Exception as e:
        print(f"   ❌ Visibility conditions test failed: {e}")
    
    # Test 3: Test AJAX school switching endpoint
    print("3. Testing AJAX school switching...")
    try:
        # Switch to second school
        second_school = created_schools[1]
        response = client.post(reverse('core:switch_school'), {
            'school_id': str(second_school.id)
        }, HTTP_HOST='localhost')
        
        if response.status_code == 200:
            import json
            data = json.loads(response.content)
            
            if data.get('success'):
                print("   ✓ AJAX school switching works")
                print(f"   ✓ Switched to: {data.get('school_name')}")
                
                # Verify session was updated
                session = client.session
                if session.get('selected_school_id') == str(second_school.id):
                    print("   ✓ Session updated correctly")
                else:
                    print("   ❌ Session not updated correctly")
            else:
                print(f"   ❌ AJAX switching failed: {data.get('message')}")
        else:
            print(f"   ❌ AJAX endpoint returned: {response.status_code}")
    except Exception as e:
        print(f"   ❌ AJAX switching test failed: {e}")
    
    # Test 4: Test responsive design elements
    print("4. Testing responsive design...")
    try:
        response = client.get('/', HTTP_HOST='localhost')
        if response.status_code == 302:
            response = client.get(response.url, HTTP_HOST='localhost')
        
        content = response.content.decode()
        
        # Check for responsive classes
        responsive_checks = [
            ('Mobile display class', 'd-md-none'),
            ('Desktop display class', 'd-none d-md-inline'),
            ('Responsive dropdown width', 'min-width: 320px'),
            ('Mobile media query', '@media (max-width: 768px)'),
            ('Responsive grid classes', 'col-'),
        ]
        
        for check_name, check_pattern in responsive_checks:
            if check_pattern in content:
                print(f"   ✓ {check_name} found")
            else:
                print(f"   ❌ {check_name} missing")
                
    except Exception as e:
        print(f"   ❌ Responsive design test failed: {e}")
    
    # Test 5: Test error handling
    print("5. Testing error handling...")
    try:
        # Test with invalid school ID
        response = client.post(reverse('core:switch_school'), {
            'school_id': 'invalid-id'
        }, HTTP_HOST='localhost')
        
        if response.status_code == 200:
            import json
            data = json.loads(response.content)
            
            if not data.get('success'):
                print("   ✓ Invalid school ID handled correctly")
            else:
                print("   ❌ Invalid school ID not handled correctly")
        else:
            print(f"   ❌ Error handling test failed: {response.status_code}")
            
        # Test with missing school ID
        response = client.post(reverse('core:switch_school'), {}, HTTP_HOST='localhost')
        
        if response.status_code == 200:
            import json
            data = json.loads(response.content)
            
            if not data.get('success'):
                print("   ✓ Missing school ID handled correctly")
            else:
                print("   ❌ Missing school ID not handled correctly")
        else:
            print(f"   ❌ Missing ID test failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error handling test failed: {e}")
    
    print("\n✅ Navbar school switcher component tests completed!")
    
    # Cleanup
    User.objects.filter(username='switchertestuser').delete()
    School.objects.filter(code__in=['SW001', 'SW002', 'SW003']).delete()
    
    return True

if __name__ == '__main__':
    try:
        test_navbar_school_switcher()
        print("\n🎉 Navbar school switcher component is working correctly!")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)