#!/usr/bin/env python
"""
Test script for school selection template interface
"""

import os
import sys
import django
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

from core.models import School

User = get_user_model()

def test_school_template_interface():
    """Test the school selection template interface"""
    
    print("Testing School Selection Template Interface...")
    
    # Create test client
    client = Client()
    
    # Create test user
    try:
        user = User.objects.get(username='templatetestuser')
        user.set_password('testpass123')
        user.is_superuser = True
        user.save()
    except User.DoesNotExist:
        user = User.objects.create_user(
            username='templatetestuser',
            email='<EMAIL>',
            password='testpass123',
            is_superuser=True
        )
    
    # Create multiple test schools for better testing
    schools_data = [
        {
            'code': 'TMPL001',
            'name': 'Template Test School 1',
            'address': '123 Template Street, Test City',
            'phone': '+1234567890',
            'email': '<EMAIL>',
            'principal_name': 'Principal Template One',
            'established_date': '2020-01-01'
        },
        {
            'code': 'TMPL002',
            'name': 'Template Test School 2',
            'address': '456 Template Avenue, Test Town',
            'phone': '+1234567891',
            'email': '<EMAIL>',
            'principal_name': 'Principal Template Two',
            'established_date': '2021-01-01'
        },
        {
            'code': 'TMPL003',
            'name': 'Template Test School 3 with Very Long Name',
            'address': '789 Very Long Template Boulevard, Extended Test Municipality',
            'phone': '+1234567892',
            'email': '<EMAIL>',
            'principal_name': 'Principal Template Three',
            'established_date': '2022-01-01'
        }
    ]
    
    created_schools = []
    for school_data in schools_data:
        school, created = School.objects.get_or_create(
            code=school_data['code'],
            defaults=school_data
        )
        created_schools.append(school)
    
    # Login user
    login_success = client.login(username='templatetestuser', password='testpass123')
    print(f"Login successful: {login_success}")
    
    # Test 1: Template renders correctly
    print("1. Testing template rendering...")
    try:
        response = client.get(reverse('core:school_select'), HTTP_HOST='localhost')
        print(f"   Response status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.content.decode()
            
            # Check for key template elements
            template_checks = [
                ('Bootstrap card layout', 'class="card school-card'),
                ('School icons', 'fas fa-university'),
                ('Responsive grid', 'col-lg-4 col-md-6'),
                ('Hover effects CSS', 'school-card:hover'),
                ('Form with CSRF', 'csrfmiddlewaretoken'),
                ('Loading states JS', 'fa-spinner fa-spin'),
                ('Professional header', 'page-header'),
                ('Continue section', 'continue-section'),
                ('Internationalization', '{% trans'),
                ('School details display', 'fas fa-code'),
                ('Phone display', 'fas fa-phone'),
                ('Email display', 'fas fa-envelope'),
                ('Address display', 'fas fa-map-marker-alt')
            ]
            
            for check_name, check_pattern in template_checks:
                if check_pattern in content:
                    print(f"   ✓ {check_name} found")
                else:
                    print(f"   ❌ {check_name} missing")
            
            print("   ✓ Template renders correctly")
        else:
            print(f"   ❌ Template failed to render: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Template rendering test failed: {e}")
    
    # Test 2: School data display
    print("2. Testing school data display...")
    try:
        response = client.get(reverse('core:school_select'), HTTP_HOST='localhost')
        if response.status_code == 200:
            content = response.content.decode()
            
            # Check if our test schools appear in the template
            for school in created_schools:
                if school.name in content and school.code in content:
                    print(f"   ✓ School '{school.name}' displayed correctly")
                else:
                    print(f"   ❌ School '{school.name}' not found in template")
            
            # Check for truncation of long addresses (should be truncated to 50 chars)
            if 'Extended Test Municipality' not in content:
                print("   ✓ Long addresses are properly truncated")
            else:
                print("   ❌ Long addresses not truncated")
                
        else:
            print(f"   ❌ Could not test school data display: {response.status_code}")
    except Exception as e:
        print(f"   ❌ School data display test failed: {e}")
    
    # Test 3: Form functionality
    print("3. Testing form functionality...")
    try:
        # Test POST with school selection
        test_school = created_schools[0]
        response = client.post(reverse('core:school_select'), {
            'school_id': str(test_school.id),
            'next': '/dashboard/'
        }, HTTP_HOST='localhost')
        
        if response.status_code == 302:  # Redirect after successful selection
            print("   ✓ Form submission works correctly")
            
            # Check if school is stored in session
            session = client.session
            if 'selected_school_id' in session:
                print("   ✓ School selection stored in session")
            else:
                print("   ❌ School selection not stored in session")
        else:
            print(f"   ❌ Form submission failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Form functionality test failed: {e}")
    
    # Test 4: Selected school indication
    print("4. Testing selected school indication...")
    try:
        # After selecting a school, check if it's highlighted
        response = client.get(reverse('core:school_select'), HTTP_HOST='localhost')
        if response.status_code == 200:
            content = response.content.decode()
            
            # Check for selected school styling
            if 'selected' in content and 'Currently Selected' in content:
                print("   ✓ Selected school is properly highlighted")
            else:
                print("   ❌ Selected school highlighting not working")
                
            # Check for continue section
            if 'continue-section' in content and 'Continue to Dashboard' in content:
                print("   ✓ Continue section displayed for selected school")
            else:
                print("   ❌ Continue section not displayed")
        else:
            print(f"   ❌ Could not test selected school indication: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Selected school indication test failed: {e}")
    
    print("\n✅ School selection template interface tests completed!")
    
    # Cleanup
    User.objects.filter(username='templatetestuser').delete()
    School.objects.filter(code__in=['TMPL001', 'TMPL002', 'TMPL003']).delete()
    
    return True

if __name__ == '__main__':
    try:
        test_school_template_interface()
        print("\n🎉 School selection template interface is working correctly!")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)