#!/usr/bin/env python
"""
Simple test script for school selection views functionality
"""

import os
import sys
import django
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

from core.models import School

User = get_user_model()

def test_school_views_basic():
    """Basic test for school selection views"""
    
    print("Testing School Selection Views (Basic)...")
    
    # Create test client
    client = Client()
    
    # Create test user
    try:
        user = User.objects.get(username='testuser')
        user.set_password('testpass123')
        user.is_superuser = True
        user.save()
    except User.DoesNotExist:
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            is_superuser=True
        )
    
    # Create test school
    school, created = School.objects.get_or_create(
        code='TS001',
        defaults={
            'name': 'Test School 1',
            'address': '123 Test Street',
            'phone': '+1234567890',
            'email': '<EMAIL>',
            'principal_name': 'Principal One',
            'established_date': '2020-01-01'
        }
    )
    
    # Login user
    login_success = client.login(username='testuser', password='testpass123')
    print(f"Login successful: {login_success}")
    
    # Test 1: Check if URLs resolve
    print("1. Testing URL resolution...")
    try:
        school_select_url = reverse('core:school_select')
        switch_school_url = reverse('core:switch_school')
        get_current_school_url = reverse('core:get_current_school')
        print(f"   ✓ URLs resolved: {school_select_url}, {switch_school_url}, {get_current_school_url}")
    except Exception as e:
        print(f"   ❌ URL resolution failed: {e}")
        return False
    
    # Test 2: Test AJAX endpoints first (simpler)
    print("2. Testing AJAX school switching...")
    try:
        response = client.post(switch_school_url, {
            'school_id': str(school.id)
        }, HTTP_HOST='localhost')
        print(f"   Switch school response status: {response.status_code}")
        if response.status_code == 200:
            data = json.loads(response.content)
            print(f"   Response data: {data}")
            if data.get('success'):
                print("   ✓ AJAX school switching works")
            else:
                print(f"   ❌ AJAX failed: {data.get('message')}")
        else:
            print(f"   ❌ Unexpected status code: {response.status_code}")
    except Exception as e:
        print(f"   ❌ AJAX test failed: {e}")
    
    # Test 3: Test get current school API
    print("3. Testing get current school API...")
    try:
        response = client.get(get_current_school_url, HTTP_HOST='localhost')
        print(f"   Get current school response status: {response.status_code}")
        if response.status_code == 200:
            data = json.loads(response.content)
            print(f"   Response data: {data}")
            print("   ✓ Get current school API works")
        else:
            print(f"   ❌ Unexpected status code: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Get current school test failed: {e}")
    
    # Test 4: Test school selection page (more complex)
    print("4. Testing school selection page...")
    try:
        response = client.get(school_select_url, HTTP_HOST='localhost')
        print(f"   School select page response status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✓ School selection page loads correctly")
        else:
            print(f"   ❌ School selection page failed with status: {response.status_code}")
            # Print response content for debugging
            content = response.content.decode()[:500]  # First 500 chars
            print(f"   Response content preview: {content}")
    except Exception as e:
        print(f"   ❌ School selection page test failed: {e}")
    
    print("\n✅ Basic school selection view tests completed!")
    
    # Cleanup
    User.objects.filter(username='testuser').delete()
    School.objects.filter(code='TS001').delete()
    
    return True

if __name__ == '__main__':
    try:
        test_school_views_basic()
        print("\n🎉 Basic school selection views test completed!")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)