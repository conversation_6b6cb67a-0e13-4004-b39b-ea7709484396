#!/usr/bin/env python
"""
Test if the Django server is running and accessible
"""
import requests
import time
import subprocess
import sys
from threading import Thread

def test_server_accessibility():
    """Test if server is accessible"""
    print("🌐 Testing server accessibility...")
    
    base_url = "http://127.0.0.1:8000"
    
    # Test basic endpoints
    endpoints = [
        "/admin/",
        "/accounts/login/",
        "/accounts/dashboard/",
    ]
    
    for endpoint in endpoints:
        try:
            url = base_url + endpoint
            response = requests.get(url, timeout=10, allow_redirects=True)
            
            if response.status_code in [200, 302, 403]:
                print(f"✅ {endpoint} - Status: {response.status_code}")
            else:
                print(f"❌ {endpoint} - Status: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"❌ {endpoint} - Connection refused (server not running)")
        except requests.exceptions.Timeout:
            print(f"❌ {endpoint} - Request timeout")
        except Exception as e:
            print(f"❌ {endpoint} - Error: {e}")
    
    # Test admin login
    try:
        session = requests.Session()
        
        # Get CSRF token
        login_page = session.get(base_url + "/admin/login/")
        if login_page.status_code == 200:
            print("✅ Admin login page accessible")
            
            # Try to extract CSRF token (simplified)
            if 'csrfmiddlewaretoken' in login_page.text:
                print("✅ CSRF token found in login page")
            else:
                print("⚠️ CSRF token not found (might be normal)")
        else:
            print(f"❌ Admin login page - Status: {login_page.status_code}")
            
    except Exception as e:
        print(f"❌ Admin login test failed: {e}")

def main():
    """Main test function"""
    print("🚀 Testing Django server accessibility...")
    print("=" * 50)
    
    print("💡 Make sure the Django server is running:")
    print("   python manage.py runserver")
    print()
    
    # Wait a moment for server to be ready
    print("⏳ Waiting 2 seconds for server to be ready...")
    time.sleep(2)
    
    # Test server accessibility
    test_server_accessibility()
    
    print("\n" + "=" * 50)
    print("🎉 Server accessibility test completed!")
    print("\n💡 If the server is running properly:")
    print("1. Open browser to: http://127.0.0.1:8000/admin/")
    print("2. Login with: admin / admin123")
    print("3. Explore the populated data")
    print("\n📊 System should be fully functional!")

if __name__ == '__main__':
    main()