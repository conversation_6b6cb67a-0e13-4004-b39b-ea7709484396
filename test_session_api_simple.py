#!/usr/bin/env python
"""
Simple test for session management API endpoints.
"""

import os
import sys
import django
from datetime import date

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from core.models import School
import json

User = get_user_model()

def test_session_api():
    """Test session management API endpoints"""
    print("Testing Session Management API...")
    
    client = Client()
    
    # Create or get test user and school
    user, created = User.objects.get_or_create(
        username='testuser_api',
        defaults={
            'email': '<EMAIL>',
            'user_type': 'admin'
        }
    )
    if created:
        user.set_password('testpass123')
        user.save()
    
    school, created = School.objects.get_or_create(
        code='TSAPI',
        defaults={
            'name': 'Test School API',
            'address': '123 API St',
            'phone': '************',
            'email': '<EMAIL>',
            'principal_name': 'Principal API',
            'established_date': date(2020, 1, 1),
            'is_active': True
        }
    )
    
    # Login user
    login_success = client.login(username='testuser_api', password='testpass123')
    print(f"Login successful: {login_success}")
    
    if not login_success:
        print("❌ Login failed, cannot test API endpoints")
        return False
    
    # Test school selection first (this should work)
    response = client.post('/core/school/select/', {
        'school_id': str(school.id)
    })
    print(f"School selection response: {response.status_code}")
    
    if response.status_code == 302:  # Redirect after successful selection
        print("✓ School selection successful")
        
        # Now test the API endpoints
        response = client.get('/core/school/current/')
        print(f"Get current school status: {response.status_code}")
        
        if response.status_code == 200:
            data = json.loads(response.content)
            print(f"✓ Current school API working: {data.get('school', {}).get('name', 'Unknown')}")
        
        # Test session status
        response = client.get('/core/school/session/status/')
        print(f"Session status response: {response.status_code}")
        
        if response.status_code == 200:
            data = json.loads(response.content)
            print(f"✓ Session status API working: Valid={data.get('session', {}).get('is_valid', False)}")
        
        return True
    else:
        print(f"❌ School selection failed with status {response.status_code}")
        return False

if __name__ == '__main__':
    success = test_session_api()
    if success:
        print("\n✅ Session API tests completed successfully!")
    else:
        print("\n❌ Session API tests failed!")
    sys.exit(0 if success else 1)