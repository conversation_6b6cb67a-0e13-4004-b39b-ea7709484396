#!/usr/bin/env python
"""
Comprehensive test for session management implementation.
Tests all requirements for task 10.
"""

import os
import sys
import django
from datetime import datetime, timedelta, date

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from core.models import School
from core.school_utils import (
    set_current_school, get_current_school, clear_current_school,
    validate_session_school_data, cleanup_invalid_school_session,
    get_school_session_info, refresh_school_session,
    SESSION_SCHOOL_ID_KEY, SESSION_SCHOOL_TIMESTAMP_KEY, SESSION_SCHOOL_VALIDATION_KEY,
    SCHOOL_SESSION_TIMEOUT_HOURS
)
from django.utils import timezone

User = get_user_model()

def test_session_management_requirements():
    """Test all session management requirements"""
    
    print("=" * 60)
    print("COMPREHENSIVE SESSION MANAGEMENT TEST")
    print("=" * 60)
    
    # Setup test data
    user, created = User.objects.get_or_create(
        username='session_test_user',
        defaults={
            'email': '<EMAIL>',
            'user_type': 'admin'
        }
    )
    if created:
        user.set_password('testpass123')
        user.save()
    
    school, created = School.objects.get_or_create(
        code='SESS_TEST',
        defaults={
            'name': 'Session Test School',
            'address': '123 Session St',
            'phone': '************',
            'email': '<EMAIL>',
            'principal_name': 'Principal Session',
            'established_date': date(2020, 1, 1),
            'is_active': True
        }
    )
    
    # Create mock request
    from django.test import RequestFactory
    factory = RequestFactory()
    request = factory.get('/')
    request.user = user
    
    # Use a real session
    from django.contrib.sessions.backends.db import SessionStore
    session = SessionStore()
    session.create()
    request.session = session
    
    print(f"Test user: {user.username}")
    print(f"Test school: {school.name}")
    print()
    
    # Test 1: Session-based school selection storage with proper data validation
    print("1. Testing session-based school selection storage...")
    
    # Test setting school
    success = set_current_school(request, school)
    print(f"   ✓ Set school success: {success}")
    assert success, "Should successfully set school"
    
    # Verify session data is stored
    assert SESSION_SCHOOL_ID_KEY in request.session, "School ID should be in session"
    assert SESSION_SCHOOL_TIMESTAMP_KEY in request.session, "Timestamp should be in session"
    assert SESSION_SCHOOL_VALIDATION_KEY in request.session, "Validation hash should be in session"
    print("   ✓ Session data properly stored with validation metadata")
    
    # Test data validation
    is_valid = validate_session_school_data(request)
    print(f"   ✓ Session data validation: {is_valid}")
    assert is_valid, "Session data should be valid"
    
    # Test 2: Session cleanup for invalid or expired school selections
    print("\n2. Testing session cleanup for invalid/expired selections...")
    
    # Test with expired timestamp
    old_timestamp = (timezone.now() - timedelta(hours=SCHOOL_SESSION_TIMEOUT_HOURS + 1)).isoformat()
    request.session[SESSION_SCHOOL_TIMESTAMP_KEY] = old_timestamp
    
    is_valid = validate_session_school_data(request)
    print(f"   ✓ Expired session validation: {is_valid}")
    assert not is_valid, "Expired session should be invalid"
    
    # Test cleanup
    cleanup_invalid_school_session(request)
    assert SESSION_SCHOOL_ID_KEY not in request.session, "Session should be cleaned up"
    print("   ✓ Invalid session data properly cleaned up")
    
    # Test 3: Helper functions for getting and setting school context
    print("\n3. Testing helper functions for school context...")
    
    # Test setting school again
    success = set_current_school(request, school)
    assert success, "Should successfully set school again"
    print("   ✓ set_current_school() function working")
    
    # Test getting school
    current_school = get_current_school(request)
    assert current_school is not None, "Should get current school"
    assert current_school.id == school.id, "Should get correct school"
    print("   ✓ get_current_school() function working")
    
    # Test clearing school
    clear_current_school(request)
    current_school = get_current_school(request)
    assert current_school is None, "School should be cleared"
    print("   ✓ clear_current_school() function working")
    
    # Test 4: Session persistence across browser refreshes and different devices
    print("\n4. Testing session persistence...")
    
    # Set school again
    set_current_school(request, school)
    
    # Simulate new request with same session
    new_request = factory.get('/')
    new_request.user = user
    new_request.session = request.session  # Same session
    
    # Should get same school
    persistent_school = get_current_school(new_request)
    assert persistent_school is not None, "School should persist across requests"
    assert persistent_school.id == school.id, "Should get same school"
    print("   ✓ Session persists across requests")
    
    # Test session info retrieval
    session_info = get_school_session_info(new_request)
    assert session_info['has_session'], "Should have session"
    assert session_info['is_valid'], "Session should be valid"
    assert session_info['school_id'] == str(school.id), "Should have correct school ID"
    print("   ✓ Session info retrieval working")
    
    # Test session refresh
    original_timestamp = new_request.session[SESSION_SCHOOL_TIMESTAMP_KEY]
    import time
    time.sleep(1)  # Wait a moment
    
    refresh_success = refresh_school_session(new_request)
    new_timestamp = new_request.session[SESSION_SCHOOL_TIMESTAMP_KEY]
    
    assert refresh_success, "Should successfully refresh session"
    assert new_timestamp != original_timestamp, "Timestamp should be updated"
    print("   ✓ Session refresh working")
    
    # Test 5: Additional validation scenarios
    print("\n5. Testing additional validation scenarios...")
    
    # Test with invalid school ID format
    request.session[SESSION_SCHOOL_ID_KEY] = "invalid-id"
    is_valid = validate_session_school_data(request)
    assert not is_valid, "Invalid school ID should fail validation"
    print("   ✓ Invalid school ID format properly rejected")
    
    # Test with missing timestamp
    request.session[SESSION_SCHOOL_ID_KEY] = str(school.id)
    del request.session[SESSION_SCHOOL_TIMESTAMP_KEY]
    is_valid = validate_session_school_data(request)
    assert not is_valid, "Missing timestamp should fail validation"
    print("   ✓ Missing timestamp properly rejected")
    
    # Test with malformed timestamp
    request.session[SESSION_SCHOOL_TIMESTAMP_KEY] = "invalid-timestamp"
    is_valid = validate_session_school_data(request)
    assert not is_valid, "Invalid timestamp should fail validation"
    print("   ✓ Invalid timestamp format properly rejected")
    
    print("\n" + "=" * 60)
    print("✅ ALL SESSION MANAGEMENT REQUIREMENTS TESTED SUCCESSFULLY!")
    print("=" * 60)
    
    print("\nImplemented features:")
    print("✓ Session-based school selection storage with proper data validation")
    print("✓ Session cleanup for invalid or expired school selections")
    print("✓ Helper functions for getting and setting school context in sessions")
    print("✓ Session persistence across browser refreshes and different devices")
    print("✓ Comprehensive validation and error handling")
    print("✓ Session refresh functionality")
    print("✓ Session information retrieval")
    print("✓ Automatic cleanup of corrupted session data")
    
    print(f"\nSession timeout configured: {SCHOOL_SESSION_TIMEOUT_HOURS} hours")
    print("Session keys used:")
    print(f"  - {SESSION_SCHOOL_ID_KEY}")
    print(f"  - {SESSION_SCHOOL_TIMESTAMP_KEY}")
    print(f"  - {SESSION_SCHOOL_VALIDATION_KEY}")
    
    return True

if __name__ == '__main__':
    try:
        success = test_session_management_requirements()
        if success:
            print("\n🎉 Task 10 implementation is COMPLETE and VERIFIED!")
            sys.exit(0)
        else:
            print("\n❌ Task 10 implementation has issues!")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)