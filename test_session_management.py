#!/usr/bin/env python
"""
Test script for school session management functionality.
Tests session persistence, validation, cleanup, and API endpoints.
"""

import os
import sys
import django
from datetime import datetime, timedelta
from django.utils import timezone

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.contrib.sessions.models import Session
from core.models import School
from core.school_utils import (
    set_current_school, get_current_school, clear_current_school,
    validate_session_school_data, cleanup_invalid_school_session,
    get_school_session_info, refresh_school_session,
    SESSION_SCHOOL_ID_KEY, SESSION_SCHOOL_TIMESTAMP_KEY, SESSION_SCHOOL_VALIDATION_KEY
)
import json

User = get_user_model()

class SessionManagementTest:
    """Test class for session management functionality"""
    
    def __init__(self):
        self.client = Client()
        self.setup_test_data()
    
    def setup_test_data(self):
        """Create test data"""
        print("Setting up test data...")
        
        # Create or get test user
        self.user, created = User.objects.get_or_create(
            username='testuser',
            defaults={
                'email': '<EMAIL>',
                'user_type': 'admin'
            }
        )
        if created:
            self.user.set_password('testpass123')
            self.user.save()
        
        # Create or get test schools
        from datetime import date
        self.school1, created = School.objects.get_or_create(
            code='TS1',
            defaults={
                'name': 'Test School 1',
                'address': '123 Test St',
                'phone': '************',
                'email': '<EMAIL>',
                'principal_name': 'Principal One',
                'established_date': date(2020, 1, 1),
                'is_active': True
            }
        )
        
        self.school2, created = School.objects.get_or_create(
            code='TS2',
            defaults={
                'name': 'Test School 2',
                'address': '456 Test Ave',
                'phone': '************',
                'email': '<EMAIL>',
                'principal_name': 'Principal Two',
                'established_date': date(2021, 1, 1),
                'is_active': True
            }
        )
        
        print(f"Created user: {self.user.username}")
        print(f"Created schools: {self.school1.name}, {self.school2.name}")
    
    def test_session_data_validation(self):
        """Test session data validation functionality"""
        print("\n=== Testing Session Data Validation ===")
        
        # Login user
        self.client.login(username='testuser', password='testpass123')
        
        # Create a mock request object
        from django.test import RequestFactory
        factory = RequestFactory()
        request = factory.get('/')
        request.user = self.user
        request.session = self.client.session
        
        # Test with no session data
        print("Testing with no session data...")
        result = validate_session_school_data(request)
        print(f"Validation result (no data): {result}")
        assert not result, "Should return False for no session data"
        
        # Test with valid session data
        print("Testing with valid session data...")
        success = set_current_school(request, self.school1)
        print(f"Set school result: {success}")
        assert success, "Should successfully set school"
        
        result = validate_session_school_data(request)
        print(f"Validation result (valid data): {result}")
        assert result, "Should return True for valid session data"
        
        # Test with expired session data
        print("Testing with expired session data...")
        # Manually set an old timestamp
        old_timestamp = (timezone.now() - timedelta(hours=25)).isoformat()
        request.session[SESSION_SCHOOL_TIMESTAMP_KEY] = old_timestamp
        
        result = validate_session_school_data(request)
        print(f"Validation result (expired): {result}")
        assert not result, "Should return False for expired session"
        
        print("✓ Session data validation tests passed")
    
    def test_session_cleanup(self):
        """Test session cleanup functionality"""
        print("\n=== Testing Session Cleanup ===")
        
        # Login user
        self.client.login(username='testuser', password='testpass123')
        
        # Create a mock request object
        from django.test import RequestFactory
        factory = RequestFactory()
        request = factory.get('/')
        request.user = self.user
        request.session = self.client.session
        
        # Set school data
        set_current_school(request, self.school1)
        
        # Verify data exists
        assert SESSION_SCHOOL_ID_KEY in request.session
        assert SESSION_SCHOOL_TIMESTAMP_KEY in request.session
        print("Session data set successfully")
        
        # Test cleanup
        cleanup_invalid_school_session(request)
        
        # Verify data is cleaned up
        assert SESSION_SCHOOL_ID_KEY not in request.session
        assert SESSION_SCHOOL_TIMESTAMP_KEY not in request.session
        assert SESSION_SCHOOL_VALIDATION_KEY not in request.session
        print("✓ Session cleanup tests passed")
    
    def test_session_info_retrieval(self):
        """Test session information retrieval"""
        print("\n=== Testing Session Info Retrieval ===")
        
        # Login user
        self.client.login(username='testuser', password='testpass123')
        
        # Create a mock request object
        from django.test import RequestFactory
        factory = RequestFactory()
        request = factory.get('/')
        request.user = self.user
        request.session = self.client.session
        
        # Test with no session
        info = get_school_session_info(request)
        print(f"Session info (no session): {info}")
        assert not info['has_session']
        assert not info['is_valid']
        
        # Test with valid session
        set_current_school(request, self.school1)
        info = get_school_session_info(request)
        print(f"Session info (valid): {info}")
        assert info['has_session']
        assert info['is_valid']
        assert info['school_id'] == str(self.school1.id)
        assert info['time_remaining'] is not None
        
        print("✓ Session info retrieval tests passed")
    
    def test_session_refresh(self):
        """Test session refresh functionality"""
        print("\n=== Testing Session Refresh ===")
        
        # Login user
        self.client.login(username='testuser', password='testpass123')
        
        # Create a mock request object
        from django.test import RequestFactory
        factory = RequestFactory()
        request = factory.get('/')
        request.user = self.user
        request.session = self.client.session
        
        # Set school
        set_current_school(request, self.school1)
        original_timestamp = request.session[SESSION_SCHOOL_TIMESTAMP_KEY]
        
        # Wait a moment and refresh
        import time
        time.sleep(1)
        
        result = refresh_school_session(request)
        new_timestamp = request.session[SESSION_SCHOOL_TIMESTAMP_KEY]
        
        print(f"Refresh result: {result}")
        print(f"Original timestamp: {original_timestamp}")
        print(f"New timestamp: {new_timestamp}")
        
        assert result, "Should successfully refresh session"
        assert new_timestamp != original_timestamp, "Timestamp should be updated"
        
        print("✓ Session refresh tests passed")
    
    def test_api_endpoints(self):
        """Test session management API endpoints"""
        print("\n=== Testing API Endpoints ===")
        
        # Login user
        self.client.login(username='testuser', password='testpass123')
        
        # Test school selection first
        response = self.client.post('/core/school/select/', {
            'school_id': self.school1.id
        })
        print(f"School selection response: {response.status_code}")
        
        # Test get current school API
        response = self.client.get('/core/school/current/')
        print(f"Get current school status: {response.status_code}")
        if response.status_code == 200:
            data = json.loads(response.content)
            print(f"Current school data: {data}")
            assert data['success'], "Should successfully get current school"
            assert 'session' in data, "Should include session information"
        
        # Test session status API
        response = self.client.get('/core/school/session/status/')
        print(f"Session status response: {response.status_code}")
        if response.status_code == 200:
            data = json.loads(response.content)
            print(f"Session status data: {data}")
            assert data['success'], "Should successfully get session status"
            assert 'session' in data, "Should include session information"
        
        # Test session refresh API
        response = self.client.post('/core/school/session/refresh/')
        print(f"Session refresh response: {response.status_code}")
        if response.status_code == 200:
            data = json.loads(response.content)
            print(f"Session refresh data: {data}")
            assert data['success'], "Should successfully refresh session"
        
        # Test session clear API
        response = self.client.post('/core/school/session/clear/')
        print(f"Session clear response: {response.status_code}")
        if response.status_code == 200:
            data = json.loads(response.content)
            print(f"Session clear data: {data}")
            assert data['success'], "Should successfully clear session"
        
        print("✓ API endpoint tests passed")
    
    def test_session_persistence(self):
        """Test session persistence across requests"""
        print("\n=== Testing Session Persistence ===")
        
        # Login user
        self.client.login(username='testuser', password='testpass123')
        
        # Select a school
        response = self.client.post('/core/school/select/', {
            'school_id': self.school1.id
        })
        print(f"School selection status: {response.status_code}")
        
        # Make multiple requests to verify persistence
        for i in range(3):
            response = self.client.get('/core/school/current/')
            if response.status_code == 200:
                data = json.loads(response.content)
                print(f"Request {i+1} - Current school: {data.get('school', {}).get('name', 'None')}")
                assert data['success'], f"Request {i+1} should have valid school"
                assert data['school']['id'] == str(self.school1.id), f"Request {i+1} should maintain same school"
        
        print("✓ Session persistence tests passed")
    
    def run_all_tests(self):
        """Run all session management tests"""
        print("Starting Session Management Tests...")
        print("=" * 50)
        
        try:
            self.test_session_data_validation()
            self.test_session_cleanup()
            self.test_session_info_retrieval()
            self.test_session_refresh()
            self.test_api_endpoints()
            self.test_session_persistence()
            
            print("\n" + "=" * 50)
            print("✅ ALL SESSION MANAGEMENT TESTS PASSED!")
            print("Session management functionality is working correctly.")
            
        except Exception as e:
            print(f"\n❌ TEST FAILED: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        return True

def main():
    """Main test function"""
    print("School ERP - Session Management Test")
    print("=" * 50)
    
    # Run tests
    test_runner = SessionManagementTest()
    success = test_runner.run_all_tests()
    
    if success:
        print("\n🎉 Session management implementation is complete and working!")
        print("\nKey features implemented:")
        print("- Session data validation with expiration checking")
        print("- Automatic session cleanup for invalid/expired data")
        print("- Session refresh functionality")
        print("- Comprehensive session information retrieval")
        print("- API endpoints for session management")
        print("- Session persistence across browser requests")
        
        return 0
    else:
        print("\n💥 Some tests failed. Please check the implementation.")
        return 1

if __name__ == '__main__':
    sys.exit(main())