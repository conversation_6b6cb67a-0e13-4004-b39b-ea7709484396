#!/usr/bin/env python
"""
Simple test script for school selection views functionality
"""

import os
import sys
import json

# Setup Django first
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
import django
django.setup()

from django.test import TestCase, Client, RequestFactory
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.contrib.messages import get_messages
from django.contrib.sessions.middleware import SessionMiddleware
from django.contrib.auth.middleware import AuthenticationMiddleware
from django.contrib.messages.middleware import MessageMiddleware
from django.middleware.csrf import CsrfViewMiddleware
from django.views.decorators.csrf import csrf_exempt

from core.models import School
from core.views import school_select, switch_school, get_current_school_api

User = get_user_model()

def test_school_views_directly():
    """Test the school selection views directly without middleware"""
    
    print("Testing School Selection Views Directly...")
    
    # Create test user
    try:
        user = User.objects.get(username='testuser')
        user.set_password('testpass123')
        user.is_superuser = True
        user.save()
    except User.DoesNotExist:
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            is_superuser=True
        )
    
    # Create test schools
    school1, created = School.objects.get_or_create(
        code='TS001',
        defaults={
            'name': 'Test School 1',
            'address': '123 Test Street',
            'phone': '+1234567890',
            'email': '<EMAIL>',
            'principal_name': 'Principal One',
            'established_date': '2020-01-01'
        }
    )
    
    school2, created = School.objects.get_or_create(
        code='TS002',
        defaults={
            'name': 'Test School 2',
            'address': '456 Test Avenue',
            'phone': '+1234567891',
            'email': '<EMAIL>',
            'principal_name': 'Principal Two',
            'established_date': '2021-01-01'
        }
    )
    
    # Create request factory
    factory = RequestFactory()
    
    # Test 1: School selection GET request
    print("1. Testing school selection GET request...")
    request = factory.get('/core/school/select/')
    request.user = user
    
    # Add session
    middleware = SessionMiddleware(lambda x: None)
    middleware.process_request(request)
    request.session.save()
    
    # Add messages
    msg_middleware = MessageMiddleware(lambda x: None)
    msg_middleware.process_request(request)
    
    try:
        response = school_select(request)
        print(f"   Response status: {response.status_code}")
        if response.status_code == 200:
            print("   ✓ School selection GET works correctly")
        else:
            print(f"   ✗ Expected 200, got {response.status_code}")
            return False
    except Exception as e:
        print(f"   ✗ Error in school selection GET: {e}")
        return False
    
    # Test 2: School selection POST request
    print("2. Testing school selection POST request...")
    request = factory.post('/core/school/select/', {
        'school_id': str(school1.id),
        'next': '/dashboard/'
    })
    request.user = user
    
    # Add session
    middleware = SessionMiddleware(lambda x: None)
    middleware.process_request(request)
    request.session.save()
    
    # Add messages
    msg_middleware = MessageMiddleware(lambda x: None)
    msg_middleware.process_request(request)
    
    # Add CSRF token
    csrf_middleware = CsrfViewMiddleware(lambda x: None)
    csrf_middleware.process_request(request)
    
    try:
        # Use csrf_exempt decorator to bypass CSRF for testing
        exempt_view = csrf_exempt(school_select)
        response = exempt_view(request)
        print(f"   Response status: {response.status_code}")
        if response.status_code == 302:  # Redirect
            print("   ✓ School selection POST works correctly")
        else:
            print(f"   ✗ Expected 302 (redirect), got {response.status_code}")
            return False
    except Exception as e:
        print(f"   ✗ Error in school selection POST: {e}")
        return False
    
    # Test 3: AJAX school switching
    print("3. Testing AJAX school switching...")
    request = factory.post('/core/school/switch/', {
        'school_id': str(school2.id)
    })
    request.user = user
    
    # Add session
    middleware = SessionMiddleware(lambda x: None)
    middleware.process_request(request)
    request.session.save()
    
    try:
        # Use csrf_exempt decorator to bypass CSRF for testing
        exempt_view = csrf_exempt(switch_school)
        response = exempt_view(request)
        print(f"   Response status: {response.status_code}")
        if response.status_code == 200:
            data = json.loads(response.content)
            if data.get('success'):
                print("   ✓ AJAX school switching works correctly")
            else:
                print(f"   ✗ AJAX response indicates failure: {data}")
                return False
        else:
            print(f"   ✗ Expected 200, got {response.status_code}")
            return False
    except Exception as e:
        print(f"   ✗ Error in AJAX school switching: {e}")
        return False
    
    # Test 4: Get current school API
    print("4. Testing get current school API...")
    request = factory.get('/core/school/current/')
    request.user = user
    
    # Add session with selected school
    middleware = SessionMiddleware(lambda x: None)
    middleware.process_request(request)
    request.session['selected_school_id'] = str(school2.id)
    request.session.save()
    
    try:
        response = get_current_school_api(request)
        print(f"   Response status: {response.status_code}")
        if response.status_code == 200:
            data = json.loads(response.content)
            if data.get('success'):
                print("   ✓ Get current school API works correctly")
            else:
                print(f"   ✗ API response indicates failure: {data}")
                return False
        else:
            print(f"   ✗ Expected 200, got {response.status_code}")
            return False
    except Exception as e:
        print(f"   ✗ Error in get current school API: {e}")
        return False
    
    print("\n✅ All direct view tests passed!")
    return True

if __name__ == '__main__':
    try:
        if test_school_views_directly():
            print("\n🎉 School selection views implementation is working correctly!")
        else:
            print("\n❌ Some tests failed!")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)