#!/usr/bin/env python
"""
Test the URL fix for NoReverseMatch error
"""
import requests
import time

def test_classes_page():
    """Test the classes page that was causing NoReverseMatch error"""
    print("🧪 Testing classes page URL fix...")
    
    base_url = "http://127.0.0.1:8000"
    
    try:
        # Test the classes page that was causing the error
        response = requests.get(f"{base_url}/students/classes/", timeout=10)
        
        if response.status_code == 200:
            print("✅ /students/classes/ - Status: 200 (Fixed!)")
            
            # Check if the page loads without NoReverseMatch error
            if "NoReverseMatch" in response.text:
                print("❌ NoReverseMatch error still present in page content")
            else:
                print("✅ No NoReverseMatch error found - URL patterns fixed!")
                
        elif response.status_code == 500:
            print("❌ /students/classes/ - Status: 500 (Server Error)")
            print("   Check server logs for details")
        else:
            print(f"⚠️ /students/classes/ - Status: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection refused - Make sure server is running")
        print("   Run: python manage.py runserver")
    except Exception as e:
        print(f"❌ Error testing classes page: {e}")

def test_other_pages():
    """Test other key pages"""
    print("\n🌐 Testing other key pages...")
    
    base_url = "http://127.0.0.1:8000"
    
    test_urls = [
        "/students/",
        "/academics/subjects/",
        "/academics/teachers/",
        "/finance/",
        "/hr/employees/",
    ]
    
    for url in test_urls:
        try:
            response = requests.get(f"{base_url}{url}", timeout=10)
            
            if response.status_code == 200:
                print(f"✅ {url} - Status: 200")
            elif response.status_code == 302:
                print(f"🔄 {url} - Status: 302 (Redirect)")
            elif response.status_code == 403:
                print(f"🔒 {url} - Status: 403 (Permission required)")
            else:
                print(f"❌ {url} - Status: {response.status_code}")
                
        except Exception as e:
            print(f"❌ {url} - Error: {e}")

def main():
    """Main test function"""
    print("🚀 Testing URL fixes...")
    print("=" * 50)
    
    print("💡 Make sure the Django server is running:")
    print("   python manage.py runserver")
    print()
    
    # Wait for server
    time.sleep(1)
    
    # Test the specific page that was causing issues
    test_classes_page()
    
    # Test other pages
    test_other_pages()
    
    print("\n" + "=" * 50)
    print("🎉 URL fix testing completed!")
    print("\n💡 If all tests pass:")
    print("1. The NoReverseMatch error should be fixed")
    print("2. All UUID-based URLs should work properly")
    print("3. The system is ready for full testing")

if __name__ == '__main__':
    main()