#!/usr/bin/env python
"""
Comprehensive URL testing script for School ERP system
Tests all URLs and generates accessibility report
"""
import os
import sys
import django
import requests
from urllib.parse import urljoin
from datetime import datetime
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
django.setup()

from django.urls import reverse
from django.test import Client
from django.contrib.auth import get_user_model
from accounts.models import User

class URLTester:
    def __init__(self):
        self.client = Client()
        self.base_url = 'http://127.0.0.1:8000'
        self.results = {
            'total_urls': 0,
            'successful': 0,
            'failed': 0,
            'redirected': 0,
            'errors': [],
            'details': []
        }
        self.users = {}
        
    def setup_test_users(self):
        """Create or get test users for different roles"""
        User = get_user_model()
        
        # Admin user
        admin, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'user_type': 'admin',
                'first_name': 'Admin',
                'last_name': 'User',
                'email': '<EMAIL>',
                'is_staff': True,
                'is_superuser': True,
                'is_active': True
            }
        )
        if created:
            admin.set_password('admin123')
            admin.save()
        self.users['admin'] = admin
        
        # Teacher user
        teacher, created = User.objects.get_or_create(
            username='teacher01',
            defaults={
                'user_type': 'teacher',
                'first_name': 'Teacher',
                'last_name': 'User',
                'email': '<EMAIL>',
                'is_active': True
            }
        )
        if created:
            teacher.set_password('password123')
            teacher.save()
        self.users['teacher'] = teacher
        
        # Parent user
        parent, created = User.objects.get_or_create(
            username='parent001',
            defaults={
                'user_type': 'parent',
                'first_name': 'Parent',
                'last_name': 'User',
                'email': '<EMAIL>',
                'is_active': True
            }
        )
        if created:
            parent.set_password('password123')
            parent.save()
        self.users['parent'] = parent
        
        # Student user
        student, created = User.objects.get_or_create(
            username='student0001',
            defaults={
                'user_type': 'student',
                'first_name': 'Student',
                'last_name': 'User',
                'email': '<EMAIL>',
                'is_active': True
            }
        )
        if created:
            student.set_password('password123')
            student.save()
        self.users['student'] = student
        
        print(f"✅ Test users setup complete")
    
    def login_user(self, user_type):
        """Login a specific user type"""
        if user_type in self.users:
            user = self.users[user_type]
            self.client.force_login(user)
            return True
        return False
    
    def test_url(self, url_name, url_path, user_type=None, url_kwargs=None):
        """Test a single URL"""
        try:
            # Login user if specified
            if user_type:
                self.login_user(user_type)
            
            # Try to reverse the URL if it's a named URL
            if url_name:
                try:
                    if url_kwargs:
                        test_url = reverse(url_name, kwargs=url_kwargs)
                    else:
                        test_url = reverse(url_name)
                except Exception as e:
                    test_url = url_path
            else:
                test_url = url_path
            
            # Make the request
            response = self.client.get(test_url, follow=True)
            
            # Analyze response
            status_code = response.status_code
            
            result = {
                'url_name': url_name,
                'url_path': test_url,
                'user_type': user_type,
                'status_code': status_code,
                'success': False,
                'message': '',
                'redirect_chain': []
            }
            
            if status_code == 200:
                result['success'] = True
                result['message'] = 'OK'
                self.results['successful'] += 1
            elif status_code in [301, 302]:
                result['success'] = True
                result['message'] = 'Redirected'
                result['redirect_chain'] = [r[0] for r in response.redirect_chain]
                self.results['redirected'] += 1
            elif status_code == 403:
                result['message'] = 'Forbidden (Permission denied)'
                self.results['failed'] += 1
            elif status_code == 404:
                result['message'] = 'Not Found'
                self.results['failed'] += 1
            elif status_code == 500:
                result['message'] = 'Internal Server Error'
                self.results['failed'] += 1
            else:
                result['message'] = f'HTTP {status_code}'
                self.results['failed'] += 1
            
            self.results['details'].append(result)
            self.results['total_urls'] += 1
            
            return result
            
        except Exception as e:
            error_result = {
                'url_name': url_name,
                'url_path': url_path,
                'user_type': user_type,
                'status_code': 'ERROR',
                'success': False,
                'message': str(e),
                'redirect_chain': []
            }
            self.results['details'].append(error_result)
            self.results['errors'].append(str(e))
            self.results['failed'] += 1
            self.results['total_urls'] += 1
            return error_result
    
    def run_comprehensive_test(self):
        """Run comprehensive URL testing"""
        print("🚀 Starting comprehensive URL testing...")
        print("="*60)
        
        # Setup test users
        self.setup_test_users()
        
        # Define URLs to test with different user types
        urls_to_test = [
            # Public URLs
            ('home', '/', None),
            ('accounts:login', '/accounts/login/', None),
            ('accounts:register', '/accounts/register/', None),
            
            # Admin URLs
            ('admin:index', '/admin/', 'admin'),
            
            # Dashboard URLs (different user types)
            ('accounts:dashboard', '/accounts/dashboard/', 'admin'),
            ('accounts:dashboard', '/accounts/dashboard/', 'teacher'),
            ('accounts:dashboard', '/accounts/dashboard/', 'parent'),
            ('accounts:dashboard', '/accounts/dashboard/', 'student'),
            
            # Student Management URLs
            ('students:student_list', '/students/', 'admin'),
            ('students:student_create', '/students/create/', 'admin'),
            ('students:grade_list', '/students/grades/', 'admin'),
            ('students:class_list', '/students/classes/', 'admin'),
            ('students:parent_list', '/students/parents/', 'admin'),
            
            # Academic URLs
            ('academics:subject_list', '/academics/subjects/', 'admin'),
            ('academics:teacher_list', '/academics/teachers/', 'admin'),
            ('academics:schedule_list', '/academics/schedules/', 'admin'),
            ('academics:exam_list', '/academics/exams/', 'admin'),
            ('academics:grade_list', '/academics/grades/', 'admin'),
            
            # Finance URLs
            ('finance:dashboard', '/finance/', 'admin'),
            ('finance:account_list', '/finance/accounts/', 'admin'),
            ('finance:fee_type_list', '/finance/fee-types/', 'admin'),
            ('finance:payment_list', '/finance/payments/', 'admin'),
            ('finance:student_fee_list', '/finance/student-fees/', 'admin'),
            
            # HR URLs
            ('hr:employee_list', '/hr/employees/', 'admin'),
            ('hr:department_list', '/hr/departments/', 'admin'),
            ('hr:position_list', '/hr/positions/', 'admin'),
            ('hr:attendance_list', '/hr/attendance/', 'admin'),
            
            # Health URLs
            ('health:health_record_list', '/health/', 'admin'),
            ('health:medical_checkup_list', '/health/checkups/', 'admin'),
            ('health:vaccination_list', '/health/vaccinations/', 'admin'),
            
            # Library URLs
            ('library:book_list', '/library/', 'admin'),
            ('library:category_list', '/library/categories/', 'admin'),
            ('library:issue_list', '/library/issues/', 'admin'),
            
            # Transportation URLs
            ('transportation:route_list', '/transportation/routes/', 'admin'),
            ('transportation:bus_list', '/transportation/buses/', 'admin'),
            ('transportation:student_transport_list', '/transportation/student-transport/', 'admin'),
            
            # Inventory URLs
            ('inventory:item_list', '/inventory/', 'admin'),
            ('inventory:category_list', '/inventory/categories/', 'admin'),
            ('inventory:supplier_list', '/inventory/suppliers/', 'admin'),
            
            # Reports URLs
            ('reports:dashboard', '/reports/', 'admin'),
            ('reports:student_report', '/reports/students/', 'admin'),
            ('reports:financial_report', '/reports/financial/', 'admin'),
            ('reports:academic_report', '/reports/academic/', 'admin'),
            
            # Core URLs
            ('core:school_settings', '/core/settings/', 'admin'),
            ('core:academic_year_list', '/core/academic-years/', 'admin'),
            ('core:semester_list', '/core/semesters/', 'admin'),
            
            # API URLs (basic endpoints)
            (None, '/api/', 'admin'),
            (None, '/api/students/', 'admin'),
            (None, '/api/teachers/', 'admin'),
            (None, '/api/classes/', 'admin'),
        ]
        
        print(f"📋 Testing {len(urls_to_test)} URLs...")
        print("-" * 60)
        
        # Test each URL
        for url_name, url_path, user_type in urls_to_test:
            result = self.test_url(url_name, url_path, user_type)
            
            # Print result
            status_icon = "✅" if result['success'] else "❌"
            user_info = f" [{user_type}]" if user_type else ""
            print(f"{status_icon} {result['url_path']}{user_info} - {result['message']}")
            
            if result['redirect_chain']:
                print(f"   └─ Redirected through: {' -> '.join(result['redirect_chain'])}")
        
        print("-" * 60)
        
        # Test some URLs with parameters (if we have data)
        print("🔍 Testing parameterized URLs...")
        
        # Get some sample IDs from database
        try:
            from students.models import Student, Grade, Class
            from academics.models import Subject, Teacher
            from finance.models import Payment
            
            # Test student detail URLs
            students = Student.objects.all()[:3]
            for student in students:
                result = self.test_url(
                    'students:student_detail',
                    f'/students/{student.pk}/',
                    'admin',
                    {'pk': student.pk}
                )
                status_icon = "✅" if result['success'] else "❌"
                print(f"{status_icon} Student Detail #{student.pk} - {result['message']}")
            
            # Test grade detail URLs
            grades = Grade.objects.all()[:3]
            for grade in grades:
                result = self.test_url(
                    'students:grade_detail',
                    f'/students/grades/{grade.pk}/',
                    'admin',
                    {'pk': grade.pk}
                )
                status_icon = "✅" if result['success'] else "❌"
                print(f"{status_icon} Grade Detail #{grade.pk} - {result['message']}")
            
        except Exception as e:
            print(f"⚠️ Could not test parameterized URLs: {e}")
        
        print("-" * 60)
        
        # Generate summary
        self.generate_summary()
        
        # Generate detailed report
        self.generate_detailed_report()
    
    def generate_summary(self):
        """Generate test summary"""
        print("\n📊 TEST SUMMARY")
        print("=" * 40)
        print(f"Total URLs tested: {self.results['total_urls']}")
        print(f"✅ Successful: {self.results['successful']}")
        print(f"🔄 Redirected: {self.results['redirected']}")
        print(f"❌ Failed: {self.results['failed']}")
        
        if self.results['total_urls'] > 0:
            success_rate = ((self.results['successful'] + self.results['redirected']) / self.results['total_urls']) * 100
            print(f"📈 Success Rate: {success_rate:.1f}%")
        
        if self.results['errors']:
            print(f"\n⚠️ Errors encountered: {len(self.results['errors'])}")
            for error in self.results['errors'][:5]:  # Show first 5 errors
                print(f"   • {error}")
            if len(self.results['errors']) > 5:
                print(f"   ... and {len(self.results['errors']) - 5} more")
    
    def generate_detailed_report(self):
        """Generate detailed HTML report"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"url_test_report_{timestamp}.html"
        
        html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL Test Report - {timestamp}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background: #f4f4f4; padding: 20px; border-radius: 5px; }}
        .summary {{ display: flex; gap: 20px; margin: 20px 0; }}
        .stat-box {{ background: #e9ecef; padding: 15px; border-radius: 5px; text-align: center; }}
        .success {{ color: #28a745; }}
        .redirect {{ color: #ffc107; }}
        .failed {{ color: #dc3545; }}
        table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .status-success {{ background-color: #d4edda; }}
        .status-redirect {{ background-color: #fff3cd; }}
        .status-failed {{ background-color: #f8d7da; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🌐 School ERP URL Test Report</h1>
        <p>Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
    </div>
    
    <div class="summary">
        <div class="stat-box">
            <h3>Total URLs</h3>
            <p style="font-size: 24px; margin: 0;">{self.results['total_urls']}</p>
        </div>
        <div class="stat-box">
            <h3 class="success">Successful</h3>
            <p style="font-size: 24px; margin: 0;">{self.results['successful']}</p>
        </div>
        <div class="stat-box">
            <h3 class="redirect">Redirected</h3>
            <p style="font-size: 24px; margin: 0;">{self.results['redirected']}</p>
        </div>
        <div class="stat-box">
            <h3 class="failed">Failed</h3>
            <p style="font-size: 24px; margin: 0;">{self.results['failed']}</p>
        </div>
    </div>
    
    <h2>📋 Detailed Results</h2>
    <table>
        <thead>
            <tr>
                <th>URL Path</th>
                <th>URL Name</th>
                <th>User Type</th>
                <th>Status Code</th>
                <th>Message</th>
                <th>Redirects</th>
            </tr>
        </thead>
        <tbody>
"""
        
        for detail in self.results['details']:
            status_class = 'status-success' if detail['success'] else 'status-failed'
            if detail['status_code'] in [301, 302]:
                status_class = 'status-redirect'
            
            redirects = ' → '.join(detail['redirect_chain']) if detail['redirect_chain'] else '-'
            
            html_content += f"""
            <tr class="{status_class}">
                <td>{detail['url_path']}</td>
                <td>{detail['url_name'] or '-'}</td>
                <td>{detail['user_type'] or '-'}</td>
                <td>{detail['status_code']}</td>
                <td>{detail['message']}</td>
                <td>{redirects}</td>
            </tr>
"""
        
        html_content += """
        </tbody>
    </table>
    
    <h2>🔧 Recommendations</h2>
    <ul>
        <li>✅ URLs returning 200 are working correctly</li>
        <li>🔄 URLs returning 301/302 are redirecting (usually normal)</li>
        <li>❌ URLs returning 404 may need route fixes</li>
        <li>🔒 URLs returning 403 may need permission adjustments</li>
        <li>💥 URLs returning 500 need debugging</li>
    </ul>
    
    <footer style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666;">
        <p>Generated by School ERP URL Tester</p>
    </footer>
</body>
</html>
"""
        
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"\n📄 Detailed report saved to: {report_filename}")
        
        # Also save JSON report
        json_filename = f"url_test_report_{timestamp}.json"
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        print(f"📄 JSON report saved to: {json_filename}")


def main():
    """Main function to run URL testing"""
    tester = URLTester()
    tester.run_comprehensive_test()
    
    print("\n🎉 URL testing completed!")
    print("\n💡 Next steps:")
    print("1. Review the generated HTML report for detailed results")
    print("2. Fix any URLs returning 404 or 500 errors")
    print("3. Verify permission settings for 403 errors")
    print("4. Test the system manually with different user roles")
    print("\n🌐 Start the development server with: python manage.py runserver")


if __name__ == '__main__':
    main()