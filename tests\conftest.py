"""
Pytest configuration and fixtures for School ERP System tests
"""
import os
import django
from django.conf import settings

# Configure Django settings
if not settings.configured:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'school_erp.settings')
    django.setup()

import pytest
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal

from core.models import School, AcademicYear
from students.models import Student, Parent
from academics.models import Grade, Subject, Class
from hr.models import Employee, Department
from finance.models import Account, GradeFee, StudentFee
from transportation.models import Vehicle, Driver, Route, BusStop
from library.models import Book, Author, Category
from inventory.models import Asset, InventoryItem, AssetCategory
from health.models import HealthProfile

User = get_user_model()


@pytest.fixture
def school():
    """Create a test school"""
    import uuid
    unique_code = f"TEST{str(uuid.uuid4())[:8].upper()}"
    return School.objects.create(
        name="Test School",
        code=unique_code,
        address="123 Test Street",
        phone="************",
        email="<EMAIL>",
        website="https://testschool.com",
        principal_name="Test Principal",
        established_date=timezone.now().date(),
        timezone="UTC",
        currency="USD",
        academic_year_start_month=9,
        is_active=True
    )


@pytest.fixture
def academic_year(school):
    """Create a test academic year"""
    current_year = timezone.now().year
    return AcademicYear.objects.create(
        school=school,
        name=f"{current_year}-{current_year + 1}",
        start_date=datetime(current_year, 9, 1).date(),
        end_date=datetime(current_year + 1, 6, 30).date(),
        is_current=True
    )


@pytest.fixture
def admin_user(school):
    """Create an admin user"""
    user = User.objects.create_user(
        username="admin",
        email="<EMAIL>",
        password="testpass123",
        first_name="Admin",
        last_name="User"
    )
    
    # Create employee record
    department = Department.objects.create(
        school=school,
        name="Administration",
        code="ADMIN"
    )
    
    Employee.objects.create(
        school=school,
        user=user,
        employee_id="EMP001",
        department=department,
        position="Administrator",
        hire_date=timezone.now().date(),
        employment_type="full_time",
        status="active"
    )
    
    return user


@pytest.fixture
def teacher_user(school):
    """Create a teacher user"""
    user = User.objects.create_user(
        username="teacher",
        email="<EMAIL>",
        password="testpass123",
        first_name="Teacher",
        last_name="User"
    )
    
    department = Department.objects.create(
        school=school,
        name="Academic",
        code="ACAD"
    )
    
    Employee.objects.create(
        school=school,
        user=user,
        employee_id="EMP002",
        department=department,
        position="Teacher",
        hire_date=timezone.now().date(),
        employment_type="full_time",
        status="active"
    )
    
    return user


@pytest.fixture
def parent_user(school):
    """Create a parent user"""
    user = User.objects.create_user(
        username="parent",
        email="<EMAIL>",
        password="testpass123",
        first_name="Parent",
        last_name="User"
    )
    
    Parent.objects.create(
        school=school,
        user=user,
        father_name="Father Name",
        mother_name="Mother Name",
        phone="************",
        email="<EMAIL>",
        address="123 Parent Street"
    )
    
    return user


@pytest.fixture
def student(school, parent_user):
    """Create a test student"""
    parent = Parent.objects.get(user=parent_user)
    return Student.objects.create(
        school=school,
        student_id="STU001",
        first_name="Test",
        last_name="Student",
        date_of_birth=datetime(2010, 1, 1).date(),
        gender="male",
        nationality="US",
        parent=parent,
        admission_date=timezone.now().date(),
        status="active"
    )


@pytest.fixture
def grade(school, academic_year):
    """Create a test grade"""
    return Grade.objects.create(
        school=school,
        academic_year=academic_year,
        name="Grade 1",
        level=1,
        capacity=30,
        is_active=True
    )


@pytest.fixture
def subject(school):
    """Create a test subject"""
    return Subject.objects.create(
        school=school,
        name="Mathematics",
        code="MATH",
        credits=3,
        is_active=True
    )


@pytest.fixture
def class_instance(school, grade, subject, teacher_user, academic_year):
    """Create a test class"""
    teacher = Employee.objects.get(user=teacher_user)
    return Class.objects.create(
        school=school,
        academic_year=academic_year,
        grade=grade,
        subject=subject,
        teacher=teacher,
        name="Math Class A",
        section="A",
        capacity=25,
        is_active=True
    )


@pytest.fixture
def account(school):
    """Create a test account"""
    return Account.objects.create(
        school=school,
        code="1000",
        name="Cash",
        account_type="asset",
        is_active=True
    )


@pytest.fixture
def grade_fee(school, grade, academic_year):
    """Create a test grade fee"""
    return GradeFee.objects.create(
        school=school,
        academic_year=academic_year,
        grade=grade,
        name="Standard Fees",
        amount=Decimal("1000.00"),
        is_active=True
    )


@pytest.fixture
def vehicle(school):
    """Create a test vehicle"""
    return Vehicle.objects.create(
        school=school,
        vehicle_number="BUS001",
        license_plate="ABC123",
        make="Ford",
        model="Transit",
        year=2020,
        capacity=50,
        fuel_type="diesel",
        status="active"
    )


@pytest.fixture
def driver(school, admin_user):
    """Create a test driver"""
    employee = Employee.objects.get(user=admin_user)
    return Driver.objects.create(
        school=school,
        employee=employee,
        license_number="DL123456",
        license_expiry=timezone.now().date() + timedelta(days=365),
        experience_years=5,
        status="active"
    )


@pytest.fixture
def route(school, vehicle, driver):
    """Create a test route"""
    return Route.objects.create(
        school=school,
        name="Route A",
        code="RT001",
        vehicle=vehicle,
        primary_driver=driver,
        max_capacity=40,
        status="active"
    )


@pytest.fixture
def bus_stop(school):
    """Create a test bus stop"""
    return BusStop.objects.create(
        school=school,
        name="Main Street Stop",
        address="123 Main Street",
        coordinates="40.7128,-74.0060",
        status="active"
    )


@pytest.fixture
def book_category():
    """Create a test book category"""
    return Category.objects.create(
        name="Fiction",
        description="Fiction books"
    )


@pytest.fixture
def author():
    """Create a test author"""
    return Author.objects.create(
        name="Test Author",
        biography="Test author biography"
    )


@pytest.fixture
def book(school, book_category, author):
    """Create a test book"""
    book = Book.objects.create(
        school=school,
        title="Test Book",
        isbn="1234567890123",
        category=book_category,
        publication_year=2020,
        copies_total=5,
        copies_available=5,
        status="available"
    )
    book.authors.add(author)
    return book


@pytest.fixture
def inventory_category(school):
    """Create a test inventory category"""
    return AssetCategory.objects.create(
        school=school,
        name="Electronics",
        description="Electronic items"
    )


@pytest.fixture
def asset(school, inventory_category):
    """Create a test asset"""
    return Asset.objects.create(
        school=school,
        name="Test Computer",
        asset_tag="COMP001",
        category=inventory_category,
        purchase_date=timezone.now().date(),
        purchase_cost=Decimal("1000.00"),
        status="active"
    )


@pytest.fixture
def inventory_item(school, inventory_category):
    """Create a test inventory item"""
    return InventoryItem.objects.create(
        school=school,
        name="Test Item",
        sku="ITEM001",
        category=inventory_category,
        unit_cost=Decimal("10.00"),
        quantity_in_stock=100,
        reorder_level=10,
        status="active"
    )


@pytest.fixture
def health_profile(school, student):
    """Create a test health profile"""
    return HealthProfile.objects.create(
        school=school,
        student=student,
        blood_type="O+",
        height=150.0,
        weight=45.0,
        allergies="None",
        medications="None",
        emergency_contact_name="Emergency Contact",
        emergency_contact_phone="************"
    )


# Database fixtures
@pytest.fixture(scope='session')
def django_db_setup():
    """Setup test database"""
    pass


@pytest.fixture
def transactional_db(db):
    """Use transactional database for tests that need it"""
    pass


# Custom markers
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.slow = pytest.mark.slow