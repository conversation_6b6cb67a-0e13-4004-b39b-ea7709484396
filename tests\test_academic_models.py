"""
Unit tests for academic models
"""
import pytest
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal

from academics.models import Grade, Subject, Class, Timetable, Attendance, Grade as GradeRecord, Exam


@pytest.mark.unit
class TestGradeModel:
    """Test Grade model"""
    
    def test_grade_creation(self, grade):
        """Test grade creation"""
        assert grade.name == "Grade 1"
        assert grade.level == 1
        assert grade.capacity == 30
        assert grade.is_active is True
        assert str(grade) == "Grade 1"
    
    def test_grade_validation(self, school, academic_year):
        """Test grade validation"""
        # Test negative level
        with pytest.raises(ValidationError):
            grade = Grade(
                school=school,
                academic_year=academic_year,
                name="Invalid Grade",
                level=-1,  # Invalid level
                capacity=30,
                is_active=True
            )
            grade.full_clean()
        
        # Test zero capacity
        with pytest.raises(ValidationError):
            grade = Grade(
                school=school,
                academic_year=academic_year,
                name="Invalid Grade",
                level=1,
                capacity=0,  # Invalid capacity
                is_active=True
            )
            grade.full_clean()
    
    def test_grade_methods(self, grade):
        """Test grade methods"""
        assert grade.get_current_enrollment() == 0  # No students enrolled yet
        assert grade.get_available_capacity() == 30
        assert grade.is_full() is False
    
    def test_grade_capacity_management(self, grade, student):
        """Test grade capacity management"""
        from students.models import StudentEnrollment
        
        # Enroll student
        StudentEnrollment.objects.create(
            school=grade.school,
            student=student,
            academic_year=grade.academic_year,
            grade=grade,
            enrollment_date=timezone.now().date(),
            status="active"
        )
        
        assert grade.get_current_enrollment() == 1
        assert grade.get_available_capacity() == 29


@pytest.mark.unit
class TestSubjectModel:
    """Test Subject model"""
    
    def test_subject_creation(self, subject):
        """Test subject creation"""
        assert subject.name == "Mathematics"
        assert subject.code == "MATH"
        assert subject.credits == 3
        assert subject.is_active is True
        assert str(subject) == "Mathematics (MATH)"
    
    def test_subject_validation(self, school):
        """Test subject validation"""
        # Test duplicate code
        Subject.objects.create(
            school=school,
            name="Science",
            code="UNIQUE001",
            credits=3,
            is_active=True
        )
        
        with pytest.raises(ValidationError):
            subject2 = Subject(
                school=school,
                name="Physics",
                code="UNIQUE001",  # Duplicate code
                credits=3,
                is_active=True
            )
            subject2.full_clean()
    
    def test_subject_methods(self, subject):
        """Test subject methods"""
        assert subject.get_total_classes() == 0  # No classes yet
        assert subject.is_core_subject() is False  # Default
    
    def test_subject_prerequisites(self, school):
        """Test subject prerequisites"""
        # Create prerequisite subject
        prereq = Subject.objects.create(
            school=school,
            name="Basic Math",
            code="BMATH",
            credits=2,
            is_active=True
        )
        
        # Create advanced subject with prerequisite
        advanced = Subject.objects.create(
            school=school,
            name="Advanced Math",
            code="AMATH",
            credits=4,
            is_active=True
        )
        advanced.prerequisites.add(prereq)
        
        assert prereq in advanced.prerequisites.all()
        assert advanced.has_prerequisites() is True


@pytest.mark.unit
class TestClassModel:
    """Test Class model"""
    
    def test_class_creation(self, class_instance):
        """Test class creation"""
        assert class_instance.name == "Math Class A"
        assert class_instance.section == "A"
        assert class_instance.capacity == 25
        assert class_instance.is_active is True
        assert str(class_instance) == "Math Class A - Grade 1"
    
    def test_class_validation(self, school, grade, subject, teacher_user, academic_year):
        """Test class validation"""
        teacher = teacher_user.employee
        
        # Test capacity exceeding grade capacity
        with pytest.raises(ValidationError):
            class_obj = Class(
                school=school,
                academic_year=academic_year,
                grade=grade,
                subject=subject,
                teacher=teacher,
                name="Invalid Class",
                section="B",
                capacity=50,  # Exceeds grade capacity of 30
                is_active=True
            )
            class_obj.full_clean()
    
    def test_class_methods(self, class_instance):
        """Test class methods"""
        assert class_instance.get_enrolled_students_count() == 0
        assert class_instance.get_available_seats() == 25
        assert class_instance.is_full() is False
    
    def test_class_schedule(self, class_instance):
        """Test class schedule"""
        # Create timetable entry
        timetable = Timetable.objects.create(
            school=class_instance.school,
            class_instance=class_instance,
            day_of_week=1,  # Monday
            start_time="09:00",
            end_time="10:00",
            is_active=True
        )
        
        schedule = class_instance.get_weekly_schedule()
        assert len(schedule) >= 1
        assert timetable in schedule


@pytest.mark.unit
class TestTimetableModel:
    """Test Timetable model"""
    
    def test_timetable_creation(self, class_instance):
        """Test timetable creation"""
        timetable = Timetable.objects.create(
            school=class_instance.school,
            class_instance=class_instance,
            day_of_week=1,  # Monday
            start_time="09:00",
            end_time="10:00",
            is_active=True
        )
        
        assert timetable.day_of_week == 1
        assert str(timetable.start_time) == "09:00:00"
        assert str(timetable.end_time) == "10:00:00"
        assert str(timetable) == f"Math Class A - Monday 09:00-10:00"
    
    def test_timetable_validation(self, class_instance):
        """Test timetable validation"""
        # Test end time before start time
        with pytest.raises(ValidationError):
            timetable = Timetable(
                school=class_instance.school,
                class_instance=class_instance,
                day_of_week=1,
                start_time="10:00",
                end_time="09:00",  # Before start time
                is_active=True
            )
            timetable.full_clean()
    
    def test_timetable_conflicts(self, class_instance, teacher_user):
        """Test timetable conflict detection"""
        # Create first timetable entry
        Timetable.objects.create(
            school=class_instance.school,
            class_instance=class_instance,
            day_of_week=1,
            start_time="09:00",
            end_time="10:00",
            is_active=True
        )
        
        # Create another class for the same teacher
        from academics.models import Class
        class2 = Class.objects.create(
            school=class_instance.school,
            academic_year=class_instance.academic_year,
            grade=class_instance.grade,
            subject=class_instance.subject,
            teacher=class_instance.teacher,
            name="Math Class B",
            section="B",
            capacity=25,
            is_active=True
        )
        
        # Try to create conflicting timetable
        with pytest.raises(ValidationError):
            timetable2 = Timetable(
                school=class_instance.school,
                class_instance=class2,
                day_of_week=1,  # Same day
                start_time="09:30",  # Overlapping time
                end_time="10:30",
                is_active=True
            )
            timetable2.full_clean()


@pytest.mark.unit
class TestAttendanceModel:
    """Test Attendance model"""
    
    def test_attendance_creation(self, class_instance, student):
        """Test attendance creation"""
        attendance = Attendance.objects.create(
            school=class_instance.school,
            class_instance=class_instance,
            student=student,
            date=timezone.now().date(),
            status="present",
            marked_by=class_instance.teacher
        )
        
        assert attendance.student == student
        assert attendance.status == "present"
        assert attendance.date == timezone.now().date()
        assert str(attendance) == f"Test Student - Math Class A - {timezone.now().date()}"
    
    def test_attendance_validation(self, class_instance, student):
        """Test attendance validation"""
        # Create first attendance record
        Attendance.objects.create(
            school=class_instance.school,
            class_instance=class_instance,
            student=student,
            date=timezone.now().date(),
            status="present",
            marked_by=class_instance.teacher
        )
        
        # Try to create duplicate attendance for same day
        with pytest.raises(ValidationError):
            attendance2 = Attendance(
                school=class_instance.school,
                class_instance=class_instance,
                student=student,
                date=timezone.now().date(),  # Same date
                status="absent",
                marked_by=class_instance.teacher
            )
            attendance2.full_clean()
    
    def test_attendance_statistics(self, class_instance, student):
        """Test attendance statistics"""
        # Create attendance records
        dates = [
            timezone.now().date() - timedelta(days=i)
            for i in range(10)
        ]
        
        for i, date in enumerate(dates):
            status = "present" if i < 8 else "absent"
            Attendance.objects.create(
                school=class_instance.school,
                class_instance=class_instance,
                student=student,
                date=date,
                status=status,
                marked_by=class_instance.teacher
            )
        
        stats = student.get_attendance_statistics(class_instance)
        assert stats['total_days'] == 10
        assert stats['present_days'] == 8
        assert stats['absent_days'] == 2
        assert stats['attendance_percentage'] == 80.0


@pytest.mark.unit
class TestExamModel:
    """Test Exam model"""
    
    def test_exam_creation(self, class_instance):
        """Test exam creation"""
        exam = Exam.objects.create(
            school=class_instance.school,
            class_instance=class_instance,
            name="Midterm Exam",
            exam_type="midterm",
            date=timezone.now().date() + timedelta(days=7),
            start_time="09:00",
            duration_minutes=120,
            total_marks=100,
            passing_marks=40
        )
        
        assert exam.name == "Midterm Exam"
        assert exam.exam_type == "midterm"
        assert exam.total_marks == 100
        assert exam.passing_marks == 40
        assert str(exam) == "Midterm Exam - Math Class A"
    
    def test_exam_validation(self, class_instance):
        """Test exam validation"""
        # Test passing marks greater than total marks
        with pytest.raises(ValidationError):
            exam = Exam(
                school=class_instance.school,
                class_instance=class_instance,
                name="Invalid Exam",
                exam_type="final",
                date=timezone.now().date() + timedelta(days=7),
                start_time="09:00",
                duration_minutes=120,
                total_marks=100,
                passing_marks=150  # Greater than total marks
            )
            exam.full_clean()
    
    def test_exam_methods(self, class_instance):
        """Test exam methods"""
        exam = Exam.objects.create(
            school=class_instance.school,
            class_instance=class_instance,
            name="Test Exam",
            exam_type="quiz",
            date=timezone.now().date() + timedelta(days=7),
            start_time="09:00",
            duration_minutes=60,
            total_marks=50,
            passing_marks=25
        )
        
        assert exam.is_upcoming() is True
        assert exam.get_duration_hours() == 1.0
        assert exam.get_passing_percentage() == 50.0