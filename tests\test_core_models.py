"""
Unit tests for core models
"""
import pytest
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import datetime, timedelta

from core.models import School, AcademicYear


@pytest.mark.unit
@pytest.mark.django_db
class TestSchoolModel:
    """Test School model"""
    
    def test_school_creation(self, school):
        """Test school creation"""
        assert school.name == "Test School"
        assert school.code.startswith("TEST")
        assert school.is_active is True
        assert str(school) == "Test School"
    
    def test_school_validation(self, db):
        """Test school validation"""
        # Test duplicate code validation
        School.objects.create(
            name="School 1",
            code="UNIQUE001",
            address="123 Test Street",
            phone="************",
            email="<EMAIL>"
        )
        
        with pytest.raises(ValidationError):
            school2 = School(
                name="School 2",
                code="UNIQUE001",  # Duplicate code
                address="456 Test Street",
                phone="************",
                email="<EMAIL>"
            )
            school2.full_clean()
    
    def test_school_methods(self, school):
        """Test school methods"""
        assert school.get_absolute_url() == f"/core/schools/{school.pk}/"
        assert school.is_academic_year_active() is False  # No academic year yet
    
    def test_school_settings(self, school):
        """Test school settings"""
        school.settings = {
            'timezone': 'UTC',
            'language': 'en',
            'currency': 'USD'
        }
        school.save()
        
        assert school.settings['timezone'] == 'UTC'
        assert school.settings['language'] == 'en'


@pytest.mark.unit
@pytest.mark.django_db
class TestAcademicYearModel:
    """Test AcademicYear model"""
    
    def test_academic_year_creation(self, academic_year):
        """Test academic year creation"""
        assert academic_year.is_current is True
        assert academic_year.school.name == "Test School"
        assert str(academic_year) == f"{academic_year.name} - Test School"
    
    def test_academic_year_validation(self, school):
        """Test academic year validation"""
        current_year = timezone.now().year
        
        # Test end date before start date
        with pytest.raises(ValidationError):
            ay = AcademicYear(
                school=school,
                name=f"{current_year}-{current_year + 1}",
                start_date=datetime(current_year, 9, 1).date(),
                end_date=datetime(current_year, 6, 30).date(),  # Before start date
                is_current=True
            )
            ay.full_clean()
    
    def test_academic_year_methods(self, academic_year):
        """Test academic year methods"""
        assert academic_year.is_active() is True
        assert academic_year.get_duration_days() > 0
        
        # Test if current date is within academic year
        today = timezone.now().date()
        if academic_year.start_date <= today <= academic_year.end_date:
            assert academic_year.is_current_date_in_year() is True
    
    def test_only_one_current_academic_year(self, school, academic_year):
        """Test that only one academic year can be current"""
        current_year = timezone.now().year + 1
        
        # Create another academic year and set as current
        ay2 = AcademicYear.objects.create(
            school=school,
            name=f"{current_year}-{current_year + 1}",
            start_date=datetime(current_year, 9, 1).date(),
            end_date=datetime(current_year + 1, 6, 30).date(),
            is_current=True
        )
        
        # Refresh first academic year
        academic_year.refresh_from_db()
        
        # First academic year should no longer be current
        assert academic_year.is_current is False
        assert ay2.is_current is True


@pytest.mark.unit
@pytest.mark.django_db
class TestSchoolModelMethods:
    """Test School model methods and properties"""
    
    def test_school_statistics(self, school, student, admin_user):
        """Test school statistics methods"""
        stats = school.get_statistics()
        
        assert 'total_students' in stats
        assert 'total_employees' in stats
        assert 'total_classes' in stats
        assert stats['total_students'] >= 1  # At least our test student
        assert stats['total_employees'] >= 1  # At least our test admin
    
    def test_school_active_status(self, school):
        """Test school active status"""
        assert school.is_active is True
        
        school.is_active = False
        school.save()
        
        assert school.is_active is False
    
    def test_school_contact_info(self, school):
        """Test school contact information"""
        assert school.phone == "************"
        assert school.email == "<EMAIL>"
        assert school.website == "https://testschool.com"
    
    def test_school_settings(self, school):
        """Test school settings"""
        assert school.timezone == "UTC"
        assert school.currency == "USD"
        assert school.academic_year_start_month == 9
        
        # Test settings JSON field
        school.settings = {'test_setting': 'test_value'}
        school.save()
        school.refresh_from_db()
        assert school.settings['test_setting'] == 'test_value'


@pytest.mark.unit
@pytest.mark.django_db
class TestAcademicYearModelMethods:
    """Test AcademicYear model methods and properties"""
    
    def test_academic_year_quarters(self, academic_year):
        """Test academic year quarter calculations"""
        quarters = academic_year.get_quarters()
        
        assert len(quarters) == 4
        assert all('start_date' in q and 'end_date' in q for q in quarters)
    
    def test_academic_year_progress(self, academic_year):
        """Test academic year progress calculation"""
        progress = academic_year.get_progress_percentage()
        
        assert 0 <= progress <= 100
    
    def test_academic_year_remaining_days(self, academic_year):
        """Test remaining days calculation"""
        remaining = academic_year.get_remaining_days()
        
        if academic_year.end_date >= timezone.now().date():
            assert remaining >= 0
        else:
            assert remaining < 0  # Past academic year
    
    def test_academic_year_semester_system(self, academic_year):
        """Test semester system"""
        semesters = academic_year.get_semesters()
        
        assert len(semesters) == 2
        assert semesters[0]['name'] == 'First Semester'
        assert semesters[1]['name'] == 'Second Semester'