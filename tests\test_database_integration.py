"""
Database Integration tests for School ERP System
"""
import pytest
from django.test import TransactionTestCase
from django.db import transaction, IntegrityError, connection
from django.db.models import Q, Count, Sum, Avg
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal


@pytest.mark.integration
class TestDatabaseTransactions:
    """Test database transaction handling"""
    
    def test_atomic_transactions(self, school, parent_user):
        """Test atomic transaction rollback"""
        from students.models import Student
        
        initial_count = Student.objects.count()
        
        try:
            with transaction.atomic():
                # Create valid student
                student1 = Student.objects.create(
                    school=school,
                    student_id="ATOMIC001",
                    first_name="Atomic",
                    last_name="Test1",
                    date_of_birth=datetime(2010, 1, 1).date(),
                    gender="male",
                    nationality="US",
                    parent=parent_user.parent,
                    admission_date=timezone.now().date(),
                    status="active"
                )
                
                # Create invalid student (duplicate ID) - should cause rollback
                student2 = Student.objects.create(
                    school=school,
                    student_id="ATOMIC001",  # Duplicate ID
                    first_name="Atomic",
                    last_name="Test2",
                    date_of_birth=datetime(2010, 1, 1).date(),
                    gender="female",
                    nationality="US",
                    parent=parent_user.parent,
                    admission_date=timezone.now().date(),
                    status="active"
                )
                
        except IntegrityError:
            # Expected - transaction should be rolled back
            pass
        
        # No students should have been created due to rollback
        final_count = Student.objects.count()
        assert final_count == initial_count
    
    def test_nested_transactions(self, school):
        """Test nested transaction handling"""
        from finance.models import Account, Transaction, TransactionEntry
        
        initial_account_count = Account.objects.count()
        initial_transaction_count = Transaction.objects.count()
        
        try:
            with transaction.atomic():
                # Create account in outer transaction
                account = Account.objects.create(
                    school=school,
                    code="NESTED001",
                    name="Nested Test Account",
                    account_type="asset",
                    is_active=True
                )
                
                try:
                    with transaction.atomic():
                        # Create transaction in inner transaction
                        txn = Transaction.objects.create(
                            school=school,
                            reference="NESTED001",
                            description="Nested transaction test",
                            date=timezone.now().date(),
                            total_amount=Decimal('100.00')
                        )
                        
                        # Force an error in inner transaction
                        raise IntegrityError("Simulated error")
                        
                except IntegrityError:
                    # Inner transaction should rollback, but outer should continue
                    pass
                
                # Account should still exist after inner transaction rollback
                assert Account.objects.filter(code="NESTED001").exists()
                
        except Exception as e:
            pytest.fail(f"Unexpected error in nested transaction test: {e}")
        
        # Account should be created, but transaction should not
        assert Account.objects.count() == initial_account_count + 1
        assert Transaction.objects.count() == initial_transaction_count
    
    def test_concurrent_access(self, school, parent_user):
        """Test concurrent database access"""
        from students.models import Student
        import threading
        import queue
        
        results = queue.Queue()
        
        def create_student(thread_id):
            """Create a student in a separate thread"""
            try:
                with transaction.atomic():
                    student = Student.objects.create(
                        school=school,
                        student_id=f"CONCURRENT{thread_id:03d}",
                        first_name=f"Concurrent{thread_id}",
                        last_name="Test",
                        date_of_birth=datetime(2010, 1, 1).date(),
                        gender="male" if thread_id % 2 == 0 else "female",
                        nationality="US",
                        parent=parent_user.parent,
                        admission_date=timezone.now().date(),
                        status="active"
                    )
                    results.put(('success', thread_id, student.id))
                    
            except Exception as e:
                results.put(('error', thread_id, str(e)))
        
        # Create multiple threads
        threads = []
        num_threads = 5
        
        for i in range(num_threads):
            thread = threading.Thread(target=create_student, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Collect results
        successful_creates = 0
        errors = 0
        
        while not results.empty():
            result_type, thread_id, data = results.get()
            if result_type == 'success':
                successful_creates += 1
            else:
                errors += 1
                print(f"Thread {thread_id} error: {data}")
        
        # Most operations should succeed
        assert successful_creates >= num_threads * 0.8  # At least 80% success rate


@pytest.mark.integration
class TestDatabaseConstraints:
    """Test database constraint enforcement"""
    
    def test_unique_constraints(self, school, parent_user):
        """Test unique constraint enforcement"""
        from students.models import Student
        
        # Create first student
        student1 = Student.objects.create(
            school=school,
            student_id="UNIQUE001",
            first_name="Unique",
            last_name="Test1",
            date_of_birth=datetime(2010, 1, 1).date(),
            gender="male",
            nationality="US",
            parent=parent_user.parent,
            admission_date=timezone.now().date(),
            status="active"
        )
        
        # Try to create student with duplicate ID
        with pytest.raises(IntegrityError):
            student2 = Student.objects.create(
                school=school,
                student_id="UNIQUE001",  # Duplicate ID
                first_name="Unique",
                last_name="Test2",
                date_of_birth=datetime(2010, 1, 1).date(),
                gender="female",
                nationality="US",
                parent=parent_user.parent,
                admission_date=timezone.now().date(),
                status="active"
            )
    
    def test_foreign_key_constraints(self, school):
        """Test foreign key constraint enforcement"""
        from students.models import Student, Parent
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # Try to create student with non-existent parent
        with pytest.raises((IntegrityError, ValueError)):
            student = Student.objects.create(
                school=school,
                student_id="FK001",
                first_name="FK",
                last_name="Test",
                date_of_birth=datetime(2010, 1, 1).date(),
                gender="male",
                nationality="US",
                parent_id=99999,  # Non-existent parent ID
                admission_date=timezone.now().date(),
                status="active"
            )
    
    def test_check_constraints(self, school):
        """Test check constraint enforcement"""
        from finance.models import Account
        
        # Try to create account with invalid data
        try:
            account = Account(
                school=school,
                code="",  # Empty code should fail validation
                name="Invalid Account",
                account_type="invalid_type",  # Invalid account type
                is_active=True
            )
            account.full_clean()  # This should raise ValidationError
            account.save()
            pytest.fail("Should have raised validation error")
            
        except Exception:
            # Expected - validation should fail
            pass


@pytest.mark.integration
class TestDatabaseQueries:
    """Test complex database queries"""
    
    def test_complex_joins(self, school, academic_year, grade, student):
        """Test complex JOIN queries"""
        from students.models import StudentEnrollment
        from academics.models import Class, Subject
        from hr.models import Employee
        
        # Create enrollment
        enrollment = StudentEnrollment.objects.create(
            school=school,
            student=student,
            academic_year=academic_year,
            grade=grade,
            enrollment_date=timezone.now().date(),
            status="active"
        )
        
        # Create subject and class
        subject = Subject.objects.create(
            school=school,
            name="Test Subject",
            code="TEST001",
            credits=3,
            is_active=True
        )
        
        # Complex query with multiple joins
        query = StudentEnrollment.objects.filter(
            school=school,
            status='active'
        ).select_related(
            'student__parent__user',
            'grade',
            'academic_year'
        ).prefetch_related(
            'student__class_assignments__subject',
            'student__class_assignments__teacher__user'
        )
        
        # Execute query
        enrollments = list(query)
        
        # Verify results
        assert len(enrollments) >= 1
        assert enrollments[0].student == student
        assert enrollments[0].grade == grade
    
    def test_aggregation_queries(self, school, academic_year):
        """Test aggregation queries"""
        from students.models import Student, Parent, StudentEnrollment
        from academics.models import Grade
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # Create test data
        grades = []
        for i in range(3):
            grade = Grade.objects.create(
                school=school,
                academic_year=academic_year,
                name=f"Grade {i+1}",
                level=i+1,
                capacity=30,
                is_active=True
            )
            grades.append(grade)
        
        # Create students in different grades
        for grade_idx, grade in enumerate(grades):
            for student_idx in range(5):  # 5 students per grade
                parent_user = User.objects.create_user(
                    username=f'aggparent{grade_idx}{student_idx}',
                    email=f'aggparent{grade_idx}{student_idx}@test.com',
                    password='testpass123'
                )
                
                parent = Parent.objects.create(
                    school=school,
                    user=parent_user,
                    father_name=f"Father {grade_idx}{student_idx}",
                    mother_name=f"Mother {grade_idx}{student_idx}",
                    phone=f"123-456-{grade_idx}{student_idx:03d}",
                    email=f"aggparent{grade_idx}{student_idx}@test.com"
                )
                
                student = Student.objects.create(
                    school=school,
                    student_id=f"AGG{grade_idx}{student_idx:03d}",
                    first_name=f"Student{grade_idx}{student_idx}",
                    last_name="Aggregation",
                    date_of_birth=datetime(2010, 1, 1).date(),
                    gender="male" if student_idx % 2 == 0 else "female",
                    nationality="US",
                    parent=parent,
                    admission_date=timezone.now().date(),
                    status="active"
                )
                
                StudentEnrollment.objects.create(
                    school=school,
                    student=student,
                    academic_year=academic_year,
                    grade=grade,
                    enrollment_date=timezone.now().date(),
                    status="active"
                )
        
        # Test aggregation queries
        grade_stats = Grade.objects.filter(
            school=school,
            academic_year=academic_year
        ).annotate(
            student_count=Count('enrollments', filter=Q(enrollments__status='active')),
            avg_level=Avg('level')
        ).order_by('level')
        
        # Verify aggregation results
        grade_list = list(grade_stats)
        assert len(grade_list) == 3
        
        for grade_stat in grade_list:
            assert grade_stat.student_count == 5  # 5 students per grade
        
        # Test school-wide aggregation
        school_stats = StudentEnrollment.objects.filter(
            school=school,
            academic_year=academic_year,
            status='active'
        ).aggregate(
            total_students=Count('id'),
            unique_grades=Count('grade', distinct=True)
        )
        
        assert school_stats['total_students'] == 15  # 3 grades × 5 students
        assert school_stats['unique_grades'] == 3
    
    def test_subqueries(self, school, academic_year):
        """Test subquery usage"""
        from students.models import Student, StudentEnrollment
        from academics.models import Grade
        from django.db.models import OuterRef, Subquery
        
        # Create test data
        grade = Grade.objects.create(
            school=school,
            academic_year=academic_year,
            name="Subquery Grade",
            level=1,
            capacity=30,
            is_active=True
        )
        
        # Query using subqueries
        latest_enrollment = StudentEnrollment.objects.filter(
            student=OuterRef('pk'),
            school=school
        ).order_by('-enrollment_date')
        
        students_with_latest_enrollment = Student.objects.filter(
            school=school,
            status='active'
        ).annotate(
            latest_enrollment_date=Subquery(
                latest_enrollment.values('enrollment_date')[:1]
            ),
            latest_grade_name=Subquery(
                latest_enrollment.values('grade__name')[:1]
            )
        )
        
        # Execute subquery
        students = list(students_with_latest_enrollment)
        
        # Verify subquery results
        for student in students:
            if hasattr(student, 'latest_enrollment_date'):
                assert student.latest_enrollment_date is not None


@pytest.mark.integration
class TestDatabasePerformance:
    """Test database performance optimization"""
    
    def test_query_optimization(self, school, academic_year):
        """Test query optimization techniques"""
        from students.models import Student, Parent, StudentEnrollment
        from academics.models import Grade
        from django.contrib.auth import get_user_model
        from django.test.utils import override_settings
        from django.db import connection
        
        User = get_user_model()
        
        # Create test data
        grade = Grade.objects.create(
            school=school,
            academic_year=academic_year,
            name="Performance Grade",
            level=1,
            capacity=100,
            is_active=True
        )
        
        # Create students
        for i in range(20):  # Reduced for CI
            parent_user = User.objects.create_user(
                username=f'perfparent{i}',
                email=f'perfparent{i}@test.com',
                password='testpass123'
            )
            
            parent = Parent.objects.create(
                school=school,
                user=parent_user,
                father_name=f"Father {i}",
                mother_name=f"Mother {i}",
                phone=f"123-456-{i:04d}",
                email=f"perfparent{i}@test.com"
            )
            
            student = Student.objects.create(
                school=school,
                student_id=f"PERF{i:04d}",
                first_name=f"Student{i}",
                last_name="Performance",
                date_of_birth=datetime(2010, 1, 1).date(),
                gender="male" if i % 2 == 0 else "female",
                nationality="US",
                parent=parent,
                admission_date=timezone.now().date(),
                status="active"
            )
            
            StudentEnrollment.objects.create(
                school=school,
                student=student,
                academic_year=academic_year,
                grade=grade,
                enrollment_date=timezone.now().date(),
                status="active"
            )
        
        # Test optimized vs unoptimized queries
        with override_settings(DEBUG=True):
            # Reset query count
            connection.queries_log.clear()
            
            # Unoptimized query (N+1 problem)
            students = Student.objects.filter(school=school, status='active')
            for student in students:
                _ = student.parent.user.first_name  # This causes N+1 queries
            
            unoptimized_query_count = len(connection.queries)
            
            # Reset query count
            connection.queries_log.clear()
            
            # Optimized query with select_related
            students = Student.objects.filter(
                school=school, 
                status='active'
            ).select_related('parent__user')
            
            for student in students:
                _ = student.parent.user.first_name  # This should use fewer queries
            
            optimized_query_count = len(connection.queries)
            
            # Optimized query should use fewer queries
            assert optimized_query_count < unoptimized_query_count
            print(f"Unoptimized: {unoptimized_query_count} queries, Optimized: {optimized_query_count} queries")
    
    def test_bulk_operations(self, school, academic_year):
        """Test bulk database operations"""
        from students.models import Student, Parent
        from django.contrib.auth import get_user_model
        import time
        
        User = get_user_model()
        
        # Test bulk_create performance
        students_data = []
        parents_data = []
        users_data = []
        
        # Prepare bulk data
        for i in range(50):  # Reduced for CI
            user = User(
                username=f'bulkparent{i}',
                email=f'bulkparent{i}@test.com',
                first_name=f'BulkParent{i}',
                last_name='Test'
            )
            user.set_password('testpass123')
            users_data.append(user)
        
        # Bulk create users
        start_time = time.time()
        User.objects.bulk_create(users_data)
        bulk_user_time = time.time() - start_time
        
        # Get created users for parent creation
        created_users = User.objects.filter(username__startswith='bulkparent').order_by('username')
        
        for i, user in enumerate(created_users):
            parent = Parent(
                school=school,
                user=user,
                father_name=f"BulkFather {i}",
                mother_name=f"BulkMother {i}",
                phone=f"123-456-{i:04d}",
                email=f"bulkparent{i}@test.com"
            )
            parents_data.append(parent)
        
        # Bulk create parents
        start_time = time.time()
        Parent.objects.bulk_create(parents_data)
        bulk_parent_time = time.time() - start_time
        
        # Get created parents for student creation
        created_parents = Parent.objects.filter(user__username__startswith='bulkparent').order_by('user__username')
        
        for i, parent in enumerate(created_parents):
            student = Student(
                school=school,
                student_id=f"BULK{i:04d}",
                first_name=f"BulkStudent{i}",
                last_name="Test",
                date_of_birth=datetime(2010, 1, 1).date(),
                gender="male" if i % 2 == 0 else "female",
                nationality="US",
                parent=parent,
                admission_date=timezone.now().date(),
                status="active"
            )
            students_data.append(student)
        
        # Bulk create students
        start_time = time.time()
        Student.objects.bulk_create(students_data)
        bulk_student_time = time.time() - start_time
        
        print(f"Bulk create times - Users: {bulk_user_time:.3f}s, Parents: {bulk_parent_time:.3f}s, Students: {bulk_student_time:.3f}s")
        
        # Verify bulk creation
        assert User.objects.filter(username__startswith='bulkparent').count() == 50
        assert Parent.objects.filter(user__username__startswith='bulkparent').count() == 50
        assert Student.objects.filter(student_id__startswith='BULK').count() == 50
        
        # Performance assertions
        assert bulk_user_time < 5.0    # Should create 50 users in under 5 seconds
        assert bulk_parent_time < 5.0  # Should create 50 parents in under 5 seconds
        assert bulk_student_time < 5.0 # Should create 50 students in under 5 seconds


@pytest.mark.integration
class TestDatabaseMigrations:
    """Test database migration handling"""
    
    def test_migration_state(self):
        """Test that all migrations are applied"""
        from django.db.migrations.executor import MigrationExecutor
        from django.db import connection
        
        executor = MigrationExecutor(connection)
        plan = executor.migration_plan(executor.loader.graph.leaf_nodes())
        
        # No pending migrations
        assert len(plan) == 0, f"Pending migrations found: {[migration.name for migration in plan]}"
    
    def test_database_schema(self):
        """Test database schema integrity"""
        from django.db import connection
        
        with connection.cursor() as cursor:
            # Get all table names
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
                ORDER BY name;
            """)
            
            tables = [row[0] for row in cursor.fetchall()]
            
            # Verify core tables exist
            expected_tables = [
                'core_school',
                'core_academicyear',
                'auth_user',
                'students_student',
                'students_parent',
                'academics_grade',
                'academics_subject',
                'finance_account',
                'transportation_vehicle',
                'library_book'
            ]
            
            for expected_table in expected_tables:
                if expected_table in tables:
                    print(f"✓ Table {expected_table} exists")
                else:
                    print(f"⚠ Table {expected_table} missing (might be expected)")
            
            # At least some core tables should exist
            core_tables_found = sum(1 for table in expected_tables if table in tables)
            assert core_tables_found >= 3, f"Too few core tables found: {core_tables_found}"