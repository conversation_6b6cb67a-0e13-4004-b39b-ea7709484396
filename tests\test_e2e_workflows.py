"""
End-to-End Workflow tests for School ERP System
"""
import pytest
from django.test import Client, TransactionTestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal

User = get_user_model()


@pytest.mark.integration
class TestStudentAdmissionWorkflow:
    """Test complete student admission workflow"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.client = Client()
    
    def test_complete_admission_process(self, school, academic_year, grade, admin_user):
        """Test complete student admission from application to enrollment"""
        self.client.force_login(admin_user)
        
        try:
            # Step 1: Create parent user
            parent_user = User.objects.create_user(
                username='admission_parent',
                email='<EMAIL>',
                password='testpass123',
                first_name='Admission',
                last_name='Parent'
            )
            
            # Step 2: Create parent profile
            from students.models import Parent
            parent = Parent.objects.create(
                school=school,
                user=parent_user,
                father_name="Admission Father",
                mother_name="Admission Mother",
                phone="************",
                email="<EMAIL>",
                address="123 Admission Street"
            )
            
            # Step 3: Submit student application
            from students.models import Student
            student = Student.objects.create(
                school=school,
                student_id="ADM001",
                first_name="Admission",
                last_name="Student",
                date_of_birth=datetime(2010, 1, 1).date(),
                gender="male",
                nationality="US",
                parent=parent,
                admission_date=timezone.now().date(),
                status="pending"  # Start with pending status
            )
            
            # Step 4: Document submission
            from students.models import StudentDocument
            documents = [
                {
                    'document_type': 'birth_certificate',
                    'title': 'Birth Certificate',
                    'is_required': True
                },
                {
                    'document_type': 'medical_records',
                    'title': 'Medical Records',
                    'is_required': True
                }
            ]
            
            for doc_data in documents:
                StudentDocument.objects.create(
                    school=school,
                    student=student,
                    document_type=doc_data['document_type'],
                    title=doc_data['title'],
                    is_required=doc_data['is_required'],
                    is_verified=False
                )
            
            # Step 5: Document verification
            for document in student.documents.all():
                document.verify_document()
            
            # Step 6: Admission approval
            student.status = "active"
            student.save()
            
            # Step 7: Grade enrollment
            from students.models import StudentEnrollment
            enrollment = StudentEnrollment.objects.create(
                school=school,
                student=student,
                academic_year=academic_year,
                grade=grade,
                enrollment_date=timezone.now().date(),
                status="active"
            )
            
            # Verify complete admission workflow
            assert student.status == "active"
            assert all(doc.is_verified for doc in student.documents.all())
            assert enrollment.grade == grade
            assert enrollment.is_active()
            
        except Exception as e:
            pytest.fail(f"Admission workflow failed: {str(e)}")


@pytest.mark.e2e
class TestStudentEnrollmentWorkflow:
    """Test student enrollment workflow"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.client = Client()
    
    def test_student_enrollment_to_fee_assignment(self, school, academic_year, admin_user):
        """Test workflow from student enrollment to fee assignment"""
        self.client.force_login(admin_user)
        
        # Step 1: Create parent and student
        parent_user = User.objects.create_user(
            username='enrollment_parent',
            email='<EMAIL>',
            password='testpass123'
        )
        
        from students.models import Parent, Student
        parent = Parent.objects.create(
            school=school,
            user=parent_user,
            father_name="Enrollment Father",
            mother_name="Enrollment Mother",
            phone="************",
            email="<EMAIL>"
        )
        
        student = Student.objects.create(
            school=school,
            student_id="ENR001",
            first_name="Enrollment",
            last_name="Student",
            date_of_birth=datetime(2010, 1, 1).date(),
            gender="female",
            nationality="US",
            parent=parent,
            admission_date=timezone.now().date(),
            status="active"
        )
        
        # Step 2: Create grade
        from academics.models import Grade
        grade = Grade.objects.create(
            school=school,
            academic_year=academic_year,
            name="Grade 1",
            level=1,
            capacity=30,
            is_active=True
        )
        
        # Step 3: Enroll student
        enrollment_data = {
            'school': school.id,
            'student': student.id,
            'academic_year': academic_year.id,
            'grade': grade.id,
            'enrollment_date': timezone.now().date().isoformat(),
            'status': 'active'
        }
        
        response = self.client.post(reverse('students:enrollment_create'), enrollment_data)
        assert response.status_code in [200, 302]
        
        # Verify enrollment
        from students.models import StudentEnrollment
        enrollment = StudentEnrollment.objects.filter(student=student).first()
        assert enrollment is not None
        assert enrollment.grade == grade
        
        # Step 4: Generate fee structure and assign fees
        from finance.models import FeeStructure, StudentFee
        
        fee_structure = FeeStructure.objects.create(
            school=school,
            academic_year=academic_year,
            grade=grade,
            name="Standard Fees",
            tuition_fee=Decimal('1000.00'),
            registration_fee=Decimal('100.00'),
            is_active=True
        )
        
        student_fee = StudentFee.objects.create(
            school=school,
            student=student,
            fee_structure=fee_structure,
            academic_year=academic_year,
            total_amount=fee_structure.get_total_fee(),
            paid_amount=Decimal('0.00'),
            due_date=timezone.now().date() + timedelta(days=30),
            status="pending"
        )
        
        # Verify complete workflow
        assert student.get_current_grade() == grade
        assert student_fee.student == student
        assert student_fee.total_amount == Decimal('1100.00')


@pytest.mark.e2e
class TestAcademicYearWorkflow:
    """Test complete academic year workflow"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.client = Client()
    
    def test_complete_academic_year_setup(self, school, admin_user):
        """Test complete academic year setup workflow"""
        self.client.force_login(admin_user)
        
        # Step 1: Create academic year
        current_year = timezone.now().year
        academic_year_data = {
            'school': school.id,
            'name': f'{current_year}-{current_year + 1}',
            'start_date': f'{current_year}-09-01',
            'end_date': f'{current_year + 1}-06-30',
            'is_current': True
        }
        
        response = self.client.post(reverse('core:academic_year_create'), academic_year_data)
        assert response.status_code in [200, 302]
        
        from core.models import AcademicYear
        academic_year = AcademicYear.objects.filter(name=f'{current_year}-{current_year + 1}').first()
        assert academic_year is not None
        
        # Step 2: Create grades
        from academics.models import Grade
        grades = []
        for level in range(1, 6):  # Grades 1-5
            grade_data = {
                'school': school.id,
                'academic_year': academic_year.id,
                'name': f'Grade {level}',
                'level': level,
                'capacity': 30,
                'is_active': True
            }
            
            response = self.client.post(reverse('academics:grade_create'), grade_data)
            assert response.status_code in [200, 302]
            
            grade = Grade.objects.filter(level=level, academic_year=academic_year).first()
            assert grade is not None
            grades.append(grade)
        
        # Step 3: Create subjects
        from academics.models import Subject
        subjects = []
        subject_names = ['Mathematics', 'English', 'Science', 'Social Studies', 'Art']
        
        for i, name in enumerate(subject_names):
            subject_data = {
                'school': school.id,
                'name': name,
                'code': f'{name[:4].upper()}{i+1}',
                'credits': 3,
                'is_active': True
            }
            
            response = self.client.post(reverse('academics:subject_create'), subject_data)
            assert response.status_code in [200, 302]
            
            subject = Subject.objects.filter(code=f'{name[:4].upper()}{i+1}').first()
            assert subject is not None
            subjects.append(subject)
        
        # Step 4: Create teachers and classes
        from hr.models import Employee, Department
        
        # Create department
        department = Department.objects.create(
            school=school,
            name="Academic",
            code="ACAD"
        )
        
        # Create teacher
        teacher_user = User.objects.create_user(
            username='teacher1',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='Teacher'
        )
        
        teacher = Employee.objects.create(
            school=school,
            user=teacher_user,
            employee_id="TEACH001",
            department=department,
            position="Teacher",
            hire_date=timezone.now().date(),
            employment_type="full_time",
            status="active"
        )
        
        # Create classes
        from academics.models import Class
        for grade in grades[:2]:  # Create classes for first 2 grades
            for subject in subjects[:3]:  # First 3 subjects
                class_data = {
                    'school': school.id,
                    'academic_year': academic_year.id,
                    'grade': grade.id,
                    'subject': subject.id,
                    'teacher': teacher.id,
                    'name': f'{subject.name} - {grade.name}',
                    'section': 'A',
                    'capacity': 25,
                    'is_active': True
                }
                
                response = self.client.post(reverse('academics:class_create'), class_data)
                assert response.status_code in [200, 302]
        
        # Verify complete setup
        assert Grade.objects.filter(academic_year=academic_year).count() == 5
        assert Subject.objects.filter(school=school).count() == 5
        assert Class.objects.filter(academic_year=academic_year).count() == 6  # 2 grades × 3 subjects


@pytest.mark.e2e
class TestFeePaymentWorkflow:
    """Test complete fee payment workflow"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.client = Client()
    
    def test_complete_fee_payment_process(self, school, academic_year, student, admin_user):
        """Test complete fee payment from generation to receipt"""
        self.client.force_login(admin_user)
        
        # Step 1: Create grade and fee structure
        from academics.models import Grade
        from finance.models import FeeStructure, StudentFee, Payment
        
        grade = Grade.objects.create(
            school=school,
            academic_year=academic_year,
            name="Grade 2",
            level=2,
            capacity=30,
            is_active=True
        )
        
        fee_structure = FeeStructure.objects.create(
            school=school,
            academic_year=academic_year,
            grade=grade,
            name="Grade 2 Fees",
            tuition_fee=Decimal('1500.00'),
            registration_fee=Decimal('200.00'),
            activity_fee=Decimal('100.00'),
            is_active=True
        )
        
        # Step 2: Generate student fee
        student_fee_data = {
            'school': school.id,
            'student': student.id,
            'fee_structure': fee_structure.id,
            'academic_year': academic_year.id,
            'total_amount': fee_structure.get_total_fee(),
            'paid_amount': Decimal('0.00'),
            'due_date': (timezone.now().date() + timedelta(days=30)).isoformat(),
            'status': 'pending'
        }
        
        response = self.client.post(reverse('finance:student_fee_create'), student_fee_data)
        assert response.status_code in [200, 302]
        
        student_fee = StudentFee.objects.filter(student=student).first()
        assert student_fee is not None
        assert student_fee.total_amount == Decimal('1800.00')
        
        # Step 3: Process partial payment
        payment_data = {
            'school': school.id,
            'student_fee': student_fee.id,
            'amount': Decimal('800.00'),
            'payment_method': 'cash',
            'payment_date': timezone.now().date().isoformat(),
            'reference_number': 'CASH001',
            'status': 'completed'
        }
        
        response = self.client.post(reverse('finance:payment_create'), payment_data)
        assert response.status_code in [200, 302]
        
        payment = Payment.objects.filter(student_fee=student_fee).first()
        assert payment is not None
        assert payment.amount == Decimal('800.00')
        
        # Update student fee
        student_fee.paid_amount += payment.amount
        student_fee.status = 'partial'
        student_fee.save()
        
        # Step 4: Process remaining payment
        remaining_amount = student_fee.get_outstanding_amount()
        
        payment_data_2 = {
            'school': school.id,
            'student_fee': student_fee.id,
            'amount': remaining_amount,
            'payment_method': 'bank_transfer',
            'payment_date': timezone.now().date().isoformat(),
            'reference_number': 'BANK001',
            'status': 'completed'
        }
        
        response = self.client.post(reverse('finance:payment_create'), payment_data_2)
        assert response.status_code in [200, 302]
        
        # Update student fee
        student_fee.paid_amount += remaining_amount
        student_fee.status = 'paid'
        student_fee.save()
        
        # Verify complete payment
        assert student_fee.get_outstanding_amount() == Decimal('0.00')
        assert student_fee.status == 'paid'
        assert Payment.objects.filter(student_fee=student_fee).count() == 2


@pytest.mark.e2e
class TestTransportationWorkflow:
    """Test complete transportation workflow"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.client = Client()
    
    def test_complete_transportation_setup(self, school, student, admin_user):
        """Test complete transportation setup and assignment"""
        self.client.force_login(admin_user)
        
        # Step 1: Create vehicle
        from transportation.models import Vehicle, Driver, Route, BusStop, StudentTransportation
        
        vehicle_data = {
            'school': school.id,
            'vehicle_number': 'BUS001',
            'license_plate': 'ABC123',
            'make': 'Ford',
            'model': 'Transit',
            'year': 2020,
            'capacity': 50,
            'fuel_type': 'diesel',
            'status': 'active'
        }
        
        response = self.client.post(reverse('transportation:vehicle_create'), vehicle_data)
        assert response.status_code in [200, 302]
        
        vehicle = Vehicle.objects.filter(vehicle_number='BUS001').first()
        assert vehicle is not None
        
        # Step 2: Create driver
        from hr.models import Employee, Department
        
        department = Department.objects.create(
            school=school,
            name="Transportation",
            code="TRANS"
        )
        
        driver_user = User.objects.create_user(
            username='driver1',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='Driver'
        )
        
        employee = Employee.objects.create(
            school=school,
            user=driver_user,
            employee_id="DRV001",
            department=department,
            position="Driver",
            hire_date=timezone.now().date(),
            employment_type="full_time",
            status="active"
        )
        
        driver = Driver.objects.create(
            school=school,
            employee=employee,
            license_number="DL123456",
            license_expiry=timezone.now().date() + timedelta(days=365),
            experience_years=5,
            status="active"
        )
        
        # Step 3: Create route
        route_data = {
            'school': school.id,
            'name': 'Route A',
            'code': 'RTA001',
            'vehicle': vehicle.id,
            'primary_driver': driver.id,
            'max_capacity': 40,
            'status': 'active'
        }
        
        response = self.client.post(reverse('transportation:route_create'), route_data)
        assert response.status_code in [200, 302]
        
        route = Route.objects.filter(code='RTA001').first()
        assert route is not None
        
        # Step 4: Create bus stops
        stops = []
        for i in range(3):
            stop_data = {
                'school': school.id,
                'name': f'Stop {i+1}',
                'address': f'{i+1}00 Street',
                'coordinates': f'40.71{28+i},-74.00{60+i}',
                'status': 'active'
            }
            
            response = self.client.post(reverse('transportation:stop_create'), stop_data)
            assert response.status_code in [200, 302]
            
            stop = BusStop.objects.filter(name=f'Stop {i+1}').first()
            assert stop is not None
            stops.append(stop)
        
        # Step 5: Assign student to transportation
        assignment_data = {
            'school': school.id,
            'student': student.id,
            'route': route.id,
            'pickup_stop': stops[0].id,
            'dropoff_stop': stops[0].id,
            'status': 'active'
        }
        
        response = self.client.post(reverse('transportation:student_assignment_create'), assignment_data)
        assert response.status_code in [200, 302]
        
        assignment = StudentTransportation.objects.filter(student=student).first()
        assert assignment is not None
        assert assignment.route == route
        
        # Verify complete setup
        assert Vehicle.objects.filter(school=school).count() >= 1
        assert Driver.objects.filter(school=school).count() >= 1
        assert Route.objects.filter(school=school).count() >= 1
        assert BusStop.objects.filter(school=school).count() >= 3
        assert StudentTransportation.objects.filter(student=student).count() >= 1


@pytest.mark.e2e
class TestLibraryWorkflow:
    """Test complete library workflow"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.client = Client()
    
    def test_complete_library_operations(self, school, student, admin_user):
        """Test complete library operations from book addition to return"""
        self.client.force_login(admin_user)
        
        # Step 1: Create book categories and authors
        from library.models import Category, Author, Book, BookBorrowing
        
        category = Category.objects.create(
            name="Science Fiction",
            description="Science fiction books"
        )
        
        author = Author.objects.create(
            name="Test Author",
            biography="Test author biography"
        )
        
        # Step 2: Add books to library
        book_data = {
            'school': school.id,
            'title': 'Test Science Book',
            'isbn': '1234567890123',
            'category': category.id,
            'publication_year': 2023,
            'copies_total': 5,
            'copies_available': 5,
            'status': 'available'
        }
        
        response = self.client.post(reverse('library:book_create'), book_data)
        assert response.status_code in [200, 302]
        
        book = Book.objects.filter(isbn='1234567890123').first()
        assert book is not None
        book.authors.add(author)
        
        # Step 3: Student borrows book
        borrowing_data = {
            'school': school.id,
            'book': book.id,
            'student': student.id,
            'borrowed_date': timezone.now().date().isoformat(),
            'due_date': (timezone.now().date() + timedelta(days=14)).isoformat(),
            'status': 'borrowed'
        }
        
        response = self.client.post(reverse('library:borrowing_create'), borrowing_data)
        assert response.status_code in [200, 302]
        
        borrowing = BookBorrowing.objects.filter(student=student, book=book).first()
        assert borrowing is not None
        assert borrowing.status == 'borrowed'
        
        # Update book availability
        book.copies_available -= 1
        book.save()
        
        # Step 4: Return book
        return_data = {
            'returned_date': timezone.now().date().isoformat(),
            'status': 'returned'
        }
        
        response = self.client.post(
            reverse('library:borrowing_return', kwargs={'pk': borrowing.id}),
            return_data
        )
        assert response.status_code in [200, 302]
        
        borrowing.refresh_from_db()
        assert borrowing.status == 'returned'
        
        # Update book availability
        book.copies_available += 1
        book.save()
        
        # Verify complete workflow
        assert book.copies_available == 5  # Back to original
        assert borrowing.returned_date is not None


@pytest.mark.e2e
class TestCompleteSchoolOperations:
    """Test complete school operations workflow"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.client = Client()
    
    def test_daily_school_operations(self, school, academic_year, admin_user):
        """Test a complete day of school operations"""
        self.client.force_login(admin_user)
        
        # Morning: Check attendance
        from academics.models import Grade, Subject, Class, Attendance
        from students.models import Student, Parent, StudentEnrollment
        
        # Create basic academic structure
        grade = Grade.objects.create(
            school=school,
            academic_year=academic_year,
            name="Grade 3",
            level=3,
            capacity=25,
            is_active=True
        )
        
        subject = Subject.objects.create(
            school=school,
            name="Mathematics",
            code="MATH3",
            credits=4,
            is_active=True
        )
        
        # Create teacher
        from hr.models import Employee, Department
        
        department = Department.objects.create(
            school=school,
            name="Academic",
            code="ACAD"
        )
        
        teacher_user = User.objects.create_user(
            username='mathteacher',
            email='<EMAIL>',
            password='testpass123'
        )
        
        teacher = Employee.objects.create(
            school=school,
            user=teacher_user,
            employee_id="TEACH001",
            department=department,
            position="Teacher",
            hire_date=timezone.now().date(),
            employment_type="full_time",
            status="active"
        )
        
        class_instance = Class.objects.create(
            school=school,
            academic_year=academic_year,
            grade=grade,
            subject=subject,
            teacher=teacher,
            name="Math Class",
            section="A",
            capacity=20,
            is_active=True
        )
        
        # Create students
        students = []
        for i in range(5):
            parent_user = User.objects.create_user(
                username=f'parent{i+1}',
                email=f'parent{i+1}@test.com',
                password='testpass123'
            )
            
            parent = Parent.objects.create(
                school=school,
                user=parent_user,
                father_name=f"Father {i+1}",
                mother_name=f"Mother {i+1}",
                phone=f"123-456-789{i+1}",
                email=f"parent{i+1}@test.com"
            )
            
            student = Student.objects.create(
                school=school,
                student_id=f"DAILY{i+1:03d}",
                first_name=f"Student{i+1}",
                last_name="Daily",
                date_of_birth=datetime(2010, 1, 1).date(),
                gender="male" if i % 2 == 0 else "female",
                nationality="US",
                parent=parent,
                admission_date=timezone.now().date(),
                status="active"
            )
            students.append(student)
            
            # Enroll student
            StudentEnrollment.objects.create(
                school=school,
                student=student,
                academic_year=academic_year,
                grade=grade,
                enrollment_date=timezone.now().date(),
                status="active"
            )
        
        # Mark attendance for all students
        for student in students:
            attendance_data = {
                'school': school.id,
                'class_instance': class_instance.id,
                'student': student.id,
                'date': timezone.now().date().isoformat(),
                'status': 'present',
                'marked_by': teacher.id
            }
            
            response = self.client.post(reverse('academics:attendance_create'), attendance_data)
            assert response.status_code in [200, 302]
        
        # Verify attendance was marked
        attendance_count = Attendance.objects.filter(
            class_instance=class_instance,
            date=timezone.now().date()
        ).count()
        assert attendance_count == 5
        
        # Afternoon: Process library operations
        from library.models import Category, Author, Book, BookBorrowing
        
        category = Category.objects.create(
            name="Educational",
            description="Educational books"
        )
        
        author = Author.objects.create(
            name="Education Author",
            biography="Educational content author"
        )
        
        book = Book.objects.create(
            school=school,
            title="Grade 3 Math Workbook",
            isbn="9876543210987",
            category=category,
            publication_year=2023,
            copies_total=10,
            copies_available=10,
            status="available"
        )
        book.authors.add(author)
        
        # Students borrow books
        for i, student in enumerate(students[:3]):  # First 3 students borrow books
            borrowing = BookBorrowing.objects.create(
                school=school,
                book=book,
                student=student,
                borrowed_date=timezone.now().date(),
                due_date=timezone.now().date() + timedelta(days=14),
                status="borrowed"
            )
            
            book.copies_available -= 1
            book.save()
        
        # Evening: Generate daily reports
        daily_stats = {
            'total_students': Student.objects.filter(school=school, status='active').count(),
            'present_today': Attendance.objects.filter(
                class_instance__school=school,
                date=timezone.now().date(),
                status='present'
            ).count(),
            'books_borrowed_today': BookBorrowing.objects.filter(
                school=school,
                borrowed_date=timezone.now().date()
            ).count(),
            'available_books': Book.objects.filter(
                school=school,
                copies_available__gt=0
            ).count()
        }
        
        # Verify daily operations
        assert daily_stats['total_students'] >= 5
        assert daily_stats['present_today'] >= 5
        assert daily_stats['books_borrowed_today'] >= 3
        assert daily_stats['available_books'] >= 1
        
        # Test dashboard access
        response = self.client.get(reverse('core:dashboard'))
        assert response.status_code == 200
        
        # Test various module dashboards
        module_urls = [
            'students:dashboard',
            'academics:dashboard',
            'finance:dashboard',
            'transportation:dashboard',
            'library:dashboard'
        ]
        
        for url_name in module_urls:
            try:
                response = self.client.get(reverse(url_name))
                assert response.status_code in [200, 404]  # 404 if URL doesn't exist
            except:
                pass  # Skip if URL doesn't exist