"""
Unit tests for finance models
"""
import pytest
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal

from finance.models import (
    Account, Transaction, TransactionEntry, FeeStructure, 
    StudentFee, Payment, Budget, BudgetItem
)


@pytest.mark.unit
class TestAccountModel:
    """Test Account model"""
    
    def test_account_creation(self, account):
        """Test account creation"""
        assert account.code == "1000"
        assert account.name == "Cash"
        assert account.account_type == "asset"
        assert account.is_active is True
        assert str(account) == "1000 - Cash"
    
    def test_account_validation(self, school):
        """Test account validation"""
        # Test duplicate code
        Account.objects.create(
            school=school,
            code="2000",
            name="Bank Account",
            account_type="asset",
            is_active=True
        )
        
        with pytest.raises(ValidationError):
            account2 = Account(
                school=school,
                code="2000",  # Duplicate code
                name="Another Account",
                account_type="liability",
                is_active=True
            )
            account2.full_clean()
    
    def test_account_hierarchy(self, school):
        """Test account hierarchy"""
        parent_account = Account.objects.create(
            school=school,
            code="1000",
            name="Assets",
            account_type="asset",
            is_active=True
        )
        
        child_account = Account.objects.create(
            school=school,
            code="1100",
            name="Current Assets",
            account_type="asset",
            parent=parent_account,
            is_active=True
        )
        
        assert child_account.parent == parent_account
        assert child_account in parent_account.children.all()
    
    def test_account_balance(self, account):
        """Test account balance calculation"""
        # Initially zero balance
        assert account.get_balance() == Decimal('0.00')
        
        # Create transaction entries
        transaction = Transaction.objects.create(
            school=account.school,
            reference="TEST001",
            description="Test transaction",
            date=timezone.now().date(),
            total_amount=Decimal('1000.00')
        )
        
        # Debit entry (increases asset balance)
        TransactionEntry.objects.create(
            transaction=transaction,
            account=account,
            debit_amount=Decimal('1000.00'),
            credit_amount=Decimal('0.00'),
            description="Test debit"
        )
        
        assert account.get_balance() == Decimal('1000.00')


@pytest.mark.unit
class TestTransactionModel:
    """Test Transaction model"""
    
    def test_transaction_creation(self, school):
        """Test transaction creation"""
        transaction = Transaction.objects.create(
            school=school,
            reference="TXN001",
            description="Test transaction",
            date=timezone.now().date(),
            total_amount=Decimal('500.00')
        )
        
        assert transaction.reference == "TXN001"
        assert transaction.total_amount == Decimal('500.00')
        assert str(transaction) == "TXN001 - Test transaction"
    
    def test_transaction_validation(self, school):
        """Test transaction validation"""
        # Test negative amount
        with pytest.raises(ValidationError):
            transaction = Transaction(
                school=school,
                reference="TXN002",
                description="Invalid transaction",
                date=timezone.now().date(),
                total_amount=Decimal('-100.00')  # Negative amount
            )
            transaction.full_clean()
    
    def test_double_entry_validation(self, school, account):
        """Test double-entry bookkeeping validation"""
        # Create another account
        account2 = Account.objects.create(
            school=school,
            code="2000",
            name="Revenue",
            account_type="revenue",
            is_active=True
        )
        
        transaction = Transaction.objects.create(
            school=school,
            reference="TXN003",
            description="Double entry test",
            date=timezone.now().date(),
            total_amount=Decimal('1000.00')
        )
        
        # Create balanced entries
        TransactionEntry.objects.create(
            transaction=transaction,
            account=account,
            debit_amount=Decimal('1000.00'),
            credit_amount=Decimal('0.00'),
            description="Debit entry"
        )
        
        TransactionEntry.objects.create(
            transaction=transaction,
            account=account2,
            debit_amount=Decimal('0.00'),
            credit_amount=Decimal('1000.00'),
            description="Credit entry"
        )
        
        assert transaction.is_balanced() is True
        assert transaction.get_total_debits() == Decimal('1000.00')
        assert transaction.get_total_credits() == Decimal('1000.00')


@pytest.mark.unit
class TestFeeStructureModel:
    """Test FeeStructure model"""
    
    def test_fee_structure_creation(self, fee_structure):
        """Test fee structure creation"""
        assert fee_structure.name == "Standard Fees"
        assert fee_structure.tuition_fee == Decimal('1000.00')
        assert fee_structure.registration_fee == Decimal('100.00')
        assert fee_structure.activity_fee == Decimal('50.00')
        assert fee_structure.is_active is True
        assert str(fee_structure) == "Standard Fees - Grade 1"
    
    def test_fee_calculation(self, fee_structure):
        """Test fee calculation"""
        total_fee = fee_structure.get_total_fee()
        expected_total = Decimal('1000.00') + Decimal('100.00') + Decimal('50.00')
        
        assert total_fee == expected_total
    
    def test_fee_breakdown(self, fee_structure):
        """Test fee breakdown"""
        breakdown = fee_structure.get_fee_breakdown()
        
        assert 'tuition_fee' in breakdown
        assert 'registration_fee' in breakdown
        assert 'activity_fee' in breakdown
        assert breakdown['tuition_fee'] == Decimal('1000.00')
    
    def test_fee_validation(self, school, grade, academic_year):
        """Test fee structure validation"""
        # Test negative fee
        with pytest.raises(ValidationError):
            fee_structure = FeeStructure(
                school=school,
                academic_year=academic_year,
                grade=grade,
                name="Invalid Fees",
                tuition_fee=Decimal('-100.00'),  # Negative fee
                registration_fee=Decimal('100.00'),
                is_active=True
            )
            fee_structure.full_clean()


@pytest.mark.unit
class TestStudentFeeModel:
    """Test StudentFee model"""
    
    def test_student_fee_creation(self, student, fee_structure):
        """Test student fee creation"""
        student_fee = StudentFee.objects.create(
            school=student.school,
            student=student,
            fee_structure=fee_structure,
            academic_year=fee_structure.academic_year,
            total_amount=fee_structure.get_total_fee(),
            paid_amount=Decimal('0.00'),
            due_date=timezone.now().date() + timedelta(days=30),
            status="pending"
        )
        
        assert student_fee.student == student
        assert student_fee.fee_structure == fee_structure
        assert student_fee.status == "pending"
        assert str(student_fee) == f"Test Student - Standard Fees"
    
    def test_fee_payment_tracking(self, student, fee_structure):
        """Test fee payment tracking"""
        student_fee = StudentFee.objects.create(
            school=student.school,
            student=student,
            fee_structure=fee_structure,
            academic_year=fee_structure.academic_year,
            total_amount=Decimal('1150.00'),
            paid_amount=Decimal('500.00'),
            due_date=timezone.now().date() + timedelta(days=30),
            status="partial"
        )
        
        assert student_fee.get_outstanding_amount() == Decimal('650.00')
        assert student_fee.get_payment_percentage() == 43.48  # 500/1150 * 100
        assert student_fee.is_overdue() is False  # Due date is in future
    
    def test_overdue_calculation(self, student, fee_structure):
        """Test overdue fee calculation"""
        student_fee = StudentFee.objects.create(
            school=student.school,
            student=student,
            fee_structure=fee_structure,
            academic_year=fee_structure.academic_year,
            total_amount=Decimal('1150.00'),
            paid_amount=Decimal('0.00'),
            due_date=timezone.now().date() - timedelta(days=10),  # Past due date
            status="pending"
        )
        
        assert student_fee.is_overdue() is True
        assert student_fee.get_days_overdue() == 10


@pytest.mark.unit
class TestPaymentModel:
    """Test Payment model"""
    
    def test_payment_creation(self, student, fee_structure):
        """Test payment creation"""
        student_fee = StudentFee.objects.create(
            school=student.school,
            student=student,
            fee_structure=fee_structure,
            academic_year=fee_structure.academic_year,
            total_amount=Decimal('1150.00'),
            paid_amount=Decimal('0.00'),
            due_date=timezone.now().date() + timedelta(days=30),
            status="pending"
        )
        
        payment = Payment.objects.create(
            school=student.school,
            student_fee=student_fee,
            amount=Decimal('500.00'),
            payment_method="cash",
            payment_date=timezone.now().date(),
            reference_number="PAY001",
            status="completed"
        )
        
        assert payment.amount == Decimal('500.00')
        assert payment.payment_method == "cash"
        assert payment.status == "completed"
        assert str(payment) == "PAY001 - $500.00"
    
    def test_payment_validation(self, student, fee_structure):
        """Test payment validation"""
        student_fee = StudentFee.objects.create(
            school=student.school,
            student=student,
            fee_structure=fee_structure,
            academic_year=fee_structure.academic_year,
            total_amount=Decimal('1150.00'),
            paid_amount=Decimal('0.00'),
            due_date=timezone.now().date() + timedelta(days=30),
            status="pending"
        )
        
        # Test payment amount exceeding outstanding amount
        with pytest.raises(ValidationError):
            payment = Payment(
                school=student.school,
                student_fee=student_fee,
                amount=Decimal('2000.00'),  # Exceeds total fee
                payment_method="cash",
                payment_date=timezone.now().date(),
                reference_number="PAY002",
                status="completed"
            )
            payment.full_clean()
    
    def test_payment_processing(self, student, fee_structure):
        """Test payment processing"""
        student_fee = StudentFee.objects.create(
            school=student.school,
            student=student,
            fee_structure=fee_structure,
            academic_year=fee_structure.academic_year,
            total_amount=Decimal('1150.00'),
            paid_amount=Decimal('0.00'),
            due_date=timezone.now().date() + timedelta(days=30),
            status="pending"
        )
        
        payment = Payment.objects.create(
            school=student.school,
            student_fee=student_fee,
            amount=Decimal('1150.00'),  # Full payment
            payment_method="bank_transfer",
            payment_date=timezone.now().date(),
            reference_number="PAY003",
            status="completed"
        )
        
        # Update student fee
        student_fee.paid_amount += payment.amount
        student_fee.status = "paid"
        student_fee.save()
        
        assert student_fee.get_outstanding_amount() == Decimal('0.00')
        assert student_fee.status == "paid"


@pytest.mark.unit
class TestBudgetModel:
    """Test Budget model"""
    
    def test_budget_creation(self, school, academic_year):
        """Test budget creation"""
        budget = Budget.objects.create(
            school=school,
            academic_year=academic_year,
            name="Annual Budget",
            total_budget=Decimal('100000.00'),
            status="draft"
        )
        
        assert budget.name == "Annual Budget"
        assert budget.total_budget == Decimal('100000.00')
        assert budget.status == "draft"
        assert str(budget) == f"Annual Budget - {academic_year.name}"
    
    def test_budget_items(self, school, academic_year):
        """Test budget items"""
        budget = Budget.objects.create(
            school=school,
            academic_year=academic_year,
            name="Test Budget",
            total_budget=Decimal('50000.00'),
            status="approved"
        )
        
        # Create budget items
        BudgetItem.objects.create(
            budget=budget,
            category="salaries",
            description="Teacher Salaries",
            allocated_amount=Decimal('30000.00'),
            spent_amount=Decimal('15000.00')
        )
        
        BudgetItem.objects.create(
            budget=budget,
            category="supplies",
            description="Office Supplies",
            allocated_amount=Decimal('5000.00'),
            spent_amount=Decimal('2000.00')
        )
        
        assert budget.get_total_allocated() == Decimal('35000.00')
        assert budget.get_total_spent() == Decimal('17000.00')
        assert budget.get_remaining_budget() == Decimal('33000.00')
    
    def test_budget_utilization(self, school, academic_year):
        """Test budget utilization calculation"""
        budget = Budget.objects.create(
            school=school,
            academic_year=academic_year,
            name="Utilization Test",
            total_budget=Decimal('10000.00'),
            status="approved"
        )
        
        BudgetItem.objects.create(
            budget=budget,
            category="equipment",
            description="Computers",
            allocated_amount=Decimal('8000.00'),
            spent_amount=Decimal('6000.00')
        )
        
        utilization = budget.get_utilization_percentage()
        assert utilization == 60.0  # 6000/10000 * 100