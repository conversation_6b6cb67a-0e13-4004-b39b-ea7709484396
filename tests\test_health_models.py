"""
Unit tests for health models
"""
import pytest
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal

from health.models import HealthProfile, MedicalIncident, Medication, VaccinationRecord


@pytest.mark.unit
class TestHealthProfileModel:
    """Test HealthProfile model"""
    
    def test_health_profile_creation(self, health_profile):
        """Test health profile creation"""
        assert health_profile.blood_type == "O+"
        assert health_profile.height == 150.0
        assert health_profile.weight == 45.0
        assert health_profile.allergies == "None"
        assert health_profile.emergency_contact_name == "Emergency Contact"
        assert str(health_profile) == "Test Student - Health Profile"
    
    def test_health_profile_validation(self, school, student):
        """Test health profile validation"""
        # Test invalid blood type
        with pytest.raises(ValidationError):
            profile = HealthProfile(
                school=school,
                student=student,
                blood_type="INVALID",  # Invalid blood type
                height=150.0,
                weight=45.0
            )
            profile.full_clean()
        
        # Test negative height
        with pytest.raises(ValidationError):
            profile = HealthProfile(
                school=school,
                student=student,
                blood_type="A+",
                height=-10.0,  # Negative height
                weight=45.0
            )
            profile.full_clean()
    
    def test_health_profile_calculations(self, health_profile):
        """Test health profile calculations"""
        bmi = health_profile.calculate_bmi()
        expected_bmi = 45.0 / ((150.0 / 100) ** 2)  # weight / (height_in_meters^2)
        
        assert abs(bmi - expected_bmi) < 0.01  # Allow small floating point differences
        assert health_profile.get_bmi_category() in ["Underweight", "Normal", "Overweight", "Obese"]
    
    def test_health_profile_methods(self, health_profile):
        """Test health profile methods"""
        assert health_profile.has_allergies() is False  # "None" means no allergies
        assert health_profile.has_medications() is False  # "None" means no medications
        
        # Update with actual allergies
        health_profile.allergies = "Peanuts, Shellfish"
        health_profile.medications = "Inhaler"
        health_profile.save()
        
        assert health_profile.has_allergies() is True
        assert health_profile.has_medications() is True


@pytest.mark.unit
class TestMedicalIncidentModel:
    """Test MedicalIncident model"""
    
    def test_incident_creation(self, school, student, admin_user):
        """Test medical incident creation"""
        incident = MedicalIncident.objects.create(
            school=school,
            student=student,
            incident_type="injury",
            description="Minor cut on finger",
            date_occurred=timezone.now().date(),
            time_occurred="10:30",
            location="Playground",
            severity="minor",
            treatment_given="First aid applied",
            reported_by=admin_user.employee,
            status="resolved"
        )
        
        assert incident.student == student
        assert incident.incident_type == "injury"
        assert incident.severity == "minor"
        assert incident.status == "resolved"
        assert str(incident) == f"Test Student - injury ({incident.date_occurred})"
    
    def test_incident_validation(self, school, student, admin_user):
        """Test medical incident validation"""
        # Test invalid severity level
        with pytest.raises(ValidationError):
            incident = MedicalIncident(
                school=school,
                student=student,
                incident_type="illness",
                description="Headache",
                date_occurred=timezone.now().date(),
                severity="invalid_severity",  # Invalid severity
                reported_by=admin_user.employee,
                status="active"
            )
            incident.full_clean()
    
    def test_incident_methods(self, school, student, admin_user):
        """Test medical incident methods"""
        incident = MedicalIncident.objects.create(
            school=school,
            student=student,
            incident_type="illness",
            description="Fever",
            date_occurred=timezone.now().date(),
            time_occurred="14:15",
            severity="moderate",
            reported_by=admin_user.employee,
            status="active"
        )
        
        assert incident.is_active() is True
        assert incident.is_severe() is False  # Only "severe" severity is considered severe
        assert incident.requires_parent_notification() is True  # Moderate and severe require notification
    
    def test_incident_follow_up(self, school, student, admin_user):
        """Test incident follow-up tracking"""
        incident = MedicalIncident.objects.create(
            school=school,
            student=student,
            incident_type="injury",
            description="Sprained ankle",
            date_occurred=timezone.now().date(),
            severity="moderate",
            treatment_given="Ice pack applied, rest recommended",
            follow_up_required=True,
            follow_up_date=timezone.now().date() + timedelta(days=3),
            reported_by=admin_user.employee,
            status="active"
        )
        
        assert incident.requires_follow_up() is True
        assert incident.is_follow_up_due() is False  # Follow-up is in future
        
        # Set follow-up date in past
        incident.follow_up_date = timezone.now().date() - timedelta(days=1)
        incident.save()
        
        assert incident.is_follow_up_overdue() is True


@pytest.mark.unit
class TestMedicationModel:
    """Test Medication model"""
    
    def test_medication_creation(self, school, student):
        """Test medication creation"""
        medication = Medication.objects.create(
            school=school,
            student=student,
            medication_name="Inhaler",
            dosage="2 puffs",
            frequency="As needed",
            prescribed_by="Dr. Smith",
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timedelta(days=30),
            instructions="Use when experiencing breathing difficulties",
            status="active"
        )
        
        assert medication.student == student
        assert medication.medication_name == "Inhaler"
        assert medication.dosage == "2 puffs"
        assert medication.status == "active"
        assert str(medication) == "Test Student - Inhaler"
    
    def test_medication_validation(self, school, student):
        """Test medication validation"""
        # Test end date before start date
        with pytest.raises(ValidationError):
            medication = Medication(
                school=school,
                student=student,
                medication_name="Test Med",
                dosage="1 tablet",
                frequency="Daily",
                start_date=timezone.now().date(),
                end_date=timezone.now().date() - timedelta(days=1),  # Before start date
                status="active"
            )
            medication.full_clean()
    
    def test_medication_methods(self, school, student):
        """Test medication methods"""
        medication = Medication.objects.create(
            school=school,
            student=student,
            medication_name="Antibiotic",
            dosage="250mg",
            frequency="Twice daily",
            start_date=timezone.now().date() - timedelta(days=5),
            end_date=timezone.now().date() + timedelta(days=5),
            status="active"
        )
        
        assert medication.is_active() is True
        assert medication.is_current() is True  # Current date is within start/end dates
        assert medication.get_duration_days() == 10
    
    def test_medication_expiry(self, school, student):
        """Test medication expiry tracking"""
        # Create expired medication
        medication = Medication.objects.create(
            school=school,
            student=student,
            medication_name="Expired Med",
            dosage="1 tablet",
            frequency="Daily",
            start_date=timezone.now().date() - timedelta(days=20),
            end_date=timezone.now().date() - timedelta(days=5),  # Expired
            status="active"
        )
        
        assert medication.is_expired() is True
        assert medication.get_days_until_expiry() < 0


@pytest.mark.unit
class TestVaccinationRecordModel:
    """Test VaccinationRecord model"""
    
    def test_vaccination_creation(self, school, student):
        """Test vaccination record creation"""
        vaccination = VaccinationRecord.objects.create(
            school=school,
            student=student,
            vaccine_name="COVID-19",
            vaccine_type="mRNA",
            date_administered=timezone.now().date(),
            administered_by="School Nurse",
            batch_number="ABC123",
            next_dose_due=timezone.now().date() + timedelta(days=21),
            status="completed"
        )
        
        assert vaccination.student == student
        assert vaccination.vaccine_name == "COVID-19"
        assert vaccination.vaccine_type == "mRNA"
        assert vaccination.status == "completed"
        assert str(vaccination) == "Test Student - COVID-19"
    
    def test_vaccination_validation(self, school, student):
        """Test vaccination validation"""
        # Test future administration date
        with pytest.raises(ValidationError):
            vaccination = VaccinationRecord(
                school=school,
                student=student,
                vaccine_name="Future Vaccine",
                date_administered=timezone.now().date() + timedelta(days=1),  # Future date
                status="completed"
            )
            vaccination.full_clean()
    
    def test_vaccination_methods(self, school, student):
        """Test vaccination methods"""
        vaccination = VaccinationRecord.objects.create(
            school=school,
            student=student,
            vaccine_name="Hepatitis B",
            date_administered=timezone.now().date() - timedelta(days=30),
            next_dose_due=timezone.now().date() + timedelta(days=150),
            status="completed"
        )
        
        assert vaccination.is_completed() is True
        assert vaccination.is_next_dose_due() is False  # Next dose is in future
        assert vaccination.get_days_until_next_dose() == 150
    
    def test_vaccination_series_tracking(self, school, student):
        """Test vaccination series tracking"""
        # Create first dose
        dose1 = VaccinationRecord.objects.create(
            school=school,
            student=student,
            vaccine_name="Hepatitis B",
            dose_number=1,
            date_administered=timezone.now().date() - timedelta(days=60),
            next_dose_due=timezone.now().date() - timedelta(days=30),
            status="completed"
        )
        
        # Create second dose
        dose2 = VaccinationRecord.objects.create(
            school=school,
            student=student,
            vaccine_name="Hepatitis B",
            dose_number=2,
            date_administered=timezone.now().date() - timedelta(days=30),
            next_dose_due=timezone.now().date() + timedelta(days=150),
            status="completed"
        )
        
        # Get vaccination series
        series = student.get_vaccination_series("Hepatitis B")
        assert len(series) == 2
        assert dose1 in series
        assert dose2 in series


@pytest.mark.unit
class TestHealthAnalytics:
    """Test health analytics and reporting"""
    
    def test_student_health_summary(self, health_profile, school, student, admin_user):
        """Test student health summary"""
        # Create some health records
        MedicalIncident.objects.create(
            school=school,
            student=student,
            incident_type="illness",
            description="Common cold",
            date_occurred=timezone.now().date() - timedelta(days=30),
            severity="minor",
            reported_by=admin_user.employee,
            status="resolved"
        )
        
        Medication.objects.create(
            school=school,
            student=student,
            medication_name="Vitamins",
            dosage="1 tablet",
            frequency="Daily",
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timedelta(days=30),
            status="active"
        )
        
        summary = student.get_health_summary()
        
        assert 'health_profile' in summary
        assert 'recent_incidents' in summary
        assert 'active_medications' in summary
        assert 'vaccination_status' in summary
    
    def test_health_alerts(self, school, student, admin_user):
        """Test health alerts generation"""
        # Create incident requiring follow-up
        incident = MedicalIncident.objects.create(
            school=school,
            student=student,
            incident_type="injury",
            description="Head bump",
            date_occurred=timezone.now().date(),
            severity="moderate",
            follow_up_required=True,
            follow_up_date=timezone.now().date() + timedelta(days=1),
            reported_by=admin_user.employee,
            status="active"
        )
        
        # Create medication expiring soon
        medication = Medication.objects.create(
            school=school,
            student=student,
            medication_name="Expiring Med",
            dosage="1 tablet",
            frequency="Daily",
            start_date=timezone.now().date() - timedelta(days=25),
            end_date=timezone.now().date() + timedelta(days=5),  # Expiring soon
            status="active"
        )
        
        alerts = student.get_health_alerts()
        
        assert len(alerts) >= 1  # Should have at least medication expiry alert
        assert any("medication" in alert['type'] for alert in alerts)