"""
Unit tests for HR models
"""
import pytest
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal

from hr.models import Employee, Department, Attendance, Leave, Payroll, PerformanceReview


@pytest.mark.unit
class TestDepartmentModel:
    """Test Department model"""
    
    def test_department_creation(self, school):
        """Test department creation"""
        department = Department.objects.create(
            school=school,
            name="Human Resources",
            code="HR",
            description="HR Department"
        )
        
        assert department.name == "Human Resources"
        assert department.code == "HR"
        assert str(department) == "Human Resources (HR)"
    
    def test_department_validation(self, school):
        """Test department validation"""
        # Create first department
        Department.objects.create(
            school=school,
            name="IT Department",
            code="IT"
        )
        
        # Try to create duplicate code
        with pytest.raises(ValidationError):
            department2 = Department(
                school=school,
                name="Information Technology",
                code="IT"  # Duplicate code
            )
            department2.full_clean()


@pytest.mark.unit
class TestEmployeeModel:
    """Test Employee model"""
    
    def test_employee_creation(self, admin_user):
        """Test employee creation"""
        employee = admin_user.employee
        
        assert employee.employee_id == "EMP001"
        assert employee.position == "Administrator"
        assert employee.status == "active"
        assert str(employee) == f"{admin_user.get_full_name()} (EMP001)"
    
    def test_employee_validation(self, school, admin_user):
        """Test employee validation"""
        department = admin_user.employee.department
        
        # Test duplicate employee ID
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        user2 = User.objects.create_user(
            username="employee2",
            email="<EMAIL>",
            password="testpass123"
        )
        
        with pytest.raises(ValidationError):
            employee2 = Employee(
                school=school,
                user=user2,
                employee_id="EMP001",  # Duplicate ID
                department=department,
                position="Staff",
                hire_date=timezone.now().date(),
                employment_type="full_time",
                status="active"
            )
            employee2.full_clean()
    
    def test_employee_methods(self, admin_user):
        """Test employee methods"""
        employee = admin_user.employee
        
        assert employee.is_active() is True
        assert employee.get_years_of_service() >= 0
        assert employee.get_full_name() == admin_user.get_full_name()
    
    def test_employee_salary_info(self, admin_user):
        """Test employee salary information"""
        employee = admin_user.employee
        employee.salary = Decimal('5000.00')
        employee.save()
        
        assert employee.salary == Decimal('5000.00')
        assert employee.get_monthly_salary() == Decimal('5000.00')


@pytest.mark.unit
class TestAttendanceModel:
    """Test HR Attendance model"""
    
    def test_attendance_creation(self, admin_user):
        """Test attendance creation"""
        employee = admin_user.employee
        
        attendance = Attendance.objects.create(
            school=employee.school,
            employee=employee,
            date=timezone.now().date(),
            check_in_time="09:00",
            check_out_time="17:00",
            status="present",
            hours_worked=8.0
        )
        
        assert attendance.employee == employee
        assert attendance.status == "present"
        assert attendance.hours_worked == 8.0
        assert str(attendance) == f"{employee.get_full_name()} - {attendance.date}"
    
    def test_attendance_validation(self, admin_user):
        """Test attendance validation"""
        employee = admin_user.employee
        
        # Create first attendance record
        Attendance.objects.create(
            school=employee.school,
            employee=employee,
            date=timezone.now().date(),
            check_in_time="09:00",
            check_out_time="17:00",
            status="present",
            hours_worked=8.0
        )
        
        # Try to create duplicate attendance for same date
        with pytest.raises(ValidationError):
            attendance2 = Attendance(
                school=employee.school,
                employee=employee,
                date=timezone.now().date(),  # Same date
                check_in_time="08:30",
                check_out_time="16:30",
                status="present",
                hours_worked=8.0
            )
            attendance2.full_clean()
    
    def test_attendance_calculations(self, admin_user):
        """Test attendance calculations"""
        employee = admin_user.employee
        
        attendance = Attendance.objects.create(
            school=employee.school,
            employee=employee,
            date=timezone.now().date(),
            check_in_time="09:15",  # 15 minutes late
            check_out_time="17:30",  # 30 minutes overtime
            status="present"
        )
        
        assert attendance.calculate_hours_worked() == 8.25  # 8 hours 15 minutes
        assert attendance.is_late() is True
        assert attendance.get_overtime_hours() == 0.5  # 30 minutes overtime


@pytest.mark.unit
class TestLeaveModel:
    """Test Leave model"""
    
    def test_leave_creation(self, admin_user):
        """Test leave creation"""
        employee = admin_user.employee
        
        leave = Leave.objects.create(
            school=employee.school,
            employee=employee,
            leave_type="annual",
            start_date=timezone.now().date() + timedelta(days=7),
            end_date=timezone.now().date() + timedelta(days=10),
            reason="Vacation",
            status="pending"
        )
        
        assert leave.employee == employee
        assert leave.leave_type == "annual"
        assert leave.status == "pending"
        assert str(leave) == f"{employee.get_full_name()} - annual ({leave.start_date})"
    
    def test_leave_validation(self, admin_user):
        """Test leave validation"""
        employee = admin_user.employee
        
        # Test end date before start date
        with pytest.raises(ValidationError):
            leave = Leave(
                school=employee.school,
                employee=employee,
                leave_type="sick",
                start_date=timezone.now().date() + timedelta(days=10),
                end_date=timezone.now().date() + timedelta(days=5),  # Before start date
                reason="Illness",
                status="pending"
            )
            leave.full_clean()
    
    def test_leave_calculations(self, admin_user):
        """Test leave calculations"""
        employee = admin_user.employee
        
        leave = Leave.objects.create(
            school=employee.school,
            employee=employee,
            leave_type="annual",
            start_date=timezone.now().date() + timedelta(days=7),
            end_date=timezone.now().date() + timedelta(days=11),  # 5 days
            reason="Vacation",
            status="approved"
        )
        
        assert leave.get_duration_days() == 5
        assert leave.is_approved() is True
        assert leave.is_upcoming() is True


@pytest.mark.unit
class TestPayrollModel:
    """Test Payroll model"""
    
    def test_payroll_creation(self, admin_user):
        """Test payroll creation"""
        employee = admin_user.employee
        employee.salary = Decimal('5000.00')
        employee.save()
        
        payroll = Payroll.objects.create(
            school=employee.school,
            employee=employee,
            month=timezone.now().date().replace(day=1),
            basic_salary=Decimal('5000.00'),
            allowances=Decimal('500.00'),
            deductions=Decimal('200.00'),
            net_salary=Decimal('5300.00'),
            status="processed"
        )
        
        assert payroll.employee == employee
        assert payroll.basic_salary == Decimal('5000.00')
        assert payroll.net_salary == Decimal('5300.00')
        assert str(payroll) == f"{employee.get_full_name()} - {payroll.month.strftime('%B %Y')}"
    
    def test_payroll_calculations(self, admin_user):
        """Test payroll calculations"""
        employee = admin_user.employee
        
        payroll = Payroll.objects.create(
            school=employee.school,
            employee=employee,
            month=timezone.now().date().replace(day=1),
            basic_salary=Decimal('5000.00'),
            allowances=Decimal('1000.00'),
            deductions=Decimal('500.00'),
            overtime_amount=Decimal('200.00'),
            status="processed"
        )
        
        gross_salary = payroll.calculate_gross_salary()
        net_salary = payroll.calculate_net_salary()
        
        assert gross_salary == Decimal('6200.00')  # 5000 + 1000 + 200
        assert net_salary == Decimal('5700.00')    # 6200 - 500
    
    def test_payroll_validation(self, admin_user):
        """Test payroll validation"""
        employee = admin_user.employee
        
        # Create first payroll record
        Payroll.objects.create(
            school=employee.school,
            employee=employee,
            month=timezone.now().date().replace(day=1),
            basic_salary=Decimal('5000.00'),
            net_salary=Decimal('5000.00'),
            status="processed"
        )
        
        # Try to create duplicate payroll for same month
        with pytest.raises(ValidationError):
            payroll2 = Payroll(
                school=employee.school,
                employee=employee,
                month=timezone.now().date().replace(day=1),  # Same month
                basic_salary=Decimal('5000.00'),
                net_salary=Decimal('5000.00'),
                status="processed"
            )
            payroll2.full_clean()


@pytest.mark.unit
class TestPerformanceReviewModel:
    """Test PerformanceReview model"""
    
    def test_performance_review_creation(self, admin_user, teacher_user):
        """Test performance review creation"""
        employee = teacher_user.employee
        reviewer = admin_user.employee
        
        review = PerformanceReview.objects.create(
            school=employee.school,
            employee=employee,
            reviewer=reviewer,
            review_period_start=timezone.now().date() - timedelta(days=90),
            review_period_end=timezone.now().date(),
            overall_rating=4.5,
            goals_achievement=4.0,
            communication_skills=4.5,
            technical_skills=5.0,
            comments="Excellent performance",
            status="completed"
        )
        
        assert review.employee == employee
        assert review.reviewer == reviewer
        assert review.overall_rating == 4.5
        assert review.status == "completed"
        assert str(review) == f"{employee.get_full_name()} - {review.review_period_end.year}"
    
    def test_performance_review_validation(self, admin_user, teacher_user):
        """Test performance review validation"""
        employee = teacher_user.employee
        reviewer = admin_user.employee
        
        # Test invalid rating (> 5.0)
        with pytest.raises(ValidationError):
            review = PerformanceReview(
                school=employee.school,
                employee=employee,
                reviewer=reviewer,
                review_period_start=timezone.now().date() - timedelta(days=90),
                review_period_end=timezone.now().date(),
                overall_rating=6.0,  # Invalid rating
                status="draft"
            )
            review.full_clean()
    
    def test_performance_review_calculations(self, admin_user, teacher_user):
        """Test performance review calculations"""
        employee = teacher_user.employee
        reviewer = admin_user.employee
        
        review = PerformanceReview.objects.create(
            school=employee.school,
            employee=employee,
            reviewer=reviewer,
            review_period_start=timezone.now().date() - timedelta(days=90),
            review_period_end=timezone.now().date(),
            overall_rating=4.2,
            goals_achievement=4.0,
            communication_skills=4.5,
            technical_skills=4.0,
            status="completed"
        )
        
        average_rating = review.calculate_average_rating()
        assert average_rating == 4.125  # (4.0 + 4.5 + 4.0) / 3
        
        assert review.get_performance_level() == "Good"  # 4.0-4.5 range
        assert review.is_completed() is True