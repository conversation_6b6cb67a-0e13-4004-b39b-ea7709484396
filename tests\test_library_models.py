"""
Unit tests for library models
"""
import pytest
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal

from library.models import Book, Author, Category, BookBorrowing, BookReservation


@pytest.mark.unit
class TestCategoryModel:
    """Test Category model"""
    
    def test_category_creation(self, book_category):
        """Test category creation"""
        assert book_category.name == "Fiction"
        assert book_category.description == "Fiction books"
        assert str(book_category) == "Fiction"
    
    def test_category_validation(self):
        """Test category validation"""
        # Test duplicate name
        Category.objects.create(name="Science", description="Science books")
        
        with pytest.raises(ValidationError):
            category2 = Category(
                name="Science",  # Duplicate name
                description="Another science category"
            )
            category2.full_clean()


@pytest.mark.unit
class TestAuthorModel:
    """Test Author model"""
    
    def test_author_creation(self, author):
        """Test author creation"""
        assert author.name == "Test Author"
        assert author.biography == "Test author biography"
        assert str(author) == "Test Author"
    
    def test_author_methods(self, author, book):
        """Test author methods"""
        assert author.get_books_count() == 1  # One book associated
        books = author.get_books()
        assert book in books


@pytest.mark.unit
class TestBookModel:
    """Test Book model"""
    
    def test_book_creation(self, book):
        """Test book creation"""
        assert book.title == "Test Book"
        assert book.isbn == "1234567890123"
        assert book.publication_year == 2020
        assert book.copies_total == 5
        assert book.copies_available == 5
        assert book.status == "available"
        assert str(book) == "Test Book"
    
    def test_book_validation(self, school, book_category, author):
        """Test book validation"""
        # Test duplicate ISBN
        Book.objects.create(
            school=school,
            title="Another Book",
            isbn="9876543210987",
            category=book_category,
            publication_year=2021,
            copies_total=3,
            copies_available=3,
            status="available"
        )
        
        with pytest.raises(ValidationError):
            book2 = Book(
                school=school,
                title="Duplicate ISBN Book",
                isbn="9876543210987",  # Duplicate ISBN
                category=book_category,
                publication_year=2021,
                copies_total=2,
                copies_available=2,
                status="available"
            )
            book2.full_clean()
    
    def test_book_availability(self, book):
        """Test book availability methods"""
        assert book.is_available() is True
        assert book.get_available_copies() == 5
        
        # Reduce available copies
        book.copies_available = 0
        book.save()
        
        assert book.is_available() is False
        assert book.get_available_copies() == 0
    
    def test_book_borrowing_status(self, book, student):
        """Test book borrowing status"""
        # Create borrowing record
        borrowing = BookBorrowing.objects.create(
            school=book.school,
            book=book,
            student=student,
            borrowed_date=timezone.now().date(),
            due_date=timezone.now().date() + timedelta(days=14),
            status="borrowed"
        )
        
        # Update book availability
        book.copies_available -= 1
        book.save()
        
        assert book.get_available_copies() == 4
        assert book.get_borrowed_copies() == 1
        assert book.is_borrowed_by_student(student) is True


@pytest.mark.unit
class TestBookBorrowingModel:
    """Test BookBorrowing model"""
    
    def test_borrowing_creation(self, book, student):
        """Test borrowing creation"""
        borrowing = BookBorrowing.objects.create(
            school=book.school,
            book=book,
            student=student,
            borrowed_date=timezone.now().date(),
            due_date=timezone.now().date() + timedelta(days=14),
            status="borrowed"
        )
        
        assert borrowing.book == book
        assert borrowing.student == student
        assert borrowing.status == "borrowed"
        assert str(borrowing) == f"Test Student - Test Book"
    
    def test_borrowing_validation(self, book, student):
        """Test borrowing validation"""
        # Create first borrowing
        BookBorrowing.objects.create(
            school=book.school,
            book=book,
            student=student,
            borrowed_date=timezone.now().date(),
            due_date=timezone.now().date() + timedelta(days=14),
            status="borrowed"
        )
        
        # Try to create duplicate active borrowing
        with pytest.raises(ValidationError):
            borrowing2 = BookBorrowing(
                school=book.school,
                book=book,
                student=student,
                borrowed_date=timezone.now().date(),
                due_date=timezone.now().date() + timedelta(days=14),
                status="borrowed"  # Duplicate active borrowing
            )
            borrowing2.full_clean()
    
    def test_borrowing_calculations(self, book, student):
        """Test borrowing calculations"""
        # Create overdue borrowing
        borrowing = BookBorrowing.objects.create(
            school=book.school,
            book=book,
            student=student,
            borrowed_date=timezone.now().date() - timedelta(days=20),
            due_date=timezone.now().date() - timedelta(days=6),  # 6 days overdue
            status="borrowed"
        )
        
        assert borrowing.is_overdue() is True
        assert borrowing.get_days_overdue() == 6
        assert borrowing.calculate_fine() > 0  # Should have fine
    
    def test_borrowing_return(self, book, student):
        """Test borrowing return process"""
        borrowing = BookBorrowing.objects.create(
            school=book.school,
            book=book,
            student=student,
            borrowed_date=timezone.now().date() - timedelta(days=10),
            due_date=timezone.now().date() + timedelta(days=4),
            status="borrowed"
        )
        
        # Return book
        borrowing.return_book()
        
        assert borrowing.status == "returned"
        assert borrowing.returned_date == timezone.now().date()


@pytest.mark.unit
class TestBookReservationModel:
    """Test BookReservation model"""
    
    def test_reservation_creation(self, book, student):
        """Test reservation creation"""
        reservation = BookReservation.objects.create(
            school=book.school,
            book=book,
            student=student,
            reservation_date=timezone.now().date(),
            expiry_date=timezone.now().date() + timedelta(days=3),
            status="active"
        )
        
        assert reservation.book == book
        assert reservation.student == student
        assert reservation.status == "active"
        assert str(reservation) == f"Test Student - Test Book (Reserved)"
    
    def test_reservation_validation(self, book, student):
        """Test reservation validation"""
        # Create first reservation
        BookReservation.objects.create(
            school=book.school,
            book=book,
            student=student,
            reservation_date=timezone.now().date(),
            expiry_date=timezone.now().date() + timedelta(days=3),
            status="active"
        )
        
        # Try to create duplicate active reservation
        with pytest.raises(ValidationError):
            reservation2 = BookReservation(
                school=book.school,
                book=book,
                student=student,
                reservation_date=timezone.now().date(),
                expiry_date=timezone.now().date() + timedelta(days=3),
                status="active"  # Duplicate active reservation
            )
            reservation2.full_clean()
    
    def test_reservation_expiry(self, book, student):
        """Test reservation expiry"""
        # Create expired reservation
        reservation = BookReservation.objects.create(
            school=book.school,
            book=book,
            student=student,
            reservation_date=timezone.now().date() - timedelta(days=5),
            expiry_date=timezone.now().date() - timedelta(days=2),  # Expired
            status="active"
        )
        
        assert reservation.is_expired() is True
        assert reservation.get_days_until_expiry() < 0
    
    def test_reservation_fulfillment(self, book, student):
        """Test reservation fulfillment"""
        reservation = BookReservation.objects.create(
            school=book.school,
            book=book,
            student=student,
            reservation_date=timezone.now().date(),
            expiry_date=timezone.now().date() + timedelta(days=3),
            status="active"
        )
        
        # Fulfill reservation
        reservation.fulfill_reservation()
        
        assert reservation.status == "fulfilled"
        assert reservation.fulfilled_date == timezone.now().date()


@pytest.mark.unit
class TestLibraryStatistics:
    """Test library statistics and analytics"""
    
    def test_book_popularity(self, book, student, school):
        """Test book popularity tracking"""
        # Create multiple borrowings
        for i in range(3):
            borrowing = BookBorrowing.objects.create(
                school=school,
                book=book,
                student=student,
                borrowed_date=timezone.now().date() - timedelta(days=30-i*10),
                due_date=timezone.now().date() - timedelta(days=16-i*10),
                returned_date=timezone.now().date() - timedelta(days=15-i*10),
                status="returned"
            )
        
        assert book.get_borrowing_count() == 3
        assert book.get_popularity_score() > 0
    
    def test_student_reading_history(self, student, book, school, book_category, author):
        """Test student reading history"""
        # Create another book
        book2 = Book.objects.create(
            school=school,
            title="Another Test Book",
            isbn="1111111111111",
            category=book_category,
            publication_year=2021,
            copies_total=3,
            copies_available=3,
            status="available"
        )
        book2.authors.add(author)
        
        # Create borrowings
        BookBorrowing.objects.create(
            school=school,
            book=book,
            student=student,
            borrowed_date=timezone.now().date() - timedelta(days=30),
            due_date=timezone.now().date() - timedelta(days=16),
            returned_date=timezone.now().date() - timedelta(days=15),
            status="returned"
        )
        
        BookBorrowing.objects.create(
            school=school,
            book=book2,
            student=student,
            borrowed_date=timezone.now().date() - timedelta(days=20),
            due_date=timezone.now().date() - timedelta(days=6),
            returned_date=timezone.now().date() - timedelta(days=5),
            status="returned"
        )
        
        reading_history = student.get_reading_history()
        assert len(reading_history) == 2
        assert book in [b.book for b in reading_history]
        assert book2 in [b.book for b in reading_history]