"""
Performance tests for School ERP System
"""
import pytest
import time
from django.test import TestCase, TransactionTestCase
from django.test import Client
from django.urls import reverse
from django.utils import timezone
from django.db import connection
from django.test.utils import override_settings
from datetime import datetime, timedelta
from decimal import Decimal


@pytest.mark.slow
class TestDatabasePerformance:
    """Test database query performance"""
    
    def test_student_query_performance(self, school, academic_year):
        """Test student query performance with large dataset"""
        from students.models import Student, Parent, StudentEnrollment
        from academics.models import Grade
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # Create test data
        grade = Grade.objects.create(
            school=school,
            academic_year=academic_year,
            name="Performance Grade",
            level=1,
            capacity=1000,
            is_active=True
        )
        
        # Create 100 students for performance testing
        students = []
        start_time = time.time()
        
        for i in range(100):
            parent_user = User.objects.create_user(
                username=f'perfparent{i}',
                email=f'perfparent{i}@test.com',
                password='testpass123'
            )
            
            parent = Parent.objects.create(
                school=school,
                user=parent_user,
                father_name=f"Father {i}",
                mother_name=f"Mother {i}",
                phone=f"123-456-{i:04d}",
                email=f"perfparent{i}@test.com"
            )
            
            student = Student.objects.create(
                school=school,
                student_id=f"PERF{i:04d}",
                first_name=f"Student{i}",
                last_name="Performance",
                date_of_birth=datetime(2010, 1, 1).date(),
                gender="male" if i % 2 == 0 else "female",
                nationality="US",
                parent=parent,
                admission_date=timezone.now().date(),
                status="active"
            )
            students.append(student)
            
            StudentEnrollment.objects.create(
                school=school,
                student=student,
                academic_year=academic_year,
                grade=grade,
                enrollment_date=timezone.now().date(),
                status="active"
            )
        
        creation_time = time.time() - start_time
        print(f"Created 100 students in {creation_time:.2f} seconds")
        
        # Test query performance
        start_time = time.time()
        
        # Query with joins
        students_with_parents = Student.objects.filter(
            school=school,
            status='active'
        ).select_related('parent__user').prefetch_related('enrollments__grade')
        
        # Force evaluation
        list(students_with_parents)
        
        query_time = time.time() - start_time
        print(f"Queried 100 students with relations in {query_time:.2f} seconds")
        
        # Performance assertions
        assert creation_time < 30.0  # Should create 100 students in under 30 seconds
        assert query_time < 5.0      # Should query with joins in under 5 seconds
    
    def test_financial_calculation_performance(self, school, academic_year):
        """Test financial calculation performance"""
        from finance.models import Account, Transaction, TransactionEntry
        from decimal import Decimal
        
        # Create accounts
        accounts = []
        for i in range(10):
            account = Account.objects.create(
                school=school,
                code=f"{1000 + i}",
                name=f"Account {i}",
                account_type="asset" if i < 5 else "revenue",
                is_active=True
            )
            accounts.append(account)
        
        # Create transactions
        start_time = time.time()
        
        for i in range(100):
            transaction = Transaction.objects.create(
                school=school,
                reference=f"PERF{i:04d}",
                description=f"Performance transaction {i}",
                date=timezone.now().date(),
                total_amount=Decimal(f"{(i+1)*100}.00")
            )
            
            # Create entries
            TransactionEntry.objects.create(
                transaction=transaction,
                account=accounts[i % 5],  # Asset accounts
                debit_amount=Decimal(f"{(i+1)*100}.00"),
                credit_amount=Decimal('0.00'),
                description=f"Debit entry {i}"
            )
            
            TransactionEntry.objects.create(
                transaction=transaction,
                account=accounts[5 + (i % 5)],  # Revenue accounts
                debit_amount=Decimal('0.00'),
                credit_amount=Decimal(f"{(i+1)*100}.00"),
                description=f"Credit entry {i}"
            )
        
        transaction_time = time.time() - start_time
        print(f"Created 100 transactions with entries in {transaction_time:.2f} seconds")
        
        # Test balance calculation performance
        start_time = time.time()
        
        from django.db.models import Sum
        for account in accounts:
            balance = TransactionEntry.objects.filter(
                account=account
            ).aggregate(
                balance=Sum('debit_amount') - Sum('credit_amount')
            )['balance']
        
        balance_time = time.time() - start_time
        print(f"Calculated balances for 10 accounts in {balance_time:.2f} seconds")
        
        # Performance assertions
        assert transaction_time < 20.0  # Should create transactions in under 20 seconds
        assert balance_time < 2.0       # Should calculate balances in under 2 seconds
    
    def test_query_count_optimization(self, school, academic_year, student):
        """Test query count optimization"""
        from django.test.utils import override_settings
        from django.db import connection
        
        # Reset query count
        connection.queries_log.clear()
        
        # Test optimized query
        with override_settings(DEBUG=True):
            from students.models import Student
            
            # Optimized query with select_related and prefetch_related
            students = Student.objects.filter(
                school=school
            ).select_related(
                'parent__user'
            ).prefetch_related(
                'enrollments__grade',
                'enrollments__academic_year'
            )
            
            # Force evaluation
            for student in students:
                _ = student.parent.user.first_name
                _ = [enrollment.grade.name for enrollment in student.enrollments.all()]
        
        query_count = len(connection.queries)
        print(f"Optimized query used {query_count} database queries")
        
        # Should use minimal queries due to optimization
        assert query_count < 10  # Should use less than 10 queries


@pytest.mark.slow
class TestAPIPerformance:
    """Test API endpoint performance"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.client = Client()
    
    def test_api_response_times(self, admin_user, school):
        """Test API endpoint response times"""
        self.client.force_login(admin_user)
        
        # Test various API endpoints
        endpoints = [
            reverse('students:student-list'),
            reverse('academics:grade-list'),
            reverse('finance:account-list'),
            reverse('transportation:vehicle-list'),
        ]
        
        response_times = {}
        
        for endpoint in endpoints:
            start_time = time.time()
            
            try:
                response = self.client.get(endpoint)
                response_time = time.time() - start_time
                response_times[endpoint] = response_time
                
                print(f"{endpoint}: {response_time:.3f} seconds")
                
                # Performance assertion
                assert response_time < 2.0  # Should respond in under 2 seconds
                assert response.status_code in [200, 404]  # Success or not found
                
            except Exception as e:
                print(f"Error testing {endpoint}: {e}")
                continue
        
        # Overall performance check
        avg_response_time = sum(response_times.values()) / len(response_times)
        assert avg_response_time < 1.0  # Average should be under 1 second
    
    def test_pagination_performance(self, admin_user, school, academic_year):
        """Test pagination performance with large datasets"""
        from students.models import Student, Parent
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        self.client.force_login(admin_user)
        
        # Create test data (smaller set for CI)
        for i in range(50):
            parent_user = User.objects.create_user(
                username=f'pagparent{i}',
                email=f'pagparent{i}@test.com',
                password='testpass123'
            )
            
            parent = Parent.objects.create(
                school=school,
                user=parent_user,
                father_name=f"Father {i}",
                mother_name=f"Mother {i}",
                phone=f"123-456-{i:04d}",
                email=f"pagparent{i}@test.com"
            )
            
            Student.objects.create(
                school=school,
                student_id=f"PAG{i:04d}",
                first_name=f"Student{i}",
                last_name="Pagination",
                date_of_birth=datetime(2010, 1, 1).date(),
                gender="male" if i % 2 == 0 else "female",
                nationality="US",
                parent=parent,
                admission_date=timezone.now().date(),
                status="active"
            )
        
        # Test paginated API response
        start_time = time.time()
        
        try:
            response = self.client.get(f"{reverse('students:student-list')}?page=1&page_size=20")
            response_time = time.time() - start_time
            
            print(f"Paginated API response time: {response_time:.3f} seconds")
            
            assert response_time < 3.0  # Should respond in under 3 seconds
            if response.status_code == 200:
                data = response.json() if hasattr(response, 'json') else {}
                if 'results' in data:
                    assert len(data['results']) <= 20  # Should respect page size
                    
        except Exception as e:
            print(f"Pagination test error: {e}")


@pytest.mark.slow
class TestMemoryUsage:
    """Test memory usage and resource management"""
    
    def test_bulk_operations_memory(self, school, academic_year):
        """Test memory usage during bulk operations"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Perform bulk operations
        from students.models import Student, Parent
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # Create data in batches to test memory management
        batch_size = 20
        total_records = 100
        
        for batch in range(0, total_records, batch_size):
            batch_data = []
            
            for i in range(batch, min(batch + batch_size, total_records)):
                parent_user = User.objects.create_user(
                    username=f'memparent{i}',
                    email=f'memparent{i}@test.com',
                    password='testpass123'
                )
                
                parent = Parent.objects.create(
                    school=school,
                    user=parent_user,
                    father_name=f"Father {i}",
                    mother_name=f"Mother {i}",
                    phone=f"123-456-{i:04d}",
                    email=f"memparent{i}@test.com"
                )
                
                student = Student(
                    school=school,
                    student_id=f"MEM{i:04d}",
                    first_name=f"Student{i}",
                    last_name="Memory",
                    date_of_birth=datetime(2010, 1, 1).date(),
                    gender="male" if i % 2 == 0 else "female",
                    nationality="US",
                    parent=parent,
                    admission_date=timezone.now().date(),
                    status="active"
                )
                batch_data.append(student)
            
            # Bulk create
            Student.objects.bulk_create(batch_data)
            
            # Check memory usage
            current_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = current_memory - initial_memory
            
            print(f"Batch {batch//batch_size + 1}: Memory usage: {current_memory:.1f} MB (+{memory_increase:.1f} MB)")
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        total_memory_increase = final_memory - initial_memory
        
        print(f"Total memory increase: {total_memory_increase:.1f} MB")
        
        # Memory usage should be reasonable
        assert total_memory_increase < 100  # Should not increase by more than 100MB


@pytest.mark.slow
class TestConcurrency:
    """Test concurrent operations"""
    
    def test_concurrent_user_access(self, school, admin_user):
        """Test concurrent user access simulation"""
        import threading
        import queue
        
        results = queue.Queue()
        
        def simulate_user_request(user_id):
            """Simulate a user request"""
            client = Client()
            client.force_login(admin_user)
            
            start_time = time.time()
            
            try:
                # Simulate typical user workflow
                response1 = client.get(reverse('core:dashboard'))
                response2 = client.get(reverse('students:student_list'))
                
                end_time = time.time()
                
                results.put({
                    'user_id': user_id,
                    'success': response1.status_code == 200 and response2.status_code in [200, 404],
                    'response_time': end_time - start_time
                })
                
            except Exception as e:
                results.put({
                    'user_id': user_id,
                    'success': False,
                    'error': str(e),
                    'response_time': time.time() - start_time
                })
        
        # Create threads to simulate concurrent users
        threads = []
        num_users = 5  # Reduced for CI environment
        
        start_time = time.time()
        
        for i in range(num_users):
            thread = threading.Thread(target=simulate_user_request, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        total_time = time.time() - start_time
        
        # Collect results
        successful_requests = 0
        total_response_time = 0
        
        while not results.empty():
            result = results.get()
            if result['success']:
                successful_requests += 1
                total_response_time += result['response_time']
            else:
                print(f"User {result['user_id']} failed: {result.get('error', 'Unknown error')}")
        
        print(f"Concurrent test: {successful_requests}/{num_users} successful requests")
        print(f"Total time: {total_time:.2f} seconds")
        
        if successful_requests > 0:
            avg_response_time = total_response_time / successful_requests
            print(f"Average response time: {avg_response_time:.2f} seconds")
            
            # Performance assertions
            assert successful_requests >= num_users * 0.8  # At least 80% success rate
            assert avg_response_time < 5.0  # Average response time under 5 seconds


@pytest.mark.slow
class TestDataIntegrity:
    """Test data integrity under stress"""
    
    def test_transaction_integrity_under_load(self, school):
        """Test transaction integrity under concurrent load"""
        from finance.models import Account, Transaction, TransactionEntry
        from decimal import Decimal
        import threading
        
        # Create accounts
        cash_account = Account.objects.create(
            school=school,
            code="1000",
            name="Cash",
            account_type="asset",
            is_active=True
        )
        
        revenue_account = Account.objects.create(
            school=school,
            code="4000",
            name="Revenue",
            account_type="revenue",
            is_active=True
        )
        
        def create_balanced_transaction(thread_id):
            """Create a balanced transaction"""
            try:
                from django.db import transaction
                
                with transaction.atomic():
                    txn = Transaction.objects.create(
                        school=school,
                        reference=f"STRESS{thread_id:04d}",
                        description=f"Stress test transaction {thread_id}",
                        date=timezone.now().date(),
                        total_amount=Decimal('100.00')
                    )
                    
                    # Debit entry
                    TransactionEntry.objects.create(
                        transaction=txn,
                        account=cash_account,
                        debit_amount=Decimal('100.00'),
                        credit_amount=Decimal('0.00'),
                        description=f"Cash received {thread_id}"
                    )
                    
                    # Credit entry
                    TransactionEntry.objects.create(
                        transaction=txn,
                        account=revenue_account,
                        debit_amount=Decimal('0.00'),
                        credit_amount=Decimal('100.00'),
                        description=f"Revenue earned {thread_id}"
                    )
                    
                return True
                
            except Exception as e:
                print(f"Transaction {thread_id} failed: {e}")
                return False
        
        # Create concurrent transactions
        threads = []
        num_transactions = 10  # Reduced for CI
        
        for i in range(num_transactions):
            thread = threading.Thread(target=create_balanced_transaction, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for completion
        for thread in threads:
            thread.join()
        
        # Verify data integrity
        total_transactions = Transaction.objects.filter(school=school).count()
        total_entries = TransactionEntry.objects.filter(
            transaction__school=school
        ).count()
        
        print(f"Created {total_transactions} transactions with {total_entries} entries")
        
        # Check balance integrity
        from django.db.models import Sum
        
        cash_balance = TransactionEntry.objects.filter(
            account=cash_account
        ).aggregate(
            balance=Sum('debit_amount') - Sum('credit_amount')
        )['balance'] or Decimal('0.00')
        
        revenue_balance = TransactionEntry.objects.filter(
            account=revenue_account
        ).aggregate(
            balance=Sum('credit_amount') - Sum('debit_amount')
        )['balance'] or Decimal('0.00')
        
        print(f"Cash balance: {cash_balance}, Revenue balance: {revenue_balance}")
        
        # Integrity assertions
        assert total_transactions >= num_transactions * 0.8  # At least 80% success
        assert total_entries == total_transactions * 2      # Each transaction has 2 entries
        assert cash_balance == revenue_balance              # Balances should match