"""
Quality Assurance tests and tools for School ERP System
"""
import pytest
import os
import subprocess
import ast
import re
from pathlib import Path
from django.test import TestCase
from django.core.management import call_command
from django.core.management.base import CommandError
from django.conf import settings


@pytest.mark.unit
class TestCodeQuality:
    """Test code quality standards"""
    
    def test_python_syntax_validation(self):
        """Test that all Python files have valid syntax"""
        python_files = []
        
        # Find all Python files
        for root, dirs, files in os.walk('.'):
            # Skip virtual environments and cache directories
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'venv', 'env']]
            
            for file in files:
                if file.endswith('.py'):
                    python_files.append(os.path.join(root, file))
        
        syntax_errors = []
        
        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    source = f.read()
                ast.parse(source, filename=file_path)
            except SyntaxError as e:
                syntax_errors.append(f"{file_path}: {e}")
            except UnicodeDecodeError:
                # Skip binary files or files with encoding issues
                continue
        
        assert len(syntax_errors) == 0, f"Syntax errors found:\n" + "\n".join(syntax_errors)
    
    def test_import_statements(self):
        """Test that all imports are valid"""
        python_files = []
        
        for root, dirs, files in os.walk('.'):
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'venv', 'env']]
            
            for file in files:
                if file.endswith('.py') and not file.startswith('test_'):
                    python_files.append(os.path.join(root, file))
        
        import_errors = []
        
        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    source = f.read()
                
                # Parse imports
                tree = ast.parse(source, filename=file_path)
                
                for node in ast.walk(tree):
                    if isinstance(node, (ast.Import, ast.ImportFrom)):
                        # Check for relative imports outside packages
                        if isinstance(node, ast.ImportFrom) and node.level > 0:
                            # This is a relative import, which is generally OK in packages
                            continue
                        
                        # Check for unused imports (basic check)
                        if isinstance(node, ast.Import):
                            for alias in node.names:
                                import_name = alias.asname or alias.name
                                if import_name not in source:
                                    # This is a very basic check, might have false positives
                                    pass
                                    
            except (SyntaxError, UnicodeDecodeError):
                continue
            except Exception as e:
                import_errors.append(f"{file_path}: {e}")
        
        # For now, just ensure no critical import errors
        assert len(import_errors) == 0, f"Import errors found:\n" + "\n".join(import_errors)
    
    def test_model_string_representations(self):
        """Test that all models have proper __str__ methods"""
        from django.apps import apps
        
        models_without_str = []
        
        for model in apps.get_models():
            # Skip abstract models and Django's built-in models
            if model._meta.abstract or model._meta.app_label in ['auth', 'contenttypes', 'sessions', 'admin']:
                continue
            
            # Check if model has custom __str__ method
            if '__str__' not in model.__dict__:
                models_without_str.append(f"{model._meta.app_label}.{model.__name__}")
        
        assert len(models_without_str) == 0, f"Models without __str__ method: {models_without_str}"
    
    def test_model_meta_classes(self):
        """Test that models have proper Meta classes with required attributes"""
        from django.apps import apps
        
        issues = []
        
        for model in apps.get_models():
            if model._meta.abstract or model._meta.app_label in ['auth', 'contenttypes', 'sessions', 'admin']:
                continue
            
            # Check for verbose_name_plural
            if not hasattr(model._meta, 'verbose_name_plural') or not model._meta.verbose_name_plural:
                issues.append(f"{model._meta.app_label}.{model.__name__} missing verbose_name_plural")
            
            # Check for ordering on models with created_at field
            if hasattr(model, 'created_at') and not model._meta.ordering:
                issues.append(f"{model._meta.app_label}.{model.__name__} should have ordering defined")
        
        # Allow some issues for now, but report them
        if issues:
            print(f"Model Meta issues found (not failing test): {issues}")


@pytest.mark.unit
class TestSecurityStandards:
    """Test security standards compliance"""
    
    def test_settings_security(self):
        """Test Django settings for security compliance"""
        security_issues = []
        
        # Check DEBUG setting
        if getattr(settings, 'DEBUG', True):
            security_issues.append("DEBUG should be False in production")
        
        # Check SECRET_KEY
        secret_key = getattr(settings, 'SECRET_KEY', '')
        if not secret_key or len(secret_key) < 50:
            security_issues.append("SECRET_KEY should be long and complex")
        
        # Check ALLOWED_HOSTS
        allowed_hosts = getattr(settings, 'ALLOWED_HOSTS', [])
        if not allowed_hosts or '*' in allowed_hosts:
            security_issues.append("ALLOWED_HOSTS should be properly configured")
        
        # Check security middleware
        middleware = getattr(settings, 'MIDDLEWARE', [])
        security_middleware = [
            'django.middleware.security.SecurityMiddleware',
            'django.contrib.sessions.middleware.SessionMiddleware',
            'django.middleware.csrf.CsrfViewMiddleware',
            'django.contrib.auth.middleware.AuthenticationMiddleware',
        ]
        
        for mw in security_middleware:
            if mw not in middleware:
                security_issues.append(f"Missing security middleware: {mw}")
        
        # For development, we'll just warn about issues
        if security_issues:
            print(f"Security issues found (warnings): {security_issues}")
    
    def test_password_validation(self):
        """Test password validation settings"""
        auth_password_validators = getattr(settings, 'AUTH_PASSWORD_VALIDATORS', [])
        
        required_validators = [
            'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
            'django.contrib.auth.password_validation.MinimumLengthValidator',
            'django.contrib.auth.password_validation.CommonPasswordValidator',
            'django.contrib.auth.password_validation.NumericPasswordValidator',
        ]
        
        configured_validators = [v.get('NAME', '') for v in auth_password_validators]
        
        missing_validators = []
        for validator in required_validators:
            if validator not in configured_validators:
                missing_validators.append(validator)
        
        if missing_validators:
            print(f"Missing password validators (warning): {missing_validators}")
    
    def test_csrf_protection(self):
        """Test CSRF protection is enabled"""
        middleware = getattr(settings, 'MIDDLEWARE', [])
        
        csrf_middleware = 'django.middleware.csrf.CsrfViewMiddleware'
        assert csrf_middleware in middleware, "CSRF middleware should be enabled"
        
        # Check CSRF settings
        csrf_cookie_secure = getattr(settings, 'CSRF_COOKIE_SECURE', False)
        csrf_cookie_httponly = getattr(settings, 'CSRF_COOKIE_HTTPONLY', False)
        
        # In development, these might be False, so just warn
        if not csrf_cookie_secure:
            print("Warning: CSRF_COOKIE_SECURE should be True in production")
        if not csrf_cookie_httponly:
            print("Warning: CSRF_COOKIE_HTTPONLY should be True")


@pytest.mark.unit
class TestDatabaseIntegrity:
    """Test database integrity and constraints"""
    
    def test_model_field_constraints(self):
        """Test that model fields have appropriate constraints"""
        from django.apps import apps
        
        issues = []
        
        for model in apps.get_models():
            if model._meta.abstract or model._meta.app_label in ['auth', 'contenttypes', 'sessions', 'admin']:
                continue
            
            for field in model._meta.fields:
                # Check for missing help_text on complex fields
                if field.__class__.__name__ in ['ForeignKey', 'ManyToManyField'] and not field.help_text:
                    issues.append(f"{model._meta.app_label}.{model.__name__}.{field.name} missing help_text")
                
                # Check for missing verbose_name
                if not field.verbose_name or field.verbose_name == field.name:
                    issues.append(f"{model._meta.app_label}.{model.__name__}.{field.name} missing verbose_name")
        
        # Allow some issues for now
        if issues:
            print(f"Field constraint issues (warnings): {len(issues)} issues found")
    
    def test_foreign_key_relationships(self):
        """Test foreign key relationships are properly defined"""
        from django.apps import apps
        
        issues = []
        
        for model in apps.get_models():
            if model._meta.abstract or model._meta.app_label in ['auth', 'contenttypes', 'sessions', 'admin']:
                continue
            
            for field in model._meta.fields:
                if field.__class__.__name__ == 'ForeignKey':
                    # Check for on_delete parameter
                    if not hasattr(field, 'on_delete') or field.on_delete is None:
                        issues.append(f"{model._meta.app_label}.{model.__name__}.{field.name} missing on_delete")
                    
                    # Check for related_name conflicts
                    if field.related_name and '+' not in field.related_name:
                        # This is a basic check, more complex validation would be needed
                        pass
        
        if issues:
            print(f"Foreign key issues (warnings): {issues}")


@pytest.mark.unit
class TestPerformanceStandards:
    """Test performance standards"""
    
    def test_database_indexes(self):
        """Test that appropriate database indexes exist"""
        from django.apps import apps
        
        missing_indexes = []
        
        for model in apps.get_models():
            if model._meta.abstract or model._meta.app_label in ['auth', 'contenttypes', 'sessions', 'admin']:
                continue
            
            # Check for indexes on foreign keys
            for field in model._meta.fields:
                if field.__class__.__name__ == 'ForeignKey':
                    if not field.db_index:
                        missing_indexes.append(f"{model._meta.app_label}.{model.__name__}.{field.name}")
        
        if missing_indexes:
            print(f"Missing database indexes (warnings): {len(missing_indexes)} fields")
    
    def test_query_optimization(self):
        """Test for potential query optimization issues"""
        # This would typically involve analyzing actual queries
        # For now, we'll do basic checks
        
        from django.apps import apps
        
        potential_issues = []
        
        for model in apps.get_models():
            if model._meta.abstract or model._meta.app_label in ['auth', 'contenttypes', 'sessions', 'admin']:
                continue
            
            # Check for models with many foreign keys (potential N+1 issues)
            fk_count = sum(1 for field in model._meta.fields if field.__class__.__name__ == 'ForeignKey')
            if fk_count > 5:
                potential_issues.append(f"{model._meta.app_label}.{model.__name__} has {fk_count} foreign keys")
        
        if potential_issues:
            print(f"Potential query optimization issues (warnings): {potential_issues}")


@pytest.mark.unit
class TestDocumentationStandards:
    """Test documentation standards"""
    
    def test_model_docstrings(self):
        """Test that models have proper docstrings"""
        from django.apps import apps
        
        missing_docstrings = []
        
        for model in apps.get_models():
            if model._meta.abstract or model._meta.app_label in ['auth', 'contenttypes', 'sessions', 'admin']:
                continue
            
            if not model.__doc__ or len(model.__doc__.strip()) < 10:
                missing_docstrings.append(f"{model._meta.app_label}.{model.__name__}")
        
        if missing_docstrings:
            print(f"Models missing docstrings (warnings): {len(missing_docstrings)} models")
    
    def test_view_docstrings(self):
        """Test that views have proper docstrings"""
        # This would require more complex analysis of view files
        # For now, we'll do a basic check
        
        view_files = []
        for root, dirs, files in os.walk('.'):
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'venv', 'env']]
            
            for file in files:
                if file == 'views.py':
                    view_files.append(os.path.join(root, file))
        
        issues = []
        
        for file_path in view_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Basic check for class-based views without docstrings
                class_pattern = r'class\s+\w+.*View.*\(.*\):'
                classes = re.findall(class_pattern, content)
                
                for class_def in classes:
                    # This is a very basic check
                    if '"""' not in content[content.find(class_def):content.find(class_def) + 200]:
                        issues.append(f"{file_path}: {class_def}")
                        
            except (UnicodeDecodeError, Exception):
                continue
        
        if issues:
            print(f"Views missing docstrings (warnings): {len(issues)} views")


@pytest.mark.unit
class TestTestCoverage:
    """Test coverage analysis"""
    
    def test_model_test_coverage(self):
        """Test that all models have corresponding tests"""
        from django.apps import apps
        
        # Get all models
        models = []
        for model in apps.get_models():
            if model._meta.abstract or model._meta.app_label in ['auth', 'contenttypes', 'sessions', 'admin']:
                continue
            models.append(f"{model._meta.app_label}.{model.__name__}")
        
        # Check for test files
        test_files = []
        for root, dirs, files in os.walk('tests'):
            for file in files:
                if file.startswith('test_') and file.endswith('.py'):
                    test_files.append(os.path.join(root, file))
        
        # Basic coverage check
        tested_models = set()
        
        for test_file in test_files:
            try:
                with open(test_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Look for model imports and usage
                for model_name in models:
                    app_label, model_class = model_name.split('.')
                    if model_class in content:
                        tested_models.add(model_name)
                        
            except (UnicodeDecodeError, Exception):
                continue
        
        untested_models = set(models) - tested_models
        
        if untested_models:
            print(f"Models without tests (warnings): {len(untested_models)} models")
        
        # Calculate coverage percentage
        coverage_percentage = (len(tested_models) / len(models)) * 100 if models else 100
        print(f"Model test coverage: {coverage_percentage:.1f}%")
        
        # We'll aim for at least 70% coverage
        assert coverage_percentage >= 70, f"Model test coverage is {coverage_percentage:.1f}%, should be at least 70%"


class QualityAssuranceRunner:
    """Quality assurance test runner"""
    
    def __init__(self):
        self.results = {
            'code_quality': [],
            'security': [],
            'performance': [],
            'documentation': [],
            'coverage': []
        }
    
    def run_all_checks(self):
        """Run all quality assurance checks"""
        print("🔍 Running Quality Assurance Checks...")
        print("=" * 50)
        
        # Run code quality checks
        self.run_code_quality_checks()
        
        # Run security checks
        self.run_security_checks()
        
        # Run performance checks
        self.run_performance_checks()
        
        # Run documentation checks
        self.run_documentation_checks()
        
        # Run coverage checks
        self.run_coverage_checks()
        
        # Generate report
        self.generate_report()
    
    def run_code_quality_checks(self):
        """Run code quality checks"""
        print("📝 Code Quality Checks...")
        
        try:
            # Run pytest for code quality tests
            result = subprocess.run([
                'python', '-m', 'pytest', 
                'tests/test_quality_assurance.py::TestCodeQuality',
                '-v'
            ], capture_output=True, text=True)
            
            self.results['code_quality'].append({
                'status': 'passed' if result.returncode == 0 else 'failed',
                'output': result.stdout,
                'errors': result.stderr
            })
            
        except Exception as e:
            self.results['code_quality'].append({
                'status': 'error',
                'output': '',
                'errors': str(e)
            })
    
    def run_security_checks(self):
        """Run security checks"""
        print("🔒 Security Checks...")
        
        try:
            result = subprocess.run([
                'python', '-m', 'pytest', 
                'tests/test_quality_assurance.py::TestSecurityStandards',
                '-v'
            ], capture_output=True, text=True)
            
            self.results['security'].append({
                'status': 'passed' if result.returncode == 0 else 'failed',
                'output': result.stdout,
                'errors': result.stderr
            })
            
        except Exception as e:
            self.results['security'].append({
                'status': 'error',
                'output': '',
                'errors': str(e)
            })
    
    def run_performance_checks(self):
        """Run performance checks"""
        print("⚡ Performance Checks...")
        
        try:
            result = subprocess.run([
                'python', '-m', 'pytest', 
                'tests/test_quality_assurance.py::TestPerformanceStandards',
                '-v'
            ], capture_output=True, text=True)
            
            self.results['performance'].append({
                'status': 'passed' if result.returncode == 0 else 'failed',
                'output': result.stdout,
                'errors': result.stderr
            })
            
        except Exception as e:
            self.results['performance'].append({
                'status': 'error',
                'output': '',
                'errors': str(e)
            })
    
    def run_documentation_checks(self):
        """Run documentation checks"""
        print("📚 Documentation Checks...")
        
        try:
            result = subprocess.run([
                'python', '-m', 'pytest', 
                'tests/test_quality_assurance.py::TestDocumentationStandards',
                '-v'
            ], capture_output=True, text=True)
            
            self.results['documentation'].append({
                'status': 'passed' if result.returncode == 0 else 'failed',
                'output': result.stdout,
                'errors': result.stderr
            })
            
        except Exception as e:
            self.results['documentation'].append({
                'status': 'error',
                'output': '',
                'errors': str(e)
            })
    
    def run_coverage_checks(self):
        """Run coverage checks"""
        print("📊 Coverage Checks...")
        
        try:
            result = subprocess.run([
                'python', '-m', 'pytest', 
                'tests/test_quality_assurance.py::TestTestCoverage',
                '-v'
            ], capture_output=True, text=True)
            
            self.results['coverage'].append({
                'status': 'passed' if result.returncode == 0 else 'failed',
                'output': result.stdout,
                'errors': result.stderr
            })
            
        except Exception as e:
            self.results['coverage'].append({
                'status': 'error',
                'output': '',
                'errors': str(e)
            })
    
    def generate_report(self):
        """Generate quality assurance report"""
        print("\n📋 Quality Assurance Report")
        print("=" * 50)
        
        total_checks = 0
        passed_checks = 0
        
        for category, results in self.results.items():
            print(f"\n{category.upper().replace('_', ' ')}:")
            
            for result in results:
                total_checks += 1
                status_icon = "✅" if result['status'] == 'passed' else "❌" if result['status'] == 'failed' else "⚠️"
                print(f"  {status_icon} {result['status'].upper()}")
                
                if result['status'] == 'passed':
                    passed_checks += 1
                
                if result['errors']:
                    print(f"    Errors: {result['errors'][:200]}...")
        
        # Calculate overall score
        if total_checks > 0:
            score = (passed_checks / total_checks) * 100
            print(f"\n🎯 Overall Quality Score: {score:.1f}% ({passed_checks}/{total_checks})")
            
            if score >= 90:
                print("🌟 Excellent quality!")
            elif score >= 80:
                print("👍 Good quality!")
            elif score >= 70:
                print("⚠️  Acceptable quality, room for improvement")
            else:
                print("❌ Quality needs significant improvement")
        
        return self.results


if __name__ == "__main__":
    runner = QualityAssuranceRunner()
    runner.run_all_checks()