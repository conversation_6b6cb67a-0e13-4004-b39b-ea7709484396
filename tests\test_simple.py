"""
Simple tests to verify test setup
"""
import pytest
import django
from django.conf import settings
from django.test import TestCase


@pytest.mark.unit
def test_django_setup():
    """Test that Django is properly configured"""
    assert settings.configured
    assert hasattr(settings, 'DATABASES')
    assert hasattr(settings, 'INSTALLED_APPS')


@pytest.mark.unit
def test_basic_math():
    """Basic test to verify pytest is working"""
    assert 1 + 1 == 2
    assert 2 * 3 == 6


@pytest.mark.unit
class TestBasicFunctionality:
    """Test basic functionality"""
    
    def test_string_operations(self):
        """Test string operations"""
        test_string = "Hello World"
        assert test_string.lower() == "hello world"
        assert test_string.upper() == "HELLO WORLD"
        assert len(test_string) == 11
    
    def test_list_operations(self):
        """Test list operations"""
        test_list = [1, 2, 3, 4, 5]
        assert len(test_list) == 5
        assert sum(test_list) == 15
        assert max(test_list) == 5
        assert min(test_list) == 1
    
    def test_dict_operations(self):
        """Test dictionary operations"""
        test_dict = {'a': 1, 'b': 2, 'c': 3}
        assert len(test_dict) == 3
        assert test_dict['a'] == 1
        assert 'b' in test_dict
        assert test_dict.get('d', 0) == 0


@pytest.mark.unit
def test_imports():
    """Test that we can import Django modules"""
    from django.contrib.auth import get_user_model
    from django.utils import timezone
    from django.db import models
    
    User = get_user_model()
    assert User is not None
    assert timezone is not None
    assert models is not None