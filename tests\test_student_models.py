"""
Unit tests for student models
"""
import pytest
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal

from students.models import Student, Parent, StudentDocument, StudentEnrollment


@pytest.mark.unit
class TestParentModel:
    """Test Parent model"""
    
    def test_parent_creation(self, parent_user, school):
        """Test parent creation"""
        parent = Parent.objects.get(user=parent_user)
        
        assert parent.school == school
        assert parent.father_name == "Father Name"
        assert parent.mother_name == "Mother Name"
        assert parent.phone == "************"
        assert str(parent) == "Father Name - Mother Name"
    
    def test_parent_validation(self, school, parent_user):
        """Test parent validation"""
        parent = Parent.objects.get(user=parent_user)
        
        # Test phone number validation
        parent.phone = "invalid-phone"
        with pytest.raises(ValidationError):
            parent.full_clean()
    
    def test_parent_methods(self, parent_user):
        """Test parent methods"""
        parent = Parent.objects.get(user=parent_user)
        
        assert parent.get_full_name() == "Father Name - Mother Name"
        assert parent.get_contact_info()['phone'] == "************"
        assert parent.get_contact_info()['email'] == "<EMAIL>"


@pytest.mark.unit
class TestStudentModel:
    """Test Student model"""
    
    def test_student_creation(self, student):
        """Test student creation"""
        assert student.student_id == "STU001"
        assert student.first_name == "Test"
        assert student.last_name == "Student"
        assert student.status == "active"
        assert str(student) == "Test Student (STU001)"
    
    def test_student_validation(self, school, parent_user):
        """Test student validation"""
        parent = Parent.objects.get(user=parent_user)
        
        # Test duplicate student ID
        Student.objects.create(
            school=school,
            student_id="UNIQUE001",
            first_name="Student",
            last_name="One",
            date_of_birth=datetime(2010, 1, 1).date(),
            gender="male",
            nationality="US",
            parent=parent,
            admission_date=timezone.now().date(),
            status="active"
        )
        
        with pytest.raises(ValidationError):
            student2 = Student(
                school=school,
                student_id="UNIQUE001",  # Duplicate ID
                first_name="Student",
                last_name="Two",
                date_of_birth=datetime(2010, 1, 1).date(),
                gender="female",
                nationality="US",
                parent=parent,
                admission_date=timezone.now().date(),
                status="active"
            )
            student2.full_clean()
    
    def test_student_age_calculation(self, student):
        """Test student age calculation"""
        age = student.get_age()
        expected_age = timezone.now().year - 2010
        
        assert age == expected_age or age == expected_age - 1  # Account for birthday
    
    def test_student_full_name(self, student):
        """Test student full name"""
        assert student.get_full_name() == "Test Student"
    
    def test_student_status_methods(self, student):
        """Test student status methods"""
        assert student.is_active() is True
        
        student.status = "inactive"
        student.save()
        
        assert student.is_active() is False
    
    def test_student_academic_info(self, student, grade, academic_year):
        """Test student academic information"""
        # Create enrollment
        enrollment = StudentEnrollment.objects.create(
            school=student.school,
            student=student,
            academic_year=academic_year,
            grade=grade,
            enrollment_date=timezone.now().date(),
            status="active"
        )
        
        current_grade = student.get_current_grade()
        assert current_grade == grade


@pytest.mark.unit
class TestStudentDocumentModel:
    """Test StudentDocument model"""
    
    def test_document_creation(self, student):
        """Test document creation"""
        document = StudentDocument.objects.create(
            school=student.school,
            student=student,
            document_type="birth_certificate",
            title="Birth Certificate",
            description="Student birth certificate",
            is_required=True,
            is_verified=False
        )
        
        assert document.student == student
        assert document.document_type == "birth_certificate"
        assert document.is_required is True
        assert document.is_verified is False
        assert str(document) == "Birth Certificate - Test Student"
    
    def test_document_verification(self, student):
        """Test document verification"""
        document = StudentDocument.objects.create(
            school=student.school,
            student=student,
            document_type="passport",
            title="Passport",
            is_required=True,
            is_verified=False
        )
        
        # Verify document
        document.verify_document()
        
        assert document.is_verified is True
        assert document.verified_date is not None
    
    def test_document_types(self, student):
        """Test different document types"""
        document_types = [
            "birth_certificate",
            "passport",
            "medical_records",
            "previous_school_records",
            "photo"
        ]
        
        for doc_type in document_types:
            document = StudentDocument.objects.create(
                school=student.school,
                student=student,
                document_type=doc_type,
                title=f"Test {doc_type}",
                is_required=True
            )
            
            assert document.document_type == doc_type


@pytest.mark.unit
class TestStudentEnrollmentModel:
    """Test StudentEnrollment model"""
    
    def test_enrollment_creation(self, student, grade, academic_year):
        """Test enrollment creation"""
        enrollment = StudentEnrollment.objects.create(
            school=student.school,
            student=student,
            academic_year=academic_year,
            grade=grade,
            enrollment_date=timezone.now().date(),
            status="active"
        )
        
        assert enrollment.student == student
        assert enrollment.grade == grade
        assert enrollment.academic_year == academic_year
        assert enrollment.status == "active"
        assert str(enrollment) == f"Test Student - Grade 1 ({academic_year.name})"
    
    def test_enrollment_validation(self, student, grade, academic_year):
        """Test enrollment validation"""
        # Create first enrollment
        StudentEnrollment.objects.create(
            school=student.school,
            student=student,
            academic_year=academic_year,
            grade=grade,
            enrollment_date=timezone.now().date(),
            status="active"
        )
        
        # Try to create duplicate active enrollment
        with pytest.raises(ValidationError):
            enrollment2 = StudentEnrollment(
                school=student.school,
                student=student,
                academic_year=academic_year,
                grade=grade,
                enrollment_date=timezone.now().date(),
                status="active"
            )
            enrollment2.full_clean()
    
    def test_enrollment_status_methods(self, student, grade, academic_year):
        """Test enrollment status methods"""
        enrollment = StudentEnrollment.objects.create(
            school=student.school,
            student=student,
            academic_year=academic_year,
            grade=grade,
            enrollment_date=timezone.now().date(),
            status="active"
        )
        
        assert enrollment.is_active() is True
        
        enrollment.status = "completed"
        enrollment.save()
        
        assert enrollment.is_active() is False
    
    def test_enrollment_duration(self, student, grade, academic_year):
        """Test enrollment duration calculation"""
        enrollment = StudentEnrollment.objects.create(
            school=student.school,
            student=student,
            academic_year=academic_year,
            grade=grade,
            enrollment_date=timezone.now().date() - timedelta(days=30),
            status="active"
        )
        
        duration = enrollment.get_enrollment_duration()
        assert duration >= 30  # At least 30 days


@pytest.mark.unit
class TestStudentModelMethods:
    """Test Student model methods and properties"""
    
    def test_student_contact_info(self, student):
        """Test student contact information"""
        contact_info = student.get_contact_info()
        
        assert 'parent_phone' in contact_info
        assert 'parent_email' in contact_info
        assert contact_info['parent_phone'] == "************"
        assert contact_info['parent_email'] == "<EMAIL>"
    
    def test_student_academic_history(self, student, grade, academic_year):
        """Test student academic history"""
        # Create enrollment
        StudentEnrollment.objects.create(
            school=student.school,
            student=student,
            academic_year=academic_year,
            grade=grade,
            enrollment_date=timezone.now().date(),
            status="active"
        )
        
        history = student.get_academic_history()
        assert len(history) >= 1
        assert history[0].grade == grade
    
    def test_student_documents_status(self, student):
        """Test student documents status"""
        # Create required documents
        StudentDocument.objects.create(
            school=student.school,
            student=student,
            document_type="birth_certificate",
            title="Birth Certificate",
            is_required=True,
            is_verified=True
        )
        
        StudentDocument.objects.create(
            school=student.school,
            student=student,
            document_type="passport",
            title="Passport",
            is_required=True,
            is_verified=False
        )
        
        docs_status = student.get_documents_status()
        
        assert docs_status['total_required'] == 2
        assert docs_status['verified'] == 1
        assert docs_status['pending'] == 1
        assert docs_status['completion_percentage'] == 50.0
    
    def test_student_admission_info(self, student):
        """Test student admission information"""
        admission_info = student.get_admission_info()
        
        assert 'admission_date' in admission_info
        assert 'days_since_admission' in admission_info
        assert admission_info['admission_date'] == student.admission_date