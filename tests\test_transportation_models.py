"""
Unit tests for transportation models
"""
import pytest
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal

from transportation.models import (
    Vehicle, Driver, Route, BusStop, RouteStop, StudentTransportation,
    GPSTracking, TransportationAttendance, TransportationFee
)


@pytest.mark.unit
class TestVehicleModel:
    """Test Vehicle model"""
    
    def test_vehicle_creation(self, vehicle):
        """Test vehicle creation"""
        assert vehicle.vehicle_number == "BUS001"
        assert vehicle.license_plate == "ABC123"
        assert vehicle.make == "Ford"
        assert vehicle.model == "Transit"
        assert vehicle.capacity == 50
        assert vehicle.status == "active"
        assert str(vehicle) == "BUS001 - Ford Transit"
    
    def test_vehicle_validation(self, school):
        """Test vehicle validation"""
        # Test duplicate vehicle number
        Vehicle.objects.create(
            school=school,
            vehicle_number="UNIQUE001",
            license_plate="XYZ789",
            make="Mercedes",
            model="Sprinter",
            year=2021,
            capacity=30,
            fuel_type="diesel",
            status="active"
        )
        
        with pytest.raises(ValidationError):
            vehicle2 = Vehicle(
                school=school,
                vehicle_number="UNIQUE001",  # Duplicate number
                license_plate="ABC456",
                make="Ford",
                model="Transit",
                year=2020,
                capacity=40,
                fuel_type="diesel",
                status="active"
            )
            vehicle2.full_clean()
    
    def test_vehicle_methods(self, vehicle):
        """Test vehicle methods"""
        assert vehicle.is_active() is True
        assert vehicle.get_age() >= 0
        assert vehicle.needs_maintenance() is False  # No maintenance date set
    
    def test_vehicle_maintenance(self, vehicle):
        """Test vehicle maintenance tracking"""
        # Set maintenance date in the past
        vehicle.last_maintenance_date = timezone.now().date() - timedelta(days=100)
        vehicle.next_maintenance_date = timezone.now().date() - timedelta(days=10)
        vehicle.save()
        
        assert vehicle.needs_maintenance() is True
        assert vehicle.is_maintenance_overdue() is True
    
    def test_vehicle_capacity_validation(self, school):
        """Test vehicle capacity validation"""
        with pytest.raises(ValidationError):
            vehicle = Vehicle(
                school=school,
                vehicle_number="INVALID001",
                license_plate="INV123",
                make="Test",
                model="Test",
                year=2020,
                capacity=0,  # Invalid capacity
                fuel_type="diesel",
                status="active"
            )
            vehicle.full_clean()


@pytest.mark.unit
class TestDriverModel:
    """Test Driver model"""
    
    def test_driver_creation(self, driver):
        """Test driver creation"""
        assert driver.license_number == "DL123456"
        assert driver.experience_years == 5
        assert driver.status == "active"
        assert str(driver) == f"{driver.employee.user.get_full_name()} (DL123456)"
    
    def test_driver_validation(self, school, admin_user):
        """Test driver validation"""
        employee = admin_user.employee
        
        # Test license expiry in the past
        with pytest.raises(ValidationError):
            driver = Driver(
                school=school,
                employee=employee,
                license_number="EXPIRED123",
                license_expiry=timezone.now().date() - timedelta(days=10),  # Expired
                experience_years=3,
                status="active"
            )
            driver.full_clean()
    
    def test_driver_methods(self, driver):
        """Test driver methods"""
        assert driver.is_active() is True
        assert driver.is_license_valid() is True
        assert driver.get_years_of_experience() == 5
    
    def test_driver_license_expiry(self, driver):
        """Test driver license expiry checking"""
        # Set license to expire soon
        driver.license_expiry = timezone.now().date() + timedelta(days=15)
        driver.save()
        
        assert driver.is_license_expiring_soon() is True
        assert driver.get_days_until_license_expiry() == 15


@pytest.mark.unit
class TestRouteModel:
    """Test Route model"""
    
    def test_route_creation(self, route):
        """Test route creation"""
        assert route.name == "Route A"
        assert route.code == "RT001"
        assert route.max_capacity == 40
        assert route.status == "active"
        assert str(route) == "Route A (RT001)"
    
    def test_route_validation(self, school, vehicle, driver):
        """Test route validation"""
        # Test duplicate code
        Route.objects.create(
            school=school,
            name="Route B",
            code="UNIQUE001",
            vehicle=vehicle,
            primary_driver=driver,
            max_capacity=35,
            status="active"
        )
        
        with pytest.raises(ValidationError):
            route2 = Route(
                school=school,
                name="Route C",
                code="UNIQUE001",  # Duplicate code
                vehicle=vehicle,
                primary_driver=driver,
                max_capacity=30,
                status="active"
            )
            route2.full_clean()
    
    def test_route_capacity_management(self, route, student):
        """Test route capacity management"""
        assert route.get_current_occupancy() == 0
        assert route.get_available_capacity() == 40
        assert route.is_full() is False
        
        # Add student to route
        StudentTransportation.objects.create(
            school=route.school,
            student=student,
            route=route,
            pickup_stop=None,  # Will create bus stop in separate test
            dropoff_stop=None,
            status="active"
        )
        
        route.refresh_from_db()
        assert route.get_current_occupancy() == 1
        assert route.get_available_capacity() == 39
    
    def test_route_methods(self, route):
        """Test route methods"""
        assert route.is_active() is True
        assert route.get_total_stops() == 0  # No stops added yet
        assert route.get_estimated_duration() is None  # Not set


@pytest.mark.unit
class TestBusStopModel:
    """Test BusStop model"""
    
    def test_bus_stop_creation(self, bus_stop):
        """Test bus stop creation"""
        assert bus_stop.name == "Main Street Stop"
        assert bus_stop.address == "123 Main Street"
        assert bus_stop.coordinates == "40.7128,-74.0060"
        assert bus_stop.status == "active"
        assert str(bus_stop) == "Main Street Stop"
    
    def test_bus_stop_validation(self, school):
        """Test bus stop validation"""
        # Test invalid coordinates format
        with pytest.raises(ValidationError):
            bus_stop = BusStop(
                school=school,
                name="Invalid Stop",
                address="456 Test Street",
                coordinates="invalid-coordinates",  # Invalid format
                status="active"
            )
            bus_stop.full_clean()
    
    def test_bus_stop_methods(self, bus_stop):
        """Test bus stop methods"""
        assert bus_stop.is_active() is True
        
        # Test coordinate parsing
        lat, lng = bus_stop.get_coordinates()
        assert lat == 40.7128
        assert lng == -74.0060
    
    def test_bus_stop_routes(self, bus_stop, route):
        """Test bus stop route associations"""
        # Add stop to route
        RouteStop.objects.create(
            route=route,
            bus_stop=bus_stop,
            sequence_order=1,
            estimated_arrival_time_morning="08:00",
            estimated_arrival_time_afternoon="15:00"
        )
        
        assert bus_stop.get_routes().count() == 1
        assert route in bus_stop.get_routes()


@pytest.mark.unit
class TestRouteStopModel:
    """Test RouteStop model"""
    
    def test_route_stop_creation(self, route, bus_stop):
        """Test route stop creation"""
        route_stop = RouteStop.objects.create(
            route=route,
            bus_stop=bus_stop,
            sequence_order=1,
            estimated_arrival_time_morning="08:00",
            estimated_arrival_time_afternoon="15:00"
        )
        
        assert route_stop.route == route
        assert route_stop.bus_stop == bus_stop
        assert route_stop.sequence_order == 1
        assert str(route_stop) == "Route A - Main Street Stop (1)"
    
    def test_route_stop_validation(self, route, bus_stop):
        """Test route stop validation"""
        # Create first route stop
        RouteStop.objects.create(
            route=route,
            bus_stop=bus_stop,
            sequence_order=1,
            estimated_arrival_time_morning="08:00",
            estimated_arrival_time_afternoon="15:00"
        )
        
        # Try to create duplicate sequence order
        with pytest.raises(ValidationError):
            route_stop2 = RouteStop(
                route=route,
                bus_stop=bus_stop,
                sequence_order=1,  # Duplicate sequence
                estimated_arrival_time_morning="08:30",
                estimated_arrival_time_afternoon="15:30"
            )
            route_stop2.full_clean()
    
    def test_route_stop_ordering(self, route, school):
        """Test route stop ordering"""
        # Create multiple bus stops
        stop1 = BusStop.objects.create(
            school=school,
            name="Stop 1",
            address="Address 1",
            coordinates="40.7128,-74.0060",
            status="active"
        )
        
        stop2 = BusStop.objects.create(
            school=school,
            name="Stop 2",
            address="Address 2",
            coordinates="40.7129,-74.0061",
            status="active"
        )
        
        # Create route stops in reverse order
        RouteStop.objects.create(
            route=route,
            bus_stop=stop2,
            sequence_order=2,
            estimated_arrival_time_morning="08:15"
        )
        
        RouteStop.objects.create(
            route=route,
            bus_stop=stop1,
            sequence_order=1,
            estimated_arrival_time_morning="08:00"
        )
        
        # Get ordered stops
        ordered_stops = route.get_ordered_stops()
        assert ordered_stops[0].bus_stop == stop1
        assert ordered_stops[1].bus_stop == stop2


@pytest.mark.unit
class TestStudentTransportationModel:
    """Test StudentTransportation model"""
    
    def test_student_transportation_creation(self, student, route, bus_stop):
        """Test student transportation creation"""
        student_transport = StudentTransportation.objects.create(
            school=student.school,
            student=student,
            route=route,
            pickup_stop=bus_stop,
            dropoff_stop=bus_stop,
            status="active"
        )
        
        assert student_transport.student == student
        assert student_transport.route == route
        assert student_transport.pickup_stop == bus_stop
        assert student_transport.status == "active"
        assert str(student_transport) == "Test Student - Route A"
    
    def test_student_transportation_validation(self, student, route, bus_stop):
        """Test student transportation validation"""
        # Create first assignment
        StudentTransportation.objects.create(
            school=student.school,
            student=student,
            route=route,
            pickup_stop=bus_stop,
            dropoff_stop=bus_stop,
            status="active"
        )
        
        # Try to create duplicate active assignment
        with pytest.raises(ValidationError):
            student_transport2 = StudentTransportation(
                school=student.school,
                student=student,
                route=route,
                pickup_stop=bus_stop,
                dropoff_stop=bus_stop,
                status="active"  # Duplicate active assignment
            )
            student_transport2.full_clean()
    
    def test_student_transportation_methods(self, student, route, bus_stop):
        """Test student transportation methods"""
        student_transport = StudentTransportation.objects.create(
            school=student.school,
            student=student,
            route=route,
            pickup_stop=bus_stop,
            dropoff_stop=bus_stop,
            status="active"
        )
        
        assert student_transport.is_active() is True
        assert student_transport.get_pickup_time() is not None
        assert student_transport.get_dropoff_time() is not None


@pytest.mark.unit
class TestGPSTrackingModel:
    """Test GPSTracking model"""
    
    def test_gps_tracking_creation(self, vehicle):
        """Test GPS tracking creation"""
        gps_tracking = GPSTracking.objects.create(
            vehicle=vehicle,
            latitude=40.7128,
            longitude=-74.0060,
            speed_kmh=25.5,
            timestamp=timezone.now(),
            engine_status="running"
        )
        
        assert gps_tracking.vehicle == vehicle
        assert gps_tracking.latitude == 40.7128
        assert gps_tracking.longitude == -74.0060
        assert gps_tracking.speed_kmh == 25.5
        assert gps_tracking.engine_status == "running"
        assert str(gps_tracking) == f"BUS001 - {gps_tracking.timestamp}"
    
    def test_gps_tracking_validation(self, vehicle):
        """Test GPS tracking validation"""
        # Test invalid latitude
        with pytest.raises(ValidationError):
            gps_tracking = GPSTracking(
                vehicle=vehicle,
                latitude=100.0,  # Invalid latitude (> 90)
                longitude=-74.0060,
                speed_kmh=25.5,
                timestamp=timezone.now(),
                engine_status="running"
            )
            gps_tracking.full_clean()
        
        # Test invalid longitude
        with pytest.raises(ValidationError):
            gps_tracking = GPSTracking(
                vehicle=vehicle,
                latitude=40.7128,
                longitude=200.0,  # Invalid longitude (> 180)
                speed_kmh=25.5,
                timestamp=timezone.now(),
                engine_status="running"
            )
            gps_tracking.full_clean()
    
    def test_gps_tracking_methods(self, vehicle):
        """Test GPS tracking methods"""
        gps_tracking = GPSTracking.objects.create(
            vehicle=vehicle,
            latitude=40.7128,
            longitude=-74.0060,
            speed_kmh=25.5,
            timestamp=timezone.now(),
            engine_status="running"
        )
        
        coordinates = gps_tracking.get_coordinates()
        assert coordinates == (40.7128, -74.0060)
        
        assert gps_tracking.is_moving() is True  # Speed > 0
        assert gps_tracking.is_engine_running() is True


@pytest.mark.unit
class TestTransportationAttendanceModel:
    """Test TransportationAttendance model"""
    
    def test_attendance_creation(self, student, route, bus_stop, driver):
        """Test transportation attendance creation"""
        student_transport = StudentTransportation.objects.create(
            school=student.school,
            student=student,
            route=route,
            pickup_stop=bus_stop,
            dropoff_stop=bus_stop,
            status="active"
        )
        
        attendance = TransportationAttendance.objects.create(
            school=student.school,
            student_transportation=student_transport,
            bus_stop=bus_stop,
            date=timezone.now().date(),
            attendance_type="pickup",
            status="present",
            actual_time="08:05",
            driver=driver
        )
        
        assert attendance.student_transportation == student_transport
        assert attendance.attendance_type == "pickup"
        assert attendance.status == "present"
        assert str(attendance) == f"Test Student - Main Street Stop - {timezone.now().date()}"
    
    def test_attendance_validation(self, student, route, bus_stop, driver):
        """Test transportation attendance validation"""
        student_transport = StudentTransportation.objects.create(
            school=student.school,
            student=student,
            route=route,
            pickup_stop=bus_stop,
            dropoff_stop=bus_stop,
            status="active"
        )
        
        # Create first attendance record
        TransportationAttendance.objects.create(
            school=student.school,
            student_transportation=student_transport,
            bus_stop=bus_stop,
            date=timezone.now().date(),
            attendance_type="pickup",
            status="present",
            actual_time="08:05",
            driver=driver
        )
        
        # Try to create duplicate attendance for same day and type
        with pytest.raises(ValidationError):
            attendance2 = TransportationAttendance(
                school=student.school,
                student_transportation=student_transport,
                bus_stop=bus_stop,
                date=timezone.now().date(),  # Same date
                attendance_type="pickup",  # Same type
                status="absent",
                driver=driver
            )
            attendance2.full_clean()


@pytest.mark.unit
class TestTransportationFeeModel:
    """Test TransportationFee model"""
    
    def test_transportation_fee_creation(self, student, route, bus_stop):
        """Test transportation fee creation"""
        student_transport = StudentTransportation.objects.create(
            school=student.school,
            student=student,
            route=route,
            pickup_stop=bus_stop,
            dropoff_stop=bus_stop,
            status="active"
        )
        
        fee = TransportationFee.objects.create(
            school=student.school,
            student_transportation=student_transport,
            month=timezone.now().date().replace(day=1),
            amount=Decimal('150.00'),
            paid_amount=Decimal('0.00'),
            due_date=timezone.now().date() + timedelta(days=15),
            status="pending"
        )
        
        assert fee.student_transportation == student_transport
        assert fee.amount == Decimal('150.00')
        assert fee.status == "pending"
        assert str(fee) == f"Test Student - Route A - {fee.month.strftime('%B %Y')}"
    
    def test_fee_payment_tracking(self, student, route, bus_stop):
        """Test fee payment tracking"""
        student_transport = StudentTransportation.objects.create(
            school=student.school,
            student=student,
            route=route,
            pickup_stop=bus_stop,
            dropoff_stop=bus_stop,
            status="active"
        )
        
        fee = TransportationFee.objects.create(
            school=student.school,
            student_transportation=student_transport,
            month=timezone.now().date().replace(day=1),
            amount=Decimal('150.00'),
            paid_amount=Decimal('75.00'),
            due_date=timezone.now().date() + timedelta(days=15),
            status="partial"
        )
        
        assert fee.get_outstanding_amount() == Decimal('75.00')
        assert fee.get_payment_percentage() == 50.0
        assert fee.is_overdue() is False
    
    def test_fee_validation(self, student, route, bus_stop):
        """Test transportation fee validation"""
        student_transport = StudentTransportation.objects.create(
            school=student.school,
            student=student,
            route=route,
            pickup_stop=bus_stop,
            dropoff_stop=bus_stop,
            status="active"
        )
        
        # Test paid amount exceeding total amount
        with pytest.raises(ValidationError):
            fee = TransportationFee(
                school=student.school,
                student_transportation=student_transport,
                month=timezone.now().date().replace(day=1),
                amount=Decimal('150.00'),
                paid_amount=Decimal('200.00'),  # Exceeds total amount
                due_date=timezone.now().date() + timedelta(days=15),
                status="paid"
            )
            fee.full_clean()