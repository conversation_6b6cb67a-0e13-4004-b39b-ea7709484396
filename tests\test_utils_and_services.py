"""
Unit tests for utility functions and services
"""
import pytest
from django.test import TestCase
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
from unittest.mock import Mock, patch

from core.utils import generate_unique_id, validate_phone_number, format_currency
from finance.services import FeeCalculationService, PaymentProcessingService
from transportation.services import RouteOptimizationService, GPSTrackingService
from communications.services import NotificationService, EmailService


@pytest.mark.unit
class TestCoreUtils:
    """Test core utility functions"""
    
    def test_generate_unique_id(self):
        """Test unique ID generation"""
        # Test default length
        id1 = generate_unique_id()
        assert len(id1) == 8
        assert id1.isalnum()
        
        # Test custom length
        id2 = generate_unique_id(length=12)
        assert len(id2) == 12
        
        # Test custom prefix
        id3 = generate_unique_id(prefix="STU")
        assert id3.startswith("STU")
        assert len(id3) == 11  # 3 (prefix) + 8 (default)
        
        # Test uniqueness
        id4 = generate_unique_id()
        assert id1 != id4
    
    def test_validate_phone_number(self):
        """Test phone number validation"""
        # Valid phone numbers
        assert validate_phone_number("************") is True
        assert validate_phone_number("+1-************") is True
        assert validate_phone_number("(*************") is True
        assert validate_phone_number("1234567890") is True
        
        # Invalid phone numbers
        assert validate_phone_number("123-456") is False
        assert validate_phone_number("abc-def-ghij") is False
        assert validate_phone_number("") is False
        assert validate_phone_number(None) is False
    
    def test_format_currency(self):
        """Test currency formatting"""
        # Test USD (default)
        assert format_currency(1000) == "$1,000.00"
        assert format_currency(1234.56) == "$1,234.56"
        assert format_currency(0) == "$0.00"
        
        # Test different currency
        assert format_currency(1000, currency="EUR") == "€1,000.00"
        
        # Test negative amounts
        assert format_currency(-500) == "-$500.00"


@pytest.mark.unit
class TestFeeCalculationService:
    """Test fee calculation service"""
    
    def test_calculate_total_fees(self, fee_structure):
        """Test total fee calculation"""
        service = FeeCalculationService()
        
        total = service.calculate_total_fees(fee_structure)
        expected = fee_structure.tuition_fee + fee_structure.registration_fee + fee_structure.activity_fee
        
        assert total == expected
    
    def test_calculate_late_fees(self, student, fee_structure):
        """Test late fee calculation"""
        from finance.models import StudentFee
        
        service = FeeCalculationService()
        
        # Create overdue fee
        student_fee = StudentFee.objects.create(
            school=student.school,
            student=student,
            fee_structure=fee_structure,
            academic_year=fee_structure.academic_year,
            total_amount=Decimal('1150.00'),
            paid_amount=Decimal('0.00'),
            due_date=timezone.now().date() - timedelta(days=10),  # 10 days overdue
            status="pending"
        )
        
        late_fee = service.calculate_late_fees(student_fee)
        assert late_fee > 0  # Should have late fee
    
    def test_calculate_discount(self, fee_structure):
        """Test discount calculation"""
        service = FeeCalculationService()
        
        # Test percentage discount
        discount = service.calculate_discount(fee_structure.get_total_fee(), discount_type="percentage", discount_value=10)
        expected_discount = fee_structure.get_total_fee() * Decimal('0.10')
        assert discount == expected_discount
        
        # Test fixed amount discount
        discount = service.calculate_discount(fee_structure.get_total_fee(), discount_type="fixed", discount_value=100)
        assert discount == Decimal('100.00')


@pytest.mark.unit
class TestPaymentProcessingService:
    """Test payment processing service"""
    
    @patch('finance.services.PaymentGateway')
    def test_process_payment(self, mock_gateway, student, fee_structure):
        """Test payment processing"""
        from finance.models import StudentFee
        
        service = PaymentProcessingService()
        
        # Create student fee
        student_fee = StudentFee.objects.create(
            school=student.school,
            student=student,
            fee_structure=fee_structure,
            academic_year=fee_structure.academic_year,
            total_amount=Decimal('1150.00'),
            paid_amount=Decimal('0.00'),
            due_date=timezone.now().date() + timedelta(days=30),
            status="pending"
        )
        
        # Mock successful payment
        mock_gateway.return_value.process_payment.return_value = {
            'success': True,
            'transaction_id': 'TXN123456',
            'amount': Decimal('500.00')
        }
        
        result = service.process_payment(
            student_fee=student_fee,
            amount=Decimal('500.00'),
            payment_method='credit_card',
            card_details={'number': '****1234', 'cvv': '***'}
        )
        
        assert result['success'] is True
        assert result['transaction_id'] == 'TXN123456'
    
    def test_validate_payment_amount(self, student, fee_structure):
        """Test payment amount validation"""
        from finance.models import StudentFee
        
        service = PaymentProcessingService()
        
        student_fee = StudentFee.objects.create(
            school=student.school,
            student=student,
            fee_structure=fee_structure,
            academic_year=fee_structure.academic_year,
            total_amount=Decimal('1150.00'),
            paid_amount=Decimal('500.00'),
            due_date=timezone.now().date() + timedelta(days=30),
            status="partial"
        )
        
        # Valid payment amount
        assert service.validate_payment_amount(student_fee, Decimal('650.00')) is True
        assert service.validate_payment_amount(student_fee, Decimal('100.00')) is True
        
        # Invalid payment amounts
        assert service.validate_payment_amount(student_fee, Decimal('0.00')) is False
        assert service.validate_payment_amount(student_fee, Decimal('1000.00')) is False  # Exceeds outstanding


@pytest.mark.unit
class TestRouteOptimizationService:
    """Test route optimization service"""
    
    def test_calculate_route_distance(self, route, bus_stop, school):
        """Test route distance calculation"""
        from transportation.models import RouteStop
        
        service = RouteOptimizationService()
        
        # Create route stops
        stop1 = bus_stop
        
        stop2 = BusStop.objects.create(
            school=school,
            name="Second Stop",
            address="456 Second Street",
            coordinates="40.7129,-74.0061",
            status="active"
        )
        
        RouteStop.objects.create(
            route=route,
            bus_stop=stop1,
            sequence_order=1
        )
        
        RouteStop.objects.create(
            route=route,
            bus_stop=stop2,
            sequence_order=2
        )
        
        distance = service.calculate_route_distance(route)
        assert distance > 0  # Should have some distance
    
    def test_optimize_stop_sequence(self, route, school):
        """Test stop sequence optimization"""
        from transportation.models import BusStop, RouteStop
        
        service = RouteOptimizationService()
        
        # Create multiple stops
        stops = []
        for i in range(3):
            stop = BusStop.objects.create(
                school=school,
                name=f"Stop {i+1}",
                address=f"{i+1}00 Street",
                coordinates=f"40.71{28+i},-74.00{60+i}",
                status="active"
            )
            stops.append(stop)
            
            RouteStop.objects.create(
                route=route,
                bus_stop=stop,
                sequence_order=i+1
            )
        
        original_sequence = [rs.sequence_order for rs in route.route_stops.all().order_by('sequence_order')]
        
        optimized_route = service.optimize_stop_sequence(route)
        
        # Should return optimized route data
        assert 'optimized_sequence' in optimized_route
        assert 'distance_saved' in optimized_route


@pytest.mark.unit
class TestGPSTrackingService:
    """Test GPS tracking service"""
    
    def test_update_vehicle_location(self, vehicle):
        """Test vehicle location update"""
        service = GPSTrackingService()
        
        location_data = {
            'latitude': 40.7128,
            'longitude': -74.0060,
            'speed_kmh': 25.5,
            'engine_status': 'running'
        }
        
        tracking = service.update_vehicle_location(vehicle, location_data)
        
        assert tracking.vehicle == vehicle
        assert tracking.latitude == 40.7128
        assert tracking.longitude == -74.0060
        assert tracking.speed_kmh == 25.5
    
    def test_get_vehicle_current_location(self, vehicle):
        """Test getting current vehicle location"""
        from transportation.models import GPSTracking
        
        service = GPSTrackingService()
        
        # Create GPS tracking record
        GPSTracking.objects.create(
            vehicle=vehicle,
            latitude=40.7128,
            longitude=-74.0060,
            speed_kmh=30.0,
            timestamp=timezone.now(),
            engine_status="running"
        )
        
        current_location = service.get_vehicle_current_location(vehicle)
        
        assert current_location is not None
        assert current_location.latitude == 40.7128
        assert current_location.longitude == -74.0060
    
    def test_calculate_distance_between_points(self):
        """Test distance calculation between GPS points"""
        service = GPSTrackingService()
        
        # Test distance between two known points
        point1 = (40.7128, -74.0060)  # New York
        point2 = (40.7589, -73.9851)  # Also New York, different location
        
        distance = service.calculate_distance_between_points(point1, point2)
        
        assert distance > 0  # Should have some distance
        assert distance < 100  # Should be reasonable distance in km


@pytest.mark.unit
class TestNotificationService:
    """Test notification service"""
    
    @patch('communications.services.send_email')
    def test_send_email_notification(self, mock_send_email, student):
        """Test email notification sending"""
        service = NotificationService()
        
        mock_send_email.return_value = True
        
        result = service.send_email_notification(
            recipient=student.parent.email,
            subject="Test Notification",
            message="This is a test notification",
            template="notification_email.html"
        )
        
        assert result is True
        mock_send_email.assert_called_once()
    
    @patch('communications.services.send_sms')
    def test_send_sms_notification(self, mock_send_sms, student):
        """Test SMS notification sending"""
        service = NotificationService()
        
        mock_send_sms.return_value = {'success': True, 'message_id': 'SMS123'}
        
        result = service.send_sms_notification(
            phone_number=student.parent.phone,
            message="Test SMS notification"
        )
        
        assert result['success'] is True
        assert 'message_id' in result
        mock_send_sms.assert_called_once()
    
    def test_create_notification_template(self):
        """Test notification template creation"""
        service = NotificationService()
        
        template_data = {
            'name': 'fee_reminder',
            'subject': 'Fee Payment Reminder',
            'content': 'Dear {parent_name}, your child {student_name} has pending fees.',
            'variables': ['parent_name', 'student_name', 'amount']
        }
        
        template = service.create_notification_template(template_data)
        
        assert template['name'] == 'fee_reminder'
        assert '{parent_name}' in template['content']
        assert 'parent_name' in template['variables']


@pytest.mark.unit
class TestEmailService:
    """Test email service"""
    
    @patch('smtplib.SMTP')
    def test_send_email(self, mock_smtp):
        """Test email sending"""
        service = EmailService()
        
        # Mock SMTP server
        mock_server = Mock()
        mock_smtp.return_value.__enter__.return_value = mock_server
        
        result = service.send_email(
            to_email="<EMAIL>",
            subject="Test Email",
            message="This is a test email",
            from_email="<EMAIL>"
        )
        
        assert result is True
        mock_server.send_message.assert_called_once()
    
    def test_validate_email_address(self):
        """Test email address validation"""
        service = EmailService()
        
        # Valid email addresses
        assert service.validate_email_address("<EMAIL>") is True
        assert service.validate_email_address("<EMAIL>") is True
        assert service.validate_email_address("<EMAIL>") is True
        
        # Invalid email addresses
        assert service.validate_email_address("invalid-email") is False
        assert service.validate_email_address("@example.com") is False
        assert service.validate_email_address("test@") is False
        assert service.validate_email_address("") is False
    
    def test_format_email_template(self):
        """Test email template formatting"""
        service = EmailService()
        
        template = "Hello {name}, your balance is {amount}."
        variables = {'name': 'John Doe', 'amount': '$150.00'}
        
        formatted = service.format_email_template(template, variables)
        
        assert formatted == "Hello John Doe, your balance is $150.00."


@pytest.mark.unit
class TestBusinessLogicValidation:
    """Test business logic validation"""
    
    def test_student_enrollment_capacity(self, grade, student):
        """Test student enrollment capacity validation"""
        from students.models import StudentEnrollment
        
        # Set grade capacity to 1
        grade.capacity = 1
        grade.save()
        
        # Enroll first student (should succeed)
        enrollment1 = StudentEnrollment.objects.create(
            school=grade.school,
            student=student,
            academic_year=grade.academic_year,
            grade=grade,
            enrollment_date=timezone.now().date(),
            status="active"
        )
        
        assert enrollment1.id is not None
        
        # Try to enroll second student (should fail due to capacity)
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        user2 = User.objects.create_user(
            username="parent2",
            email="<EMAIL>",
            password="testpass123"
        )
        
        from students.models import Parent
        parent2 = Parent.objects.create(
            school=grade.school,
            user=user2,
            father_name="Father 2",
            mother_name="Mother 2",
            phone="************",
            email="<EMAIL>"
        )
        
        from students.models import Student
        student2 = Student.objects.create(
            school=grade.school,
            student_id="STU002",
            first_name="Student",
            last_name="Two",
            date_of_birth=datetime(2010, 1, 1).date(),
            gender="female",
            nationality="US",
            parent=parent2,
            admission_date=timezone.now().date(),
            status="active"
        )
        
        with pytest.raises(ValidationError):
            enrollment2 = StudentEnrollment(
                school=grade.school,
                student=student2,
                academic_year=grade.academic_year,
                grade=grade,
                enrollment_date=timezone.now().date(),
                status="active"
            )
            enrollment2.full_clean()
    
    def test_fee_payment_validation(self, student, fee_structure):
        """Test fee payment business logic validation"""
        from finance.models import StudentFee, Payment
        
        # Create student fee
        student_fee = StudentFee.objects.create(
            school=student.school,
            student=student,
            fee_structure=fee_structure,
            academic_year=fee_structure.academic_year,
            total_amount=Decimal('1000.00'),
            paid_amount=Decimal('0.00'),
            due_date=timezone.now().date() + timedelta(days=30),
            status="pending"
        )
        
        # Valid payment
        payment1 = Payment.objects.create(
            school=student.school,
            student_fee=student_fee,
            amount=Decimal('500.00'),
            payment_method="cash",
            payment_date=timezone.now().date(),
            reference_number="PAY001",
            status="completed"
        )
        
        assert payment1.id is not None
        
        # Try to pay more than outstanding amount
        with pytest.raises(ValidationError):
            payment2 = Payment(
                school=student.school,
                student_fee=student_fee,
                amount=Decimal('600.00'),  # Only 500 remaining
                payment_method="cash",
                payment_date=timezone.now().date(),
                reference_number="PAY002",
                status="completed"
            )
            payment2.full_clean()