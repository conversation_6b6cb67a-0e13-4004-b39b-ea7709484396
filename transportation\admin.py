from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import (
    Vehicle, Driver, Route, BusStop, RouteStop, StudentTransportation,
    RouteOptimization, GPSTracking, TransportationAnalytics,
    TransportationAttendance, TransportationFee, ParentNotification
)


@admin.register(Vehicle)
class VehicleAdmin(admin.ModelAdmin):
    list_display = [
        'vehicle_number', 'license_plate', 'vehicle_type', 'capacity', 
        'status', 'maintenance_status', 'insurance_status'
    ]
    list_filter = ['vehicle_type', 'status', 'fuel_type', 'school']
    search_fields = ['vehicle_number', 'license_plate', 'make', 'model']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('school', 'vehicle_number', 'license_plate', 'vehicle_type')
        }),
        (_('Vehicle Details'), {
            'fields': ('make', 'model', 'year', 'capacity', 'fuel_type')
        }),
        (_('Status & Dates'), {
            'fields': ('status', 'purchase_date', 'insurance_expiry', 'registration_expiry')
        }),
        (_('Maintenance'), {
            'fields': ('last_maintenance_date', 'next_maintenance_date')
        }),
        (_('GPS & Tracking'), {
            'fields': ('gps_device_id',)
        }),
        (_('Additional Information'), {
            'fields': ('notes',)
        }),
        (_('System Information'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def maintenance_status(self, obj):
        if obj.is_maintenance_due:
            return format_html(
                '<span style="color: red;">⚠️ Due</span>'
            )
        return format_html('<span style="color: green;">✓ OK</span>')
    maintenance_status.short_description = _('Maintenance')
    
    def insurance_status(self, obj):
        if obj.is_insurance_expiring_soon:
            return format_html(
                '<span style="color: orange;">⚠️ Expiring Soon</span>'
            )
        return format_html('<span style="color: green;">✓ Valid</span>')
    insurance_status.short_description = _('Insurance')


@admin.register(Driver)
class DriverAdmin(admin.ModelAdmin):
    list_display = [
        'full_name', 'license_number', 'license_type', 'status', 
        'license_status', 'experience_years'
    ]
    list_filter = ['status', 'license_type', 'school']
    search_fields = ['employee__first_name', 'employee__last_name', 'license_number']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('school', 'employee')
        }),
        (_('License Information'), {
            'fields': ('license_number', 'license_type', 'license_expiry', 'medical_certificate_expiry')
        }),
        (_('Experience & Status'), {
            'fields': ('experience_years', 'status')
        }),
        (_('Emergency Contact'), {
            'fields': ('emergency_contact_name', 'emergency_contact_phone')
        }),
        (_('Additional Information'), {
            'fields': ('notes',)
        }),
        (_('System Information'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def full_name(self, obj):
        return obj.full_name
    full_name.short_description = _('Name')
    
    def license_status(self, obj):
        if obj.is_license_expiring_soon:
            return format_html(
                '<span style="color: orange;">⚠️ Expiring Soon</span>'
            )
        return format_html('<span style="color: green;">✓ Valid</span>')
    license_status.short_description = _('License Status')


class RouteStopInline(admin.TabularInline):
    model = RouteStop
    extra = 0
    fields = [
        'bus_stop', 'sequence_order', 'estimated_arrival_time_morning',
        'estimated_arrival_time_afternoon', 'distance_from_previous_km',
        'is_pickup_point', 'is_dropoff_point'
    ]
    ordering = ['sequence_order']


@admin.register(Route)
class RouteAdmin(admin.ModelAdmin):
    list_display = [
        'code', 'name', 'route_type', 'status', 'occupancy_display',
        'vehicle', 'primary_driver', 'total_distance_km'
    ]
    list_filter = ['route_type', 'status', 'school']
    search_fields = ['code', 'name', 'name_ar']
    readonly_fields = ['created_at', 'updated_at', 'current_occupancy']
    inlines = [RouteStopInline]
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('school', 'name', 'name_ar', 'code', 'route_type')
        }),
        (_('Assignment'), {
            'fields': ('vehicle', 'primary_driver', 'backup_driver')
        }),
        (_('Schedule'), {
            'fields': ('start_time_morning', 'start_time_afternoon', 'estimated_duration_minutes')
        }),
        (_('Capacity & Occupancy'), {
            'fields': ('max_capacity', 'current_occupancy')
        }),
        (_('Route Details'), {
            'fields': ('total_distance_km', 'monthly_fee', 'status')
        }),
        (_('Optimization Data'), {
            'fields': ('route_coordinates', 'optimization_data'),
            'classes': ('collapse',)
        }),
        (_('Additional Information'), {
            'fields': ('notes',)
        }),
        (_('System Information'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def occupancy_display(self, obj):
        percentage = obj.occupancy_percentage
        if percentage >= 90:
            color = 'red'
        elif percentage >= 70:
            color = 'orange'
        else:
            color = 'green'
        
        return format_html(
            '<span style="color: {};">{}/{} ({}%)</span>',
            color, obj.current_occupancy, obj.max_capacity, int(percentage)
        )
    occupancy_display.short_description = _('Occupancy')
    
    actions = ['optimize_routes']
    
    def optimize_routes(self, request, queryset):
        from .services import RouteOptimizationService
        service = RouteOptimizationService()
        
        optimized_count = 0
        for route in queryset:
            try:
                service.optimize_route(route, 'nearest_neighbor')
                optimized_count += 1
            except Exception:
                pass
        
        self.message_user(request, f'Optimized {optimized_count} routes successfully.')
    optimize_routes.short_description = _('Optimize selected routes')


@admin.register(BusStop)
class BusStopAdmin(admin.ModelAdmin):
    list_display = [
        'code', 'name', 'status', 'coordinates_display', 
        'safety_rating', 'routes_count'
    ]
    list_filter = ['status', 'safety_rating', 'school']
    search_fields = ['code', 'name', 'name_ar', 'address']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('school', 'name', 'name_ar', 'code')
        }),
        (_('Location'), {
            'fields': ('address', 'latitude', 'longitude', 'landmark')
        }),
        (_('Properties'), {
            'fields': ('status', 'safety_rating', 'accessibility_features')
        }),
        (_('Additional Information'), {
            'fields': ('notes',)
        }),
        (_('System Information'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def coordinates_display(self, obj):
        if obj.coordinates:
            lat, lng = obj.coordinates
            return format_html(
                '<a href="{}" target="_blank">{:.6f}, {:.6f}</a>',
                obj.google_maps_url, lat, lng
            )
        return '-'
    coordinates_display.short_description = _('Coordinates')
    
    def routes_count(self, obj):
        count = obj.route_stops.count()
        if count > 0:
            return format_html(
                '<a href="{}?bus_stop__id__exact={}">{} routes</a>',
                reverse('admin:transportation_routestop_changelist'),
                obj.id, count
            )
        return '0 routes'
    routes_count.short_description = _('Routes')


@admin.register(StudentTransportation)
class StudentTransportationAdmin(admin.ModelAdmin):
    list_display = [
        'student', 'route', 'pickup_stop', 'dropoff_stop', 
        'status', 'monthly_fee', 'start_date'
    ]
    list_filter = ['status', 'route', 'school']
    search_fields = [
        'student__first_name', 'student__last_name', 
        'route__name', 'route__code'
    ]
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        (_('Assignment'), {
            'fields': ('school', 'student', 'route')
        }),
        (_('Stops'), {
            'fields': ('pickup_stop', 'dropoff_stop')
        }),
        (_('Status & Dates'), {
            'fields': ('status', 'start_date', 'end_date')
        }),
        (_('Financial'), {
            'fields': ('monthly_fee',)
        }),
        (_('Emergency Contact'), {
            'fields': ('emergency_contact_name', 'emergency_contact_phone')
        }),
        (_('Special Requirements'), {
            'fields': ('special_needs', 'notes')
        }),
        (_('System Information'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(RouteOptimization)
class RouteOptimizationAdmin(admin.ModelAdmin):
    list_display = [
        'route', 'optimization_type', 'status', 'savings_display',
        'processed_at'
    ]
    list_filter = ['optimization_type', 'status', 'school']
    readonly_fields = [
        'created_at', 'updated_at', 'processed_at', 'distance_savings_km',
        'time_savings_minutes'
    ]
    
    fieldsets = (
        (_('Optimization Details'), {
            'fields': ('school', 'route', 'optimization_type', 'status')
        }),
        (_('Original Metrics'), {
            'fields': ('original_distance_km', 'original_duration_minutes')
        }),
        (_('Optimized Metrics'), {
            'fields': ('optimized_distance_km', 'optimized_duration_minutes')
        }),
        (_('Savings'), {
            'fields': ('fuel_savings_percentage', 'time_savings_percentage', 'distance_savings_km', 'time_savings_minutes')
        }),
        (_('Technical Data'), {
            'fields': ('optimization_parameters', 'optimization_results'),
            'classes': ('collapse',)
        }),
        (_('Processing'), {
            'fields': ('processed_at', 'error_message')
        }),
        (_('System Information'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def savings_display(self, obj):
        if obj.status == 'completed' and obj.distance_savings_km:
            return format_html(
                '<span style="color: green;">-{:.1f}km ({:.1f}%)</span>',
                obj.distance_savings_km, obj.fuel_savings_percentage or 0
            )
        return '-'
    savings_display.short_description = _('Savings')


@admin.register(GPSTracking)
class GPSTrackingAdmin(admin.ModelAdmin):
    list_display = [
        'vehicle', 'route', 'coordinates_display', 'speed_kmh',
        'timestamp', 'engine_status'
    ]
    list_filter = ['vehicle', 'route', 'engine_status', 'timestamp']
    readonly_fields = ['created_at', 'updated_at', 'google_maps_url']
    date_hierarchy = 'timestamp'
    
    fieldsets = (
        (_('Vehicle & Route'), {
            'fields': ('school', 'vehicle', 'route')
        }),
        (_('Location Data'), {
            'fields': ('latitude', 'longitude', 'altitude', 'accuracy')
        }),
        (_('Movement Data'), {
            'fields': ('speed_kmh', 'heading', 'timestamp')
        }),
        (_('Vehicle Status'), {
            'fields': ('engine_status', 'fuel_level_percentage')
        }),
        (_('Additional Data'), {
            'fields': ('additional_data',),
            'classes': ('collapse',)
        }),
        (_('System Information'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def coordinates_display(self, obj):
        return format_html(
            '<a href="{}" target="_blank">{:.6f}, {:.6f}</a>',
            obj.google_maps_url, obj.latitude, obj.longitude
        )
    coordinates_display.short_description = _('Coordinates')


@admin.register(TransportationAnalytics)
class TransportationAnalyticsAdmin(admin.ModelAdmin):
    list_display = [
        'metric_type', 'route', 'vehicle', 'date_range',
        'efficiency_score', 'on_time_percentage'
    ]
    list_filter = ['metric_type', 'route', 'vehicle', 'school']
    readonly_fields = ['created_at', 'updated_at', 'cost_per_km', 'cost_per_student']
    date_hierarchy = 'date_to'
    
    fieldsets = (
        (_('Analytics Details'), {
            'fields': ('school', 'metric_type', 'route', 'vehicle')
        }),
        (_('Date Range'), {
            'fields': ('date_from', 'date_to')
        }),
        (_('Distance & Time'), {
            'fields': ('total_distance_km', 'total_duration_minutes', 'average_speed_kmh')
        }),
        (_('Costs'), {
            'fields': ('fuel_consumed_liters', 'fuel_cost', 'maintenance_cost', 'cost_per_km', 'cost_per_student')
        }),
        (_('Performance'), {
            'fields': ('students_transported', 'on_time_percentage', 'efficiency_score')
        }),
        (_('Detailed Data'), {
            'fields': ('metrics_data',),
            'classes': ('collapse',)
        }),
        (_('System Information'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def date_range(self, obj):
        return f"{obj.date_from} to {obj.date_to}"
    date_range.short_description = _('Date Range')


@admin.register(TransportationAttendance)
class TransportationAttendanceAdmin(admin.ModelAdmin):
    list_display = [
        'student_transportation', 'date', 'attendance_type', 'status',
        'scheduled_time', 'actual_time', 'bus_stop'
    ]
    list_filter = ['date', 'attendance_type', 'status']
    search_fields = [
        'student_transportation__student__first_name',
        'student_transportation__student__last_name',
        'bus_stop__name'
    ]
    readonly_fields = ['created_at', 'updated_at', 'is_on_time', 'delay_minutes']
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('student_transportation', 'date', 'attendance_type', 'status')
        }),
        (_('Time Information'), {
            'fields': ('scheduled_time', 'actual_time', 'is_on_time', 'delay_minutes')
        }),
        (_('Location Information'), {
            'fields': ('bus_stop', 'driver', 'gps_location')
        }),
        (_('Additional Information'), {
            'fields': ('notes',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(TransportationFee)
class TransportationFeeAdmin(admin.ModelAdmin):
    list_display = [
        'student_transportation', 'month', 'calculation_type', 'total_fee',
        'status', 'due_date', 'is_overdue'
    ]
    list_filter = ['month', 'calculation_type', 'status', 'due_date']
    search_fields = [
        'student_transportation__student__first_name',
        'student_transportation__student__last_name'
    ]
    readonly_fields = ['created_at', 'updated_at', 'is_overdue', 'days_overdue', 'total_fee']
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('student_transportation', 'month', 'calculation_type', 'status')
        }),
        (_('Fee Calculation'), {
            'fields': (
                'base_fee', 'distance_km', 'distance_rate_per_km',
                'number_of_stops', 'rate_per_stop', 'zone_multiplier',
                'discount_percentage', 'additional_charges', 'total_fee'
            )
        }),
        (_('Payment Information'), {
            'fields': ('due_date', 'paid_date', 'payment_reference', 'is_overdue', 'days_overdue')
        }),
        (_('Additional Information'), {
            'fields': ('calculation_details', 'notes')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    actions = ['calculate_fees', 'mark_as_paid']
    
    def calculate_fees(self, request, queryset):
        for fee in queryset:
            fee.calculate_fee()
            fee.save()
        self.message_user(request, f'Calculated fees for {queryset.count()} records.')
    calculate_fees.short_description = _('Calculate selected fees')
    
    def mark_as_paid(self, request, queryset):
        from django.utils import timezone
        queryset.update(status='paid', paid_date=timezone.now().date())
        self.message_user(request, f'Marked {queryset.count()} fees as paid.')
    mark_as_paid.short_description = _('Mark selected fees as paid')


@admin.register(ParentNotification)
class ParentNotificationAdmin(admin.ModelAdmin):
    list_display = [
        'student_transportation', 'notification_type', 'channel',
        'recipient_name', 'status', 'scheduled_time', 'sent_time'
    ]
    list_filter = ['notification_type', 'channel', 'status', 'scheduled_time']
    search_fields = [
        'student_transportation__student__first_name',
        'student_transportation__student__last_name',
        'recipient_name', 'recipient_contact'
    ]
    readonly_fields = ['created_at', 'updated_at', 'sent_time', 'delivered_time', 'read_time', 'is_overdue']
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('student_transportation', 'notification_type', 'channel', 'status')
        }),
        (_('Recipient Information'), {
            'fields': ('recipient_name', 'recipient_contact')
        }),
        (_('Message Content'), {
            'fields': ('subject', 'message')
        }),
        (_('Timing Information'), {
            'fields': ('scheduled_time', 'sent_time', 'delivered_time', 'read_time', 'is_overdue')
        }),
        (_('Additional Information'), {
            'fields': ('error_message', 'metadata')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    actions = ['send_notifications', 'mark_as_sent']
    
    def send_notifications(self, request, queryset):
        from .services import ParentNotificationService
        from django.utils import timezone
        service = ParentNotificationService()
        sent_count = 0
        
        for notification in queryset.filter(status='pending'):
            try:
                # Here you would integrate with actual notification services
                notification.status = 'sent'
                notification.sent_time = timezone.now()
                notification.save()
                sent_count += 1
            except Exception as e:
                notification.status = 'failed'
                notification.error_message = str(e)
                notification.save()
        
        self.message_user(request, f'Sent {sent_count} notifications.')
    send_notifications.short_description = _('Send selected notifications')
    
    def mark_as_sent(self, request, queryset):
        from django.utils import timezone
        queryset.update(status='sent', sent_time=timezone.now())
        self.message_user(request, f'Marked {queryset.count()} notifications as sent.')
    mark_as_sent.short_description = _('Mark selected notifications as sent')