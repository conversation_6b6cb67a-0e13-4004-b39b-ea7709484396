"""
Forms for transportation module
"""

from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from .models import (
    Vehicle, Driver, Route, BusStop, RouteStop, StudentTransportation,
    RouteOptimization, TransportationAttendance, TransportationFee,
    ParentNotification
)
from students.models import Student
from hr.models import Employee


class VehicleForm(forms.ModelForm):
    """Form for creating/editing vehicles"""
    
    class Meta:
        model = Vehicle
        fields = [
            'vehicle_number', 'license_plate', 'vehicle_type', 'make', 'model',
            'year', 'capacity', 'fuel_type', 'status', 'purchase_date',
            'insurance_expiry', 'registration_expiry', 'last_maintenance_date',
            'next_maintenance_date', 'gps_device_id', 'notes'
        ]
        widgets = {
            'vehicle_number': forms.TextInput(attrs={'class': 'form-control'}),
            'license_plate': forms.TextInput(attrs={'class': 'form-control'}),
            'vehicle_type': forms.Select(attrs={'class': 'form-control'}),
            'make': forms.TextInput(attrs={'class': 'form-control'}),
            'model': forms.TextInput(attrs={'class': 'form-control'}),
            'year': forms.NumberInput(attrs={'class': 'form-control'}),
            'capacity': forms.NumberInput(attrs={'class': 'form-control'}),
            'fuel_type': forms.Select(attrs={'class': 'form-control'}),
            'status': forms.Select(attrs={'class': 'form-control'}),
            'purchase_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'insurance_expiry': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'registration_expiry': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'last_maintenance_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'next_maintenance_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'gps_device_id': forms.TextInput(attrs={'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }


class DriverForm(forms.ModelForm):
    """Form for creating/editing drivers"""
    
    def __init__(self, *args, **kwargs):
        school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if school:
            # Filter employees to only show those from the same school
            self.fields['employee'].queryset = Employee.objects.filter(
                school=school,
                user__isnull=False
            ).exclude(
                driver_profile__isnull=False
            )
    
    class Meta:
        model = Driver
        fields = [
            'employee', 'license_number', 'license_type', 'license_expiry',
            'medical_certificate_expiry', 'experience_years', 'status',
            'emergency_contact_name', 'emergency_contact_phone', 'notes'
        ]
        widgets = {
            'employee': forms.Select(attrs={'class': 'form-control'}),
            'license_number': forms.TextInput(attrs={'class': 'form-control'}),
            'license_type': forms.TextInput(attrs={'class': 'form-control'}),
            'license_expiry': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'medical_certificate_expiry': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'experience_years': forms.NumberInput(attrs={'class': 'form-control'}),
            'status': forms.Select(attrs={'class': 'form-control'}),
            'emergency_contact_name': forms.TextInput(attrs={'class': 'form-control'}),
            'emergency_contact_phone': forms.TextInput(attrs={'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }


class BusStopForm(forms.ModelForm):
    """Form for creating/editing bus stops"""
    
    class Meta:
        model = BusStop
        fields = [
            'name', 'name_ar', 'code', 'address', 'latitude', 'longitude',
            'status', 'landmark', 'safety_rating', 'accessibility_features', 'notes'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'name_ar': forms.TextInput(attrs={'class': 'form-control'}),
            'code': forms.TextInput(attrs={'class': 'form-control'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
            'latitude': forms.NumberInput(attrs={'class': 'form-control', 'step': 'any'}),
            'longitude': forms.NumberInput(attrs={'class': 'form-control', 'step': 'any'}),
            'status': forms.Select(attrs={'class': 'form-control'}),
            'landmark': forms.TextInput(attrs={'class': 'form-control'}),
            'safety_rating': forms.NumberInput(attrs={'class': 'form-control', 'min': 1, 'max': 5}),
            'accessibility_features': forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }


class RouteForm(forms.ModelForm):
    """Form for creating/editing routes"""
    
    def __init__(self, *args, **kwargs):
        school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if school:
            # Filter vehicles and drivers to only show those from the same school
            self.fields['vehicle'].queryset = Vehicle.objects.filter(
                school=school,
                status='active'
            )
            self.fields['primary_driver'].queryset = Driver.objects.filter(
                school=school,
                status='active'
            )
            self.fields['backup_driver'].queryset = Driver.objects.filter(
                school=school,
                status='active'
            )
    
    class Meta:
        model = Route
        fields = [
            'name', 'name_ar', 'code', 'route_type', 'vehicle',
            'primary_driver', 'backup_driver', 'status',
            'start_time_morning', 'start_time_afternoon',
            'estimated_duration_minutes', 'max_capacity', 'monthly_fee', 'notes'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'name_ar': forms.TextInput(attrs={'class': 'form-control'}),
            'code': forms.TextInput(attrs={'class': 'form-control'}),
            'route_type': forms.Select(attrs={'class': 'form-control'}),
            'vehicle': forms.Select(attrs={'class': 'form-control'}),
            'primary_driver': forms.Select(attrs={'class': 'form-control'}),
            'backup_driver': forms.Select(attrs={'class': 'form-control'}),
            'status': forms.Select(attrs={'class': 'form-control'}),
            'start_time_morning': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),
            'start_time_afternoon': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),
            'estimated_duration_minutes': forms.NumberInput(attrs={'class': 'form-control'}),
            'max_capacity': forms.NumberInput(attrs={'class': 'form-control'}),
            'monthly_fee': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }
    
    def clean(self):
        cleaned_data = super().clean()
        vehicle = cleaned_data.get('vehicle')
        max_capacity = cleaned_data.get('max_capacity')
        
        if vehicle and max_capacity and max_capacity > vehicle.capacity:
            raise ValidationError(
                _('Route capacity cannot exceed vehicle capacity (%(capacity)s)') % {
                    'capacity': vehicle.capacity
                }
            )
        
        return cleaned_data


class RouteStopForm(forms.ModelForm):
    """Form for creating/editing route stops"""
    
    def __init__(self, *args, **kwargs):
        school = kwargs.pop('school', None)
        route = kwargs.pop('route', None)
        super().__init__(*args, **kwargs)
        
        if school:
            self.fields['bus_stop'].queryset = BusStop.objects.filter(
                school=school,
                status='active'
            )
        
        if route:
            self.fields['route'].initial = route
            self.fields['route'].widget = forms.HiddenInput()
    
    class Meta:
        model = RouteStop
        fields = [
            'route', 'bus_stop', 'sequence_order',
            'estimated_arrival_time_morning', 'estimated_arrival_time_afternoon',
            'estimated_departure_time_morning', 'estimated_departure_time_afternoon',
            'distance_from_previous_km', 'estimated_travel_time_minutes',
            'stop_duration_minutes', 'is_pickup_point', 'is_dropoff_point'
        ]
        widgets = {
            'route': forms.Select(attrs={'class': 'form-control'}),
            'bus_stop': forms.Select(attrs={'class': 'form-control'}),
            'sequence_order': forms.NumberInput(attrs={'class': 'form-control'}),
            'estimated_arrival_time_morning': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),
            'estimated_arrival_time_afternoon': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),
            'estimated_departure_time_morning': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),
            'estimated_departure_time_afternoon': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),
            'distance_from_previous_km': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'estimated_travel_time_minutes': forms.NumberInput(attrs={'class': 'form-control'}),
            'stop_duration_minutes': forms.NumberInput(attrs={'class': 'form-control'}),
            'is_pickup_point': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_dropoff_point': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }


class StudentTransportationForm(forms.ModelForm):
    """Form for assigning students to transportation"""
    
    def __init__(self, *args, **kwargs):
        school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if school:
            # Filter students, routes, and stops to only show those from the same school
            self.fields['student'].queryset = Student.objects.filter(school=school)
            self.fields['route'].queryset = Route.objects.filter(
                school=school,
                status='active'
            )
            self.fields['pickup_stop'].queryset = BusStop.objects.filter(
                school=school,
                status='active'
            )
            self.fields['dropoff_stop'].queryset = BusStop.objects.filter(
                school=school,
                status='active'
            )
    
    class Meta:
        model = StudentTransportation
        fields = [
            'student', 'route', 'pickup_stop', 'dropoff_stop', 'status',
            'start_date', 'end_date', 'monthly_fee',
            'emergency_contact_name', 'emergency_contact_phone',
            'special_needs', 'notes'
        ]
        widgets = {
            'student': forms.Select(attrs={'class': 'form-control'}),
            'route': forms.Select(attrs={'class': 'form-control'}),
            'pickup_stop': forms.Select(attrs={'class': 'form-control'}),
            'dropoff_stop': forms.Select(attrs={'class': 'form-control'}),
            'status': forms.Select(attrs={'class': 'form-control'}),
            'start_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'monthly_fee': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'emergency_contact_name': forms.TextInput(attrs={'class': 'form-control'}),
            'emergency_contact_phone': forms.TextInput(attrs={'class': 'form-control'}),
            'special_needs': forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }
    
    def clean(self):
        cleaned_data = super().clean()
        route = cleaned_data.get('route')
        pickup_stop = cleaned_data.get('pickup_stop')
        dropoff_stop = cleaned_data.get('dropoff_stop')
        
        if route and pickup_stop:
            # Check if pickup stop is part of the route
            if not RouteStop.objects.filter(route=route, bus_stop=pickup_stop).exists():
                raise ValidationError(
                    _('Pickup stop must be part of the selected route.')
                )
        
        if route and dropoff_stop:
            # Check if dropoff stop is part of the route
            if not RouteStop.objects.filter(route=route, bus_stop=dropoff_stop).exists():
                raise ValidationError(
                    _('Drop-off stop must be part of the selected route.')
                )
        
        if route and route.is_full:
            raise ValidationError(
                _('Selected route is at full capacity.')
            )
        
        return cleaned_data


class RouteOptimizationForm(forms.ModelForm):
    """Form for route optimization"""
    
    class Meta:
        model = RouteOptimization
        fields = ['optimization_type']
        widgets = {
            'optimization_type': forms.Select(attrs={'class': 'form-control'}),
        }


class RouteSearchForm(forms.Form):
    """Form for searching routes"""
    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Search routes...'),
        })
    )
    status = forms.ChoiceField(
        choices=[('', _('All Statuses'))] + Route.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    route_type = forms.ChoiceField(
        choices=[('', _('All Types'))] + Route.ROUTE_TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )


class VehicleSearchForm(forms.Form):
    """Form for searching vehicles"""
    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Search vehicles...'),
        })
    )
    status = forms.ChoiceField(
        choices=[('', _('All Statuses'))] + Vehicle.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    vehicle_type = forms.ChoiceField(
        choices=[('', _('All Types'))] + Vehicle.VEHICLE_TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )


class DriverSearchForm(forms.Form):
    """Form for searching drivers"""
    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Search drivers...'),
        })
    )
    status = forms.ChoiceField(
        choices=[('', _('All Statuses'))] + Driver.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )


class BusStopSearchForm(forms.Form):
    """Form for searching bus stops"""
    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Search bus stops...'),
        })
    )
    status = forms.ChoiceField(
        choices=[('', _('All Statuses'))] + BusStop.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )


class StudentTransportationSearchForm(forms.Form):
    """Form for searching student transportation assignments"""
    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Search students...'),
        })
    )
    status = forms.ChoiceField(
        choices=[('', _('All Statuses'))] + StudentTransportation.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    route = forms.ModelChoiceField(
        queryset=Route.objects.none(),
        required=False,
        empty_label=_('All Routes'),
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    def __init__(self, *args, **kwargs):
        school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if school:
            self.fields['route'].queryset = Route.objects.filter(school=school)


class GPSDataForm(forms.Form):
    """Form for submitting GPS data"""
    vehicle_id = forms.UUIDField(widget=forms.HiddenInput())
    latitude = forms.DecimalField(
        max_digits=10,
        decimal_places=8,
        widget=forms.NumberInput(attrs={'step': 'any'})
    )
    longitude = forms.DecimalField(
        max_digits=11,
        decimal_places=8,
        widget=forms.NumberInput(attrs={'step': 'any'})
    )
    speed = forms.DecimalField(
        max_digits=6,
        decimal_places=2,
        required=False,
        widget=forms.NumberInput(attrs={'step': '0.01'})
    )
    heading = forms.DecimalField(
        max_digits=6,
        decimal_places=2,
        required=False,
        widget=forms.NumberInput(attrs={'step': '0.01'})
    )
    altitude = forms.DecimalField(
        max_digits=8,
        decimal_places=2,
        required=False,
        widget=forms.NumberInput(attrs={'step': '0.01'})
    )
    accuracy = forms.DecimalField(
        max_digits=6,
        decimal_places=2,
        required=False,
        widget=forms.NumberInput(attrs={'step': '0.01'})
    )
    engine_status = forms.ChoiceField(
        choices=[
            ('', _('Unknown')),
            ('on', _('Engine On')),
            ('off', _('Engine Off')),
            ('idle', _('Idle')),
        ],
        required=False
    )
    fuel_level = forms.DecimalField(
        max_digits=5,
        decimal_places=2,
        required=False,
        widget=forms.NumberInput(attrs={'step': '0.01', 'min': 0, 'max': 100})
    )


class RouteStopFormSet(forms.BaseInlineFormSet):
    """Formset for route stops"""
    
    def clean(self):
        """Validate that sequence orders are unique and consecutive"""
        if any(self.errors):
            return
        
        sequence_orders = []
        for form in self.forms:
            if form.cleaned_data and not form.cleaned_data.get('DELETE', False):
                sequence_order = form.cleaned_data.get('sequence_order')
                if sequence_order:
                    if sequence_order in sequence_orders:
                        raise ValidationError(_('Sequence orders must be unique.'))
                    sequence_orders.append(sequence_order)
        
        # Check if sequence orders are consecutive starting from 1
        sequence_orders.sort()
        expected_sequence = list(range(1, len(sequence_orders) + 1))
        if sequence_orders != expected_sequence:
            raise ValidationError(
                _('Sequence orders must be consecutive starting from 1.')
            )


class TransportationAttendanceForm(forms.ModelForm):
    """Form for recording transportation attendance"""
    
    def __init__(self, *args, **kwargs):
        school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if school:
            self.fields['student_transportation'].queryset = StudentTransportation.objects.filter(
                school=school,
                status='active'
            ).select_related('student', 'route')
    
    class Meta:
        model = TransportationAttendance
        fields = [
            'student_transportation', 'date', 'attendance_type', 'status',
            'scheduled_time', 'actual_time', 'bus_stop', 'notes'
        ]
        widgets = {
            'student_transportation': forms.Select(attrs={'class': 'form-control'}),
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'attendance_type': forms.Select(attrs={'class': 'form-control'}),
            'status': forms.Select(attrs={'class': 'form-control'}),
            'scheduled_time': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),
            'actual_time': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),
            'bus_stop': forms.Select(attrs={'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
        }


class TransportationFeeForm(forms.ModelForm):
    """Form for managing transportation fees"""
    
    def __init__(self, *args, **kwargs):
        school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if school:
            self.fields['student_transportation'].queryset = StudentTransportation.objects.filter(
                school=school,
                status='active'
            ).select_related('student', 'route')
    
    class Meta:
        model = TransportationFee
        fields = [
            'student_transportation', 'month', 'calculation_type', 'base_fee',
            'distance_km', 'distance_rate_per_km', 'number_of_stops', 'rate_per_stop',
            'zone_multiplier', 'discount_percentage', 'additional_charges',
            'due_date', 'notes'
        ]
        widgets = {
            'student_transportation': forms.Select(attrs={'class': 'form-control'}),
            'month': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'calculation_type': forms.Select(attrs={'class': 'form-control'}),
            'base_fee': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'distance_km': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'distance_rate_per_km': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'number_of_stops': forms.NumberInput(attrs={'class': 'form-control'}),
            'rate_per_stop': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'zone_multiplier': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'discount_percentage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': 0, 'max': 100}),
            'additional_charges': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'due_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        # Calculate total fee before saving
        instance.calculate_fee()
        if commit:
            instance.save()
        return instance


class ParentNotificationForm(forms.ModelForm):
    """Form for creating parent notifications"""
    
    def __init__(self, *args, **kwargs):
        school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if school:
            self.fields['student_transportation'].queryset = StudentTransportation.objects.filter(
                school=school,
                status='active'
            ).select_related('student', 'route')
    
    class Meta:
        model = ParentNotification
        fields = [
            'student_transportation', 'notification_type', 'channel',
            'recipient_name', 'recipient_contact', 'subject', 'message',
            'scheduled_time'
        ]
        widgets = {
            'student_transportation': forms.Select(attrs={'class': 'form-control'}),
            'notification_type': forms.Select(attrs={'class': 'form-control'}),
            'channel': forms.Select(attrs={'class': 'form-control'}),
            'recipient_name': forms.TextInput(attrs={'class': 'form-control'}),
            'recipient_contact': forms.TextInput(attrs={'class': 'form-control'}),
            'subject': forms.TextInput(attrs={'class': 'form-control'}),
            'message': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'scheduled_time': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
        }


class AttendanceReportForm(forms.Form):
    """Form for generating attendance reports"""
    
    def __init__(self, *args, **kwargs):
        school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if school:
            self.fields['route'].queryset = Route.objects.filter(
                school=school,
                status='active'
            )
    
    route = forms.ModelChoiceField(
        queryset=Route.objects.none(),
        required=False,
        empty_label=_('All Routes'),
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    date_from = forms.DateField(
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    
    date_to = forms.DateField(
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    
    attendance_type = forms.ChoiceField(
        choices=[
            ('', _('Both Pickup and Drop-off')),
            ('pickup', _('Pickup Only')),
            ('dropoff', _('Drop-off Only')),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )


class FeeReportForm(forms.Form):
    """Form for generating fee reports"""
    
    month = forms.DateField(
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'month'})
    )
    
    status = forms.ChoiceField(
        choices=[('', _('All Statuses'))] + TransportationFee.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    calculation_type = forms.ChoiceField(
        choices=[('', _('All Types'))] + TransportationFee.CALCULATION_TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )


class BulkAttendanceForm(forms.Form):
    """Form for bulk attendance recording"""
    
    def __init__(self, *args, **kwargs):
        school = kwargs.pop('school', None)
        route = kwargs.pop('route', None)
        super().__init__(*args, **kwargs)
        
        if school:
            self.fields['route'].queryset = Route.objects.filter(
                school=school,
                status='active'
            )
        
        if route:
            self.fields['route'].initial = route
    
    route = forms.ModelChoiceField(
        queryset=Route.objects.none(),
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    date = forms.DateField(
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    
    attendance_type = forms.ChoiceField(
        choices=TransportationAttendance.ATTENDANCE_TYPE_CHOICES,
        widget=forms.Select(attrs={'class': 'form-control'})
    )


class FeeCalculationForm(forms.Form):
    """Form for bulk fee calculation"""
    
    month = forms.DateField(
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'month'})
    )
    
    calculation_type = forms.ChoiceField(
        choices=TransportationFee.CALCULATION_TYPE_CHOICES,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    recalculate_existing = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        help_text=_('Recalculate fees that have already been calculated')
    )


# Additional models are already imported above


# Create the formset
RouteStopInlineFormSet = forms.inlineformset_factory(
    Route,
    RouteStop,
    form=RouteStopForm,
    formset=RouteStopFormSet,
    extra=1,
    can_delete=True,
    fields=[
        'bus_stop', 'sequence_order', 'estimated_arrival_time_morning',
        'estimated_arrival_time_afternoon', 'is_pickup_point', 'is_dropoff_point'
    ]
)