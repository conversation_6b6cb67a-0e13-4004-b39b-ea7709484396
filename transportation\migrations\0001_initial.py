# Generated by Django 5.2.4 on 2025-08-04 13:42

import django.core.validators
import django.db.models.deletion
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("core", "0002_auditlog_academicyear_created_by_and_more"),
        ("hr", "0005_performancegoal_performanceimprovementplan_and_more"),
        ("students", "0008_grade_max_age_grade_max_capacity_grade_min_age"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="BusStop",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.<PERSON><PERSON>an<PERSON>ield(default=True, verbose_name="Is Active"),
                ),
                ("name", models.<PERSON>r<PERSON><PERSON>(max_length=100, verbose_name="Stop Name")),
                (
                    "name_ar",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="Stop Name (Arabic)",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        help_text="Unique stop identifier",
                        max_length=20,
                        verbose_name="Stop Code",
                    ),
                ),
                ("address", models.TextField(verbose_name="Address")),
                (
                    "latitude",
                    models.DecimalField(
                        blank=True,
                        decimal_places=8,
                        max_digits=10,
                        null=True,
                        verbose_name="Latitude",
                    ),
                ),
                (
                    "longitude",
                    models.DecimalField(
                        blank=True,
                        decimal_places=8,
                        max_digits=11,
                        null=True,
                        verbose_name="Longitude",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("inactive", "Inactive"),
                            ("temporary", "Temporary"),
                        ],
                        default="active",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "landmark",
                    models.CharField(
                        blank=True, max_length=200, verbose_name="Nearby Landmark"
                    ),
                ),
                (
                    "safety_rating",
                    models.PositiveIntegerField(
                        default=5,
                        help_text="Safety rating from 1 (poor) to 5 (excellent)",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                        verbose_name="Safety Rating",
                    ),
                ),
                (
                    "accessibility_features",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="List of accessibility features available at this stop",
                        verbose_name="Accessibility Features",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Bus Stop",
                "verbose_name_plural": "Bus Stops",
            },
        ),
        migrations.CreateModel(
            name="Driver",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "license_number",
                    models.CharField(
                        max_length=20, unique=True, verbose_name="Driver License Number"
                    ),
                ),
                (
                    "license_type",
                    models.CharField(
                        help_text="e.g., CDL, Class B, etc.",
                        max_length=10,
                        verbose_name="License Type",
                    ),
                ),
                (
                    "license_expiry",
                    models.DateField(verbose_name="License Expiry Date"),
                ),
                (
                    "medical_certificate_expiry",
                    models.DateField(
                        blank=True, null=True, verbose_name="Medical Certificate Expiry"
                    ),
                ),
                (
                    "experience_years",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Years of Experience"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("inactive", "Inactive"),
                            ("suspended", "Suspended"),
                            ("terminated", "Terminated"),
                        ],
                        default="active",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "emergency_contact_name",
                    models.CharField(
                        max_length=100, verbose_name="Emergency Contact Name"
                    ),
                ),
                (
                    "emergency_contact_phone",
                    models.CharField(
                        max_length=20,
                        validators=[
                            django.core.validators.RegexValidator(
                                message='Phone number must be entered in the format: "+999999999". Up to 15 digits allowed.',
                                regex="^\\+?1?\\d{9,15}$",
                            )
                        ],
                        verbose_name="Emergency Contact Phone",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "employee",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="driver_profile",
                        to="hr.employee",
                        verbose_name="Employee",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Driver",
                "verbose_name_plural": "Drivers",
            },
        ),
        migrations.CreateModel(
            name="Route",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("name", models.CharField(max_length=100, verbose_name="Route Name")),
                (
                    "name_ar",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="Route Name (Arabic)",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        help_text="Unique route identifier",
                        max_length=20,
                        verbose_name="Route Code",
                    ),
                ),
                (
                    "route_type",
                    models.CharField(
                        choices=[
                            ("morning", "Morning Route"),
                            ("afternoon", "Afternoon Route"),
                            ("both", "Both Morning & Afternoon"),
                            ("special", "Special Route"),
                        ],
                        default="both",
                        max_length=20,
                        verbose_name="Route Type",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("inactive", "Inactive"),
                            ("suspended", "Suspended"),
                            ("planning", "Planning"),
                        ],
                        default="planning",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "start_time_morning",
                    models.TimeField(
                        blank=True, null=True, verbose_name="Morning Start Time"
                    ),
                ),
                (
                    "start_time_afternoon",
                    models.TimeField(
                        blank=True, null=True, verbose_name="Afternoon Start Time"
                    ),
                ),
                (
                    "estimated_duration_minutes",
                    models.PositiveIntegerField(
                        default=60, verbose_name="Estimated Duration (Minutes)"
                    ),
                ),
                (
                    "total_distance_km",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=8,
                        verbose_name="Total Distance (KM)",
                    ),
                ),
                (
                    "max_capacity",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Maximum number of students for this route",
                        verbose_name="Maximum Capacity",
                    ),
                ),
                (
                    "current_occupancy",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Current number of students assigned to this route",
                        verbose_name="Current Occupancy",
                    ),
                ),
                (
                    "monthly_fee",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=10,
                        verbose_name="Monthly Transportation Fee",
                    ),
                ),
                (
                    "route_coordinates",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="GPS coordinates for the optimized route path",
                        verbose_name="Route Coordinates",
                    ),
                ),
                (
                    "optimization_data",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Data from route optimization algorithms",
                        verbose_name="Route Optimization Data",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "backup_driver",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="backup_routes",
                        to="transportation.driver",
                        verbose_name="Backup Driver",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "primary_driver",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="primary_routes",
                        to="transportation.driver",
                        verbose_name="Primary Driver",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Route",
                "verbose_name_plural": "Routes",
            },
        ),
        migrations.CreateModel(
            name="RouteOptimization",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "optimization_type",
                    models.CharField(
                        choices=[
                            ("distance", "Distance Optimization"),
                            ("time", "Time Optimization"),
                            ("fuel", "Fuel Efficiency"),
                            ("capacity", "Capacity Optimization"),
                            ("hybrid", "Hybrid Optimization"),
                        ],
                        max_length=20,
                        verbose_name="Optimization Type",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("processing", "Processing"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "original_distance_km",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=8,
                        verbose_name="Original Distance (KM)",
                    ),
                ),
                (
                    "optimized_distance_km",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=8,
                        null=True,
                        verbose_name="Optimized Distance (KM)",
                    ),
                ),
                (
                    "original_duration_minutes",
                    models.PositiveIntegerField(
                        verbose_name="Original Duration (Minutes)"
                    ),
                ),
                (
                    "optimized_duration_minutes",
                    models.PositiveIntegerField(
                        blank=True,
                        null=True,
                        verbose_name="Optimized Duration (Minutes)",
                    ),
                ),
                (
                    "fuel_savings_percentage",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=5,
                        null=True,
                        verbose_name="Fuel Savings (%)",
                    ),
                ),
                (
                    "time_savings_percentage",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=5,
                        null=True,
                        verbose_name="Time Savings (%)",
                    ),
                ),
                (
                    "optimization_parameters",
                    models.JSONField(
                        default=dict,
                        help_text="Parameters used for optimization algorithm",
                        verbose_name="Optimization Parameters",
                    ),
                ),
                (
                    "optimization_results",
                    models.JSONField(
                        default=dict,
                        help_text="Detailed results from optimization algorithm",
                        verbose_name="Optimization Results",
                    ),
                ),
                (
                    "processed_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Processed At"
                    ),
                ),
                (
                    "error_message",
                    models.TextField(blank=True, verbose_name="Error Message"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "route",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="optimizations",
                        to="transportation.route",
                        verbose_name="Route",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Route Optimization",
                "verbose_name_plural": "Route Optimizations",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="RouteStop",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "sequence_order",
                    models.PositiveIntegerField(
                        help_text="Order of this stop in the route sequence",
                        verbose_name="Sequence Order",
                    ),
                ),
                (
                    "estimated_arrival_time_morning",
                    models.TimeField(
                        blank=True,
                        null=True,
                        verbose_name="Estimated Morning Arrival Time",
                    ),
                ),
                (
                    "estimated_arrival_time_afternoon",
                    models.TimeField(
                        blank=True,
                        null=True,
                        verbose_name="Estimated Afternoon Arrival Time",
                    ),
                ),
                (
                    "estimated_departure_time_morning",
                    models.TimeField(
                        blank=True,
                        null=True,
                        verbose_name="Estimated Morning Departure Time",
                    ),
                ),
                (
                    "estimated_departure_time_afternoon",
                    models.TimeField(
                        blank=True,
                        null=True,
                        verbose_name="Estimated Afternoon Departure Time",
                    ),
                ),
                (
                    "distance_from_previous_km",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=8,
                        verbose_name="Distance from Previous Stop (KM)",
                    ),
                ),
                (
                    "estimated_travel_time_minutes",
                    models.PositiveIntegerField(
                        default=5,
                        help_text="Travel time from previous stop",
                        verbose_name="Estimated Travel Time (Minutes)",
                    ),
                ),
                (
                    "stop_duration_minutes",
                    models.PositiveIntegerField(
                        default=2,
                        help_text="Time spent at this stop for pickup/drop-off",
                        verbose_name="Stop Duration (Minutes)",
                    ),
                ),
                (
                    "is_pickup_point",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this stop is used for morning pickup",
                        verbose_name="Is Pickup Point",
                    ),
                ),
                (
                    "is_dropoff_point",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this stop is used for afternoon drop-off",
                        verbose_name="Is Drop-off Point",
                    ),
                ),
                (
                    "bus_stop",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="route_stops",
                        to="transportation.busstop",
                        verbose_name="Bus Stop",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "route",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="route_stops",
                        to="transportation.route",
                        verbose_name="Route",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Route Stop",
                "verbose_name_plural": "Route Stops",
                "ordering": ["route", "sequence_order"],
            },
        ),
        migrations.CreateModel(
            name="StudentTransportation",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("inactive", "Inactive"),
                            ("suspended", "Suspended"),
                            ("graduated", "Graduated"),
                        ],
                        default="active",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                ("start_date", models.DateField(verbose_name="Start Date")),
                (
                    "end_date",
                    models.DateField(blank=True, null=True, verbose_name="End Date"),
                ),
                (
                    "monthly_fee",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="Monthly Fee"
                    ),
                ),
                (
                    "emergency_contact_name",
                    models.CharField(
                        max_length=100, verbose_name="Emergency Contact Name"
                    ),
                ),
                (
                    "emergency_contact_phone",
                    models.CharField(
                        max_length=20,
                        validators=[
                            django.core.validators.RegexValidator(
                                message='Phone number must be entered in the format: "+999999999". Up to 15 digits allowed.',
                                regex="^\\+?1?\\d{9,15}$",
                            )
                        ],
                        verbose_name="Emergency Contact Phone",
                    ),
                ),
                (
                    "special_needs",
                    models.TextField(
                        blank=True,
                        help_text="Any special transportation requirements or medical needs",
                        verbose_name="Special Needs",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "dropoff_stop",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="dropoff_assignments",
                        to="transportation.busstop",
                        verbose_name="Drop-off Stop",
                    ),
                ),
                (
                    "pickup_stop",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="pickup_assignments",
                        to="transportation.busstop",
                        verbose_name="Pickup Stop",
                    ),
                ),
                (
                    "route",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="student_assignments",
                        to="transportation.route",
                        verbose_name="Route",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transportation_assignments",
                        to="students.student",
                        verbose_name="Student",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Student Transportation",
                "verbose_name_plural": "Student Transportation",
            },
        ),
        migrations.CreateModel(
            name="Vehicle",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "vehicle_number",
                    models.CharField(
                        help_text="Unique vehicle identification number",
                        max_length=20,
                        verbose_name="Vehicle Number",
                    ),
                ),
                (
                    "license_plate",
                    models.CharField(
                        max_length=15,
                        unique=True,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="License plate must contain only letters, numbers, hyphens and spaces",
                                regex="^[A-Z0-9\\-\\s]+$",
                            )
                        ],
                        verbose_name="License Plate",
                    ),
                ),
                (
                    "vehicle_type",
                    models.CharField(
                        choices=[
                            ("bus", "Bus"),
                            ("van", "Van"),
                            ("car", "Car"),
                            ("minibus", "Minibus"),
                        ],
                        default="bus",
                        max_length=20,
                        verbose_name="Vehicle Type",
                    ),
                ),
                ("make", models.CharField(max_length=50, verbose_name="Make")),
                ("model", models.CharField(max_length=50, verbose_name="Model")),
                (
                    "year",
                    models.PositiveIntegerField(
                        validators=[
                            django.core.validators.MinValueValidator(1990),
                            django.core.validators.MaxValueValidator(2030),
                        ],
                        verbose_name="Year",
                    ),
                ),
                (
                    "capacity",
                    models.PositiveIntegerField(
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="Seating Capacity",
                    ),
                ),
                (
                    "fuel_type",
                    models.CharField(
                        choices=[
                            ("petrol", "Petrol"),
                            ("diesel", "Diesel"),
                            ("electric", "Electric"),
                            ("hybrid", "Hybrid"),
                        ],
                        default="diesel",
                        max_length=20,
                        verbose_name="Fuel Type",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("maintenance", "Under Maintenance"),
                            ("inactive", "Inactive"),
                            ("retired", "Retired"),
                        ],
                        default="active",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "purchase_date",
                    models.DateField(
                        blank=True, null=True, verbose_name="Purchase Date"
                    ),
                ),
                (
                    "insurance_expiry",
                    models.DateField(
                        blank=True, null=True, verbose_name="Insurance Expiry Date"
                    ),
                ),
                (
                    "registration_expiry",
                    models.DateField(
                        blank=True, null=True, verbose_name="Registration Expiry Date"
                    ),
                ),
                (
                    "last_maintenance_date",
                    models.DateField(
                        blank=True, null=True, verbose_name="Last Maintenance Date"
                    ),
                ),
                (
                    "next_maintenance_date",
                    models.DateField(
                        blank=True, null=True, verbose_name="Next Maintenance Date"
                    ),
                ),
                (
                    "gps_device_id",
                    models.CharField(
                        blank=True,
                        help_text="Unique identifier for GPS tracking device",
                        max_length=50,
                        null=True,
                        verbose_name="GPS Device ID",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Vehicle",
                "verbose_name_plural": "Vehicles",
            },
        ),
        migrations.CreateModel(
            name="TransportationAnalytics",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "metric_type",
                    models.CharField(
                        choices=[
                            ("daily", "Daily Metrics"),
                            ("weekly", "Weekly Metrics"),
                            ("monthly", "Monthly Metrics"),
                            ("route_performance", "Route Performance"),
                            ("fuel_consumption", "Fuel Consumption"),
                            ("maintenance_costs", "Maintenance Costs"),
                        ],
                        max_length=30,
                        verbose_name="Metric Type",
                    ),
                ),
                ("date_from", models.DateField(verbose_name="Date From")),
                ("date_to", models.DateField(verbose_name="Date To")),
                (
                    "total_distance_km",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=10,
                        verbose_name="Total Distance (KM)",
                    ),
                ),
                (
                    "total_duration_minutes",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Total Duration (Minutes)"
                    ),
                ),
                (
                    "fuel_consumed_liters",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=8,
                        verbose_name="Fuel Consumed (Liters)",
                    ),
                ),
                (
                    "fuel_cost",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=10,
                        verbose_name="Fuel Cost",
                    ),
                ),
                (
                    "maintenance_cost",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=10,
                        verbose_name="Maintenance Cost",
                    ),
                ),
                (
                    "students_transported",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Students Transported"
                    ),
                ),
                (
                    "on_time_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=5,
                        verbose_name="On-Time Percentage",
                    ),
                ),
                (
                    "average_speed_kmh",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=6,
                        verbose_name="Average Speed (KM/H)",
                    ),
                ),
                (
                    "efficiency_score",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="Efficiency Score",
                    ),
                ),
                (
                    "metrics_data",
                    models.JSONField(
                        default=dict, verbose_name="Detailed Metrics Data"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "route",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="analytics",
                        to="transportation.route",
                        verbose_name="Route",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "vehicle",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="analytics",
                        to="transportation.vehicle",
                        verbose_name="Vehicle",
                    ),
                ),
            ],
            options={
                "verbose_name": "Transportation Analytics",
                "verbose_name_plural": "Transportation Analytics",
                "ordering": ["-date_to"],
            },
        ),
        migrations.AddField(
            model_name="route",
            name="vehicle",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="routes",
                to="transportation.vehicle",
                verbose_name="Assigned Vehicle",
            ),
        ),
        migrations.CreateModel(
            name="GPSTracking",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "latitude",
                    models.DecimalField(
                        decimal_places=8, max_digits=10, verbose_name="Latitude"
                    ),
                ),
                (
                    "longitude",
                    models.DecimalField(
                        decimal_places=8, max_digits=11, verbose_name="Longitude"
                    ),
                ),
                (
                    "speed_kmh",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=6,
                        verbose_name="Speed (KM/H)",
                    ),
                ),
                (
                    "heading",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=6,
                        null=True,
                        verbose_name="Heading (Degrees)",
                    ),
                ),
                (
                    "altitude",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=8,
                        null=True,
                        verbose_name="Altitude (Meters)",
                    ),
                ),
                (
                    "accuracy",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=6,
                        null=True,
                        verbose_name="GPS Accuracy (Meters)",
                    ),
                ),
                ("timestamp", models.DateTimeField(verbose_name="GPS Timestamp")),
                (
                    "engine_status",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("on", "Engine On"),
                            ("off", "Engine Off"),
                            ("idle", "Idle"),
                        ],
                        max_length=20,
                        null=True,
                        verbose_name="Engine Status",
                    ),
                ),
                (
                    "fuel_level_percentage",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=5,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="Fuel Level (%)",
                    ),
                ),
                (
                    "additional_data",
                    models.JSONField(
                        blank=True, default=dict, verbose_name="Additional GPS Data"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
                (
                    "route",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="gps_tracking",
                        to="transportation.route",
                        verbose_name="Route",
                    ),
                ),
                (
                    "vehicle",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="gps_tracking",
                        to="transportation.vehicle",
                        verbose_name="Vehicle",
                    ),
                ),
            ],
            options={
                "verbose_name": "GPS Tracking",
                "verbose_name_plural": "GPS Tracking",
                "ordering": ["-timestamp"],
            },
        ),
        migrations.AddIndex(
            model_name="busstop",
            index=models.Index(
                fields=["school", "status"], name="transportat_school__5f9b91_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="busstop",
            index=models.Index(fields=["code"], name="transportat_code_33afb8_idx"),
        ),
        migrations.AddIndex(
            model_name="busstop",
            index=models.Index(
                fields=["latitude", "longitude"], name="transportat_latitud_9d0ca6_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="busstop",
            unique_together={("school", "code")},
        ),
        migrations.AddIndex(
            model_name="driver",
            index=models.Index(
                fields=["school", "status"], name="transportat_school__43e8a9_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="driver",
            index=models.Index(
                fields=["license_number"], name="transportat_license_ab6c56_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="routeoptimization",
            index=models.Index(
                fields=["route", "status"], name="transportat_route_i_d9944f_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="routeoptimization",
            index=models.Index(
                fields=["optimization_type"], name="transportat_optimiz_b0ef55_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="routeoptimization",
            index=models.Index(
                fields=["processed_at"], name="transportat_process_1bfc42_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="routestop",
            index=models.Index(
                fields=["route", "sequence_order"],
                name="transportat_route_i_b59468_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="routestop",
            index=models.Index(
                fields=["bus_stop"], name="transportat_bus_sto_5f8f58_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="routestop",
            unique_together={("route", "bus_stop")},
        ),
        migrations.AddIndex(
            model_name="studenttransportation",
            index=models.Index(
                fields=["school", "status"], name="transportat_school__921ad4_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="studenttransportation",
            index=models.Index(
                fields=["route", "status"], name="transportat_route_i_45dc7e_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="studenttransportation",
            index=models.Index(
                fields=["student"], name="transportat_student_61b9b0_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="studenttransportation",
            unique_together={("student", "route")},
        ),
        migrations.AddIndex(
            model_name="vehicle",
            index=models.Index(
                fields=["school", "status"], name="transportat_school__a2a40f_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="vehicle",
            index=models.Index(
                fields=["license_plate"], name="transportat_license_36141b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="vehicle",
            index=models.Index(
                fields=["vehicle_type"], name="transportat_vehicle_61fc56_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="vehicle",
            unique_together={("school", "vehicle_number")},
        ),
        migrations.AddIndex(
            model_name="transportationanalytics",
            index=models.Index(
                fields=["school", "metric_type", "date_to"],
                name="transportat_school__7064a3_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="transportationanalytics",
            index=models.Index(
                fields=["route", "date_to"], name="transportat_route_i_6575b5_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="transportationanalytics",
            index=models.Index(
                fields=["vehicle", "date_to"], name="transportat_vehicle_1b9ce2_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="route",
            index=models.Index(
                fields=["school", "status"], name="transportat_school__b598d4_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="route",
            index=models.Index(fields=["code"], name="transportat_code_bd5b17_idx"),
        ),
        migrations.AddIndex(
            model_name="route",
            index=models.Index(
                fields=["route_type"], name="transportat_route_t_0c3e18_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="route",
            unique_together={("school", "code")},
        ),
        migrations.AddIndex(
            model_name="gpstracking",
            index=models.Index(
                fields=["vehicle", "timestamp"], name="transportat_vehicle_2ddddd_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="gpstracking",
            index=models.Index(
                fields=["route", "timestamp"], name="transportat_route_i_c69577_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="gpstracking",
            index=models.Index(
                fields=["timestamp"], name="transportat_timesta_35c7fb_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="gpstracking",
            index=models.Index(
                fields=["latitude", "longitude"], name="transportat_latitud_aa9869_idx"
            ),
        ),
    ]
