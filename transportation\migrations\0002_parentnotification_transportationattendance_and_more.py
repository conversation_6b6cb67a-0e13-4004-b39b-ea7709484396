# Generated by Django 5.2.5 on 2025-08-08 14:09

import django.core.validators
import django.db.models.deletion
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0005_integrationprovider_integration_integrationschedule_and_more"),
        ("transportation", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ParentNotification",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("pickup_reminder", "Pickup Reminder"),
                            ("dropoff_confirmation", "Drop-off Confirmation"),
                            ("delay_alert", "Delay Alert"),
                            ("absence_alert", "Absence Alert"),
                            ("route_change", "Route Change"),
                            ("fee_reminder", "Fee Reminder"),
                            ("emergency", "Emergency"),
                        ],
                        max_length=30,
                        verbose_name="Notification Type",
                    ),
                ),
                (
                    "channel",
                    models.CharField(
                        choices=[
                            ("email", "Email"),
                            ("sms", "SMS"),
                            ("whatsapp", "WhatsApp"),
                            ("push", "Push Notification"),
                            ("in_app", "In-App Notification"),
                        ],
                        max_length=20,
                        verbose_name="Channel",
                    ),
                ),
                (
                    "recipient_name",
                    models.CharField(max_length=100, verbose_name="Recipient Name"),
                ),
                (
                    "recipient_contact",
                    models.CharField(
                        help_text="Email, phone number, or other contact information",
                        max_length=100,
                        verbose_name="Recipient Contact",
                    ),
                ),
                ("subject", models.CharField(max_length=200, verbose_name="Subject")),
                ("message", models.TextField(verbose_name="Message")),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("sent", "Sent"),
                            ("delivered", "Delivered"),
                            ("read", "Read"),
                            ("failed", "Failed"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "scheduled_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Scheduled Time"
                    ),
                ),
                (
                    "sent_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Sent Time"
                    ),
                ),
                (
                    "delivered_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Delivered Time"
                    ),
                ),
                (
                    "read_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Read Time"
                    ),
                ),
                (
                    "error_message",
                    models.TextField(blank=True, verbose_name="Error Message"),
                ),
                (
                    "metadata",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Additional notification data",
                        verbose_name="Metadata",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "student_transportation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notifications",
                        to="transportation.studenttransportation",
                        verbose_name="Student Transportation",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Parent Notification",
                "verbose_name_plural": "Parent Notifications",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["student_transportation", "notification_type"],
                        name="transportat_student_8daf90_idx",
                    ),
                    models.Index(
                        fields=["status", "scheduled_time"],
                        name="transportat_status_5a5cc4_idx",
                    ),
                    models.Index(
                        fields=["sent_time"], name="transportat_sent_ti_c59ce1_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="TransportationAttendance",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("date", models.DateField(verbose_name="Date")),
                (
                    "attendance_type",
                    models.CharField(
                        choices=[("pickup", "Pickup"), ("dropoff", "Drop-off")],
                        max_length=20,
                        verbose_name="Attendance Type",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("present", "Present"),
                            ("absent", "Absent"),
                            ("late", "Late"),
                            ("early", "Early"),
                        ],
                        default="present",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                ("scheduled_time", models.TimeField(verbose_name="Scheduled Time")),
                (
                    "actual_time",
                    models.TimeField(blank=True, null=True, verbose_name="Actual Time"),
                ),
                (
                    "gps_location",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="GPS coordinates when attendance was recorded",
                        verbose_name="GPS Location",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "bus_stop",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="transportation.busstop",
                        verbose_name="Bus Stop",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "driver",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="transportation.driver",
                        verbose_name="Driver",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "student_transportation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="attendance_records",
                        to="transportation.studenttransportation",
                        verbose_name="Student Transportation",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Transportation Attendance",
                "verbose_name_plural": "Transportation Attendance",
                "indexes": [
                    models.Index(
                        fields=["date", "attendance_type"],
                        name="transportat_date_f42c91_idx",
                    ),
                    models.Index(
                        fields=["student_transportation", "date"],
                        name="transportat_student_c41e4d_idx",
                    ),
                    models.Index(
                        fields=["status"], name="transportat_status_01a565_idx"
                    ),
                ],
                "unique_together": {
                    ("student_transportation", "date", "attendance_type")
                },
            },
        ),
        migrations.CreateModel(
            name="TransportationFee",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "month",
                    models.DateField(
                        help_text="First day of the month for which fee is calculated",
                        verbose_name="Month",
                    ),
                ),
                (
                    "calculation_type",
                    models.CharField(
                        choices=[
                            ("fixed", "Fixed Monthly Fee"),
                            ("distance", "Distance-based"),
                            ("zone", "Zone-based"),
                            ("stops", "Number of Stops"),
                        ],
                        default="fixed",
                        max_length=20,
                        verbose_name="Calculation Type",
                    ),
                ),
                (
                    "base_fee",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="Base Fee"
                    ),
                ),
                (
                    "distance_km",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=8,
                        verbose_name="Distance (KM)",
                    ),
                ),
                (
                    "distance_rate_per_km",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=6,
                        verbose_name="Rate per KM",
                    ),
                ),
                (
                    "number_of_stops",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Number of Stops"
                    ),
                ),
                (
                    "rate_per_stop",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=6,
                        verbose_name="Rate per Stop",
                    ),
                ),
                (
                    "zone_multiplier",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("1.00"),
                        max_digits=4,
                        verbose_name="Zone Multiplier",
                    ),
                ),
                (
                    "discount_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="Discount (%)",
                    ),
                ),
                (
                    "additional_charges",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=10,
                        verbose_name="Additional Charges",
                    ),
                ),
                (
                    "total_fee",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="Total Fee"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("calculated", "Calculated"),
                            ("invoiced", "Invoiced"),
                            ("paid", "Paid"),
                            ("overdue", "Overdue"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                ("due_date", models.DateField(verbose_name="Due Date")),
                (
                    "paid_date",
                    models.DateField(blank=True, null=True, verbose_name="Paid Date"),
                ),
                (
                    "payment_reference",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Payment Reference"
                    ),
                ),
                (
                    "calculation_details",
                    models.JSONField(
                        default=dict,
                        help_text="Detailed breakdown of fee calculation",
                        verbose_name="Calculation Details",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "school",
                    models.ForeignKey(
                        help_text="School this record belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.school",
                        verbose_name="School",
                    ),
                ),
                (
                    "student_transportation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="fees",
                        to="transportation.studenttransportation",
                        verbose_name="Student Transportation",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Transportation Fee",
                "verbose_name_plural": "Transportation Fees",
                "indexes": [
                    models.Index(
                        fields=["month", "status"], name="transportat_month_59e961_idx"
                    ),
                    models.Index(
                        fields=["due_date"], name="transportat_due_dat_8d0eb3_idx"
                    ),
                    models.Index(
                        fields=["student_transportation", "status"],
                        name="transportat_student_f0d387_idx",
                    ),
                ],
                "unique_together": {("student_transportation", "month")},
            },
        ),
    ]
