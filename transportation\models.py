from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator, RegexValidator
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal
import uuid
from core.models import BaseModel
from students.models import Student
from hr.models import Employee


class Vehicle(BaseModel):
    """
    Vehicle model for managing school buses and transportation vehicles
    """
    VEHICLE_TYPE_CHOICES = [
        ('bus', _('Bus')),
        ('van', _('Van')),
        ('car', _('Car')),
        ('minibus', _('Minibus')),
    ]
    
    STATUS_CHOICES = [
        ('active', _('Active')),
        ('maintenance', _('Under Maintenance')),
        ('inactive', _('Inactive')),
        ('retired', _('Retired')),
    ]

    vehicle_number = models.CharField(
        max_length=20,
        verbose_name=_('Vehicle Number'),
        help_text=_('Unique vehicle identification number')
    )
    
    license_plate = models.CharField(
        max_length=15,
        unique=True,
        verbose_name=_('License Plate'),
        validators=[RegexValidator(
            regex=r'^[A-Z0-9\-\s]+$',
            message=_('License plate must contain only letters, numbers, hyphens and spaces')
        )]
    )
    
    vehicle_type = models.CharField(
        max_length=20,
        choices=VEHICLE_TYPE_CHOICES,
        default='bus',
        verbose_name=_('Vehicle Type')
    )
    
    make = models.CharField(
        max_length=50,
        verbose_name=_('Make')
    )
    
    model = models.CharField(
        max_length=50,
        verbose_name=_('Model')
    )
    
    year = models.PositiveIntegerField(
        verbose_name=_('Year'),
        validators=[
            MinValueValidator(1990),
            MaxValueValidator(2030)
        ]
    )
    
    capacity = models.PositiveIntegerField(
        verbose_name=_('Seating Capacity'),
        validators=[MinValueValidator(1)]
    )
    
    fuel_type = models.CharField(
        max_length=20,
        choices=[
            ('petrol', _('Petrol')),
            ('diesel', _('Diesel')),
            ('electric', _('Electric')),
            ('hybrid', _('Hybrid')),
        ],
        default='diesel',
        verbose_name=_('Fuel Type')
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='active',
        verbose_name=_('Status')
    )
    
    purchase_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Purchase Date')
    )
    
    insurance_expiry = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Insurance Expiry Date')
    )
    
    registration_expiry = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Registration Expiry Date')
    )
    
    last_maintenance_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Last Maintenance Date')
    )
    
    next_maintenance_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Next Maintenance Date')
    )
    
    gps_device_id = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('GPS Device ID'),
        help_text=_('Unique identifier for GPS tracking device')
    )
    
    notes = models.TextField(
        blank=True,
        verbose_name=_('Notes')
    )

    class Meta:
        verbose_name = _('Vehicle')
        verbose_name_plural = _('Vehicles')
        unique_together = ['school', 'vehicle_number']
        indexes = [
            models.Index(fields=['school', 'status']),
            models.Index(fields=['license_plate']),
            models.Index(fields=['vehicle_type']),
        ]

    def __str__(self):
        return f"{self.vehicle_number} - {self.license_plate}"
    
    def clean(self):
        if self.insurance_expiry and self.insurance_expiry < timezone.now().date():
            raise ValidationError(_('Insurance expiry date cannot be in the past'))
        
        if self.registration_expiry and self.registration_expiry < timezone.now().date():
            raise ValidationError(_('Registration expiry date cannot be in the past'))
    
    @property
    def is_maintenance_due(self):
        """Check if maintenance is due"""
        if not self.next_maintenance_date:
            return False
        return self.next_maintenance_date <= timezone.now().date()
    
    @property
    def is_insurance_expiring_soon(self):
        """Check if insurance is expiring within 30 days"""
        if not self.insurance_expiry:
            return False
        days_until_expiry = (self.insurance_expiry - timezone.now().date()).days
        return 0 <= days_until_expiry <= 30


class Driver(BaseModel):
    """
    Driver model for managing transportation staff
    """
    STATUS_CHOICES = [
        ('active', _('Active')),
        ('inactive', _('Inactive')),
        ('suspended', _('Suspended')),
        ('terminated', _('Terminated')),
    ]

    employee = models.OneToOneField(
        Employee,
        on_delete=models.CASCADE,
        related_name='driver_profile',
        verbose_name=_('Employee')
    )
    
    license_number = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Driver License Number')
    )
    
    license_type = models.CharField(
        max_length=10,
        verbose_name=_('License Type'),
        help_text=_('e.g., CDL, Class B, etc.')
    )
    
    license_expiry = models.DateField(
        verbose_name=_('License Expiry Date')
    )
    
    medical_certificate_expiry = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Medical Certificate Expiry')
    )
    
    experience_years = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Years of Experience')
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='active',
        verbose_name=_('Status')
    )
    
    emergency_contact_name = models.CharField(
        max_length=100,
        verbose_name=_('Emergency Contact Name')
    )
    
    emergency_contact_phone = models.CharField(
        max_length=20,
        verbose_name=_('Emergency Contact Phone'),
        validators=[RegexValidator(
            regex=r'^\+?1?\d{9,15}$',
            message=_('Phone number must be entered in the format: "+999999999". Up to 15 digits allowed.')
        )]
    )
    
    notes = models.TextField(
        blank=True,
        verbose_name=_('Notes')
    )

    class Meta:
        verbose_name = _('Driver')
        verbose_name_plural = _('Drivers')
        indexes = [
            models.Index(fields=['school', 'status']),
            models.Index(fields=['license_number']),
        ]

    def __str__(self):
        return f"{self.employee.user.first_name} {self.employee.user.last_name} - {self.license_number}"
    
    def clean(self):
        if self.license_expiry < timezone.now().date():
            raise ValidationError(_('License expiry date cannot be in the past'))
    
    @property
    def is_license_expiring_soon(self):
        """Check if license is expiring within 30 days"""
        days_until_expiry = (self.license_expiry - timezone.now().date()).days
        return 0 <= days_until_expiry <= 30
    
    @property
    def full_name(self):
        return f"{self.employee.user.first_name} {self.employee.user.last_name}"


class Route(BaseModel):
    """
    Transportation route model with optimization capabilities
    """
    STATUS_CHOICES = [
        ('active', _('Active')),
        ('inactive', _('Inactive')),
        ('suspended', _('Suspended')),
        ('planning', _('Planning')),
    ]
    
    ROUTE_TYPE_CHOICES = [
        ('morning', _('Morning Route')),
        ('afternoon', _('Afternoon Route')),
        ('both', _('Both Morning & Afternoon')),
        ('special', _('Special Route')),
    ]

    name = models.CharField(
        max_length=100,
        verbose_name=_('Route Name')
    )
    
    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Route Name (Arabic)')
    )
    
    code = models.CharField(
        max_length=20,
        verbose_name=_('Route Code'),
        help_text=_('Unique route identifier')
    )
    
    route_type = models.CharField(
        max_length=20,
        choices=ROUTE_TYPE_CHOICES,
        default='both',
        verbose_name=_('Route Type')
    )
    
    vehicle = models.ForeignKey(
        Vehicle,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='routes',
        verbose_name=_('Assigned Vehicle')
    )
    
    primary_driver = models.ForeignKey(
        Driver,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='primary_routes',
        verbose_name=_('Primary Driver')
    )
    
    backup_driver = models.ForeignKey(
        Driver,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='backup_routes',
        verbose_name=_('Backup Driver')
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='planning',
        verbose_name=_('Status')
    )
    
    start_time_morning = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_('Morning Start Time')
    )
    
    start_time_afternoon = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_('Afternoon Start Time')
    )
    
    estimated_duration_minutes = models.PositiveIntegerField(
        default=60,
        verbose_name=_('Estimated Duration (Minutes)')
    )
    
    total_distance_km = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Total Distance (KM)')
    )
    
    max_capacity = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Maximum Capacity'),
        help_text=_('Maximum number of students for this route')
    )
    
    current_occupancy = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Current Occupancy'),
        help_text=_('Current number of students assigned to this route')
    )
    
    monthly_fee = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Monthly Transportation Fee')
    )
    
    route_coordinates = models.JSONField(
        default=list,
        blank=True,
        verbose_name=_('Route Coordinates'),
        help_text=_('GPS coordinates for the optimized route path')
    )
    
    optimization_data = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Route Optimization Data'),
        help_text=_('Data from route optimization algorithms')
    )
    
    notes = models.TextField(
        blank=True,
        verbose_name=_('Notes')
    )

    class Meta:
        verbose_name = _('Route')
        verbose_name_plural = _('Routes')
        unique_together = ['school', 'code']
        indexes = [
            models.Index(fields=['school', 'status']),
            models.Index(fields=['code']),
            models.Index(fields=['route_type']),
        ]

    def __str__(self):
        return f"{self.code} - {self.name}"
    
    def clean(self):
        if self.vehicle and self.max_capacity > self.vehicle.capacity:
            raise ValidationError(_('Route capacity cannot exceed vehicle capacity'))
        
        if self.current_occupancy > self.max_capacity:
            raise ValidationError(_('Current occupancy cannot exceed maximum capacity'))
    
    @property
    def occupancy_percentage(self):
        """Calculate occupancy percentage"""
        if self.max_capacity == 0:
            return 0
        return (self.current_occupancy / self.max_capacity) * 100
    
    @property
    def available_seats(self):
        """Calculate available seats"""
        return self.max_capacity - self.current_occupancy
    
    @property
    def is_full(self):
        """Check if route is at full capacity"""
        return self.current_occupancy >= self.max_capacity
    
    def update_occupancy(self):
        """Update current occupancy based on active student assignments"""
        from .models import StudentTransportation  # Avoid circular import
        self.current_occupancy = StudentTransportation.objects.filter(
            route=self,
            status='active'
        ).count()
        self.save(update_fields=['current_occupancy'])


class BusStop(BaseModel):
    """
    Bus stop model for managing pickup/drop-off locations
    """
    STATUS_CHOICES = [
        ('active', _('Active')),
        ('inactive', _('Inactive')),
        ('temporary', _('Temporary')),
    ]

    name = models.CharField(
        max_length=100,
        verbose_name=_('Stop Name')
    )
    
    name_ar = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Stop Name (Arabic)')
    )
    
    code = models.CharField(
        max_length=20,
        verbose_name=_('Stop Code'),
        help_text=_('Unique stop identifier')
    )
    
    address = models.TextField(
        verbose_name=_('Address')
    )
    
    latitude = models.DecimalField(
        max_digits=10,
        decimal_places=8,
        null=True,
        blank=True,
        verbose_name=_('Latitude')
    )
    
    longitude = models.DecimalField(
        max_digits=11,
        decimal_places=8,
        null=True,
        blank=True,
        verbose_name=_('Longitude')
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='active',
        verbose_name=_('Status')
    )
    
    landmark = models.CharField(
        max_length=200,
        blank=True,
        verbose_name=_('Nearby Landmark')
    )
    
    safety_rating = models.PositiveIntegerField(
        default=5,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_('Safety Rating'),
        help_text=_('Safety rating from 1 (poor) to 5 (excellent)')
    )
    
    accessibility_features = models.JSONField(
        default=list,
        blank=True,
        verbose_name=_('Accessibility Features'),
        help_text=_('List of accessibility features available at this stop')
    )
    
    notes = models.TextField(
        blank=True,
        verbose_name=_('Notes')
    )

    class Meta:
        verbose_name = _('Bus Stop')
        verbose_name_plural = _('Bus Stops')
        unique_together = ['school', 'code']
        indexes = [
            models.Index(fields=['school', 'status']),
            models.Index(fields=['code']),
            models.Index(fields=['latitude', 'longitude']),
        ]

    def __str__(self):
        return f"{self.code} - {self.name}"
    
    def clean(self):
        if self.latitude and (self.latitude < -90 or self.latitude > 90):
            raise ValidationError(_('Latitude must be between -90 and 90'))
        
        if self.longitude and (self.longitude < -180 or self.longitude > 180):
            raise ValidationError(_('Longitude must be between -180 and 180'))
    
    @property
    def coordinates(self):
        """Return coordinates as tuple"""
        if self.latitude and self.longitude:
            return (float(self.latitude), float(self.longitude))
        return None
    
    @property
    def google_maps_url(self):
        """Generate Google Maps URL for this location"""
        if self.coordinates:
            lat, lng = self.coordinates
            return f"https://www.google.com/maps?q={lat},{lng}"
        return None


class RouteStop(BaseModel):
    """
    Junction model for route-stop relationships with sequencing
    """
    route = models.ForeignKey(
        Route,
        on_delete=models.CASCADE,
        related_name='route_stops',
        verbose_name=_('Route')
    )
    
    bus_stop = models.ForeignKey(
        BusStop,
        on_delete=models.CASCADE,
        related_name='route_stops',
        verbose_name=_('Bus Stop')
    )
    
    sequence_order = models.PositiveIntegerField(
        verbose_name=_('Sequence Order'),
        help_text=_('Order of this stop in the route sequence')
    )
    
    estimated_arrival_time_morning = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_('Estimated Morning Arrival Time')
    )
    
    estimated_arrival_time_afternoon = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_('Estimated Afternoon Arrival Time')
    )
    
    estimated_departure_time_morning = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_('Estimated Morning Departure Time')
    )
    
    estimated_departure_time_afternoon = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_('Estimated Afternoon Departure Time')
    )
    
    distance_from_previous_km = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Distance from Previous Stop (KM)')
    )
    
    estimated_travel_time_minutes = models.PositiveIntegerField(
        default=5,
        verbose_name=_('Estimated Travel Time (Minutes)'),
        help_text=_('Travel time from previous stop')
    )
    
    stop_duration_minutes = models.PositiveIntegerField(
        default=2,
        verbose_name=_('Stop Duration (Minutes)'),
        help_text=_('Time spent at this stop for pickup/drop-off')
    )
    
    is_pickup_point = models.BooleanField(
        default=True,
        verbose_name=_('Is Pickup Point'),
        help_text=_('Whether this stop is used for morning pickup')
    )
    
    is_dropoff_point = models.BooleanField(
        default=True,
        verbose_name=_('Is Drop-off Point'),
        help_text=_('Whether this stop is used for afternoon drop-off')
    )

    class Meta:
        verbose_name = _('Route Stop')
        verbose_name_plural = _('Route Stops')
        unique_together = ['route', 'bus_stop']
        ordering = ['route', 'sequence_order']
        indexes = [
            models.Index(fields=['route', 'sequence_order']),
            models.Index(fields=['bus_stop']),
        ]

    def __str__(self):
        return f"{self.route.code} - {self.bus_stop.name} (#{self.sequence_order})"
    
    def clean(self):
        # Ensure sequence order is unique within route
        if RouteStop.objects.filter(
            route=self.route,
            sequence_order=self.sequence_order
        ).exclude(pk=self.pk).exists():
            raise ValidationError(_('Sequence order must be unique within the route'))


class StudentTransportation(BaseModel):
    """
    Model for managing student transportation assignments
    """
    STATUS_CHOICES = [
        ('active', _('Active')),
        ('inactive', _('Inactive')),
        ('suspended', _('Suspended')),
        ('graduated', _('Graduated')),
    ]

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='transportation_assignments',
        verbose_name=_('Student')
    )
    
    route = models.ForeignKey(
        Route,
        on_delete=models.CASCADE,
        related_name='student_assignments',
        verbose_name=_('Route')
    )
    
    pickup_stop = models.ForeignKey(
        BusStop,
        on_delete=models.CASCADE,
        related_name='pickup_assignments',
        verbose_name=_('Pickup Stop')
    )
    
    dropoff_stop = models.ForeignKey(
        BusStop,
        on_delete=models.CASCADE,
        related_name='dropoff_assignments',
        verbose_name=_('Drop-off Stop')
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='active',
        verbose_name=_('Status')
    )
    
    start_date = models.DateField(
        verbose_name=_('Start Date')
    )
    
    end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('End Date')
    )
    
    monthly_fee = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Monthly Fee')
    )
    
    emergency_contact_name = models.CharField(
        max_length=100,
        verbose_name=_('Emergency Contact Name')
    )
    
    emergency_contact_phone = models.CharField(
        max_length=20,
        verbose_name=_('Emergency Contact Phone'),
        validators=[RegexValidator(
            regex=r'^\+?1?\d{9,15}$',
            message=_('Phone number must be entered in the format: "+999999999". Up to 15 digits allowed.')
        )]
    )
    
    special_needs = models.TextField(
        blank=True,
        verbose_name=_('Special Needs'),
        help_text=_('Any special transportation requirements or medical needs')
    )
    
    notes = models.TextField(
        blank=True,
        verbose_name=_('Notes')
    )

    class Meta:
        verbose_name = _('Student Transportation')
        verbose_name_plural = _('Student Transportation')
        unique_together = ['student', 'route']
        indexes = [
            models.Index(fields=['school', 'status']),
            models.Index(fields=['route', 'status']),
            models.Index(fields=['student']),
        ]

    def __str__(self):
        return f"{self.student} - {self.route.name}"
    
    def clean(self):
        if self.end_date and self.end_date < self.start_date:
            raise ValidationError(_('End date cannot be before start date'))
        
        # Ensure pickup and dropoff stops are part of the route
        if not RouteStop.objects.filter(route=self.route, bus_stop=self.pickup_stop).exists():
            raise ValidationError(_('Pickup stop must be part of the selected route'))
        
        if not RouteStop.objects.filter(route=self.route, bus_stop=self.dropoff_stop).exists():
            raise ValidationError(_('Drop-off stop must be part of the selected route'))


class RouteOptimization(BaseModel):
    """
    Model for storing route optimization results and analytics
    """
    OPTIMIZATION_TYPE_CHOICES = [
        ('distance', _('Distance Optimization')),
        ('time', _('Time Optimization')),
        ('fuel', _('Fuel Efficiency')),
        ('capacity', _('Capacity Optimization')),
        ('hybrid', _('Hybrid Optimization')),
    ]
    
    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('processing', _('Processing')),
        ('completed', _('Completed')),
        ('failed', _('Failed')),
    ]

    route = models.ForeignKey(
        Route,
        on_delete=models.CASCADE,
        related_name='optimizations',
        verbose_name=_('Route')
    )
    
    optimization_type = models.CharField(
        max_length=20,
        choices=OPTIMIZATION_TYPE_CHOICES,
        verbose_name=_('Optimization Type')
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('Status')
    )
    
    original_distance_km = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        verbose_name=_('Original Distance (KM)')
    )
    
    optimized_distance_km = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_('Optimized Distance (KM)')
    )
    
    original_duration_minutes = models.PositiveIntegerField(
        verbose_name=_('Original Duration (Minutes)')
    )
    
    optimized_duration_minutes = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name=_('Optimized Duration (Minutes)')
    )
    
    fuel_savings_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_('Fuel Savings (%)')
    )
    
    time_savings_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_('Time Savings (%)')
    )
    
    optimization_parameters = models.JSONField(
        default=dict,
        verbose_name=_('Optimization Parameters'),
        help_text=_('Parameters used for optimization algorithm')
    )
    
    optimization_results = models.JSONField(
        default=dict,
        verbose_name=_('Optimization Results'),
        help_text=_('Detailed results from optimization algorithm')
    )
    
    processed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Processed At')
    )
    
    error_message = models.TextField(
        blank=True,
        verbose_name=_('Error Message')
    )

    class Meta:
        verbose_name = _('Route Optimization')
        verbose_name_plural = _('Route Optimizations')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['route', 'status']),
            models.Index(fields=['optimization_type']),
            models.Index(fields=['processed_at']),
        ]

    def __str__(self):
        return f"{self.route.code} - {self.get_optimization_type_display()} ({self.status})"
    
    @property
    def distance_savings_km(self):
        """Calculate distance savings in kilometers"""
        if self.optimized_distance_km:
            return self.original_distance_km - self.optimized_distance_km
        return Decimal('0.00')
    
    @property
    def time_savings_minutes(self):
        """Calculate time savings in minutes"""
        if self.optimized_duration_minutes:
            return self.original_duration_minutes - self.optimized_duration_minutes
        return 0


class GPSTracking(BaseModel):
    """
    Model for storing GPS tracking data from vehicles
    """
    vehicle = models.ForeignKey(
        Vehicle,
        on_delete=models.CASCADE,
        related_name='gps_tracking',
        verbose_name=_('Vehicle')
    )
    
    route = models.ForeignKey(
        Route,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='gps_tracking',
        verbose_name=_('Route')
    )
    
    latitude = models.DecimalField(
        max_digits=10,
        decimal_places=8,
        verbose_name=_('Latitude')
    )
    
    longitude = models.DecimalField(
        max_digits=11,
        decimal_places=8,
        verbose_name=_('Longitude')
    )
    
    speed_kmh = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Speed (KM/H)')
    )
    
    heading = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_('Heading (Degrees)')
    )
    
    altitude = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_('Altitude (Meters)')
    )
    
    accuracy = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_('GPS Accuracy (Meters)')
    )
    
    timestamp = models.DateTimeField(
        verbose_name=_('GPS Timestamp')
    )
    
    engine_status = models.CharField(
        max_length=20,
        choices=[
            ('on', _('Engine On')),
            ('off', _('Engine Off')),
            ('idle', _('Idle')),
        ],
        null=True,
        blank=True,
        verbose_name=_('Engine Status')
    )
    
    fuel_level_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name=_('Fuel Level (%)')
    )
    
    additional_data = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Additional GPS Data')
    )

    class Meta:
        verbose_name = _('GPS Tracking')
        verbose_name_plural = _('GPS Tracking')
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['vehicle', 'timestamp']),
            models.Index(fields=['route', 'timestamp']),
            models.Index(fields=['timestamp']),
            models.Index(fields=['latitude', 'longitude']),
        ]

    def __str__(self):
        return f"{self.vehicle.license_plate} - {self.timestamp}"
    
    @property
    def coordinates(self):
        """Return coordinates as tuple"""
        return (float(self.latitude), float(self.longitude))
    
    @property
    def google_maps_url(self):
        """Generate Google Maps URL for this location"""
        lat, lng = self.coordinates
        return f"https://www.google.com/maps?q={lat},{lng}"


class TransportationAttendance(BaseModel):
    """
    Model for tracking student pickup/drop-off attendance
    """
    ATTENDANCE_TYPE_CHOICES = [
        ('pickup', _('Pickup')),
        ('dropoff', _('Drop-off')),
    ]
    
    STATUS_CHOICES = [
        ('present', _('Present')),
        ('absent', _('Absent')),
        ('late', _('Late')),
        ('early', _('Early')),
    ]

    student_transportation = models.ForeignKey(
        StudentTransportation,
        on_delete=models.CASCADE,
        related_name='attendance_records',
        verbose_name=_('Student Transportation')
    )
    
    date = models.DateField(
        verbose_name=_('Date')
    )
    
    attendance_type = models.CharField(
        max_length=20,
        choices=ATTENDANCE_TYPE_CHOICES,
        verbose_name=_('Attendance Type')
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='present',
        verbose_name=_('Status')
    )
    
    scheduled_time = models.TimeField(
        verbose_name=_('Scheduled Time')
    )
    
    actual_time = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_('Actual Time')
    )
    
    bus_stop = models.ForeignKey(
        BusStop,
        on_delete=models.CASCADE,
        verbose_name=_('Bus Stop')
    )
    
    driver = models.ForeignKey(
        Driver,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Driver')
    )
    
    gps_location = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('GPS Location'),
        help_text=_('GPS coordinates when attendance was recorded')
    )
    
    notes = models.TextField(
        blank=True,
        verbose_name=_('Notes')
    )

    class Meta:
        verbose_name = _('Transportation Attendance')
        verbose_name_plural = _('Transportation Attendance')
        unique_together = ['student_transportation', 'date', 'attendance_type']
        indexes = [
            models.Index(fields=['date', 'attendance_type']),
            models.Index(fields=['student_transportation', 'date']),
            models.Index(fields=['status']),
        ]

    def __str__(self):
        return f"{self.student_transportation.student} - {self.date} - {self.get_attendance_type_display()}"
    
    @property
    def is_on_time(self):
        """Check if student was on time"""
        if not self.actual_time or not self.scheduled_time:
            return None
        
        # Convert to minutes for comparison
        scheduled_minutes = self.scheduled_time.hour * 60 + self.scheduled_time.minute
        actual_minutes = self.actual_time.hour * 60 + self.actual_time.minute
        
        # Allow 5 minutes tolerance
        return abs(actual_minutes - scheduled_minutes) <= 5
    
    @property
    def delay_minutes(self):
        """Calculate delay in minutes"""
        if not self.actual_time or not self.scheduled_time:
            return 0
        
        scheduled_minutes = self.scheduled_time.hour * 60 + self.scheduled_time.minute
        actual_minutes = self.actual_time.hour * 60 + self.actual_time.minute
        
        return actual_minutes - scheduled_minutes


class TransportationFee(BaseModel):
    """
    Model for managing transportation fee calculations and payments
    """
    CALCULATION_TYPE_CHOICES = [
        ('fixed', _('Fixed Monthly Fee')),
        ('distance', _('Distance-based')),
        ('zone', _('Zone-based')),
        ('stops', _('Number of Stops')),
    ]
    
    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('calculated', _('Calculated')),
        ('invoiced', _('Invoiced')),
        ('paid', _('Paid')),
        ('overdue', _('Overdue')),
    ]

    student_transportation = models.ForeignKey(
        StudentTransportation,
        on_delete=models.CASCADE,
        related_name='fees',
        verbose_name=_('Student Transportation')
    )
    
    month = models.DateField(
        verbose_name=_('Month'),
        help_text=_('First day of the month for which fee is calculated')
    )
    
    calculation_type = models.CharField(
        max_length=20,
        choices=CALCULATION_TYPE_CHOICES,
        default='fixed',
        verbose_name=_('Calculation Type')
    )
    
    base_fee = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Base Fee')
    )
    
    distance_km = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Distance (KM)')
    )
    
    distance_rate_per_km = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Rate per KM')
    )
    
    number_of_stops = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Number of Stops')
    )
    
    rate_per_stop = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Rate per Stop')
    )
    
    zone_multiplier = models.DecimalField(
        max_digits=4,
        decimal_places=2,
        default=Decimal('1.00'),
        verbose_name=_('Zone Multiplier')
    )
    
    discount_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name=_('Discount (%)')
    )
    
    additional_charges = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Additional Charges')
    )
    
    total_fee = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('Total Fee')
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('Status')
    )
    
    due_date = models.DateField(
        verbose_name=_('Due Date')
    )
    
    paid_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Paid Date')
    )
    
    payment_reference = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_('Payment Reference')
    )
    
    calculation_details = models.JSONField(
        default=dict,
        verbose_name=_('Calculation Details'),
        help_text=_('Detailed breakdown of fee calculation')
    )
    
    notes = models.TextField(
        blank=True,
        verbose_name=_('Notes')
    )

    class Meta:
        verbose_name = _('Transportation Fee')
        verbose_name_plural = _('Transportation Fees')
        unique_together = ['student_transportation', 'month']
        indexes = [
            models.Index(fields=['month', 'status']),
            models.Index(fields=['due_date']),
            models.Index(fields=['student_transportation', 'status']),
        ]

    def __str__(self):
        return f"{self.student_transportation.student} - {self.month.strftime('%B %Y')} - {self.total_fee}"
    
    def clean(self):
        if self.paid_date and self.paid_date < self.month:
            raise ValidationError(_('Paid date cannot be before the fee month'))
    
    @property
    def is_overdue(self):
        """Check if fee is overdue"""
        return self.due_date < timezone.now().date() and self.status not in ['paid']
    
    @property
    def days_overdue(self):
        """Calculate days overdue"""
        if not self.is_overdue:
            return 0
        return (timezone.now().date() - self.due_date).days
    
    def calculate_fee(self):
        """Calculate the total fee based on calculation type"""
        if self.calculation_type == 'fixed':
            calculated_fee = self.base_fee
            
        elif self.calculation_type == 'distance':
            calculated_fee = self.base_fee + (self.distance_km * self.distance_rate_per_km)
            
        elif self.calculation_type == 'zone':
            calculated_fee = self.base_fee * self.zone_multiplier
            
        elif self.calculation_type == 'stops':
            calculated_fee = self.base_fee + (self.number_of_stops * self.rate_per_stop)
            
        else:
            calculated_fee = self.base_fee
        
        # Apply discount
        if self.discount_percentage > 0:
            discount_amount = calculated_fee * (self.discount_percentage / 100)
            calculated_fee -= discount_amount
        
        # Add additional charges
        calculated_fee += self.additional_charges
        
        # Store calculation details
        self.calculation_details = {
            'base_fee': float(self.base_fee),
            'distance_km': float(self.distance_km),
            'distance_rate_per_km': float(self.distance_rate_per_km),
            'number_of_stops': self.number_of_stops,
            'rate_per_stop': float(self.rate_per_stop),
            'zone_multiplier': float(self.zone_multiplier),
            'discount_percentage': float(self.discount_percentage),
            'additional_charges': float(self.additional_charges),
            'calculated_at': timezone.now().isoformat(),
        }
        
        self.total_fee = calculated_fee
        return calculated_fee


class ParentNotification(BaseModel):
    """
    Model for managing parent notifications related to transportation
    """
    NOTIFICATION_TYPE_CHOICES = [
        ('pickup_reminder', _('Pickup Reminder')),
        ('dropoff_confirmation', _('Drop-off Confirmation')),
        ('delay_alert', _('Delay Alert')),
        ('absence_alert', _('Absence Alert')),
        ('route_change', _('Route Change')),
        ('fee_reminder', _('Fee Reminder')),
        ('emergency', _('Emergency')),
    ]
    
    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('sent', _('Sent')),
        ('delivered', _('Delivered')),
        ('read', _('Read')),
        ('failed', _('Failed')),
    ]
    
    CHANNEL_CHOICES = [
        ('email', _('Email')),
        ('sms', _('SMS')),
        ('whatsapp', _('WhatsApp')),
        ('push', _('Push Notification')),
        ('in_app', _('In-App Notification')),
    ]

    student_transportation = models.ForeignKey(
        StudentTransportation,
        on_delete=models.CASCADE,
        related_name='notifications',
        verbose_name=_('Student Transportation')
    )
    
    notification_type = models.CharField(
        max_length=30,
        choices=NOTIFICATION_TYPE_CHOICES,
        verbose_name=_('Notification Type')
    )
    
    channel = models.CharField(
        max_length=20,
        choices=CHANNEL_CHOICES,
        verbose_name=_('Channel')
    )
    
    recipient_name = models.CharField(
        max_length=100,
        verbose_name=_('Recipient Name')
    )
    
    recipient_contact = models.CharField(
        max_length=100,
        verbose_name=_('Recipient Contact'),
        help_text=_('Email, phone number, or other contact information')
    )
    
    subject = models.CharField(
        max_length=200,
        verbose_name=_('Subject')
    )
    
    message = models.TextField(
        verbose_name=_('Message')
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('Status')
    )
    
    scheduled_time = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Scheduled Time')
    )
    
    sent_time = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Sent Time')
    )
    
    delivered_time = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Delivered Time')
    )
    
    read_time = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Read Time')
    )
    
    error_message = models.TextField(
        blank=True,
        verbose_name=_('Error Message')
    )
    
    metadata = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('Metadata'),
        help_text=_('Additional notification data')
    )

    class Meta:
        verbose_name = _('Parent Notification')
        verbose_name_plural = _('Parent Notifications')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['student_transportation', 'notification_type']),
            models.Index(fields=['status', 'scheduled_time']),
            models.Index(fields=['sent_time']),
        ]

    def __str__(self):
        return f"{self.recipient_name} - {self.get_notification_type_display()} - {self.status}"
    
    @property
    def is_overdue(self):
        """Check if notification is overdue for sending"""
        if self.status != 'pending' or not self.scheduled_time:
            return False
        return self.scheduled_time < timezone.now()


class TransportationAnalytics(BaseModel):
    """
    Model for storing transportation analytics and performance metrics
    ring transportation analytics and metrics
    """
    METRIC_TYPE_CHOICES = [
        ('daily', _('Daily Metrics')),
        ('weekly', _('Weekly Metrics')),
        ('monthly', _('Monthly Metrics')),
        ('route_performance', _('Route Performance')),
        ('fuel_consumption', _('Fuel Consumption')),
        ('maintenance_costs', _('Maintenance Costs')),
    ]

    metric_type = models.CharField(
        max_length=30,
        choices=METRIC_TYPE_CHOICES,
        verbose_name=_('Metric Type')
    )
    
    route = models.ForeignKey(
        Route,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='analytics',
        verbose_name=_('Route')
    )
    
    vehicle = models.ForeignKey(
        Vehicle,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='analytics',
        verbose_name=_('Vehicle')
    )
    
    date_from = models.DateField(
        verbose_name=_('Date From')
    )
    
    date_to = models.DateField(
        verbose_name=_('Date To')
    )
    
    total_distance_km = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Total Distance (KM)')
    )
    
    total_duration_minutes = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Total Duration (Minutes)')
    )
    
    fuel_consumed_liters = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Fuel Consumed (Liters)')
    )
    
    fuel_cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Fuel Cost')
    )
    
    maintenance_cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Maintenance Cost')
    )
    
    students_transported = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Students Transported')
    )
    
    on_time_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('On-Time Percentage')
    )
    
    average_speed_kmh = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Average Speed (KM/H)')
    )
    
    efficiency_score = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name=_('Efficiency Score')
    )
    
    metrics_data = models.JSONField(
        default=dict,
        verbose_name=_('Detailed Metrics Data')
    )

    class Meta:
        verbose_name = _('Transportation Analytics')
        verbose_name_plural = _('Transportation Analytics')
        ordering = ['-date_to']
        indexes = [
            models.Index(fields=['school', 'metric_type', 'date_to']),
            models.Index(fields=['route', 'date_to']),
            models.Index(fields=['vehicle', 'date_to']),
        ]

    def __str__(self):
        return f"{self.get_metric_type_display()} - {self.date_from} to {self.date_to}"
    
    @property
    def cost_per_km(self):
        """Calculate cost per kilometer"""
        if self.total_distance_km > 0:
            total_cost = self.fuel_cost + self.maintenance_cost
            return total_cost / self.total_distance_km
        return Decimal('0.00')
    
    @property
    def cost_per_student(self):
        """Calculate cost per student transported"""
        if self.students_transported > 0:
            total_cost = self.fuel_cost + self.maintenance_cost
            return total_cost / self.students_transported
        return Decimal('0.00')