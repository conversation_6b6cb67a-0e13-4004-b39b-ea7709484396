"""
Serializers for transportation module API
"""

from rest_framework import serializers
from .models import (
    Vehicle, Driver, Route, BusStop, RouteStop, StudentTransportation,
    RouteOptimization, GPSTracking, TransportationAnalytics,
    TransportationAttendance, TransportationFee, ParentNotification
)


class VehicleSerializer(serializers.ModelSerializer):
    """Vehicle serializer"""
    maintenance_status = serializers.SerializerMethodField()
    insurance_status = serializers.SerializerMethodField()
    
    class Meta:
        model = Vehicle
        fields = [
            'id', 'vehicle_number', 'license_plate', 'vehicle_type', 'make', 'model',
            'year', 'capacity', 'fuel_type', 'status', 'purchase_date',
            'insurance_expiry', 'registration_expiry', 'last_maintenance_date',
            'next_maintenance_date', 'gps_device_id', 'notes', 'maintenance_status',
            'insurance_status', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'maintenance_status', 'insurance_status']
    
    def get_maintenance_status(self, obj):
        return 'due' if obj.is_maintenance_due else 'ok'
    
    def get_insurance_status(self, obj):
        return 'expiring_soon' if obj.is_insurance_expiring_soon else 'valid'


class DriverSerializer(serializers.ModelSerializer):
    """Driver serializer"""
    full_name = serializers.SerializerMethodField()
    license_status = serializers.SerializerMethodField()
    employee_name = serializers.CharField(source='employee.full_name', read_only=True)
    
    class Meta:
        model = Driver
        fields = [
            'id', 'employee', 'employee_name', 'license_number', 'license_type',
            'license_expiry', 'medical_certificate_expiry', 'experience_years',
            'status', 'emergency_contact_name', 'emergency_contact_phone',
            'notes', 'full_name', 'license_status', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'full_name', 'license_status', 'employee_name', 'created_at', 'updated_at']
    
    def get_full_name(self, obj):
        return obj.full_name
    
    def get_license_status(self, obj):
        return 'expiring_soon' if obj.is_license_expiring_soon else 'valid'


class BusStopSerializer(serializers.ModelSerializer):
    """Bus stop serializer"""
    coordinates = serializers.SerializerMethodField()
    google_maps_url = serializers.SerializerMethodField()
    routes_count = serializers.SerializerMethodField()
    
    class Meta:
        model = BusStop
        fields = [
            'id', 'name', 'name_ar', 'code', 'address', 'latitude', 'longitude',
            'status', 'landmark', 'safety_rating', 'accessibility_features',
            'notes', 'coordinates', 'google_maps_url', 'routes_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'coordinates', 'google_maps_url', 'routes_count', 'created_at', 'updated_at']
    
    def get_coordinates(self, obj):
        return obj.coordinates
    
    def get_google_maps_url(self, obj):
        return obj.google_maps_url
    
    def get_routes_count(self, obj):
        return obj.route_stops.count()


class RouteStopSerializer(serializers.ModelSerializer):
    """Route stop serializer"""
    bus_stop_name = serializers.CharField(source='bus_stop.name', read_only=True)
    bus_stop_coordinates = serializers.SerializerMethodField()
    
    class Meta:
        model = RouteStop
        fields = [
            'id', 'bus_stop', 'bus_stop_name', 'bus_stop_coordinates', 'sequence_order',
            'estimated_arrival_time_morning', 'estimated_arrival_time_afternoon',
            'estimated_departure_time_morning', 'estimated_departure_time_afternoon',
            'distance_from_previous_km', 'estimated_travel_time_minutes',
            'stop_duration_minutes', 'is_pickup_point', 'is_dropoff_point'
        ]
        read_only_fields = ['id', 'bus_stop_name', 'bus_stop_coordinates']
    
    def get_bus_stop_coordinates(self, obj):
        return obj.bus_stop.coordinates


class RouteSerializer(serializers.ModelSerializer):
    """Route serializer"""
    vehicle_info = VehicleSerializer(source='vehicle', read_only=True)
    driver_info = DriverSerializer(source='primary_driver', read_only=True)
    route_stops = RouteStopSerializer(many=True, read_only=True)
    occupancy_percentage = serializers.SerializerMethodField()
    available_seats = serializers.SerializerMethodField()
    is_full = serializers.SerializerMethodField()
    
    class Meta:
        model = Route
        fields = [
            'id', 'name', 'name_ar', 'code', 'route_type', 'vehicle', 'vehicle_info',
            'primary_driver', 'driver_info', 'backup_driver', 'status',
            'start_time_morning', 'start_time_afternoon', 'estimated_duration_minutes',
            'total_distance_km', 'max_capacity', 'current_occupancy', 'monthly_fee',
            'route_coordinates', 'optimization_data', 'notes', 'route_stops',
            'occupancy_percentage', 'available_seats', 'is_full',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'vehicle_info', 'driver_info', 'route_stops', 'current_occupancy',
            'occupancy_percentage', 'available_seats', 'is_full', 'created_at', 'updated_at'
        ]
    
    def get_occupancy_percentage(self, obj):
        return obj.occupancy_percentage
    
    def get_available_seats(self, obj):
        return obj.available_seats
    
    def get_is_full(self, obj):
        return obj.is_full


class StudentTransportationSerializer(serializers.ModelSerializer):
    """Student transportation serializer"""
    student_name = serializers.CharField(source='student.full_name', read_only=True)
    route_name = serializers.CharField(source='route.name', read_only=True)
    pickup_stop_name = serializers.CharField(source='pickup_stop.name', read_only=True)
    dropoff_stop_name = serializers.CharField(source='dropoff_stop.name', read_only=True)
    
    class Meta:
        model = StudentTransportation
        fields = [
            'id', 'student', 'student_name', 'route', 'route_name',
            'pickup_stop', 'pickup_stop_name', 'dropoff_stop', 'dropoff_stop_name',
            'status', 'start_date', 'end_date', 'monthly_fee',
            'emergency_contact_name', 'emergency_contact_phone',
            'special_needs', 'notes', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'student_name', 'route_name', 'pickup_stop_name',
            'dropoff_stop_name', 'created_at', 'updated_at'
        ]


class RouteOptimizationSerializer(serializers.ModelSerializer):
    """Route optimization serializer"""
    route_name = serializers.CharField(source='route.name', read_only=True)
    distance_savings_km = serializers.SerializerMethodField()
    time_savings_minutes = serializers.SerializerMethodField()
    
    class Meta:
        model = RouteOptimization
        fields = [
            'id', 'route', 'route_name', 'optimization_type', 'status',
            'original_distance_km', 'optimized_distance_km', 'original_duration_minutes',
            'optimized_duration_minutes', 'fuel_savings_percentage', 'time_savings_percentage',
            'optimization_parameters', 'optimization_results', 'processed_at',
            'error_message', 'distance_savings_km', 'time_savings_minutes',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'route_name', 'distance_savings_km', 'time_savings_minutes',
            'created_at', 'updated_at'
        ]
    
    def get_distance_savings_km(self, obj):
        return float(obj.distance_savings_km) if obj.distance_savings_km else None
    
    def get_time_savings_minutes(self, obj):
        return obj.time_savings_minutes


class GPSTrackingSerializer(serializers.ModelSerializer):
    """GPS tracking serializer"""
    vehicle_info = serializers.SerializerMethodField()
    route_name = serializers.CharField(source='route.name', read_only=True)
    coordinates = serializers.SerializerMethodField()
    google_maps_url = serializers.SerializerMethodField()
    
    class Meta:
        model = GPSTracking
        fields = [
            'id', 'vehicle', 'vehicle_info', 'route', 'route_name',
            'latitude', 'longitude', 'coordinates', 'speed_kmh', 'heading',
            'altitude', 'accuracy', 'timestamp', 'engine_status',
            'fuel_level_percentage', 'additional_data', 'google_maps_url',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'vehicle_info', 'route_name', 'coordinates',
            'google_maps_url', 'created_at', 'updated_at'
        ]
    
    def get_vehicle_info(self, obj):
        return {
            'license_plate': obj.vehicle.license_plate,
            'vehicle_number': obj.vehicle.vehicle_number
        }
    
    def get_coordinates(self, obj):
        return obj.coordinates
    
    def get_google_maps_url(self, obj):
        return obj.google_maps_url


class TransportationAnalyticsSerializer(serializers.ModelSerializer):
    """Transportation analytics serializer"""
    route_name = serializers.CharField(source='route.name', read_only=True)
    vehicle_info = serializers.SerializerMethodField()
    cost_per_km = serializers.SerializerMethodField()
    cost_per_student = serializers.SerializerMethodField()
    date_range = serializers.SerializerMethodField()
    
    class Meta:
        model = TransportationAnalytics
        fields = [
            'id', 'metric_type', 'route', 'route_name', 'vehicle', 'vehicle_info',
            'date_from', 'date_to', 'date_range', 'total_distance_km',
            'total_duration_minutes', 'fuel_consumed_liters', 'fuel_cost',
            'maintenance_cost', 'students_transported', 'on_time_percentage',
            'average_speed_kmh', 'efficiency_score', 'metrics_data',
            'cost_per_km', 'cost_per_student', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'route_name', 'vehicle_info', 'date_range',
            'cost_per_km', 'cost_per_student', 'created_at', 'updated_at'
        ]
    
    def get_vehicle_info(self, obj):
        if obj.vehicle:
            return {
                'license_plate': obj.vehicle.license_plate,
                'vehicle_number': obj.vehicle.vehicle_number
            }
        return None
    
    def get_cost_per_km(self, obj):
        return float(obj.cost_per_km) if obj.cost_per_km else None
    
    def get_cost_per_student(self, obj):
        return float(obj.cost_per_student) if obj.cost_per_student else None
    
    def get_date_range(self, obj):
        return f"{obj.date_from} to {obj.date_to}"


# Nested serializers for complex operations
class RouteWithStopsSerializer(RouteSerializer):
    """Route serializer with detailed stops information"""
    stops_detail = serializers.SerializerMethodField()
    students_assigned = serializers.SerializerMethodField()
    
    class Meta(RouteSerializer.Meta):
        fields = RouteSerializer.Meta.fields + ['stops_detail', 'students_assigned']
    
    def get_stops_detail(self, obj):
        route_stops = obj.route_stops.select_related('bus_stop').order_by('sequence_order')
        return RouteStopSerializer(route_stops, many=True).data
    
    def get_students_assigned(self, obj):
        assignments = obj.student_assignments.filter(status='active').select_related('student')
        return StudentTransportationSerializer(assignments, many=True).data


class VehicleWithTrackingSerializer(VehicleSerializer):
    """Vehicle serializer with recent tracking data"""
    recent_tracking = serializers.SerializerMethodField()
    current_location = serializers.SerializerMethodField()
    
    class Meta(VehicleSerializer.Meta):
        fields = VehicleSerializer.Meta.fields + ['recent_tracking', 'current_location']
    
    def get_recent_tracking(self, obj):
        recent = obj.gps_tracking.order_by('-timestamp')[:5]
        return GPSTrackingSerializer(recent, many=True).data
    
    def get_current_location(self, obj):
        from .services import GPSTrackingService
        service = GPSTrackingService()
        location = service.get_vehicle_current_location(obj)
        return GPSTrackingSerializer(location).data if location else None


class TransportationAttendanceSerializer(serializers.ModelSerializer):
    """Transportation attendance serializer"""
    student_name = serializers.CharField(source='student_transportation.student.full_name', read_only=True)
    route_name = serializers.CharField(source='student_transportation.route.name', read_only=True)
    bus_stop_name = serializers.CharField(source='bus_stop.name', read_only=True)
    is_on_time = serializers.BooleanField(read_only=True)
    delay_minutes = serializers.IntegerField(read_only=True)
    
    class Meta:
        model = TransportationAttendance
        fields = [
            'id', 'student_transportation', 'student_name', 'route_name',
            'date', 'attendance_type', 'status', 'scheduled_time', 'actual_time',
            'bus_stop', 'bus_stop_name', 'driver', 'gps_location', 'notes',
            'is_on_time', 'delay_minutes', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'student_name', 'route_name', 'bus_stop_name',
            'is_on_time', 'delay_minutes', 'created_at', 'updated_at'
        ]


class TransportationFeeSerializer(serializers.ModelSerializer):
    """Transportation fee serializer"""
    student_name = serializers.CharField(source='student_transportation.student.full_name', read_only=True)
    route_name = serializers.CharField(source='student_transportation.route.name', read_only=True)
    is_overdue = serializers.BooleanField(read_only=True)
    days_overdue = serializers.IntegerField(read_only=True)
    
    class Meta:
        model = TransportationFee
        fields = [
            'id', 'student_transportation', 'student_name', 'route_name',
            'month', 'calculation_type', 'base_fee', 'distance_km',
            'distance_rate_per_km', 'number_of_stops', 'rate_per_stop',
            'zone_multiplier', 'discount_percentage', 'additional_charges',
            'total_fee', 'status', 'due_date', 'paid_date', 'payment_reference',
            'calculation_details', 'notes', 'is_overdue', 'days_overdue',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'student_name', 'route_name', 'total_fee',
            'is_overdue', 'days_overdue', 'created_at', 'updated_at'
        ]
    
    def create(self, validated_data):
        instance = super().create(validated_data)
        instance.calculate_fee()
        instance.save()
        return instance
    
    def update(self, instance, validated_data):
        instance = super().update(instance, validated_data)
        instance.calculate_fee()
        instance.save()
        return instance


class ParentNotificationSerializer(serializers.ModelSerializer):
    """Parent notification serializer"""
    student_name = serializers.CharField(source='student_transportation.student.full_name', read_only=True)
    route_name = serializers.CharField(source='student_transportation.route.name', read_only=True)
    is_overdue = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = ParentNotification
        fields = [
            'id', 'student_transportation', 'student_name', 'route_name',
            'notification_type', 'channel', 'recipient_name', 'recipient_contact',
            'subject', 'message', 'status', 'scheduled_time', 'sent_time',
            'delivered_time', 'read_time', 'error_message', 'metadata',
            'is_overdue', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'student_name', 'route_name', 'is_overdue',
            'sent_time', 'delivered_time', 'read_time', 'created_at', 'updated_at'
        ]


class RouteDashboardSerializer(serializers.Serializer):
    """Serializer for route dashboard data"""
    route = RouteSerializer()
    vehicle = VehicleSerializer()
    driver = DriverSerializer()
    stops = RouteStopSerializer(many=True)
    students = StudentTransportationSerializer(many=True)
    current_location = GPSTrackingSerializer()
    performance = serializers.DictField()
    
    class Meta:
        fields = [
            'route', 'vehicle', 'driver', 'stops', 'students',
            'current_location', 'performance'
        ]