"""
Transportation services for route management and optimization
"""

import math
import json
from typing import List, Dict, Tuple, Optional
from decimal import Decimal
from datetime import datetime, timedelta
from django.db import transaction
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.db.models import Q, Count, Sum, Avg, F
from django.utils.translation import gettext_lazy as _
from .models import (
    Route, BusStop, RouteStop, Vehicle, Driver, StudentTransportation,
    RouteOptimization, GPSTracking, TransportationAnalytics,
    TransportationFee, TransportationAttendance, ParentNotification
)


class RouteOptimizationService:
    """
    Service for optimizing transportation routes using various algorithms
    """
    
    def __init__(self):
        self.earth_radius_km = 6371.0  # Earth's radius in kilometers
    
    def calculate_distance(self, coord1: Tuple[float, float], coord2: Tuple[float, float]) -> float:
        """
        Calculate the great circle distance between two points on Earth using Haversine formula
        
        Args:
            coord1: Tuple of (latitude, longitude) for first point
            coord2: Tuple of (latitude, longitude) for second point
            
        Returns:
            Distance in kilometers
        """
        lat1, lon1 = math.radians(coord1[0]), math.radians(coord1[1])
        lat2, lon2 = math.radians(coord2[0]), math.radians(coord2[1])
        
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        
        return self.earth_radius_km * c
    
    def calculate_route_distance(self, stops: List[BusStop]) -> float:
        """
        Calculate total distance for a route with given stops
        
        Args:
            stops: List of BusStop objects in order
            
        Returns:
            Total distance in kilometers
        """
        if len(stops) < 2:
            return 0.0
        
        total_distance = 0.0
        for i in range(len(stops) - 1):
            coord1 = stops[i].coordinates
            coord2 = stops[i + 1].coordinates
            
            if coord1 and coord2:
                total_distance += self.calculate_distance(coord1, coord2)
        
        return total_distance
    
    def nearest_neighbor_optimization(self, route: Route) -> Dict:
        """
        Optimize route using Nearest Neighbor algorithm
        
        Args:
            route: Route object to optimize
            
        Returns:
            Dictionary with optimization results
        """
        route_stops = RouteStop.objects.filter(route=route).select_related('bus_stop')
        
        if route_stops.count() < 2:
            return {'error': 'Route must have at least 2 stops for optimization'}
        
        stops = [rs.bus_stop for rs in route_stops]
        
        # Start with the first stop
        optimized_stops = [stops[0]]
        remaining_stops = stops[1:]
        
        current_stop = stops[0]
        
        while remaining_stops:
            # Find nearest unvisited stop
            nearest_stop = None
            min_distance = float('inf')
            
            for stop in remaining_stops:
                if current_stop.coordinates and stop.coordinates:
                    distance = self.calculate_distance(
                        current_stop.coordinates,
                        stop.coordinates
                    )
                    if distance < min_distance:
                        min_distance = distance
                        nearest_stop = stop
            
            if nearest_stop:
                optimized_stops.append(nearest_stop)
                remaining_stops.remove(nearest_stop)
                current_stop = nearest_stop
            else:
                # If no coordinates available, just add remaining stops in order
                optimized_stops.extend(remaining_stops)
                break
        
        # Calculate distances
        original_distance = self.calculate_route_distance(stops)
        optimized_distance = self.calculate_route_distance(optimized_stops)
        
        return {
            'original_distance': original_distance,
            'optimized_distance': optimized_distance,
            'optimized_stops': optimized_stops,
            'savings_km': original_distance - optimized_distance,
            'savings_percentage': ((original_distance - optimized_distance) / original_distance * 100) if original_distance > 0 else 0
        }
    
    @transaction.atomic
    def optimize_route(self, route: Route, optimization_type: str = 'nearest_neighbor') -> RouteOptimization:
        """
        Optimize a route and save the results
        
        Args:
            route: Route object to optimize
            optimization_type: Type of optimization algorithm to use
            
        Returns:
            RouteOptimization object with results
        """
        # Create optimization record
        optimization = RouteOptimization.objects.create(
            school=route.school,
            route=route,
            optimization_type=optimization_type,
            status='processing',
            original_distance_km=route.total_distance_km,
            original_duration_minutes=route.estimated_duration_minutes,
            optimization_parameters={
                'algorithm': optimization_type,
                'timestamp': timezone.now().isoformat()
            }
        )
        
        try:
            # Run optimization algorithm
            if optimization_type == 'nearest_neighbor':
                results = self.nearest_neighbor_optimization(route)
            else:
                raise ValueError(f"Unknown optimization type: {optimization_type}")
            
            if 'error' in results:
                optimization.status = 'failed'
                optimization.error_message = results['error']
            else:
                # Update optimization record with results
                optimization.optimized_distance_km = Decimal(str(results['optimized_distance']))
                optimization.fuel_savings_percentage = Decimal(str(results['savings_percentage']))
                optimization.time_savings_percentage = Decimal(str(results['savings_percentage']))  # Assume same as distance
                
                # Convert BusStop objects to serializable data
                serializable_results = results.copy()
                if 'optimized_stops' in serializable_results:
                    serializable_results['optimized_stops'] = [
                        {'id': str(stop.id), 'name': stop.name, 'code': stop.code}
                        for stop in results['optimized_stops']
                    ]
                
                optimization.optimization_results = serializable_results
                optimization.status = 'completed'
            
            optimization.processed_at = timezone.now()
            optimization.save()
            
        except Exception as e:
            optimization.status = 'failed'
            optimization.error_message = str(e)
            optimization.processed_at = timezone.now()
            optimization.save()
            raise
        
        return optimization


class RouteAnalyticsService:
    """
    Service for generating transportation analytics and reports
    """
    
    def calculate_route_efficiency(self, route: Route, date_from: datetime, date_to: datetime) -> Dict:
        """
        Calculate efficiency metrics for a route over a time period
        """
        gps_data = GPSTracking.objects.filter(
            route=route,
            timestamp__range=[date_from, date_to]
        ).order_by('timestamp')
        
        if not gps_data.exists():
            return {'error': 'No GPS data available for the specified period'}
        
        total_distance = 0.0
        total_duration = 0
        on_time_trips = 0
        total_trips = 0
        
        # Group GPS data by trips (assuming gaps > 30 minutes indicate new trips)
        trips = []
        current_trip = []
        
        for gps_point in gps_data:
            if (current_trip and 
                (gps_point.timestamp - current_trip[-1].timestamp).total_seconds() > 1800):  # 30 minutes
                trips.append(current_trip)
                current_trip = [gps_point]
            else:
                current_trip.append(gps_point)
        
        if current_trip:
            trips.append(current_trip)
        
        # Analyze each trip
        for trip in trips:
            if len(trip) < 2:
                continue
            
            trip_distance = 0.0
            trip_duration = (trip[-1].timestamp - trip[0].timestamp).total_seconds() / 60  # minutes
            
            # Calculate trip distance
            for i in range(len(trip) - 1):
                coord1 = (float(trip[i].latitude), float(trip[i].longitude))
                coord2 = (float(trip[i+1].latitude), float(trip[i+1].longitude))
                trip_distance += RouteOptimizationService().calculate_distance(coord1, coord2)
            
            total_distance += trip_distance
            total_duration += trip_duration
            total_trips += 1
            
            # Check if trip was on time (within 10 minutes of scheduled time)
            if trip_duration <= route.estimated_duration_minutes * 1.1:  # 10% tolerance
                on_time_trips += 1
        
        # Calculate metrics
        on_time_percentage = (on_time_trips / total_trips * 100) if total_trips > 0 else 0
        average_speed = (total_distance / (total_duration / 60)) if total_duration > 0 else 0  # km/h
        efficiency_score = min(100, (on_time_percentage + min(100, average_speed * 2)) / 2)
        
        return {
            'total_distance_km': total_distance,
            'total_duration_minutes': total_duration,
            'total_trips': total_trips,
            'on_time_trips': on_time_trips,
            'on_time_percentage': on_time_percentage,
            'average_speed_kmh': average_speed,
            'efficiency_score': efficiency_score,
        }
    
    @transaction.atomic
    def generate_route_analytics(self, route: Route, date_from: datetime, date_to: datetime) -> TransportationAnalytics:
        """
        Generate and save analytics for a route
        """
        metrics = self.calculate_route_efficiency(route, date_from, date_to)
        
        if 'error' in metrics:
            raise ValidationError(metrics['error'])
        
        # Count students transported
        students_count = StudentTransportation.objects.filter(
            route=route,
            status='active',
            start_date__lte=date_to.date()
        ).count()
        
        analytics = TransportationAnalytics.objects.create(
            school=route.school,
            route=route,
            vehicle=route.vehicle,
            metric_type='route_performance',
            date_from=date_from.date(),
            date_to=date_to.date(),
            total_distance_km=Decimal(str(metrics['total_distance_km'])),
            total_duration_minutes=int(metrics['total_duration_minutes']),
            students_transported=students_count,
            on_time_percentage=Decimal(str(metrics['on_time_percentage'])),
            average_speed_kmh=Decimal(str(metrics['average_speed_kmh'])),
            efficiency_score=Decimal(str(metrics['efficiency_score'])),
            metrics_data=metrics
        )
        
        return analytics
    
    def generate_fuel_consumption_analytics(self, vehicle: Vehicle, date_from: datetime, date_to: datetime) -> TransportationAnalytics:
        """
        Generate fuel consumption analytics for a vehicle
        """
        gps_data = GPSTracking.objects.filter(
            vehicle=vehicle,
            timestamp__range=[date_from, date_to],
            fuel_level_percentage__isnull=False
        ).order_by('timestamp')
        
        if not gps_data.exists():
            raise ValidationError('No fuel data available for the specified period')
        
        # Calculate fuel consumption
        fuel_readings = [float(reading.fuel_level_percentage) for reading in gps_data]
        total_fuel_consumed = max(fuel_readings) - min(fuel_readings) if fuel_readings else 0
        
        # Calculate distance traveled
        total_distance = 0.0
        for i in range(len(gps_data) - 1):
            coord1 = gps_data[i].coordinates
            coord2 = gps_data[i + 1].coordinates
            if coord1 and coord2:
                total_distance += RouteOptimizationService().calculate_distance(coord1, coord2)
        
        # Calculate fuel efficiency (km per liter)
        fuel_efficiency = total_distance / (total_fuel_consumed / 100 * vehicle.capacity) if total_fuel_consumed > 0 else 0
        
        analytics = TransportationAnalytics.objects.create(
            school=vehicle.school,
            vehicle=vehicle,
            metric_type='fuel_consumption',
            date_from=date_from.date(),
            date_to=date_to.date(),
            total_distance_km=Decimal(str(total_distance)),
            fuel_consumed_liters=Decimal(str(total_fuel_consumed)),
            metrics_data={
                'fuel_efficiency_km_per_liter': fuel_efficiency,
                'fuel_readings': fuel_readings,
                'total_fuel_consumed_percentage': total_fuel_consumed
            }
        )
        
        return analytics
    
    def generate_maintenance_cost_analytics(self, vehicle: Vehicle, date_from: datetime, date_to: datetime) -> TransportationAnalytics:
        """
        Generate maintenance cost analytics for a vehicle
        """
        # This would integrate with a maintenance management system
        # For now, we'll create a placeholder implementation
        
        # Calculate distance traveled for maintenance scheduling
        gps_data = GPSTracking.objects.filter(
            vehicle=vehicle,
            timestamp__range=[date_from, date_to]
        ).order_by('timestamp')
        
        total_distance = 0.0
        for i in range(len(gps_data) - 1):
            coord1 = gps_data[i].coordinates
            coord2 = gps_data[i + 1].coordinates
            if coord1 and coord2:
                total_distance += RouteOptimizationService().calculate_distance(coord1, coord2)
        
        # Estimate maintenance costs based on distance and vehicle age
        vehicle_age = timezone.now().year - vehicle.year
        base_cost_per_km = Decimal('0.15')  # Base maintenance cost per km
        age_multiplier = Decimal(str(1 + (vehicle_age * 0.1)))  # 10% increase per year
        
        estimated_maintenance_cost = Decimal(str(total_distance)) * base_cost_per_km * age_multiplier
        
        analytics = TransportationAnalytics.objects.create(
            school=vehicle.school,
            vehicle=vehicle,
            metric_type='maintenance_costs',
            date_from=date_from.date(),
            date_to=date_to.date(),
            total_distance_km=Decimal(str(total_distance)),
            maintenance_cost=estimated_maintenance_cost,
            metrics_data={
                'vehicle_age': vehicle_age,
                'cost_per_km': float(base_cost_per_km * age_multiplier),
                'estimated_cost': float(estimated_maintenance_cost)
            }
        )
        
        return analytics
    
    def generate_comprehensive_route_report(self, route: Route, date_from: datetime, date_to: datetime) -> Dict:
        """
        Generate comprehensive analytics report for a route
        """
        report = {
            'route': route,
            'period': {
                'from': date_from.date(),
                'to': date_to.date(),
                'days': (date_to.date() - date_from.date()).days + 1
            },
            'performance': {},
            'efficiency': {},
            'costs': {},
            'students': {},
            'safety': {}
        }
        
        # Performance metrics
        try:
            performance_analytics = self.generate_route_analytics(route, date_from, date_to)
            report['performance'] = {
                'total_distance_km': float(performance_analytics.total_distance_km),
                'total_duration_minutes': performance_analytics.total_duration_minutes,
                'on_time_percentage': float(performance_analytics.on_time_percentage),
                'efficiency_score': float(performance_analytics.efficiency_score),
                'average_speed_kmh': float(performance_analytics.average_speed_kmh)
            }
        except ValidationError:
            report['performance'] = {'error': 'No GPS data available'}
        
        # Fuel efficiency
        if route.vehicle:
            try:
                fuel_analytics = self.generate_fuel_consumption_analytics(route.vehicle, date_from, date_to)
                report['efficiency'] = {
                    'fuel_consumed_liters': float(fuel_analytics.fuel_consumed_liters),
                    'fuel_efficiency': fuel_analytics.metrics_data.get('fuel_efficiency_km_per_liter', 0),
                    'cost_per_km': float(fuel_analytics.fuel_consumed_liters) * 1.5 / float(fuel_analytics.total_distance_km) if fuel_analytics.total_distance_km > 0 else 0
                }
            except ValidationError:
                report['efficiency'] = {'error': 'No fuel data available'}
        
        # Cost analysis
        if route.vehicle:
            try:
                maintenance_analytics = self.generate_maintenance_cost_analytics(route.vehicle, date_from, date_to)
                report['costs'] = {
                    'maintenance_cost': float(maintenance_analytics.maintenance_cost),
                    'cost_per_km': maintenance_analytics.metrics_data.get('cost_per_km', 0),
                    'estimated_annual_cost': float(maintenance_analytics.maintenance_cost) * 365 / report['period']['days']
                }
            except Exception:
                report['costs'] = {'error': 'Unable to calculate costs'}
        
        # Student statistics
        active_students = StudentTransportation.objects.filter(
            route=route,
            status='active',
            start_date__lte=date_to.date()
        )
        
        report['students'] = {
            'total_assigned': active_students.count(),
            'capacity_utilization': (active_students.count() / route.max_capacity * 100) if route.max_capacity > 0 else 0,
            'revenue_potential': sum(float(s.monthly_fee) for s in active_students)
        }
        
        # Safety metrics
        if route.vehicle:
            safety_incidents = GPSTracking.objects.filter(
                vehicle=route.vehicle,
                timestamp__range=[date_from, date_to],
                speed_kmh__gt=80  # Speeding incidents
            ).count()
            
            report['safety'] = {
                'speeding_incidents': safety_incidents,
                'safety_score': max(0, 100 - (safety_incidents * 10)),  # Deduct 10 points per incident
                'average_speed': report['performance'].get('average_speed_kmh', 0)
            }
        
        return report
    
    def generate_fleet_analytics(self, school, date_from: datetime, date_to: datetime) -> Dict:
        """
        Generate fleet-wide analytics for a school
        """
        vehicles = Vehicle.objects.filter(school=school, status='active')
        routes = Route.objects.filter(school=school, status='active')
        
        fleet_report = {
            'school': school,
            'period': {
                'from': date_from.date(),
                'to': date_to.date(),
                'days': (date_to.date() - date_from.date()).days + 1
            },
            'fleet_summary': {
                'total_vehicles': vehicles.count(),
                'total_routes': routes.count(),
                'total_capacity': sum(v.capacity for v in vehicles),
                'vehicles_in_service': vehicles.filter(status='active').count()
            },
            'performance_summary': {},
            'cost_summary': {},
            'utilization_summary': {},
            'route_reports': []
        }
        
        # Aggregate performance metrics
        total_distance = Decimal('0.00')
        total_fuel_consumed = Decimal('0.00')
        total_maintenance_cost = Decimal('0.00')
        total_students = 0
        
        for route in routes:
            try:
                route_report = self.generate_comprehensive_route_report(route, date_from, date_to)
                fleet_report['route_reports'].append(route_report)
                
                # Aggregate metrics
                if 'performance' in route_report and 'total_distance_km' in route_report['performance']:
                    total_distance += Decimal(str(route_report['performance']['total_distance_km']))
                
                if 'efficiency' in route_report and 'fuel_consumed_liters' in route_report['efficiency']:
                    total_fuel_consumed += Decimal(str(route_report['efficiency']['fuel_consumed_liters']))
                
                if 'costs' in route_report and 'maintenance_cost' in route_report['costs']:
                    total_maintenance_cost += Decimal(str(route_report['costs']['maintenance_cost']))
                
                if 'students' in route_report:
                    total_students += route_report['students']['total_assigned']
                    
            except Exception as e:
                # Log error but continue with other routes
                print(f"Error generating report for route {route.code}: {e}")
        
        # Calculate fleet-wide metrics
        fleet_report['performance_summary'] = {
            'total_distance_km': float(total_distance),
            'average_distance_per_vehicle': float(total_distance) / vehicles.count() if vehicles.count() > 0 else 0,
            'total_fuel_consumed_liters': float(total_fuel_consumed),
            'fleet_fuel_efficiency': float(total_distance) / float(total_fuel_consumed) if total_fuel_consumed > 0 else 0
        }
        
        fleet_report['cost_summary'] = {
            'total_maintenance_cost': float(total_maintenance_cost),
            'cost_per_vehicle': float(total_maintenance_cost) / vehicles.count() if vehicles.count() > 0 else 0,
            'cost_per_km': float(total_maintenance_cost) / float(total_distance) if total_distance > 0 else 0,
            'cost_per_student': float(total_maintenance_cost) / total_students if total_students > 0 else 0
        }
        
        fleet_report['utilization_summary'] = {
            'total_students_transported': total_students,
            'average_students_per_route': total_students / routes.count() if routes.count() > 0 else 0,
            'fleet_capacity_utilization': (total_students / fleet_report['fleet_summary']['total_capacity'] * 100) if fleet_report['fleet_summary']['total_capacity'] > 0 else 0
        }
        
        return fleet_report
    
    def get_performance_trends(self, route: Route, days: int = 30) -> Dict:
        """
        Get performance trends for a route over time
        """
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        # Get analytics data points
        analytics = TransportationAnalytics.objects.filter(
            route=route,
            date_from__gte=start_date.date(),
            metric_type='route_performance'
        ).order_by('date_from')
        
        trends = {
            'route': route,
            'period_days': days,
            'data_points': analytics.count(),
            'trends': {
                'efficiency_score': [],
                'on_time_percentage': [],
                'distance_per_day': [],
                'fuel_efficiency': []
            },
            'averages': {},
            'improvements': {}
        }
        
        if analytics.exists():
            # Extract trend data
            for analytic in analytics:
                trends['trends']['efficiency_score'].append({
                    'date': analytic.date_from,
                    'value': float(analytic.efficiency_score)
                })
                trends['trends']['on_time_percentage'].append({
                    'date': analytic.date_from,
                    'value': float(analytic.on_time_percentage)
                })
                trends['trends']['distance_per_day'].append({
                    'date': analytic.date_from,
                    'value': float(analytic.total_distance_km)
                })
            
            # Calculate averages
            trends['averages'] = {
                'efficiency_score': sum(a.efficiency_score for a in analytics) / analytics.count(),
                'on_time_percentage': sum(a.on_time_percentage for a in analytics) / analytics.count(),
                'average_distance_per_day': sum(a.total_distance_km for a in analytics) / analytics.count()
            }
            
            # Calculate improvements (compare first half vs second half)
            mid_point = analytics.count() // 2
            if mid_point > 0:
                first_half = analytics[:mid_point]
                second_half = analytics[mid_point:]
                
                first_half_avg = sum(a.efficiency_score for a in first_half) / len(first_half)
                second_half_avg = sum(a.efficiency_score for a in second_half) / len(second_half)
                
                trends['improvements'] = {
                    'efficiency_improvement': float(second_half_avg - first_half_avg),
                    'trend_direction': 'improving' if second_half_avg > first_half_avg else 'declining'
                }
        
        return trends


class GPSTrackingService:
    """
    Service for handling GPS tracking data and real-time location updates
    """
    
    def process_gps_data(self, vehicle: Vehicle, gps_data: Dict) -> GPSTracking:
        """
        Process and save GPS tracking data from a vehicle
        
        Args:
            vehicle: Vehicle object
            gps_data: Dictionary containing GPS data
            
        Returns:
            GPSTracking object
        """
        required_fields = ['latitude', 'longitude', 'timestamp']
        for field in required_fields:
            if field not in gps_data:
                raise ValidationError(f"Missing required field: {field}")
        
        # Parse timestamp
        if isinstance(gps_data['timestamp'], str):
            timestamp = datetime.fromisoformat(gps_data['timestamp'].replace('Z', '+00:00'))
        else:
            timestamp = gps_data['timestamp']
        
        # Determine current route (if any)
        current_route = None
        active_routes = Route.objects.filter(
            vehicle=vehicle,
            status='active'
        )
        
        if active_routes.exists():
            current_route = active_routes.first()
        
        tracking = GPSTracking.objects.create(
            school=vehicle.school,
            vehicle=vehicle,
            route=current_route,
            latitude=Decimal(str(gps_data['latitude'])),
            longitude=Decimal(str(gps_data['longitude'])),
            speed_kmh=Decimal(str(gps_data.get('speed', 0))),
            heading=Decimal(str(gps_data['heading'])) if 'heading' in gps_data else None,
            altitude=Decimal(str(gps_data['altitude'])) if 'altitude' in gps_data else None,
            accuracy=Decimal(str(gps_data['accuracy'])) if 'accuracy' in gps_data else None,
            timestamp=timestamp,
            engine_status=gps_data.get('engine_status'),
            fuel_level_percentage=Decimal(str(gps_data['fuel_level'])) if 'fuel_level' in gps_data else None,
            additional_data={k: v for k, v in gps_data.items() if k not in required_fields}
        )
        
        return tracking
    
    def get_vehicle_current_location(self, vehicle: Vehicle) -> Optional[GPSTracking]:
        """
        Get the most recent GPS location for a vehicle
        """
        return GPSTracking.objects.filter(vehicle=vehicle).order_by('-timestamp').first()
    
    def check_geofence_alerts(self, tracking: GPSTracking) -> List[Dict]:
        """
        Check if vehicle is within expected geofence areas and generate alerts
        """
        alerts = []
        
        if not tracking.route:
            return alerts
        
        # Check if vehicle is near any of the route stops
        route_stops = RouteStop.objects.filter(route=tracking.route).select_related('bus_stop')
        vehicle_coords = tracking.coordinates
        
        min_distance_to_stop = float('inf')
        nearest_stop = None
        
        for route_stop in route_stops:
            if route_stop.bus_stop.coordinates:
                distance = RouteOptimizationService().calculate_distance(
                    vehicle_coords,
                    route_stop.bus_stop.coordinates
                )
                if distance < min_distance_to_stop:
                    min_distance_to_stop = distance
                    nearest_stop = route_stop.bus_stop
        
        # Alert if vehicle is too far from route (> 2km from nearest stop)
        if min_distance_to_stop > 2.0:
            alerts.append({
                'type': 'route_deviation',
                'message': f'Vehicle {tracking.vehicle.license_plate} is {min_distance_to_stop:.1f}km from nearest stop',
                'severity': 'warning',
                'distance_km': min_distance_to_stop,
                'nearest_stop': nearest_stop.name if nearest_stop else None
            })
        
        # Alert for excessive speed (> 80 km/h)
        if tracking.speed_kmh > 80:
            alerts.append({
                'type': 'speeding',
                'message': f'Vehicle {tracking.vehicle.license_plate} exceeding speed limit: {tracking.speed_kmh} km/h',
                'severity': 'high',
                'speed_kmh': float(tracking.speed_kmh)
            })
        
        return alerts
    
    def get_route_vehicle_locations(self, route: Route) -> List[GPSTracking]:
        """
        Get current locations of all vehicles on a route
        """
        if not route.vehicle:
            return []
        
        latest_location = self.get_vehicle_current_location(route.vehicle)
        return [latest_location] if latest_location else []
    
    def get_real_time_location_updates(self, vehicle: Vehicle, minutes: int = 30) -> List[GPSTracking]:
        """
        Get real-time location updates for a vehicle over the last N minutes
        """
        cutoff_time = timezone.now() - timedelta(minutes=minutes)
        return GPSTracking.objects.filter(
            vehicle=vehicle,
            timestamp__gte=cutoff_time
        ).order_by('-timestamp')
    
    def calculate_eta_to_stop(self, vehicle: Vehicle, target_stop: BusStop) -> Dict:
        """
        Calculate estimated time of arrival to a specific bus stop
        """
        current_location = self.get_vehicle_current_location(vehicle)
        if not current_location or not target_stop.coordinates:
            return {'error': 'Unable to calculate ETA - missing location data'}
        
        # Calculate distance to target stop
        distance_km = RouteOptimizationService().calculate_distance(
            current_location.coordinates,
            target_stop.coordinates
        )
        
        # Get recent speed data
        recent_tracking = self.get_real_time_location_updates(vehicle, 10)  # Last 10 minutes
        if recent_tracking:
            avg_speed = sum(float(t.speed_kmh) for t in recent_tracking) / len(recent_tracking)
        else:
            avg_speed = 30.0  # Default city speed
        
        # Calculate ETA
        if avg_speed > 0:
            eta_hours = distance_km / avg_speed
            eta_minutes = eta_hours * 60
            eta_time = timezone.now() + timedelta(minutes=eta_minutes)
        else:
            eta_time = None
        
        return {
            'distance_km': distance_km,
            'average_speed_kmh': avg_speed,
            'eta_minutes': eta_minutes if avg_speed > 0 else None,
            'eta_time': eta_time,
            'current_location': current_location.coordinates,
            'target_location': target_stop.coordinates
        }
    
    def get_route_progress(self, route: Route) -> Dict:
        """
        Get real-time progress of a route
        """
        if not route.vehicle:
            return {'error': 'No vehicle assigned to route'}
        
        current_location = self.get_vehicle_current_location(route.vehicle)
        if not current_location:
            return {'error': 'No current location data available'}
        
        # Get route stops in order
        route_stops = RouteStop.objects.filter(route=route).select_related('bus_stop').order_by('sequence_order')
        
        progress_data = {
            'route': route,
            'current_location': current_location.coordinates,
            'stops_progress': [],
            'next_stop': None,
            'completed_stops': 0,
            'total_stops': route_stops.count(),
            'progress_percentage': 0
        }
        
        # Calculate progress for each stop
        for route_stop in route_stops:
            if not route_stop.bus_stop.coordinates:
                continue
            
            distance_to_stop = RouteOptimizationService().calculate_distance(
                current_location.coordinates,
                route_stop.bus_stop.coordinates
            )
            
            # Consider stop "completed" if vehicle is within 200m
            is_completed = distance_to_stop <= 0.2
            
            stop_progress = {
                'stop': route_stop.bus_stop,
                'sequence_order': route_stop.sequence_order,
                'distance_km': distance_to_stop,
                'is_completed': is_completed,
                'eta': self.calculate_eta_to_stop(route.vehicle, route_stop.bus_stop) if not is_completed else None
            }
            
            progress_data['stops_progress'].append(stop_progress)
            
            if is_completed:
                progress_data['completed_stops'] += 1
            elif not progress_data['next_stop']:
                progress_data['next_stop'] = stop_progress
        
        # Calculate overall progress percentage
        if progress_data['total_stops'] > 0:
            progress_data['progress_percentage'] = (progress_data['completed_stops'] / progress_data['total_stops']) * 100
        
        return progress_data
    
    def detect_route_deviations(self, vehicle: Vehicle, tolerance_km: float = 1.0) -> List[Dict]:
        """
        Detect if vehicle has deviated from its assigned route
        """
        current_location = self.get_vehicle_current_location(vehicle)
        if not current_location or not current_location.route:
            return []
        
        route = current_location.route
        route_stops = RouteStop.objects.filter(route=route).select_related('bus_stop')
        
        # Find minimum distance to any stop on the route
        min_distance = float('inf')
        nearest_stop = None
        
        for route_stop in route_stops:
            if route_stop.bus_stop.coordinates:
                distance = RouteOptimizationService().calculate_distance(
                    current_location.coordinates,
                    route_stop.bus_stop.coordinates
                )
                if distance < min_distance:
                    min_distance = distance
                    nearest_stop = route_stop.bus_stop
        
        deviations = []
        
        # Check if vehicle is too far from route
        if min_distance > tolerance_km:
            deviations.append({
                'type': 'route_deviation',
                'severity': 'high' if min_distance > tolerance_km * 2 else 'medium',
                'distance_from_route_km': min_distance,
                'nearest_stop': nearest_stop.name if nearest_stop else None,
                'message': f'Vehicle is {min_distance:.1f}km from nearest route stop',
                'timestamp': current_location.timestamp
            })
        
        return deviations
    
    def get_vehicle_status_summary(self, vehicle: Vehicle) -> Dict:
        """
        Get comprehensive real-time status summary for a vehicle
        """
        current_location = self.get_vehicle_current_location(vehicle)
        
        summary = {
            'vehicle': vehicle,
            'last_update': current_location.timestamp if current_location else None,
            'current_location': current_location.coordinates if current_location else None,
            'current_speed': float(current_location.speed_kmh) if current_location else 0,
            'engine_status': current_location.engine_status if current_location else 'unknown',
            'fuel_level': float(current_location.fuel_level_percentage) if current_location and current_location.fuel_level_percentage else None,
            'is_online': False,
            'alerts': [],
            'route_progress': None
        }
        
        if current_location:
            # Check if vehicle is online (last update within 5 minutes)
            time_since_update = timezone.now() - current_location.timestamp
            summary['is_online'] = time_since_update.total_seconds() <= 300  # 5 minutes
            
            # Get alerts
            summary['alerts'] = self.check_geofence_alerts(current_location)
            
            # Get route progress if vehicle is on a route
            if current_location.route:
                summary['route_progress'] = self.get_route_progress(current_location.route)
        
        return summary


class TransportationFeeService:
    """
    Service for calculating and managing transportation fees
    """
    
    def calculate_monthly_fee(self, student_transportation: StudentTransportation, 
                            month: datetime, calculation_type: str = 'fixed') -> TransportationFee:
        """
        Calculate monthly transportation fee for a student
        """
        # Get or create fee record
        month_start = month.replace(day=1).date()
        fee, created = TransportationFee.objects.get_or_create(
            student_transportation=student_transportation,
            month=month_start,
            defaults={
                'calculation_type': calculation_type,
                'base_fee': student_transportation.monthly_fee,
                'due_date': month_start.replace(day=15),  # Due on 15th of the month
                'total_fee': Decimal('0.00'),
            }
        )
        
        if not created and fee.status in ['paid']:
            return fee  # Don't recalculate paid fees
        
        # Set calculation parameters based on type
        route = student_transportation.route
        
        if calculation_type == 'distance':
            # Calculate distance from pickup to dropoff
            pickup_stop = RouteStop.objects.filter(
                route=route, 
                bus_stop=student_transportation.pickup_stop
            ).first()
            dropoff_stop = RouteStop.objects.filter(
                route=route, 
                bus_stop=student_transportation.dropoff_stop
            ).first()
            
            if pickup_stop and dropoff_stop:
                # Calculate distance between stops
                distance = abs(dropoff_stop.sequence_order - pickup_stop.sequence_order) * 2  # Rough estimate
                fee.distance_km = Decimal(str(distance))
                fee.distance_rate_per_km = Decimal('5.00')  # Default rate per km
        
        # Apply any discounts (e.g., sibling discount)
        siblings_count = StudentTransportation.objects.filter(
            student__parents__in=student_transportation.student.parents.all(),
            status='active'
        ).exclude(id=student_transportation.id).count()
        
        if siblings_count > 0:
            fee.discount_percentage = Decimal('10.00')  # 10% sibling discount
        
        # Calculate total fee
        fee.calculate_fee()
        fee.status = 'calculated'
        fee.save()
        
        return fee


class StudentAttendanceService:
    """
    Service for managing student pickup/drop-off attendance
    """
    
    def record_attendance(self, student_transportation: StudentTransportation, 
                         attendance_type: str, status: str = 'present',
                         actual_time: datetime = None, gps_location: Dict = None) -> TransportationAttendance:
        """
        Record student attendance for pickup or drop-off
        """
        today = timezone.now().date()
        actual_time = actual_time or timezone.now().time()
        
        # Determine scheduled time and bus stop
        if attendance_type == 'pickup':
            bus_stop = student_transportation.pickup_stop
            route_stop = RouteStop.objects.filter(
                route=student_transportation.route,
                bus_stop=bus_stop
            ).first()
            scheduled_time = route_stop.estimated_arrival_time_morning if route_stop else None
        else:  # dropoff
            bus_stop = student_transportation.dropoff_stop
            route_stop = RouteStop.objects.filter(
                route=student_transportation.route,
                bus_stop=bus_stop
            ).first()
            scheduled_time = route_stop.estimated_arrival_time_afternoon if route_stop else None
        
        # Get or create attendance record
        attendance, created = TransportationAttendance.objects.get_or_create(
            student_transportation=student_transportation,
            date=today,
            attendance_type=attendance_type,
            defaults={
                'status': status,
                'scheduled_time': scheduled_time or timezone.now().time(),
                'actual_time': actual_time,
                'bus_stop': bus_stop,
                'driver': student_transportation.route.primary_driver,
                'gps_location': gps_location or {},
            }
        )
        
        if not created:
            # Update existing record
            attendance.status = status
            attendance.actual_time = actual_time
            if gps_location:
                attendance.gps_location = gps_location
            attendance.save()
        
        return attendance


class ParentNotificationService:
    """
    Service for managing parent notifications related to transportation
    """
    
    def send_pickup_reminder(self, student_transportation: StudentTransportation, 
                           scheduled_time: datetime) -> List[ParentNotification]:
        """
        Send pickup reminder to parents
        """
        # Get parent contact information
        parents = student_transportation.student.parents.all()
        notifications = []
        
        for parent in parents:
            if parent.phone:  # Send SMS
                notification = ParentNotification.objects.create(
                    student_transportation=student_transportation,
                    notification_type='pickup_reminder',
                    channel='sms',
                    recipient_name=f"{parent.first_name} {parent.last_name}",
                    recipient_contact=parent.phone,
                    subject=_('Bus Pickup Reminder'),
                    message=_(
                        'Reminder: Bus pickup for {student} is scheduled at {time} from {stop}. '
                        'Route: {route}'
                    ).format(
                        student=student_transportation.student.first_name,
                        time=scheduled_time.strftime('%H:%M'),
                        stop=student_transportation.pickup_stop.name,
                        route=student_transportation.route.name
                    ),
                    scheduled_time=scheduled_time - timedelta(minutes=15),  # 15 minutes before
                )
                notifications.append(notification)
        
        return notifications
    
    def send_dropoff_confirmation(self, attendance: TransportationAttendance) -> List[ParentNotification]:
        """
        Send drop-off confirmation to parents
        """
        student_transportation = attendance.student_transportation
        parents = student_transportation.student.parents.all()
        notifications = []
        
        for parent in parents:
            if parent.phone:  # Send SMS
                notification = ParentNotification.objects.create(
                    student_transportation=student_transportation,
                    notification_type='dropoff_confirmation',
                    channel='sms',
                    recipient_name=f"{parent.first_name} {parent.last_name}",
                    recipient_contact=parent.phone,
                    subject=_('Drop-off Confirmation'),
                    message=_(
                        '{student} has been safely dropped off at {stop} at {time}.'
                    ).format(
                        student=student_transportation.student.first_name,
                        stop=attendance.bus_stop.name,
                        time=attendance.actual_time.strftime('%H:%M') if attendance.actual_time else 'N/A'
                    ),
                    scheduled_time=timezone.now(),
                )
                notifications.append(notification)
        
        return notifications


class RouteManagementService:
    """
    Comprehensive service for managing transportation routes and student assignments
    """
    
    def __init__(self):
        self.optimization_service = RouteOptimizationService()
        self.analytics_service = RouteAnalyticsService()
        self.gps_service = GPSTrackingService()
        self.fee_service = TransportationFeeService()
        self.attendance_service = StudentAttendanceService()
        self.notification_service = ParentNotificationService()
    
    @transaction.atomic
    def create_route_with_stops(self, route_data: Dict, stops_data: List[Dict]) -> Route:
        """
        Create a new route with associated stops
        """
        # Create route
        route = Route.objects.create(**route_data)
        
        # Create route stops
        for i, stop_data in enumerate(stops_data):
            bus_stop_id = stop_data.get('bus_stop_id')
            bus_stop = BusStop.objects.get(id=bus_stop_id)
            
            # Remove bus_stop_id and sequence_order from stop_data to avoid conflict
            # Also map field names to match the model
            clean_stop_data = {}
            for k, v in stop_data.items():
                if k not in ['bus_stop_id', 'sequence_order']:
                    # Map test field names to model field names
                    if k == 'arrival_time_morning':
                        clean_stop_data['estimated_arrival_time_morning'] = v
                    elif k == 'arrival_time_afternoon':
                        clean_stop_data['estimated_arrival_time_afternoon'] = v
                    else:
                        clean_stop_data[k] = v
            
            RouteStop.objects.create(
                school=route.school,
                route=route,
                bus_stop=bus_stop,
                sequence_order=i + 1,
                **clean_stop_data
            )
        
        # Calculate initial route distance
        self._update_route_metrics(route)
        
        return route
    
    @transaction.atomic
    def assign_student_to_route(self, student, route: Route, 
                              pickup_stop: BusStop, dropoff_stop: BusStop,
                              assignment_data: Dict = None) -> StudentTransportation:
        """
        Assign a student to a transportation route
        """
        # Validate route capacity
        if route.is_full:
            raise ValidationError(_('Route is at full capacity'))
        
        # Validate stops are part of the route
        if not RouteStop.objects.filter(route=route, bus_stop=pickup_stop).exists():
            raise ValidationError(_('Pickup stop is not part of the selected route'))
        
        if not RouteStop.objects.filter(route=route, bus_stop=dropoff_stop).exists():
            raise ValidationError(_('Drop-off stop is not part of the selected route'))
        
        # Create assignment
        assignment_data = assignment_data or {}
        assignment_data.update({
            'student': student,
            'route': route,
            'pickup_stop': pickup_stop,
            'dropoff_stop': dropoff_stop,
            'monthly_fee': assignment_data.get('monthly_fee', route.monthly_fee),
            'start_date': assignment_data.get('start_date', timezone.now().date()),
        })
        
        # Get emergency contact from student's parents
        parent = student.parents.first()
        if parent:
            assignment_data.update({
                'emergency_contact_name': f"{parent.first_name} {parent.last_name}",
                'emergency_contact_phone': parent.phone,
            })
        
        assignment = StudentTransportation.objects.create(**assignment_data)
        
        # Update route occupancy
        route.update_occupancy()
        
        return assignment
    
    def get_route_dashboard_data(self, route: Route) -> Dict:
        """
        Get comprehensive dashboard data for a route
        """
        # Basic route information
        data = {
            'route': {
                'id': route.id,
                'name': route.name,
                'code': route.code,
                'status': route.status,
                'total_distance_km': float(route.total_distance_km),
                'estimated_duration_minutes': route.estimated_duration_minutes,
                'occupancy_percentage': route.occupancy_percentage,
            },
            'vehicle': {
                'id': route.vehicle.id,
                'license_plate': route.vehicle.license_plate,
                'capacity': route.vehicle.capacity,
            } if route.vehicle else None,
            'primary_driver': {
                'id': route.primary_driver.id,
                'name': route.primary_driver.full_name,
            } if route.primary_driver else None,
            'backup_driver': {
                'id': route.backup_driver.id,
                'name': route.backup_driver.full_name,
            } if route.backup_driver else None,
            'driver': {
                'id': route.primary_driver.id,
                'name': route.primary_driver.full_name,
            } if route.primary_driver else None,
            'total_stops': route.route_stops.count(),
            'assigned_students': route.student_assignments.filter(status='active').count(),
            'occupancy_percentage': route.occupancy_percentage,
            'available_seats': route.available_seats,
        }
        
        # Recent GPS tracking
        if route.vehicle:
            data['current_location'] = self.gps_service.get_vehicle_current_location(route.vehicle)
        
        # Recent analytics
        recent_analytics = TransportationAnalytics.objects.filter(
            route=route,
            metric_type='route_performance'
        ).order_by('-date_to').first()
        
        if recent_analytics:
            data['recent_performance'] = {
                'efficiency_score': recent_analytics.efficiency_score,
                'on_time_percentage': recent_analytics.on_time_percentage,
                'average_speed': recent_analytics.average_speed_kmh,
            }
        
        # Add route stops information
        data['stops'] = RouteStop.objects.filter(route=route).select_related('bus_stop').order_by('sequence_order')
        
        # Add students information
        data['students'] = StudentTransportation.objects.filter(
            route=route,
            status='active'
        ).select_related('student', 'pickup_stop', 'dropoff_stop')
        
        return data
    
    def _update_route_metrics(self, route: Route):
        """
        Update route distance and duration metrics
        """
        route_stops = route.route_stops.all().select_related('bus_stop')
        stops = [rs.bus_stop for rs in route_stops]
        
        if len(stops) >= 2:
            total_distance = self.optimization_service.calculate_route_distance(stops)
            route.total_distance_km = Decimal(str(total_distance))
            
            # Estimate duration (assuming average speed of 30 km/h in city)
            estimated_duration = (total_distance / 30) * 60  # minutes
            route.estimated_duration_minutes = int(estimated_duration)
            
            route.save()