import pytest
from decimal import Decimal
from datetime import datetime, date, time, timedelta
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.contrib.auth import get_user_model
from core.models import School, AcademicYear
from students.models import Student, Grade
from hr.models import Employee
from .models import (
    Vehicle, Driver, Route, BusStop, RouteStop, StudentTransportation,
    RouteOptimization, GPSTracking, TransportationAnalytics
)
from .services import (
    RouteOptimizationService, RouteAnalyticsService, GPSTrackingService,
    RouteManagementService
)

User = get_user_model()


class TransportationModelsTestCase(TestCase):
    """Test cases for transportation models"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="+**********",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2000, 1, 1)
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            first_name="<PERSON>",
            last_name="Driver"
        )
        
        # Create department and position for employee
        from hr.models import Department, Position
        
        self.department = Department.objects.create(
            school=self.school,
            name="Transportation",
            code="TRANS",
            description="Transportation Department"
        )
        
        self.position = Position.objects.create(
            school=self.school,
            title="Driver",
            department=self.department,
            description="Bus Driver Position",
            min_salary=Decimal('2500.00'),
            max_salary=Decimal('3500.00')
        )
        
        # Create employee for driver
        self.employee = Employee.objects.create(
            school=self.school,
            user=self.user,
            employee_id="EMP001",
            position=self.position,
            hire_date=date.today(),
            salary=Decimal('3000.00'),
            emergency_contact_name="Jane Driver",
            emergency_contact_phone="+**********",
            emergency_contact_relationship="Spouse"
        )
    
    def test_vehicle_creation(self):
        """Test vehicle model creation and validation"""
        vehicle = Vehicle.objects.create(
            school=self.school,
            vehicle_number="BUS001",
            license_plate="ABC123",
            vehicle_type="bus",
            make="Mercedes",
            model="Sprinter",
            year=2020,
            capacity=30,
            fuel_type="diesel",
            status="active"
        )
        
        self.assertEqual(str(vehicle), "BUS001 - ABC123")
        self.assertEqual(vehicle.capacity, 30)
        self.assertFalse(vehicle.is_maintenance_due)
        self.assertFalse(vehicle.is_insurance_expiring_soon)
    
    def test_vehicle_maintenance_due(self):
        """Test vehicle maintenance due property"""
        vehicle = Vehicle.objects.create(
            school=self.school,
            vehicle_number="BUS002",
            license_plate="DEF456",
            vehicle_type="bus",
            make="Mercedes",
            model="Sprinter",
            year=2020,
            capacity=30,
            next_maintenance_date=date.today() - timedelta(days=1)  # Past due
        )
        
        self.assertTrue(vehicle.is_maintenance_due)
    
    def test_driver_creation(self):
        """Test driver model creation and validation"""
        driver = Driver.objects.create(
            school=self.school,
            employee=self.employee,
            license_number="DL123456",
            license_type="CDL",
            license_expiry=date.today() + timedelta(days=365),
            experience_years=5,
            status="active",
            emergency_contact_name="Jane Driver",
            emergency_contact_phone="+**********"
        )
        
        self.assertEqual(str(driver), "John Driver - DL123456")
        self.assertEqual(driver.full_name, "John Driver")
        self.assertFalse(driver.is_license_expiring_soon)
    
    def test_driver_license_expiring_soon(self):
        """Test driver license expiring soon property"""
        driver = Driver.objects.create(
            school=self.school,
            employee=self.employee,
            license_number="DL789012",
            license_type="CDL",
            license_expiry=date.today() + timedelta(days=15),  # Expiring soon
            experience_years=5,
            status="active",
            emergency_contact_name="Jane Driver",
            emergency_contact_phone="+**********"
        )
        
        self.assertTrue(driver.is_license_expiring_soon)
    
    def test_bus_stop_creation(self):
        """Test bus stop model creation and validation"""
        bus_stop = BusStop.objects.create(
            school=self.school,
            name="Main Street Stop",
            code="MS001",
            address="123 Main Street",
            latitude=Decimal('40.7128'),
            longitude=Decimal('-74.0060'),
            status="active",
            safety_rating=5
        )
        
        self.assertEqual(str(bus_stop), "MS001 - Main Street Stop")
        self.assertEqual(bus_stop.coordinates, (40.7128, -74.0060))
        self.assertIsNotNone(bus_stop.google_maps_url)
    
    def test_bus_stop_coordinate_validation(self):
        """Test bus stop coordinate validation"""
        with self.assertRaises(ValidationError):
            bus_stop = BusStop(
                school=self.school,
                name="Invalid Stop",
                code="INV001",
                address="Invalid Address",
                latitude=Decimal('91.0'),  # Invalid latitude
                longitude=Decimal('-74.0060'),
                status="active"
            )
            bus_stop.full_clean()
    
    def test_route_creation(self):
        """Test route model creation and properties"""
        vehicle = Vehicle.objects.create(
            school=self.school,
            vehicle_number="BUS001",
            license_plate="ABC123",
            vehicle_type="bus",
            make="Mercedes",
            model="Sprinter",
            year=2020,
            capacity=30
        )
        
        driver = Driver.objects.create(
            school=self.school,
            employee=self.employee,
            license_number="DL123456",
            license_type="CDL",
            license_expiry=date.today() + timedelta(days=365),
            experience_years=5,
            status="active",
            emergency_contact_name="Jane Driver",
            emergency_contact_phone="+**********"
        )
        
        route = Route.objects.create(
            school=self.school,
            name="Route A",
            code="RT001",
            route_type="both",
            vehicle=vehicle,
            primary_driver=driver,
            max_capacity=25,
            current_occupancy=10,
            monthly_fee=Decimal('100.00')
        )
        
        self.assertEqual(str(route), "RT001 - Route A")
        self.assertEqual(route.occupancy_percentage, 40.0)
        self.assertEqual(route.available_seats, 15)
        self.assertFalse(route.is_full)
    
    def test_route_occupancy_update(self):
        """Test route occupancy update functionality"""
        # Create necessary objects
        vehicle = Vehicle.objects.create(
            school=self.school,
            vehicle_number="BUS001",
            license_plate="ABC123",
            vehicle_type="bus",
            make="Mercedes",
            model="Sprinter",
            year=2020,
            capacity=30
        )
        
        route = Route.objects.create(
            school=self.school,
            name="Route A",
            code="RT001",
            vehicle=vehicle,
            max_capacity=25
        )
        
        # Create student and grade
        grade = Grade.objects.create(
            school=self.school,
            name="Grade 1",
            level=1
        )
        
        # Create parent user and parent
        parent_user = User.objects.create_user(
            username="parent1",
            email="<EMAIL>",
            first_name="John",
            last_name="Parent"
        )
        
        from students.models import Parent
        parent = Parent.objects.create(
            school=self.school,
            user=parent_user,
            father_name="John Parent",
            father_phone="+**********",
            home_address="123 Test Street"
        )
        
        student_user = User.objects.create_user(
            username="student1",
            email="<EMAIL>",
            first_name="Test",
            last_name="Student"
        )
        
        student = Student.objects.create(
            school=self.school,
            user=student_user,
            parent=parent,
            student_id="STU001",
            admission_number="ADM001",
            first_name="Test",
            last_name="Student",
            date_of_birth=date(2010, 1, 1),
            gender="M",
            nationality="US",
            admission_date=date.today()
        )
        
        bus_stop = BusStop.objects.create(
            school=self.school,
            name="Test Stop",
            code="TS001",
            address="Test Address"
        )
        
        # Create student transportation assignment
        StudentTransportation.objects.create(
            school=self.school,
            student=student,
            route=route,
            pickup_stop=bus_stop,
            dropoff_stop=bus_stop,
            status="active",
            start_date=date.today(),
            monthly_fee=Decimal('100.00'),
            emergency_contact_name="Parent",
            emergency_contact_phone="+**********"
        )
        
        # Update occupancy
        route.update_occupancy()
        route.refresh_from_db()
        
        self.assertEqual(route.current_occupancy, 1)


class RouteOptimizationServiceTestCase(TestCase):
    """Test cases for route optimization service"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="+**********",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2000, 1, 1)
        )
        
        self.service = RouteOptimizationService()
        
        # Create bus stops with coordinates
        self.stop1 = BusStop.objects.create(
            school=self.school,
            name="Stop 1",
            code="S001",
            address="Address 1",
            latitude=Decimal('40.7128'),
            longitude=Decimal('-74.0060')
        )
        
        self.stop2 = BusStop.objects.create(
            school=self.school,
            name="Stop 2",
            code="S002",
            address="Address 2",
            latitude=Decimal('40.7589'),
            longitude=Decimal('-73.9851')
        )
        
        self.stop3 = BusStop.objects.create(
            school=self.school,
            name="Stop 3",
            code="S003",
            address="Address 3",
            latitude=Decimal('40.7505'),
            longitude=Decimal('-73.9934')
        )
        
        # Create route
        self.route = Route.objects.create(
            school=self.school,
            name="Test Route",
            code="TR001",
            max_capacity=30
        )
        
        # Create route stops
        RouteStop.objects.create(
            school=self.school,
            route=self.route,
            bus_stop=self.stop1,
            sequence_order=1
        )
        
        RouteStop.objects.create(
            school=self.school,
            route=self.route,
            bus_stop=self.stop2,
            sequence_order=2
        )
        
        RouteStop.objects.create(
            school=self.school,
            route=self.route,
            bus_stop=self.stop3,
            sequence_order=3
        )
    
    def test_calculate_distance(self):
        """Test distance calculation between two coordinates"""
        coord1 = (40.7128, -74.0060)  # New York
        coord2 = (40.7589, -73.9851)  # Times Square
        
        distance = self.service.calculate_distance(coord1, coord2)
        
        # Distance should be approximately 5.7 km
        self.assertGreater(distance, 5.0)
        self.assertLess(distance, 7.0)
    
    def test_calculate_route_distance(self):
        """Test total route distance calculation"""
        stops = [self.stop1, self.stop2, self.stop3]
        
        total_distance = self.service.calculate_route_distance(stops)
        
        self.assertGreater(total_distance, 0)
        self.assertIsInstance(total_distance, float)
    
    def test_nearest_neighbor_optimization(self):
        """Test nearest neighbor optimization algorithm"""
        result = self.service.nearest_neighbor_optimization(self.route)
        
        self.assertIn('original_distance', result)
        self.assertIn('optimized_distance', result)
        self.assertIn('optimized_stops', result)
        self.assertIn('savings_km', result)
        self.assertIn('savings_percentage', result)
        
        self.assertEqual(len(result['optimized_stops']), 3)
    
    def test_optimize_route(self):
        """Test complete route optimization process"""
        optimization = self.service.optimize_route(self.route, 'nearest_neighbor')
        
        self.assertIsInstance(optimization, RouteOptimization)
        self.assertEqual(optimization.route, self.route)
        self.assertEqual(optimization.optimization_type, 'nearest_neighbor')
        self.assertIn(optimization.status, ['completed', 'failed'])


class GPSTrackingServiceTestCase(TestCase):
    """Test cases for GPS tracking service"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="+**********",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2000, 1, 1)
        )
        
        self.vehicle = Vehicle.objects.create(
            school=self.school,
            vehicle_number="BUS001",
            license_plate="ABC123",
            vehicle_type="bus",
            make="Test Make",
            model="Test Model",
            year=2020,
            capacity=30
        )
        
        self.service = GPSTrackingService()
    
    def test_process_gps_data(self):
        """Test GPS data processing"""
        gps_data = {
            'latitude': 40.7128,
            'longitude': -74.0060,
            'speed': 25.5,
            'heading': 180.0,
            'timestamp': timezone.now().isoformat(),
            'engine_status': 'on',
            'fuel_level': 75.0
        }
        
        tracking = self.service.process_gps_data(self.vehicle, gps_data)
        
        self.assertIsInstance(tracking, GPSTracking)
        self.assertEqual(tracking.vehicle, self.vehicle)
        self.assertEqual(float(tracking.latitude), 40.7128)
        self.assertEqual(float(tracking.longitude), -74.0060)
        self.assertEqual(float(tracking.speed_kmh), 25.5)
    
    def test_get_vehicle_current_location(self):
        """Test getting vehicle's current location"""
        # Create GPS tracking data
        GPSTracking.objects.create(
            school=self.school,
            vehicle=self.vehicle,
            latitude=Decimal('40.7128'),
            longitude=Decimal('-74.0060'),
            timestamp=timezone.now()
        )
        
        location = self.service.get_vehicle_current_location(self.vehicle)
        
        self.assertIsNotNone(location)
        self.assertEqual(location.vehicle, self.vehicle)
    
    def test_check_geofence_alerts(self):
        """Test geofence alert checking"""
        # Create route and stops
        route = Route.objects.create(
            school=self.school,
            name="Test Route",
            code="TR001",
            vehicle=self.vehicle
        )
        
        bus_stop = BusStop.objects.create(
            school=self.school,
            name="Test Stop",
            code="TS001",
            address="Test Address",
            latitude=Decimal('40.7128'),
            longitude=Decimal('-74.0060')
        )
        
        RouteStop.objects.create(
            school=self.school,
            route=route,
            bus_stop=bus_stop,
            sequence_order=1
        )
        
        # Create GPS tracking far from route
        tracking = GPSTracking.objects.create(
            school=self.school,
            vehicle=self.vehicle,
            route=route,
            latitude=Decimal('41.0000'),  # Far from stop
            longitude=Decimal('-75.0000'),
            speed_kmh=Decimal('90.0'),  # Speeding
            timestamp=timezone.now()
        )
        
        alerts = self.service.check_geofence_alerts(tracking)
        
        self.assertGreater(len(alerts), 0)
        alert_types = [alert['type'] for alert in alerts]
        self.assertIn('route_deviation', alert_types)
        self.assertIn('speeding', alert_types)


class RouteManagementServiceTestCase(TestCase):
    """Test cases for route management service"""
    
    def setUp(self):
        """Set up test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="+**********",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2000, 1, 1)
        )
        
        self.service = RouteManagementService()
        
        # Create bus stops
        self.stop1 = BusStop.objects.create(
            school=self.school,
            name="Stop 1",
            code="S001",
            address="Address 1"
        )
        
        self.stop2 = BusStop.objects.create(
            school=self.school,
            name="Stop 2",
            code="S002",
            address="Address 2"
        )
    
    def test_create_route_with_stops(self):
        """Test creating route with associated stops"""
        route_data = {
            'school': self.school,
            'name': 'Test Route',
            'code': 'TR001',
            'route_type': 'both',
            'max_capacity': 30
        }
        
        stops_data = [
            {
                'bus_stop_id': self.stop1.id,
                'sequence_order': 1,
                'is_pickup_point': True,
                'is_dropoff_point': True
            },
            {
                'bus_stop_id': self.stop2.id,
                'sequence_order': 2,
                'is_pickup_point': True,
                'is_dropoff_point': True
            }
        ]
        
        route = self.service.create_route_with_stops(route_data, stops_data)
        
        self.assertIsInstance(route, Route)
        self.assertEqual(route.name, 'Test Route')
        self.assertEqual(route.route_stops.count(), 2)
    
    def test_get_route_dashboard_data(self):
        """Test getting comprehensive route dashboard data"""
        route = Route.objects.create(
            school=self.school,
            name="Test Route",
            code="TR001",
            max_capacity=30,
            current_occupancy=15
        )
        
        RouteStop.objects.create(
            school=self.school,
            route=route,
            bus_stop=self.stop1,
            sequence_order=1
        )
        
        dashboard_data = self.service.get_route_dashboard_data(route)
        
        self.assertIn('route', dashboard_data)
        self.assertIn('stops', dashboard_data)
        self.assertIn('students', dashboard_data)
        
        self.assertEqual(dashboard_data['route']['name'], 'Test Route')
        self.assertEqual(dashboard_data['route']['occupancy_percentage'], 50.0)
        self.assertEqual(len(dashboard_data['stops']), 1)


class TransportationIntegrationTestCase(TestCase):
    """Integration tests for transportation system"""
    
    def setUp(self):
        """Set up comprehensive test data"""
        self.school = School.objects.create(
            name="Test School",
            code="TEST001",
            address="123 Test St",
            phone="+**********",
            email="<EMAIL>",
            principal_name="Test Principal",
            established_date=date(2000, 1, 1)
        )
        
        # Create user and employee for driver
        self.user = User.objects.create_user(
            username="driver1",
            email="<EMAIL>",
            password="testpass123",
            first_name="John",
            last_name="Driver"
        )
        
        # Create department and position for employee
        from hr.models import Department, Position
        
        self.department = Department.objects.create(
            school=self.school,
            name="Transportation",
            code="TRANS",
            description="Transportation Department"
        )
        
        self.position = Position.objects.create(
            school=self.school,
            title="Driver",
            department=self.department,
            description="Bus Driver Position",
            min_salary=Decimal('2500.00'),
            max_salary=Decimal('3500.00')
        )
        
        self.employee = Employee.objects.create(
            school=self.school,
            user=self.user,
            employee_id="EMP001",
            position=self.position,
            hire_date=date.today(),
            salary=Decimal('3000.00'),
            emergency_contact_name="Jane Driver",
            emergency_contact_phone="+**********",
            emergency_contact_relationship="Spouse"
        )
        
        # Create vehicle
        self.vehicle = Vehicle.objects.create(
            school=self.school,
            vehicle_number="BUS001",
            license_plate="ABC123",
            vehicle_type="bus",
            make="Test Make",
            model="Test Model",
            year=2020,
            capacity=30
        )
        
        # Create driver
        self.driver = Driver.objects.create(
            school=self.school,
            employee=self.employee,
            license_number="DL123456",
            license_type="CDL",
            license_expiry=date.today() + timedelta(days=365),
            experience_years=5,
            status="active",
            emergency_contact_name="Jane Driver",
            emergency_contact_phone="+**********"
        )
        
        # Create bus stops
        self.stops = []
        for i in range(5):
            stop = BusStop.objects.create(
                school=self.school,
                name=f"Stop {i+1}",
                code=f"S{i+1:03d}",
                address=f"Address {i+1}",
                latitude=Decimal(f'40.{7100 + i}'),
                longitude=Decimal(f'-74.{i:04d}')
            )
            self.stops.append(stop)
    
    def test_complete_route_workflow(self):
        """Test complete route creation and management workflow"""
        # 1. Create route with stops
        service = RouteManagementService()
        
        route_data = {
            'school': self.school,
            'name': 'Morning Route A',
            'code': 'MRA001',
            'route_type': 'morning',
            'vehicle': self.vehicle,
            'primary_driver': self.driver,
            'max_capacity': 25,
            'start_time_morning': time(7, 0),
            'estimated_duration_minutes': 45,
            'monthly_fee': Decimal('150.00')
        }
        
        stops_data = [
            {
                'bus_stop_id': self.stops[i].id,
                'sequence_order': i + 1,
                'arrival_time_morning': time(7, 5 + i * 3),
                'is_pickup_point': True,
                'is_dropoff_point': False
            }
            for i in range(3)
        ]
        
        route = service.create_route_with_stops(route_data, stops_data)
        
        # 2. Optimize route
        optimization_service = RouteOptimizationService()
        optimization = optimization_service.optimize_route(route, 'nearest_neighbor')
        
        self.assertEqual(optimization.route, route)
        self.assertIn(optimization.status, ['completed', 'failed'])
        
        # 3. Add GPS tracking data
        gps_service = GPSTrackingService()
        
        for i, stop in enumerate(self.stops[:3]):
            if stop.coordinates:
                gps_data = {
                    'latitude': float(stop.latitude),
                    'longitude': float(stop.longitude),
                    'speed': 25.0,
                    'timestamp': (timezone.now() + timedelta(minutes=i * 5)).isoformat(),
                    'engine_status': 'on'
                }
                
                tracking = gps_service.process_gps_data(self.vehicle, gps_data)
                self.assertIsNotNone(tracking)
        
        # 4. Generate analytics
        analytics_service = RouteAnalyticsService()
        
        # Create some mock GPS data for analytics
        for i in range(10):
            GPSTracking.objects.create(
                school=self.school,
                vehicle=self.vehicle,
                route=route,
                latitude=Decimal('40.7128'),
                longitude=Decimal('-74.0060'),
                speed_kmh=Decimal('30.0'),
                timestamp=timezone.now() - timedelta(hours=i)
            )
        
        try:
            analytics = analytics_service.generate_route_analytics(
                route,
                timezone.now() - timedelta(days=1),
                timezone.now()
            )
            self.assertIsInstance(analytics, TransportationAnalytics)
        except ValidationError:
            # Analytics might fail if there's insufficient data
            pass
        
        # 5. Get dashboard data
        dashboard_data = service.get_route_dashboard_data(route)
        
        self.assertIn('route', dashboard_data)
        self.assertIn('vehicle', dashboard_data)
        self.assertIn('driver', dashboard_data)
        self.assertIn('stops', dashboard_data)
        
        self.assertEqual(dashboard_data['route']['name'], 'Morning Route A')
        self.assertEqual(dashboard_data['vehicle']['license_plate'], 'ABC123')
        self.assertEqual(dashboard_data['driver']['name'], 'John Driver')
        self.assertEqual(len(dashboard_data['stops']), 3)


# Pytest fixtures and tests
@pytest.fixture
def school():
    """Create a test school"""
    return School.objects.create(
        name="Test School",
        code="TEST001",
        address="123 Test St",
        phone="+**********",
        email="<EMAIL>",
        principal_name="Test Principal",
        established_date=date(2000, 1, 1)
    )


@pytest.fixture
def vehicle(school):
    """Create a test vehicle"""
    return Vehicle.objects.create(
        school=school,
        vehicle_number="BUS001",
        license_plate="ABC123",
        vehicle_type="bus",
        capacity=30
    )


@pytest.fixture
def bus_stop(school):
    """Create a test bus stop"""
    return BusStop.objects.create(
        school=school,
        name="Test Stop",
        code="TS001",
        address="Test Address",
        latitude=Decimal('40.7128'),
        longitude=Decimal('-74.0060')
    )


@pytest.mark.django_db
def test_vehicle_str_representation(vehicle):
    """Test vehicle string representation"""
    assert str(vehicle) == "BUS001 - ABC123"


@pytest.mark.django_db
def test_bus_stop_coordinates(bus_stop):
    """Test bus stop coordinates property"""
    assert bus_stop.coordinates == (40.7128, -74.0060)
    assert "google.com/maps" in bus_stop.google_maps_url


@pytest.mark.django_db
def test_route_optimization_service():
    """Test route optimization service functionality"""
    service = RouteOptimizationService()
    
    # Test distance calculation
    coord1 = (40.7128, -74.0060)
    coord2 = (40.7589, -73.9851)
    
    distance = service.calculate_distance(coord1, coord2)
    assert distance > 0
    assert isinstance(distance, float)
