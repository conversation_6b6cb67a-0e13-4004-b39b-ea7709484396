"""
URL patterns for transportation module
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'transportation'

# API Router for REST endpoints
router = DefaultRouter()
router.register(r'vehicles', views.VehicleViewSet)
router.register(r'drivers', views.DriverViewSet)
router.register(r'routes', views.RouteViewSet)
router.register(r'bus-stops', views.BusStopViewSet)
router.register(r'student-transportation', views.StudentTransportationViewSet)
router.register(r'student-attendance', views.StudentAttendanceViewSet)
router.register(r'transportation-fees', views.TransportationFeeViewSet)
router.register(r'parent-notifications', views.ParentNotificationViewSet)
router.register(r'gps-tracking', views.GPSTrackingViewSet)
router.register(r'analytics', views.TransportationAnalyticsViewSet)

urlpatterns = [
    # Web views
    path('', views.TransportationDashboardView.as_view(), name='dashboard'),
    path('routes/', views.RouteListView.as_view(), name='route_list'),
    path('routes/create/', views.RouteCreateView.as_view(), name='route_create'),
    path('routes/<uuid:pk>/', views.RouteDetailView.as_view(), name='route_detail'),
    path('routes/<uuid:pk>/edit/', views.RouteUpdateView.as_view(), name='route_edit'),
    path('routes/<uuid:pk>/update/', views.RouteUpdateView.as_view(), name='route_update'),
    path('routes/<uuid:pk>/optimize/', views.RouteOptimizeView.as_view(), name='route_optimize'),
    path('routes/<uuid:pk>/analytics/', views.RouteAnalyticsView.as_view(), name='route_analytics'),
    
    path('vehicles/', views.VehicleListView.as_view(), name='vehicle_list'),
    path('vehicles/create/', views.VehicleCreateView.as_view(), name='vehicle_create'),
    path('vehicles/<uuid:pk>/', views.VehicleDetailView.as_view(), name='vehicle_detail'),
    path('vehicles/<uuid:pk>/edit/', views.VehicleUpdateView.as_view(), name='vehicle_edit'),
    path('vehicles/<uuid:pk>/update/', views.VehicleUpdateView.as_view(), name='vehicle_update'),
    path('vehicles/<uuid:pk>/tracking/', views.VehicleTrackingView.as_view(), name='vehicle_tracking'),
    
    path('drivers/', views.DriverListView.as_view(), name='driver_list'),
    path('drivers/create/', views.DriverCreateView.as_view(), name='driver_create'),
    path('drivers/<uuid:pk>/', views.DriverDetailView.as_view(), name='driver_detail'),
    path('drivers/<uuid:pk>/edit/', views.DriverUpdateView.as_view(), name='driver_edit'),
    
    path('stops/', views.BusStopListView.as_view(), name='stop_list'),
    path('stops/create/', views.BusStopCreateView.as_view(), name='stop_create'),
    path('stops/<uuid:pk>/', views.BusStopDetailView.as_view(), name='stop_detail'),
    path('stops/<uuid:pk>/edit/', views.BusStopUpdateView.as_view(), name='stop_edit'),
    
    path('students/', views.StudentTransportationListView.as_view(), name='student_list'),
    path('students/assign/', views.StudentTransportationCreateView.as_view(), name='student_assign'),
    path('students/create/', views.StudentTransportationCreateView.as_view(), name='student_create'),
    path('students/<uuid:pk>/', views.StudentTransportationDetailView.as_view(), name='student_detail'),
    path('students/<uuid:pk>/edit/', views.StudentTransportationUpdateView.as_view(), name='student_edit'),
    
    # Attendance URLs
    path('attendance/', views.StudentAttendanceListView.as_view(), name='attendance_list'),
    path('attendance/record/', views.StudentAttendanceCreateView.as_view(), name='attendance_record'),
    path('attendance/bulk/', views.BulkAttendanceView.as_view(), name='attendance_bulk'),
    
    # Fee URLs
    path('fees/', views.TransportationFeeListView.as_view(), name='fee_list'),
    path('fees/create/', views.TransportationFeeCreateView.as_view(), name='fee_create'),
    path('fees/calculate/', views.FeeCalculationView.as_view(), name='fee_calculate'),
    
    # Notification URLs
    path('notifications/', views.ParentNotificationListView.as_view(), name='notification_list'),
    path('notifications/create/', views.ParentNotificationCreateView.as_view(), name='notification_create'),
    
    # Reports URLs
    path('reports/', views.TransportationReportsView.as_view(), name='reports'),
    path('reports/attendance/', views.AttendanceReportView.as_view(), name='attendance_report'),
    path('reports/fees/', views.FeeReportView.as_view(), name='fee_report'),
    
    path('analytics/', views.AnalyticsView.as_view(), name='analytics'),
    
    # AJAX endpoints
    path('ajax/route-stops/<uuid:route_id>/', views.RouteStopsAjaxView.as_view(), name='ajax_route_stops'),
    path('ajax/vehicle-location/<uuid:vehicle_id>/', views.VehicleLocationAjaxView.as_view(), name='ajax_vehicle_location'),
    path('ajax/route-optimization-status/<uuid:optimization_id>/', views.OptimizationStatusAjaxView.as_view(), name='ajax_optimization_status'),
    
    # API endpoints
    path('api/', include(router.urls)),
    path('api/routes/<uuid:route_id>/optimize/', views.RouteOptimizeAPIView.as_view(), name='api_route_optimize'),
    path('api/vehicles/<uuid:vehicle_id>/gps/', views.VehicleGPSAPIView.as_view(), name='api_vehicle_gps'),
    path('api/routes/<uuid:route_id>/dashboard/', views.RouteDashboardAPIView.as_view(), name='api_route_dashboard'),
]