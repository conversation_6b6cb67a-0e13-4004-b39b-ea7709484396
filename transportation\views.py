"""
Views for transportation module
"""

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.urls import reverse_lazy
from django.views.generic import (
    ListView, DetailView, CreateView, UpdateView, DeleteView, TemplateView
)
from django.http import JsonResponse, HttpResponseRedirect
from django.utils.translation import gettext_lazy as _
from django.db.models import Q, Count, Avg, Sum, F, Max
from django.utils import timezone
from datetime import timedelta
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated

from core.mixins import SchoolMixin
from .models import (
    Vehicle, Driver, Route, BusStop, RouteStop, StudentTransportation,
    RouteOptimization, GPSTracking, TransportationAnalytics, TransportationAttendance,
    TransportationFee, ParentNotification
)
from .services import (
    RouteOptimizationService, RouteAnalyticsService, GPSTrackingService,
    RouteManagementService
)
from .serializers import (
    VehicleSerializer, DriverSerializer, RouteSerializer, BusStopSerializer,
    StudentTransportationSerializer, GPSTrackingSerializer, TransportationAnalyticsSerializer,
    TransportationAttendanceSerializer, TransportationFeeSerializer, ParentNotificationSerializer
)
from .forms import (
    VehicleForm, DriverForm, RouteForm, BusStopForm, StudentTransportationForm,
    RouteOptimizationForm, TransportationAttendanceForm, TransportationFeeForm,
    ParentNotificationForm
)


# Dashboard Views
class TransportationDashboardView(LoginRequiredMixin, SchoolMixin, TemplateView):
    """Main transportation dashboard"""
    template_name = 'transportation/dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.get_school()
        
        # Summary statistics
        context.update({
            'total_vehicles': Vehicle.objects.filter(school=school, is_active=True).count(),
            'active_routes': Route.objects.filter(school=school, status='active').count(),
            'total_drivers': Driver.objects.filter(school=school, status='active').count(),
            'total_students': StudentTransportation.objects.filter(school=school, status='active').count(),
            'total_stops': BusStop.objects.filter(school=school, status='active').count(),
        })
        
        # Recent activities
        context['recent_optimizations'] = RouteOptimization.objects.filter(
            route__school=school
        ).order_by('-created_at')[:5]
        
        # Vehicles needing attention
        context['maintenance_due'] = Vehicle.objects.filter(
            school=school,
            next_maintenance_date__lte=timezone.now().date(),
            status='active'
        )
        
        context['insurance_expiring'] = Vehicle.objects.filter(
            school=school,
            insurance_expiry__lte=timezone.now().date() + timedelta(days=30),
            status='active'
        )
        
        # Route efficiency data
        context['route_efficiency'] = Route.objects.filter(
            school=school,
            status='active'
        ).annotate(
            occupancy_rate=Count('student_assignments') * 100.0 / F('max_capacity')
        )
        
        return context


# Vehicle Views
class VehicleListView(LoginRequiredMixin, SchoolMixin, ListView):
    """List all vehicles"""
    model = Vehicle
    template_name = 'transportation/vehicle_list.html'
    context_object_name = 'vehicles'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = Vehicle.objects.filter(school=self.get_school())
        
        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(vehicle_number__icontains=search) |
                Q(license_plate__icontains=search) |
                Q(make__icontains=search) |
                Q(model__icontains=search)
            )
        
        # Filter by status
        status_filter = self.request.GET.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        return queryset.order_by('-created_at')


class VehicleDetailView(LoginRequiredMixin, SchoolMixin, DetailView):
    """Vehicle detail view"""
    model = Vehicle
    template_name = 'transportation/vehicle_detail.html'
    context_object_name = 'vehicle'
    
    def get_queryset(self):
        return Vehicle.objects.filter(school=self.get_school())
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        vehicle = self.get_object()
        
        # Recent GPS tracking
        context['recent_tracking'] = GPSTracking.objects.filter(
            vehicle=vehicle
        ).order_by('-timestamp')[:10]
        
        # Assigned routes
        context['assigned_routes'] = Route.objects.filter(vehicle=vehicle)
        
        return context


class VehicleCreateView(LoginRequiredMixin, SchoolMixin, CreateView):
    """Create new vehicle"""
    model = Vehicle
    form_class = VehicleForm
    template_name = 'transportation/vehicle_form.html'
    success_url = reverse_lazy('transportation:vehicle_list')
    
    def form_valid(self, form):
        form.instance.school = self.get_school()
        messages.success(self.request, _('Vehicle created successfully.'))
        return super().form_valid(form)


class VehicleUpdateView(LoginRequiredMixin, SchoolMixin, UpdateView):
    """Update vehicle"""
    model = Vehicle
    form_class = VehicleForm
    template_name = 'transportation/vehicle_form.html'
    success_url = reverse_lazy('transportation:vehicle_list')
    
    def get_queryset(self):
        return Vehicle.objects.filter(school=self.get_school())
    
    def form_valid(self, form):
        messages.success(self.request, _('Vehicle updated successfully.'))
        return super().form_valid(form)


class VehicleTrackingView(LoginRequiredMixin, SchoolMixin, DetailView):
    """Vehicle GPS tracking view"""
    model = Vehicle
    template_name = 'transportation/vehicle_tracking.html'
    context_object_name = 'vehicle'
    
    def get_queryset(self):
        return Vehicle.objects.filter(school=self.get_school())
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        vehicle = self.get_object()
        
        # Get GPS tracking data for the last 24 hours
        since = timezone.now() - timedelta(hours=24)
        context['tracking_data'] = GPSTracking.objects.filter(
            vehicle=vehicle,
            timestamp__gte=since
        ).order_by('timestamp')
        
        # Current location
        gps_service = GPSTrackingService()
        context['current_location'] = gps_service.get_vehicle_current_location(vehicle)
        
        return context


# Route Views
class RouteListView(LoginRequiredMixin, SchoolMixin, ListView):
    """List all routes"""
    model = Route
    template_name = 'transportation/route_list.html'
    context_object_name = 'routes'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = Route.objects.filter(school=self.get_school()).select_related(
            'vehicle', 'primary_driver__employee'
        )
        
        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(code__icontains=search) |
                Q(name_ar__icontains=search)
            )
        
        # Filter by status
        status_filter = self.request.GET.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        return queryset.order_by('-created_at')


class RouteDetailView(LoginRequiredMixin, SchoolMixin, DetailView):
    """Route detail view"""
    model = Route
    template_name = 'transportation/route_detail.html'
    context_object_name = 'route'
    
    def get_queryset(self):
        return Route.objects.filter(school=self.get_school())
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        route = self.get_object()
        
        # Route stops
        context['route_stops'] = RouteStop.objects.filter(
            route=route
        ).select_related('bus_stop').order_by('sequence_order')
        
        # Assigned students
        context['assigned_students'] = StudentTransportation.objects.filter(
            route=route,
            status='active'
        ).select_related('student')
        
        # Recent optimizations
        context['recent_optimizations'] = RouteOptimization.objects.filter(
            route=route
        ).order_by('-created_at')[:5]
        
        # Performance metrics
        analytics_service = RouteAnalyticsService()
        try:
            context['performance'] = analytics_service.get_route_performance_summary(route)
        except:
            context['performance'] = None
        
        return context


class RouteCreateView(LoginRequiredMixin, SchoolMixin, CreateView):
    """Create new route"""
    model = Route
    form_class = RouteForm
    template_name = 'transportation/route_form.html'
    success_url = reverse_lazy('transportation:route_list')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['school'] = self.get_school()
        return kwargs
    
    def form_valid(self, form):
        form.instance.school = self.get_school()
        messages.success(self.request, _('Route created successfully.'))
        return super().form_valid(form)


class RouteUpdateView(LoginRequiredMixin, SchoolMixin, UpdateView):
    """Update route"""
    model = Route
    form_class = RouteForm
    template_name = 'transportation/route_form.html'
    success_url = reverse_lazy('transportation:route_list')
    
    def get_queryset(self):
        return Route.objects.filter(school=self.get_school())
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['school'] = self.get_school()
        return kwargs
    
    def form_valid(self, form):
        messages.success(self.request, _('Route updated successfully.'))
        return super().form_valid(form)


class RouteOptimizeView(LoginRequiredMixin, SchoolMixin, DetailView):
    """Route optimization view"""
    model = Route
    template_name = 'transportation/route_optimize.html'
    context_object_name = 'route'
    
    def get_queryset(self):
        return Route.objects.filter(school=self.get_school())
    
    def post(self, request, *args, **kwargs):
        route = self.get_object()
        optimization_type = request.POST.get('optimization_type', 'nearest_neighbor')
        
        try:
            service = RouteOptimizationService()
            optimization = service.optimize_route(route, optimization_type)
            
            if optimization.status == 'completed':
                messages.success(request, _('Route optimized successfully.'))
            else:
                messages.error(request, _('Route optimization failed: {}').format(optimization.error_message))
        
        except Exception as e:
            messages.error(request, _('Route optimization failed: {}').format(str(e)))
        
        return redirect('transportation:route_detail', pk=route.pk)


class RouteAnalyticsView(LoginRequiredMixin, SchoolMixin, DetailView):
    """Route analytics view"""
    model = Route
    template_name = 'transportation/route_analytics.html'
    context_object_name = 'route'
    
    def get_queryset(self):
        return Route.objects.filter(school=self.get_school())
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        route = self.get_object()
        
        # Analytics for different time periods
        analytics_service = RouteAnalyticsService()
        
        for days in [7, 30, 90]:
            try:
                performance = analytics_service.get_route_performance_summary(route, days)
                context[f'performance_{days}d'] = performance
            except:
                context[f'performance_{days}d'] = None
        
        # Historical analytics
        context['historical_analytics'] = TransportationAnalytics.objects.filter(
            route=route
        ).order_by('-date_to')[:12]
        
        return context


# Driver Views
class DriverListView(LoginRequiredMixin, SchoolMixin, ListView):
    """List all drivers"""
    model = Driver
    template_name = 'transportation/driver_list.html'
    context_object_name = 'drivers'
    paginate_by = 20
    
    def get_queryset(self):
        return Driver.objects.filter(school=self.get_school()).select_related('employee')


class DriverDetailView(LoginRequiredMixin, SchoolMixin, DetailView):
    """Driver detail view"""
    model = Driver
    template_name = 'transportation/driver_detail.html'
    context_object_name = 'driver'
    
    def get_queryset(self):
        return Driver.objects.filter(school=self.get_school())


class DriverCreateView(LoginRequiredMixin, SchoolMixin, CreateView):
    """Create new driver"""
    model = Driver
    form_class = DriverForm
    template_name = 'transportation/driver_form.html'
    success_url = reverse_lazy('transportation:driver_list')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['school'] = self.get_school()
        return kwargs
    
    def form_valid(self, form):
        form.instance.school = self.get_school()
        messages.success(self.request, _('Driver created successfully.'))
        return super().form_valid(form)


class DriverUpdateView(LoginRequiredMixin, SchoolMixin, UpdateView):
    """Update driver"""
    model = Driver
    form_class = DriverForm
    template_name = 'transportation/driver_form.html'
    success_url = reverse_lazy('transportation:driver_list')
    
    def get_queryset(self):
        return Driver.objects.filter(school=self.get_school())
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['school'] = self.get_school()
        return kwargs
    
    def form_valid(self, form):
        messages.success(self.request, _('Driver updated successfully.'))
        return super().form_valid(form)


# Bus Stop Views
class BusStopListView(LoginRequiredMixin, SchoolMixin, ListView):
    """List all bus stops"""
    model = BusStop
    template_name = 'transportation/stop_list.html'
    context_object_name = 'stops'
    paginate_by = 20
    
    def get_queryset(self):
        return BusStop.objects.filter(school=self.get_school())


class BusStopDetailView(LoginRequiredMixin, SchoolMixin, DetailView):
    """Bus stop detail view"""
    model = BusStop
    template_name = 'transportation/stop_detail.html'
    context_object_name = 'stop'
    
    def get_queryset(self):
        return BusStop.objects.filter(school=self.get_school())


class BusStopCreateView(LoginRequiredMixin, SchoolMixin, CreateView):
    """Create new bus stop"""
    model = BusStop
    form_class = BusStopForm
    template_name = 'transportation/stop_form.html'
    success_url = reverse_lazy('transportation:stop_list')
    
    def form_valid(self, form):
        form.instance.school = self.get_school()
        messages.success(self.request, _('Bus stop created successfully.'))
        return super().form_valid(form)


class BusStopUpdateView(LoginRequiredMixin, SchoolMixin, UpdateView):
    """Update bus stop"""
    model = BusStop
    form_class = BusStopForm
    template_name = 'transportation/stop_form.html'
    success_url = reverse_lazy('transportation:stop_list')
    
    def get_queryset(self):
        return BusStop.objects.filter(school=self.get_school())
    
    def form_valid(self, form):
        messages.success(self.request, _('Bus stop updated successfully.'))
        return super().form_valid(form)


# Student Transportation Views
class StudentTransportationListView(LoginRequiredMixin, SchoolMixin, ListView):
    """List all student transportation assignments"""
    model = StudentTransportation
    template_name = 'transportation/student_list.html'
    context_object_name = 'assignments'
    paginate_by = 20
    
    def get_queryset(self):
        return StudentTransportation.objects.filter(
            school=self.get_school()
        ).select_related('student', 'route', 'pickup_stop', 'dropoff_stop')


class StudentTransportationDetailView(LoginRequiredMixin, SchoolMixin, DetailView):
    """Student transportation detail view"""
    model = StudentTransportation
    template_name = 'transportation/student_detail.html'
    context_object_name = 'assignment'
    
    def get_queryset(self):
        return StudentTransportation.objects.filter(school=self.get_school()).select_related(
            'student', 'route', 'pickup_stop', 'dropoff_stop', 
            'route__vehicle', 'route__primary_driver__employee'
        )
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        assignment = self.get_object()
        
        # Recent attendance records (last 30 days)
        from .models import TransportationAttendance
        context['recent_attendance'] = TransportationAttendance.objects.filter(
            student_transportation=assignment,
            date__gte=timezone.now().date() - timedelta(days=30)
        ).select_related('bus_stop', 'driver__employee').order_by('-date')[:20]
        
        # Fee history (last 12 months)
        from .models import TransportationFee
        context['fee_history'] = TransportationFee.objects.filter(
            student_transportation=assignment,
            month__gte=timezone.now().date() - timedelta(days=365)
        ).order_by('-month')[:12]
        
        # Recent notifications (last 30 days)
        from .models import ParentNotification
        context['recent_notifications'] = ParentNotification.objects.filter(
            student_transportation=assignment,
            created_at__gte=timezone.now() - timedelta(days=30)
        ).order_by('-created_at')[:10]
        
        return context


class StudentTransportationCreateView(LoginRequiredMixin, SchoolMixin, CreateView):
    """Assign student to transportation"""
    model = StudentTransportation
    form_class = StudentTransportationForm
    template_name = 'transportation/student_form.html'
    success_url = reverse_lazy('transportation:student_list')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['school'] = self.get_school()
        return kwargs
    
    def form_valid(self, form):
        form.instance.school = self.get_school()
        
        # Update route occupancy
        route = form.instance.route
        route.update_occupancy()
        
        messages.success(self.request, _('Student assigned to transportation successfully.'))
        return super().form_valid(form)


class StudentTransportationUpdateView(LoginRequiredMixin, SchoolMixin, UpdateView):
    """Update student transportation assignment"""
    model = StudentTransportation
    form_class = StudentTransportationForm
    template_name = 'transportation/student_form.html'
    success_url = reverse_lazy('transportation:student_list')
    
    def get_queryset(self):
        return StudentTransportation.objects.filter(school=self.get_school())
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['school'] = self.get_school()
        return kwargs
    
    def form_valid(self, form):
        messages.success(self.request, _('Student transportation updated successfully.'))
        return super().form_valid(form)


# Analytics and Reports Views
class AnalyticsView(LoginRequiredMixin, SchoolMixin, TemplateView):
    """Transportation analytics dashboard"""
    template_name = 'transportation/analytics.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.get_school()
        
        # Overall statistics
        context['total_routes'] = Route.objects.filter(school=school).count()
        context['active_routes'] = Route.objects.filter(school=school, status='active').count()
        context['total_vehicles'] = Vehicle.objects.filter(school=school).count()
        context['total_students'] = StudentTransportation.objects.filter(school=school, status='active').count()
        
        # Route efficiency metrics
        routes = Route.objects.filter(school=school, status='active')
        context['route_efficiency'] = []
        
        for route in routes:
            occupancy_rate = (route.current_occupancy / route.max_capacity * 100) if route.max_capacity > 0 else 0
            context['route_efficiency'].append({
                'route': route,
                'occupancy_rate': occupancy_rate,
                'distance': route.total_distance_km,
                'students': route.current_occupancy
            })
        
        # Recent analytics
        context['recent_analytics'] = TransportationAnalytics.objects.filter(
            route__school=school
        ).order_by('-date_to')[:10]
        
        return context


class ReportsView(LoginRequiredMixin, SchoolMixin, TemplateView):
    """Transportation reports"""
    template_name = 'transportation/reports.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.get_school()
        
        # Vehicle maintenance report
        context['maintenance_due'] = Vehicle.objects.filter(
            school=school,
            next_maintenance_date__lte=timezone.now().date() + timedelta(days=30)
        )
        
        # Driver license expiry report
        context['license_expiring'] = Driver.objects.filter(
            school=school,
            license_expiry__lte=timezone.now().date() + timedelta(days=30)
        )
        
        # Route optimization opportunities
        context['optimization_opportunities'] = Route.objects.filter(
            school=school,
            status='active'
        ).annotate(
            last_optimization=Max('optimizations__processed_at')
        ).filter(
            Q(last_optimization__isnull=True) |
            Q(last_optimization__lt=timezone.now() - timedelta(days=30))
        )
        
        return context


# AJAX Views
class RouteStopsAjaxView(LoginRequiredMixin, SchoolMixin, APIView):
    """Get route stops via AJAX"""
    
    def get(self, request, route_id):
        try:
            route = Route.objects.get(id=route_id, school=self.get_school())
            stops = RouteStop.objects.filter(route=route).select_related('bus_stop').order_by('sequence_order')
            
            data = [{
                'id': rs.bus_stop.id,
                'name': rs.bus_stop.name,
                'sequence': rs.sequence_order,
                'coordinates': rs.bus_stop.coordinates,
                'arrival_time_morning': rs.estimated_arrival_time_morning.strftime('%H:%M') if rs.estimated_arrival_time_morning else None,
                'arrival_time_afternoon': rs.estimated_arrival_time_afternoon.strftime('%H:%M') if rs.estimated_arrival_time_afternoon else None,
            } for rs in stops]
            
            return JsonResponse({'stops': data})
        
        except Route.DoesNotExist:
            return JsonResponse({'error': 'Route not found'}, status=404)


class VehicleLocationAjaxView(LoginRequiredMixin, SchoolMixin, APIView):
    """Get vehicle current location via AJAX"""
    
    def get(self, request, vehicle_id):
        try:
            vehicle = Vehicle.objects.get(id=vehicle_id, school=self.get_school())
            gps_service = GPSTrackingService()
            location = gps_service.get_vehicle_current_location(vehicle)
            
            if location:
                data = {
                    'latitude': float(location.latitude),
                    'longitude': float(location.longitude),
                    'speed': float(location.speed_kmh),
                    'timestamp': location.timestamp.isoformat(),
                    'engine_status': location.engine_status,
                }
            else:
                data = {'error': 'No location data available'}
            
            return JsonResponse(data)
        
        except Vehicle.DoesNotExist:
            return JsonResponse({'error': 'Vehicle not found'}, status=404)


class OptimizationStatusAjaxView(LoginRequiredMixin, SchoolMixin, APIView):
    """Get route optimization status via AJAX"""
    
    def get(self, request, optimization_id):
        try:
            optimization = RouteOptimization.objects.get(
                id=optimization_id,
                route__school=self.get_school()
            )
            
            data = {
                'status': optimization.status,
                'processed_at': optimization.processed_at.isoformat() if optimization.processed_at else None,
                'error_message': optimization.error_message,
                'savings_km': float(optimization.distance_savings_km) if optimization.distance_savings_km else None,
                'savings_percentage': float(optimization.fuel_savings_percentage) if optimization.fuel_savings_percentage else None,
            }
            
            return JsonResponse(data)
        
        except RouteOptimization.DoesNotExist:
            return JsonResponse({'error': 'Optimization not found'}, status=404)


# Student Attendance Views
class StudentAttendanceListView(LoginRequiredMixin, SchoolMixin, ListView):
    """List student attendance records"""
    model = TransportationAttendance
    template_name = 'transportation/attendance_list.html'
    context_object_name = 'attendance_records'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = TransportationAttendance.objects.filter(
            student_transportation__school=self.get_school()
        ).select_related('student_transportation__student', 'bus_stop')
        
        # Filter by date range
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        
        if date_from:
            queryset = queryset.filter(date__gte=date_from)
        if date_to:
            queryset = queryset.filter(date__lte=date_to)
        
        # Filter by route
        route_id = self.request.GET.get('route')
        if route_id:
            queryset = queryset.filter(student_transportation__route_id=route_id)
        
        # Filter by attendance type
        attendance_type = self.request.GET.get('attendance_type')
        if attendance_type:
            queryset = queryset.filter(attendance_type=attendance_type)
        
        return queryset.order_by('-date', 'attendance_type')


class StudentAttendanceCreateView(LoginRequiredMixin, SchoolMixin, CreateView):
    """Record student attendance"""
    model = TransportationAttendance
    form_class = TransportationAttendanceForm
    template_name = 'transportation/attendance_form.html'
    success_url = reverse_lazy('transportation:attendance_list')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['school'] = self.get_school()
        return kwargs
    
    def form_valid(self, form):
        form.instance.school = self.get_school()
        messages.success(self.request, _('Attendance recorded successfully.'))
        return super().form_valid(form)


class BulkAttendanceView(LoginRequiredMixin, SchoolMixin, TemplateView):
    """Bulk attendance recording view"""
    template_name = 'transportation/bulk_attendance.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = BulkAttendanceForm(school=self.get_school())
        return context
    
    def post(self, request, *args, **kwargs):
        form = BulkAttendanceForm(request.POST, school=self.get_school())
        
        if form.is_valid():
            route = form.cleaned_data['route']
            date = form.cleaned_data['date']
            attendance_type = form.cleaned_data['attendance_type']
            
            # Get all students for this route
            students = StudentTransportation.objects.filter(
                route=route,
                status='active'
            )
            
            # Process attendance data from form
            attendance_service = TransportationAttendanceService()
            recorded_count = 0
            
            for student_transport in students:
                student_id = str(student_transport.id)
                status = request.POST.get(f'status_{student_id}', 'present')
                actual_time = request.POST.get(f'time_{student_id}')
                
                if actual_time:
                    from datetime import datetime
                    actual_time = datetime.strptime(actual_time, '%H:%M').time()
                
                attendance_service.record_attendance(
                    student_transport,
                    attendance_type,
                    status,
                    actual_time
                )
                recorded_count += 1
            
            messages.success(request, _('Recorded attendance for {} students.').format(recorded_count))
            return redirect('transportation:attendance_list')
        
        return self.render_to_response({'form': form})


# Transportation Fee Views
class TransportationFeeListView(LoginRequiredMixin, SchoolMixin, ListView):
    """List transportation fees"""
    model = TransportationFee
    template_name = 'transportation/fee_list.html'
    context_object_name = 'fees'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = TransportationFee.objects.filter(
            student_transportation__school=self.get_school()
        ).select_related('student_transportation__student', 'student_transportation__route')
        
        # Filter by month
        month = self.request.GET.get('month')
        if month:
            queryset = queryset.filter(month__startswith=month)
        
        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
        
        return queryset.order_by('-month', 'student_transportation__student__first_name')


class TransportationFeeCreateView(LoginRequiredMixin, SchoolMixin, CreateView):
    """Create transportation fee"""
    model = TransportationFee
    form_class = TransportationFeeForm
    template_name = 'transportation/fee_form.html'
    success_url = reverse_lazy('transportation:fee_list')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['school'] = self.get_school()
        return kwargs
    
    def form_valid(self, form):
        form.instance.school = self.get_school()
        messages.success(self.request, _('Transportation fee created successfully.'))
        return super().form_valid(form)


class FeeCalculationView(LoginRequiredMixin, SchoolMixin, TemplateView):
    """Bulk fee calculation view"""
    template_name = 'transportation/fee_calculation.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = FeeCalculationForm()
        return context
    
    def post(self, request, *args, **kwargs):
        form = FeeCalculationForm(request.POST)
        
        if form.is_valid():
            month = form.cleaned_data['month']
            calculation_type = form.cleaned_data['calculation_type']
            recalculate_existing = form.cleaned_data['recalculate_existing']
            
            fee_service = TransportationFeeService()
            fees = fee_service.generate_monthly_fees(self.get_school(), month)
            
            messages.success(request, _('Generated {} transportation fees.').format(len(fees)))
            return redirect('transportation:fee_list')
        
        return self.render_to_response({'form': form})


# Parent Notification Views
class ParentNotificationListView(LoginRequiredMixin, SchoolMixin, ListView):
    """List parent notifications"""
    model = ParentNotification
    template_name = 'transportation/notification_list.html'
    context_object_name = 'notifications'
    paginate_by = 20
    
    def get_queryset(self):
        return ParentNotification.objects.filter(
            student_transportation__school=self.get_school()
        ).select_related('student_transportation__student').order_by('-created_at')


class ParentNotificationCreateView(LoginRequiredMixin, SchoolMixin, CreateView):
    """Create parent notification"""
    model = ParentNotification
    form_class = ParentNotificationForm
    template_name = 'transportation/notification_form.html'
    success_url = reverse_lazy('transportation:notification_list')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['school'] = self.get_school()
        return kwargs
    
    def form_valid(self, form):
        form.instance.school = self.get_school()
        messages.success(self.request, _('Parent notification created successfully.'))
        return super().form_valid(form)


# Transportation Reports Views
class TransportationReportsView(LoginRequiredMixin, SchoolMixin, TemplateView):
    """Transportation reports dashboard"""
    template_name = 'transportation/reports.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.get_school()
        
        # Report forms
        context['attendance_form'] = AttendanceReportForm(school=school)
        context['fee_form'] = FeeReportForm()
        
        # Quick stats
        today = timezone.now().date()
        context['today_attendance'] = TransportationAttendance.objects.filter(
            student_transportation__school=school,
            date=today
        ).count()
        
        context['overdue_fees'] = TransportationFee.objects.filter(
            student_transportation__school=school,
            due_date__lt=today,
            status__in=['pending', 'calculated', 'invoiced']
        ).count()
        
        context['pending_notifications'] = ParentNotification.objects.filter(
            student_transportation__school=school,
            status='pending'
        ).count()
        
        return context


class AttendanceReportView(LoginRequiredMixin, SchoolMixin, TemplateView):
    """Generate attendance report"""
    template_name = 'transportation/attendance_report.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get parameters from request
        route_id = self.request.GET.get('route')
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        attendance_type = self.request.GET.get('attendance_type')
        
        if date_from and date_to:
            from datetime import datetime
            date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
            date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
            
            # Generate report
            reporting_service = TransportationReportingService()
            report_data = reporting_service.generate_student_transportation_report(
                self.get_school(), date_from, date_to
            )
            
            context['report_data'] = report_data
            context['date_from'] = date_from
            context['date_to'] = date_to
        
        return context


class FeeReportView(LoginRequiredMixin, SchoolMixin, TemplateView):
    """Generate fee collection report"""
    template_name = 'transportation/fee_report.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get parameters from request
        month = self.request.GET.get('month')
        
        if month:
            from datetime import datetime
            month_date = datetime.strptime(month, '%Y-%m')
            
            # Generate report
            reporting_service = TransportationReportingService()
            report_data = reporting_service.generate_fee_collection_report(
                self.get_school(), month_date
            )
            
            context['report_data'] = report_data
            context['month'] = month_date
        
        return context


# API ViewSets
class VehicleViewSet(viewsets.ModelViewSet):
    """Vehicle API ViewSet"""
    queryset = Vehicle.objects.all()
    serializer_class = VehicleSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return Vehicle.objects.filter(school=self.request.user.employee.school)


class DriverViewSet(viewsets.ModelViewSet):
    """Driver API ViewSet"""
    queryset = Driver.objects.all()
    serializer_class = DriverSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return Driver.objects.filter(school=self.request.user.employee.school)


class RouteViewSet(viewsets.ModelViewSet):
    """Route API ViewSet"""
    queryset = Route.objects.all()
    serializer_class = RouteSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return Route.objects.filter(school=self.request.user.employee.school)
    
    @action(detail=True, methods=['post'])
    def optimize(self, request, pk=None):
        """Optimize route endpoint"""
        route = self.get_object()
        optimization_type = request.data.get('optimization_type', 'nearest_neighbor')
        
        try:
            service = RouteOptimizationService()
            optimization = service.optimize_route(route, optimization_type)
            
            return Response({
                'status': optimization.status,
                'optimization_id': optimization.id,
                'message': 'Route optimization started successfully'
            })
        
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


class BusStopViewSet(viewsets.ModelViewSet):
    """Bus Stop API ViewSet"""
    queryset = BusStop.objects.all()
    serializer_class = BusStopSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return BusStop.objects.filter(school=self.request.user.employee.school)


class StudentTransportationViewSet(viewsets.ModelViewSet):
    """Student Transportation API ViewSet"""
    queryset = StudentTransportation.objects.all()
    serializer_class = StudentTransportationSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return StudentTransportation.objects.filter(school=self.request.user.employee.school)


class GPSTrackingViewSet(viewsets.ModelViewSet):
    """GPS Tracking API ViewSet"""
    queryset = GPSTracking.objects.all()
    serializer_class = GPSTrackingSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return GPSTracking.objects.filter(vehicle__school=self.request.user.employee.school)


class StudentAttendanceViewSet(viewsets.ModelViewSet):
    """Student Attendance API ViewSet"""
    queryset = TransportationAttendance.objects.all()
    serializer_class = TransportationAttendanceSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return TransportationAttendance.objects.filter(
            student_transportation__school=self.request.user.employee.school
        ).select_related('student_transportation__student', 'bus_stop')
    
    @action(detail=False, methods=['post'])
    def bulk_record(self, request):
        """Bulk attendance recording endpoint"""
        route_id = request.data.get('route_id')
        date = request.data.get('date')
        attendance_type = request.data.get('attendance_type')
        attendance_data = request.data.get('attendance_data', [])
        
        try:
            route = Route.objects.get(id=route_id, school=request.user.employee.school)
            attendance_service = TransportationAttendanceService()
            
            recorded_count = 0
            for data in attendance_data:
                student_transport = StudentTransportation.objects.get(
                    id=data['student_transportation_id'],
                    route=route
                )
                
                attendance_service.record_attendance(
                    student_transport,
                    attendance_type,
                    data.get('status', 'present'),
                    data.get('actual_time')
                )
                recorded_count += 1
            
            return Response({
                'message': f'Recorded attendance for {recorded_count} students',
                'recorded_count': recorded_count
            })
        
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


class TransportationFeeViewSet(viewsets.ModelViewSet):
    """Transportation Fee API ViewSet"""
    queryset = TransportationFee.objects.all()
    serializer_class = TransportationFeeSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return TransportationFee.objects.filter(
            student_transportation__school=self.request.user.employee.school
        ).select_related('student_transportation__student', 'student_transportation__route')
    
    @action(detail=False, methods=['post'])
    def calculate_monthly_fees(self, request):
        """Calculate monthly fees for all students"""
        month = request.data.get('month')
        calculation_type = request.data.get('calculation_type', 'fixed')
        
        try:
            from datetime import datetime
            month_date = datetime.strptime(month, '%Y-%m')
            
            fee_service = TransportationFeeService()
            fees = fee_service.generate_monthly_fees(request.user.employee.school, month_date)
            
            return Response({
                'message': f'Generated {len(fees)} transportation fees',
                'fees_count': len(fees),
                'month': month
            })
        
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def overdue_fees(self, request):
        """Get overdue fees"""
        fee_service = TransportationFeeService()
        overdue_fees = fee_service.get_overdue_fees(request.user.employee.school)
        
        serializer = self.get_serializer(overdue_fees, many=True)
        return Response(serializer.data)


class ParentNotificationViewSet(viewsets.ModelViewSet):
    """Parent Notification API ViewSet"""
    queryset = ParentNotification.objects.all()
    serializer_class = ParentNotificationSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return ParentNotification.objects.filter(
            student_transportation__school=self.request.user.employee.school
        ).select_related('student_transportation__student')
    
    @action(detail=False, methods=['post'])
    def send_pickup_reminders(self, request):
        """Send pickup reminders for a route"""
        route_id = request.data.get('route_id')
        scheduled_time = request.data.get('scheduled_time')
        
        try:
            route = Route.objects.get(id=route_id, school=request.user.employee.school)
            
            # Get all active students on this route
            students = StudentTransportation.objects.filter(
                route=route,
                status='active'
            )
            
            notification_service = ParentNotificationService()
            notifications_sent = 0
            
            for student_transport in students:
                from datetime import datetime
                scheduled_datetime = datetime.fromisoformat(scheduled_time.replace('Z', '+00:00'))
                notifications = notification_service.send_pickup_reminder(
                    student_transport, 
                    scheduled_datetime
                )
                notifications_sent += len(notifications)
            
            return Response({
                'message': f'Sent {notifications_sent} pickup reminders',
                'notifications_sent': notifications_sent
            })
        
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['post'])
    def process_pending(self, request):
        """Process pending notifications"""
        notification_service = ParentNotificationService()
        sent_count = notification_service.process_pending_notifications()
        
        return Response({
            'message': f'Processed {sent_count} pending notifications',
            'sent_count': sent_count
        })


class TransportationAnalyticsViewSet(viewsets.ReadOnlyModelViewSet):
    """Transportation Analytics API ViewSet"""
    queryset = TransportationAnalytics.objects.all()
    serializer_class = TransportationAnalyticsSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return TransportationAnalytics.objects.filter(route__school=self.request.user.employee.school)


# Additional API Views
class RouteOptimizeAPIView(APIView):
    """API endpoint for route optimization"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request, route_id):
        try:
            route = Route.objects.get(id=route_id, school=request.user.employee.school)
            optimization_type = request.data.get('optimization_type', 'nearest_neighbor')
            
            service = RouteOptimizationService()
            optimization = service.optimize_route(route, optimization_type)
            
            return Response({
                'optimization_id': optimization.id,
                'status': optimization.status,
                'message': 'Route optimization started'
            })
        
        except Route.DoesNotExist:
            return Response({'error': 'Route not found'}, status=404)
        except Exception as e:
            return Response({'error': str(e)}, status=400)


class VehicleGPSAPIView(APIView):
    """API endpoint for vehicle GPS data"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request, vehicle_id):
        """Receive GPS data from vehicle"""
        try:
            vehicle = Vehicle.objects.get(id=vehicle_id, school=request.user.employee.school)
            
            service = GPSTrackingService()
            tracking = service.process_gps_data(vehicle, request.data)
            
            # Check for alerts
            alerts = service.check_geofence_alerts(tracking)
            
            return Response({
                'tracking_id': tracking.id,
                'alerts': alerts,
                'message': 'GPS data processed successfully'
            })
        
        except Vehicle.DoesNotExist:
            return Response({'error': 'Vehicle not found'}, status=404)
        except Exception as e:
            return Response({'error': str(e)}, status=400)
    
    def get(self, request, vehicle_id):
        """Get vehicle GPS history"""
        try:
            vehicle = Vehicle.objects.get(id=vehicle_id, school=request.user.employee.school)
            
            # Get GPS data for the last 24 hours by default
            hours = int(request.GET.get('hours', 24))
            since = timezone.now() - timedelta(hours=hours)
            
            tracking_data = GPSTracking.objects.filter(
                vehicle=vehicle,
                timestamp__gte=since
            ).order_by('timestamp')
            
            serializer = GPSTrackingSerializer(tracking_data, many=True)
            return Response(serializer.data)
        
        except Vehicle.DoesNotExist:
            return Response({'error': 'Vehicle not found'}, status=404)


class RouteDashboardAPIView(APIView):
    """API endpoint for route dashboard data"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request, route_id):
        try:
            route = Route.objects.get(id=route_id, school=request.user.employee.school)
            
            service = RouteManagementService()
            dashboard_data = service.get_route_dashboard_data(route)
            
            return Response(dashboard_data)
        
        except Route.DoesNotExist:
            return Response({'error': 'Route not found'}, status=404)
