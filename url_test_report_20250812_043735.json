{"total_urls": 56, "successful": 0, "failed": 56, "redirected": 0, "errors": [], "details": [{"url_name": "home", "url_path": "/", "user_type": null, "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "accounts:login", "url_path": "/accounts/login/", "user_type": null, "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "accounts:register", "url_path": "/accounts/register/", "user_type": null, "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "admin:index", "url_path": "/admin/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "accounts:dashboard", "url_path": "/accounts/dashboard/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "accounts:dashboard", "url_path": "/accounts/dashboard/", "user_type": "teacher", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "accounts:dashboard", "url_path": "/accounts/dashboard/", "user_type": "parent", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "accounts:dashboard", "url_path": "/accounts/dashboard/", "user_type": "student", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "students:student_list", "url_path": "/students/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "students:student_create", "url_path": "/students/create/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "students:grade_list", "url_path": "/students/grades/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "students:class_list", "url_path": "/students/classes/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "students:parent_list", "url_path": "/students/parents/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "academics:subject_list", "url_path": "/academics/subjects/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "academics:teacher_list", "url_path": "/academics/teachers/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "academics:schedule_list", "url_path": "/academics/schedules/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "academics:exam_list", "url_path": "/academics/exams/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "academics:grade_list", "url_path": "/academics/grades/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "finance:dashboard", "url_path": "/finance/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "finance:account_list", "url_path": "/finance/accounts/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "finance:fee_type_list", "url_path": "/finance/fee-types/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "finance:payment_list", "url_path": "/finance/payments/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "finance:student_fee_list", "url_path": "/finance/student-fees/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "hr:employee_list", "url_path": "/hr/employees/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "hr:department_list", "url_path": "/hr/departments/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "hr:position_list", "url_path": "/hr/positions/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "hr:attendance_list", "url_path": "/hr/attendance/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "health:health_record_list", "url_path": "/health/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "health:medical_checkup_list", "url_path": "/health/checkups/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "health:vaccination_list", "url_path": "/health/vaccinations/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "library:book_list", "url_path": "/library/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "library:category_list", "url_path": "/library/categories/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "library:issue_list", "url_path": "/library/issues/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "transportation:route_list", "url_path": "/transportation/routes/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "transportation:bus_list", "url_path": "/transportation/buses/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "transportation:student_transport_list", "url_path": "/transportation/student-transport/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "inventory:item_list", "url_path": "/inventory/items/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "inventory:category_list", "url_path": "/inventory/categories/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "inventory:supplier_list", "url_path": "/inventory/suppliers/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "reports:dashboard", "url_path": "/reports/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "reports:student_report", "url_path": "/reports/students/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "reports:financial_report", "url_path": "/reports/financial/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "reports:academic_report", "url_path": "/reports/academic/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "core:school_settings", "url_path": "/core/settings/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "core:academic_year_list", "url_path": "/core/academic-years/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "core:semester_list", "url_path": "/core/semesters/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": null, "url_path": "/api/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": null, "url_path": "/api/students/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": null, "url_path": "/api/teachers/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": null, "url_path": "/api/classes/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "students:student_detail", "url_path": "/students/462b0cce-f320-4c21-9d79-4248141cace3/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "students:student_detail", "url_path": "/students/8c2f461f-0f7d-4e8f-9ca4-b769631252ac/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "students:student_detail", "url_path": "/students/c2e95143-e1f3-40b1-b281-bf6de0885d7d/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "students:grade_detail", "url_path": "/students/grades/8ea05cae-87b1-4cd3-911f-b75b7fd56b6f/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "students:grade_detail", "url_path": "/students/grades/885e8fdf-e140-41a5-8348-551c7b32bbce/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}, {"url_name": "students:grade_detail", "url_path": "/students/grades/2d59b6c4-9829-4813-955f-a0aec056ace8/", "user_type": "admin", "status_code": 400, "success": false, "message": "HTTP 400", "redirect_chain": []}]}