
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL Test Report - 20250812_050713</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f4f4f4; padding: 20px; border-radius: 5px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .stat-box { background: #e9ecef; padding: 15px; border-radius: 5px; text-align: center; }
        .success { color: #28a745; }
        .redirect { color: #ffc107; }
        .failed { color: #dc3545; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .status-success { background-color: #d4edda; }
        .status-redirect { background-color: #fff3cd; }
        .status-failed { background-color: #f8d7da; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌐 School ERP URL Test Report</h1>
        <p>Generated on: 2025-08-12 05:07:13</p>
    </div>
    
    <div class="summary">
        <div class="stat-box">
            <h3>Total URLs</h3>
            <p style="font-size: 24px; margin: 0;">56</p>
        </div>
        <div class="stat-box">
            <h3 class="success">Successful</h3>
            <p style="font-size: 24px; margin: 0;">0</p>
        </div>
        <div class="stat-box">
            <h3 class="redirect">Redirected</h3>
            <p style="font-size: 24px; margin: 0;">0</p>
        </div>
        <div class="stat-box">
            <h3 class="failed">Failed</h3>
            <p style="font-size: 24px; margin: 0;">56</p>
        </div>
    </div>
    
    <h2>📋 Detailed Results</h2>
    <table>
        <thead>
            <tr>
                <th>URL Path</th>
                <th>URL Name</th>
                <th>User Type</th>
                <th>Status Code</th>
                <th>Message</th>
                <th>Redirects</th>
            </tr>
        </thead>
        <tbody>

            <tr class="status-failed">
                <td>/</td>
                <td>home</td>
                <td>-</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/accounts/login/</td>
                <td>accounts:login</td>
                <td>-</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/accounts/register/</td>
                <td>accounts:register</td>
                <td>-</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/admin/</td>
                <td>admin:index</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/accounts/dashboard/</td>
                <td>accounts:dashboard</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/accounts/dashboard/</td>
                <td>accounts:dashboard</td>
                <td>teacher</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/accounts/dashboard/</td>
                <td>accounts:dashboard</td>
                <td>parent</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/accounts/dashboard/</td>
                <td>accounts:dashboard</td>
                <td>student</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/students/</td>
                <td>students:student_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/students/create/</td>
                <td>students:student_create</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/students/grades/</td>
                <td>students:grade_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/students/classes/</td>
                <td>students:class_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/students/parents/</td>
                <td>students:parent_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/academics/subjects/</td>
                <td>academics:subject_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/academics/teachers/</td>
                <td>academics:teacher_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/academics/schedules/</td>
                <td>academics:schedule_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/academics/exams/</td>
                <td>academics:exam_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/academics/grades/</td>
                <td>academics:grade_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/finance/</td>
                <td>finance:dashboard</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/finance/accounts/</td>
                <td>finance:account_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/finance/fee-types/</td>
                <td>finance:fee_type_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/finance/payments/</td>
                <td>finance:payment_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/finance/student-fees/</td>
                <td>finance:student_fee_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/hr/employees/</td>
                <td>hr:employee_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/hr/departments/</td>
                <td>hr:department_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/hr/positions/</td>
                <td>hr:position_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/hr/attendance/</td>
                <td>hr:attendance_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/health/</td>
                <td>health:health_record_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/health/checkups/</td>
                <td>health:medical_checkup_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/health/vaccinations/</td>
                <td>health:vaccination_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/library/</td>
                <td>library:book_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/library/categories/</td>
                <td>library:category_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/library/issues/</td>
                <td>library:issue_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/transportation/routes/</td>
                <td>transportation:route_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/transportation/buses/</td>
                <td>transportation:bus_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/transportation/student-transport/</td>
                <td>transportation:student_transport_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/inventory/items/</td>
                <td>inventory:item_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/inventory/categories/</td>
                <td>inventory:category_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/inventory/suppliers/</td>
                <td>inventory:supplier_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/reports/</td>
                <td>reports:dashboard</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/reports/students/</td>
                <td>reports:student_report</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/reports/financial/</td>
                <td>reports:financial_report</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/reports/academic/</td>
                <td>reports:academic_report</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/core/settings/</td>
                <td>core:school_settings</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/core/academic-years/</td>
                <td>core:academic_year_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/core/semesters/</td>
                <td>core:semester_list</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/api/</td>
                <td>-</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/api/students/</td>
                <td>-</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/api/teachers/</td>
                <td>-</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/api/classes/</td>
                <td>-</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/students/462b0cce-f320-4c21-9d79-4248141cace3/</td>
                <td>students:student_detail</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/students/8c2f461f-0f7d-4e8f-9ca4-b769631252ac/</td>
                <td>students:student_detail</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/students/c2e95143-e1f3-40b1-b281-bf6de0885d7d/</td>
                <td>students:student_detail</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/students/grades/8ea05cae-87b1-4cd3-911f-b75b7fd56b6f/</td>
                <td>students:grade_detail</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/students/grades/885e8fdf-e140-41a5-8348-551c7b32bbce/</td>
                <td>students:grade_detail</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

            <tr class="status-failed">
                <td>/students/grades/2d59b6c4-9829-4813-955f-a0aec056ace8/</td>
                <td>students:grade_detail</td>
                <td>admin</td>
                <td>400</td>
                <td>HTTP 400</td>
                <td>-</td>
            </tr>

        </tbody>
    </table>
    
    <h2>🔧 Recommendations</h2>
    <ul>
        <li>✅ URLs returning 200 are working correctly</li>
        <li>🔄 URLs returning 301/302 are redirecting (usually normal)</li>
        <li>❌ URLs returning 404 may need route fixes</li>
        <li>🔒 URLs returning 403 may need permission adjustments</li>
        <li>💥 URLs returning 500 need debugging</li>
    </ul>
    
    <footer style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666;">
        <p>Generated by School ERP URL Tester</p>
    </footer>
</body>
</html>
